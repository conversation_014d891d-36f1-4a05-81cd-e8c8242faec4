
[/Script/UnrealEd.LevelEditorPlaySettings]
PlayFromHerePlayerStartClassName=/Script/Engine.PlayerStartPIE
GameGetsMouseControl=False
ShowMouseControlLabel=True
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
ShouldMinimizeEditorOnVRPIE=True
AutoRecompileBlueprints=True
EnableGameSound=True
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bOnlyLoadVisibleLevelsInPIE=False
bPreferToStreamLevelsInPIE=False
bBindSequencerToPIE=False
bBindSequencerToSimulate=True
PIEAlwaysOnTop=False
NewWindowWidth=1600
NewWindowHeight=900
NewWindowPosition=(X=0,Y=0)
CenterNewWindow=True
StandaloneWindowWidth=1600
StandaloneWindowHeight=900
StandaloneWindowPosition=(X=0,Y=0)
CenterStandaloneWindow=True
DisableStandaloneSound=False
AdditionalLaunchParameters=-log
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
bAutoCompileBlueprintsOnLaunch=True
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNetDedicated=False
PlayNumberOfClients=1
ServerPort=17777
ClientWindowWidth=640
AutoConnectToServer=True
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
AdditionalServerGameOptions=
AdditionalLaunchOptions=
MultipleInstanceLastHeight=0
MultipleInstanceLastWidth=0
LastExecutedLaunchDevice=WindowsNoEditor@3ZBAMAS0T8HFXGX
LastExecutedLaunchName=3ZBAMAS0T8HFXGX
LastExecutedLaunchModeType=LaunchMode_OnDevice
LastExecutedPlayModeLocation=PlayLocation_DefaultPlayerStart
LastExecutedPlayModeType=PlayMode_InNewProcess
LastExecutedPIEPreviewDevice=
-LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9")
-LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10")
-LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10")
-LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10")
-LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10")
-LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10")
-LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9")
+LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9")
+LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10")
+LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10")
+LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10")
+LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10")
+LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10")
+LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9")
-MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10")
-MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9")
-MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10")
-MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9")
-MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9")
+MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10")
+MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9")
+MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10")
+MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9")
+MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="Apple iPhone 4, 4S, iTouch 4 (Portrait)",Width=640,Height=960,AspectRatio="2:3")
-PhoneScreenResolutions=(Description="Apple iPhone 4, 4S, iTouch 4 (Landscape)",Width=960,Height=640,AspectRatio="3:2")
-PhoneScreenResolutions=(Description="Apple iPhone 5, 5S, iTouch 5 (Portrait)",Width=640,Height=1136,AspectRatio="~9:16")
-PhoneScreenResolutions=(Description="Apple iPhone 5, 5S, iTouch 5 (Landscape)",Width=1136,Height=640,AspectRatio="~16:9")
-PhoneScreenResolutions=(Description="Apple iPhone 6 (Portrait)",Width=750,Height=1334,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="Apple iPhone 6 (Landscape)",Width=1334,Height=750,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="Apple iPhone 6+ (Portrait)",Width=1080,Height=1920,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="Apple iPhone 6+ (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="HTC One (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
-PhoneScreenResolutions=(Description="HTC One (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
-PhoneScreenResolutions=(Description="Samsung Galaxy S4 (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
-PhoneScreenResolutions=(Description="Samsung Galaxy S4 (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="Apple iPhone 4, 4S, iTouch 4 (Portrait)",Width=640,Height=960,AspectRatio="2:3")
+PhoneScreenResolutions=(Description="Apple iPhone 4, 4S, iTouch 4 (Landscape)",Width=960,Height=640,AspectRatio="3:2")
+PhoneScreenResolutions=(Description="Apple iPhone 5, 5S, iTouch 5 (Portrait)",Width=640,Height=1136,AspectRatio="~9:16")
+PhoneScreenResolutions=(Description="Apple iPhone 5, 5S, iTouch 5 (Landscape)",Width=1136,Height=640,AspectRatio="~16:9")
+PhoneScreenResolutions=(Description="Apple iPhone 6 (Portrait)",Width=750,Height=1334,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="Apple iPhone 6 (Landscape)",Width=1334,Height=750,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="Apple iPhone 6+ (Portrait)",Width=1080,Height=1920,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="Apple iPhone 6+ (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="HTC One (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
+PhoneScreenResolutions=(Description="HTC One (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
+PhoneScreenResolutions=(Description="Samsung Galaxy S4 (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
+PhoneScreenResolutions=(Description="Samsung Galaxy S4 (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
-TabletScreenResolutions=(Description="Apple iPad 2, iPad Mini (Portrait)",Width=768,Height=1024,AspectRatio="~3:4")
-TabletScreenResolutions=(Description="Apple iPad 2, iPad Mini (Landscape)",Width=1024,Height=768,AspectRatio="~4:3")
-TabletScreenResolutions=(Description="Apple iPad 3, 4, Air (Portrait)",Width=1536,Height=2048,AspectRatio="3:4")
-TabletScreenResolutions=(Description="Apple iPad 3, 4, Air (Landscape)",Width=2048,Height=1536,AspectRatio="4:3")
-TabletScreenResolutions=(Description="Microsoft Surface RT (Portrait)",Width=768,Height=1366,AspectRatio="9:16")
-TabletScreenResolutions=(Description="Microsoft Surface RT (Landscape)",Width=1366,Height=768,AspectRatio="16:9")
-TabletScreenResolutions=(Description="Microsoft Surface Pro (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
-TabletScreenResolutions=(Description="Microsoft Surface Pro (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
+TabletScreenResolutions=(Description="Apple iPad 2, iPad Mini (Portrait)",Width=768,Height=1024,AspectRatio="~3:4")
+TabletScreenResolutions=(Description="Apple iPad 2, iPad Mini (Landscape)",Width=1024,Height=768,AspectRatio="~4:3")
+TabletScreenResolutions=(Description="Apple iPad 3, 4, Air (Portrait)",Width=1536,Height=2048,AspectRatio="3:4")
+TabletScreenResolutions=(Description="Apple iPad 3, 4, Air (Landscape)",Width=2048,Height=1536,AspectRatio="4:3")
+TabletScreenResolutions=(Description="Microsoft Surface RT (Portrait)",Width=768,Height=1366,AspectRatio="9:16")
+TabletScreenResolutions=(Description="Microsoft Surface RT (Landscape)",Width=1366,Height=768,AspectRatio="16:9")
+TabletScreenResolutions=(Description="Microsoft Surface Pro (Portrait)",Width=1080,Height=1920,AspectRatio="9:16")
+TabletScreenResolutions=(Description="Microsoft Surface Pro (Landscape)",Width=1920,Height=1080,AspectRatio="16:9")
-TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9")
-TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9")
-TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9")
-TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1")
+TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9")
+TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9")
+TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9")
+TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1")


