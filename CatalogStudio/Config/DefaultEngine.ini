[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/Maps/Root.Root
GameDefaultMap=/Game/Maps/Root.Root
GameInstanceClass=/Script/DesignStation.CatalogStudioGameInstance
GlobalDefaultGameMode=/Script/DesignStation.CatalogGameMode

[/Script/Engine.Engine]
GameEngine=/Script/DesignStation.CatalogStudioGameEngine
bUseFixedFrameRate=True
FixedFrameRate=90.000000
NearClipPlane=3.000000

[/Script/Engine.RendererSettings]
r.CustomDepth=3
r.ClearCoatNormal=True
r.PostProcessing.PropagateAlpha=0
r.TemporalAA.Upsampling=True
vr.ODSCapture=True
r.SkinCache.CompileShaders=False
r.RayTracing=False
r.ReflectionMethod=0
r.GenerateMeshDistanceFields=False
r.DynamicGlobalIlluminationMethod=0
r.Lumen.TraceMeshSDFs=0
r.Shadow.Virtual.Enable=0
r.VirtualTextures=False
r.VT.EnableAutoImport=False
r.VirtualTexturedLightmaps=True
r.D3D12.UseCommittedTexturesNV=1
r.Lumen.HardwareRayTracing=False
r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject=False
r.RayTracing.Shadows=True
r.RayTracing.Skylight=False
r.DefaultFeature.MotionBlur=False
r.DBuffer=False

[/Script/Engine.UserInterfaceSettings]
UIScaleCurve=(EditorCurveData=(PreInfinityExtrap=RCCE_Constant,PostInfinityExtrap=RCCE_Constant,DefaultValue=340282346638528859811704183484516925440.000000,Keys=((Time=480.000000,Value=1.000000),(Time=720.000000,Value=1.000000),(Time=1080.000000,Value=1.000000),(Time=1680.000000,Value=1.000000),(Time=1920.000000,Value=1.000000),(Time=8640.000000,Value=1.000000))),ExternalCurve=None)
UIScaleRule=LongestSide
ApplicationScale=1.000000

[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
bSupportsPortraitOrientation=False
bSupportsUpsideDownOrientation=False
bSupportsLandscapeLeftOrientation=True
PreferredLandscapeOrientation=LandscapeLeft

[/Script/Engine.PhysicsSettings]
DefaultGravityZ=-980.000000
DefaultTerminalVelocity=4000.000000
DefaultFluidFriction=0.300000
SimulateScratchMemorySize=262144
RagdollAggregateThreshold=4
TriangleMeshTriangleMinAreaThreshold=5.000000
bEnableAsyncScene=False
bEnableShapeSharing=False
bEnablePCM=True
bEnableStabilization=False
bWarnMissingLocks=True
bEnable2DPhysics=False
PhysicErrorCorrection=(PingExtrapolation=0.100000,PingLimit=100.000000,ErrorPerLinearDifference=1.000000,ErrorPerAngularDifference=1.000000,MaxRestoredStateError=1.000000,MaxLinearHardSnapDistance=400.000000,PositionLerp=0.000000,AngleLerp=0.400000,LinearVelocityCoefficient=100.000000,AngularVelocityCoefficient=10.000000,ErrorAccumulationSeconds=0.500000,ErrorAccumulationDistanceSq=15.000000,ErrorAccumulationSimilarity=100.000000)
LockedAxis=Invalid
DefaultDegreesOfFreedom=Full3D
BounceThresholdVelocity=200.000000
FrictionCombineMode=Average
RestitutionCombineMode=Average
MaxAngularVelocity=3600.000000
MaxDepenetrationVelocity=0.000000
ContactOffsetMultiplier=0.020000
MinContactOffset=2.000000
MaxContactOffset=8.000000
bSimulateSkeletalMeshOnDedicatedServer=True
DefaultShapeComplexity=CTF_UseSimpleAndComplex
bDefaultHasComplexCollision=True
bSuppressFaceRemapTable=False
bSupportUVFromHitResults=False
bDisableActiveActors=False
bDisableKinematicStaticPairs=False
bDisableKinematicKinematicPairs=False
bDisableCCD=False
bEnableEnhancedDeterminism=False
MaxPhysicsDeltaTime=0.033333
bSubstepping=False
bSubsteppingAsync=False
MaxSubstepDeltaTime=0.016667
MaxSubsteps=6
SyncSceneSmoothingFactor=0.000000
AsyncSceneSmoothingFactor=0.990000
InitialAverageFrameRate=0.016667
PhysXTreeRebuildRate=10
DefaultBroadphaseSettings=(bUseMBPOnClient=False,bUseMBPOnServer=False,MBPBounds=(Min=(X=0.000000,Y=0.000000,Z=0.000000),Max=(X=0.000000,Y=0.000000,Z=0.000000),IsValid=0),MBPNumSubdivs=2)

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX11
-D3D12TargetedShaderFormats=PCD3D_SM5
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=VisualStudio2022
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=1F01DE00490EC9407F8883BE7B496F0B
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[Core.Log]
LogActor=VeryVerbose

[/Script/LevelSequence.LevelSequenceProjectSettings]
LevelSequence.DefaultClockSource=0

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=None

[/Script/GameplayDebugger.GameplayDebuggerConfig]
ActivationKey=None
//ActivationKey=Apostrophe
