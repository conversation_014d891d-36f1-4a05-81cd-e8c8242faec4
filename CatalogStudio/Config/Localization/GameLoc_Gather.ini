;METADATA=(Diff=true, UseCommands=true)
[CommonSettings]
ManifestDependencies=../../UE55/Engine/Content/Localization/Engine/Engine.manifest
ManifestDependencies=../../UE55/Engine/Content/Localization/Editor/Editor.manifest
SourcePath=Content/Localization/GameLoc
DestinationPath=Content/Localization/GameLoc
ManifestName=GameLoc.manifest
ArchiveName=GameLoc.archive
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=zh

[GatherTextStep0]
CommandletClass=GatherTextFromSource
SearchDirectoryPaths=Source
ExcludePathFilters=Config/Localization/*
FileNameFilters=*.h
FileNameFilters=*.cpp
FileNameFilters=*.ini
FileNameFilters=*.csv
ShouldGatherFromEditorOnlyData=false

[GatherTextStep1]
CommandletClass=GatherTextFromAssets
IncludePathFilters=Content/*
ExcludePathFilters=Content/Localization/*
ExcludePathFilters=Content/L10N/*
PackageFileNameFilters=*.umap
PackageFileNameFilters=*.uasset
ShouldExcludeDerivedClasses=false
ShouldGatherFromEditorOnlyData=false
SkipGatherCache=false

[GatherTextStep2]
CommandletClass=GenerateGatherManifest

[GatherTextStep3]
CommandletClass=GenerateGatherArchive

[GatherTextStep4]
CommandletClass=GenerateTextLocalizationReport
bWordCountReport=true
WordCountReportName=GameLoc.csv
bConflictReport=true
ConflictReportName=GameLoc_Conflicts.txt

