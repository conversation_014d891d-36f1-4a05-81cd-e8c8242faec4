{"FileVersion": 3, "EngineAssociation": "{A4919BF6-443E-D1DC-4DA5-77A970034CE5}", "Category": "", "Description": "", "Modules": [{"Name": "DesignStation", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "CoreUObject", "UMG", "Slate", "SlateCore"]}], "Plugins": [{"Name": "VictoryUMG", "Enabled": true}, {"Name": "OculusVR", "Enabled": false}, {"Name": "SteamVR", "Enabled": false}, {"Name": "MagicCore", "Enabled": true}, {"Name": "GeometryEdit", "Enabled": true}, {"Name": "FbxFileImport", "Enabled": true}, {"Name": "WebCommunication", "Enabled": true, "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/content/3b04f3c4af5445959cee4448846b55a0"}, {"Name": "DLSS", "Enabled": false, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "ChaosNiagara", "Enabled": false}, {"Name": "AlembicImporter", "Enabled": false}, {"Name": "GeometryCache", "Enabled": false}, {"Name": "Niagara", "Enabled": false}, {"Name": "EnvironmentQueryEditor", "Enabled": false}, {"Name": "AISupport", "Enabled": false}, {"Name": "AndroidMedia", "Enabled": false}, {"Name": "AndroidMoviePlayer", "Enabled": false}, {"Name": "OnlineSubsystemGooglePlay", "Enabled": false, "SupportedTargetPlatforms": ["Android"]}, {"Name": "AndroidPermission", "Enabled": false}, {"Name": "AndroidFileServer", "Enabled": false}, {"Name": "AndroidDeviceProfileSelector", "Enabled": false}, {"Name": "AnimationModifierLibrary", "Enabled": false}, {"Name": "AnimationSharing", "Enabled": false}, {"Name": "AppleImageUtils", "Enabled": false}, {"Name": "AppleMoviePlayer", "Enabled": false}], "TargetPlatforms": ["WindowsNoEditor", "HoloLens", "Windows"]}