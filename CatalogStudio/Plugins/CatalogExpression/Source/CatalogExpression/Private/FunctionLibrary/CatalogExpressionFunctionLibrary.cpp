// Fill out your copyright notice in the Description page of Project Settings.


#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

#include "Lexer/ExpressionLexer.h"
#include "Parser/ExpressionParser.h"

#include <chrono>

#include "Decimal.h"
#include "DecimalMath.h"

#define LOCTEXT_NAMESPACE "CatalogExpression"

FString UCatalogExpressionFunctionLibrary::GetTokenTypeDisplayName(EExpressionLexerTokenType InType)
{
	switch (InType)
	{
	case EExpressionLexerTokenType::Unknown:				return TEXT("Unknown");
	case EExpressionLexerTokenType::Number:					return TEXT("Number");
	case EExpressionLexerTokenType::Float:					return TEXT("Float");
	case EExpressionLexerTokenType::CharLiteral:			return TEXT("CharLiteral");
	case EExpressionLexerTokenType::String:					return TEXT("String");
	case EExpressionLexerTokenType::Identifier:				return TEXT("Identifier");
	case EExpressionLexerTokenType::MonocularOperator:		return TEXT("MonocularOperator");
	case EExpressionLexerTokenType::BinocularOperator:		return TEXT("BinocularOperator");
	case EExpressionLexerTokenType::Keyword:				return TEXT("Keyword");
	case EExpressionLexerTokenType::Comment:				return TEXT("Comment");
	case EExpressionLexerTokenType::LeftParen:				return TEXT("LeftParen");
	case EExpressionLexerTokenType::RightParen:				return TEXT("RightParen");
	case EExpressionLexerTokenType::LeftBrace:				return TEXT("LeftBrace");
	case EExpressionLexerTokenType::RightBrace:				return TEXT("RightBrace");
	case EExpressionLexerTokenType::Comma:					return TEXT("Comma");
	case EExpressionLexerTokenType::Semicolon:				return TEXT("Semicolon");
	case EExpressionLexerTokenType::LineFeed:				return TEXT("LineFeed");
	case EExpressionLexerTokenType::Whitespace:				return TEXT("Whitespace");
	case EExpressionLexerTokenType::EndOfFile:				return TEXT("EndOfFile");
	default:												return TEXT("Unknown");
	}
}

EExpressionFunctionType UCatalogExpressionFunctionLibrary::GetExpressionFunctionType(const FString& InFunctionName)
{
	FString UpperName = InFunctionName.ToUpper();
	if (UpperName.Equals(TEXT("IF")))
	{
		return EExpressionFunctionType::If;
	}
	else if (UpperName.Equals(TEXT("COND")))
	{
		return EExpressionFunctionType::Condition;
	}
	else if (UpperName.Equals(TEXT("MAX")))
	{
		return EExpressionFunctionType::Max;
	}
	else if (UpperName.Equals(TEXT("MIN")))
	{
		return EExpressionFunctionType::Min;
	}
	else if (UpperName.Equals(TEXT("SIN")))
	{
		return EExpressionFunctionType::Sin;
	}
	else if (UpperName.Equals(TEXT("COS")))
	{
		return EExpressionFunctionType::Cos;
	}
	else if (UpperName.Equals(TEXT("TAN")))
	{
		return EExpressionFunctionType::Tan;
	}
	else if (UpperName.Equals(TEXT("ASIN")))
	{
		return EExpressionFunctionType::ArcSin;
	}
	else if (UpperName.Equals(TEXT("ACOS")))
	{
		return EExpressionFunctionType::ArcCos;
	}
	else if (UpperName.Equals(TEXT("ATAN")))
	{
		return EExpressionFunctionType::ArcTan;
	}
	else if (UpperName.Equals(TEXT("ATAN2")))
	{
		return EExpressionFunctionType::ArcTan2;
	}
	else if (UpperName.Equals(TEXT("SQRT")))
	{
		return EExpressionFunctionType::Sqrt;
	}
	else if (UpperName.Equals(TEXT("ABS")))
	{
		return EExpressionFunctionType::Abs;
	}
	else if (UpperName.Equals(TEXT("COMB")))
	{
		return EExpressionFunctionType::Combine;
	}
	else if (UpperName.Equals(TEXT("POW")))
	{
		return EExpressionFunctionType::Power;
	}
	else if (UpperName.Equals(TEXT("ROUND")))
	{
		return EExpressionFunctionType::Round;
	}
	else if (UpperName.Equals(TEXT("CEIL")))
	{
		return EExpressionFunctionType::Ceil;
	}
	else if (UpperName.Equals(TEXT("FLOOR")))
	{
		return EExpressionFunctionType::Floor;
	}
	else if (UpperName.Equals(TEXT("TODEG")))
	{
		return EExpressionFunctionType::ToDeg;
	}
	else if (UpperName.Equals(TEXT("TORAD")))
	{
		return EExpressionFunctionType::ToRad;
	}
	else if (UpperName.Equals(TEXT("PI")))
	{
		return EExpressionFunctionType::Pi;
	}
	else if (UpperName.Equals(TEXT("GRIDSNAP")))
	{
		return EExpressionFunctionType::GridSnap;
	}

	return EExpressionFunctionType::Unknow;
}

FString UCatalogExpressionFunctionLibrary::RemoveQuoteMarkFromString(FString InValue)
{
	if (InValue.RemoveFromStart(TEXT("\"")))
	{
		InValue.RemoveFromEnd(TEXT("\""));
	}
	else if (InValue.RemoveFromStart(TEXT("'")))
	{
		InValue.RemoveFromEnd(TEXT("'"));
	}

	return InValue;
}

bool UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(const FString& Source)
{
	if (Source.IsEmpty())
	{
		return false;
	}

	const TCHAR* Ptr = *Source;

	if (*Ptr == TEXT('+') || *Ptr == TEXT('-'))
	{
		++Ptr;
	}

	bool bHasDigits = false;
	bool bHasDot = false;
	bool bHasExponent = false;

	while (*Ptr != TEXT('\0'))
	{
		if (FChar::IsDigit(*Ptr))
		{
			bHasDigits = true;
		}
		else if (*Ptr == TEXT('.'))
		{
			if (bHasDot || bHasExponent)
			{
				return false;
			}

			bHasDot = true;
		}
		else if (*Ptr == TEXT('e') || *Ptr == TEXT('E'))
		{
			if (bHasExponent || !bHasDigits)
			{
				return false;
			}

			bHasExponent = true;
			bHasDigits = false;

			++Ptr;
			if (*Ptr == TEXT('+') || *Ptr == TEXT('-'))
			{
				++Ptr;
			}
			continue;
		}
		else
		{
			return false;
		}

		++Ptr;
	}

	return bHasDigits;
}

bool UCatalogExpressionFunctionLibrary::CheckStringIsInteger(const FString& Source)
{
	if (Source.IsEmpty())
	{
		return false;
	}

	const TCHAR* Ptr = *Source;
	const TCHAR* End = Ptr + Source.Len();

	if (*Ptr == '+' || *Ptr == '-')
	{
		++Ptr;
		if (Ptr == End)
		{
			return false;
		}
	}

	const TCHAR* IntStart = Ptr;
	while (Ptr < End && FChar::IsDigit(*Ptr))
	{
		++Ptr;
	}

	int64 LenInt = static_cast<int64>(Ptr - IntStart);

	const TCHAR* FracStart = nullptr;
	int64 LenFrac = 0;
	if (Ptr < End && *Ptr == '.')
	{
		++Ptr;
		FracStart = Ptr;
		while (Ptr < End && FChar::IsDigit(*Ptr))
		{
			++Ptr;
		}

		LenFrac = static_cast<int64>(Ptr - FracStart);
	}

	if (LenInt == 0 && LenFrac == 0)
	{
		return false;
	}

	if (Ptr != End)
	{
		return false;
	}

	if (LenFrac == 0)
	{
		return true;
	}

	for (const TCHAR* Char = FracStart; Char < FracStart + LenFrac; ++Char)
	{
		if (*Char != '0')
		{
			return false;
		}
	}

	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateExpressionNode(const TSharedPtr<FAstNode>& Node, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (!Node.IsValid())
	{
		throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Invalid node.")));
	}

	auto MapContainsName = [&](const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& InMap,const FString& InName)
		{
			return InMap.Contains(InName);
		};

	switch (Node->GetType())
	{
	case EAstNodeType::Number:
		{
			TSharedPtr<FAstNodeNumber> NumberNode = StaticCastSharedPtr<FAstNodeNumber>(Node);
			OutResult = NumberNode->Value;
			return true;
		}

	case EAstNodeType::Variable:
		{
			TSharedPtr<FAstNodeVariable> VariableNode = StaticCastSharedPtr<FAstNodeVariable>(Node);
			if (VariableNode->bLocalReference && MapContainsName(LocalSymbolTables, VariableNode->Name))
			{
				OutResult = LocalSymbolTables[VariableNode->Name];
				return true;
			}

			if (MapContainsName(ParentSymbolTables, VariableNode->Name))
			{
				OutResult = ParentSymbolTables[VariableNode->Name];
				return true;
			}

			OutError = FString::Printf(TEXT("Undefined variable: %s"), *VariableNode->Name);
			return false;
		}

	case EAstNodeType::UnaryOp:
		{
			TSharedPtr<FAstNodeUnaryOp> UnaryNode = StaticCastSharedPtr<FAstNodeUnaryOp>(Node);

			if (UnaryNode->GetType() == EAstNodeType::String)
			{
				OutError = TEXT("Not allowed string as operand");
				return false;
			}

			FString Operand;
			if (!EvaluateExpressionNode(UnaryNode->Operand, ParentSymbolTables, LocalSymbolTables, Operand, OutError))
			{
				return false;
			}

			if (!CheckStringIsNumeric(Operand))
			{
				OutError = TEXT("Not allowed string as operand");
				return false;
			}

			return EvaluateUnaryOperator(UnaryNode->Operator, Operand, OutResult, OutError);
		}

	case EAstNodeType::BinaryOp:
		{
			TSharedPtr<FAstNodeBinaryOp> BinaryNode = StaticCastSharedPtr<FAstNodeBinaryOp>(Node);

			FString Left;
			if (!EvaluateExpressionNode(BinaryNode->LeftOperand, ParentSymbolTables, LocalSymbolTables, Left, OutError))
			{
				return false;
			}

			FString Right;
			if (!EvaluateExpressionNode(BinaryNode->RightOperand, ParentSymbolTables, LocalSymbolTables, Right, OutError))
			{
				return false;
			}

			bool bHasStringOperand = !CheckStringIsNumeric(Left) || !CheckStringIsNumeric(Right);
			bool bIsEqualsOperator = BinaryNode->Operator.Equals(TEXT("==")) || BinaryNode->Operator.Equals(TEXT("!="));
			if (bHasStringOperand && !bIsEqualsOperator)
			{
				OutError = FString::Printf(TEXT("Not allowed string as operand for operator [%s]"), *BinaryNode->Operator);
				return false;
			}

			return EvaluateBinaryOperator(Left, BinaryNode->Operator, Right, OutResult, OutError);
		}

	case EAstNodeType::FunctionCall:
		{
			TSharedPtr<FAstNodeFunctionCall> FunctionNode = StaticCastSharedPtr<FAstNodeFunctionCall>(Node);
			
			EExpressionFunctionType FunctionType = GetExpressionFunctionType(FunctionNode->Name);
			if (FunctionType == EExpressionFunctionType::Unknow)
			{
				OutError = FString::Printf(TEXT("Unsupported function: %s"), *FunctionNode->Name);
				return false;
			}
			
			switch (FunctionType)
			{
			case EExpressionFunctionType::If:
				return EvaluateFunctionCall_If(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Condition:
				return EvaluateFunctionCall_Condition(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Max:
				return EvaluateFunctionCall_Max(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Min:
				return EvaluateFunctionCall_Min(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Sin:
				return EvaluateFunctionCall_Sin(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Cos:
				return EvaluateFunctionCall_Cos(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Tan:
				return EvaluateFunctionCall_Tan(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ArcSin:
				return EvaluateFunctionCall_ArcSin(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ArcCos:
				return EvaluateFunctionCall_ArcCos(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ArcTan:
				return EvaluateFunctionCall_ArcTan(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ArcTan2:
				return EvaluateFunctionCall_ArcTan2(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Sqrt:
				return EvaluateFunctionCall_Sqrt(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Abs:
				return EvaluateFunctionCall_Abs(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Combine:
				return EvaluateFunctionCall_Combine(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Power:
				return EvaluateFunctionCall_Power(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Round:
				return EvaluateFunctionCall_Round(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Ceil:
				return EvaluateFunctionCall_Ceil(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Floor:
				return EvaluateFunctionCall_Floor(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ToDeg:
				return EvaluateFunctionCall_ToDeg(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::ToRad:
				return EvaluateFunctionCall_ToRad(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::Pi:
				return EvaluateFunctionCall_Pi(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			case EExpressionFunctionType::GridSnap:
				return EvaluateFunctionCall_GridSnap(FunctionNode->Arguments, ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			default:
				{
					OutError = FString::Printf(TEXT("Unsupported function: %s"), *FunctionNode->Name);
					return false;
				}
			}
		}

	case EAstNodeType::String:
		{
			TSharedPtr<FAstNodeString> StringNode = StaticCastSharedPtr<FAstNodeString>(Node);
			OutResult = RemoveQuoteMarkFromString(StringNode->Value);
			return true;
		}
	default:
		{
			OutError = TEXT("Unsupported node type in evaluation.");
			return false;
		}
	}
}

FText UCatalogExpressionFunctionLibrary::GetFunctionTypeOperatorText(EExpressionFunctionType Type)
{
	switch (Type)
	{
	case EExpressionFunctionType::If:
		return LOCTEXT("FunctionTypeOperatorIf", "IF(Exp, A, B)");
	case EExpressionFunctionType::Condition:
		return LOCTEXT("FunctionTypeOperatorCondition", "COND(Exp, A, B)");
	case EExpressionFunctionType::Max:
		return LOCTEXT("FunctionTypeOperatorMax", "MAX(A, B)");
	case EExpressionFunctionType::Min:
		return LOCTEXT("FunctionTypeOperatorMin", "MIN(A, B)");
	case EExpressionFunctionType::Sin:
		return LOCTEXT("FunctionTypeOperatorSin", "SIN(Val)");
	case EExpressionFunctionType::Cos:
		return LOCTEXT("FunctionTypeOperatorCos", "COS(Val)");
	case EExpressionFunctionType::Tan:
		return LOCTEXT("FunctionTypeOperatorTan", "TAN(Val)");
	case EExpressionFunctionType::ArcSin:
		return LOCTEXT("FunctionTypeOperatorArcSin", "ASIN(Val)");
	case EExpressionFunctionType::ArcCos:
		return LOCTEXT("FunctionTypeOperatorArcCos", "ACOS(Val)");
	case EExpressionFunctionType::ArcTan:
		return LOCTEXT("FunctionTypeOperatorArcTan", "ATAN(Val)");
	case EExpressionFunctionType::ArcTan2:
		return LOCTEXT("FunctionTypeOperatorArcTan2", "ATAN2(A, B)");
	case EExpressionFunctionType::Sqrt:
		return LOCTEXT("FunctionTypeOperatorSqrt", "SQRT(Val)");
	case EExpressionFunctionType::Abs:
		return LOCTEXT("FunctionTypeOperatorAbs", "ABS(Val)");
	case EExpressionFunctionType::Combine:
		return LOCTEXT("FunctionTypeOperatorCombine", "COMB(Val)");
	case EExpressionFunctionType::Power:
		return LOCTEXT("FunctionTypeOperatorPower", "POW(Val)");
	case EExpressionFunctionType::Round:
		return LOCTEXT("FunctionTypeOperatorRound", "ROUND(Val)");
	case EExpressionFunctionType::Ceil:
		return LOCTEXT("FunctionTypeOperatorCeil", "CEIL(Val)");
	case EExpressionFunctionType::Floor:
		return LOCTEXT("FunctionTypeOperatorFloor", "FLOOR(Val)");
	case EExpressionFunctionType::ToDeg:
		return LOCTEXT("FunctionTypeOperatorToDeg", "TODEG(Val)");
	case EExpressionFunctionType::ToRad:
		return LOCTEXT("FunctionTypeOperatorToRad", "TORAD(Val)");
	case EExpressionFunctionType::Pi:
		return LOCTEXT("FunctionTypeOperatorPi", "PI()");
	case EExpressionFunctionType::GridSnap:
		return LOCTEXT("FunctionTypeOperatorGridSnap", "GRIDSNAP(Val, GridVal)");
	default:
		return FText::GetEmpty();
	}
}

FText UCatalogExpressionFunctionLibrary::GetFunctionTypeDisplayText(EExpressionFunctionType Type)
{
	switch (Type)
	{
	case EExpressionFunctionType::If:
		return LOCTEXT("FunctionTypeDisplayIf", "IF");
	case EExpressionFunctionType::Condition:
		return LOCTEXT("FunctionTypeDisplayCondition", "COND");
	case EExpressionFunctionType::Max:
		return LOCTEXT("FunctionTypeDisplayMax", "MAX\nChoose higher value");
	case EExpressionFunctionType::Min:
		return LOCTEXT("FunctionTypeDisplayMin", "MIN\nChoose lower value");
	case EExpressionFunctionType::Sin:
		return LOCTEXT("FunctionTypeDisplaySin", "SIN\nSine calculation");
	case EExpressionFunctionType::Cos:
		return LOCTEXT("FunctionTypeDisplayCos", "COS\nCosine calculation");
	case EExpressionFunctionType::Tan:
		return LOCTEXT("FunctionTypeDisplayTan", "TAN\nTangent calculation");
	case EExpressionFunctionType::ArcSin:
		return LOCTEXT("FunctionTypeDisplayArcSin", "ASIN\nArc sine calculation");
	case EExpressionFunctionType::ArcCos:
		return LOCTEXT("FunctionTypeDisplayArcCos", "ACOS\nArc cosine calculation");
	case EExpressionFunctionType::ArcTan:
		return LOCTEXT("FunctionTypeDisplayArcTan", "ATAN\nArc tangent calculation");
	case EExpressionFunctionType::ArcTan2:
		return LOCTEXT("FunctionTypeDisplayArcTan2", "ATAN2\nArc tangent 2 calculation");
	case EExpressionFunctionType::Sqrt:
		return LOCTEXT("FunctionTypeDisplaySqrt", "SQRT\nSquare root");
	case EExpressionFunctionType::Abs:
		return LOCTEXT("FunctionTypeDisplayAbs", "ABS\nAbsolute value calculation");
	case EExpressionFunctionType::Combine:
		return LOCTEXT("FunctionTypeDisplayCombine", "COMB\nCombine two strings");
	case EExpressionFunctionType::Power:
		return LOCTEXT("FunctionTypeDisplayPower", "POW\nPower calculation");
	case EExpressionFunctionType::Round:
		return LOCTEXT("FunctionTypeDisplayRound", "ROUND\nRound calculation");
	case EExpressionFunctionType::Ceil:
		return LOCTEXT("FunctionTypeDisplayCeil", "CEIL\nCeil calculation");
	case EExpressionFunctionType::Floor:
		return LOCTEXT("FunctionTypeDisplayFloor", "FLOOR\nFloor calculation");
	case EExpressionFunctionType::ToDeg:
		return LOCTEXT("FunctionTypeDisplayToDeg", "TODEG\nConvert radians to degress");
	case EExpressionFunctionType::ToRad:
		return LOCTEXT("FunctionTypeDisplayToRad", "TORAD\nConvert degress to radians");
	case EExpressionFunctionType::Pi:
		return LOCTEXT("FunctionTypeDisplayPi", "PI\nGet the mathematical pi");
	case EExpressionFunctionType::GridSnap:
		return LOCTEXT("FunctionTypeDisplayGridSnap", "GRIDSNAP\nGridding the value");
	default:
		return FText::GetEmpty();
	}
}

FText UCatalogExpressionFunctionLibrary::GetFunctionTypeToolTip(EExpressionFunctionType Type)
{
	switch (Type)
	{
	case EExpressionFunctionType::If:
		return LOCTEXT("FunctionTypeToolTipIf", "IF(EXPR, A, B)");
	case EExpressionFunctionType::Condition:
		return LOCTEXT("FunctionTypeToolTipCondition", "COND(EXP, A, B)");
	case EExpressionFunctionType::Max:
		return LOCTEXT("FunctionTypeToolTipMax", "MAX(A, B)");
	case EExpressionFunctionType::Min:
		return LOCTEXT("FunctionTypeToolTipMin", "MIN(A, B)");
	case EExpressionFunctionType::Sin:
		return LOCTEXT("FunctionTypeToolTipSin", "SIN(Val)");
	case EExpressionFunctionType::Cos:
		return LOCTEXT("FunctionTypeToolTipCos", "COS(Val)");
	case EExpressionFunctionType::Tan:
		return LOCTEXT("FunctionTypeToolTipTan", "TAN(Val)");
	case EExpressionFunctionType::ArcSin:
		return LOCTEXT("FunctionTypeToolTipArcSin", "ASIN(Val)");
	case EExpressionFunctionType::ArcCos:
		return LOCTEXT("FunctionTypeToolTipArcCos", "ACOS(Val)");
	case EExpressionFunctionType::ArcTan:
		return LOCTEXT("FunctionTypeToolTipArcTan", "ATAN(Val)");
	case EExpressionFunctionType::ArcTan2:
		return LOCTEXT("FunctionTypeToolTipArcTan2", "ATAN2(A, B)");
	case EExpressionFunctionType::Sqrt:
		return LOCTEXT("FunctionTypeToolTipSqrt", "SQRT(Val)");
	case EExpressionFunctionType::Abs:
		return LOCTEXT("FunctionTypeToolTipAbs", "ABS(Val)");
	case EExpressionFunctionType::Combine:
		return LOCTEXT("FunctionTypeToolTipCombine", "COMB(A, B)");
	case EExpressionFunctionType::Power:
		return LOCTEXT("FunctionTypeToolTipPower", "POW(A, B)");
	case EExpressionFunctionType::Round:
		return LOCTEXT("FunctionTypeToolTipRound", "ROUND(Val)  round half away from zero");
	case EExpressionFunctionType::Ceil:
		return LOCTEXT("FunctionTypeToolTipCeil", "CEIL(Val)");
	case EExpressionFunctionType::Floor:
		return LOCTEXT("FunctionTypeToolTipFloor", "FLOOR(Val)");
	case EExpressionFunctionType::ToDeg:
		return LOCTEXT("FunctionTypeToolTipToDeg", "TODEG(Val)");
	case EExpressionFunctionType::ToRad:
		return LOCTEXT("FunctionTypeToolTipToRad", "TORAD(Val)");
	case EExpressionFunctionType::Pi:
		return LOCTEXT("FunctionTypeToolTipPi", "PI()");
	case EExpressionFunctionType::GridSnap:
		return LOCTEXT("FunctionTypeToolTipGridSnap", "GRIDSNAP(Val/EXP, GridVal)");
	default:
		return FText::GetEmpty();
	}
}

FText UCatalogExpressionFunctionLibrary::GetMathOperatorText(EExpressionMathOperatorType Type)
{
	switch (Type)
	{
	case EExpressionMathOperatorType::Add:
		return LOCTEXT("MathOperatorTypeOperatprAdd","+");
	case EExpressionMathOperatorType::Minus:
		return LOCTEXT("MathOperatorTypeOperatprMinus","-");
	case EExpressionMathOperatorType::Multiply:
		return LOCTEXT("MathOperatorTypeOperatprMultiply","*");
	case EExpressionMathOperatorType::Divide:
		return LOCTEXT("MathOperatorTypeOperatprDivide","/");
	case EExpressionMathOperatorType::Negation:
		return LOCTEXT("MathOperatorTypeOperatprNegation","!");
	case EExpressionMathOperatorType::BitReversal:
		return LOCTEXT("MathOperatorTypeOperatprBitReversal","~");
	case EExpressionMathOperatorType::Equal:
		return LOCTEXT("MathOperatorTypeOperatprEqual","==");
	case EExpressionMathOperatorType::NotEqual:
		return LOCTEXT("MathOperatorTypeOperatprNotEqual","!=");
	case EExpressionMathOperatorType::Less:
		return LOCTEXT("MathOperatorTypeOperatprLess","<");
	case EExpressionMathOperatorType::LessOrEqual:
		return LOCTEXT("MathOperatorTypeOperatprLessOrEqual","<=");
	case EExpressionMathOperatorType::Greater:
		return LOCTEXT("MathOperatorTypeOperatprGreater",">");
	case EExpressionMathOperatorType::GreaterOrEqual:
		return LOCTEXT("MathOperatorTypeOperatprGreaterOrEqual",">=");
	case EExpressionMathOperatorType::And:
		return LOCTEXT("MathOperatorTypeOperatprAnd","&&");
	case EExpressionMathOperatorType::Or:
		return LOCTEXT("MathOperatorTypeOperatprOr","||");
	case EExpressionMathOperatorType::BitAnd:
		return LOCTEXT("MathOperatorTypeOperatprBitAnd","&");
	case EExpressionMathOperatorType::BitOr:
		return LOCTEXT("MathOperatorTypeOperatprBitOr","|");
	case EExpressionMathOperatorType::Power:
		return LOCTEXT("MathOperatorTypeOperatprPower","^");
	case EExpressionMathOperatorType::PowerAssign:
		return LOCTEXT("MathOperatorTypeOperatprPowerAssign","^=");
	case EExpressionMathOperatorType::Mod:
		return LOCTEXT("MathOperatorTypeOperatprMod","%");
	default:
		return FText::GetEmpty();
	}

}

FText UCatalogExpressionFunctionLibrary::GetMathOperatorDisplayText(EExpressionMathOperatorType Type)
{

	switch (Type)
	{
	case EExpressionMathOperatorType::Add:
		return LOCTEXT("MathOperatorTypeDisplayAdd", "+\n(Add)");
	case EExpressionMathOperatorType::Minus:
		return LOCTEXT("MathOperatorTypeDisplayMinus", "-\n(Minus)");
	case EExpressionMathOperatorType::Multiply:
		return LOCTEXT("MathOperatorTypeDisplayMultiply", "*\n(Multiply)");
	case EExpressionMathOperatorType::Divide:
		return LOCTEXT("MathOperatorTypeDisplayDivide", "/\n(Divide)");
	case EExpressionMathOperatorType::Negation:
		return LOCTEXT("MathOperatorTypeDisplayNegation", "!\n(Negation)");
	case EExpressionMathOperatorType::BitReversal:
		return LOCTEXT("MathOperatorTypeDisplayBitReversal", "~\n(BitReversal)");
	case EExpressionMathOperatorType::Equal:
		return LOCTEXT("MathOperatorTypeDisplayEqual", "==\n(Equal)");
	case EExpressionMathOperatorType::NotEqual:
		return LOCTEXT("MathOperatorTypeDisplayNotEqual", "!=\n(NotEqual)");
	case EExpressionMathOperatorType::Less:
		return LOCTEXT("MathOperatorTypeDisplayLess", "<\n(Less)");
	case EExpressionMathOperatorType::LessOrEqual:
		return LOCTEXT("MathOperatorTypeDisplayLessOrEqual", "<=\n(LessOrEqual)");
	case EExpressionMathOperatorType::Greater:
		return LOCTEXT("MathOperatorTypeDisplayGreater", ">\n(Greater)");
	case EExpressionMathOperatorType::GreaterOrEqual:
		return LOCTEXT("MathOperatorTypeDisplayGreaterOrEqual", ">=\n(GreaterOrEqual)");
	case EExpressionMathOperatorType::And:
		return LOCTEXT("MathOperatorTypeDisplayAnd", "&&\n(And)");
	case EExpressionMathOperatorType::Or:
		return LOCTEXT("MathOperatorTypeDisplayOr", "||\n(Or)");
	case EExpressionMathOperatorType::BitAnd:
		return LOCTEXT("MathOperatorTypeDisplayBitAnd", "&\n(BitAnd)");
	case EExpressionMathOperatorType::BitOr:
		return LOCTEXT("MathOperatorTypeDisplayBitOr", "|\n(BitOr)");
	case EExpressionMathOperatorType::Power:
		return LOCTEXT("MathOperatorTypeDisplayPower", "^\n(Power)");
	case EExpressionMathOperatorType::PowerAssign:
		return LOCTEXT("MathOperatorTypeDisplayPowerAssign", "^=\n(PowerAssign)");
	case EExpressionMathOperatorType::Mod:
		return LOCTEXT("MathOperatorTypeDisplayMod", "%\n(Mod)");
	default:
		return FText::GetEmpty();
	}
}

FText UCatalogExpressionFunctionLibrary::GetMathOperatorToolTip(EExpressionMathOperatorType Type)
{

	switch (Type)
	{
	case EExpressionMathOperatorType::Add:
		return LOCTEXT("MathOperatorTypeToolTipAdd", "+ [1 + 1 -> 2] [+100 -> 0 + 100]");
	case EExpressionMathOperatorType::Minus:
		return LOCTEXT("MathOperatorTypeToolTipMinus", "- [1 - 1 -> 0] [-100 -> 0 - 100]");
	case EExpressionMathOperatorType::Multiply:
		return LOCTEXT("MathOperatorTypeToolTipMultiply", "* [2 * 2 -> 4]");
	case EExpressionMathOperatorType::Divide:
		return LOCTEXT("MathOperatorTypeToolTipDivide", "/ [2 / 2 -> 1]");
	case EExpressionMathOperatorType::Negation:
		return LOCTEXT("MathOperatorTypeToolTipNegation", "! [!1 -> 0, !0 -> 1]");
	case EExpressionMathOperatorType::BitReversal:
		return LOCTEXT("MathOperatorTypeToolTipBitReversal", "~ [Bit -> 0001, 0101, ~Bit -> 1110, 1010]");
	case EExpressionMathOperatorType::Equal:
		return LOCTEXT("MathOperatorTypeToolTipEqual", "== [1 == 1 -> 1, \"Equal\" == \"Equal\" -> 1, \"Equal\" == \"equal\" -> 0]");
	case EExpressionMathOperatorType::NotEqual:
		return LOCTEXT("MathOperatorTypeToolTipNotEqual", "!= [1 != 1 -> 0, \"Equal\" != \"Equal\" -> 0, \"Equal\" != \"equal\" -> 1]");
	case EExpressionMathOperatorType::Less:
		return LOCTEXT("MathOperatorTypeToolTipLess", "< [1 < 2 -> 1, 2 < 2 -> 0]");
	case EExpressionMathOperatorType::LessOrEqual:
		return LOCTEXT("MathOperatorTypeToolTipLessOrEqual", "<= [1 <= 2 -> 1, 2 <= 2 -> 1]");
	case EExpressionMathOperatorType::Greater:
		return LOCTEXT("MathOperatorTypeToolTipGreater", "> [2 > 1 -> 1, 2 > 2 -> 0]");
	case EExpressionMathOperatorType::GreaterOrEqual:
		return LOCTEXT("MathOperatorTypeToolTipGreaterOrEqual", ">= [2 >= 1 -> 1, 2 >= 2 -> 1]");
	case EExpressionMathOperatorType::And:
		return LOCTEXT("MathOperatorTypeToolTipAnd", "&& [1 > 0 && 2 > 1 -> 1]");
	case EExpressionMathOperatorType::Or:
		return LOCTEXT("MathOperatorTypeToolTipOr", "|| [1 > 1 || 2 > 1 -> 1]");
	case EExpressionMathOperatorType::BitAnd:
		return LOCTEXT("MathOperatorTypeToolTipBitAnd", "& [A -> 0001, B -> 0001, A & B -> 1]");
	case EExpressionMathOperatorType::BitOr:
		return LOCTEXT("MathOperatorTypeToolTipBitOr", "| [A -> 0000, B -> 0001, A | B -> 1]");
	case EExpressionMathOperatorType::Power:
		return LOCTEXT("MathOperatorTypeToolTipPower", "^ [2 ^ 3 -> 8]");
	case EExpressionMathOperatorType::PowerAssign:
		return LOCTEXT("MathOperatorTypeToolTipPowerAssign", "^= [2 ^= 3 -> 8]");
	case EExpressionMathOperatorType::Mod:
		return LOCTEXT("MathOperatorTypeToolTipMod", "% [3 % 2 -> 1]");
	default:
		return FText::GetEmpty();
	}
}

TArray<FCatalogExpressionDescriptor> UCatalogExpressionFunctionLibrary::CollectAllDescriptors()
{
	TArray<FCatalogExpressionDescriptor> Result;

	// Function call
	for (uint8 Index = 1; Index < static_cast<uint8>(EExpressionFunctionType::MaxType); ++Index)
	{
		EExpressionFunctionType Type = static_cast<EExpressionFunctionType>(Index);
		Result.Add({ true, Type, GetFunctionTypeOperatorText(Type), GetFunctionTypeDisplayText(Type), GetFunctionTypeToolTip(Type) });
	}

	// Math operator
	for (uint8 Index = 1; Index < static_cast<uint8>(EExpressionMathOperatorType::MaxType); ++Index)
	{
		EExpressionMathOperatorType Type = static_cast<EExpressionMathOperatorType>(Index);
		Result.Add({ false, Type, GetMathOperatorText(Type), GetMathOperatorDisplayText(Type), GetMathOperatorToolTip(Type) });
	}

	return Result;
}

bool UCatalogExpressionFunctionLibrary::EvaluateUnaryOperator(const FString& Operator, const FString& Operand, FString& OutResult, FString& OutError)
{
	if (Operator == TEXT("-"))
	{
		OutResult = (-FDecimal(Operand)).ToString(16);
		return true;
	}
	else if (Operator == TEXT("+"))
	{
		OutResult = Operand;
		return true;
	}
	else if (Operator == TEXT("!"))
	{
		OutResult = FDecimal(Operand) == FDecimal(0) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT("~"))
	{
		OutResult = FString::Printf(TEXT("%lld"), ~FCString::Atoi64(*Operand));
		return true;
	}

	OutError = FString::Printf(TEXT("Unsupported unary operator: %s"), *Operator);
	return false;
}

bool UCatalogExpressionFunctionLibrary::EvaluateBinaryOperator(const FString& Left, const FString& Operator, const FString& Right, FString& OutResult, FString& OutError)
{
	if (Operator == TEXT("+"))
	{
		OutResult = (FDecimal(Left) + FDecimal(Right)).ToString(16);
		return true;
	}
	else if (Operator == TEXT("-"))
	{
		OutResult = (FDecimal(Left) - FDecimal(Right)).ToString(16);
		return true;
	}
	else if (Operator == TEXT("*"))
	{
		OutResult = (FDecimal(Left) * FDecimal(Right)).ToString(16);
		return true;
	}
	else if (Operator == TEXT("/"))
	{
		FDecimal RightDecimal(Right);
		if (RightDecimal.IsNearlyEqual(0))
		{
			OutError = TEXT("Dividend cannot be zero.");
			return false;
		}

		OutResult = (FDecimal(Left) / RightDecimal).ToString(16);
		return true;
	}
	else if (Operator == TEXT("=="))
	{
		if (!CheckStringIsNumeric(Left) || !CheckStringIsNumeric(Right))
		{
			OutResult = Left.Equals(Right) ? TEXT("1") : TEXT("0");
		}
		else
		{
			OutResult = FDecimal(Left) == FDecimal(Right) ? TEXT("1") : TEXT("0");
		}
		return true;
	}
	else if (Operator == TEXT("!="))
	{
		if (!CheckStringIsNumeric(Left) || !CheckStringIsNumeric(Right))
		{
			OutResult = Left.Equals(Right) ? TEXT("0") : TEXT("1");
		}
		else
		{
			OutResult = FDecimal(Left) != FDecimal(Right) ? TEXT("1") : TEXT("0");
		}
		return true;
	}
	else if (Operator == TEXT("<"))
	{
		OutResult = FDecimal(Left) < FDecimal(Right) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT(">"))
	{
		OutResult = FDecimal(Left) > FDecimal(Right) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT("<="))
	{
		OutResult = FDecimal(Left) <= FDecimal(Right) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT(">="))
	{
		OutResult = FDecimal(Left) >= FDecimal(Right) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT("&&"))
	{
		FDecimal ZeroDecimal(0);
		OutResult = (FDecimal(Left) != ZeroDecimal && FDecimal(Right) != ZeroDecimal) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT("||"))
	{
		FDecimal ZeroDecimal(0);
		OutResult = (FDecimal(Left) != ZeroDecimal || FDecimal(Right) != ZeroDecimal) ? TEXT("1") : TEXT("0");
		return true;
	}
	else if (Operator == TEXT("&"))
	{
		OutResult = FString::FromInt(FCString::Atoi(*Left) & FCString::Atoi(*Right));
		return true;
	}
	else if (Operator == TEXT("|"))
	{
		OutResult = FString::FromInt(FCString::Atoi(*Left) | FCString::Atoi(*Right));
		return true;
	}
	else if (Operator == TEXT("^"))
	{
		OutResult = FDecimalMath::Power(FDecimal(Left), FDecimal(Right)).ToString(16);
		return true;
	}
	else if (Operator == TEXT("^="))
	{
		int64 Result = FCString::Atoi64(*Left);
		Result ^= FCString::Atoi64(*Right);
		OutResult = FString::Printf(TEXT("%lld"), Result);
		return true;
	}
	else if (Operator == TEXT("%"))
	{
		OutResult = FDecimalMath::RemainderCustom(FDecimal(Left), FDecimal(Right)).ToString(16);
		return true;
	}
	
	OutError = FString::Printf(TEXT("Unsupported binary operator: %s"), *Operator);
	return false;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_If(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 3)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of IF function. (%d)"), InArgs.Num());
		return false;
	}

	FString Expr;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Expr, OutError))
	{
		return false;
	}

	if (FDecimal(Expr) != 0)
	{
		return EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
	}
	else
	{
		return EvaluateExpressionNode(InArgs[2], ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
	}
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Condition(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() == 3)
	{
		FString Expr;
		if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Expr, OutError))
		{
			return false;
		}

		if (FDecimal(Expr) != 0)
		{
			return EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
		}
		else
		{
			return EvaluateExpressionNode(InArgs[2], ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
		}
	}
	else if (InArgs.Num() > 3 && (InArgs.Num() & 1))
	{
		for (int32 Index = 0; Index < InArgs.Num() - 1; Index += 2)
		{
			FString Expr;
			if (!EvaluateExpressionNode(InArgs[Index], ParentSymbolTables, LocalSymbolTables, Expr, OutError))
			{
				return false;
			}

			if (FDecimal(Expr) != 0)
			{
				return EvaluateExpressionNode(InArgs[Index + 1], ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
			}
		}

		return EvaluateExpressionNode(InArgs.Last(), ParentSymbolTables, LocalSymbolTables, OutResult, OutError);
	}
	else
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of COND function. (%d)"), InArgs.Num());
		return false;
	}
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Max(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 2)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of MAX function. (%d)"), InArgs.Num());
		return false;
	}

	FString Left, Right;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Left, OutError) || !EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, Right, OutError))
	{
		return false;
	}
	
	OutResult = FDecimal(Left) > FDecimal(Right) ? Left : Right;
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Min(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 2)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of MIN function. (%d)"), InArgs.Num());
		return false;
	}

	FString Left, Right;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Left, OutError) || !EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, Right, OutError))
	{
		return false;
	}
	
	OutResult = FDecimal(Left) < FDecimal(Right) ? Left : Right;
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Sin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of SIN function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Sin(FDecimalMath::DegreesToRadians(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Cos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of COS function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Cos(FDecimalMath::DegreesToRadians(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Tan(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of TAN function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Tan(FDecimalMath::DegreesToRadians(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ArcSin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ASIN function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::RadiansToDegrees(FDecimalMath::Asin(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ArcCos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ACOS function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::RadiansToDegrees(FDecimalMath::Acos(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ArcTan(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ATAN function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::RadiansToDegrees(FDecimalMath::Atan(FDecimal(Val))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ArcTan2(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 2)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ATAN2 function. (%d)"), InArgs.Num());
		return false;
	}

	FString A, B;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, A, OutError) || !EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, B, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::RadiansToDegrees(FDecimalMath::Atan2(FDecimal(A), FDecimal(B))).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Sqrt(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of SQRT function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Sqrt(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Abs(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ABS function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Abs(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Combine(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	FString Result;
	for (const FAstNodePtr& Arg : InArgs)
	{
		FString Value;
		if (!EvaluateExpressionNode(Arg, ParentSymbolTables, LocalSymbolTables, Value, OutError))
		{
			return false;
		}

		if (CheckStringIsInteger(Value))
		{
			Value.Split(TEXT("."), &Value, nullptr);
		}

		Result.Append(Value);
	}

	OutResult = Result;
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Power(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 2)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of POW function. (%d)"), InArgs.Num());
		return false;
	}

	FString A, B;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, A, OutError) || !EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, B, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Power(FDecimal(A), FDecimal(B)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Round(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of ROUND function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimal(Val).ToString(0);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Ceil(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of CEIL function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Ceil(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Floor(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of FLOOR function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::Floor(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ToDeg(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of TODEG function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::RadiansToDegrees(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_ToRad(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 1)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of TORAD function. (%d)"), InArgs.Num());
		return false;
	}

	FString Val;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Val, OutError))
	{
		return false;
	}

	OutResult = FDecimalMath::DegreesToRadians(FDecimal(Val)).ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Pi(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 0)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of TORAD function. (%d)"), InArgs.Num());
		return false;
	}

	OutResult = FDecimal::ConstantPI.ToString(16);
	return true;
}

bool UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_GridSnap(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError)
{
	if (InArgs.Num() != 2)
	{
		OutError = FString::Printf(TEXT("Wrong number of parameters of GRIDSNAP function, (%d)"), InArgs.Num());
		return false;
	}

	FString Source;
	if (!EvaluateExpressionNode(InArgs[0], ParentSymbolTables, LocalSymbolTables, Source, OutError))
	{
		return false;
	}

	if (!CheckStringIsNumeric(Source))
	{
		OutResult = Source;
	}
	else
	{
		FString GridVal;
		if (!EvaluateExpressionNode(InArgs[1], ParentSymbolTables, LocalSymbolTables, GridVal, OutError))
		{
			return false;
		}

		FDecimal SnappedVal = FDecimalMath::GridSnap(Source, GridVal);
		OutResult = SnappedVal.ToString(16);
	}

	return true;
}

void UCatalogExpressionFunctionLibrary::TestCatalogExpression()
{
	UE_LOG(LogTemp, Log, TEXT("================================ Expression Testing ================================"));

	FString ExpressionFilePath = TEXT("D:/Library/Desktop/expression_all.txt");

	FString FileContent;
	if (!FFileHelper::LoadFileToString(FileContent, *ExpressionFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("Can not load expression file: %s"), *ExpressionFilePath);
		return;
	}

	TArray<FString> AllExpressions;
	FileContent.ParseIntoArray(AllExpressions, TEXT("\r\n"));

	if (AllExpressions.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Parse expression file to arrray failed."));
		return;
	}

	UE_LOG(LogTemp, Log, TEXT("Adding an empty expression for test."));
	AllExpressions.Add(TEXT(""));

	for (const FString& Expression : AllExpressions)
	{
		UE_LOG(LogTemp, Log, TEXT("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"));
		UE_LOG(LogTemp, Log, TEXT("Expression: %s"), *Expression);

		TArray<FAstNodePtr> Nodes;
		FString Error;

		FExpressionParser Parser;
		if (!Parser.Parse(Expression, Nodes, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Parse expression failed: %s"), *Error);

			TArray<FExpressionLexerToken> Tokens;

			FExpressionLexer Lexer;
			Lexer.SetContent(Expression);
			if (!Lexer.Tokenize(Tokens, Error))
			{
				UE_LOG(LogTemp, Error, TEXT("Tokenize expression failed: %s"), *Error);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("Tokenized expression: "));
				for (const FExpressionLexerToken& Token : Tokens)
				{
					UE_LOG(LogTemp, Log, TEXT("%s"), *Token.ToString());
				}
			}
		}
		else if (Nodes.Num() != 1)
		{
			UE_LOG(LogTemp, Error, TEXT("Parse expression successfully, but generated wrong result count: %d"), Nodes.Num());
		}
		else
		{
			FString Result;
			if (!EvaluateExpressionNode(Nodes[0], { {TEXT("W"), TEXT("800")}, {TEXT("H"), TEXT("500")}, {TEXT("D"), TEXT("300")} }, {}, Result, Error))
			{
				UE_LOG(LogTemp, Error, TEXT("Evalute expression failed: %s"), *Error);
				break;
			}
			UE_LOG(LogTemp, Log, TEXT("Evalute expression result: %s"), *Result);
		}

		UE_LOG(LogTemp, Log, TEXT("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"));
	}

	UE_LOG(LogTemp, Log, TEXT("================================ End Testing ================================"));
}

void UCatalogExpressionFunctionLibrary::PrintAstNode(const TSharedPtr<FAstNode>& Node, int32 IndentLevel)
{
    const FString Indent = FString::ChrN(IndentLevel * 2, ' '); // 2 spaces per indent level

    switch (Node->GetType())
    {
    case EAstNodeType::Number:
        {
            const FAstNodeNumber* NumberNode = static_cast<FAstNodeNumber*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sNumber: %s"), *Indent, *NumberNode->Value);
        }
        break;

    case EAstNodeType::String:
        {
            const FAstNodeString* StringNode = static_cast<FAstNodeString*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sString: %s"), *Indent, *StringNode->Value);
        }
        break;

    case EAstNodeType::Variable:
        {
            const FAstNodeVariable* VarNode = static_cast<FAstNodeVariable*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sVariable: %s, is local reference: %s"), *Indent, *VarNode->Name, VarNode->bLocalReference ? TEXT("true") : TEXT("false"));
        }
        break;

    case EAstNodeType::UnaryOp:
        {
            const FAstNodeUnaryOp* UnaryNode = static_cast<FAstNodeUnaryOp*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sUnaryOp: %s"), *Indent, *UnaryNode->Operator);
            PrintAstNode(UnaryNode->Operand, IndentLevel + 1);
        }
        break;

    case EAstNodeType::BinaryOp:
        {
            const FAstNodeBinaryOp* BinaryNode = static_cast<FAstNodeBinaryOp*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sBinaryOp: %s"), *Indent, *BinaryNode->Operator);
            PrintAstNode(BinaryNode->LeftOperand, IndentLevel + 1);
            PrintAstNode(BinaryNode->RightOperand, IndentLevel + 1);
        }
        break;

    case EAstNodeType::FunctionCall:
        {
            const FAstNodeFunctionCall* FuncNode = static_cast<FAstNodeFunctionCall*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sFunctionCall: %s"), *Indent, *FuncNode->Name);
            for (const FAstNodePtr& Arg : FuncNode->Arguments)
            {
                PrintAstNode(Arg, IndentLevel + 1);
            }
        }
        break;

    case EAstNodeType::Comment:
        {
            const FAstNodeComment* CommentNode = static_cast<FAstNodeComment*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sComment at (%d,%d): %s"),
                   *Indent,
                   CommentNode->Position.X,
                   CommentNode->Position.Y,
                   *CommentNode->Content);
        }
        break;

    default:
        UE_LOG(LogTemp, Display, TEXT("%sUnknown AST node type"), *Indent);
        break;
    }
}

#undef LOCTEXT_NAMESPACE
