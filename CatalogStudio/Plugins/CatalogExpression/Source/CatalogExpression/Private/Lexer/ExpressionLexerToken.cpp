#include "Lexer/ExpressionLexerToken.h"

#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

FExpressionLexerToken::FExpressionLexerToken()
	: Type(EExpressionLexerTokenType::Unknown)
	, Value()
	, Position(0)
{
}

FExpressionLexerToken::FExpressionLexerToken(EExpressionLexerTokenType InType, const FString& InValue, const FIntPoint& InPosition)
	: Type(InType)
	, Value(InValue)
	, Position(InPosition)
{}

FString FExpressionLexerToken::ToString() const
{
	return FString::Printf(TEXT("%s - %s [%d:%d]"), *UCatalogExpressionFunctionLibrary::GetTokenTypeDisplayName(Type), *Value, Position.X, Position.Y);
}
