// Fill out your copyright notice in the Description page of Project Settings.


#include "Parser/ExpressionParser.h"

#include "Lexer/ExpressionLexer.h"

DECLARE_LOG_CATEGORY_CLASS(LogExpressionParser, Log, All);

FExpressionParser::FExpressionParser()
	: Offset(0)
{
}

bool FExpressionParser::Parse(const FString& InContent, TArray<FAstNodePtr>& OutAstTree, FString& OutError)
{
	Offset = 0;
	Tokens.Empty();

	FExpressionLexer Lexer;
	Lexer.SetContent(InContent);

	if (!Lexer.Tokenize(Tokens, OutError))
	{
		OutAstTree.Empty();
		return false;
	}

	while (true)
	{
		SkipWhitespace();
		const FExpressionLexerToken& Token = PeekToken();
		if (Token.Type == EExpressionLexerTokenType::EndOfFile)
		{
			break;
		}

		if (Token.Type == EExpressionLexerTokenType::Comment)
		{
			ConsumeToken();
			continue;
		}

		FAstNodePtr Node;
		if (!ParseExpression(Node, OutError))
		{
			OutAstTree.Empty();
			return false;
		}

		OutAstTree.Push(Node);
	}

	SkipWhitespace();
	if (Offset < Tokens.Num() && Tokens[Offset].Type != EExpressionLexerTokenType::EndOfFile)
	{
		OutAstTree.Empty();

		const FExpressionLexerToken& Remaining = Tokens[Offset];
		OutError = FString::Printf(TEXT("Unexpected token after expression: '%s' at [%d:%d]"), *Remaining.Value, Remaining.Position.X, Remaining.Position.Y);
		return false;
	}

	return true;
}

const TArray<FExpressionLexerToken>& FExpressionParser::GetTokens() const
{
	return Tokens;
}

void FExpressionParser::SkipWhitespace()
{
	while (Offset < Tokens.Num() && (Tokens[Offset].Type == EExpressionLexerTokenType::Whitespace || Tokens[Offset].Type == EExpressionLexerTokenType::Comment || Tokens[Offset].Type == EExpressionLexerTokenType::LineFeed))
	{
		++Offset;
	}
}

const FExpressionLexerToken& FExpressionParser::PeekToken()
{
	SkipWhitespace();
	if (Offset < Tokens.Num())
	{
		return Tokens[Offset];
	}

	static FExpressionLexerToken EOFToken(EExpressionLexerTokenType::EndOfFile, TEXT(""), FIntPoint(0));
	return EOFToken;
}

const FExpressionLexerToken& FExpressionParser::ConsumeToken()
{
	SkipWhitespace();
	const FExpressionLexerToken& Token = PeekToken();
	++Offset;
	return Token;
}

bool FExpressionParser::MatchType(EExpressionLexerTokenType InType)
{
	SkipWhitespace();
	if (PeekToken().Type == InType)
	{
		ConsumeToken();
		return true;
	}

	return false;
}

bool FExpressionParser::MatchValue(const FString& InValue)
{
	SkipWhitespace();
	if (PeekToken().Value == InValue)
	{
		ConsumeToken();
		return true;
	}

	return false;
}

bool FExpressionParser::ParseExpression(FAstNodePtr& OutNode, FString& OutError)
{
	return ParseLogicalOr(OutNode, OutError);
}

bool FExpressionParser::ParseLogicalOr(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseLogicalAnd(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("||")))
		{
			FAstNodePtr Right;
			if (!ParseLogicalAnd(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseLogicalAnd(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseEquality(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("&&")))
		{
			FAstNodePtr Right;
			if (!ParseEquality(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseEquality(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseComparision(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("==")) || MatchValue(TEXT("!=")))
		{
			FAstNodePtr Right;
			if (!ParseComparision(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseComparision(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseTerm(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("<")) || MatchValue(TEXT(">")) || MatchValue(TEXT("<=")) || MatchValue(TEXT(">=")))
		{
			FAstNodePtr Right;
			if (!ParseTerm(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseTerm(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseFactor(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("+")) || MatchValue(TEXT("-")))
		{
			FAstNodePtr Right;
			if (!ParseFactor(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseFactor(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseExponent(Node, OutError))
	{
		return false;
	}

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("*")) || MatchValue(TEXT("/")) || MatchValue(TEXT("%")))
		{
			FAstNodePtr Right;
			if (!ParseExponent(Right, OutError))
			{
				return false;
			}

			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseExponent(FAstNodePtr& OutNode, FString& OutError)
{
	FAstNodePtr Node;
	if (!ParseUnary(Node, OutError))
	{
		return false;
	}

	if (MatchValue(TEXT("^")))
	{
		const FExpressionLexerToken& Token = Tokens[Offset - 1];
		FAstNodePtr Right;
		if (!ParseExponent(Right, OutError))
		{
			return false;
		}

		Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
	}

	OutNode = Node;
	return true;
}

bool FExpressionParser::ParseUnary(FAstNodePtr& OutNode, FString& OutError)
{
	const FExpressionLexerToken& Token = PeekToken();
	if (MatchValue(TEXT("-")) || MatchValue(TEXT("!")) || MatchValue(TEXT("+")) || MatchValue(TEXT("~")))
	{
		FAstNodePtr Operand;
		if (!ParseUnary(Operand, OutError))
		{
			return false;
		}

		OutNode = MakeShared<FAstNodeUnaryOp>(Token.Value, Operand);
		return true;
	}

	return ParsePrimary(OutNode, OutError);
}

bool FExpressionParser::ParsePrimary(FAstNodePtr& OutNode, FString& OutError)
{
	const FExpressionLexerToken& Token = PeekToken();

	switch (Token.Type)
	{
	case EExpressionLexerTokenType::Number:
	case EExpressionLexerTokenType::Float:
		{
			ConsumeToken();
			OutNode = MakeShared<FAstNodeNumber>(Token.Value);
			return true;
		}
	case EExpressionLexerTokenType::String:
	case EExpressionLexerTokenType::CharLiteral:
		{
			ConsumeToken();
			OutNode = MakeShared<FAstNodeString>(Token.Value);
			return true;
		}
	case EExpressionLexerTokenType::Identifier:
		{
			FString Name = Token.Value;
			ConsumeToken();
			if (MatchType(EExpressionLexerTokenType::LeftParen))
			{
				return ParseFunctionCall(Name, OutNode, OutError);
			}
			else
			{
				OutNode = MakeShared<FAstNodeVariable>(Name);
				return true;
			}
		}
	case EExpressionLexerTokenType::LeftParen:
		{
			ConsumeToken();
			FAstNodePtr Expr;
			if (!ParseExpression(Expr, OutError))
			{
				return false;
			}

			if (!MatchType(EExpressionLexerTokenType::RightParen))
			{
				throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Expected ')'")));
				OutError = TEXT("Expected ')'");
				return false;
			}

			OutNode = Expr;
			return true;
		}
	case EExpressionLexerTokenType::LeftBrace:
		{
			ConsumeToken();
			FAstNodePtr Content;
			if (!ParsePrimary(Content, OutError))
			{
				return false;
			}

			if (Content->GetType() != EAstNodeType::Variable)
			{
				OutError = TEXT("Excepted variable in braces");
				return false;
			}
			else if (!MatchType(EExpressionLexerTokenType::RightBrace))
			{
				OutError = TEXT("Excepted right brace after variable");
				return false;
			}
			else
			{
				TSharedPtr<FAstNodeVariable> VariableNode = StaticCastSharedPtr<FAstNodeVariable>(Content);
				OutNode = MakeShared<FAstNodeVariable>(VariableNode->Name, true);
				return true;
			}
		}
	default:
		{
			OutError = TEXT("Unexpected token in primary expression");
			return false;
		}
	}
}

bool FExpressionParser::ParseFunctionCall(const FString& InName, FAstNodePtr& OutNode, FString& OutError)
{
	TArray<FAstNodePtr> Args;

	// 检查是否为空参数列表 ()
	if (MatchType(EExpressionLexerTokenType::RightParen))
	{
		OutNode = MakeShared<FAstNodeFunctionCall>(InName, Args);
		return true;
	}

	// 解析参数列表
	do
	{
		FAstNodePtr NewArgNode;
		if (!ParseExpression(NewArgNode, OutError))
		{
			return false;
		}

		Args.Add(NewArgNode);

		// 检查是否结束
		if (PeekToken().Type == EExpressionLexerTokenType::RightParen)
		{
			break;
		}

		// 必须有逗号分隔参数
		if (!MatchType(EExpressionLexerTokenType::Comma))
		{
			OutError = FString::Printf(TEXT("Expected ',' or ')' after argument %d of function '%s'"), Args.Num(), *InName);
			return false;
		}
	} while (true);

	// 确保以右括号结束
	if (!MatchType(EExpressionLexerTokenType::RightParen))
	{
		OutError = FString::Printf(TEXT("Expected ')' after function arguments for '%s' at [%d:%d]"), *InName, PeekToken().Position.X, PeekToken().Position.Y);
		return false;
	}

	OutNode = MakeShared<FAstNodeFunctionCall>(InName, Args);
	return true;
}
