#pragma once

#include "CoreMinimal.h"

/** Case sensitive hashing function for TMap */
template <typename ValueType>
struct FCaseSensitiveStringMapFuncs : BaseKeyFuncs<ValueType, FString, /*bInAllowDuplicateKeys*/ false>
{
	static FORCEINLINE const FString& GetSetKey(const TPair<FString, ValueType>& Element)
	{
		return Element.Key;
	}

	static FORCEINLINE bool Matches(const FString& A, const FString& B)
	{
		return A.Equals(B, ESearchCase::CaseSensitive);
	}

	static FORCEINLINE uint32 GetKeyHash(const FString& Key)
	{
		return FCrc::StrCrc32<TCHAR>(*Key);
	}
};

/**
 * Usage:
 *	1.TMap<FString, STRING_CASE_SENSITIVE_DEFINE(ValueType)> Map;
 *	2.TMap<FString, ValueType, FDefaultSetAllocator, FCaseSensitiveStringMapFuncs<ValueType>>
*/
#define STRING_CASE_SENSITIVE_DEFINE(ValueType) ValueType, FDefaultSetAllocator, FCaseSensitiveStringMapFuncs<ValueType>
