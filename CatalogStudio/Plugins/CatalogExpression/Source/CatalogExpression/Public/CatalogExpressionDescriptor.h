#pragma once
#include "CoreMinimal.h"
#include "CatalogExpressionDescriptor.generated.h"

UENUM(BlueprintType)
enum class EExpressionFunctionType : uint8
{
	Unknow = 0,
	If,				// IF(EXPR, A, B)
	Condition,		// COND(EXP, A, B)
	Max,			// MAX(A, B)
	Min,			// MIN(A, B)
	Sin,			// SIN(Val)
	Cos,			// COS(Val)
	Tan,			// TAN(Val)
	ArcSin,			// ASIN(Val)
	ArcCos,			// ACOS(Val)
	ArcTan,			// ATAN(Val)
	ArcTan2,		// ATAN2(A, B)
	Sqrt,			// SQRT(Val)
	Abs,			// ABS(Val)
	Combine,		// COMB(A, B)
	Power,			// POW(A, B)
	Round,			// ROUND(Val)  round half away from zero
	Ceil,			// CEIL(Val)
	Floor,			// FLOOR(Val)
	ToDeg,			// TODEG(Val)
	ToRad,			// TORAD(Val)
	Pi,				// PI()
	GridSnap,		// GRIDSNAP(Val/EXP, GridVal)
	MaxType
};

UENUM(BlueprintType)
enum class EExpressionMathOperatorType : uint8
{
	Unknow = 0,
	Add,			// + [1 + 1 -> 2] [+100 -> 0 + 100]
	Minus,			// - [1 - 1 -> 0] [-100 -> 0 - 100]
	Multiply,		// * [2 * 2 -> 4]
	Divide,			// / [2 / 2 -> 1]
	Negation,		// ! [!1 -> 0, !0 -> 1]
	BitReversal,	// ~ [Bit -> 0001, 0101, ~Bit -> 1110, 1010]
	Equal,			// == [1 == 1 -> 1, "Equal" == "Equal" -> 1, "Equal" == "equal" -> 0]
	NotEqual,		// != [1 != 1 -> 0, "Equal" != "Equal" -> 0, "Equal" != "equal" -> 1]
	Less,			// < [1 < 2 -> 1, 2 < 2 -> 0]
	LessOrEqual,	// <= [1 <= 2 -> 1, 2 <= 2 -> 1]
	Greater,		// > [2 > 1 -> 1, 2 > 2 -> 0]
	GreaterOrEqual,	// >= [2 >= 1 -> 1, 2 >= 2 -> 1]
	And,			// && [1 > 0 && 2 > 1 -> 1]
	Or,				// || [1 > 1 || 2 > 1 -> 1]
	BitAnd,			// & [A -> 0001, B -> 0001, A & B -> 1]
	BitOr,			// | [A -> 0000, B -> 0001, A | B -> 1]
	Power,			// ^ [2 ^ 3 -> 8]
	PowerAssign,	// ^= [2 ^= 3 -> 8]
	Mod,			// % [FDecimalMath::RemainderCustom]
	MaxType
};

USTRUCT(BlueprintType)
struct CATALOGEXPRESSION_API FCatalogExpressionDescriptor
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CatalogExpression | Descriptor")
	bool bIsFunctionCall;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CatalogExpression | Descriptor")
	uint8 Type;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CatalogExpression | Descriptor")
	FText Operator;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CatalogExpression | Descriptor")
	FText DisplayText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CatalogExpression | Descriptor")
	FText ToolTip;

	FCatalogExpressionDescriptor()
	{

	}

	FCatalogExpressionDescriptor(bool bInIsFunctionCall, EExpressionFunctionType InType, const FText& InOperator, const FText& InDisplayText, const FText& InToolTip)
		: bIsFunctionCall(bInIsFunctionCall)
		, Type(static_cast<uint8>(InType))
		, Operator(InOperator)
		, DisplayText(InDisplayText)
		, ToolTip(InToolTip)
	{
		
	}

	FCatalogExpressionDescriptor(bool bInIsFunctionCall, EExpressionMathOperatorType InType, const FText& InOperator, const FText& InDisplayText, const FText& InToolTip)
		: bIsFunctionCall(bInIsFunctionCall)
		, Type(static_cast<uint8>(InType))
		, Operator(InOperator)
		, DisplayText(InDisplayText)
		, ToolTip(InToolTip)
	{
		
	}
	bool operator ==(const FCatalogExpressionDescriptor& Other)
	{
		return bIsFunctionCall == Other.bIsFunctionCall
			&& Type == Other.Type
			&& Operator.EqualTo(Other.Operator)
			&& DisplayText.EqualTo(Other.Operator)
			&& ToolTip.EqualTo(Other.ToolTip);
	 }
};
