// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Lexer/ExpressionLexerToken.h"
#include "CaseSensitiveKeyFuncs.h"
#include "CatalogExpressionDescriptor.h"
#include "CatalogExpressionFunctionLibrary.generated.h"

struct FAstNode;

/**
 * 
 */
UCLASS()
class CATALOGEXPRESSION_API UCatalogExpressionFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintPure, Category = "CatalogExpression | Lexer | Token")
	static FString GetTokenTypeDisplayName(EExpressionLexerTokenType InType);

	UFUNCTION(BlueprintPure, Category = "CatalogExpression | Parser")
	static EExpressionFunctionType GetExpressionFunctionType(const FString& InFunctionName);

	static FString RemoveQuoteMarkFromString(FString InValue);

	static bool CheckStringIsNumeric(const FString& Source);

	static bool CheckStringIsInteger(const FString& Source);
	
	static bool EvaluateExpressionNode(const TSharedPtr<FAstNode>& Node, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static FText GetFunctionTypeOperatorText(EExpressionFunctionType Type);
	static FText GetFunctionTypeDisplayText(EExpressionFunctionType Type);
	static FText GetFunctionTypeToolTip(EExpressionFunctionType Type);

	static FText GetMathOperatorText(EExpressionMathOperatorType Type);
	static FText GetMathOperatorDisplayText(EExpressionMathOperatorType Type);
	static FText GetMathOperatorToolTip(EExpressionMathOperatorType Type);
	
	static TArray<FCatalogExpressionDescriptor> CollectAllDescriptors();

protected:
	static bool EvaluateUnaryOperator(const FString& Operator, const FString& Operand, FString& OutResult, FString& OutError);
	static bool EvaluateBinaryOperator(const FString& Left, const FString& Operator, const FString& Right, FString& OutResult, FString& OutError);
	
	static bool EvaluateFunctionCall_If(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Condition(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Max(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Min(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	
	static bool EvaluateFunctionCall_Sin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Cos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Tan(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static bool EvaluateFunctionCall_ArcSin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_ArcCos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_ArcTan(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_ArcTan2(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static bool EvaluateFunctionCall_Sqrt(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Abs(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Combine(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static bool EvaluateFunctionCall_Power(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Round(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Ceil(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_Floor(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_ToDeg(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);
	static bool EvaluateFunctionCall_ToRad(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static bool EvaluateFunctionCall_Pi(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

	static bool EvaluateFunctionCall_GridSnap(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FString& OutResult, FString& OutError);

public:
	// Test functions
	UFUNCTION(BlueprintCallable, Category = "CatalogExpression | Lexer | Token | Test")
	static void TestCatalogExpression();

	static void PrintAstNode(const TSharedPtr<FAstNode>& Node, int32 IndentLevel = 0);
};
