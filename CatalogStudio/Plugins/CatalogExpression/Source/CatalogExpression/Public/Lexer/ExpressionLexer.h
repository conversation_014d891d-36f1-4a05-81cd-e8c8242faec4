// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ExpressionLexerToken.h"

enum class EExpressionLexerState : uint8
{
	ELS_Start = 0,		// 起始状态
	ELS_Number,			// 整数
	ELS_Float,			// 浮点数
	ELS_FloatExponent,	// 科学计数法
	ELS_CharLiteral,	// 字符常量
	ELS_String,			// 字符串
	ELS_StringEscape,	// 字符串中的转义符
	ELS_Identifier,		// 标识符
	ELS_Comment,		// 注释
	ELS_Operator,		// 运算符
	ELS_Whitespace		// 空白符
};

/**
 * 
 */
class CATALOGEXPRESSION_API FExpressionLexer
{
public:
	FExpressionLexer();
	virtual ~FExpressionLexer() = default;

	const FString& GetContent() const;
	void SetContent(const FString& InContent);

	void Reset();

	bool NextToken(FExpressionLexerToken& OutToken, FString& OutError);
	bool Tokenize(TArray<FExpressionLexerToken>& OutTokens, FString& OutError);

protected:
	bool IsOperator(const TCHAR& InChar) const;
	bool IsLetter(const TCHAR& InChar) const;
	bool IsLetterOrNumber(const TCHAR& InChar) const;

	bool CurrentChar(TCHAR& OutChar) const;
	void Advance();
	bool IsAtEnd() const;

protected:
	int32 Offset;
	FString Content;
};
