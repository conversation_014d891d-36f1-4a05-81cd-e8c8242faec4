// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

enum class EAstNodeType : uint8
{
	Unknow = 0,
	Number,
	String,
	Variable,
	UnaryOp,
	BinaryOp,
	FunctionCall,
	Comment
};

struct CATALOGEXPRESSION_API FAstNode
{
	virtual ~FAstNode() = default;

	virtual EAstNodeType GetType() const { return EAstNodeType::Unknow; }
};

typedef TSharedPtr<FAstNode> FAstNodePtr;

struct CATALOGEXPRESSION_API FAstNodeNumber : public FAstNode
{
	FString Value;
	explicit FAstNodeNumber(const FString& InValue) : Value(InValue) {}

	virtual EAstNodeType GetType() const override { return EAstNodeType::Number; }
};

struct CATALOGEXPRESSION_API FAstNodeString : public FAstNode
{
	FString Value;
	explicit FAstNodeString(const FString& InValue) : Value(InValue) {}

	virtual EAstNodeType GetType() const override { return EAstNodeType::String; }
};

struct CATALOGEXPRESSION_API FAstNodeVariable : public FAstNode
{
	FString Name;
	bool bLocalReference;
	explicit FAstNodeVariable(const FString& InName, bool bInLocalReference = false) : Name(InName), bLocalReference(bInLocalReference) {}

	virtual EAstNodeType GetType() const override { return EAstNodeType::Variable; }
};

struct CATALOGEXPRESSION_API FAstNodeUnaryOp : public FAstNode
{
	FString Operator;
	FAstNodePtr Operand;
	FAstNodeUnaryOp(const FString& InOp, const FAstNodePtr& InOperand)
		: Operator(InOp), Operand(InOperand)
	{}

	virtual EAstNodeType GetType() const override { return EAstNodeType::UnaryOp; }
};

struct CATALOGEXPRESSION_API FAstNodeBinaryOp : public FAstNode
{
	FAstNodePtr LeftOperand;
	FString Operator;
	FAstNodePtr RightOperand;
	FAstNodeBinaryOp(const FAstNodePtr& InLeft, const FString& InOp, const FAstNodePtr& InRight)
		: LeftOperand(InLeft), Operator(InOp), RightOperand(InRight)
	{}

	virtual EAstNodeType GetType() const override { return EAstNodeType::BinaryOp; }
};

struct CATALOGEXPRESSION_API FAstNodeFunctionCall : public FAstNode
{
	FString Name;
	TArray<FAstNodePtr> Arguments;
	FAstNodeFunctionCall(const FString& InName, const TArray<FAstNodePtr>& InArguments)
		: Name(InName), Arguments(InArguments)
	{}

	virtual EAstNodeType GetType() const override { return EAstNodeType::FunctionCall; }
};

struct CATALOGEXPRESSION_API FAstNodeComment : public FAstNode
{
	FString Content;
	FIntPoint Position;
	explicit FAstNodeComment(const FString& InContent, const FIntPoint& InPosition)
		: Content(InContent), Position(InPosition)
	{}

	virtual EAstNodeType GetType() const override { return EAstNodeType::Comment; }
};
