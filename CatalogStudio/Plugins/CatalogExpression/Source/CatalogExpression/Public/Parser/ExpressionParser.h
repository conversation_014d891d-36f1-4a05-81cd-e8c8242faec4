// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "Lexer/ExpressionLexerToken.h"
#include "AstNode.h"

/**
 * 
 */
class CATALOGEXPRESSION_API FExpressionParser
{
public:
	FExpressionParser();
	virtual ~FExpressionParser() = default;

	bool Parse(const FString& InContent, TArray<FAstNodePtr>& OutAstTree, FString& OutError);

	const TArray<FExpressionLexerToken>& GetTokens() const;

protected:
	void SkipWhitespace();

	const FExpressionLexerToken& PeekToken();
	const FExpressionLexerToken& ConsumeToken();

	bool MatchType(EExpressionLexerTokenType InType);
	bool MatchValue(const FString& InValue);

	bool ParseExpression(FAstNodePtr& OutNode, FString& OutError);
	bool ParseLogicalOr(FAstNodePtr& OutNode, FString& OutError);
	bool ParseLogicalAnd(FAstNodePtr& OutNode, FString& OutError);
	bool ParseEquality(FAstNodePtr& OutNode, FString& OutError);
	bool ParseComparision(FAstNodePtr& OutNode, FString& OutError);
	bool ParseTerm(FAstNodePtr& OutNode, FString& OutError);
	bool ParseFactor(FAstNodePtr& OutNode, FString& OutError);
	bool ParseExponent(FAstNodePtr& OutNode, FString& OutError);
	bool ParseUnary(FAstNodePtr& OutNode, FString& OutError);
	bool ParsePrimary(FAstNodePtr& OutNode, FString& OutError);
	bool ParseFunctionCall(const FString& InName, FAstNodePtr& OutNode, FString& OutError);
	
protected:
	int32 Offset;
	TArray<FExpressionLexerToken> Tokens;
};
