// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.IO;

public class EasyDXF : ModuleRules
{
	private string ThirdPartyDir
	{
		get
		{
			return Path.GetFullPath(Path.Combine(ModuleDirectory, "../../ThirdParty/LibDXF"));
		}
	}
	
	public EasyDXF(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
			);
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				// ... add other public dependencies that you statically link with here ...
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"CoreUObject",
				"Projects"
				// ... add private dependencies that you statically link with here ...	
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);
		
		PublicIncludePaths.Add(Path.Combine(ThirdPartyDir, "include"));
		
		PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyDir, "lib/LibDXF.lib"));
		
		PublicDelayLoadDLLs.Add("LibDXF.dll");
		
		RuntimeDependencies.Add(Path.Combine(ThirdPartyDir, "bin/LibDXF.dll"));

		PublicDefinitions.Add("DXFLIB_DLL");

		bUseRTTI = true;
	}
}
