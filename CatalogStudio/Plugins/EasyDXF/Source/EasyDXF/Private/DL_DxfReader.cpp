#include "DL_DxfReader.h"
#include <cassert>
#include "dl_dxf.h"
#include "FormatConversion.h"

using namespace DxfLib;
using module = DL_Codes::module;
using ObjectType = DL_Codes::ObjectType;
DL_DxfReader::DL_DxfReader()
{
    currentObjectType = ObjectType::DXF_UNKNOWN;
    TargetModuletype = module::DXF_UNKNOW;
    Moduletype = module::DXF_UNKNOW;
    firstCall = false;
    groupCode = 0;
    groupCodeTmp = "";
    groupValue = "";
    values.clear();
}

DL_DxfReader::~DL_DxfReader()
{
    values.clear();
}
void DL_DxfReader::processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue)
{
    if (NewGroupCode == DL_STRGRP_START &&
        (NewGroupValue._Equal(DXF_VALUE_SECTION)
            || NewGroupValue._Equal(DXF_VALUE_ENDSECTION)
            || NewGroupValue._Equal(DXF_VALUE_EOF)
            )
        )
    {
        return;
    }
    //判断设置当前模块，直接返回
    if (NewGroupCode == DL_SECTION_NAME_CODE)
    {
        if (NewGroupValue._Equal(DXF_VALUE_HEADER))
        {
            Moduletype = module::DXF_HEADER;
            return ;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_CLASSES))
        {
            Moduletype = module::DXF_CLASSES;
            return ;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_TABLES))
        {
            Moduletype = module::DXF_TABLES;
            return ;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_BLOCKS))
        {
            Moduletype = module::DXF_BLOCKS;
            return ;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_ENTITIES))
        {
            Moduletype = module::DXF_ENTITIES;
            return ;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_OBJECTS))
        {
            Moduletype = module::DXF_OBJECTS;
            return ;
        }
    }
}


bool DL_DxfReader::LoadFile(const std::string& file)
{
    FILE* fp;
    firstCall = true;
    currentObjectType = ObjectType::DXF_UNKNOWN;

    errno_t Error = fopen_s(&fp, file.c_str(), "rt");
    if (Error == 0) {
        std::locale oldLocale = std::locale::global(std::locale("C"));	// use dot in numbers
        while (readDxfGroups(fp)) {}
        std::locale::global(oldLocale);
        fclose(fp);
        return true;
    }

    return false;
}

bool DL_DxfReader::readDxfGroups(FILE* fp)
{

    // Read one group of the DXF file and strip the lines:
    if (getStrippedLine(groupCodeTmp, DL_DXF_MAXLINE, fp) &&
        getStrippedLine(groupValue, DL_DXF_MAXLINE, fp, false)) {

        groupCode = (unsigned int)toInt(groupCodeTmp);
        //groupValue = Format::FormatConversion::utf8_to_gbk(groupValue);
        //groupValue = Format::FormatConversion::gbk_to_utf8(groupValue);
        processCodeValuePair(groupCode, groupValue);
        if (groupValue._Equal(DXF_VALUE_ENDSECTION) && TargetModuletype == Moduletype)
        {
            return false;
        }
    }

    return !feof(fp);
}

bool DL_DxfReader::getStrippedLine(std::string& s, unsigned int size, FILE* fp, bool stripSpace)
{
    if (!feof(fp)) {
        // The whole line in the file.  Includes space for NULL.
        char* wholeLine = new char[size];
        // Only the useful part of the line
        char* line;

        line = fgets(wholeLine, size, fp);

        if (line != NULL && line[0] != '\0') { // Evaluates to fgets() retval
            // line == wholeLine at this point.
            // Both guaranteed to be NULL terminated.

            // Strip leading whitespace and trailing CR/LF.
            stripWhiteSpace(&line, stripSpace);

            s = line;
            assert(size > s.length());
        }

        delete[] wholeLine; // Done with wholeLine

        return true;
    }
    else {
        s = "";
        return false;
    }
}

bool DL_DxfReader::LoadFile(std::istream& stream)
{
    if (stream.good()) {
        firstCall = true;
        currentObjectType = ObjectType::DXF_UNKNOWN;;
        while (readDxfGroups(stream)) {}
        return true;
    }
    return false;
}


bool DL_DxfReader::readDxfGroups(std::istream& stream)
{
    if (getStrippedLine(groupCodeTmp, DL_DXF_MAXLINE, stream) &&
        getStrippedLine(groupValue, DL_DXF_MAXLINE, stream, false)) {

        groupCode = (unsigned int)toInt(groupCodeTmp);
        //groupValue = Format::FormatConversion::utf8_to_gbk(groupValue);
        groupValue = Format::FormatConversion::gbk_to_utf8(groupValue);
        processCodeValuePair(groupCode, groupValue);
        //读完目标段直接返回
        if (groupValue._Equal(DXF_VALUE_ENDSECTION)&& TargetModuletype == Moduletype)
        {
            return false;
        }
    }
    return !stream.eof();
}

bool DL_DxfReader::getStrippedLine(std::string& s, unsigned int size, std::istream& stream, bool stripSpace)
{
    if (!stream.eof()) {
        // Only the useful part of the line
        char* line = new char[size + 1];
        char* oriLine = line;
        stream.getline(line, size);
        stripWhiteSpace(&line, stripSpace);
        s = line;
        assert(size > s.length());
        delete[] oriLine;
        return true;
    }
    else {
        s[0] = '\0';
        return false;
    }
}

bool DL_DxfReader::stripWhiteSpace(char** s, bool stripSpaces)
{
    size_t lastChar = strlen(*s) - 1;

    // Is last character CR or LF?
    while ((lastChar >= 0) &&
        (((*s)[lastChar] == 10) || ((*s)[lastChar] == 13) ||
            (stripSpaces && ((*s)[lastChar] == ' ' || ((*s)[lastChar] == '\t'))))) {
        (*s)[lastChar] = '\0';
        lastChar--;
    }

    // Skip whitespace, excluding \n, at beginning of line
    if (stripSpaces) {
        while ((*s)[0] == ' ' || (*s)[0] == '\t') {
            ++(*s);
        }
    }
    return ((*s) ? true : false);
}

void DL_DxfReader::GenerateAttributes()
{

    std::string layer = getStringValue(8, "0");

    int width;
    // Compatibility with qcad1:
    if (hasValue(39) && !hasValue(370)) {
        width = getIntValue(39, -1);
    }
    // since autocad 2002:
    else if (hasValue(370)) {
        width = getIntValue(370, -1);
    }
    // default to BYLAYER:
    else {
        width = -1;
    }

    int color;
    color = getIntValue(62, 256);
    int color24;
    color24 = getIntValue(420, -1);
    int handle;
    handle = getInt16Value(5, -1);

    std::string linetype = getStringValue(6, "BYLAYER");

    attributes = DL_Attributes(layer,                   // layer
        color,                   // color
        color24,                 // 24 bit color
        width,                   // width
        linetype,                // linetype
        handle);                 // handle
    attributes.setInPaperSpace((bool)getIntValue(67, 0));
    attributes.setLinetypeScale(getRealValue(48, 1.0));

}