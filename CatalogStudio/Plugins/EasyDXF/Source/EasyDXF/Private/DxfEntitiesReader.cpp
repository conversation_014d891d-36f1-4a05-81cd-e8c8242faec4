
#include "DxfEntitiesReader.h"

#include "DxfTempData.h"
#include "dl_codes.h"


DxfLib::FDxfEntitiesReader::FDxfEntitiesReader()
{
	TargetModuletype = DL_Codes::module::DXF_ENTITIES;
}

DxfLib::FDxfEntitiesReader::~FDxfEntitiesReader()
{
}

void DxfLib::FDxfEntitiesReader::processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue)
{
    DL_DxfReader::processCodeValuePair(NewGroupCode, NewGroupValue);

    if (Moduletype != DL_Codes::module::DXF_ENTITIES)
    {
        return;
    }

    //直接过滤 handle和Ownerhandle 写文件时重新分配
    if (NewGroupCode == 5 || NewGroupCode == 330)
    {
        return;
    }


    if (NewGroupCode == DL_ENTITY_TYPE_CODE) {
        // Add the previously parsed entity via creationInterface

        GenerateAttributes();
        switch (currentObjectType) {
        case ObjectType::DXF_BLOCK:
        {
            //一个Block起始 
            //StartBlock();
        }
        break;
        case ObjectType::DXF_ENTITY_POINT:
        {
            AddPoint();
        }
        break;
        case ObjectType::DXF_ENTITY_LINE:
        {
            AddLine();
        }
        break;
        case ObjectType::DXF_ENTITY_LWPOLYLINE:
        {
            AddLWPolyLine();
        }
        break;
        case ObjectType::DXF_ENTITY_ARC:
        {
            AddArc();
        }
        break;
        case ObjectType::DXF_ENTITY_CIRCLE:
        {
            AddCircle();
        }
        break;
        case ObjectType::DXF_ENTITY_ELLIPSE:
        {
            AddEllipse();
        }
        break;
        case ObjectType::DXF_ENTITY_ATTDEF:
        {
            //AddAttDef();
        }
        break;
        case ObjectType::DXF_ENTITY_TEXT:
        {
            //AddText();
        }
        break;
        case ObjectType::DXF_ENTITY_MTEXT:
            //AddMText();
            break;
        case ObjectType::DXF_ENDBLK:
        {
            //EndBlock();
            //一个Blcok结束
        }
        break;
        case ObjectType::DXF_ENTITY_HATCH:
        {
            //AddHatchData();
        }
        break;
        default:
            break;
        }
        values.clear();
        // Read Blocks:
        if (NewGroupValue == DXF_VALUE_BLOCK) {
            currentObjectType = ObjectType::DXF_BLOCK;
        }
        else if (NewGroupValue == DXF_VALUE_ENDBLK) {
            currentObjectType = ObjectType::DXF_ENDBLK;
        }
        // Read entities:
        else if (NewGroupValue._Equal(DXF_VALUE_ATTDEF))
        {
            currentObjectType = ObjectType::DXF_ENTITY_ATTDEF;
        }
        else if (NewGroupValue == DXF_VALUE_POINT) {
            currentObjectType = ObjectType::DXF_ENTITY_POINT;
        }
        else if (NewGroupValue == DXF_VALUE_LINE) {
            currentObjectType = ObjectType::DXF_ENTITY_LINE;
        }
        else if (NewGroupValue == DXF_VALUE_XLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_XLINE;
        }
        else if (NewGroupValue == DXF_VALUE_RAY) {
            currentObjectType = ObjectType::DXF_ENTITY_RAY;
        }
        else if (NewGroupValue == DXF_VALUE_POLYLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_POLYLINE;
        }
        else if (NewGroupValue == DXF_VALUE_LWPOLYLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_LWPOLYLINE;
        }
        else if (NewGroupValue == "VERTEX") {
            currentObjectType = ObjectType::DXF_ENTITY_VERTEX;
        }
        else if (NewGroupValue == "SPLINE") {
            currentObjectType = ObjectType::DXF_ENTITY_SPLINE;
        }
        else if (NewGroupValue == DXF_VALUE_ARC) {
            currentObjectType = ObjectType::DXF_ENTITY_ARC;
        }
        else if (NewGroupValue == "ELLIPSE") {
            currentObjectType = ObjectType::DXF_ENTITY_ELLIPSE;
        }
        else if (NewGroupValue == DXF_VALUE_CIRCLE) {
            currentObjectType = ObjectType::DXF_ENTITY_CIRCLE;
        }
        else if (NewGroupValue == DXF_VALUE_TEXT) {
            currentObjectType = ObjectType::DXF_ENTITY_TEXT;
        }
        else if (NewGroupValue == DXF_Value_MTEXT) {
            currentObjectType = ObjectType::DXF_ENTITY_MTEXT;
        }
        else if (NewGroupValue == "ARCALIGNEDTEXT") {
            currentObjectType = ObjectType::DXF_ENTITY_ARCALIGNEDTEXT;
        }
        else if (NewGroupValue == "DIMENSION") {
            currentObjectType = ObjectType::DXF_ENTITY_DIMENSION;
        }
        else if (NewGroupValue == "LEADER") {
            currentObjectType = ObjectType::DXF_ENTITY_LEADER;
        }
        else if (NewGroupValue == "HATCH") {
            currentObjectType = ObjectType::DXF_ENTITY_HATCH;
        }
        else if (NewGroupValue == "IMAGE") {
            currentObjectType = ObjectType::DXF_ENTITY_IMAGE;
        }
        else if (NewGroupValue == "IMAGEDEF") {
            currentObjectType = ObjectType::DXF_ENTITY_IMAGEDEF;
        }
        else if (NewGroupValue == "TRACE") {
            currentObjectType = ObjectType::DXF_ENTITY_TRACE;
        }
        else if (NewGroupValue == "SOLID") {
            currentObjectType = ObjectType::DXF_ENTITY_SOLID;
        }
        else if (NewGroupValue == "3DFACE") {
            currentObjectType = ObjectType::DXF_ENTITY_3DFACE;
        }
        else if (NewGroupValue == DXF_VLAUE_SEQEND) {
            currentObjectType = ObjectType::DXF_ENTITY_SEQEND;
        }
        else if (NewGroupValue == "XRECORD") {
            currentObjectType = ObjectType::DXF_XRECORD;
        }
        else {
            currentObjectType = ObjectType::DXF_UNKNOWN;
        }

    }
    bool bHandled = false;
    if (NewGroupCode < DL_DXF_MAXGROUPCODE)
    {
        //如果是多段线需要对顶点数据单独缓存
        if (currentObjectType == ObjectType::DXF_ENTITY_LWPOLYLINE)
        {
            bHandled = HandleLWPolylineData();
        }
        else if (currentObjectType == ObjectType::DXF_ENTITY_HATCH)
        {
            //bHandled = HandleHatchData();
        }
    }
    if (!bHandled)
    {
        values[NewGroupCode] = NewGroupValue;
    }
    return;
}


void DxfLib::FDxfEntitiesReader::AddArc()
{
    FDxfArcData* ArcData = new FDxfArcData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        getRealValue(40, 0.0),
        getRealValue(50, 0.0),
        getRealValue(51, 0.0),
        attributes);

    FDxfTempData::GetInstance()->AddEntity(TSharedPtr<FDxfArcData>( ArcData));
}

void DxfLib::FDxfEntitiesReader::AddCircle()
{
    FDxfCircleData* CircleData = new FDxfCircleData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        getRealValue(40, 0),
        attributes);

    FDxfTempData::GetInstance()->AddEntity(TSharedPtr<FDxfCircleData>(CircleData));
}

void DxfLib::FDxfEntitiesReader::AddEllipse()
{
    FDxfEllipseData* CircleData = new FDxfEllipseData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        FVector(getRealValue(11, 0), getRealValue(21, 0), getRealValue(31, 0)),
        getRealValue(40, 0),
        attributes);
    FDxfTempData::GetInstance()->AddEntity(TSharedPtr<FDxfEllipseData>(CircleData));
}

void DxfLib::FDxfEntitiesReader::AddPoint()
{
    FDxfPointData* PointData = new FDxfPointData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        attributes);

    //FDxfTempData::GetInstance()->AddEntity(PointData);
}

void DxfLib::FDxfEntitiesReader::AddLine()
{
    FDxfLineData* LineData = new FDxfLineData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        FVector(getRealValue(11, 0), getRealValue(21, 0), getRealValue(31, 0)),
        attributes);

    FDxfTempData::GetInstance()->AddEntity(TSharedPtr<FDxfLineData>(LineData));
}

void DxfLib::FDxfEntitiesReader::AddLWPolyLine()
{
    uint32 VertexSize = FMath::Min(MaxVertices, (uint32)VertexCaches.Num());
    FDxfLWPolyLineData* LWPolyLineData = new FDxfLWPolyLineData(
        MaxVertices,
        getIntValue(70, 0),
        attributes
    );
    for (int32 i = 0; i < VertexCaches.Num(); i++)
    {
        LWPolyLineData->AddPoint(VertexCaches[i]);
    }
    FDxfTempData::GetInstance()->AddEntity(TSharedPtr<FDxfLWPolyLineData>(LWPolyLineData));
}

bool DxfLib::FDxfEntitiesReader::HandleLWPolylineData()
{
    if (groupCode == 90) {
        MaxVertices = toInt(groupValue);
        if (MaxVertices > 0) {
            VertexCaches.Empty();
            VertexCaches.AddUninitialized(MaxVertices);
            for (auto &VIte : VertexCaches)
            {
                VIte = FVector::ZeroVector;
            }
        }
        VerTexCachesIndex = -1;
        return true;
    }
    else if (groupCode == 10)
    {
        VerTexCachesIndex++;
        VerTexCachesIndex = FMath::Min(VerTexCachesIndex, VertexCaches.Num());
        VertexCaches[VerTexCachesIndex].X = toReal(groupValue);
        return true;

    }
    else if (groupCode == 20)
    {
        VertexCaches[VerTexCachesIndex].Y = toReal(groupValue);
        return true;
    }
    else if (groupCode == 42)
    {
        VertexCaches[VerTexCachesIndex].Z = toReal(groupValue);
        return true;
    }

    return false;
}