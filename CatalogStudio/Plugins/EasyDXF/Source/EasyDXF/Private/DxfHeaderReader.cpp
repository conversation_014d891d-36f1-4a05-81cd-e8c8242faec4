#include "DxfHeaderReader.h"
#include "dl_codes.h"
#include "DxfTempData.h"

using namespace DxfLib;
using module = DL_Codes::module;
using ObjectType = DL_Codes::ObjectType;
FDxfHeaderReader::FDxfHeaderReader()
{
    TargetModuletype = module::DXF_HEADER;
}

void FDxfHeaderReader::processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue)
{

    DL_DxfReader::processCodeValuePair(NewGroupCode, NewGroupValue);
	
    if (Moduletype != module::DXF_HEADER)
    {
        return;
    }
    if (NewGroupCode == 9) {
        // Add the previously parsed entity via creationInterface
        switch (currentObjectType) {
        case ObjectType::DXF_SETTING:
            AddHeaderSetting();
            break;
        default:
            break;
        }
        values.clear();
        // Read DXF variable:
        if (groupValue[0] == '$') {
            currentObjectType = ObjectType::DXF_SETTING;
        }
        else {
            currentObjectType = ObjectType::DXF_UNKNOWN;
        }
    }
    values[NewGroupCode] = groupValue;
    return;
}

void FDxfHeaderReader::setVariableVector(const std::string& Name, double X, double Y, double Z, int Code)
{
    //FVector InVector(X, Y, Z);
    FVector InVector;
	InVector.X = X;
	InVector.Y = Y;
	InVector.Z = Z;
    FDxfHeadVectorItem* HeadItem = new FDxfHeadVectorItem(Name, InVector, Code);
    FDxfTempData::GetInstance()->GetDxfHeader()->AddHeaderItem(HeadItem);
}

void FDxfHeaderReader::setVariableString(const std::string& Name, const std::string& Value, int Code)
{
    FDxfHeadStringItem* HeadItem = new FDxfHeadStringItem(Name, Value, Code);
    FDxfTempData::GetInstance()->GetDxfHeader()->AddHeaderItem(HeadItem);
}

void FDxfHeaderReader::setVariableInt(const std::string& Name, int Value, int Code)
{
    FDxfHeadIntItem* HeadItem = new FDxfHeadIntItem(Name, Value, Code);
    FDxfTempData::GetInstance()->GetDxfHeader()->AddHeaderItem(HeadItem);
}

void FDxfHeaderReader::setVariableDouble(const std::string& Name, double Value, int Code)
{
    FDxfHeadDoubleItem* HeadItem = new FDxfHeadDoubleItem(Name, Value, Code);
    FDxfTempData::GetInstance()->GetDxfHeader()->AddHeaderItem(HeadItem);
}

void FDxfHeaderReader::AddHeaderSetting()
{

    int c = -1;
    std::string settingKey = getStringValue(9, "");
    if (settingKey == "")
    {
        return;
    }

    values.erase(9);
    std::map<int, std::string>::iterator it = values.begin();
    if (it != values.end()) {
        c = it->first;
    }
    if (c >= 0 && c <= 9) {
        setVariableString(settingKey, values[c], c);
    }
    // vector
    else if (c >= 10 && c <= 39) {
        if (c == 10) {
            setVariableVector(
                settingKey,
                getRealValue(c, 0.0),
                getRealValue(c + 10, 0.0),
                getRealValue(c + 20, NAN),
                c);
        }
    }
    // double
    else if (c >= 40 && c <= 59) {
        setVariableDouble(settingKey, getRealValue(c, 0.0), c);
    }
    // int
    else if (c >= 60 && c <= 99) {
        setVariableInt(settingKey, getIntValue(c, 0), c);
    }
    // misc
    else if (c >= 0) {
        setVariableString(settingKey, getStringValue(c, ""), c);
    }

}

