#include "DxfTablesReader.h"
#include "DxfTempData.h"

//DECLARE_LOG_CATEGORY_EXTERN(LogConstruction, Log, All)

using namespace DxfLib;
using module = DL_Codes::module;
using ObjectType = DL_Codes::ObjectType;
DxfLib::FDxfTablesReader::FDxfTablesReader()
{
    TargetModuletype = module::DXF_TABLES;
}
void DxfLib::FDxfTablesReader::processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue)
{
	DL_DxfReader::processCodeValuePair(NewGroupCode, NewGroupValue);

	if (Moduletype != module::DXF_TABLES)
	{
		return;
	}
    if (NewGroupCode == 0&&(NewGroupValue._Equal(DXF_VALUE_TABLE)))
    {
        return;
        
    }

    if (NewGroupCode == DL_ENTITY_TYPE_CODE) {
        // Add the previously parsed entity via creationInterface
        switch (currentObjectType) {
        case ObjectType::DXF_LINETYPE:
        {
            AddLineType();
        }
        break;
        case ObjectType::DXF_LAYER:
        {
            AddLayer();
        }
        break;
        case ObjectType::DXF_STYLE:
        {
            AddTextStyle();
        }
        break;
        case ObjectType::DXF_DIMSTYLE:
        {
            AddDimensionStyle();
        }
        break;
        case ObjectType::DXF_APPID:
        {
            AddAppID();
        }
            break;
        default:
            break;
        }
        values.clear();
        // Read DXF variable:
        if (NewGroupValue._Equal(DXF_VALUR_LINETYPE)) {
            currentObjectType = ObjectType::DXF_LINETYPE;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_LAYER))
        {
            currentObjectType = ObjectType::DXF_LAYER;
        }
        else if(NewGroupValue._Equal(DXF_VALUE_STYLE))
        {
            currentObjectType = ObjectType::DXF_STYLE;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_DIMSTYLE))
        {
            currentObjectType = ObjectType::DXF_DIMSTYLE;
        }
        else if (NewGroupValue._Equal(DXF_VALUE_APPID))
        {
            currentObjectType = ObjectType::DXF_APPID;
        }
        else {
            currentObjectType = ObjectType::DXF_UNKNOWN;
        }
    }
    if (NewGroupCode< DL_DXF_MAXGROUPCODE)
    {
        if (currentObjectType == ObjectType::DXF_LINETYPE)
        {
            //缓存LineTypePattern,暂时没有解析74Code
            if (NewGroupCode ==  49)
            {
                PatternsCache.push_back(toReal(NewGroupValue));
            }
        }
    }
    if (currentObjectType!= ObjectType::DXF_UNKNOWN)
    {
        values[NewGroupCode] = NewGroupValue;
    }
}

void DxfLib::FDxfTablesReader::AddLineType()
{
    std::string name = getStringValue(2, "");
    if (name.length() == 0) {
        return;
    }
    int numDashes = getIntValue(73, 0);
    //double dashes[numDashes];
    FDxfLineTypeData* LineType = new FDxfLineTypeData(
        // name:
        name,
        // description:
        getStringValue(3, ""),
        // flags
        getIntValue(70, 0),
        // pattern length:
        getRealValue(40, 0.0)
        // pattern:
        //dashes
    );
    LineType->SetPattern(PatternsCache);
    PatternsCache.clear();
    if (name != "By Layer" && name != "By Block" && name != "BYLAYER" && name != "BYBLOCK") {
        FDxfTempData::GetInstance()->GetDxfTables()->AddLineType(LineType);
    }
}

void DxfLib::FDxfTablesReader::AddLayer()
{
    
    GenerateAttributes();
    if (attributes.getColor() == 256 || attributes.getColor() == 0) {
        attributes.setColor(7);
    }
    if (attributes.getWidth() < 0) {
        attributes.setWidth(1);
    }

    std::string linetype = attributes.getLinetype();
    std::transform(linetype.begin(), linetype.end(), linetype.begin(), ::toupper);
    if (linetype == "BYLAYER" || linetype == "BYBLOCK") {
        attributes.setLinetype("CONTINUOUS");
    }

    // add layer
    std::string name = getStringValue(2, "");
    if (name.length() == 0) {
        return;
    }

    FDxfLayer* Layer = new FDxfLayer(name,getIntValue(70,0), attributes.getColor()<0, attributes);

    FDxfTempData::GetInstance()->GetDxfTables()->AddLayer(Layer);
}

void DxfLib::FDxfTablesReader::AddTextStyle()
{
    std::string name = getStringValue(2, "");
    if (name.length() == 0) {
        return;
    }
    std::string Handle = getStringValue(5,"");
    if (!Handle.empty())
    {
        AddStyleHandleCache(Handle, name);
    }
    FDxfStyleData* Style = new FDxfStyleData(        // name:
        name,
        // flags
        getIntValue(70, 0),
        // fixed text heigth:
        getRealValue(40, 0.0),
        // width factor:
        getRealValue(41, 0.0),
        // oblique angle:
        getRealValue(50, 0.0),
        // text generation flags:
        getIntValue(71, 0),
        // last height used:
        getRealValue(42, 0.0),
        // primart font file:
        getStringValue(3, ""),
        // big font file:
        getStringValue(4, "")
        );
    FDxfTempData::GetInstance()->GetDxfTables()->AddStyle(Style);
}

void DxfLib::FDxfTablesReader::AddAppID()
{
    std::string name = getStringValue(2, "");
    if (name.length() == 0) {
        return;
    }
    FDxfAppIDData* AppIdData = new FDxfAppIDData(
        getStringValue(2, ""),
        getIntValue(70, 0)
    );
    FDxfTempData::GetInstance()->GetDxfTables()->AddAppID(AppIdData);
}

void DxfLib::FDxfTablesReader::AddDimensionStyle()
{

    std::string StyleHandle =  getStringValue(340, "");
    std::string StyleName = GetStyleNameByHandle(StyleHandle);

    FDxfDimensionStyle* DimStyle = new FDxfDimensionStyle(
        getStringValue(2, ""),
        getStringValue(3, ""),
        getStringValue(4, ""),
        getIntValue(70, 0),
        getRealValue(40, 0.0),
        getRealValue(41, 0.0),
        getRealValue(42, 0.0),
        getRealValue(43, 0.0),
        getRealValue(44, 0.0),
        getRealValue(45, 0.0),
        getRealValue(46, 0.0),
        getRealValue(47, 0.0),
        getRealValue(48, 0.0),
        getRealValue(140, 0.0),
        getRealValue(141, 0.0),
        getRealValue(142, 0.0),
        getRealValue(143, 0.0),
        getRealValue(144, 0.0),
        getRealValue(145, 0.0),
        getRealValue(146, 0.0),
        getRealValue(147, 0.0),
        getRealValue(148, 0.0),
        getIntValue(71, -1),
        getIntValue(72, -1),
        getIntValue(73, -1),
        getIntValue(74, -1),
        getIntValue(75, -1),
        getIntValue(76, -1),
        getIntValue(77, -1),
        getIntValue(78, -1),
        getIntValue(79, -1),
        getIntValue(170, -1),
        getIntValue(171, -1),
        getIntValue(172, -1),
        getIntValue(173, -1),
        getIntValue(174, -1),
        getIntValue(175, -1),
        getIntValue(176, -1),
        getIntValue(177, -1),
        getIntValue(178, -1),
        getIntValue(179, -1),
        getIntValue(271, -1),
        getIntValue(272, -1),
        getIntValue(273, -1),
        getIntValue(274, -1),
        getIntValue(275, -1),
        getIntValue(276, -1),
        getIntValue(277, -1),
        getIntValue(278, -1),
        getIntValue(279, -1),
        getIntValue(280, -1),
        getIntValue(281, -1),
        getIntValue(282, -1),
        getIntValue(283, -1),
        getIntValue(284, -1),
        getIntValue(285, -1),
        getIntValue(286, -1),
        getIntValue(288, -1),
        getIntValue(289, -1),
        StyleName
    );

    FDxfTempData::GetInstance()->GetDxfTables()->AddDimendionStyle(DimStyle);
       /* 3    ,  
        4    ,
        40   ,0.0
        41   ,0.0
        42   ,0.0
        43   ,0.0
        44   ,0.0
        45   ,0.0
        46   ,0.0
        47   ,0.0
        48   ,0.0
        140  ,0.0
        141  ,0.0
        142  ,0.0
        143  ,0.0
        144  ,0.0
        145  ,0.0
        146  ,0.0
        147  ,0.0
        148  ,0.0
        71   ,0.0
        72   ,0.0
        73   ,0.0
        74   ,0.0
        75   ,0.0
        76   ,0.0
        77   ,0.0
        78   ,0.0
        79   ,0.0
        170  ,0.0
        171  ,0.0
        172  ,0.0
        173  ,0.0
        174  ,0.0
        175  ,0.0
        176  ,0.0
        177  ,0.0
        178  ,0.0
        179  ,0.0
        271  ,0.0
        272  ,0.0
        273  ,0.0
        274  ,0.0
        275  ,0.0
        276  ,0.0
        277  ,0.0
        278  ,0.0
        279  ,0.0
        280  ,0.0
        281  ,0.0
        282  ,0.0
        283  ,0.0
        284  ,0.0
        285  ,0.0
        286  ,0.0
        288  ,0.0
        289  ,0.0*/
}

void DxfLib::FDxfTablesReader::AddStyleHandleCache(const std::string& Handle, const std::string& StyleName)
{
    auto HandleCache = StyleHandleCache.find(Handle);

    if (HandleCache != StyleHandleCache.end())
    {
		//UE_LOG(LogConstruction, Warning, TEXT("字体名称重复，略过当前字体: %s"), *FString(StyleName.c_str()));
        return;
    }
    StyleHandleCache[Handle] = StyleName;
}

std::string DxfLib::FDxfTablesReader::GetStyleNameByHandle(const std::string& Handle)
{
    if (Handle.empty())
    {
        return "";
    }
    auto HandleCache = StyleHandleCache.find(Handle);
    if (HandleCache == StyleHandleCache.end())
    {
        return "";
       
    }
    return StyleHandleCache[Handle];
}
