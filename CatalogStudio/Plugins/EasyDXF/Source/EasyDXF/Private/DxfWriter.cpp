#include "DxfWriter.h"
#include "FormatConversion.h"


void DxfWriter::dxfString(int gc, const std::string& value) const
{
	//string FormatStr = Format::FormatConversion::gbk_to_utf8(value);
	//string FormatStr = Format::FormatConversion::utf8_to_gbk(value);
	std::string FormatStr = Format::FormatConversion::Utf8ToDxfUnicodeEscapes(value);
	DL_WriterA::dxfString(gc, FormatStr);

}

void DxfWriter::dxfString(int gc, const char* value) const
{
	//string FormatStr = Format::FormatConversion::gbk_to_utf8(value);
	//string FormatStr = Format::FormatConversion::utf8_to_gbk(value);
	std::string FormatStr = Format::FormatConversion::Utf8ToDxfUnicodeEscapes(value);
	DL_WriterA::dxfString(gc, FormatStr);
}
