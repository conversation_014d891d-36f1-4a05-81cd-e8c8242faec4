#include "DXFWriterHelper.h"
#include "dl_writer_ascii.h"
#include "DxfWriter.h"
#include "DxfTempData.h"
#include "FormatConversion.h"



using namespace DxfLib;
//using namespace rapidjson;


bool DxfLib::FDxfWriterHelper::ExportDxdFile(const FString& InPath)
{
	auto UTF8Str = StringCast<UTF8CHAR>(*InPath);

	std::string OutPath =  Format::FormatConversion::utf8_to_default(reinterpret_cast<const char*>( UTF8Str.Get()), UTF8Str.Length());
	//std::string OutPath02 = Format::FormatConversion::UE4_to_default(InPath);

	DxfWriter* Writer = new DxfWriter(OutPath.c_str());
	if (Writer->openFailed()) {
		delete Writer;
		UE_LOG(LogTemp, Error, TEXT("Export File Open Failed!"));
		return false;
	}
	//Header
	FDxfTempData::GetInstance()->GetDxfHeader()->WriteToDxfFile(*Writer);

	const FBlockSource* DefaultBlocks = FDxfTempData::GetInstance()->GetDxfBlocks()->GetBlockSource("");
	
	//Tables
	FDxfTempData::GetInstance()->GetDxfTables()->WriteToDxfFile(*Writer, DefaultBlocks);

	//Blocks
	Writer->sectionBlocks();
	if (DefaultBlocks)
	{
		DefaultBlocks->WriteToDxfFile(*Writer);
	}
	Writer->sectionEnd();

	//Entities
	Writer->sectionEntities();

	auto EntityDatas = FDxfTempData::GetInstance()->GetEntityDatas();
	for (auto EntityIte : EntityDatas)
	{
		if(!EntityIte)
		{
			continue;
		}

		EntityIte->WriteToDxfFile(*Writer);
	}

	Writer->sectionEnd();
	WriteObjects(*Writer);
	Writer->dxfEOF();
	Writer->close();
	delete Writer;
	return true;
}

void DxfLib::FDxfWriterHelper::WriteObjects(const DL_WriterA& DW)
{
	DW.dxfString(0, "SECTION");
	DW.dxfString(2, "OBJECTS");

	DW.dxfString(0, "DICTIONARY");
	DW.dxfHex(5, 0xC);
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(280, 0);
	DW.dxfInt(281, 1);
	DW.dxfString(3, "ACAD_GROUP");
	DW.dxfHex(350, 0xD);
	DW.dxfString(3, "ACAD_LAYOUT");
	DW.dxfHex(350, 0x1A);
	DW.dxfString(3, "ACAD_MLINESTYLE");
	DW.dxfHex(350, 0x17);
	DW.dxfString(3, "ACAD_PLOTSETTINGS");
	DW.dxfHex(350, 0x19);
	DW.dxfString(3, "ACAD_PLOTSTYLENAME");
	DW.dxfHex(350, 0xE);
	DW.dxfString(3, "AcDbVariableDictionary");
	int acDbVariableDictionaryHandle = DW.handle(350);
	//int acDbVariableDictionaryHandle = DW.getNextHandle();
	//DW.dxfHex(350, acDbVariableDictionaryHandle);
	//DW.incHandle();


	DW.dxfString(0, "DICTIONARY");
	DW.dxfHex(5, 0xD);
	//DW.handle();                                    // D
	//DW.dxfHex(330, 0xC);
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(280, 0);
	DW.dxfInt(281, 1);


	DW.dxfString(0, "ACDBDICTIONARYWDFLT");
	DW.dxfHex(5, 0xE);
	//dicId4 = DW.handle();                           // E
	//DW.dxfHex(330, 0xC);                       // C
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(281, 1);
	DW.dxfString(3, "Normal");
	DW.dxfHex(350, 0xF);
	//DW.dxfHex(350, DW.getNextHandle()+5);        // F
	DW.dxfString(100, "AcDbDictionaryWithDefault");
	DW.dxfHex(340, 0xF);
	//DW.dxfHex(340, DW.getNextHandle()+5);        // F


	DW.dxfString(0, "ACDBPLACEHOLDER");
	DW.dxfHex(5, 0xF);
	//DW.handle();                                    // F
	//DW.dxfHex(330, dicId4);                      // E


	DW.dxfString(0, "DICTIONARY");
	//dicId3 = DW.handle();                           // 17
	DW.dxfHex(5, 0x17);
	//DW.dxfHex(330, 0xC);                       // C
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(280, 0);
	DW.dxfInt(281, 1);
	DW.dxfString(3, "Standard");
	DW.dxfHex(350, 0x18);
	//DW.dxfHex(350, DW.getNextHandle()+5);        // 18


	DW.dxfString(0, "MLINESTYLE");
	DW.dxfHex(5, 0x18);
	//DW.handle();                                    // 18
	//DW.dxfHex(330, dicId3);                      // 17
	DW.dxfString(100, "AcDbMlineStyle");
	DW.dxfString(2, "STANDARD");
	DW.dxfInt(70, 0);
	DW.dxfString(3, "");
	DW.dxfInt(62, 256);
	DW.dxfReal(51, 90.0);
	DW.dxfReal(52, 90.0);
	DW.dxfInt(71, 2);
	DW.dxfReal(49, 0.5);
	DW.dxfInt(62, 256);
	DW.dxfString(6, "BYLAYER");
	DW.dxfReal(49, -0.5);
	DW.dxfInt(62, 256);
	DW.dxfString(6, "BYLAYER");


	DW.dxfString(0, "DICTIONARY");
	DW.dxfHex(5, 0x19);
	//DW.handle();                           // 17
	//DW.dxfHex(330, 0xC);                       // C
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(280, 0);
	DW.dxfInt(281, 1);


	DW.dxfString(0, "DICTIONARY");
	//dicId2 = DW.handle();                           // 1A
	DW.dxfHex(5, 0x1A);
	//DW.dxfHex(330, 0xC);
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(281, 1);
	DW.dxfString(3, "Layout1");
	DW.dxfHex(350, 0x1E);
	//DW.dxfHex(350, DW.getNextHandle()+2);        // 1E
	DW.dxfString(3, "Layout2");
	DW.dxfHex(350, 0x26);
	//DW.dxfHex(350, DW.getNextHandle()+4);        // 26
	DW.dxfString(3, "Model");
	DW.dxfHex(350, 0x22);
	//DW.dxfHex(350, DW.getNextHandle()+5);        // 22


	DW.dxfString(0, "LAYOUT");
	DW.dxfHex(5, 0x1E);
	//DW.handle();                                    // 1E
	//DW.dxfHex(330, dicId2);                      // 1A
	DW.dxfString(100, "AcDbPlotSettings");
	DW.dxfString(1, "");
	DW.dxfString(2, "none_device");
	DW.dxfString(4, "");
	DW.dxfString(6, "");
	DW.dxfReal(40, 0.0);
	DW.dxfReal(41, 0.0);
	DW.dxfReal(42, 0.0);
	DW.dxfReal(43, 0.0);
	DW.dxfReal(44, 0.0);
	DW.dxfReal(45, 0.0);
	DW.dxfReal(46, 0.0);
	DW.dxfReal(47, 0.0);
	DW.dxfReal(48, 0.0);
	DW.dxfReal(49, 0.0);
	DW.dxfReal(140, 0.0);
	DW.dxfReal(141, 0.0);
	DW.dxfReal(142, 1.0);
	DW.dxfReal(143, 1.0);
	DW.dxfInt(70, 688);
	DW.dxfInt(72, 0);
	DW.dxfInt(73, 0);
	DW.dxfInt(74, 5);
	DW.dxfString(7, "");
	DW.dxfInt(75, 16);
	DW.dxfReal(147, 1.0);
	DW.dxfReal(148, 0.0);
	DW.dxfReal(149, 0.0);
	DW.dxfString(100, "AcDbLayout");
	DW.dxfString(1, "Layout1");
	DW.dxfInt(70, 1);
	DW.dxfInt(71, 1);
	DW.dxfReal(10, 0.0);
	DW.dxfReal(20, 0.0);
	DW.dxfReal(11, 420.0);
	DW.dxfReal(21, 297.0);
	DW.dxfReal(12, 0.0);
	DW.dxfReal(22, 0.0);
	DW.dxfReal(32, 0.0);
	DW.dxfReal(14, 1.000000000000000E+20);
	DW.dxfReal(24, 1.000000000000000E+20);
	DW.dxfReal(34, 1.000000000000000E+20);
	DW.dxfReal(15, -1.000000000000000E+20);
	DW.dxfReal(25, -1.000000000000000E+20);
	DW.dxfReal(35, -1.000000000000000E+20);
	DW.dxfReal(146, 0.0);
	DW.dxfReal(13, 0.0);
	DW.dxfReal(23, 0.0);
	DW.dxfReal(33, 0.0);
	DW.dxfReal(16, 1.0);
	DW.dxfReal(26, 0.0);
	DW.dxfReal(36, 0.0);
	DW.dxfReal(17, 0.0);
	DW.dxfReal(27, 1.0);
	DW.dxfReal(37, 0.0);
	DW.dxfInt(76, 0);
	//DW.dxfHex(330, DW.getPaperSpaceHandle());    // 1B
	DW.dxfHex(330, 0x1B);


	DW.dxfString(0, "LAYOUT");
	DW.dxfHex(5, 0x22);
	//DW.handle();                                    // 22
	//DW.dxfHex(330, dicId2);                      // 1A
	DW.dxfString(100, "AcDbPlotSettings");
	DW.dxfString(1, "");
	DW.dxfString(2, "none_device");
	DW.dxfString(4, "");
	DW.dxfString(6, "");
	DW.dxfReal(40, 0.0);
	DW.dxfReal(41, 0.0);
	DW.dxfReal(42, 0.0);
	DW.dxfReal(43, 0.0);
	DW.dxfReal(44, 0.0);
	DW.dxfReal(45, 0.0);
	DW.dxfReal(46, 0.0);
	DW.dxfReal(47, 0.0);
	DW.dxfReal(48, 0.0);
	DW.dxfReal(49, 0.0);
	DW.dxfReal(140, 0.0);
	DW.dxfReal(141, 0.0);
	DW.dxfReal(142, 1.0);
	DW.dxfReal(143, 1.0);
	DW.dxfInt(70, 1712);
	DW.dxfInt(72, 0);
	DW.dxfInt(73, 0);
	DW.dxfInt(74, 0);
	DW.dxfString(7, "");
	DW.dxfInt(75, 0);
	DW.dxfReal(147, 1.0);
	DW.dxfReal(148, 0.0);
	DW.dxfReal(149, 0.0);
	DW.dxfString(100, "AcDbLayout");
	DW.dxfString(1, "Model");
	DW.dxfInt(70, 1);
	DW.dxfInt(71, 0);
	DW.dxfReal(10, 0.0);
	DW.dxfReal(20, 0.0);
	DW.dxfReal(11, 12.0);
	DW.dxfReal(21, 9.0);
	DW.dxfReal(12, 0.0);
	DW.dxfReal(22, 0.0);
	DW.dxfReal(32, 0.0);
	DW.dxfReal(14, 0.0);
	DW.dxfReal(24, 0.0);
	DW.dxfReal(34, 0.0);
	DW.dxfReal(15, 0.0);
	DW.dxfReal(25, 0.0);
	DW.dxfReal(35, 0.0);
	DW.dxfReal(146, 0.0);
	DW.dxfReal(13, 0.0);
	DW.dxfReal(23, 0.0);
	DW.dxfReal(33, 0.0);
	DW.dxfReal(16, 1.0);
	DW.dxfReal(26, 0.0);
	DW.dxfReal(36, 0.0);
	DW.dxfReal(17, 0.0);
	DW.dxfReal(27, 1.0);
	DW.dxfReal(37, 0.0);
	DW.dxfInt(76, 0);
	//DW.dxfHex(330, DW.getModelSpaceHandle());    // 1F
	DW.dxfHex(330, 0x1F);


	DW.dxfString(0, "LAYOUT");
	//DW.handle();                                    // 26
	DW.dxfHex(5, 0x26);
	//DW.dxfHex(330, dicId2);                      // 1A
	DW.dxfString(100, "AcDbPlotSettings");
	DW.dxfString(1, "");
	DW.dxfString(2, "none_device");
	DW.dxfString(4, "");
	DW.dxfString(6, "");
	DW.dxfReal(40, 0.0);
	DW.dxfReal(41, 0.0);
	DW.dxfReal(42, 0.0);
	DW.dxfReal(43, 0.0);
	DW.dxfReal(44, 0.0);
	DW.dxfReal(45, 0.0);
	DW.dxfReal(46, 0.0);
	DW.dxfReal(47, 0.0);
	DW.dxfReal(48, 0.0);
	DW.dxfReal(49, 0.0);
	DW.dxfReal(140, 0.0);
	DW.dxfReal(141, 0.0);
	DW.dxfReal(142, 1.0);
	DW.dxfReal(143, 1.0);
	DW.dxfInt(70, 688);
	DW.dxfInt(72, 0);
	DW.dxfInt(73, 0);
	DW.dxfInt(74, 5);
	DW.dxfString(7, "");
	DW.dxfInt(75, 16);
	DW.dxfReal(147, 1.0);
	DW.dxfReal(148, 0.0);
	DW.dxfReal(149, 0.0);
	DW.dxfString(100, "AcDbLayout");
	DW.dxfString(1, "Layout2");
	DW.dxfInt(70, 1);
	DW.dxfInt(71, 2);
	DW.dxfReal(10, 0.0);
	DW.dxfReal(20, 0.0);
	DW.dxfReal(11, 12.0);
	DW.dxfReal(21, 9.0);
	DW.dxfReal(12, 0.0);
	DW.dxfReal(22, 0.0);
	DW.dxfReal(32, 0.0);
	DW.dxfReal(14, 0.0);
	DW.dxfReal(24, 0.0);
	DW.dxfReal(34, 0.0);
	DW.dxfReal(15, 0.0);
	DW.dxfReal(25, 0.0);
	DW.dxfReal(35, 0.0);
	DW.dxfReal(146, 0.0);
	DW.dxfReal(13, 0.0);
	DW.dxfReal(23, 0.0);
	DW.dxfReal(33, 0.0);
	DW.dxfReal(16, 1.0);
	DW.dxfReal(26, 0.0);
	DW.dxfReal(36, 0.0);
	DW.dxfReal(17, 0.0);
	DW.dxfReal(27, 1.0);
	DW.dxfReal(37, 0.0);
	DW.dxfInt(76, 0);
	//DW.dxfHex(330, DW.getPaperSpace0Handle());   // 23
	DW.dxfHex(330, 0x23);

	DW.dxfString(0, "DICTIONARY");
	//DW.dxfHex(5, 0x2C);
	//dicId5 =
	DW.dxfHex(5, acDbVariableDictionaryHandle);
	//DW.handle();                           // 2C
	//DW.dxfHex(330, 0xC);                       // C
	DW.dxfString(100, "AcDbDictionary");
	DW.dxfInt(281, 1);
	DW.dxfString(3, "DIMASSOC");
	//DW.dxfHex(350, 0x2F);
	DW.dxfHex(350, DW.getNextHandle() + 1);        // 2E
	DW.dxfString(3, "HIDETEXT");
	//DW.dxfHex(350, 0x2E);
	DW.dxfHex(350, DW.getNextHandle());        // 2D


	DW.dxfString(0, "DICTIONARYVAR");
	//DW.dxfHex(5, 0x2E);
	DW.handle();                                    // 2E
	//DW.dxfHex(330, dicId5);                      // 2C
	DW.dxfString(100, "DictionaryVariables");
	DW.dxfInt(280, 0);
	DW.dxfInt(1, 2);


	DW.dxfString(0, "DICTIONARYVAR");
	//DW.dxfHex(5, 0x2D);
	DW.handle();                                    // 2D
	//DW.dxfHex(330, dicId5);                      // 2C
	DW.dxfString(100, "DictionaryVariables");
	DW.dxfInt(280, 0);
	DW.dxfInt(1, 1);

	DW.sectionEnd();
}

