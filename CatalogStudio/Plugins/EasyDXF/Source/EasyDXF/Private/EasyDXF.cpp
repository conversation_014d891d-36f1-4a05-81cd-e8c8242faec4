// Copyright Epic Games, Inc. All Rights Reserved.

#include "EasyDXF.h"

#include "Interfaces/IPluginManager.h"

#define LOCTEXT_NAMESPACE "FEasyDXFModule"

void FEasyDXFModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	const FString PluginDir = IPluginManager::Get().FindPlugin("EasyDXF")->GetBaseDir();
	LibDXFHandle = FPlatformProcess::GetDllHandle(*FPaths::Combine(PluginDir, "ThirdParty/LibDXF/bin/LibDXF.dll"));
	if (LibDXFHandle == nullptr)
	{
		UE_LOG(LogTemp, Error, TEXT("%s - Failed to load dependency dll 'LibDXF.dll'"), __FUNCTIONW__);
		return;
	}
}

void FEasyDXFModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	if (LibDXFHandle != nullptr)
	{
		FPlatformProcess::FreeDllHandle(LibDXFHandle);
		LibDXFHandle = nullptr;
	}
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FEasyDXFModule, EasyDXF)