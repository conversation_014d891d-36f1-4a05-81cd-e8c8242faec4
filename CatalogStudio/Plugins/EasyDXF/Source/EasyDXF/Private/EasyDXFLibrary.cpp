// Fill out your copyright notice in the Description page of Project Settings.


#include "EasyDXFLibrary.h"
#include "DxfEntitiesReader.h"
#include "FormatConversion.h"

void FEasyDXFLibrary::InitializeCAD()
{
	DxfLib::FDxfTempData::GetInstance()->CreateInstance();
}

void FEasyDXFLibrary::DeinitializeCAD()
{
	DxfLib::FDxfTempData::GetInstance()->DeleteInstance();
}

void FEasyDXFLibrary::LoadDXFEntities(const FString& InDXFFile)
{
	TSharedPtr<DxfLib::FDxfEntitiesReader> EntitiesReader = MakeShared<DxfLib::FDxfEntitiesReader>();
	EntitiesReader->LoadFile(Format::FormatConversion::UE4_to_default(InDXFFile));
}

void FEasyDXFLibrary::GetLWPolyLineEntities(TArray<DxfLib::FDxfLWPolyLineData*>& OutLWPolyLines)
{
	const TArray< TSharedPtr<DxfLib::FDxfBaseEntityData>>& AllEntityDatas = DxfLib::FDxfTempData::GetInstance()->GetEntityDatas();
	for (auto DataIte : AllEntityDatas)
	{
		DxfLib::FDxfLWPolyLineData* DataPtr = dynamic_cast<DxfLib::FDxfLWPolyLineData*>(DataIte.Get());
		if (DataPtr != nullptr)
		{
			OutLWPolyLines.Add(DataPtr);
		}
	}
}

void FEasyDXFLibrary::GetLWPolyLineEntities(TArray<FVector>& OutPointInUE4s, TArray<double>& OutBulges)
{
	TArray<DxfLib::FDxfLWPolyLineData*> OutLWPolyLines;
	GetLWPolyLineEntities(OutLWPolyLines);

	TArray<FVector> PointInUE4s;
	TArray<double>  Bulges;
	for (auto PointIte : OutLWPolyLines[0]->GetPoints())
	{
		FVector UE4Point = FEasyDXFLibrary::CADToUE4(PointIte);
		PointInUE4s.Add(FVector(UE4Point.X, UE4Point.Y, 0.0f));
		Bulges.Add(UE4Point.Z);
	}
}

void FEasyDXFLibrary::GetEllipseEntities(TArray<DxfLib::FDxfEllipseData*>& OutEllipses)
{
	const TArray< TSharedPtr<DxfLib::FDxfBaseEntityData>>& AllEntityDatas = DxfLib::FDxfTempData::GetInstance()->GetEntityDatas();
	for (auto DataIte : AllEntityDatas)
	{
		DxfLib::FDxfEllipseData* DataPtr = dynamic_cast<DxfLib::FDxfEllipseData*>(DataIte.Get());
		if (DataPtr != nullptr)
		{
			OutEllipses.Add(DataPtr);
		}
	}
}

FVector FEasyDXFLibrary::UE4ToCAD(const FVector& InVector)
{
	//FTransform PTOCAD(FVector::YAxisVector, FVector::XAxisVector, FVector::ZAxisVector, FVector::ZeroVector);
	//PTOCAD.SetScale3D(FVector(-1, 1, 1));

	//return PTOCAD.TransformPosition(InVector);

	return FVector(InVector.Y /** 10.0f*/, InVector.X /** 10.0f*/, InVector.Z /** 10.0f*/);
}

FVector FEasyDXFLibrary::CADToUE4(const FVector& InVector)
{
	return FVector(InVector.X /** 0.1f*/, InVector.Y /** 0.1f*/, InVector.Z /** 0.1f*/);
}