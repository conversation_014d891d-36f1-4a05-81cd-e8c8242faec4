#include "FormatConversion.h"
#include <sstream>
#include <iomanip>
#include <codecvt>
#include <Windows/MinWindows.h>

std::string Format::FormatConversion::gbk_to_utf8(const std::string& str)
{
	if (str.empty())
	{
		return std::string();
	}

	int wide_len = MultiByteToWideChar(
		936,                // GBK
		0,
		str.data(),
		static_cast<int>(str.size()), // 指定长度（不包括隐含 '\0'）
		nullptr,
		0
	);
	if (wide_len <= 0) {
		return std::string(); // 转换失败，返回空串（可改为返回原串）
	}

	std::wstring wide;
	wide.resize(wide_len);
	int res1 = MultiByteToWideChar(
		936,
		0,
		str.data(),
		static_cast<int>(str.size()),
		&wide[0],
		wide_len
	);
	if (res1 == 0) {
		return std::string();
	}

	// 2) wide (UTF-16) -> UTF-8
	int utf8_len = WideCharToMultiByte(
		CP_UTF8,
		0,
		wide.c_str(),
		wide_len,
		nullptr,
		0,
		nullptr,
		nullptr
	);
	if (utf8_len <= 0) {
		return std::string();
	}

	std::string utf8;
	utf8.resize(utf8_len);
	int res2 = WideCharToMultiByte(
		CP_UTF8,
		0,
		wide.c_str(),
		wide_len,
		&utf8[0],
		utf8_len,
		nullptr,
		nullptr
	);
	if (res2 == 0) {
		return std::string();
	}
	return utf8;
}

std::string Format::FormatConversion::utf8_to_default(const char* str, int length)
{
	int wide_len = MultiByteToWideChar(
		CP_UTF8,                // GBK
		0,
		str,
		length, // 指定长度（不包括隐含 '\0'）
		nullptr,
		0
	);
	if (wide_len <= 0) {
		return std::string(); // 转换失败，返回空串（可改为返回原串）
	}

	std::wstring wide;
	wide.resize(wide_len);
	int res1 = MultiByteToWideChar(
		CP_UTF8,
		0,
		str,
		length,
		&wide[0],
		wide_len
	);
	if (res1 == 0) {
		return std::string();
	}


	// 2) wide (UTF-16) -> default
	int default_len = WideCharToMultiByte(
		CP_ACP,
		0,
		wide.c_str(),
		wide_len,
		nullptr,
		0,
		nullptr,
		nullptr
	);
	if (default_len <= 0) {
		return std::string();
	}

	std::string defaultstr;
	defaultstr.resize(default_len);
	int res2 = WideCharToMultiByte(
		CP_ACP,
		0,
		wide.c_str(),
		wide_len,
		&defaultstr[0],
		default_len,
		nullptr,
		nullptr
	);
	if (res2 == 0) {
		return std::string();
	}
	return defaultstr;

	//try
	//{
	//	std::wstring_convert<std::codecvt_utf8<wchar_t> > conv;
	//	std::wstring tmp_wstr = conv.from_bytes(str);

	//	//GBK locale name in windows
	//	const char* GBK_LOCALE_NAME = ".936";
	//	std::wstring_convert<std::codecvt_byname<wchar_t, char, mbstate_t>> convert(new std::codecvt_byname<wchar_t, char, mbstate_t>(GBK_LOCALE_NAME));
	//	return convert.to_bytes(tmp_wstr);
	//}
	//catch (const std::exception&)
	//{
	//	return str;
	//}

}

std::string Format::FormatConversion::UE4_to_default(const FString& InStr)
{
	// 2) wide (UTF-16) -> default
	int default_len = WideCharToMultiByte(
		CP_ACP,
		0,
		*InStr,
		InStr.Len(),
		nullptr,
		0,
		nullptr,
		nullptr
	);
	if (default_len <= 0) {
		return std::string();
	}

	std::string defaultstr;
	defaultstr.resize(default_len);
	int res2 = WideCharToMultiByte(
		CP_ACP,
		0,
		*InStr,
		InStr.Len(),
		&defaultstr[0],
		default_len,
		nullptr,
		nullptr
	);
	if (res2 == 0) {
		return std::string();
	}
	return defaultstr;
}

std::string Format::FormatConversion::Utf8ToDxfUnicodeEscapes(const std::string& utf8)
{
	std::ostringstream oss;
	size_t i = 0;
	while (i < utf8.size())
	{
		unsigned char c = utf8[i];
		uint32_t codepoint = 0;
		if (c < 0x80)
		{
			codepoint = c;
			i += 1;
		}
		else if ((c & 0xE0) == 0xC0)
		{
			codepoint = ((utf8[i] & 0x1F) << 6) | (utf8[i + 1] & 0x3F);
			i += 2;
		}
		else if ((c & 0xF0) == 0xE0)
		{
			codepoint = ((utf8[i] & 0x0F) << 12) | ((utf8[i + 1] & 0x3F) << 6) | (utf8[i + 2] & 0x3F);
			i += 3;
		}
		else if ((c & 0xF8) == 0xF0)
		{
			codepoint = ((utf8[i] & 0x07) << 18) | ((utf8[i + 1] & 0x3F) << 12) | ((utf8[i + 2] & 0x3F) << 6) | (utf8[i + 3] & 0x3F);
			i += 4;
		}
		else
		{
			// 非法字符，跳过
			i += 1;
			continue;
		}

		if (codepoint >= 0x80) // 非ASCII字符才转义
		{
			oss << "\\U+" << std::uppercase << std::hex << std::setw(4) << std::setfill('0') << codepoint;
		}
		else
		{
			oss << static_cast<char>(codepoint);
		}
	}
	return oss.str();
}

std::string Format::FormatConversion::Utf8ToDxfUnicodeEscapes(const char* utf8)
{
	if (!utf8) return "";
	return Utf8ToDxfUnicodeEscapes(std::string(utf8));
}

std::string DxfLib::UEStrToDxfStr(const FString& InStr)
{
	//std::string Result = Format::FormatConversion::utf8_to_gbk(TCHAR_TO_UTF8(*InStr));
	std::string Result = TCHAR_TO_UTF8(*InStr);
	return Result;
}

FString DxfLib::DxfStrToUEStr(const std::string& InStr)
{
	//FString Result = UTF8_TO_TCHAR(Format::FormatConversion::gbk_to_utf8(InStr).c_str());
	FString Result = UTF8_TO_TCHAR(InStr.c_str());
	//std::wstring WStr = Format::FormatConversion::utf8_to_wstr(InStr);
	//FString Result(WStr.c_str());
	return Result;
}
