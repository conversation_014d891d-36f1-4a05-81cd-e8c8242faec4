#ifndef DL_DXFREADER_H
#define DL_DXFREADER_H
#endif // !DL_DXFREADER_H


#pragma once

#include <map>
#include <string>
#include <sstream>
#include <limits>
#include <algorithm>

#include "dl_creationadapter.h"



namespace DxfLib {

    class EASYDXF_API DL_DxfReader : public DL_CreationAdapter
    {
    public:
        DL_DxfReader();
        virtual ~DL_DxfReader();

    public:

        virtual void processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue) override;

    public:

        virtual bool LoadFile(const std::string& file);

        virtual bool readDxfGroups(FILE* fp);

        virtual bool getStrippedLine(std::string& s, unsigned int size, FILE* fp, bool stripSpace = true);

        virtual bool LoadFile(std::istream& stream);

        virtual bool readDxfGroups(std::istream& stream);

        bool getStrippedLine(std::string& s, unsigned int size, std::istream& stream, bool stripSpace = true);

        virtual bool stripWhiteSpace(char** s, bool stripSpaces = true);

    protected:
        void GenerateAttributes();



        bool hasValue(int code) {
            return values.count(code) == 1;
        }

        int getIntValue(int code, int def) {
            if (!hasValue(code)) {
                return def;
            }
            return toInt(values[code]);
        }

        int toInt(const std::string& str) {
            char* p;
            return strtol(str.c_str(), &p, 10);
        }

        int getInt16Value(int code, int def) {
            if (!hasValue(code)) {
                return def;
            }
            return toInt16(values[code]);
        }

        int toInt16(const std::string& str) {
            char* p;
            return strtol(str.c_str(), &p, 16);
        }

        bool toBool(const std::string& str) {
            char* p;
            return (bool)strtol(str.c_str(), &p, 10);
        }

        std::string getStringValue(int code, const std::string& def) {
            if (!hasValue(code)) {
                return def;
            }
            return values[code];
        }

        double getRealValue(int code, double def) {
            if (!hasValue(code)) {
                return def;
            }
            return toReal(values[code]);
        }
        double toReal(const std::string& str) {
            double ret;
            // make sure the real value uses '.' not ',':
            std::string str2 = str;
            std::replace(str2.begin(), str2.end(), ',', '.');
            // make sure c++ expects '.' not ',':
            std::istringstream istr(str2);
            //istr.imbue(std::locale("C"));
            istr >> ret;
            return ret;
        }

    protected:
        bool firstCall;
        // Current entity type
        DL_Codes::ObjectType currentObjectType;

        // Only the useful part of the group code
        std::string groupCodeTmp;
        // ...same as integer
        unsigned int groupCode;
        // Only the useful part of the group value
        std::string groupValue;

        // Stores the group codes
        std::map<int, std::string> values;

        DL_Codes::module Moduletype = DL_Codes::module::DXF_UNKNOW;

        DL_Codes::module TargetModuletype;

        
    };
}