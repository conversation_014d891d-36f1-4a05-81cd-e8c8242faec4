#pragma once
#include "DL_DxfReader.h"
//namespace Geometry {
//    struct FVector;
//}
//using namespace Geometry;


namespace DxfLib {

    struct FDxfBlockData;
    struct FDxfDimensionBase;
    struct FDxfHatchData;
    class EASYDXF_API FDxfBlocksReader :public DL_DxfReader
    {

    public:
        FDxfBlocksReader();
        explicit FDxfBlocksReader(const std::string& InSourceName);
        virtual ~FDxfBlocksReader();

        virtual void processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue) override;

        //virtual bool LoadFile(const std::string& file) override;
    private:

      

        void StartBlock();

        void EndBlock();

        void AddAttDef();
        void AddArc();
        void AddCircle();
        void AddPoint();

        void AddLine();
        void AddLWPolyLine();
        void AddSpline();
        bool HandleSplineData();
        void AddText();
        void AddMText();
        bool HandleLWPolylineData();

        void  HandleDIMENSION();

        FDxfDimensionBase GetDimData();
        void AddDimLinear();
        void AddDimAligned();
        void AddDimRadial();
        void AddDimDiametric();
        void AddDimAngular();
        void AddDimAngular3P();
        void AddDimOrdinate();


        bool HandleHatchData();
        void AddHatchData();


      
    private:
        FDxfBlockData* BlockDataCache = nullptr;


        uint32 MaxVertices;
        int VerTexCachesIndex;
        std::vector<FVector> VertexCaches;
        //加载文件的文件名
        std::string SourceName;

        /**
        * Edge type. 1=line, 2=arc, 3=elliptic arc, 4=spline.
        */
        int EdgeType;

        struct FDxfBaseEntityData* EntityCache;


    public:
        std::string GetSourceName() { return SourceName; }
    };
}
