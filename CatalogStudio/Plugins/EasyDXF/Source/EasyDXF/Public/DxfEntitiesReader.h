#pragma once
#include "DL_DxfReader.h"

namespace DxfLib {

    struct FDxfBlockData;
    struct FDxfDimensionBase;
    struct FDxfHatchData;
    class FDxfEntitiesReader :public DL_DxfReader
    {

    public:
        FDxfEntitiesReader();
        virtual ~FDxfEntitiesReader();

        virtual void processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue) override;

    private:
        void AddArc();
        void AddCircle();
        void AddEllipse();
        void AddPoint();

        void AddLine();
        void AddLWPolyLine();
        bool HandleLWPolylineData();
    private:
        uint32 MaxVertices = 0;
        int32 VerTexCachesIndex = 0;
        TArray<FVector> VertexCaches;
        FString SourceName;
    };
}