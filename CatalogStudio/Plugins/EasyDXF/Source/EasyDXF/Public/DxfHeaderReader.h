#pragma once
#include "DL_DxfReader.h"

namespace DxfLib
{
    class EASYDXF_API FDxfHeaderReader :
        public DL_DxfReader
    {
    public:
        FDxfHeaderReader();
    public:

        virtual void processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue) override;

        virtual void setVariableVector(const std::string& Name, double X, double Y, double Z, int Code) override;
        virtual void setVariableString(const std::string& Name, const std::string& Value, int Code) override;
        virtual void setVariableInt(const std::string& Name, int Value, int Code) override;
        virtual void setVariableDouble(const std::string& Name, double Value, int Code) override;

    private:
        void AddHeaderSetting();

    };
}