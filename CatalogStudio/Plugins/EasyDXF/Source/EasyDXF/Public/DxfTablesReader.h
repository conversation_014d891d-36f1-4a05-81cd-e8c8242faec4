#pragma once
#include "DL_DxfReader.h"

namespace DxfLib
{
    class EASYDXF_API FDxfTablesReader :
        public DL_DxfReader
    {
    public:
        FDxfTablesReader();


    public:
        virtual void processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue) override;


    private:
        void AddLineType();

        void AddLayer();

        void AddTextStyle();

        void AddAppID();

        void AddDimensionStyle();


        void AddStyleHandleCache(const std::string& Handle, const std::string& StyleName);
        std::string GetStyleNameByHandle(const std::string& Handle);
    private:

        std::vector<double> PatternsCache;

        
        std::map<std::string, std::string> StyleHandleCache;
    };
}
