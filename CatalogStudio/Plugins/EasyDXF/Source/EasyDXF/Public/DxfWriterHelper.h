#pragma once
#include <vector>
//#include "document.h"
//namespace Geometry
//{
//	struct FVector;
//}
//using namespace rapidjson;
//using namespace Geometry;
class DL_WriterA;
struct FInputFrame;
class DL_Attributes;
struct  FBlockSource;
namespace DxfLib
{
	namespace FDxfWriterHelper
	{
		bool EASYDXF_API ExportDxdFile(const FString& InPath);

		//ObjectSample
		void EASYDXF_API WriteObjects(const DL_WriterA& DW);
	};

}
