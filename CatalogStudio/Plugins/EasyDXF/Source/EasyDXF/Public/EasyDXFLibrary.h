// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "Kismet/BlueprintFunctionLibrary.h"
#include "DxfTempData.h"
//#include "EasyDXFLibrary.generated.h"

/**
 * 
 */
//UCLASS()
class EASYDXF_API FEasyDXFLibrary /*: public UBlueprintFunctionLibrary*/
{
	//GENERATED_BODY()
	
public:

	static void InitializeCAD();
	
	static void DeinitializeCAD();
	
	static void LoadDXFEntities(const FString& InDXFFile);

	static void GetLWPolyLineEntities(TArray<DxfLib::FDxfLWPolyLineData*> &OutLWPolyLines);

	static void GetLWPolyLineEntities(TArray<FVector>& OutPointInUE4s,TArray<double> &OutBulges);

	static void GetEllipseEntities(TArray<DxfLib::FDxfEllipseData*>& OutEllipses);

	//UE4坐标转换到CAD坐标
	static FVector UE4ToCAD(const FVector& InVector);

	//CAD坐标转换到UE4坐标
	static FVector CADToUE4(const FVector& InVector);
};
