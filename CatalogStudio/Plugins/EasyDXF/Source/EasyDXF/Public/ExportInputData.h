#pragma once

#include <vector>
#include "DxfTempData.h"



struct EASYDXF_API FInputFrame
{
	FInputFrame() {
		FrameName.clear();
		Datas.clear();
	};
	~FInputFrame()
	{
		for (size_t i = 0; i < Datas.size(); i++)
		{
			delete Datas[i];
			Datas[i] = nullptr;
		}
	}
public:
	std::string FrameName;
	std::vector<DxfLib::FDxfBaseEntityData*> Datas;
	
	void AddIputData(DxfLib::FDxfBaseEntityData* InputData)
	{
		if (InputData)
		{
			Datas.push_back(InputData);
		}
	}
};