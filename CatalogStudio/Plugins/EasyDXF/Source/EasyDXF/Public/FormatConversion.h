#pragma once

 #include <string>

//using namespace std;

namespace Format {
	class EASYDXF_API FormatConversion
	{
	public:
		static std::string gbk_to_utf8(const std::string& str);

		//utf8字符串转换成系统默认代码页编码的字符串
		static std::string utf8_to_default(const char* str,int length);

		static std::string UE4_to_default(const FString& InStr);

        // 将 UTF-8 字符串转为 DXF 的 \U+XXXX 结构
		static std::string Utf8ToDxfUnicodeEscapes(const std::string& utf8);
        
        // 将 const char* (UTF-8) 转为 DXF 的 \U+XXXX 结构
		static std::string Utf8ToDxfUnicodeEscapes(const char* utf8);

	};
}

namespace DxfLib
{
	std::string EASYDXF_API UEStrToDxfStr(const FString& InStr);
	FString EASYDXF_API DxfStrToUEStr(const std::string& InStr);
};
