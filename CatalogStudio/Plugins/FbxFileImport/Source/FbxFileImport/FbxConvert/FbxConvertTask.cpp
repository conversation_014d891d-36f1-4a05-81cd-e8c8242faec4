// Fill out your copyright notice in the Description page of Project Settings.

#include "FbxConvertTask.h"
#include "FbxConverterFunctionLibrary.h"
#include "Runtime/Engine/Classes/Engine/World.h"
#include "Misc/Paths.h"




void FbxConvertAsyncTask::DoWork()
{
	if (FbxData.IsValid())
	{
		bool Res = UFbxConverterFunctionLibrary::ConvertFbxFileToMeshFile(FbxData->FilePath, FbxData->MeshInfos);
		FbxData->ConvertResult = Res;
	}
}

UFbxConvertTask::UFbxConvertTask()
	:TaskFinished(false)
	, ConvertTask(nullptr)
	, FbxData(nullptr)
	, TimeElapsed(0.0f)
{}

void UFbxConvertTask::StartConvert(const FString& InFbxFilePath)
{
	if (nullptr == ConvertTask)
	{
		FbxData = MakeShared<FbxAsyncData>();
		FbxData->FilePath = InFbxFilePath;
		const FString FileExtension = FPaths::GetExtension(InFbxFilePath);
		if (FileExtension.Equals(TEXT("pak"), ESearchCase::IgnoreCase))
		{
			FbxData->ConvertResult = true;
			TaskFinished = true;
			FbxConvertProgress.ExecuteIfBound(0.0f);
			FbxConvertComplete.ExecuteIfBound(true);
		}
		else
		{
			FbxData->ConvertResult = false;
			TaskFinished = false;
			ConvertTask = new FAsyncTask<FbxConvertAsyncTask>(FbxData);
			ConvertTask->StartBackgroundTask();
			TimeElapsed = 0.0f;
			GWorld->GetTimerManager().SetTimer(TimerHandler, this, &UFbxConvertTask::OnTaskCheckTimerHanlder, 0.1f, true, 0.0f);
		}
	}
}

void UFbxConvertTask::StopConvert()
{
	if (nullptr != ConvertTask)
	{
		ConvertTask->Cancel();
	}
	if (TimerHandler.IsValid())
	{
		GWorld->GetTimerManager().ClearTimer(TimerHandler);
	}
}

bool UFbxConvertTask::GetMeshInfo(TArray<FPMCSection>& OutMeshInfo)
{
	if (TaskFinished && FbxData.IsValid() && FbxData->ConvertResult)
	{
		OutMeshInfo = FbxData->MeshInfos;
		return true;
	}
	return false;
}

FString UFbxConvertTask::GetFilePath() const
{
	return FbxData.IsValid() ? FbxData->FilePath : TEXT("");
}

void UFbxConvertTask::OnTaskCheckTimerHanlder()
{
	if (nullptr != ConvertTask)
	{
		if (ConvertTask->IsDone())
		{
			GWorld->GetTimerManager().ClearTimer(TimerHandler);
			ConvertTask->EnsureCompletion();
			delete ConvertTask;
			ConvertTask = nullptr;
			TaskFinished = true;
			FbxConvertProgress.ExecuteIfBound(100.0f);
			FbxConvertComplete.ExecuteIfBound(FbxData.IsValid() ? FbxData->ConvertResult : false);
			return;
		}
		TimeElapsed += 0.1;
		FbxConvertProgress.ExecuteIfBound(FMath::Clamp(TimeElapsed * 1.5f, 0.0f, 90.0f));
	}
	else
	{
		TimeElapsed = 0.0f;
		GWorld->GetTimerManager().ClearTimer(TimerHandler);
	}
}
