// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Runtime/Core/Public/Async/AsyncWork.h"
#include "Runtime/Engine/Public/TimerManager.h"
#include "MagicCore/Public/PMCSection.h"
#include "FbxConvertTask.generated.h"

//Progress is in [0.0f,100.0f]
DECLARE_DYNAMIC_DELEGATE_OneParam(FFbxConvertProgressDelegate, const float&, Progress);
DECLARE_DYNAMIC_DELEGATE_OneParam(FFbxConvertCompleteDelegate, bool, Success);

struct FbxAsyncData
{
	FString FilePath;
	TArray<FPMCSection> MeshInfos;
	bool ConvertResult;
};

class FbxConvertAsyncTask : public FNonAbandonableTask
{
	friend class FAsyncTask<FbxConvertAsyncTask>;

	TSharedPtr<FbxAsyncData> FbxData;

	FbxConvertAsyncTask(const TSharedPtr<FbxAsyncData>& InFbxData)
		: FbxData(InFbxData)
	{
	}

	void DoWork();

	FORCEINLINE TStatId GetStatId() const
	{
		RETURN_QUICK_DECLARE_CYCLE_STAT(FbxConvertAsyncTask, STATGROUP_ThreadPoolAsyncTasks);
	}
};

/**
 *
 */
UCLASS()
class FBXFILEIMPORT_API UFbxConvertTask : public UObject
{
	GENERATED_BODY()

public:

	FFbxConvertProgressDelegate FbxConvertProgress;
	FFbxConvertCompleteDelegate FbxConvertComplete;

public:

	UFbxConvertTask();

	//Before call this function,bind complete delegate first
	void StartConvert(const FString& InFbxFilePath);
	void StopConvert();
	bool GetMeshInfo(TArray<FPMCSection>& OutMeshInfo);

	FString GetFilePath() const;
protected:

	UFUNCTION()
		void OnTaskCheckTimerHanlder();

protected:

	bool	TaskFinished;

	FAsyncTask<FbxConvertAsyncTask>* ConvertTask;

	TSharedPtr<FbxAsyncData> FbxData;

	float	TimeElapsed;

	FTimerHandle	TimerHandler;
};
