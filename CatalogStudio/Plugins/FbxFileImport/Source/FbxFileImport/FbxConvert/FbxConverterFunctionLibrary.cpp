// Fill out your copyright notice in the Description page of Project Settings.

#include "FbxConverterFunctionLibrary.h"
#include "core/fbxmanager.h"
#include "fileio/fbxiosettings.h"
#include "fileio/fbximporter.h"
#include "scene/fbxscene.h"
#include "scene/geometry/fbxmesh.h"
#include "scene/geometry/fbxnode.h"
#include "scene/geometry/fbxcluster.h"
#include <utils/fbxrootnodeutility.h>
#include <utils/fbxgeometryconverter.h>
#include "scene/shading/fbxsurfacephong.h"
#include "scene/shading/fbxsurfacelambert.h"
#include "scene/shading/fbxfiletexture.h"
#include "scene/shading/fbxlayeredtexture.h"
#include "Runtime/Core/Public/Misc/Paths.h"

using namespace fbxsdk;

//#ifdef IOS_REF
//#undef  IOS_REF
//#define IOS_REF (*(SdkManager->GetIOSettings()))
//#endif

DEFINE_LOG_CATEGORY(ConvertLog);

template <typename ArrayType>
bool ValidateArraySize(ArrayType const& Array, int32 ExpectedSize)
{
	return Array.Num() == 0 || Array.Num() == ExpectedSize;
}

enum
{
	MAX_MESH_TEXTURE_COORDS = 8,
};

// Wraps some common code useful for multiple fbx import code path
struct FFBXUVs
{
	// constructor
	FFBXUVs(FbxMesh* Mesh)
		: UniqueUVCount(0)
	{
		check(Mesh);

		//
		//	store the UVs in arrays for fast access in the later looping of triangles 
		//
		// mapping from UVSets to Fbx LayerElementUV
		// Fbx UVSets may be duplicated, remove the duplicated UVSets in the mapping 
		int32 LayerCount = Mesh->GetLayerCount();
		if (LayerCount > 0)
		{
			int32 UVLayerIndex;
			for (UVLayerIndex = 0; UVLayerIndex < LayerCount; UVLayerIndex++)
			{
				FbxLayer* lLayer = Mesh->GetLayer(UVLayerIndex);
				int UVSetCount = lLayer->GetUVSetCount();
				if (UVSetCount)
				{
					FbxArray<FbxLayerElementUV const*> EleUVs = lLayer->GetUVSets();
					for (int UVIndex = 0; UVIndex < UVSetCount; UVIndex++)
					{
						FbxLayerElementUV const* ElementUV = EleUVs[UVIndex];
						if (ElementUV)
						{
							const char* UVSetName = ElementUV->GetName();
							FString LocalUVSetName = UTF8_TO_TCHAR(UVSetName);
							if (LocalUVSetName.IsEmpty())
							{
								LocalUVSetName = TEXT("UVmap_") + FString::FromInt(UVLayerIndex);
							}

							UVSets.AddUnique(LocalUVSetName);
						}
					}
				}
			}
		}


		// If the the UV sets are named using the following format (UVChannel_X; where X ranges from 1 to 4)
		// we will re-order them based on these names.  Any UV sets that do not follow this naming convention
		// will be slotted into available spaces.
		if (UVSets.Num())
		{
			for (int32 ChannelNumIdx = 0; ChannelNumIdx < 4; ChannelNumIdx++)
			{
				FString ChannelName = FString::Printf(TEXT("UVChannel_%d"), ChannelNumIdx + 1);
				int32 SetIdx = UVSets.Find(ChannelName);

				// If the specially formatted UVSet name appears in the list and it is in the wrong spot,
				// we will swap it into the correct spot.
				if (SetIdx != INDEX_NONE && SetIdx != ChannelNumIdx)
				{
					// If we are going to swap to a position that is outside the bounds of the
					// array, then we pad out to that spot with empty data.
					for (int32 ArrSize = UVSets.Num(); ArrSize < ChannelNumIdx + 1; ArrSize++)
					{
						UVSets.Add(FString(TEXT("")));
					}
					//Swap the entry into the appropriate spot.
					UVSets.Swap(SetIdx, ChannelNumIdx);
				}
			}
		}
	}

	void Phase2(FbxMesh* Mesh)
	{
		//
		//	store the UVs in arrays for fast access in the later looping of triangles 
		//
		UniqueUVCount = UVSets.Num();
		if (UniqueUVCount > 0)
		{
			LayerElementUV.AddZeroed(UniqueUVCount);
			UVReferenceMode.AddZeroed(UniqueUVCount);
			UVMappingMode.AddZeroed(UniqueUVCount);
		}
		for (int32 UVIndex = 0; UVIndex < UniqueUVCount; UVIndex++)
		{
			LayerElementUV[UVIndex] = NULL;
			for (int32 UVLayerIndex = 0, LayerCount = Mesh->GetLayerCount(); UVLayerIndex < LayerCount; UVLayerIndex++)
			{
				FbxLayer* lLayer = Mesh->GetLayer(UVLayerIndex);
				int UVSetCount = lLayer->GetUVSetCount();
				if (UVSetCount)
				{
					FbxArray<FbxLayerElementUV const*> EleUVs = lLayer->GetUVSets();
					for (int32 FbxUVIndex = 0; FbxUVIndex < UVSetCount; FbxUVIndex++)
					{
						FbxLayerElementUV const* ElementUV = EleUVs[FbxUVIndex];
						if (ElementUV)
						{
							const char* UVSetName = ElementUV->GetName();
							FString LocalUVSetName = UTF8_TO_TCHAR(UVSetName);
							if (LocalUVSetName.IsEmpty())
							{
								LocalUVSetName = TEXT("UVmap_") + FString::FromInt(UVLayerIndex);
							}
							if (LocalUVSetName == UVSets[UVIndex])
							{
								LayerElementUV[UVIndex] = ElementUV;
								UVReferenceMode[UVIndex] = ElementUV->GetReferenceMode();
								UVMappingMode[UVIndex] = ElementUV->GetMappingMode();
								break;
							}
						}
					}
				}
			}
		}
		UniqueUVCount = FMath::Min<int32>(UniqueUVCount, MAX_MESH_TEXTURE_COORDS);
	}

	int32 FindLightUVIndex() const
	{
		// See if any of our UV set entry names match LightMapUV.
		for (int32 UVSetIdx = 0; UVSetIdx < UVSets.Num(); UVSetIdx++)
		{
			if (UVSets[UVSetIdx] == TEXT("LightMapUV"))
			{
				return UVSetIdx;
			}
		}

		// not found
		return INDEX_NONE;
	}

	// @param FaceCornerIndex usually TriangleIndex * 3 + CornerIndex but more complicated for mixed n-gons
	int32 ComputeUVIndex(int32 UVLayerIndex, int32 lControlPointIndex, int32 FaceCornerIndex) const
	{
		int32 UVMapIndex = (UVMappingMode[UVLayerIndex] == FbxLayerElement::eByControlPoint) ? lControlPointIndex : FaceCornerIndex;

		int32 Ret;

		if (UVReferenceMode[UVLayerIndex] == FbxLayerElement::eDirect)
		{
			Ret = UVMapIndex;
		}
		else
		{
			FbxLayerElementArrayTemplate<int>& Array = LayerElementUV[UVLayerIndex]->GetIndexArray();
			Ret = Array.GetAt(UVMapIndex);
		}

		return Ret;
	}

	// todo: is that needed? could the dtor do it?
	void Cleanup()
	{
		//
		// clean up.  This needs to happen before the mesh is destroyed
		//
		LayerElementUV.Empty();
		UVReferenceMode.Empty();
		UVMappingMode.Empty();
	}

	TArray<FString> UVSets;
	TArray<FbxLayerElementUV const*> LayerElementUV;
	TArray<FbxLayerElement::EReferenceMode> UVReferenceMode;
	TArray<FbxLayerElement::EMappingMode> UVMappingMode;
	int32 UniqueUVCount;
};

struct FFbxMaterial
{
	FbxSurfaceMaterial* FbxMaterial;
	UMaterialInterface* Material;

	FString GetName() const { return FbxMaterial ? ANSI_TO_TCHAR(FbxMaterial->GetName()) : TEXT("None"); }
};

struct FCustomRawMesh
{
	/** Material index. Array[FaceId] = int32 */
	TArray<int32> FaceMaterialIndices;
	/** Smoothing mask. Array[FaceId] = uint32 */
	TArray<uint32> FaceSmoothingMasks;

	/** Position in local space. Array[VertexId] = float3(x,y,z) */
	TArray<FVector> VertexPositions;

	/** Index of the vertex at this wedge. Array[WedgeId] = VertexId */
	TArray<int32> Triangles;
	/** Tangent, U direction. Array[WedgeId] = float3(x,y,z) */
	TArray<FVector>	WedgeTangentX;
	/** Tangent, V direction. Array[WedgeId] = float3(x,y,z) */
	TArray<FVector>	WedgeTangentY;
	/** Normal. Array[WedgeId] = float3(x,y,z) */
	TArray<FVector>	WedgeTangentZ;
	/** Texture coordinates. Array[UVId][WedgeId]=float2(u,v) */
	TArray<FVector2D> WedgeTexCoords[MAX_MESH_TEXTURE_COORDS];
	/** Color. Array[WedgeId]=float3(r,g,b,a) */
	TArray<FColor> WedgeColors;

	/**
	* Map from material index -> original material index at import time. It's
	* valid for this to be empty in which case material index == original
	* material index.
	*/
	TArray<int32> MaterialIndexToImportIndex;

	/** Empties all data streams. */
	void Empty();

	/**
	* Returns true if the mesh contains valid information.
	*  - Validates that stream sizes match.
	*  - Validates that there is at least one texture coordinate.
	*  - Validates that indices are valid positions in the vertex stream.
	*/
	bool IsValid() const;

	/**
	* Returns true if the mesh contains valid information or slightly invalid information that we can fix.
	*  - Validates that stream sizes match.
	*  - Validates that there is at least one texture coordinate.
	*  - Validates that indices are valid positions in the vertex stream.
	*/
	bool IsValidOrFixable() const;

	/** Helper for getting the position of a wedge. */
	FORCEINLINE FVector GetWedgePosition(int32 WedgeIndex) const
	{
		return VertexPositions[Triangles[WedgeIndex]];
	}

	/**
	* Compacts materials by removing any that have no associated triangles.
	* Also updates the material index map.
	*/
	void CompactMaterialIndices();
};

bool FCustomRawMesh::IsValid() const
{
	int32 NumVertices = VertexPositions.Num();
	int32 NumWedges = Triangles.Num();
	int32 NumFaces = NumWedges / 3;

	bool bValid = NumVertices > 0
		&& NumWedges > 0
		&& NumFaces > 0
		&& (NumWedges / 3) == NumFaces
		&& ValidateArraySize(FaceMaterialIndices, NumFaces)
		&& ValidateArraySize(FaceSmoothingMasks, NumFaces)
		&& ValidateArraySize(WedgeTangentX, NumWedges)
		&& ValidateArraySize(WedgeTangentY, NumWedges)
		&& ValidateArraySize(WedgeTangentZ, NumWedges)
		&& ValidateArraySize(WedgeColors, NumWedges)
		// All meshes must have a valid texture coordinate.
		&& WedgeTexCoords[0].Num() == NumWedges;

	for (int32 TexCoordIndex = 1; TexCoordIndex < MAX_MESH_TEXTURE_COORDS; ++TexCoordIndex)
	{
		bValid = bValid && ValidateArraySize(WedgeTexCoords[TexCoordIndex], NumWedges);
	}

	int32 WedgeIndex = 0;
	while (bValid && WedgeIndex < NumWedges)
	{
		bValid = bValid && (Triangles[WedgeIndex] < NumVertices);
		WedgeIndex++;
	}

	return bValid;
}

bool FCustomRawMesh::IsValidOrFixable() const
{
	int32 NumVertices = VertexPositions.Num();
	int32 NumWedges = Triangles.Num();
	int32 NumFaces = NumWedges / 3;
	int32 NumTexCoords = WedgeTexCoords[0].Num();
	int32 NumFaceSmoothingMasks = FaceSmoothingMasks.Num();
	int32 NumFaceMaterialIndices = FaceMaterialIndices.Num();

	bool bValidOrFixable = NumVertices > 0
		&& NumWedges > 0
		&& NumFaces > 0
		&& (NumWedges / 3) == NumFaces
		&& NumFaceMaterialIndices == NumFaces
		&& NumFaceSmoothingMasks == NumFaces
		&& ValidateArraySize(WedgeColors, NumWedges)
		// All meshes must have a valid texture coordinate.
		&& NumTexCoords == NumWedges;

	for (int32 TexCoordIndex = 1; TexCoordIndex < MAX_MESH_TEXTURE_COORDS; ++TexCoordIndex)
	{
		bValidOrFixable = bValidOrFixable && ValidateArraySize(WedgeTexCoords[TexCoordIndex], NumWedges);
	}

	int32 WedgeIndex = 0;
	while (bValidOrFixable && WedgeIndex < NumWedges)
	{
		bValidOrFixable = bValidOrFixable && (Triangles[WedgeIndex] < NumVertices);
		WedgeIndex++;
	}

	return bValidOrFixable;
}

void FCustomRawMesh::CompactMaterialIndices()
{
	MaterialIndexToImportIndex.Reset();
	if (IsValidOrFixable())
	{
		// Count the number of triangles per section.
		TArray<int32, TInlineAllocator<8> > NumTrianglesPerSection;
		int32 NumFaces = FaceMaterialIndices.Num();
		for (int32 FaceIndex = 0; FaceIndex < NumFaces; ++FaceIndex)
		{
			int32 MaterialIndex = FaceMaterialIndices[FaceIndex];
			if (MaterialIndex >= NumTrianglesPerSection.Num())
			{
				NumTrianglesPerSection.AddZeroed(MaterialIndex - NumTrianglesPerSection.Num() + 1);
			}
			if (MaterialIndex >= 0)
			{
				NumTrianglesPerSection[MaterialIndex]++;
			}
		}

		// Identify non-zero sections and assign new materials.
		TArray<int32, TInlineAllocator<8> > ImportIndexToMaterialIndex;
		for (int32 SectionIndex = 0; SectionIndex < NumTrianglesPerSection.Num(); ++SectionIndex)
		{
			int32 NewMaterialIndex = INDEX_NONE;
			if (NumTrianglesPerSection[SectionIndex] > 0)
			{
				NewMaterialIndex = MaterialIndexToImportIndex.Add(SectionIndex);
			}
			ImportIndexToMaterialIndex.Add(NewMaterialIndex);
		}

		// If some sections will be removed, remap material indices for each face.
		if (MaterialIndexToImportIndex.Num() != ImportIndexToMaterialIndex.Num())
		{
			for (int32 FaceIndex = 0; FaceIndex < NumFaces; ++FaceIndex)
			{
				int32 MaterialIndex = FaceMaterialIndices[FaceIndex];
				FaceMaterialIndices[FaceIndex] = ImportIndexToMaterialIndex[MaterialIndex];
			}
		}
		else
		{
			MaterialIndexToImportIndex.Reset();
		}
	}
}

int32 CreateNodeMaterials(FbxNode* FbxNode, TArray<UMaterialInterface*>& OutMaterials, TArray<FString>& UVSets, bool bForSkeletalMesh)
{
	int32 MaterialCount = FbxNode->GetMaterialCount();
	TArray<FbxSurfaceMaterial*> UsedSurfaceMaterials;
	FbxMesh* MeshNode = FbxNode->GetMesh();
	TSet<int32> UsedMaterialIndexes;
	if (MeshNode)
	{
		for (int32 ElementMaterialIndex = 0; ElementMaterialIndex < MeshNode->GetElementMaterialCount(); ++ElementMaterialIndex)
		{
			FbxGeometryElementMaterial* ElementMaterial = MeshNode->GetElementMaterial(ElementMaterialIndex);
			switch (ElementMaterial->GetMappingMode())
			{
			case FbxLayerElement::eAllSame:
			{
				if (ElementMaterial->GetIndexArray().GetCount() > 0)
				{
					UsedMaterialIndexes.Add(ElementMaterial->GetIndexArray()[0]);
				}
			}
			break;
			case FbxLayerElement::eByPolygon:
			{
				for (int32 MaterialIndex = 0; MaterialIndex < ElementMaterial->GetIndexArray().GetCount(); ++MaterialIndex)
				{
					UsedMaterialIndexes.Add(ElementMaterial->GetIndexArray()[MaterialIndex]);
				}
			}
			break;
			}
		}
	}
	for (int32 MaterialIndex = 0; MaterialIndex < MaterialCount; ++MaterialIndex)
	{
		//Create only the material used by the mesh element material
		if (MeshNode == nullptr || UsedMaterialIndexes.Contains(MaterialIndex))
		{
			FbxSurfaceMaterial* FbxMaterial = FbxNode->GetMaterial(MaterialIndex);

			if (FbxMaterial)
			{
				//CreateUnrealMaterial(*FbxMaterial, OutMaterials, UVSets, bForSkeletalMesh);
			}
		}
		else
		{
			OutMaterials.Add(nullptr);
		}
	}
	return MaterialCount;
}

FbxAMatrix ComputeTotalMatrix(FbxNode* Node, FbxScene* Scene)
{
	FbxAMatrix Geometry;
	FbxVector4 Translation, Rotation, Scaling;
	Translation = Node->GetGeometricTranslation(FbxNode::eSourcePivot);
	Rotation = Node->GetGeometricRotation(FbxNode::eSourcePivot);
	Scaling = Node->GetGeometricScaling(FbxNode::eSourcePivot);
	Geometry.SetT(Translation);
	Geometry.SetR(Rotation);
	Geometry.SetS(Scaling);

	//For Single Matrix situation, obtain transfrom matrix from eDESTINATION_SET, which include pivot offsets and pre/post rotations.
	FbxAMatrix& GlobalTransform = Scene->GetAnimationEvaluator()->GetNodeGlobalTransform(Node);

	//We must always add the geometric transform. Only Max use the geometric transform which is an offset to the local transform of the node
	FbxAMatrix TotalMatrix = GlobalTransform * Geometry;

	return TotalMatrix;
}

bool IsOddNegativeScale(FbxAMatrix& TotalMatrix)
{
	FbxVector4 Scale = TotalMatrix.GetS();
	int32 NegativeNum = 0;

	if (Scale[0] < 0) NegativeNum++;
	if (Scale[1] < 0) NegativeNum++;
	if (Scale[2] < 0) NegativeNum++;

	return NegativeNum == 1 || NegativeNum == 3;
}

FVector ConvertPos(FbxVector4 Vector)
{
	FVector Out;
	Out[0] = Vector[0];
	// flip Y, then the right-handed axis system is converted to LHS
	Out[1] = -Vector[1];
	Out[2] = Vector[2];
	return Out;
}



FVector ConvertDir(FbxVector4 Vector)
{
	FVector Out;
	Out[0] = Vector[0];
	Out[1] = -Vector[1];
	Out[2] = Vector[2];
	return Out;
}

void InitializeManager(FbxManager*& SdkManager)
{
	// Create the SdkManager
	SdkManager = FbxManager::Create();

	// create an IOSettings object
	FbxIOSettings* ios = FbxIOSettings::Create(SdkManager, IOSROOT);
	SdkManager->SetIOSettings(ios);
}

bool OpenFile(FString Filename, FbxManager* SdkManager, FbxImporter*& Importer)
{
	bool Result = true;

	int32 SDKMajor, SDKMinor, SDKRevision;

	// Create an importer.
	Importer = FbxImporter::Create(SdkManager, "");

	// Get the version number of the FBX files generated by the
	// version of FBX SDK that you are using.
	FbxManager::GetFileFormatVersion(SDKMajor, SDKMinor, SDKRevision);

	// Initialize the importer by providing a filename.
	Importer->ParseForStatistics(true);

	const bool bImportStatus = Importer->Initialize(TCHAR_TO_UTF8(*Filename));

	if (!bImportStatus)  // Problem with the file to be imported
	{
		return false;
	}

	// Skip the version check if we are just parsing for information or scene info.
	if (true)
	{
		int32 FileMajor, FileMinor, FileRevision;
		Importer->GetFileVersion(FileMajor, FileMinor, FileRevision);

		int32 FileVersion = (FileMajor << 16 | FileMinor << 8 | FileRevision);
		int32 SDKVersion = (SDKMajor << 16 | SDKMinor << 8 | SDKRevision);

		if (FileVersion != SDKVersion)
		{
			// Appending the SDK version to the config key causes the warning to automatically reappear even if previously suppressed when the SDK version we use changes. 
			FString ConfigStr = FString::Printf(TEXT("Warning_OutOfDateFBX_%d"), SDKVersion);

			FString FileVerStr = FString::Printf(TEXT("%d.%d.%d"), FileMajor, FileMinor, FileRevision);
			FString SDKVerStr = FString::Printf(TEXT("%d.%d.%d"), SDKMajor, SDKMinor, SDKRevision);

			const FText WarningText = FText::Format(
				NSLOCTEXT("UnrealEd", "Warning_OutOfDateFBX", "An out of date FBX has been detected.\nImporting different versions of FBX files than the SDK version can cause undesirable results.\n\nFile Version: {0}\nSDK Version: {1}"),
				FText::FromString(FileVerStr), FText::FromString(SDKVerStr));
		}
	}

	return Result;
}

bool ImportFile(FbxManager* SdkManager, FbxImporter* Importer, FbxScene*& Scene)
{
	bool bStatus;

	// Create the Scene
	Scene = FbxScene::Create(SdkManager, "");

	//int32 FileMajor, FileMinor, FileRevision;

	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_MATERIAL, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_TEXTURE, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_LINK, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_SHAPE, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_GOBO, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_ANIMATION, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_SKINS, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_DEFORMATION, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_FBX_GLOBAL_SETTINGS, true);
	(*(SdkManager->GetIOSettings())).SetBoolProp(IMP_TAKE, true);

	// Import the scene.
	bStatus = Importer->Import(Scene);

	//Make sure we don't have name clash for materials
	//if (bPreventMaterialNameClash)
	//{
	//	FixMaterialClashName();
	//}

	// Get the version number of the FBX file format.
	//Importer->GetFileVersion(FileMajor, FileMinor, FileRevision);
	//FbxFileVersion = FString::Printf(TEXT("%d.%d.%d"), FileMajor, FileMinor, FileRevision);

	// output result
	return bStatus;
}

void ConvertScene(FbxScene* Scene)
{
	//Set the original file information
	FbxAxisSystem FileAxisSystem = Scene->GetGlobalSettings().GetAxisSystem();
	FbxSystemUnit FileUnitSystem = Scene->GetGlobalSettings().GetSystemUnit();

	if (true)
	{
		// we use -Y as forward axis here when we import. This is odd considering our forward axis is technically +X
		// but this is to mimic Maya/Max behavior where if you make a model facing +X facing, 
		// when you import that mesh, you want +X facing in engine. 
		// only thing that doesn't work is hand flipping because Max/Maya is RHS but UE is LHS
		// On the positive note, we now have import transform set up you can do to rotate mesh if you don't like default setting
		FbxAxisSystem::ECoordSystem CoordSystem = FbxAxisSystem::eRightHanded;
		FbxAxisSystem::EUpVector UpVector = FbxAxisSystem::eZAxis;
		FbxAxisSystem::EFrontVector FrontVector = (FbxAxisSystem::EFrontVector)-FbxAxisSystem::eParityOdd;
		//if (GetImportOptions()->bForceFrontXAxis) false
		//{
		//	FrontVector = FbxAxisSystem::eParityEven;
		//}


		FbxAxisSystem UnrealImportAxis(UpVector, FrontVector, CoordSystem);

		FbxAxisSystem SourceSetup = Scene->GetGlobalSettings().GetAxisSystem();

		if (SourceSetup != UnrealImportAxis)
		{
			FbxRootNodeUtility::RemoveAllFbxRoots(Scene);
			UnrealImportAxis.ConvertScene(Scene);
			//FbxAMatrix JointOrientationMatrix;
			//JointOrientationMatrix.SetIdentity();
			//if (GetImportOptions()->bForceFrontXAxis)
			//{
			//	JointOrientationMatrix.SetR(FbxVector4(-90.0, -90.0, 0.0));
			//}
			//FFbxDataConverter::SetJointPostConversionMatrix(JointOrientationMatrix);
		}
	}
	// Convert the scene's units to what is used in this program, if needed.
	// The base unit used in both FBX and Unreal is centimeters.  So unless the units 
	// are already in centimeters (ie: scalefactor 1.0) then it needs to be converted
	//if (GetImportOptions()->bConvertSceneUnit && Scene->GetGlobalSettings().GetSystemUnit() != FbxSystemUnit::cm)
	//{
	//	FbxSystemUnit::cm.ConvertScene(Scene);
	//}

	//Reset all the transform evaluation cache since we change some node transform
	Scene->GetAnimationEvaluator()->Reset();
}

void ValidateAllMeshesAreReferenceByNodeAttribute(FbxScene* Scene)
{
	for (int GeoIndex = 0; GeoIndex < Scene->GetGeometryCount(); ++GeoIndex)
	{
		bool FoundOneGeometryLinkToANode = false;
		FbxGeometry* Geometry = Scene->GetGeometry(GeoIndex);
		for (int NodeIndex = 0; NodeIndex < Scene->GetNodeCount(); ++NodeIndex)
		{
			FbxNode* SceneNode = Scene->GetNode(NodeIndex);
			FbxGeometry* NodeGeometry = static_cast<FbxGeometry*>(SceneNode->GetMesh());
			if (NodeGeometry && NodeGeometry->GetUniqueID() == Geometry->GetUniqueID())
			{
				FoundOneGeometryLinkToANode = true;
				break;
			}
		}
	}
}



void RecursiveGetAllMeshNode(TArray<FbxNode*>& OutAllNode, FbxNode* Node)
{
	if (Node->GetMesh() != nullptr)
	{
		OutAllNode.Add(Node);
		return;
	}
	for (int32 ChildIndex = 0; ChildIndex < Node->GetChildCount(); ++ChildIndex)
	{
		RecursiveGetAllMeshNode(OutAllNode, Node->GetChild(ChildIndex));
	}
}

struct FCustomFbxMaterial
{
	FString MaterialName;
	FLinearColor MaterialColor;
	TArray<FString> TextureFilePath;
	FCustomFbxMaterial() :MaterialName(), MaterialColor(FLinearColor::White) {}
	FCustomFbxMaterial(const FString& InMaterialName, const FLinearColor& InColor) :MaterialName(InMaterialName), MaterialColor(InColor) {}
	bool operator==(const FCustomFbxMaterial& OtherMaterial) { return this->MaterialName == OtherMaterial.MaterialName && this->MaterialColor == OtherMaterial.MaterialColor; }
};

int32 AddUniqueCustomFbxMaterial(const struct FCustomFbxMaterial& InMaterial, TArray<struct FCustomFbxMaterial>& Arr)
{
	for (int32 i = 0; i < Arr.Num(); i++)
	{
		if (Arr[i] == InMaterial)
		{
			return i;
		}
	}
	return Arr.Add(InMaterial);
}

ANSICHAR* MakeName(const ANSICHAR* Name)
{
	const int SpecialChars[] = { '.', ',', '/', '`', '%' };

	const int len = FCStringAnsi::Strlen(Name);
	ANSICHAR* TmpName = new ANSICHAR[len + 1];

	FCStringAnsi::Strcpy(TmpName, len + 1, Name);

	for (int32 i = 0; i < UE_ARRAY_COUNT(SpecialChars); i++)
	{
		ANSICHAR* CharPtr = TmpName;
		while ((CharPtr = FCStringAnsi::Strchr(CharPtr, SpecialChars[i])) != NULL)
		{
			CharPtr[0] = '_';
		}
	}

	// Remove namespaces
	ANSICHAR* NewName;
	NewName = FCStringAnsi::Strchr(TmpName, ':');

	// there may be multiple namespace, so find the last ':'
	while (NewName && FCStringAnsi::Strchr(NewName + 1, ':'))
	{
		NewName = FCStringAnsi::Strchr(NewName + 1, ':');
	}

	if (NewName)
	{
		return NewName + 1;
	}

	return TmpName;
}

bool FindTextureInfoFromMaterial(FbxSurfaceMaterial& SurfaceMaterial, TArray<FString>& TexturePaths)
{
	FbxProperty FbxProperty = SurfaceMaterial.FindProperty(FbxSurfaceMaterial::sDiffuse);
	if (FbxProperty.IsValid())
	{
		int32 LayeredTextureCount = FbxProperty.GetSrcObjectCount<FbxLayeredTexture>();
		UE_LOG(ConvertLog, Log, TEXT("--------- LayeredTextureCount is: %d"), LayeredTextureCount);
		if (LayeredTextureCount > 0)
		{
			for (int32 i = 0; i < LayeredTextureCount; ++i)
			{
				FbxLayeredTexture* LayeredTexture = FbxProperty.GetSrcObject<FbxLayeredTexture>(i);
				if (LayeredTexture)
				{
					int32 TextureCount = LayeredTexture->GetSrcObjectCount<FbxFileTexture>();
					UE_LOG(ConvertLog, Log, TEXT("--------- TextureCount is: %d"), TextureCount);
					for (int32 j = 0; j < TextureCount; ++j)
					{
						FbxFileTexture* FbxTexture = LayeredTexture->GetSrcObject<FbxFileTexture>(j);
						if (FbxTexture)
						{
							UE_LOG(ConvertLog, Log, TEXT("--------- FbxTexture is: %s"), UTF8_TO_TCHAR(FbxTexture->GetFileName()));
							TexturePaths.AddUnique(UTF8_TO_TCHAR(FbxTexture->GetFileName()));
						}
						else
						{
							UE_LOG(ConvertLog, Log, TEXT("--------- FbxTexture is null "));
						}
					}
				}
			}
		}
	}
	return TexturePaths.Num() != 0;
}

bool BuildStaticMeshFromGeometry(FbxNode* Node, FbxScene* Scene, TArray<struct FCustomFbxMaterial>& MeshMaterials, FbxGeometryConverter* GeometryConverter, FCustomRawMesh& RawMesh)
{
	FbxMesh* Mesh = Node->GetMesh();

	//remove the bad polygons before getting any data from mesh
	Mesh->RemoveBadPolygons();

	//Get the base layer of the mesh
	FbxLayer* BaseLayer = Mesh->GetLayer(0);
	if (BaseLayer == NULL)
	{
		return false;
	}

	FFBXUVs FBXUVs(Mesh);

	//
	// create materials
	//
	int32 MaterialCount = 0;
	TArray<UMaterialInterface*> Materials;
	bool bForSkeletalMesh = false;
	//CreateNodeMaterials(Node, Materials, FBXUVs.UVSets, bForSkeletalMesh);

	MaterialCount = Node->GetMaterialCount();

	// Used later to offset the material indices on the raw triangle data
	int32 MaterialIndexOffset = MeshMaterials.Num();

	for (int32 MaterialIndex = 0; MaterialIndex < MaterialCount; MaterialIndex++)
	{
		//FFbxMaterial* NewMaterial = new(MeshMaterials) FFbxMaterial;
		FbxSurfaceMaterial* FbxMaterial = Node->GetMaterial(MaterialIndex);
		FString MaterialName = UTF8_TO_TCHAR(MakeName(FbxMaterial->GetNameOnly()));
		UE_LOG(ConvertLog, Log, TEXT("--------- MaterialName is: %s"), *MaterialName);
		FCustomFbxMaterial NewCustomFbxMaterial(MaterialName, FLinearColor::White);
		FindTextureInfoFromMaterial(*FbxMaterial, NewCustomFbxMaterial.TextureFilePath);
		int32 Index = AddUniqueCustomFbxMaterial(NewCustomFbxMaterial, MeshMaterials);
		FbxDouble3 DiffuseColor;
		if (FbxMaterial)
		{
			bool FoundColor = true;
			if (FbxMaterial->GetClassId().Is(FbxSurfacePhong::ClassId))
			{
				DiffuseColor = ((FbxSurfacePhong*)(FbxMaterial))->Diffuse.Get();
			}
			else if (FbxMaterial->GetClassId().Is(FbxSurfaceLambert::ClassId))
			{
				DiffuseColor = ((FbxSurfaceLambert*)(FbxMaterial))->Diffuse.Get();
			}
			else
			{
				FoundColor = false;
			}
			if (FoundColor)
			{
				MeshMaterials[Index].MaterialColor.R = (float)(DiffuseColor[0]);
				MeshMaterials[Index].MaterialColor.G = (float)(DiffuseColor[1]);
				MeshMaterials[Index].MaterialColor.B = (float)(DiffuseColor[2]);
			}
		}
		//NewMaterial->FbxMaterial = FbxMaterial;
		//NewMaterial->Material = Materials[MaterialIndex];
	}
	//if (MaterialCount == 0)
	//{
	//	UMaterial* DefaultMaterial = UMaterial::GetDefaultMaterial(MD_Surface);
	//	check(DefaultMaterial);
	//	FFbxMaterial* NewMaterial = new(MeshMaterials) FFbxMaterial;
	//	NewMaterial->Material = DefaultMaterial;
	//	NewMaterial->FbxMaterial = NULL;
	//	MaterialCount = 1;
	//}

	//
	// Convert data format to unreal-compatible
	//

	// Must do this before triangulating the mesh due to an FBX bug in TriangulateMeshAdvance
	int32 LayerSmoothingCount = Mesh->GetLayerCount(FbxLayerElement::eSmoothing);
	for (int32 i = 0; i < LayerSmoothingCount; i++)
	{
		FbxLayerElementSmoothing const* SmoothingInfo = Mesh->GetLayer(0)->GetSmoothing();
		if (SmoothingInfo && SmoothingInfo->GetMappingMode() != FbxLayerElement::eByPolygon)
		{
			GeometryConverter->ComputePolygonSmoothingFromEdgeSmoothing(Mesh, i);
		}
	}

	if (!Mesh->IsTriangleMesh())
	{
		const bool bReplace = true;
		FbxNodeAttribute* ConvertedNode = GeometryConverter->Triangulate(Mesh, bReplace);

		if (ConvertedNode != NULL && ConvertedNode->GetAttributeType() == FbxNodeAttribute::eMesh)
		{
			Mesh = static_cast<FbxMesh*>(ConvertedNode);
		}
		else
		{
			return false; // not clean, missing some dealloc
		}
	}

	// renew the base layer
	BaseLayer = Mesh->GetLayer(0);

	//
	//	get the "material index" layer.  Do this AFTER the triangulation step as that may reorder material indices
	//
	FbxLayerElementMaterial* LayerElementMaterial = BaseLayer->GetMaterials();
	FbxLayerElement::EMappingMode MaterialMappingMode = LayerElementMaterial ?
		LayerElementMaterial->GetMappingMode() : FbxLayerElement::eByPolygon;

	//	todo second phase UV, ok to put in first phase?
	FBXUVs.Phase2(Mesh);

	//
	// get the smoothing group layer
	//
	bool bSmoothingAvailable = false;

	FbxLayerElementSmoothing const* SmoothingInfo = BaseLayer->GetSmoothing();
	FbxLayerElement::EReferenceMode SmoothingReferenceMode(FbxLayerElement::eDirect);
	FbxLayerElement::EMappingMode SmoothingMappingMode(FbxLayerElement::eByEdge);
	if (SmoothingInfo)
	{

		if (SmoothingInfo->GetMappingMode() == FbxLayerElement::eByPolygon)
		{
			bSmoothingAvailable = true;
		}


		SmoothingReferenceMode = SmoothingInfo->GetReferenceMode();
		SmoothingMappingMode = SmoothingInfo->GetMappingMode();
	}

	//
	// get the first vertex color layer
	//
	FbxLayerElementVertexColor* LayerElementVertexColor = BaseLayer->GetVertexColors();
	FbxLayerElement::EReferenceMode VertexColorReferenceMode(FbxLayerElement::eDirect);
	FbxLayerElement::EMappingMode VertexColorMappingMode(FbxLayerElement::eByControlPoint);
	if (LayerElementVertexColor)
	{
		VertexColorReferenceMode = LayerElementVertexColor->GetReferenceMode();
		VertexColorMappingMode = LayerElementVertexColor->GetMappingMode();
	}

	//
	// get the first normal layer
	//
	FbxLayerElementNormal* LayerElementNormal = BaseLayer->GetNormals();
	FbxLayerElementTangent* LayerElementTangent = BaseLayer->GetTangents();
	FbxLayerElementBinormal* LayerElementBinormal = BaseLayer->GetBinormals();

	//whether there is normal, tangent and binormal data in this mesh
	bool bHasNTBInformation = LayerElementNormal && LayerElementTangent && LayerElementBinormal;

	FbxLayerElement::EReferenceMode NormalReferenceMode(FbxLayerElement::eDirect);
	FbxLayerElement::EMappingMode NormalMappingMode(FbxLayerElement::eByControlPoint);
	if (LayerElementNormal)
	{
		NormalReferenceMode = LayerElementNormal->GetReferenceMode();
		NormalMappingMode = LayerElementNormal->GetMappingMode();
	}

	FbxLayerElement::EReferenceMode TangentReferenceMode(FbxLayerElement::eDirect);
	FbxLayerElement::EMappingMode TangentMappingMode(FbxLayerElement::eByControlPoint);
	if (LayerElementTangent)
	{
		TangentReferenceMode = LayerElementTangent->GetReferenceMode();
		TangentMappingMode = LayerElementTangent->GetMappingMode();
	}

	FbxLayerElement::EReferenceMode BinormalReferenceMode(FbxLayerElement::eDirect);
	FbxLayerElement::EMappingMode BinormalMappingMode(FbxLayerElement::eByControlPoint);
	if (LayerElementBinormal)
	{
		BinormalReferenceMode = LayerElementBinormal->GetReferenceMode();
		BinormalMappingMode = LayerElementBinormal->GetMappingMode();
	}

	//
	// build un-mesh triangles
	//

	// Construct the matrices for the conversion from right handed to left handed system
	FbxAMatrix TotalMatrix;
	FbxAMatrix TotalMatrixForNormal;
	TotalMatrix = ComputeTotalMatrix(Node, Scene);
	TotalMatrixForNormal = TotalMatrix.Inverse();
	TotalMatrixForNormal = TotalMatrixForNormal.Transpose();
	int32 TriangleCount = Mesh->GetPolygonCount();

	if (TriangleCount == 0)
	{
		return false;
	}

	int32 VertexCount = Mesh->GetControlPointsCount();
	int32 WedgeCount = TriangleCount * 3;
	bool OddNegativeScale = IsOddNegativeScale(TotalMatrix);

	int32 VertexOffset = RawMesh.VertexPositions.Num();
	int32 WedgeOffset = RawMesh.Triangles.Num();
	int32 TriangleOffset = RawMesh.FaceMaterialIndices.Num();

	int32 MaxMaterialIndex = 0;

	// Reserve space for attributes.
	RawMesh.FaceMaterialIndices.AddZeroed(TriangleCount);
	RawMesh.FaceSmoothingMasks.AddZeroed(TriangleCount);
	RawMesh.Triangles.AddZeroed(WedgeCount);

	if (bHasNTBInformation || RawMesh.WedgeTangentX.Num() > 0 || RawMesh.WedgeTangentY.Num() > 0)
	{
		RawMesh.WedgeTangentX.AddZeroed(WedgeOffset + WedgeCount - RawMesh.WedgeTangentX.Num());
		RawMesh.WedgeTangentY.AddZeroed(WedgeOffset + WedgeCount - RawMesh.WedgeTangentY.Num());
	}

	if (LayerElementNormal || RawMesh.WedgeTangentZ.Num() > 0)
	{
		RawMesh.WedgeTangentZ.AddZeroed(WedgeOffset + WedgeCount - RawMesh.WedgeTangentZ.Num());
	}

	if (LayerElementVertexColor || RawMesh.WedgeColors.Num())
	{
		int32 NumNewColors = WedgeOffset + WedgeCount - RawMesh.WedgeColors.Num();
		int32 FirstNewColor = RawMesh.WedgeColors.Num();
		RawMesh.WedgeColors.AddUninitialized(NumNewColors);
		for (int32 WedgeIndex = FirstNewColor; WedgeIndex < FirstNewColor + NumNewColors; ++WedgeIndex)
		{
			RawMesh.WedgeColors[WedgeIndex] = FColor::White;
		}
	}

	// When importing multiple mesh pieces to the same static mesh.  Ensure each mesh piece has the same number of Uv's
	int32 ExistingUVCount = 0;
	for (int32 ExistingUVIndex = 0; ExistingUVIndex < MAX_MESH_TEXTURE_COORDS; ++ExistingUVIndex)
	{
		if (RawMesh.WedgeTexCoords[ExistingUVIndex].Num() > 0)
		{
			// Mesh already has UVs at this index
			++ExistingUVCount;
		}
		else
		{
			// No more UVs
			break;
		}
	}

	int32 UVCount = FMath::Max(FBXUVs.UniqueUVCount, ExistingUVCount);

	// At least one UV set must exist.  
	UVCount = FMath::Max(1, UVCount);

	for (int32 UVLayerIndex = 0; UVLayerIndex < UVCount; UVLayerIndex++)
	{
		RawMesh.WedgeTexCoords[UVLayerIndex].AddZeroed(WedgeOffset + WedgeCount - RawMesh.WedgeTexCoords[UVLayerIndex].Num());
	}

	int32 TriangleIndex;
	TMap<int32, int32> IndexMap;
	bool bHasNonDegenerateTriangles = false;

	for (TriangleIndex = 0; TriangleIndex < TriangleCount; TriangleIndex++)
	{
		int32 DestTriangleIndex = TriangleOffset + TriangleIndex;
		FVector CornerPositions[3];

		for (int32 CornerIndex = 0; CornerIndex < 3; CornerIndex++)
		{
			// If there are odd number negative scale, invert the vertex order for triangles
			int32 WedgeIndex = WedgeOffset + TriangleIndex * 3 + (OddNegativeScale ? 2 - CornerIndex : CornerIndex);

			// Store vertex index and position.
			int32 ControlPointIndex = Mesh->GetPolygonVertex(TriangleIndex, CornerIndex);
			int32* ExistingIndex = IndexMap.Find(ControlPointIndex);
			if (ExistingIndex)
			{
				RawMesh.Triangles[WedgeIndex] = *ExistingIndex;
				CornerPositions[CornerIndex] = RawMesh.VertexPositions[*ExistingIndex];
			}
			else
			{
				FbxVector4 FbxPosition = Mesh->GetControlPoints()[ControlPointIndex];
				FbxVector4 FinalPosition = TotalMatrix.MultT(FbxPosition);
				int32 VertexIndex = RawMesh.VertexPositions.Add(ConvertPos(FinalPosition));
				RawMesh.Triangles[WedgeIndex] = VertexIndex;
				IndexMap.Add(ControlPointIndex, VertexIndex);
				CornerPositions[CornerIndex] = RawMesh.VertexPositions[VertexIndex];
			}

			//
			// normals, tangents and binormals
			//
			if (LayerElementNormal)
			{
				int TriangleCornerIndex = TriangleIndex * 3 + CornerIndex;
				//normals may have different reference and mapping mode than tangents and binormals
				int NormalMapIndex = (NormalMappingMode == FbxLayerElement::eByControlPoint) ?
					ControlPointIndex : TriangleCornerIndex;
				int NormalValueIndex = (NormalReferenceMode == FbxLayerElement::eDirect) ?
					NormalMapIndex : LayerElementNormal->GetIndexArray().GetAt(NormalMapIndex);

				//tangents and binormals share the same reference, mapping mode and index array
				if (bHasNTBInformation)
				{
					int TangentMapIndex = (TangentMappingMode == FbxLayerElement::eByControlPoint) ?
						ControlPointIndex : TriangleCornerIndex;
					int TangentValueIndex = (TangentReferenceMode == FbxLayerElement::eDirect) ?
						TangentMapIndex : LayerElementTangent->GetIndexArray().GetAt(TangentMapIndex);

					FbxVector4 TempValue = LayerElementTangent->GetDirectArray().GetAt(TangentValueIndex);
					TempValue = TotalMatrixForNormal.MultT(TempValue);
					FVector TangentX = ConvertDir(TempValue);
					RawMesh.WedgeTangentX[WedgeIndex] = TangentX.GetSafeNormal();

					int BinormalMapIndex = (BinormalMappingMode == FbxLayerElement::eByControlPoint) ?
						ControlPointIndex : TriangleCornerIndex;
					int BinormalValueIndex = (BinormalReferenceMode == FbxLayerElement::eDirect) ?
						BinormalMapIndex : LayerElementBinormal->GetIndexArray().GetAt(BinormalMapIndex);

					TempValue = LayerElementBinormal->GetDirectArray().GetAt(BinormalValueIndex);
					TempValue = TotalMatrixForNormal.MultT(TempValue);
					FVector TangentY = -ConvertDir(TempValue);
					RawMesh.WedgeTangentY[WedgeIndex] = TangentY.GetSafeNormal();
				}

				FbxVector4 TempValue = LayerElementNormal->GetDirectArray().GetAt(NormalValueIndex);
				TempValue = TotalMatrixForNormal.MultT(TempValue);
				FVector TangentZ = ConvertDir(TempValue);
				RawMesh.WedgeTangentZ[WedgeIndex] = TangentZ.GetSafeNormal();
			}
		}

		// Check if the triangle just discovered is non-degenerate if we haven't found one yet
		if (!bHasNonDegenerateTriangles)
		{
			float ComparisonThreshold = THRESH_POINTS_ARE_SAME;

			if (!(CornerPositions[0].Equals(CornerPositions[1], ComparisonThreshold)
				|| CornerPositions[0].Equals(CornerPositions[2], ComparisonThreshold)
				|| CornerPositions[1].Equals(CornerPositions[2], ComparisonThreshold)))
			{
				bHasNonDegenerateTriangles = true;
			}
		}

		//
		// smoothing mask
		//
		if (bSmoothingAvailable && SmoothingInfo)
		{
			if (SmoothingMappingMode == FbxLayerElement::eByPolygon)
			{
				int lSmoothingIndex = (SmoothingReferenceMode == FbxLayerElement::eDirect) ? TriangleIndex : SmoothingInfo->GetIndexArray().GetAt(TriangleIndex);
				RawMesh.FaceSmoothingMasks[DestTriangleIndex] = SmoothingInfo->GetDirectArray().GetAt(lSmoothingIndex);
			}
		}

		//
		// uvs
		//
		// In FBX file, the same UV may be saved multiple times, i.e., there may be same UV in LayerElementUV
		// So we don't import the duplicate UVs
		//UE_LOG(ConvertLog, Log, TEXT("--------- FBXUVs.UniqueUVCount is: %d"), FBXUVs.UniqueUVCount);
		int32 UVLayerIndex;
		for (UVLayerIndex = 0; UVLayerIndex < FBXUVs.UniqueUVCount; UVLayerIndex++)
		{
			//UE_LOG(ConvertLog, Log, TEXT("--------- FBXUVs.LayerElementUV[UVLayerIndex] is: %d"), FBXUVs.LayerElementUV[UVLayerIndex] != NULL);
			if (FBXUVs.LayerElementUV[UVLayerIndex] != NULL)
			{
				for (int32 CornerIndex = 0; CornerIndex < 3; CornerIndex++)
				{
					// If there are odd number negative scale, invert the vertex order for triangles
					int32 WedgeIndex = WedgeOffset + TriangleIndex * 3 + (OddNegativeScale ? 2 - CornerIndex : CornerIndex);


					int lControlPointIndex = Mesh->GetPolygonVertex(TriangleIndex, CornerIndex);
					int UVMapIndex = (FBXUVs.UVMappingMode[UVLayerIndex] == FbxLayerElement::eByControlPoint) ? lControlPointIndex : TriangleIndex * 3 + CornerIndex;
					int32 UVIndex = (FBXUVs.UVReferenceMode[UVLayerIndex] == FbxLayerElement::eDirect) ?
						UVMapIndex : FBXUVs.LayerElementUV[UVLayerIndex]->GetIndexArray().GetAt(UVMapIndex);

					FbxVector2	UVVector = FBXUVs.LayerElementUV[UVLayerIndex]->GetDirectArray().GetAt(UVIndex);

					RawMesh.WedgeTexCoords[UVLayerIndex][WedgeIndex].X = static_cast<float>(UVVector[0]);
					RawMesh.WedgeTexCoords[UVLayerIndex][WedgeIndex].Y = 1.f - static_cast<float>(UVVector[1]);   //flip the Y of UVs for DirectX
																												  //UE_LOG(ConvertLog, Log, TEXT("--------- RawMesh.WedgeTexCoords[UVLayerIndex][WedgeIndex] is: %s"), *RawMesh.WedgeTexCoords[UVLayerIndex][WedgeIndex].ToString());
				}
			}
		}

		//
		// material index
		//
		int32 MaterialIndex = 0;
		if (MaterialCount > 0)
		{
			if (LayerElementMaterial)
			{
				switch (MaterialMappingMode)
				{
					// material index is stored in the IndexArray, not the DirectArray (which is irrelevant with 2009.1)
				case FbxLayerElement::eAllSame:
				{
					MaterialIndex = LayerElementMaterial->GetIndexArray().GetAt(0);
				}
				break;
				case FbxLayerElement::eByPolygon:
				{
					MaterialIndex = LayerElementMaterial->GetIndexArray().GetAt(TriangleIndex);
				}
				break;
				}
			}
		}
		MaterialIndex += MaterialIndexOffset;

		if (MaterialIndex >= MaterialCount + MaterialIndexOffset || MaterialIndex < 0)
		{
			MaterialIndex = 0;
		}

		RawMesh.FaceMaterialIndices[DestTriangleIndex] = MaterialIndex;
	}

	// needed?
	FBXUVs.Cleanup();

	bool bIsValidMesh = bHasNonDegenerateTriangles;

	return bIsValidMesh;
}

bool UFbxConverterFunctionLibrary::ConvertFbxFileToMeshFile(const FString& FbxFileAbsolutePath, TArray<FPMCSection>& OutMeshInfos)
{
	FbxManager* SdkManager = nullptr;
	InitializeManager(SdkManager);
	FbxImporter* Importer = nullptr;
	if (OpenFile(FbxFileAbsolutePath, SdkManager, Importer))
	{
		FbxScene* Scene = nullptr;
		if (ImportFile(SdkManager, Importer, Scene))
		{
			ConvertScene(Scene);
			ValidateAllMeshesAreReferenceByNodeAttribute(Scene);
			FbxNode* RootNode = Scene->GetRootNode();
			TArray<FbxNode*> MeshNodeArray;
			RecursiveGetAllMeshNode(MeshNodeArray, RootNode);
			TArray<FCustomFbxMaterial> MeshMaterials;
			FCustomRawMesh NewRawMesh;
			// A mapping of vertex positions to their color in the existing static mesh
			FbxGeometryConverter* GeometryConverter = new FbxGeometryConverter(SdkManager);
			for (int MeshIndex = 0; MeshIndex < MeshNodeArray.Num(); MeshIndex++)
			{
				FbxNode* Node = MeshNodeArray[MeshIndex];

				if (Node->GetMesh())
				{
					if (!BuildStaticMeshFromGeometry(Node, Scene, MeshMaterials, GeometryConverter, NewRawMesh))
					{
						//break;
					}
				}
			}
			Scene->Destroy();
			Importer->Destroy();
			SdkManager->Destroy();

			TMap<int32, int32> MaterialIndexMap;
			TMap<int32, struct FMaterialMesh> MaterialUVMap;
			int32 FaceIndex = 0;
			for (auto& Iter : NewRawMesh.FaceMaterialIndices)
			{
				UE_LOG(ConvertLog, Log, TEXT("Total is:  %d  current is : %d"), NewRawMesh.FaceMaterialIndices.Num(), FaceIndex);
				int32 MeshIndex = -1;
				if (MaterialIndexMap.Contains(Iter))
				{
					MeshIndex = MaterialIndexMap[Iter];
				}
				else
				{
					MeshIndex = OutMeshInfos.AddDefaulted();
					MaterialIndexMap.Add(Iter, MeshIndex);
					MaterialUVMap.Add(MeshIndex, FMaterialMesh());
				}
				if (OutMeshInfos.IsValidIndex(MeshIndex))
				{
					int32 StartIndex = 3 * FaceIndex;
					int32 Offset = OutMeshInfos[MeshIndex].Triangles.AddZeroed(3);
					for (int32 i = 0; i < 3; ++i)
					{
						const int32 P = NewRawMesh.Triangles[StartIndex + i];
						const int32 CountBefore = OutMeshInfos[MeshIndex].Vertexes.Num();
						if (MaterialUVMap[MeshIndex].VertexUVMap.Contains(NewRawMesh.VertexPositions[P]))
						{
							int32 OldIndex = MaterialUVMap[MeshIndex].VertexUVMap[NewRawMesh.VertexPositions[P]].DoesUvExists(NewRawMesh.WedgeTexCoords[0][StartIndex + i]);
							if (-1 != OldIndex)
							{
								OutMeshInfos[MeshIndex].Triangles[Offset + i] = OldIndex;
							}
							else
							{
								int32 NewP = OutMeshInfos[MeshIndex].Vertexes.Add(NewRawMesh.VertexPositions[P]);
								OutMeshInfos[MeshIndex].UV.Add(NewRawMesh.WedgeTexCoords[0][StartIndex + i]);
								OutMeshInfos[MeshIndex].Normals.Add(NewRawMesh.WedgeTangentZ[StartIndex + i]);
								OutMeshInfos[MeshIndex].Triangles[Offset + i] = NewP;
								MaterialUVMap[MeshIndex].VertexUVMap[NewRawMesh.VertexPositions[P]].AddUV(FVertexUVAndIndexPair(NewRawMesh.WedgeTexCoords[0][StartIndex + i], NewP));
							}
						}
						else
						{
							int32 NewP = OutMeshInfos[MeshIndex].Vertexes.AddUnique(NewRawMesh.VertexPositions[P]);
							OutMeshInfos[MeshIndex].UV.Add(NewRawMesh.WedgeTexCoords[0][StartIndex + i]);
							OutMeshInfos[MeshIndex].Normals.Add(NewRawMesh.WedgeTangentZ[StartIndex + i]);
							OutMeshInfos[MeshIndex].Triangles[Offset + i] = NewP;
							MaterialUVMap[MeshIndex].VertexUVMap.Add(NewRawMesh.VertexPositions[P], FCustomVertexUVs(FVertexUVAndIndexPair(NewRawMesh.WedgeTexCoords[0][StartIndex + i], NewP)));
						}
					}
				}
				++FaceIndex;
			}

			return true;
		}
	}
	return false;
}
