// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "MagicCore/Public/PMCSection.h"
#include "FbxConverterFunctionLibrary.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(ConvertLog, Log, All);



/**
 *
 */
UCLASS()
class FBXFILEIMPORT_API UFbxConverterFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	static bool ConvertFbxFileToMeshFile(const FString& FbxFileAbsolutePath, TArray<FPMCSection>& OutMeshInfos);

protected:

	struct FVertexUVAndIndexPair
	{
		FVector2D UV;
		int32	Index;
		FVertexUVAndIndexPair() {}
		FVertexUVAndIndexPair(const FVector2D& InUv, const int32& InIndex) :UV(InUv), Index(InIndex) {}
		bool operator==(const FVertexUVAndIndexPair& InLeft) const
		{
			return UV.Equals(InLeft.UV);
		}
	};

	struct FCustomVertexUVs
	{
		TArray<FVertexUVAndIndexPair> UVIndexPairs;
		FCustomVertexUVs() {}
		FCustomVertexUVs(const FVertexUVAndIndexPair& InData)
		{
			UVIndexPairs.Add(InData);
		}
		void AddUV(const FVertexUVAndIndexPair& InData)
		{
			UVIndexPairs.AddUnique(InData);
		}
		int32 DoesUvExists(const FVector2D& InUv)
		{
			FVertexUVAndIndexPair* Result = UVIndexPairs.FindByPredicate([&](const FVertexUVAndIndexPair& InOther)->bool { return InOther.UV.Equals(InUv); });
			//UE_LOG(ConvertLog, Log, TEXT("Find %s %d"), *InUv.ToString(), nullptr != Result);
			return  nullptr != Result ? Result->Index : -1;
		}
	};

	struct FMaterialMesh
	{
		TMap<FVector, struct FCustomVertexUVs> VertexUVMap;
		FMaterialMesh() {}
	};
};
