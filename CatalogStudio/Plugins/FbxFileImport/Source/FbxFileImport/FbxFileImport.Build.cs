// Fill out your copyright notice in the Description page of Project Settings.
using System.IO;
using UnrealBuildTool;

public class FbxFileImport : ModuleRules
{
	private string ThirdPartyPath
	{
		get { return Path.GetFullPath(Path.Combine(ModuleDirectory, "../../ThirdParty/")); }
	}

	public FbxFileImport(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] { "Core", "CoreUObject", "Engine", "MagicCore" });

		PrivateDependencyModuleNames.AddRange(new string[] { "Core", "CoreUObject", "Engine", "MagicCore" });

		AddEngineThirdPartyPrivateStaticDependencies(Target, "FBX");

		//LoadFbxSdk(Target);

		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
	}

	public bool LoadFbxSdk(ReadOnlyTargetRules Target)
	{
		bool isLibrarySupported = false;
		string FBXAPIPath = Path.Combine(ThirdPartyPath, "FBXSDK");
		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
			isLibrarySupported = true;

			//string LibrariesPath = Path.Combine(FBXAPIPath, "lib", "x64", "release");
			//if (Target.LinkType != TargetLinkType.Monolithic)
			//{
			//	System.Console.WriteLine(LibrariesPath);
			//	PublicAdditionalLibraries.Add(Path.Combine(LibrariesPath, "libfbxsdk.lib"));

			//	// We are using DLL versions of the FBX libraries
			//	//PublicDefinitions.Add("FBXSDK_SHARED");

			//	//RuntimeDependencies.Add("$(EngineDir)/Binaries/Win64/libfbxsdk.dll");
			//}
			//else
			//{
			//	if (Target.bUseStaticCRT)
			//	{
			//		PublicAdditionalLibraries.Add(Path.Combine(LibrariesPath, "libfbxsdk-mt.lib"));
			//	}
			//	else
			//	{
			//		PublicAdditionalLibraries.Add(Path.Combine(LibrariesPath, "libfbxsdk-md.lib"));
			//	}
			//}
		}

		if (isLibrarySupported)
		{
			// Include path
			PublicIncludePaths.Add(Path.Combine(FBXAPIPath, "include"));
			PrivateIncludePaths.Add(Path.Combine(FBXAPIPath, "include"));
			PublicIncludePaths.Add(Path.Combine(FBXAPIPath, "include", "fbxsdk"));
			PrivateIncludePaths.Add(Path.Combine(FBXAPIPath, "include", "fbxsdk"));
		}

		return isLibrarySupported;
	}
}
