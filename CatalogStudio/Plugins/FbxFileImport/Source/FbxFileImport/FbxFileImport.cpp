// Copyright Epic Games, Inc. All Rights Reserved.

#include "FbxFileImport.h"
#include "Core.h"
#include "Modules/ModuleManager.h"

#define LOCTEXT_NAMESPACE "FFbxFileImportModule"

void FFbxFileImportModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	// Get the base directory of this plugin
//	FString BaseDir = IPluginManager::Get().FindPlugin("OsgbFileReader")->GetBaseDir();
//
//	// Add on the relative location of the third party dll and load it
//	FString LibraryPath;
//#if PLATFORM_WINDOWS
//	LibraryPath = FPaths::Combine(*BaseDir, TEXT("Binaries/ThirdParty/OsgbReader/Win64/OsgbReader.dll"));
//#elif PLATFORM_MAC
//	LibraryPath = FPaths::Combine(*BaseDir, TEXT("Source/ThirdParty/OsgbReader/Mac/Release/OsgbReader.dylib"));
//#endif // PLATFORM_WINDOWS
//	LibraryPath = FPaths::ConvertRelativePathToFull(LibraryPath);
//	UE_LOG(LogTemp, Log, TEXT("LibraryPath=%s"), *LibraryPath);
//	OsgbLibraryHandle = !LibraryPath.IsEmpty() ? FPlatformProcess::GetDllHandle(*LibraryPath) : nullptr;

	//if (!OsgbLibraryHandle)
	//{
	//	FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("ThirdPartyLibraryError", "Failed to load example third party library"));
	//}
}

void FFbxFileImportModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	//if (OsgbLibraryHandle)
	//{
	//	FPlatformProcess::FreeDllHandle(OsgbLibraryHandle);
	//	OsgbLibraryHandle = nullptr;
	//}
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FFbxFileImportModule, FbxFileImport)