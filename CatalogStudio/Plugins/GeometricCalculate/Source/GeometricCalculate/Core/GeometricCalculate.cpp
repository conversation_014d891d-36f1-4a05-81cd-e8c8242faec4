// Copyright Epic Games, Inc. All Rights Reserved.

#include "GeometricCalculate.h"
#include "Runtime/Projects/Public/Interfaces/IPluginManager.h"
#include "Runtime/Core/Public/HAL/FileManager.h"

#define LOCTEXT_NAMESPACE "FGeometricCalculateModule"

void FGeometricCalculateModule::StartupModule()
{
	const FString PluginDir = IPluginManager::Get().FindPlugin(TEXT("GeometricCalculate"))->GetBaseDir();
	FString BinPath;
	IFileManager& FileManager = IFileManager::Get();
}

void FGeometricCalculateModule::ShutdownModule()
{
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FGeometricCalculateModule, GeometricCalculate)