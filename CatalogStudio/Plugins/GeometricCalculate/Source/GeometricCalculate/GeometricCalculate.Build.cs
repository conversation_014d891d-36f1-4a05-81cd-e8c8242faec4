// Copyright Epic Games, Inc. All Rights Reserved.

using System.IO;
using UnrealBuildTool;

public class GeometricCalculate : ModuleRules
{

    private string ThirdPartyPath
    {
        get { return Path.GetFullPath(Path.Combine(ModuleDirectory, "../../ThirdParty/")); }
    }
    private string BinariesWin64Path
    {
        get { return Path.GetFullPath(Path.Combine(ModuleDirectory, "../../Binaries/Win64")); }
    }

    public GeometricCalculate(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        UndefinedIdentifierWarningLevel = WarningLevel.Off;

        PublicIncludePaths.AddRange(
			new string[] {  });
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
                 "Projects",
                 "Eigen",
                 "GeometryCore",
                 "Boost",
                 "DynamicMesh",
                 "GeometryFramework",
                 "GeometryAlgorithms",
                 "MagicCore",
                 "GeometryScriptingCore"

				// ... add other public dependencies that you statically link with here ...
			}
            );
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore",
                 "Eigen",
                 "GeometryCore",
                 "Boost",
                 "DynamicMesh",
                 "GeometryFramework",
                 "GeometryAlgorithms",
                 "MagicCore",
                 "GeometryScriptingCore"
				// ... add private dependencies that you statically link with here ...	
			}
            );
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);
	}
}
