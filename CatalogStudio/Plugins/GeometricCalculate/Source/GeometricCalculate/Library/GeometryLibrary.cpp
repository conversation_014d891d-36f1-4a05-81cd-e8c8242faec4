// Copyright Epic Games, Inc. All Rights Reserved.

#include "GeometryLibrary.h"
#include "Generators/PolygonEdgeMeshGenerator.h"
#include "Generators/PlanarPolygonMeshGenerator.h"
#include "DynamicMesh/Public/Operations/MinimalHoleFiller.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "DynamicMesh/Public/Remesher.h"
#include "Generators/LineSegmentGenerators.h"
#include "Generators/MinimalBoxMeshGenerator.h"
#include "Generators/SweepGenerator.h"
#include "GeometryAlgorithms/Public/XAtlasWrapper.h"
#include "Implicit/Morphology.h"
#include "Implicit/Solidify.h"
#include "MeshSimplification.h"

#pragma warning(push)
#pragma warning(disable:6294) /* Ill-defined for-loop:  initial condition does not satisfy test.  Loop body not executed. */
#pragma warning(disable:6326) /* Potential comparison of a constant with another constant. */
#pragma warning(disable:4456) /* declaration of 'LocalVariable' hides previous local declaration */ 
#pragma warning(disable:4457) /* declaration of 'LocalVariable' hides function parameter */ 
#pragma warning(disable:4458) /* declaration of 'LocalVariable' hides class member */ 
#pragma warning(disable:4459) /* declaration of 'LocalVariable' hides global declaration */ 
#pragma warning(disable:6244) /* local declaration of <variable> hides previous declaration at <line> of <file> */
#pragma warning(disable:4702) /* unreachable code */

PRAGMA_DEFAULT_VISIBILITY_START
PRAGMA_DISABLE_DEPRECATION_WARNINGS

#include <Eigen/src/Geometry/Quaternion.h>

PRAGMA_ENABLE_DEPRECATION_WARNINGS
PRAGMA_DEFAULT_VISIBILITY_END

#pragma warning(pop)

using namespace UE::Geometry;
using namespace UE::Math;

#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

void FGeometryLibrary::CheckLibrary()
{
}

FMatrix FGeometryLibrary::GetTransformMatrixFromVectors(const FVector& A, const FVector& B)
{
    if (FVector::Parallel(A, B))
    {
        return FMatrix::Identity;
    }

    FVector FromNorm = A.GetSafeNormal();
    FVector ToNorm = B.GetSafeNormal();
    FVector Axis = FVector::CrossProduct(FromNorm, ToNorm);
    float Angle = -FMath::Acos(FVector::DotProduct(FromNorm, ToNorm));
    FQuat RotationQuat = FQuat(Axis, Angle);
    FMatrix RotationMatrix = FRotationMatrix::Make(RotationQuat);

    FMatrix TranslationMatrix = FMatrix::Identity;
    TranslationMatrix.M[3][0] = B.X;
    TranslationMatrix.M[3][1] = B.Y;
    TranslationMatrix.M[3][2] = B.Z;

    FMatrix TransformMatrix = RotationMatrix * TranslationMatrix;
    return TransformMatrix;
}

void FGeometryLibrary::transformationMaterix(const Eigen::Vector3d& inVecBefore, const Eigen::Vector3d& inVecAfter, Eigen::Matrix3d& outMat)
{
    outMat = Eigen::Quaterniond::FromTwoVectors(inVecBefore, inVecAfter).toRotationMatrix();
    //auto M = GetTransformMatrixFromVectors(FVector(inVecBefore.x(), inVecBefore.y(), inVecBefore.z()), FVector(inVecAfter.x(), inVecAfter.y(), inVecAfter.z()));
   /* outMat << M.M[0][0], M.M[0][1], M.M[0][2],
        M.M[1][0], M.M[1][1], M.M[1][2],
        M.M[2][0], M.M[2][1], M.M[2][2];*/
}

void FGeometryLibrary::transformationPlan(const FVector& inVecBefore, const FVector& inVecAfter, TArray<FVector>& inOutPlan)
{
    if (inOutPlan.Num() < 3)
    {
        return;
    }
    int32 numberOfVertices = inOutPlan.Num();

    int32 i = 0;
    Eigen::Matrix3d transformtion;
    Eigen::Vector3d norVec;
    norVec << inVecBefore.X, inVecBefore.Y, inVecBefore.Z;

    Eigen::Vector3d zVec;
    zVec << inVecAfter.X, inVecAfter.Y, inVecAfter.Z;
    transformationMaterix(norVec, zVec, transformtion);
    int index = 0;
    for (auto& iter : inOutPlan)
    {
        Eigen::Vector3d beforeVec;
        beforeVec << iter.X, iter.Y, iter.Z;
        auto newVec = transformtion * beforeVec;
        iter = FVector(newVec.x(), newVec.y(), newVec.z());
        ++index;
    }
}

void FGeometryLibrary::transformationPoint(const FVector& inVecBefore, const FVector& inVecAfter, FVector& inOutPoint)
{

    int32 i = 0;
    Eigen::Matrix3d transformtion;
    Eigen::Vector3d norVec;
    norVec << inVecBefore.X, inVecBefore.Y, inVecBefore.Z;

    Eigen::Vector3d zVec;
    zVec << inVecAfter.X, inVecAfter.Y, inVecAfter.Z;
    transformationMaterix(norVec, zVec, transformtion);
    Eigen::Vector3d beforeVec;
    beforeVec << inOutPoint.X, inOutPoint.Y, inOutPoint.Z;
    auto newVec = transformtion * beforeVec;
    inOutPoint = FVector(newVec.x(), newVec.y(), newVec.z());
}

void FGeometryLibrary::delaunayTriangulation(const TArray<FVector>& inPoints, const FVector& inNormal, FGeoMesh& outMesh)
{
    if (inPoints.Num() < 3)
    {
        return;
    }

    int32 numberOfVertices = inPoints.Num();
    //Eigen::MatrixXd vertices(numberOfVertices, 2);
    int32 i = 0;
    Eigen::Matrix3d transformtion;
    Eigen::Vector3d norVec;
    norVec << inNormal.X, inNormal.Y, inNormal.Z;

    Eigen::Vector3d zVec;
    zVec << .0f, .0f, 1.f;
    transformationMaterix(norVec, zVec, transformtion);
    int index = 0;
    for (auto& iter : inPoints)
    {
        Eigen::Vector3d beforeVec;
        beforeVec << iter.X, iter.Y, iter.Z;
        auto newVec = transformtion * beforeVec;

        ++index;
    }


}

void FGeometryLibrary::createPlanarPolygonMesh(const TArray<FVector>& inPoints, const FVector& inNormal, FGeoMesh& outMesh)
{
    TArray<FVector> tempPot;
    TArray<FVector> tempVec;



    for (const auto&  iter : inPoints)
    {
        tempPot.AddUnique(iter);
    }
    auto numOfPoints = tempPot.Num();

    for (int32 cur = 0; cur < numOfPoints; cur++)
    {
        //int32 pre = (cur - 1 + numOfPoints) % numOfPoints;
        //int32 next = (cur + 1) % numOfPoints;
        //auto v0 = tempPot[cur] - tempPot[pre];
        //v0.Normalize();
        //auto v1 = tempPot[next] - tempPot[cur];
        //v1.Normalize();

        //if (!v0.Cross(v1).Equals(FVector::ZeroVector,0.000001f))
        {
            tempVec.AddUnique(tempPot[cur]);
        }

    }

    if (tempVec.Num() < 3)
    {
        return;
    }
    if (FMath::IsNearlyEqual(tempVec[0].Z, 0.0f) && !FMath::IsNearlyEqual(tempVec[0].X, 0.0f) || FMath::IsNearlyEqual(tempVec[0].Y, 0.0f))
    {
        auto t = tempVec[0];
        tempVec.RemoveAt(0);
        tempVec.Add(t);
    }
    
    int32 numOfVertices = tempVec.Num();
    Eigen::Matrix3d transformtion;
    Eigen::Vector3d norVec;
    norVec << inNormal.X, inNormal.Y, inNormal.Z;

    Eigen::Vector3d zVec;
    zVec << .0f, .0f, 1.f;
    transformationMaterix(norVec, zVec, transformtion);

    TArray<FVector2D> polygonVerts;

    polygonVerts.SetNumZeroed(numOfVertices);
    int index = 0;
    for (auto& iter : tempVec)
    {
        Eigen::Vector3d beforeVec;
        beforeVec << iter.X, iter.Y, iter.Z;
        auto newVec = transformtion * beforeVec;
        FVector(newVec.x(), newVec.y(), newVec.z());
        polygonVerts[index] = FVector2D(newVec.x(), newVec.y());
        ++index;
    }

    FPlanarPolygonMeshGenerator planarPolygonMeshGenerator;
    planarPolygonMeshGenerator.SetPolygon(polygonVerts);
    planarPolygonMeshGenerator.Normal = FVector3f(.0f, .0f, 1.f);
    planarPolygonMeshGenerator.Generate();
    bool bIsClockwise = planarPolygonMeshGenerator.Polygon.IsClockwise();
    outMesh.vertexs.Append(tempVec);

    TArray<FIndex3i> triangles = planarPolygonMeshGenerator.Triangles;

    for (const auto& iter : triangles)
    {
        FVector nor;
        trangleNormal(outMesh.vertexs[iter[0]], outMesh.vertexs[iter[1]], outMesh.vertexs[iter[2]], nor);
        nor.Normalize();
        if (FVector::DotProduct(nor, inNormal) <  0)
        {
            outMesh.indices.Add(iter[0]);
            outMesh.indices.Add(iter[1]);
            outMesh.indices.Add(iter[2]);
        }
        else
        {
            outMesh.indices.Add(iter[2]);
            outMesh.indices.Add(iter[1]);
            outMesh.indices.Add(iter[0]);
        }

    }
    outMesh.uv = planarPolygonMeshGenerator.UVs;

    outMesh.normals.SetNumZeroed(outMesh.vertexs.Num());
    for (auto & iter : outMesh.normals)
    {
        iter.X = inNormal.X;
        iter.Y = inNormal.Y;
        iter.Z = inNormal.Z;

    }
}

void FGeometryLibrary::createCurvedSurfaceWithTwoArcSegment(const TArray<FVector>& inPointsA, const TArray<FVector>& inPointsB, FGeoMesh& outMesh)
{
    int32 numOfA = inPointsA.Num();
    int32 numOfB = inPointsB.Num();

    if (numOfA != numOfB)
    {
        return;
    }
    TMap<int32, FGeoMesh> meshMap;
    for (int32 i = 0; i < numOfA; ++i)
    {
        int32 next = (i + 1) % numOfA;
        TArray <FVector> subPlan;
        subPlan.SetNumZeroed(4);

        subPlan[0] = inPointsA[i];
        subPlan[1] = inPointsA[next];
        subPlan[2] = inPointsB[next];
        subPlan[3] = inPointsB[i];

        FVector nor = FVector::CrossProduct(subPlan[1] - subPlan[0], subPlan[2] - subPlan[1]);
        FGeoMesh newMesh;
        createPlanarPolygonMesh(subPlan, nor, newMesh);
        outMesh.Append(newMesh);
    }
}

void FGeometryLibrary::createEdgesPolygonMesh(const TArray<TPair<FVector,FVector>>& inEdge, const FVector& inNormal, FGeoMesh& outMesh)
{
    TArray<FFrame3d> polygon;
    int32 numOfEdge = inEdge.Num();
    polygon.SetNumZeroed(numOfEdge);
    int32 i = 0;
    for (auto & iter : inEdge)
    {
        FFrame3d edgeFrame;
        edgeFrame.Origin = iter.Key;
        FVector xAxis = iter.Value - iter.Key;
        xAxis.Normalize();
        edgeFrame.AlignAxis(0, xAxis);
        polygon[i] = edgeFrame;
    }
    TArray<double> offset;
    offset.SetNumZeroed(numOfEdge);
    FPolygonEdgeMeshGenerator polygonEdgeMeshGenerator(polygon, true, offset);
    polygonEdgeMeshGenerator.Generate();
    outMesh.vertexs.Append(polygonEdgeMeshGenerator.Vertices);

    TArray<FIndex3i> triangles = polygonEdgeMeshGenerator.Triangles;
    for (const auto& iter : triangles)
    {

        outMesh.indices.Add(iter[0]);
        outMesh.indices.Add(iter[1]);
        outMesh.indices.Add(iter[2]);
    }
    outMesh.uv = polygonEdgeMeshGenerator.UVs;
    outMesh.normals = polygonEdgeMeshGenerator.Normals;
}

void FGeometryLibrary::marchingCubes(const TArray<FVector>& inPoints, FGeoMesh& outMesh)
{
    //FMarchingCubes marchingCubesGenerator;


    //int32 i = 0;
    ////for (auto & iter : inPoints)
    ////{
    ////    //marchingCubesGenerator.VertexSectionLists;
    ////    marchingCubesGenerator.SetVertex(i, iter);
    ////    ++i;
    ////}
    //
    //TVector<double> min(.0f, .0f, .0f);
    //TVector<double> max(.0f, .0f, .0f);

    //for (auto & iter : inPoints)
    //{
    //    min = TVector<double>(
    //        TMathUtil<double>::Min(iter.X, min.X),
    //        TMathUtil<double>::Min(iter.Y, min.Y),
    //        TMathUtil<double>::Min(iter.Z, min.Z));

    //    max = TVector<double>(
    //        TMathUtil<double>::Max(iter.X, max.X),
    //        TMathUtil<double>::Max(iter.Y, max.Y),
    //        TMathUtil<double>::Max(iter.Z, max.Z));
    //}
    //marchingCubesGenerator.Bounds = TAxisAlignedBox3<double>(min, max);
    //marchingCubesGenerator.CubeSize = 0.25f;
    //auto shpere = ([&](const FVector& center, const FVector& p, const double& r) { return FMath::Pow(center.X - p.X,2)  + FMath::Pow(center.Y - p.Y, 2) + FMath::Pow(center.Z - p.Z, 2) - r * r; });

    //TFunction<double(TVector<double>)> function([&](const TVector<double>& inParam)
    //    {
    //        double r = 0.25f;
    //        double dis = 10.f;
    //        FVector c;
    //        for (const auto& iter : inPoints)
    //        {
    //            if (FMath::Min(FVector::Distance(inParam, iter),dis))
    //            {
    //                dis = FVector::Distance(inParam, iter);
    //                c = iter;
    //            }
    //        }

    //        return shpere(c, inParam,r);
    //    });
    //marchingCubesGenerator.Implicit = function;
    //marchingCubesGenerator.GenerateContinuation(inPoints);



    //marchingCubesGenerator.Generate();
    //outMesh.vertexs = marchingCubesGenerator.Vertices;
    //for (auto & iter : marchingCubesGenerator.Triangles)
    //{
    //    outMesh.indices.Add(iter.A);
    //    outMesh.indices.Add(iter.B);
    //    outMesh.indices.Add(iter.C);
    //}    

    //outMesh.uv = marchingCubesGenerator.UVs;

    //outMesh.normals = marchingCubesGenerator.Normals;
}

void FGeometryLibrary::blendMarchingCubes(const TArray<FVector>& inPoints, FGeoMesh& outMesh)
{
}

void FGeometryLibrary::ballPivotingMesh(const TArray<FVector>& inPoints, FGeoMesh& outMesh)
{

    //const double radius = 0.5f;
    //const double radius_mls = radius * 2.0;
    //int numOfPoints = inPoints.Num();
    //pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGBNormal>());

    //for (const auto & iter : inPoints)
    //{
    //    pcl::PointXYZRGBNormal point((float)iter.X, (float)iter.Y, (float)iter.Z);
    //    cloud->push_back(point);
    //}
   
    //Pivoter<pcl::PointXYZRGBNormal> pivoter;

    //pivoter.setSearchRadius(radius);
    //pivoter.setInputCloud(cloud);
    //pivoter.setEstimatedRadius(numOfPoints, 5, 0.99f);
    //pcl::PolygonMesh::Ptr mesh = pivoter.proceed();
    //outMesh.vertexs = inPoints;
    //for (auto & iter : mesh->polygons)
    //{
    //    outMesh.indices.Add(iter.vertices[0]);
    //    outMesh.indices.Add(iter.vertices[1]);
    //    outMesh.indices.Add(iter.vertices[2]);
    //}

}

bool FGeometryLibrary::createArcSegmentByRadius(const double& radius, const FVector& startPoint, const FVector& endPoint, const int32& inStep, const  FVector& inNormal,bool bBigPart, const FTransform& transform, TArray<FVector>& outVertices)
{
    if (radius == 0)
    {
        return false;
    }
    auto shortRadius = radius;// +0.09;


    FVector segVec = endPoint - startPoint;
    segVec.Normalize();
    FVector segCenter = (endPoint + startPoint) * 0.5f;
    double segLen = FVector::Distance(startPoint, endPoint);

    FVector xAxis = segVec;
    FVector yAxis = -FVector::CrossProduct(segVec, inNormal);
    yAxis.Normalize();
    if (shortRadius < 0)
    {
        yAxis = -yAxis;
    }
    segLen = FCString::Atof(*FString::Printf(TEXT(" % .2f"), segLen));
    double disFromLineToCenter = FMath::Pow(shortRadius,2) - FMath::Pow(segLen * .5f, 2);
    if (disFromLineToCenter < -0.00000001f)
    {
        return false;
    }
    disFromLineToCenter = FMath::Sqrt(disFromLineToCenter);
    FVector center = bBigPart ? segCenter + yAxis * disFromLineToCenter : segCenter - yAxis * disFromLineToCenter;
    int32 step = 4;

    double  circumference   = 2 * PI * shortRadius;
    double  bigPartRadio    = (shortRadius + disFromLineToCenter) / (shortRadius * 2);
    double  stepLen = .5f;
    step = FMath::Max(step, bBigPart ? bigPartRadio * circumference / stepLen : (1 - bigPartRadio) * circumference / stepLen);
    TArray<FVector> tempVec;

    //use arcSegment
    FVector sc = startPoint - center;
    sc.Normalize();
    FVector ec = endPoint - center;
    ec.Normalize();
    double angeleStart = FGeometryLibrary::verctorToAngle(sc, xAxis, yAxis, shortRadius > 0);
    double angeleEnd = FGeometryLibrary::verctorToAngle(ec, xAxis, yAxis, shortRadius > 0);

    GenerateArcSegments<double>(inStep, FMath::Abs(shortRadius), angeleStart, angeleEnd, center, xAxis, yAxis, TTransformSRT3<double>()
        , [&](const TVector<double>& A, const TVector<double>& B)
        {

                tempVec.AddUnique(A);
                tempVec.AddUnique(B);
            
        });
    int32 numOfTemp = tempVec.Num();
    if (numOfTemp <= 0)
    {
        return false;
    }

    double disStart = FVector::Distance(tempVec[0], startPoint);
    double disEnd = FVector::Distance(tempVec[0], endPoint);

    if (disStart > disEnd)
    {
        FArrayOperatorLibrary::ReverseArray(tempVec);
    }
    outVertices = tempVec;

    return true;
}
bool FGeometryLibrary::createArcSegmentByHeight(const double& arcHeight, const FVector& startPoint, const FVector& endPoint, const int32& inStep, const  FVector& inNormal, const FTransform& transform, TArray<FVector>& outVertices)
{
    if (arcHeight == 0)
    {
        return false;
    }

    FVector segVec = endPoint - startPoint;
    segVec.Normalize();
    FVector segCenter = (endPoint + startPoint) * 0.5f;
    double segLen = FVector::Distance(startPoint, endPoint);
    if (segLen == 0)
    {
        return false;
    }
    FVector xAxis = segVec;
    FVector yAxis = -FVector::CrossProduct(segVec, inNormal);
    yAxis.Normalize();
    if (arcHeight < 0)
    {
        yAxis = -yAxis;
    }
    double angA = FMath::RadiansToDegrees(FMath::Atan(segLen * 0.5f / FMath::Abs(arcHeight)));
    double angO = 180.f - angA * 2;
    if (angO < 0)
    {
        return false;
    }
    double sinO = FMath::Sin(FMath::DegreesToRadians(angO));
    if (sinO == 0)
    {
        return false;
    }

    double radius = segLen * 0.5f / sinO;
    
    FVector center = segCenter - yAxis * (radius - FMath::Abs(arcHeight));
    int32 step = 4;

    double  circumference = 2 * PI * radius;
    double  arctRadio = FMath::Abs(arcHeight) / (radius * 2);
    double  stepLen = 0.05f;
    step = FMath::Max(step, FMath::Abs(arctRadio) * circumference / stepLen);
    TArray<FVector> tempVec;
    
    //use function arcSeg to get arc
    FVector sc = startPoint - center;
    sc.Normalize();
    FVector ec = endPoint - center;
    ec.Normalize();

    double angeleStart = FGeometryLibrary::verctorToAngle(sc, xAxis, yAxis, arcHeight > 0);
    double angeleEnd = FGeometryLibrary::verctorToAngle(ec, xAxis,yAxis, arcHeight > 0);

    GenerateArcSegments<double>(inStep, radius, angeleStart, angeleEnd, center, xAxis, yAxis, TTransformSRT3<double>()
        , [&](const TVector<double>& A, const TVector<double>& B)
        {
                tempVec.AddUnique(A);
                tempVec.AddUnique(B);
            
        });

    int32 numOfTemp = tempVec.Num();
    if (numOfTemp <= 0)
    {
        return false;
    }

    double disStart = FVector::Distance(tempVec[0], startPoint);
    double disEnd = FVector::Distance(tempVec[0], endPoint);

    if (disStart > disEnd)
    {
        FArrayOperatorLibrary::ReverseArray(tempVec);
    }
    outVertices = tempVec;

    return true;
}

double FGeometryLibrary::verctorToAngle(const FVector& inVec, const FVector& inAxisX, const FVector& inAxisY, bool bOutside )
{
    double cosA = FVector::DotProduct(inVec, inAxisX)/ inVec.Size();
    double sinA = FVector::DotProduct(inVec, inAxisY)/ inVec.Size();
    double rad = FMath::Abs(FMath::Acos(cosA));
    if (sinA < 0 && cosA > 0)
    {
        rad =  -rad;
    }
    else if (sinA < 0 && cosA < 0 /*&& bOutside*/)
    {
        rad = FMath::DegreesToRadians(360.f) - rad;
    }

    return rad;
}

double FGeometryLibrary::radOfTwoVector(const FVector& vecA, const FVector& vecB)
{
	const double& CosPhi = FVector::DotProduct(vecA, vecB) / (vecA.Size() * vecB.Size());
    const double& SinPhi = FVector::CrossProduct(vecA, vecB).Size() / (vecA.Size() * vecB.Size());
    double Phi = atan2(SinPhi, CosPhi);
    if(Phi < 0)
    {
        Phi += 2 * PI;
    }
    return Phi;
}

double FGeometryLibrary::radOfTwoVector(const FVector& vecA, const FVector& vecB,const FVector& inNormal)
{
    double cosA = FVector::DotProduct(vecA, vecB) / (vecA.Size() * vecB.Size());

    auto cross = FVector::CrossProduct(vecA, vecB).GetSafeNormal();
    bool bTag = (cross.Dot(inNormal) > 0);
    return bTag ? -FMath::Acos(cosA) : FMath::Acos(cosA);
}



void FGeometryLibrary::scalePolygonMesh(const TMap<int32, TArray<FVector>>& inLinePoints, const TArray<double>& inDistance, const FVector& inNormal, TArray<FVector>& outKeyPoints, TMap<FVector, FVector>& outAllPoints,bool bIsCustom)
{
    //out old point and new point
    TArray<FVector> oldVertexs;

    TMap<int32, int32> edgeMap;    //edge belongs
    TArray<int32> lineCounts;      //index and points num
    int32 j = 0;

    bool bOneLine = inLinePoints.Num() == 1;

    if (bOneLine)
    {
        for (const auto& iter : inLinePoints)
        {
            //keyPointMap.Add(iter.Value[0], iter.Value[0]);
            TMap<FVector, FVector> edges;
            lineCounts.Add(iter.Value.Num());
            for (int32 i = 0; i < iter.Value.Num(); ++i)
            {
                edgeMap.Add(j, iter.Key);
                j++;

                oldVertexs.Add(iter.Value[i]);
            }
        }
    }
    else
    {
        for (const auto& iter : inLinePoints)
        {
            //keyPointMap.Add(iter.Value[0], iter.Value[0]);
            TMap<FVector, FVector> edges;
            lineCounts.Add(iter.Value.Num());
            for (int32 i = 0; i < iter.Value.Num() - 1; ++i)
            {
                edgeMap.Add(j, iter.Key);
                j++;

                oldVertexs.AddUnique(iter.Value[i]);
            }
        }
    }

    int32 numOfOldVertexs = oldVertexs.Num();

    for (int32 cur = 0; cur < numOfOldVertexs; ++cur)
    {
        int32 pre = (cur - 1 + numOfOldVertexs) % numOfOldVertexs;
        int32 next = (cur + 1) % numOfOldVertexs;

        FVector vecPre = oldVertexs[pre] - oldVertexs[cur];
        vecPre.Normalize();

        FVector norPre = FVector::CrossProduct(vecPre, inNormal);
        norPre.Normalize();

        FVector vecNext = oldVertexs[cur] - oldVertexs[next];
        vecNext.Normalize();

        FVector norNext = FVector::CrossProduct(vecNext, inNormal);
        norNext.Normalize();

        int32 preBelong = edgeMap[pre];
        int32 nextBelong = edgeMap[next];
        int32 curBelong = edgeMap[cur];


        FVector temp0 = oldVertexs[pre] + norPre * inDistance[curBelong];
        FVector temp1 = oldVertexs[next] + norNext * inDistance[nextBelong];

        FVector newPoint;
        if (vectorIntersectPoint(vecPre, temp0, vecNext, temp1, newPoint))
        {
            //keyPointMap[oldVertexs[cur]] = newPoint;
            outAllPoints.Add(oldVertexs[cur], newPoint);
            if (preBelong != nextBelong)
            {
                outKeyPoints.Add(newPoint);
            }
        }
        else
        {
	        if (vecPre.Equals(vecNext,0.0000001f))
	        {
                newPoint = oldVertexs[cur] + norPre * inDistance[curBelong];
                outAllPoints.Add(oldVertexs[cur], newPoint);
                if (preBelong != nextBelong)
                {
                    outKeyPoints.Add(newPoint);
                }
	        }

            UE_LOG(LogTemp, Warning, TEXT("CUR FALSE %d"), cur);
        }
    }

    if (!bIsCustom)
    {
        return;
    }

    for (auto& oldIter : inLinePoints)
    {
        auto oldPoints = oldIter.Value;
        int32 numOfPoints = oldPoints.Num();
        if (numOfPoints <= 2)
        {
            continue;
        }
        //arc
        auto oldStart = oldPoints[0];
        auto oldEnd = oldPoints.Last();
        if (!outAllPoints.Contains(oldStart))
        {
            continue;
        }
        if (!outAllPoints.Contains(oldEnd))
        {
            continue;
        }
        double maxDis = 0;
        FVector topPoint;
        for (const auto & iter : oldPoints)
        {
            auto dis = FMath::PointDistToLine(iter, oldEnd - oldStart, oldStart);
            if (dis > maxDis)
            {
                maxDis = dis;
                topPoint = iter;
            }
        }
        auto a = oldEnd - oldStart;
        FVector moveDir = FVector::CrossProduct(inNormal, a).GetSafeNormal();
        FVector newTop = moveDir * inDistance[oldIter.Key] + topPoint;


        auto newStartToEnd = outAllPoints[oldEnd] - outAllPoints[oldStart];
        auto newHeight = FMath::PointDistToLine(newTop, newStartToEnd, outAllPoints[oldStart]);
       
        auto oldStartToTop = topPoint - oldStart;
        oldStartToTop.Normalize();
        auto fakeNor = FVector::CrossProduct(a, oldStartToTop).GetSafeNormal();
        auto tag = FVector::DotProduct(fakeNor, inNormal);
        if (tag <  0)
        {
            newHeight = -newHeight;
        }
        
        TArray<FVector> newArc;

        createArcSegmentByHeight(newHeight, outAllPoints[oldStart] , outAllPoints[oldEnd], numOfPoints - 1, inNormal, FTransform(), newArc);
        if (newArc.Num() != oldIter.Value.Num())
        {
            return;
        }
        int32 arcIndex = 0;
        for (auto& iter : oldIter.Value)
        {
            if (outAllPoints.Contains(iter))
            {
                outAllPoints[iter] = newArc[arcIndex];
            }
            ++arcIndex;
        }
    }

 
}

void FGeometryLibrary::scalePolygonMesh(const TMap<int32, TArray<FVector>>& inLinePoints, const TArray<double>& inDistance, const FVector& inNormal, TMap<int32, TArray<FVector>>& outAllPoints)
{
    if (inDistance.Num() != inLinePoints.Num())
    {
        return;
    }
    int32 lineIndex = 0;
    TMap<int32, TArray<FVector>> moveLines;
    for (const auto& lineIter : inLinePoints)
    {    
        moveLines.Add(lineIter);
        TArray<FVector> oldVertex = lineIter.Value;
        auto numOfOldVertex = oldVertex.Num();
        for (int32 cur = 1; cur < numOfOldVertex - 1; ++cur)
        {
            int32 pre = cur - 1;
            int32 next = cur + 1;
            FVector   vecPre = oldVertex[pre] - oldVertex[cur];
            vecPre.Normalize();

            FVector vecNext = oldVertex[cur] - oldVertex[next];
            vecNext.Normalize();
      
            FVector norPre = FVector::CrossProduct(vecPre, inNormal);
            norPre.Normalize();
            FVector norNext = FVector::CrossProduct(vecNext, inNormal);
            norNext.Normalize();

            FVector temp0 = oldVertex[pre] + norPre * inDistance[lineIndex];
            FVector temp1 = oldVertex[next] + norNext * inDistance[lineIndex];

            FVector newPoint;
            if (vectorIntersectPoint(vecPre, temp0, vecNext, temp1, newPoint))
            {
            }
        }
    }
}

void FGeometryLibrary::geoMeshConvertToDynamicMesh3(const FGeoMesh& inMesh,  FDynamicMesh3& outMesh)
{  
    int32 numOfVertexs = inMesh.vertexs.Num();
    FDynamicMesh3 newMesh;
    TArray<int32> newIndices;
    for (int32 i = 0; i < numOfVertexs; ++i)
    {
        FVertexInfo vi;
        vi.Position = inMesh.vertexs[i];
        if (i < inMesh.normals.Num())
        {
            vi.Normal = inMesh.normals[i];
        }
        
        if (i < inMesh.uv.Num())
        {
            vi.UV = inMesh.uv[i];
        }
        else
        {
            vi.UV = FVector2f(0, 0);
        }
        newIndices.Add(outMesh.AppendVertex(vi));
    }
    for (int32 i = 0; i < inMesh.indices.Num(); i += 3)
    {
        FIndex3i index3;
        index3.A = inMesh.indices[i];
        index3.B = inMesh.indices[i + 1];
        index3.C = inMesh.indices[i + 2];
        outMesh.AppendTriangle(index3);
    }
}

void FGeometryLibrary::dynamicMesh3ConvertToGeoMesh(const FDynamicMesh3& inMesh, FGeoMesh& outMesh)
{
    outMesh.empty();
    int32 numOfVertex = inMesh.MaxVertexID();
    outMesh.vertexs.SetNumZeroed(numOfVertex);
    for (int32 i = 0; i < numOfVertex; i++)
    {
        outMesh.vertexs[i] = inMesh.GetVertex(i);
    }

    int32 numOfTri = inMesh.MaxTriangleID();
    outMesh.indices.SetNumZeroed(numOfTri * 3);
    int32 j = 0;
    TMap<FVector, FVector2f> vertexUV;
    for (int32 i = 0; i < numOfTri; i++)
    {
        int32 indexA = inMesh.GetTriangle(i).A;
        int32 indexB = inMesh.GetTriangle(i).B;
        int32 indexC = inMesh.GetTriangle(i).C;

        outMesh.indices[j] = indexA;
        outMesh.indices[++j] = indexB;
        outMesh.indices[++j] = indexC;
     
        ++j;
    }

    int32 numOfNor = numOfVertex;
    outMesh.normals.SetNumZeroed(numOfNor);
    for (int32 i = 0; i < numOfNor; i++)
    {
        outMesh.normals[i] = inMesh.GetVertexNormal(i);
    }

    int32 numOfUV = numOfVertex;
    outMesh.uv.SetNumZeroed(numOfUV);
    for (int32 i = 0; i < numOfUV; i++)
    {
        outMesh.uv[i] = inMesh.GetVertexUV(i);
    }

    //get outline

    //TMap<int32, TArray<TPair<FVector, FVector>>> allEdges;
    //classifyPlanEdges(inMesh, 0.8, allEdges);
    //for (auto& iter : allEdges)
    //{
    //    outMesh.edges.Append(iter.Value);
    //}

}

void FGeometryLibrary::createMeshByTwoPolygon(const TArray<FVector>& inPolygonA, const TArray<FVector>& inPolygonB, const FVector& inNormal, FGeoMesh& outMesh)
{
    TArray<FVector> newBase;
    TArray<FVector> newScale;

   // UE_LOG(LogTemp, Warning, TEXT("inPolygonA.Num() == inPolygonB.Num()::{%d} "), inPolygonA.Num() == inPolygonB.Num());
    check(inPolygonA.Num() == inPolygonB.Num());

    for (int32 i = 0; i < inPolygonA.Num(); ++i)
    {
        int32 next = (i + 1) % inPolygonA.Num();
        TArray <FVector> subPlan;
        //subPlan.SetNumZeroed(4);

        subPlan.AddUnique(inPolygonA[i]);
        subPlan.AddUnique(inPolygonA[next]);
        subPlan.AddUnique(inPolygonB[next]);
        subPlan.AddUnique(inPolygonB[i]);


        //subPlan[0] = pa[i];
        //subPlan[1] = pa[next];
        //subPlan[2] = pb[next];
        //subPlan[3] = pb[i];
        if (subPlan.Num() < 3)
        {
            continue;
        }
        FVector nor = FVector::CrossProduct(subPlan[2] - subPlan[1],subPlan[1] - subPlan[0]);
        FGeoMesh newMesh;
        createPlanarPolygonMesh(subPlan, nor, newMesh);
        FGeoMesh resMesh;
        //meshBoolean(newMesh, outMesh, FMeshBoolean::EBooleanOp::Union, resMesh);
        //outMesh.empty();
        outMesh.addMesh(newMesh);
        newBase.AddUnique(inPolygonA[i]);
        newScale.AddUnique(inPolygonB[i]);
    }
    //FGeoMesh newMeshA;
    //FArrayOperatorLibrary::ReverseArray(newBase);
    //createPlanarPolygonMesh(newBase, -inNormal, newMeshA);
    //outMesh.addMesh(newMeshA);

    //FGeoMesh newMeshB;
    //createPlanarPolygonMesh(newScale, inNormal, newMeshB);
    //outMesh.addMesh(newMeshB);

    simplifyMesh(outMesh);
    FGeoMesh newMeshA;
    FArrayOperatorLibrary::ReverseArray(newBase);
    createPlanarPolygonMesh(newBase, -inNormal, newMeshA);
    outMesh.addMesh(newMeshA);
    //FGeoMesh TempMesh;
    //meshBoolean(outMesh, newMeshA, FMeshBoolean::EBooleanOp::Union, TempMesh);
    FGeoMesh newMeshB;
    createPlanarPolygonMesh(newScale, inNormal, newMeshB);
    //outMesh.empty();
    //meshBoolean(TempMesh, newMeshB, FMeshBoolean::EBooleanOp::Union, outMesh);
    outMesh.addMesh(newMeshB);
    simplifyMesh(outMesh);

}

void FGeometryLibrary::createMeshByTwoPolygon(const TArray<FVector>& inPolygonA, const TMap<FVector, FVector>& ABMap, const FVector& inNormal, FGeoMesh& outMesh)
{
    TArray<FVector> newBase;
    TArray<FVector> newScale;

    //check(inPolygonA.Num() == ABMap.Num());

    for (int32 i = 0; i < inPolygonA.Num(); ++i)
    {
        int32 next = (i + 1) % inPolygonA.Num();
        TArray <FVector> subPlan;
        //subPlan.SetNumZeroed(4);

        subPlan.AddUnique(inPolygonA[i]);
        subPlan.AddUnique(inPolygonA[next]);
        FVector bnext;
        for (auto & iter : ABMap)
        {
            if (iter.Key.Equals(inPolygonA[next],0.000000000001f))
            {
                bnext = iter.Value;
                subPlan.AddUnique(iter.Value);
                break;
            }
        }
        FVector bI;
        for (auto& iter : ABMap)
        {
            if (iter.Key.Equals(inPolygonA[i], 0.000000000001f))
            {
                bI = iter.Value;
                subPlan.AddUnique(iter.Value);
                break;
            }
        }

        if (subPlan.Num() < 3)
        {
            continue;
        }
        FVector nor = FVector::CrossProduct(subPlan[2] - subPlan[1], subPlan[1] - subPlan[0]);
        FGeoMesh newMesh;
        createPlanarPolygonMesh(subPlan, nor, newMesh);
        outMesh.addMesh(newMesh);
        newBase.AddUnique(inPolygonA[i]);
        newScale.AddUnique(bI);
    }
    FGeoMesh newMeshA;
    FArrayOperatorLibrary::ReverseArray(newBase);
    createPlanarPolygonMesh(newBase, -inNormal, newMeshA);
    outMesh.addMesh(newMeshA);
    //FGeoMesh TempMesh;
    //meshBoolean(outMesh, newMeshA, FMeshBoolean::EBooleanOp::Union, TempMesh);
    FGeoMesh newMeshB;
    createPlanarPolygonMesh(newScale, inNormal, newMeshB);
    //outMesh.empty();
    //meshBoolean(TempMesh, newMeshB, FMeshBoolean::EBooleanOp::Union, outMesh);

    outMesh.addMesh(newMeshB);
    simplifyMesh(outMesh);
}

bool FGeometryLibrary::FindPointSegmentIntersection(const FVector& Point, const FVector& SegmentStart,
    const FVector& SegmentEnd)
{
    const FVector Da = Point - SegmentStart;
    const FVector Db = SegmentEnd - SegmentStart;
    if (FVector::CrossProduct(Da, Db).IsNearlyZero())
    {
        const float Res = Da.SizeSquared() / Db.SizeSquared();
        const float DotProd = FVector::DotProduct(Da, Db);
        if (DotProd >= 0 && 0 <= Res && Res <= 1)
        {
            return true;
        }
    }
    return false;
}

bool FGeometryLibrary::SegmentIntersection3D(const FVector& Segment1Start, const FVector& Segment1End,
    const FVector& Segment2Start, const FVector& Segment2End,
    FVector& IntersectionPoint)
{
	//const FVector SegADirection = (SegmentEndA - SegmentStartA) / (SegmentEndA - SegmentStartA).Size();
	//const FVector SegBDirection = (SegmentEndB - SegmentStartB) / (SegmentEndB - SegmentStartB).Size();
 //   FVector Intersection = FVector::Zero();
 //   if(FVector::DotProduct(SegADirection, SegBDirection) == 1)
 //   {
	//    // segment 平行
 //       return false;
 //   }

 //   FVector StartPointSeg = SegmentStartB - SegmentStartA;
 //   FVector VecS1 = FVector::CrossProduct(SegADirection, SegBDirection);
 //   FVector VecS2 = FVector::CrossProduct(StartPointSeg, SegBDirection);
 //   const auto Num = FVector::DotProduct(StartPointSeg, VecS1);

 //   if(Num >= 1E-07f || Num <= -1E-07f)
 //   {
 //       // 直线不共面
 //       return false;
 //   }

 //   auto P2P3Norm = FVector::CrossProduct(StartPointSeg, SegADirection).Size();

	//const auto MutAB = StartPointSeg.X * SegADirection.X + StartPointSeg.Y * SegADirection.Y + StartPointSeg.Z * SegADirection.Z;

 //   auto P3Pnt = SegmentStartA + (MutAB) * SegADirection;
 //   auto CosTheta = abs(SegADirection.X * SegBDirection.X + SegADirection.Y * SegBDirection.Y + SegADirection.Z * SegBDirection.Z);
 //   if(CosTheta < 1E-07f)
 //   {
 //       Intersection = P3Pnt;
 //   }
 //   if(CosTheta > 1E-07f)
 //   {
 //       auto TanTheta = FMath::Sqrt(1 - FMath::Square(CosTheta)) / CosTheta;
 //       auto KP3Norm = P2P3Norm / TanTheta;

 //       auto K1Pnt = P3Pnt + KP3Norm * SegADirection;
 //       auto K2Pnt = P3Pnt - KP3Norm * SegADirection;

 //       auto P2K1Vec = K1Pnt - SegmentStartB;
 //       auto P2K2Vec = K2Pnt - SegmentStartB;

 //       auto D1 = FVector::CrossProduct(P2K1Vec, SegBDirection).Size();
 //       auto D2 = FVector::CrossProduct(P2K2Vec, SegBDirection).Size();

 //       if (D1 < D2) Intersection = K1Pnt;
 //       else Intersection = K2Pnt;
 //   }

 //   out_IntersectionPoint = Intersection;
 //   /*const auto Num2 = FVector::DotProduct(VecS2, VecS1) / VecS1.SizeSquared();

 //   if(Num2 < 0 || Num2 > 1)
 //   {
 //       return false;
 //   }
 //   Intersection = SegmentStartA + SegADirection * Num2;
 //   out_IntersectionPoint = Intersection;*/
 //   return true;

    // both points
    if (Segment1Start == Segment1End && Segment2Start == Segment2End)
    {
        if (Segment1Start == Segment2Start)
        {
            IntersectionPoint = Segment1Start;
            return true;
        }
        return false;
    }

    //segment 1 is point
    if (Segment1Start == Segment1End)
    {
        if (FindPointSegmentIntersection(Segment1Start, Segment2Start, Segment2End))
        {
            IntersectionPoint = Segment1Start;
            return true;
        }
        return false;
    }

    //segment 2 is point
    if (Segment2Start == Segment2End)
    {
        if (FindPointSegmentIntersection(Segment2Start, Segment1Start, Segment1End))
        {
            IntersectionPoint = Segment2Start;
            return true;
        }
        return false;
    }

    //both segments
    const FVector S1Delta = Segment1End - Segment1Start;
    const FVector S2Delta = Segment2End - Segment2Start;
    const FVector Delta = Segment2Start - Segment1Start;
    const FVector Cross = FVector::CrossProduct(S1Delta, S2Delta);

    if (Cross.IsNearlyZero())
    {
        if (!FVector::CrossProduct(Delta, S2Delta).IsNearlyZero())
        {
            //parallel
            return false;
        }
        //overlap
        const float T0 = FVector::DotProduct(Delta, S1Delta) / S1Delta.SizeSquared();
        const float T1 = T0 + FVector::DotProduct(S2Delta, S1Delta) / S1Delta.SizeSquared();
        if (T0 <= 0 && T0 <= 1)
        {
            IntersectionPoint = Segment1Start + (S1Delta * T0);
            return true;
        }
        if (T0 <= 0 && 0 <= T1)
        {
            IntersectionPoint = Segment1Start;
            return true;
        }
        if (0 <= T1 && T1 <= 1)
        {
            IntersectionPoint = Segment1Start + (S1Delta * T0);
            return true;
        }
        if (T0 <= 1 && 1 <= T1)
        {
            IntersectionPoint = Segment1Start;
            return true;
        }
        return false;
    }

    if (!FMath::IsNearlyZero(FVector::DotProduct(Delta, Cross)))
    {
        //skew
        return false;
    }

    FVector Cross1 = FVector::CrossProduct(Delta, S1Delta);
    const FVector Cross2 = FVector::CrossProduct(Delta, S2Delta);
    FVector Start = Segment2Start;
    FVector SDelta = S2Delta;
    if (Cross1.IsNearlyZero())
    {
        Cross1 = Cross2;
        Start = Segment1Start;
        SDelta = S1Delta;
    }

    const float Dot1 = FVector::DotProduct(Cross1, Cross);
    const float Dot2 = FVector::DotProduct(Cross2, Cross);
    const float Denom = Cross.SizeSquared();

    if (0 <= Dot1 && Dot1 <= Denom && 0 <= Dot2 && Dot2 <= Denom)
    {
        IntersectionPoint = Start + (SDelta * (Dot1 / Denom));
        return true;
    }
    return false;
}

FVector FGeometryLibrary::GetCenterCircle(const TArray<FVector>& Points)
{
    if(Points.Num() != 3)
    {
        UE_LOG(LogTemp, Error, TEXT("The params not equals Three!"));
        return FVector::Zero();
    }
    double x1 = Points[0].X,
	       x2 = Points[1].X,
           x3 = Points[2].X;
    double y1 = Points[0].Y,
           y2 = Points[1].Y,
           y3 = Points[2].Y;
    double z1 = Points[0].Z,
           z2 = Points[1].Z,
           z3 = Points[2].Z;

    double a1 = (y1 * z2 - y2 * z1 - y1 * z3 + y3 * z1 + y2 * z3 - y3 * z2),
        b1 = -(x1 * z2 - x2 * z1 - x1 * z3 + x3 * z1 + x2 * z3 - x3 * z2),
        c1 = (x1 * y2 - x2 * y1 - x1 * y3 + x3 * y1 + x2 * y3 - x3 * y2),
        d1 = -(x1 * y2 * z3 - x1 * y3 * z2 - x2 * y1 * z3 + x2 * y3 * z1 + x3 * y1 * z2 - x3 * y2 * z1);

    double a2 = 2 * (x2 - x1),
        b2 = 2 * (y2 - y1),
        c2 = 2 * (z2 - z1),
        d2 = x1 * x1 + y1 * y1 + z1 * z1 - x2 * x2 - y2 * y2 - z2 * z2;

    double a3 = 2 * (x3 - x1),
        b3 = 2 * (y3 - y1),
        c3 = 2 * (z3 - z1),
        d3 = x1 * x1 + y1 * y1 + z1 * z1 - x3 * x3 - y3 * y3 - z3 * z3;

    double cx = -(b1 * c2 * d3 - b1 * c3 * d2 - b2 * c1 * d3 + b2 * c3 * d1 + b3 * c1 * d2 - b3 * c2 * d1)
        / (a1 * b2 * c3 - a1 * b3 * c2 - a2 * b1 * c3 + a2 * b3 * c1 + a3 * b1 * c2 - a3 * b2 * c1);
    double cy = (a1 * c2 * d3 - a1 * c3 * d2 - a2 * c1 * d3 + a2 * c3 * d1 + a3 * c1 * d2 - a3 * c2 * d1)
        / (a1 * b2 * c3 - a1 * b3 * c2 - a2 * b1 * c3 + a2 * b3 * c1 + a3 * b1 * c2 - a3 * b2 * c1);
    double cz = -(a1 * b2 * d3 - a1 * b3 * d2 - a2 * b1 * d3 + a2 * b3 * d1 + a3 * b1 * d2 - a3 * b2 * d1)
        / (a1 * b2 * c3 - a1 * b3 * c2 - a2 * b1 * c3 + a2 * b3 * c1 + a3 * b1 * c2 - a3 * b2 * c1);

    return FVector{ cx, cy, cz };
}

void FGeometryLibrary::RotateUV(TArray<FVector2D>& InOutUVs, const double Angle)
{
    for (auto& UV : InOutUVs)
    {
        const auto X1 = cos(Angle) * UV.X - sin(Angle) * UV.Y;
        const auto Y1 = sin(Angle) * UV.X + cos(Angle) * UV.Y;
        UV.X = X1;
        UV.Y = Y1;
    }
}

void FGeometryLibrary::RotateUV(FVector2f& InOutUV, const double Angle)
{
    const auto X1 = cos(Angle) * InOutUV.X - sin(Angle) * InOutUV.Y;
    const auto Y1 = sin(Angle) * InOutUV.X + cos(Angle) * InOutUV.Y;
    InOutUV.X = X1;
    InOutUV.Y = Y1;
}

#define CONSTRUCT_TRI_INFO(VECTYPE, SAV, SBV, SCV, CHECK0, CHECK1)  \
    if(Check.Contains(SAV)) {                                       \
		Check.Remove(SAV);                                          \
		(VECTYPE) SAV##To##SBV = (SAV - SBV).GetSafeNormal();       \
		(VECTYPE) SAV##To##SCV = (SAV - SCV).GetSafeNormal();       \
		(VECTYPE) SAV##ToFBv = (SAV - CHECK0).GetSafeNormal();      \
		(VECTYPE) SAV##ToFCv = (SAV - CHECK1).GetSafeNormal();      \
		if (SAV##To##SBV == SAV##ToFBv)                             \
		{                                                           \
			bIsFindBreak = true;                                    \
		    double s1 = (SAV - SBV).Size();                         \
		    double s2 = (SAV - CHECK0).Size();                      \
		    FVector IntPoint = s1 > s2 ? CHECK0 : SBV;              \
		    SaveFlag.Add(NeedHandle,                                \
		        FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},  \
		            TPair<int32, int32>{NeedHandle.Value, j},                   \
		            SAV, IntPoint,                                  \
					{i, TPair<FVector, FVector>{SAV, SBV}},         \
					{j, TPair<FVector, FVector>{SAV, CHECK0}}});    \
		    break;                                                  \
		}                                                           \
		if (SAV##To##SBV == SAV##ToFCv)                             \
		{                                                           \
		    bIsFindBreak = true;                                    \
		    double s1 = (SAV - SBV).Size();                         \
		    double s2 = (SAV - CHECK1).Size();                      \
		    FVector IntPoint = s1 > s2 ? CHECK1 : SBV;              \
		    SaveFlag.Add(NeedHandle,                                \
		        FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},  \
		            TPair<int32, int32>{NeedHandle.Value, j} ,                  \
		            SAV, IntPoint,                                  \
                {i, TPair<FVector, FVector>{SAV, SBV} },            \
                { j, TPair<FVector, FVector>{SAV, CHECK1}}});        \
		    break;                                                  \
		}                                                           \
		if (SAV##To##SCV == SAV##ToFBv)                             \
		{                                                           \
		    bIsFindBreak = true;                                    \
		    double s1 = (SAV - SCV).Size();                         \
		    double s2 = (SAV - CHECK0).Size();                      \
		    FVector IntPoint = s1 > s2 ? CHECK0 : SCV;              \
		    SaveFlag.Add(NeedHandle,                                \
		        FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},  \
		            TPair<int32, int32>{NeedHandle.Value, j},                   \
		            SAV, IntPoint,                                  \
					{i, TPair<FVector, FVector>{SAV, SCV}},         \
                { j, TPair<FVector, FVector>{SAV, CHECK0}}});       \
		    break;                                                  \
		}                                                           \
		if (SAV##To##SCV == SAV##ToFCv)                             \
		{                                                           \
		    bIsFindBreak = true;                                    \
		    double s1 = (SAV - SCV).Size();                         \
		    double s2 = (SAV - CHECK1).Size();                      \
		    FVector IntPoint = s1 > s2 ? CHECK1 : SCV;              \
		    SaveFlag.Add(NeedHandle,                                \
		        FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},  \
		            TPair<int32, int32>{NeedHandle.Value, j},                   \
		            SAV, IntPoint,                                  \
                { i, TPair<FVector, FVector>{SAV, SCV} },           \
                {j, TPair<FVector, FVector>{SAV, CHECK1}} });       \
		    break;                                                  \
		}                                                           \
    } 



TArray<TArray<int32>> FGeometryLibrary::MergeMeshByNormal(const FDynamicMesh3& inMesh,
    TArray<TArray<int32>>& Charts, double MaxAngle, TMap<TPair<int32, int32>, FMergeChartTriInfo>& InOutSaveFlag, TSet<TPair<int32, int32>>& InOutChartMergePair)
{
    std::function<bool(const TArray<FVector>&, const FVector&, const double)> Contains = [&](const TArray<FVector>& Check, const FVector& TriPoint, const double Torelance) -> bool
    {
        for (const FVector& var : Check)
        {
            if (var.Equals(TriPoint, Torelance)) return true;
        }
        return false;
    };


    TArray<TSet<int32>> Res;
    TArray<TArray<int32>> ResArraySorted;
    Res.Reset(Charts.Num());
    // key -> Chart index  value -> triangle index, Normal
    TMap<int32, TArray<TPair<int32, FVector3d>>> SaveNormals;
    // the chart need  
    TSet<TPair<int32, int32>> ResNeedHandle;

    // 1. 找到每个chart中一个三角形顶点坐标, 计算三角形的法线
    for (int i = 0; i < Charts.Num(); i++)
    {
        TArray<int32>& Chart = Charts[i];
        TArray<FVector3d> Temp;
        TArray<TPair<int32, FVector3d>> TriIndexToNormal;
        for (const int32 TriIndex : Chart)
        {
            FVector3d Normal = inMesh.GetTriNormal(TriIndex);


            auto info = inMesh.GetTriangle(TriIndex);
            FVector3d v1 = inMesh.GetVertex(info.A);
            FVector3d v2 = inMesh.GetVertex(info.B);
            FVector3d v3 = inMesh.GetVertex(info.C);
            

            bool bIsContains = false;
            for (FVector3d& Vector : Temp)
            {
                if (Vector.Equals(Normal, 0.0000001)) {
                    bIsContains = true;
                	break;
                }
            }
            if(!bIsContains)
            {
                Temp.Push(Normal);
                TriIndexToNormal.Push({ TriIndex, Normal });
            }
        }
        SaveNormals.Add(i, TriIndexToNormal);
    }

    for (int i = 0; i < Charts.Num(); i++)
    {
        // ToDo 如果这两个边
        TArray<TPair<int32, FVector3d>> Value = *SaveNormals.Find(i);
        // 第i个chart的每个法线
        for (TPair<int32, FVector3d>& Tuple : Value)
        {
            for (int j = 0; j < Charts.Num(); j++)
            {
                if (j == i) continue;
                if(ResNeedHandle.Contains({i, j}) || ResNeedHandle.Contains({j, i})) continue;
                TArray<TPair<int32, FVector3d>> jValue = *SaveNormals.Find(j);
                // 两个有不同法线？所有chart都至少有两条法线
                for (TPair<int32, FVector3d>& JNormalValue : jValue)
                {
                    const double AngleBetweenNormal = radOfTwoVector(Tuple.Value, JNormalValue.Value);
                    if (AngleBetweenNormal <= MaxAngle)
                    {
                        i < j ? ResNeedHandle.Add({ i, j }) : ResNeedHandle.Add({ j, i });
                        break;
                    }
                }
            }
        }

    }

    // Tri index, point
    TMap<int32, TArray<FVector>> InValidChart;

    constexpr double tolerance = 0.00001;

    // 3. 处理ResNeedHandle
    TArray<TPair<int32, int32>> ItemRemove;
    for (const TPair<int32, int32>& NeedHandle : ResNeedHandle)
        //for(auto NeedHandle = ResNeedHandle.begin(); NeedHandle != ResNeedHandle.end(); )
    {
       /* if(SaveNormals[NeedHandle.Key].Num() == 1 
            && SaveNormals[NeedHandle.Value].Num() == 1
            && SaveNormals[NeedHandle.Key][0].Value.Equals(SaveNormals[NeedHandle.Value][0].Value, 0.000000001))
        {
            continue;
        }*/
        TArray<int32> FirstChart = Charts[NeedHandle.Key];
        TArray<int32> SecondChart = Charts[NeedHandle.Value];
        bool bIsFindBreak = false;
        for (int i = 0; i < FirstChart.Num(); i++)
        {
            auto FirstTri = inMesh.GetTriangle(FirstChart[i]);
            FVector FAv = inMesh.GetVertex(FirstTri.A);
            FVector FBv = inMesh.GetVertex(FirstTri.B);
            FVector FCv = inMesh.GetVertex(FirstTri.C);

            for (int j = 0; j < SecondChart.Num(); j++)
            {
                TArray<FVector> Check;
                Check.Push(FAv);
                Check.Push(FBv);
                Check.Push(FCv);

                TArray<TPair<FVector, FVector>> OverlapEdge;

                auto SecondTri = inMesh.GetTriangle(SecondChart[j]);
                FVector SAv = inMesh.GetVertex(SecondTri.A);
                FVector SBv = inMesh.GetVertex(SecondTri.B);
                FVector SCv = inMesh.GetVertex(SecondTri.C);
                if ((Check.Contains(SAv) && Check.Contains(SBv))
                    || (Check.Contains(SBv) && Check.Contains(SCv))
                    || (Check.Contains(SCv) && Check.Contains(SAv)))
                { // case1. 找到了而且相交，但是角度太大，不能合并
                    // 找到两个chart相交的三角形，计算法线
                    bIsFindBreak = true;
                    FVector3d FirstNormal = inMesh.GetTriNormal(FirstChart[i]);
                    FVector3d SecondNormal = inMesh.GetTriNormal(SecondChart[j]);
                    if (radOfTwoVector(FirstNormal, SecondNormal) > MaxAngle)
                    {
                        ItemRemove.Add(NeedHandle);
                        break;
                    }
                    if(Contains(Check, SAv, tolerance) && Contains(Check, SBv, tolerance))
	                    InOutSaveFlag.Add(NeedHandle, 
	                        FMergeChartTriInfo{TPair<int32, int32>{NeedHandle.Key, i}, TPair<int32, int32>{NeedHandle.Value, j}, SAv, SBv,
	                        {i, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()}}, {j, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()}} });
                    if (Contains(Check, SAv, tolerance) && Contains(Check, SCv, tolerance))
                        InOutSaveFlag.Add(NeedHandle,
                            FMergeChartTriInfo{ TPair<int32, int32>{NeedHandle.Key, i}, TPair<int32, int32>{NeedHandle.Value, j}, SAv, SCv,
							{i, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()} }, { j, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()}} });
                    if (Contains(Check, SBv, tolerance) && Contains(Check, SCv, tolerance))
                        InOutSaveFlag.Add(NeedHandle,
                            FMergeChartTriInfo{ TPair<int32, int32>{NeedHandle.Key, i}, TPair<int32, int32>{NeedHandle.Value, j}, SBv, SCv,
                            {i, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()} }, { j, TPair<FVector, FVector>{FVector::Zero(), FVector::Zero()}} });
                }
            	else if ((Check.Contains(SAv) && !Check.Contains(SBv) && !Check.Contains(SCv)) 
                    || (!Check.Contains(SAv) && Check.Contains(SBv) && !Check.Contains(SCv))
                    ||(!Check.Contains(SAv) && !Check.Contains(SBv) && Check.Contains(SCv)))
            	{
            		if(Check.Contains(SAv))
            		{
                        Check.Remove(SAv);
                        TVector<double> SAvToSBv = (SAv - SBv).GetSafeNormal();
                        TVector<double> SAvToSCv = (SAv - SCv).GetSafeNormal();
                        TVector<double> SAvToFBv = (SAv - Check[0]).GetSafeNormal();
                        TVector<double> SAvToFCv = (SAv - Check[1]).GetSafeNormal();
                        if(SAvToSBv == SAvToFBv)
                        {

                            bIsFindBreak = true;
                            double s1 = (SAv - SBv).Size();
                        	double s2 = (SAv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SBv;
                            InOutSaveFlag.Add(NeedHandle, 
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                	TPair<int32, int32>{NeedHandle.Value, j},
                                	SAv, IntPoint,
                                {i, TPair<FVector, FVector>{SAv, SBv}},
                                {j, TPair<FVector, FVector>{SAv, Check[0]}}});
                            break;
                        }
                        if (SAvToSBv == SAvToFCv)
                        {
                           
                            bIsFindBreak = true;
                            double s1 = (SAv - SBv).Size();
                            double s2 = (SAv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SBv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                	TPair<int32, int32>{NeedHandle.Value, j} ,
                                	SAv, IntPoint,
                                {i, TPair<FVector, FVector>{SAv, SBv}},
                                {j, TPair<FVector, FVector>{SAv, Check[1]}}});
                            break;
                        }
                        if (SAvToSCv == SAvToFBv)
                        {

                            bIsFindBreak = true;
                            double s1 = (SAv - SCv).Size();
                            double s2 = (SAv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SCv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SAv, IntPoint,
                                {i, TPair<FVector, FVector>{SAv, SCv}},
                                {j, TPair<FVector, FVector>{SAv, Check[0]}} });
                            break;
                        }
                        if (SAvToSCv == SAvToFCv)
                        {

                            bIsFindBreak = true;
                            double s1 = (SAv - SCv).Size();
                            double s2 = (SAv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SCv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SAv, IntPoint,
                                {i, TPair<FVector, FVector>{SAv, SCv}},
                                {j, TPair<FVector, FVector>{SAv, Check[1]}} });
                            break;
                        }

                        // 出现了两三角形有点不同的奇怪情况
                        double SBvAngleFBv = FMath::Acos(FVector::DotProduct(SAvToFBv, SAvToSBv));
                        if(SBvAngleFBv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SAv, SBv}},
                                {j, TPair<FVector, FVector>{SAv, FBv}}});
                            break;
                        }

                        double SBvAngleFCv = FMath::Acos(FVector::DotProduct(SAvToFCv, SAvToSBv));
                        if (SBvAngleFCv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SAv, SBv}},
                                {j, TPair<FVector, FVector>{SAv, FCv}} });
                            break;
                        }

                        double SCvAngleFBv = FMath::Acos(FVector::DotProduct(SAvToFBv, SAvToSCv));
                        if (SCvAngleFBv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SAv, SCv}},
                                {j, TPair<FVector, FVector>{SAv, FBv}} });
                            break;
                        }

                        double SCvAngleFCv = FMath::Acos(FVector::DotProduct(SAvToFCv, SAvToSCv));
                        if (SCvAngleFCv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SAv, SCv}},
                                {j, TPair<FVector, FVector>{SAv, FCv}} });
                            break;
                        }
            		}

            		//CONSTRUCT_RELOAD_VERSION_INFO(FVector<double>, SBv, SAv, SCv, Check[0], Check[1]);
              //      CONSTRUCT_RELOAD_VERSION_INFO(FVector<double>, SCv, SAv, SBv, Check[0], Check[1]);

                    if(Check.Contains(SBv))
                    {
                        Check.Remove(SBv);
                        TVector<double> SBvToSAv = (SBv - SAv).GetSafeNormal();
                        TVector<double> SBvToSCv = (SBv - SCv).GetSafeNormal();
                        TVector<double> SBvToFAv = (SBv - Check[0]).GetSafeNormal();
                        TVector<double> SBvToFCv = (SBv - Check[1]).GetSafeNormal();
                        if (SBvToSAv == SBvToFAv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SBv - SAv).Size();
                            double s2 = (SBv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SAv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SBv, IntPoint,
                                {i, TPair<FVector, FVector>{SBv, SAv}},
                                {j, TPair<FVector, FVector>{SBv, Check[0]}} });
                            break;
                        }
                        if (SBvToSAv == SBvToFCv)
                        {

                            bIsFindBreak = true;
                            double s1 = (SBv - SAv).Size();
                            double s2 = (SBv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SAv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SBv, IntPoint,
                                {i, TPair<FVector, FVector>{SBv, SAv}},
                                {j, TPair<FVector, FVector>{SBv, Check[1]}} });
                            break;
                        }
                        if (SBvToSCv == SBvToFAv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SBv - SCv).Size();
                            double s2 = (SBv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SCv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SBv, IntPoint,
                                {i, TPair<FVector, FVector>{SBv, SCv}},
                                {j, TPair<FVector, FVector>{SBv, Check[0]}} });
                            break;
                        }
                        if (SBvToSCv == SBvToFCv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SBv - SCv).Size();
                            double s2 = (SBv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SCv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SBv, IntPoint,
                                {i, TPair<FVector, FVector>{SBv, SCv}},
                                {j, TPair<FVector, FVector>{SBv, Check[1]}} });
                            break;
                        }

                        double SAvAngleFAv = FMath::Acos(FVector::DotProduct(SBvToSAv, SBvToFAv));
                        if (SAvAngleFAv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SBv, SAv}},
                                {j, TPair<FVector, FVector>{SBv, FAv}} });
                            break;
                        }

                        double SAvAngleFCv = FMath::Acos(FVector::DotProduct(SBvToSAv, SBvToFCv));
                        if (SAvAngleFCv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SBv, SAv}},
                                {j, TPair<FVector, FVector>{SBv, FCv}} });
                            break;
                        }

                        double SCvAngleFAv = FMath::Acos(FVector::DotProduct(SBvToSCv, SBvToFAv));
                        if (SCvAngleFAv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SBv, SCv}},
                                {j, TPair<FVector, FVector>{SBv, FAv}} });
                            break;
                        }

                        double SCvAngleFCv = FMath::Acos(FVector::DotProduct(SBvToSCv, SBvToFCv));
                        if (SCvAngleFCv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SBv, SCv}},
                                {j, TPair<FVector, FVector>{SBv, FCv}} });
                            break;
                        }
                    }
                    if(Check.Contains(SCv))
                    {
                        Check.Remove(SCv);
                        TVector<double> SCvToSAv = (SCv - SAv).GetSafeNormal();
                        TVector<double> SCvToSBv = (SCv - SBv).GetSafeNormal();
                        TVector<double> SCvToFAv = (SCv - Check[0]).GetSafeNormal();
                        TVector<double> SCvToFBv = (SBv - Check[1]).GetSafeNormal();
                        if (SCvToSAv == SCvToFAv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SCv - SAv).Size();
                            double s2 = (SCv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SAv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SCv, IntPoint,
                                {i, TPair<FVector, FVector>{SCv, SAv}},
                                {j, TPair<FVector, FVector>{SCv, Check[0]}} });
                            break;
                        }
                        if (SCvToSAv == SCvToFBv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SCv - SAv).Size();
                            double s2 = (SCv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SAv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SCv, IntPoint,
                                {i, TPair<FVector, FVector>{SCv, SAv}},
                                {j, TPair<FVector, FVector>{SCv, Check[1]}} });
                            break;
                        }
                        if (SCvToSBv == SCvToFAv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SCv - SBv).Size();
                            double s2 = (SCv - Check[0]).Size();
                            FVector IntPoint = s1 > s2 ? Check[0] : SBv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SCv, IntPoint,
                                {i, TPair<FVector, FVector>{SCv, SBv}},
                                {j, TPair<FVector, FVector>{SCv, Check[0]}} });
                            break;
                        }
                        if (SCvToSBv == SCvToFBv)
                        {
                            bIsFindBreak = true;
                            double s1 = (SCv - SBv).Size();
                            double s2 = (SCv - Check[1]).Size();
                            FVector IntPoint = s1 > s2 ? Check[1] : SAv;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 1, TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j} ,
                                    SCv, IntPoint,
                                {i, TPair<FVector, FVector>{SCv, SBv}},
                                {j, TPair<FVector, FVector>{SCv, Check[1]}} });
                            break;
                        }

                        double SAvAngleFAv = FMath::Acos(FVector::DotProduct(SCvToSAv, SCvToFAv));
                        if (SAvAngleFAv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SCv, SAv}},
                                {j, TPair<FVector, FVector>{SCv, FAv}} });
                            break;
                        }

                        double SAvAngleFBv = FMath::Acos(FVector::DotProduct(SCvToSAv, SCvToFBv));
                        if (SAvAngleFBv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SCv, SAv}},
                                {j, TPair<FVector, FVector>{SCv, FBv}} });
                            break;
                        }

                        double SBvAngleFAv = FMath::Acos(FVector::DotProduct(SCvToSBv, SCvToFAv));
                        if (SBvAngleFAv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SCv, SBv}},
                                {j, TPair<FVector, FVector>{SCv, FAv}} });
                            break;
                        }

                        double SBvAngleFBv = FMath::Acos(FVector::DotProduct(SCvToSBv, SCvToFBv));
                        if (SBvAngleFBv < tolerance)
                        {
                            bIsFindBreak = true;
                            InOutSaveFlag.Add(NeedHandle,
                                FMergeChartTriInfo{ 3,  TPair<int32, int32>{NeedHandle.Key, i},
                                    TPair<int32, int32>{NeedHandle.Value, j},
                                    FVector::ZeroVector, FVector::ZeroVector,
                                    {i, TPair<FVector, FVector>{SCv, SBv}},
                                {j, TPair<FVector, FVector>{SCv, FBv}} });
                            break;
                        }
                    }
            	}
                else if(JudgeAndGetOverlapEdge(Check, TArray<FVector>{SAv, SBv, SCv}, OverlapEdge))
                {
                    bIsFindBreak = true;
                    InOutSaveFlag.Add(NeedHandle,
                        FMergeChartTriInfo{ 2, TPair<int32, int32>{NeedHandle.Key, i},
                            TPair<int32, int32>{NeedHandle.Value, j} ,
                            OverlapEdge[2].Key, OverlapEdge[2].Value,
                        {i, TPair<FVector, FVector>{OverlapEdge[0].Key, OverlapEdge[0].Value}},
                        {j, TPair<FVector, FVector>{OverlapEdge[1].Key, OverlapEdge[1].Value}} });
                    break;
                }
            }
            if (bIsFindBreak) break;
        }
        // case2 没找到，即不相交
        if (!bIsFindBreak) ItemRemove.Add(NeedHandle);
    }



    for (TPair<int32, int32>& Remove : ItemRemove)
    {
        ResNeedHandle.Remove(Remove);
    }

    TArray<TPair<int32, int32>> NeedDel;

    for (const TPair<int32, int32>& NeedHandle : ResNeedHandle) {
        auto first = NeedHandle.Key;
        auto second = NeedHandle.Value;
        auto firstNormalNum = SaveNormals[first].Num();
        auto secondNormalNum = SaveNormals[second].Num();
        if ((firstNormalNum != 1 && secondNormalNum == 1)
            || (firstNormalNum == 1 && secondNormalNum != 1)) {

            if (SaveNormals[first][0].Value.Equals(SaveNormals[second][0].Value)) continue;

            NeedDel.Add(NeedHandle);
            continue;
        }
        if(firstNormalNum == 1 && secondNormalNum == 1)
        {
            auto t1 = SaveNormals[first][0].Value;
            auto t2 = SaveNormals[second][0].Value;
            auto firstNormal = SaveNormals[first][0].Value.GetSafeNormal();
            auto secondNormal = SaveNormals[second][0].Value.GetSafeNormal();
            if(firstNormal != secondNormal)
            {
                NeedDel.Add(NeedHandle);
                continue;
            }
        }

        TArray<int32> IndexInArray;
        for (int i = 0; i < Res.Num(); i++)
        {
            TSet<int32>& Re = Res[i];
            if (Re.Contains(NeedHandle.Key) || Re.Contains(NeedHandle.Value))
            {
                Re.Add(NeedHandle.Value);
                Re.Add(NeedHandle.Key);
                IndexInArray.Push(i);
            }
        }
        if (IndexInArray.IsEmpty())
        {
            Res.Push(TSet<int32>{NeedHandle.Key, NeedHandle.Value});
        }
        else if (IndexInArray.Num() > 1)
        {
            // merge 数组
            for (int i = 1; i < IndexInArray.Num(); i++)
            {
                Res[IndexInArray[0]].Append(Res[IndexInArray[i]]);
            }
            for (int i = IndexInArray.Num() - 1; i > 0; i--)
            {
                Res.RemoveAt(IndexInArray[i]);
            }
        }
    }
    
    for (const auto& var : NeedDel) {
        ResNeedHandle.Remove(var);
    }
    
    for (TSet<int32>& Re1 : Res)
    {
       auto ReArray =  Re1.Array();
       ReArray.Sort();
       ResArraySorted.Push(ReArray);
    }

    InOutChartMergePair = ResNeedHandle;

    return  ResArraySorted;
    // return ResNeedHandle;
}

bool FGeometryLibrary::JudgeAndGetOverlapEdge(const TArray<FVector>& FirstTri, const TArray<FVector>& SecondTri,
    TArray<TPair<FVector, FVector>>& InOutOverlapPoint)
{
    if(FirstTri.Num() != 3 || SecondTri.Num() != 3)
    {
        UE_LOG(LogTemp, Error, TEXT("The number of Input point is less than THREE"));
    }
    const FVector A1 = FirstTri[0];
    const FVector B1 = FirstTri[1];
    const FVector C1 = FirstTri[2];

    const FVector A2 = SecondTri[0];
    const FVector B2 = SecondTri[1];
    const FVector C2 = SecondTri[2];

    const TVector<double> A1ToB1 = A1 - B1;
    const TVector<double> B1ToC1 = B1 - C1;
    const TVector<double> C1ToA1 = C1 - A1;

    const TVector<double> A2ToB2 = A2 - B2;
    const TVector<double> B2ToC2 = B2 - C2;
    const TVector<double> C2ToA2 = C2 - A2;

    TArray<TPair<FVector, FVector>> EdgePairA = { {A1, B1}, {B1, C1}, {C1, A1} };
    TArray<TPair<FVector, FVector>> EdgePairB = { {A2, B2}, {B2, C2}, {C2, A2} };

    TArray<FVector> EdgeA = { A1ToB1, B1ToC1, C1ToA1 };
    TArray<FVector> EdgeB = { A2ToB2, B2ToC2, C2ToA2 };

    TArray<FVector> Edge1 = { A1ToB1.GetSafeNormal(), B1ToC1.GetSafeNormal(), C1ToA1.GetSafeNormal() };
    TArray<FVector> Edge2 = { A2ToB2.GetSafeNormal(), B2ToC2.GetSafeNormal(), C2ToA2.GetSafeNormal() };

    bool bFindCommonEdge = false;
    TPair<int32, int32> OverlapIndex{-1, -1};
    for (int i = 0; i < Edge1.Num(); i++) 
    {
	    for (int j = 0; j < Edge2.Num(); j++)
	    {
		    if(Edge1[i] == Edge2[j] || Edge1[i] == -Edge2[j])
		    {
                bFindCommonEdge = true;
                OverlapIndex.Key = i;
                OverlapIndex.Value = j;
		    }
	    }
    }
    if (!bFindCommonEdge) return false;

    TPair<FVector, FVector> TriPointA = EdgePairA[OverlapIndex.Key];
    TPair<FVector, FVector> TriPointB = EdgePairB[OverlapIndex.Value];

    

    if ((TriPointA.Key - TriPointB.Key).GetSafeNormal() != (TriPointA.Key - TriPointB.Value).GetSafeNormal()
        && (TriPointA.Key - TriPointB.Key).GetSafeNormal() != -(TriPointA.Key - TriPointB.Value).GetSafeNormal()) return false;

    if ((TriPointA.Key - TriPointB.Key).GetSafeNormal() == (TriPointA.Key - TriPointB.Value).GetSafeNormal()
        && (TriPointA.Value - TriPointB.Key).GetSafeNormal() == (TriPointA.Value - TriPointB.Value).GetSafeNormal()
        && (TriPointA.Key - TriPointB.Key).GetSafeNormal() == (TriPointA.Value - TriPointB.Key).GetSafeNormal()) return false;

    TPair<FVector, FVector> OverlapPoint = (TriPointA.Key - TriPointB.Key).GetSafeNormal() == (TriPointA.Key - TriPointB.Value).GetSafeNormal() ? TriPointB : TriPointA;

    InOutOverlapPoint.Add(TriPointA);
    InOutOverlapPoint.Add(TriPointB);
    InOutOverlapPoint.Add(OverlapPoint);
    return true;
}

bool FGeometryLibrary::TriangleNear(const TArray<FVector>& FirstTri, const TArray<FVector>& SecondTri)
{
    bool bNear = true;
    // 1
    std::function<bool(const TArray<FVector>&, const TArray<FVector>&)> TriEdgeEqual = [&](const TArray<FVector>& Tri1, const TArray<FVector>& Tri2) -> bool {
        int32 count = 0;
        for (const auto var : Tri1) {
            if (Tri2.Contains(var)) count++;
        }
        return count == 2;
    };

    // 2
    std::function<bool(const TArray<FVector>&, const TArray<FVector>&)> TriEdgeOverlap = [&](const TArray<FVector>& Tri1, const TArray<FVector>& Tri2) -> bool {
        TArray<TPair<FVector, FVector>> InOutOverlapPoint;
        return JudgeAndGetOverlapEdge(Tri1, Tri2, InOutOverlapPoint);
    };


    // 3
    std::function<bool(const TArray<FVector>&, const TArray<FVector>&)> TriEdgeContain = [&](const TArray<FVector>& Tri1, const TArray<FVector>& Tri2) -> bool {
        TArray<FVector> CommPoint;
        
        for (const auto Var : Tri1) {
            if (Tri2.Contains(Var)) {
                CommPoint.Add(Var);
            }
        }
        if (CommPoint.Num() != 1) return false;
        TArray<FVector> TriA = Tri1;
        TArray<FVector> TriB = Tri2;
        TriA.Remove(CommPoint[0]);
        TriB.Remove(CommPoint[0]);
        auto CommToA = (CommPoint[0] - TriA[0]).GetSafeNormal();
        auto CommToB = (CommPoint[0] - TriA[1]).GetSafeNormal();
        auto CommToC = (CommPoint[0] - TriB[0]).GetSafeNormal();
        auto CommToD = (CommPoint[0] - TriB[1]).GetSafeNormal();
        
        return CommToA == CommToC || CommToA == CommToD || CommToB == CommToC || CommToB == CommToD;

    };

    // 4
    std::function<bool(const TArray<FVector>&, const TArray<FVector>&)> TriEdgeOther = [&](const TArray<FVector>& Tri1, const TArray<FVector>& Tri2) -> bool {
        return false;
    };

    return TriEdgeEqual(FirstTri, SecondTri) || TriEdgeOverlap(FirstTri, SecondTri) || TriEdgeContain(FirstTri, SecondTri) || TriEdgeOther(FirstTri, SecondTri);
}

void FGeometryLibrary::TriangleNearMap(const FDynamicMesh3& InMesh, TMap<int32, TArray<int32>>& OutMap)
{
    auto TriangleMax = InMesh.MaxTriangleID();
    
    for (int32 i = 0; i < TriangleMax; ++i)
    {
        auto Neighbour = InMesh.GetTriNeighbourTris(i);
        OutMap.Add(i, TArray<int32>());
        for (const auto & T  : Neighbour.ABC)
        {
            if (InMesh.IsTriangle(T))
            {
                OutMap[i].Add(T);
            }
        }
       

        //   
        //for (int32 j = 0; j < TriangleMax; ++j)
        //{
        //    if (i == j)
        //    {
        //        continue;
        //    }
        //    const auto& TriangleJ = InMesh.GetTriangle(j);
        //    const auto& JA = InMesh.GetVertex(TriangleJ.A);
        //    const auto& JB = InMesh.GetVertex(TriangleJ.B);
        //    const auto& JC = InMesh.GetVertex(TriangleJ.C);

        //    if (TriangleNear(TArray<FVector>{IA, IB, IC}, TArray<FVector>{JA, JB, JC}) && OutMap.Contains(i))
        //    {
        //         OutMap[i].Add(j);
        //    }
        //}

    }
}

bool FGeometryLibrary::TwoTrianglesInSamePlane(const FDynamicMesh3& InMesh, const int32& IndexA, const int32& IndexB)
{
    if (IndexA == -1)
    {
        return true;
    }
    auto NormalA = InMesh.GetTriNormal(IndexA).GetSafeNormal();
    auto NormalB = InMesh.GetTriNormal(IndexB).GetSafeNormal();
    
    if (FMath::IsNearlyEqual(NormalA.Dot(NormalB),1.0f,0.1f))
    {
        return true;
    }
    
    auto Angle = FMath::Acos(FVector::DotProduct(NormalA, NormalB));

    auto AreaA = InMesh.GetTriArea(IndexA);
    auto AreaB = InMesh.GetTriArea(IndexB);
    if (Angle < 0.323599f && FMath::IsNearlyEqual(AreaA, AreaB, 50.f))
    {
        return true;
    }
    return false;
}

void FGeometryLibrary::ComputeCharts(const FDynamicMesh3& InMesh, TArray<TArray<int32>>& OutCharts)
{
    TMap<int32, TArray<int32>> NearMap;
    TriangleNearMap(InMesh, NearMap);
    TArray<int32> NearKeys;
    NearMap.GenerateKeyArray(NearKeys);

    TSet<int32> Visited;
    
    std::function<void(TArray<int32>&, const int32&, const TArray<int32>&)> AddChild = [&](TArray<int32>& Path, const int32& TriIndex, const TArray<int32>& NextPos)
    {
        if (Path.Contains(TriIndex))
        {
            return;
        }
        
        
        if (TriIndex != -1)
        {
           Path.Add(TriIndex);

        }

        for (auto& Next : NextPos)
        {
            if (TriIndex == -1 || TwoTrianglesInSamePlane(InMesh, TriIndex, Next) && NearMap.Contains(Next))
            {
                AddChild(Path, Next, NearMap[Next]);
            }
            else
            {
                continue;
            }
        }
    };

    //std::function<void(TArray<int32>&, const int32&, const TArray<int32>&)> AddChild = [&](TArray<int32>& Path, const int32& TriIndex, const TArray<int32>& NextPos)
    //{
    //    TSet<int32> NextSet;
    //    for (auto& Next : NextPos)
    //    {
    //        if (TriIndex == -1 || TwoTrianglesInSamePlane(InMesh, TriIndex, Next))
    //        {
    //            if (!Path.Contains(Next))
    //            {
    //                Path.Add(Next);
    //                NextSet.Add(Next);
    //            }

    //        }
    //        else
    //        {
    //            continue;
    //        }
    //    }
    //    for (const auto& Iter: NextSet)
    //    {
    //        if (NearMap.Contains(Iter))
    //        {
    //            AddChild(Path, Iter, NearMap[Iter]);
    //        }
    //    }
    //};

    for (const int32& Var : NearKeys) 
    {

        auto N = InMesh.GetTriNormal(Var);
       // UE_LOG(LogTemp,Warning,TEXT("GetTriNormal %s"), *N.ToString())

        if (Visited.Contains(Var))
        {
            continue;
        }
        TArray<int32> Chart;
        TArray<int32> RootChild;
        RootChild.Add(Var);
        AddChild(Chart, -1, RootChild);
        Visited.Append(Chart);
        OutCharts.Add(Chart);
    }

}

void FGeometryLibrary::ComputeChartMeshes(const FDynamicMesh3& InMesh, const TArray<TArray<int32>>& InCharts, TArray<FGeoMesh>& OutMesh)
{
    for (const auto & Chart : InCharts)
    {
        FGeoMesh Mesh;
        for (const auto& TriIndex : Chart)
        {
            auto Tri = InMesh.GetTriangle(TriIndex);

            auto Area = InMesh.GetTriArea(TriIndex);
            if (FMath::IsNearlyEqual(Area,0.f,0.000000001f))
            {
                continue;
            }
            
            auto VA = InMesh.GetVertex(Tri.A);
            auto VB = InMesh.GetVertex(Tri.B);
            auto VC = InMesh.GetVertex(Tri.C);

            auto N = InMesh.GetTriNormal(TriIndex);

            auto& MeshVertex = Mesh.vertexs;
            auto& MeshIndices = Mesh.indices;
            auto& MeshNormal = Mesh.normals;
            auto& MeshUV = Mesh.uv;

            //A
            bool ContainsA = false;
            FVector2f UVA = FVector2f(0, 0);
            if (MeshVertex.Contains(VA))
            {
                auto Index = MeshVertex.Find(VA);
                MeshIndices.Add(Index);
                ContainsA = true;
                UVA = MeshUV[Index];
            }
            else
            {
                MeshVertex.Add(VA);
                MeshIndices.Add(MeshVertex.Num() - 1);
                MeshNormal.Add(FVector3f(N));
            }

            //B
            bool ContainsB = false;
            FVector2f UVB = FVector2f(0, 0);
            if (MeshVertex.Contains(VB))
            {
                auto Index = MeshVertex.Find(VB);
                MeshIndices.Add(Index);
                ContainsB = true;
                UVB = MeshUV[Index];
            }
            else
            {
                MeshVertex.Add(VB);
                MeshIndices.Add(MeshVertex.Num() - 1);
                MeshNormal.Add(FVector3f(N));
            }

            //C
            bool ContainsC = false;
            FVector2f UVC = FVector2f(0, 0);
            if (MeshVertex.Contains(VC))
            {
                auto Index = MeshVertex.Find(VC);
                MeshIndices.Add(Index);
                ContainsC = true;
                UVC = MeshUV[Index];
            }
            else
            {
                MeshVertex.Add(VC);
                MeshIndices.Add(MeshVertex.Num() - 1);
                MeshNormal.Add(FVector3f(N));
            }

            //uv
            FVector I;
            FVector D;
            FVector AxisX;
            FVector AxisY;
            auto T = FVector::CrossProduct(N, FVector::ZAxisVector).GetSafeNormal();
            bool TheSamePlane = T.Equals(FVector::ZeroVector, 0.1f) || T.Equals(-FVector::ZeroVector, 0.1f);
            


            if (!TheSamePlane && FMath::IntersectPlanes2(I, D, FPlane(VA, N), FPlane(FVector::ZeroVector, FVector::ZAxisVector)))
            {
                AxisX = D;


            }
            else
            {
                AxisX = FVector::XAxisVector;
            }
            AxisX.Normalize();

            AxisY = -FVector::CrossProduct(AxisX,N).GetSafeNormal();
            
            //if (AxisX.Dot(FVector::ZAxisVector) < 0)
            //{
            //    AxisX = -AxisX;
            //}
            //AxisY = AxisY;

            if (ContainsB)
            {
                if (!ContainsA)
                {
                    auto VAB = VA - VB;
                    VAB *= 0.01f;
                    UVA = UVB + FVector2f(VAB.Dot(AxisX), VAB.Dot(AxisY));
                    MeshUV.Add(UVA);
                }

                if (!ContainsC)
                {
                    auto VCB = VC - VB;
                    VCB *= 0.01f;
                    UVC = UVB + FVector2f(VCB.Dot(AxisX), VCB.Dot(AxisY));
                    MeshUV.Add(UVC);
                }
            }
            else if (ContainsC)
            {
                if (!ContainsA)
                {
                    auto VAC = VA - VC;
                    VAC *= 0.01f;
                    UVA = UVC + FVector2f(VAC.Dot(AxisX), VAC.Dot(AxisY));
                    MeshUV.Add(UVA);
                }

                if (!ContainsB)
                {
                    auto VBC = VB - VC;
                    VBC *= 0.01f;
                    UVB = UVC + FVector2f(VBC.Dot(AxisX), VBC.Dot(AxisY));
                    MeshUV.Add(UVB);
                }
            }
            else
            {
                if (!ContainsA)
                {
                    MeshUV.Add(UVA);
                }
                if (!ContainsB)
                {
                    auto VBA = VB - VA;
                    VBA *= 0.01f;
                    UVB = UVA + FVector2f(VBA.Dot(AxisX), VBA.Dot(AxisY));
                    MeshUV.Add(UVB );
                }

                if (!ContainsC)
                {
                    auto VCA = VC - VA;
                    VCA *= 0.01f;
                    UVC = UVA + FVector2f(VCA.Dot(AxisX), VCA.Dot(AxisY));
                    MeshUV.Add(UVC);
                }              
            }

        }
        OutMesh.Add(Mesh);
    }
}

void FGeometryLibrary::ComputeUVsByPatchBuilder(FDynamicMesh3& EditMesh)
{


}

FDynamicMesh3 FGeometryLibrary::solidifyMesh(const FDynamicMesh3& inMesh,const double& radius)
{

    FMinimalBoxMeshGenerator SmallBoxGen;
    SmallBoxGen.Box = FOrientedBox3d(FVector3d::Zero(), radius * 0.05 * FVector3d::One());
    FDynamicMesh3 SmallBoxMesh(&SmallBoxGen.Generate());

    // create a bounding-box tree, then copy the imported mesh and make an Editor for it
    FDynamicMeshAABBTree3   bvTree(&inMesh);
    FDynamicMesh3           accumMesh(inMesh);
    FDynamicMeshEditor      meshEditor(&accumMesh);


    // make a new AABBTree for the accumulated mesh-with-boxes
    FDynamicMeshAABBTree3   accumMeshBVTree(&accumMesh);
    // build a fast-winding-number evaluation data structure
    TFastWindingTree<FDynamicMesh3> fastWinding(&accumMeshBVTree);


    // "solidify" the mesh by extracting an iso-surface of the fast-winding field, using marching cubes
    // (this all happens inside TImplicitSolidify)

    int32   targetVoxelCount = 250;
    double  extendBounds = 0.05f;
    TImplicitSolidify<FDynamicMesh3>    solidifyCalc(&accumMesh, &accumMeshBVTree, &fastWinding);
    solidifyCalc.SetCellSizeAndExtendBounds(accumMeshBVTree.GetBoundingBox(), extendBounds, targetVoxelCount);
    solidifyCalc.WindingThreshold = 0.05;
    solidifyCalc.SurfaceSearchSteps = 0.5;
    solidifyCalc.bSolidAtBoundaries = true;
    solidifyCalc.ExtendBounds = extendBounds;
    FDynamicMesh3   solidMesh(&solidifyCalc.Generate());
    // position the mesh to the right of the imported mesh
    //MeshTransforms::Translate(SolidMesh, SolidMesh.GetBounds().Width() * FVector3d::UnitX());
    return solidMesh;
}

FDynamicMesh3 FGeometryLibrary::offsetMesh(const FDynamicMesh3& inMesh, const double& offsetDistance, const int32& voxelCount)
{
    TImplicitMorphology<FDynamicMesh3> implicitMorphology;
    implicitMorphology.MorphologyOp = TImplicitMorphology<FDynamicMesh3>::EMorphologyOp::Close;
    implicitMorphology.Source = &inMesh;
    FDynamicMeshAABBTree3 solidSpatial(&inMesh);
    implicitMorphology.SourceSpatial = &solidSpatial;
    implicitMorphology.SetCellSizesAndDistance(inMesh.GetBounds(), offsetDistance, voxelCount, voxelCount);
    FDynamicMesh3 offsetSolidMesh(&implicitMorphology.Generate());
    return offsetSolidMesh;
}


FDynamicMesh3 FGeometryLibrary::simplifyMesh(const FDynamicMesh3& inMesh,const int32& triangleCount)
{
    FDynamicMesh3 simplifiedSolidMesh(inMesh);
    FQEMSimplification simplifier(&simplifiedSolidMesh);
    simplifier.SimplifyToTriangleCount(triangleCount);
    return simplifiedSolidMesh;

}

void FGeometryLibrary::simplifyMesh(FGeoMesh& inOutMesh)
{
    //simplify mesh
    {
        FGeoMesh SimpMesh;
        TArray<TArray<int32>> SaveTris;
        for (int32 i = 0; i < inOutMesh.indices.Num(); ++i)
        {
            int32 Index = inOutMesh.indices[i];

            auto InV = inOutMesh.vertexs[Index];
            bool bContain = false;
            int32 SimpIndex = 0;
            for (const auto& SV : SimpMesh.vertexs)
            {
                if (SV.Equals(InV, 0.000001f))
                {
                    bContain = true;
                    SimpMesh.indices.Add(SimpIndex);
                    if (SaveTris.Num() > 0 && SaveTris.Last().Num() < 3)
                    {
                        SaveTris.Last().Add(SimpIndex);
                    }
                    else
                    {
                        SaveTris.Add(TArray<int32>{SimpIndex});
                    }
                    break;
                }
                ++SimpIndex;
            }
            if (!bContain)
            {
                SimpMesh.vertexs.Add(inOutMesh.vertexs[Index]);
                SimpMesh.indices.Add(SimpMesh.vertexs.Num() - 1);
                if (SaveTris.Num() > 0 && SaveTris.Last().Num() < 3)
                {
                    SaveTris.Last().Add(SimpMesh.vertexs.Num() - 1);
                }
                else
                {
                    SaveTris.Add(TArray<int32>{SimpMesh.vertexs.Num() - 1});
                }
                if (Index < inOutMesh.normals.Num())
                {
                    SimpMesh.normals.Add(inOutMesh.normals[Index]);
                }
                if (Index < inOutMesh.uv.Num())
                {
                    SimpMesh.uv.Add(inOutMesh.uv[Index]);
                }
            }


            if (!SimpMesh.indices.IsEmpty() && SimpMesh.indices.Num() % 3 == 0)
            {
                auto A = SimpMesh.indices.Last(2);
                auto B = SimpMesh.indices.Last(1);
                auto C = SimpMesh.indices.Last(0);

                bool bTriVisited = false;

                for (int32 i1 = 0; i1 < SaveTris.Num() - 1; i1++)
                {
                    if (SaveTris[i1].Num() == 3)
                    {
                        if (SaveTris[i1].Contains(A) && SaveTris[i1].Contains(B)&&SaveTris[i1].Contains(C))
                        {
                            //bTriVisited = true;
                            break;
                        }
                    }
                }

                auto calculateTriangleArea = [](double x1, double y1, double z1, double x2, double y2, double z2, double x3, double y3, double z3) {
                    double a = sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1) + (z2 - z1) * (z2 - z1));
                    double b = sqrt((x3 - x2) * (x3 - x2) + (y3 - y2) * (y3 - y2) + (z3 - z2) * (z3 - z2));
                    double c = sqrt((x1 - x3) * (x1 - x3) + (y1 - y3) * (y1 - y3) + (z1 - z3) * (z1 - z3));
                    double s = 0.5 * (a + b + c);
                    return sqrt(s * (s - a) * (s - b) * (s - c));
                };
                if (/*calculateTriangleArea(SimpMesh.vertexs[A].X, SimpMesh.vertexs[A].Y, SimpMesh.vertexs[A].Z,
                    SimpMesh.vertexs[B].X, SimpMesh.vertexs[B].Y, SimpMesh.vertexs[B].Z,
                    SimpMesh.vertexs[C].X, SimpMesh.vertexs[C].Y, SimpMesh.vertexs[C].Z) <= 0.000000001f
                    || */bTriVisited)
                {
                    SimpMesh.indices.RemoveAt(SimpMesh.indices.Num() - 1);
                    SimpMesh.indices.RemoveAt(SimpMesh.indices.Num() - 1);
                    SimpMesh.indices.RemoveAt(SimpMesh.indices.Num() - 1);
                }
            }
        }
        inOutMesh.empty();
        inOutMesh.addMesh(SimpMesh);
    }
}

FDynamicMesh3 FGeometryLibrary::remesh(const FDynamicMesh3& inMesh, const double& radius)
{
    FDynamicMesh3 remeshMesh(inMesh);
    remeshMesh.DiscardAttributes();
    FQueueRemesher queueRemesher(&remeshMesh);
    queueRemesher.SetTargetEdgeLength(radius * 0.05);
    queueRemesher.SmoothSpeedT = 0.05;
    queueRemesher.FastestRemesh();
    return remeshMesh;
}

bool FGeometryLibrary::classifyTriangle(const FDynamicMesh3& inMesh, const double& inRange, TArray<FVector>& outNormals,TMap<int32, TArray<UE::Geometry::FIndex3i>>& outTris)
{
    int32 numOfTri = inMesh.MaxTriangleID();
    TMap<int32, TArray<FVector>> planNormalMap;

    for (int32 i = 0; i < numOfTri; ++i)
    {
        auto tri = inMesh.GetTriangle(i);
        auto vecA = inMesh.GetVertex(tri.A);
        auto vecB = inMesh.GetVertex(tri.B);
        auto vecC = inMesh.GetVertex(tri.C);
        if (vecA == vecB || vecB == vecC || vecC == vecA)
        {
            continue;
        }
        FVector nor;
        trangleNormal(vecA, vecB, vecC, nor);
        nor.Normalize();


        int32 findPlan = -1;
        int32 num = planNormalMap.Num();
        for (auto& iter : planNormalMap)
        {
            for (auto& norIter : iter.Value)
            {
                if (FVector::DotProduct(nor, norIter) > inRange)
                {
                    findPlan = iter.Key;
                    break;
                }
            }
            if (findPlan > -1)
            {
                break;
            }
        }
        if (findPlan > -1 && planNormalMap.Contains(findPlan) && outTris.Contains(findPlan))
        {
            planNormalMap[findPlan].Add(nor);
            outTris[findPlan].Add(FIndex3i(tri.A, tri.B, tri.C));
        }
        else
        {
            TArray<FVector> normals;
            normals.Add(nor);
            planNormalMap.Add(num, normals);

            TArray<FIndex3i> vec3;
            vec3.Add(FIndex3i(tri.A, tri.B, tri.C));
            outTris.Add(num, vec3);
        }
    }
    return outTris.Num() > 0;
}

bool FGeometryLibrary::classifyTriangle(const FDynamicMesh3& inMesh, const TArray<FVector>& unwrapPath, TMap<int32, TArray<UE::Geometry::FIndex3i>>& outTris)
{
    auto numOfTri = inMesh.MaxTriangleID();
    TArray<TPair<FVector, FVector>> pathLines;
    for (int32 i = 0; i < unwrapPath.Num() - 1; i++)
    {
        if (i > 0 && FMath::IsNearlyEqual(FVector::DotProduct((unwrapPath[i] - unwrapPath[i - 1]).GetSafeNormal()
            , (unwrapPath[i + 1] - unwrapPath[i]).GetSafeNormal()), -1.0f, 0.000001f))
        {
            continue;
        }
        pathLines.Add(TPair<FVector, FVector>(unwrapPath[i], unwrapPath[i + 1]));
    }
    for (int32 i = 0; i < numOfTri; i++)
    {
        auto tri = inMesh.GetTriangle(i);
        auto va = inMesh.GetVertex(tri.A);
        auto vb = inMesh.GetVertex(tri.B);
        auto vc = inMesh.GetVertex(tri.C);
        if (va == vb || vb == vc || vc == va)
        {
            continue;
        }

        FVector nor = inMesh.GetTriNormal(i);
        //trangleNormal(va, vb, vc, nor);

        if (pathLines.IsEmpty() && i == 0)
        {
            pathLines.Add(TPair<FVector, FVector>(FVector::ZeroVector, nor));
        }

        bool bVer = false;
        TPair<FVector, FVector> nearSeg;
        double minDis = 9999999.9f;
        int32 pathIndex = 0;
        int32 nearIndex = -1;

        for (auto& iter : pathLines)
        {
            auto pathDir = iter.Value - iter.Key;
            pathDir.Normalize();

            if (FMath::IsNearlyEqual(FVector::DotProduct(pathDir, nor), 1.0f, 0.000001f)
                || FMath::IsNearlyEqual(FVector::DotProduct(pathDir, nor), -1.0f, 0.000001f))
            {
                if (!outTris.Contains(-1))
                {
                    TArray<FIndex3i> tris;
                    tris.Add(tri);
                    outTris.Add(-1, tris);
                }
                else
                {
                    outTris[-1].Add(tri);
                }
                bVer = true;
                break;
            }
            else
            {
                auto center = (va + vb + vc) / 3;
                auto dis = FMath::PointDistToSegment(center, iter.Key, iter.Value);
                auto disA = FMath::PointDistToSegment(va, iter.Key, iter.Value);
                auto disB = FMath::PointDistToSegment(vb, iter.Key, iter.Value);
                auto disC = FMath::PointDistToSegment(vc, iter.Key, iter.Value);
                auto minTemp = FMath::Min3(disA, disB, disC);
                if (dis < minDis)
                {
                    minDis = dis;
                    nearSeg = TPair<FVector, FVector>(iter.Key, iter.Value);
                    nearIndex = pathIndex;
                }
            }
            ++pathIndex;
        }
        if (nearIndex != -1)
        {
            if (!outTris.Contains(nearIndex))
            {
                TArray<FIndex3i> tris;
                tris.Add(tri);
                outTris.Add(nearIndex, tris);
            }
            else
            {
                outTris[nearIndex].Add(tri);
            }
        }
    }
    return true;
}

bool FGeometryLibrary::classifyPlanEdges(const FDynamicMesh3& inMesh, const double& inNormalRange, TMap<int32, TArray<TPair<FVector, FVector>>>& outPlanEdges)
{
    TArray<FVector> planNormals;
    TMap<int32, TArray<UE::Geometry::FIndex3i>> classifyTris;
    classifyTriangle(inMesh, inNormalRange, planNormals, classifyTris);
    outPlanEdges.Empty();

    int32 index = 0;
    for (auto & iter : classifyTris)
    {
        TArray <TPair<FVector, FVector>> edges;
        outPlanEdges.Add(index, edges);
        for (auto& iterTri : iter.Value)
        {

            auto vecA = inMesh.GetVertex(iterTri.A);
            auto vecB = inMesh.GetVertex(iterTri.B);
            auto vecC = inMesh.GetVertex(iterTri.C);

            auto edgeAB = TPair<FVector, FVector>(vecA, vecB);
            auto edgeBC = TPair<FVector, FVector>(vecB, vecC);
            auto edgeCA = TPair<FVector, FVector>(vecC, vecA);

            outPlanEdges[index].Add(edgeAB);
            outPlanEdges[index].Add(edgeBC);
            outPlanEdges[index].Add(edgeCA);
        }

        TArray<TPair<FVector, FVector>> removeEdges;
        for (int32 i = 0; i < outPlanEdges[index].Num(); i++)
        {
            for (int32 j = 0; j < outPlanEdges[index].Num(); j++)
            {
                if (i == j)
                {
                    continue;
                }
                if (outPlanEdges[index][i].Key.Equals(outPlanEdges[index][j].Key) && outPlanEdges[index][i].Value.Equals(outPlanEdges[index][j].Value) 
                    || outPlanEdges[index][i].Key.Equals(outPlanEdges[index][j].Value) && outPlanEdges[index][i].Value.Equals(outPlanEdges[index][j].Key))
                {
                    removeEdges.Add(outPlanEdges[index][i]);
                }
            }
        }
        outPlanEdges[index].RemoveAll([&](TPair<FVector, FVector> a) 
            {
                return removeEdges.Contains(a);
            });
        ++index;
    }

    return true;
}

bool FGeometryLibrary::uvUnwrap(FDynamicMesh3& inMesh, const double& inRange)
{
    //TArray<FVector> planNormals;
    //TMap<int32, TArray<UE::Geometry::FIndex3i>> classifyTris;
    //TArray<FVector> moveNormals;
    ////TMap<int32, TArray<TPair<FVector, FVector>>> planEdges;
    ////classifyPlanEdges(inMesh, inRange, planEdges);
    ////for (const auto & iter : planEdges)
    ////{
    ////    TArray<FVector> outline;
    ////    edgesToOutline(iter.Value, outline);
    ////    FGeoMesh newPlanMesh;
    ////}
    ////TArray<FVector> outline;
    ////edgesToOutline();
    //classifyTriangle(inMesh, inRange, planNormals,classifyTris);
    //for (const auto & iter : classifyTris)
    //{
    //    FVector planNormal = planNormals[iter.Key];
    //    FGeoMesh newPlanMesh;
    //    TArray<int32>& newIndics = newPlanMesh.indices;
    //    TArray<FVector>& newVertex = newPlanMesh.vertexs;
    //    TArray<FVector3f>& newNormals = newPlanMesh.normals;

    //    for (const auto vecIter : iter.Value)
    //    {
    //        FGeoMesh newPlanMesh;
    //        auto vecA = inMesh.GetVertex(vecIter.A);
    //        auto vecB = inMesh.GetVertex(vecIter.B);
    //        auto vecC = inMesh.GetVertex(vecIter.C);
    //        FVector newNormal;
    //        trangleNormal(vecA, vecB, vecC, newNormal);
    //        newNormals.Add(FVector3f(newNormal));
    //        int32 indexA = newVertex.Find(vecA);
    //        int32 indexB = newVertex.Find(vecB);
    //        int32 indexC = newVertex.Find(vecC);

    //        if (INDEX_NONE != indexA)
    //        {
    //            newPlanMesh.vertexs.Add(vecA);
    //            newPlanMesh.indices.Add(newVertex.Num() - 1);
    //        }
    //        else
    //        {
    //            newIndics.Add(indexA);
    //        }
    //        if (INDEX_NONE != indexB)
    //        {
    //            newVertex.Add(vecB);
    //            newIndics.Add(newVertex.Num() - 1);
    //        }
    //        else
    //        {
    //            newIndics.Add(indexB);
    //        }
    //        if (INDEX_NONE != indexC)
    //        {
    //            newVertex.Add(vecC);
    //            newIndics.Add(newVertex.Num() - 1);
    //        }
    //        else
    //        {
    //            newIndics.Add(indexC);
    //        }
    //    }
    //    bool bMovePlan = false;
    //    for (auto & iter : moveNormals)
    //    {
    //        if (iter.Cross(planNormal).Equals(FVector::ZeroVector,0.000001f))
    //        {
    //            bMovePlan = true;
    //            break;
    //        }
    //    }


    //}
    return false;
}

bool FGeometryLibrary::edgesToOutline(const TArray<TPair<FVector, FVector>>& inEdges, TArray<FVector>& outline)
{
    return false;
}

bool FGeometryLibrary::vectorIntersectPoint(const FVector& vecA, const FVector& pointA, const FVector& vecB, const FVector& pointB, FVector& outPoint)
{
    if (FMath::IsNearlyEqual(FVector::DotProduct(vecA, vecB),1,0.000000001f))
    {
        return false;   //two vector parallel
    }


    FVector seg0    = pointB - pointA;
    FVector vecS0 = FVector::CrossProduct(vecA, vecB);
    FVector vecS1 = FVector::CrossProduct(seg0, vecB);

    double m = FVector::DotProduct(seg0, vecS0);
    if (!FMath::IsNearlyZero(m,0.000001f))
    {
        return false;   //two vectors are not in the same plan
    }

    double n = FVector::DotProduct(vecS0, vecS1) / FMath::Pow(vecS0.Size(), 2);
    outPoint = pointA + vecA * n;
    return true;
}

void FGeometryLibrary::trangleNormal(const FVector& v0, const FVector& v1, const FVector& v2, FVector& outNormal)
{
    // counter clockwise

    auto vec0 = v1 - v0;
    auto vec1 = v2 - v1;
    outNormal = FVector::CrossProduct(vec0, vec1);
    outNormal.Normalize();
}

void FGeometryLibrary::uniformVertexOfTwoPlan(TArray<FVector>& planA,const FVector& inNormalA, TArray<FVector>& planB, const FVector& inNormalB)
{
    auto numA = planA.Num();
    auto numB = planB.Num();
    if (numA < 3 || numB < 3)
    {
        return;
    }
    if (numA != numB)
    {
        uniformVertexNum(planA, inNormalA, planB, inNormalB);
        numA = planA.Num();
        numB = planB.Num();
    }

    auto copyA = planA;
    auto copyB = planB;

    transformationPlan(inNormalB, -inNormalA, copyB);

    FVector baryA = FVector::ZeroVector;
    FVector baryB = FVector::ZeroVector;

    for (const auto& iter : copyA)
    {
        baryA += iter;
    }
    baryA /= copyA.Num();
    for (auto& iter : copyA)
    {
        iter -= baryA;
    }

    for (const auto& iter : copyB)
    {
        baryB += iter;
    }
    baryB /= copyB.Num();
    for (auto& iter : copyB)
    {
        iter -= baryB;
    }

    int32 newPlanBStart = 0;
    TArray<int32> newIndex;
    for (int32 i = 0; i < numA; i++)
    {
        int32 tempIndex = 0;
        double minDis = 999999.0f;
        FVector dir = copyA[i].GetSafeNormal();
        double maxLen = 0.0f;
        for (int32 j = 0; j < numB; j++)
        {
            auto tempDis = FVector::Distance(copyA[i], copyB[j]);
            auto tempLen = FVector::DotProduct(dir, copyB[j].GetSafeNormal());
            if (tempDis < minDis && tempLen> 0.0f && tempLen > maxLen)
            {

                tempIndex = j;
                minDis = tempDis;
                maxLen = tempLen;
            }
        }
        newIndex.Add(tempIndex);
    }
    auto tempOut = planB;
    planB.Empty();
    planB.SetNumZeroed(tempOut.Num());
    int32 i = 0;
    for (auto & iter : newIndex)
    {
        if (iter > tempOut.Num())
        {
            continue;
        }
        planB[i] = tempOut[iter];
        ++i;
    }

}

void FGeometryLibrary::uniformVertexNum(TArray<FVector>& planA, const FVector& inNormalA, TArray<FVector>& planB, const FVector& inNormalB)
{
    auto numA = planA.Num();
    auto numB = planB.Num();
    if (numA < 3 || numB < 3)
    {
        return;
    }
    auto copyA = planA;
    auto copyB = planB;

    transformationPlan(inNormalB, -inNormalA, copyB);

    FVector baryA = FVector::ZeroVector;
    FVector baryB = FVector::ZeroVector;
    for (const auto& iter : copyA)
    {
        baryA += iter;
    }
    baryA /= copyA.Num();
    for (auto& iter : copyA)
    {
        iter -= baryA;
    }

    for (const auto& iter : copyB)
    {
        baryB += iter;
    }
    baryB /= copyB.Num();
    for (auto& iter : copyB)
    {
        iter -= baryB;
    }
    int32 newPlanBStart = 0;

    TArray<int32> keepIndex;
    if (numA < numB)
    {
        for (int32 i = 0; i < numA; ++i)
        {
            double minDis = 9999999.0f;
            int32 keep;
            for (int32 j = 0; j < numB; ++j)
            {
                auto tempDis = FVector::Distance(copyA[i], copyB[j]);
                
                if (tempDis < minDis)
                {
                    minDis = tempDis;
                    keep = j;
                }
            }
            keepIndex.Add(keep);
        }

        auto temp = planB;
        planB.Empty();
        for (const auto & iter : keepIndex)
        {
            planB.Add(temp[iter]);
        }
        //for (int32 i = 0; i < temp.Num(); i++)
        //{
        //    if (keepIndex.Contains(i))
        //    {
        //        planB.Add(temp[i]);
        //    }
        //}
    }
    else if (numA > numB)
    {
        for (int32 i = 0; i < numB; ++i)
        {
            int32 keep;
            double minDis = 9999999.0f;
            for (int32 j = 0; j < numA; ++j)
            {
                auto tempDis = FVector::Distance(copyA[j], copyB[i]);
                if (tempDis < minDis)
                {
                    minDis = tempDis;
                    keep = j;
                }
            }
            keepIndex.Add(keep);
        }
        auto temp = planA;
        planA.Empty();
        for (const auto & iter : keepIndex)
        {
            planA.Add(temp[iter]);
        }
        //for (int32 i = 0; i < temp.Num(); i++)
        //{
        //    if (keepIndex.Contains(i))
        //    {
        //        planA.Add(temp[i]);
        //    }
        //}
    }
}

bool FGeometryLibrary::segmentPlaneIntersectPoint(const TPair<FVector, FVector>& inSegment, const FVector& inPlaneNormal, const FVector& inPlanePoint, FVector& outPoint)
{
    

    return false;
}

TArray<FVector> FGeometryLibrary::OffsetPolygon(const TArray<FVector>& InPolygon, const double& InOffset, const FVector& InNormal)
{
	auto Polygon = InPolygon;
	transformationPlan(InNormal, FVector::ZAxisVector, Polygon);

    TArray<TPair<FVector, FVector>> Lines;
    for (int32 i = 0; i < Polygon.Num(); i++)
    {
		int32 prev = (i + Polygon.Num() - 1) % Polygon.Num();
		int32 next = (i + 1) % Polygon.Num();
		Lines.Add(TPair<FVector, FVector>(Polygon[prev], Polygon[i]));
    }

    for (auto& Iter : Lines)
    {
        FVector& Start = Iter.Key;
        FVector& End = Iter.Value;


        FVector Direction = (End - Start).GetSafeNormal();
        FVector Normal = FVector::CrossProduct(FVector::ZAxisVector, Direction).GetSafeNormal();

        Start += InOffset * Normal;
        End += InOffset * Normal;
    }

	auto Num = Lines.Num();
    for (int32 i = 0; i < Num; ++i)
    {
        int32 next = (i + 1) % Num;
        int32 pre = (i + Num - 1) % Num;

        const FVector& Start = Lines[i].Key;
        const FVector& End = Lines[i].Value;
        const FVector& Direction = (End - Start).GetSafeNormal();

        const FVector& NextStart = Lines[next].Key;
        const FVector& NextEnd = Lines[next].Value;
        const FVector& NextDirection = (NextEnd - NextStart).GetSafeNormal();

        const FVector& PreStart = Lines[pre].Key;
        const FVector& PreEnd = Lines[pre].Value;
        const FVector& PreDirection = (PreEnd - PreStart).GetSafeNormal();

        FVector NewStart = Start;
        FVector NewEnd = End;

        if (!FVector::Parallel(Direction, PreDirection) /*&& !CopyLines[i].StartPoint.bIsOnWall*/
            && FMath::SegmentIntersection2D(Start, End, PreStart, PreEnd, NewStart))
        {
            Lines[i].Key = NewStart;
        }

        if (!FVector::Parallel(Direction, NextDirection) /*&& !CopyLines[i].EndPoint.bIsOnWall*/
            && FMath::SegmentIntersection2D(Start, End, NextStart, NextEnd, NewEnd))
        {
            Lines[i].Value = NewEnd;
        }
    }
    TArray<FVector> NewPoints;
	NewPoints.SetNumZeroed(Num);
	for (int32 i = 0; i < Num; i++)
	{
		NewPoints[i] = Lines[i].Key;
	}

    transformationPlan(FVector::ZAxisVector, InNormal, NewPoints);

    return NewPoints;
}

bool FGeometryLibrary::clockwise(const TArray<FVector>& InPolygon, const FVector& InNormal)
{
    if (InPolygon.IsEmpty())
    {
        return false;
    }
    TArray<FVector> tempPot;
    TArray<FVector> tempVec;

    for (const auto& iter : InPolygon)
    {
        tempPot.AddUnique(iter);
    }
    auto numOfPoints = tempPot.Num();

    for (int32 cur = 0; cur < numOfPoints; cur++)
    {
           tempVec.AddUnique(tempPot[cur]);
    }

    if (tempVec.Num() < 3)
    {
        return false;
    }

    int32 numOfVertices = tempVec.Num();
    Eigen::Matrix3d transformtion;
    Eigen::Vector3d norVec;
    norVec << InNormal.X, InNormal.Y, InNormal.Z;

    Eigen::Vector3d zVec;
    zVec << .0f, .0f, 1.f;
    transformationMaterix(norVec, zVec, transformtion);

    TArray<FVector> polygonVerts;

    polygonVerts.SetNumZeroed(numOfVertices);
    int index = 0;
    for (auto& iter : tempVec)
    {
        Eigen::Vector3d beforeVec;
        beforeVec << iter.X, iter.Y, iter.Z;
        auto newVec = transformtion * beforeVec;
        FVector(newVec.x(), newVec.y(), newVec.z());
        polygonVerts[index] = FVector(newVec.x(), newVec.y(), 0);
        ++index;
    }

    int32 BLIndex = PolygonLeftBottom(polygonVerts, InNormal);
    int32 Next = (BLIndex + 1) % polygonVerts.Num();
    int32 Pre = (BLIndex - 1 + polygonVerts.Num()) % polygonVerts.Num();

    auto Dir0 = (polygonVerts[BLIndex] - polygonVerts[Pre]).GetSafeNormal();
    auto Dir1 = (polygonVerts[Next] - polygonVerts[BLIndex]).GetSafeNormal();

    return FVector::CrossProduct(Dir0, Dir1).Z > 0;
}

void FGeometryLibrary::meshBoolean(const FGeoMesh& target, const FGeoMesh& tool, const FMeshBoolean::EBooleanOp& operation, FGeoMesh& outMesh)
{

    FDynamicMesh3 dyTarMesh = FDynamicMesh3();
    FDynamicMesh3 meshB;
    geoMeshConvertToDynamicMesh3(target, dyTarMesh);
 


    FDynamicMesh3 dyToolMesh = FDynamicMesh3();
    geoMeshConvertToDynamicMesh3(tool, dyToolMesh);



    //{
    //    FActorSpawnParameters SpawnInfo;
    //    ADynamicMeshActor* NewActor = GWorld->SpawnActor<ADynamicMeshActor>(SpawnInfo);
    //    UDynamicMeshComponent* NewComponent = NewActor->GetDynamicMeshComponent();
    //    FDynamicMesh3 SetMesh = MoveTemp(dyTarMesh);
    //    if (SetMesh.IsCompact() == false)
    //    {
    //        SetMesh.CompactInPlace();
    //    }
    //    NewComponent->SetMesh(MoveTemp(SetMesh));
    //    NewComponent->NotifyMeshUpdated();
    //    return;
    //    //
    //}


    //{
    //    FActorSpawnParameters SpawnInfo;
    //    ADynamicMeshActor* NewActor = GWorld->SpawnActor<ADynamicMeshActor>(SpawnInfo);
    //    UDynamicMeshComponent* NewComponent = NewActor->GetDynamicMeshComponent();
    //    FDynamicMesh3 SetMesh = MoveTemp(dyToolMesh);
    //    if (SetMesh.IsCompact() == false)
    //    {
    //        SetMesh.CompactInPlace();
    //    }
    //    NewComponent->SetMesh(MoveTemp(SetMesh));
    //    NewComponent->NotifyMeshUpdated();
    //     return;
    //     
    //}


   /* outMesh = tool;
    return;*/
    
    //{

    //    TObjectPtr<UDynamicMesh> TarMesh = NewObject<UDynamicMesh>();
    //    TarMesh->SetMesh(MoveTemp(dyTarMesh));

    //    TObjectPtr<UDynamicMesh> ToolMesh = NewObject<UDynamicMesh>();
    //    ToolMesh->SetMesh(MoveTemp(dyToolMesh));
    //    FGeometryScriptMeshBooleanOptions Options;
    //    TObjectPtr<UDynamicMesh> newMesh = UGeometryScriptLibrary_MeshBooleanFunctions::ApplyMeshBoolean(TarMesh, FTransform(), ToolMesh, FTransform(), EGeometryScriptBooleanOperation::Subtract, Options);
    //    auto meshData = newMesh->GetMeshRef();
    //    dynamicMesh3ConvertToGeoMesh(meshData, outMesh);

    //    return;
    //}

    FDynamicMesh3 resultMesh;
    if (tool.vertexs.Num() == 0)
    {
        resultMesh = dyTarMesh;
    }
    else
    {
      FMeshBoolean meahBool(&dyTarMesh, &dyToolMesh, &resultMesh, operation);

      //if (meahBool.Compute())
      {
          //meahBool.TryToImproveTriQualityThreshold = -1;
          meahBool.bTrackAllNewEdges = true;
          meahBool.bWeldSharedEdges = false;
          meahBool.Compute();


          //resultMesh.CompactInPlace();
          //resultMesh.EnableAttributes();
          //resultMesh.EnableVertexNormals(FVector3f(0, 0, 1));
          //resultMesh.EnableVertexUVs(FVector2f(0, 0));
          //uvUnwrap(resultMesh);   //use xastls to remesh

          //auto NewBoundaryEdges = MoveTemp(meahBool.CreatedBoundaryEdges);

          //MeshTransforms::ApplyTransformInverse(resultMesh, meahBool.ResultTransform, true);
          
          /*if (NewBoundaryEdges.Num() > 0)
          {
              FMeshBoundaryLoops OpenBoundary(&resultMesh, false);
              TSet<int> ConsiderEdges(NewBoundaryEdges);
              OpenBoundary.EdgeFilterFunc = [&ConsiderEdges](int EID)
              {
                  return ConsiderEdges.Contains(EID);
              };
              OpenBoundary.Compute();

              for (FEdgeLoop& Loop : OpenBoundary.Loops)
              {
                  FMinimalHoleFiller Filler(&resultMesh, Loop);
                  Filler.Fill();
              }
          }*/

          //{
          //    FActorSpawnParameters SpawnInfo;
          //    ADynamicMeshActor* NewActor = GWorld->SpawnActor<ADynamicMeshActor>(SpawnInfo);
          //    UDynamicMeshComponent* NewComponent = NewActor->GetDynamicMeshComponent();
          //    FDynamicMesh3 SetMesh = MoveTemp(resultMesh);
          //    if (SetMesh.IsCompact() == false)
          //    {
          //        SetMesh.CompactInPlace();
          //    }
          //    NewComponent->SetMesh(MoveTemp(SetMesh));
          //    NewComponent->NotifyMeshUpdated();
          //    return;

          //}

      }
      /*if (NewBoundaryEdges.Num() > 0)
      {
          FMeshBoundaryLoops OpenBoundary(&resultMesh, false);
          TSet<int> ConsiderEdges(NewBoundaryEdges);
          OpenBoundary.EdgeFilterFunc = [&ConsiderEdges](int EID)
          {
              return ConsiderEdges.Contains(EID);
          };
          OpenBoundary.Compute();

          for (FEdgeLoop& Loop : OpenBoundary.Loops)
          {
              FMinimalHoleFiller Filler(&resultMesh, Loop);
              Filler.Fill();
          }
      }*/

    }
    resultMesh.CompactInPlace();

    resultMesh.EnableVertexNormals(FVector3f(0,0,1));
    resultMesh.EnableVertexUVs(FVector2f(0,0));
    //FQEMSimplification simplifier(&resultMesh);
    //simplifier.SimplifyToMinimalPlanar();
    //uvUnwrap(resultMesh);   //use xastls to remesh
    //FAttrMeshSimplification Simplification(&resultMesh);
    //Simplification.SimplifyToMinimalPlanar(0.1);
    dynamicMesh3ConvertToGeoMesh(resultMesh, outMesh);

}

void FGeometryLibrary::CreateSplineMesh(const TArray<FVector>& InSectionMesh, const FVector& InNormal, const TArray<FVector>& GrowPath, FGeoMesh& OutMesh)
{
    if (GrowPath.Num() < 2)
    {
        return;
    }
    TArray<FVector> Path;
    for (const auto & P : GrowPath)
    {
        Path.AddUnique(P);
    }

    FArrayOperatorLibrary::ReverseArray(Path);

    FGeneralizedCylinderGenerator SweepGenerator;
    int32 PlaneType = 0;
    if (InNormal.Equals(FVector::XAxisVector, 0.001f))
    {
        PlaneType = 1;
    }
    else if (InNormal.Equals(FVector::YAxisVector, 0.001f))
    {

        PlaneType = 2;
    }
    for (const auto & V : InSectionMesh)
    {
        FVector2D Vertex;
        if (PlaneType == 0)
        {
            Vertex.X = V.X;
            Vertex.Y = -V.Y;
        }
        else if(PlaneType == 1)
        {
            Vertex.X = V.Z ;
            Vertex.Y = V.Y;
        }
        else if (PlaneType == 2)
        {
            Vertex.X = V.X;
            Vertex.Y = V.Z;
        }
        SweepGenerator.CrossSection.AppendVertex(Vertex);
    }
    int32 Index = 0;

    //for (const auto & P : Path)
    //{
    //    if (Index == 0)
    //    {
    //        SweepGenerator.PathFrames.Add(FFrame3d(P, (Path[Index + 1] - Path[Index]).GetSafeNormal()));
    //        SweepGenerator.PathScales.Add(FVector2D(1, 1));
    //    }
    //    else if (Index == Path.Num() - 1)
    //    {
    //        SweepGenerator.PathFrames.Add(FFrame3d(P, (Path[Index] - Path[Index - 1]).GetSafeNormal()));
    //        SweepGenerator.PathScales.Add(FVector2D(1, 1));
    //    }
    //    else
    //    {

    //        auto A = Path[Index + 1] - Path[Index];
    //        auto B = Path[Index] - Path[Index - 1];
    //        A.Normalize();
    //        B.Normalize();
    //        double CosAB = FVector::DotProduct(A, B);
    //        double T = PI / 2 - FMath::Acos(CosAB) / 2;
    //        double CosX = FMath::Abs(FMath::Cos(T));
    //        if (CosX > 0.69f)
    //        {
    //            auto Scale = 1 / CosX;
    //            SweepGenerator.PathFrames.Add(FFrame3d(P, ((A + B) * 0.5f).GetSafeNormal()));
    //            if (PlaneType == 1)
    //                SweepGenerator.PathScales.Add(FVector2D(Scale, 1));
    //            else
    //                SweepGenerator.PathScales.Add(FVector2D(1, Scale));
    //        }
    //    }
    //    ++Index;
    //}
    SweepGenerator.Path = Path;
    SweepGenerator.bCapped = true;
    SweepGenerator.Generate();
    auto V = SweepGenerator.Vertices;
    OutMesh.vertexs.Append(SweepGenerator.Vertices);
    TArray<int32> I;
    for (const auto & T : SweepGenerator.Triangles)
    {
        I.Append(T.ABC);
        OutMesh.indices.Append(T.ABC);
    }

    //FVector SpawnLocation = FVector(0.0f, 0.0f, 0.0f); // 设置生成位置
    //FRotator SpawnRotation = FRotator(0.0f, 0.0f, 0.0f); // 设置生成旋转

    //// 3. 使用 SpawnActor 函数生成新的 Actor
    //AConvertActor* ConvertActor = GWorld->SpawnActor<AConvertActor>(AConvertActor::StaticClass(), SpawnLocation, SpawnRotation);

    //if (!ConvertActor)
    //{
    //    return;
    //}

    //ConvertActor->CreateProceduralMesh(V, I);

    //if (GrowPath.Num() < 2)
    //{
    //    return;
    //}
    //auto Dir = GrowPath[0] - GrowPath[1];
    //Dir.Normalize();
    //auto SectionMesh = InSectionMesh;
    //transformationPlan(InNormal, FVector::XAxisVector, SectionMesh);

    //TArray<FVector> SectionB;
    //SectionB.SetNumZeroed(SectionMesh.Num());
    //int32 Index = 0;
    //for (const auto & iter : SectionMesh)
    //{
    //    SectionB[Index] = iter + FVector::XAxisVector * 10.f;
    //    ++Index;
    //}
    //FGeoMesh SectionModel;
    //createMeshByTwoPolygon(SectionMesh, SectionB, InNormal, SectionModel);

    //// 2. 指定生成的位置和旋转信息
    //FVector SpawnLocation = FVector(0.0f, 0.0f, 0.0f); // 设置生成位置
    //FRotator SpawnRotation = FRotator(0.0f, 0.0f, 0.0f); // 设置生成旋转

    //// 3. 使用 SpawnActor 函数生成新的 Actor
    //AConvertActor* ConvertActor = GWorld->SpawnActor<AConvertActor>(AConvertActor::StaticClass(), SpawnLocation, SpawnRotation);

    //if (!ConvertActor)
    //{
    //    return;
    //}

    //ConvertActor->CreateProceduralMesh(SectionModel.vertexs, SectionModel.indices);
    //auto StaticMesh = FConvertMeshLibrary::ConvertProceduralMeshToStaticMesh(ConvertActor->GetProceduralMesh(), FString(TEXT("ProceduralMesh")));
    //ConvertActor->GetSplineMeshInfo(StaticMesh, GrowPath, OutMesh.vertexs, OutMesh.indices);
    //ConvertActor->Destroy();
}

//TSharedPtr<triangleTreeNode> FGeometryLibrary::createTriangleTree(const FDynamicMesh3& inMesh,const TArray<FIndex3i>& tris)
//{
//    auto num = tris.Num();
//    TSharedPtr<triangleTreeNode> origin;
//    auto tempTris = tris;
//    for (int32 i = 0; i < num; ++i)
//    {
//        TSharedPtr<triangleTreeNode> newNode = MakeShared<triangleTreeNode>();
//        newNode->data = tris[i];
//        if (i == 0)
//        {
//            origin = newNode;
//        }
//        else if (i < num - 1)
//        {
//            for (int32 j = i + 1; j < num; j++)
//            {
//
//            }
//        }
//    }
//    return origin;
//}

bool FGeometryLibrary::uvUnwrap(FDynamicMesh3& inMesh, TArray<TArray<int32>>& outCharts)
{
    if (inMesh.IsCompact() == false)
    {
        return false;
    }

    int32 NumVertices = inMesh.VertexCount();
    TArray<FVector3f> VertexBuffer;
    VertexBuffer.SetNum(NumVertices);
    for (int32 k = 0; k < NumVertices; ++k)
    {
        VertexBuffer[k] = (FVector3f)inMesh.GetVertex(k);
    }


    TArray<int32> IndexBuffer;
    IndexBuffer.Reserve(inMesh.TriangleCount() * 3);
    for (FIndex3i Triangle : inMesh.TrianglesItr())
    {
        IndexBuffer.Add(Triangle.A);
        IndexBuffer.Add(Triangle.B);
        IndexBuffer.Add(Triangle.C);
    }

    TArray<FVector2D> uvVertexBuffer;
    TArray<int32> uvIndexBuffer;
    TArray<int32> vertexRemapArray;
    XAtlasWrapper::XAtlasChartOptions ChartOptions;
    XAtlasWrapper::XAtlasPackOptions PackOptions;
    //PackOptions.Resolution = 10;
    PackOptions.TexelsPerUnit = 9;
    //PackOptions.bRotateChartsToAxis = false;
    //PackOptions.bRotateCharts = false;

    //ChartOptions.MaxCost = 100;
    //ChartOptions.bFixWinding = true;
    //if (NumVertices < 100)
    //{
    //    ChartOptions.MaxChartArea = 79.f;
    //}
    bool success = XAtlasWrapper::ComputeCharts(IndexBuffer, VertexBuffer, ChartOptions, PackOptions, uvVertexBuffer, uvIndexBuffer, vertexRemapArray, outCharts);
    if (success)
    {
        
        inMesh.Clear();
        inMesh.EnableVertexNormals(FVector3f(0, 0, 1));
        inMesh.EnableVertexUVs(FVector2f(0, 0));
        
        int32 numOfVertexs = vertexRemapArray.Num();
        //ParallelFor(numOfVertexs, [&](int32 i)
            for (int32 i = 0; i < numOfVertexs; ++i)
            {
                FVertexInfo vi;
                vi.Position = FVector(VertexBuffer[vertexRemapArray[i]]);
                vi.bHaveN = true;
                vi.bHaveUV = true;
                vi.Normal = FVector3f(0, 0, 1);
                vi.UV = FVector2f(uvVertexBuffer[i]);
                inMesh.AppendVertex(vi);
            }

        //ParallelFor(uvIndexBuffer.Num(), [&](int32 i)
            for (int32 i = 0; i < uvIndexBuffer.Num(); i += 3)
            {
                FIndex3i index3;
                index3.A = uvIndexBuffer[i];
                index3.B = uvIndexBuffer[i + 1];
                index3.C = uvIndexBuffer[i + 2];
                inMesh.AppendTriangle(index3);

            }
        FMeshNormals::QuickComputeVertexNormals(inMesh);

    }
    return success;
}

bool FGeometryLibrary::uvUnwrap(FDynamicMesh3& inMesh)
{
    if (inMesh.IsCompact() == false)
    {
        return false;
    }

    int32 NumVertices = inMesh.VertexCount();
    TArray<FVector3f> VertexBuffer;
    VertexBuffer.SetNum(NumVertices);
    for (int32 k = 0; k < NumVertices; ++k)
    {
        VertexBuffer[k] = (FVector3f)inMesh.GetVertex(k);
    }


    TArray<int32> IndexBuffer;
    IndexBuffer.Reserve(inMesh.TriangleCount() * 3);
    for (FIndex3i Triangle : inMesh.TrianglesItr())
    {
        IndexBuffer.Add(Triangle.A);
        IndexBuffer.Add(Triangle.B);
        IndexBuffer.Add(Triangle.C);
    }

    TArray<FVector2D> uvVertexBuffer;
    TArray<int32> uvIndexBuffer;
    TArray<int32> vertexRemapArray;
    XAtlasWrapper::XAtlasChartOptions ChartOptions;
    XAtlasWrapper::XAtlasPackOptions PackOptions;


    PackOptions.Resolution = 10;
    PackOptions.TexelsPerUnit = 1;

    bool success = XAtlasWrapper::ComputeUVs(IndexBuffer, VertexBuffer, ChartOptions, PackOptions, uvVertexBuffer, uvIndexBuffer, vertexRemapArray);
    if (success)
    {

        inMesh.Clear();
        inMesh.EnableVertexNormals(FVector3f(0, 0, 1));
        inMesh.EnableVertexUVs(FVector2f(0, 0));

        int32 numOfVertexs = vertexRemapArray.Num();
        //ParallelFor(numOfVertexs, [&](int32 i)
        for (int32 i = 0; i < numOfVertexs; ++i)
        {
            FVertexInfo vi;
            vi.Position = FVector(VertexBuffer[vertexRemapArray[i]]);
            vi.bHaveN = true;
            vi.bHaveUV = true;
            vi.Normal = FVector3f(0, 0, 1);
            vi.UV = FVector2f(uvVertexBuffer[i]);
            inMesh.AppendVertex(vi);
        }

        //ParallelFor(uvIndexBuffer.Num(), [&](int32 i)
        for (int32 i = 0; i < uvIndexBuffer.Num(); i += 3)
        {
            FIndex3i index3;
            index3.A = uvIndexBuffer[i];
            index3.B = uvIndexBuffer[i + 1];
            index3.C = uvIndexBuffer[i + 2];
            inMesh.AppendTriangle(index3);

        }
        FMeshNormals::QuickComputeVertexNormals(inMesh);

    }
    return success;
}

bool FGeometryLibrary::uvUnwrap(FDynamicMesh3& inMesh, const TArray<TArray<int32>>& inCharts, const TArray<FVector>& unwrapPath)
{
    inMesh.EnableAttributes();
    auto numOfTri = inMesh.MaxTriangleID();
    TArray<TPair<FVector, FVector>> pathLines;
    for (int32 i = 0; i < unwrapPath.Num() - 1; i++)
    {
        if (i > 0 && FMath::IsNearlyEqual(FVector::DotProduct((unwrapPath[i] - unwrapPath[i - 1]).GetSafeNormal()
            ,(unwrapPath[i + 1] - unwrapPath[i]).GetSafeNormal()),-1.0f,0.000001f))
        {
            continue;
        }
        pathLines.Add(TPair<FVector, FVector>(unwrapPath[i], unwrapPath[i + 1]));
    }

    TArray<FVector> normals;
    TMap<int32, TArray<UE::Geometry::FIndex3i>> pathTris;
    classifyTriangle(inMesh, unwrapPath, pathTris);

    for (const auto& iter : pathTris)
    {

        int32 num = iter.Value.Num();
        if (num <= 0)
        {
            continue;
        }
        TMap<FVector, FVector2f> uvs;
        if (iter.Key == -1)
        {
            for (int32 i = 0; i < num; i++)
            {
                auto tri = pathTris[iter.Key][i];
                auto va = inMesh.GetVertex(tri.A);
                auto vb = inMesh.GetVertex(tri.B);
                auto vc = inMesh.GetVertex(tri.C);
                FVector nor;
                trangleNormal(va, vb, vc, nor);

                TArray<FVector> triVecs;
                triVecs.AddUnique(va);
                triVecs.AddUnique(vb);
                triVecs.AddUnique(vc);
                if (triVecs.Num() != 3)
                {
                    continue;
                }

                TArray<FVector2f> triUvs;
                FVector uvX = nor.Cross(FVector::ZAxisVector).GetSafeNormal();
                if (uvX.Equals(FVector::ZeroVector))
                {
                    uvX = FVector::YAxisVector;
                }
                FVector uvY = uvX.Cross(nor).GetSafeNormal();
                for (const auto& v : triVecs)
                {
                    triUvs.Add(FVector2f(uvX.Dot(v) * 0.01f, uvY.Dot(v) * 0.01f));
                }

                inMesh.SetVertexUV(tri.A, triUvs[0]);
                inMesh.SetVertexUV(tri.B, triUvs[1]);
                inMesh.SetVertexUV(tri.C, triUvs[2]);
            }
        }
        else
        {
            auto tempTris = pathTris[iter.Key];

            for (int32 i = 0; i < num; ++i)
            {
                if (i == 0)
                {
                    auto tri = pathTris[iter.Key][i];
                    auto va = inMesh.GetVertex(tri.A);
                    auto vb = inMesh.GetVertex(tri.B);
                    auto vc = inMesh.GetVertex(tri.C);
                }
            }
        }
    }

    TMap<TPair<int32, int32>, TArray<UE::Geometry::FIndex3i>> tris;
    TMap<int32, TArray<UE::Geometry::FIndex3i>> planTris;
    TArray<FVector> planNormals;
    //classifyTriangle(inMesh, 0.6, planNormals, planTris);
    for (int32 i = 0; i < inCharts.Num(); ++i)
    {
        TArray<UE::Geometry::FIndex3i> index3s;
        planTris.Add(i, index3s);
        for (int32 j = 0; j < inCharts[i].Num(); ++j)
        {
            planTris[i].Add(inMesh.GetTriangle(inCharts[i][j]));
        }
    }

    int32 index = 0; 

    for (const auto& planIter : planTris)
    {
        for (const auto& planTriIter : planIter.Value)
        {
            for (const auto& pathIter : pathTris)
            {
                if (pathIter.Key == -1)
                {
                    continue;
                }

                for (const auto & pathTriIter: pathIter.Value)
                {
                    if (planTriIter == pathTriIter)
                    {               
                        if (tris.Contains(TPair<int32, int32>(planIter.Key, pathIter.Key)))
                        {
                            tris[TPair<int32, int32>(planIter.Key, pathIter.Key)].Add(pathTriIter);
                        }
                        else
                        {
                            TArray<FIndex3i> newTri;
                            newTri.Add(pathTriIter);
                            tris.Add(TPair<int32, int32>(planIter.Key, pathIter.Key), newTri);

                        }
                    }
                }
            }
        }
    }
    //ParallelFor(tris.Num(),[&](int32 triIndex)
     TArray<int32> existPoints;

    for (auto & iter : tris)
    {
        TMap<FVector, FVector2f> uvmap;
        TMap<int32, FVector2f> indUVMap;
        TArray<int32> points;
        if (iter.Key.Value == -1)
        {
            //int32 num = iter.Value.Num();
            //if (num <=0)
            //{
            //    continue;
            //}
            //for (int32 i = 0; i < num; i++)
            //{
            //    auto tri = tris[iter.Key][i];
            //    auto va = inMesh.GetVertex(tri.A);
            //    auto vb = inMesh.GetVertex(tri.B);
            //    auto vc = inMesh.GetVertex(tri.C);
            //    FVector nor;
            //    trangleNormal(va, vb, vc, nor);

            //    TArray<FVector> triVecs;
            //    triVecs.Add(va);
            //    triVecs.Add(vb);
            //    triVecs.Add(vc);

            //    TArray<FVector2f> triUvs;
            //    FVector uvX = nor.Cross(FVector::ZAxisVector);
            //    FVector uvY = uvX.Cross(nor);
            //    for (const auto & v : triVecs)
            //    {
            //        triUvs.Add(FVector2f(uvX.Dot(v) * 0.01f, uvY.Dot(v) * 0.01f));
            //    }

            //    inMesh.SetVertexUV(tri.A, triUvs[0]);
            //    inMesh.SetVertexUV(tri.B, triUvs[1]);
            //    inMesh.SetVertexUV(tri.C, triUvs[2]);
            //}

        }
        else
        {
            FVector pathDir;
            int32 num = iter.Value.Num();

            pathDir = pathLines[iter.Key.Value].Value - pathLines[iter.Key.Value].Key;
            pathDir.Normalize();       

            tris[iter.Key].Sort([](const FIndex3i& TA, const FIndex3i& TB) {return TA.A < TB.A; });

            //ParallelFor(tris[iter.Key].Num(), [&](int32 i)
            for (int32 i = 0; i < num; i++)
            {

                auto tri = tris[iter.Key][i];
                auto va = inMesh.GetVertex(tri.A);
                auto vb = inMesh.GetVertex(tri.B);
                auto vc = inMesh.GetVertex(tri.C);
                auto triIndex = inMesh.FindTriangle(tri.A, tri.B, tri.C);

                FVector nor;// = inMesh.GetTriNormal(i);
                trangleNormal(va, vb, vc, nor);


                if (existPoints.Contains(tri.A))
                {
                    tri.A = inMesh.AppendVertex(va);
                    inMesh.SetTriangle(triIndex, tri);
                }
                points.Add(tri.A);
 

                if (existPoints.Contains(tri.B))
                {
                    tri.B = inMesh.AppendVertex(vb);
                    inMesh.SetTriangle(triIndex, tri);
                }
                points.Add(tri.B);

                if (existPoints.Contains(tri.C))
                {
                    tri.C = inMesh.AppendVertex(vc);
                    inMesh.SetTriangle(triIndex, tri);
                }
                points.Add(tri.C);


                TArray<FVector> triVecs;
                triVecs.AddUnique(va);
                triVecs.AddUnique(vb);
                triVecs.AddUnique(vc);
                if (triVecs.Num() != 3)
                {
                    continue;
                }
                TArray<FVector2f> triUvs;
                //transformationPlan(nor, FVector::ZAxisVector, triVecs);
                triUvs.SetNumZeroed(3);
                
                FVector uvX = nor.Cross(FVector::ZAxisVector).GetSafeNormal();
                if (uvX.Equals(FVector::ZeroVector))
                {
                    uvX = FVector::YAxisVector;
                }
                FVector uvY = uvX.Cross(nor).GetSafeNormal();

                if (uvmap.Contains(vc))
                {
                    triUvs[2] = uvmap[vc];
                    if (uvmap.Contains(vb))
                    {
                        triUvs[1] = uvmap[vb];
                    }
                    else
                    {
                        auto a = triVecs[1] - triVecs[2];
                        triUvs[1] = FVector2f(triUvs[2].X + uvX.Dot(a) * 0.01, triUvs[2].Y + uvY.Dot(a) * 0.01);
                    }
                    if (uvmap.Contains(va))
                    {
                        triUvs[0] = uvmap[va];
                    }
                    else
                    {
                        auto b = triVecs[0] - triVecs[2];
                        triUvs[0] = FVector2f(triUvs[2].X + uvX.Dot(b) * 0.01, triUvs[2].Y + uvY.Dot(b) * 0.01);
                    }

                }
                //else if (uvmap.Contains(vb))
                //{
                //    triUvs[1] = uvmap[vb];
                //    auto a = triVecs[0] - triVecs[1];
                //    triUvs[0] = FVector2f(triUvs[1].X + uvX.Dot(a) * 0.01, triUvs[1].Y + uvY.Dot(a) * 0.01);
                //    auto b = triVecs[2] - triVecs[1];
                //    triUvs[2] = FVector2f(triUvs[1].X + uvX.Dot(b) * 0.01, triUvs[1].Y + uvY.Dot(b) * 0.01);
                //}
                else if (uvmap.Contains(va))
                {
                    triUvs[0] = uvmap[va];
                    if (uvmap.Contains(vb))
                    {
                        triUvs[1] = uvmap[vb];
                    }
                    else
                    {
                        auto a = triVecs[1] - triVecs[0];
                        triUvs[1] = FVector2f(triUvs[0].X + uvX.Dot(a) * 0.01, triUvs[0].Y + uvY.Dot(a) * 0.01);
                    }
                    if (uvmap.Contains(vc))
                    {
                        triUvs[2] = uvmap[vc];
                    }
                    else
                    {
                        auto b = triVecs[2] - triVecs[0];
                        triUvs[2] = FVector2f(triUvs[0].X + uvX.Dot(b) * 0.01, triUvs[0].Y + uvY.Dot(b) * 0.01);
                    }
                }
                else if (uvmap.Contains(vb))
                {
                    triUvs[1] = uvmap[vb];
                    if (uvmap.Contains(va))
                    {
                        triUvs[0] = uvmap[va];
                    }
                    else
                    {
                        auto a = triVecs[0] - triVecs[1];
                        triUvs[0] = FVector2f(triUvs[1].X + uvX.Dot(a) * 0.01, triUvs[1].Y + uvY.Dot(a) * 0.01);
                    }
                    if (uvmap.Contains(vc))
                    {
                        triUvs[2] = uvmap[vc];
                    }
                    else
                    {
                        auto b = triVecs[2] - triVecs[1];
                        triUvs[2] = FVector2f(triUvs[1].X + uvX.Dot(b) * 0.01, triUvs[1].Y + uvY.Dot(b) * 0.01);
                    }

                }
                else
                {
                    triUvs[0] = FVector2f(0, 0);
                    auto a = triVecs[1] - triVecs[0];
                    triUvs[1] = FVector2f(uvX.Dot(a) * 0.01, uvY.Dot(a) * 0.01);
                    auto b = triVecs[2] - triVecs[0];
                    triUvs[2] = FVector2f(uvX.Dot(b) * 0.01, uvY.Dot(b) * 0.01);
                }

                if (!uvmap.Contains(triVecs[0]))
                {
                    uvmap.Add(triVecs[0], triUvs[0]);
                }
                if (!uvmap.Contains(triVecs[1]))
                {
                    uvmap.Add(triVecs[1], triUvs[1]);
                }
                if (!uvmap.Contains(triVecs[2]))
                {
                    uvmap.Add(triVecs[2], triUvs[2]);

                }

                inMesh.SetVertexUV(tri.A, triUvs[0]);
                inMesh.SetVertexUV(tri.B, triUvs[1]);
                inMesh.SetVertexUV(tri.C, triUvs[2]);
            }
            existPoints.Append(points);

        }
    }
    return true;
}

void FGeometryLibrary::uvHorizon(FDynamicMesh3& inOutMesh, const TArray<TArray<int32>>& meshCharts)
{
 
    for (const auto& c : meshCharts)
    {
        bool bRotationY = false;
        bool bMRotationY = false;
        bool bMirrorY = false;
        bool bRotationX = false;
        bool bMRotationX = false;
        bool bMirrorX = false;
        FVector rotatorVec = FVector::ZeroVector;

        TArray<int32> vertexIndex;

        for (int32 i = 0; i < c.Num(); ++i)
        {
            auto tri = inOutMesh.GetTriangle(c[i]);
            auto a = tri.A;
            auto b = tri.B;
            auto c1 = tri.C;

            vertexIndex.AddUnique(a);
            vertexIndex.AddUnique(b);
            vertexIndex.AddUnique(c1);
        }

        TMap<int32, FVector2f> uvMap; // point index and uv
        TMap<FVector, FVector2f> vertexMap; // point vertex and uv

        auto allTriangles = c;
        //calculate first triangle uv
        if (c.Num() <= 0)
        {
            continue;
        }
        {
            auto triFirst = inOutMesh.GetTriangle(c[0]);
            auto ia = triFirst.A;
            auto ib = triFirst.B;
            auto ic = triFirst.C;

            auto va = inOutMesh.GetVertex(ia);
            auto vb = inOutMesh.GetVertex(ib);
            auto vc = inOutMesh.GetVertex(ic);
            FVector vnormal = FVector(inOutMesh.GetVertexNormal(ia));
            FVector normal;
            trangleNormal(va, vb, vc, normal);
            if (normal.Dot(vnormal) < 0)
            {
                normal = -normal;
            }
            

            FVector xAxis;
            FVector yAxis;
            if (normal.Equals(FVector::ZAxisVector))
            {
                xAxis = FVector::VectorPlaneProject(FVector::XAxisVector, normal).GetSafeNormal();
                yAxis = FVector::VectorPlaneProject(FVector::YAxisVector, normal).GetSafeNormal();
            }
            else if (normal.Equals(-FVector::ZAxisVector))
            {
                xAxis = FVector::VectorPlaneProject(FVector::XAxisVector, normal).GetSafeNormal();
                yAxis = FVector::VectorPlaneProject(-FVector::YAxisVector, normal).GetSafeNormal();
            }
            else
            {
                yAxis = FVector::VectorPlaneProject(-FVector::ZAxisVector, normal).GetSafeNormal();
                xAxis = FVector::CrossProduct(yAxis, normal).GetSafeNormal();
            }
            
            UE_LOG(LogTemp, Error, TEXT("000 xAxis %s yAxis %s"), *xAxis.ToString(), *yAxis.ToString())
            FVector2f uva = FVector2f(0, 0);
            FVector2f uvb = FVector2f((vb - va).Dot(xAxis), (vb - va).Dot(yAxis));
            FVector2f uvc = FVector2f((vc - va).Dot(xAxis), (vc - va).Dot(yAxis));

            uvMap.Add(ia, uva);
            uvMap.Add(ib, uvb);
            uvMap.Add(ic, uvc);

            vertexMap.Add(va, uva);
            vertexMap.Add(vb, uvb);
            vertexMap.Add(vc, uvc);

            allTriangles.RemoveAt(0);
        }
  

        //calculate uv of triangles left 
        if (c.Num() <= 1)
        {
            for (auto& mapIter : uvMap)
            {
                inOutMesh.SetVertexUV(mapIter.Key, mapIter.Value * 0.01f);
            }
            continue;
        }
        
        //prevent while unlimited loopping
        auto funSum = [](const int32& count)
        {
            int32 sum = 0;
            int32 n = 1;
            for (n; n < count+1; ++n)
            {
                sum += n;
            }
            return sum;
        };
        int32 maxCount = funSum(allTriangles.Num());
        int32 count = 0;

        while (allTriangles.Num() > 0)
        {
            auto temp = allTriangles;
            for (const auto & i : temp)
            {

                auto tri = inOutMesh.GetTriangle(i);
                auto ia = tri.A;
                auto ib = tri.B;
                auto ic = tri.C;

                auto va = inOutMesh.GetVertex(ia);
                auto vb = inOutMesh.GetVertex(ib);
                auto vc = inOutMesh.GetVertex(ic);

                int32 containIndex = -1;
                int32 containVertex = -1;
                if (uvMap.Contains(ia))
                {
                    containIndex = 0;
                }
                else if (uvMap.Contains(ib))
                {
                    containIndex = 1;
                }
                else if (uvMap.Contains(ic))
                {
                    containIndex = 2;
                }
                else if (vertexMap.Contains(va))
                {
                    containVertex = 0;
                }
                else if (vertexMap.Contains(vb))
                {
                    containVertex = 1;
                }
                else if (vertexMap.Contains(vc))
                {
                    containVertex = 2;
                }
                //dont contain this triangle
                if (containIndex == -1 && containVertex == -1)
                {
                    continue;
                }

                //else
                FVector xAxis;
                FVector yAxis;
                FVector vnormal = FVector(inOutMesh.GetVertexNormal(ia));
                FVector normal;
                trangleNormal(va, vb, vc, normal);
                if (normal.Dot(vnormal) < 0)
                {
                    normal = -normal;
                }
                //UE_LOG(LogTemp, Warning, TEXT("normal %s"), *normal.ToString())

                if (normal.Equals(FVector::ZAxisVector))
                {
                    xAxis = FVector::VectorPlaneProject(FVector::XAxisVector, normal).GetSafeNormal();
                    yAxis = FVector::VectorPlaneProject(FVector::YAxisVector, normal).GetSafeNormal();

                }
                else if (normal.Equals(-FVector::ZAxisVector))
                {
                    xAxis = FVector::VectorPlaneProject(FVector::XAxisVector, normal).GetSafeNormal();
                    yAxis = FVector::VectorPlaneProject(-FVector::YAxisVector, normal).GetSafeNormal();
                }
                else
                {
                    yAxis = FVector::VectorPlaneProject(-FVector::ZAxisVector, normal).GetSafeNormal();
                    xAxis = FVector::CrossProduct(yAxis, normal).GetSafeNormal();
                }
                UE_LOG(LogTemp, Error, TEXT("normal %s"), *normal.ToString())
                UE_LOG(LogTemp, Error, TEXT("xAxis %s yAxis %s"), *xAxis.ToString(), *yAxis.ToString())
                if (containIndex == 0)
                {
                    FVector2f uva = uvMap[ia];
                    FVector2f uvb = uva + FVector2f((vb - va).Dot(xAxis), (vb - va).Dot(yAxis));
                    FVector2f uvc = uva + FVector2f((vc - va).Dot(xAxis), (vc - va).Dot(yAxis));

                    //uvMap.Add(ia, uva);
                    uvMap.Add(ib, uvb);
                    uvMap.Add(ic, uvc);

                    vertexMap.Add(vb, uvb);
                    vertexMap.Add(vc, uvc);
                }
                else if (containIndex == 1)
                {
                    FVector2f uvb = uvMap[ib];
                    FVector2f uva = uvb + FVector2f((va - vb).Dot(xAxis), (va - vb).Dot(yAxis));
                    FVector2f uvc = uvb + FVector2f((vc - vb).Dot(xAxis), (vc - vb).Dot(yAxis));

                    uvMap.Add(ia, uva);
                    //uvMap.Add(ib, uvb);
                    uvMap.Add(ic, uvc);

                    vertexMap.Add(va, uva);
                    vertexMap.Add(vc, uvc);
                }
                else if (containIndex == 2)
                {
                    FVector2f uvc = uvMap[ic];
                    FVector2f uvb = uvc + FVector2f((vb - vc).Dot(xAxis), (vb - vc).Dot(yAxis));
                    FVector2f uva = uvc + FVector2f((va - vc).Dot(xAxis), (va - vc).Dot(yAxis));

                    uvMap.Add(ia, uva);
                    uvMap.Add(ib, uvb);
                    //uvMap.Add(ic, uvc);

                    vertexMap.Add(va, uva);
                    vertexMap.Add(vb, uvb);
                }
                else if (containVertex == 0)
                {
                    FVector2f uva = vertexMap[va];
                    FVector2f uvb = uva + FVector2f((vb - va).Dot(xAxis), (vb - va).Dot(yAxis));
                    FVector2f uvc = uva + FVector2f((vc - va).Dot(xAxis), (vc - va).Dot(yAxis));

                    uvMap.Add(ia, uva);
                    uvMap.Add(ib, uvb);
                    uvMap.Add(ic, uvc);

                    vertexMap.Add(vb, uvb);
                    vertexMap.Add(vc, uvc);
                }
                else if (containVertex == 1)
                {
                    FVector2f uvb = vertexMap[vb];
                    FVector2f uva = uvb + FVector2f((va - vb).Dot(xAxis), (va - vb).Dot(yAxis));
                    FVector2f uvc = uvb + FVector2f((vc - vb).Dot(xAxis), (vc - vb).Dot(yAxis));

                    uvMap.Add(ia, uva);
                    uvMap.Add(ib, uvb);
                    uvMap.Add(ic, uvc);

                    vertexMap.Add(va, uva);
                    vertexMap.Add(vc, uvc);
                }
                else if (containVertex == 2)
                {
                    FVector2f uvc = vertexMap[vc];
                    FVector2f uvb = uvc + FVector2f((vb - vc).Dot(xAxis), (vb - vc).Dot(yAxis));
                    FVector2f uva = uvc + FVector2f((va - vc).Dot(xAxis), (va - vc).Dot(yAxis));

                    uvMap.Add(ia, uva);
                    uvMap.Add(ib, uvb);
                    uvMap.Add(ic, uvc);

                    vertexMap.Add(va, uva);
                    vertexMap.Add(vb, uvb);
                }
               
                allTriangles.Remove(i);
            }
            ++count;
            if (count > maxCount)
            {
                break;
            }
        }

        for (auto & mapIter : uvMap)
        {
            inOutMesh.SetVertexUV(mapIter.Key, mapIter.Value * 0.01f);
        }
    }
}

bool FGeometryLibrary::uvUnwrap(const TArray<FVector>& unwrapPath, FGeoMesh& inOutMesh)
{
    if (unwrapPath.IsEmpty())
    {
        simplifyMesh(inOutMesh);

        FDynamicMesh3 dyMesh = FDynamicMesh3();
        geoMeshConvertToDynamicMesh3(inOutMesh, dyMesh);
        TArray<TArray<int32>> charts;
        //uvUnwrap(dyMesh, charts);   //use xastls to remesh
        ComputeCharts(dyMesh, charts);
        TArray<FGeoMesh> Geos;
        FMeshNormals::QuickComputeVertexNormals(dyMesh);
        ComputeChartMeshes(dyMesh, charts, Geos);
        FGeoMesh OutMesh;
        for (auto& iter : Geos)
        {
            bool bIsArc = false;
            FVector FirstNor;
            for (int32 i = 0; i < iter.indices.Num(); i += 3)
            {
                FVector TempNor;
                trangleNormal(iter.vertexs[iter.indices[i]], iter.vertexs[iter.indices[i + 1]], iter.vertexs[iter.indices[i + 2]], TempNor);
                if (i == 0)
                {
                    FirstNor = TempNor;
                }
                else if (!FirstNor.Equals(TempNor, 0.00001f))
                {
                    bIsArc = true;
                    break;
                }
            }

            if (bIsArc)
            {
                FDynamicMesh3 PlaneMesh = FDynamicMesh3();
                geoMeshConvertToDynamicMesh3(iter, PlaneMesh);
                TArray<TArray<int32>> temp;
                uvUnwrap(PlaneMesh, temp);
                dynamicMesh3ConvertToGeoMesh(PlaneMesh, iter);
            }
            OutMesh.addMesh(iter);
        }

        TMap<TPair<int32, int32>, FMergeChartTriInfo> SaveFlag;
        TSet<TPair<int32, int32>> ChartNeedMerge;
        //TArray<TArray<int32>> InOutChartMergePair = MergeMeshByNormal(dyMesh, charts, 0, SaveFlag, ChartNeedMerge);


        inOutMesh.empty();

        TArray<TPair<FVector, FVector>> outLines;
        TArray<TArray<TPair<FVector, FVector>>> SaveChartFlagZERO;
        SaveChartFlagZERO.Reset(ChartNeedMerge.Num());
        TArray<TArray<TPair<FVector, FVector>>> SaveChartFlagONE;
        SaveChartFlagONE.Reset(ChartNeedMerge.Num());

        for (int i = 0; i < ChartNeedMerge.Num(); i++)
        {
            SaveChartFlagZERO.Push(TArray<TPair<FVector, FVector>>{});
        }

        for (int32 i = 0; i < charts.Num(); ++i)
        {
            TArray<TPair<FVector, FVector>> lines;
            for (int32 j = 0; j < charts[i].Num(); ++j)
            {
                auto tri = dyMesh.GetTriangle(charts[i][j]);
                //UE_LOG(LogTemp, Warning, TEXT("tri %d , %d, %d"), tri.A, tri.B, tri.C);
                FVector av = dyMesh.GetVertex(tri.A);
                FVector bv = dyMesh.GetVertex(tri.B);
                FVector cv = dyMesh.GetVertex(tri.C);


                if (!lines.Contains(TPair<FVector, FVector>(av, bv)) &&
                    !lines.Contains(TPair<FVector, FVector>(bv, av)))
                {
                    lines.Add(TPair<FVector, FVector>(av, bv));
                }
                else
                {
                    lines.Remove(TPair<FVector, FVector>(av, bv));
                    lines.Remove(TPair<FVector, FVector>(bv, av));

                }
                if (!lines.Contains(TPair<FVector, FVector>(bv, cv)) &&
                    !lines.Contains(TPair<FVector, FVector>(cv, bv)))
                {
                    lines.Add(TPair<FVector, FVector>(bv, cv));
                }
                else
                {
                    lines.Remove(TPair<FVector, FVector>(bv, cv));
                    lines.Remove(TPair<FVector, FVector>(cv, bv));
                }
                if (!lines.Contains(TPair<FVector, FVector>(av, cv)) &&
                    !lines.Contains(TPair<FVector, FVector>(cv, av)))
                {
                    lines.Add(TPair<FVector, FVector>(cv, av));
                }
                else
                {
                    lines.Remove(TPair<FVector, FVector>(av, cv));
                    lines.Remove(TPair<FVector, FVector>(cv, av));
                }
            }

            bool bIsSave = false;
            //for (int j = 0; j < ChartNeedMerge.Num(); j++)
            //{
            //    if (ChartNeedMerge[j].Contains(i) && SaveFlag[{ChartNeedMerge[j][0], ChartNeedMerge[j][1]}].Flag == 0)
            //    {
            //        SaveChartFlagZERO[j].Append(lines);
            //        bIsSave = true;
            //    }
            //    if (ChartNeedMerge[j].Contains(i) && SaveFlag[{ChartNeedMerge[j][0], ChartNeedMerge[j][1]}].Flag == 1)
            //    {
            //        SaveChartFlagONE[j].Append(lines);
            //        bIsSave = true;
            //    }
            //}
            if (!bIsSave)
            {
                outLines.Append(lines);
            }

        }

        /* for (auto& NeedMerge : ChartNeedMerge)
         {
             if (SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Flag == 0)
             {
                 FVector point1 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point1;
                 FVector point2 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point2;
                 outLines.Remove({ point1, point2 });
                 outLines.Remove({ point2, point1 });
             }
             if (SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Flag == 1)
             {
                 FVector point1 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point1;
                 FVector point2 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point2;
                 TPair<int32, TPair<FVector, FVector>> FirIntLine = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].FirChartToIntersectionWidget;
                 TPair<int32, TPair<FVector, FVector>> SecIntLine = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].SecChartToIntersectionWidget;

                 if (FirIntLine.Value == TPair<FVector, FVector>{point1, point2} ||
                     FirIntLine.Value == TPair<FVector, FVector>{point2, point1})
                 {
                     TVector<double> p1 = SecIntLine.Value.Key;
                     TVector<double> p2 = SecIntLine.Value.Value;

                     if ((!outLines.Contains(TPair<FVector, FVector>{point1, point2}) && !outLines.Contains(TPair<FVector, FVector>{point2, point1}))
                         || (!outLines.Contains(TPair<FVector, FVector>{p1, p2}) && !outLines.Contains(TPair<FVector, FVector>{p2, p1}))) continue;

                     outLines.Remove({ point1, point2 });
                     outLines.Remove({ point2, point1 });

                     outLines.Remove({ p1, p2 });
                     outLines.Remove({ p2, p1 });
                     TPair<FVector, FVector> newPoint{ point2, p1 == point1 ? p2 : p1 };
                     outLines.Add(newPoint);
                 }
                 else
                 {
                     TVector<double> p1 = FirIntLine.Value.Key;
                     TVector<double> p2 = FirIntLine.Value.Value;

                     if ((!outLines.Contains(TPair<FVector, FVector>{point1, point2}) && !outLines.Contains(TPair<FVector, FVector>{point2, point1}))
                         || (!outLines.Contains(TPair<FVector, FVector>{p1, p2}) && !outLines.Contains(TPair<FVector, FVector>{p2, p1}))) continue;

                     outLines.Remove({ point1, point2 });
                     outLines.Remove({ point2, point1 });

                     outLines.Remove({ p1, p2 });
                     outLines.Remove({ p2, p1 });
                     TPair<FVector, FVector> newPoint{ point2, p1 == point1 ? p2 : p1 };
                     outLines.Add(newPoint);
                 }
             }
             if (SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Flag == 2)
             {
                 FVector IntPoint1 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point1;
                 FVector IntPoint2 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Point2;
                 TPair<int32, TPair<FVector, FVector>> FirIntLine = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].FirChartToIntersectionWidget;
                 TPair<int32, TPair<FVector, FVector>> SecIntLine = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].SecChartToIntersectionWidget;

                 if (FirIntLine.Value == TPair<FVector, FVector>{IntPoint1, IntPoint2}
                 || FirIntLine.Value == TPair<FVector, FVector>{IntPoint2, IntPoint1})
                 {
                     FVector p1 = SecIntLine.Value.Key;
                     FVector p2 = SecIntLine.Value.Value;

                     if ((!outLines.Contains(TPair<FVector, FVector>{IntPoint1, IntPoint2}) && !outLines.Contains(TPair<FVector, FVector>{IntPoint2, IntPoint1}))
                         || (!outLines.Contains(TPair<FVector, FVector>{p1, p2}) && !outLines.Contains(TPair<FVector, FVector>{p2, p1}))) continue;

                     outLines.Remove({ IntPoint1, IntPoint2 });
                     outLines.Remove({ IntPoint2, IntPoint1 });

                     outLines.Remove({ p1, p2 });
                     outLines.Remove({ p2, p1 });


                     if ((p2 - IntPoint2).Size() < (p2 - IntPoint1).Size())
                     {
                         TPair<FVector, FVector> newPoint1{ p2, IntPoint2 };
                         TPair<FVector, FVector> newPoint2{ p1, IntPoint1 };
                         outLines.Add(newPoint1);
                         outLines.Add(newPoint2);
                     }
                     else
                     {
                         TPair<FVector, FVector> newPoint1{ p2, IntPoint1 };
                         TPair<FVector, FVector> newPoint2{ p1, IntPoint2 };
                         outLines.Add(newPoint1);
                         outLines.Add(newPoint2);
                     }
                 }
                 else
                 {
                     FVector p1 = FirIntLine.Value.Key;
                     FVector p2 = FirIntLine.Value.Value;

                     if ((!outLines.Contains(TPair<FVector, FVector>{IntPoint1, IntPoint2}) && !outLines.Contains(TPair<FVector, FVector>{IntPoint2, IntPoint1}))
                         || (!outLines.Contains(TPair<FVector, FVector>{p1, p2}) && !outLines.Contains(TPair<FVector, FVector>{p2, p1}))) continue;

                     outLines.Remove({ IntPoint1, IntPoint2 });
                     outLines.Remove({ IntPoint2, IntPoint1 });

                     outLines.Remove({ p1, p2 });
                     outLines.Remove({ p2, p1 });

                     if ((p2 - IntPoint2).Size() < (p2 - IntPoint1).Size())
                     {
                         TPair<FVector, FVector> newPoint1{ p2, IntPoint2 };
                         TPair<FVector, FVector> newPoint2{ p1, IntPoint1 };
                         outLines.Add(newPoint1);
                         outLines.Add(newPoint2);
                     }
                     else
                     {
                         TPair<FVector, FVector> newPoint1{ p2, IntPoint1 };
                         TPair<FVector, FVector> newPoint2{ p1, IntPoint2 };
                         outLines.Add(newPoint1);
                         outLines.Add(newPoint2);
                     }
                 }
             }

             if (SaveFlag[{NeedMerge.Key, NeedMerge.Value}].Flag == 3)
             {
                 FVector firstPoint1 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].FirChartToIntersectionWidget.Value.Key;
                 FVector firstPoint2 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].FirChartToIntersectionWidget.Value.Value;

                 FVector secondPoint1 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].SecChartToIntersectionWidget.Value.Key;
                 FVector secondPoint2 = SaveFlag[{NeedMerge.Key, NeedMerge.Value}].SecChartToIntersectionWidget.Value.Value;

                 outLines.Remove({ firstPoint1, firstPoint2 });
                 outLines.Remove({ firstPoint2, firstPoint1 });

                 outLines.Remove({ secondPoint1, secondPoint2 });
                 outLines.Remove({ secondPoint2, secondPoint1 });
             }
         }*/

         //TArray<TArray<int32>> newCharts;

         //auto leftCharts = charts;
         //for (const auto& group : InOutChartMergePair)
         //{
         //    TArray<int32> tempCharts;
         //    for (const auto& chart : group)
         //    {
         //        if (chart < charts.Num())
         //        {
         //            tempCharts.Append(charts[chart]);
         //            leftCharts.Remove(charts[chart]);
         //        }
         //    }
         //    newCharts.Add(tempCharts);
         //}
         //for (auto left : leftCharts)
         //{
         //    newCharts.Add(left);
         //}

         //if (tempCharts.Num() < 100)
         //{
         //    uvHorizon(dyMesh, newCharts);
         //}
         //dynamicMesh3ConvertToGeoMesh(dyMesh, inOutMesh);
        inOutMesh = OutMesh;
        inOutMesh.edges.Empty();
        inOutMesh.edges.Append(outLines);
    }
    return true;
}

void FGeometryLibrary::PathGroup(const TArray<FVector>& InPath, TArray<TArray<FVector>>& OutGroupPath)
{
    TArray<FVector> CleanPath;
    for (auto & Point : InPath)
    {
        if (!CleanPath.IsEmpty() && CleanPath.Last().Equals(Point,0.01))
        {
            continue;
        }
        CleanPath.Add(Point);
    }

    TArray<FVector> Group;
    for (auto & Point : CleanPath)
    {
        if (Group.Num() < 2)
        {
            Group.Add(Point);
        }
        else
        {
            auto Last0 = Group.Last();
            auto Last1 = Group.Last(1);

            auto PreDir = (Last0 - Last1).GetSafeNormal();
            auto CurDir = (Point - Last0).GetSafeNormal();
            if (PreDir.Dot(CurDir) > 0.9)
            {
                Group.Add(Point);
            }
            else
            {
                OutGroupPath.Add(Group);
                Group.Empty();
                Group.Add(Last0);
                Group.Add(Point);
            }
        }
    }
    if (!Group.IsEmpty())
    {
        OutGroupPath.Add(Group);
    }
}

void FGeometryLibrary::createMeshByPolyonPath(const TArray<FVector>& inPolygonVertex,const FVector& inNormal, const TArray<FVector>& inPath, const TArray<FVector>& endPolygon, bool bReverse, bool bCalculatePathUV ,FGeoMesh& outMesh)
{
    if (inPath.IsEmpty())
    {
        createPlanarPolygonMesh(inPolygonVertex, inNormal, outMesh);
        return;
    }
   
    TArray<TArray<FVector>> GroupPath;
    PathGroup(inPath, GroupPath);
    TArray<TArray<TArray<FVector>>>  SliceOnPath;  //path->point->slice
    if (GroupPath.IsEmpty() || GroupPath[0].Num() < 2 || GroupPath.Last().Num() < 2)
    {
        return;
    }

    TArray<FVector> SliceCleanOutline;
    for (auto & V : inPolygonVertex)
    {
        bool bHasPoint = false;
        if (!SliceCleanOutline.IsEmpty() && SliceCleanOutline.Last().Equals(V,0.01))
        {
            bHasPoint = true;
        }

        if (!bHasPoint)
        {
            SliceCleanOutline.Add(V);
        }
    }

    if (SliceCleanOutline.IsEmpty())
    {
        return;
    }
    if (!SliceCleanOutline[0].Equals(SliceCleanOutline.Last(), 0.01))
    {
        auto ClosePoint = SliceCleanOutline[0];
        SliceCleanOutline.Add(ClosePoint);
    }

    auto StartX = FString::Printf(TEXT(" % .2f"), GroupPath[0][0].X);
    auto StartY = FString::Printf(TEXT(" % .2f"), GroupPath[0][0].Y);
    auto StartZ = FString::Printf(TEXT(" % .2f"), GroupPath[0][0].Z);

    auto EndX = FString::Printf(TEXT(" % .2f"), GroupPath.Last().Last().X);
    auto EndY = FString::Printf(TEXT(" % .2f"), GroupPath.Last().Last().Y);
    auto EndZ = FString::Printf(TEXT(" % .2f"), GroupPath.Last().Last().Z);

    bool bLoop = (StartX.Equals(EndX)) && (StartY.Equals(EndY)) && (StartZ.Equals(EndZ));
    auto PathStartDir = (GroupPath[0][1] - GroupPath[0][0]).GetSafeNormal();
    auto PathEndDir = (GroupPath.Last().Last() - GroupPath.Last().Last(1)).GetSafeNormal();
    auto StartDir = bLoop ? (PathStartDir + PathEndDir).GetSafeNormal() : PathStartDir;
    auto EndDir = bLoop ? (PathStartDir + PathEndDir).GetSafeNormal() : PathEndDir;

    auto ProjectSlice = [](const TArray<FVector> & InSliceOutline,const FVector & InPathPoint, const FVector& InPreDir,const FVector& InSliceDir,bool bNeedMerge,TArray<FVector> & InOutSlice)
        {
            InOutSlice.Empty();
            for (const auto& P : InSliceOutline)
            {
                FVector TraceStart = P - InPreDir * 999999.9f;
                FVector TraceEnd = P + InPreDir * 999999.9f;

                auto NewVec = FMath::LinePlaneIntersection<double>(TraceStart, TraceEnd, InPathPoint, InSliceDir);
                auto NewDir = (NewVec - P).GetSafeNormal();
                if (bNeedMerge && FVector::DotProduct(InPreDir, NewDir) < 0.1f)
                {
                    NewVec = P;
                }
                InOutSlice.Add(NewVec);
            }
        };



    TArray<FVector> SliceOutline = SliceCleanOutline;
    TArray<FVector> EveryStartDir;

    for (int32 i = 0; i < GroupPath.Num(); ++i)
    {
        TArray<TArray<FVector>> TempSlice;
        FVector TempDir = FVector::ZeroVector;


        for (int32 j = 0; j < GroupPath[i].Num(); ++j)
        {
            bool bIsStart = false;
            bool bIsEnd = false;

            FVector PreDir = FVector::ZeroVector;
            FVector NextDir = FVector::ZeroVector;
            FVector SliceDir = FVector::ZeroVector;

            int32 Pre = j - 1;
            int32 Next = j + 1;

            if (j == 0)
            {
                if (i == 0)
                {
                    //放样起点
                    SliceDir = StartDir;
                    NextDir = (GroupPath[i][j + 1] - GroupPath[i][j]).GetSafeNormal();
                    bIsStart = true;
                }
                else
                {
                    //中间路径起点
                    if (GroupPath[i - 1].Num() > 1)
                    {
                        PreDir = (GroupPath[i - 1].Last() - GroupPath[i - 1].Last(1)).GetSafeNormal();
                        NextDir = (GroupPath[i][j + 1] - GroupPath[i][j]).GetSafeNormal();
                        SliceDir = (PreDir + NextDir).GetSafeNormal();

                    }
                }
            }
            else if (j == GroupPath[i].Num() - 1)
            {
                if (i == GroupPath.Num() - 1)
                {
                    //放样终点
                    PreDir = (GroupPath[i][j] - GroupPath[i][j - 1]).GetSafeNormal();
                    SliceDir = EndDir;
                    bIsEnd = true;
                }
                else
                {
                    //中间放样终点
                    if (GroupPath[i + 1].Num() > 1)
                    {
                        PreDir = (GroupPath[i][j] - GroupPath[i][j - 1]).GetSafeNormal();
                        NextDir = (GroupPath[i + 1][1] - GroupPath[i + 1][0]).GetSafeNormal();
                        SliceDir = (PreDir + NextDir).GetSafeNormal();
                    }
                }
                TempDir = SliceDir;

            }
            else
            {
                //中间放样路径
                PreDir = (GroupPath[i][j] - GroupPath[i][Pre]).GetSafeNormal();
                NextDir = (GroupPath[i][Next] - GroupPath[i][j]).GetSafeNormal();
                SliceDir = (PreDir + NextDir).GetSafeNormal();
            }

            if (j == 0)
            {
                EveryStartDir.Add(SliceDir);
            }

            TArray<FVector> NewSlice;

            if (bIsStart)
            {
                auto OrgSlice = SliceCleanOutline;
                transformationPlan(inNormal, -PathStartDir, OrgSlice);

                for (auto& iter : OrgSlice)
                {
                    iter += GroupPath[i][j];
                }

                if (bLoop)
                {
                    ProjectSlice(OrgSlice, GroupPath[i][j], PathStartDir, SliceDir,false, NewSlice);
                }
                else
                {
                    NewSlice = OrgSlice;
                }
            }
            else if (bIsEnd && !bLoop)
            {
                NewSlice.Empty();
                NewSlice = SliceCleanOutline;
                transformationPlan(inNormal, -PathEndDir, NewSlice);

                for (auto& iter : NewSlice)
                {
                    iter += GroupPath[i][j];
                }
            }
            else
            {
                NewSlice.Empty();
                for (const auto& P : SliceOutline)
                {
                    FVector TraceStart = P;
                    FVector TraceEnd = P + PreDir * 999999.9f;

                    auto NewVec = FMath::LinePlaneIntersection<double>(TraceStart, TraceEnd, GroupPath[i][j], SliceDir);
                    auto NewDir = (NewVec - P).GetSafeNormal();
                    if (FVector::DotProduct(PreDir, NewDir) < 0.1f && j != GroupPath[i].Num() - 1)
                    {
                        //NewVec = P;
                    }
                    NewSlice.Add(NewVec);
                }
            }

            TempSlice.Add(NewSlice);
            SliceOutline.Empty();
            SliceOutline = NewSlice;


        }
        if (TempSlice.Num() > 0)
        {
            SliceOnPath.Add(TempSlice);
        }
    }

    if (SliceOnPath.IsEmpty() || EveryStartDir.Num() != SliceOnPath.Num())
    {
        return;
    }

    for (int32 i = 0; i < SliceOnPath.Num(); i++)
    {
        TArray<FVector> FirstSlice = SliceOnPath[i][0];
        FVector SliceNormal = EveryStartDir[i];
        FPlane LimitPlane = FPlane(SliceOnPath[i][0][0], SliceNormal);

        for (int32 j = 1; j < SliceOnPath[i].Num(); ++j)
        {
            bool bSliceIntersect = false;
            for (int32 m = 0; m < SliceOnPath[i][j].Num(); ++m)
            {
                int32 n = (m + 1) % SliceOnPath[i][j].Num();
                FVector OutIntersection;
                if (FMath::SegmentPlaneIntersection(SliceOnPath[i][j][m], SliceOnPath[i][j][n], LimitPlane, OutIntersection))
                {
                    bSliceIntersect = true;
                    break;
                }
            }

            if (bSliceIntersect)
            {
                if (SliceOnPath[i][j].Num() == SliceOnPath[i][0].Num())
                {
                    for (int32 m = 0; m < SliceOnPath[i][j].Num(); ++m)
                    {
                        int32 n = (m + 1) % SliceOnPath[i][j].Num();
                        FVector CurDir = (SliceOnPath[i][0][m] - SliceOnPath[i][j][m]).GetSafeNormal();
                        if (CurDir.Dot(-SliceNormal) < 0)
                        {
                            SliceOnPath[i][j][m] = SliceOnPath[i][0][m];
                        }
                    }
                }
            }
        }


        if (!bLoop && i == 0)
        {
            continue;
        }

        int32 pre = (i - 1 + SliceOnPath.Num()) % SliceOnPath.Num();
        for (int32 j = SliceOnPath[pre].Num() - 1; j >= 0; --j)
        {
            bool bSliceIntersect = false;
            for (int32 m = 0; m < SliceOnPath[pre][j].Num(); ++m)
            {
                int32 n = (m + 1) % SliceOnPath[pre][j].Num();
                FVector OutIntersection;
                if (FMath::SegmentPlaneIntersection(SliceOnPath[pre][j][m], SliceOnPath[pre][j][n], LimitPlane, OutIntersection))
                {
                    bSliceIntersect = true;
                    break;
                }
            }

            if (bSliceIntersect)
            {
                if (SliceOnPath[pre][j].Num() == SliceOnPath[i][0].Num())
                {
                    for (int32 m = 0; m < SliceOnPath[pre][j].Num(); ++m)
                    {
                        int32 n = (m + 1) % SliceOnPath[pre][j].Num();
                        FVector CurDir = (SliceOnPath[i][0][m] - SliceOnPath[pre][j][m]).GetSafeNormal();
                        if (CurDir.Dot(SliceNormal) < 0)
                        {
                            SliceOnPath[pre][j][m] = SliceOnPath[i][0][m];
                        }
                    }
                }
            }
        }
    }

    for (int32 i = 0; i < SliceOnPath.Num(); ++i)
    {
        FGeoMesh SliceMesh;

        FVector2f UV0 = FVector2f::ZeroVector;
        FVector2f UV1 = FVector2f::ZeroVector;
        FVector2f UV2 = FVector2f::ZeroVector;
        FVector2f UV3 = FVector2f::ZeroVector;

        for (int32 j = 0; j < SliceOnPath[i].Num(); ++j)
        {
            if (j == SliceOnPath[i].Num() - 1)
            {
                if (i == SliceOnPath.Num() - 1 && !bLoop)
                {
                    FGeoMesh NewMesh;
                    createPlanarPolygonMesh(SliceOnPath[i][j], EndDir, NewMesh);
                    outMesh.addMesh(NewMesh);
                }
            }
            else
            {

                if (i == 0 && j == 0 && !bLoop)
                {
                    FGeoMesh NewMesh;
                    createPlanarPolygonMesh(SliceOnPath[i][j], -StartDir, NewMesh);
                    outMesh.addMesh(NewMesh);
                }

                for (int32 m = 0; m < SliceOnPath[i][j].Num() - 1; ++m)
                {
                    int32 n = m + 1;

                    FVector Point0 = SliceOnPath[i][j][m];
                    FVector Point1 = SliceOnPath[i][j][n];
                    FVector Point2 = SliceOnPath[i][j + 1][n];
                    FVector Point3 = SliceOnPath[i][j + 1][m];

                    FVector Nor = FVector::CrossProduct(Point1 - Point0, Point2 - Point1).GetSafeNormal();
                    FVector UDir = (GroupPath[i][j + 1] - GroupPath[i][j]).GetSafeNormal();
                    FVector VDir = FVector::CrossProduct(Nor, UDir).GetSafeNormal();



                    FGeoMesh NewMesh;
                    //createPlanarPolygonMesh({ Point0 ,Point1 ,Point2 ,Point3 }, Nor, NewMesh);
                    NewMesh.vertexs = { Point0 ,Point1 ,Point2 ,Point3 };
                    NewMesh.indices = TArray<int32>{ 0,2,1,0,3,2 };
                    NewMesh.uv.Empty();
                    NewMesh.normals = TArray<FVector3f>{ FVector3f(Nor),FVector3f(Nor) ,FVector3f(Nor) ,FVector3f(Nor) };
                    if (m == 0)
                    {
                        FVector Offset1 = Point1 - Point0;
                        UV1 = FVector2f(Offset1.Dot(UDir) * 0.01, Offset1.Dot(VDir) * 0.01) + UV0;
                        FVector Offset2 = Point2 - Point0;
                        UV2 = FVector2f(Offset2.Dot(UDir) * 0.01, Offset2.Dot(VDir) * 0.01) + UV0;
                        FVector Offset3 = Point3 - Point0;
                        UV3 = FVector2f(Offset3.Dot(UDir) * 0.01, Offset3.Dot(VDir) * 0.01) + UV0;
                    }
                    else
                    {
                        UV0 = UV1;
                        UV3 = UV2;

                        FVector Offset1 = Point1 - Point0;
                        UV1 = FVector2f(Offset1.Dot(UDir) * 0.01, Offset1.Dot(VDir) * 0.01) + UV0;
                        FVector Offset2 = Point2 - Point0;
                        UV2 = FVector2f(Offset2.Dot(UDir) * 0.01, Offset2.Dot(VDir) * 0.01) + UV0;
                    }

                    NewMesh.uv.Add(UV0);
                    NewMesh.uv.Add(UV1);
                    NewMesh.uv.Add(UV2);
                    NewMesh.uv.Add(UV3);

                    if (m == SliceOnPath[i][j].Num() - 2 || bCalculatePathUV)
                    {
                        SliceMesh.addMesh(NewMesh);
                    }
                    else
                    {
                        SliceMesh.Append(NewMesh);
                    }
                }
            }

        }

        outMesh.addMesh(SliceMesh);
    }
}

int32 FGeometryLibrary::PolygonLeftBottom(const TArray<FVector>& inPoints, const FVector& inNormal)
{
    //only xy plan
    int32 Count = inPoints.Num();
    double MinX = MAX_dbl;
    double MinY = MAX_dbl;
    int32 Index = -1;
    for (int32 i = 0; i < Count; ++i)
    {

        if (inPoints[i].X < MinX)
        {
            MinX = inPoints[i].X;
            MinY = inPoints[i].Y;
            Index = i;
        }
        else if (inPoints[i].X == MinX)
        {
            if (inPoints[i].Y < MinY)
            {
                MinY = inPoints[i].Y;
                Index = i;
            }
        }
    }
    return Index;
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif
