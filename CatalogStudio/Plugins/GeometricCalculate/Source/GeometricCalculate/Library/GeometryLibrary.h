// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Operations/MeshBoolean.h"
#include "Runtime/GeometryFramework/Public/UDynamicMesh.h"

#pragma warning(push)
#pragma warning(disable:6294) /* Ill-defined for-loop:  initial condition does not satisfy test.  Loop body not executed. */
#pragma warning(disable:6326) /* Potential comparison of a constant with another constant. */
#pragma warning(disable:4456) /* declaration of 'LocalVariable' hides previous local declaration */ 
#pragma warning(disable:4457) /* declaration of 'LocalVariable' hides function parameter */ 
#pragma warning(disable:4458) /* declaration of 'LocalVariable' hides class member */ 
#pragma warning(disable:4459) /* declaration of 'LocalVariable' hides global declaration */ 
#pragma warning(disable:6244) /* local declaration of <variable> hides previous declaration at <line> of <file> */
#pragma warning(disable:4702) /* unreachable code */

PRAGMA_DEFAULT_VISIBILITY_START
PRAGMA_DISABLE_DEPRECATION_WARNINGS

#include <Eigen/Dense>

PRAGMA_ENABLE_DEPRECATION_WARNINGS
PRAGMA_DEFAULT_VISIBILITY_END

#pragma warning(pop)

#define MERGE_TRI_AREA_TOLERANCE 0.2

struct FGeoMesh
{
public:
    TArray<FVector>     vertexs;
    TArray<int32>       indices;
    TArray<FVector3f>	normals;
    TArray<FVector2f>	uv;
    TArray<TPair<FVector, FVector>> edges;

public:
    FGeoMesh()
        :vertexs(TArray<FVector>())
        , indices(TArray<int32>())
        , normals(TArray<FVector3f>())
        , uv(TArray<FVector2f>())
        , edges(TArray<TPair<FVector, FVector>>())
    {

    }
    FGeoMesh(const FGeoMesh& inMesh)
        :vertexs(inMesh.vertexs)
        , indices(inMesh.indices)
        , normals(inMesh.normals)
        , uv(inMesh.uv)
        , edges(inMesh.edges)
    {

    }

    void addMesh(const FGeoMesh& inOther)
    {
        for (auto& iter : inOther.indices)
        {
            indices.Add(vertexs.Num() + iter);
        }
        vertexs.Append(inOther.vertexs);
        normals.Append(inOther.normals);
        uv.Append(inOther.uv);
        edges.Append(inOther.edges);
    }

    void Append(const FGeoMesh& inOther)
    {
        auto newIndices = inOther.indices;
        int32 oldIndice = 0;
        TMap<int32, int32> OldAndNewIndex;
        for (auto& v : inOther.vertexs)
        {
            int32 index = vertexs.Find(v);
            if (index != INDEX_NONE)
            {
                OldAndNewIndex.Add(oldIndice,index);
            }
            else
            {
                vertexs.Add(v);
                uv.Add(inOther.uv[oldIndice]);
                normals.Add(inOther.normals[oldIndice]);
                OldAndNewIndex.Add(oldIndice, vertexs.Num() - 1);
            }
            ++oldIndice;
        }

        for (auto & iter : inOther.indices)
        {
            if (OldAndNewIndex.Contains(iter))
            {
                indices.Add(OldAndNewIndex[iter]);
            }
            else
            {
                indices.Add(vertexs.Num() + iter - OldAndNewIndex.Num());
            }
        }
        //vertexs.Append(inOther.vertexs);
        //normals.Append(inOther.normals);
        //uv.Append(inOther.uv);
        edges.Append(inOther.edges);
    }

    void empty()
    {
        vertexs.Empty();
        indices.Empty();
        normals.Empty();
        uv.Empty();
        edges.Empty();
    }
};

//struct triangleTreeNode
//{
//public:
//    FIndex3i data;
//    TSharedPtr<triangleTreeNode> father;
//    TArray<TSharedPtr<triangleTreeNode>> children;
//
//public:
//    triangleTreeNode()
//    {
//        data = FIndex3i();
//        father = nullptr;
//    }
//    void addChildren(TSharedPtr<triangleTreeNode> childNode)
//    {
//        children.Add(childNode);
//    }
//
//};

struct FMergeChartTriInfo
{
    /*
     * Flag = 0        Flag = 1    Flag = 2      Flag = 3  奇怪情况
     *   /\             /\           /\
     *  /  \           /  \         /  \
     * /____\         /____\       /____\
     * \    /             \/         \/
     *  \  /              
     *   \/
     */
    int32 Flag = 0; 

    TPair<int32, int32> IndexInChartsFirst;   // 第IndexInChartsFirst.key个chart的IndexInChartsFirst.value三角形和IndexInChartsSecond.key个chart的IndexInChartsSecond.value个三角形相交
    TPair<int32, int32> IndexInChartsSecond;

    FVector Point1; // 对于flag = 1， Point1存放公共点， Point2存放相交点
    FVector Point2;

    TPair<int32, TPair<FVector, FVector>> FirChartToIntersectionWidget;
    TPair<int32, TPair<FVector, FVector>> SecChartToIntersectionWidget;

    FMergeChartTriInfo(int32 _flag, TPair<int32, int32> _IndexInChartsFirst, TPair<int, int> _IndexInChartSecond, FVector _Point1, FVector _Point2, 
        TPair<int32, TPair<FVector, FVector>> _FirChartToIntersectionWidget, TPair<int32, TPair<FVector, FVector>> _SecChartToIntersectionWidget) :
		Flag(_flag),
		IndexInChartsFirst(_IndexInChartsFirst),
		IndexInChartsSecond(_IndexInChartSecond),
		Point1(_Point1),
		Point2(_Point2),
		FirChartToIntersectionWidget(_FirChartToIntersectionWidget),
		SecChartToIntersectionWidget(_SecChartToIntersectionWidget){}

    FMergeChartTriInfo(TPair<int32, int32> _IndexInChartsFirst, TPair<int32, int32> _IndexInChartSecond, FVector _Point1, FVector _Point2, 
        TPair<int32, TPair<FVector, FVector>> _FirChartToIntersectionWidget, TPair<int32, TPair<FVector, FVector>> _SecChartToIntersectionWidget) :
		IndexInChartsFirst(_IndexInChartsFirst),
		IndexInChartsSecond(_IndexInChartSecond),
        Point1(_Point1),
        Point2(_Point2),
        FirChartToIntersectionWidget(_FirChartToIntersectionWidget),
        SecChartToIntersectionWidget(_SecChartToIntersectionWidget) {}
};


class GEOMETRICCALCULATE_API FGeometryLibrary
{

public:
    void CheckLibrary();

private:

    static void boundOfPoints(const TArray<FVector> & inPoints, FVector& min, FVector& max)
    {
        min = FVector(.0f, .0f, .0f);
        max = FVector(.0f, .0f, .0f);

        for (auto& iter : inPoints)
        {
            min = FVector(
                FMath::Min(iter.X, min.X),
                FMath::Min(iter.Y, min.Y),
                FMath::Min(iter.Z, min.Z));

            max = FVector(
                FMath::Max(iter.X, max.X),
                FMath::Max(iter.Y, max.Y),
                FMath::Max(iter.Z, max.Z));
        }
    }
public:

	static	void delaunayTriangulation(const TArray<FVector>& inPoints, const FVector& inNormal, FGeoMesh& outMesh);

    static void createPlanarPolygonMesh(const TArray<FVector>& inPoints, const FVector& inNormal, FGeoMesh& outMesh);
    
    static void createCurvedSurfaceWithTwoArcSegment(const TArray<FVector>& inPointsA,const TArray<FVector>& inPointsB, FGeoMesh& outMesh);


    static void createEdgesPolygonMesh(const TArray<TPair<FVector, FVector>>& inEdge, const FVector& inNormal, FGeoMesh& outMesh);


    static void marchingCubes(const TArray<FVector>& inPoints, FGeoMesh& outMesh);

    static void blendMarchingCubes(const TArray<FVector>& inPoints, FGeoMesh& outMesh);

    static void ballPivotingMesh(const TArray<FVector>& inPoints, FGeoMesh& outMesh);

    static bool createArcSegmentByRadius(const double& radius, const FVector& startPoint, const FVector& endPoint, const int32& inStep, const  FVector& inNormal, bool bBigPart, const FTransform& transform, TArray<FVector>& outVertices);

    static bool createArcSegmentByHeight(const double& arcHeight, const FVector& startPoint, const FVector& endPoint, const int32& inStep, const  FVector& inNormal, const FTransform& transform, TArray<FVector>& outVertices);


    static void scalePolygonMesh(const TMap<int32, TArray<FVector>>& inLinePoints, const TArray<double>& inDistance, const FVector& inNormal, TArray<FVector>& outKeyPoints, TMap<FVector, FVector>& outAllPoints, bool bIsCustom = true);

    static void scalePolygonMesh(const TMap<int32, TArray<FVector>>& inLinePoints, const TArray<double>& inDistance, const FVector& inNormal, TMap<int32, TArray<FVector>>& outAllPoints);

    static void geoMeshConvertToDynamicMesh3(const FGeoMesh& inMesh, FDynamicMesh3& outMesh);

    static void dynamicMesh3ConvertToGeoMesh(const FDynamicMesh3& inMesh, FGeoMesh& outMesh);



    static bool uvUnwrap(const TArray<FVector>& unwrapPath,FGeoMesh& inOutMesh);


    static void PathGroup(const TArray<FVector>& InPath, TArray<TArray<FVector>>& OutGroupPath);

    static void createMeshByPolyonPath(const TArray<FVector>& inPolygonVertex, const FVector& inNormal, const TArray<FVector>& inPath,const TArray<FVector>& endPolygon, bool bReverse, bool bCalculatePathUV,FGeoMesh& outMesh);

    static void createMeshByTwoPolygon(const TArray<FVector>& inPolygonA, const TArray<FVector>& inPolygonB, const FVector& inNormal, FGeoMesh& outMesh);

    //use map to find the point of b linking to a
    static void createMeshByTwoPolygon(const TArray<FVector>& inPolygonA, const TMap<FVector, FVector>& ABMap,const FVector& inNormal, FGeoMesh& outMesh);

    /**
     * @brief 3D中计算两条线段的交点
     * @param SegmentStartA 
     * @param SegmentEndA 
     * @param SegmentStartB 
     * @param SegmentEndB 
     * @param out_IntersectionPoint 
     * @return 
     */
    static bool SegmentIntersection3D(const FVector& SegmentStartA, const FVector& SegmentEndA, const FVector& SegmentStartB, const FVector& SegmentEndB, FVector& out_IntersectionPoint);

    /**
     * @brief 根据3点计算圆心
     * @param PointInCircle 选择圆上三点组成Array传入 
     * @return 
     */
    static FVector GetCenterCircle(const TArray<FVector>& PointInCircle);

    /**
     * @brief 旋转UV
     * @param InOutUVs 
     * @param Angle 旋转角度 弧度制
     */
    static void RotateUV(TArray<FVector2D>& InOutUVs, const double Angle);
    static void RotateUV(FVector2f& InOutUV, const double Angle);

    /**
     * @brief 合并法线角度小于阈值的chart
     * @param inMesh 
     * @param Charts 
     * @param MaxAngle 角度阈值
     * @param InOutSaveFlag 记录需要合并的chart的相关信息
     * @param InOutChartMergePair 按对输出需要合并的chart
     * @return 需要合并的chart， chart Index已排序
     */
    static TArray<TArray<int32>> MergeMeshByNormal(const FDynamicMesh3& inMesh, TArray<TArray<int32>>& Charts, double MaxAngle, TMap<TPair<int32, int32>, 
        FMergeChartTriInfo>& InOutSaveFlag, TSet<TPair<int32, int32>>& InOutChartMergePair);

    /**
     * @brief 仅在特殊条件下判断，非通用
     * @param FirstTri 
     * @param SecondTri 
     * @param InOutOverlapPoint [0] [1] [2]
     * @return 
     */
    static bool JudgeAndGetOverlapEdge(const TArray<FVector>& FirstTri, const TArray<FVector>& SecondTri, TArray<TPair<FVector, FVector>>& InOutOverlapPoint);

    static bool TriangleNear(const TArray<FVector>& FirstTri, const TArray<FVector>& SecondTri);

    static void TriangleNearMap(const FDynamicMesh3& InMesh, TMap<int32, TArray<int32>>& OutMap);

    static bool TwoTrianglesInSamePlane(const FDynamicMesh3& InMesh, const int32& IndexA, const int32& IndexB);

    static void ComputeCharts(const FDynamicMesh3& InMesh,TArray<TArray<int32>>& OutCharts);

    static void ComputeChartMeshes(const FDynamicMesh3& InMesh, const TArray<TArray<int32>>& OutCharts, TArray<FGeoMesh>& OutMesh);

    static void ComputeUVsByPatchBuilder(FDynamicMesh3& InOutMesh);
private:
    //mesh tool
    static FDynamicMesh3 solidifyMesh(const FDynamicMesh3& inMesh, const double& radius);
    static FDynamicMesh3 offsetMesh(const FDynamicMesh3& inMesh, const double& offsetDistance, const int32& voxelCount);
    static FDynamicMesh3 simplifyMesh(const FDynamicMesh3& inMesh, const int32& triangleCount);
    static void simplifyMesh(FGeoMesh& InOutMesh);

    static FDynamicMesh3 remesh(const FDynamicMesh3& inMesh, const double& radius);
    static bool classifyTriangle(const FDynamicMesh3& inMesh, const double& inRange, TArray<FVector>& outNormals,TMap<int32, TArray<UE::Geometry::FIndex3i>>& outTris);
    static bool classifyTriangle(const FDynamicMesh3& inMesh, const TArray<FVector>& path, TMap<int32, TArray<UE::Geometry::FIndex3i>>& outTris);
    static bool classifyPlanEdges(const FDynamicMesh3& inMesh, const double& inNormalRange, TMap<int32, TArray<TPair<FVector, FVector>>>& outPlanEdges);
    static bool uvUnwrap(FDynamicMesh3& inMesh, const double& inRange);
    static bool edgesToOutline(const TArray<TPair<FVector, FVector>>& inEdges, TArray<FVector>& outline);
    static bool uvUnwrap(FDynamicMesh3& inMesh, TArray<TArray<int32>>& outCharts);
    static bool uvUnwrap(FDynamicMesh3& inMesh);

    static bool uvUnwrap(FDynamicMesh3& inMesh, const TArray<TArray<int32>>& inCharts, const TArray<FVector>& unwrapPath);

    // use in LineLineIntersection3D
    static bool FindPointSegmentIntersection(const FVector& Point, const FVector& SegmentStart,
        const FVector& SegmentEnd);

    //make the xAxis of uv parellel to xy plane
    static void uvHorizon(FDynamicMesh3& inOutMesh, const TArray<TArray<int32>>& meshCharts);
public:
    //math tool

    static FMatrix GetTransformMatrixFromVectors(const FVector& A, const FVector& B);
    static	void transformationMaterix(const Eigen::Vector3d& inVecBefore, const Eigen::Vector3d& inVecAfter, Eigen::Matrix3d& outMat);

    static	void transformationPlan(const FVector& inVecBefore, const FVector& inVecAfter, TArray<FVector>& inOutPlan);
    static	void transformationPoint(const FVector& inVecBefore, const FVector& inVecAfter, FVector& inOutPoint);

    static double verctorToAngle(const FVector& inVec, const FVector& inAxisX, const FVector& inAxisY, bool bOutside = true);
    static double radOfTwoVector(const FVector& vecA, const FVector& vecB);
    static double radOfTwoVector(const FVector& vecA, const FVector& vecB, const FVector& inNormal);

    static bool vectorIntersectPoint(const FVector& vecA, const FVector& pointA, const FVector& vecB, const FVector& pointB, FVector& outPoint);

    static void trangleNormal(const FVector& p0, const FVector& p1, const FVector& p2, FVector& outNormal);

    static void uniformVertexOfTwoPlan(TArray<FVector>& planA, const FVector& inNormalA, TArray<FVector>& planB, const FVector& inNormalB);
    static void uniformVertexNum(TArray<FVector>& planA, const FVector& inNormalA, TArray<FVector>& planB, const FVector& inNormalB);
    static bool segmentPlaneIntersectPoint(const TPair<FVector, FVector>& inSegment, const FVector& inPlaneNormal, const FVector& inPlanePoint, FVector& outPoint);

	static TArray<FVector> OffsetPolygon(const TArray<FVector>& InPolygon, const double& InOffset,const FVector& InNormal);
    static bool clockwise(const TArray<FVector>& inPoints, const FVector& inNormal);
    static int32 PolygonLeftBottom(const TArray<FVector>& inPoints, const FVector& inNormal);

public:
    //bool function
    static void meshBoolean(const FGeoMesh& target, const FGeoMesh& tool, const UE::Geometry::FMeshBoolean::EBooleanOp& operation, FGeoMesh& outMesh);

//private:
//    TSharedPtr<triangleTreeNode> createTriangleTree(const FDynamicMesh3& inMesh,const TArray<FIndex3i>& tris);

public:
    //about spline mesh
    static void CreateSplineMesh(const TArray<FVector>& InSectionMesh,const FVector& InNormal, const TArray<FVector>& unwrapPath, FGeoMesh& OutMesh);
};

