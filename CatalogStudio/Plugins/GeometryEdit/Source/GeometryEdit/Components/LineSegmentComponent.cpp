// Fill out your copyright notice in the Description page of Project Settings.


#include "LineSegmentComponent.h"
#include "EngineGlobals.h"
#include "RHI.h"
#include "RenderingThread.h"
#include "RenderResource.h"
#include "VertexFactory.h"
#include "LocalVertexFactory.h"
#include "PrimitiveViewRelevance.h"
#include "PrimitiveSceneProxy.h"
#include "Engine/Engine.h"
#include "MaterialShared.h"
#include "Materials/Material.h"
#include "SceneManagement.h"
#include "UObject/UObjectHash.h"
#include "UObject/UObjectIterator.h"



/** Represents a ULineSegmentComponent to the scene manager. */
class FLineSegmentSceneProxy final : public FPrimitiveSceneProxy
{
public:
	SIZE_T GetTypeHash() const override
	{
		static size_t UniquePointer;
		return reinterpret_cast<size_t>(&UniquePointer);
	}

	FLineSegmentSceneProxy(ULineSegmentComponent* Component)
		: FPrimitiveSceneProxy(Component)
		, LineColor(Component->LineColor)
		, LinePoints(Component->LinePoints)
		, Linesize(Component->Linesize)
	{
		bWillEverBeLit = false;
	}

	virtual ~FLineSegmentSceneProxy()
	{
		LinePoints.Empty();
	}

	virtual void GetDynamicMeshElements(const TArray<const FSceneView*>& Views, const FSceneViewFamily& ViewFamily, uint32 VisibilityMap, FMeshElementCollector& Collector) const override
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_ArrowSceneProxy_DrawDynamicElements);

		FMatrix EffectiveLocalToWorld = GetLocalToWorld();
		const TArray<FLinearColor> Colors = { FLinearColor::Green,FLinearColor::Blue,FLinearColor::Red };
		for (int32 ViewIndex = 0; ViewIndex < Views.Num(); ViewIndex++)
		{
			if (VisibilityMap & (1 << ViewIndex))
			{
				const FSceneView* View = Views[ViewIndex];
				FPrimitiveDrawInterface* PDI = Collector.GetPDI(ViewIndex);

				const int32 LastIndex = LinePoints.Num() - 1;
				for (int32 i = 0; i < LastIndex; i++)
				{
					const FVector WorldStartPoint = EffectiveLocalToWorld.TransformPosition(LinePoints[i]);
					const FVector WorldEndPoint = EffectiveLocalToWorld.TransformPosition(LinePoints[i + 1]);
					PDI->DrawLine(WorldStartPoint, WorldEndPoint, LineColor, SDPG_World, Linesize, 0.0f, true);
				}
			}
		}
	}

	virtual FPrimitiveViewRelevance GetViewRelevance(const FSceneView* View) const override
	{
		FPrimitiveViewRelevance Result;
		Result.bDrawRelevance = IsShown(View);
		Result.bDynamicRelevance = true;
		Result.bSeparateTranslucency = Result.bNormalTranslucency = true;
		return Result;
	}

	virtual uint32 GetMemoryFootprint(void) const override { return(sizeof(*this) + GetAllocatedSize()); }
	uint32 GetAllocatedSize(void) const { return(FPrimitiveSceneProxy::GetAllocatedSize()); }

	//void SetExternalDynamicPixelToWorldScale(float* DynamicPixelToWorldScale)
	//{
	//	ExternalDynamicPixelToWorldScale = DynamicPixelToWorldScale;
	//}

private:

	FLinearColor LineColor;

	TArray<FVector> LinePoints;

	float Linesize;

	// set in ::GetDynamicMeshElements() for use by Component hit testing
	//float* ExternalDynamicPixelToWorldScale = nullptr;

};

ULineSegmentComponent::ULineSegmentComponent(const FObjectInitializer& ObjectInitializer)
	: UPrimitiveComponent(ObjectInitializer)
{
	// Structure to hold one-time initialization
	bAutoActivate = true;

	SetCollisionObjectType(ECollisionChannel::ECC_Visibility);

	SetGenerateOverlapEvents(true);

}

FPrimitiveSceneProxy* ULineSegmentComponent::CreateSceneProxy()
{
	FLineSegmentSceneProxy* NewProxy = new FLineSegmentSceneProxy(this);
	//NewProxy->SetExternalDynamicPixelToWorldScale(&DynamicPixelToWorldScale);
	return NewProxy;
}

FBoxSphereBounds ULineSegmentComponent::CalcBounds(const FTransform& LocalToWorld) const
{
	FBox BBox(ForceInit);
	for (const auto& Point : LinePoints)
	{
		BBox += LocalToWorld.TransformPosition(Point);
	}
	if (BBox.IsValid)
	{
		// Points are in world space, so no need to transform.
		return FBoxSphereBounds(BBox);
	}
	else
	{
		const FVector BoxExtent(1.f);
		return FBoxSphereBounds(LocalToWorld.GetLocation(), BoxExtent, 1.f);
	}
}

void ULineSegmentComponent::SetLineColor(FLinearColor NewColor)
{
	GeometryLock.Lock();
	LineColor = NewColor;
	GeometryLock.Unlock();
	MarkRenderStateDirty();
}

void ULineSegmentComponent::SetLinesize(float NewSize)
{
	GeometryLock.Lock();
	Linesize = NewSize;
	GeometryLock.Unlock();
	MarkRenderStateDirty();
}

void ULineSegmentComponent::SetLinePoints(TArray<FVector> NewPoints)
{
	GeometryLock.Lock();
	LinePoints = NewPoints;
	GeometryLock.Unlock();
	MarkRenderStateDirty();
}

bool ULineSegmentComponent::LineTraceComponent(FHitResult& OutHit, const FVector Start, const FVector End, const FCollisionQueryParams& Params)
{
	const FTransform& Transform = this->GetComponentToWorld();
	// transform points into local space
	const FVector StartLocal = Transform.InverseTransformPosition(Start);
	const FVector EndLocal = Transform.InverseTransformPosition(End);
	const int32 LastIndex = LinePoints.Num() - 1;
	for (int32 i = 0; i < LastIndex; ++i)
	{
		FVector NearestOnLine, NearestOnRay;
		FMath::SegmentDistToSegmentSafe(LinePoints[i], LinePoints[i + 1], StartLocal, EndLocal, NearestOnLine, NearestOnRay);
		double Distance = FVector::Distance(NearestOnLine, NearestOnRay);
		if (Distance <= 0.5f)
		{
			OutHit.Component = this;
			OutHit.Distance = FVector::Distance(Start, NearestOnLine);
			OutHit.ImpactPoint = NearestOnLine;
			OutHit.Time = OutHit.Distance / FVector::Dist(StartLocal, EndLocal);
			return true;
		}
	}
	return false;
}

