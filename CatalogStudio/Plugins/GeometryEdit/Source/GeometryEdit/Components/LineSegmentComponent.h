// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "Components/PrimitiveComponent.h"
#include "LineSegmentComponent.generated.h"

class FPrimitiveSceneProxy;

/**
 *
 */
UCLASS(ClassGroup = Utility, editinlinenew, meta = (BlueprintSpawnableComponent))
class GEOMETRYEDIT_API ULineSegmentComponent : public UPrimitiveComponent
{
	GENERATED_UCLASS_BODY()

public:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components|LineSegment")
		FLinearColor LineColor = FLinearColor::Red;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components|LineSegment")
		float Linesize = 4.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components|LineSegment")
		TArray<FVector> LinePoints;

private:

	// protects CurrentLines and CurrentPoints
	FCriticalSection GeometryLock;

public:

	UFUNCTION(BlueprintCallable, DisplayName = "SetLineColor", Category = "Components|LineSegment")
		virtual void SetLineColor(FLinearColor NewColor);

	UFUNCTION(BlueprintCallable, DisplayName = "SetLinesize", Category = "Components|LineSegment")
		virtual void SetLinesize(float NewSize);

	UFUNCTION(BlueprintCallable, DisplayName = "SetLinePoints", Category = "Components|LineSegment")
		virtual void SetLinePoints(TArray<FVector> NewPoints);

public:

	//~ Begin UPrimitiveComponent Interface.
	virtual FPrimitiveSceneProxy* CreateSceneProxy() override;
	virtual bool LineTraceComponent(FHitResult& OutHit, const FVector Start, const FVector End, const FCollisionQueryParams& Params) override;
	//~ End UPrimitiveComponent Interface.

	//~ Begin USceneComponent Interface.
	virtual FBoxSphereBounds CalcBounds(const FTransform& LocalToWorld) const override;
	//~ Begin USceneComponent Interface.
};
