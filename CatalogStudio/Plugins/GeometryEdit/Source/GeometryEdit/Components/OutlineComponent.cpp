// Fill out your copyright notice in the Description page of Project Settings.


#include "OutlineComponent.h"
#include "EngineGlobals.h"
#include "RHI.h"
#include "RenderingThread.h"
#include "RenderResource.h"
#include "VertexFactory.h"
#include "LocalVertexFactory.h"
#include "PrimitiveViewRelevance.h"
#include "PrimitiveSceneProxy.h"
#include "Engine/Engine.h"
#include "MaterialShared.h"
#include "Materials/Material.h"
#include "SceneManagement.h"
#include "UObject/UObjectHash.h"
#include "UObject/UObjectIterator.h"




/** Represents a UOutlineComponent to the scene manager. */
class FOutlineSceneProxy final : public FPrimitiveSceneProxy
{
public:
	SIZE_T GetTypeHash() const override
	{
		static size_t UniquePointer;
		return reinterpret_cast<size_t>(&UniquePointer);
	}

	FOutlineSceneProxy(UOutlineComponent* Component)
		: FPrimitiveSceneProxy(Component)
		, LineColor(Component->LineColor)
		, Outlines(Component->Outlines)
		, Outlinesize(Component->Outlinesize)
	{
		bWillEverBeLit = false;

	}

	virtual ~FOutlineSceneProxy()
	{
		Outlines.Empty();
	}

	virtual void GetDynamicMeshElements(const TArray<const FSceneView*>& Views, const FSceneViewFamily& ViewFamily, uint32 VisibilityMap, FMeshElementCollector& Collector) const override
	{
		QUICK_SCOPE_CYCLE_COUNTER(STAT_ArrowSceneProxy_DrawDynamicElements);

		FMatrix EffectiveLocalToWorld = GetLocalToWorld();

		for (int32 ViewIndex = 0; ViewIndex < Views.Num(); ViewIndex++)
		{
			if (VisibilityMap & (1 << ViewIndex))
			{
				const FSceneView* View = Views[ViewIndex];
				FPrimitiveDrawInterface* PDI = Collector.GetPDI(ViewIndex);
				for (int32 i = 0; i < Outlines.Num(); i++)
				{
					const FVector WorldStartPoint = EffectiveLocalToWorld.TransformPosition(Outlines[i].StartPoint);
					const FVector WorldEndPoint = EffectiveLocalToWorld.TransformPosition(Outlines[i].EndPoint);
					PDI->DrawLine(WorldStartPoint, WorldEndPoint, LineColor, SDPG_World, Outlinesize, 1.0f, true);
				}
			}
		}
	}

	virtual FPrimitiveViewRelevance GetViewRelevance(const FSceneView* View) const override
	{
		FPrimitiveViewRelevance Result;
		Result.bDrawRelevance = IsShown(View);
		Result.bDynamicRelevance = true;
		Result.bSeparateTranslucency = Result.bNormalTranslucency = true;
		return Result;
	}

	virtual uint32 GetMemoryFootprint(void) const override { return(sizeof(*this) + GetAllocatedSize()); }
	uint32 GetAllocatedSize(void) const { return(FPrimitiveSceneProxy::GetAllocatedSize()); }

private:

	FLinearColor LineColor;

	TArray<FOutlineLineSegments> Outlines;

	float Outlinesize;

};

UOutlineComponent::UOutlineComponent(const FObjectInitializer& ObjectInitializer)
	: UPrimitiveComponent(ObjectInitializer)
	, LineColor(FLinearColor::Black)
	, Outlinesize(2.f)
{
	// Structure to hold one-time initialization
	bAutoActivate = true;

	SetCollisionProfileName(UCollisionProfile::NoCollision_ProfileName);

	SetGenerateOverlapEvents(false);

}

FPrimitiveSceneProxy* UOutlineComponent::CreateSceneProxy()
{
	return new FOutlineSceneProxy(this);
}

FBoxSphereBounds UOutlineComponent::CalcBounds(const FTransform& LocalToWorld) const
{
	FBox BBox(ForceInit);
	for (const auto& Line : Outlines)
	{
		BBox += LocalToWorld.TransformPosition(Line.StartPoint);
		BBox += LocalToWorld.TransformPosition(Line.EndPoint);
	}
	if (BBox.IsValid)
	{
		// Points are in world space, so no need to transform.
		return FBoxSphereBounds(BBox);
	}
	else
	{
		const FVector BoxExtent(1.f);
		return FBoxSphereBounds(LocalToWorld.GetLocation(), BoxExtent, 1.f);
	}
}

void UOutlineComponent::SetLineColor(FLinearColor NewColor)
{
	LineColor = NewColor;
	MarkRenderStateDirty();
}

void UOutlineComponent::SetOutlines(TArray<FOutlineLineSegments> NewLines)
{
	Outlines = NewLines;
	MarkRenderStateDirty();
}

void UOutlineComponent::AddOutlines(FOutlineLineSegments NewLines)
{
	Outlines.Add(NewLines);
	MarkRenderStateDirty();
}

void UOutlineComponent::ClearOutlines()
{
	Outlines.Empty();
	MarkRenderStateDirty();
}

void UOutlineComponent::SetOutlineSize(float NewSize)
{
	Outlinesize = NewSize;
	MarkRenderStateDirty();
}
