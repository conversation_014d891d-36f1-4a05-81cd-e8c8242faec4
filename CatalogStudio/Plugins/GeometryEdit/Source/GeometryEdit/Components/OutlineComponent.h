// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "Components/PrimitiveComponent.h"
#include "OutlineComponent.generated.h"

class FPrimitiveSceneProxy;

USTRUCT(Blueprintable, BlueprintType)
struct GEOMETRYEDIT_API FOutlineLineSegments
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Outline)
		FVector StartPoint;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Outline)
		FVector EndPoint;

	FOutlineLineSegments()
		:StartPoint(ForceInit)
		, EndPoint(ForceInit)
	{}

	FOutlineLineSegments(const FVector& InStart, const FVector& InEnd)
		:StartPoint(InStart)
		, EndPoint(InEnd)
	{}
};

/**
 *
 */
UCLASS(ClassGroup = Utility, editinlinenew, meta = (BlueprintSpawnableComponent))
class GEOMETRYEDIT_API UOutlineComponent : public UPrimitiveComponent
{
	GENERATED_UCLASS_BODY()

public:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = OutlineComponent)
		FLinearColor LineColor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = OutlineComponent)
		TArray<FOutlineLineSegments> Outlines;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = OutlineComponent)
		float Outlinesize;

public:

	UFUNCTION(BlueprintCallable, DisplayName = "SetLineColor", Category = "Components|Outline")
		virtual void SetLineColor(FLinearColor NewColor);

	UFUNCTION(BlueprintCallable, DisplayName = "SetOutlines", Category = "Components|Outline")
		virtual void SetOutlines(TArray<FOutlineLineSegments> NewLines);

	UFUNCTION(BlueprintCallable, DisplayName = "AddOutlines", Category = "Components|Outline")
		virtual void AddOutlines(FOutlineLineSegments NewLines);

	UFUNCTION(BlueprintCallable, DisplayName = "AddOutlines", Category = "Components|Outline")
		virtual void ClearOutlines();

	UFUNCTION(BlueprintCallable, DisplayName = "SetOutlineSize", Category = "Components|Outline")
		virtual void SetOutlineSize(float NewSize);

	//~ Begin UPrimitiveComponent Interface.
	virtual FPrimitiveSceneProxy* CreateSceneProxy() override;
	virtual FBoxSphereBounds CalcBounds(const FTransform& LocalToWorld) const override;

};
