// Fill out your copyright notice in the Description page of Project Settings.


#include "VolatileMeshComponent.h"


UVolatileMeshComponent::UVolatileMeshComponent(const FObjectInitializer& ObjectInitializer)
	: UProceduralMeshComponent(ObjectInitializer)
{

}

UVolatileMeshComponent::~UVolatileMeshComponent()
{
	MaterialSectionMap.Empty();
}

void UVolatileMeshComponent::CreateMeshSection(int32 SectionIndex, const FPMCSection& InMeshSection, bool bCreateCollision)
{
	TArray<FLinearColor> EmptyColor;
	UProceduralMeshComponent::CreateMeshSection_LinearColor(SectionIndex, InMeshSection.Vertexes, InMeshSection.Triangles, InMeshSection.Normals, InMeshSection.UV, EmptyColor, InMeshSection.Tangents, bCreateCollision);
	if (MaterialSectionMap.Contains(SectionIndex))
		MaterialSectionMap[SectionIndex] = FMaterialNameInsPair(InMeshSection.SectionName, nullptr);
	else
		MaterialSectionMap.Add(SectionIndex, FMaterialNameInsPair(InMeshSection.SectionName, nullptr));
}

void UVolatileMeshComponent::UpdateMeshSection(int32 SectionIndex, const FPMCSection& InMeshSection)
{
	TArray<FLinearColor> EmptyColor;
	UProceduralMeshComponent::UpdateMeshSection_LinearColor(SectionIndex, InMeshSection.Vertexes, InMeshSection.Normals, InMeshSection.UV, EmptyColor, InMeshSection.Tangents);
	if (MaterialSectionMap.Contains(SectionIndex))
		MaterialSectionMap[SectionIndex] = FMaterialNameInsPair(InMeshSection.SectionName, nullptr);
	else
		MaterialSectionMap.Add(SectionIndex, FMaterialNameInsPair(InMeshSection.SectionName, nullptr));
}

void UVolatileMeshComponent::ClearMeshSection(int32 SectionIndex)
{
	UProceduralMeshComponent::ClearMeshSection(SectionIndex);
	if (MaterialSectionMap.Contains(SectionIndex))
		MaterialSectionMap.Remove(SectionIndex);
}

void UVolatileMeshComponent::ClearAllMeshSections()
{
	UProceduralMeshComponent::ClearAllMeshSections();
	MaterialSectionMap.Empty();
}

void UVolatileMeshComponent::GetMaterialIndexs(FName MaterialName, TArray<int32>& SectionIndexs) const
{
	for (auto& Iter : MaterialSectionMap)
	{
		if (Iter.Value.MaterialName.IsEqual(MaterialName))SectionIndexs.Add(Iter.Key);
	}
}

bool UVolatileMeshComponent::SetMaterialWithName(int32 SectionIndex, FName MaterialName, class UMaterialInterface* Material)
{
	if (MaterialSectionMap.Contains(SectionIndex))
	{
		UProceduralMeshComponent::SetMaterial(SectionIndex, Material);
		MaterialSectionMap[SectionIndex] = FMaterialNameInsPair(MaterialName, Material);
		return true;
	}
	return false;
}

void UVolatileMeshComponent::UpdateMaterialByName(FName MaterialName, class UMaterialInterface* Material, bool OnlyData/* = false*/)
{
	TArray<int32> SectionIndexs;
	for (auto& Iter : MaterialSectionMap)
	{
		if (Iter.Value.MaterialName.IsEqual(MaterialName)) SectionIndexs.Add(Iter.Key);
	}
	if (OnlyData)
	{
		for (auto& SectionIndex : SectionIndexs)
		{
			MaterialSectionMap[SectionIndex].MaterialIns = Material;
		}
	}
	else
	{
		for (auto& SectionIndex : SectionIndexs)
		{
			UProceduralMeshComponent::SetMaterial(SectionIndex, Material);
			MaterialSectionMap[SectionIndex].MaterialIns = Material;
		}
	}
}

void UVolatileMeshComponent::UpdateMaterialForTemporary(class UMaterialInterface* Material)
{
	if (nullptr == Material)
	{
		for (auto& Iter : MaterialSectionMap)
		{
			UProceduralMeshComponent::SetMaterial(Iter.Key, Iter.Value.MaterialIns);
		}
	}
	else
	{
		const int32 SectionCount = UProceduralMeshComponent::GetNumSections();
		for (int32 SectionIndex = 0; SectionIndex < SectionCount; ++SectionIndex)
		{
			UProceduralMeshComponent::SetMaterial(SectionIndex, Material);
		}
	}
}

void UVolatileMeshComponent::GetUsedMaterialNames(TArray<FName>& MaterialNames) const
{
	TArray<FMaterialNameInsPair> Pairs;
	MaterialSectionMap.GenerateValueArray(Pairs);
	if (Pairs.Num() > 0)
	{
		MaterialNames.Init(NAME_None, Pairs.Num());
		for (int32 i = 0; i < Pairs.Num(); ++i)
			MaterialNames[i] = Pairs[i].MaterialName;
	}
}

FPrimitiveSceneProxy* UVolatileMeshComponent::CreateSceneProxy()
{
	return UProceduralMeshComponent::CreateSceneProxy();
}
