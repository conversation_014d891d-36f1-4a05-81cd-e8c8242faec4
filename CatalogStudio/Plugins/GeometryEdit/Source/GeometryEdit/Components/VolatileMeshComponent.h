// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ProceduralMeshComponent/Public/ProceduralMeshComponent.h"
#include "MagicCore/Public/PMCSection.h"
#include "VolatileMeshComponent.generated.h"

USTRUCT()
struct FMaterialNameInsPair
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY()
		FName				MaterialName = NAME_None;

	UPROPERTY()
		UMaterialInterface* MaterialIns = nullptr;

	FMaterialNameInsPair() = default;

	FMaterialNameInsPair(FName InName, UMaterialInterface* InIns)
		:MaterialName(InName)
		, MaterialIns(InIns)
	{

	}
};

/**
 *
 */
UCLASS(ClassGroup = Utility, editinlinenew, meta = (BlueprintSpawnableComponent))
class GEOMETRYEDIT_API UVolatileMeshComponent : public UProceduralMeshComponent
{
	GENERATED_BODY()

public:

	UVolatileMeshComponent(const FObjectInitializer& ObjectInitializer);

	virtual ~UVolatileMeshComponent();

	void CreateMeshSection(int32 SectionIndex, const FPMCSection& InMeshSection, bool bCreateCollision);

	void UpdateMeshSection(int32 SectionIndex, const FPMCSection& InMeshSection);

	void ClearMeshSection(int32 SectionIndex);

	void ClearAllMeshSections();

	//获取所有使用MaterialSlotName的SectionIndex
	void GetMaterialIndexs(FName MaterialName, TArray<int32>& SectionIndexs) const;

	//设Section的材质，SectionIndex为索引，MaterialSlotName为使用的材质名称，Material为使用的材质
	bool SetMaterialWithName(int32 SectionIndex, FName MaterialName, class UMaterialInterface* Material);

	//更新所有材质名称等于MaterialName的section
	void UpdateMaterialByName(FName MaterialName, class UMaterialInterface* Material, bool OnlyData = false);

	//暂时改变所有section的材质，但并不改变材质的名称，用于显示模式的切换
	void UpdateMaterialForTemporary(class UMaterialInterface* Material);

	//获取所有使用的材质名称
	void GetUsedMaterialNames(TArray<FName>& MaterialNames) const;

	//~ Begin UPrimitiveComponent Interface.
	virtual FPrimitiveSceneProxy* CreateSceneProxy() override;

private:

	UPROPERTY()
		TMap<int32, FMaterialNameInsPair> MaterialSectionMap;//记录材质ID与模型索引的关系

};
