// Fill out your copyright notice in the Description page of Project Settings.


#include "LoftRoutine.h"
#include "Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"

FLoftRoutine::FLoftRoutine()
	:RoutinePoints(TArray<FVector>())
	, PlaneNormal(FVector::UpVector)
	, PlaneForward(FVector::ForwardVector)
	, bLoop(false)
	, bValidData(false)
{
}

FLoftRoutine::FLoftRoutine(const TArray<FVector>& InRoutine, const FVector& InNormal, const FVector& InForward, bool InLoop)
{
	RoutinePoints = InRoutine;
	PlaneNormal = InNormal.GetSafeNormal();
	PlaneForward = InForward;
	bLoop = InLoop;
	while (RoutinePoints.Num() >= 3)
	{
		bool bFoundParallel = false;
		const int32 LastIndex = InLoop ? 0 : 1;
		int32 Index = InLoop ? RoutinePoints.Num() - 1 : RoutinePoints.Num() - 2;
		for (; Index >= LastIndex; --Index)
		{
			const int32 NextIndex = (Index + 1) % RoutinePoints.Num();
			const FVector NextDir = (RoutinePoints[NextIndex] - RoutinePoints[Index]).GetSafeNormal();
			const int32 PreIndex = (Index - 1 + RoutinePoints.Num()) % RoutinePoints.Num();
			const FVector PreDir = (RoutinePoints[PreIndex] - RoutinePoints[Index]).GetSafeNormal();
			if (NextDir.IsNearlyZero(THRESH_NORMALS_ARE_SAME) || PreDir.IsNearlyZero(THRESH_NORMALS_ARE_SAME) || FVector::Parallel(PreDir, NextDir, THRESH_NORMALS_ARE_PARALLEL))
			{
				RoutinePoints.RemoveAt(Index);
				bFoundParallel = true;
			}
		}
		if (!bFoundParallel) break;
	}
	if (RoutinePoints.Num() < 1 || (bLoop && (RoutinePoints.Num() < 3)))
	{
		ResetLoftRoutine();
		return;
	}
	if (RoutinePoints.Num() >= 3)
	{
		bool bIsCCW = FPolygon3DLibrary::IsPolygonCCWWinding(RoutinePoints, PlaneNormal);
		if (bIsCCW) FArrayOperatorLibrary::ReverseArray<FVector>(RoutinePoints);
	}
	bValidData = true;
}

FLoftRoutine::~FLoftRoutine()
{
	ResetLoftRoutine();
}

void FLoftRoutine::ResetLoftRoutine()
{
	RoutinePoints.Empty();
	PlaneNormal = FVector::UpVector;
	bLoop = false;
	bValidData = false;
}

bool FLoftRoutine::CalculateRoutineOutsideNormal(TArray<FVector>& OutsideNormals) const
{
	if (!bValidData) return false;
	if (1 == RoutinePoints.Num())
	{
		//const FVector OutsideNormal = (PlaneNormal ^ PlaneForward).GetSafeNormal();
		OutsideNormals.Add(PlaneForward);
		return true;
	}
	const int32 Count = bLoop ? RoutinePoints.Num() : (RoutinePoints.Num() - 1);
	TArray<FVector> LocalNormal = TArray<FVector>();
	LocalNormal.Init(FVector::ZeroVector, RoutinePoints.Num());
	for (int32 i = 0; i < Count; ++i)
	{
		const int32 NextIndex = (i + 1) % RoutinePoints.Num();
		const FVector Dir = (RoutinePoints[NextIndex] - RoutinePoints[i]).GetSafeNormal();
		const FVector Normal = (PlaneNormal ^ Dir).GetSafeNormal();
		LocalNormal[i] = Normal.IsNearlyZero(THRESH_POINTS_ARE_SAME) ? PlaneForward : Normal;
	}
	if (!bLoop) LocalNormal[Count] = LocalNormal[Count - 1];
	OutsideNormals = LocalNormal;
	for (int32 i = (bLoop ? 0 : 1); i < Count; ++i)
	{
		const int32 PreIndex = (i - 1 + LocalNormal.Num()) % LocalNormal.Num();
		OutsideNormals[i] = LocalNormal[i] + LocalNormal[PreIndex];
	}
	return true;
}

bool FLoftRoutine::RoutinePointUpDir(TArray<FVector>& UpDir) const
{
	if (0 == RoutinePoints.Num()) return false;
	UpDir.Init(FVector::ZeroVector, RoutinePoints.Num());
	if (1 == RoutinePoints.Num())
	{
		UpDir[0] = (PlaneNormal ^ PlaneForward).GetSafeNormal();
		return true;
	}
	if (bLoop)
	{
		for (int32 i = 0; i < RoutinePoints.Num(); ++i)
		{
			const int32 Next = (i + 1) % RoutinePoints.Num();
			UpDir[i] = (PlaneNormal ^ (RoutinePoints[Next] - RoutinePoints[i])).GetSafeNormal();
		}
		for (int32 i = 0; i < RoutinePoints.Num(); ++i)
		{
			const int32 Pre = (RoutinePoints.Num() + i - 1) % RoutinePoints.Num();
			const int32 Next = (i + 1) % RoutinePoints.Num();
			const FVector NextDir = (RoutinePoints[Next] - RoutinePoints[i]).GetSafeNormal();
			const FVector PreDir = (RoutinePoints[Pre] - RoutinePoints[i]).GetSafeNormal();
			FVector FinalUpDir = NextDir + PreDir;
			FinalUpDir = ((UpDir[i] | FinalUpDir) * FinalUpDir).GetSafeNormal();
			FinalUpDir *= (1 / (FinalUpDir ^ NextDir).Size());
			UpDir[i] = FinalUpDir;
		}
	}
	else
	{
		const int32 Count = RoutinePoints.Num() - 1;
		for (int32 i = 0; i < Count; ++i)
		{
			const int32 Next = i + 1;
			UpDir[i] = (PlaneNormal ^ (RoutinePoints[Next] - RoutinePoints[i])).GetSafeNormal();
		}
		UpDir[Count] = UpDir[Count - 1];
		for (int32 i = 1; i < Count; ++i)
		{
			const int32 Pre = i - 1;
			const int32 Next = i + 1;
			const FVector NextDir = (RoutinePoints[Next] - RoutinePoints[i]).GetSafeNormal();
			const FVector PreDir = (RoutinePoints[Pre] - RoutinePoints[i]).GetSafeNormal();
			FVector FinalUpDir = NextDir + PreDir;
			FinalUpDir = ((UpDir[i] | FinalUpDir) * FinalUpDir).GetSafeNormal();
			FinalUpDir *= (1 / (FinalUpDir ^ NextDir).Size());
			UpDir[i] = FinalUpDir;
		}
	}
	return true;
}

void FLoftRoutine::TransformRoutinePoints(const FVector& InZeroPoint)
{
	for (int32 i = 0; i < RoutinePoints.Num(); ++i)
		RoutinePoints[i] -= InZeroPoint;
}
