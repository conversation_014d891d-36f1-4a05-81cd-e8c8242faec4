// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

/**
 *
 */
struct GEOMETRYEDIT_API FLoftRoutine
{
private:
	TArray<FVector>	RoutinePoints;

	FVector			PlaneNormal;

	FVector			PlaneForward;

	bool			bLoop;

	bool			bValidData;

public:
	FLoftRoutine();

	FLoftRoutine(const TArray<FVector>& InRoutine, const FVector& InNormal, const FVector& InForward, bool InLoop);

	virtual ~FLoftRoutine();

	void ResetLoftRoutine();

	bool IsValidRoutine() const { return bValidData; }

	const TArray<FVector>& GetRoutinePoints() const { return RoutinePoints; }

	const FVector& GetPlaneNormal() const { return PlaneNormal; }

	const FVector& GetPlaneForward() const { return PlaneForward; }

	bool IsLoop() const { return bLoop; }

	bool CalculateRoutineOutsideNormal(TArray<FVector>& OutsideNormals) const;

	bool RoutinePointUpDir(TArray<FVector>& UpDir) const;

	void TransformRoutinePoints(const FVector& InZeroPoint);
};
