// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LoftRoutine.h"

/**
 *
 */
struct GEOMETRYEDIT_API FShearePolygon : FLoftRoutine
{
private:

	FVector				TangentX;//UV正向

	TArray<FVector2D>	UV;

public:

	FShearePolygon();

	FShearePolygon(const TArray<FVector>& InRoutine, const FVector& InNormal, bool InLoop, const FVector& InTangent, const FVector& InOriginPoint, const float& UVRateInCM);

	virtual ~FShearePolygon();

	void ResetShearePolygon();

	const FVector& GetTangentX() const { return TangentX; }

	const TArray<FVector2D>& GetPointsUV() const { return UV; }
};
