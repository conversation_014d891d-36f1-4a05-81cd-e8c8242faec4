// Fill out your copyright notice in the Description page of Project Settings.


#include "VolatileLoftSection.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "Polygon3DLibrary.h"



FVolatileLoftSection::FVolatileLoftSection(const TArray<FPolygonPoint>& InSection, const FVector& InTangentY, const FVector& InTangentX, const FVector& InNormal, const float& InUVRateInCM)
	:UVRateInCM(InUVRateInCM)
	, TangentY(InTangentY)
	, TangentX(InTangentX)
	, Normal(InNormal)
{
	if (InSection.Num() < 1 || (InSection.Num() < 3))
	{
		ResetLoftSection();
		return;
	}
	SectionPoints = InSection;
	const bool bLoop = SectionPoints.Num() > 2;
	const auto FirstPoint = SectionPoints[0];
	if (bLoop) SectionPoints.Add(FirstPoint);
	UV_Y.Init(0.0f, SectionPoints.Num());
	float Sum = 0.0f;
	for (int32 i = 1; i < SectionPoints.Num(); ++i)
	{
		Sum += FVector::Distance(SectionPoints[i].Point, SectionPoints[i - 1].Point);
		UV_Y[i] = Sum / UVRateInCM;
	}
	UV.Init(FVector2D::ZeroVector, SectionPoints.Num());
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
	{
		UV[i].X = (SectionPoints[i].Point | InTangentX) / UVRateInCM;
		UV[i].Y = (SectionPoints[i].Point | InTangentY) / UVRateInCM;
	}
	bValidData = true;
}

FVolatileLoftSection::~FVolatileLoftSection()
{
	ResetLoftSection();
}

void FVolatileLoftSection::ResetLoftSection()
{
	SectionPoints.Empty();
	UV_Y.Empty();
	bValidData = false;
	UVRateInCM = 100.0f;
	TangentY = FVector::UpVector;
	TangentX = FVector::ForwardVector;
	Normal = FVector::RightVector;
}

bool FVolatileLoftSection::TransformSection(const FVector& InForward, const FVector& InUp, const float& InScalSize, FVolatileLoftSection& OutSection) const
{
	if (!bValidData)
	{
		OutSection.ResetLoftSection();
		return false;
	}
	OutSection = *this;
	OutSection.ScaleSectionAlongForwardDir(InScalSize);
	TArray<FVector> Points;
	FPolygonPoint::ConvertToVectorArray(SectionPoints, Points);
	bool bCCWBefore = FPolygon3DLibrary::IsPolygonCCWWinding(Points, (TangentX ^ TangentY).GetSafeNormal());
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
		OutSection.SectionPoints[i] = (OutSection.SectionPoints[i].Point | TangentX) * InForward + (OutSection.SectionPoints[i].Point | TangentY) * InUp;
	FPolygonPoint::ConvertToVectorArray(OutSection.SectionPoints, Points);
	bool bCCWAfter = FPolygon3DLibrary::IsPolygonCCWWinding(Points, (InForward ^ InUp).GetSafeNormal());
	if (bCCWBefore != bCCWAfter) FArrayOperatorLibrary::ReverseArray<FVector2D>(OutSection.UV);
	return true;
}

void FVolatileLoftSection::TranslateSection(const FVector& Offset)
{
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
		SectionPoints[i].Point += Offset;
}

void FVolatileLoftSection::ScaleSectionAlongForwardDir(const float& InScalSize)
{
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
	{
		const float Width = SectionPoints[i].Point | TangentX;
		const float Height = SectionPoints[i].Point | TangentY;
		SectionPoints[i].Point = InScalSize * Width * TangentX + Height * TangentY;
	}
}

void FVolatileLoftSection::Reverse()
{
	FArrayOperatorLibrary::ReverseArray<FPolygonPoint>(SectionPoints);
	FArrayOperatorLibrary::ReverseArray<float>(UV_Y);
	FArrayOperatorLibrary::ReverseArray<FVector2D>(UV);
}

void FVolatileLoftSection::CalculateUV()
{
	UV.Empty();
	UV.Init(FVector2D::ZeroVector, SectionPoints.Num());
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
	{
		UV[i].X = (SectionPoints[i].Point | TangentX) / UVRateInCM;
		UV[i].Y = (SectionPoints[i].Point | TangentY) / UVRateInCM;
	}
}


FCatalogLoftSection::FCatalogLoftSection(const TArray<FPolygonPoint>& InSection, const FVector& InNormal, const FVector& InTangentX, const float& InUVRateInCM)
	:SectionPoints(InSection)
	, TangentX(InTangentX)
	, Normal(InNormal)
	, UVRateInCM(InUVRateInCM)
{

}

FCatalogLoftSection::~FCatalogLoftSection()
{

}

void FCatalogLoftSection::GetEdgeOutsideNormal(TArray<FVector>& OutsideNormal) const
{
	TArray<FVector> Points;
	FPolygonPoint::ConvertToVectorArray(SectionPoints, Points);
	OutsideNormal.Init(FVector::ZeroVector, SectionPoints.Num());
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
	{
		const int32 Next = (i + 1) % Points.Num();
		OutsideNormal[i] = (Normal ^ (Points[Next] - Points[i])).GetSafeNormal();
	}
	bool bCCW = FPolygon3DLibrary::IsPolygonCCWWinding(Points, Normal);
	for (int32 i = 0; bCCW && (i < SectionPoints.Num()); ++i)
	{
		OutsideNormal[i] *= -1.0f;
	}
}

bool FCatalogLoftSection::TransformSection(const FVector& InTargetLocation, const FVector& InForward, const FVector& InUp, const float& InScalSize, FCatalogLoftSection& OutSection) const
{
	const int32 Count = SectionPoints.Num();
	if (Count < 1) return false;
	OutSection.SectionPoints.Init(FVector::ZeroVector, Count);
	OutSection.Normal = (InUp ^ InForward).GetSafeNormal();
	OutSection.TangentX = InForward;
	const FVector TangentY = (Normal ^ TangentX).GetSafeNormal();
	for (int32 i = 0; i < Count; ++i)
	{
		OutSection.SectionPoints[i].Point = ((SectionPoints[i].Point | TangentX) * InForward + (SectionPoints[i].Point | TangentY) * InUp * InScalSize);
		OutSection.SectionPoints[i].Point += InTargetLocation;
	}
	return true;
}

void FCatalogLoftSection::operator+=(const FVector& Offset)
{
	for (int32 i = 0; i < SectionPoints.Num(); ++i)
		SectionPoints[i].Point += Offset;
}

void FCatalogLoftSection::GenerateOutline(TArray<TPair<FVector, FVector>>& Outlines) const
{
	const int32 Count = SectionPoints.Num();
	int32 Offset = Outlines.AddDefaulted(Count);
	for (int32 i = 0; i < Count; ++i, ++Offset)
	{
		const int32 Next = (i + 1) % Count;
		Outlines[Offset].Key = SectionPoints[i].Point;
		Outlines[Offset].Value = SectionPoints[Next].Point;
	}
}

void FCatalogLoftSection::GenerateMesh(FPMCSection& Mesh) const
{
	TArray<FVector> Points;
	FPolygonPoint::ConvertToVectorArray(SectionPoints, Points);
	FPolygon3DLibrary::GenerateMeshFromPolygon2D(Points, Normal, TangentX, Mesh);
}

void FCatalogLoftSection::CalculateUV(TArray<FVector2D>& OutUV) const
{
	const int32 Count = SectionPoints.Num();
	//OutUV.Init(FVector2D::ZeroVector, Count);
	//for (int32 i = 0; i < Count; ++i)
	//{
	//	const int32 Next = (i + 1) % Count;
	//	Outlines[Offset].Key = SectionPoints[i].Point;
	//	Outlines[Offset].Value = SectionPoints[Next].Point;
	//}
}
