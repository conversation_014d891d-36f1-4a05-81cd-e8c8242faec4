// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryEdit/Data/PolygonPoint.h"
#include "MagicCore/Public/PMCSection.h"

/**
 *
 */
struct GEOMETRYEDIT_API FVolatileLoftSection
{
protected:

	TArray<FPolygonPoint>	SectionPoints;

	TArray<float>	UV_Y;

	TArray<FVector2D> UV;

	bool			bValidData;

	float			UVRateInCM;

	FVector TangentY;

	FVector TangentX;

	FVector Normal;

public:

	FVolatileLoftSection()
		:SectionPoints(TArray<FVector>())
		, UV_Y(TArray<float>())
		, UV(TArray<FVector2D>())
		, bValidData(false)
		, UVRateInCM(100.0f)
		, TangentY(FVector::UpVector)
		, TangentX(FVector::ForwardVector)
		, Normal(FVector::RightVector)
	{

	}

	FVolatileLoftSection(const TArray<FPolygonPoint>& InSection, const FVector& InTangentY, const FVector& InTangentX, const FVector& InNormal, const float& InUVRateInCM);

	virtual ~FVolatileLoftSection();

	const TArray<FPolygonPoint>& GetSectionPoints() const { return SectionPoints; }

	const TArray<float>& GetSectionPointsUV_Y() const { return UV_Y; }

	const TArray<FVector2D>& GetSectionPointsUV() const { return UV; }

	bool IsValidData() const { return bValidData; }

	const FVector& GetTangentY() const { return TangentY; }

	const FVector& GetTangentX() const { return TangentX; }

	const FVector& GetNormal() const { return Normal; }

	const float& GetUVRateInCM() const { return UVRateInCM; }

	void ResetLoftSection();

	bool TransformSection(const FVector& InForward, const FVector& InUp, const float& InScalSize, FVolatileLoftSection& OutSection) const;

	void TranslateSection(const FVector& Offset);

	void ScaleSectionAlongForwardDir(const float& InScalSize);

	void Reverse();

private:

	void CalculateUV();
};

struct GEOMETRYEDIT_API FCatalogLoftSection
{
protected:

	TArray<FPolygonPoint>	SectionPoints;

	FVector TangentX;

	FVector Normal;

	float			UVRateInCM;

public:

	FCatalogLoftSection()
		:SectionPoints(TArray<FPolygonPoint>())
		, TangentX(FVector::ForwardVector)
		, Normal(FVector::RightVector)
		, UVRateInCM(100.0f)
	{

	}

	FCatalogLoftSection(const TArray<FPolygonPoint>& InSection, const FVector& InNormal, const FVector& InTangentX, const float& InUVRateInCM);

	virtual ~FCatalogLoftSection();

	const TArray<FPolygonPoint>& GetSectionPoints() const { return SectionPoints; }

	const FVector& GetTangentX() const { return TangentX; }

	const FVector& GetNormal() const { return Normal; }

	const FVector& SetNormal(const FVector& NewNormal) { Normal = NewNormal; return Normal; }

	const float& GetUVRateInCM() const { return UVRateInCM; }

	void GetEdgeOutsideNormal(TArray<FVector>& OutsideNormal) const;

	bool TransformSection(const FVector& InTargetLocation, const FVector& InForward, const FVector& InUp, const float& InScalSize, FCatalogLoftSection& OutSection) const;

	void operator+=(const FVector& Offset);

	void GenerateOutline(TArray<TPair<FVector, FVector>>& Outlines) const;

	void GenerateMesh(FPMCSection& Mesh) const;

	void CalculateUV(TArray<FVector2D>& OutUV) const;
};