#include "MeshTriangulator.h"
#include "Components/LineBatchComponent.h"
using namespace TriangleSpace;

FVector TriangleSpace::FLine::getStartPoint() const
{
	return startPoint;
}

FVector TriangleSpace::FLine::getEndPoint() const
{
	return endPoint;
}

EPointToLine TriangleSpace::FLine::pointToPosition(const FVector & p) const
{
	float f = (startPoint.X - p.X)*(endPoint.Y - p.Y)
		- (startPoint.Y - p.Y)*(endPoint.X - p.X);
	if (f < 0)	
		return EPointToLine::ELeft;	
	else if (f > 0)	
		return EPointToLine::ERight;	
	return EPointToLine::EOnline;
}

bool TriangleSpace::FLine::isTheSame(const FLine& oth)
{
	return startPoint == oth.getStartPoint() && endPoint == oth.getEndPoint()
		|| endPoint == oth.getStartPoint() && startPoint == oth.getEndPoint();
}

bool TriangleSpace::FLine::operator==(const FLine& oth) const
{
	return startPoint == oth.getStartPoint() && endPoint == oth.getEndPoint()
		|| endPoint == oth.getStartPoint() && startPoint == oth.getEndPoint();
}

TriangleSpace::FTriangle::FTriangle(const FVector & p0, const FVector & p1, const FVector & p2)
{
	points.Add(p0);
	points.Add(p1);
	points.Add(p2);
}

EPointToTriangle TriangleSpace::FTriangle::pointToPosition(const FVector & p)
{
	TArray<FLine> lines = getLines();
	EPointToTriangle pos = EPointToTriangle::EInside;
	for (auto & l : lines)
	{
		EPointToLine temp = l.pointToPosition(p);
		if (EPointToLine::EOnline == temp)
			pos = EPointToTriangle::EOnBorder;
		else if (EPointToLine::ERight == temp)
		{
			pos = EPointToTriangle::EOutside;
			break;
		}
	}
	return pos;
}

TArray<FLine> TriangleSpace::FTriangle::getLines() const
{
	TArray<FLine>lines;
	lines.SetNumZeroed(3);
	lines[0] = FLine(points[0], points[1]);
	lines[1] = FLine(points[1], points[2]);
	lines[2] = FLine(points[2], points[0]);
	return lines;
}

TArray<FVector> TriangleSpace::FTriangle::getPoints() const
{
	return points;
}

FVector FTriangle::getCenter() const
{
	FVector cp = (points[0] + points[1] + points[2]) / 3;
	//cp.Z = points[0].Z;
	return cp;
}

void TriangleSpace::FTriangle::differentLines(const FTriangle& oth, TArray<FLine>& outLines) const
{
	outLines.Empty();
	TArray<FLine> tLine = getLines();
	TArray<FLine> oLine = oth.getLines();

	outLines.Append(tLine);
	for (auto& l : oLine)
	{
		if (tLine.Contains(l))
			outLines.Remove(l);		
		else
			outLines.Add(l);
	}
}

void TriangleSpace::FTriangle::insectionLines(const FTriangle& oth, TArray<FLine>& outLines) const
{
	outLines.Empty();
	TArray<FLine> tLine = getLines();
	TArray<FLine> oLine = oth.getLines();

	//outLines.Append(tLine);
	for (auto& l : oLine)
	{
		for (auto& r : tLine)
		{
			if (l.isTheSame(r))
			{
				outLines.AddUnique(r);
			}
		}
	
	}
}

bool TriangleSpace::FTriangle::containsLine(const FLine& inLine)
{
	for (auto & l : getLines())
	{
		if (l.isTheSame(inLine))
			return true;
	}
	return false;
}

bool FTriangle::containsPoint(const FVector& inPoint)
{
	return inPoint == points[0]
		|| inPoint == points[1]
		|| inPoint == points[2];
}

bool TriangleSpace::FTriangle::triangleCircle(const FVector2D& p1, const FVector2D& p2, const FVector2D& p3, FVector2D& center, float& radius)
{
	float  x1, x2, x3, y1, y2, y3;

	x1 = p1.X;
	x2 = p2.X;
	x3 = p3.X;
	y1 = p1.Y;
	y2 = p2.Y;
	y3 = p3.Y;

	float a = sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
	float b = sqrt((x1 - x3) * (x1 - x3) + (y1 - y3) * (y1 - y3));
	float c = sqrt((x2 - x3) * (x2 - x3) + (y2 - y3) * (y2 - y3));
	float p = (a + b + c) / 2;
	float S = sqrt(p * (p - a) * (p - b) * (p - c));
	radius = a * b * c / (4 * S);

	float t1 = x1 * x1 + y1 * y1;
	float t2 = x2 * x2 + y2 * y2;
	float t3 = x3 * x3 + y3 * y3;
	float temp = x1 * y2 + x2 * y3 + x3 * y1 - x1 * y3 - x2 * y1 - x3 * y2;
	float x = (t2 * y3 + t1 * y2 + t3 * y1 - t2 * y1 - t3 * y2 - t1 * y3) / temp / 2;
	float y = (t3 * x2 + t2 * x1 + t1 * x3 - t1 * x2 - t2 * x3 - t3 * x1) / temp / 2;

	center.X = x;
	center.Y = y;

	return true;
}

bool TriangleSpace::FTriangle::isPointInTriangleCircle(const FVector2D& p)
{
	FVector2D c;
	float r;
	triangleCircle(FVector2D(points[0]), FVector2D(points[1]), FVector2D(points[2]), c, r);
	return sqrt((p.X - c.X) * (p.X - c.X) + (p.Y - c.Y) * (p.Y - c.Y)) < r;
}

bool FTriangle::isLineCross(const FLine& inLine)
{
	TArray<FLine> lines;
	lines.SetNumZeroed(3);
	lines[0] = FLine(points[0], points[1]);
	lines[1] = FLine(points[1], points[2]);
	lines[2] = FLine(points[2], points[0]);

	for (const auto & l : lines)
	{
		if ((l.pointToPosition(inLine.getStartPoint()) == EPointToLine::ELeft && l.pointToPosition(inLine.getEndPoint()) == EPointToLine::ERight
			|| l.pointToPosition(inLine.getStartPoint()) == EPointToLine::ERight && l.pointToPosition(inLine.getEndPoint()) == EPointToLine::ELeft)
			&& (inLine.pointToPosition(l.getStartPoint()) == EPointToLine::ELeft && inLine.pointToPosition(l.getEndPoint()) == EPointToLine::ERight
			|| inLine.pointToPosition(l.getStartPoint()) == EPointToLine::ERight && inLine.pointToPosition(l.getEndPoint()) == EPointToLine::ELeft))
		{
			return true;
		}	
	}
	return false;
}

bool TriangleSpace::FTriangle::operator==(const FTriangle& oth)
{
	TArray<FVector> sp = points;
	for (auto & p : oth.getPoints())
	{
		sp.AddUnique(p);
	}
	return sp.Num() == 3;
}


bool FMeshTriangulator::trangleEdges(TArray<TriangleSpace::FNode> nodes, TArray<TriangleSpace::FEdge>& outEdges, const FVector& inNormal)
{
	if (nodes.Num() < 3)
		return false;
	TArray<TriangleSpace::FNode> outlineNodes;
	TMap<int32,TArray<TriangleSpace::FNode>> insertNodes;
	int32 index = -1;


	for (auto& iter : nodes)
	{
		if (iter.getOutline())
		{
			outlineNodes.Add(iter);
			index = iter.getIndex();
		}
		
		int32 index2 = iter.getBorderIndex();
		if (iter.getBorderIndex() != -1)
		{
			if (!insertNodes.Contains(index2))
			{
				TArray<TriangleSpace::FNode> tempnodes;
				tempnodes.Add(iter);
				insertNodes.Add(index2, tempnodes);
			}
			else
			{
				insertNodes[index2].Add(iter);
			}
		}
	}
	TArray<TriangleSpace::FEdge> outlineEdges;
	TArray<TriangleSpace::FEdge> allEdges;

	getEdges(outlineNodes, outlineEdges);
	for (auto & iter : insertNodes)
	{
		TArray<TriangleSpace::FEdge> edges;
		getEdges(iter.Value, edges);
		outlineEdges.Append(edges);

	}
	outEdges.Append(outlineEdges);

	TArray<TriangleSpace::FEdge> newEdges;
	for (int32 i = 0; i < outEdges.Num(); ++i)
	{
		auto currentEdge = outEdges[i];
		FNode TNode;

		if (!outEdges[i].getMaxAngleLeftNode(nodes, TNode, inNormal)
			||  outEdges[i].getStartNode().getBorderIndex() != -1 && outEdges[i].getStartNode().getBorderIndex() == TNode.getBorderIndex() && outEdges[i].getEndNode().getBorderIndex() == TNode.getBorderIndex())
			continue;

		auto edge0 = TriangleSpace::FEdge(outEdges[i].getStartNode(),TNode);
		auto edge1 = TriangleSpace::FEdge(TNode,outEdges[i].getEndNode());
		{
			addEdge(outEdges, edge0, edge1,inNormal);
		}

	}
	return true;	
}

bool FMeshTriangulator::trangleEdges3D(TArray<TriangleSpace::FNode> nodes, TArray<TriangleSpace::FEdge>& outEdges, const FVector& inNormal)
{
	if (nodes.Num() < 3)
		return false;
	TArray<TriangleSpace::FNode> outlineNodes;
	TMap<int32, TArray<TriangleSpace::FNode>> insertNodes;
	int32 index = -1;


	for (auto& iter : nodes)
	{
		if (iter.getOutline())
		{
			outlineNodes.Add(iter);
			index = iter.getIndex();
		}

		int32 index2 = iter.getBorderIndex();
		if (iter.getBorderIndex() != -1)
		{
			if (!insertNodes.Contains(index2))
			{
				TArray<TriangleSpace::FNode> tempnodes;
				tempnodes.Add(iter);
				insertNodes.Add(index2, tempnodes);
			}
			else
			{
				insertNodes[index2].Add(iter);
			}
		}
	}
	TArray<TriangleSpace::FEdge> outlineEdges;
	TArray<TriangleSpace::FEdge> allEdges;

	getEdges(outlineNodes, outlineEdges);
	for (auto& iter : insertNodes)
	{
		TArray<TriangleSpace::FEdge> edges;
		getEdges(iter.Value, edges);
		outlineEdges.Append(edges);

	}
	outEdges.Append(outlineEdges);

	TArray<TriangleSpace::FEdge> newEdges;
	for (int32 i = 0; i < outEdges.Num(); ++i)
	{
		auto currentEdge = outEdges[i];
		FNode TNode;

		if (!outEdges[i].get3DMaxAngleLeftNode(nodes, TNode, inNormal)
			|| outEdges[i].getStartNode().getBorderIndex() != -1 && outEdges[i].getStartNode().getBorderIndex() == TNode.getBorderIndex() && outEdges[i].getEndNode().getBorderIndex() == TNode.getBorderIndex())
			continue;

		auto edge0 = TriangleSpace::FEdge(outEdges[i].getStartNode(), TNode);
		auto edge1 = TriangleSpace::FEdge(TNode, outEdges[i].getEndNode());
		addEdge(outEdges, edge0, inNormal);
		addEdge(outEdges, edge1, inNormal);
		{
			//addEdge(outEdges, edge0, edge1,inNormal);
		}

	}
	return true;
}

bool FMeshTriangulator::getEdges(const TArray<FNode>& nodes, TArray<TriangleSpace::FEdge>& outEdges)
{
	if (nodes.Num() == 0)
		return false;
	for (int32 i = 0; i < nodes.Num(); ++i)
	{
		if (i == nodes.Num() - 1)
		{
			outEdges.Add(TriangleSpace::FEdge(nodes[i], nodes[0]));
			break;
		}
		outEdges.Add(TriangleSpace::FEdge(nodes[i], nodes[i + 1]));
	}
	return true;
}

bool FMeshTriangulator::canAddEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdge, const FVector& inNormal)
{
	for (auto& iter : edges)
	{

		bool b1 = iter.leftNode(newEdge.getStartNode(), inNormal);
		bool b2 = iter.rightNode(newEdge.getEndNode(), inNormal);

		bool b3 = iter.rightNode(newEdge.getStartNode(), inNormal);
		bool b4 = iter.leftNode(newEdge.getEndNode(), inNormal);

		bool b5 = newEdge.leftNode(iter.getStartNode(), inNormal);
		bool b6 = newEdge.rightNode(iter.getEndNode(), inNormal);

		bool b7 = newEdge.rightNode(iter.getStartNode(), inNormal);
		bool b8 = newEdge.leftNode(iter.getEndNode(), inNormal);

		if ((b1 && b2 || b3 && b4) && (b5 && b6 || b7 && b8))
		{
			return false;
		}
	}
	return true;
}

bool FMeshTriangulator::addEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdge, const FVector& inNormal)
{

	for (auto & iter : edges)
	{
		if (iter.equal(newEdge))
			return false;
	}
	edges.Add(newEdge);
	return true;
}

bool FMeshTriangulator::addEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdgeA, TriangleSpace::FEdge newEdgeB, const FVector& inNormal)
{
	if (canAddEdge(edges, newEdgeA, inNormal))
	{
		addEdge(edges,newEdgeA, inNormal);
	}
	if (canAddEdge(edges, newEdgeB, inNormal))
	{
		addEdge(edges, newEdgeB, inNormal);

	}
	return true;
}

void FMeshTriangulator::generateNodes(const TArray<FVector>& outline, const TArray<TArray<FVector>>& insides, TArray<TriangleSpace::FNode>& outNodes)
{
	int32 index = -1;
	for (int32 i = 0; i < outline.Num(); ++i)
	{
		++index;
		outNodes.Add(FNode(index, outline[i],true,-1));
	}
	if (index < 2)
		return;
	for (int32 i = 0; i < insides.Num(); ++i)
	{
		for (int32 j = 0; j < insides[i].Num(); ++j)
		{
			++index;
			outNodes.Add(FNode(index, insides[i][j], false, i));
		}
	}
}

void FMeshTriangulator::makeTrangles(TArray<TriangleSpace::FEdge> edges, TArray<TriangleSpace::FDelaunayTriangle>& outTrangles, const FVector& inNormal,bool bHasInsert)
{
	for (auto & i : edges)
	{
		TArray<TriangleSpace::FEdge> sEdges;
		TArray<TriangleSpace::FEdge> eEdges;
		TriangleSpace::FNode sn = i.getStartNode();
		TriangleSpace::FNode en = i.getEndNode();
		for (auto& j : edges)
		{
			if (i.equal(j))
				continue;
			if (j.containsNode(sn))
			{
				sEdges.Add(j);
			}
			else if (j.containsNode(en))
			{
				eEdges.Add(j);
			}
		}

		for (auto& s : sEdges)
		{
			for (auto& e : eEdges)
			{
				TriangleSpace::FNode crossNode;
				if (s.lineCross(e, crossNode))
				{
					FVector edgeDir = i.getDirection();
					if (i.leftNode(crossNode, inNormal))
							outTrangles.AddUnique(TriangleSpace::FDelaunayTriangle(i.getStartNode(), i.getEndNode(), crossNode));			
					else if (i.rightNode(crossNode, inNormal))
					{
						if (!bHasInsert || !i.getEndNode().getOutline() && !i.getStartNode().getOutline())
							outTrangles.AddUnique(TriangleSpace::FDelaunayTriangle(i.getStartNode(), crossNode, i.getEndNode()));
					}
					continue;
				}
			}
		}
	}
}


bool FMeshTriangulator::nodesAnticlock(TArray<TriangleSpace::FNode>& nodes, const FVector& inNormal)
{
	//在平面上计算面积 面积为正则绕法线为逆时针

	auto sortNodes = nodes;
	if (sortNodes.Num() < 3)
		return false;

	TArray<FVector> pos;
	pos.SetNumZeroed(sortNodes.Num());

	for (int32 i = 0; i < sortNodes.Num(); ++i)
	{
		pos[i] = sortNodes[i].getPosition();
	}

	return FMathLibrary::Compute3DArea(pos, inNormal) > 0;
}

bool FMeshTriangulator::antclockNodes(TArray<TriangleSpace::FNode>& nodes, const FVector& inNormal)
{
	if (nodes.Num() < 3)
		return false;

	if (nodesAnticlock(nodes, inNormal))
	{
		return false;
	}
	TArray<FNode> statckNodes = nodes;
	int32 index = statckNodes.Num() - 1;
	for (auto & iter : statckNodes)
	{
		nodes[index] = FNode(index, iter.getPosition(), iter.getOutline(), iter.getBorderIndex());
		--index;
	}
	return true;
}

double FMathLibrary::Compute2DArea(const TArray<FVector2D>& PolygonPoints)
{
	double Area = 0.0;
	const int32 Length = PolygonPoints.Num();
	if (Length < 3)
	{
		return Area;
	}
	FVector2D CurPoint;
	FVector2D NextPoint;
	for (int32 i(0); i < Length; ++i)
	{
		CurPoint = PolygonPoints[i];
		NextPoint = PolygonPoints[(i + 1) % Length];
		Area += (CurPoint.X * NextPoint.Y - NextPoint.X * CurPoint.Y);
	}
	return Area * 0.5f;
}


double FMathLibrary::Compute3DArea(const TArray<FVector>& PolygonPoints, const FVector& inNormal)
{
	double area = 0.0;
	const int32 count = PolygonPoints.Num();
	if (count < 3)
	{
		return area;
	}

	FVector a = PolygonPoints[1] - PolygonPoints[0];

	for (int32 i = 1; i < count; i++)
	{
		FVector b = PolygonPoints[i] - PolygonPoints[0];
		FVector cross = FVector::CrossProduct(b, a);

		area += FVector::DotProduct(inNormal, cross);
	}
	return 0.5 * area;
}

bool FMathLibrary::pointsAntclock(const TArray<FVector>& nodes, const FVector& inNormal)
{
	return Compute3DArea(nodes, inNormal) > 0;
}


void FMathLibrary::transformPoint(const FVector& inNormal, const FVector& disNormal, TArray<FVector>& points)
{

	FVector tempNormal = FVector::CrossProduct(inNormal, disNormal);

	auto angele = angle3D(inNormal, disNormal, inNormal);
	for (auto & iter : points)
	{
		iter = iter.RotateAngleAxis(angele, tempNormal);
	}
}

double FMathLibrary::angle3D(const FVector& VecA, const FVector& VecB, const FVector& inNormal)
{
	//绕法线逆时针为正
	double dot = FVector::DotProduct(VecA, VecB);
	double cosRad = dot / (VecA.Size() * VecB.Size());
	auto a = FMath::RadiansToDegrees(FMath::Acos(cosRad));
	//FVector n = FVector::CrossProduct(VecA, VecB);
	//if ((n + inNormal).IsNearlyZero(0.000001f))
	//{
	//	a = -a;
	//}
	return a;
}

FVector FMathLibrary::getCenterPoint(const TArray<FVector>& inPoints)
{	
	FVector sum = FVector::ZeroVector;
	for (auto & iter : inPoints)
	{
		sum += iter;
	}
	return sum / inPoints.Num();
}

bool FMathLibrary::pointCmp(const FVector& p0, const FVector& p1, const FVector& centerPoint, const FVector& inNormal)
{
	FVector vec0 = centerPoint - p0;
	FVector vec1 = centerPoint - p1;

	double a = FVector::DotProduct(FVector::CrossProduct(vec0, vec1).GetSafeNormal(), inNormal);
	if (a == 0)
	{
		return vec0.Size() > vec1.Size();
	}

	return a > 0;
}

void FMathLibrary::clockwiseSortPoint(TArray<FVector>& polygonPoints, const FVector& inNormal)
{
	auto c = getCenterPoint(polygonPoints);
	polygonPoints.Sort([&](const FVector& p0, const FVector& p1) {return pointCmp(p0, p1,c, inNormal); });
}

bool TriangleSpace::FEdge::containsNode(FNode node)
{
	return startNode.equal(node) || endNode.equal(node);
}

bool TriangleSpace::FEdge::lineCross(FEdge edge,FNode& crossNode)
{
	if (containsNode(edge.getStartNode()))
	{
		crossNode = edge.getStartNode();
		return true;
	}
	else if (containsNode(edge.getEndNode()))
	{
		crossNode = edge.getEndNode();
		return true;
	}
	return  false;
}

bool TriangleSpace::FEdge::getMaxAngleLeftNode(TArray<TriangleSpace::FNode> nodes, TriangleSpace::FNode & outNode, const FVector& inNormal)
{
	auto leftNodes = getLeftNodes(nodes, inNormal);
	TArray<FNode> maxAngleLeftNodes;
	float maxAngle = 0;
	for (auto& iter : leftNodes)
	{
		auto startDir = FEdge(startNode, iter).getDirection();
		auto endDir = FEdge(endNode, iter).getDirection();
		startDir.Normalize();
		endDir.Normalize();
		auto angle = FMathLibrary::angle3D(startDir, endDir, inNormal);

		if (angle > maxAngle)
		{
			maxAngleLeftNodes.Empty();
			maxAngleLeftNodes.Add(iter);
			maxAngle = angle;
		}
		else if (angle == maxAngle)
		{
			maxAngleLeftNodes.Add(iter);
		}
	}
	if (maxAngleLeftNodes.Num() == 0)
	{
		return false;
	}
	else if (maxAngleLeftNodes.Num() == 1)
	{
		outNode = maxAngleLeftNodes[0];
		return true;
	}
	else if(maxAngleLeftNodes.Num() == 2)
	{
		auto edge0 = FEdge(startNode, maxAngleLeftNodes[0]);
		auto edge1 = FEdge(startNode, maxAngleLeftNodes[1]);

		auto dir0 = edge0.getDirection();
		auto dir1 = edge1.getDirection();

		auto selfDir = getDirection();

		auto angle0 = FMath::Abs(FMathLibrary::angle3D(selfDir, dir0, inNormal));
		auto angle1 = FMath::Abs(FMathLibrary::angle3D(selfDir, dir1, inNormal));

		
		if (angle0 > angle1)
		{
			outNode = maxAngleLeftNodes[0];
		}
		else
		{
			outNode = maxAngleLeftNodes[1];
		}
		return true;
	}
	return false;
}

bool TriangleSpace::FEdge::get3DMaxAngleLeftNode(TArray<FNode> nodes, TriangleSpace::FNode& outNode, const FVector& inNormal)
{
	auto leftNodes = getReverseNodes(nodes, inNormal);
	TArray<FNode> maxAngleLeftNodes;
	float maxAngle = 0;

	for (auto& iter : leftNodes)
	{
		auto startDir = FEdge(startNode, iter).getDirection();
		auto endDir = FEdge(endNode, iter).getDirection();

		auto angle = FMathLibrary::angle3D(startDir, endDir, inNormal);

		if (angle > maxAngle)
		{
			maxAngleLeftNodes.Empty();
			maxAngleLeftNodes.Add(iter);
			maxAngle = angle;
		}
		else if (angle == maxAngle)
		{
			maxAngleLeftNodes.Add(iter);
		}
	}
	if (maxAngleLeftNodes.Num() == 0)
	{
		return false;
	}
	else if (maxAngleLeftNodes.Num() == 1)
	{
		outNode = maxAngleLeftNodes[0];
		return true;
	}
	else if (maxAngleLeftNodes.Num() == 2)
	{
		auto edge0 = FEdge(startNode, maxAngleLeftNodes[0]);
		auto edge1 = FEdge(startNode, maxAngleLeftNodes[1]);

		auto dir0 = edge0.getDirection();
		auto dir1 = edge1.getDirection();

		auto selfDir = getDirection();

		auto angle0 = FMath::Abs(FMathLibrary::angle3D(selfDir, dir0, inNormal));
		auto angle1 = FMath::Abs(FMathLibrary::angle3D(selfDir, dir1, inNormal));


		if (angle0 > angle1)
		{
			outNode = maxAngleLeftNodes[0];
		}
		else
		{
			outNode = maxAngleLeftNodes[1];
		}
		return true;
	}
	return false;
}

bool TriangleSpace::FEdge::getMaxAngleRightNode(TArray<FNode> nodes, TriangleSpace::FNode& outNode, const FVector& inNormal)
{
	auto rightNodes = getRightNodes(nodes, inNormal);

	TArray<FNode> maxAngleRightNodes;
	float maxAngle = 0;
	for (auto& iter : rightNodes)
	{
		auto startDir = FEdge(startNode, iter).getDirection();
		auto endDir = FEdge(endNode, iter).getDirection();
		auto cosRad = startDir.CosineAngle2D(endDir);
		if (FVector::CrossProduct(inNormal, FVector::XAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(startDir.Y, startDir.Z,0);
			FVector vec1 = FVector(endDir.Y, endDir.Z, 0);
			cosRad = vec0.CosineAngle2D(vec1);

		}
		else if (FVector::CrossProduct(inNormal, FVector::YAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(startDir.X, startDir.Z, 0);
			FVector vec1 = FVector(endDir.X, endDir.Z, 0);
			cosRad = vec0.CosineAngle2D(vec1);
		}

		
		auto angle = FMath::RadiansToDegrees(FMath::Acos(cosRad));
		if (angle >  maxAngle)
		{
			maxAngleRightNodes.Empty();
			maxAngleRightNodes.Add(iter);
			maxAngle = angle;
		}
		else if (angle == maxAngle)
		{
			maxAngleRightNodes.Add(iter);
		}
	}
	if (maxAngleRightNodes.Num() == 0)
	{
		return false;
	}
	else if (maxAngleRightNodes.Num() == 1)
	{
		outNode = maxAngleRightNodes[0];
		return true;
	}
	else if (maxAngleRightNodes.Num() == 2)
	{
		auto edge0 = FEdge(startNode, maxAngleRightNodes[0]);
		auto edge1 = FEdge(startNode, maxAngleRightNodes[1]);

		auto dir0 = edge0.getDirection();
		auto dir1 = edge1.getDirection();

		auto selfDir = getDirection();
		auto cosRad0 = selfDir.CosineAngle2D(dir0);

		if (FVector::CrossProduct(inNormal, FVector::XAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(selfDir.Y, selfDir.Z, 0);
			FVector vec1 = FVector(dir0.Y, dir0.Z, 0);
			cosRad0 = vec0.CosineAngle2D(vec1);

		}
		else if (FVector::CrossProduct(inNormal, FVector::YAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(selfDir.X, selfDir.Z, 0);
			FVector vec1 = FVector(dir0.X, dir0.Z, 0);
			cosRad0 = vec0.CosineAngle2D(vec1);
		}

		auto angle0 = FMath::RadiansToDegrees(FMath::Acos(cosRad0));
		auto cosRad1 = selfDir.CosineAngle2D(dir1);

		if (FVector::CrossProduct(inNormal, FVector::XAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(selfDir.Y, selfDir.Z, 0);
			FVector vec1 = FVector(dir1.Y, dir1.Z, 0);
			cosRad1 = vec0.CosineAngle2D(vec1);

		}
		else if (FVector::CrossProduct(inNormal, FVector::YAxisVector).IsNearlyZero(0.000000001))
		{
			FVector vec0 = FVector(selfDir.X, selfDir.Z, 0);
			FVector vec1 = FVector(dir1.X, dir1.Z, 0);
			cosRad1 = vec0.CosineAngle2D(vec1);
		}

		auto angle1 = FMath::RadiansToDegrees(FMath::Acos(cosRad1));

		if (angle0 > angle1)
		{
			outNode = maxAngleRightNodes[0];
		}
		else
		{
			outNode = maxAngleRightNodes[1];
		}
		return true;
	}
	return false;
}

TArray<TriangleSpace::FNode> TriangleSpace::FEdge::getLeftNodes(TArray<TriangleSpace::FNode> nodes, const FVector& inNormal)
{
	TArray<TriangleSpace::FNode> leftNodes;
	for (auto & iter : nodes)
	{
		if (iter.equal(startNode) || iter.equal(endNode))
			continue;
		else if (!leftNode(iter, inNormal))
			continue;
		leftNodes.Add(iter);
	}
	return leftNodes;
}

TArray<TriangleSpace::FNode> TriangleSpace::FEdge::getRightNodes(TArray<TriangleSpace::FNode> nodes, const FVector& inNormal)
{
	TArray<TriangleSpace::FNode> rightNodes;
	for (auto& iter : nodes)
	{
		if (iter.equal(startNode) || iter.equal(endNode))
			continue;
		else if (!rightNode(iter, inNormal))
			continue;
		rightNodes.Add(iter);
	}
	return rightNodes;
}

TArray<FNode> TriangleSpace::FEdge::getReverseNodes(TArray<FNode> nodes, const FVector& inNormal)
{
	TArray<TriangleSpace::FNode> reNodes;
	for (auto& iter : nodes)
	{
		if (iter.equal(startNode) || iter.equal(endNode))
			continue;
		else if (!reverseNode(iter, inNormal))
			continue;
		reNodes.Add(iter);
	}
	return reNodes;
}

bool TriangleSpace::FEdge::leftNode(TriangleSpace::FNode node,const FVector& inNormal)
{
	auto otherEdge = FEdge(startNode, node);
	auto selfDir = getDirection();
	auto otherDir = otherEdge.getDirection();
	//auto z = selfDir.X * otherDir.Y - selfDir.Y * otherDir.X;
	auto crossValue = FVector::CrossProduct(selfDir, otherDir);
	auto z = FVector::DotProduct(crossValue, inNormal);
	return z < 0;
}

bool TriangleSpace::FEdge::rightNode(TriangleSpace::FNode node, const FVector& inNormal)
{
	auto otherEdge = FEdge(startNode, node);
	auto selfDir = getDirection();
	auto otherDir = otherEdge.getDirection();
	//auto z = selfDir.X * otherDir.Y - selfDir.Y * otherDir.X;
	auto crossValue = FVector::CrossProduct(selfDir, otherDir);
	auto z = FVector::DotProduct(crossValue, inNormal);


	return z > 0;
}
bool TriangleSpace::FEdge::reverseNode(FNode node, const FVector& inNormal)
{
	auto otherEdge = FEdge(startNode, node);
	auto otherDir = otherEdge.getDirection();

	auto a = FVector::DotProduct(otherDir, inNormal);

	return a < 0;
}
FNode TriangleSpace::FEdge::getMaxAngleNode(TArray<TriangleSpace::FNode> nodes)
{
	float maxAngle = 0.0f;
	FNode maxAngleNode;
	for (auto & iter : nodes)
	{
		if (startNode.equal(iter))
			continue;
		auto edge = FEdge(startNode, iter);
		auto selfDir = getDirection();
		auto edgeDir = edge.getDirection();

		auto cosRad = selfDir.CosineAngle2D(edgeDir);
		auto angle = FMath::RadiansToDegrees(FMath::Acos(cosRad));
		if (angle > maxAngle)
		{
			maxAngle = angle;
			maxAngleNode = iter;
		}
	}
	return maxAngleNode;
}

bool FMeshTriangulator::delaunayTrangleWithBorder(const TArray<FVector>& outline, const TArray <TArray<FVector>>& insertPoints,  const FVector& inNormal, TArray<FVector>& outVertex, TArray<TArray<int32>>& outIndex)
{

	FVector AbsNormal = inNormal.GetAbs();

	TArray<TriangleSpace::FDelaunayTriangle> trangles;
	TArray<TriangleSpace::FEdge> edges;
	TArray<TArray<FVector>> allInserts = insertPoints;
	for (auto& i : allInserts)
	{
		if (FVector::CrossProduct(inNormal,FVector::UpVector).IsNearlyZero(0.000000001))
		{
			for (auto& j : i)
			{
				j.Z = outline[0].Z;
			}
		}
		else if (FVector::CrossProduct(inNormal, FVector::XAxisVector).IsNearlyZero(0.000000001))
		{
			for (auto& j : i)
			{
				j.X = outline[0].X;
			}
		}
		else if (FVector::CrossProduct(inNormal, FVector::YAxisVector).IsNearlyZero(0.000000001))
		{
			for (auto& j : i)
			{
				j.Y = outline[0].Y;
			}
		}
	}
	TArray<TriangleSpace::FNode> nodes;
	//获取顶点
	generateNodes(outline, allInserts, nodes);

	//顶点绕法线逆时针排序
	antclockNodes(nodes, inNormal);

	//生成三角边
	trangleEdges(nodes, edges, inNormal);

	//制作三角形
	makeTrangles(edges, trangles, inNormal, allInserts.Num() > 0);
	//procedureTrangles(trangles, nodes);
	if (trangles.Num() == 0)
	{
		return false;
	}
	outVertex.Append(outline);
	for (auto & i : allInserts)
	{
		outVertex.Append(i);
	}

	for (auto& t : trangles)
	{
		TArray<int32> indexs;
		indexs.SetNumZeroed(3);
		TArray<FVector> tp = t.getNodePositions();
		if (tp.Num() < 3)
			return false;
		for (int32 i = 0; i < outVertex.Num(); ++i)
		{

				if (outVertex[i] == tp[0])
					indexs[0] = i;
				else if (outVertex[i] == tp[1])
					indexs[1] = i;
				else if (outVertex[i] == tp[2])
					indexs[2] = i;
		}
		outIndex.Add(indexs);
	}
	//UE_LOG(LogTemp, Log, TEXT("obj x"));
	//for (auto& i : outVertex)
	//	UE_LOG(LogTemp, Warning, TEXT("v %f %f %f"), i.X, i.Y, 0);
	//for (auto& i : outIndex)
	//	UE_LOG(LogTemp, Warning, TEXT("f %d %d %d"), i[0] + 1, i[1] + 1, i[2] + 1);

	return true;
}

bool FMeshTriangulator::enclosueModel(const TArray<FEnclosure>& inEnclosure, const FVector& inNormal, TArray<FVector>& outVertex, TArray<TArray<int32>>& outIndex)
{
	TArray<TArray<FNode>> cross;
	TMap<int32,TArray<TriangleSpace::FEdge>> edges;
	TMap<int32, FVector> normals;
	TArray<FVector> Vecs;
	int32 count = 0;
	cross.SetNumZeroed(inEnclosure.Num());
	for (const auto & iter : inEnclosure)
	{
		generateNodes(iter.outline, TArray <TArray<FVector>> (), cross[count]);
		antclockNodes(cross[count], iter.normal);
		TArray<TriangleSpace::FEdge> edge;
		getEdges(cross[count], edge);
		edges.Add(count, edge);
		normals.Add(count, iter.normal);
		Vecs.Append(iter.outline);
		++count;
	}
	for (int32 i = 0; i < count; ++i)
	{
		TArray<FNode> otherNodes;
		for (auto& iter : edges)
		{
			if (iter.Key != i)
			{
				otherNodes.Append(cross[i]);
			}
		}

		for (auto & iEdge : edges[i])
		{
			trangleEdges3D(otherNodes, edges[i], normals[i]);
		}
	}
	TArray<TriangleSpace::FEdge> allEdges;
	for (auto & iter : edges)
	{
		allEdges.Append(iter.Value);
	}

	TArray<TriangleSpace::FDelaunayTriangle> trangles;
	//制作三角形
	makeTrangles(allEdges, trangles, inNormal, false);
	//procedureTrangles(trangles, nodes);
	if (trangles.Num() == 0)
	{
		return false;
	}
	outVertex.Append(Vecs);


	for (auto& t : trangles)
	{
		TArray<int32> indexs;
		indexs.SetNumZeroed(3);
		TArray<FVector> tp = t.getNodePositions();
		if (tp.Num() < 3)
			return false;
		for (int32 i = 0; i < outVertex.Num(); ++i)
		{

			if (outVertex[i] == tp[0])
				indexs[0] = i;
			else if (outVertex[i] == tp[1])
				indexs[1] = i;
			else if (outVertex[i] == tp[2])
				indexs[2] = i;
		}
		outIndex.Add(indexs);
	}
	return true;
}

//bool FMeshTriangulator::createModel(const TArray<FEnclosure>& enclosur, const FVector& buildNormal, FModel& outModel)
//{
//	TArray<FNode> floors;
//	for (auto & iter : enclosur)
//	{
//		TArray<FNode> floor;
//		generateNodes(iter.outline, TArray<TArray<FVector>>(), floor);
//		antclockNodes(floor, iter.normal);
//		floors.Append(floor);
//	}
//
//	for (auto & iter : floors)
//	{
//
//	}
//
//	return false;
//}


bool TriangleSpace::FDelaunayTriangle::operator==(const FDelaunayTriangle& other) const
{
	TArray<TriangleSpace::FNode> nodes;
	nodes.Add(startNode);
	nodes.Add(middleNode);
	nodes.Add(endNode);

	nodes.AddUnique(other.startNode);
	nodes.AddUnique(other.middleNode);
	nodes.AddUnique(other.endNode);
	return nodes.Num() == 3;
}

bool TriangleSpace::FDelaunayTriangle::edgeCross(TriangleSpace::FEdge inEdge,const FVector& inNormal)
{

	TArray<FEdge> edges;
	edges.SetNumZeroed(3);
	edges[0] = FEdge(startNode, middleNode);
	edges[1] = FEdge(middleNode, endNode);
	edges[2] = FEdge(endNode, startNode);
	for (auto& iter : edges)
	{
		bool b1 = iter.leftNode(inEdge.getStartNode(), inNormal);
		bool b2 = iter.rightNode(inEdge.getEndNode(), inNormal);

		bool b3 = iter.rightNode(inEdge.getStartNode(), inNormal);
		bool b4 = iter.leftNode(inEdge.getEndNode(), inNormal);

		bool b5 = inEdge.leftNode(iter.getStartNode(), inNormal);
		bool b6 = inEdge.rightNode(iter.getEndNode(), inNormal);

		bool b7 = inEdge.rightNode(iter.getStartNode(), inNormal);
		bool b8 = inEdge.leftNode(iter.getEndNode(), inNormal);

		if ((b1 && b2 || b3 && b4) && (b5 && b6 || b7 && b8))
		{
			return true;
		}
	}
	return false;
}

FEnclosure::FEnclosure()
{
	outline = TArray<FVector>();
	bFill = true;
}
