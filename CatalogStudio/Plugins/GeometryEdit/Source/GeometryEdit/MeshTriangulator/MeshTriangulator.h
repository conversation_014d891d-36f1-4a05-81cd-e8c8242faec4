#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"

namespace TriangleSpace
{
	enum EPointToLine
	{
		ELeft,
		EOnline,
		ERight
	};

	enum EPointToTriangle
	{
		EInside,
		EOnBorder,
		EOutside
	};

	class GEOMETRYEDIT_API FNode
	{
	public:

		FNode() {}
		FNode(const int32& inIndex, const FVector& inPosition,bool ouline,const int32& inBorderIndex)
			:index(inIndex), position(inPosition), bOutline(ouline),borderIndex(inBorderIndex),bTriangleStart(false)
		{}
		
		int32 getIndex() { return index; }
		FVector getPosition() { return position;}
		void setOutline(bool inTrue) { bOutline = inTrue; }
		bool getOutline() { return bOutline; }
		bool equal(FNode otherNode) { return index == otherNode.getIndex() || position.Equals(otherNode.getPosition(),0.000001f); }
		bool equal(FNode otherNode)const { return index == otherNode.getIndex() || position.Equals(otherNode.getPosition(), 0.000001f); }
		int32 getBorderIndex() { return borderIndex; }
		bool getTriangleStart() { return bTriangleStart; }
		void setTriangleStart(bool isTrue) { bTriangleStart = isTrue; }
		void operator = (FNode oth) 
		{ 
			index = oth.getIndex();
			position = oth.getPosition(); 
			bOutline = oth.getOutline();
			borderIndex = oth.getBorderIndex();
			bTriangleStart = oth.getTriangleStart();

		}
		bool operator == (FNode oth) const
		{
			return equal(oth);
		}


	private:
		int32 index;
		FVector position;
		bool bOutline;
		int32 borderIndex;
		bool bTriangleStart;
	};

	class GEOMETRYEDIT_API FEdge
	{
	public:
		FEdge() {}
		FEdge(FNode inStartNode, FNode inEndNode)
			:startNode(inStartNode), endNode(inEndNode),bHasLeftTriangle(false)
		{}

		void setStartNode(const FNode& inStartNode) { startNode = inStartNode; }
		void setEndNode(const FNode& inEndNode) { endNode = inEndNode; }

		FNode getStartNode() { return startNode; }
		FNode getEndNode() { return endNode; }

		FVector getDirection() { return endNode.getPosition() - startNode.getPosition(); }

		bool contactStartNode(FEdge other) { return startNode.equal(other.getEndNode()); }
		bool contactEndNode(FEdge other) { return endNode.equal(other.getStartNode()); }
		bool equal(FEdge other) {
			return startNode.equal(other.getStartNode()) && endNode.equal(other.getEndNode())
				|| endNode.equal(other.getStartNode()) && startNode.equal(other.getEndNode());
		}

		bool containsNode(FNode node);
		bool lineCross(FEdge edge,FNode& crossNode);
		void setHasLeftTriangle(bool inTrue) { bHasLeftTriangle = inTrue; }
		bool getHasLeftTriangle() { return bHasLeftTriangle; }
	public:

		bool getMaxAngleLeftNode(TArray<FNode> nodes, TriangleSpace::FNode& outNode, const FVector& InNormal);
		bool get3DMaxAngleLeftNode(TArray<FNode> nodes, TriangleSpace::FNode& outNode, const FVector& InNormal);

		bool getMaxAngleRightNode(TArray<FNode> nodes, TriangleSpace::FNode& outNode, const FVector& InNormal);
		FNode getMaxAngleNode(TArray<FNode> nodes);
		TArray<FNode> getLeftNodes(TArray<FNode> nodes, const FVector& InNormal);
		TArray<FNode> getRightNodes(TArray<FNode> nodes, const FVector& InNormal);
		TArray<FNode> getReverseNodes(TArray<FNode> nodes, const FVector& InNormal);

		bool leftNode(FNode node, const FVector& InNormal);
		bool rightNode(FNode node, const FVector& InNormal);
		bool reverseNode(FNode node, const FVector& InNormal);

	private:
		FNode startNode;
		FNode endNode;
		bool bHasLeftTriangle;
	};

	class GEOMETRYEDIT_API FDelaunayTriangle
	{
	public:
		FDelaunayTriangle(const FNode& inStartNode, const FNode& inMiddleNode, const FNode& inEndNode)
			:startNode(inStartNode), middleNode(inMiddleNode), endNode(inEndNode)
		{}
		FNode getStartNode() { return startNode; }
		FNode getMiddleNode() { return middleNode; }
		FNode getEndNode() { return endNode; }
		TArray<FVector> getNodePositions()
		{
			TArray<FVector> vecs;
			vecs.Add(startNode.getPosition());
			vecs.Add(middleNode.getPosition());
			vecs.Add(endNode.getPosition());
			return vecs;
		}

		bool operator == (const FDelaunayTriangle& other) const;

		bool edgeCross(TriangleSpace::FEdge inEdge,const FVector& InNormal);
	private:
		FNode startNode;
		FNode middleNode;
		FNode endNode;
	};

	class GEOMETRYEDIT_API FLine
	{
	public:
		FLine(){}
		FLine(const FVector& inStartPoint, const FVector& inEndPoint):
			startPoint(inStartPoint), endPoint(inEndPoint){}
		FVector getStartPoint() const;
		FVector getEndPoint() const;
		EPointToLine pointToPosition(const FVector& p) const;

		bool isTheSame(const FLine& oth);
		bool operator == (const FLine& oth) const;
	private:
		FVector startPoint;
		FVector endPoint;

	};
	class GEOMETRYEDIT_API FTriangle
	{
	public:
		FTriangle(){}
		FTriangle(const FVector& p0, const FVector& p1, const FVector& p2);
		EPointToTriangle pointToPosition(const FVector& p);
		TArray<FLine> getLines() const;
		TArray<FVector> getPoints() const;

		FVector getCenter() const;

		void differentLines(const FTriangle& oth, TArray<FLine>& outLines) const;
		void insectionLines(const FTriangle& oth, TArray<FLine>& outLines) const;

		bool containsLine(const FLine& inLine);
		bool containsPoint(const FVector& inPoint);
		bool triangleCircle(const FVector2D& p1, const FVector2D& p2, const FVector2D& p3, FVector2D& center, float& radius);
		bool isPointInTriangleCircle(const FVector2D& p);
		bool isLineCross(const FLine& inLine);
		bool operator == (const FTriangle& oth);
	private:
		TArray<FVector> points;
	};
}

class GEOMETRYEDIT_API FMathLibrary
{
public:
	static double Compute2DArea(const TArray<FVector2D>& PolygonPoints);
	static double Compute3DArea(const TArray<FVector>& PolygonPoints, const FVector& inNormal);
	static bool pointsAntclock(const TArray<FVector>& nodes, const FVector& inNormal);

	static void transformPoint(const FVector& inNormal, const FVector& disNormal, TArray<FVector>& points);
	static double angle3D(const FVector& VecA, const FVector& VecB, const FVector& inNormal);
	static FVector getCenterPoint(const TArray<FVector>& inPoints);
	static bool pointCmp(const FVector& p0, const FVector& p1, const FVector& centerPoint, const FVector& inNormal);
	static void clockwiseSortPoint(TArray<FVector>& polygonPoints, const FVector& inNormal);
};


class GEOMETRYEDIT_API FEnclosure
{
public:
	FEnclosure();
	FEnclosure(const int32 & inIndex,const TArray<FVector>& inOutline, const FVector& inNormal,bool isFill) :
		index(inIndex),outline(inOutline), normal(inNormal), bFill(isFill) {}
public:
	int32 index;
	TArray<FVector> outline;
	FVector normal;
	bool bFill;
};


class GEOMETRYEDIT_API FMeshTriangulator
{
private:
	static bool trangleEdges(TArray<TriangleSpace::FNode> nodes,TArray<TriangleSpace::FEdge>& outEdges, const FVector& inNormal);
	static bool trangleEdges3D(TArray<TriangleSpace::FNode> nodes, TArray<TriangleSpace::FEdge>& outEdges, const FVector& inNormal);

	static bool getEdges(const TArray<TriangleSpace::FNode>& nodes, TArray<TriangleSpace::FEdge>& outEdges);
	static bool canAddEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdge, const FVector& inNormal);

	static bool addEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdge, const FVector& inNormal);
	static bool addEdge(TArray<TriangleSpace::FEdge>& edges, TriangleSpace::FEdge newEdgeA, TriangleSpace::FEdge newEdgeB,const FVector& inNormal);

	static void generateNodes(const TArray<FVector>& outline, const TArray<TArray<FVector>>& insides, TArray<TriangleSpace::FNode>& outNodes);
	static void makeTrangles(TArray<TriangleSpace::FEdge> edges, TArray<TriangleSpace::FDelaunayTriangle>& outTrangles, const FVector& inNormal, bool bHasInsert = false);

	static bool nodesAnticlock(TArray<TriangleSpace::FNode>& nodes, const FVector& inNormal);
	static bool antclockNodes(TArray<TriangleSpace::FNode>& nodes, const FVector& inNormal);


public:

	static bool delaunayTrangleWithBorder(const TArray<FVector>& outline, const TArray <TArray<FVector>>& insertPoints, const FVector& inNormal, TArray<FVector>& outVertex, TArray<TArray<int32>>& outIndex);
	static bool enclosueModel(const TArray<FEnclosure>& inEnclosure, const FVector& inNormal, TArray<FVector>& outVertex, TArray<TArray<int32>>& outIndex);

};
