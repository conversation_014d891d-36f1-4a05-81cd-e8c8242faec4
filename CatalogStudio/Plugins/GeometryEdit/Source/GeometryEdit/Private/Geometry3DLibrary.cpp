// Copyright Epic Games, Inc. All Rights Reserved.

#include "Geometry3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"


bool FGeometry3DLibrary::GenerateArcByRadius(const FVector& Start, const FVector& End, const FVector& Normal, const float& Radius, bool BigArc, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount)
{
	const float HalfChordLength = FVector::Dist(Start, End) * 0.5f;
	const float AbsRadius = FMath::Abs(Radius);
	float DeltaAngle = FMath::Acos(1.0f - (MinmalChordLength * MinmalChordLength) / (Radius * Radius * 2.0f));
	TArray<FVector> Points;
	if (FMath::IsNearlyEqual(HalfChordLength, AbsRadius, 0.01f))
	{//
		const FVector CenterPoint = (Start + End) * 0.5f;
		const FVector XDir = (Start - CenterPoint).GetSafeNormal();
		const FVector YDir = (Normal ^ XDir).GetSafeNormal();
		float EndAngle = -1.0f * PI * FMath::Sign(Radius);
		//Begin fix bug CATALOG-1418 等分时不存在大小弧
		//if (BigArc) EndAngle = (EndAngle > 0) ? (EndAngle - PI * 2.0f) : (EndAngle + PI * 2.0f);
		//End fix bug CATALOG-1418
		int32 PointCount = FMath::Floor(FMath::Abs(EndAngle) / DeltaAngle);
		if (InCount > 0)
		{
			DeltaAngle = FMath::RadiansToDegrees((FMath::DegreesToRadians(DeltaAngle) * PointCount) / InCount);
			PointCount = InCount;
		}
		const float StartAngle = FMath::Min(EndAngle, 0.0f);
		EndAngle = FMath::Max(EndAngle, 0.0f);
		UE_LOG(LogTemp, Log, TEXT("StartAngle=%f,EndAngle=%f,DeltaAngle=%f,PointCount=%d,MinmalChordLength=%f"), StartAngle, EndAngle, DeltaAngle, PointCount, MinmalChordLength);
		Points.Init(FVector::ZeroVector, PointCount + 1);
		for (int32 i = 0; i <= PointCount; ++i)
		{
			const float Theta = StartAngle + DeltaAngle * i;
			Points[i] = CenterPoint + (XDir * FMath::Cos(Theta) + YDir * FMath::Sin(Theta)) * AbsRadius;
		}
		if (!FMath::IsNearlyEqual(StartAngle + DeltaAngle * PointCount, EndAngle, 0.000017f))
		{//千分之一度
			Points.Add(CenterPoint + (XDir * FMath::Cos(EndAngle) + YDir * FMath::Sin(EndAngle)) * AbsRadius);
		}
		if (FMath::IsNearlyZero(EndAngle, 0.000017f)) FArrayOperatorLibrary::ReverseArray(Points);
		ArcPoints = MoveTemp(Points);
		return true;
	}
	if (AbsRadius > HalfChordLength)
	{
		const FVector CenterPoint = (Normal ^ (End - Start)).GetSafeNormal() * FMath::Sign(Radius) * (BigArc ? 1.0f : -1.0f) * FMath::Sqrt(Radius * Radius - HalfChordLength * HalfChordLength) + (End + Start) * 0.5f;
		const FVector XDir = (Start - CenterPoint).GetSafeNormal();
		const FVector YDir = (Normal ^ XDir).GetSafeNormal();
		const FVector EndDir = (End - CenterPoint).GetSafeNormal();
		float EndAngle = FMath::Acos(XDir | EndDir) * FMath::Sign(YDir | EndDir);
		if (BigArc) EndAngle = (EndAngle > 0) ? (EndAngle - PI * 2.0f) : (EndAngle + PI * 2.0f);
		int32 PointCount = FMath::Floor(FMath::Abs(EndAngle) / DeltaAngle);
		if (InCount > 0)
		{
			DeltaAngle = FMath::RadiansToDegrees((FMath::DegreesToRadians(DeltaAngle) * PointCount) / InCount);
			PointCount = InCount;
		}
		const float StartAngle = FMath::Min(EndAngle, 0.0f);
		EndAngle = FMath::Max(EndAngle, 0.0f);
		Points.Init(FVector::ZeroVector, PointCount + 1);
		for (int32 i = 0; i <= PointCount; ++i)
		{
			const float Theta = StartAngle + DeltaAngle * i;
			Points[i] = CenterPoint + (XDir * FMath::Cos(Theta) + YDir * FMath::Sin(Theta)) * AbsRadius;
		}
		if (!FMath::IsNearlyEqual(StartAngle + DeltaAngle * PointCount, EndAngle, 0.000017f))
		{//千分之一度
			Points.Add(CenterPoint + (XDir * FMath::Cos(EndAngle) + YDir * FMath::Sin(EndAngle)) * AbsRadius);
		}
		if (FMath::IsNearlyZero(EndAngle, 0.000017f)) FArrayOperatorLibrary::ReverseArray(Points);
		ArcPoints = MoveTemp(Points);
		return true;
	}
	return false;
}

bool FGeometry3DLibrary::GenerateArcByCamber(const FVector& Start, const FVector& End, const FVector& Normal, const float& Camber, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount)
{
	const float HalfChordLength = FVector::Dist(Start, End) * 0.5f;
	const float AbsRadius = FMath::Abs((Camber * Camber + HalfChordLength * HalfChordLength) / (2.0f * Camber));
	float DeltaAngle = FMath::Acos(1.0f - (MinmalChordLength * MinmalChordLength) / (AbsRadius * AbsRadius * 2.0f));
	UE_LOG(LogTemp, Warning, TEXT("CamberCamberCamberCamberCamber=%f"), Camber);
	UE_LOG(LogTemp, Warning, TEXT("DeltaAngleDeltaAngleDeltaAngleDeltaAngle=%f"), DeltaAngle);


	TArray<FVector> Points;
	if (FMath::IsNearlyEqual(HalfChordLength, AbsRadius, 0.01f))
	{
		const FVector CenterPoint = (Start + End) * 0.5f;
		const FVector XDir = (Start - CenterPoint).GetSafeNormal();
		const FVector YDir = (Normal ^ XDir).GetSafeNormal();
		float EndAngle = -1.0f * PI * FMath::Sign(Camber);
		int32 PointCount = FMath::Floor(FMath::Abs(EndAngle) / DeltaAngle);
		if (InCount > 0)
		{
			DeltaAngle = FMath::RadiansToDegrees((FMath::DegreesToRadians(DeltaAngle) * PointCount) / InCount);
			PointCount = InCount;
		}
		const float StartAngle = FMath::Min(EndAngle, 0.0f);
		EndAngle = FMath::Max(EndAngle, 0.0f);
		Points.Init(FVector::ZeroVector, PointCount + 1);
		for (int32 i = 0; i <= PointCount; ++i)
		{
			const float Theta = StartAngle + DeltaAngle * i;
			Points[i] = CenterPoint + (XDir * FMath::Cos(Theta) + YDir * FMath::Sin(Theta)) * AbsRadius;
			UE_LOG(LogTemp, Warning, TEXT("Points ======> %s"), *Points[i].ToString());
		}
		if (!FMath::IsNearlyEqual(StartAngle + DeltaAngle * PointCount, EndAngle, 0.000017f))
		{//千分之一度
			Points.Add(CenterPoint + (XDir * FMath::Cos(EndAngle) + YDir * FMath::Sin(EndAngle)) * AbsRadius);
		}
		else
		{
			//UE_LOG(LogTemp, Warning, TEXT("================= %f ========================"), StartAngle + DeltaAngle * PointCount);
		}
		if (FMath::IsNearlyZero(EndAngle, 0.000017f)) FArrayOperatorLibrary::ReverseArray(Points);
		ArcPoints = MoveTemp(Points);
		return true;
	}
	{
		const bool bBigArc = FMath::Abs(Camber) > AbsRadius;
		const FVector CenterPoint = (Normal ^ (End - Start)).GetSafeNormal() * FMath::Sign(Camber) * (FMath::Abs(Camber) - AbsRadius) + (End + Start) * 0.5f;
		const FVector XDir = (Start - CenterPoint).GetSafeNormal();
		const FVector YDir = (Normal ^ XDir).GetSafeNormal();
		const FVector EndDir = (End - CenterPoint).GetSafeNormal();
		float EndAngle = FMath::Acos(XDir | EndDir) * FMath::Sign(YDir | EndDir);

		UE_LOG(LogTemp, Log, TEXT("AbsRadius=%f,Camber=%f,EndAngle=%f"), AbsRadius, Camber, EndAngle);
		if (bBigArc) EndAngle = (EndAngle > 0) ? (EndAngle - PI * 2.0f) : (EndAngle + PI * 2.0f);
		int32 PointCount = FMath::Floor(FMath::Abs(EndAngle) / DeltaAngle);
		UE_LOG(LogTemp, Log, TEXT("DeltaAngle=%f"), DeltaAngle);
		if (InCount > 0)
		{
			DeltaAngle = FMath::RadiansToDegrees((FMath::DegreesToRadians(DeltaAngle) * PointCount) / InCount);
			PointCount = InCount;
		}
		Points.Init(FVector::ZeroVector, PointCount + 1);
		const float StartAngle = FMath::Min(EndAngle, 0.0f);
		EndAngle = FMath::Max(EndAngle, 0.0f);
		UE_LOG(LogTemp, Log, TEXT("AbsRadius=%f,Camber=%f,StartAngle=%f,EndAngle=%f,PointCount=%d,DeltaAngle=%f"), AbsRadius, Camber, StartAngle, EndAngle, PointCount, DeltaAngle);
		if (FMath::IsNearlyZero(DeltaAngle, 0.00001f))
		{//Fix bug CATALOG-1533
			ArcPoints.Init(FVector::ZeroVector, 2);
			ArcPoints[0] = Start;
			ArcPoints[1] = End;
			return true;
		}
		for (int32 i = 0; i <= PointCount; ++i)
		{
			const float Theta = StartAngle + DeltaAngle * i;
			Points[i] = CenterPoint + (XDir * FMath::Cos(Theta) + YDir * FMath::Sin(Theta)) * AbsRadius;
		}
		if (!FMath::IsNearlyEqual(StartAngle + DeltaAngle * PointCount, EndAngle, 0.000017f))
		{//千分之一度
			Points.Add(CenterPoint + (XDir * FMath::Cos(EndAngle) + YDir * FMath::Sin(EndAngle)) * AbsRadius);
		}
		if (FMath::IsNearlyZero(EndAngle, 0.000017f)) FArrayOperatorLibrary::ReverseArray(Points);
		ArcPoints = MoveTemp(Points);
		return true;
	}
	return false;
}

bool FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(const FVector& Start, const FVector& End, const FVector& Normal, const float& Radius, bool BigArc, const float& MinmalChordLength, TArray<FVector>& ArcPoints,  const int32& InCount)
{
	TArray<FVector> Points;
	bool Res = FGeometry3DLibrary::GenerateArcByRadius(Start, End, Normal, Radius, BigArc, MinmalChordLength, Points, InCount);
	if (false == Res) return false;
	if (Points.Num() >= 2)
	{
		ArcPoints.Init(FVector::ZeroVector, Points.Num() - 2);
		FMemory::Memcpy(&ArcPoints[0], &Points[1], sizeof(FVector) * ArcPoints.Num());
	}
	return true;
}

bool FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(const FVector& Start, const FVector& End, const FVector& Normal, const float& Camber, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount)
{
	TArray<FVector> Points;
	bool Res = FGeometry3DLibrary::GenerateArcByCamber(Start, End, Normal, Camber, MinmalChordLength, Points, InCount);
	if (false == Res) return false;
	if (Points.Num() >= 2)
	{
		ArcPoints.Init(FVector::ZeroVector, Points.Num() - 2);
		FMemory::Memcpy(&ArcPoints[0], &Points[1], sizeof(FVector) * ArcPoints.Num());
	}
	return true;
}

bool FGeometry3DLibrary::GenerateRectanglePlanPoints(const FVector& Min, const FVector& Max, const FVector& Normal, const FVector& TangentX, TArray<FVector>& RectPoints)
{
	const FVector TangentY = (Normal ^ TangentX).GetSafeNormal();
	const FVector Dir = Max - Min;
	const float XLength = (Dir | TangentX);
	const float YLength = (Dir | TangentY);
	const float ZLength = (Dir | Normal);
	if (FMath::IsNearlyZero(XLength, 0.01f) || FMath::IsNearlyZero(YLength, 0.01f)) return false;
	RectPoints.Init(Min, 4);
	RectPoints[1] = XLength * TangentX + Min;
	RectPoints[2] = Max;
	RectPoints[3] = YLength * TangentY + Min + ZLength * Normal;

	if (RectPoints.Num() >= 3)
	{
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(RectPoints, Normal);
		if (bCCWWinding)
		{
			FArrayOperatorLibrary::ReverseArray(RectPoints);
			auto first = RectPoints.Pop();
			RectPoints.Insert(first, 0);
		}
	}

	return true;
}

bool FGeometry3DLibrary::GenerateEllipsePlanPoints(const FVector& Center, const float& TangentXRadius, const float& TangentYRadius, const FVector& Normal, const FVector& TangentX, TArray<FVector>& Points, const int32& InCount)
{
	if (FMath::IsNearlyZero(TangentXRadius, 0.01f) || FMath::IsNearlyZero(TangentYRadius, 0.01f)) return false;
	const float AbsTangentXRadius = FMath::Abs(TangentXRadius);
	const float AbsTangentYRadius = FMath::Abs(TangentYRadius);
	int32 PointCount = 0;
	{
		//const float L = 2.0f * PI * FMath::Min(AbsTangentXRadius, AbsTangentYRadius) + 4.0f * FMath::Abs(AbsTangentXRadius - AbsTangentYRadius);
		//float MinimalChordLength = TangentXRadius / 25 + TangentYRadius / 25;
		//if (MinimalChordLength < 1)
		//{
		//	MinimalChordLength = 1;
		//}
		//PointCount = FMath::Floor(L / MinimalChordLength);

		Points.Init(Center, InCount);
	}
	const FVector TangentY = (Normal ^ TangentX).GetSafeNormal();
	const float DeltaAngle = 2.0f * PI / InCount;
	for (int32 i = 0; i < InCount; ++i)
	{
		const float Angle = DeltaAngle * i;
		const FVector TangentXDir = AbsTangentXRadius * FMath::Cos(Angle) * TangentX;
		Points[i] += TangentXDir;
		const FVector TangentYDir = AbsTangentYRadius * FMath::Sin(Angle) * TangentY;
		Points[i] += TangentYDir;
	}

	if (Points.Num() >= 3)
	{
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(Points, Normal);
		if (bCCWWinding)
		{
			FArrayOperatorLibrary::ReverseArray(Points);
			auto first = Points.Pop();
			Points.Insert(first, 0);
		}
	}

	return true;
}
