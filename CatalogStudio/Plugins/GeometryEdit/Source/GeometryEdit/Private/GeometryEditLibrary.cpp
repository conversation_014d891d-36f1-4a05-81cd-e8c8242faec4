// Copyright Epic Games, Inc. All Rights Reserved.

#include "GeometryEditLibrary.h"
#include "MeshDescriptionBuilder.h"
#include "StaticMeshAttributes.h"


bool FGeometryEditLibrary::ConvertPMCSectionsToMeshDescription(const TArray<FPMCSection>& InSections, FMeshDescription& Description)
{
	FStaticMeshAttributes StaticMeshAttributes(Description);
	StaticMeshAttributes.Register();
	FMeshDescriptionBuilder Builder;
	Builder.SetMeshDescription(&Description);
	Builder.EnablePolyGroups();
	Builder.SetNumUVLayers(1);
	int32 MaterialIndex = 0;
	for (auto Section : InSections)
	{
		FPolygonGroupID PolygonGroupID = Builder.AppendPolygonGroup();
		TArray<FVertexInstanceID> VertexInstanceIDs;
		VertexInstanceIDs.SetNum(Section.Vertexes.Num());
		for (int32 i = 0; i < Section.Vertexes.Num(); ++i)
		{
			auto VertexID = Builder.AppendVertex(Section.Vertexes[i]);
			VertexInstanceIDs[i] = Builder.AppendInstance(VertexID);
			Builder.SetInstanceNormal(VertexInstanceIDs[i], Section.Normals[i]);
			Builder.SetInstanceUV(VertexInstanceIDs[i], Section.UV[i]);
			Builder.SetInstanceColor(VertexInstanceIDs[i], FVector4f(1.0, 1.0, 1.0, 1.0));
		}
		const int32 TriangleCount = Section.Triangles.Num() / 3;
		for (int32 TriangleIndex = 0; TriangleIndex < TriangleCount; ++TriangleIndex)
		{
			const int32 TriangleBase = TriangleIndex * 3;
			const int32& A = Section.Triangles[TriangleBase];
			const int32& B = Section.Triangles[TriangleBase + 1];
			const int32& C = Section.Triangles[TriangleBase + 2];
			Builder.AppendTriangle(VertexInstanceIDs[A], VertexInstanceIDs[B], VertexInstanceIDs[C], PolygonGroupID);
		}
		TAttributesSet<FPolygonGroupID>& PolygonGroups = Description.PolygonGroupAttributes();
		PolygonGroups.SetAttribute<FName>(PolygonGroupID, MeshAttribute::PolygonGroup::ImportedMaterialSlotName, 0, Section.SectionName);
	}
	return true;
}

void FGeometryEditLibrary::GenerateMeshFromTriangles(const TArray<FPMCTriangle>& InTriangles, FPMCSection& Mesh)
{
	{
		const int32 IndexCount = InTriangles.Num() * 3;
		Mesh.Vertexes.AddDefaulted(IndexCount);
		Mesh.UV.AddDefaulted(IndexCount);
		Mesh.Triangles.AddDefaulted(IndexCount);
		Mesh.Normals.AddDefaulted(IndexCount);
		Mesh.Tangents.AddDefaulted(IndexCount);
	}
	int32 TriangleIndex = 0;
	TMap<int32, int32> IndexMap;
	for (const auto& Triangle : InTriangles)
	{
		const int32 Index1 = IndexMap.Contains(Triangle.Vertexes[0].VertexIndex) ? IndexMap[Triangle.Vertexes[0].VertexIndex] : (IndexMap.Add(Triangle.Vertexes[0].VertexIndex, IndexMap.Num()), IndexMap[Triangle.Vertexes[0].VertexIndex]);
		const int32 Index2 = IndexMap.Contains(Triangle.Vertexes[1].VertexIndex) ? IndexMap[Triangle.Vertexes[1].VertexIndex] : (IndexMap.Add(Triangle.Vertexes[1].VertexIndex, IndexMap.Num()), IndexMap[Triangle.Vertexes[1].VertexIndex]);
		const int32 Index3 = IndexMap.Contains(Triangle.Vertexes[2].VertexIndex) ? IndexMap[Triangle.Vertexes[2].VertexIndex] : (IndexMap.Add(Triangle.Vertexes[2].VertexIndex, IndexMap.Num()), IndexMap[Triangle.Vertexes[2].VertexIndex]);

		Mesh.Vertexes[Index1] = Triangle.Vertexes[0].Pos;
		Mesh.Vertexes[Index2] = Triangle.Vertexes[1].Pos;
		Mesh.Vertexes[Index3] = Triangle.Vertexes[2].Pos;

		Mesh.UV[Index1] = Triangle.Vertexes[0].UV;
		Mesh.UV[Index2] = Triangle.Vertexes[1].UV;
		Mesh.UV[Index3] = Triangle.Vertexes[2].UV;

		Mesh.Normals[Index1] = Triangle.Vertexes[0].Normal;
		Mesh.Normals[Index2] = Triangle.Vertexes[1].Normal;
		Mesh.Normals[Index3] = Triangle.Vertexes[2].Normal;

		Mesh.Tangents[Index1] = Triangle.Vertexes[0].Tangent;
		Mesh.Tangents[Index2] = Triangle.Vertexes[1].Tangent;
		Mesh.Tangents[Index3] = Triangle.Vertexes[2].Tangent;

		const int32 BaseIndex = TriangleIndex * 3;
		Mesh.Triangles[BaseIndex] = Index1;
		Mesh.Triangles[BaseIndex + 1] = Index2;
		Mesh.Triangles[BaseIndex + 2] = Index3;
		++TriangleIndex;
	}
	if (IndexMap.Num() < Mesh.Vertexes.Num())
	{
		const int32 VertexCount = Mesh.Vertexes.Num() - IndexMap.Num();
		Mesh.Vertexes.RemoveAt(IndexMap.Num(), VertexCount);
		Mesh.UV.RemoveAt(IndexMap.Num(), VertexCount);
		Mesh.Normals.RemoveAt(IndexMap.Num(), VertexCount);
		Mesh.Tangents.RemoveAt(IndexMap.Num(), VertexCount);
	}
}

void FGeometryEditLibrary::GenerateMeshFromTriangles(const TArray<FVector>& InVertexes, const TArray<int32>& InTriangles, const FVector& InNormal, const FVector& InTangentX, FPMCSection& Mesh)
{
	if (InVertexes.Num() < 3 || InTriangles.Num() < 3) return;
	if (0 != (InTriangles.Num() % 3)) return;
	{
		Mesh.Vertexes = InVertexes;
		Mesh.Triangles = InTriangles;
		Mesh.Normals.Init(InNormal, InVertexes.Num());
		FProcMeshTangent Tangent;
		Tangent.TangentX = InTangentX;
		Tangent.bFlipTangentY = false;
		Mesh.Tangents.Init(Tangent, InVertexes.Num());
	
		FVector TangentY = InNormal ^ InTangentX.GetSafeNormal();
		
		FVector2D UVMin(BIG_NUMBER, BIG_NUMBER);
		Mesh.UV.Init(FVector2D::ZeroVector, InVertexes.Num());
		const auto& UVZeroPoint = InVertexes[0];

		if (InTangentX == FVector::XAxisVector && TangentY.Z > 0)
		{
			TangentY = -TangentY;
		}
		else if (InTangentX == FVector::YAxisVector && TangentY.Z >  0)
		{
			TangentY = -TangentY;
			Tangent.TangentX = -Tangent.TangentX;
		}
		for (int32 Index = 0; Index < InVertexes.Num(); ++Index)
		{
			auto& Vertex = InVertexes[Index];
			Mesh.UV[Index].X = (Vertex | Tangent.TangentX) * 0.01f;
			Mesh.UV[Index].Y = (Vertex | TangentY) * 0.01f;

			UVMin.X = FMath::Min(Mesh.UV[Index].X, UVMin.X);
		}
		for (int32 Index = 0; Index < InVertexes.Num(); ++Index)
		{
			Mesh.UV[Index].X -= UVMin.X;
		}

	}
}

//bool FGeometryEditLibrary::ConvertLineSegmentToRadiusArc(const TPair<FVector, FVector>& LineSegment, const FVector& Normal, const float& Radius, bool BigArc, TArray<FVector>& ArcPoints)
//{
//	const FVector LineDir = LineSegment.Value - LineSegment.Key;
//	const float RadiusSquare = Radius * Radius;
//	const float HalfLineLength = LineDir.SizeSquared() / 4.0f;
//	if (RadiusSquare < HalfLineLength) return false;
//	const float DeltaD = FMath::Sqrt(RadiusSquare - HalfLineLength);
//	FVector OutsideDir = (Normal ^ LineDir).GetSafeNormal();
//	FVector RadiusCenter = -1.0f * OutsideDir * DeltaD + (LineSegment.Value - LineSegment.Key);
//	return true;
//}
