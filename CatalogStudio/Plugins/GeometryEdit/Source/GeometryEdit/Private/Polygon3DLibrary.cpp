// Copyright Epic Games, Inc. All Rights Reserved.

#include "Polygon3DLibrary.h"
#include "GeometryEditLibrary.h"
#include "GeometryEdit/MeshTriangulator/MeshTriangulator.h"

bool FPolygon3DLibrary::IsPolygonCCWWinding(const TArray<FVector>& InPolygon, const FVector& InNormal)
{
	if (InPolygon.Num() <= 2) return true;
	TArray<FVector2D> Polygon2D;
	const int32 PointCount = InPolygon.Num();
	{
		Polygon2D.Init(FVector2D::ZeroVector, InPolygon.Num());
		const FVector OriginalPoint = InPolygon[0];
		const FVector Forward = (InPolygon[1] - InPolygon[0]).GetSafeNormal();
		const FVector Right = (InNormal ^ Forward).GetSafeNormal();
		for (int32 i = 1; i < PointCount; ++i)
		{
			const FVector Dir = InPolygon[i] - OriginalPoint;
			Polygon2D[i].X = Forward | Dir;
			Polygon2D[i].Y = Right | Dir;
		}
	}
	float Sum = 0.0f;
	for (int PointIndex = 0; PointIndex < PointCount; ++PointIndex)
	{
		const auto& A = Polygon2D[PointIndex];
		const auto& B = Polygon2D[(PointIndex + 1) % PointCount];
		Sum += (B.X - A.X) * (B.Y + A.Y);
	}
	return (Sum < 0.0f);
}

bool FPolygon3DLibrary::IsConvexVertex(const FVector& TestPoint, const FVector& PrePoint, const FVector& NextPoint, const FVector& PolygonNormal, bool PolygonCCW)
{
	const float ConvexSign = PolygonCCW ? 1.0f : -1.0f;
	const FVector Normal = (TestPoint - PrePoint) ^ (NextPoint - TestPoint);
	const bool bConvex = ConvexSign * (Normal | PolygonNormal) > 0.0f;
	return bConvex;
}

bool FPolygon3DLibrary::TriangulatePolygon2D(const TArray<FVector>& InPolygon2D, const FVector& InPolygonNormal ,TArray<int32>& Triangles)
{
	TArray<int32> IndexMap;
	bool bCCW = true;
	{
		TArray<FVector> Polygon2D = InPolygon2D;
		FPolygon3DLibrary::RemoveConcurrentPoints(Polygon2D, true);
		bCCW = FPolygon3DLibrary::IsPolygonCCWWinding(Polygon2D, InPolygonNormal);

		IndexMap.Init(0, InPolygon2D.Num());
		for (int32 i = 1; i < InPolygon2D.Num(); ++i)
			IndexMap[i] = i;
	}
	const float ConvexSign = bCCW ? 1.0f : -1.0f;
	int64 InvalidVertexCount = 0;
	while (IndexMap.Num() >= 3)
	{
		for (int32 i = 0; i < IndexMap.Num(); ++i)
		{
			const int32& PreIndex = IndexMap[(i - 1 + IndexMap.Num()) % IndexMap.Num()];
			const int32& TestIndex = IndexMap[i];
			const int32& NextIndex = IndexMap[(i + 1) % IndexMap.Num()];
			const FVector Normal = (InPolygon2D[TestIndex] - InPolygon2D[PreIndex]) ^ (InPolygon2D[NextIndex] - InPolygon2D[TestIndex]);
			UE_LOG(LogTemp, Log, TEXT("X=%3.6f Y=%3.6f Z=%3.6f"), Normal.X, Normal.Y, Normal.Z);
			if (Normal.IsNearlyZero(0.001f))
			{//点相等或者平行
				//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::TriangulatePolygon2D point equal or parallel"));
				IndexMap.RemoveAt(i);
				InvalidVertexCount = 0;
				if (IndexMap.Num() < 3) return false;
				continue;
			}
			const bool bConvex = ConvexSign * (Normal | InPolygonNormal) > 0.0f;
			//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::TriangulatePolygon2D (Normal | InPolygonNormal)=%f"), (Normal | InPolygonNormal));
			//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::TriangulatePolygon2D PreIndex=%d TestIndex=%d NextIndex=%d"), PreIndex, TestIndex, NextIndex);
			if (3 == IndexMap.Num())
			{
				int32 Offset = Triangles.AddDefaulted(3);
				Triangles[Offset] = bCCW ? NextIndex : PreIndex;
				Triangles[++Offset] = TestIndex;
				Triangles[++Offset] = bCCW ? PreIndex : NextIndex;
				return true;
			}
			if (!bConvex)
			{
				++InvalidVertexCount;
				if (InvalidVertexCount >= IndexMap.Num()) return true;
				continue;//
			}
			const int32 LastVertexCount = IndexMap.Num() - 3;
			//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::TriangulatePolygon2D LastVertexCount=%d"), LastVertexCount);
			bool bValidVertex = true;
			for (int32 j = 1; j <= LastVertexCount; ++j)
			{
				const int32& OtherIndex = IndexMap[(i + 1 + j) % IndexMap.Num()];
				EPointTriangleRelationship Relationship = FPolygon3DLibrary::IsPointInTriangle(InPolygon2D[PreIndex], InPolygon2D[TestIndex], InPolygon2D[NextIndex], InPolygon2D[OtherIndex]);
				if (EPointTriangleRelationship::VecOutside != Relationship)
				{
					bValidVertex = false;
					break;
				}
			}
			if (!bValidVertex)
			{
				++InvalidVertexCount;
				if (InvalidVertexCount >= IndexMap.Num()) return true;
				continue;//
			}
			InvalidVertexCount = 0;
			int32 Offset = Triangles.AddDefaulted(3);
			Triangles[Offset] = bCCW ? NextIndex : PreIndex;
			Triangles[++Offset] = TestIndex;
			Triangles[++Offset] = bCCW ? PreIndex : NextIndex;
			IndexMap.RemoveAt(i);
		}
	}
	UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::TriangulatePolygon2D finish"));
	return true;
}

bool FPolygon3DLibrary::TriangulatePolygon2D(const TArray<FVector>& InPolygon2D, const FVector& InPolygonNormal, TArray<FVector>& OutPolygon2D, TArray<int32>& Triangles)
{
	TArray <TArray<FVector>> insertPoints;
	TArray<TArray<int32>> outIndex;
	bool Res = FMeshTriangulator::delaunayTrangleWithBorder(InPolygon2D, insertPoints, InPolygonNormal, OutPolygon2D, outIndex);
	if (!Res)
	{
		return false;
	}
	for (const auto& iter : outIndex)
	{
		Triangles.Append(iter);
	}
	return Res;
}

EPointTriangleRelationship FPolygon3DLibrary::IsPointInTriangle(const FVector& A, const FVector& B, const FVector& C, const FVector& TestPoint)
{
	//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::IsPointInTriangle A=%s B=%s C=%s P=%s"), *A.ToString(), *B.ToString(), *C.ToString(), *TestPoint.ToString());
	FVector TriangleVertices[3];
	TriangleVertices[0] = A;
	TriangleVertices[1] = B;
	TriangleVertices[2] = C;
	float AngleSum = 0.0f;
	for (int PointIndex = 0; PointIndex < 3; ++PointIndex)
	{
		const FVector& VecAB = TriangleVertices[PointIndex] - TestPoint;
		const int32 NextPointIndex = (PointIndex + 1) % 3;
		const FVector& VecAC = TriangleVertices[NextPointIndex] - TestPoint;
		const float SizeAB = VecAB.Size();
		const float SizeAC = VecAC.Size();
		const float SizeBC = FVector::Distance(TriangleVertices[PointIndex], TriangleVertices[NextPointIndex]);
		if (FMath::IsNearlyEqual(SizeBC, SizeAC + SizeAB, THRESH_SPLIT_POLY_PRECISELY))
		{
			if (FMath::IsNearlyZero(SizeAB, THRESH_SPLIT_POLY_PRECISELY)) return EPointTriangleRelationship::EqualWithVertice;
			if (!FMath::IsNearlyZero(SizeAC, THRESH_SPLIT_POLY_PRECISELY)) return EPointTriangleRelationship::WithinEdge;
			return EPointTriangleRelationship::EqualWithVertice;
		}
		AngleSum += FMath::Acos(FVector::DotProduct(VecAB, VecAC) / (SizeAB * SizeAC));
	}
	//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::IsPointInTriangle AngleSum=%f "), AngleSum);
	return (FMath::Abs(AngleSum - 2 * PI) <= 0.001f) ? EPointTriangleRelationship::	VecInside : EPointTriangleRelationship::VecOutside;
}

EPointTriangleRelationship FPolygon3DLibrary::IsPointInTriangle(const FVector& A, const FVector& B, const FVector& C, const FVector& TestPoint, int32& Index)
{
	//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::IsPointInTriangle A=%s B=%s C=%s P=%s"), *A.ToString(), *B.ToString(), *C.ToString(), *TestPoint.ToString());
	FVector TriangleVertices[3] = { A,B,C };
	float AngleSum = 0.0f;
	for (int PointIndex = 0; PointIndex < 3; ++PointIndex)
	{
		const FVector& VecAB = TriangleVertices[PointIndex] - TestPoint;
		const int32 NextPointIndex = (PointIndex + 1) % 3;
		const FVector& VecAC = TriangleVertices[NextPointIndex] - TestPoint;
		const float SizeAB = VecAB.Size();
		const float SizeAC = VecAC.Size();
		const float SizeBC = FVector::Distance(TriangleVertices[PointIndex], TriangleVertices[NextPointIndex]);
		if (FMath::IsNearlyEqual(SizeBC, SizeAC + SizeAB, THRESH_SPLIT_POLY_PRECISELY))
		{
			Index = PointIndex;
			if (FMath::IsNearlyZero(SizeAB, THRESH_SPLIT_POLY_PRECISELY)) return EPointTriangleRelationship::EqualWithVertice;
			if (!FMath::IsNearlyZero(SizeAC, THRESH_SPLIT_POLY_PRECISELY)) return EPointTriangleRelationship::WithinEdge;
			return EPointTriangleRelationship::EqualWithVertice;
		}
		AngleSum += FMath::Acos(FVector::DotProduct(VecAB, VecAC) / (SizeAB * SizeAC));
	}
	Index = INDEX_NONE;
	//UE_LOG(LogTemp, Log, TEXT("FPolygon3DLibrary::IsPointInTriangle AngleSum=%f "), AngleSum);
	return (FMath::Abs(AngleSum - 2 * PI) <= 0.001f) ? EPointTriangleRelationship::VecInside : EPointTriangleRelationship::VecOutside;
}

float FPolygon3DLibrary::TriangleArea(const FVector& A, const FVector& B, const FVector& C)
{
	const FVector Cross = (B - A) ^ (C - A);
	return Cross.Size() * 0.5f;
}

void FPolygon3DLibrary::ProjectPolygonToPlane(const TArray<FVector>& InPolygon, const FVector& InPolygonNormal, const FPlane& InPlane, TArray<FVector>& ProjectPolygon)
{
	if (InPolygon.Num() < 1) return;
	const int32 PointCount = InPolygon.Num();
	ProjectPolygon.Init(FVector::ZeroVector, PointCount);
	for (int32 i = 0; i < PointCount; ++i)
		ProjectPolygon[i] = FMath::RayPlaneIntersection(InPolygon[i], InPolygonNormal, InPlane);
}

bool FPolygon3DLibrary::IsPointInPolygon(const TArray<FVector>& Polygon, const FVector& InNormal, const FVector& InPoint)
{
	const int32 PointCount = Polygon.Num();
	if (PointCount < 3) return false;
	const FVector Ray = (Polygon[1] - Polygon[0]).GetSafeNormal();
	TArray<float> PointDot = TArray<float>();
	{
		const FVector RayNormal = (InNormal ^ Ray).GetSafeNormal();
		const FPlane Plane(InPoint, RayNormal);
		PointDot.Init(0.0f, PointCount);
		for (int32 i = 0; i < PointCount; ++i)
			PointDot[i] = Plane.PlaneDot(Polygon[i]);
	}
	int32 CrossTime = 0;
	int32 PositiveTime = 0;
	int32 NegativeTime = 0;
	for (int32 i = 0; i < PointCount; ++i)
	{
		const int32 Next = (i + 1) % PointCount;
		const FVector ThisPoint = Polygon[i] - InPoint;
		const FVector NextPoint = Polygon[Next] - InPoint;
		if ((ThisPoint | Ray) <= 0.0f && (NextPoint | Ray) <= 0.0f) continue;
		if (FMath::IsNearlyEqual(PointDot[i], PointDot[Next], THRESH_SPLIT_POLY_PRECISELY)) continue;
		//UE_LOG(LogTemp, Log, TEXT("PointDot[%d]=%f PointDot[%d]=%f"), i, PointDot[i], Next, PointDot[Next]);
		{
			const float Alpha = FMath::Abs(PointDot[i] / (PointDot[i] - PointDot[Next]));
			const FVector CrossPoint = FMath::Lerp(Polygon[i], Polygon[Next], Alpha);
			if (((CrossPoint - InPoint) | Ray) <= 0.0f) continue;
		}
		const bool PointEqual = FMath::Abs(PointDot[i]) <= THRESH_SPLIT_POLY_PRECISELY;
		const bool NextPointEqual = FMath::Abs(PointDot[Next]) <= THRESH_SPLIT_POLY_PRECISELY;
		if (PointEqual && !NextPointEqual)
		{
			FMath::IsNegativeFloat(PointDot[Next]) ? ++NegativeTime : ++PositiveTime;
			continue;
		}
		if (!PointEqual && NextPointEqual)
		{
			FMath::IsNegativeFloat(PointDot[i]) ? ++NegativeTime : ++PositiveTime;
			continue;
		}
		if (!PointEqual && !NextPointEqual && (PointDot[i] * PointDot[Next] < 0.0f))
		{
			++CrossTime;
		}
	}
	return 1 == (FMath::Min(NegativeTime, PositiveTime) + CrossTime) % 2;
}

void FPolygon3DLibrary::RemoveConcurrentPoints(TArray<FVector>& InPolygon, bool IsLoop)
{
	if (InPolygon.Num() <= 2) return;
	TArray<FVector> Result = InPolygon;
	for (int32 i = InPolygon.Num() - 1; i >= 0; --i)
	{
		if (1 == i && !IsLoop) break;
		const int32 Test = (i - 1 + Result.Num()) % Result.Num();
		const int32 Next = (Test - 1 + Result.Num()) % Result.Num();
		const auto& A = Result[i];
		const auto& B = Result[Test];
		const auto& C = Result[Next];
		if (A.Equals(B, 0.000000001f) || C.Equals(B, 0.000000001f))
		{
			Result.RemoveAt(Test);
		}
		else
		{
			auto BA = (A - B).GetSafeNormal();
			auto BC = (C - B).GetSafeNormal();
			if (FVector::Parallel(BA, BC, THRESH_NORMALS_ARE_PARALLEL)) Result.RemoveAt(Test);
		}
		if (Result.Num() <= 2) break;
	}
	InPolygon = Result;
}

void FPolygon3DLibrary::RemoveCoincidencePoints(TArray<FVector>& InPolygon, bool IsLoop)
{
	if (InPolygon.Num() <= 2) return;
	TArray<FVector> Result = InPolygon;
	for (int32 i = InPolygon.Num() - 1; i >= 0; --i)
	{
		if (1 == i && !IsLoop) break;
		const int32 Test = (i - 1 + Result.Num()) % Result.Num();
		const int32 Next = (Test - 1 + Result.Num()) % Result.Num();
		const auto& A = Result[i];
		const auto& B = Result[Test];
		const auto& C = Result[Next];
		if (A.Equals(B, THRESH_SPLIT_POLY_PRECISELY) || C.Equals(B, THRESH_SPLIT_POLY_PRECISELY))
		{
			Result.RemoveAt(Test);
		}
		if (Result.Num() <= 2) break;
	}
	InPolygon = Result;
}

FBox FPolygon3DLibrary::PolygonBoundingBox(const TArray<FVector>& InPolygon)
{
	FBox Bound(ForceInit);
	for (auto& Point : InPolygon)
		Bound += Point;
	return Bound;
}

void FPolygon3DLibrary::GenerateMeshFromPolygon2D(const TArray<FVector>& InPolygon, const FVector& InNormal, const FVector& InTangentX, const FVector& InUVOriginPoint, const float& UVRateInCM, FPMCSection& Mesh)
{
	Mesh.Vertexes = InPolygon;
	FPolygon3DLibrary::RemoveConcurrentPoints(Mesh.Vertexes, true);
	FPolygon3DLibrary::TriangulatePolygon2D(Mesh.Vertexes, InNormal, Mesh.Triangles);
	const int32 VertexCount = Mesh.Vertexes.Num();
	FPolygon3DLibrary::CalculateVertexesUV(Mesh.Vertexes, InNormal, InTangentX, InUVOriginPoint, UVRateInCM, Mesh.UV);
	Mesh.Normals.Init(InNormal, VertexCount);
	Mesh.Tangents.Init(FProcMeshTangent(InTangentX, false), VertexCount);
}

void FPolygon3DLibrary::GenerateMeshFromPolygon2D(const TArray<FVector>& InPolygon, const FVector& InNormal, const FVector& InTangentX, FPMCSection& Mesh)
{
	TArray<FVector> TempPolygon = InPolygon;
	FPolygon3DLibrary::RemoveConcurrentPoints(TempPolygon, true);
	FPolygon3DLibrary::TriangulatePolygon2D(TempPolygon, InNormal, Mesh.Vertexes, Mesh.Triangles);
	FGeometryEditLibrary::GenerateMeshFromTriangles(Mesh.Vertexes, Mesh.Triangles, InNormal, InTangentX, Mesh);
}

void FPolygon3DLibrary::CalculateVertexesUV(const TArray<FVector>& InVertexes, const FVector& InNormal, const FVector& InTangentX, const FVector& InUVOriginPoint, const float& UVRateInCM, TArray<FVector2D>& OutUV)
{
	const int32 VertexCount = InVertexes.Num();
	OutUV.Init(FVector2D::ZeroVector, VertexCount);
	const FVector TangentY = (InNormal ^ InTangentX).GetSafeNormal();
	for (int32 i = 0; i < VertexCount; ++i)
	{
		const FVector DeltaPos = InVertexes[i] - InUVOriginPoint;
		OutUV[i].X = (DeltaPos | InTangentX) / UVRateInCM;
		OutUV[i].Y = (DeltaPos | TangentY) / UVRateInCM;
	}
}