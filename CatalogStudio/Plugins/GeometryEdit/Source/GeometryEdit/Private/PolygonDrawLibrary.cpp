// Copyright Epic Games, Inc. All Rights Reserved.

#include "PolygonDrawLibrary.h"
#include "GeometryEdit.h"
#include "Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"

bool FPolygonDrawLibrary::DrawPolygonTriangles(TArray<FPMCTriangle>& OriginTriangles, const FVector& DrawDir, const float& UVRateInCM, TArray<FPMCTriangle>& TrianglesDrew, TArray<FPMCTriangle>& CapTriangles)
{
	if (OriginTriangles.Num() < 1) return true;
	TArray<FPMCVertex> Vertexes = TArray<FPMCVertex>();
	TArray<TPair<int32, int32>> OutlinePoints;
	FPMCTriangle::ReindexTriangleVertex(OriginTriangles, Vertexes);
	TrianglesDrew = OriginTriangles;
	FPMCTriangle::FindOutlineEdges(OriginTriangles, OutlinePoints);
	TArray<FPMCVertex> DrewVertexes = Vertexes;
	const FVector TriangleNormal = OriginTriangles[0].GetTriangleNormal();
	{
		for (int32 i = 0; i < DrewVertexes.Num(); ++i)
			DrewVertexes[i].Pos += DrawDir;
		for (int32 i = 0; i < TrianglesDrew.Num(); ++i)
		{
			auto& EditTriangle = TrianglesDrew[i];
			for (int32 j = 0; j < 3; ++j)
			{
				auto& EditVertex = EditTriangle.Vertexes[j];
				EditVertex.Pos = DrewVertexes[EditVertex.VertexIndex].Pos;
				EditVertex.VertexIndex += Vertexes.Num();
			}
		}
		const float DrawWithSameDir = TriangleNormal | DrawDir;
		auto& ReverseTriangles = FMath::IsNegativeFloat(DrawWithSameDir) ? TrianglesDrew : OriginTriangles;
		for (int32 i = 0; i < ReverseTriangles.Num(); ++i)
			ReverseTriangles[i].ReverseTriangleWinding();
	}
	{
		CapTriangles.AddDefaulted(OutlinePoints.Num() * 2);
		for (int32 i = 0; i < OutlinePoints.Num(); ++i)
		{
			const auto& Edge = OutlinePoints[i];
			TArray<FPMCVertex> ParallelogramVertexes;
			{
				const FVector TangentX = (Vertexes[Edge.Value].Pos - Vertexes[Edge.Key].Pos).GetSafeNormal();
				const FVector Normal = (TriangleNormal ^ TangentX).GetSafeNormal();
				const FVector TangnetY = (Normal ^ TangentX).GetSafeNormal();
				TArray<FVector> Parallelogram;
				Parallelogram.Init(FVector::ZeroVector, 4);
				Parallelogram[0] = Vertexes[Edge.Key].Pos;
				Parallelogram[1] = Vertexes[Edge.Value].Pos;
				Parallelogram[2] = DrewVertexes[Edge.Value].Pos;
				Parallelogram[3] = DrewVertexes[Edge.Key].Pos;
				bool bIsCCW = FPolygon3DLibrary::IsPolygonCCWWinding(Parallelogram, Normal);
				UE_LOG(LogTemp, Log, TEXT("FPolygonDrawLibrary::DrawPolygonTriangles Normal=%s bIsCCW=%d"), *Normal.ToString(), bIsCCW);
				if (bIsCCW)
					FArrayOperatorLibrary::ReverseArray<FVector>(Parallelogram);

				ParallelogramVertexes.Init(FPMCVertex(), 4);
				const int32 BaseVertexIndex = (i << 2) + (Vertexes.Num() << 1);
				for (int32 VertexIndex = 0; VertexIndex < 4; ++VertexIndex)
				{
					ParallelogramVertexes[VertexIndex].Pos = Parallelogram[VertexIndex];
					ParallelogramVertexes[VertexIndex].Normal = Normal;
					ParallelogramVertexes[VertexIndex].Tangent.TangentX = TangentX;
					auto Delta = (Parallelogram[VertexIndex] - Vertexes[Edge.Key].Pos);
					ParallelogramVertexes[VertexIndex].UV = FVector2D((Delta | TangentX) / UVRateInCM, (Delta | TangnetY) / UVRateInCM);
					ParallelogramVertexes[VertexIndex].VertexIndex = BaseVertexIndex + VertexIndex;
				}
			}
			const int32 BaseTriangleIndex = i << 1;
			for (int32 j = 0; j < 2; ++j)
			{
				auto& EditTriangle = CapTriangles[BaseTriangleIndex + j];
				EditTriangle.Vertexes[0] = ParallelogramVertexes[0];
				EditTriangle.Vertexes[1] = ParallelogramVertexes[j + 1];
				EditTriangle.Vertexes[2] = ParallelogramVertexes[j + 2];
			}
		}
	}
	return true;
}
