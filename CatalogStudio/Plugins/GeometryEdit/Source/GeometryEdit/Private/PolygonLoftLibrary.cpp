// Copyright Epic Games, Inc. All Rights Reserved.

#include "PolygonLoftLibrary.h"
#include "GeometryEdit.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "Polygon3DLibrary.h"
#include "GeometryEdit/Data/PolygonPoint.h"


bool FPolygonLoftLibrary::GenerateLoftMesh(const FLoftRoutine& InRoutine, const FLoftSection& InSection, FPMCSection& Mesh)
{
	const float& UVRateInCM = InSection.GetUVRateInCM();
	TArray<FLoftSection> NewSections = TArray<FLoftSection>();
	const auto& RoutinePoits = InRoutine.GetRoutinePoints();
	if (RoutinePoits.Num() < 1) return false;
	{
		TArray<FVector> RoutineNormals = TArray<FVector>();
		bool Res = InRoutine.CalculateRoutineOutsideNormal(RoutineNormals);
		if (!Res) return false;
		NewSections.Init(FLoftSection(), RoutineNormals.Num());
		int32 i = 0;

		for (const auto& Normal : RoutineNormals)
		{
			Res = InSection.TransformSection(Normal.GetSafeNormal(), InRoutine.GetPlaneNormal(), Normal.Size(), NewSections[i]);
			if (!Res) return false;
			NewSections[i].TranslateSection(RoutinePoits[i]);
			++i;
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::RemoveParallel IsValidSection=%d"), NewSections.Num());
	}
	const int32 RoutineCount = InRoutine.IsLoop() ? RoutinePoits.Num() : (RoutinePoits.Num() - 1);
	auto& SectionPoints = InSection.GetSectionPoints();
	if (SectionPoints.Num() < 2) return false;
	const int32 SectionPointCount = SectionPoints.Num() - 1;
	{
		const int32 VertexCount = (SectionPointCount << 2) * RoutineCount;
		Mesh.Vertexes.Init(FVector::ZeroVector, VertexCount);
		Mesh.UV.Init(FVector2D::ZeroVector, VertexCount);
		Mesh.Normals.Init(FVector::ZeroVector, VertexCount);
		Mesh.Tangents.Init(FProcMeshTangent(), VertexCount);
		Mesh.Triangles.Init(0, SectionPointCount * RoutineCount * 6);
	}
	const auto& SectionUV = InSection.GetSectionPointsUV();
	for (int32 i = 0; i < RoutineCount; ++i)
	{
		const int32 NextRoutinePoint = (i + 1) % RoutinePoits.Num();
		const FVector Tangent = (RoutinePoits[NextRoutinePoint] - RoutinePoits[i]).GetSafeNormal();
		const auto& CurrentSection = NewSections[i].GetSectionPoints();
		const auto& NextSection = NewSections[NextRoutinePoint].GetSectionPoints();
		for (int32 j = 0; j < SectionPointCount; ++j)
		{
			int32 Offset = i * (SectionPointCount << 2) + (j << 2);
			const int32 NextSectionPoint = (j + 1) % SectionPoints.Num();
			const int32 P1 = Offset;
			const int32 P2 = ++Offset;
			const int32 P3 = ++Offset;
			const int32 P4 = ++Offset;
			Mesh.Vertexes[P1] = NextSection[j];
			Mesh.Vertexes[P2] = CurrentSection[j];
			Mesh.Vertexes[P3] = CurrentSection[NextSectionPoint];
			Mesh.Vertexes[P4] = NextSection[NextSectionPoint];
			Mesh.Tangents[P4].TangentX = Mesh.Tangents[P3].TangentX = Mesh.Tangents[P2].TangentX = Mesh.Tangents[P1].TangentX = Tangent;
			{
				Mesh.UV[P1].X = (Tangent | (Mesh.Vertexes[P1] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P2].X = (Tangent | (Mesh.Vertexes[P2] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P3].X = (Tangent | (Mesh.Vertexes[P3] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P4].X = (Tangent | (Mesh.Vertexes[P4] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P2].Y = Mesh.UV[P1].Y = SectionUV[j].Y;
				Mesh.UV[P4].Y = Mesh.UV[P3].Y = SectionUV[NextSectionPoint].Y;
			}
			const FVector Normal = (Tangent ^ (CurrentSection[NextSectionPoint] - CurrentSection[j])).GetSafeNormal();
			Mesh.Normals[P4] = Mesh.Normals[P3] = Mesh.Normals[P2] = Mesh.Normals[P1] = Normal;
			Offset = (i * SectionPointCount + j) * 6;
			Mesh.Triangles[Offset] = P1;
			Mesh.Triangles[++Offset] = P2;
			Mesh.Triangles[++Offset] = P3;

			Mesh.Triangles[++Offset] = P1;
			Mesh.Triangles[++Offset] = P3;
			Mesh.Triangles[++Offset] = P4;
		}
	}
	if (InRoutine.IsLoop()) return true;
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? FVector::ForwardVector : (RoutinePoits[0] - RoutinePoits[1]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[0].GetSectionPoints();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME)) EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.AddDefaulted(EndSectionPoints.Num());
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i];
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[0])) / UVRateInCM;
			Mesh.UV[Index].X = SectionUV[i].X;
		}
		TArray<int32> Triangles = TArray<int32>();
		FPolygon3DLibrary::TriangulatePolygon2D(EndSectionPoints, SectionNormal, Triangles);
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? -FVector::ForwardVector : (RoutinePoits[RoutinePoits.Num() - 1] - RoutinePoits[RoutinePoits.Num() - 2]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[NewSections.Num() - 1].GetSectionPoints();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME)) EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.AddDefaulted(EndSectionPoints.Num());
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i];
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[RoutinePoits.Num() - 1])) / UVRateInCM;
			Mesh.UV[Index].X = SectionUV[i].X;
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::GenerateLoftMesh SectionNormal=%s"), *SectionNormal.ToString());
		TArray<int32> Triangles = TArray<int32>();
		FPolygon3DLibrary::TriangulatePolygon2D(EndSectionPoints, SectionNormal, Triangles);
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	return true;
}

bool FPolygonLoftLibrary::GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, const FLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines)
{
	const float& UVRateInCM = InSection.GetUVRateInCM();
	TArray<FLoftSection> NewSections = TArray<FLoftSection>();
	const auto& RoutinePoits = InRoutine.GetRoutinePoints();
	if (RoutinePoits.Num() < 1) return false;
	{
		TArray<FVector> RoutineNormals = TArray<FVector>();
		bool Res = InRoutine.CalculateRoutineOutsideNormal(RoutineNormals);
		if (!Res) return false;
		NewSections.Init(FLoftSection(), RoutineNormals.Num());
		int32 i = 0;

		for (const auto& Normal : RoutineNormals)
		{
			Res = InSection.TransformSection(Normal.GetSafeNormal(), InRoutine.GetPlaneNormal(), Normal.Size(), NewSections[i]);
			if (!Res) return false;
			NewSections[i].TranslateSection(RoutinePoits[i]);
			++i;
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::RemoveParallel IsValidSection=%d"), NewSections.Num());
	}
	const int32 RoutineCount = InRoutine.IsLoop() ? RoutinePoits.Num() : (RoutinePoits.Num() - 1);
	const TArray<FVector>& SectionPoints = InSection.GetSectionPoints();
	if (SectionPoints.Num() < 2) return false;
	const int32 SectionPointCount = SectionPoints.Num() - 1;
	{
		const int32 VertexCount = (SectionPointCount << 2) * RoutineCount;
		Mesh.Vertexes.Init(FVector::ZeroVector, VertexCount);
		Mesh.UV.Init(FVector2D::ZeroVector, VertexCount);
		Mesh.Normals.Init(FVector::ZeroVector, VertexCount);
		Mesh.Tangents.Init(FProcMeshTangent(), VertexCount);
		Mesh.Triangles.Init(0, SectionPointCount * RoutineCount * 6);
		Outlines.Init(TPair<FVector, FVector>(FVector::ZeroVector, FVector::ZeroVector), SectionPointCount * RoutineCount);
	}
	const auto& SectionUV = InSection.GetSectionPointsUV();
	for (int32 i = 0; i < RoutineCount; ++i)
	{
		const int32 NextRoutinePoint = (i + 1) % RoutinePoits.Num();
		const FVector Tangent = (RoutinePoits[NextRoutinePoint] - RoutinePoits[i]).GetSafeNormal();
		const auto& CurrentSection = NewSections[i].GetSectionPoints();
		const auto& NextSection = NewSections[NextRoutinePoint].GetSectionPoints();
		for (int32 j = 0; j < SectionPointCount; ++j)
		{
			int32 Offset = i * (SectionPointCount << 2) + (j << 2);
			const int32 NextSectionPoint = (j + 1) % SectionPoints.Num();
			const int32 P1 = Offset;
			const int32 P2 = ++Offset;
			const int32 P3 = ++Offset;
			const int32 P4 = ++Offset;
			Mesh.Vertexes[P1] = NextSection[j];
			Mesh.Vertexes[P2] = CurrentSection[j];
			Mesh.Vertexes[P3] = CurrentSection[NextSectionPoint];
			Mesh.Vertexes[P4] = NextSection[NextSectionPoint];
			Mesh.Tangents[P4].TangentX = Mesh.Tangents[P3].TangentX = Mesh.Tangents[P2].TangentX = Mesh.Tangents[P1].TangentX = Tangent;
			{
				Mesh.UV[P1].X = (Tangent | (Mesh.Vertexes[P1] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P2].X = (Tangent | (Mesh.Vertexes[P2] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P3].X = (Tangent | (Mesh.Vertexes[P3] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P4].X = (Tangent | (Mesh.Vertexes[P4] - CurrentSection[0])) / UVRateInCM;
				Mesh.UV[P2].Y = Mesh.UV[P1].Y = SectionUV[j].Y;
				Mesh.UV[P4].Y = Mesh.UV[P3].Y = SectionUV[NextSectionPoint].Y;
			}
			const FVector Normal = (Tangent ^ (CurrentSection[NextSectionPoint] - CurrentSection[j])).GetSafeNormal();
			Mesh.Normals[P4] = Mesh.Normals[P3] = Mesh.Normals[P2] = Mesh.Normals[P1] = Normal;
			const int32 EdgeIndex = i * SectionPointCount + j;
			Offset = EdgeIndex * 6;
			Mesh.Triangles[Offset] = P1;
			Mesh.Triangles[++Offset] = P2;
			Mesh.Triangles[++Offset] = P3;

			Mesh.Triangles[++Offset] = P1;
			Mesh.Triangles[++Offset] = P3;
			Mesh.Triangles[++Offset] = P4;

			Outlines[EdgeIndex].Key = CurrentSection[j];
			Outlines[EdgeIndex].Value = NextSection[j];
		}
	}
	if (InRoutine.IsLoop()) return true;
	{//添加头尾截面的轮廓
		const int32 EdgeCount = InSection.GetSectionPoints().Num() - 1;
		const int32 OutlineOffset = Outlines.AddDefaulted(EdgeCount << 1);
		const auto& HeadPoints = NewSections[0].GetSectionPoints();
		const auto& TailPoints = NewSections[NewSections.Num() - 1].GetSectionPoints();
		for (int32 i = 0; i < EdgeCount; ++i)
		{
			const int32 HeadOutlineIndex = OutlineOffset + i;
			Outlines[HeadOutlineIndex].Key = HeadPoints[i];
			Outlines[HeadOutlineIndex].Value = HeadPoints[i + 1];
			const int32 TailOutlineIndex = OutlineOffset + i + EdgeCount;
			Outlines[TailOutlineIndex].Key = TailPoints[i];
			Outlines[TailOutlineIndex].Value = TailPoints[i + 1];
		}
	}
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? FVector::ForwardVector : (RoutinePoits[0] - RoutinePoits[1]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[0].GetSectionPoints();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME)) EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.AddDefaulted(EndSectionPoints.Num());
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i];
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[0])) / UVRateInCM;
			Mesh.UV[Index].X = SectionUV[i].X;
		}
		TArray<int32> Triangles = TArray<int32>();
		FPolygon3DLibrary::TriangulatePolygon2D(EndSectionPoints, SectionNormal, Triangles);
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? -FVector::ForwardVector : (RoutinePoits[RoutinePoits.Num() - 1] - RoutinePoits[RoutinePoits.Num() - 2]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[NewSections.Num() - 1].GetSectionPoints();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME)) EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.AddDefaulted(EndSectionPoints.Num());
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i];
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[RoutinePoits.Num() - 1])) / UVRateInCM;
			Mesh.UV[Index].X = SectionUV[i].X;
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::GenerateLoftMesh SectionNormal=%s"), *SectionNormal.ToString());
		TArray<int32> Triangles = TArray<int32>();
		FPolygon3DLibrary::TriangulatePolygon2D(EndSectionPoints, SectionNormal, Triangles);
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	return true;
}

bool FPolygonLoftLibrary::GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, FVolatileLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines)
{
	const auto& RoutinePoits = InRoutine.GetRoutinePoints();
	if (RoutinePoits.Num() < 1) return false;
	if (1 == RoutinePoits.Num())
	{//只有一个点时直接将点移动到放样点
		TArray<FVector> Points;
		FPolygonPoint::ConvertToVectorArray(InSection.GetSectionPoints(), Points);
		FPolygon3DLibrary::GenerateMeshFromPolygon2D(Points, InSection.GetNormal(), InSection.GetTangentX(), Mesh);
		Mesh += RoutinePoits[0];
		return true;
	}
	if (2 == RoutinePoits.Num())
	{//有两个点时点的顺序决定放样后的向上方向

	}
	const float& UVRateInCM = InSection.GetUVRateInCM();
	TArray<FVolatileLoftSection> NewSections = TArray<FVolatileLoftSection>();
	{
		TArray<FVector> RoutineNormals = TArray<FVector>();
		bool Res = InRoutine.CalculateRoutineOutsideNormal(RoutineNormals);
		if (!Res) return false;
		NewSections.Init(FVolatileLoftSection(), RoutineNormals.Num());
		int32 i = 0;
		bool bNeedReverse = false;
		for (const auto& Normal : RoutineNormals)
		{
			Res = InSection.TransformSection(Normal.GetSafeNormal(), InRoutine.GetPlaneNormal(), Normal.Size(), NewSections[i]);
			if (!Res) return false;
			NewSections[i].TranslateSection(RoutinePoits[i]);
			++i;
		}
		{
			TArray<FVector> Points;
			FPolygonPoint::ConvertToVectorArray(NewSections[0].GetSectionPoints(), Points);
			bool bCCW = FPolygon3DLibrary::IsPolygonCCWWinding(Points, (RoutineNormals[0] ^ InRoutine.GetPlaneNormal()).GetSafeNormal());
			for (i = 0; bCCW && (i < NewSections.Num()); ++i)
				NewSections[i].Reverse();
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::RemoveParallel IsValidSection=%d"), NewSections.Num());
	}
	const int32 RoutineCount = InRoutine.IsLoop() ? RoutinePoits.Num() : (RoutinePoits.Num() - 1);
	const TArray<FPolygonPoint>& SectionPoints = InSection.GetSectionPoints();
	if (SectionPoints.Num() < 2) return false;
	const int32 SectionPointCount = SectionPoints.Num() - 1;
	{
		const int32 VertexCount = (SectionPointCount << 2) * RoutineCount;
		Mesh.Vertexes.Init(FVector::ZeroVector, VertexCount);
		Mesh.UV.Init(FVector2D::ZeroVector, VertexCount);
		Mesh.Normals.Init(FVector::ZeroVector, VertexCount);
		Mesh.Tangents.Init(FProcMeshTangent(), VertexCount);
		Mesh.Triangles.Init(0, SectionPointCount * RoutineCount * 6);
		Outlines.Init(TPair<FVector, FVector>(FVector::ZeroVector, FVector::ZeroVector), SectionPointCount * RoutineCount);
	}
	const auto& SectionUV = InSection.GetSectionPointsUV_Y();
	int32 OutlineCount = 0;
	for (int32 i = 0; i < RoutineCount; ++i)
	{
		const int32 NextRoutinePoint = (i + 1) % RoutinePoits.Num();
		const FVector Tangent = (RoutinePoits[NextRoutinePoint] - RoutinePoits[i]).GetSafeNormal();
		const auto& CurrentSection = NewSections[i].GetSectionPoints();
		const auto& NextSection = NewSections[NextRoutinePoint].GetSectionPoints();
		for (int32 j = 0; j < SectionPointCount; ++j)
		{
			int32 Offset = i * (SectionPointCount << 2) + (j << 2);
			const int32 NextSectionPoint = (j + 1) % SectionPoints.Num();
			const int32 P1 = Offset;
			const int32 P2 = ++Offset;
			const int32 P3 = ++Offset;
			const int32 P4 = ++Offset;
			Mesh.Vertexes[P1] = NextSection[j].Point;
			Mesh.Vertexes[P2] = CurrentSection[j].Point;
			Mesh.Vertexes[P3] = CurrentSection[NextSectionPoint].Point;
			Mesh.Vertexes[P4] = NextSection[NextSectionPoint].Point;
			Mesh.Tangents[P4].TangentX = Mesh.Tangents[P3].TangentX = Mesh.Tangents[P2].TangentX = Mesh.Tangents[P1].TangentX = Tangent;
			{
				Mesh.UV[P1].X = (Tangent | (Mesh.Vertexes[P1] - CurrentSection[0].Point)) / UVRateInCM;
				Mesh.UV[P2].X = (Tangent | (Mesh.Vertexes[P2] - CurrentSection[0].Point)) / UVRateInCM;
				Mesh.UV[P3].X = (Tangent | (Mesh.Vertexes[P3] - CurrentSection[0].Point)) / UVRateInCM;
				Mesh.UV[P4].X = (Tangent | (Mesh.Vertexes[P4] - CurrentSection[0].Point)) / UVRateInCM;
				Mesh.UV[P2].Y = Mesh.UV[P1].Y = SectionUV[j];
				Mesh.UV[P4].Y = Mesh.UV[P3].Y = SectionUV[NextSectionPoint];
			}
			const FVector Normal = (Tangent ^ (CurrentSection[NextSectionPoint] - CurrentSection[j])).GetSafeNormal();
			Mesh.Normals[P4] = Mesh.Normals[P3] = Mesh.Normals[P2] = Mesh.Normals[P1] = Normal;
			const int32 EdgeIndex = i * SectionPointCount + j;
			Offset = EdgeIndex * 6;
			Mesh.Triangles[Offset] = P1;
			Mesh.Triangles[++Offset] = P2;
			Mesh.Triangles[++Offset] = P3;

			Mesh.Triangles[++Offset] = P1;
			Mesh.Triangles[++Offset] = P3;
			Mesh.Triangles[++Offset] = P4;

			if (CurrentSection[j].bOutline)
			{
				Outlines[OutlineCount].Key = CurrentSection[j].Point;
				Outlines[OutlineCount].Value = NextSection[j].Point;
				++OutlineCount;
			}
		}
	}
	if (InRoutine.IsLoop()) return true;
	{//添加头尾截面的轮廓
		const int32 EdgeCount = InSection.GetSectionPoints().Num() - 1;
		const int32 OutlineOffset = Outlines.AddDefaulted(EdgeCount << 1);
		const auto& HeadPoints = NewSections[0].GetSectionPoints();
		const auto& TailPoints = NewSections[NewSections.Num() - 1].GetSectionPoints();
		for (int32 i = 0; i < EdgeCount; ++i)
		{
			Outlines[OutlineCount].Key = HeadPoints[i].Point;
			Outlines[OutlineCount].Value = HeadPoints[i + 1].Point;
			++OutlineCount;
			Outlines[OutlineCount].Key = TailPoints[i].Point;
			Outlines[OutlineCount].Value = TailPoints[i + 1].Point;
			++OutlineCount;
		}
		if (OutlineCount < Outlines.Num()) Outlines.RemoveAt(OutlineCount, Outlines.Num() - OutlineCount);
	}
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? InRoutine.GetPlaneForward() : (RoutinePoits[0] - RoutinePoits[1]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[0].GetSectionPoints();
		auto EndSectionUV = NewSections[0].GetSectionPointsUV();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME))
		{
			EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
			EndSectionUV.RemoveAt(EndSectionPoints.Num() - 1);
		}
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.Append(EndSectionUV);
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i].Point;
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			//Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[0])) / UVRateInCM;
			//Mesh.UV[Index].X = SectionUV[i].X;
		}
		TArray<int32> Triangles = TArray<int32>();
		{
			TArray<FVector> VectorArray;
			FPolygonPoint::ConvertToVectorArray(EndSectionPoints, VectorArray);
			FPolygon3DLibrary::TriangulatePolygon2D(VectorArray, SectionNormal, Triangles);
		}
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	{
		FVector SectionNormal = 1 == RoutinePoits.Num() ? -FVector::ForwardVector : (RoutinePoits[RoutinePoits.Num() - 1] - RoutinePoits[RoutinePoits.Num() - 2]).GetSafeNormal();
		FVector Tangent = (SectionNormal ^ InRoutine.GetPlaneNormal()).GetSafeNormal();
		auto EndSectionPoints = NewSections[NewSections.Num() - 1].GetSectionPoints();
		auto EndSectionUV = NewSections[NewSections.Num() - 1].GetSectionPointsUV();
		if ((EndSectionPoints[0] - EndSectionPoints[EndSectionPoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME))
		{
			EndSectionPoints.RemoveAt(EndSectionPoints.Num() - 1);
			EndSectionUV.RemoveAt(EndSectionPoints.Num() - 1);
		}
		const int32 Offset = Mesh.Vertexes.Num();
		Mesh.Vertexes.AddDefaulted(EndSectionPoints.Num());
		Mesh.Normals.AddDefaulted(EndSectionPoints.Num());
		Mesh.UV.Append(EndSectionUV);
		Mesh.Tangents.AddDefaulted(EndSectionPoints.Num());
		for (int32 i = 0; i < EndSectionPoints.Num(); ++i)
		{
			const int32 Index = Offset + i;
			Mesh.Vertexes[Index] = EndSectionPoints[i].Point;
			Mesh.Normals[Index] = SectionNormal;
			Mesh.Tangents[Index].TangentX = Tangent;
			//Mesh.UV[Index].Y = (InRoutine.GetPlaneNormal() | (EndSectionPoints[i] - RoutinePoits[RoutinePoits.Num() - 1])) / UVRateInCM;
			//Mesh.UV[Index].X = SectionUV[i].X;
		}
		UE_LOG(LogTemp, Log, TEXT("FPolygonLoftLibrary::GenerateLoftMesh SectionNormal=%s"), *SectionNormal.ToString());
		TArray<int32> Triangles = TArray<int32>();
		{
			TArray<FVector> VectorArray;
			FPolygonPoint::ConvertToVectorArray(EndSectionPoints, VectorArray);
			FPolygon3DLibrary::TriangulatePolygon2D(VectorArray, SectionNormal, Triangles);
		}
		for (int32 i = 0; i < Triangles.Num(); ++i)
			Triangles[i] += Offset;
		Mesh.Triangles.Append(Triangles);
	}
	return true;
}

bool FPolygonLoftLibrary::GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, const FCatalogLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines)
{
	const auto& RoutinePoits = InRoutine.GetRoutinePoints();
	if (RoutinePoits.Num() < 1) return false;
	if (1 == RoutinePoits.Num())
	{//只有一个点时直接将点移动到放样点
		InSection.GenerateMesh(Mesh);
		Mesh += RoutinePoits[0];
		return true;
	}
	{//有两个点时点的顺序决定放样后的向上方向
		const FVector Forward = InRoutine.GetPlaneNormal();
		TArray<FVector> Updirs;
		InRoutine.RoutinePointUpDir(Updirs);
		TArray<FCatalogLoftSection> NewSections;
		NewSections.Init(FCatalogLoftSection(), Updirs.Num());
		for (int32 i = 0; i < Updirs.Num(); ++i)
		{
			InSection.TransformSection(RoutinePoits[i], Forward, Updirs[i].GetSafeNormal(), Updirs[i].Size(), NewSections[i]);
		}
		int32 CapCount = InRoutine.IsLoop() ? Updirs.Num() : (Updirs.Num() - 1);
		for (int32 i = 0; i < CapCount; ++i)
		{
			const int32 Next = (i + 1) % Updirs.Num();
			FPMCSection CapMesh;
			FPolygonLoftLibrary::GenerateMeshBetweenSections(NewSections[i], NewSections[Next], 0.0f, CapMesh, Outlines);
			Mesh += CapMesh;
		}
		for (auto& SectionIter : NewSections)
		{
			SectionIter.GenerateOutline(Outlines);
		}
		if (false == InRoutine.IsLoop())
		{
			NewSections[0].SetNormal((RoutinePoits[0] - RoutinePoits[1]).GetSafeNormal());
			{
				FPMCSection SectionMesh;
				NewSections[0].GenerateMesh(SectionMesh);
				Mesh += SectionMesh;
			}
			const int32 LastSectionIndex = RoutinePoits.Num() - 1;
			NewSections[LastSectionIndex].SetNormal((RoutinePoits[LastSectionIndex] - RoutinePoits[LastSectionIndex - 1]).GetSafeNormal());
			{
				FPMCSection SectionMesh;
				NewSections[LastSectionIndex].GenerateMesh(SectionMesh);
				Mesh += SectionMesh;
			}
		}
		return true;
	}
	//return false;
}

bool FPolygonLoftLibrary::GenerateMeshBetweenSections(const FCatalogLoftSection& First, const FCatalogLoftSection& Second, const float& UVBaseX, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines)
{
	auto& FirstPoints = First.GetSectionPoints();
	auto& SecondPoints = Second.GetSectionPoints();
	if ((FirstPoints.Num() != SecondPoints.Num()) || (0 == FirstPoints.Num())) return false;
	TArray<FVector> OutsideNormal;
	First.GetEdgeOutsideNormal(OutsideNormal);
	const int32 Count = FirstPoints.Num();
	int32 Offset = Outlines.AddDefaulted(Count);
	const FVector TangentX = (SecondPoints[0].Point - FirstPoints[0].Point).GetSafeNormal();
	float UVBase = 0.0f;
	for (int32 i = 0; i < Count; i++)
	{
		const int32 Next = (i + 1) % Count;

		FPMCSection CapSecton;
		CapSecton.Vertexes.AddDefaulted(4);
		CapSecton.Vertexes[0] = FirstPoints[i].Point;
		CapSecton.Vertexes[1] = SecondPoints[i].Point;
		CapSecton.Vertexes[2] = SecondPoints[Next].Point;
		CapSecton.Vertexes[3] = FirstPoints[Next].Point;

		CapSecton.Triangles.Init(0, 6);
		if ((((CapSecton.Vertexes[2] - CapSecton.Vertexes[1]) ^ (CapSecton.Vertexes[0] - CapSecton.Vertexes[1])) | OutsideNormal[i]) > 0.0f)
		{//顺时针
			//0-3-2
			CapSecton.Triangles[1] = 3;
			CapSecton.Triangles[2] = 2;
			//0-2-1
			CapSecton.Triangles[4] = 2;
			CapSecton.Triangles[5] = 1;

		}
		else
		{//逆时针
			//0-1-2
			CapSecton.Triangles[1] = 1;
			CapSecton.Triangles[2] = 2;
			//0-2-3
			CapSecton.Triangles[4] = 2;
			CapSecton.Triangles[5] = 3;
		}

		FVector Normal = ((CapSecton.Vertexes[CapSecton.Triangles[0]] - CapSecton.Vertexes[CapSecton.Triangles[1]]) ^ (CapSecton.Vertexes[CapSecton.Triangles[2]] - CapSecton.Vertexes[CapSecton.Triangles[1]])).GetSafeNormal();
		CapSecton.Normals.Init(Normal, 4);

		FVector TangentY = (Normal ^ TangentX).GetSafeNormal();
		CapSecton.Tangents.Init(FProcMeshTangent(TangentX, false), 4);

		CapSecton.UV.Init(FVector2D::ZeroVector, 4);
		for (int32 j = 0; j < 4; j++)
		{
			FVector Delta = CapSecton.Vertexes[j] - CapSecton.Vertexes[0];
			CapSecton.UV[j].X = (Delta | TangentX) / First.GetUVRateInCM() + UVBaseX;
			CapSecton.UV[j].Y = (Delta | TangentY) / First.GetUVRateInCM() + UVBase;
		}
		UVBase = CapSecton.UV[3].Y;

		Mesh += CapSecton;

		if (!FirstPoints[i].bOutline) continue;
		Outlines[Offset].Key = FirstPoints[i].Point;
		Outlines[Offset].Value = SecondPoints[i].Point;
		++Offset;
	}
	if (Offset < Outlines.Num()) Outlines.RemoveAt(Offset, Outlines.Num() - Offset);
	return true;
}