// Copyright Epic Games, Inc. All Rights Reserved.

#include "PolygonScaleLibrary.h"
#include "GeometryEdit.h"
#include "Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"

bool FPolygonScaleLibrary::ScalePolygon(const TArray<FVector>& InPolygonToScale, const FVector& InPolygonNormal, const TArray<float>& InScale, TArray<FVector>& PolygonScaled)
{
	const int32 PointCount = InPolygonToScale.Num();
	if (PointCount != InScale.Num()) return false;
	bool bIsCCW = false;
	TArray<FVector> PolygonToScale = InPolygonToScale;
	TArray<float> Scale = InScale;
	{
		FPolygon3DLibrary::RemoveConcurrentPoints(PolygonToScale, true);
		bIsCCW = FPolygon3DLibrary::IsPolygonCCWWinding(PolygonToScale, InPolygonNormal);
		if (bIsCCW)
		{
			PolygonToScale = InPolygonToScale;
			FArrayOperatorLibrary::ReverseArray<FVector>(PolygonToScale);
			FArrayOperatorLibrary::ReverseArray(Scale);
		}
	}
	TArray<TPair<FVector, FVector>> ScaledPoints;
	TArray<bool> ScalePointsValueValid;
	{
		TArray<TPair<FVector, FVector>> ScaledPointsNormalAndDir;
		ScaledPoints.Init(TPair<FVector, FVector>(FVector::ZeroVector, FVector::ZeroVector), PointCount);
		ScalePointsValueValid.Init(false, PointCount);
		ScaledPointsNormalAndDir.Init(TPair<FVector, FVector>(FVector::ZeroVector, FVector::ZeroVector), PointCount);
		for (int32 i = 0; i < PointCount; ++i)
		{
			const int32 Next = (i + 1) % PointCount;
			ScaledPoints[i].Key = PolygonToScale[i];
			ScaledPoints[i].Value = PolygonToScale[Next];
			ScaledPointsNormalAndDir[i].Value = (ScaledPoints[i].Value - ScaledPoints[i].Key).GetSafeNormal();
			ScaledPointsNormalAndDir[i].Key = (InPolygonNormal ^ ScaledPointsNormalAndDir[i].Value).GetSafeNormal();
			const FVector ScaleOffset = ScaledPointsNormalAndDir[i].Key * Scale[i];
			ScaledPoints[i].Key += ScaleOffset;
			ScaledPoints[i].Value += ScaleOffset;
		}
		for (int32 i = 0; i < PointCount; ++i)
		{
			const int32 Pre = (i - 1 + PointCount) % PointCount;
			const int32 Next = (i + 1) % PointCount;

			const FPlane LinePlane(ScaledPoints[i].Key, ScaledPointsNormalAndDir[i].Key);
			if (FVector::Parallel(ScaledPointsNormalAndDir[i].Key, ScaledPointsNormalAndDir[Pre].Key, THRESH_NORMALS_ARE_PARALLEL))
			{
				ScalePointsValueValid[Pre] = true;
			}
			else
			{
				FVector CrossPoint = FMath::RayPlaneIntersection(ScaledPoints[Pre].Key, ScaledPointsNormalAndDir[Pre].Value, LinePlane);
				ScaledPoints[Pre].Value = CrossPoint;
				ScaledPoints[i].Key = CrossPoint;
			}
			if (!FVector::Parallel(ScaledPointsNormalAndDir[i].Key, ScaledPointsNormalAndDir[Next].Key, THRESH_NORMALS_ARE_PARALLEL))
			{
				FVector CrossPoint = FMath::RayPlaneIntersection(ScaledPoints[Next].Key, ScaledPointsNormalAndDir[Next].Value, LinePlane);
				ScaledPoints[Next].Key = CrossPoint;
				ScaledPoints[i].Value = CrossPoint;
			}
		}
		for (int32 i = 0; i < PointCount; ++i)
		{
			const FVector LineDir = ScaledPoints[i].Value - ScaledPoints[i].Key;
			const float DotValue = ScaledPointsNormalAndDir[i].Value | LineDir;
			if (DotValue < 0.0f) return false;
		}
	}
	int32 PointOffset = PolygonScaled.AddDefaulted(ScaledPoints.Num() << 1);
	for (int32 i = 0; i < ScaledPoints.Num(); ++i)
	{
		PolygonScaled[PointOffset++] = ScaledPoints[i].Key;
		if (ScalePointsValueValid[i]) PolygonScaled[PointOffset++] = ScaledPoints[i].Value;
	}
	if (PointOffset < PolygonScaled.Num()) PolygonScaled.RemoveAt(PointOffset, PolygonScaled.Num() - PointOffset);
	return true;
}

bool FPolygonScaleLibrary::ShiftPolygon(TArray<FVector>& PolygonToShift, const FVector& InPolygonNormal, const TArray<float>& InScale)
{
	TArray<FVector> PolygonScaled;
	bool Res = FPolygonScaleLibrary::ScalePolygon(PolygonToShift, InPolygonNormal, InScale, PolygonScaled);
	if (!Res) return false;
	PolygonToShift = PolygonScaled;
	return true;
}
