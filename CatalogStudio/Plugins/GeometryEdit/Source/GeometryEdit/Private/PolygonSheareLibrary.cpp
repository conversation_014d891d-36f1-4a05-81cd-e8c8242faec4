// Copyright Epic Games, Inc. All Rights Reserved.

#include "PolygonSheareLibrary.h"
#include "GeometryEdit.h"
#include "Polygon3DLibrary.h"


bool FPolygonSheareLibrary::ShearePolygon(const FShearePolygon& PolygonToSheare, const TArray<FVector>& Polygon, TArray<FPMCTriangle>& Triangles)
{
	if (!PolygonToSheare.IsValidRoutine() || (0 == Polygon.Num())) return false;
	TArray<FVector> ProjectionPolygon;
	{//将原始点投影到被剪切面上
		const FPlane ShearePlane(PolygonToSheare.GetRoutinePoints()[0], PolygonToSheare.GetPlaneNormal());
		ProjectionPolygon.SetNum(Polygon.Num());
		for (int32 i = 0; i < Polygon.Num(); ++i)
		{
			ProjectionPolygon[i] = FVector::PointPlaneProject(Polygon[i], ShearePlane);
		}
	}
	TArray<FPMCTriangle> TrianglesToSheare = TArray<FPMCTriangle>();
	TArray<FPMCVertex> Vertices = TArray<FPMCVertex>();
	FPolygonSheareLibrary::ConvertShearePolygonToTriangles(PolygonToSheare, TrianglesToSheare, Vertices);
	int32 TriangleIndex = 0;
	while (TriangleIndex < TrianglesToSheare.Num())
	{
		bool bSplited = false;
		for (int32 i = 0; i < ProjectionPolygon.Num(); ++i)
		{
			const int32 Next = (i + 1) % ProjectionPolygon.Num();
			TArray<FPMCTriangle> NewTriangles;
			bSplited = FPolygonSheareLibrary::SplitTriangleByLinesegment(TrianglesToSheare[TriangleIndex], TPair<FVector, FVector>(ProjectionPolygon[i], ProjectionPolygon[Next]), PolygonToSheare.GetPlaneNormal(), Vertices, NewTriangles);
			if (bSplited)
			{
				TrianglesToSheare[TriangleIndex] = NewTriangles[NewTriangles.Num() - 1];
				NewTriangles.Pop();
				TrianglesToSheare.Append(NewTriangles);
				break;
			}
		}
		if (!bSplited)	++TriangleIndex;
	}
	TArray<FPMCTriangle> InsideTriangles = TArray<FPMCTriangle>();
	Triangles = TArray<FPMCTriangle>();
	FPolygonSheareLibrary::FindInsideTriangles(TrianglesToSheare, ProjectionPolygon, PolygonToSheare.GetPlaneNormal(), InsideTriangles, Triangles);
	return true;
}

void FPolygonSheareLibrary::ConvertShearePolygonToTriangles(const FShearePolygon& PolygonToSheare, TArray<FPMCTriangle>& TrianglesToSheare, TArray<FPMCVertex>& Vertices)
{
	TArray<int32> Indice = TArray<int32>();
	const auto& PolygonPoints = PolygonToSheare.GetRoutinePoints();
	const auto& PolygonPointsUV = PolygonToSheare.GetPointsUV();
	bool Res = FPolygon3DLibrary::TriangulatePolygon2D(PolygonPoints, PolygonToSheare.GetPlaneNormal(), Indice);
	if (!Res) return;
	Vertices.Init(FPMCVertex(), PolygonPoints.Num());
	const int32 TriangleCount = Indice.Num() / 3;
	TrianglesToSheare.Init(FPMCTriangle(), TriangleCount);
	for (int32 i = 0; i < TriangleCount; ++i)
	{
		FPMCTriangle& EditTriangle = TrianglesToSheare[i];
		int32 BaseIndex = i * 3;
		for (int32 j = 0; j < 3; ++j)
		{
			const int32 PointIndex = Indice[BaseIndex + j];
			EditTriangle.Vertexes[j].Pos = PolygonPoints[PointIndex];
			EditTriangle.Vertexes[j].Normal = PolygonToSheare.GetPlaneNormal();
			EditTriangle.Vertexes[j].UV = PolygonPointsUV[PointIndex];
			EditTriangle.Vertexes[j].Tangent.TangentX = PolygonToSheare.GetTangentX();
			EditTriangle.Vertexes[j].VertexIndex = PointIndex;
			Vertices[PointIndex] = EditTriangle.Vertexes[j];
		}
	}
}

bool FPolygonSheareLibrary::SplitTriangleByLinesegment(const FPMCTriangle& InTriangle, const TPair<FVector, FVector>& InLinesegment, const FVector& LineNormal, TArray<FPMCVertex>& Vertices, TArray<FPMCTriangle>& OutNewTriangles)
{
	bool PointInTriangle = FPolygonSheareLibrary::SplitTriangleByInnerPoint(InTriangle, InLinesegment.Key, Vertices, OutNewTriangles);
	if (PointInTriangle) return true;
	PointInTriangle = FPolygonSheareLibrary::SplitTriangleByInnerPoint(InTriangle, InLinesegment.Value, Vertices, OutNewTriangles);
	if (PointInTriangle) return true;
	FVector LineDir = InLinesegment.Value - InLinesegment.Key;
	const float LineLength = LineDir.Size();
	LineDir = LineDir.GetSafeNormal();
	float PlaneDist[3] = { 0.0f,0.0f,0.0f };
	{
		FPlane Plane(InLinesegment.Key, (LineNormal ^ LineDir).GetSafeNormal());
		for (int32 i = 0; i < 3; i++) PlaneDist[i] = Plane.PlaneDot(InTriangle.Vertexes[i].Pos);
	}
	for (int32 EdgeIdx = 0; EdgeIdx < 3; EdgeIdx++)
	{
		const int32 NextVert = (EdgeIdx + 1) % 3;
		if (FMath::IsNegativeFloat(PlaneDist[EdgeIdx]) == FMath::IsNegativeFloat(PlaneDist[NextVert]) || FMath::IsNearlyZero(PlaneDist[EdgeIdx], THRESH_POINTS_ARE_NEAR) || FMath::IsNearlyZero(PlaneDist[NextVert], THRESH_POINTS_ARE_NEAR)) continue;

		float Alpha = -PlaneDist[EdgeIdx] / (PlaneDist[NextVert] - PlaneDist[EdgeIdx]);
		FPMCVertex InterpolateVertice = FPMCVertex::InterpolateVert(InTriangle.Vertexes[EdgeIdx], InTriangle.Vertexes[NextVert], Alpha);
		const float Dot = (InterpolateVertice.Pos - InLinesegment.Key) | LineDir;
		if (Dot < 0.0f || Dot > LineLength) continue;

		InterpolateVertice.VertexIndex = Vertices.IndexOfByPredicate([InterpolateVertice](const FPMCVertex& InOther) { return InOther.Equals(InterpolateVertice); });
		if (INDEX_NONE == InterpolateVertice.VertexIndex) InterpolateVertice.VertexIndex = Vertices.Add(InterpolateVertice);

		OutNewTriangles.AddDefaulted(2);
		OutNewTriangles[0].Vertexes[0] = InTriangle.Vertexes[EdgeIdx];
		OutNewTriangles[0].Vertexes[1] = InterpolateVertice;
		OutNewTriangles[0].Vertexes[2] = InTriangle.Vertexes[(NextVert + 1) % 3];
		if (OutNewTriangles[0].IsRedundantTriangle()) return false;
		OutNewTriangles[1].Vertexes[0] = InterpolateVertice;
		OutNewTriangles[1].Vertexes[1] = InTriangle.Vertexes[NextVert];
		OutNewTriangles[1].Vertexes[2] = InTriangle.Vertexes[(NextVert + 1) % 3];
		if (OutNewTriangles[1].IsRedundantTriangle()) return false;
		return true;
	}
	return false;
}

bool FPolygonSheareLibrary::SplitTriangleByInnerPoint(const FPMCTriangle& InTriangle, const FVector& InPoint, TArray<FPMCVertex>& Vertices, TArray<FPMCTriangle>& OutNewTriangles)
{
	int32 PointOrEdgeIndex = INDEX_NONE;
	EPointTriangleRelationship PointRelationship = FPolygon3DLibrary::IsPointInTriangle(InTriangle.Vertexes[0].Pos, InTriangle.Vertexes[1].Pos, InTriangle.Vertexes[2].Pos, InPoint, PointOrEdgeIndex);
	if (EPointTriangleRelationship::EqualWithVertice == PointRelationship || EPointTriangleRelationship::VecOutside == PointRelationship) return false;
	if (EPointTriangleRelationship::VecInside == PointRelationship)
	{
		const FVector TriangleNormal = ((InTriangle.Vertexes[1] - InTriangle.Vertexes[0]) ^ (InTriangle.Vertexes[2] - InTriangle.Vertexes[0])).GetSafeNormal();
		FPMCVertex NewVertice = FPMCVertex();
		{
			const FVector EdgeNormal = (TriangleNormal ^ (InPoint - InTriangle.Vertexes[0].Pos)).GetSafeNormal();
			FPlane Plane(InTriangle.Vertexes[0].Pos, EdgeNormal);
			const float PlaneDist1 = FMath::Abs(Plane.PlaneDot(InTriangle.Vertexes[1].Pos));
			const float PlaneDist2 = FMath::Abs(Plane.PlaneDot(InTriangle.Vertexes[2].Pos));
			float Alpha = PlaneDist1 / (PlaneDist2 + PlaneDist1);
			const FPMCVertex InterpolateVertice = FPMCVertex::InterpolateVert(InTriangle.Vertexes[1], InTriangle.Vertexes[2], Alpha);
			Alpha = FVector::Dist(InPoint, InTriangle.Vertexes[0].Pos) / FVector::Dist(InterpolateVertice.Pos, InTriangle.Vertexes[0].Pos);
			//UE_LOG(LogTemp, Log, TEXT("FPolygonSheareLibrary::SplitTriangleByInnerPoint Before NewVertice.Pos=%s NewVertice.UV=%s"), *NewVertice.Pos.ToString(), *NewVertice.UV.ToString());
			NewVertice = FPMCVertex::InterpolateVert(InTriangle.Vertexes[0], InterpolateVertice, Alpha);
			//UE_LOG(LogTemp, Log, TEXT("FPolygonSheareLibrary::SplitTriangleByInnerPoint After NewVertice.Pos=%s NewVertice.UV=%s"), *NewVertice.Pos.ToString(), *NewVertice.UV.ToString());
		}
		NewVertice.VertexIndex = Vertices.IndexOfByPredicate([NewVertice](const FPMCVertex& InOther) { return InOther.Equals(NewVertice); });
		if (INDEX_NONE == NewVertice.VertexIndex)
			NewVertice.VertexIndex = Vertices.Add(NewVertice);

		OutNewTriangles.AddDefaulted(3);
		OutNewTriangles[0].Vertexes[0] = InTriangle.Vertexes[0];
		OutNewTriangles[0].Vertexes[1] = InTriangle.Vertexes[1];
		OutNewTriangles[0].Vertexes[2] = NewVertice;

		OutNewTriangles[1].Vertexes[0] = InTriangle.Vertexes[1];
		OutNewTriangles[1].Vertexes[1] = InTriangle.Vertexes[2];
		OutNewTriangles[1].Vertexes[2] = NewVertice;

		OutNewTriangles[2].Vertexes[0] = InTriangle.Vertexes[2];
		OutNewTriangles[2].Vertexes[1] = InTriangle.Vertexes[0];
		OutNewTriangles[2].Vertexes[2] = NewVertice;
		return true;
	}
	else if (INDEX_NONE != PointOrEdgeIndex)
	{
		const int32 NextPointIndex = (PointOrEdgeIndex + 1) % 3;
		float Alpha = FVector::Dist(InTriangle.Vertexes[PointOrEdgeIndex].Pos, InPoint) / FVector::Dist(InTriangle.Vertexes[PointOrEdgeIndex].Pos, InTriangle.Vertexes[NextPointIndex].Pos);
		FPMCVertex NewVertice = FPMCVertex::InterpolateVert(InTriangle.Vertexes[PointOrEdgeIndex], InTriangle.Vertexes[NextPointIndex], Alpha);
		NewVertice.VertexIndex = Vertices.IndexOfByPredicate([NewVertice](const FPMCVertex& InOther) { return InOther.Equals(NewVertice); });
		if (INDEX_NONE == NewVertice.VertexIndex)
			NewVertice.VertexIndex = Vertices.Add(NewVertice);

		OutNewTriangles.AddDefaulted(2);
		OutNewTriangles[0].Vertexes[0] = InTriangle.Vertexes[PointOrEdgeIndex];
		OutNewTriangles[0].Vertexes[1] = NewVertice;
		OutNewTriangles[0].Vertexes[2] = InTriangle.Vertexes[(NextPointIndex + 1) % 3];

		OutNewTriangles[1].Vertexes[0] = NewVertice;
		OutNewTriangles[1].Vertexes[1] = InTriangle.Vertexes[NextPointIndex];
		OutNewTriangles[1].Vertexes[2] = InTriangle.Vertexes[(NextPointIndex + 1) % 3];
		return true;
	}
	return false;
}

void FPolygonSheareLibrary::FindInsideTriangles(const TArray<FPMCTriangle>& InTriangles, const TArray<FVector>& InPolygon, const FVector& PolygonNormal, TArray<FPMCTriangle>& InsideTriangles, TArray<FPMCTriangle>& OutsideTriangles)
{
	int32 InsideTriangleCount = InsideTriangles.AddDefaulted(InTriangles.Num());
	int32 OutsideTriangleCount = OutsideTriangles.AddDefaulted(InTriangles.Num());
	TMap<int32, bool> VertexInside;
	for (const auto& TriangleIter : InTriangles)
	{
		const FVector CenterPoint = ((TriangleIter.Vertexes[0].Pos + TriangleIter.Vertexes[1].Pos) * 0.5f + TriangleIter.Vertexes[2].Pos) * 0.5f;
		bool bInsideTriangle = FPolygon3DLibrary::IsPointInPolygon(InPolygon, PolygonNormal, CenterPoint);
		if (bInsideTriangle)
			InsideTriangles[InsideTriangleCount++] = TriangleIter;
		else
			OutsideTriangles[OutsideTriangleCount++] = TriangleIter;
	}
	if (InsideTriangleCount < InTriangles.Num()) InsideTriangles.RemoveAt(InsideTriangleCount, InTriangles.Num() - InsideTriangleCount);
	if (OutsideTriangleCount < InTriangles.Num()) OutsideTriangles.RemoveAt(OutsideTriangleCount, InTriangles.Num() - OutsideTriangleCount);
}
