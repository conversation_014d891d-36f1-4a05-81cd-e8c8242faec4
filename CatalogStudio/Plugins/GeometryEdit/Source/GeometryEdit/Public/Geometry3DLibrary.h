// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once
#include "MagicCore/Public/PMCSection.h"



class GEOMETRYEDIT_API FGeometry3DLibrary
{

public:

	//
	static bool GenerateArcByRadius(const FVector& Start, const FVector& End, const FVector& Normal, const float& Radius, bool BigArc, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount = -1);

	static bool GenerateArcByCamber(const FVector& Start, const FVector& End, const FVector& Normal, const float& Camber, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount = -1);

	static bool GenerateArcByRadiusWithoutStartAndEnd(const FVector& Start, const FVector& End, const FVector& Normal, const float& Radius, bool BigArc, const float& MinmalChordLength, TArray<FVector>& ArcPoints1, const int32& InCount = -1);

	static bool GenerateArcByCamberWithoutStartAndEnd(const FVector& Start, const FVector& End, const FVector& Normal, const float& Camber, const float& MinmalChordLength, TArray<FVector>& ArcPoints, const int32& InCount = -1);

	static bool GenerateRectanglePlanPoints(const FVector& Min, const FVector& Max, const FVector& Normal, const FVector& TangentX, TArray<FVector>& RectPoints);

	static bool GenerateEllipsePlanPoints(const FVector& Center, const float& TangentXRadius, const float& TangentYRadius, const FVector& Normal, const FVector& TangentX, TArray<FVector>& Points,const int32& InCount = 20);
};
