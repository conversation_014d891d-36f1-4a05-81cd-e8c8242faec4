// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "MagicCore/Public/PMCSection.h"
#include "MagicCore/Public/MeshSheareData.h"


class GEOMETRYEDIT_API FGeometryEditLibrary
{

public:

	static bool ConvertPMCSectionsToMeshDescription(const TArray<FPMCSection>& InSections, FMeshDescription& Description);

	static void GenerateMeshFromTriangles(const TArray<FPMCTriangle>& InTriangles, FPMCSection& Mesh);

	static void GenerateMeshFromTriangles(const TArray<FVector>& InVertexes, const TArray<int32>& InTriangles, const FVector& InNormal, const FVector& InTangentX, FPMCSection& Mesh);

	//static void ConvertLineSegmentToRadiusArc(const TPair<FVector, FVector>& LineSegment, const FVector& Normal, const float& Radius, bool <PERSON>rc, TArray<FVector>& ArcPoints);

};
