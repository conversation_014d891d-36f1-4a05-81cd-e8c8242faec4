// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "MagicCore/Public/PMCSection.h"

enum GEOMETRYEDIT_API EPointTriangleRelationship
{
	EqualWithVertice,
	WithinEdge,
	VecInside,
	VecOutside
};


class GEOMETRYEDIT_API FPolygon3DLibrary
{

public:

	//判断多边形是否是顺时针，调用时需要确保InPolygon是有效的,InNormal为多边形所在平面的单位法向量
	//返回true表示多边形是顺时针，否则为逆时针
	static bool IsPolygonCCWWinding(const TArray<FVector>& InPolygon, const FVector& InNormal);

	static bool IsConvexVertex(const FVector& TestPoint, const FVector& PrePoint, const FVector& NextPoint, const FVector& PolygonNormal, bool PolygonCCW);

	static bool TriangulatePolygon2D(const TArray<FVector>& InPolygon2D, const FVector& InPolygonNormal, TArray<int32>& Triangles);
	static bool TriangulatePolygon2D(const TArray<FVector>& InPolygon2D, const FVector& InPolygonNormal,TArray<FVector>& OutPolygon2D, TArray<int32>& Triangles);


	static EPointTriangleRelationship IsPointInTriangle(const FVector& A, const FVector& B, const FVector& C, const FVector& TestPoint);

	static EPointTriangleRelationship IsPointInTriangle(const FVector& A, const FVector& B, const FVector& C, const FVector& TestPoint, int32& Index);

	static float TriangleArea(const FVector& A, const FVector& B, const FVector& C);

	static void ProjectPolygonToPlane(const TArray<FVector>& InPolygon, const FVector& InPolygonNormal, const FPlane& InPlane, TArray<FVector>& ProjectPolygon);

	static bool IsPointInPolygon(const TArray<FVector>& Polygon, const FVector& InNormal, const FVector& InPoint);

	static void RemoveConcurrentPoints(TArray<FVector>& InPolygon, bool IsLoop);

	static void RemoveCoincidencePoints(TArray<FVector>& InPolygon, bool IsLoop);

	static FBox PolygonBoundingBox(const TArray<FVector>& InPolygon);

	static void GenerateMeshFromPolygon2D(const TArray<FVector>& InPolygon, const FVector& InNormal, const FVector& InTangentX, const FVector& InUVOriginPoint, const float& UVRateInCM, FPMCSection& Mesh);

	static void GenerateMeshFromPolygon2D(const TArray<FVector>& InPolygon, const FVector& InNormal, const FVector& InTangentX, FPMCSection& Mesh);

	static void CalculateVertexesUV(const TArray<FVector>& InVertexes, const FVector& InNormal, const FVector& InTangentX, const FVector& InUVOriginPoint, const float& UVRateInCM, TArray<FVector2D>& OutUV);

};
