// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "GeometryEdit/Data/ShearePolygon.h"
#include "MagicCore/Public/MeshSheareData.h"
#include "MagicCore/Public/PMCSection.h"


class GEOMETRYEDIT_API FPolygonDrawLibrary
{

public:

	static bool DrawPolygonTriangles(TArray<FPMCTriangle>& OriginTriangles, const FVector& DrawDir, const float& UVRateInCM, TArray<FPMCTriangle>& TrianglesDrew, TArray<FPMCTriangle>& CapTriangles);

};
