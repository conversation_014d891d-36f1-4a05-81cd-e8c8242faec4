// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "GeometryEdit/Data/LoftRoutine.h"
#include "GeometryEdit/Data/LoftSection.h"
#include "GeometryEdit/Data/VolatileLoftSection.h"
#include "MagicCore/Public/PMCSection.h"


class GEOMETRYEDIT_API FPolygonLoftLibrary
{

public:

	static bool GenerateLoftMesh(const FLoftRoutine& InRoutine, const FLoftSection& InSection, FPMCSection& Mesh);

	static bool GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, const FLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines);

	static bool GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, FVolatileLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines);

	static bool GenerateLoftMeshWithOutline(const FLoftRoutine& InRoutine, const FCatalogLoftSection& InSection, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines);

private:

	static bool GenerateMeshBetweenSections(const FCatalogLoftSection& First, const FCatalogLoftSection& Second, const float& UVBaseX, FPMCSection& Mesh, TArray<TPair<FVector, FVector>>& Outlines);
};
