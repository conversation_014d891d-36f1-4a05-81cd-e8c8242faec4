// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "GeometryEdit/Data/LoftRoutine.h"

typedef FLoftRoutine FScalePolygon;

class GEOMETRYEDIT_API FPolygonScaleLibrary
{

public:

	static bool ScalePolygon(const TArray<FVector>& InPolygonToScale, const FVector& InPolygonNormal, const TArray<float>& InScale, TArray<FVector>& PolygonScaled);

	static bool ShiftPolygon(TArray<FVector>& PolygonToShift, const FVector& InPolygonNormal, const TArray<float>& InScale);

};
