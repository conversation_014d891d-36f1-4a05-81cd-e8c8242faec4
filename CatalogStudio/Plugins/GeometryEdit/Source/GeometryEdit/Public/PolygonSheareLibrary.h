// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "GeometryEdit/Data/ShearePolygon.h"
#include "MagicCore/Public/MeshSheareData.h"
#include "MagicCore/Public/PMCSection.h"


class GEOMETRYEDIT_API FPolygonSheareLibrary
{

public:

	static bool ShearePolygon(const FShearePolygon& PolygonToSheare, const TArray<FVector>& Polygon, TArray<FPMCTriangle>& Triangles);

	static void ConvertShearePolygonToTriangles(const FShearePolygon& PolygonToSheare, TArray<FPMCTriangle>& TrianglesToSheare, TArray<FPMCVertex>& Vertices);

private:

	static bool SplitTriangleByLinesegment(const FPMCTriangle& InTriangle, const TPair<FVector, FVector>& InLinesegment, const FVector& LineNormal, TArray<FPMCVertex>& Vertices, TArray<FPMCTriangle>& OutNewTriangles);

	static bool SplitTriangleByInnerPoint(const FPMCTriangle& InTriangle, const FVector& InPoint, TArray<FPMCVertex>& Vertices, TArray<FPMCTriangle>& OutNewTriangles);

	static void FindInsideTriangles(const TArray<FPMCTriangle>& InTriangles, const TArray<FVector>& InPolygon, const FVector& PolygonNormal, TArray<FPMCTriangle>& InsideTriangles, TArray<FPMCTriangle>& OutsideTriangles);

};
