// Copyright 1998-2018 Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.IO;

public class GrammerAnalysis : ModuleRules
{
	public GrammerAnalysis(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Public"));
        PrivateIncludePaths.Add(Path.Combine(ModuleDirectory, "Private"));

  //      PublicIncludePaths.AddRange(
		//	new string[] {
		//		"GrammerAnalysis/Public"
		//		// ... add public include paths required here ...
		//	}
		//	);
				
		
		//PrivateIncludePaths.AddRange(
		//	new string[] {
		//		"GrammerAnalysis/Private",
		//		// ... add other private include paths required here ...
		//	}
		//	);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
                "DecimalNumber"
				// ... add other public dependencies that you statically link with here ...
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"CoreUObject",
				"Engine"
				// ... add private dependencies that you statically link with here ...	
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);
	}
}
