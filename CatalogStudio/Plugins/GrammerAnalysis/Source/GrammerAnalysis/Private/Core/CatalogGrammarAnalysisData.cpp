// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/CatalogGrammarAnalysisData.h"

#include "DecimalMath.h"

DEFINE_LOG_CATEGORY(CatalogGrammarAnalysisLog);

FCatalogGrammarAnalysisData::FCatalogGrammarAnalysisData()
	: GrammarType(ECatalogGrammarType::E_None)
{
}

FCatalogGrammarAnalysisData::FCatalogGrammarAnalysisData(const FDecimal& InDecimalValue)
	: DecimalValue(InDecimalValue)
	, GrammarType(ECatalogGrammarType::E_Decimal)
{
}

FCatalogGrammarAnalysisData::FCatalogGrammarAnalysisData(const FString& InDescriptionValue)
	: DescriptionValue(InDescriptionValue)
	, GrammarType(ECatalogGrammarType::E_Description)
{
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator=(const FCatalogGrammarAnalysisData& Other)
{
	if(Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue = Other.DecimalValue;
		GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		DescriptionValue = Other.DescriptionValue;
		GrammarType = ECatalogGrammarType::E_Description;
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator+=(const FCatalogGrammarAnalysisData& Other)
{
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue += Other.DecimalValue;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		DescriptionValue.Append(Other.DescriptionValue);
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator-=(const FCatalogGrammarAnalysisData& Other)
{
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue -= Other.DecimalValue;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		DescriptionValue.RemoveFromEnd(Other.DescriptionValue);
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator*=(const FCatalogGrammarAnalysisData& Other)
{
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue -= Other.DecimalValue;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator *="));
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator/=(const FCatalogGrammarAnalysisData& Other)
{
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue /= Other.DecimalValue;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator /="));
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::operator%=(const FCatalogGrammarAnalysisData& Other)
{
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		DecimalValue = DecimalValue % Other.DecimalValue;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator modf"));
	}
	return *this;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator+(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = DecimalValue + Other.DecimalValue;
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		Result.DescriptionValue.Append(Other.DescriptionValue);
		Result.GrammarType = ECatalogGrammarType::E_Description;
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator-(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = DecimalValue - Other.DecimalValue;
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		Result.DescriptionValue.RemoveFromStart(Other.DescriptionValue);
		Result.GrammarType = ECatalogGrammarType::E_Description;
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator*(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = DecimalValue * Other.DecimalValue;
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator *"));
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator/(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = DecimalValue / Other.DecimalValue;
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator /"));
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator%(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::RemainderCustom(DecimalValue, Other.DecimalValue);
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator modf"));
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator-() const
{
	FCatalogGrammarAnalysisData Result;
	if (GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = -DecimalValue;
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator -(Reversal)"));
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator!() const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res =  FDecimalMath::IsNearlyZero(DecimalValue, FDecimal(FString(TEXT("0.01"))));
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator==(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue == Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		bool Res = DescriptionValue.Equals(Other.DescriptionValue);
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Description;
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator!=(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue != Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		bool Res = !DescriptionValue.Equals(Other.DescriptionValue);
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Description;
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator>(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue > Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator >"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator>=(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue >= Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator >="));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator<(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue < Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator <"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator<=(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res = DecimalValue <= Other.DecimalValue;
		Result.DecimalValue = FDecimal(static_cast<int32>(Res));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator <="));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator&&(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res1 = !this->IsNearlyZero(0.01);
		bool Res2 = !Other.IsNearlyZero(0.01);
		Result.DecimalValue = FDecimal(static_cast<int32>(Res1 && Res2));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator &&"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::operator||(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		bool Res1 = !this->IsNearlyZero(0.01);
		bool Res2 = !Other.IsNearlyZero(0.01);
		Result.DecimalValue = FDecimal(static_cast<int32>(Res1 || Res2));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator ||"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Max(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Max(DecimalValue, Other.DecimalValue);
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator MAX"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Min(const FCatalogGrammarAnalysisData& Other) const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal && Other.GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Min(DecimalValue, Other.DecimalValue);
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description && Other.GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator MIN"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Sin() const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Sin(FDecimalMath::DegreesToRadians(FDecimalMath::UnwindDegrees(DecimalValue)));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator SIN"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Cos() const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Cos(FDecimalMath::DegreesToRadians(FDecimalMath::UnwindDegrees(DecimalValue)));
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator COS"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Sqrt() const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Sqrt(DecimalValue);
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator SQRT"));
	}

	return Result;
}

FCatalogGrammarAnalysisData FCatalogGrammarAnalysisData::Abs() const
{
	FCatalogGrammarAnalysisData Result;
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		Result.DecimalValue = FDecimalMath::Abs(DecimalValue);
		Result.GrammarType = ECatalogGrammarType::E_Decimal;
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		UE_LOG(CatalogGrammarAnalysisLog, Log, TEXT("description has no operator ABS"));
	}

	return Result;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::Append(const FCatalogGrammarAnalysisData& Other)
{
	GrammarType = ECatalogGrammarType::E_Description;
	DescriptionValue = GetDescription();
	FString OtherDescription = Other.GetDescription();
	if (DescriptionValue.IsEmpty())
	{
		DescriptionValue = OtherDescription;
	}
	else
	{
		DescriptionValue.Append(OtherDescription);
	}
	return *this;
}

FCatalogGrammarAnalysisData& FCatalogGrammarAnalysisData::ConvertToDescription()
{
	if(GrammarType == ECatalogGrammarType::E_Description)
	{
		DescriptionValue.InsertAt(0, TEXT("\""));
		DescriptionValue.AppendChar('\"');
	}
	return *this;
}

bool FCatalogGrammarAnalysisData::IsNumeric() const
{
	return GrammarType == ECatalogGrammarType::E_Decimal;
}

bool FCatalogGrammarAnalysisData::IsPositive() const
{
	if (GrammarType == ECatalogGrammarType::E_Decimal)
	{
		return DecimalValue > FDecimal(0.0);
	}
	return false;
}

bool FCatalogGrammarAnalysisData::IsNearlyZero(const double& Tolerance) const
{
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		return FDecimalMath::IsNearlyZero(DecimalValue, FDecimal(Tolerance));
	}
	return true;
}

FString FCatalogGrammarAnalysisData::GetValue(const int32& DecimalNum) const
{
	if(GrammarType == ECatalogGrammarType::E_Decimal)
	{
		return DecimalValue.ToString(DecimalNum);
	}
	else if(GrammarType == ECatalogGrammarType::E_Description)
	{
		return DescriptionValue;
	}

	return TEXT("");
}

FString FCatalogGrammarAnalysisData::GetDescription() const
{
	if (GrammarType == ECatalogGrammarType::E_Decimal)
	{
		return FCatalogGrammarAnalysisData::FormatDescription(DecimalValue.ToString(16));
	}
	else if (GrammarType == ECatalogGrammarType::E_Description)
	{
		return FCatalogGrammarAnalysisData::FormatDescription(DescriptionValue);
	}
	return TEXT("");
}

FString FCatalogGrammarAnalysisData::FormatDescription(const FString& InDes)
{
	FString FormatRes = InDes;
	if(FormatRes.IsNumeric())
	{
		double Temp = FCString::Atod(*FormatRes);
		int32 CeilTemp = FMath::CeilToInt(Temp);
		if(FMath::IsNearlyZero(Temp - static_cast<double>(CeilTemp), 0.01))
		{
			FormatRes = FString::FromInt(CeilTemp);
		}
	}
	else if(FormatRes.Len() > 2)
	{
		if ('"' == FormatRes[0] && '"' == FormatRes[FormatRes.Len() - 1])
			FormatRes = FormatRes.Mid(1, FormatRes.Len() - 2);
	}
	return FormatRes;
}

