// Copyright Epic Games, Inc. All Rights Reserved.

#include "GrammerAnalysisSubsystem.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "DecimalMath.h"
#include "Decimal.h"
#include "Core/CatalogGrammarAnalysisData.h"

#define LOCTEXT_NAMESPACE "UGrammerAnalysisSubsystem"

#define END_SYMBOL ('$')
#define START_CHARACTER (static_cast<uint8>('S'))
#define END_CHARACTER (static_cast<uint8>(END_SYMBOL))
#define EMPTY_CHARACTER (static_cast<uint8>('@'))

DEFINE_LOG_CATEGORY(GrammerAnalysisLog);

void PrintStack(const FString& StackName, const TArray<FString>& InStack)
{
	//FString Content;
	//for (auto& Iter : InStack)
	//{
	//	Content.Append(TEXT(","));
	//	Content.Append(Iter);
	//}
	//UE_LOG(GrammerAnalysisLog, Warning, TEXT("Stack %s content is : %s "), *StackName, *Content);
}

void UGrammerAnalysisSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	UGameInstanceSubsystem::Initialize(Collection);
	//------------------------------------------------------------------
	TArray<int8> OperatorPriorityMap[20] =
	{//行表示栈顶，列表示队列中。1表示栈顶优先级高，-1表示栈顶优先级低。
	 //{  +,	-, ^(负号), *,	/,	%,	(,	), !,	>,	<,	>=,	<=,	!=,	==,	&&,	||,	f(,	,	#}
		{ 1,	1,	-1,	   -1,	-1,	-1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//+
		{ 1,	1,	-1,    -1,	-1,	-1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//-
		{ 1,	1,	-1,	   1,	1,	1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//^(负号)
		{ 1,	1,	-1,	   1,	1,	1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//*
		{ 1,	1,	-1,	   1,	1,	1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },///
		{ 1,	1,	-1,	   1,	1,	1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//%
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	0, -1,  -1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	2 },//(
		{ 2,	2,	2,	  2,	2,	2,	2,	2,  2,  2,	2,	2,	2,	2,	2,	2,	2,	2,	2,	2 },//)
		{ 1,	1,	-1,	   1,	1,	1,	-1,	1, -1,  1,	1,	1,	1,	1,	1,	1,	1,	-1,	1,	1 },//!
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//>
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//<
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//>=
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//<=
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//!=
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  2,	2,	2,	2,	2,	2,	1,	1,	-1,	1,	1 },//==
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  -1,	-1,	-1,	-1,	-1,	-1,	1,	1,	-1,	1,	1 },//&&
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  -1,	-1,	-1,	-1,	-1,	-1,	1,	1,	-1,	1,	1 },//||
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  -1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	1 },//f(
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	1, -1,  -1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	1 },//,
		{ -1,	-1,	-1,	  -1,	-1,	-1,	-1,	2, -1,  -1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	-1,	0 },//#
	};
	for (int32 i = 0; i < 20; ++i)
		GOperatorPriorityMap[i] = OperatorPriorityMap[i];
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	TArray<TSharedPtr<struct FGrammer>> GrammerInfo;
	TArray<uint8> UnTerminalSymbols;
	bool Res = ReadGrammerFromFile(GrammerInfo, UnTerminalSymbols, TerminalSymbols);
	if (Res)
	{
		Res = CreateAnalyticalTable(GrammerInfo, UnTerminalSymbols, TerminalSymbols);
		if (Res)
		{
			FString ErrorMessage;
			Res = IsGrammerCorrect(AnalyticalTable, TerminalSymbols, ErrorMessage);
			if (!Res)
			{
				AnalyticalTable.Empty();
				UE_LOG(GrammerAnalysisLog, Log, TEXT("%s"), *ErrorMessage);
				return;
			}
			//UE_LOG(GrammerAnalysisLog, Log, TEXT("CreateAnalyticalTable success!"));
			return;
		}
		UE_LOG(GrammerAnalysisLog, Log, TEXT("CreateAnalyticalTable error!"));
		return;
	}
	UE_LOG(GrammerAnalysisLog, Log, TEXT("ReadGrammerFromFile error!"));
	return;
}

void UGrammerAnalysisSubsystem::Deinitialize()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	AnalyticalTable.Empty();
	TerminalSymbols.Empty();
	for (int32 i = 0; i < 18; ++i)
	{
		GOperatorPriorityMap[i].Empty();
	}
}

bool UGrammerAnalysisSubsystem::ReadGrammerFromFile(TArray<TSharedPtr<struct FGrammer>>& InGrammer, TArray<uint8>& InUnTerminalSymbols, TArray<uint8>& InTerminalSymbols)
{
	FString GrammerFilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectConfigDir() + FString(TEXT("grammer.bin")));
	if (FPaths::FileExists(GrammerFilePath))
	{
		TArray<FString> Grammers;
		bool Res = FFileHelper::LoadFileToStringArray(Grammers, *GrammerFilePath);
		for (auto GrammerIter : Grammers)
		{
			if (GrammerIter.Len() < 4) continue;
			for (int32 i = 0; i < GrammerIter.Len(); ++i)
			{
				if (1 == i || 2 == i) continue;
				if (GrammerIter[i] >= 'A' && GrammerIter[i] <= 'Z')
				{
					InUnTerminalSymbols.AddUnique(static_cast<uint8>(GrammerIter[i]));
				}
				else
				{
					InTerminalSymbols.AddUnique(static_cast<uint8>(GrammerIter[i]));
				}
			}
			FString value = GrammerIter.Right(GrammerIter.Len() - 3);
			TSharedPtr<struct FGrammer> NewGrammerItem = MakeShareable<FGrammer>(new FGrammer(GrammerIter[0], value));
			InGrammer.Push(NewGrammerItem);
		}
		return InGrammer.Num() > 0;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("UGrammerAnalysisSubsystem::ReadGrammerFromFile open file failed"));
	}
	return false;
}


bool UGrammerAnalysisSubsystem::FindFirst(const FString& InCharacters, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFirst)
{
	const uint8 FirstCharacter = static_cast<uint8>(InCharacters[0]);
	if (INDEX_NONE != InVT.Find(FirstCharacter))
	{//推导的第一个字符为终结符,将此终结符并入集合中，并跳过此推导中的其他字符。
		OutFirst.Add(FirstCharacter);
		return true;
	}
	else if (INDEX_NONE != InVN.Find(FirstCharacter))
	{
		int32 i = 0;
		int32 EmptyCount = 0;
		while (i < InCharacters.Len())
		{
			TSet<uint8> NewFirst;
			const uint8 CharacterAtI = static_cast<uint8>(InCharacters[i]);
			if (INDEX_NONE != InVN.Find(CharacterAtI))
			{
				if (!InVNStatus[CharacterAtI])
				{
					InVNStatus[CharacterAtI] = true;
					if (FindFirst(CharacterAtI, InGrammer, InVN, InVT, InVNStatus, NewFirst))
					{
						if (!NewFirst.Contains(EMPTY_CHARACTER))
						{
							OutFirst = OutFirst.Union(NewFirst);
							break;
						}
						else
						{
							NewFirst.Remove(EMPTY_CHARACTER);
							OutFirst = OutFirst.Union(NewFirst);
							++EmptyCount;
						}
					}
					else
					{
						return false;
					}
				}
			}
			else
			{
				NewFirst.Add(CharacterAtI);
				OutFirst = OutFirst.Union(NewFirst);
				break;
			}
			++i;
		}
		if (EmptyCount == InCharacters.Len())
		{
			OutFirst.Add(EMPTY_CHARACTER);
		}
		return true;
	}
	else
	{
		return false;
	}
}

bool UGrammerAnalysisSubsystem::FindFirst(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFirst)
{
	if (InGrammer.Num() <= 0 || InVN.Num() <= 0 || InVT.Num() <= 0)
		return false;
	if (INDEX_NONE != InVT.Find(static_cast<uint8>(InCharacter)))
	{//如果是终结符，FIRST集是它本身。
		OutFirst.Add(static_cast<uint8>(InCharacter));
		return true;
	}
	else if (INDEX_NONE != InVN.Find(static_cast<uint8>(InCharacter)))
	{//如果是非终结符
		InVNStatus[static_cast<uint8>(InCharacter)] = true;
		for (auto& item : InGrammer)
		{
			if (InCharacter == item->Key)
			{
				if (false == FindFirst(item->Value, InGrammer, InVN, InVT, InVNStatus, OutFirst))
					return false;
			}
		}
		return true;
	}
	return false;
}

bool UGrammerAnalysisSubsystem::FindFirst(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TSet<uint8>& OutFirst)
{
	TMap<uint8, bool> VNStatus;
	for (auto& item : InVN)
	{
		VNStatus.Add(item, false);
	}
	return FindFirst(InCharacter, InGrammer, InVN, InVT, VNStatus, OutFirst);
}

bool UGrammerAnalysisSubsystem::FindFollow(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFollow)
{
	if (InVNStatus[InCharacter])
		return true;
	InVNStatus[InCharacter] = true;
	const TSharedPtr<struct FGrammer>& Start = InGrammer[0];
	if (InCharacter == Start->Key)
	{//如果是开始符号，把$放入FOLLOW集中。
		OutFollow.Add(END_CHARACTER);
		return true;
	}
	for (auto& item : InGrammer)
	{
		int32 i = 0;
		while (i < item->Value.Len())
		{
			const uint8 CharacterAtI = static_cast<uint8>(item->Value[i]);
			if (InCharacter == CharacterAtI)
			{
				if (i == item->Value.Len() - 1)
				{
					if (false == FindFollow(item->Key, InGrammer, InVN, InVT, InVNStatus, OutFollow))
						return false;
				}
				else
				{
					TSet<uint8> First;
					FString SubStr(item->Value.Right(item->Value.Len() - i - 1));
					TMap<uint8, bool> VNStatus;
					for (auto& Iter : InVN)
					{
						VNStatus.Add(Iter, false);
					}
					if (FindFirst(SubStr, InGrammer, InVN, InVT, VNStatus, First))
					{
						OutFollow = OutFollow.Union(First);
						OutFollow.Remove(EMPTY_CHARACTER);
						if (First.Contains(EMPTY_CHARACTER))
						{
							if (false == FindFollow(item->Key, InGrammer, InVN, InVT, InVNStatus, OutFollow))
								return false;
						}
					}
					else
					{
						return false;
					}
				}
			}
			++i;
		}
	}
	return true;
}

bool UGrammerAnalysisSubsystem::FindFollow(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TSet<uint8>& OutFollow)
{
	if (InGrammer.Num() <= 0 || InVN.Num() <= 0 || InVT.Num() <= 0)
		return false;
	TMap<uint8, bool> VNStatus;
	for (auto& item : InVN)
	{
		VNStatus.Add(item, false);
	}
	return FindFollow(InCharacter, InGrammer, InVN, InVT, VNStatus, OutFollow);
}

bool UGrammerAnalysisSubsystem::CreateAnalyticalTable(const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT)
{
	if (InGrammer.Num() <= 0 || InVN.Num() <= 0 || InVT.Num() <= 0)
		return false;

	TMap<uint8, bool> VNStatus;
	for (auto& item : InVN)
	{
		VNStatus.Add(item, false);
		AnalyticalTable.Add(item, MakeShareable<struct FTableRow>(new FTableRow()));
	}
	for (auto& item : InGrammer)
	{
		for (auto& statu : VNStatus)
		{
			statu.Value = false;
		}
		TSet<uint8> First;
		if (FindFirst(item->Value, InGrammer, InVN, InVT, VNStatus, First))
		{
			for (auto& first : First)
			{
				if (EMPTY_CHARACTER != first)
				{
					AnalyticalTable[item->Key]->RowItemList.Add(MakeShareable<FTableRowItem>(new FTableRowItem(first, item)));
				}
			}
			if (First.Contains(EMPTY_CHARACTER))
			{
				TSet<uint8> Follow;
				if (FindFollow(item->Key, InGrammer, InVN, InVT, Follow))
				{
					for (auto& follow : Follow)
					{
						AnalyticalTable[item->Key]->RowItemList.Add(MakeShareable<FTableRowItem>(new FTableRowItem(follow, item)));
					}
				}
				else
				{
					return false;
				}
			}
		}
		else
		{
			return false;
		}
	}
	return true;
}

bool UGrammerAnalysisSubsystem::IsGrammerCorrect(const TMap<uint8, TSharedPtr<struct FTableRow>>& InAnalysisTable, const TArray<uint8>& InVT, FString& ErrorMessage)
{
	if (InAnalysisTable.Num() <= 0)
		return false;
	for (auto& item : InAnalysisTable)
	{
		for (auto& t : InVT)
		{
			int32 count = item.Value->GetColumnCount(t);
			if (count >= 2)
			{
				ErrorMessage = FString::Printf(TEXT("%c has %d exps"), t, count);
				return false;
			}
		}
	}
	return true;
}

bool UGrammerAnalysisSubsystem::GrammerAnalysis(const FString& InSentence, FString& OutErrorMessage)
{
	int32 i = 0;
	TArray<uint8> AnalysisStack;
	AnalysisStack.Push(END_CHARACTER);
	AnalysisStack.Push(START_CHARACTER);
	while (END_CHARACTER != AnalysisStack.Top())
	{
		if (EMPTY_CHARACTER == AnalysisStack.Top())
		{
			AnalysisStack.Pop();
		}
		else if (static_cast<uint8>('t') == AnalysisStack.Top() || AnalysisStack.Top() == static_cast<uint8>(InSentence[i]))
		{
			AnalysisStack.Pop();
			++i;
			i = i >= InSentence.Len() ? InSentence.Len() - 1 : i;
		}
		else if (INDEX_NONE != TerminalSymbols.Find(AnalysisStack.Top()))
		{
			OutErrorMessage = FString::Printf(TEXT("Unkonwn Terminal symbol %c"), AnalysisStack.Top());
			return false;
		}
		else
		{
			TSharedPtr<struct FTableRowItem> TableRow = nullptr;
			if ((static_cast<uint8>('G') == AnalysisStack.Top() || static_cast<uint8>('H') == AnalysisStack.Top()) && (static_cast<uint8>('\"') != static_cast<uint8>(InSentence[i])))
			{//G用于文本时的归约,t代表任意字符。
				TableRow = AnalyticalTable[AnalysisStack.Top()]->FindTableRow(static_cast<uint8>('t'));
			}
			else
			{
				TableRow = AnalyticalTable[AnalysisStack.Top()]->FindTableRow(static_cast<uint8>(InSentence[i]));
			}
			if (TableRow.IsValid())
			{
				AnalysisStack.Pop();
				int32 k = TableRow->Grammer->Value.Len() - 1;
				while (k >= 0)
				{
					AnalysisStack.Push(static_cast<uint8>(TableRow->Grammer->Value[k]));
					--k;
				}
			}
			else
			{
				OutErrorMessage = FString::Printf(TEXT("Symbol %c grammer error!"), InSentence[i]);
				return false;
			}
		}
	}

	return InSentence.Len() - 1 == i;
}


bool UGrammerAnalysisSubsystem::IsExpressionCorrect(const TArray<FString>& InWords, FString& OutErrorMessage)
{
	if (AnalyticalTable.Num() > 0)
	{
		if (InWords.Num() > 0)
		{
			FString Exp;
			if (ConvertWordsToLanguageTerminalSymbols(InWords, Exp, OutErrorMessage))
			{
				Exp.AppendChar('$');
				return GrammerAnalysis(Exp, OutErrorMessage);
			}
			return false;
		}
		FText EmptyMsg = NSLOCTEXT(LOCTEXT_NAMESPACE, "AnalyticalTableEmpty", "AnalyticalTable is empty,you have to restart the program!");
		OutErrorMessage = EmptyMsg.ToString();
		return false;
	}
	FText EmptyMsg = NSLOCTEXT(LOCTEXT_NAMESPACE, "AnalyticalTableEmpty", "AnalyticalTable is empty,you have to restart the program!");
	OutErrorMessage = EmptyMsg.ToString();
	return false;
}

FString UGrammerAnalysisSubsystem::MapStringToTerminalSymbol(const FString& InString)
{
	if (InString.Equals(TEXT("if"), ESearchCase::IgnoreCase))
	{
		return TEXT("a");
	}
	if (InString.Equals(TEXT("cond"), ESearchCase::IgnoreCase))
	{
		return TEXT("b");
	}
	if (InString.Equals(TEXT("max"), ESearchCase::IgnoreCase))
	{
		return TEXT("c");
	}
	if (InString.Equals(TEXT("min"), ESearchCase::IgnoreCase))
	{
		return TEXT("d");
	}
	if (InString.Equals(TEXT("sin"), ESearchCase::IgnoreCase))
	{
		return TEXT("e");
	}
	if (InString.Equals(TEXT("cos"), ESearchCase::IgnoreCase))
	{
		return TEXT("f");
	}
	if (InString.Equals(TEXT("sqrt"), ESearchCase::IgnoreCase))
	{
		return TEXT("g");
	}
	if (InString.Equals(TEXT("abs"), ESearchCase::IgnoreCase))
	{
		return TEXT("h");
	}
	if (InString.Equals(TEXT(">"), ESearchCase::IgnoreCase))
	{
		return TEXT("i");
	}
	if (InString.Equals(TEXT("<"), ESearchCase::IgnoreCase))
	{
		return TEXT("j");
	}
	if (InString.Equals(TEXT("comb"), ESearchCase::IgnoreCase))
	{
		return TEXT("k");
	}
	return InString;
}

bool UGrammerAnalysisSubsystem::ConvertWordsToLanguageTerminalSymbols(const TArray<FString>& InWords, FString& OutFormatExpression, FString& OutErrorMessage)
{
	if (InWords.Num() <= 0)
	{
		FText EmptyMsg = NSLOCTEXT(LOCTEXT_NAMESPACE, "WordsEmpty", "No word passed in,make sure lexical analysis is correct!");
		OutErrorMessage = EmptyMsg.ToString();
		return false;
	}
	for (auto& iter : InWords)
	{
		OutFormatExpression.Append(MapStringToTerminalSymbol(iter));
	}
	return true;
}

#undef START_CHARACTER
#undef END_CHARACTER
#undef EMPTY_CHARACTER
#undef END_SYMBOL

bool UGrammerAnalysisSubsystem::IsDigitalCharacter(const TCHAR& InCharacter)
{
	return (InCharacter >= '0' && InCharacter <= '9') || ('.' == InCharacter);
}

bool UGrammerAnalysisSubsystem::IsMathematicalOperator(const TCHAR& InCharacter)
{
	return ('+' == InCharacter) || ('-' == InCharacter) || ('^' == InCharacter) || ('*' == InCharacter) || ('/' == InCharacter) || ('%' == InCharacter);
}

bool UGrammerAnalysisSubsystem::IsMathematicalOperator(const FString& InOperator)
{
	return InOperator.Equals(TEXT("+")) || InOperator.Equals(TEXT("-")) || InOperator.Equals(TEXT("^")) || InOperator.Equals(TEXT("*")) || InOperator.Equals(TEXT("/")) || InOperator.Equals(TEXT("%"));
}

bool UGrammerAnalysisSubsystem::IsLogicOperator(const TCHAR& InCharacter)
{
	return ('!' == InCharacter) || ('<' == InCharacter) || ('>' == InCharacter) || ('&' == InCharacter) || ('|' == InCharacter) || ('=' == InCharacter);
}

bool UGrammerAnalysisSubsystem::IsValidLogicOperator(const FString& InOperator)
{
	return InOperator.Equals(TEXT("!=")) || InOperator.Equals(TEXT(">")) || InOperator.Equals(TEXT(">=")) || InOperator.Equals(TEXT("<")) || InOperator.Equals(TEXT("<=")) || InOperator.Equals(TEXT("==")) || InOperator.Equals(TEXT("&&")) || InOperator.Equals(TEXT("||")) || InOperator.Equals(TEXT("!"));
}

bool UGrammerAnalysisSubsystem::IsSeparator(const TCHAR& InCharacter)
{
	return (',' == InCharacter) || ('(' == InCharacter) || (')' == InCharacter) || ('"' == InCharacter);
}

bool UGrammerAnalysisSubsystem::IsSeparator(const FString& InOperator)
{
	return InOperator.Equals(TEXT(",")) || InOperator.Equals(TEXT("(")) || InOperator.Equals(TEXT(")"));
}

bool UGrammerAnalysisSubsystem::IsLetter(const TCHAR& InCharacter)
{
	return (InCharacter >= 'a' && InCharacter <= 'z') || (InCharacter >= 'A' && InCharacter <= 'Z');
}

bool UGrammerAnalysisSubsystem::IsUnsupportCharacter(const TCHAR& InCharacter)
{
	return (':' == InCharacter) || ('[' == InCharacter) || (']' == InCharacter) || ('{' == InCharacter) || ('}' == InCharacter) || ('`' == InCharacter) || ('~' == InCharacter) || ('@' == InCharacter) || ('#' == InCharacter) || ('$' == InCharacter) || ('^' == InCharacter) || ('_' == InCharacter) || (';' == InCharacter) || ('\'' == InCharacter) || ('?' == InCharacter);
}

int32 UGrammerAnalysisSubsystem::GetOperatorOprandNum(const FString& InOperator)
{
	if (InOperator.Equals(TEXT("^"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("!"), ESearchCase::IgnoreCase))
		return 1;
	return 2;
}

bool UGrammerAnalysisSubsystem::LexicalAnalysis(const FString& InExpression, TArray<FString>& OutWords, FString& OutErrorMessage)
{
	enum ELexicalState
	{
		Undefine,
		Parameter,
		Const,
		Logic,
		Text
	};
	FString OriginalExpression = InExpression;
	while (OriginalExpression.Len() > 0 && '.' == OriginalExpression[0])
		OriginalExpression.RemoveAt(0);

	if (OriginalExpression.IsEmpty())
	{
		OutErrorMessage = FString::Printf(TEXT("--- Expression is empty ---"));
		return false;
	}
	//if (OriginalExpression.IsNumeric()) //此处是0.3.4期间添加同期注释
	//{
	//	OutErrorMessage = FString::Printf(TEXT("--- Expression is Number ---"));
	//	return false;
	//}

	while (OriginalExpression.Len() > 0 && ('\r' == OriginalExpression[0] || '\n' == OriginalExpression[0] || ' ' == OriginalExpression[0]))
		OriginalExpression.RemoveAt(0);

	if (OriginalExpression.IsEmpty())
	{
		OutErrorMessage = FString::Printf(TEXT("--- Expression is empty ---"));
		return true;
	}
	int32 i = 0;
	ELexicalState LexicalState = ELexicalState::Undefine;
	FString Word;
	bool HasComment = false;
	while (i < OriginalExpression.Len())
	{
		//UE_LOG(GrammerAnalysisLog, Warning, TEXT("%c"), OriginalExpression[i]);
		if ('\n' == OriginalExpression[i] || '\r' == OriginalExpression[i] || ' ' == OriginalExpression[i])
		{
			if (HasComment || ELexicalState::Text == LexicalState)
				Word.AppendChar(OriginalExpression[i]);
			++i;
			continue;
		}
		if (IsDigitalCharacter(OriginalExpression[i]))
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				if (ELexicalState::Logic == LexicalState)
				{
					OutWords.Add(Word);
					Word.Empty();
					LexicalState = ELexicalState::Const;
				}
				else if (ELexicalState::Undefine == LexicalState)
				{
					LexicalState = ELexicalState::Const;
				}
			}
			Word.AppendChar(OriginalExpression[i]);
		}
		else if (IsMathematicalOperator(OriginalExpression[i]))
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				if (0 < Word.Len())
				{
					OutWords.Add(Word);
					Word.Empty();
				}
				Word.AppendChar(OriginalExpression[i]);
				OutWords.Add(Word);
				Word.Empty();
				LexicalState = ELexicalState::Undefine;
			}
			else
			{
				Word.AppendChar(OriginalExpression[i]);
			}
		}
		else if (IsLogicOperator(OriginalExpression[i]))
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				if (ELexicalState::Logic == LexicalState)
				{
					FString LogicOperator(Word);
					LogicOperator.AppendChar(OriginalExpression[i]);
					if (IsValidLogicOperator(LogicOperator))
					{
						OutWords.Add(LogicOperator);
						Word.Empty();
						LexicalState = ELexicalState::Undefine;
					}
					else
					{
						OutErrorMessage = FString::Printf(TEXT("--- Unknown operator %s ---"), *LogicOperator);
						return false;
					}
				}
				else if (ELexicalState::Logic != LexicalState)
				{
					if (Word.Len() > 0)
					{
						OutWords.Add(Word);
						Word.Empty();
					}
					Word.AppendChar(OriginalExpression[i]);
					LexicalState = ELexicalState::Logic;
				}
			}
			else
			{
				Word.AppendChar(OriginalExpression[i]);
			}
		}
		else if (IsSeparator(OriginalExpression[i]))
		{
			if (ELexicalState::Text == LexicalState)
			{
				if ('"' == OriginalExpression[i])
				{
					Word.AppendChar(OriginalExpression[i]);
					OutWords.Add(Word);
					Word.Empty();
					LexicalState = ELexicalState::Undefine;
				}
				else
				{
					Word.AppendChar(OriginalExpression[i]);
				}
			}
			else if (HasComment)
			{
				Word.AppendChar(OriginalExpression[i]);
			}
			else
			{
				if (Word.Len() > 0)
				{
					OutWords.Add(Word);
					Word.Empty();
				}
				if ('"' == OriginalExpression[i])
				{
					Word.AppendChar(OriginalExpression[i]);
					LexicalState = ELexicalState::Text;
				}
				else
				{
					Word.AppendChar(OriginalExpression[i]);
					OutWords.Add(Word);
					Word.Empty();
					LexicalState = ELexicalState::Undefine;
				}
			}
		}
		else if (IsLetter(OriginalExpression[i])/* || !IsUnsupportCharacter(OriginalExpression[i]) Fix bug CATALOG-538*/)
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				if (ELexicalState::Const == LexicalState || ELexicalState::Logic == LexicalState)
				{
					OutWords.Add(Word);
					Word.Empty();
				}
				LexicalState = ELexicalState::Parameter;
			}
			Word.AppendChar(OriginalExpression[i]);
		}
		else if ('#' == OriginalExpression[i])
		{
			if (ELexicalState::Text != LexicalState)
				HasComment = !HasComment;
			Word.AppendChar(OriginalExpression[i]);
		}
		else if (IsUnsupportCharacter(OriginalExpression[i]))
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				OutErrorMessage = FString::Printf(TEXT("--- Unsupperted character %c  "), OriginalExpression[i]);
				return false;
			}
			Word.AppendChar(OriginalExpression[i]);
		}
		else
		{
			if (ELexicalState::Text != LexicalState && !HasComment)
			{
				OutErrorMessage = FString::Printf(TEXT("--- Unrecognize character %c  "), OriginalExpression[i]);
				return false;
			}
			Word.AppendChar(OriginalExpression[i]);
		}
		++i;
	}
	if (ELexicalState::Text == LexicalState)
	{
		OutErrorMessage = FString::Printf(TEXT("--- Unfound \"\"\" ---"));
		return false;
	}
	if (HasComment)
	{
		Word.AppendChar('#');
	}
	if (Word.Len() > 0)
		OutWords.Add(Word);
	PrintStack(TEXT("OutWords"), OutWords);
	if (OutWords.Num() > 0)
	{
		return true;
	}
	OutErrorMessage = FString::Printf(TEXT("--- No result ---"));
	return false;
}

FString UGrammerAnalysisSubsystem::RemoveComment(const FString& InWord, TArray<TPair<int32, FString>>& Comments)
{
	int32 StartCommentIndex = INDEX_NONE;
	for (int32 i = 0; i < InWord.Len(); ++i)
	{
		if ('#' != InWord[i])
		{
			if ((INDEX_NONE == StartCommentIndex) && (' ' == InWord[i] || '\r' == InWord[i] || '\n' == InWord[i]))
			{
				TPair<int32, FString> NewComment(i, InWord.Mid(i, 1));
				Comments.Add(NewComment);
			}
			continue;
		}
		if (INDEX_NONE == StartCommentIndex)
		{
			StartCommentIndex = i;
		}
		else
		{
			TPair<int32, FString> NewComment(StartCommentIndex, InWord.Mid(StartCommentIndex, i - StartCommentIndex + 1));
			Comments.Add(NewComment);
			StartCommentIndex = INDEX_NONE;
		}
	}
	if (INDEX_NONE != StartCommentIndex)
	{
		TPair<int32, FString> NewComment(StartCommentIndex, InWord.Mid(StartCommentIndex, InWord.Len() - StartCommentIndex));
		Comments.Add(NewComment);
	}
	FString Result = InWord;
	for (int32 i = Comments.Num() - 1; i >= 0; --i)
	{
		Result.RemoveAt(Comments[i].Key, Comments[i].Value.Len());
	}
	return Result;
}

FString UGrammerAnalysisSubsystem::AddComment(const FString& InOriginalWord, const FString& InCleanWord)
{
	if (InOriginalWord.Equals(InCleanWord, ESearchCase::IgnoreCase))
		return InCleanWord;
	if ('"' == InOriginalWord[0])
	{
		int32 Index = INDEX_NONE;
		if (InOriginalWord.Right(InOriginalWord.Len() - 1).FindChar('"', Index))
		{
			return InCleanWord + InOriginalWord.Right(InOriginalWord.Len() - Index - 2);
		}
	}
	else if ('"' == InOriginalWord[InOriginalWord.Len() - 1])
	{
		int32 Index = INDEX_NONE;
		if (InOriginalWord.Left(InOriginalWord.Len() - 1).FindLastChar('"', Index))
		{
			return InOriginalWord.Left(Index) + InCleanWord;
		}
	}
	else
	{
		int32 FirstSharp = INDEX_NONE;
		int32 LastSharp = INDEX_NONE;
		InOriginalWord.FindChar('#', FirstSharp);
		InOriginalWord.FindLastChar('#', LastSharp);
		if (INDEX_NONE != FirstSharp && INDEX_NONE != LastSharp && FirstSharp != LastSharp)
		{
			return InCleanWord.Left(FirstSharp) + InOriginalWord.Mid(FirstSharp, LastSharp - FirstSharp + 1) + InCleanWord.Right(InCleanWord.Len() - FirstSharp);
		}
	}
	return InCleanWord;
}

FString UGrammerAnalysisSubsystem::RemoveAllSpecialCharacter(const FString& InWordWithoutComment)
{
	int32 Len = InWordWithoutComment.Len();
	int32 Pos = 0;
	while (Pos < Len && (' ' == InWordWithoutComment[Pos] || '\r' == InWordWithoutComment[Pos] || '\n' == InWordWithoutComment[Pos] || '\0' == InWordWithoutComment[Pos]))
		++Pos;
	while (Pos < Len && (' ' == InWordWithoutComment[Len - 1] || '\r' == InWordWithoutComment[Len - 1] || '\n' == InWordWithoutComment[Len - 1] || '\0' == InWordWithoutComment[Pos]))
		--Len;
	if (Len <= Pos)
		return TEXT("");
	return InWordWithoutComment.Mid(Pos, Len - Pos);
}

bool UGrammerAnalysisSubsystem::FormatWords(const TArray<FString>& InWords, TArray<FString>& OutFormatWords, FString& OutErrorMessage)
{
	int32 i = 0;
	while (i < InWords.Num())
	{
		FString CleanWord = RemoveAllSpecialCharacter(InWords[i]);
		if (CleanWord.IsEmpty())
		{
			++i;
			continue;
		}
		if (IsFunctionName(CleanWord))
		{
			OutFormatWords.Add(AddComment(InWords[i], CleanWord.ToUpper()));
			const int32 NextIndex = i + 1;
			if (!InWords.IsValidIndex(NextIndex) || !InWords[NextIndex].Equals(TEXT("(")))
			{
				OutErrorMessage = FString::Printf(TEXT("Function %s must fellowed by ("), *CleanWord);
				return false;
			}
			OutFormatWords[OutFormatWords.Num() - 1].AppendChar('(');
			i += 2;
			continue;
		}
		else if ('"' == CleanWord[0] && '"' == CleanWord[CleanWord.Len() - 1])
		{
			FString CleanValue = CleanWord.Mid(1, CleanWord.Len() - 2);
			OutFormatWords.Add(AddComment(InWords[i], CleanValue));
			++i;
			continue;
		}
		else if (CleanWord.Equals(TEXT("-")))
		{
			if (0 == i || IsMathematicalOperator(InWords[i - 1]) || IsValidLogicOperator(InWords[i - 1]) || InWords[i - 1].Equals(TEXT("(")) || InWords[i - 1].Equals(TEXT(",")))
			{
				OutFormatWords.Add(TEXT("^"));
				++i;
				continue;
			}
		}
		OutFormatWords.Add(AddComment(InWords[i], CleanWord));
		++i;
	}
	PrintStack(TEXT("OutFormatWords"), OutFormatWords);
	return true;
}

bool UGrammerAnalysisSubsystem::IsFunctionName(const FString& InCharacter)
{
	TArray<FString> FunctionNames = { TEXT("If"),TEXT("Cond"),TEXT("Max"),TEXT("Min"),TEXT("Sin"),TEXT("Cos"),TEXT("Sqrt"),TEXT("Abs"),TEXT("Comb") };
	FString* FoundResult = FunctionNames.FindByPredicate([&](const FString& InItem)->bool {return InCharacter.Equals(InItem, ESearchCase::IgnoreCase); });
	return nullptr != FoundResult;
}

bool UGrammerAnalysisSubsystem::IsFunctionNameWithOpenParen(const FString& InCharacter)
{
	TArray<FString> FunctionNames = { TEXT("If("),TEXT("Cond("),TEXT("Max("),TEXT("Min("),TEXT("Sin("),TEXT("Cos("),TEXT("Sqrt("),TEXT("Abs("),TEXT("Comb(") };
	FString* FoundResult = FunctionNames.FindByPredicate([&](const FString& InItem)->bool {return InCharacter.Equals(InItem, ESearchCase::IgnoreCase); });
	return nullptr != FoundResult;
}

NS_GrammerAnalysis::EOperatorPriority UGrammerAnalysisSubsystem::ComparePriority(const FString& InLeftOperator, const FString& InRightOperator)
{//比较时左侧为行，右侧为列
	int32 LeftOperatorIndex = FindOperatorIndexInPriorityMap(InLeftOperator);
	int32 RightOperatorIndex = FindOperatorIndexInPriorityMap(InRightOperator);
	if (-1 != LeftOperatorIndex && -1 != RightOperatorIndex)
	{
		return static_cast<NS_GrammerAnalysis::EOperatorPriority>(GOperatorPriorityMap[LeftOperatorIndex][RightOperatorIndex] + 1);
	}
	return NS_GrammerAnalysis::EOperatorPriority::Unkonwn;
}

int32 UGrammerAnalysisSubsystem::FindOperatorIndexInPriorityMap(const FString& InOperator)
{
	int32 i = 0;
	FString OperatorName(InOperator);
	if (IsFunctionNameWithOpenParen(InOperator))
	{
		OperatorName = TEXT("f");
	}
	TArray<FString> OperatorIndex = { TEXT("+"),TEXT("-"),TEXT("^"),TEXT("*") ,TEXT("/") ,TEXT("%") ,TEXT("(") ,TEXT(")") ,TEXT("!")  ,TEXT(">") ,TEXT("<") ,TEXT(">=") ,TEXT("<=") ,TEXT("!=") ,TEXT("==") ,TEXT("&&") ,TEXT("||") ,TEXT("f") ,TEXT(",") ,TEXT("#") };
	int32 Index = 0;
	if (OperatorIndex.Find(OperatorName, Index))
		return Index;

	return -1;
}

bool UGrammerAnalysisSubsystem::CalculateOperatorValue(const FString& InOperator, const TArray<FString>& InOprands, FString& OutValue)
{
	if (InOperator.Equals(TEXT("+"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = FString::Printf(TEXT("%f"), FCString::Atof(*InOprands[0]) + FCString::Atof(*InOprands[1]));
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("-"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = FString::Printf(TEXT("%f"), FCString::Atof(*InOprands[0]) - FCString::Atof(*InOprands[1]));
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("^"), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = FString::Printf(TEXT("%f"), -FCString::Atof(*InOprands[0]));
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("*"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = FString::Printf(TEXT("%f"), FCString::Atof(*InOprands[0]) * FCString::Atof(*InOprands[1]));
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("/"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			if (!FMath::IsNearlyZero(FCString::Atof(*InOprands[1])))
			{
				OutValue = FString::Printf(TEXT("%f"), FCString::Atof(*InOprands[0]) / FCString::Atof(*InOprands[1]));
				return true;
			}
		}
	}
	else if (InOperator.Equals(TEXT("%"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			if (!FMath::IsNearlyZero(FCString::Atof(*InOprands[1])))
			{
				float IntPart = 0.0f;
				float K = FMath::Modf(FCString::Atof(*InOprands[0]) / FCString::Atof(*InOprands[1]), &IntPart);
				//UE_LOG(LogTemp, Log, TEXT("[%s][%s][%f][%f]"), *InOprands[0], *InOprands[1], IntPart, K);
				OutValue = FString::Printf(TEXT("%f"), K * FCString::Atof(*InOprands[1]));
				return true;
			}
		}
	}
	else if (InOperator.Equals(TEXT("!"), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			bool res = FMath::IsNearlyZero(FCString::Atof(*InOprands[0]), 0.01f);
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT(">"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool res = FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			res = !res && (FCString::Atof(*InOprands[0]) > FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("<"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool res = FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			res = !res && (FCString::Atof(*InOprands[0]) < FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT(">="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool res = FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			res = res || (FCString::Atof(*InOprands[0]) >= FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("<="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool res = FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			res = res || (FCString::Atof(*InOprands[0]) < FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("!="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num())
		{
			bool res = false;
			if (InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
			{
				res = !FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			}
			else
			{
				res = InOprands[0] != InOprands[1];
			}
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("=="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num())
		{
			bool res = false;
			if (InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
			{
				res = FMath::IsNearlyEqual(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]), 0.01f);
			}
			else
			{
				res = InOprands[0] == InOprands[1];
			}
			OutValue = FString::Printf(TEXT("%d"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("&&"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool Left = !FMath::IsNearlyZero(FCString::Atof(*InOprands[0]), 0.01f);
			bool Right = !FMath::IsNearlyZero(FCString::Atof(*InOprands[1]), 0.01f);
			OutValue = FString::Printf(TEXT("%d"), Left && Right);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("||"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			bool Left = !FMath::IsNearlyZero(FCString::Atof(*InOprands[0]), 0.01f);
			bool Right = !FMath::IsNearlyZero(FCString::Atof(*InOprands[1]), 0.01f);
			OutValue = FString::Printf(TEXT("%d"), Left || Right);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Max"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Max("), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			float Max = FMath::Max<float>(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%f"), Max);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Min"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Min("), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			float Min = FMath::Min<float>(FCString::Atof(*InOprands[0]), FCString::Atof(*InOprands[1]));
			OutValue = FString::Printf(TEXT("%f"), Min);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("If"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("If("), ESearchCase::IgnoreCase))
	{
		if (3 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = !FMath::IsNearlyZero(FCString::Atof(*InOprands[0]), 0.01f) ? InOprands[1] : InOprands[2];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Cond"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Cond("), ESearchCase::IgnoreCase))
	{
		if ((InOprands.Num() > 2) && (1 == InOprands.Num() % 2))
		{
			const int32 Max = InOprands.Num() / 2;
			for (int32 i = 0; i < Max; ++i)
			{
				if (!InOprands[2 * i].IsNumeric())
				{
					return false;
				}
				if (!FMath::IsNearlyZero(FCString::Atof(*InOprands[2 * i]), 0.01f))
				{
					OutValue = InOprands[2 * i + 1];
					return true;
				}
			}
			OutValue = InOprands[InOprands.Num() - 1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Sin"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Sin("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			float res = UKismetMathLibrary::DegSin(FCString::Atof(*InOprands[0]));
			OutValue = FString::Printf(TEXT("%f"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Cos"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Cos("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			float res = UKismetMathLibrary::DegCos(FCString::Atof(*InOprands[0]));
			OutValue = FString::Printf(TEXT("%f"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Sqrt"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Sqrt("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && FCString::Atof(*InOprands[0]) >= 0.0f)
		{
			float res = FMath::Sqrt(FCString::Atof(*InOprands[0]));
			OutValue = FString::Printf(TEXT("%f"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Abs"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Abs("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			float res = FMath::Abs<float>(FCString::Atof(*InOprands[0]));
			OutValue = FString::Printf(TEXT("%f"), res);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Comb"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Comb("), ESearchCase::IgnoreCase))
	{
		if (1 < InOprands.Num())
		{
			for (auto& Iter : InOprands)
			{
				if (Iter.IsNumeric())
				{
					float res = FMath::Abs<float>(FCString::Atof(*Iter));
					int32 IntRes = FMath::CeilToInt(res);
					if (FMath::IsNearlyZero(res - IntRes))
						OutValue.Append(FString::Printf(TEXT("%d"), IntRes));
					else
						OutValue.Append(FString::Printf(TEXT("%f"), res));

				}
				else if (Iter.Len() > 2)
				{
					FString Str = Iter;
					if ('"' == Iter[0] && '"' == Iter[Iter.Len() - 1])
						Str = Iter.Mid(1, Iter.Len() - 2);
					OutValue.Append(Str);
				}
				else
				{
					OutValue.Append(Iter);
				}
			}
			OutValue = FString::Printf(TEXT("\"%s\""), *OutValue);
			return true;
		}
	}
	return false;
}

bool UGrammerAnalysisSubsystem::CalculateOperatorValue_MultiPrecision(const FString& InOperator, const TArray<FCatalogGrammarAnalysisData>& InOprands, FCatalogGrammarAnalysisData& OutValue)
{
	if (InOperator.Equals(TEXT("+"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] + InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("-"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] - InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("^"), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = -InOprands[0];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("*"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] * InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("/"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			if (!InOprands[1].IsNearlyZero())
			{
				OutValue = InOprands[0] / InOprands[1];
				return true;
			}
		}
	}
	else if (InOperator.Equals(TEXT("%"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			if (!InOprands[1].IsNearlyZero())
			{
				OutValue = InOprands[0] % InOprands[1];
				return true;
			}
		}
	}
	else if (InOperator.Equals(TEXT("!"), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = !InOprands[0];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT(">"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] > InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("<"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] < InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT(">="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] >= InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("<="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] <= InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("!="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num())
		{
			OutValue = InOprands[0] != InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("=="), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num())
		{
			OutValue = InOprands[0] == InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("&&"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] && InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("||"), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0] || InOprands[1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Max"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Max("), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0].Max(InOprands[1]);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Min"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Min("), ESearchCase::IgnoreCase))
	{
		if (2 == InOprands.Num() && InOprands[0].IsNumeric() && InOprands[1].IsNumeric())
		{
			OutValue = InOprands[0].Min(InOprands[1]);
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("If"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("If("), ESearchCase::IgnoreCase))
	{
		if (3 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = !InOprands[0].IsNearlyZero(0.01) ? InOprands[1] : InOprands[2];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Cond"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Cond("), ESearchCase::IgnoreCase))
	{
		if ((InOprands.Num() > 2) && (1 == InOprands.Num() % 2))
		{
			const int32 Max = InOprands.Num() / 2;
			for (int32 i = 0; i < Max; ++i)
			{
				if (!InOprands[2 * i].IsNumeric())
				{
					return false;
				}
				if (!InOprands[2 * i].IsNearlyZero(0.01))
				{
					OutValue = InOprands[2 * i + 1];
					return true;
				}
			}
			OutValue = InOprands[InOprands.Num() - 1];
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Sin"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Sin("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = InOprands[0].Sin();
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Cos"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Cos("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = InOprands[0].Cos();
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Sqrt"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Sqrt("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsPositive())
		{
			OutValue = InOprands[0].Sqrt();
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Abs"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Abs("), ESearchCase::IgnoreCase))
	{
		if (1 == InOprands.Num() && InOprands[0].IsNumeric())
		{
			OutValue = InOprands[0].Abs();
			return true;
		}
	}
	else if (InOperator.Equals(TEXT("Comb"), ESearchCase::IgnoreCase) || InOperator.Equals(TEXT("Comb("), ESearchCase::IgnoreCase))
	{
		if (1 < InOprands.Num())
		{
			for (auto& Iter : InOprands)
			{
				OutValue.Append(Iter);
			}
			OutValue.ConvertToDescription();
			return true;
		}
	}
	return false;
}

bool UGrammerAnalysisSubsystem::GetOperandsFromOperandStack(TArray<FString>& InOperandStack, const int32& InOperandCount, TArray<FString>& OutOperands)
{
	OutOperands.Empty();
	if (InOperandStack.Num() < InOperandCount)
	{
		if (0 == InOperandStack.Num())
			return false;
		OutOperands.AddZeroed(InOperandStack.Num());
		int32 i = InOperandStack.Num() - 1;
		while (i >= 0)
		{
			OutOperands[i] = InOperandStack.Top();
			InOperandStack.Pop();
			--i;
		}
	}
	else
	{
		OutOperands.AddZeroed(InOperandCount);
		for (int32 i = 1; i <= InOperandCount; ++i)
		{
			OutOperands[InOperandCount - i] = InOperandStack.Top();
			InOperandStack.Pop();
		}
	}
	return true;
}

bool UGrammerAnalysisSubsystem::CalculateExpressionValue(const TArray<FString>& InWords, FString& OutValue, FString& OutErrorMessage)
{
	TArray<FString> Terms(InWords);
	Terms.Add(TEXT("#"));
	TArray<FString> OperatorStack;
	OperatorStack.Push(TEXT("#"));
	TArray<FString> OperandStack;
	TArray<FString> TempOperandStack;
	int32 i = 0;
	while (i < Terms.Num())
	{
		if (Terms[i].IsEmpty())
		{
			++i;
			continue;
		}
		FString WordWithoutComment = RemoveAllSpecialCharacter(Terms[i]);
		if (WordWithoutComment.Equals(TEXT("!")))
		{
			if (i >= Terms.Num() -1)
			{
				OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *WordWithoutComment);
				return false;
			}
			bool bFindNum = false;
			for (int32 j = i+1; j < Terms.Num(); ++j)
			{
				if (Terms[j].IsNumeric())
				{
					bFindNum = true;
					break;
				}
			}
			if (!bFindNum)
			{
				OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *WordWithoutComment);
				return false;
			}
		}
		if (WordWithoutComment.Equals(TEXT("#")) || IsValidLogicOperator(WordWithoutComment) || IsSeparator(WordWithoutComment) || IsMathematicalOperator(WordWithoutComment) || IsFunctionNameWithOpenParen(WordWithoutComment))
		{

			NS_GrammerAnalysis::EOperatorPriority OperatorPriority = ComparePriority(OperatorStack.Top(), WordWithoutComment);
			if (NS_GrammerAnalysis::EOperatorPriority::Unkonwn == OperatorPriority)
			{
				if (WordWithoutComment.Equals(TEXT("(")) || OperatorStack.Top().Equals(TEXT("(")))
				{
					OutErrorMessage = FString::Printf(TEXT("'(' is unmatch"));
				}
				else if (WordWithoutComment.Equals(TEXT(")")) || OperatorStack.Top().Equals(TEXT("(")))
				{
					OutErrorMessage = FString::Printf(TEXT("')' is unmatch"));
				}
				else
				{
					OutErrorMessage = FString::Printf(TEXT("Unknown error arround %s"), *WordWithoutComment);
				}
				return false;
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Lower == OperatorPriority)
			{
				OperatorStack.Push(WordWithoutComment);
				++i;
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Higher == OperatorPriority)
			{
				if (OperatorStack.Top().Equals(TEXT(",")))
				{
					if (OperandStack.Num() <= 0)
						return false;
					TempOperandStack.Insert(OperandStack.Top(), 0);
					OperandStack.Pop();
					OperatorStack.Pop();
				}
				else
				{
					TArray<FString> Oprands;
					if (IsFunctionNameWithOpenParen(OperatorStack.Top()))
					{
						if (OperandStack.Num() <= 0)
							return false;
						TempOperandStack.Insert(OperandStack.Top(), 0);
						OperandStack.Pop();
						Oprands = TempOperandStack;
						TempOperandStack.Empty();
						++i;
					}
					else
					{
						if (!GetOperandsFromOperandStack(OperandStack, GetOperatorOprandNum(OperatorStack.Top()), Oprands))
						{
							OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *OperatorStack.Top());
							return false;
						}
					}
					FString Value;
					if (CalculateOperatorValue(OperatorStack.Top(), Oprands, Value))
					{
						OperandStack.Push(Value);
						OperatorStack.Pop();
					}
					else
					{
						return false;
					}
				}
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Equal == OperatorPriority)
			{
				OperatorStack.Pop();
				++i;
			}
		}
		else
		{
			OperandStack.Push(WordWithoutComment);
			++i;
		}
		PrintStack(TEXT("OperatorStack"), OperatorStack);
		PrintStack(TEXT("OperandStack"), OperandStack);
	}
	if (0 == OperatorStack.Num() && 1 == OperandStack.Num())
	{
		OutValue = OperandStack.Top();
		OutValue = !OutValue.IsEmpty() && OutValue.IsNumeric() && FMath::IsNearlyZero(FCString::Atof(*OutValue)) ? TEXT("0.0") : OutValue;//Fix bug CATALOG-1394
		OperandStack.Pop();
		return true;
	}
	OutErrorMessage = FString::Printf(TEXT("Unknown error"));
	return false;
}

bool UGrammerAnalysisSubsystem::CalculateExpressionValue_MultiPrecision(const TArray<FString>& InWords, FString& OutValue, FString& OutErrorMessage, const int32& DecimalNum)
{
	TArray<FString> Terms(InWords);
	Terms.Add(TEXT("#"));
	TArray<FString> OperatorStack;
	OperatorStack.Push(TEXT("#"));
	TArray<FString> OperandStack;
	TArray<FString> TempOperandStack;
	int32 i = 0;
	while (i < Terms.Num())
	{
		if (Terms[i].IsEmpty())
		{
			++i;
			continue;
		}
		FString WordWithoutComment = RemoveAllSpecialCharacter(Terms[i]);
		if (WordWithoutComment.Equals(TEXT("!")))
		{
			if (i >= Terms.Num() - 1)
			{
				OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *WordWithoutComment);
				return false;
			}
			bool bFindNum = false;
			for (int32 j = i + 1; j < Terms.Num(); ++j)
			{
				if (Terms[j].IsNumeric())
				{
					bFindNum = true;
					break;
				}
			}
			if (!bFindNum)
			{
				OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *WordWithoutComment);
				return false;
			}
		}
		if (WordWithoutComment.Equals(TEXT("#")) || IsValidLogicOperator(WordWithoutComment) || IsSeparator(WordWithoutComment) || IsMathematicalOperator(WordWithoutComment) || IsFunctionNameWithOpenParen(WordWithoutComment))
		{

			NS_GrammerAnalysis::EOperatorPriority OperatorPriority = ComparePriority(OperatorStack.Top(), WordWithoutComment);
			if (NS_GrammerAnalysis::EOperatorPriority::Unkonwn == OperatorPriority)
			{
				if (WordWithoutComment.Equals(TEXT("(")) || OperatorStack.Top().Equals(TEXT("(")))
				{
					OutErrorMessage = FString::Printf(TEXT("'(' is unmatch"));
				}
				else if (WordWithoutComment.Equals(TEXT(")")) || OperatorStack.Top().Equals(TEXT("(")))
				{
					OutErrorMessage = FString::Printf(TEXT("')' is unmatch"));
				}
				else
				{
					OutErrorMessage = FString::Printf(TEXT("Unknown error arround %s"), *WordWithoutComment);
				}
				return false;
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Lower == OperatorPriority)
			{
				OperatorStack.Push(WordWithoutComment);
				++i;
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Higher == OperatorPriority)
			{
				if (OperatorStack.Top().Equals(TEXT(",")))
				{
					if (OperandStack.Num() <= 0)
						return false;
					TempOperandStack.Insert(OperandStack.Top(), 0);
					OperandStack.Pop();
					OperatorStack.Pop();
				}
				else
				{
					TArray<FString> Oprands;
					if (IsFunctionNameWithOpenParen(OperatorStack.Top()))
					{
						if (OperandStack.Num() <= 0)
							return false;
						TempOperandStack.Insert(OperandStack.Top(), 0);
						OperandStack.Pop();
						Oprands = TempOperandStack;
						TempOperandStack.Empty();
						++i;
					}
					else
					{
						if (!GetOperandsFromOperandStack(OperandStack, GetOperatorOprandNum(OperatorStack.Top()), Oprands))
						{
							OutErrorMessage = FString::Printf(TEXT("Expression is wrong arround %s"), *OperatorStack.Top());
							return false;
						}
					}
					TArray<FCatalogGrammarAnalysisData> GrammarOprands;
					for (const auto& Iter : Oprands)
					{
						if(Iter.IsNumeric() || Iter.IsEmpty())
						{
							FCatalogGrammarAnalysisData DecimalData{FDecimal(Iter)};
							GrammarOprands.Add(FCatalogGrammarAnalysisData(DecimalData));
						}
						else
						{
							FCatalogGrammarAnalysisData DescriptionData{Iter};
							GrammarOprands.Add(DescriptionData);
						}
					}
					FCatalogGrammarAnalysisData GrammarValue;
					if (CalculateOperatorValue_MultiPrecision(OperatorStack.Top(), GrammarOprands, GrammarValue))
					{
						OperandStack.Push(GrammarValue.GetValue());
						OperatorStack.Pop();
					}
					else
					{
						return false;
					}
				}
			}
			else if (NS_GrammerAnalysis::EOperatorPriority::Equal == OperatorPriority)
			{
				OperatorStack.Pop();
				++i;
			}
		}
		else
		{
			OperandStack.Push(WordWithoutComment);
			++i;
		}
		PrintStack(TEXT("OperatorStack"), OperatorStack);
		PrintStack(TEXT("OperandStack"), OperandStack);
	}
	if (0 == OperatorStack.Num() && 1 == OperandStack.Num())
	{
		OutValue = OperandStack.Top();
		OperandStack.Pop();
		return true;
	}
	OutErrorMessage = FString::Printf(TEXT("Unknown error"));
	return false;
}

bool UGrammerAnalysisSubsystem::GetParametersInExpression(const FString& InExpression, TArray<FString>& OutWords, FString& OutErrorMessage)
{
	TArray<FString> Words;
	bool Res = this->LexicalAnalysis(InExpression, Words, OutErrorMessage);
	if (Res)
	{
		for (auto& Iter : Words)
		{
			if (!Iter.IsEmpty() && !Iter.IsNumeric() && !this->IsFunctionName(Iter) && !this->IsLogicOperator(Iter[0]) && !this->IsMathematicalOperator(Iter[0]) && !this->IsSeparator(Iter[0]) && '"' != Iter[0])
			{
				OutWords.AddUnique(Iter);
				//UE_LOG(LogTemp, Log, TEXT("-------------  Find parameter %s  -----------"), *Iter);
			}
		}
	}
	return Res;
}

bool UGrammerAnalysisSubsystem::IsParameter(const FString& InWord)
{
	if (InWord.IsEmpty())
		return false;
	if ('"' == InWord[0] || InWord.IsNumeric() || this->IsFunctionName(InWord))
		return false;
	FString Word = InWord;
	if (InWord.Len() > 1 && '-' == InWord[0])
	{
		Word = InWord.Right(InWord.Len() - 1);
	}
	if (!(('a' <= Word[0] && 'z' >= Word[0]) || ('A' <= Word[0] && 'Z' >= Word[0])))
		return false;
	return true;
}

bool UGrammerAnalysisSubsystem::IsFunction(const FString& InWord)
{
	return this->IsFunctionName(InWord);
}
