// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Decimal.h"
#include "CatalogGrammarAnalysisData.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(CatalogGrammarAnalysisLog, Log, All);

UENUM()
enum class ECatalogGrammarType : uint8
{
	E_None = 0						UMETA(DisplayName = "默认不可用类型"),
	E_Decimal						UMETA(DisplayName = "用于数值计算类型"),
	E_Description					UMETA(DisplayName = "用于描述类型")
};

/**
 * 
 */
USTRUCT(BlueprintType)
struct GRAMMERANALYSIS_API FCatalogGrammarAnalysisData
{
   GENERATED_USTRUCT_BODY()

public:
	/*
	 * @@ 用于数值运算
	 * @@ 例如：+ - * / %...
	 */
	UPROPERTY()
	FDecimal DecimalValue;

	/*
	 * @@ 用于描述，不可进行数值操作，可进行字符串操作
	 * @@ 例如：Comb
	 */
	UPROPERTY()
	FString DescriptionValue;

	UPROPERTY()
	ECatalogGrammarType GrammarType;

public:
	FCatalogGrammarAnalysisData();
	FCatalogGrammarAnalysisData(const FDecimal& InDecimalValue);
	FCatalogGrammarAnalysisData(const FString& InDescriptionValue);

	//operator
	FCatalogGrammarAnalysisData& operator=(const FCatalogGrammarAnalysisData& Other);
	
	FCatalogGrammarAnalysisData& operator+=(const FCatalogGrammarAnalysisData& Other);
	FCatalogGrammarAnalysisData& operator-=(const FCatalogGrammarAnalysisData& Other);
	FCatalogGrammarAnalysisData& operator*=(const FCatalogGrammarAnalysisData& Other);
	FCatalogGrammarAnalysisData& operator/=(const FCatalogGrammarAnalysisData& Other);
	FCatalogGrammarAnalysisData& operator%=(const FCatalogGrammarAnalysisData& Other);
	
	FCatalogGrammarAnalysisData operator+(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator-(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator*(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator/(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator%(const FCatalogGrammarAnalysisData& Other) const;

	FCatalogGrammarAnalysisData operator-() const;
	FCatalogGrammarAnalysisData operator!() const;
	
	FCatalogGrammarAnalysisData operator==(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator!=(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator>(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator>=(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator<(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator<=(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator&&(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData operator||(const FCatalogGrammarAnalysisData& Other) const;

	FCatalogGrammarAnalysisData Max(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData Min(const FCatalogGrammarAnalysisData& Other) const;
	FCatalogGrammarAnalysisData Sin() const;
	FCatalogGrammarAnalysisData Cos() const;
	FCatalogGrammarAnalysisData Sqrt() const;
	FCatalogGrammarAnalysisData Abs() const;
	
	FCatalogGrammarAnalysisData& Append(const FCatalogGrammarAnalysisData& Other);
	FCatalogGrammarAnalysisData& ConvertToDescription();

	//
	bool IsNumeric() const;
	bool IsPositive() const;
	bool IsNearlyZero(const double& Tolerance = UE_DOUBLE_SMALL_NUMBER) const;
	
	FString GetValue(const int32& DecimalNum = 16) const;
	
	FString GetDescription() const;
	static FString FormatDescription(const FString& InDes);
	
};

