// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "GrammerAnalysisSubsystem.generated.h"

struct FCatalogGrammarAnalysisData;
DECLARE_LOG_CATEGORY_EXTERN(GrammerAnalysisLog, Log, Log);

namespace NS_GrammerAnalysis
{
	enum EOperatorPriority
	{
		Lower,
		Equal,
		Higher,
		Unkonwn
	};
}

struct FDecimal;

UCLASS()
class GRAMMERANALYSIS_API UGrammerAnalysisSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	bool IsFunction(const FString& InWord);

	bool LexicalAnalysis(const FString& InExpression, TArray<FString>& OutWords, FString& OutErrorMessage);

	bool IsParameter(const FString& InWord);

	bool GetParametersInExpression(const FString& InExpression, TArray<FString>& OutWords, FString& OutErrorMessage);

	bool CalculateExpressionValue(const TArray<FString>& InWords, FString& OutValue, FString& OutErrorMessage);

	bool CalculateExpressionValue_MultiPrecision(const TArray<FString>& InWords, FString& OutValue, FString& OutErrorMessage, const int32& DecimalNum = 16);

	bool IsExpressionCorrect(const TArray<FString>& InWords, FString& OutErrorMessage);

	bool FormatWords(const TArray<FString>& InWords, TArray<FString>& OutFormatWords, FString& OutErrorMessage);

	FString RemoveComment(const FString& InWord, TArray<TPair<int32, FString>>& Comments);

private:

	struct FGrammer
	{
		uint8 Key;
		FString Value;
		FGrammer() :Key(0), Value(TEXT("")) {}
		FGrammer(const char& InKey, const FString& InValue) :Key(InKey), Value(InValue) {}
		FString ToString()
		{
			FString str = FString::Printf(TEXT("%d -> %s"), Key, *Value);
			return str;
		}
	};

	struct FTableRowItem
	{
		uint8 TerminalCharacter;
		TSharedPtr<struct FGrammer> Grammer;
		FTableRowItem() :TerminalCharacter('\0'), Grammer(nullptr) {}
		FTableRowItem(const uint8& InTerminalCharacter, const TSharedPtr<struct FGrammer>& InGrammer) :TerminalCharacter(InTerminalCharacter), Grammer(InGrammer) {}
	};

	struct FTableRow
	{
		TArray<TSharedPtr<struct FTableRowItem>> RowItemList;
		TSharedPtr<struct FTableRowItem> FindTableRow(const uint8& InTerminalCharacter)
		{
			for (auto& item : RowItemList)
			{
				if (InTerminalCharacter == item->TerminalCharacter)
				{
					return item;
				}
			}
			return nullptr;
		}
		int32 GetColumnCount(const char& InTerminalCharacter)
		{
			int32 count = 0;
			for (auto& item : RowItemList)
			{
				if (InTerminalCharacter == item->TerminalCharacter)
				{
					++count;
				}
			}
			return count;
		}
	};

	bool ReadGrammerFromFile(TArray<TSharedPtr<struct FGrammer>>& InGrammer, TArray<uint8>& InUnTerminalSymbols, TArray<uint8>& InTerminalSymbols);

	bool FindFirst(const FString& InCharacters, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFirst);

	bool FindFirst(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFirst);

	bool FindFirst(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TSet<uint8>& OutFirst);

	bool FindFollow(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TMap<uint8, bool>& InVNStatus, TSet<uint8>& OutFollow);

	bool FindFollow(const uint8& InCharacter, const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT, TSet<uint8>& OutFollow);

	bool CreateAnalyticalTable(const TArray<TSharedPtr<struct FGrammer>>& InGrammer, const TArray<uint8>& InVN, const TArray<uint8>& InVT);

	bool IsGrammerCorrect(const TMap<uint8, TSharedPtr<struct FTableRow>>& InAnalysisTable, const TArray<uint8>& InVT, FString& ErrorMessage);

	bool GrammerAnalysis(const FString& InSentence, FString& OutErrorMessage);

	//
	bool IsDigitalCharacter(const TCHAR& InCharacter);

	bool IsMathematicalOperator(const TCHAR& InCharacter);

	bool IsLogicOperator(const TCHAR& InCharacter);

	bool IsValidLogicOperator(const FString& InOperator);

	bool IsSeparator(const TCHAR& InCharacter);

	bool IsLetter(const TCHAR& InCharacter);

	bool IsUnsupportCharacter(const TCHAR& InCharacter);

	//
	bool ConvertWordsToLanguageTerminalSymbols(const TArray<FString>& InWords, FString& OutFormatExpression, FString& OutErrorMessage);

	FString AddComment(const FString& InOriginalWord, const FString& InCleanWord);

	FString RemoveAllSpecialCharacter(const FString& InWordWithoutComment);

	FString MapStringToTerminalSymbol(const FString& InString);

	//
	bool IsFunctionName(const FString& InCharacter);

	bool IsFunctionNameWithOpenParen(const FString& InCharacter);

	bool IsMathematicalOperator(const FString& InOperator);

	bool IsSeparator(const FString& InOperator);

	NS_GrammerAnalysis::EOperatorPriority ComparePriority(const FString& InLeftOperator, const FString& InRightOperator);

	int32 FindOperatorIndexInPriorityMap(const FString& InOperator);

	int32 GetOperatorOprandNum(const FString& InOperator);

	bool CalculateOperatorValue(const FString& InOperator, const TArray<FString>& InOprands, FString& OutValue);
	bool CalculateOperatorValue_MultiPrecision(const FString& InOperator, const TArray<FCatalogGrammarAnalysisData>& InOprands, FCatalogGrammarAnalysisData& OutValue);

	bool GetOperandsFromOperandStack(TArray<FString>& InOperandStack, const int32& InOperandCount, TArray<FString>& OutOperands);

private:

	TMap<uint8, TSharedPtr<struct FTableRow>> AnalyticalTable;

	TArray<uint8> TerminalSymbols;

	TArray<int8> GOperatorPriorityMap[20];

};
