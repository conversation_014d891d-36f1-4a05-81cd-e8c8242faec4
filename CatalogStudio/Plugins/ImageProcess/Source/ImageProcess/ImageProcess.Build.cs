// Copyright 1998-2018 Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.IO;

public class ImageProcess : ModuleRules
{
    public ImageProcess(ReadOnlyTargetRules Target) : base(Target)
    {

        UndefinedIdentifierWarningLevel = WarningLevel.Off;
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "Projects",
                "RHI",
                "RenderCore",
                //"OpenCV455Library"
                //"OpenCV455"
				// ... add other public dependencies that you statically link with here ...
			}
            );


        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                ///"OpenCV455Library"
                //"OpenCV455"
				// ... add private dependencies that you statically link with here ...	
			}
            );


        string PlatformDir = Target.Platform.ToString();
        string IncPath = Path.Combine(ModuleDirectory, "../../ThirdParty/OpenCV/include");
        string LibPath = Path.Combine(ModuleDirectory, "../../ThirdParty/OpenCV/lib");
        string LibName = "opencv_world455";

        PublicSystemIncludePaths.Add(IncPath);
        PublicAdditionalLibraries.Add(Path.Combine(LibPath, LibName + ".lib"));
        string DLLName = LibName + ".dll";
        PublicDelayLoadDLLs.Add(DLLName);
        RuntimeDependencies.Add(System.IO.Path.Combine(LibPath, DLLName));
        PublicDefinitions.Add("WITH_OPENCV=1");

 
    }


}
