// Copyright 1998-2018 Epic Games, Inc. All Rights Reserved.

#include "ImageProcess.h"
#include "opencv2/opencv.hpp" 
#include "opencv2/core/core.hpp" 
#include "opencv2/core/mat.hpp"
#include "opencv2/imgproc/types_c.h"
#include "Runtime/Projects/Public/Interfaces/IPluginManager.h"
#include <opencv2/core/types_c.h>
#include <opencv2/core/core_c.h>
#include <opencv2/imgproc/imgproc_c.h>
//#include "opencv2/highgui/highgui.hpp"

#include <fstream>
#include <vector>

#define LOCTEXT_NAMESPACE "FImageProcessModule"

void FImageProcessModule::StartupModule()
{
	const FString PluginDir = IPluginManager::Get().FindPlugin(TEXT("ImageProcess"))->GetBaseDir();
	FString LibraryPath;

#if WITH_OPENCV
	LibraryPath = FPaths::Combine(*PluginDir, TEXT("ThirdParty/OpenCV/lib/"));
	UE_LOG(LogTemp, Warning, TEXT("opencv world LibraryPath == %s"), *(LibraryPath + TEXT("opencv_world455.dll")));
	OpenCV_World_Handler = FPlatformProcess::GetDllHandle(*(LibraryPath + TEXT("opencv_world455.dll")));
	//OpenCV_FFmpeg_Handler = FPlatformProcess::GetDllHandle(*(LibraryPath + TEXT("opencv_ffmpeg340_64.dll")));
	if (!OpenCV_World_Handler /*|| !OpenCV_FFmpeg_Handler*/)
	{
		UE_LOG(LogTemp, Error, TEXT("Load OpenCV dll failed!"));
	}
#endif


	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	ImageProcessPtr = new FImageProcess();
}

void FImageProcessModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	if (ImageProcessPtr != NULL)
	{
		delete ImageProcessPtr;
		ImageProcessPtr = NULL;
	}
#if WITH_OPENCV
	if (OpenCV_World_Handler)
	{
		FPlatformProcess::FreeDllHandle(OpenCV_World_Handler);
		OpenCV_World_Handler = nullptr;
	}
	if (OpenCV_FFmpeg_Handler)
	{
		FPlatformProcess::FreeDllHandle(OpenCV_FFmpeg_Handler);
		OpenCV_FFmpeg_Handler = nullptr;
	}
#elif PLATFORM_ANDROID

#elif PLATFORM_IOS

#endif
}

//std::string FString2StdString(const FString& InStr)
//{
//	std::wstring _FilePath(*InStr);
//	_bstr_t t = _FilePath.c_str();
//	char* pchar = (char*)t;
//	return std::string(pchar);
//}


bool LoadImageFromFile(const FString& ImagePath, cv::Mat& OutImage)
{
	//std::string strPath = TCHAR_TO_UTF8(*ImagePath);
	//OutImage = cv::imread(strPath);
	std::wstring FilePath(*ImagePath);
	std::ifstream f(FilePath, std::iostream::binary);
	if (!f)return false;
	std::filebuf* pBuf = f.rdbuf();
	size_t fileSize = pBuf->pubseekoff(0, f.end, f.in);
	if (fileSize <= 1024)return false;
	pBuf->pubseekpos(0, f.in);

	std::vector<uchar> buffer(fileSize);
	pBuf->sgetn((char*)buffer.data(), fileSize);

	OutImage = cv::imdecode(buffer, cv::IMREAD_COLOR);
	f.close();
	return !OutImage.empty();
}

bool FImageProcess::ThumbnailConvert(const FString& ImagePath, const FString& TargetImagePath, int32 ThumbnailSizeX, int32 ThumbnailSizeY, int32 OffsetX, int32 OffsetY, EImageType InImageType)
{
	cv::Mat image;

	LoadImageFromFile(ImagePath, image);

	if (image.empty())
	{
		UE_LOG(LogTemp, Error, TEXT("Load image data from file %s failed!"), *ImagePath);
		return false;
	}

	cv::Rect RectLeft;
	cv::Rect RectRight;

	int BlackEdge = 0;

	for (int i = 0; i < image.cols; ++i)
	{
		if (image.at<cv::Vec3b>(0, i)[0] == 0
			&& image.at<cv::Vec3b>(0, i)[1] == 0
			&& image.at<cv::Vec3b>(0, i)[2] == 0)
		{
			++BlackEdge;
		}
		else
		{
			break;
		}
	}

	RectLeft = cv::Rect(BlackEdge, 0, image.cols - BlackEdge, image.rows - 1);

	BlackEdge = 0;

	for (int i = image.cols - 1; i >= 0; --i)
	{
		if (image.at<cv::Vec3b>(0, i)[0] == 0
			&& image.at<cv::Vec3b>(0, i)[1] == 0
			&& image.at<cv::Vec3b>(0, i)[2] == 0)
		{
			++BlackEdge;
		}
		else
		{
			break;
		}
	}

	RectRight = cv::Rect(0, 0, image.cols - BlackEdge, image.rows - 1);

	cv::Rect RectCenter = RectLeft & RectRight;

	cv::Mat thumbnail = image(RectCenter);

	if (thumbnail.cols / 2 < 256 || thumbnail.rows / 2 < 256)
	{
		UE_LOG(LogTemp, Error, TEXT("Image size error cols=%d rows=%d"), thumbnail.cols, thumbnail.rows);
		return false;
	}

	cv::Rect rect(thumbnail.cols / 2 - 256, thumbnail.rows / 2 - 256, 512, 512);
	thumbnail = thumbnail(rect);

	const std::string strTargetPath = TCHAR_TO_UTF8(*TargetImagePath);
	bool Res = cv::imwrite(strTargetPath, thumbnail);
	if (false == Res)
	{
		UE_LOG(LogTemp, Error, TEXT("Write image to %s failed"), *TargetImagePath);
	}
	return Res;
}

bool FImageProcess::ResizeImage(const FString& ImagePath, const int32& newCols, const int32& newRows, const FString& OutImagePath)
{
	cv::Mat oldImage;
	bool Res = LoadImageFromFile(ImagePath, oldImage);
	if (!Res)return false;
	cv::Mat newImage;
	cv::resize(oldImage, newImage, cv::Size(newCols, newRows), 0, 0, cv::INTER_AREA);
	Res = cv::imwrite((TCHAR_TO_UTF8(*OutImagePath)), newImage);
	return Res;
}

UTexture2D* FImageProcess::LoadTexture2DFromImage(const FString& ImagePath, const int32& NewWidth, const int32& NewHeight)
{
	cv::Mat oldImage;
	bool Res = LoadImageFromFile(ImagePath, oldImage);
	if (!Res)
	{
		return nullptr;
	}
	FVector2D  Size = FVector2D(oldImage.cols, oldImage.rows);
	auto K = (NewHeight - Size.Y)  / (NewWidth - Size.X);
	cv::Mat newImage = oldImage;
	auto Step = FMath::RoundToInt(Size.X/NewWidth);

	for (int32 i = Step - 1; i  >=0; --i)
	{
		if (i == 0)
		{
			cv::resize(oldImage, newImage, cv::Size(NewWidth, NewHeight), 0, 0, cv::INTER_AREA);
			break;
		}
		cv::resize(oldImage, newImage, cv::Size(NewWidth + i * NewWidth, NewHeight + i * NewWidth * K ), 0, 0, cv::INTER_AREA);
		oldImage = newImage;
	}

	auto NewSize = FVector2D(newImage.cols, newImage.rows);
	TArray<FColor> ColorData;
	ColorData.Init(FColor(0, 0, 0, 255), NewSize.X * NewSize.Y);
	for (int32 y = 0; y < NewSize.Y; y++)
	{
		for (int32 x = 0; x < NewSize.X; x++)
		{
			int i = x + (y * NewSize.X);
			ColorData[i] = FColor(newImage.data[i * 3 + 2],
				newImage.data[i * 3 + 1],
				newImage.data[i * 3 + 0],
				255);
		}
	}
	UTexture2D* Texture = UTexture2D::CreateTransient(NewSize.X, NewSize.Y);
	if (Texture)
	{
		void* TextureData = Texture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
		FMemory::Memcpy(TextureData, ColorData.GetData(), ColorData.Num() * sizeof(FColor));
		Texture->GetPlatformData()->Mips[0].BulkData.Unlock();
		Texture->UpdateResource();
	}

	return Texture;
}


#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FImageProcessModule, ImageProcess)