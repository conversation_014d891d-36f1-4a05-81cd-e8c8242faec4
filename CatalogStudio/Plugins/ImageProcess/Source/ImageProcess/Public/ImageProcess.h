// Copyright 1998-2018 Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

enum EImageType
{
	NormalImage,
	WhiteImage,
	MaterialImage
};


class IImageProcessInterface
{
public:
	virtual ~IImageProcessInterface() {}
	virtual bool ThumbnailConvert(const FString& ImagePath, const FString& TargetImagePath, int32 ThumbnailSizeX = 512, int32 ThumbnailSizeY = 512, int32 OffsetX = 3, int32 OffsetY = 3, EImageType InImageType = EImageType::NormalImage) = 0;
	virtual bool ResizeImage(const FString& ImagePath, const int32& newCols, const int32& newRows, const FString& OutImagePath) = 0;
	virtual UTexture2D* LoadTexture2DFromImage(const FString& ImagePath, const int32& NewWidth, const int32& NewHeight) = 0;
};

class FImageProcess : public IImageProcessInterface
{
public:
	virtual ~FImageProcess() {}
	virtual bool ThumbnailConvert(const FString& ImagePath, const FString& TargetImagePath, int32 ThumbnailSizeX = 512, int32 ThumbnailSizeY = 512, int32 OffsetX = 3, int32 OffsetY = 3, EImageType InImageType = EImageType::NormalImage) override;
	virtual bool ResizeImage(const FString& ImagePath, const int32& newCols, const int32& newRows, const FString& OutImagePath)override;
	virtual UTexture2D* LoadTexture2DFromImage(const FString& ImagePath, const int32& NewWidth, const int32& NewHeight) override;

};

class FImageProcessModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	static IImageProcessInterface* Get()
	{
		FImageProcessModule& ImageProcessModule = FModuleManager::Get().LoadModuleChecked<FImageProcessModule>("ImageProcess");
		return ImageProcessModule.GetSingleton();
	}

private:

	virtual IImageProcessInterface* GetSingleton() const { return ImageProcessPtr; }
	IImageProcessInterface* ImageProcessPtr;

private:
	void* OpenCV_World_Handler;
	void* OpenCV_FFmpeg_Handler;
};