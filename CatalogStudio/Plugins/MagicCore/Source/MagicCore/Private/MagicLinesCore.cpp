// Copyright Epic Games, Inc. All Rights Reserved.

#include "MagicLinesCore.h"

UMagicLinesCore::UMagicLinesCore(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer)
{

}

bool UMagicLinesCore::IsPointInLineSegmentExcludeSegPoint(const FVector2D& InPoint, const FVector2D& InLineSegPoint1, const FVector2D& InLineSegPoint2)
{
	const float LineSegLength = FVector2D::Distance(InLineSegPoint1, InLineSegPoint2);
	const float P1Length = FVector2D::Distance(InPoint, InLineSegPoint1);
	const float P2Length = FVector2D::Distance(InPoint, InLineSegPoint2);

	return ((P1Length + P2Length) == LineSegLength) && (P1Length != 0) && (P2Length != 0);
}

bool UMagicLinesCore::IsLineInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment& Segment2)
{
	if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsTwoLineStartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsOneLine1StartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		FVector2D(Segment1.End) == FVector2D(Segment2.Start))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsOneLine2StartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		FVector2D(Segment1.Start) == FVector2D(Segment2.End))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsTwoLineEndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsOneLine1EndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		FVector2D(Segment1.Start) == FVector2D(Segment2.End))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsOneLine2EndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2)
{
	if (!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.Start), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment2.Start), FVector2D(Segment2.End)) &&
		!IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.Start), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment2.End), FVector2D(Segment1.Start), FVector2D(Segment1.End)) &&
		FVector2D(Segment1.End) == FVector2D(Segment2.Start))
		return true;
	else
		return false;
}

bool UMagicLinesCore::IsTwoIntersect(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, FVector2D & point1, FVector2D & point2)
{
	if (!UMagicLinesCore::IsVertical(Segment1, Segment) || !UMagicLinesCore::IsVertical(Segment3, Segment))
	{
		return false;
	}
	FVector2D point11;
	FVector2D point22;
	if (IsColockWise(Segment1.Start, Segment1.End, Segment3.Start) == 1 && IsColockWise(Segment1.End, Segment3.Start, Segment3.End) == 1 && GetIntersect(Segment1, Segment, point11) && GetIntersect(Segment3, Segment, point22))
	{
		if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End.X, Segment1.End.Y), FVector2D(Segment1.Start.X, Segment1.Start.Y), point11)
			&& IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start.X, Segment3.Start.Y), point22, FVector2D(Segment3.End.X, Segment3.End.Y))
			&& FVector2D::Distance(FVector2D(Segment1.End.X, Segment1.End.Y), point11) == FVector2D::Distance(FVector2D(Segment3.Start.X, Segment3.Start.Y), point22))
		{
			point1 = point11;
			point2 = point22;
			return true;
		}
	}
	return false;
}

bool UMagicLinesCore::IsOneIntersect(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, FVector2D & point, bool & isSegment1)
{
	FVector2D point11 = FVector2D::ZeroVector;
	FVector2D point22 = FVector2D::ZeroVector;
	if (IsColockWise(Segment1.Start, Segment1.End, Segment3.Start) == 1 && IsColockWise(Segment1.End, Segment3.Start, Segment3.End) == 1)
	{
		if (GetIntersect(Segment1, Segment, point11) && !GetIntersect(Segment3, Segment, point22) && IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End.X, Segment1.End.Y), FVector2D(Segment1.Start.X, Segment1.Start.Y), point11))
		{
			point = point11;
			isSegment1 = true;
			return true;
		}
		else if (GetIntersect(Segment3, Segment, point22) && !GetIntersect(Segment1, Segment, point11) && IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start.X, Segment3.Start.Y), FVector2D(Segment3.End.X, Segment3.End.Y), point22))
		{
			point = point22;
			isSegment1 = false;
			return true;
		}
	}
	return false;
}

bool UMagicLinesCore::IsTwoInterCoincideSegment(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment)
{
	if (IsColockWise(Segment1.Start, Segment1.End, Segment3.Start) == 1 && IsColockWise(Segment1.End, Segment3.Start, Segment3.End) == 1)
	{
		if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment1.Start), FVector2D(Segment.End))
			&& IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start), FVector2D(Segment.Start), FVector2D(Segment3.End))
			&& FVector2D::Distance(FVector2D(Segment1.End), FVector2D(Segment.End)) == FVector2D::Distance(FVector2D(Segment3.Start), FVector2D(Segment.Start))
			&& FVector2D::Distance(FVector2D(Segment1.End), FVector2D(Segment.End)) < 60.f)
		{
			return true;
		}
	}
	return false;
}

bool UMagicLinesCore::IsOneInterCoincideSegment(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, bool& isSegment1)
{
	if (IsColockWise(Segment1.Start, Segment1.End, Segment3.Start) == 1 && IsColockWise(Segment1.End, Segment3.Start, Segment3.End) == 1)
	{
		if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment1.Start), FVector2D(Segment.End))
			&& !IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment1.Start), FVector2D(Segment.Start))
			&& !IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start), FVector2D(Segment.Start), FVector2D(Segment3.End))
			&& FVector2D::Distance(FVector2D(Segment1.End), FVector2D(Segment.End)) > 0.f
			&& FVector2D::Distance(FVector2D(Segment1.End), FVector2D(Segment.End)) < 60.f)
		{
			isSegment1 = true;
			return true;
		}
		else if (IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start), FVector2D(Segment.Start), FVector2D(Segment3.End))
			&& !IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment3.Start), FVector2D(Segment.End), FVector2D(Segment3.Start))
			&& !IsPointInLineSegmentExcludeSegPoint(FVector2D(Segment1.End), FVector2D(Segment1.Start), FVector2D(Segment.End))
			&& FVector2D::Distance(FVector2D(Segment1.End), FVector2D(Segment.End)) > 0.f
			&& FVector2D::Distance(FVector2D(Segment3.Start), FVector2D(Segment.Start)) < 60.f)
		{
			isSegment1 = false;
			return true;
		}
	}
	return false;
}

bool UMagicLinesCore::GetIntersect(const FSegment & Segment1, const FSegment & Segment2, FVector2D & point)
{
	point = FVector2D::ZeroVector;
	if (!UMagicLinesCore::IsVertical(Segment1, Segment2)) return false;

	FVector2D AB = FVector2D(Segment1.End - Segment1.Start).GetSafeNormal();
	FVector2D AC = FVector2D(Segment2.Start - Segment1.Start);
	FVector2D Point = AB * (FVector2D::DotProduct(AC, AB)) + FVector2D(Segment1.Start);

	//UE_LOG(LogTemp, Error, TEXT("IsPointInLine %d"), IsPointInLineSegmentExcludeSegPoint(Point, FVector2D(l2.Start), FVector2D(l2.End)));
	if (!IsPointInLineSegmentExcludeSegPoint(Point, FVector2D(Segment2.Start), FVector2D(Segment2.End))) return false;

	const float distance = FVector2D::Distance((AC | AB) > 0 ? FVector2D(Segment1.End) : FVector2D(Segment1.Start), Point);
	UE_LOG(LogTemp, Error, TEXT("GetIntersect %f"), distance);
	if (distance > 60.f || distance <= 0.f) return false;
	point = Point;
	return true;
}

void UMagicLinesCore::DeleteSegmentToSegments(const FSegment& Segment1, TArray<FSegment>& Segments)
{
	int32 IndexOfDeletingMagic = Segments.IndexOfByPredicate([&](const FSegment& Line)->bool {return (Segment1.Start - Line.Start).IsNearlyZero(THRESH_POINTS_ARE_SAME) && (Segment1.End - Line.End).IsNearlyZero(THRESH_POINTS_ARE_SAME); });
	if (INDEX_NONE != IndexOfDeletingMagic)
	{
		Segments.RemoveAtSwap(IndexOfDeletingMagic);
	}
}

bool UMagicLinesCore::IsVertical(const FSegment & Segment1, const FSegment & Segment2)
{
	const FVector2D AB = FVector2D(Segment1.End - Segment1.Start).GetSafeNormal();
	const FVector2D CD = FVector2D(Segment2.End - Segment2.Start).GetSafeNormal();
	
	return FMath::IsNearlyZero(AB | CD, THRESH_POINTS_ARE_SAME);
}

int32 UMagicLinesCore::IsColockWise(const FVector & Point1, const FVector & Point2, const FVector & Point3)
{
	int32 val = (Point2.Y - Point1.Y) * (Point3.X - Point2.X) -
		(Point2.X - Point1.X) * (Point3.Y - Point2.Y);

	if (val == 0) return 0;  // collinear
	return (val > 0) ? 1 : 2; //1 clock or 2 counterclock-wise
}

void UMagicLinesCore::AddZ(TArray<FSegment>& Segment1,const float addz)
{
	for (int32 i = 0; i < Segment1.Num(); i++)
	{
		Segment1[i].Start.Z += addz;
		Segment1[i].End.Z += addz;
	}
}

