// Fill out your copyright notice in the Description page of Project Settings.


#include "MeshSheareData.h"

FPMCVertex FPMCVertex::InterpolateVert(const FPMCVertex& V0, const FPMCVertex& V1, float Alpha)
{
	FPMCVertex Result;

	// Handle dodgy alpha
	if (FMath::IsNaN(Alpha) || !FMath::IsFinite(Alpha))
	{
		Result = V1;
		return Result;
	}
	Result.Pos = FMath::Lerp(V0.Pos, V1.Pos, Alpha);
	Result.UV = FMath::Lerp(V0.UV, V1.UV, Alpha);
	Result.Tangent.TangentX = V0.Tangent.TangentX;
	Result.Normal = V0.Normal;
	return Result;
}

FVector FPMCVertex::operator-(const FPMCVertex& InOther) const
{
	return Pos - InOther.Pos;
}

bool FPMCVertex::Equals(const FPMCVertex& InOther) const
{
	if (!Pos.Equals(InOther.Pos, THRESH_POINTS_ARE_SAME))
	{
		return false;
	}

	if (!Tangent.TangentX.Equals(InOther.Tangent.TangentX, THRESH_NORMALS_ARE_SAME))
	{
		return false;
	}

	if (!Normal.Equals(InOther.Normal, THRESH_NORMALS_ARE_SAME))
	{
		return false;
	}

	return UV.Equals(InOther.UV, 1.0f / 1024.0f);
}

bool FPMCTriangle::IsRedundantTriangle() const
{
	return Vertexes[0].Pos.Equals(Vertexes[1].Pos, FLOAT_NORMAL_THRESH) ||
		Vertexes[0].Pos.Equals(Vertexes[2].Pos, FLOAT_NORMAL_THRESH) ||
		Vertexes[1].Pos.Equals(Vertexes[2].Pos, FLOAT_NORMAL_THRESH);
}

void FPMCTriangle::ReverseTriangleWinding()
{
	auto Temp = Vertexes[0];
	Vertexes[0] = Vertexes[2];
	Vertexes[2] = Temp;
	for (int32 i = 0; i < 3; ++i)
		Vertexes[i].Normal *= -1.0f;
}

FVector FPMCTriangle::GetTriangleNormal() const
{
	const FVector Normal = (Vertexes[2] - Vertexes[0]) ^ (Vertexes[1] - Vertexes[0]);
	return Normal.GetSafeNormal();
}

void FPMCTriangle::ReindexTriangleVertex(TArray<FPMCTriangle>& Triangles, TArray<FPMCVertex>& Vertexes)
{
	int32 Offset = Vertexes.AddDefaulted(Triangles.Num() * 3);
	TMap<int32, int32> IndexMap;
	for (int32 i = 0; i < Triangles.Num(); ++i)
	{
		auto& EditTriangle = Triangles[i];
		for (int32 j = 0; j < 3; ++j)
		{
			auto& EditVertex = EditTriangle.Vertexes[j];
			if (IndexMap.Contains(EditVertex.VertexIndex))
			{
				EditVertex.VertexIndex = IndexMap[EditVertex.VertexIndex];
				continue;
			}
			IndexMap.Add(EditVertex.VertexIndex, Offset);
			EditVertex.VertexIndex = Offset;
			Vertexes[Offset] = EditVertex;
			++Offset;
		}
	}
	if (Offset < Vertexes.Num()) Vertexes.RemoveAt(Offset, Vertexes.Num() - Offset);
}

void FPMCTriangle::FindOutlineEdges(const TArray<FPMCTriangle>& OriginTriangles, TArray<TPair<int32, int32>>& OutlineEdges)
{
	if (OriginTriangles.Num() < 1) return;
	struct FEdge
	{
		int32 A = INDEX_NONE;
		int32 B = INDEX_NONE;
		FEdge() :A(0), B(0) { }
		FEdge(const int32& InA, const int32& InB) :A(InA), B(InB) { }
		bool operator==(const FEdge& InOther) const { return (A == InOther.A && B == InOther.B) || ((B == InOther.A && A == InOther.B)); }
	};
	TArray<TPair<FEdge, int32>> EdgeCountMap;
	EdgeCountMap.Init(TPair<FEdge, int32>(FEdge(), 0), OriginTriangles.Num() * 3);
	int32 EdgeCount = 0;
	for (const auto& Triangle : OriginTriangles)
	{
		for (int32 i = 0; i < 3; ++i)
		{
			const int32 Next = (i + 1) % 3;
			const FEdge Edge(Triangle.Vertexes[i].VertexIndex, Triangle.Vertexes[Next].VertexIndex);
			int32 Index = EdgeCountMap.IndexOfByPredicate([&](const TPair<FEdge, int32>& InOther) { return Edge == InOther.Key; });
			if (INDEX_NONE == Index)
			{
				EdgeCountMap[EdgeCount].Key = Edge;
				Index = EdgeCount;
				++EdgeCount;
			}
			++EdgeCountMap[Index].Value;
		}
	}
	OutlineEdges.Init(TPair<int32, int32>(INDEX_NONE, INDEX_NONE), EdgeCount);
	int32 OutlineEdgeCount = 0;
	for (int32 i = 0; i < EdgeCount; ++i)
	{
		const auto& EditEdge = EdgeCountMap[i];
		if (1 != EditEdge.Value) continue;
		OutlineEdges[OutlineEdgeCount].Key = EditEdge.Key.A;
		OutlineEdges[OutlineEdgeCount].Value = EditEdge.Key.B;
		++OutlineEdgeCount;
	}
	if (OutlineEdgeCount < EdgeCount) OutlineEdges.RemoveAt(OutlineEdgeCount, EdgeCount - OutlineEdgeCount);
}

void FPMCTriangle::MergeTriangles(const TArray<FPMCTriangle>& MergeTriangles, TArray<FPMCTriangle>& Triangles)
{

}
