// Fill out your copyright notice in the Description page of Project Settings.


#include "PMCSection.h"

FPMCSection::FPMCSection()
	:Vertexes(TArray<FVector>())
	, Triangles(TArray<int32>())
	, Normals(TArray<FVector>())
	, UV(TArray<FVector2D>())
	, Tangents(TArray<FProcMeshTangent>())
	, SectionName(NAME_None)
{

}

FPMCSection::FPMCSection(const FName& InName)
	:Vertexes(TArray<FVector>())
	, Triangles(TArray<int32>())
	, Normals(TArray<FVector>())
	, UV(TArray<FVector2D>())
	, Tangents(TArray<FProcMeshTangent>())
	, SectionName(InName)
{

}

FPMCSection::~FPMCSection()
{
	Empty();
}

void FPMCSection::operator+=(const FPMCSection& InOther)
{
	TArray<int32> NewTriangles = InOther.Triangles;
	for (int32 i = 0; i < NewTriangles.Num(); ++i)
		NewTriangles[i] += Vertexes.Num();
	Vertexes.Append(InOther.Vertexes);
	Triangles.Append(NewTriangles);
	Normals.Append(InOther.Normals);
	UV.Append(InOther.UV);
	Tangents.Append(InOther.Tangents);
}

void FPMCSection::operator+=(const FVector& InOther)
{
	for (int32 i = 0; i < Vertexes.Num(); i++)
		Vertexes[i] += InOther;
}

FPMCSection FPMCSection::operator+(const FPMCSection& InOther)
{
	FPMCSection Result = *this;
	Result += InOther;
	return Result;
}

bool FPMCSection::IsEmpty() const
{
	return Vertexes.Num() <= 0 || Triangles.Num() <= 0 || Normals.Num() <= 0 || UV.Num() <= 0 || Tangents.Num() <= 0;
}

void FPMCSection::Empty()
{
	Vertexes.Empty();
	Triangles.Empty();
	Normals.Empty();
	UV.Empty();
	Tangents.Empty();
	SectionName = NAME_None;
}

float FPMCSection::SurfaceArea() const
{
	float Area = 0.0f;
	const int32 TriangleCount = Triangles.Num() / 3;
	for (int32 i = 0; i < TriangleCount; ++i)
	{
		const int32 Base = i * 3;
		const float TriangleArea = ((Vertexes[Base + 2] - Vertexes[Base]) ^ (Vertexes[Base + 1] - Vertexes[Base])).Size() * 0.5f;
		Area += TriangleArea;
	}
	return Area;
}

bool FPMCSection::operator!=(const FPMCSection& InOther) const
{
	bool Res = Vertexes.Num() != InOther.Vertexes.Num() || UV.Num() != InOther.UV.Num() || Normals.Num() != InOther.Normals.Num() || Tangents.Num() != InOther.Tangents.Num() || Triangles.Num() != InOther.Triangles.Num();
	return Res;
}

void FPMCSection::GetBoundingBox(FBox& OutBox) const
{
	FBox Bound(ForceInit);
	for (auto& Vertex : Vertexes)
		Bound += Vertex;
	OutBox = Bound;
}

void FPMCSection::FlipNormal()
{
	const int32 TriangleCount = Triangles.Num() / 3;
	for (int32 i = 0; i < TriangleCount; ++i)
	{
		const int32 A = i * 3;
		const int32 B = A + 2;
		Triangles[A] = Triangles[A] ^ Triangles[B];
		Triangles[B] = Triangles[A] ^ Triangles[B];
		Triangles[A] = Triangles[A] ^ Triangles[B];
	}
	for (int32 i = 0; i < Normals.Num(); ++i)
	{
		Normals[i] *= -1.0f;
		UV[i].Y *= -1.0f;
	}
}