// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once


class MAGICCORE_API FArrayOperatorLibrary
{

public:

	template<typename T>
	static void ReverseArray(TArray<T>& Array)
	{
		const int32 LastIndex = Array.Num() - 1;
		const int32 Half = Array.Num() >> 1;
		for (int32 i = 0; i < Half; ++i)
		{
			const int32 SawpIndex = LastIndex - i;
			T Temp = Array[SawpIndex];
			Array[SawpIndex] = Array[i];
			Array[i] = Temp;
		}
	}

	template<typename T>
	static void OffsetArray(TArray<T>& Array, const T& var)
	{
		for (int32 i = 0; i < Array.Num(); ++i)
		{
			Array[i] -= var;
		}
	}

	template<typename T1, typename T2>
	static TArray<T2> ConvertArrayType(const TArray<T1>& InArray)
	{
		TArray<T2> OutArray = TArray<T2>();
		if (0 >= InArray.Num()) return OutArray;
		OutArray.AddDefaulted(InArray.Num());
		for (int32 i = 0; i < InArray.Num(); ++i)
			OutArray[i] = T2(InArray[i]);
		return OutArray;
	}

	template<typename SourceClass, typename TargetClass>
	static TArray<TargetClass*> ConvertArrayType(const TArray<SourceClass*>& InArray)
	{
		TArray<TargetClass*> OutArray = TArray<TargetClass*>();
		int32 Offset = OutArray.AddZeroed(InArray.Num());
		while (Offset < InArray.Num())
		{
			TargetClass* Actor = Cast<TargetClass>(InArray[Offset]);
			OutArray[Offset] = Actor;
			++Offset;
		}
		return OutArray;
	}

	template<typename T>
	static bool CompareArrayElement(const TArray<T>& Array1, const TArray<T>& Array2)
	{
		if (Array1.Num() != Array2.Num()) return false;
		for (int32 i = 0; i < Array1.Num(); ++i)
		{
			bool bFound = false;
			for (int32 j = 0; j < Array2.Num(); ++j)
			{
				if (Array1[i] != Array2[j]) continue;
				bFound = true;
				break;
			}
			if (!bFound) return false;
		}
		return true;
	}
};
