// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "MagicLinesCore.generated.h"

struct MAGICCORE_API FSegment
{
public:
	FVector Start;
	FVector End;
	int32 ModelNum;
	float Height;
	FString uuid;

public:

	FSegment()
	{
		Start = FVector::ZeroVector;
		End = FVector::ZeroVector;
		ModelNum = -1;
		Height = -1.0;
		uuid = FString();
	}
	FSegment(FVector v1, FVector v2, int32 n, float h, FString u)
	{
		Start = v1;
		End = v2;
		ModelNum = n;
		Height = h;
		uuid = u;
	}
	bool operator==(const FSegment s)
	{
		if ((Start == s.Start && End == s.End)
			|| (Start == s.End && End == s.Start))
			return true;
		else
			return false;
	}
	FString ToString() const
	{
		return FString::Printf(TEXT("%s | %s"), *Start.ToString(), *End.ToString());
	}
};

struct MAGICCORE_API FMagicPoints
{
public:
	bool loop;
	int32 ModelNum;
	TArray<FVector> Points;
	float Height;
	FString uuid;

public:
	FMagicPoints()
	{
		loop = false;
		ModelNum = -1;
		Points = TArray<FVector>();
		Height = -1.0;
		uuid = FString();
	}
};

struct MAGICCORE_API FMagicLines
{
public:
	TArray<FSegment> lines;
	bool loop;
	int32 ModelNum;

public:
	FMagicLines()
	{
		lines = TArray<FSegment>();
		loop = false;
		ModelNum = -1;
	}
};

struct MAGICCORE_API FNewSegment
{
public:
	FVector Start;
	FVector End;
	FVector NormalDir;
	FString uuid;

public:
	FNewSegment()
	{
		Start = FVector::ZeroVector;
		End = FVector::ZeroVector;
		NormalDir = FVector::ZeroVector;
		uuid = FString();
	}
};

struct MAGICCORE_API FAllNewSements
{
public:
	TArray<FNewSegment> lines;
	bool loop;

public:
	FAllNewSements()
	{
		lines = TArray<FNewSegment>();
		loop = false;
	}
};

struct MAGICCORE_API FDecorateBasePoints
{
public:
	TArray<FVector> BasePoints;//BottomPoint
	float Height;
	FString uuid;

public:
	FDecorateBasePoints()
	{
		BasePoints = TArray<FVector>();
		Height = -1;
		uuid = FString();
	}
};

UCLASS()
class MAGICCORE_API UMagicLinesCore: public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

	//UMagicLinesCore(const FObjectInitializer& ObjectInitializer);

public:

	static bool IsPointInLineSegmentExcludeSegPoint(const FVector2D & InPoint, const FVector2D & InLineSegPoint1, const FVector2D & InLineSegPoint2);//点在线上（排除端点）
	static bool IsLineInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//线段在线段上（排除端点）

	static bool IsTwoLineStartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//两线起点互相在对方线上
	static bool IsOneLine1StartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//仅线一起点在线二上
	static bool IsOneLine2StartPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//仅线二起点在线一上
	static bool IsTwoLineEndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//两线终点互相在对方线上
	static bool IsOneLine1EndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//仅线一终点在线二上
	static bool IsOneLine2EndPointInLineSegmentExcludeSegPoint(const FSegment & Segment1, const FSegment & Segment2);//仅线二终点在线一上

	static bool IsTwoIntersect(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, FVector2D & point1, FVector2D & point2);//线一与线三交目标线于线上
	static bool IsOneIntersect(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, FVector2D & point, bool & isSegment1);//线一与线三 是否线一交目标线于线上
	static bool IsTwoInterCoincideSegment(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment);//线一与线三交目标线于端点
	static bool IsOneInterCoincideSegment(const FSegment & Segment1, const FSegment & Segment3, const FSegment & Segment, bool & isSegment1);//线一与线三 是否线一交目标线于端点

	static bool GetIntersect(const FSegment& Segment1, const FSegment& Segment2, FVector2D& point);//垂直并且距离小于60

	static void DeleteSegmentToSegments(const FSegment& Segment1, TArray<FSegment>& Segments);

	static bool IsVertical(const FSegment& Segment1, const FSegment& Segment2);

	static int32 IsColockWise(const FVector & Point1, const FVector & Point2, const FVector & Point3);//是否顺时针

	static void AddZ(TArray<FSegment> & Segment1,const float addz);
};
