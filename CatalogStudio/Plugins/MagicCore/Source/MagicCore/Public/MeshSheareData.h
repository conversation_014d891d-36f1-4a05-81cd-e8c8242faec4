// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ProceduralMeshComponent/Public/ProceduralMeshComponent.h"


/**
 *
 */
struct MAGICCORE_API FPMCVertex
{
public:

	FVector				Pos;

	FVector2D			UV;

	FProcMeshTangent	Tangent;

	FVector				Normal;

	int32				VertexIndex;

public:

	FPMCVertex() :Pos(FVector::ZeroVector), UV(FVector2D::ZeroVector), Tangent(FProcMeshTangent()), Normal(FVector::ZeroVector), VertexIndex(0) {}

	static FPMCVertex InterpolateVert(const FPMCVertex& V0, const FPMCVertex& V1, float Alpha);

	FVector operator-(const FPMCVertex& InOther) const;

	bool Equals(const FPMCVertex& InOther) const;
};

struct MAGICCORE_API FPMCTriangle
{
public:

	FPMCVertex Vertexes[3];

public:

	bool IsRedundantTriangle() const;

	FPMCTriangle()
	{
		FMemory::Memzero(this, sizeof(FPMCTriangle));
	}

	void ReverseTriangleWinding();

	FVector GetTriangleNormal() const;

	static void ReindexTriangleVertex(TArray<FPMCTriangle>& Triangles, TArray<FPMCVertex>& Vertices);

	static void FindOutlineEdges(const TArray<FPMCTriangle>& OriginTriangles, TArray<TPair<int32, int32>>& OutlineEdges);

	static void MergeTriangles(const TArray<FPMCTriangle>& MergeTriangles, TArray<FPMCTriangle>& Triangles);
};

struct MAGICCORE_API FPMCTriangleWithOutline : FPMCTriangle
{
public:

	bool Isolated[3];

public:

	FPMCTriangleWithOutline()
		:FPMCTriangle()
	{
		FMemory::Memzero(Isolated, sizeof(bool) * 3);
	}

};
