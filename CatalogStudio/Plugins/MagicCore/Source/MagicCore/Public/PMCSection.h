// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ProceduralMeshComponent/Public/ProceduralMeshComponent.h"
/**
 *
 */
struct MAGICCORE_API FPMCSection
{
public:

	TArray<FVector>				Vertexes;

	TArray<int32>				Triangles;

	TArray<FVector>				Normals;

	TArray<FVector2D>			UV;

	TArray<FProcMeshTangent>	Tangents;

	FName						SectionName;

public:
	FPMCSection();

	FPMCSection(const FName& InName);

	virtual ~FPMCSection();

	bool Equal_Precise(const FPMCSection& InOther)
	{
		bool EqualVertexes = Vertexes.Num() == InOther.Vertexes.Num();
		if (EqualVertexes)
		{
			for (int32 i = 0; i < Vertexes.Num(); ++i)
			{
				if ((Vertexes[i] - InOther.Vertexes[i]).Size() > 0.1)
				{
					EqualVertexes = false;
					break;
				}
			}
		}

		bool EqualTriangles = Triangles.Num() == InOther.Triangles.Num();
		if(EqualTriangles)
		{
			for (int32 i = 0; i < Triangles.Num(); ++i)
			{
				if (Triangles[i] != InOther.Triangles[i])
				{
					EqualTriangles = false;
					break;
				}
			}
		}

		bool EqualNormals = Normals.Num() == InOther.Normals.Num();
		if (EqualNormals)
		{
			for (int32 i = 0; i < Normals.Num(); ++i)
			{
				if ((Normals[i] - InOther.Normals[i]).Size() > 0.1)
				{
					EqualNormals = false;
					break;
				}
			}
		}

		bool EqualUV = UV.Num() == InOther.UV.Num();
		if(EqualUV)
		{
			for (int32 i = 0; i < UV.Num(); ++i)
			{
				if ((UV[i] - InOther.UV[i]).Size() > 0.1)
				{
					EqualUV = false;
					break;
				}
			}
		}

		bool EqualTangents = Tangents.Num() == InOther.Tangents.Num();
		if (EqualTangents)
		{
			for (int32 i = 0; i < Tangents.Num(); ++i)
			{
				if ((Tangents[i].TangentX - InOther.Tangents[i].TangentX).Size() > 0.1)
				{
					EqualTangents = false;
					break;
				}
			}
		}

		const bool EqualName = SectionName == InOther.SectionName;

		return EqualVertexes && EqualTriangles && EqualNormals && EqualUV && EqualTangents && EqualName;
	}

	virtual void operator+=(const FPMCSection& InOther);

	void operator+=(const FVector& InOther);

	FPMCSection operator+(const FPMCSection& InOther);

	bool IsEmpty() const;

	void Empty();

	//计算模型的表面积，单位为平方厘米
	float SurfaceArea() const;

	bool operator!=(const FPMCSection& InOther) const;

	void GetBoundingBox(FBox& OutBox) const;

	void FlipNormal();
};
