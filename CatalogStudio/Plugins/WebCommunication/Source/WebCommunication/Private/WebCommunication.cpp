// Copyright 2017-2019 <PERSON>(<PERSON>cke). All Rights Reserved.

#include "WebCommunication.h"

#define LOCTEXT_NAMESPACE "FWebCommunicationModule"

void FWebCommunicationModule::StartupModule()
{
	//设置网络超时时间为30分钟，以解决发布时所需时间过长导致超时的问题
	FHttpModule::Get().SetHttpTimeout(30.0f * 60.0f);
}

void FWebCommunicationModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FWebCommunicationModule, WebCommunication)