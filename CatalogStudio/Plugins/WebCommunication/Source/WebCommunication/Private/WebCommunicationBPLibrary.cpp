// Copyright 2017-2019 <PERSON>(<PERSON><PERSON>). All Rights Reserved.

#include "WebCommunicationBPLibrary.h"

UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::webcom;
//static FExecuteHttpRequestsThread* httpRequestsThread = nullptr;
//static FSendHTTPInvidualDataToServerThread* httpInvidualDataToServerThread = nullptr;

UWebCommunicationBPLibrary::UWebCommunicationBPLibrary(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer) {

	webcom = this;

	//if (webComObjectMbitTimerThread == nullptr)
	//	webComObjectMbitTimerThread = new FWebComObjectMbitTimerThread();

	//Delegate
	onhttpRequestCompleteDelegate.AddDynamic(this, &UWebCommunicationBPLibrary::httpRequestCompleteDelegate);
	onhttpFileProgressDelegate.AddDynamic(this, &UWebCommunicationBPLibrary::httpFileProgressDelegate);
	onhttpFileDownloadDelegate.AddDynamic(this, &UWebCommunicationBPLibrary::httpFileDownloadDelegate);
	onhttpFileUploadDelegate.AddDynamic(this, &UWebCommunicationBPLibrary::httpFileUploadDelegate);

	//found in /etc/mime.types on my linux server
	mimeTypes.Add("ez", "application/andrew-inset");
	mimeTypes.Add("anx", "application/annodex");
	mimeTypes.Add("atom", "application/atom+xml");
	mimeTypes.Add("atomcat", "application/atomcat+xml");
	mimeTypes.Add("atomsrv", "application/atomserv+xml");
	mimeTypes.Add("lin", "application/bbolin");
	mimeTypes.Add("cu", "application/cu-seeme");
	mimeTypes.Add("davmount", "application/davmount+xml");
	mimeTypes.Add("dcm", "application/dicom");
	mimeTypes.Add("tsp", "application/dsptype");
	mimeTypes.Add("es", "application/ecmascript");
	mimeTypes.Add("spl", "application/futuresplash");
	mimeTypes.Add("hta", "application/hta");
	mimeTypes.Add("jar", "application/java-archive");
	mimeTypes.Add("ser", "application/java-serialized-object");
	mimeTypes.Add("class", "application/java-vm");
	mimeTypes.Add("js", "application/javascript");
	mimeTypes.Add("json", "application/json");
	mimeTypes.Add("m3g", "application/m3g");
	mimeTypes.Add("hqx", "application/mac-binhex40");
	mimeTypes.Add("cpt", "application/mac-compactpro");
	mimeTypes.Add("nb", "application/mathematica");
	mimeTypes.Add("nbp", "application/mathematica");
	mimeTypes.Add("mbox", "application/mbox");
	mimeTypes.Add("mdb", "application/msaccess");
	mimeTypes.Add("doc", "application/msword");
	mimeTypes.Add("dot", "application/msword");
	mimeTypes.Add("mxf", "application/mxf");
	mimeTypes.Add("bin", "application/octet-stream");
	mimeTypes.Add("oda", "application/oda");
	mimeTypes.Add("ogx", "application/ogg");
	mimeTypes.Add("one", "application/onenote");
	mimeTypes.Add("onetoc2", "application/onenote");
	mimeTypes.Add("onetmp", "application/onenote");
	mimeTypes.Add("onepkg", "application/onenote");
	mimeTypes.Add("pdf", "application/pdf");
	mimeTypes.Add("pgp", "application/pgp-encrypted");
	mimeTypes.Add("key", "application/pgp-keys");
	mimeTypes.Add("sig", "application/pgp-signature");
	mimeTypes.Add("prf", "application/pics-rules");
	mimeTypes.Add("ps", "application/postscript");
	mimeTypes.Add("ai", "application/postscript");
	mimeTypes.Add("eps", "application/postscript");
	mimeTypes.Add("epsi", "application/postscript");
	mimeTypes.Add("epsf", "application/postscript");
	mimeTypes.Add("eps2", "application/postscript");
	mimeTypes.Add("eps3", "application/postscript");
	mimeTypes.Add("rar", "application/rar");
	mimeTypes.Add("rdf", "application/rdf+xml");
	mimeTypes.Add("rtf", "application/rtf");
	mimeTypes.Add("stl", "application/sla");
	mimeTypes.Add("smi", "application/smil");
	mimeTypes.Add("smil", "application/smil");
	mimeTypes.Add("xhtml", "application/xhtml+xml");
	mimeTypes.Add("xht", "application/xhtml+xml");
	mimeTypes.Add("xml", "application/xml");
	mimeTypes.Add("xsl", "application/xml");
	mimeTypes.Add("xsd", "application/xml");
	mimeTypes.Add("xspf", "application/xspf+xml");
	mimeTypes.Add("zip", "application/zip");
	mimeTypes.Add("xlam", "application/vnd.ms-excel.addin.macroEnabled.12");
	mimeTypes.Add("xlsb", "application/vnd.ms-excel.sheet.binary.macroEnabled.12");
	mimeTypes.Add("xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12");
	mimeTypes.Add("xltm", "application/vnd.ms-excel.template.macroEnabled.12");
	mimeTypes.Add("ppam", "application/vnd.ms-powerpoint.addin.macroEnabled.12");
	mimeTypes.Add("pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12");
	mimeTypes.Add("sldm", "application/vnd.ms-powerpoint.slide.macroEnabled.12");
	mimeTypes.Add("ppsm", "application/vnd.ms-powerpoint.slideshow.macroEnabled.12");
	mimeTypes.Add("potm", "application/vnd.ms-powerpoint.template.macroEnabled.12");
	mimeTypes.Add("docm", "application/vnd.ms-word.document.macroEnabled.12");
	mimeTypes.Add("dotm", "application/vnd.ms-word.template.macroEnabled.12");
	mimeTypes.Add("odc", "application/vnd.oasis.opendocument.chart");
	mimeTypes.Add("odb", "application/vnd.oasis.opendocument.database");
	mimeTypes.Add("odf", "application/vnd.oasis.opendocument.formula");
	mimeTypes.Add("odg", "application/vnd.oasis.opendocument.graphics");
	mimeTypes.Add("otg", "application/vnd.oasis.opendocument.graphics-template");
	mimeTypes.Add("odi", "application/vnd.oasis.opendocument.image");
	mimeTypes.Add("odp", "application/vnd.oasis.opendocument.presentation");
	mimeTypes.Add("otp", "application/vnd.oasis.opendocument.presentation-template");
	mimeTypes.Add("ods", "application/vnd.oasis.opendocument.spreadsheet");
	mimeTypes.Add("ots", "application/vnd.oasis.opendocument.spreadsheet-template");
	mimeTypes.Add("odm", "application/vnd.oasis.opendocument.text-master");
	mimeTypes.Add("ott", "application/vnd.oasis.opendocument.text-template");
	mimeTypes.Add("oth", "application/vnd.oasis.opendocument.text-web");
	mimeTypes.Add("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
	mimeTypes.Add("sldx", "application/vnd.openxmlformats-officedocument.presentationml.slide");
	mimeTypes.Add("ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
	mimeTypes.Add("potx", "application/vnd.openxmlformats-officedocument.presentationml.template");
	mimeTypes.Add("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
	mimeTypes.Add("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
	mimeTypes.Add("xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
	mimeTypes.Add("xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
	mimeTypes.Add("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
	mimeTypes.Add("dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
	mimeTypes.Add("sgl", "application/vnd.stardivision.writer-global");
	mimeTypes.Add("sti", "application/vnd.sun.xml.impress.template");
	mimeTypes.Add("wk", "application/x-123");
	mimeTypes.Add("7z", "application/x-7z-compressed");
	mimeTypes.Add("abw", "application/x-abiword");
	mimeTypes.Add("dmg", "application/x-apple-diskimage");
	mimeTypes.Add("bcpio", "application/x-bcpio");
	mimeTypes.Add("torrent", "application/x-bittorrent");
	mimeTypes.Add("cab", "application/x-cab");
	mimeTypes.Add("cbr", "application/x-cbr");
	mimeTypes.Add("cbz", "application/x-cbz");
	mimeTypes.Add("cdf", "application/x-cdf");
	mimeTypes.Add("cda", "application/x-cdf");
	mimeTypes.Add("vcd", "application/x-cdlink");
	mimeTypes.Add("pgn", "application/x-chess-pgn");
	mimeTypes.Add("mph", "application/x-comsol");
	mimeTypes.Add("cpio", "application/x-cpio");
	mimeTypes.Add("csh", "application/x-csh");
	mimeTypes.Add("deb", "application/x-debian-package");
	mimeTypes.Add("udeb", "application/x-debian-package");
	mimeTypes.Add("dcr", "application/x-director");
	mimeTypes.Add("dir", "application/x-director");
	mimeTypes.Add("dxr", "application/x-director");
	mimeTypes.Add("dms", "application/x-dms");
	mimeTypes.Add("wad", "application/x-doom");
	mimeTypes.Add("dvi", "application/x-dvi");
	mimeTypes.Add("pfa", "application/x-font");
	mimeTypes.Add("pfb", "application/x-font");
	mimeTypes.Add("gsf", "application/x-font");
	mimeTypes.Add("pcf", "application/x-font");
	mimeTypes.Add("pcf.Z", "application/x-font");
	mimeTypes.Add("woff", "application/x-font-woff");
	mimeTypes.Add("mm", "application/x-freemind");
	mimeTypes.Add("spl", "application/x-futuresplash");
	mimeTypes.Add("gan", "application/x-ganttproject");
	mimeTypes.Add("gnumeric", "application/x-gnumeric");
	mimeTypes.Add("sgf", "application/x-go-sgf");
	mimeTypes.Add("gcf", "application/x-graphing-calculator");
	mimeTypes.Add("gtar", "application/x-gtar");
	mimeTypes.Add("tgz", "application/x-gtar-compressed");
	mimeTypes.Add("taz", "application/x-gtar-compressed");
	mimeTypes.Add("hdf", "application/x-hdf");
	mimeTypes.Add("rhtml", "#application/x-httpd-eruby");
	mimeTypes.Add("phtml", "#application/x-httpd-php");
	mimeTypes.Add("pht", "#application/x-httpd-php");
	mimeTypes.Add("php", "#application/x-httpd-php");
	mimeTypes.Add("phps", "#application/x-httpd-php-source");
	mimeTypes.Add("php3", "#application/x-httpd-php3");
	mimeTypes.Add("php3p", "#application/x-httpd-php3-preprocessed");
	mimeTypes.Add("php4", "#application/x-httpd-php4");
	mimeTypes.Add("php5", "#application/x-httpd-php5");
	mimeTypes.Add("hwp", "application/x-hwp");
	mimeTypes.Add("ica", "application/x-ica");
	mimeTypes.Add("info", "application/x-info");
	mimeTypes.Add("ins", "application/x-internet-signup");
	mimeTypes.Add("isp", "application/x-internet-signup");
	mimeTypes.Add("iii", "application/x-iphone");
	mimeTypes.Add("iso", "application/x-iso9660-image");
	mimeTypes.Add("jam", "application/x-jam");
	mimeTypes.Add("jnlp", "application/x-java-jnlp-file");
	mimeTypes.Add("jmz", "application/x-jmol");
	mimeTypes.Add("chrt", "application/x-kchart");
	mimeTypes.Add("kil", "application/x-killustrator");
	mimeTypes.Add("skp", "application/x-koan");
	mimeTypes.Add("skd", "application/x-koan");
	mimeTypes.Add("skt", "application/x-koan");
	mimeTypes.Add("skm", "application/x-koan");
	mimeTypes.Add("kpr", "application/x-kpresenter");
	mimeTypes.Add("kpt", "application/x-kpresenter");
	mimeTypes.Add("ksp", "application/x-kspread");
	mimeTypes.Add("kwd", "application/x-kword");
	mimeTypes.Add("kwt", "application/x-kword");
	mimeTypes.Add("latex", "application/x-latex");
	mimeTypes.Add("lha", "application/x-lha");
	mimeTypes.Add("lyx", "application/x-lyx");
	mimeTypes.Add("lzh", "application/x-lzh");
	mimeTypes.Add("lzx", "application/x-lzx");
	mimeTypes.Add("frm", "application/x-maker");
	mimeTypes.Add("maker", "application/x-maker");
	mimeTypes.Add("frame", "application/x-maker");
	mimeTypes.Add("fm", "application/x-maker");
	mimeTypes.Add("fb", "application/x-maker");
	mimeTypes.Add("book", "application/x-maker");
	mimeTypes.Add("fbdoc", "application/x-maker");
	mimeTypes.Add("md5", "application/x-md5");
	mimeTypes.Add("mif", "application/x-mif");
	mimeTypes.Add("m3u8", "application/x-mpegURL");
	mimeTypes.Add("wmd", "application/x-ms-wmd");
	mimeTypes.Add("wmz", "application/x-ms-wmz");
	mimeTypes.Add("com", "application/x-msdos-program");
	mimeTypes.Add("exe", "application/x-msdos-program");
	mimeTypes.Add("bat", "application/x-msdos-program");
	mimeTypes.Add("dll", "application/x-msdos-program");
	mimeTypes.Add("msi", "application/x-msi");
	mimeTypes.Add("nc", "application/x-netcdf");
	mimeTypes.Add("pac", "application/x-ns-proxy-autoconfig");
	mimeTypes.Add("dat", "application/x-ns-proxy-autoconfig");
	mimeTypes.Add("nwc", "application/x-nwc");
	mimeTypes.Add("o", "application/x-object");
	mimeTypes.Add("oza", "application/x-oz-application");
	mimeTypes.Add("p7r", "application/x-pkcs7-certreqresp");
	mimeTypes.Add("crl", "application/x-pkcs7-crl");
	mimeTypes.Add("pyc", "application/x-python-code");
	mimeTypes.Add("pyo", "application/x-python-code");
	mimeTypes.Add("qgs", "application/x-qgis");
	mimeTypes.Add("shp", "application/x-qgis");
	mimeTypes.Add("shx", "application/x-qgis");
	mimeTypes.Add("qtl", "application/x-quicktimeplayer");
	mimeTypes.Add("rdp", "application/x-rdp");
	mimeTypes.Add("rpm", "application/x-redhat-package-manager");
	mimeTypes.Add("rss", "application/x-rss+xml");
	mimeTypes.Add("rb", "application/x-ruby");
	mimeTypes.Add("sci", "application/x-scilab");
	mimeTypes.Add("sce", "application/x-scilab");
	mimeTypes.Add("xcos", "application/x-scilab-xcos");
	mimeTypes.Add("sh", "application/x-sh");
	mimeTypes.Add("sha1", "application/x-sha1");
	mimeTypes.Add("shar", "application/x-shar");
	mimeTypes.Add("swf", "application/x-shockwave-flash");
	mimeTypes.Add("swfl", "application/x-shockwave-flash");
	mimeTypes.Add("scr", "application/x-silverlight");
	mimeTypes.Add("sql", "application/x-sql");
	mimeTypes.Add("sit", "application/x-stuffit");
	mimeTypes.Add("sitx", "application/x-stuffit");
	mimeTypes.Add("sv4cpio", "application/x-sv4cpio");
	mimeTypes.Add("sv4crc", "application/x-sv4crc");
	mimeTypes.Add("tar", "application/x-tar");
	mimeTypes.Add("tcl", "application/x-tcl");
	mimeTypes.Add("gf", "application/x-tex-gf");
	mimeTypes.Add("pk", "application/x-tex-pk");
	mimeTypes.Add("texinfo", "application/x-texinfo");
	mimeTypes.Add("texi", "application/x-texinfo");
	mimeTypes.Add("~", "application/x-trash");
	mimeTypes.Add("%", "application/x-trash");
	mimeTypes.Add("bak", "application/x-trash");
	mimeTypes.Add("old", "application/x-trash");
	mimeTypes.Add("sik", "application/x-trash");
	mimeTypes.Add("t", "application/x-troff");
	mimeTypes.Add("tr", "application/x-troff");
	mimeTypes.Add("roff", "application/x-troff");
	mimeTypes.Add("man", "application/x-troff-man");
	mimeTypes.Add("me", "application/x-troff-me");
	mimeTypes.Add("ms", "application/x-troff-ms");
	mimeTypes.Add("ustar", "application/x-ustar");
	mimeTypes.Add("src", "application/x-wais-source");
	mimeTypes.Add("wz", "application/x-wingz");
	mimeTypes.Add("crt", "application/x-x509-ca-cert");
	mimeTypes.Add("xcf", "application/x-xcf");
	mimeTypes.Add("fig", "application/x-xfig");
	mimeTypes.Add("xpi", "application/x-xpinstall");
	mimeTypes.Add("amr", "audio/amr");
	mimeTypes.Add("awb", "audio/amr-wb");
	mimeTypes.Add("amr", "audio/amr");
	mimeTypes.Add("awb", "audio/amr-wb");
	mimeTypes.Add("axa", "audio/annodex");
	mimeTypes.Add("au", "audio/basic");
	mimeTypes.Add("snd", "audio/basic");
	mimeTypes.Add("csd", "audio/csound");
	mimeTypes.Add("orc", "audio/csound");
	mimeTypes.Add("sco", "audio/csound");
	mimeTypes.Add("flac", "audio/flac");
	mimeTypes.Add("mid", "audio/midi");
	mimeTypes.Add("midi", "audio/midi");
	mimeTypes.Add("kar", "audio/midi");
	mimeTypes.Add("mpga", "audio/mpeg");
	mimeTypes.Add("mpega", "audio/mpeg");
	mimeTypes.Add("mp2", "audio/mpeg");
	mimeTypes.Add("mp3", "audio/mpeg");
	mimeTypes.Add("m4a", "audio/mpeg");
	mimeTypes.Add("m3u", "audio/mpegurl");
	mimeTypes.Add("oga", "audio/ogg");
	mimeTypes.Add("ogg", "audio/ogg");
	mimeTypes.Add("spx", "audio/ogg");
	mimeTypes.Add("sid", "audio/prs.sid");
	mimeTypes.Add("aif", "audio/x-aiff");
	mimeTypes.Add("aiff", "audio/x-aiff");
	mimeTypes.Add("aifc", "audio/x-aiff");
	mimeTypes.Add("gsm", "audio/x-gsm");
	mimeTypes.Add("m3u", "audio/x-mpegurl");
	mimeTypes.Add("wma", "audio/x-ms-wma");
	mimeTypes.Add("wax", "audio/x-ms-wax");
	mimeTypes.Add("ra", "audio/x-pn-realaudio");
	mimeTypes.Add("rm", "audio/x-pn-realaudio");
	mimeTypes.Add("ram", "audio/x-pn-realaudio");
	mimeTypes.Add("ra", "audio/x-realaudio");
	mimeTypes.Add("pls", "audio/x-scpls");
	mimeTypes.Add("sd2", "audio/x-sd2");
	mimeTypes.Add("wav", "audio/x-wav");
	mimeTypes.Add("alc", "chemical/x-alchemy");
	mimeTypes.Add("cac", "chemical/x-cache");
	mimeTypes.Add("cache", "chemical/x-cache");
	mimeTypes.Add("csf", "chemical/x-cache-csf");
	mimeTypes.Add("cbin", "chemical/x-cactvs-binary");
	mimeTypes.Add("cascii", "chemical/x-cactvs-binary");
	mimeTypes.Add("ctab", "chemical/x-cactvs-binary");
	mimeTypes.Add("cdx", "chemical/x-cdx");
	mimeTypes.Add("cer", "chemical/x-cerius");
	mimeTypes.Add("c3d", "chemical/x-chem3d");
	mimeTypes.Add("chm", "chemical/x-chemdraw");
	mimeTypes.Add("cif", "chemical/x-cif");
	mimeTypes.Add("cmdf", "chemical/x-cmdf");
	mimeTypes.Add("cml", "chemical/x-cml");
	mimeTypes.Add("cpa", "chemical/x-compass");
	mimeTypes.Add("bsd", "chemical/x-crossfire");
	mimeTypes.Add("csml", "chemical/x-csml");
	mimeTypes.Add("csm", "chemical/x-csml");
	mimeTypes.Add("ctx", "chemical/x-ctx");
	mimeTypes.Add("cxf", "chemical/x-cxf");
	mimeTypes.Add("cef", "chemical/x-cxf");
	mimeTypes.Add("smi", "#chemical/x-daylight-smiles");
	mimeTypes.Add("emb", "chemical/x-embl-dl-nucleotide");
	mimeTypes.Add("embl", "chemical/x-embl-dl-nucleotide");
	mimeTypes.Add("spc", "chemical/x-galactic-spc");
	mimeTypes.Add("inp", "chemical/x-gamess-input");
	mimeTypes.Add("gam", "chemical/x-gamess-input");
	mimeTypes.Add("gamin", "chemical/x-gamess-input");
	mimeTypes.Add("fch", "chemical/x-gaussian-checkpoint");
	mimeTypes.Add("fchk", "chemical/x-gaussian-checkpoint");
	mimeTypes.Add("cub", "chemical/x-gaussian-cube");
	mimeTypes.Add("gau", "chemical/x-gaussian-input");
	mimeTypes.Add("gjc", "chemical/x-gaussian-input");
	mimeTypes.Add("gjf", "chemical/x-gaussian-input");
	mimeTypes.Add("gal", "chemical/x-gaussian-log");
	mimeTypes.Add("gcg", "chemical/x-gcg8-sequence");
	mimeTypes.Add("gen", "chemical/x-genbank");
	mimeTypes.Add("hin", "chemical/x-hin");
	mimeTypes.Add("istr", "chemical/x-isostar");
	mimeTypes.Add("ist", "chemical/x-isostar");
	mimeTypes.Add("jdx", "chemical/x-jcamp-dx");
	mimeTypes.Add("dx", "chemical/x-jcamp-dx");
	mimeTypes.Add("kin", "chemical/x-kinemage");
	mimeTypes.Add("mcm", "chemical/x-macmolecule");
	mimeTypes.Add("mmd", "chemical/x-macromodel-input");
	mimeTypes.Add("mmod", "chemical/x-macromodel-input");
	mimeTypes.Add("mol", "chemical/x-mdl-molfile");
	mimeTypes.Add("rd", "chemical/x-mdl-rdfile");
	mimeTypes.Add("rxn", "chemical/x-mdl-rxnfile");
	mimeTypes.Add("sd", "chemical/x-mdl-sdfile");
	mimeTypes.Add("sdf", "chemical/x-mdl-sdfile");
	mimeTypes.Add("tgf", "chemical/x-mdl-tgf");
	mimeTypes.Add("mif", "#chemical/x-mif");
	mimeTypes.Add("mcif", "chemical/x-mmcif");
	mimeTypes.Add("mol2", "chemical/x-mol2");
	mimeTypes.Add("b", "chemical/x-molconn-Z");
	mimeTypes.Add("gpt", "chemical/x-mopac-graph");
	mimeTypes.Add("mop", "chemical/x-mopac-input");
	mimeTypes.Add("mopcrt", "chemical/x-mopac-input");
	mimeTypes.Add("mpc", "chemical/x-mopac-input");
	mimeTypes.Add("zmt", "chemical/x-mopac-input");
	mimeTypes.Add("moo", "chemical/x-mopac-out");
	mimeTypes.Add("mvb", "chemical/x-mopac-vib");
	mimeTypes.Add("asn", "chemical/x-ncbi-asn1");
	mimeTypes.Add("prt", "chemical/x-ncbi-asn1-ascii");
	mimeTypes.Add("ent", "chemical/x-ncbi-asn1-ascii");
	mimeTypes.Add("val", "chemical/x-ncbi-asn1-binary");
	mimeTypes.Add("aso", "chemical/x-ncbi-asn1-binary");
	mimeTypes.Add("asn", "chemical/x-ncbi-asn1-spec");
	mimeTypes.Add("pdb", "chemical/x-pdb");
	mimeTypes.Add("ent", "chemical/x-pdb");
	mimeTypes.Add("ros", "chemical/x-rosdal");
	mimeTypes.Add("sw", "chemical/x-swissprot");
	mimeTypes.Add("vms", "chemical/x-vamas-iso14976");
	mimeTypes.Add("vmd", "chemical/x-vmd");
	mimeTypes.Add("xtel", "chemical/x-xtel");
	mimeTypes.Add("xyz", "chemical/x-xyz");
	mimeTypes.Add("gif", "image/gif");
	mimeTypes.Add("ief", "image/ief");
	mimeTypes.Add("jpeg", "image/jpeg");
	mimeTypes.Add("jpg", "image/jpeg");
	mimeTypes.Add("jpe", "image/jpeg");
	mimeTypes.Add("pcx", "image/pcx");
	mimeTypes.Add("png", "image/png");
	mimeTypes.Add("svg", "image/svg+xml");
	mimeTypes.Add("svgz", "image/svg+xml");
	mimeTypes.Add("tiff", "image/tiff");
	mimeTypes.Add("tif", "image/tiff");
	mimeTypes.Add("djvu", "image/vnd.djvu");
	mimeTypes.Add("djv", "image/vnd.djvu");
	mimeTypes.Add("ico", "image/vnd.microsoft.icon");
	mimeTypes.Add("wbmp", "image/vnd.wap.wbmp");
	mimeTypes.Add("cr2", "image/x-canon-cr2");
	mimeTypes.Add("crw", "image/x-canon-crw");
	mimeTypes.Add("ras", "image/x-cmu-raster");
	mimeTypes.Add("cdr", "image/x-coreldraw");
	mimeTypes.Add("pat", "image/x-coreldrawpattern");
	mimeTypes.Add("cdt", "image/x-coreldrawtemplate");
	mimeTypes.Add("cpt", "image/x-corelphotopaint");
	mimeTypes.Add("erf", "image/x-epson-erf");
	mimeTypes.Add("art", "image/x-jg");
	mimeTypes.Add("jng", "image/x-jng");
	mimeTypes.Add("bmp", "image/x-ms-bmp");
	mimeTypes.Add("nef", "image/x-nikon-nef");
	mimeTypes.Add("orf", "image/x-olympus-orf");
	mimeTypes.Add("psd", "image/x-photoshop");
	mimeTypes.Add("pnm", "image/x-portable-anymap");
	mimeTypes.Add("pbm", "image/x-portable-bitmap");
	mimeTypes.Add("pgm", "image/x-portable-graymap");
	mimeTypes.Add("ppm", "image/x-portable-pixmap");
	mimeTypes.Add("rgb", "image/x-rgb");
	mimeTypes.Add("xbm", "image/x-xbitmap");
	mimeTypes.Add("xpm", "image/x-xpixmap");
	mimeTypes.Add("xwd", "image/x-xwindowdump");
	mimeTypes.Add("eml", "message/rfc822");
	mimeTypes.Add("igs", "model/iges");
	mimeTypes.Add("iges", "model/iges");
	mimeTypes.Add("msh", "model/mesh");
	mimeTypes.Add("mesh", "model/mesh");
	mimeTypes.Add("silo", "model/mesh");
	mimeTypes.Add("wrl", "model/vrml");
	mimeTypes.Add("vrml", "model/vrml");
	mimeTypes.Add("x3dv", "model/x3d+vrml");
	mimeTypes.Add("x3d", "model/x3d+xml");
	mimeTypes.Add("x3db", "model/x3d+binary");
	mimeTypes.Add("appcache", "text/cache-manifest");
	mimeTypes.Add("ics", "text/calendar");
	mimeTypes.Add("icz", "text/calendar");
	mimeTypes.Add("css", "text/css");
	mimeTypes.Add("csv", "text/csv");
	mimeTypes.Add("323", "text/h323");
	mimeTypes.Add("html", "text/html");
	mimeTypes.Add("htm", "text/html");
	mimeTypes.Add("shtml", "text/html");
	mimeTypes.Add("uls", "text/iuls");
	mimeTypes.Add("mml", "text/mathml");
	mimeTypes.Add("asc", "text/plain");
	mimeTypes.Add("txt", "text/plain");
	mimeTypes.Add("text", "text/plain");
	mimeTypes.Add("pot", "text/plain");
	mimeTypes.Add("brf", "text/plain");
	mimeTypes.Add("srt", "text/plain");
	mimeTypes.Add("rtx", "text/richtext");
	mimeTypes.Add("sct", "text/scriptlet");
	mimeTypes.Add("wsc", "text/scriptlet");
	mimeTypes.Add("tm", "text/texmacs");
	mimeTypes.Add("tsv", "text/tab-separated-values");
	mimeTypes.Add("jad", "text/vnd.sun.j2me.app-descriptor");
	mimeTypes.Add("wml", "text/vnd.wap.wml");
	mimeTypes.Add("wmls", "text/vnd.wap.wmlscript");
	mimeTypes.Add("bib", "text/x-bibtex");
	mimeTypes.Add("boo", "text/x-boo");
	mimeTypes.Add("h++", "text/x-c++hdr");
	mimeTypes.Add("hpp", "text/x-c++hdr");
	mimeTypes.Add("hxx", "text/x-c++hdr");
	mimeTypes.Add("hh", "text/x-c++hdr");
	mimeTypes.Add("c++", "text/x-c++src");
	mimeTypes.Add("cpp", "text/x-c++src");
	mimeTypes.Add("cxx", "text/x-c++src");
	mimeTypes.Add("cc", "text/x-c++src");
	mimeTypes.Add("h", "text/x-chdr");
	mimeTypes.Add("htc", "text/x-component");
	mimeTypes.Add("csh", "text/x-csh");
	mimeTypes.Add("c", "text/x-csrc");
	mimeTypes.Add("d", "text/x-dsrc");
	mimeTypes.Add("diff", "text/x-diff");
	mimeTypes.Add("patch", "text/x-diff");
	mimeTypes.Add("hs", "text/x-haskell");
	mimeTypes.Add("java", "text/x-java");
	mimeTypes.Add("ly", "text/x-lilypond");
	mimeTypes.Add("lhs", "text/x-literate-haskell");
	mimeTypes.Add("moc", "text/x-moc");
	mimeTypes.Add("p", "text/x-pascal");
	mimeTypes.Add("pas", "text/x-pascal");
	mimeTypes.Add("gcd", "text/x-pcs-gcd");
	mimeTypes.Add("pl", "text/x-perl");
	mimeTypes.Add("pm", "text/x-perl");
	mimeTypes.Add("py", "text/x-python");
	mimeTypes.Add("scala", "text/x-scala");
	mimeTypes.Add("etx", "text/x-setext");
	mimeTypes.Add("sfv", "text/x-sfv");
	mimeTypes.Add("sh", "text/x-sh");
	mimeTypes.Add("tcl", "text/x-tcl");
	mimeTypes.Add("tk", "text/x-tcl");
	mimeTypes.Add("tex", "text/x-tex");
	mimeTypes.Add("ltx", "text/x-tex");
	mimeTypes.Add("sty", "text/x-tex");
	mimeTypes.Add("cls", "text/x-tex");
	mimeTypes.Add("vcs", "text/x-vcalendar");
	mimeTypes.Add("vcf", "text/x-vcard");
	mimeTypes.Add("3gp", "video/3gpp");
	mimeTypes.Add("axv", "video/annodex");
	mimeTypes.Add("dl", "video/dl");
	mimeTypes.Add("dif", "video/dv");
	mimeTypes.Add("dv", "video/dv");
	mimeTypes.Add("fli", "video/fli");
	mimeTypes.Add("gl", "video/gl");
	mimeTypes.Add("mpeg", "video/mpeg");
	mimeTypes.Add("mpg", "video/mpeg");
	mimeTypes.Add("mpe", "video/mpeg");
	mimeTypes.Add("ts", "video/MP2T");
	mimeTypes.Add("mp4", "video/mp4");
	mimeTypes.Add("qt", "video/quicktime");
	mimeTypes.Add("mov", "video/quicktime");
	mimeTypes.Add("ogv", "video/ogg");
	mimeTypes.Add("webm", "video/webm");
	mimeTypes.Add("mxu", "video/vnd.mpegurl");
	mimeTypes.Add("flv", "video/x-flv");
	mimeTypes.Add("lsf", "video/x-la-asf");
	mimeTypes.Add("lsx", "video/x-la-asf");
	mimeTypes.Add("mng", "video/x-mng");
	mimeTypes.Add("asf", "video/x-ms-asf");
	mimeTypes.Add("asx", "video/x-ms-asf");
	mimeTypes.Add("wm", "video/x-ms-wm");
	mimeTypes.Add("wmv", "video/x-ms-wmv");
	mimeTypes.Add("wmx", "video/x-ms-wmx");
	mimeTypes.Add("wvx", "video/x-ms-wvx");
	mimeTypes.Add("avi", "video/x-msvideo");
	mimeTypes.Add("movie", "video/x-sgi-movie");
	mimeTypes.Add("mpv", "video/x-matroska");
	mimeTypes.Add("mkv", "video/x-matroska");
	mimeTypes.Add("ice", "x-conference/x-cooltalk");
	mimeTypes.Add("sisx", "x-epoc/x-sisx-app");
	mimeTypes.Add("vrm", "x-world/x-vrml");
	mimeTypes.Add("vrml", "x-world/x-vrml");
	mimeTypes.Add("wrl", "x-world/x-vrml");
}



UWebCommunicationBPLibrary::~UWebCommunicationBPLibrary() {
	//if (httpRequestsThread != nullptr)
	//	httpRequestsThread->stopThread();
	//if (httpInvidualDataToServerThread != nullptr)
	//	httpInvidualDataToServerThread->stopThread();
}

/*Delegate functions*/
void UWebCommunicationBPLibrary::httpRequestCompleteDelegate(const TArray<FString>& data, const TArray<FString>& headers, const int32 statusCode, const TArray<uint8>& byteData, const FString requestID) {
	if (toCancelRequests.Find(requestID) != nullptr) {
		toCancelRequests.Remove(requestID);
	}
}
void UWebCommunicationBPLibrary::httpRequestCompleteGoogleInfoDelegate(const FString fileName, const int32 fileSizeInBytes, const int32 statusCode, const FString downloadID, const FString requestID) {}
void UWebCommunicationBPLibrary::httpFileProgressDelegate(const float size, const uint64 bytesSent, const float percentUpload, const uint64 bytesReceived, const float percentDownload) {}
void UWebCommunicationBPLibrary::httpFileDownloadDelegate(const float size, const float megaBytesReceived, const float percentDownload, const float megaBit, const FString requestID) {}
void UWebCommunicationBPLibrary::httpFileUploadDelegate(const float size, const int32 bytesSent, const float percentUpload, const FString requestID) {}

UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::getTarget() {
	return webcom;
}

UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::getWebCommunicationTarget() {
	return webcom;
}

void UWebCommunicationBPLibrary::cancelRequest(FString requestID) {
	UWebCommunicationBPLibrary::toCancelRequests.Add(requestID, requestID);
}

int32 UWebCommunicationBPLibrary::megabyteToByte(int32 mb) {
	return (mb * 1024 * 1024);
}

float UWebCommunicationBPLibrary::byteToMegabyte(int32 byte) {
	return ((float)byte / 1024 / 1024);
}

//TArray<uint8> UWebCommunicationBPLibrary::xxxTest(){
//	TArray<uint8> data;
//	FString dir = FPaths::ConvertRelativePathToFull("H:/Dokumente/Unreal Projects/BasicCode22/Content/test.data");
//	FFileHelper::LoadFileToArray(data, *dir);
//	return data;
//}

UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::httpRequestGET(FString url, FString optionalRequestID, FString& requestID) {

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return UWebCommunicationBPLibrary::webcom;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	//requestObject->AddToRoot();

	FhttpRequest httpRequest;
	httpRequest.url = url;
	httpRequest.request = requestObject;
	httpRequest.requestType = EHTTPWebComRequestType::GET;

	TArray<struct FhttpRequest> httpRequests;
	httpRequests.Add(httpRequest);

	UWebCommunicationBPLibrary::webcom->startRequests(httpRequests);

	//if (httpRequestsThread == nullptr) {
	//	httpRequestsThread = new FExecuteHttpRequestsThread(httpRequests);
	//}
	//else {
	//	httpRequestsThread->addRequests(httpRequests);
	//}

	return UWebCommunicationBPLibrary::webcom;
}


void UWebCommunicationBPLibrary::executeHttpRequests(TArray<struct FhttpRequest> httpRequests, UWebCommunicationBPLibrary*& WebCommunicationTarget) {
	UWebCommunicationBPLibrary::webcom->startRequests(httpRequests);
	WebCommunicationTarget = UWebCommunicationBPLibrary::webcom;
	/*
	if (httpRequestsThread == nullptr) {
		httpRequestsThread = new FExecuteHttpRequestsThread(httpRequests);
	}
	else {
		httpRequestsThread->addRequests(httpRequests);
	}*/
}

void UWebCommunicationBPLibrary::CreateHttpRequestGET(TArray<struct FhttpRequest> otherHttpRequests, FString url, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	//webcom->runRequestProgress = true;
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GET;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestGETDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType actionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePath, const TMap<FString, FString>& InHeaders, FString optionalRequestID) {
	if (filePath.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestGETDownload: FilePath not set."));
		return;
	}
	FString dir;
	if (DirectoryType == EHTTPWebComFileUpload::E_ad) {
		dir = FPaths::ConvertRelativePathToFull(filePath);
	}
	else {
		FString gameDir = FPaths::ProjectDir();
		dir = FPaths::ConvertRelativePathToFull(gameDir + filePath);
	}

	//if (FPaths::DirectoryExists(FPaths::GetPath(dir)) == false) {
	//	UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestGETDownload: Directory does not exist."));
	//	return;
	//}


	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setFileCancelResumeDownloadDir(dir);


	switch (actionIfFileExists)
	{
	case EHTTPWebComFileDownloadResumeType::E_NOT_OVERWRITE:
		if (FPaths::FileExists(dir)) {
			UE_LOG(LogTemp, Warning, TEXT("CreateHttpRequestGETDownload: File exist. Cancel download."));
			//onhttpRequestCompleteDelegate.Broadcast(dataArray, statusCode, byteArray, requestIDP);
			return;
		}
		break;
	case EHTTPWebComFileDownloadResumeType::E_OVERWRITE:
		FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*dir);
		break;
	case EHTTPWebComFileDownloadResumeType::E_RESUME:
		requestObject->setFileCancelResumeDownloadByteStart(FPlatformFileManager::Get().GetPlatformFile().FileSize(*dir));
		break;
	}


	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GET;
	httpRequest.url = url;
	httpRequest.request = requestObject;
	//httpRequest.POSTData = InHeaders;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestPOSTDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType actionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePath, const TMap<FString, FString>& InHeaders, FString optionalRequestID) {
	if (filePath.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestPOSTDownload: FilePath not set."));
		return;
	}
	FString dir;
	if (DirectoryType == EHTTPWebComFileUpload::E_ad) {
		dir = FPaths::ConvertRelativePathToFull(filePath);
	}
	else {
		FString gameDir = FPaths::ProjectDir();
		dir = FPaths::ConvertRelativePathToFull(gameDir + filePath);
	}

	//if (FPaths::DirectoryExists(FPaths::GetPath(dir)) == false) {
	//	UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestGETDownload: Directory does not exist."));
	//	return;
	//}


	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setFileCancelResumeDownloadDir(dir);


	switch (actionIfFileExists)
	{
	case EHTTPWebComFileDownloadResumeType::E_NOT_OVERWRITE:
		if (FPaths::FileExists(dir)) {
			UE_LOG(LogTemp, Warning, TEXT("CreateHttpRequestGETDownload: File exist. Cancel download."));
			//onhttpRequestCompleteDelegate.Broadcast(dataArray, statusCode, byteArray, requestIDP);
			return;
		}
		break;
	case EHTTPWebComFileDownloadResumeType::E_OVERWRITE:
		FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*dir);
		break;
	case EHTTPWebComFileDownloadResumeType::E_RESUME:
		requestObject->setFileCancelResumeDownloadByteStart(FPlatformFileManager::Get().GetPlatformFile().FileSize(*dir));
		break;
	}


	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::POSTDownload;
	httpRequest.url = url;
	httpRequest.request = requestObject;
	httpRequest.POSTData = InHeaders;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}


void UWebCommunicationBPLibrary::CreateHttpRequestGETLowRamDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType actionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePath, int32 fileSize, FString optionalRequestID) {
	if (filePath.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestGETLowRamDownload: FilePath not set."));
		return;
	}
	FString dir;
	if (DirectoryType == EHTTPWebComFileUpload::E_ad) {
		dir = FPaths::ConvertRelativePathToFull(filePath);
	}
	else {
		FString gameDir = FPaths::ProjectDir();
		dir = FPaths::ConvertRelativePathToFull(gameDir + filePath);
	}

	if (FPaths::DirectoryExists(FPaths::GetPath(dir)) == false) {
		UE_LOG(LogTemp, Error, TEXT("CreateHttpRequestGETLowRamDownload: Directory does not exist."));
		return;
	}


	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setFileCancelResumeDownloadBytePartSize(fileSize);
	requestObject->setFileCancelResumeDownloadDir(dir);


	switch (actionIfFileExists)
	{
	case EHTTPWebComFileDownloadResumeType::E_NOT_OVERWRITE:
		if (FPaths::FileExists(dir)) {
			UE_LOG(LogTemp, Warning, TEXT("CreateHttpRequestGETLowRamDownload: File exist. Cancel download."));
			//onhttpRequestCompleteDelegate.Broadcast(dataArray, statusCode, byteArray, requestIDP);
			return;
		}
		break;
	case EHTTPWebComFileDownloadResumeType::E_OVERWRITE:
		FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*dir);
		break;
	case EHTTPWebComFileDownloadResumeType::E_RESUME:
		requestObject->setFileCancelResumeDownloadByteStart(FPlatformFileManager::Get().GetPlatformFile().FileSize(*dir));
		break;
	}


	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GETLowRamDownload;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestGoogleDrive(TArray<struct FhttpRequest> otherHttpRequests, FString downloadID, FString optionalRequestID, int32 optionalFileSizeInByte, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	if (downloadID.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("No downloadId in CreateHttpRequestGoogleDrive."));
		return;
	}
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();
	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setDownloadID(downloadID);
	requestObject->setMultiStepDownload(1);
	requestObject->setMultiStepDownloadType(EMultiStepType::GOOGLE);
	requestObject->setHTMLFileSize(optionalFileSizeInByte);

	FString url = "https://drive.google.com/uc?id=" + downloadID + "&export=download";

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GET;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestGoogleDriveFileInfo(TArray<struct FhttpRequest> otherHttpRequests, FString downloadID, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	if (downloadID.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("No downloadId in CreateHttpRequestGoogleDriveFileInfo."));
		return;
	}

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();
	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setDownloadID(downloadID);
	requestObject->setMultiStepDownloadType(EMultiStepType::GOOGLE_INFO);

	FString url = "https://drive.google.com/file/d/" + downloadID + "/view";

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GET;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestAnonfiles(TArray<struct FhttpRequest> otherHttpRequests, FString url, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
	requestObject->setMultiStepDownload(1);
	requestObject->setMultiStepDownloadType(EMultiStepType::ANONFILE);

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::GET;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::httpRequestPOST(FString url, const TArray<FString> postData, FString optionalRequestID, FString& requestID) {
	//webcom->runRequestProgress = true;

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return UWebCommunicationBPLibrary::webcom;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::POST;
	httpRequest.url = url;
	httpRequest.request = requestObject;

	FString key;
	FString val;
	for (int32 i = 0; i != postData.Num(); ++i) {
		if ((i + 1) % 2 == 0) {
			httpRequest.POSTData.Add(key, postData[i]);
		}
		else {
			key = postData[i];
		}
	}
	TArray<struct FhttpRequest> httpRequests;
	httpRequests.Add(httpRequest);

	UWebCommunicationBPLibrary::webcom->startRequests(httpRequests);

	/*if (httpRequestsThread == nullptr) {
		httpRequestsThread = new FExecuteHttpRequestsThread(httpRequests);
	}
	else {
		httpRequestsThread->addRequests(httpRequests);
	}*/

	return UWebCommunicationBPLibrary::webcom;
}


void UWebCommunicationBPLibrary::CreateHttpRequestPOST(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> POSTData, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();


	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::POST;
	httpRequest.url = url;
	httpRequest.POSTData = POSTData;
	httpRequest.request = requestObject;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}


UWebCommunicationBPLibrary* UWebCommunicationBPLibrary::httpRequestIndividual(FString url, TMap<FString, FString> header, FString verb, FString content, FString requestID) {
	if (header.Num() == 0) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : You have not specified a header. An HTTP request does not work without a header.Add headers like user-agent,Content-Type,Content-Length"));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return UWebCommunicationBPLibrary::webcom;
	}

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return UWebCommunicationBPLibrary::webcom;
	}

	verb = verb.ToUpper();

	if (!verb.Equals("GET") && !verb.Equals("HEAD") && !verb.Equals("POST") && !verb.Equals("PUT") && !verb.Equals("DELETE")
		&& !verb.Equals("TRACE") && !verb.Equals("OPTIONS") && !verb.Equals("CONNECT") && !verb.Equals("PATCH")) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : Wrong verb. Possible values:GET,HEAD,POST,PUT,DELETE,TRACE,OPTIONS,CONNECT,PATCH"));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return UWebCommunicationBPLibrary::webcom;
	}

	//webcom->runRequestProgress = true;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();
	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	UWebCommunicationBPLibrary::webcom->startIndividualRequest(url, header, verb, content, webcom, requestObject);
	//if (CreateHttpRequestIndividual == nullptr) {
	//	httpInvidualDataToServerThread = new FSendHTTPInvidualDataToServerThread(url, header, verb, content, webcom, requestObject);
	//}
	//else {
	//	httpInvidualDataToServerThread->addRequests(url, header, verb, content, webcom, requestObject);
	//}
	return UWebCommunicationBPLibrary::webcom;
}

void UWebCommunicationBPLibrary::CreateHttpRequestIndividual(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> header, FString verb, const TArray<uint8>& Content, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID, bool addContentLengthHeader) {

	if (header.Num() == 0) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : You have not specified a header. An HTTP request does not work without a header.Add headers like user-agent,Content-Type,Content-Length"));
		return;
	}

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	verb = verb.ToUpper();

	if (!verb.Equals("GET") && !verb.Equals("HEAD") && !verb.Equals("POST") && !verb.Equals("PUT") && !verb.Equals("DELETE")
		&& !verb.Equals("TRACE") && !verb.Equals("OPTIONS") && !verb.Equals("CONNECT") && !verb.Equals("PATCH")) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : Wrong verb. Possible values:GET,HEAD,POST,PUT,DELETE,TRACE,OPTIONS,CONNECT,PATCH"));
		return;
	}

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();
	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::INDIVIDUAL;
	httpRequest.url = url;
	httpRequest.header = header;
	httpRequest.verb = verb;
	httpRequest.ContentBytes = Content;
	httpRequest.request = requestObject;
	httpRequest.bAddContentLength = addContentLengthHeader;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateStringHttpRequestIndividual(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> header, FString verb, const FString& Content, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID, bool addContentLengthHeader/* = true*/)
{
	if (header.Num() == 0) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : You have not specified a header. An HTTP request does not work without a header.Add headers like user-agent,Content-Type,Content-Length"));
		return;
	}

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	verb = verb.ToUpper();

	if (!verb.Equals("GET") && !verb.Equals("HEAD") && !verb.Equals("POST") && !verb.Equals("PUT") && !verb.Equals("DELETE")
		&& !verb.Equals("TRACE") && !verb.Equals("OPTIONS") && !verb.Equals("CONNECT") && !verb.Equals("PATCH")) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : Wrong verb. Possible values:GET,HEAD,POST,PUT,DELETE,TRACE,OPTIONS,CONNECT,PATCH"));
		return;
	}

	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();
	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::STRING_INDIVIDUAL;
	httpRequest.url = url;
	httpRequest.header = header;
	httpRequest.verb = verb;
	httpRequest.content = Content;
	httpRequest.request = requestObject;
	httpRequest.bAddContentLength = addContentLengthHeader;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::httpRequestFileUpload(FString url, EHTTPWebComFileUpload DirectoryType, FString id, FString filePath, EHTTPWebComFileUploadType uploadType, FString optionalRequestID, FString& requestID) {

	TArray<struct FhttpRequest> otherHttpRequests;
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	if (uploadType == EHTTPWebComFileUploadType::E_fut_post) {
		httpRequest.requestType = EHTTPWebComRequestType::POST_UPLOAD;
	}
	else {
		httpRequest.requestType = EHTTPWebComRequestType::PUT;
	}
	httpRequest.url = url;
	httpRequest.request = requestObject;
	httpRequest.DirectoryType = DirectoryType;
	httpRequest.filePath = filePath;
	httpRequest.fileID = id;

	otherHttpRequests.Add(httpRequest);

	UWebCommunicationBPLibrary::webcom->startRequests(otherHttpRequests);
	/*if (httpRequestsThread == nullptr) {
		httpRequestsThread = new FExecuteHttpRequestsThread(otherHttpRequests);
	}
	else {
		httpRequestsThread->addRequests(otherHttpRequests);
	}*/
}

void UWebCommunicationBPLibrary::CreateHttpRequestFileUploadPOST(TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileUpload DirectoryType, FString filePath, FString fileID, TMap<FString, FString> POSTData, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();


	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::POST_UPLOAD;
	httpRequest.url = url;
	httpRequest.POSTData = POSTData;
	httpRequest.request = requestObject;
	httpRequest.DirectoryType = DirectoryType;
	httpRequest.filePath = filePath;
	httpRequest.fileID = fileID;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::CreateHttpRequestFileUploadPUT(TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileUpload DirectoryType, FString filePath, FString fileID, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID) {
	requestID = optionalRequestID;
	if (requestID.IsEmpty())
		requestID = FGuid::NewGuid().ToString();

	if (url.Len() < 3) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : URL to short."));
		TArray<uint8> byteArray;
		TArray<FString> dataArray;
		TArray<FString> headers;
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, 400, byteArray, requestID);
		return;
	}

	UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
	requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());

	FhttpRequest httpRequest;
	httpRequest.requestType = EHTTPWebComRequestType::PUT;
	httpRequest.url = url;
	httpRequest.request = requestObject;
	httpRequest.DirectoryType = DirectoryType;
	httpRequest.filePath = filePath;
	httpRequest.fileID = fileID;

	otherHttpRequests.Add(httpRequest);
	httpRequests = otherHttpRequests;
}

void UWebCommunicationBPLibrary::byteDataToFile(EHTTPWebComFileBytesToFileActionType fileAction, TArray<uint8> byteData, EHTTPWebComFileUpload DirectoryType, FString filePath) {

	new FByteToFileThread(byteData, DirectoryType, filePath, fileAction);
}


TArray<FString> UWebCommunicationBPLibrary::createAndAppendPOSTData(FString id, FString value, TArray<FString> POSTData) {
	TArray<FString> postData;
	if (id.IsEmpty() || value.IsEmpty()) {
		return postData;
	}

	postData.Add(FGenericPlatformHttp::UrlEncode(id) + "=" + FGenericPlatformHttp::UrlEncode(value));


	if (POSTData.Num() > 0) {
		POSTData.Append(postData);
		return POSTData;
	}

	return postData;
}

TArray<FString> UWebCommunicationBPLibrary::createPOSTData(FString id, FString value) {
	TArray<FString> postData;
	if (id.IsEmpty() || value.IsEmpty()) {
		return postData;
	}

	postData.Add(FGenericPlatformHttp::UrlEncode(id) + "=" + FGenericPlatformHttp::UrlEncode(value));

	return postData;
}


void UWebCommunicationBPLibrary::addHTTPRequestHeader(FString id, FString value, bool removeAfterHTTPRequest) {
	webcom->removeHeaders = removeAfterHTTPRequest;
	if (id.IsEmpty()) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Id not set."));
		return;
	}
	if (value.IsEmpty()) {
		UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: %s removed."), *id);
		webcom->additionalHeader.Remove(id);
		return;
	}

	webcom->additionalHeader.Add(id, value);
}



FString UWebCommunicationBPLibrary::getMimeType(FString fileType) {
	//fileType = fileType.Replace(TEXT("."), TEXT(""));
	if (mimeTypes.Find(fileType) == nullptr) {
		return "application/octet-stream";
	}
	FString mimeType = *mimeTypes.Find(fileType);
	if (mimeType.IsEmpty()) {
		mimeType = "application/octet-stream";
	}
	return mimeType;
}

void UWebCommunicationBPLibrary::startRequests(TArray<struct FhttpRequest>& httpRequests) {
	for (int32 i = 0; i < httpRequests.Num(); i++) {
		UWebCommunicationRequestCompleteObject* requestObject = httpRequests[i].request;
		FString url = httpRequests[i].url;
		TMap<FString, FString> POSTData = httpRequests[i].POSTData;
		EHTTPWebComRequestType requestType = httpRequests[i].requestType;

		if (requestObject == nullptr) {
			UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (153)."));
			continue;
		}

		//requestObject->setWebComHttpRequest(httpRequests[i]);

		TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request = FHttpModule::Get().CreateRequest();

		request->SetHeader("User-Agent", "UE4 Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.81 Safari/537.36");
		if (UWebCommunicationBPLibrary::webcom->additionalHeader.Num() > 0) {
			for (auto& element : UWebCommunicationBPLibrary::webcom->additionalHeader) {
				request->SetHeader(element.Key, element.Value);
			}
		}

		switch (requestType) {
		case EHTTPWebComRequestType::GET:
		{
			fillDownloadRequest(httpRequests[i], request, TEXT("GET"));
			if (requestObject->getMultiStepDownload() > 0) {
				request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepComplete);
				request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepDownload);
				request->ProcessRequest();
				continue;
			}
			if ((requestObject->getFileCancelResumeDownloadByteStart() > 0)) {
				request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-");
			}
			request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
			break;
		}
		case EHTTPWebComRequestType::POSTDownload:
		{
			fillDownloadRequest(httpRequests[i], request, TEXT("POST"));
			if (requestObject->getMultiStepDownload() > 0) {
				request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepComplete);
				request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepDownload);
				request->ProcessRequest();
				continue;
			}
			if ((requestObject->getFileCancelResumeDownloadByteStart() > 0)) {
				request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-");
			}
			request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
			break;
		}
		case EHTTPWebComRequestType::GETLowRamDownload:
		{
			fillDownloadRequest(httpRequests[i], request, TEXT("GET"));
			request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestCompleteLowRam);
			request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownloadLowRam);
			if (requestObject->getFileCancelResumeDownloadByteFullSize() == 0) {
				request->SetHeader("Range", "bytes=0-0");
			}
			else {
				if ((requestObject->getFileCancelResumeDownloadByteStart() + requestObject->getFileCancelResumeDownloadBytePartSize()) > requestObject->getFileCancelResumeDownloadByteFullSize()) {
					request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-");
				}
				else {
					request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart() + requestObject->getFileCancelResumeDownloadBytePartSize()));
				}

			}
			//UE_LOG(LogTemp, Error, TEXT("Head: %s"), *request->GetHeader("Range"));
			request->ProcessRequest();
			continue;
			break;
		}
		case EHTTPWebComRequestType::PUT:
		case EHTTPWebComRequestType::POST_UPLOAD:
			if (!fillRequestUPLOAD(httpRequests[i], request)) {
				continue;
			}
			request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressUpload);
			break;
		case EHTTPWebComRequestType::POST:
			fillRequestPOST(httpRequests[i], request);
			request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
			break;
		case EHTTPWebComRequestType::INDIVIDUAL:
		{
			request->SetVerb(httpRequests[i].verb);
			request->SetURL(url);
			request->SetContent(httpRequests[i].ContentBytes);
			if (httpRequests[i].bAddContentLength) {
				httpRequests[i].header.Add("Content-Length", FString::FromInt(request->GetContent().Num()));
			}
			if (httpRequests[i].header.Num() > 0) {
				for (auto& element : httpRequests[i].header) {
					request->SetHeader(element.Key, element.Value);
				}
			}
			break;
		}
		case EHTTPWebComRequestType::STRING_INDIVIDUAL:
		{
			request->SetVerb(httpRequests[i].verb);
			request->SetURL(url);
			request->SetContentAsString(httpRequests[i].content);
			if (httpRequests[i].bAddContentLength) {
				httpRequests[i].header.Add("Content-Length", FString::FromInt(request->GetContent().Num()));
			}
			if (httpRequests[i].header.Num() > 0) {
				for (auto& element : httpRequests[i].header) {
					request->SetHeader(element.Key, element.Value);
				}
			}
			break;
		}
		}


		/*for (int32 i = 0; i < request->GetAllHeaders().Num(); i++){
			UE_LOG(LogTemp, Error, TEXT("Head: %s"), *request->GetAllHeaders()[i]);
		}*/

		request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestComplete);

		request->ProcessRequest();
	}
	httpRequests.Empty();
}

void UWebCommunicationBPLibrary::fillDownloadRequest(FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request, const FString& InVerb) {
	//request->SetHeader("Content-Type", "application/x-www-form-urlencoded");
	//request->SetVerb("GET");
	request->SetVerb(*InVerb);
	request->SetURL(requestStruct.url);
	for (auto& HeaderIter : requestStruct.POSTData)
	{
		request->SetHeader(HeaderIter.Key, HeaderIter.Value);
		if (HeaderIter.Key.Equals(TEXT("file_path")))
		{
			request->SetContentAsString(HeaderIter.Value);
			//if (httpRequests[i].bAddContentLength) {
			//	httpRequests[i].header.Add("Content-Length", FString::FromInt(request->GetContent().Num()));
			//}
		}
	}
}

void UWebCommunicationBPLibrary::fillRequestPOST(FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request) {
	request->SetHeader("Content-Type", "application/x-www-form-urlencoded");
	request->SetVerb("POST");
	request->SetURL(requestStruct.url);

	FString JoinedStr = postMapToString(requestStruct.POSTData);
	request->SetContentAsString(JoinedStr);
	request->SetHeader("Content-Length", FString::FromInt(JoinedStr.Len()));
}

bool UWebCommunicationBPLibrary::fillRequestUPLOAD(FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request) {

	FString dir;
	if (requestStruct.DirectoryType == EHTTPWebComFileUpload::E_ad) {
		dir = FPaths::ConvertRelativePathToFull(requestStruct.filePath);
	}
	else {
		FString gameDir = FPaths::ProjectDir();
		dir = FPaths::ConvertRelativePathToFull(gameDir + requestStruct.filePath);
	}


	if (!FPaths::FileExists(dir)) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: File not found: %s"), *dir);
		return false;
	}

	TArray<uint8> fileData;
	if (!FFileHelper::LoadFileToArray(fileData, *dir)) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Can't read File: %s"), *dir);
		return false;
	}

	FString fileName = FPaths::GetCleanFilename(dir);
	FString fileFormat = FPaths::GetExtension(fileName, false);
	FString mimeType = UWebCommunicationBPLibrary::getWebCommunicationTarget()->getMimeType(fileFormat);


	if (requestStruct.requestType == EHTTPWebComRequestType::POST_UPLOAD) {
		request->SetVerb("POST");
	}
	else {
		request->SetVerb("PUT");
	}
	request->SetURL(requestStruct.url);

	for (auto& HeaderIter : requestStruct.POSTData)
	{
		request->SetHeader(HeaderIter.Key, HeaderIter.Value);
	}
	FString boundary = "WebKitFormBoundary3soAkdFiRkxe5rBL";
	request->SetHeader("Content-Type", "multipart/form-data; boundary=----" + boundary);

	TArray<uint8> dataToSend;

	//add File
	TArray<uint8> fileHeaderArray = fstringToByteArray("------" + boundary + "\r\nContent-Disposition: form-data; name=\"" + requestStruct.fileID + "\"; filename=\"" + fileName + "\"\r\n" + mimeType + "\r\n\r\n");
	dataToSend.Append(fileHeaderArray);
	dataToSend.Append(fileData);

	//add Post Data
	//if (requestStruct.POSTData.Num() > 0) {
	//	TArray<FString> keys;
	//	requestStruct.POSTData.GetKeys(keys);
	//	for (int i = 0; i < keys.Num(); i++) {
	//		if (requestStruct.POSTData.Find(keys[i]) == nullptr) {
	//			UE_LOG(LogTemp, Warning, TEXT("UWebCommunicationBPLibrary: Parameter %s has no value."), *keys[i]);
	//			continue;
	//		}
	//		FString postVal = "\r\n------" + boundary + "\r\nContent-Disposition: form-data; name=\"" + keys[i] + "\"\r\n\r\n" + *requestStruct.POSTData.Find(keys[i]);
	//		dataToSend.Append(fstringToByteArray(postVal));
	//	}
	//}

	//file head after file data
	TArray<uint8> bottomArray = fstringToByteArray("\r\n------" + boundary + "--\r\n");
	dataToSend.Append(bottomArray);

	request->SetHeader("Content-Length", FString::FromInt(dataToSend.Num()));
	if (UWebCommunicationBPLibrary::webcom->additionalHeader.Num() > 0) {
		for (auto& element : UWebCommunicationBPLibrary::webcom->additionalHeader) {
			request->SetHeader(element.Key, element.Value);
		}
	}

	request->SetContent(dataToSend);

	return true;
}

TArray<uint8> UWebCommunicationBPLibrary::fstringToByteArray(FString data) {
	TArray<uint8> byteArray;
	/*FBufferArchive buffer;
	buffer << data;
	byteArray.Append(buffer);
	byteArray.RemoveAt(0, 4);
	byteArray.RemoveAt(byteArray.Num() - 1, 1);*/
	FTCHARToUTF8 Convert(*data);
	byteArray.Append((uint8*)((ANSICHAR*)Convert.Get()), Convert.Length());
	return byteArray;
}

FString UWebCommunicationBPLibrary::postMapToString(TMap<FString, FString> POSTData) {
	FString JoinedStr;
	TArray<FString> keys;
	POSTData.GetKeys(keys);
	for (int j = 0; j < keys.Num(); j++) {
		FString* val = POSTData.Find(keys[j]);
		FString param;
		if (val != nullptr) {
			param = keys[j] + "=" + *val;
		}

		if (JoinedStr.IsEmpty()) {
			JoinedStr = param;
		}
		else {
			JoinedStr += "&" + param;
		}
	}
	return JoinedStr;
}

void UWebCommunicationBPLibrary::startIndividualRequest(FString url, TMap<FString, FString> header, FString verb, FString content, UWebCommunicationBPLibrary* userver, UWebCommunicationRequestCompleteObject* requestObject) {
	if (requestObject == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (266)."));
		return;
	}

	TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request = FHttpModule::Get().CreateRequest();
	request->SetVerb(verb);
	request->SetURL(url);
	request->SetContentAsString(content);

	bool headerTest = false;
	if (header.Num() > 0) {
		for (auto& element : header) {
			request->SetHeader(element.Key, element.Value);
			if (!headerTest && element.Value.Len() > 0) {
				headerTest = true;
			}
		}
	}

	if (!headerTest) {
		UE_LOG(LogTemp, Error, TEXT("Cancel Request : You have not specified a header. An HTTP request does not work without a header.Add headers like user-agent,Content-Type,Content-Length"));
		//UWebCommunicationBPLibrary::cancelRequest();
	}
	else {
		request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestComplete);
		request->OnRequestProgress64().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);

		request->ProcessRequest();
	}

}


//void UWebCommunicationBPLibrary::registerRequestObjectForMbit(FString requestID, UWebCommunicationRequestCompleteObject* requestObject){
//	if (webComObjectMbitTimerThread != nullptr)
//		webComObjectMbitTimerThread->registerRequestObject(requestID, requestObject);
//}
//
//void UWebCommunicationBPLibrary::unRegisterRequestObjectForMbit(FString requestID){
//	if (webComObjectMbitTimerThread != nullptr)
//		webComObjectMbitTimerThread->unRegisterRequestObject(requestID);
//}

FString UWebCommunicationBPLibrary::urlEncode(FString urlParameter) {
	return FGenericPlatformHttp::UrlEncode(urlParameter);
}

FString UWebCommunicationBPLibrary::urlEncodePure(FString urlParameter) {
	return FGenericPlatformHttp::UrlEncode(urlParameter);
}
