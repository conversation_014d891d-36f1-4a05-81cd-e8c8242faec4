// Copyright 2017-2019 <PERSON>(<PERSON><PERSON>). All Rights Reserved.
#include "WebCommunicationRequestCompleteObject.h"


UWebCommunicationRequestCompleteObject::UWebCommunicationRequestCompleteObject(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
	this->AddToRoot();
	if (webComObjectMbitTimerThread == nullptr)
		webComObjectMbitTimerThread = new FWebComObjectMbitTimerThread(this);
}

UWebCommunicationRequestCompleteObject::~UWebCommunicationRequestCompleteObject() {
	if (webComObjectMbitTimerThread != nullptr) {
		webComObjectMbitTimerThread->stopThread();
		webComObjectMbitTimerThread = nullptr;
	}
}

void UWebCommunicationRequestCompleteObject::requestComplete(FHttpRequestPtr request, FHttpResponsePtr response, bool bWasSuccessful) {
	this->RemoveFromRoot();
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (12)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;
	if (webcom->removeHeaders) {
		webcom->additionalHeader.Empty();
	}

	EMultiStepType multistepGlobal = multiStepDownloadType;
	FString downloadIDGlobal = downloadID;

	if (!bWasSuccessful || !response.IsValid()) {
		//UE_LOG(LogTemp, Display, TEXT("Request unsuccessful, check the url"));
		TArray<FString> dataArray;
		TArray<uint8> byteArray;
		dataArray.Add("No Response");
		TArray<FString> headers;
		int32 statusCode = 500;
		//switch to gamethread
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, downloadIDGlobal, requestIDP, multistepGlobal]() {
			if (multistepGlobal == EMultiStepType::GOOGLE_INFO) {
				webcom->onhttpRequestCompleteGoogleInfoDelegate.Broadcast("",0,statusCode, downloadIDGlobal,requestIDP);
			}
			else {
				webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			}
			
		});

		//write download to file
		if (fileCancelResumeDownloadDir.IsEmpty() == false && downloadDataCopy.Num() > 0) {
			FArchive* writer = IFileManager::Get().CreateFileWriter(*fileCancelResumeDownloadDir, EFileWrite::FILEWRITE_Append);
			if (writer) {
				writer->Seek(writer->TotalSize());
				writer->Serialize(downloadDataCopy.GetData(), downloadDataCopy.Num());
				downloadDataCopy.Empty();
				writer->Close();
			}
		}
		return;
	}

	TArray<uint8> byteArray = response->GetContent();
	FString dataString = response->GetContentAsString();
	TArray<FString> dataArray;
	dataString.ParseIntoArray(dataArray, TEXT("\r\n"), true);
	TArray<FString> headers = response->GetAllHeaders();

	if (multiStepDownloadType == EMultiStepType::GOOGLE_INFO) {
		//UE_LOG(LogTemp, Warning, TEXT("UWebCommunicationBPLibrary: %s"),*dataString);

		//read fileinfos from google drive html/javascript view page
		FString left;
		FString right;
		dataString.Split("itemJson:", &left, &right);
		
		TArray<FString> lines;
		int32 lineCount = right.ParseIntoArray(lines, TEXT(","), true);

		if (lineCount > 27) {
			FString fileName = lines[1];
			fileName = fileName.Replace(TEXT("\""), TEXT(""));

			FString fileSizeString = lines[27];
			fileSizeString = fileSizeString.Replace(TEXT("\""), TEXT(""));
			fileSizeString = fileSizeString.Replace(TEXT("]"), TEXT(""));

			int32 fileSize = FCString::Atoi64(*fileSizeString);

			int32 statusCode = response->GetResponseCode();

			/*UE_LOG(LogTemp, Warning, TEXT("FileName: %s"),*fileName);
			UE_LOG(LogTemp, Warning, TEXT("FileSize: %i"),fileSize);*/

			AsyncTask(ENamedThreads::GameThread, [webcom, fileName, fileSize,statusCode, downloadIDGlobal, requestIDP, multistepGlobal]() {
				webcom->onhttpRequestCompleteGoogleInfoDelegate.Broadcast(fileName, fileSize, statusCode, downloadIDGlobal, requestIDP);
			});

			return;	
		}

		AsyncTask(ENamedThreads::GameThread, [webcom, downloadIDGlobal, requestIDP, multistepGlobal]() {
			webcom->onhttpRequestCompleteGoogleInfoDelegate.Broadcast("", 0, 452, downloadIDGlobal,requestIDP);
		});
		
		return;
	}

	//if its a resume download and the existing file is => then the file on server then empty data 
	if (fileCancelResumeDownloadDir.IsEmpty() == false) {

		//Content-Range: bytes 0-0/104857600 
		FString responseSize = response->GetHeader("Content-Range");

		if (responseSize.IsEmpty()) {
			responseSize = response->GetHeader("content-range");
		}
		else {
			if (responseSize.IsEmpty()) {
				responseSize = response->GetHeader("Content-range");
			}
			else {
				if (responseSize.IsEmpty()) {
					responseSize = response->GetHeader("content-Range");
				}
			}
		}

		if (responseSize.IsEmpty() == false) {
			FString left;
			FString right;
			responseSize.Split("/", &left, &right);
			responseSize = right;

			int32 fullSize = FCString::Atoi(*responseSize);
			//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary filesize: %i"), FPlatformFileManager::Get().GetPlatformFile().FileSize(*fileCancelResumeDownloadDir));
			if (FPlatformFileManager::Get().GetPlatformFile().FileSize(*fileCancelResumeDownloadDir) >= fullSize) {
				byteArray.Empty();
				dataArray.Empty();
			}
		}


	}



	int32 statusCode = response->GetResponseCode();



	//switch to gamethread
	AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
	});

}

void UWebCommunicationRequestCompleteObject::requestProgressUpload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived) {
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (53)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;

	if (request.IsValid()) {
		if (webCommunicationBPLibrary->toCancelRequests.Find(requestID) != nullptr) {
			AsyncTask(ENamedThreads::GameThread, [request]() {
				request->CancelRequest();
			});
			webCommunicationBPLibrary->toCancelRequests.Remove(requestID);
			UE_LOG(LogTemp, Warning, TEXT("Request has been canceled. %s"),*requestID);
			return;
		}

		float size = (float)request->GetContentLength();
		if (size <= 0.0f)
			size = 1.0f;
		float percentUpload = FMath::Clamp(((float)bytesSent / size) * 100.0f, 0.0f, 100.0f);
		float percentDownload = 0.0f;

		AsyncTask(ENamedThreads::GameThread, [webcom,size, bytesSent, percentUpload, bytesReceived, percentDownload, requestIDP]() {
			webcom->onhttpFileProgressDelegate.Broadcast(size, bytesSent, percentUpload, bytesReceived, percentDownload);
			webcom->onhttpFileUploadDelegate.Broadcast(size, bytesSent, percentUpload, requestIDP);
		});

		//UE_LOG(LogTemp, Display, TEXT("size:%f | bytesSent:%i | percentUpload:%f | bytesReceived:%i | percentDownload:%f"), size, bytesSent, percentUpload, bytesReceived, percentDownload);
	}
}

void UWebCommunicationRequestCompleteObject::requestProgressDownload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived) {
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (83)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;

	if (request.IsValid()) {
		if (webCommunicationBPLibrary->toCancelRequests.Find(requestID) != nullptr) {
			AsyncTask(ENamedThreads::GameThread, [request]() {
				request->CancelRequest();
			});
			webCommunicationBPLibrary->toCancelRequests.Remove(requestID);
			UE_LOG(LogTemp, Warning, TEXT("Request has been canceled. %s"), *requestID);
			downloadDataCopy.Append(request->GetResponse()->GetContent());
			return;
		}

		FHttpResponsePtr response = request->GetResponse();
		if (response.IsValid()) {
			bytesReceived = bytesReceived + fileCancelResumeDownloadByteStart;
			float size = (float)(response->GetContentLength() + fileCancelResumeDownloadByteStart);
			if (size <= 0.0f)size = 1.0f;
			float percentUpload = 0.0f;
			float percentDownload = FMath::Clamp(((float)bytesReceived / size) * 100.0f, 0.0f, 100.0f);
		

			//for the mbit thread
			bytesCurrent = bytesReceived;
			float mbitGlobal = mbit;

			float sizeMB = size / 1024 / 1024;
			float bytesReceivedMB = (float)bytesReceived / 1024 / 1024;

			AsyncTask(ENamedThreads::GameThread, [webcom, size, bytesSent, percentUpload, bytesReceived, percentDownload, requestIDP, mbitGlobal, sizeMB, bytesReceivedMB]() {
				webcom->onhttpFileProgressDelegate.Broadcast(size, bytesSent, percentUpload, bytesReceived, percentDownload);
				webcom->onhttpFileDownloadDelegate.Broadcast(sizeMB, bytesReceivedMB, percentDownload, mbitGlobal, requestIDP);
			});

			//UE_LOG(LogTemp, Display, TEXT("size:%f | bytesSent:%i | percentUpload:%f | bytesReceived:%i | percentDownload:%f"), size, bytesSent, percentUpload, bytesReceived, percentDownload);
		}
	}
}

void UWebCommunicationRequestCompleteObject::requestCompleteLowRam(FHttpRequestPtr request, FHttpResponsePtr response, bool bWasSuccessful) {
	this->RemoveFromRoot();
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (12)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;
	if (webcom->removeHeaders) {
		webcom->additionalHeader.Empty();
	}


	if (!bWasSuccessful || !response.IsValid()) {
		//UE_LOG(LogTemp, Display, TEXT("Request unsuccessful, check the url"));
		TArray<FString> dataArray;
		TArray<uint8> byteArray;
		dataArray.Add("No Response");
		int32 statusCode = 500;
		TArray<FString> headers;
		//switch to gamethread
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
			webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			});
		return;
	}

	//
	//for (int32 i = 0; i < response->GetAllHeaders().Num(); i++)
	//{
	//	UE_LOG(LogTemp, Display, TEXT("REsponsehead: %s "), *response->GetAllHeaders()[i]);
	//}

	//Content-Range: bytes 0-0/104857600 

	FString responseSize = response->GetHeader("Content-Range");

	if (responseSize.IsEmpty()) {
		responseSize = response->GetHeader("content-range");
	}
	else {
		if (responseSize.IsEmpty()) {
			responseSize = response->GetHeader("Content-range");
		}
		else {
			if (responseSize.IsEmpty()) {
				responseSize = response->GetHeader("content-Range");
			}
		}
	}



	TArray<uint8> byteArray;
	TArray<FString> dataArray;
	int32 statusCode = response->GetResponseCode();
	TArray<FString> headers;
	if (responseSize.IsEmpty()) {
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
			webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			});
		UE_LOG(LogTemp, Error, TEXT("File Download: Server does not suport download range or wrong url. Cancel Download"));
		return;
	}

	FString left;
	FString right;
	responseSize.Split("/", &left, &right);
	responseSize = right;

	int32 fullSize = FCString::Atoi(*responseSize);

	if (responseSize.IsEmpty() || fullSize < 1 || fileCancelResumeDownloadBytePartSize < 1) {
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
			webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			});
		UE_LOG(LogTemp, Error, TEXT("File Download: Server does not suport download range or wrong url. Cancel Download"));
		return;
	}

	if (FPlatformFileManager::Get().GetPlatformFile().FileSize(*fileCancelResumeDownloadDir) >= fullSize) {
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
			webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			});
		UE_LOG(LogTemp, Error, TEXT("File Download: File size equal to or larger than on the server. Cancel Download"));
		return;
	}


	if (fileCancelResumeDownloadByteFullSize == 0) {

		if (fileCancelResumeDownloadBytePartSize > fullSize)
			fullSize = fileCancelResumeDownloadBytePartSize;

		TArray<struct FhttpRequest> httpRequestsBack;
		FString requestIDBack;
		TArray<struct FhttpRequest> otherHttpRequests;
		webcom->CreateHttpRequestGETLowRamDownload(httpRequestsBack, requestIDBack, otherHttpRequests, request->GetURL(), EHTTPWebComFileDownloadResumeType::E_RESUME, EHTTPWebComFileUpload::E_ad, fileCancelResumeDownloadDir, (fileCancelResumeDownloadBytePartSize), requestID);

		if (httpRequestsBack.Num() > 0) {
			FhttpRequest createdRequest = httpRequestsBack.Last();
			createdRequest.request->setFileCancelResumeDownloadByteFullSize(fullSize);
			webcom->executeHttpRequests(httpRequestsBack, webcom);
		}
		else {
			AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
				webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
				});
			UE_LOG(LogTemp, Error, TEXT("Something's went wrong. download is aborted "));
		}
		return;
	}



	FArchive* writer = IFileManager::Get().CreateFileWriter(*fileCancelResumeDownloadDir, EFileWrite::FILEWRITE_Append);
	if (!writer) {
		UE_LOG(LogTemp, Error, TEXT("File Download: Can't write into %s "), *fileCancelResumeDownloadDir);
	}
	else {
		if (response.IsValid()) {

			TArray<uint8> data = response->GetContent();
			bool finish = false;
			if ((writer->TotalSize() + data.Num()) >= fullSize)
				finish = true;

			writer->Seek(writer->TotalSize());
			writer->Serialize(data.GetData(), data.Num());

			if (!finish) {
				TArray<struct FhttpRequest> httpRequestsBack;
				FString requestIDBack;
				TArray<struct FhttpRequest> otherHttpRequests;

				/*if ((writer->TotalSize() + fileCancelResumeDownloadBytePartSize) > fileCancelResumeDownloadByteFullSize) {
					fileCancelResumeDownloadBytePartSize = fileCancelResumeDownloadByteFullSize - writer->TotalSize();
				}*/


				webcom->CreateHttpRequestGETLowRamDownload(httpRequestsBack, requestIDBack, otherHttpRequests, request->GetURL(), EHTTPWebComFileDownloadResumeType::E_RESUME, EHTTPWebComFileUpload::E_ad, fileCancelResumeDownloadDir, (fileCancelResumeDownloadBytePartSize), requestID);

				if (httpRequestsBack.Num() > 0) {
					FhttpRequest createdRequest = httpRequestsBack.Last();
					//httpRequestsBack.RemoveAt(httpRequestsBack.Num()-1);
					createdRequest.request->setFileCancelResumeDownloadByteFullSize(fileCancelResumeDownloadByteFullSize);
					createdRequest.request->setFileCancelResumeDownloadByteStart(fileCancelResumeDownloadByteStart + data.Num());
					createdRequest.request->setMbit(mbit);
					//httpRequestsBack.Add(createdRequest);

					webcom->executeHttpRequests(httpRequestsBack, webcom);
				}
				else {
					UE_LOG(LogTemp, Error, TEXT("Something's went wrong. download is aborted "));
				}
			}
			else {
				//switch to gamethread
				AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
					webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
				});
			}


			//UE_LOG(LogTemp, Display, TEXT("Cancel1: %i | %i"), fileCancelResumeDownloadByteStart+data.Num(), writer->TotalSize());

			data.Empty();

		}
		/*	else {
				UE_LOG(LogTemp, Display, TEXT("Cancel3: Request successful"));
			}*/
		writer->Close();

		return;
	}


}
	


void UWebCommunicationRequestCompleteObject::requestProgressDownloadLowRam(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived){
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (127)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;

	if (request.IsValid()) {
		if (webCommunicationBPLibrary->toCancelRequests.Find(requestID) != nullptr) {
			AsyncTask(ENamedThreads::GameThread, [request]() {
				request->CancelRequest();
			});
			webCommunicationBPLibrary->toCancelRequests.Remove(requestID);
			UE_LOG(LogTemp, Warning, TEXT("Request has been canceled. %s"), *requestID);
			return;
		}

		FHttpResponsePtr response = request->GetResponse();
	

		
		if (response.IsValid()) {
			float size = (float)fileCancelResumeDownloadByteFullSize;
			if (size <= 0)
				size = 1;
			float percentUpload = 0;
			bytesReceived = bytesReceived + fileCancelResumeDownloadByteStart;
			float percentDownload = ((float)bytesReceived / size) * 100;

			//for the mbit thread
			bytesCurrent = bytesReceived;
			float mbitGlobal = mbit;

			float sizeMB = size / 1024 / 1024;
			float bytesReceivedMB = (float)bytesReceived / 1024 / 1024;
					
			AsyncTask(ENamedThreads::GameThread, [webcom, size, bytesSent, percentUpload, bytesReceived, percentDownload, requestIDP, mbitGlobal, sizeMB, bytesReceivedMB]() {
				webcom->onhttpFileProgressDelegate.Broadcast(size, bytesSent, percentUpload, bytesReceived, percentDownload);
				webcom->onhttpFileDownloadDelegate.Broadcast(sizeMB, bytesReceivedMB, percentDownload, mbitGlobal, requestIDP);
			});

		}
	}
}

void UWebCommunicationRequestCompleteObject::requestMultiStepComplete(FHttpRequestPtr Request, FHttpResponsePtr response, bool bWasSuccessful){
	this->RemoveFromRoot();
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (12)."));
		return;
	}

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;
	if (webcom->removeHeaders) {
		webcom->additionalHeader.Empty();
	}

	if (!bWasSuccessful || !response.IsValid()) {
		//UE_LOG(LogTemp, Display, TEXT("Request unsuccessful, check the url"));
		TArray<FString> dataArray;
		TArray<uint8> byteArray;
		dataArray.Add("No Response");
		int32 statusCode = 500;
		TArray<FString> headers;
		//switch to gamethread
		AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
			webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
		});
		return;
	}

	TArray<uint8> byteArray = response->GetContent();
	FString dataString = response->GetContentAsString();
	TArray<FString> dataArray;
	dataString.ParseIntoArray(dataArray, TEXT("\r\n"), true);

	int32 statusCode = response->GetResponseCode();

	TArray<FString> headers = response->GetAllHeaders();

	//gooledrive has a virus check for big files. read the confirm code from the html code and start a regluar get download request
	if (multiStepDownload == 1) {
		dataString = dataArray.Last();
		
		//UE_LOG(LogTemp, Error, TEXT("filesize: %s"),*sizeStr);

		FString newUrl = generateURLFromHTML(dataString, Request.Get()->GetURL());

		//no virus message
		if (newUrl.EndsWith("confirm=")) {
			AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
				webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
			});
			return;
		}
		

		TArray<struct FhttpRequest> otherHttpRequests;
		TArray<struct FhttpRequest> httpRequests;
	
		UWebCommunicationRequestCompleteObject* requestObject = NewObject<UWebCommunicationRequestCompleteObject>(UWebCommunicationRequestCompleteObject::StaticClass());
		requestObject->setRequestID(requestID, UWebCommunicationBPLibrary::getWebCommunicationTarget());
		requestObject->setDownloadID(downloadID);
		requestObject->setMultiStepDownload(2);
		

		FhttpRequest httpRequest;
		httpRequest.requestType = EHTTPWebComRequestType::GET;
		httpRequest.url = newUrl;
		requestObject->setHTMLFileSize(HTMLFileSize);
		httpRequest.request = requestObject;

		otherHttpRequests.Add(httpRequest);
		httpRequests = otherHttpRequests;
	
		webCommunicationBPLibrary->executeHttpRequests(httpRequests, webCommunicationBPLibrary);
		return;
	}

	/*if (multiStepDownload == 2) {
		UE_LOG(LogTemp, Error, TEXT("google drive download finish: %i"), byteArray.Num());
	}*/

	//switch to gamethread
	AsyncTask(ENamedThreads::GameThread, [webcom, dataArray, headers, statusCode, byteArray, requestIDP]() {
		webcom->onhttpRequestCompleteDelegate.Broadcast(dataArray, headers, statusCode, byteArray, requestIDP);
	});
}

void UWebCommunicationRequestCompleteObject::requestMultiStepDownload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived){
	if (webCommunicationBPLibrary == nullptr) {
		UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (83)."));
		return;
	}

	if (multiStepDownload != 2 && HTMLFileSize <= 0)
		return;

	//UE_LOG(LogTemp, Display, TEXT("UWebCommunicationBPLibrary: RequestID %s"), *requestID);

	UWebCommunicationBPLibrary* webcom = webCommunicationBPLibrary;
	FString requestIDP = requestID;

	if (request.IsValid()) {
		if (webCommunicationBPLibrary->toCancelRequests.Find(requestID) != nullptr) {
			AsyncTask(ENamedThreads::GameThread, [request]() {
				request->CancelRequest();
			});
			UE_LOG(LogTemp, Warning, TEXT("Request has been canceled. %s"), *requestID);
			return;
		}

		FHttpResponsePtr response = request->GetResponse();
		/*UE_LOG(LogTemp, Warning, TEXT("bytesReceived: %i"), response->GetContentLength());
		UE_LOG(LogTemp, Warning, TEXT("GetContent: %i"), response->GetContent().Num());
		UE_LOG(LogTemp, Warning, TEXT("x3x: %i"), (int32)HTMLFileSize);*/

		if (response.IsValid()) {
			float size = (HTMLFileSize == 0) ? response->GetContentLength() : (float)HTMLFileSize;
			if (size <= 0)
				size = 1;
			float percentUpload = 0;
			float percentDownload = ((float)bytesReceived / size) * 100;
			if (percentDownload > 100) {
				percentDownload = 100;
				//multiStepDownload = 0;
			}

			//for the mbit thread
			bytesCurrent = bytesReceived;

			float mbitGlobal = mbit;
			float sizeMB = size / 1024 / 1024;
			float bytesReceivedMB = (float)bytesReceived / 1024 / 1024;

			AsyncTask(ENamedThreads::GameThread, [webcom, size, bytesSent, percentUpload, bytesReceived, percentDownload, requestIDP, mbitGlobal, sizeMB, bytesReceivedMB]() {
				webcom->onhttpFileProgressDelegate.Broadcast(size, bytesSent, percentUpload, bytesReceived, percentDownload);
				webcom->onhttpFileDownloadDelegate.Broadcast(sizeMB, bytesReceivedMB, percentDownload, mbitGlobal, requestIDP);
			});

			//UE_LOG(LogTemp, Display, TEXT("size:%f | bytesSent:%i | percentUpload:%f | bytesReceived:%i | percentDownload:%f"), size, bytesSent, percentUpload, bytesReceived, percentDownload);
		}
	}
}

//void UWebCommunicationRequestCompleteObject::setActionIfFileExists(EHTTPWebComFileDownloadResumeType a){
//	actionIfFileExists = a;
//}
//
//EHTTPWebComFileDownloadResumeType UWebCommunicationRequestCompleteObject::getActionIfFileExists(){
//	return actionIfFileExists;
//}

void UWebCommunicationRequestCompleteObject::setRequestID(FString requestIDP, UWebCommunicationBPLibrary* webCommunicationBPLibraryP) {
	requestID = requestIDP;
	webCommunicationBPLibrary = webCommunicationBPLibraryP;
}

FString UWebCommunicationRequestCompleteObject::getRequestID() {
	return requestID;
}

void UWebCommunicationRequestCompleteObject::setDownloadID(FString downloadIDP){
	downloadID = downloadIDP;
}

FString UWebCommunicationRequestCompleteObject::getDownloadID(){
	return downloadID;
}

void UWebCommunicationRequestCompleteObject::setMultiStepDownload(int32 stepP) {
	multiStepDownload = stepP;
	//UE_LOG(LogTemp, Warning, TEXT("UWebCommunicationBPLibrary: %i."), multiStepDownload);
}

int32 UWebCommunicationRequestCompleteObject::getMultiStepDownload() {
	return multiStepDownload;
}

void UWebCommunicationRequestCompleteObject::setMultiStepDownloadType(EMultiStepType multiStepDownloadTypeP){
	multiStepDownloadType = multiStepDownloadTypeP;
}

EMultiStepType UWebCommunicationRequestCompleteObject::getMultiStepDownloadType(){
	return multiStepDownloadType;
}

FString UWebCommunicationRequestCompleteObject::generateURLFromHTML(FString html, FString oldURL){
	FString newURL;
	if (multiStepDownloadType == EMultiStepType::GOOGLE) {
		//get confirm id from html
		FString left;
		FString right;
		html.Split(";confirm=", &left, &right);
		right.Split("&", &left, &right);
		FString linkParam = "download&confirm=" + left + "&";
		newURL = oldURL + "&confirm=" + left;//oldURL.Replace(TEXT("download&"), *linkParam);

		//get file size from html
		if (HTMLFileSize == 0) {
			html.Split("uc-name-size", &left, &right);
			right.Split("</span>", &left, &right);
			FString sizeStr = left;
			FString left2;
			FString right2;
			sizeStr.Split("</a>", &left2, &right2, ESearchCase::CaseSensitive, ESearchDir::FromEnd);
			sizeStr = right2.Replace(TEXT("("), TEXT("")).Replace(TEXT(")"), TEXT("")).Replace(TEXT(" "), TEXT("")).Replace(TEXT(","), TEXT("."));
			if (sizeStr.Contains("M")) {
				sizeStr = sizeStr.Replace(TEXT("M"), TEXT(""));
				HTMLFileSize = FCString::Atof(*sizeStr) * 1024 * 1024;
			}
			else {
				sizeStr = sizeStr.Replace(TEXT("G"), TEXT(""));
				HTMLFileSize = ((double)FCString::Atof(*sizeStr)) * 1024 * 1024 * 1024;
			}
		}
		return newURL;
	}


	if (multiStepDownloadType == EMultiStepType::ANONFILE) {
		//get confirm id from html
		FString left;
		FString right;
		html.Split("id=\"download-url\"", &left, &right);
		right.Split("</a>", &left, &right);
		FString left2;
		FString right2;
		left.Split("href=", &left2, &right2);
		right2.Split("><", &newURL, &right);
		newURL = newURL.Replace(TEXT("\""), TEXT(""));
	}

	return newURL;
}

void UWebCommunicationRequestCompleteObject::setFileCancelResumeDownloadByteFullSize(int32 size) {
	fileCancelResumeDownloadByteFullSize = size;
}

int32 UWebCommunicationRequestCompleteObject::getFileCancelResumeDownloadByteFullSize() {
	return fileCancelResumeDownloadByteFullSize;
}

void UWebCommunicationRequestCompleteObject::setFileCancelResumeDownloadBytePartSize(int32 size){
	fileCancelResumeDownloadBytePartSize = size;
}

int32 UWebCommunicationRequestCompleteObject::getFileCancelResumeDownloadBytePartSize(){
	return fileCancelResumeDownloadBytePartSize;
}

void UWebCommunicationRequestCompleteObject::setFileCancelResumeDownloadByteStart(int32 size){
	if (size < 0) {
		fileCancelResumeDownloadByteStart = 0;
		return;
	}
	fileCancelResumeDownloadByteStart = size;
}

int32 UWebCommunicationRequestCompleteObject::getFileCancelResumeDownloadByteStart(){
	return fileCancelResumeDownloadByteStart;
}

void UWebCommunicationRequestCompleteObject::setFileCancelResumeDownloadDir(FString dir){
	fileCancelResumeDownloadDir = dir;
}

FString UWebCommunicationRequestCompleteObject::getFileCancelResumeDownloadDir(){
	return fileCancelResumeDownloadDir;
}

int32 UWebCommunicationRequestCompleteObject::getBytesCurrent(){
	return bytesCurrent;
}

int32 UWebCommunicationRequestCompleteObject::getBytesLast(){
	return bytesLast;
}

void UWebCommunicationRequestCompleteObject::setBytesLast(int32 l){
	bytesLast = l;
}

void UWebCommunicationRequestCompleteObject::setMbit(float m){
	mbit = m;
}

//void UWebCommunicationRequestCompleteObject::setWebComHttpRequest(FhttpRequest httpRequestP){
//	httpRequest = httpRequestP;
//}

void UWebCommunicationRequestCompleteObject::setHTMLFileSize(int32 size) {
	HTMLFileSize = size;
}

int32 UWebCommunicationRequestCompleteObject::getHTMLFileSize() {
	return HTMLFileSize;
}