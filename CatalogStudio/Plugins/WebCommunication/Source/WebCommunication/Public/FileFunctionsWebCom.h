// Copyright 2017-2019 <PERSON> (<PERSON><PERSON>). All Rights Reserved.
#pragma once

#include "WebCommunication.h"
#include "FileFunctionsWebCom.generated.h"


UENUM(BlueprintType)
enum class EFileFunctionsWebComDirectoryType : uint8
{
	E_gd	UMETA(DisplayName = "Game directory"),
	E_ad 	UMETA(DisplayName = "Absolute directory")
};


UCLASS(Blueprintable, BlueprintType)
class UFileFunctionsWebCom : public UObject
{
	GENERATED_UCLASS_BODY()

public:


	//APEND bytes in eine Datei muss vorhenden sein fuer webcom plugin. vielleicht auch eine node zum aufteilen einer datei

	static FString getCleanDirectory(EFileFunctionsWebComDirectoryType directoryType, FString filePath);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void writeBytesToFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, TArray<uint8> bytes, bool& success);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void addBytesToFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, TArray<uint8> bytes, bool& success);
	//UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
	//	static void splittFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, int32 parts, bool& success);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static TArray<uint8> readBytesFromFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, bool &success);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void readStringFromFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, bool& success, FString& data);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void writeStringToFile(EFileFunctionsWebComDirectoryType directoryType, FString data, FString filePath, bool& success);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void getMD5FromFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath, bool& success, FString& MD5);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void bytesToBase64String(TArray<uint8> bytes, FString& base64String);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static TArray<uint8> base64StringToBytes(EFileFunctionsWebComDirectoryType directoryType, FString base64String, bool& success);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void fileToBase64String(EFileFunctionsWebComDirectoryType directoryType, FString filePath, bool& success, FString& base64String, FString& fileName);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool fileExists(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool directoryExists(EFileFunctionsWebComDirectoryType directoryType, FString path);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static int32 fileSize(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool deleteFile(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** Delete a directory and return true if the directory was deleted or otherwise does not exist. **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool deleteDirectory(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** Return true if the file is read only. **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool isReadOnly(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** Attempt to move a file. Return true if successful. Will not overwrite existing files. **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool moveFile(EFileFunctionsWebComDirectoryType directoryTypeTo, FString filePathTo, EFileFunctionsWebComDirectoryType directoryTypeFrom, FString filePathFrom);
	/** Attempt to change the read only status of a file. Return true if successful. **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool setReadOnly(EFileFunctionsWebComDirectoryType directoryType, FString filePath, bool bNewReadOnlyValue);
	/** Return the modification time of a file. Returns FDateTime::MinValue() on failure **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static FDateTime getTimeStamp(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** Sets the modification time of a file **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static void	setTimeStamp(EFileFunctionsWebComDirectoryType directoryType, FString filePath, FDateTime DateTime);
	/** Return the last access time of a file. Returns FDateTime::MinValue() on failure **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static FDateTime getAccessTimeStamp(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** For case insensitive filesystems, returns the full path of the file with the same case as in the filesystem */
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static FString getFilenameOnDisk(EFileFunctionsWebComDirectoryType directoryType, FString filePath);
	/** Create a directory and return true if the directory was created or already existed. **/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File")
		static bool createDirectory(EFileFunctionsWebComDirectoryType directoryType, FString path);

private:


};

