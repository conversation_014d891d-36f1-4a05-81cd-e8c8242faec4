// Copyright 2017-2019 <PERSON>(<PERSON><PERSON>). All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Runtime/Core/Public/Serialization/BufferArchive.h"
#include "Runtime/Online/HTTP/Public/Http.h"
#include "Runtime/Online/HTTP/Public/GenericPlatform/GenericPlatformHttp.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Runtime/Core/Public/Async/Async.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "Runtime/Core/Public/HAL/FileManager.h"
#include "Runtime/Core/Public/Misc/Base64.h"
#include "Runtime/Core/Public/Misc/SecureHash.h"
#include "Modules/ModuleManager.h"


#ifndef __FileFunctionsWebCom
#define __FileFunctionsWebCom
#include "FileFunctionsWebCom.h"
#endif

#ifndef __WebComCompleteObject
#define __WebComCompleteObject
#include "WebCommunicationRequestCompleteObject.h"
#endif

#ifndef __WebComLib
#define __WebComLib
#include "WebCommunicationBPLibrary.h"
#endif

class FWebCommunicationModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;
};