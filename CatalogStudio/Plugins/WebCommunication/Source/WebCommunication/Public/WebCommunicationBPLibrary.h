// Copyright 2017-2019 <PERSON>(<PERSON><PERSON>). All Rights Reserved.

#pragma once

#include "WebCommunication.h"
#include "WebCommunicationBPLibrary.generated.h"

class UWebCommunicationRequestCompleteObject;

UENUM(BlueprintType)
enum class EHTTPWebComFileUpload : uint8
{
	E_gd	UMETA(DisplayName = "Game directory"),
	E_ad 	UMETA(DisplayName = "Absolute directory")
};

UENUM(BlueprintType)
enum class EHTTPWebComRequestType : uint8
{
	GET 				UMETA(DisplayName = "GET"),
	GETLowRamDownload 	UMETA(DisplayName = "GETLowRamDownload"),
	PUT					UMETA(DisplayName = "PUT"),
	POST 				UMETA(DisplayName = "POST"),
	POST_UPLOAD 		UMETA(DisplayName = "POST_UPLOAD"),
	INDIVIDUAL 			UMETA(DisplayName = "INDIVIDUAL"),
	POSTDownload		UMETA(DisplayName = "POST_DOWNLOAD"),
	STRING_INDIVIDUAL 	UMETA(DisplayName = "StringIndividual")
};

UENUM(BlueprintType)
enum class EHTTPWebComFileUploadType : uint8
{
	E_fut_put	UMETA(DisplayName = "PUT"),
	E_fut_post 	UMETA(DisplayName = "POST")
};

UENUM(BlueprintType)
enum class EHTTPWebComFileDownloadResumeType : uint8
{
	E_OVERWRITE		UMETA(DisplayName = "Overwrite"),
	E_NOT_OVERWRITE UMETA(DisplayName = "Cancel Download"),
	E_RESUME		UMETA(DisplayName = "Resume")
};

UENUM(BlueprintType)
enum class EHTTPWebComFileBytesToFileActionType : uint8
{
	E_OVERWRITE		UMETA(DisplayName = "Overwrite"),
	E_NOT_OVERWRITE UMETA(DisplayName = "Cancel Download")
};

USTRUCT(BlueprintType)
struct FhttpRequest
{
	GENERATED_USTRUCT_BODY()

	FString	url;
	UWebCommunicationRequestCompleteObject* request;
	TMap<FString, FString> POSTData;
	EHTTPWebComRequestType requestType;

	EHTTPWebComFileUpload DirectoryType;
	FString filePath;
	FString fileID;

	TMap<FString, FString> header;
	FString verb;
	FString content;
	TArray<uint8> ContentBytes;

	bool bAddContentLength;
};


UCLASS()
class UWebCommunicationBPLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

public:
	static UWebCommunicationBPLibrary* webcom;

	~UWebCommunicationBPLibrary();

	//Delegates
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FiveParams(FhttpRequestCompleteDelegate, const TArray<FString>&, data, const TArray<FString>&, headers, const int32, statusCode, const TArray<uint8>&, byteData, const FString, requestID);
	UFUNCTION()
		void httpRequestCompleteDelegate(const TArray<FString>& data, const TArray<FString>& headers, const int32 statusCode, const TArray<uint8>& byteData, const FString requestID);
	UPROPERTY(BlueprintAssignable, Category = "WebCommunication|Events|RequestComplete")
		FhttpRequestCompleteDelegate onhttpRequestCompleteDelegate;

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FiveParams(FhttpRequestCompleteGoogleInfoDelegate, const FString, fileName, const int32, fileSizeInBytes, const int32, statusCode, const FString, downloadID, const FString, requestID);
	UFUNCTION()
		void httpRequestCompleteGoogleInfoDelegate(const FString fileName, const int32 fileSizeInBytes, const int32 statusCode, const FString downloadID, const FString requestID);
	UPROPERTY(BlueprintAssignable, Category = "WebCommunication|Events|RequestCompleteGoogleInfo")
		FhttpRequestCompleteGoogleInfoDelegate onhttpRequestCompleteGoogleInfoDelegate;

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FiveParams(FhttpFileProgressDelegate, const float, size, const uint64, bytesSent, const float, percentUpload, const uint64, bytesReceived, const float, percentDownload);
	/**
	* This function is deprecated and will be removed. Please use httpFileDownload or httpFileUpload.
	*/
	UFUNCTION()
		void httpFileProgressDelegate(const float size, const uint64 bytesSent, const float percentUpload, const uint64 bytesReceived, const float percentDownload);
	UPROPERTY(BlueprintAssignable, Category = "WebCommunication|Deprecated|Events|FileProgress")
		FhttpFileProgressDelegate onhttpFileProgressDelegate;


	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FiveParams(FhttpFileDownloadDelegate, const float, size, const float, megaBytesReceived, const float, percentDownload, const float, megaBit, const FString, requestID);
	/**
	* @param size Filesize in Megabyte
	* @param megaBit Downloadspeed in Megabit/second
	*/
	UFUNCTION()
		void httpFileDownloadDelegate(const float size, const float megaBytesReceived, const float percentDownload, const float megaBit, const FString requestID);
	UPROPERTY(BlueprintAssignable, Category = "WebCommunication|Events|Download")
		FhttpFileDownloadDelegate onhttpFileDownloadDelegate;

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FhttpFileUploadDelegate, const float, size, const int32, bytesSent, const float, percentUpload, const FString, requestID);
	/**
	* @param size Filesize in Byte
	*/
	UFUNCTION()
		void httpFileUploadDelegate(const float size, const int32 bytesSent, const float percentUpload, const FString requestID);
	UPROPERTY(BlueprintAssignable, Category = "WebCommunication|Events|Upload")
		FhttpFileUploadDelegate onhttpFileUploadDelegate;


	/**
	* This function is deprecated and will be removed. Please use getWebCommunicationTarget. 
	*/
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "WebCommunication|Deprecated")
		static UWebCommunicationBPLibrary* getTarget();

	/**
	* Return a Target for the Events
	*/
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "WebCommunication")
		static UWebCommunicationBPLibrary* getWebCommunicationTarget();


	/**
	* This function is deprecated and will be removed. Please use httpRequestGET.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static UWebCommunicationBPLibrary* httpRequestGET(FString url, FString optionalRequestID, FString &requestID);

	/**
	* Send a GET HTTP Request to Webserver.
	* @param optionalRequestID Your own RequestID. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	* @param requestID It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestGET(TArray<struct FhttpRequest> otherHttpRequests, FString url, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID);

	/**
	* For downloads. Data is copied to the hard disk if you cancel the download.
	* @param ActionIfFileExists Overwrite = overwrites an existing file otherwise a new one will be created. Cancel Download = If the file already exists, the download will be aborted. Resume = Download continues. If the file is missing, the download is started from the beginning.
	* @param FileSizeStepsInBytes Default = 10 megabytes. Steps in which the download is aborted and restarted. Specified in bytes. You can use the node "MegabyteToByte" for conversion.
	* @param optionalRequestID Your own RequestID. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	* @param requestID It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET|Download", meta = (AutoCreateRefTerm = "otherHttpRequests"))
	static void CreateHttpRequestGETDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType ActionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePathWithFileName, const TMap<FString, FString>& InHeaders, FString optionalRequestID = "");

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET|Download", meta = (AutoCreateRefTerm = "otherHttpRequests"))
	static void CreateHttpRequestPOSTDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType ActionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePathWithFileName, const TMap<FString, FString>& InHeaders, FString optionalRequestID = "");

	/**
	* For downloads. The file is downloaded in steps. This significantly reduces RAM consumption. The web server must support resuming aborted downloads.
	* @param ActionIfFileExists Overwrite = overwrites an existing file otherwise a new one will be created. Cancel Download = If the file already exists, the download will be aborted. Resume = Download continues. If the file is missing, the download is started from the beginning.
	* @param FileSizeStepsInBytes Default = 10 megabytes. Steps in which the download is aborted and restarted. Specified in bytes. You can use the node "MegabyteToByte" for conversion. 
	* @param optionalRequestID Your own RequestID. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	* @param requestID It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET|Download", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestGETLowRamDownload(TArray<struct FhttpRequest>& httpRequests, FString& requestID, TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileDownloadResumeType ActionIfFileExists, EHTTPWebComFileUpload DirectoryType, FString filePathWithFileName, int32 FileSizeStepsInBytes = 10485760, FString optionalRequestID ="");

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestGoogleDrive(TArray<struct FhttpRequest> otherHttpRequests, FString downloadID, FString optionalRequestID, int32 optionalFileSizeInByte, TArray<struct FhttpRequest> &httpRequests, FString &requestID);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestGoogleDriveFileInfo(TArray<struct FhttpRequest> otherHttpRequests, FString downloadID, FString optionalRequestID, TArray<struct FhttpRequest>& httpRequests, FString& requestID);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|GET", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestAnonfiles(TArray<struct FhttpRequest> otherHttpRequests, FString url, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication")
		static void executeHttpRequests(TArray<struct FhttpRequest> httpRequests, UWebCommunicationBPLibrary* &WebCommunicationTarget);

	
	/**
	* This function is deprecated and will be removed. Please use httpRequestPOST.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static UWebCommunicationBPLibrary* httpRequestPOST(FString url, const TArray<FString> POSTData, FString optionalRequestID, FString &requestID);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|POST", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestPOST(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> POSTData, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID);


	/**
	* Send a POST HTTP Request to Webserver.
	* @param url
	* @param POSTData
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	/*UFUNCTION(BlueprintCallable, Category = "WebCommunication|POST")
		static void httpRequestPOST(FString url, TMap<FString,FString> POSTData, FString optionalRequestID, UWebCommunicationBPLibrary* &WebCommunicationTarget, FString &requestID);*/

	/**
	* Send multiple POST HTTP Requests to Webserver.
	* @param url
	* @param POSTData
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	/*UFUNCTION(BlueprintCallable, Category = "WebCommunication|POST")
		static void httpRequestsPOST(TArray<FString> url, TArray<struct FhttpPOSTParams> POSTData, TArray<FString> optionalRequestIDs, UWebCommunicationBPLibrary* &WebCommunicationTarget, TArray<FString> &requestIDs);*/
	

	/**
	* Individual HTTP Request. You have to set all parameters yourself.
	*
	* @param url Server URL
	* @param header User-Agent, Content-Type
	* @param verb POST, GET, PUT, PATCH, DELETE
	* @param content Request Body
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static UWebCommunicationBPLibrary* httpRequestIndividual(FString url, TMap<FString, FString> header, FString verb, FString content, FString requestID);

	/**
	* Individual HTTP Request. You have to set all parameters yourself.
	*
	* @param url Server URL
	* @param header User-Agent, Content-Type
	* @param verb POST, GET, PUT, PATCH, DELETE
	* @param content Request Body
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	* @param addContentLengthHeader add Content-Length header with the length of the content.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Individual", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestIndividual(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> header, FString verb, const TArray<uint8>& Content, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID, bool addContentLengthHeader = true);

	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Individual", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateStringHttpRequestIndividual(TArray<struct FhttpRequest> otherHttpRequests, FString url, TMap<FString, FString> header, FString verb, const FString& Content, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID, bool addContentLengthHeader = true);
	/**
	* Send a Fila to a Webserver.
	*
	* @param url Server URL
	* @param DirectoryType Absolute or Relative
	* @param id name equivalent to <input type="file" name="myFile"> from an html upload
	* @param filePath File with path
	* @param uploadType Upload progress is only refreshed with type PUT
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static void httpRequestFileUpload(FString url, EHTTPWebComFileUpload DirectoryType, FString id, FString filePath, EHTTPWebComFileUploadType uploadType, FString optionalRequestID, FString &requestID);

	/**
	* Send a Fila to a Webserver.
	*
	* @param otherHttpRequests  
	* @param url Server URL
	* @param DirectoryType Absolute or Relative
	* @param filePath File with path
	* @param fileID name equivalent to <input type="file" name="myFile"> from an html upload
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File Upload", meta = (AutoCreateRefTerm = "otherHttpRequests,POSTData"))
		static void CreateHttpRequestFileUploadPOST(TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileUpload DirectoryType, FString filePath, FString fileID, TMap<FString, FString> POSTData, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID);

	/**
	* Send a Fila to a Webserver.
	*
	* @param otherHttpRequests
	* @param url Server URL
	* @param DirectoryType Absolute or Relative
	* @param filePath File with path
	* @param fileID name equivalent to <input type="file" name="myFile"> from an html upload
	* @param requestID Optional parameter. It will be output again in the httpRequestComplete event. This allows the request being assigned to the response.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|File Upload", meta = (AutoCreateRefTerm = "otherHttpRequests"))
		static void CreateHttpRequestFileUploadPUT(TArray<struct FhttpRequest> otherHttpRequests, FString url, EHTTPWebComFileUpload DirectoryType, FString filePath, FString fileID, FString optionalRequestID, TArray<struct FhttpRequest> &httpRequests, FString &requestID);
	

	/**
	* This function is deprecated and will be removed. Please use httpRequestPOST.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static TArray<FString> createAndAppendPOSTData(FString id, FString value, TArray<FString> POSTData);

	/**
	* This function is deprecated and will be removed. Please use httpRequestPOST.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Deprecated")
		static TArray<FString> createPOSTData(FString id, FString value);

	/*New headers are added. Overwritten existing headers. Remain in memory until the game is restarted or after request if you want.*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication|Header")
		static void addHTTPRequestHeader(FString id, FString value, bool removeAfterHTTPRequest);

	/**
	* Create a File from bytes.
	*
	* @param byteData from httpRequestCompleteDelegate Event
	* @param DirectoryType Absolute or Relative
	* @param filePath path with filename c:\myFile.zip or content\myFile.zip
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication")
		static void byteDataToFile(EHTTPWebComFileBytesToFileActionType fileAction,TArray<uint8> byteData, EHTTPWebComFileUpload DirectoryType, FString filePath);

	/**
	* Returns a percent-encoded version of the passed in string. Do not use with strings with http::// but for the parameters.
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication")
		static WEBCOMMUNICATION_API FString urlEncode(FString urlParameter);

	/**
	* Returns a percent-encoded version of the passed in string. Do not use with strings with http::// but for the parameters.
	*/
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "WebCommunication")
		static FString urlEncodePure(FString urlParameter);


	/**
	* Cancel Request (Upload/Download) 
	*/
	UFUNCTION(BlueprintCallable, Category = "WebCommunication")
		void cancelRequest(FString requestID);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "WebCommunication")
		static int32 megabyteToByte(int32 mb);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "WebCommunication")
		static float byteToMegabyte(int32 byte);



	//UFUNCTION(BlueprintCallable, Category = "WebCommunication|Header")
	//	static TArray<uint8> xxxTest();


	//void requestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful);
	/*void requestProgressUpload(FHttpRequestPtr Request, int32 BytesSent, int32 BytesReceived);
	void requestProgressDownload(FHttpRequestPtr Request, int32 BytesSent, int32 BytesReceived);*/

	TMap<FString, FString> additionalHeader;
	bool removeHeaders;

	FString getMimeType(FString fileType);
	TMap<FString, FString> mimeTypes;

	TMap<FString, FString> toCancelRequests;

	//class FWebComObjectMbitTimerThread* webComObjectMbitTimerThread = nullptr;

	//void registerRequestObjectForMbit(FString requestID, UWebCommunicationRequestCompleteObject* requestObject);
	//void unRegisterRequestObjectForMbit(FString requestID);

	//bool runRequestProgress;
	private:
		void startRequests(TArray<struct FhttpRequest>& httpRequests);
		void fillDownloadRequest(FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request, const FString& InVerb);
		void fillRequestPOST(struct FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request);
		bool fillRequestUPLOAD(struct FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request);
		TArray<uint8> fstringToByteArray(FString data);
		FString postMapToString(TMap<FString, FString> POSTData);

		void startIndividualRequest(FString url, TMap<FString, FString> header, FString verb, FString content, UWebCommunicationBPLibrary* userver, UWebCommunicationRequestCompleteObject* requestObject);
};


/*GET AND POST */
//class FExecuteHttpRequestsThread : public FRunnable {
//
//public:
//
//	FExecuteHttpRequestsThread(TArray<struct FhttpRequest> &httpRequestsP) :
//		httpRequests(httpRequestsP) {
//		FString threadName = "FSendHTTPGETDataToServerThread_" + FGuid::NewGuid().ToString();
//		thread = FRunnableThread::Create(this, *threadName, 0, EThreadPriority::TPri_Normal);
//	}
//
//	virtual uint32 Run() override {
//
//		//waiting for full initialisation
//		while(thread == nullptr)
//			FPlatformProcess::Sleep(0.01);
//
//		while (run) {
//
//			for (int32 i = 0; i < httpRequests.Num(); i++) {
//				UWebCommunicationRequestCompleteObject* requestObject = httpRequests[i].request;
//				FString url = httpRequests[i].url;
//				TMap<FString, FString> POSTData = httpRequests[i].POSTData;
//				EHTTPWebComRequestType requestType = httpRequests[i].requestType;
//
//				if (requestObject == nullptr) {
//					UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (153)."));
//					continue;
//				}
//
//				//requestObject->setWebComHttpRequest(httpRequests[i]);
//
//				TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request = FHttpModule::Get().CreateRequest();
//
//				request->SetHeader("User-Agent", "UE4 Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.81 Safari/537.36");
//				if (UWebCommunicationBPLibrary::webcom->additionalHeader.Num() > 0) {
//					for (auto& element : UWebCommunicationBPLibrary::webcom->additionalHeader) {
//						request->SetHeader(element.Key, element.Value);
//					}
//				}
//
//				switch (requestType) {
//				case EHTTPWebComRequestType::GET:
//					fillRequestGET(httpRequests[i], request);
//					if (requestObject->getMultiStepDownload() > 0) {
//						request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepComplete);
//						request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestMultiStepDownload);
//						request->ProcessRequest();
//						continue;
//					}
//					if ((requestObject->getFileCancelResumeDownloadByteStart() > 0)) {
//						request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-");
//					}
//					request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
//					break;
//				case EHTTPWebComRequestType::GETLowRamDownload:
//					fillRequestGET(httpRequests[i], request);
//					request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestCompleteLowRam);
//					request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownloadLowRam);
//					if (requestObject->getFileCancelResumeDownloadByteFullSize() == 0) {
//						request->SetHeader("Range", "bytes=0-0");
//					}
//					else {
//						if ((requestObject->getFileCancelResumeDownloadByteStart() + requestObject->getFileCancelResumeDownloadBytePartSize()) > requestObject->getFileCancelResumeDownloadByteFullSize()) {
//							request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-");
//						}
//						else {
//							request->SetHeader("Range", "bytes=" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart()) + "-" + FString::FromInt(requestObject->getFileCancelResumeDownloadByteStart() + requestObject->getFileCancelResumeDownloadBytePartSize()));
//						}
//						
//					}
//					//UE_LOG(LogTemp, Error, TEXT("Head: %s"), *request->GetHeader("Range"));
//					request->ProcessRequest();
//					continue;
//					break;
//				case EHTTPWebComRequestType::PUT:
//				case EHTTPWebComRequestType::POST_UPLOAD:
//					if (!fillRequestUPLOAD(httpRequests[i], request)) {
//						continue;
//					}
//					request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressUpload);
//					break;
//				case EHTTPWebComRequestType::POST:
//					fillRequestPOST(httpRequests[i], request);
//					request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
//					break;
//				case EHTTPWebComRequestType::INDIVIDUAL:
//					request->SetVerb(httpRequests[i].verb);
//					request->SetURL(url);
//					request->SetContentAsString(httpRequests[i].content);
//
//					if (httpRequests[i].header.Num() > 0) {
//						for (auto& element : httpRequests[i].header) {
//							request->SetHeader(element.Key, element.Value);
//						}
//					}
//					break;
//				}
//
//
//				/*for (int32 i = 0; i < request->GetAllHeaders().Num(); i++){
//					UE_LOG(LogTemp, Error, TEXT("Head: %s"), *request->GetAllHeaders()[i]);
//				}*/
//
//				request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestComplete);
//
//				request->ProcessRequest();
//			}
//			httpRequests.Empty();
//
//			if (run ) {
//				pauseThread(true);
//
//
//				while (paused) {
//					FPlatformProcess::Sleep(0.01);
//				}
//
//			}
//		}
//		thread = nullptr;
//		return 0;
//	}
//
//	void fillRequestGET(struct FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request) {
//		request->SetHeader("Content-Type", "application/x-www-form-urlencoded");
//		request->SetVerb("GET");
//		request->SetURL(requestStruct.url);
//	}
//
//	void fillRequestPOST(struct FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request) {
//		request->SetHeader("Content-Type", "application/x-www-form-urlencoded");
//		request->SetVerb("POST");
//		request->SetURL(requestStruct.url);
//
//		FString JoinedStr = postMapToString(requestStruct.POSTData);
//		request->SetContentAsString(JoinedStr);
//		request->SetHeader("Content-Length", FString::FromInt(JoinedStr.Len()));
//	}
//
//	bool fillRequestUPLOAD(struct FhttpRequest requestStruct, TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request) {
//
//		FString dir;
//		if (requestStruct.DirectoryType == EHTTPWebComFileUpload::E_ad) {
//			dir = FPaths::ConvertRelativePathToFull(requestStruct.filePath);
//		}
//		else {
//			FString gameDir = FPaths::ProjectDir();
//			dir = FPaths::ConvertRelativePathToFull(gameDir + requestStruct.filePath);
//		}
//
//
//		if (!FPaths::FileExists(dir)) {
//			UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: File not found: %s"), *dir);
//			return false;
//		}
//
//		TArray<uint8> fileData;
//		if (!FFileHelper::LoadFileToArray(fileData, *dir)) {
//			UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Can't read File: %s"), *dir);
//			return false;
//		}
//
//		FString fileName = FPaths::GetCleanFilename(dir);
//		FString fileFormat = FPaths::GetExtension(fileName, false);
//		FString mimeType = UWebCommunicationBPLibrary::getWebCommunicationTarget()->getMimeType(fileFormat);
//
//
//		if (requestStruct.requestType == EHTTPWebComRequestType::POST_UPLOAD) {
//			request->SetVerb("POST");
//		}
//		else {
//			request->SetVerb("PUT");
//		}
//		request->SetURL(requestStruct.url);
//
//		FString boundary = "UE4WebCommunicationPlugin_" + FString::FromInt(FDateTime::Now().GetTicks());
//		request->SetHeader("Content-Type", "multipart/form-data; boundary=----" + boundary);
//
//		TArray<uint8> dataToSend;
//
//		//add File
//		TArray<uint8> fileHeaderArray = fstringToByteArray("------" + boundary + "\r\nContent-Disposition: form-data; name=\"" + requestStruct.fileID + "\"; filename=\"" + fileName + "\"\r\n" + mimeType + "\r\n\r\n");
//		dataToSend.Append(fileHeaderArray);
//		dataToSend.Append(fileData);	
//		
//		//add Post Data
//		if (requestStruct.POSTData.Num() > 0) {
//			TArray<FString> keys;
//			requestStruct.POSTData.GetKeys(keys);
//			for (int i = 0; i < keys.Num(); i++) {
//				if (requestStruct.POSTData.Find(keys[i]) == nullptr) {
//					UE_LOG(LogTemp, Warning, TEXT("UWebCommunicationBPLibrary: Parameter %s has no value."), *keys[i]);
//					continue;
//				}
//				FString postVal = "\r\n------" + boundary + "\r\nContent-Disposition: form-data; name=\""+ keys[i] +"\"\r\n\r\n"+*requestStruct.POSTData.Find(keys[i]);
//				dataToSend.Append(fstringToByteArray(postVal));
//			}
//		}
//
//		//file head after file data
//		TArray<uint8> bottomArray = fstringToByteArray("\r\n------" + boundary + "--\r\n");
//		dataToSend.Append(bottomArray);
//
//		request->SetHeader("Content-Length", FString::FromInt(dataToSend.Num()));
//		if (UWebCommunicationBPLibrary::webcom->additionalHeader.Num() > 0) {
//			for (auto& element : UWebCommunicationBPLibrary::webcom->additionalHeader) {
//				request->SetHeader(element.Key, element.Value);
//			}
//		}
//
//		request->SetContent(dataToSend);
//
//		return true;
//	}
//
//	TArray<uint8> fstringToByteArray(FString data) {
//		TArray<uint8> byteArray;
//		/*FBufferArchive buffer;
//		buffer << data;
//		byteArray.Append(buffer);
//		byteArray.RemoveAt(0, 4);
//		byteArray.RemoveAt(byteArray.Num() - 1, 1);*/
//		FTCHARToUTF8 Convert(*data);
//		byteArray.Append((uint8*)((ANSICHAR*)Convert.Get()),Convert.Length());
//		return byteArray;
//	}
//
//	FString postMapToString(TMap<FString,FString> POSTData) {
//		FString JoinedStr;
//		TArray<FString> keys;
//		POSTData.GetKeys(keys);
//		for (int j = 0; j < keys.Num(); j++) {
//			FString* val = POSTData.Find(keys[j]);
//			FString param;
//			if (val != nullptr) {
//				param = keys[j] + "=" + *val;
//			}
//
//			if (JoinedStr.IsEmpty()) {
//				JoinedStr = param;
//			}
//			else {
//				JoinedStr += "&" + param;
//			}
//		}
//		return JoinedStr;
//	}
//
//	FRunnableThread* getThread() {
//		return thread;
//	}
//
//	void setThread(FRunnableThread* threadP) {
//		thread = threadP;
//	}
//
//	void stopThread() {
//		run = false;
//		if (thread != nullptr) {
//			pauseThread(false);
//		}
//	}
//
//	bool isRun() {
//		return run;
//	}
//
//	void addRequests(TArray<struct FhttpRequest> httpRequestsP) {
//		httpRequests = httpRequestsP;
//		pauseThread(false);
//	}
//	void pauseThread(bool pause) {
//		paused = pause;
//		thread->Suspend(pause);
//	}
//
//protected:
//	TArray<struct FhttpRequest> httpRequests;
//	bool run = true;
//	FRunnableThread*		thread = nullptr;
//	bool					paused;
//};


/* INVIDUAL REQUEST Thread*/
//class FSendHTTPInvidualDataToServerThread : public FRunnable {
//
//public:
//
//	FSendHTTPInvidualDataToServerThread(FString urlP, TMap<FString, FString> headerP, FString verbP, FString contentP, UWebCommunicationBPLibrary* userverP, UWebCommunicationRequestCompleteObject* requestObjectP) :
//		url(urlP),
//		header(headerP),
//		verb(verbP),
//		content(contentP),
//		userver(userverP),
//		requestObject(requestObjectP){
//		FString threadName = "FSendHTTPInvidualDataToServerThread_" + FGuid::NewGuid().ToString();
//		thread = FRunnableThread::Create(this, *threadName, 0, EThreadPriority::TPri_Normal);
//	}
//
//	virtual uint32 Run() override {
//
//		//waiting for full initialisation
//		while (thread == nullptr)
//			FPlatformProcess::Sleep(0.01);
//
//		while (run) {
//
//			if (requestObject == nullptr) {
//				UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Missing Object instance (266)."));
//				continue;
//			}
//
//			TSharedRef<IHttpRequest, ESPMode::ThreadSafe> request = FHttpModule::Get().CreateRequest();
//			request->SetVerb(verb);
//			request->SetURL(url);
//			request->SetContentAsString(content);
//
//			bool headerTest = false;
//			if (header.Num() > 0) {
//				for (auto& element : header) {
//					request->SetHeader(element.Key, element.Value);
//					if (!headerTest && element.Value.Len() > 0) {
//						headerTest = true;
//					}
//				}
//			}
//
//			if (!headerTest) {
//				UE_LOG(LogTemp, Error, TEXT("Cancel Request : You have not specified a header. An HTTP request does not work without a header.Add headers like user-agent,Content-Type,Content-Length"));
//				//UWebCommunicationBPLibrary::cancelRequest();
//			}
//			else {
//				request->OnProcessRequestComplete().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestComplete);
//				request->OnRequestProgress().BindUObject(requestObject, &UWebCommunicationRequestCompleteObject::requestProgressDownload);
//
//				request->ProcessRequest();
//			}
//						
//
//			if (run) {
//				pauseThread(true);
//
//				//android workaround. suspend do not work on android
//#if PLATFORM_ANDROID || PLATFORM_IOS
//				while (paused) {
//					FPlatformProcess::Sleep(0.01);
//				}
//#endif
//			}
//		}
//		return 0;
//	}
//
//
//	FRunnableThread* getThread() {
//		return thread;
//	}
//
//	void setThread(FRunnableThread* threadP) {
//		thread = threadP;
//	}
//
//	void stopThread() {
//		run = false;
//		if (thread != nullptr) {
//			pauseThread(false);
//		}
//	}
//
//	bool isRun() {
//		return run;
//	}
//
//	void addRequests(FString urlP, TMap<FString, FString> headerP, FString verbP, FString contentP, UWebCommunicationBPLibrary* userverP, UWebCommunicationRequestCompleteObject* requestObjectP) {
//		url = urlP;
//		header = headerP;
//		verb = verbP;
//		content = contentP;
//		userver = userverP;
//		requestObject = requestObjectP;
//		thread->Suspend(false);
//	}
//	void pauseThread(bool pause) {
//		paused = pause;
//		thread->Suspend(pause);
//	}
//
//
//protected:
//	FString							url;
//	TMap<FString, FString>			header;
//	FString							verb;
//	FString							content;
//	UWebCommunicationBPLibrary*		userver;
//	UWebCommunicationRequestCompleteObject* requestObject = nullptr;
//	bool run = true;
//	FRunnableThread*		thread = nullptr;
//	bool					paused;
//
//};


class FByteToFileThread : public FRunnable {

public:

	FByteToFileThread(TArray<uint8> byteDataP, EHTTPWebComFileUpload DirectoryTypeP, FString filePathP, EHTTPWebComFileBytesToFileActionType fileActionP) :
		byteData(byteDataP),
		DirectoryType(DirectoryTypeP),
		filePath(filePathP),
		fileAction(fileActionP){
		FString threadName = "FByteToFileThread" + FGuid::NewGuid().ToString();
		FRunnableThread::Create(this, *threadName, 0, EThreadPriority::TPri_Normal);
	}

	virtual uint32 Run() override {
		if (byteData.Num() <= 0) {
			UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: Empty byteData Array."));
			return 0;
		}
		if (filePath.IsEmpty()) {
			UE_LOG(LogTemp, Error, TEXT("UWebCommunicationBPLibrary: FilePath not set."));
			return 0;
		}
		FString dir;
		if (DirectoryType == EHTTPWebComFileUpload::E_ad) {
			dir = FPaths::ConvertRelativePathToFull(filePath);
		}
		else {
			FString gameDir = FPaths::ProjectDir();
			dir = FPaths::ConvertRelativePathToFull(gameDir + filePath);
		}

		switch (fileAction)
		{
		case EHTTPWebComFileBytesToFileActionType::E_NOT_OVERWRITE:
			if (FPaths::FileExists(dir)) {
				UE_LOG(LogTemp, Warning, TEXT("UWebCommunicationBPLibrary: File exist. Cancel write to file."));
				return 0;
			}
			break;
		case EHTTPWebComFileBytesToFileActionType::E_OVERWRITE:
			FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*dir);
		}

		FArchive* writer = IFileManager::Get().CreateFileWriter(*dir, EFileWrite::FILEWRITE_Append);
	
		if (!writer) {
			UE_LOG(LogTemp, Error, TEXT("ByteDataToFile: Can't write into %s "), *dir);
		}
		else {
			writer->Seek(writer->TotalSize());
			writer->Serialize(byteData.GetData(), byteData.Num());
			byteData.Empty();
			writer->Close();
		}

		delete writer;

		//FFileHelper::SaveArrayToFile(byteData, *dir);

		return 0;
	}


protected:
	TArray<uint8> byteData;
	EHTTPWebComFileUpload DirectoryType;
	FString filePath;
	EHTTPWebComFileBytesToFileActionType fileAction;

};

///*MBit Timer */
//class FWebComObjectMbitTimerThread : public FRunnable {
//
//public:
//
//	FWebComObjectMbitTimerThread() {
//		FString threadName = "FWebComObjectMbitTimerThread" + FGuid::NewGuid().ToString();
//		thread = FRunnableThread::Create(this, *threadName, 0, EThreadPriority::TPri_Normal);
//	}
//
//	virtual uint32 Run() override {
//
//
//		while (run) {
//			TArray<UWebCommunicationRequestCompleteObject*> arr;
//			requestObjects.GenerateValueArray(arr);
//
//			for (int32 i = 0; i < arr.Num(); i++){
//				if (arr[i]->getBytesLast() == 0) {
//					arr[i]->setBytesLast(arr[i]->getBytesCurrent());
//					continue;
//				}
//
//				
//				arr[i]->setMbit(((float)arr[i]->getBytesCurrent() - (float)arr[i]->getBytesLast()) / 1024 / 1024 * 8);
//				UE_LOG(LogTemp, Error, TEXT("mbit: %i | %i | %f"),arr[i]->getBytesCurrent(), arr[i]->getBytesLast(), (((float)arr[i]->getBytesCurrent() - (float)arr[i]->getBytesLast()) / 1024 / 1024 * 8) );
//				arr[i]->setBytesLast(arr[i]->getBytesCurrent());
//
//			}
//			FPlatformProcess::Sleep(1);
//		}
//
//		thread = nullptr;
//		return 0;
//	}
//
//
//
//	FRunnableThread* getThread() {
//		return thread;
//	}
//
//	void setThread(FRunnableThread* threadP) {
//		thread = threadP;
//	}
//
//	void stopThread() {
//		run = false;
//		if (thread != nullptr) {
//			pauseThread(false);
//		}
//	}
//
//	bool isRun() {
//		return run;
//	}
//
//	void registerRequestObject(FString requestID, UWebCommunicationRequestCompleteObject* requestObject) {
//		requestObjects.Add(requestID, requestObject);
//	}
//
//	void unRegisterRequestObject(FString requestID) {
//		requestObjects.Remove(requestID);
//	}
//
//	void pauseThread(bool pause) {
//		paused = pause;
//		thread->Suspend(pause);
//	}
//
//protected:
//	TMap<FString,UWebCommunicationRequestCompleteObject*> requestObjects;
//	bool run = true;
//	FRunnableThread* thread = nullptr;
//	bool	paused;
//};