// Copyright 2017-2019 <PERSON>(<PERSON><PERSON>). All Rights Reserved.
#pragma once

#include "WebCommunication.h"
#include "WebCommunicationRequestCompleteObject.generated.h"

class UWebCommunicationBPLibrary;

enum class EMultiStepType : uint8
{
	GOOGLE		UMETA(DisplayName = "GoogleDrive"),
	GOOGLE_INFO		UMETA(DisplayName = "GoogleDriveInfo"),
	ANONFILE 	UMETA(DisplayName = "Anonfiles")
};

UCLASS(Blueprintable, BlueprintType)
class UWebCommunicationRequestCompleteObject : public UObject
{
	GENERATED_UCLASS_BODY()
		~UWebCommunicationRequestCompleteObject();

public:
	void requestComplete(FHttpRequestPtr Request, FHttpResponsePtr response, bool bWasSuccessful);
	void requestProgressUpload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived);
	void requestProgressDownload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived);

	void requestCompleteLowRam(FHttpRequestPtr Request, FHttpResponsePtr response, bool bWasSuccessful);
	void requestProgressDownloadLowRam(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived);


	void setRequestID(FString requestID, UWebCommunicationBPLibrary* webCommunicationBPLibrary);
	FString getRequestID();

	void setDownloadID(FString downloadID);
	FString getDownloadID();


	void setMultiStepDownload(int32 step);
	int32 getMultiStepDownload();
	void setMultiStepDownloadType(EMultiStepType multiStepDownloadType);
	EMultiStepType getMultiStepDownloadType();
	int32 getHTMLFileSize();
	void setHTMLFileSize(int32 htmlfilesize);

	void requestMultiStepComplete(FHttpRequestPtr Request, FHttpResponsePtr response, bool bWasSuccessful);
	void requestMultiStepDownload(FHttpRequestPtr request, uint64 bytesSent, uint64 bytesReceived);


	//EHTTPWebComFileDownloadResumeType actionIfFileExists;
	//void setActionIfFileExists(EHTTPWebComFileDownloadResumeType actionIfFileExists);
	//EHTTPWebComFileDownloadResumeType getActionIfFileExists();


	int32 fileCancelResumeDownloadByteFullSize = 0;
	void setFileCancelResumeDownloadByteFullSize(int32 size);
	int32 getFileCancelResumeDownloadByteFullSize();

	int32 fileCancelResumeDownloadBytePartSize = 0;
	void setFileCancelResumeDownloadBytePartSize(int32 size);
	int32 getFileCancelResumeDownloadBytePartSize();

	int32 fileCancelResumeDownloadByteStart = 0;
	void setFileCancelResumeDownloadByteStart(int32 size);
	int32 getFileCancelResumeDownloadByteStart();

	FString fileCancelResumeDownloadDir;
	void setFileCancelResumeDownloadDir(FString dir);
	FString getFileCancelResumeDownloadDir();

	int32 getBytesCurrent();
	int32 getBytesLast();
	void setBytesLast(int32 l);
	void setMbit(float m);

	//void setWebComHttpRequest(struct FhttpRequest httpRequest);

private:
	FString requestID;
	FString downloadID;
	UWebCommunicationBPLibrary* webCommunicationBPLibrary = nullptr;

	int32 multiStepDownload;
	EMultiStepType multiStepDownloadType;
	int32 HTMLFileSize;

	FString generateURLFromHTML(FString html,FString oldURL);

	int32 bytesCurrent;
	int32 bytesLast;
	float mbit;

	class FWebComObjectMbitTimerThread* webComObjectMbitTimerThread = nullptr;

	TArray<uint8> downloadDataCopy;

	//struct FhttpRequest httpRequest;
};



/*MBit Timer */
class FWebComObjectMbitTimerThread : public FRunnable {

public:

	FWebComObjectMbitTimerThread(UWebCommunicationRequestCompleteObject* robjectP):
		robject(robjectP){
		FString threadName = "FWebComObjectMbitTimerThread" + FGuid::NewGuid().ToString();
		thread = FRunnableThread::Create(this, *threadName, 0, EThreadPriority::TPri_Normal);
	}

	virtual uint32 Run() override {

		FString s = FGuid::NewGuid().ToString();

		while (run) {
			if ((nullptr == robject || !robject->IsValidLowLevel())) return 0;

			if (robject->getRequestID().IsEmpty() == false) {
				//UE_LOG(LogTemp, Error, TEXT("mbit thread %s"), *s);
				if (robject->getBytesLast() == 0) {
					robject->setBytesLast(robject->getBytesCurrent());
					//UE_LOG(LogTemp, Error, TEXT("mbit xxxxxxxxxxxxxx"), *s);
				}
				else {
					float mbit = ((float)robject->getBytesCurrent() - (float)robject->getBytesLast()) / 1024 / 1024 * 8;
					if (mbit > 0) {
						robject->setMbit(mbit);
					}
					if (robject->getBytesCurrent() > 0)
						robject->setBytesLast(robject->getBytesCurrent());

					//UE_LOG(LogTemp, Warning, TEXT("mbit: %i | %i | %f"), robject->getBytesCurrent(), robject->getBytesLast(), mbit);
				}
			}

			FPlatformProcess::Sleep(1);
		}

		thread = nullptr;
		return 0;
	}



	FRunnableThread* getThread() {
		return thread;
	}

	void setThread(FRunnableThread* threadP) {
		thread = threadP;
	}

	void stopThread() {
		run = false;
	}

	bool isRun() {
		return run;
	}

	void registerRequestObject(FString requestID, UWebCommunicationRequestCompleteObject* requestObject) {
		requestObjects.Add(requestID, requestObject);
	}

	void unRegisterRequestObject(FString requestID) {
		requestObjects.Remove(requestID);
	}


protected:
	TMap<FString, UWebCommunicationRequestCompleteObject*> requestObjects;
	bool run = true;
	FRunnableThread* thread = nullptr;

	UWebCommunicationRequestCompleteObject* robject;
};