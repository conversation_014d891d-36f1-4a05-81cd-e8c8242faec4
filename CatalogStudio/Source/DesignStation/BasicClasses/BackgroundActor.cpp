// Fill out your copyright notice in the Description page of Project Settings.

#include "BackgroundActor.h"
#include "Runtime/Engine/Classes/Engine/StaticMesh.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"


// Sets default values
ABackgroundActor::ABackgroundActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	GridMeshComp = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("GridMesh"));
	UMaterial* GridMat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/GridMat.GridMat'"));
	if (GridMat)
	{
		GridMatInstance = UMaterialInstanceDynamic::Create(GridMat, nullptr);
		UStaticMesh* GridMesh = LoadObject<UStaticMesh>(nullptr, TEXT("StaticMesh'/Engine/BasicShapes/Plane.Plane'"));
		if (GridMatInstance && GridMesh)
		{
			GridMeshComp->SetStaticMesh(GridMesh);
			GridMeshComp->SetMaterial(0, GridMatInstance);
		}
	}
	GridMeshComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	GridMeshComp->SetRelativeScale3D(FVector(100.0f));

}

void ABackgroundActor::UpdateGridScale(const float& InCameraDistance)
{
	UE_LOG(LogTemp, Log, TEXT("---------  %f  ---------"), InCameraDistance);
	int32 FloorDistance = FCatalogFunctionLibrary::Floor(static_cast<int32>(InCameraDistance));
	int32 MatScale = 1000.0 / FloorDistance;
	UE_LOG(LogTemp, Log, TEXT("---------  %d  ---------"), MatScale);
	if (GridMatInstance && GridMeshComp)
	{
		//GridMatInstance->SetScalarParameterValue(FName(TEXT("DistanceSize")), static_cast<float>(MatScale));
		//GridMeshComp->SetMaterial(0, GridMatInstance);
	}
}

// Called when the game starts or when spawned
void ABackgroundActor::BeginPlay()
{
	Super::BeginPlay();
	this->SetActorLocation(FVector(20.0f, 20.0f, 0.0));
}

