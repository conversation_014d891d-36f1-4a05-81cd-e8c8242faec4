// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "BackgroundActor.generated.h"

UCLASS()
class DESIGNSTATION_API ABackgroundActor : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ABackgroundActor();

	void UpdateGridScale(const float& InCameraDistance);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

private:

	UPROPERTY()
	UStaticMeshComponent* GridMeshComp;

	UPROPERTY()
	UMaterialInstanceDynamic* GridMatInstance;
	
};
