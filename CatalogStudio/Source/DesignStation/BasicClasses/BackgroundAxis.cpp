// Fill out your copyright notice in the Description page of Project Settings.

#include "BackgroundAxis.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"


#define AXIS_LINE_SIZE 2.0f

// Sets default values
ABackgroundAxis::ABackgroundAxis()
	:DisplayAxisType(EPlanPolygonBelongs::EUnknown)
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));

	UStaticMesh* ArrowMesh = LoadObject<UStaticMesh>(nullptr, TEXT("StaticMesh'/Game/Mesh/zhizheng_mk_Cone001.zhizheng_mk_Cone001'"));
	UStaticMesh* Tag_Mesh = LoadObject<UStaticMesh>(nullptr, TEXT("StaticMesh'/Game/Mesh/Mesh_Axis_Tag.Mesh_Axis_Tag'"));
	TArray<FOutlineLineSegments> Lines;
	Lines.Add(FOutlineLineSegments());
	Lines[0].StartPoint = FVector::ZeroVector;
	{
		X_Axis = CreateDefaultSubobject<UOutlineComponent>(TEXT("X_Axis"));
		X_Axis->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		X_Axis->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		X_Axis->SetLineColor(FLinearColor::Red);
		X_Axis->SetOutlineSize(AXIS_LINE_SIZE);
		Lines[0].EndPoint = FVector(100.0f, 0.0f, 0.0f);
		X_Axis->SetOutlines(Lines);

		X_Axis_Arrow = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("X_Axis_Arrow"));
		X_Axis_Arrow->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		X_Axis_Arrow->SetMobility(EComponentMobility::Movable);
		X_Axis_Arrow->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		X_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		X_Axis_Arrow->SetStaticMesh(ArrowMesh);
		UMaterial* X_Axis_Mat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/Axis/Mat_AxisX.Mat_AxisX'"));
		X_Axis_Arrow->SetMaterial(0, Cast<UMaterialInterface>(X_Axis_Mat));

		X_Tag = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("X_Tag"));
		//X_Tag->AttachToComponent(X_Axis, FAttachmentTransformRules::KeepRelativeTransform);
		X_Tag->SetMobility(EComponentMobility::Movable);
		X_Tag->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		X_Tag->SetRelativeLocation(FVector(110.0f, 0.0f, 0.0f));
		if (Tag_Mesh)
			X_Tag->SetStaticMesh(Tag_Mesh);

		UMaterialInstance* X_Tag_Mat = LoadObject<UMaterialInstance>(nullptr, TEXT("MaterialInstanceConstant'/Game/Materials/Axis/X.X'"));
		if (X_Tag_Mat)
		{
			X_Tag->SetMaterial(0, X_Tag_Mat);
		}
	}
	{
		Y_Axis = CreateDefaultSubobject<UOutlineComponent>(TEXT("Y_Axis"));
		Y_Axis->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		Y_Axis->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Y_Axis->SetLineColor(FLinearColor::Green);
		Y_Axis->SetOutlineSize(AXIS_LINE_SIZE);
		Lines[0].EndPoint = FVector(0.0f, 100.0f, 0.0f);
		Y_Axis->SetOutlines(Lines);

		Y_Axis_Arrow = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Y_Axis_Arrow"));
		Y_Axis_Arrow->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		Y_Axis_Arrow->SetMobility(EComponentMobility::Movable);
		Y_Axis_Arrow->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Y_Axis_Arrow->SetRelativeRotation(FRotator(0.0f, 90.0f, 0.0f));
		Y_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		Y_Axis_Arrow->SetStaticMesh(ArrowMesh);
		UMaterial* Y_Axis_Mat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/Axis/Mat_AxisY.Mat_AxisY'"));
		Y_Axis_Arrow->SetMaterial(0, Cast<UMaterialInterface>(Y_Axis_Mat));

		Y_Tag = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Y_Tag"));
		//Y_Tag->AttachToComponent(Y_Axis, FAttachmentTransformRules::KeepRelativeTransform);
		Y_Tag->SetMobility(EComponentMobility::Movable);
		Y_Tag->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Y_Tag->SetRelativeLocation(FVector(0.0f, 110.0f, 0.0f));
		if (Tag_Mesh)
			Y_Tag->SetStaticMesh(Tag_Mesh);

		UMaterialInstance* Y_Tag_Mat = LoadObject<UMaterialInstance>(nullptr, TEXT("MaterialInstanceConstant'/Game/Materials/Axis/Y.Y'"));
		if (Y_Tag_Mat)
		{
			Y_Tag->SetMaterial(0, Y_Tag_Mat);
		}
	}
	{
		Z_Axis = CreateDefaultSubobject<UOutlineComponent>(TEXT("Z_Axis"));
		Z_Axis->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		Z_Axis->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Z_Axis->SetLineColor(FLinearColor::Blue);
		Z_Axis->SetOutlineSize(AXIS_LINE_SIZE);
		Lines[0].EndPoint = FVector(0.0f, 0.0f, 100.0f);
		Z_Axis->SetOutlines(Lines);

		Z_Axis_Arrow = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Z_Axis_Arrow"));
		Z_Axis_Arrow->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		Z_Axis_Arrow->SetMobility(EComponentMobility::Movable);
		Z_Axis_Arrow->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Z_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		Z_Axis_Arrow->SetRelativeRotation(FRotator(90.0f, 0.0f, 0.0f));
		Z_Axis_Arrow->SetStaticMesh(ArrowMesh);
		UMaterial* Z_Axis_Mat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/Axis/Mat_AxisZ.Mat_AxisZ'"));
		Z_Axis_Arrow->SetMaterial(0, Cast<UMaterialInterface>(Z_Axis_Mat));

		Z_Tag = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Z_Tag"));
		//Z_Tag->AttachToComponent(Z_Axis, FAttachmentTransformRules::KeepRelativeTransform);
		Z_Tag->SetMobility(EComponentMobility::Movable);
		Z_Tag->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		Z_Tag->SetRelativeLocation(FVector(0.0f, 0.0f, 110.0f));
		if (Tag_Mesh)
			Z_Tag->SetStaticMesh(Tag_Mesh);

		UMaterialInstance* Z_Tag_Mat = LoadObject<UMaterialInstance>(nullptr, TEXT("MaterialInstanceConstant'/Game/Materials/Axis/Z.Z'"));
		if (Z_Tag_Mat)
		{
			Z_Tag->SetMaterial(0, Z_Tag_Mat);
		}
	}
}


void ABackgroundAxis::ChangeSizeByContentSize(const FVector& InContentSize)
{
	FVector NewSize = InContentSize * 2;
	TArray<FOutlineLineSegments> Lines;
	Lines.Add(FOutlineLineSegments());
	Lines[0].StartPoint = FVector::ZeroVector;
	const FVector XYZSize(FMath::Max<float>(NewSize.X, 100.0f), FMath::Max<float>(NewSize.Y, 100.0f), FMath::Max<float>(NewSize.Z, 100.0f));
	if (X_Axis->IsVisible())
	{
		Lines[0].EndPoint = FVector(XYZSize.X, 0.0f, 0.0f);
		X_Axis->SetOutlines(Lines);
		X_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		X_Tag->SetRelativeLocation(FVector(XYZSize.X + 10.0f, 0.0f, 0.0f));
	}
	if (Y_Axis->IsVisible())
	{
		Lines[0].EndPoint = FVector(0.0f, XYZSize.Y, 0.0f);
		Y_Axis->SetOutlines(Lines);
		Y_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		Y_Tag->SetRelativeLocation(FVector(0.0f, XYZSize.Y + 10.0f, 0.0f));
	}
	if (Z_Axis->IsVisible())
	{
		Lines[0].EndPoint = FVector(0.0f, 0.0f, XYZSize.Z);
		Z_Axis->SetOutlines(Lines);
		Z_Axis_Arrow->SetRelativeLocation(Lines[0].EndPoint);
		Z_Tag->SetRelativeLocation(FVector(0.0f, 0.0f, XYZSize.Z + 10.0f));
	}
}

#define UPDATE_AXIS_VISIBILITY(AxisType,Visibility)\
AxisType##_Axis->SetVisibility(Visibility, true);\
AxisType##_Axis_Arrow->SetVisibility(Visibility, true);\
AxisType##_Tag->SetVisibility(Visibility, true);\

void ABackgroundAxis::UpdateAxisType(EPlanPolygonBelongs AxisType)
{
	DisplayAxisType = AxisType;
	if (EPlanPolygonBelongs::EXY_Plan == AxisType)
	{
		UPDATE_AXIS_VISIBILITY(X, true)
			UPDATE_AXIS_VISIBILITY(Y, true)
			UPDATE_AXIS_VISIBILITY(Z, false)
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == AxisType)
	{
		UPDATE_AXIS_VISIBILITY(X, true)
			UPDATE_AXIS_VISIBILITY(Y, false)
			UPDATE_AXIS_VISIBILITY(Z, true)
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == AxisType)
	{
		UPDATE_AXIS_VISIBILITY(X, false)
			UPDATE_AXIS_VISIBILITY(Y, true)
			UPDATE_AXIS_VISIBILITY(Z, true)
	}
	else
	{
		UPDATE_AXIS_VISIBILITY(X, true)
			UPDATE_AXIS_VISIBILITY(Y, true)
			UPDATE_AXIS_VISIBILITY(Z, true)
	}
}

void ABackgroundAxis::ChangeTagRotation(const FVector& InCameraLocation, const float& InCameraWidth)
{
	float Scale = InCameraWidth * 3.0f / 521.0f;
	Scale = FMath::Max<float>(Scale, 0.5f);
	Scale = FMath::Min<float>(Scale, 10.0f);
	if (EPlanPolygonBelongs::EXY_Plan == DisplayAxisType)
	{
		X_Tag->SetRelativeRotation(FRotator(90.0f, 0.0f, 0.0f));
		Y_Tag->SetRelativeRotation(FRotator(90.0f, 0.0f, 0.0f));
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == DisplayAxisType)
	{
		X_Tag->SetRelativeRotation(FRotator(0.0f, 90.0f, 90.0f));
		Z_Tag->SetRelativeRotation(FRotator(0.0f, 90.0f, 90.0f));
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == DisplayAxisType)
	{
		Y_Tag->SetRelativeRotation(FRotator(180, -180.0f, -90.0f));
		Z_Tag->SetRelativeRotation(FRotator(180, -180.0f, -90.0f));
	}
	else
	{
		{
			FVector Forward = InCameraLocation - X_Tag->GetRelativeLocation();
			Forward.Normalize();
			FRotator Rotation = Forward.Rotation();
			Rotation.Roll = 90.0f;
			X_Tag->SetRelativeRotation(Rotation);
		}
		{
			FVector Forward = InCameraLocation - Y_Tag->GetRelativeLocation();
			Forward.Normalize();
			FRotator Rotation = Forward.Rotation();
			Rotation.Roll = 90.0f;
			Y_Tag->SetRelativeRotation(Rotation);
		}
		{
			FVector Forward = InCameraLocation - Z_Tag->GetRelativeLocation();
			Forward.Normalize();
			FRotator Rotation = Forward.Rotation();
			Rotation.Roll = 90.0f;
			Z_Tag->SetRelativeRotation(Rotation);
		}
	}
}


// Called when the game starts or when spawned
void ABackgroundAxis::BeginPlay()
{
	AActor::BeginPlay();
}

// Called every frame
void ABackgroundAxis::Tick(float DeltaTime)
{
	AActor::Tick(DeltaTime);
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (IS_OBJECT_PTR_VALID(PC))
	{
		ChangeTagRotation(PC->PlayerCameraManager->GetCameraLocation(), PC->PlayerCameraManager->GetOrthoWidth());
	}
}

