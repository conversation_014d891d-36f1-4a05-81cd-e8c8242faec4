// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "Runtime/Engine/Classes/Components/StaticMeshComponent.h"
#include "GeometryEdit/Components/OutlineComponent.h"
#include "BackgroundAxis.generated.h"


UCLASS()
class DESIGNSTATION_API ABackgroundAxis : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	ABackgroundAxis();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	void ChangeSizeByContentSize(const FVector& InContentSize);

	void UpdateAxisType(EPlanPolygonBelongs AxisType);

	void ChangeTagRotation(const FVector& InCameraLocation, const float& InCameraWidth);

protected:

	EPlanPolygonBelongs	DisplayAxisType;

	UPROPERTY()
		UOutlineComponent* X_Axis;
	UPROPERTY()
		UStaticMeshComponent* X_Axis_Arrow;
	UPROPERTY()
		UStaticMeshComponent* X_Tag;

	UPROPERTY()
		UOutlineComponent* Y_Axis;
	UPROPERTY()
		UStaticMeshComponent* Y_Axis_Arrow;
	UPROPERTY()
		UStaticMeshComponent* Y_Tag;

	UPROPERTY()
		UOutlineComponent* Z_Axis;
	UPROPERTY()
		UStaticMeshComponent* Z_Axis_Arrow;
	UPROPERTY()
		UStaticMeshComponent* Z_Tag;

};
