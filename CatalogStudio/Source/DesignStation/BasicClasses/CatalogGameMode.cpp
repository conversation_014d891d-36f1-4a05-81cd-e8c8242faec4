// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogGameMode.h"
#include "CatalogPlayerController.h"
#include "CatalogPawn.h"
#include "Runtime/Core/Public/Misc/Paths.h"

//_ThumbnailConvertPtr	ACatalogGameMode::ThumbnailConvertPtr = nullptr;
//
//void*	ACatalogGameMode::_DllHandle = nullptr;


ACatalogGameMode::ACatalogGameMode()
{
	ACatalogGameMode::PlayerControllerClass = ACatalogPlayerController::StaticClass();
	ACatalogGameMode::DefaultPawnClass = ACatalogPawn::StaticClass();

	//if (nullptr == ACatalogGameMode::_DllHandle)
	//{
	//	ACatalogGameMode::_DllHandle = ACatalogGameMode::LoadDll(FPaths::ProjectPluginsDir() + TEXT("ImageProcess"), TEXT("ImageProcess.dll"));
	//}
	//if (nullptr != ACatalogGameMode::_DllHandle)
	//{
	//	ACatalogGameMode::ThumbnailConvertPtr = (_ThumbnailConvertPtr)FPlatformProcess::GetDllExport(ACatalogGameMode::_DllHandle, TEXT("ThumbnailConvert"));
	//}
}

//void* ACatalogGameMode::LoadDll(const FString& InPath, const FString& InName)
//{
//	FString FilePath = InPath + TEXT("/") + InName;
//	if (FPaths::FileExists(FilePath))
//	{
//		//ͨ��FPlatformProcess::GetDllHandle��ȡdll�ľ��
//		return FPlatformProcess::GetDllHandle(*FilePath);//Retrieve the DLL.
//	}
//	return nullptr;
//}

//int32 ACatalogGameMode::ThumbnailConvert(const FString& InImagePath, const FString& OutImagePath, const int32& Width, const int32& Height)
//{
//	if (ACatalogGameMode::ThumbnailConvertPtr != nullptr)
//	{
//		return ACatalogGameMode::ThumbnailConvertPtr("F:\\Test.jpg", "F:\\Test2.png", 100, 100);
//	}
//	return -5;
//}