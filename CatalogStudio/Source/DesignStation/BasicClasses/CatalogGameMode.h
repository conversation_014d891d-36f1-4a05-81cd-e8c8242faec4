// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "CatalogGameMode.generated.h"

//typedef int(*_ThumbnailConvertPtr)(const char* ImagePath, const char* OutputImagePath, int Width, int Height);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API ACatalogGameMode : public AGameModeBase
{
	GENERATED_BODY()
	
public:

	ACatalogGameMode();

//	static int32 ThumbnailConvert(const FString& InImagePath, const FString& OutImagePath, const int32& Width, const int32& Height);
//
//private:
//
//	static void* LoadDll(const FString& InPath, const FString& InName);
//
//private:
//
//	static _ThumbnailConvertPtr	ThumbnailConvertPtr;
//
//	static void * _DllHandle;
	
};
