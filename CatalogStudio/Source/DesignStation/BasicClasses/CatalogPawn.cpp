// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogPawn.h"
#include "Runtime/Engine/Classes/Engine/StaticMesh.h"
#include "DesignStation/BasicClasses/Managers/SingleComponentManager.h"
#include "DesignStation/BasicClasses/Managers/MultiComponentManager.h"
#include "CatalogPlayerController.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/BasicClasses/Managers/CustomMaterialEditManager.h"
#include "DesignStation/UI/FrontDirectory/FrontShowWidget.h"
#include "Managers/ViewManager.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"


FCameraState::FCameraState(const FRotator& InRotation, const float& InArmLength, const FVector& InLocation, const float& InOrthWidth, const ECameraProjectionMode::Type& InProjectionMode, float InFOV/* = 90*/)
	:Rotation(InRotation)
	, ArmLength(InArmLength)
	, Location(InLocation)
	, OrthWidth(InOrthWidth)
	, FieldOfView(InFOV)
	, ProjectionMode(InProjectionMode)
{
}

const FCameraState& FCameraState::GetDollHouseDefaultCameraState()
{
	static const FCameraState DollHouseDefaultCameraState(FRotator(-45.0, -90.0f, 0.0), 300.0f, FVector::ZeroVector, 512.0f, ECameraProjectionMode::Type::Perspective);
	return DollHouseDefaultCameraState;
}

const FCameraState& FCameraState::GetXYPlanDefaultCameraState()
{
	static const FCameraState XYPlanDefaultCameraState(FRotator(-90.0, -90.0f, 0.0), 300.0f, FVector::ZeroVector, 512.0f, ECameraProjectionMode::Type::Orthographic);
	return XYPlanDefaultCameraState;
}

const FCameraState& FCameraState::GetYZPlanDefaultCameraState()
{
	static const FCameraState DollHouseDefaultCameraState(FRotator(0.0, -180.0f, 0.0), 300.0f, FVector::ZeroVector, 512.0f, ECameraProjectionMode::Type::Orthographic);
	return DollHouseDefaultCameraState;
}

const FCameraState& FCameraState::GetXZPlanDefaultCameraState()
{
	static const FCameraState XZPlanDefaultCameraState(FRotator(0.0, -90.0f, 0.0), 300.0f, FVector::ZeroVector, 512.0f, ECameraProjectionMode::Type::Orthographic);
	return XZPlanDefaultCameraState;
}

const FCameraState& FCameraState::GetMaterialEditCameraState()
{
	static const FCameraState MaterialCameraState(FRotator(20.2f, 180.0f, 0.0), (FVector(977.0, -13.0, -331.0) - FVector(977.0, -13.0, -331.0)).Size(), FVector(977.0, -13.0, -331.0), 512.0f, ECameraProjectionMode::Type::Perspective, 10.0f);
	return MaterialCameraState;
}

const FCameraState& FCameraState::GetViewMaterialCameraState()
{
	static const FCameraState ViewMaterialCameraState(FRotator(20.2f, 180.0f, 0.0), 1000.0f, FVector(977.0, -13.0, -331.0), 512.0f, ECameraProjectionMode::Type::Perspective, 10.0f);
	return ViewMaterialCameraState;
}


#define CAMERA_ORTHO_FAR_CLIP (2097152.0f)
#define CAMERA_ORTHO_NEAR_CLIP (0.0f)

// Sets default values
ACatalogPawn::ACatalogPawn()
	:CurrentManager(nullptr)
	, CurrentManagerType(EManagerType::EMainManager)
	, CurrentCameraType(ECameraType::EDollHouse)
{
	// Set this pawn to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
	AutoPossessAI = EAutoPossessAI::Disabled;
	AIControllerClass = nullptr;
	bNetLoadOnClient = false;
	bNetUseOwnerRelevancy = false;
	bReplicates = false;
	NetDormancy = ENetDormancy::DORM_Never;

	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	SpringArm = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraArm"));
	SpringArm->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	SpringArm->SetRelativeRotation(FRotator(-45.0, -90.0f, 0.0));
	SpringArm->bDoCollisionTest = false;
	SpringArm->TargetArmLength = 300.0f;

	MainCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("MainCamera"));
	MainCamera->AttachToComponent(SpringArm, FAttachmentTransformRules::KeepRelativeTransform);

	RFrontView = FRotator(0.0, -90.0f, 0.0);
	RRearView = FRotator(0.0, 90.0f, 0.0);
	RLeftView = FRotator(0.0, 0.0f, 0.0f);
	RRightView = FRotator(0.0, 180.0f, 0.0f);
	RTopView = FRotator(-90.0f, -90.0f, 0.0);
	RTLFView = FRotator(-45.0f, -45.0f, 0.0);
	RTLRView = FRotator(-45.0f, 45.0f, 0.0);
	RTRFView = FRotator(-45.0f, 225.0f, 0.0);
	RTRRView = FRotator(-45.0f, -225.0f, 0.0);
	RMaterialView = FRotator(0, 0, 0);

}

#undef CAMERA_ORTHO_FAR_CLIP
#undef CAMERA_ORTHO_NEAR_CLIP

// Called when the game starts or when spawned
void ACatalogPawn::BeginPlay()
{
	Super::BeginPlay();

	//ULocalDatabaseSubsystem* LocalDatabaseSys = GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	//const FString LocalDBPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));
	//LocalDatabaseSys->OpenDatabase(LocalDBPath);
	//{
	//	//FTransform DefaultTransform = FTransform(FRotator::ZeroRotator, FVector(0.0f, 0.0f, -0.01f));
	//	//AActor* NewGridActor = this->GetWorld()->SpawnActor(ABackgroundActor::StaticClass(), &DefaultTransform);
	//	//GridActor = Cast<ABackgroundActor>(NewGridActor);
	//}

	MainManager = NewObject<UMainManager>(this);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParameterDatas;
	MainManager->StartUp(ParameterDatas);
	MainManager->OnChangeToSingleComponentEdit.BindUFunction(this, FName(TEXT("OnEditSingleComponentHandler")));
	MainManager->OnChangeToMultiComponentEdit.BindUFunction(this, FName(TEXT("OnEditMultiComponentHandler")));
	MainManager->OnChangeToMaterialEdit.BindUFunction(this, FName(TEXT("OnEditMaterialHandler")));
	CurrentManager = MainManager;

	PreCameraStates.Add(ECameraType::EDollHouse, FCameraState::GetDollHouseDefaultCameraState());
	PreCameraStates.Add(ECameraType::EXYPlan2D, FCameraState::GetXYPlanDefaultCameraState());
	PreCameraStates.Add(ECameraType::EYZPlan2D, FCameraState::GetYZPlanDefaultCameraState());
	PreCameraStates.Add(ECameraType::EXZPlan2D, FCameraState::GetXZPlanDefaultCameraState());
	PreCameraStates.Add(ECameraType::EMaterialEdit, FCameraState::GetMaterialEditCameraState());
	PreCameraStates.Add(ECameraType::EViewMaterial, FCameraState::GetViewMaterialCameraState());
}

// Called every frame
void ACatalogPawn::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if (EManagerType::EMainManager != CurrentManagerType)
	{
		if (IS_OBJECT_PTR_VALID(CurrentManager))
			CurrentManager->OnMouseMove();
	}
}

void ACatalogPawn::OnEditSingleComponentHandler(const FSingleComponentTableData& SingleComponentData, const FFolderTableData& OpenFolderData)
{
	CurrentManager->Shutdown();
	CurrentManager = nullptr;
	USingleComponentManager* SingleComponentManager = NewObject<USingleComponentManager>(this);
	SingleComponentManager->SetSingleComponentToEdit(SingleComponentData);
	SingleComponentManager->SetSingleCompFolderData(OpenFolderData);
	SingleComponentManager->SetCameraLocation(MainCamera->GetComponentLocation());
	SingleComponentManager->SetCameraWidth(MainCamera->OrthoWidth);
	SingleComponentManager->OnChangeCameraType.BindUFunction(this, FName(TEXT("OnChangeCameraTypeHandler")));
	SingleComponentManager->OnBackToMain.BindUFunction(this, FName(TEXT("OnBackToMainHandler")));
	SingleComponentManager->TakePictureTypeDelegate.BindUFunction(this, FName(TEXT("OnTakePictureHandler")));

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParameterDatas;
	//UParameterRelativeLibrary::SelectParametersWithEnumInfoByFolderID(SingleComponentData.folder_id, ParameterDatas);
	SingleComponentManager->StartUp(ParameterDatas);
	CurrentManager = SingleComponentManager;
	CurrentManagerType = EManagerType::ESingleComponentManager;
	FCameraState DefaultDollHouse = FCameraState::GetDollHouseDefaultCameraState();
	PreCameraStates[ECameraType::EDollHouse] = DefaultDollHouse;
	ChangeCameraType(ECameraType::EDollHouse);
	//this->ApplyNewCameraState(FCameraState::GetDollHouseDefaultCameraState());
}

void ACatalogPawn::OnEditMultiComponentHandler(const FFolderTableData& OpenFolderData)
{
	CurrentManager->Shutdown();
	CurrentManager = nullptr;
	UMultiComponentManager* MultiComponentManager = NewObject<UMultiComponentManager>(this);
	MultiComponentManager->SetMultiCompFolderData(OpenFolderData);
	MultiComponentManager->SetCameraLocation(MainCamera->GetComponentLocation());
	MultiComponentManager->SetCameraWidth(MainCamera->OrthoWidth);
	MultiComponentManager->OnBackToMain.BindUFunction(this, FName(TEXT("OnBackToMainHandler")));
	MultiComponentManager->TakePictureTypeDelegate.BindUFunction(this, FName(TEXT("OnTakePictureHandler")));
	CurrentManager = MultiComponentManager;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ParameterDatas;
	//FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParameters(true, OpenFolderData.id, ParameterDatas);
	CurrentManager->StartUp(ParameterDatas);
	CurrentManagerType = EManagerType::EMultiComponentManager;
	ChangeCameraType(ECameraType::EDollHouse);
	//this->ApplyNewCameraState(FCameraState::GetDollHouseDefaultCameraState());
}

void ACatalogPawn::OnEditMaterialHandler(const FCustomMaterialTableData& MaterialData, const FFolderTableData& OpenFolderData)
{
	CurrentManager->Shutdown();
	CurrentManager = nullptr;
	UCustomMaterialEditManager* CustomMaterialEdit = NewObject<UCustomMaterialEditManager>(this);
	CustomMaterialEdit->SetCustomMaterialToEdit(MaterialData);
	CustomMaterialEdit->SetMatFolderData(OpenFolderData);
	CustomMaterialEdit->OnBackToMain.BindUFunction(this, FName(TEXT("OnBackToMainHandler")));
	CustomMaterialEdit->TakePictureTypeDelegate.BindUFunction(this, FName(TEXT("OnTakePictureHandler")));
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParameterDatas;
	//TArray<FParameterData> Parameters;
	//FLocalDatabaseParameterLibrary::RetriveFileParametersByID(OpenFolderData.id, Parameters);
	//CustomMaterialEdit->SetMatParam(Parameters);
	CustomMaterialEdit->StartUp(ParameterDatas);
	//CustomMaterialEdit->OnUESearchFolderParameters(MaterialData.folder_id);
	CurrentManager = CustomMaterialEdit;
	CurrentManagerType = EManagerType::EMaterialManager;
	ChangeCameraType(ECameraType::EMaterialEdit);
	this->ApplyNewCameraState(FCameraState::GetMaterialEditCameraState());
}

void ACatalogPawn::OnViewModuleToShowHandler(const FCSModelMatData& ViewData)
{
	CurrentManager->Shutdown();
	CurrentManager = nullptr;

	UViewManager* ViewManager = NewObject<UViewManager>(this);
	//ViewManager->InitManager(ViewData);
	ViewManager->Init(ViewData);
	ViewManager->SetCameraLocation(MainCamera->GetComponentLocation());
	ViewManager->SetCameraWidth(MainCamera->OrthoWidth);
	ViewManager->OnBackToMain.BindUFunction(this, FName(TEXT("OnBackToMainHandler")));
	CurrentManager = ViewManager;

	CurrentManager->StartUp(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>());
	CurrentManagerType = EManagerType::EViewManager;

	ChangeCameraType(ViewManager->IsModelType() ? ECameraType::EDollHouse : ECameraType::EViewMaterial);
}

void ACatalogPawn::ResetCameraTransform()
{

	ChangeCameraType(CurrentCameraType);
	auto CurrentLocation = GetActorLocation();
	if (ECameraType::EXYPlan2D == CurrentCameraType)
	{
		SetActorLocation(FVector(CurrentLocation.X, CurrentLocation.Y, 0.0f));
	}
	else if (ECameraType::EYZPlan2D == CurrentCameraType)
	{
		SetActorLocation(FVector(CurrentLocation.X, 0.0f, CurrentLocation.Z));
	}
	else if (ECameraType::EXZPlan2D == CurrentCameraType)
	{
		SetActorLocation(FVector(0.0f, CurrentLocation.Y, CurrentLocation.Z));
	}
}

void ACatalogPawn::OnBackToMainHandler(const FFolderTableData& FolderData)
{
	if (EManagerType::EMainManager != CurrentManagerType)
	{
		if (CurrentManagerType == EManagerType::EViewManager)
		{
			UFrontShowWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
		}

		CurrentManager->Shutdown();
		CurrentManager = nullptr;
		CurrentManagerType = EManagerType::EMainManager;
		MainManager->BringBack();
		MainManager->SyncSelectItem(FolderData);
		CurrentManager = MainManager;
	}
}

void ACatalogPawn::OnTakePictureHandler(const int32& PictureType)
{
	ECameraBtnType CameraPicture = static_cast<ECameraBtnType>(PictureType);

	UE_LOG(LogTemp, Log, TEXT("take picture type %d"), PictureType);
	if (PictureType == 11 || PictureType == 10)
	{
		CurrentManager->ShowAxis(true);
	}
	else
	{
		CurrentManager->ShowAxis(false);
	}
	ECameraType CameraType;
	if (PictureType == -1)
	{
		CameraType = ECameraType::ENone;
	}
	else if (PictureType == -2)
	{
		CameraType = ECameraType::E2D;
	}
	else
	{
		CameraType = static_cast<ECameraType>(PictureType + 5);
	}

	this->TakePictureType(CameraType);

}

float ACatalogPawn::CameraDistance(const ECameraType& CameraType)
{
	FVector ActorCenter = CurrentManager->GetActorCenter();
	FVector ActorBox = CurrentManager->GetActorBox();
	UE_LOG(LogTemp, Log, TEXT("ACatalogPawn::CameraDistance ActorBox=%s ActorCenter=%s"), *ActorBox.ToString(), *ActorCenter.ToString());

	float scale = GEngine->GameViewport->GetWindow()->GetSizeInScreen().X / 512;
	float Max = 0.0f;
	float Temp = 0.0f;
	float D = 1.f;
	if (ActorBox.IsNearlyZero(0.01f)) return 300;

	if (ECameraType::EFrontView == CameraType || ECameraType::ERearView == CameraType)
	{
		Max = FMath::Sqrt(ActorBox.Z * ActorBox.Z + ActorBox.X * ActorBox.X) * 2.0f;
		Temp = ActorBox.Y;
		scale /= 2;
		D = 1.2f;
	}
	else if (ECameraType::ELeftView == CameraType || ECameraType::ERightView == CameraType)
	{
		Max = FMath::Sqrt(ActorBox.Y * ActorBox.Y + ActorBox.Z * ActorBox.Z) * 2.0f;
		Temp = ActorBox.X;
		scale /= 2;
		D = 1.2f;
	}
	else if (ECameraType::ETopView == CameraType)
	{
		Max = FMath::Sqrt(ActorBox.Y * ActorBox.Y + ActorBox.X * ActorBox.X) * 2.0f;
		Temp = ActorBox.Z;
		scale /= 2;
		D = 1.3f;
	}
	else
	{
		Max = FMath::Max(ActorBox.Y, ActorBox.X);
		Max = FMath::Sqrt(Max * Max + ActorBox.Z * ActorBox.Z);
		Temp = Max * 0.5f;
		D = 1.15f;
		return (Max * scale + Temp) / D;
	}
	return FMath::Max(10.1f, Max * scale + Temp) / D;
}

FVector ACatalogPawn::CameraCenter(const ECameraType& CameraType)
{
	FVector ActorCenter = CurrentManager->GetActorCenter();
	return ActorCenter;
}

void ACatalogPawn::OnChangeCameraTypeHandler(int32 IntValue)
{
	ECameraType CameraType = static_cast<ECameraType>(IntValue);
	this->ChangeCameraType(CameraType);
}

void ACatalogPawn::ChangeCameraType(const ECameraType& InNewType)
{
	if (InNewType != CurrentCameraType)
	{
		FCameraState& PreState = PreCameraStates[CurrentCameraType];
		this->SavePreCameraState(PreState);
		CurrentCameraType = InNewType;
	}
	const FCameraState& NewState = PreCameraStates[InNewType];
	this->ApplyNewCameraState(NewState);
	/*FRotator NewRotator = FRotator::ZeroRotator;
	FVector NewLocation = FVector(0.0f, 0.0f, -0.1f);
	if (ECameraType::EXZPlan2D == CurrentCameraType)
	{
		NewRotator = FRotator(0.0f, 0.0f, 90.0f);
		NewLocation = FVector(0.0f, -0.1f, 0.0f);
	}
	else if (ECameraType::EYZPlan2D == CurrentCameraType)
	{
		NewRotator = FRotator(90.0f, 0.0f, 0.0f);
		NewLocation = FVector(-0.1f, 0.0f, 0.0f);
	}
	else if (ECameraType::EXYPlan2D == CurrentCameraType)
	{
		FVector NewLocation = FVector(0.0f, 0.0f, -20.0f);
	}*/
	if (IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->OnCameraLocationOrWidthChanged(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
	}
	CameraLocationWidthChanged.ExecuteIfBound(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
}

void ACatalogPawn::TakePictureType(const ECameraType& CameraType)
{
	if (EManagerType::EMaterialManager == CurrentManagerType)
	{
		return;
	}
	
	if (ECameraType::ENone == CameraType)
	{
		TakePictureState = FCameraState(
			FRotator(-10, -100.0f, 0),
			CameraDistance(ECameraType::EFrontView) * 20,
			CameraCenter(ECameraType::EFrontView),
			512.0f,
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::EFrontView == CameraType)
	{
		TakePictureState = FCameraState(
			RFrontView,
			CameraDistance(ECameraType::EFrontView) *20,
			CameraCenter(ECameraType::EFrontView),
			512.0f, 
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::ERearView == CameraType)
	{
		TakePictureState = FCameraState(
			RRearView, 
			CameraDistance(ECameraType::ERearView) * 20,
			CameraCenter(ECameraType::ERearView),
			512.0f, 
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::ELeftView == CameraType)
	{
		TakePictureState = FCameraState(
			RLeftView, 
			CameraDistance(ECameraType::ELeftView) * 20,
			CameraCenter(ECameraType::ELeftView), 
			512.0f, 
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::ERightView == CameraType)
	{
		TakePictureState = FCameraState(
			RRightView, 
			CameraDistance(ECameraType::ERightView) * 20,
			CameraCenter(ECameraType::ERightView), 
			512.0f,
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::ETopView == CameraType)
	{
		TakePictureState = FCameraState(
			RTopView, 
			CameraDistance(ECameraType::ETopView) * 5, 
			CameraCenter(ECameraType::ETopView), 
			512.0f, 
			ECameraProjectionMode::Type::Orthographic);
	}
	else if (ECameraType::ETLFView == CameraType)
	{
		TakePictureState = FCameraState(RTLFView, 
			CameraDistance(ECameraType::ETLFView) * 5, 
			CameraCenter(ECameraType::ETLFView), 
			512.0f, 
			ECameraProjectionMode::Type::Perspective);
	}
	else if (ECameraType::ETLRView == CameraType)
	{
		TakePictureState = FCameraState(RTLRView, 
			CameraDistance(ECameraType::ETLRView) * 5, 
			CameraCenter(ECameraType::ETLRView), 
			512.0f, 
			ECameraProjectionMode::Type::Perspective);
	}
	else if (ECameraType::ETRFView == CameraType)
	{
		TakePictureState = FCameraState(RTRFView,
			CameraDistance(ECameraType::ETRFView) * 5, 
			CameraCenter(ECameraType::ETRFView), 
			512.0f, 
			ECameraProjectionMode::Type::Perspective);
	}
	else if (ECameraType::ETRRView == CameraType)
	{
		TakePictureState = FCameraState(RTRRView, 
			CameraDistance(ECameraType::ETRRView) * 5, 
			CameraCenter(ECameraType::ETRRView), 
			512.0f, 
			ECameraProjectionMode::Type::Perspective);
	}
	else if (ECameraType::ESure == CameraType)
	{
		ChangeCameraType(CurrentCameraType);
		return;
	}
	else if (ECameraType::ECancel == CameraType)
	{
		ChangeCameraType(CurrentCameraType);
		return;
	}
	else if (ECameraType::E2D == CameraType)
	{
		if (CurrentCameraType == ECameraType::EXYPlan2D)
		{
			TakePictureState = FCameraState(RTopView,
				CameraDistance(ECameraType::ETopView),
				CameraCenter(ECameraType::ETopView), 
				512.0f, 
				ECameraProjectionMode::Type::Perspective);
		}
		else if (CurrentCameraType == ECameraType::EYZPlan2D)
		{
			TakePictureState = FCameraState(RRightView,
				CameraDistance(ECameraType::ERightView),
				CameraCenter(ECameraType::ERightView), 
				512.0f, 
				ECameraProjectionMode::Type::Perspective);
		}
		else if (CurrentCameraType == ECameraType::EXZPlan2D)
		{
			TakePictureState = FCameraState(RFrontView, 
				CameraDistance(ECameraType::EFrontView),
				CameraCenter(ECameraType::EFrontView), 
				512.0f, 
				ECameraProjectionMode::Type::Perspective);
		}
	}
	this->ApplyNewCameraState(TakePictureState);
}

void ACatalogPawn::OnMouseXMove(const float& InValue)
{
	if (EManagerType::EMainManager != CurrentManagerType && EManagerType::EMaterialManager != CurrentManagerType)
	{
		bool MiddleMouseButtonDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::MiddleMouseButton);
		bool LeftMouseButtonDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::LeftMouseButton);
		if (LeftMouseButtonDown && !MiddleMouseButtonDown)
		{
			if (ECameraType::EDollHouse == CurrentCameraType)
			{
				FRotator OldRotator = SpringArm->GetRelativeTransform().Rotator();
				const float YawMoveSpeed = 4.0f;
				OldRotator.Yaw += InValue * YawMoveSpeed;
				if (OldRotator.Yaw > 180.0f)
				{
					OldRotator.Yaw -= 360.0f;
				}
				else if (OldRotator.Yaw < -180.0f)
				{
					OldRotator.Yaw += 360.0f;
				}
				SpringArm->SetRelativeRotation(OldRotator);
			}
			else
			{
				FVector OldLocation = SpringArm->GetRelativeTransform().GetLocation();
				const float TwoDSpeed = 4.0f;
				const float Delta = InValue * TwoDSpeed;
				switch (CurrentCameraType)
				{
				case ECameraType::EYZPlan2D:OldLocation.Y += Delta; break;
				case ECameraType::EXYPlan2D:
				case ECameraType::EXZPlan2D:OldLocation.X -= Delta; break;
				}
				SpringArm->SetRelativeLocation(OldLocation);
			}
		}
		else if (!LeftMouseButtonDown && MiddleMouseButtonDown)
		{
			if (ECameraType::EDollHouse == CurrentCameraType 
				|| ECameraType::EXYPlan2D == CurrentCameraType 
				|| ECameraType::EXZPlan2D == CurrentCameraType
				|| ECameraType::EYZPlan2D == CurrentCameraType)
			{
				FVector RightVector = SpringArm->GetRightVector();
				RightVector.Normalize();
				const float RightSpeed = -6.0f;
				RightVector *= (RightSpeed * InValue);
				//UE_LOG(LogTemp, Log, TEXT("------------ ACatalogPawn::OnMouseXMove %s  -------------"), *RightVector.ToString());
				this->AddActorLocalOffset(RightVector);
			}
		}
	}
	if (IS_OBJECT_PTR_VALID(CurrentManager))
		CurrentManager->OnCameraLocationOrWidthChanged(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
	CameraLocationWidthChanged.ExecuteIfBound(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
}

void ACatalogPawn::OnMouseYMove(const float& InValue)
{
	if (EManagerType::EMainManager != CurrentManagerType && EManagerType::EMaterialManager != CurrentManagerType)
	{
		bool MiddleMouseButtonDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::MiddleMouseButton);
		bool LeftMouseButtonDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::LeftMouseButton);
		if (LeftMouseButtonDown && !MiddleMouseButtonDown)
		{
			if (ECameraType::EDollHouse == CurrentCameraType)
			{
				FRotator OldRotator = SpringArm->GetRelativeTransform().Rotator();
				const float PitchMoveSpeed = 4.0f;
				OldRotator.Pitch += InValue * PitchMoveSpeed;
				OldRotator.Pitch = FMath::Clamp<float>(OldRotator.Pitch, -89.9f, 89.9f);
				SpringArm->SetRelativeRotation(OldRotator);
			}
			else
			{
				FVector OldLocation = SpringArm->GetRelativeTransform().GetLocation();
				const float TwoDSpeed = 4.0f;
				const float Delta = InValue * TwoDSpeed;
				switch (CurrentCameraType)
				{
				case ECameraType::EXYPlan2D:OldLocation.Y += Delta; break;
				case ECameraType::EYZPlan2D:
				case ECameraType::EXZPlan2D:OldLocation.Z -= Delta; break;
				}
				SpringArm->SetRelativeLocation(OldLocation);
			}
		}
		else if (!LeftMouseButtonDown && MiddleMouseButtonDown)
		{
			if (ECameraType::EDollHouse == CurrentCameraType
				|| ECameraType::EXZPlan2D == CurrentCameraType
				|| ECameraType::EYZPlan2D == CurrentCameraType)
			{
				FVector Location = this->GetActorLocation();
				const float ZSpeed = -8.0f;
				Location.Z += InValue * ZSpeed;
				this->SetActorLocation(Location);
			}
			else if (ECameraType::EXYPlan2D == CurrentCameraType)
			{
				FVector Location = this->GetActorLocation();
				const float ZSpeed = 6.0f;
				Location.Y += InValue * ZSpeed;
				this->SetActorLocation(Location);
			}
		}
	}
	if (IS_OBJECT_PTR_VALID(CurrentManager))
		CurrentManager->OnCameraLocationOrWidthChanged(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
	CameraLocationWidthChanged.ExecuteIfBound(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
}

void ACatalogPawn::OnMouseWheelScroll(const float& InValue)
{
	if (EManagerType::EMaterialManager != CurrentManagerType)
	{
		
		if (MainCamera->ProjectionMode == ECameraProjectionMode::Type::Perspective)
		{
			const int32 ScrollSpeed = 16;
			int32 NewArmLenght = (int32)SpringArm->TargetArmLength + ScrollSpeed * (int32)(InValue) * (-1);
			NewArmLenght = FMath::Clamp<int32>(NewArmLenght, 1, 10000);
			//NewArmLenght = FMath::Max<int32>(NewArmLenght, 1);
			//NewArmLenght = FMath::Min<int32>(NewArmLenght, 10000);
			SpringArm->TargetArmLength = NewArmLenght;

			/*const float ScrollSpeed = 16.0f;
			float NewArmLenght = SpringArm->TargetArmLength - ScrollSpeed * InValue;
			NewArmLenght = FMath::Max<float>(NewArmLenght, 1.0f);
			NewArmLenght = FMath::Min<float>(NewArmLenght, 10000.0f);
			SpringArm->TargetArmLength = NewArmLenght;*/
			//GridActor->UpdateGridScale(NewArmLenght);
			//UE_LOG(LogTemp, Log, TEXT("----- NewArmLenght %f  -----"), NewArmLenght);
		}
		else
		{
			int32 OldOrthoWidth = (int32)MainCamera->OrthoWidth;
			const int32 ScrollSpeed = 16.0f;
			OldOrthoWidth -= ScrollSpeed * (int32)(InValue);
			OldOrthoWidth = FMath::Clamp(OldOrthoWidth, MainCamera->OrthoNearClipPlane, MainCamera->OrthoFarClipPlane);
			MainCamera->OrthoWidth = OldOrthoWidth;

			/*float OldOrthoWidth = MainCamera->OrthoWidth;
			const float ScrollSpeed = 16.0f;
			OldOrthoWidth -= ScrollSpeed * InValue;
			OldOrthoWidth = FMath::Clamp(OldOrthoWidth, MainCamera->OrthoNearClipPlane, MainCamera->OrthoFarClipPlane);
			MainCamera->OrthoWidth = OldOrthoWidth;*/
		}
	}
	if (IS_OBJECT_PTR_VALID(CurrentManager))
		CurrentManager->OnCameraLocationOrWidthChanged(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
	CameraLocationWidthChanged.ExecuteIfBound(MainCamera->GetComponentLocation(), MainCamera->OrthoWidth);
}

void ACatalogPawn::ApplyNewCameraState(const FCameraState& InNewState)
{
	if (MainCamera)
	{
		MainCamera->ProjectionMode = InNewState.ProjectionMode;
		MainCamera->OrthoWidth = InNewState.OrthWidth;
		MainCamera->SetFieldOfView(InNewState.FieldOfView);
	}
	if (SpringArm)
	{
		SpringArm->SetRelativeRotation(InNewState.Rotation);
		SpringArm->TargetArmLength = InNewState.ArmLength;
	}
	if (ECameraType::EDollHouse == CurrentCameraType || ECameraType::EMaterialEdit == CurrentCameraType)
	{
		SpringArm->SetRelativeLocation(FVector::ZeroVector);
		this->SetActorLocation(InNewState.Location);
	}
	else
	{
		SpringArm->SetRelativeLocation(InNewState.Location);
	}

	/*UE_LOG(LogTemp, Warning, TEXT("Pawn Loc : %s"), *this->GetActorLocation().ToString());
	UE_LOG(LogTemp, Warning, TEXT("MainCamera Trans : %s"), *MainCamera->GetComponentTransform().ToString());
	UE_LOG(LogTemp, Warning, TEXT("SpringArm Loc : %s"), *SpringArm->GetComponentTransform().ToString());*/
}

void ACatalogPawn::SavePreCameraState(FCameraState& InPreState)
{
	if (MainCamera)
	{
		InPreState.ProjectionMode = MainCamera->ProjectionMode;
		InPreState.OrthWidth = MainCamera->OrthoWidth;
	}
	if (SpringArm)
	{
		FTransform trans = SpringArm->GetRelativeTransform();
		InPreState.Rotation = FRotator(trans.GetRotation());
		InPreState.ArmLength = SpringArm->TargetArmLength;
	}
	if (ECameraType::EDollHouse == CurrentCameraType)
	{
		InPreState.Location = this->GetActorLocation();
	}
	else
	{
		InPreState.Location = SpringArm->GetRelativeLocation();
	}
}

void ACatalogPawn::OnMouseLeftButtonClick(const FVector& InMousePosition)
{
	if (EManagerType::EMainManager != CurrentManagerType)
	{
		if (IS_OBJECT_PTR_VALID(CurrentManager))
			CurrentManager->OnLeftMouseButtonClick(InMousePosition);
	}
}

void ACatalogPawn::OnMouseRightButtonClick(const FVector& InMousePosition)
{
	if (IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->OnRightMouseButtonClick(InMousePosition);
	}
}

void ACatalogPawn::OnKeyReleaseHandler(const FKey& InKey)
{
	if (EManagerType::EMainManager != CurrentManagerType && IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->OnKeyClicked(InKey);
	}
}

void ACatalogPawn::OnCtrlSSaveHandler()
{
	if (IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->SaveTempData();
	}
}

void ACatalogPawn::ExitGame()
{
	if (IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->ExitGame();
	}
}

void ACatalogPawn::WindowSizeChanged()
{
	if (IS_OBJECT_PTR_VALID(CurrentManager))
	{
		CurrentManager->WindowSizeChanged();
	}
}
