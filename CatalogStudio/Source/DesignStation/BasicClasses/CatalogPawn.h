// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "Runtime/Engine/Classes/GameFramework/SpringArmComponent.h"
#include "Runtime/Engine/Classes/Camera/CameraComponent.h"
#include "Runtime/Engine/Classes/Components/ArrowComponent.h"
#include "BackgroundActor.h"
#include "DesignStation/BasicClasses/Managers/MainManager.h"
#include "CatalogPawn.generated.h"

enum class ECameraType : uint8
{
	EDollHouse,
	EXYPlan2D,
	EYZPlan2D,
	EXZPlan2D,
	EMaterialEdit,

	EFrontView,
	ERearView,
	ELeftView,
	ERightView,
	ETopView,
	ETLFView,
	ETLRView,
	ETRFView,
	ETRRView,
	EImport,
	ESure,
	ECancel,
	ENone,
	E2D,
	EViewMaterial
};




struct FCameraState
{
	FRotator Rotation;
	float	 ArmLength;
	FVector	 Location;
	float	 OrthWidth;
	float    FieldOfView;
	ECameraProjectionMode::Type ProjectionMode;
	FCameraState() :Rotation(FRotator::ZeroRotator), ArmLength(300.0f), Location(FVector::ZeroVector), OrthWidth(512), FieldOfView(90), ProjectionMode(ECameraProjectionMode::Type::Perspective) {}
	FCameraState(const FRotator& InRotation, const float& InArmLength, const FVector& InLocation, const float& InOrthWidth, const ECameraProjectionMode::Type& InProjectionMode, float InFOV = 90);

	static const FCameraState& GetDollHouseDefaultCameraState();

	static const FCameraState& GetXYPlanDefaultCameraState();

	static const FCameraState& GetYZPlanDefaultCameraState();

	static const FCameraState& GetXZPlanDefaultCameraState();

	static const FCameraState& GetMaterialEditCameraState();

	static const FCameraState& GetViewMaterialCameraState();
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FCameraLocationOrWidthChangedDelegate, const FVector&, NewLocation, const float&, NewWidth);

UCLASS()
class DESIGNSTATION_API ACatalogPawn : public APawn
{
	GENERATED_BODY()

public:
	// Sets default values for this pawn's properties
	ACatalogPawn();

	void ChangeCameraType(const ECameraType& InNewType);

	void TakePictureType(const ECameraType& PictureType);

	void ExitGame();

	void WindowSizeChanged();

	// Called every frame
	virtual void Tick(float DeltaTime) override;

	void OnMouseXMove(const float& InValue);

	void OnMouseYMove(const float& InValue);

	//Invalue=-1 while scroll up,InValue=1 while scroll down
	void OnMouseWheelScroll(const float& InValue);

	void OnMouseLeftButtonClick(const FVector& InMousePosition);

	void OnMouseRightButtonClick(const FVector& InMousePosition);

	void OnKeyReleaseHandler(const FKey& InKey);

	void OnCtrlSSaveHandler();

	UFUNCTION()
	void OnViewModuleToShowHandler(const FCSModelMatData& ViewData);

	void ResetCameraTransform();

	UManagerBase* GetCurrentManager() { return CurrentManager; }

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	void ApplyNewCameraState(const FCameraState& InNewState);

	void SavePreCameraState(FCameraState& InPreState);

	UFUNCTION()
		void OnEditSingleComponentHandler(const FSingleComponentTableData& SingleComponentData, const FFolderTableData& OpenFolderData);

	UFUNCTION()
		void OnEditMultiComponentHandler(const FFolderTableData& OpenFolderData);

	UFUNCTION()
		void OnEditMaterialHandler(const FCustomMaterialTableData& MaterialData, const FFolderTableData& OpenFolderData);

		
	UFUNCTION()
		void OnChangeCameraTypeHandler(int32 IntValue);

	UFUNCTION()
		void OnBackToMainHandler(const FFolderTableData& FolderData);

	UFUNCTION()
		void OnTakePictureHandler(const int32& PictureType);

protected:

	FCameraLocationOrWidthChangedDelegate	CameraLocationWidthChanged;

	UPROPERTY()
		UManagerBase* CurrentManager;

	UPROPERTY()
		UMainManager* MainManager;

	EManagerType CurrentManagerType;
	//

	TMap<ECameraType, FCameraState> PreCameraStates;

	ECameraType CurrentCameraType;

	//UPROPERTY()
	//ABackgroundActor* GridActor;

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
		USpringArmComponent* SpringArm;

	UPROPERTY(BlueprintReadOnly, EditAnywhere)
		UCameraComponent* MainCamera;

	float CameraDistance(const ECameraType& CameraType);

	FVector CameraCenter(const ECameraType& CameraType);

private:
	FRotator RFrontView;
	FRotator RRearView;
	FRotator RLeftView;
	FRotator RRightView;
	FRotator RTopView;
	FRotator RTLFView;
	FRotator RTLRView;
	FRotator RTRFView;
	FRotator RTRRView;
	FRotator RMaterialView;

	FCameraState TakePictureState;
	FCameraState CurrentCameraState;
};
