// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogPlayerController.h"
#include "CatalogPawn.h"
#include "Runtime/Engine/Public/TimerManager.h"

#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Runtime/Core/Public/Modules/ModuleManager.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"
#include "Runtime/Core/Public/Modules/ModuleManager.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "Engine/PostProcessVolume.h"
#include "Kismet/GameplayStatics.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"

#ifdef USE_REF_LOCAL_FILE
#else

#define LOGIN_REQUEST	(TEXT("LoginRequest"))
#define	UPLOAD_FILE_REQUEST	(TEXT("UploadFile"))
#define	DOWNLOAD_FILE_REQUEST	(TEXT("DownloadFile"))


#define QUERY_RELEASE_LOG (TEXT("QueryReleaseLog"))

#define MERGE_INSERT_LOG (TEXT("MergeInsertLog"))
#define SEARCH_MERGE_LOG (TEXT("SearchMergeLog"))

#define RELEASE (TEXT("Release"))

#define NET_STATE_OK 200

#endif

ACatalogPlayerController* ACatalogPlayerController::PCInstance = nullptr;

extern const FString RootParentID;

ACatalogPlayerController* ACatalogPlayerController::Get()
{
	return ACatalogPlayerController::PCInstance;
}

ACatalogPlayerController::ACatalogPlayerController()
	:bRightMouseButtonDown(false)
#ifdef USE_REF_LOCAL_FILE
#else
	, AccessToken(TEXT(""))
#endif
	, ScreenShotPath(TEXT(""))
{
	bShowMouseCursor = true;
	ACatalogPlayerController::PCInstance = this;
	SetReleaseLock(false);
}

bool ACatalogPlayerController::OnNetworkRequestCompleted(const TArray<FString>& InJsonDataString, const TArray<FString>& InHeaders, const int32& InStatusCode, const TArray<uint8>& InDataBytes, const FString& InRequestType)
{
#ifdef USE_REF_LOCAL_FILE

	return UCatalogNetworkSubsystem::GetInstance()->Connet_PC_NetworkRequestCompleted(InJsonDataString, InHeaders, InStatusCode, InDataBytes, InRequestType);

#else
	UE_LOG(LogTemp, Log, TEXT("RequestType %s InDataBytes count is %d "), *InRequestType, InDataBytes.Num());
	ERequestType RequestType = ERequestType::ENone;
	FString UUID = TEXT("");
	{
		if (InRequestType.Contains(UPLOAD_FILE_REQUEST))
		{
			RequestType = ERequestType::EUploadFile;
		}
		else if (InRequestType.Contains(DOWNLOAD_FILE_REQUEST))
		{
			RequestType = ERequestType::EDownloadFile;
		}
		else
		{
			RequestType = RequestTypeMap[InRequestType];
		}
	}
	{
		UE_LOG(LogTemp, Log, TEXT("RequestType is %d "), static_cast<int32>(RequestType));
		TMap<FString, FString> Headers;
		{//解决报头
			for (auto& HeaderIter : InHeaders)
			{
				FString Key;
				FString Value;
				bool bSplited = HeaderIter.Split(TEXT(":"), &Key, &Value);
				if (bSplited && !Headers.Contains(Key))
				{
					Headers.Add(Key, Value);
				}
			}
		}
		UUID = Headers.Contains(TEXT("uuid")) ? Headers[TEXT("uuid")] : TEXT("");
		if (UUID.IsEmpty())
		{
			if ((InJsonDataString.Num() > 0) && (InJsonDataString[0] == TEXT("正在发布中，请稍后重试")))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT(" "), FText::FromStringTable(FName("PosSt"), TEXT("release is in process try again later")).ToString());
				UE_LOG(LogTemp, Log, TEXT("Quit function"));
				return false;
			}
			UE_LOG(LogTemp, Log, TEXT("UUID is empty!"));
			//return false;
		}
		UUID = UUID.TrimStartAndEnd();
	}
	switch (RequestType)
	{
	case ERequestType::ELoginRequest:
	{
		UE_LOG(LogTemp, Log, TEXT("%s "), *InJsonDataString[0]);

		//std::shared_ptr<catalog_studio_message::LoginCheckResponse> pMsg(new catalog_studio_message::LoginCheckResponse);
		//pMsg->ParseFromArray(InDataBytes.GetData(), InDataBytes.Num());
		LoginCheckResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ERequestType::EQueryReleaseLog:
	{
		if (InJsonDataString.Num() > 0)
		{
			OnQueryReleaseLogResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		}
		break;
	}
	case ERequestType::EUploadFile:
	{
		FString FilePath = InRequestType.Right(InRequestType.Len() - FString(UPLOAD_FILE_REQUEST).Len() - 1);
		UploadFileResponseDelegate.Broadcast(200 == InStatusCode, FilePath);
		break;
	}
	case ERequestType::EDownloadFile:
	{
		UE_LOG(LogTemp, Log, TEXT("ERequestType::EDownloadFile"));
		FString FilePath = InRequestType.Right(InRequestType.Len() - FString(DOWNLOAD_FILE_REQUEST).Len() - 1);
		if (0 == FilePath.Len()) break;
		UE_LOG(LogTemp, Error, TEXT("ERequestType::EDownloadFile FilePath @ %s"), *FilePath);
		bool bSuccess = false;
		int32 StatusCode = InStatusCode;
		if (200 == InStatusCode)
		{
			for (int32 i = 0; (i < InJsonDataString.Num()) && (InJsonDataString[i].Contains(TEXT("{"))); ++i)
			{
				auto& Json = InJsonDataString[i];
				FUserInfoTableDataMsg DownloadMsg;
				bool bSuccess = UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FUserInfoTableDataMsg>(Json, DownloadMsg);//正常的JSON数据一定会大于20
				if (bSuccess)
				{
					StatusCode = FCString::Atoi(*DownloadMsg.code);
					break;
				}
			}
		}
		UUID = DownloadFilesQueue.WhichPackageFileIn(FilePath);
		if (200 == StatusCode)
		{//如果下载的文件是pak文件，则Mount此文件
			//在写文件之前需要判断是否存在旧文件，如果存在需要先将旧文件Unmount，然后删除旧文件
			FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + FilePath);
			bool Res = false;
			const FString FileExtension = FPaths::GetExtension(FilePath);
			if (FileExtension.Equals(TEXT("pak"), ESearchCase::IgnoreCase))
			{//如果文件后缀为Pak则Mount
				GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->UnmountPakFile(FilePath);
				FCatalogFunctionLibrary::DeleteFile(FilePath);
				Res = FCatalogFunctionLibrary::WriteDataToFile(FilePath, InDataBytes);
				GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->MountPakFile(FilePath, true);
			}
			else
			{
				Res = FCatalogFunctionLibrary::WriteDataToFile(FilePath, InDataBytes);
			}
		}
		else if (1044 == StatusCode)
		{
			UE_LOG(LogTemp, Error, TEXT("ERequestType::EDownloadFile StatusCode @ 1044"));
			//check(false);
		}
		{
			int32 Index = IndexOfUUID(UUID);
			if (INDEX_NONE != Index)
			{
				DownloadFilesQueue.Update(DownloadThreadInfo[Index]->UUID, DownloadThreadInfo[Index]->FilePath, NFileNetworkOpertator::EDownloadState::Downloaded);
				if (DownloadFilesQueue.IsDownloaded(DownloadThreadInfo[Index]->UUID))
				{
					TArray<FString> FilePaths;
					DownloadFilesQueue.GetFilePaths(DownloadThreadInfo[Index]->UUID, FilePaths);
					DownloadFileResponseDelegate.Broadcast(UUID, 200 == StatusCode || 500 == StatusCode, FilePaths);
				}
				DownloadFilesQueue.TryRemoveDownloadedPackage();
				DownloadThreadInfo[Index].Reset();
				FString NewUUID, NewFilePath;
				if (DownloadFilesQueue.First(NewUUID, NewFilePath))
				{
					this->StartDownloadFile(NewUUID, NewFilePath);
				}
			}
		}
		break;
	}
	case ERequestType::EMergeInsertLog:
	{
		OnMergeInsertResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ERequestType::ESearchMergeLog:
	{
		OnSearchMergeLogResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ERequestType::ERelease:
	{
		OnReleaseResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	default:
		break;
	}

	return false;
#endif
}

void ACatalogPlayerController::BeginPlay()
{
	Super::BeginPlay();
	ConsoleCommand("g.TimeoutForBlockOnRenderFence 9999999", false);

#ifdef USE_REF_LOCAL_FILE
#else
	RequestTypeMap.Add(LOGIN_REQUEST, ERequestType::ELoginRequest);
	RequestTypeMap.Add(UPLOAD_FILE_REQUEST, ERequestType::EUploadFile);
	RequestTypeMap.Add(DOWNLOAD_FILE_REQUEST, ERequestType::EDownloadFile);
	RequestTypeMap.Add(QUERY_RELEASE_LOG, ERequestType::EQueryReleaseLog);
	RequestTypeMap.Add(MERGE_INSERT_LOG, ERequestType::EMergeInsertLog);
	RequestTypeMap.Add(SEARCH_MERGE_LOG, ERequestType::ESearchMergeLog);
	RequestTypeMap.Add(RELEASE, ERequestType::ERelease);
#endif

	//启动时Mount所有的pak
	//GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>();
}

void ACatalogPlayerController::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
}

void ACatalogPlayerController::SetupInputComponent()
{
	APlayerController::SetupInputComponent();
	InputComponent->BindAction(TEXT("RightMouseButton"), EInputEvent::IE_Pressed, this, &ACatalogPlayerController::OnRightMousePressHandler);
	InputComponent->BindAction(TEXT("RightMouseButton"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnRightMouseReleaseHandler);
	InputComponent->BindAction(TEXT("LeftMouseButton"), EInputEvent::IE_Pressed, this, &ACatalogPlayerController::OnLeftMousePressHandler);
	InputComponent->BindAction(TEXT("LeftMouseButton"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnLeftMouseReleaseHandler);

	InputComponent->BindAction(TEXT("CtrlSSave"), EInputEvent::IE_Pressed, this, &ACatalogPlayerController::OnCtrlSSaveHandler);

	InputComponent->BindAxis(TEXT("MouseXMove"), this, &ACatalogPlayerController::OnMouseXMoveHandler);
	InputComponent->BindAxis(TEXT("MouseYMove"), this, &ACatalogPlayerController::OnMouseYMoveHandler);
	InputComponent->BindAxis(TEXT("MouseWheelScroll"), this, &ACatalogPlayerController::OnMouseWheelScrollMoveHandler);
	//InputComponent->BindAction(TEXT("SwitchToXY"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnSwitchPawnCameraToXY);
	//InputComponent->BindAction(TEXT("SwitchToYZ"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnSwitchPawnCameraToYZ);
	//InputComponent->BindAction(TEXT("SwitchToXZ"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnSwitchPawnCameraToXZ);
	//InputComponent->BindAction(TEXT("SwitchToDollHouse"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnSwitchPawnCameraToDollHouse);
	//InputComponent->BindAction(TEXT("ChangeToArrow"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnChangeToArrow);
	//InputComponent->BindAction(TEXT("ChangeToDrawPoint"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnChangeToDrawPoint);
	InputComponent->BindAction(TEXT("DeleteKey"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnDeleteKeyReleaseHandler);
	InputComponent->BindAction(TEXT("CtrlSaveKey"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnCtrlSKeyReleaseHandler);
	InputComponent->BindAction(TEXT("CtrlOpenKey"), EInputEvent::IE_Released, this, &ACatalogPlayerController::OnCtrlOKeyReleaseHandler);
	InputComponent->BindKey(EKeys::E, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyEReleaseHandler);
	
	//InputComponent->BindKey(EKeys::M, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyMReleaseHandler);
	//InputComponent->BindKey(EKeys::N, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyNReleaseHandler);
	//InputComponent->BindKey(EKeys::L, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyLReleaseHandler);
	//InputComponent->BindKey(EKeys::K, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyKReleaseHandler);
	//InputComponent->BindKey(EKeys::J, EInputEvent::IE_Released, this, &ACatalogPlayerController::OnKeyJReleaseHandler);
}

void ACatalogPlayerController::SetPostProcessType(const int32& InPPM)
{
	TArray<AActor*> PPM1Arr;
	UGameplayStatics::GetAllActorsWithTag(this, FName("PPM1"), PPM1Arr);
	TArray<AActor*> PPM2Arr;
	UGameplayStatics::GetAllActorsWithTag(this, FName("PPM2"), PPM2Arr);
	if(PPM1Arr.Num() > 0 && PPM2Arr.Num() > 0)
	{
		Cast<APostProcessVolume>(PPM1Arr[0])->BlendWeight = InPPM == 3 ? 0.0f : 1.0f;
		Cast<APostProcessVolume>(PPM2Arr[0])->BlendWeight = InPPM == 3 ? 1.0f : 0.0f;
	}
}

FString ACatalogPlayerController::GetAccessToken() const
{
	return UCatalogNetworkSubsystem::GetInstance()->GetAccessToken();
}

UCatalogNetworkSubsystem* ACatalogPlayerController::GetNetworkSubsystem()
{
	return UCatalogNetworkSubsystem::GetInstance();
}

bool ACatalogPlayerController::IsAdminLogin() const
{
	return UCatalogNetworkSubsystem::GetInstance()->IsAdminLogin();
}

TArray<FString> ACatalogPlayerController::GetStyleCraft()
{
	return UCatalogNetworkSubsystem::GetInstance()->GetStyleCraft();
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ACatalogPlayerController::GetGlobalParameterMap()
{
	if (GlobalParameterMap.IsEmpty())
	{
		FRefParamData Data;
		if (FPaths::FileExists(URefToParamDataLibrary::GetRefParamsAddress()))
		{
			UProtobufOperatorFunctionLibrary::LoadRelationFromFile(URefToParamDataLibrary::GetRefParamsAddress(), Data);
		}
		GlobalParameterMap = Data.GenerateParamsMap();
	}

	return GlobalParameterMap;
}

TArray<FParameterGroupTableData> ACatalogPlayerController::GetParamGroup()
{
	if (ParameterGroup.IsEmpty())
	{
		FRefParamData Data;
		if (FPaths::FileExists(URefToParamDataLibrary::GetRefParamsAddress()))
		{
			UProtobufOperatorFunctionLibrary::LoadRelationFromFile(URefToParamDataLibrary::GetRefParamsAddress(), Data);
		}
		ParameterGroup = Data.ParamGroups;
	}

	return ParameterGroup;
}

TArray<FParameterGroupTableData>& ACatalogPlayerController::GetParamGroupRef()
{
	if (ParameterGroup.IsEmpty())
	{
		GetParamGroup();
	}
	return ParameterGroup;
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ACatalogPlayerController::GetGlobalParameterMapRef()
{
	if (GlobalParameterMap.IsEmpty())
	{
		GetGlobalParameterMap();
	}
	return GlobalParameterMap;
}

TArray<FParameterData> ACatalogPlayerController::GetGlobalParameterArr()
{
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParams = GetGlobalParameterMap();
	TArray<FParameterData> GlobalParamsArr;
	GlobalParams.GenerateValueArray(GlobalParamsArr);
	return GlobalParamsArr;
}

void ACatalogPlayerController::ClearGlobalParamData()
{
	ParameterGroup.Empty();
	GlobalParameterMap.Empty();
}

TArray<FParameterData> ACatalogPlayerController::ConstructGroupParam(const int32& GroupID)
{
	TArray<FParameterData> GroupParams;
	for (const auto Param : GlobalParameterMap)
	{
		if (Param.Value.Data.classific_id == GroupID)
		{
			GroupParams.Add(Param.Value);
		}
	}
	return GroupParams;
}

void ACatalogPlayerController::InsertParam(const FParameterData& Parameters)
{
	GlobalParameterMap.Add(Parameters.Data.name, Parameters);
}

TArray<FParameterData> ACatalogPlayerController::SearchNameToParam(const FString& Str)
{
	TArray<FParameterData> SearchParams;
	for (const auto Param : GlobalParameterMap)
	{
		if (Param.Value.Data.name.Contains(Str,ESearchCase::CaseSensitive)
			|| Param.Value.Data.description.Contains(Str, ESearchCase::CaseSensitive)
			|| Param.Value.Data.param_id.Contains(Str, ESearchCase::CaseSensitive))
		{
			SearchParams.Add(Param.Value);
		}
	}
	return SearchParams;
}

bool ACatalogPlayerController::HasGroupName(const FString& GroupName)
{
	for (const auto PG : ParameterGroup)
	{
		if (GroupName.Equals(PG.group_name, ESearchCase::CaseSensitive))
		{
			return true;
		}
	}
	return false;
}

bool ACatalogPlayerController::IsDescriptionUnique(const FString& Des)
{
	for (const auto GPM : GlobalParameterMap)
	{
		if (Des.Equals(GPM.Value.Data.description, ESearchCase::CaseSensitive))
		{
			return false;
		}
	}

	return true;
}

bool ACatalogPlayerController::IsNameUnique(const FString& Name)
{
	for (const auto GPM : GlobalParameterMap)
	{
		if (Name.Equals(GPM.Value.Data.name, ESearchCase::CaseSensitive))
		{
			return false;
		}
	}

	return true;
}

bool ACatalogPlayerController::IsParamIDUnique(const FString& ID)
{
	for (const auto GPM : GlobalParameterMap)
	{
		if (ID.Equals(GPM.Value.Data.param_id, ESearchCase::CaseSensitive))
		{
			return false;
		}
	}

	return true;
}

void ACatalogPlayerController::UpdateGroupData(const FParameterGroupTableData& GroupData)
{
	for (auto& PG : ParameterGroup)
	{
		if (PG.id == GroupData.id)
		{
			PG = GroupData;
			break;
		}
	}
}

void ACatalogPlayerController::UpdateParamData(const FParameterData& Parameters)
{
	if (GlobalParameterMap.Contains(Parameters.Data.name))
	{
		GlobalParameterMap[Parameters.Data.name] = Parameters;
	}
	else
	{
		GlobalParameterMap.Add(Parameters.Data.name, Parameters);
	}
}

bool ACatalogPlayerController::TakeScreenShot(const FString& FilePath, bool ShowUI)
{
	if (UGameViewportClient::OnScreenshotCaptured().IsBound())
		return false;
	ACatalogPlayerController::ScreenShotPath = FilePath;
	auto ScreenshotCaptureHandler = [&](int32 InWidth, int32 InHeight, const TArray<FColor>& InColor)->void {this->OnScreenShotCompletedHandler(InWidth, InHeight, InColor); };
	ACatalogPlayerController::ScreenShotHandler = UGameViewportClient::OnScreenshotCaptured().AddLambda(ScreenshotCaptureHandler);
	FScreenshotRequest::RequestScreenshot(FilePath, ShowUI, false);
	return true;
}

void ACatalogPlayerController::OnScreenShotCompletedHandler(int32 InWidth, int32 InHeight, const TArray<FColor>& InColor)
{
	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));
	TSharedPtr<IImageWrapper> ImageWrapper;
	FString Extention = FPaths::GetExtension(ACatalogPlayerController::ScreenShotPath);
	if (Extention.Equals(TEXT("png"), ESearchCase::IgnoreCase))
	{
		TArray<uint8> PNGData;
		TArray<FColor> BitmapCopy(InColor);
		FImageUtils::CompressImageArray(InWidth, InHeight, BitmapCopy, PNGData);
		FFileHelper::SaveArrayToFile(PNGData, *ACatalogPlayerController::ScreenShotPath);
	}
	//else if (Extention.Equals(TEXT("jpeg"), ESearchCase::IgnoreCase))
	//{
	//	ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::JPEG);
	//	ImageWrapper->SetRaw(InColor.GetData(), InColor.GetAllocatedSize(), InWidth, InHeight, ERGBFormat::BGRA, 8);
	//	auto& JPEGData = ImageWrapper->GetCompressed(100);
	//	FFileHelper::SaveArrayToFile(JPEGData, *ACatalogPlayerController::ScreenShotPath);
	//}
	//else if (Extention.Equals(TEXT("exr"), ESearchCase::IgnoreCase))
	//{
	//	ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::EXR);
	//	ImageWrapper->SetRaw(InColor.GetData(), InColor.GetAllocatedSize(), InWidth, InHeight, ERGBFormat::BGRA, 8);
	//	auto& Data = ImageWrapper->GetCompressed(100);
	//	FFileHelper::SaveArrayToFile(Data, *ACatalogPlayerController::ScreenShotPath);
	//}
	//else
	//{
	//	//默认使用Jpg格式
	//	ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::JPEG);
	//	ImageWrapper->SetRaw(InColor.GetData(), InColor.GetAllocatedSize(), InWidth, InHeight, ERGBFormat::BGRA, 8);
	//	auto& JPEGData = ImageWrapper->GetCompressed(100);
	//	FFileHelper::SaveArrayToFile(JPEGData, *ACatalogPlayerController::ScreenShotPath);
	//}
	UGameViewportClient::OnScreenshotCaptured().Remove(ACatalogPlayerController::ScreenShotHandler);
	ImageWrapper.Reset();
	OnScreenShotCompletedDelegate.ExecuteIfBound();
}

void ACatalogPlayerController::OnRightMousePressHandler()
{
	bRightMouseButtonDown = true;
}

void ACatalogPlayerController::OnRightMouseReleaseHandler()
{
	bRightMouseButtonDown = false;
	FVector Location;
	FVector Direction;
	if (this->DeprojectMousePositionToWorld(Location, Direction))
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->OnMouseRightButtonClick(Location);
	}
}

void ACatalogPlayerController::OnLeftMousePressHandler()
{
	bLeftMouseButtonDown = true;
}

void ACatalogPlayerController::OnLeftMouseReleaseHandler()
{
	bLeftMouseButtonDown = false;
	FVector Location;
	FVector Direction;
	if (this->DeprojectMousePositionToWorld(Location, Direction))
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->OnMouseLeftButtonClick(Location);
	}
}

void ACatalogPlayerController::OnCtrlSSaveHandler()
{
	if(ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn()))
	{
		CurrentPawn->OnCtrlSSaveHandler();
	}
}

void ACatalogPlayerController::OnMouseXMoveHandler(float Value)
{
	if (!FMath::IsNearlyZero(Value))
	{
		//UE_LOG(LogTemp, Log, TEXT("----- OnMouseXMoveHandler %f  -----"), Value);
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->OnMouseXMove(Value);
	}
}

void ACatalogPlayerController::OnMouseYMoveHandler(float Value)
{
	if (!FMath::IsNearlyZero(Value))
	{
		//UE_LOG(LogTemp, Log, TEXT("----- OnMouseYMoveHandler %f  -----"), Value);
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->OnMouseYMove(Value);
	}
}

void ACatalogPlayerController::OnMouseWheelScrollMoveHandler(float Value)
{
	if (!FMath::IsNearlyZero(Value))
	{
		//UE_LOG(LogTemp, Log, TEXT("----- OnMouseYMoveHandler %f  -----"), Value);
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->OnMouseWheelScroll(Value);
	}
}

void ACatalogPlayerController::OnSwitchPawnCameraToXY()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->ChangeCameraType(ECameraType::EXYPlan2D);
}

void ACatalogPlayerController::OnSwitchPawnCameraToYZ()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->ChangeCameraType(ECameraType::EYZPlan2D);
}

void ACatalogPlayerController::OnSwitchPawnCameraToXZ()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->ChangeCameraType(ECameraType::EXZPlan2D);
}

void ACatalogPlayerController::OnSwitchPawnCameraToDollHouse()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->ChangeCameraType(ECameraType::EDollHouse);
}

void ACatalogPlayerController::OnChangeToArrow()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::A);
}

void ACatalogPlayerController::OnChangeToDrawPoint()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::P);
}

void ACatalogPlayerController::OnDeleteKeyReleaseHandler()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::Delete);
}

void ACatalogPlayerController::OnCtrlSKeyReleaseHandler()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::S);
}

void ACatalogPlayerController::OnCtrlOKeyReleaseHandler()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::O);
}

void ACatalogPlayerController::OnKeyEReleaseHandler()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->OnKeyReleaseHandler(EKeys::E);
}

void ACatalogPlayerController::OpenViewModule(const FCSModelMatData& ViewData)
{
	if (Cast<ACatalogPawn>(this->GetPawn()))
	{
		Cast<ACatalogPawn>(this->GetPawn())->OnViewModuleToShowHandler(ViewData);
	}
}

void ACatalogPlayerController::CatalogLoadDatFile(const FString& FileID)
{
	if (!FileID.IsEmpty())
	{
		const FString& FileAbsPath = URefToFileData::GetFileAddress(FileID);
		if (FPaths::FileExists(FileAbsPath))
		{
			FRefToLocalFileData FileData;
			UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FileAbsPath, FileData);
		}
	}
}

void ACatalogPlayerController::CatalogUploadDatFile(const FString& FileID)
{
	if (!FileID.IsEmpty())
	{
		const FString& FileAbsPath = URefToFileData::GetFileAddress(FileID);
		if (FPaths::FileExists(FileAbsPath))
		{
			const FString& FileRelativePath = URefToFileData::GetFileRelativeAddress(FileID);
			UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
		}
	}
}

void ACatalogPlayerController::CatalogRemoveSameParamsInDatFile(const FString& FileID)
{
	if (!FileID.IsEmpty())
	{
		const FString& FileAbsPath = URefToFileData::GetFileAddress(FileID);
		if (FPaths::FileExists(FileAbsPath))
		{
			FRefToLocalFileData FileData;
			UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FileAbsPath, FileData);

			//remove same params
			FileData.ParamDatas = URefRelationFunction::FormatCleanParams(FileData.ParamDatas);
			UProtobufOperatorFunctionLibrary::SaveRelationToFile(FileAbsPath, FileData);

			//upload
			const FString& FileRelativePath = URefToFileData::GetFileRelativeAddress(FileID);
			UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
		}
	}
}

void ACatalogPlayerController::CatalogLoadDataFileBySelect()
{
	FString FilePath;
	FCatalogFunctionLibrary::OpenFileDialogForDatFile(FilePath);
	if (FPaths::FileExists(FilePath))
	{
		FRefToLocalFileData FileData;
		UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FilePath, FileData);
	}
}

void ACatalogPlayerController::CatalogURLDownload()
{
	FString FilePath;
	FCatalogFunctionLibrary::OpenFileDialogForDatFile(FilePath);
	if (FPaths::FileExists(FilePath) && FilePath.EndsWith(TEXT(".txt")))
	{
		TArray<FString> FileStr;
		FFileHelper::LoadFileToStringArray(FileStr, *FilePath);
		if (FileStr.Num() >= 3)
		{
			const FString& FileURL = FileStr[0];
			const int32& FileType = FCString::Atoi(*FileStr[1]);
			const FString& FileRelativePath = FileStr[2];
			UCatalogNetworkSubsystem::GetInstance()->SendDownloadURLRequest(FileURL, FileType, FileRelativePath);
		}
	}
}

void ACatalogPlayerController::SyncDBDataToFile()
{
	Cast<ACatalogPawn>(GetPawn())->GetCurrentManager()->SyncDBDataToFile();
}

void ACatalogPlayerController::CatalogMultiPrecisionCalculate()
{
	/*
	*  @@ 打开txt文件【内容为计算方程】， 例如：
	*  @@ 5>6&&5>3
	*  @@ sin(if(max(10,20)>50,30,90))
	*  @@ cos(cond(max(10,20)>50,30,min(10,20)<20,90,60))
	*/

	/*FString ExpressionFilePath;
	FCatalogFunctionLibrary::OpenFileDialogForDatFile(ExpressionFilePath);
	if (FPaths::FileExists(ExpressionFilePath) && ExpressionFilePath.EndsWith(TEXT(".txt")))
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GP = GetGlobalParameterMap();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PP;

		TArray<FString> Expressions;
		FFileHelper::LoadFileToStringArray(Expressions, *ExpressionFilePath);
		for (const auto& Exp : Expressions)
		{
			UE_LOG(LogTemp, Warning, TEXT("***************************************************************************************************************"));
			UE_LOG(LogTemp, Log, TEXT("express is [%s]"), *Exp);
			if (Exp.IsEmpty()) continue;

			FString O_V, O_E;
			bool Res1 = UParameterRelativeLibrary::CalculateParameterExpression_Old(GP, PP, Exp, O_V, O_E);
			UE_LOG(LogTemp, Log, TEXT("Old Calculate Is [%s][%s]"), *O_V, *O_E);


			FString N_V, N_E;
			bool Res2 = UParameterRelativeLibrary::CalculateParameterExpression_MultiPrecision(GP, PP, O_E, N_V, N_E);
			UE_LOG(LogTemp, Log, TEXT("New Calculate Is [%s][%s]"), *N_V, *N_E);
			UE_LOG(LogTemp, Warning, TEXT("***************************************************************************************************************"));

		}
	}*/
}

void ACatalogPlayerController::CatalogCalculateSumTolerance()
{
	/*
	 *  @@ 打开txt文件【内容为计算方程】
	 *  @@ 第一行为计算的新变量 SumTol【初始化】, 用于存储每一步计算的值
	 *  @@ 第二行开始后续用此值[SumTol]计算并回填结果
	 *  @@ 最后一行为所有计算的直接计算结果
	 */
	/*FString ExpressionFilePath;
	FCatalogFunctionLibrary::OpenFileDialogForDatFile(ExpressionFilePath);
	if (FPaths::FileExists(ExpressionFilePath) && ExpressionFilePath.EndsWith(TEXT(".txt")))
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GP = GetGlobalParameterMap();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  O_PP;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  N_PP;

		TArray<FString> Expressions;
		FFileHelper::LoadFileToStringArray(Expressions, *ExpressionFilePath);
		UE_LOG(LogTemp, Warning, TEXT("=========================================================================================================================="));
		if(Expressions.Num() > 0)
		{
			FString InitExpression = Expressions[0];
			FString O_V_0, O_E_0;
			bool Res1 = UParameterRelativeLibrary::CalculateParameterExpression_Old(GP, O_PP, InitExpression, O_V_0, O_E_0);
			UE_LOG(LogTemp, Log, TEXT("[0] Old Calculate Is [%s][%s]"), *O_V_0, *O_E_0);

			FParameterData ContinueCal;
			ContinueCal.Data.name = TEXT("SumTol");
			ContinueCal.Data.expression = O_V_0;
			ContinueCal.Data.value = O_V_0;
			O_PP.Add(ContinueCal.Data.name, ContinueCal);


			FString N_V_0, N_E_0;
			bool Res2 = UParameterRelativeLibrary::CalculateParameterExpression_MultiPrecision(GP, N_PP, InitExpression, N_V_0, N_E_0);
			UE_LOG(LogTemp, Log, TEXT("[0] New Calculate Is [%s][%s]"), *N_V_0, *N_E_0);

			ContinueCal.Data.expression = N_V_0;
			ContinueCal.Data.value = N_V_0;
			N_PP.Add(ContinueCal.Data.name, ContinueCal);

		}
		for (int32 i = 1; i < Expressions.Num() - 1; ++i)
		{
			FString Exp = Expressions[i];
			if (Exp.IsEmpty()) continue;

			FString O_V_I, O_E_I;
			bool Res1 = UParameterRelativeLibrary::CalculateParameterExpression_Old(GP, O_PP, Exp, O_V_I, O_E_I);
			UE_LOG(LogTemp, Log, TEXT("[%d] Old Calculate Is [%s][%s]"), i, *O_V_I, *O_E_I);

			FParameterData* TempContinueCal = O_PP.Find(TEXT("SumTol"));
			TempContinueCal->Data.expression = O_V_I;
			TempContinueCal->Data.value = O_V_I;

			FString N_V_I, N_E_I;
			bool Res2 = UParameterRelativeLibrary::CalculateParameterExpression_MultiPrecision(GP, N_PP, Exp, N_V_I, N_E_I);
			UE_LOG(LogTemp, Log, TEXT("[%d] New Calculate Is [%s][%s]"), i, *N_V_I, *N_E_I);

			FParameterData* TempContinueCal2 = N_PP.Find(TEXT("SumTol"));
			TempContinueCal2->Data.expression = N_V_I;
			TempContinueCal2->Data.value = N_V_I;
		}

		FString O_V_L, O_E_L;
		bool Res1 = UParameterRelativeLibrary::CalculateParameterExpression_Old(GP, O_PP, Expressions.Last(), O_V_L, O_E_L);
		UE_LOG(LogTemp, Log, TEXT("[%d] Old Calculate Is [%s][%s]"), Expressions.Num() - 1, *O_V_L, *O_E_L);

		FParameterData* TempContinueCal = O_PP.Find(TEXT("SumTol"));
		TempContinueCal->Data.expression = O_V_L;
		TempContinueCal->Data.value = O_V_L;

		FString N_V_L, N_E_L;
		bool Res2 = UParameterRelativeLibrary::CalculateParameterExpression_MultiPrecision(GP, N_PP, Expressions.Last(), N_V_L, N_E_L);
		UE_LOG(LogTemp, Log, TEXT("[%d] New Calculate Is [%s][%s]"), Expressions.Num() - 1, *N_V_L, *N_E_L);

		FParameterData* TempContinueCal2 = N_PP.Find(TEXT("SumTol"));
		TempContinueCal2->Data.expression = N_V_L;
		TempContinueCal2->Data.value = N_V_L;
		
		UE_LOG(LogTemp, Warning, TEXT("=============================================================================================================================="));
	}*/
}

//void ACatalogPlayerController::AutoTempSaveTest()
//{
//	FRefToLocalFileData FileData;
//	FileData.FolderDBData.id = FGuid::NewGuid().ToString();
//	FileData.FolderDBData.folder_id = TEXT("52000202");
//	FileData.FolderDBData.folder_name = TEXT("测试零时保存");
//	const FString AutoSaveRelativePath = URefToFileData::GetAutoTempSaveFileRelativeAddress(
//		FileData.FolderDBData.id,
//		FileData.FolderDBData.folder_id,
//		FileData.FolderDBData.folder_name,
//		URefRelationFunction::GetCurrentPCData(),
//		URefRelationFunction::GetCurrentPCTime()
//		);
//	const FString AbsPth = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), AutoSaveRelativePath));
//	UProtobufOperatorFunctionLibrary::SaveRelationToFile(AbsPth, FileData);
//}

/*
*  @@ insert data and upload file to olo server
*/

void ACatalogPlayerController::OnKeyMReleaseHandler()
{
#ifdef USE_REF_LOCAL_FILE

	/*
	*  @@ insert data and upload file to olo server
	* 
	*/
	//UFolderWidget::Get()->SyncLocalDBFile();


#else


	/*
	*  @@ old generate single file for test 
	*/

	UFolderWidget* FolderInstance = UFolderWidget::Get();
	FFolderTableData FolderData;
	if(FolderInstance)
	{
		//文件先判断
		if(FolderInstance->GetCurrentSelectFile())
		{
			FolderData = FolderInstance->GetCurrentSelectFile()->GetItemData();
		}
		else if (FolderInstance->GetCurrentSelectItem())
		{
			FolderData = FolderInstance->GetCurrentSelectItem()->GetItemData();
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT(" Select Item is nullptr"));
			return;
		}

		if(FolderData.IsValid())
		{
			TArray<FParameterData> LevelParams;
			bool bSuccess = FLocalDatabaseParameterLibrary::GetCurrentLevelParameters(FolderData.id, FolderData.can_add_subfolder, LevelParams);
			//if(bSuccess)
			{
				//表信息
				FRefDirectoryData DirData;
				URefRelationFunction::ConvertDBDataToDirctoryData(FolderData, DirData);

				//文件
				FRefToLocalFileData DBRefData;
				FCatalogFolderDataDB CatalogDBFolder(
					   FolderData.id,FolderData.folder_id, FolderData.folder_name,
					   FolderData.folder_code, FolderData.folder_code_exp,
					   static_cast<int32>(FolderData.folder_type), FolderData.thumbnail_path, FolderData.parent_id,
					   FolderData.visibility_exp, FolderData.visibility, FolderData.folder_order, FolderData.is_new,
					FolderData.can_add_subfolder, DirData.backendFolderPath, DirData.fontFolderPath
					   );

				//if(!FolderData.can_add_subfolder)
				{
					//文件内部数据填充
					if(FolderData.folder_type == EFolderType::EMultiComponents)
					{
						TArray<FMultiComponentDataItem> MultiComponentsData;
						FMultiComTableOperatorLibrary::RetriveFileMultiComponents(FolderData.id, MultiComponentsData);
						if(MultiComponentsData.Num() > 0)
						{
							TArray<FRefToFileComponentData> RefComponents = URefToFileData::ConvertToRefComponentsData(MultiComponentsData);
							DBRefData.Init(RefComponents);
						}
					}
					else if(FolderData.folder_type == EFolderType::ESingleComponent)
					{
						FComponentFileData FileData;
						FSingleComponentTableData SingleComponent;
						FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(FolderData.id, SingleComponent);
						FileData.file_data_path = SingleComponent.data_path;
						FileData.depend_files = SingleComponent.depend_files;

						//获取内部单部件结构
						if(!SingleComponent.data_path.IsEmpty())
						{
							FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComponent.data_path);
							FSingleComponentProperty SingleComponentProperty;
							if(FPaths::FileExists(AbsPath) && !UProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(AbsPath, SingleComponentProperty))
								UE_LOG(LogTemp, Error, TEXT("Load Single Component File Error"));

							if(SingleComponentProperty.IsValid())
							{
								FileData.Init(SingleComponentProperty);
							}
							DBRefData.Init(FileData);
						}
					}
					
				}
				DBRefData.Init(CatalogDBFolder, LevelParams);

				if(DBRefData.IsValid())
				{
					//生成文件
					FString RefFilePathIdentify = URefToFileData::GetFileAddress(
						DBRefData.FolderDBData.folder_id.IsEmpty() ? DBRefData.FolderDBData.id : DBRefData.FolderDBData.folder_id);
					UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, DBRefData);

					//更新数据库数据
					FString FileMD5 = TEXT("");
					int64 FileSize = 0;
					bool bValid = ACatalogPlayerController::GetFileMD5AndSize(RefFilePathIdentify, FileMD5, FileSize);
					DirData.md5 = FileMD5;

					bool Res = URefToDirectoryDataLibrary::InsertDirectoryInfo(DirData);
					checkf(Res, TEXT("insert db error"));
				}
				
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FolderInstance is nullptr"));
	}

#endif // USE_REF_LOCAL_FILE
}

void ACatalogPlayerController::OnKeyNReleaseHandler()
{
#ifdef USE_REF_LOCAL_FILE

	/*TMap<FString, FRefDirectoryData> RemainDatas = UFolderWidget::Get()->GetSyncDatas();
	for (auto RD : RemainDatas)
	{
		UE_LOG(LogTemp, Warning, TEXT("InsertDataToServerDatabase --- id [%s], folderId [%s], folderName [%s]"),
			*RD.Value.id, *RD.Value.folderId, *RD.Value.folderName);
	}*/

#else

	if(UFolderWidget* FolderInstance = UFolderWidget::Get())
	{
		FFolderTableData FolderData;
		//文件先判断
		if(FolderInstance->GetCurrentSelectFile())
		{
			FolderData = FolderInstance->GetCurrentSelectFile()->GetItemData();
		}
		else if (FolderInstance->GetCurrentSelectItem())
		{
			FolderData = FolderInstance->GetCurrentSelectItem()->GetItemData();
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT(" Select Item is nullptr"));
			return;
		}

		if(FolderData.IsValid())
		{
			//测试id改为folder_id
#ifdef USE_FOLDER_ID_REPLACE_ID
			FString RefFilePathIdentify = URefToFileData::GetFileAddress(FolderData.folder_id);
#else
			FString RefFilePathIdentify = URefToFileData::GetFileAddress(FolderData.id);
#endif
			FRefToLocalFileData DBRefData;
			bool Res = UProtobufOperatorFunctionLibrary::LoadRelationFromFile(RefFilePathIdentify, DBRefData);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FolderInstance is nullptr"));
	}

#endif
}

void ACatalogPlayerController::OnKeyLReleaseHandler()
{//style
	//FRefToStyleFile RefStyleFile;

	////1-获取风格
	//TArray<FDecorateStyle> OutStyle = TArray<FDecorateStyle>();
	//FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_style ORDER BY SORT_ORDER ASC"), OutStyle);
	//for(auto& Iter : OutStyle)
	//{
	//	auto& NewStyle = RefStyleFile.style_datas.AddDefaulted_GetRef();
	//	FString LocalMD5 = TEXT("");
	//	int64 FileSize = 0;
	//	if (!Iter.thumbnail_path.IsEmpty())
	//	{
	//		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + Iter.thumbnail_path);
	//		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
	//	}
	//	NewStyle.Init(Iter.id, Iter.code, Iter.craft, Iter.description, Iter.thumbnail_path, LocalMD5, Iter.sort_order, Iter.checked);
	//}

	////2-获取内容
	//TArray<FDecorateContent> OutContent = TArray<FDecorateContent>();
	//FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_content ORDER BY SORT_ORDER ASC"), OutContent);
	//for(auto& Iter : OutContent)
	//{
	//	auto& NewContent = RefStyleFile.content_datas.AddDefaulted_GetRef();
	//	NewContent.Init(Iter.id, Iter.name, Iter.sort_order);
	//}

	////3-获取选项
	//TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
	//FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel ORDER BY SORT_ORDER ASC")), OutSelection);

	////选项按照内容进行分类
	//TMap<FString, TArray<FRefToOptionData>> ContentOptionMap;
	//for(auto& Iter : OutSelection)
	//{
	//	if(!Iter.id.IsEmpty() && !Iter.parent_id.IsEmpty())
	//	{
	//		//选项参数
	//		TArray<FParameterData> OptionParams;
	//		TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *Iter.id), OutParamsTableData);
	//		for (auto TableDataIter : OutParamsTableData)
	//		{
	//			FParameterData Temp = FParameterData();
	//			TArray<FEnumParameterTableData> OutEnumData = TArray<FEnumParameterTableData>();
	//			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *TableDataIter.id), OutEnumData);
	//			Temp.Data = TableDataIter;
	//			Temp.EnumData = OutEnumData;
	//			OptionParams.Add(Temp);
	//		}

	//		if(ContentOptionMap.Contains(Iter.parent_id))
	//		{
	//			auto& NewOption = ContentOptionMap[Iter.parent_id].AddDefaulted_GetRef();
	//			NewOption.Init(
	//				Iter.id, 
	//				Iter.code, 
	//				Iter.description,
	//				TEXT(""),
	//				Iter.visibility,
	//				Iter.visibility_exp, 
	//				Iter.sort_order, 
	//				Iter.parent_id
	//			);
	//			NewOption.Init(OptionParams);
	//		}
	//		else
	//		{
	//			FRefToOptionData NewOption;
	//			NewOption.Init(
	//				Iter.id,
	//				Iter.code,
	//				Iter.description,
	//				TEXT(""),
	//				Iter.visibility,
	//				Iter.visibility_exp,
	//				Iter.sort_order,
	//				Iter.parent_id
	//			);
	//			NewOption.Init(OptionParams);
	//			ContentOptionMap.Add(Iter.parent_id, { NewOption });
	//		}
	//	}
	//}
	////将选项填充到内容中
	//for(auto& Iter : ContentOptionMap)
	//{
	//	if(!Iter.Key.IsEmpty())
	//	{
	//		const int32 ContentIndex = RefStyleFile.content_datas.IndexOfByPredicate(
	//			[Iter](const FRefToContentData& InContent)->bool
	//			{
	//				return InContent.content_id.Equals(Iter.Key);
	//			}
	//		);
	//		if(ContentIndex != INDEX_NONE)
	//		{
	//			RefStyleFile.content_datas[ContentIndex].InitOptions(Iter.Value);
	//		}
	//	}
	//	
	//}

	////4-获取选项组
	//TArray<FDecoSelectionCheckData> OutSelectionChecks = TArray<FDecoSelectionCheckData>();
	//FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch")), OutSelectionChecks);

	//TMap<FString, TMap<FString, FOptionCheckArr>> Content_StyleOptionCheckMap;
	////分组
	//for(auto& Iter : OutSelectionChecks)
	//{
	//	if(!Iter.style_id.IsEmpty() && !Iter.content_id.IsEmpty() && !Iter.selection_id.IsEmpty())
	//	{
	//		FRefToOptionCheck NewCheck(Iter.selection_id, Iter.content_id, Iter.style_id, Iter.is_prime);
	//		if(Content_StyleOptionCheckMap.Contains(Iter.content_id))
	//		{
	//			if(Content_StyleOptionCheckMap[Iter.content_id].Contains(Iter.style_id))
	//			{
	//				Content_StyleOptionCheckMap[Iter.content_id][Iter.style_id].Add(NewCheck);
	//			}
	//			else
	//			{
	//				FOptionCheckArr CheckArr;
	//				CheckArr.Add(NewCheck);
	//				Content_StyleOptionCheckMap[Iter.content_id].Add(Iter.style_id, CheckArr);
	//			}
	//		}
	//		else
	//		{
	//			FOptionCheckArr CheckArr;
	//			CheckArr.Add(NewCheck);
	//			Content_StyleOptionCheckMap.Add(Iter.content_id, { { Iter.style_id, CheckArr}});
	//		}
	//	}
	//}
	////将选项填充到内容中
	//for (auto& Iter : Content_StyleOptionCheckMap)
	//{
	//	if (!Iter.Key.IsEmpty())
	//	{
	//		const int32 ContentIndex = RefStyleFile.content_datas.IndexOfByPredicate(
	//			[Iter](const FRefToContentData& InContent)->bool
	//			{
	//				return InContent.content_id.Equals(Iter.Key);
	//			}
	//		);
	//		if (ContentIndex != INDEX_NONE)
	//		{
	//			RefStyleFile.content_datas[ContentIndex].InitChecks(Iter.Value);
	//		}
	//	}
	//}

	////测试id改为folder_id
	//FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	//UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, RefStyleFile);

}

void ACatalogPlayerController::OnKeyKReleaseHandler()
{
	/*FRefToStyleFile RefStyleFile;
	FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	UProtobufOperatorFunctionLibrary::LoadRelationFromFile(RefFilePathIdentify, RefStyleFile);*/

	UFolderWidget::Get()->FormatStyleRefData();
}

void ACatalogPlayerController::OnKeyJReleaseHandler()
{
	//UFolderWidget::Get()->UploadFileRequest(URefToStyleDataLibrary::GetStyleRelativeAddress());

	/*TArray<FParameterGroupTableData> ParametersGroup;
	FLocalDatabaseParameterLibrary::RetriveParameterGroups(ParametersGroup);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalData;
	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalData);

	FRefParamData RefParamData;
	RefParamData.Init(GlobalData);
	RefParamData.Init(ParametersGroup);

	const FString RefFilePathIdentify = URefToParamDataLibrary::GetRefParamsAddress();
	UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, RefParamData);

	UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(URefToParamDataLibrary::GetRelativePath());*/
}

void ACatalogPlayerController::ExitGame()
{
	ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
	CurrentPawn->ExitGame();
}

void ACatalogPlayerController::WindowSizeChanged()
{
	auto SizeChangedTimer = [&]()->void {
		UE_LOG(LogTemp, Log, TEXT("ACatalogPlayerController::WindowSizeChanged   SizeChangedTimer"));
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(this->GetPawn());
		CurrentPawn->WindowSizeChanged();
	};
	FTimerHandle	TimeHandle;
	GetWorldTimerManager().SetTimer(TimeHandle, FTimerDelegate::CreateLambda(SizeChangedTimer), 0.1f, false);
}

#ifdef USE_REF_LOCAL_FILE
#else
FString ACatalogPlayerController::SendLoginCheckRequest(const FString& InUserName, const FString& InPassword)
{
	FString URL = FString::Printf(TEXT("%slogin/userLogin"), SERVER_URL);
	FString Content = FString::Printf(TEXT("{\"password\" : \"%s\" , \"userName\" : \"%s\"}"), *InPassword, *InUserName);
	FString UUID = FGuid::NewGuid().ToString();
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Content);
	BP_SendJsonStringNetworkRequest(URL, Content, LOGIN_REQUEST, UUID);
	return UUID;
}

void ACatalogPlayerController::LoginCheckResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	ACatalogPlayerController* pThis = static_cast<ACatalogPlayerController*>(pData);
	if (pThis)
	{
		UE_LOG(LogTemp, Warning, TEXT("%s"), *InJson);

		FUserInfoTableDataMsg UserInfo;
		UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FUserInfoTableDataMsg>(InJson, UserInfo);
		AccessToken = UserInfo.resp.token;
		bool LoginSuccess = (NetworkStatus == NET_STATE_OK) && UserInfo.success;
		pThis->LoginResponseDelegate.Broadcast(InUUID, LoginSuccess, UserInfo.resp);
	}
}

FString ACatalogPlayerController::NetLogoutRequest()
{
	AccessToken = TEXT("");
	return FString();
}

FString ACatalogPlayerController::DownloadFileRequest(const FString& FilePath)
{
	TArray<FString> FilesToDownload;
	FilesToDownload.Add(FilePath);
	return DownloadMultiFilesRequest(FilesToDownload);
}

FString ACatalogPlayerController::DownloadMultiFilesRequest(const TArray<FString>& FilePaths)
{
	FString UUID = FGuid::NewGuid().ToString().ToLower();
	bool Downloaded = false;
	for (auto& FilePath : FilePaths)
	{
		if (FilePath.Len() > 4)
		{
			Downloaded = true;
			DownloadFilesQueue.AddFile(UUID, FilePath);
			UE_LOG(LogTemp, Warning, TEXT("ACatalogPlayerController::DownloadMultiFilesRequest StartDownload @ %s"), *FilePath);
			StartDownloadFile(UUID, FilePath);
		}
	}
	return Downloaded ? UUID : TEXT("");
}

bool ACatalogPlayerController::StartDownloadFile(const FString& UUID, const FString& FilePath)
{
	const float CurrentTime = UKismetSystemLibrary::GetGameTimeInSeconds(this);
	for (int i = 0; i < MAX_DOWNLOAD_UPLOAD_THREAD; ++i)
	{
		if (DownloadThreadInfo[i].IsValid())
		{
			const FString FileExtention = FPaths::GetExtension(DownloadThreadInfo[i]->FilePath);
			const float Timeout = FileExtention.Equals(TEXT("pak"), ESearchCase::IgnoreCase) ? 40.0f : 10.0f;//PAK文件较大下载用时较久
			if (CurrentTime - DownloadThreadInfo[i]->StartTime > Timeout)
			{
				TArray<FString> Headers;
				Headers.Add(FString::Printf(TEXT("uuid:%s"), *DownloadThreadInfo[i]->FilePath));
				OnNetworkRequestCompleted(TArray<FString>(), Headers, 0, TArray<uint8>(), FString::Printf(TEXT("%s-%s"), DOWNLOAD_FILE_REQUEST, *DownloadThreadInfo[i]->FilePath));
			}
		}
		if (false == DownloadThreadInfo[i].IsValid())
		{
			if (FilePath.IsEmpty()) return TEXT("");
			FString RequestType = FString::Printf(TEXT("%s-%s"), DOWNLOAD_FILE_REQUEST, *FilePath);
			FString PercentURL = FString::Printf(TEXT("{\"filePath\": \"%s\"}"), *FilePath);
			//FString PercentURL = UJsonUtilitiesLibrary::ConvertToPercentURL(FilePath);
			FString FileABPath = FPaths::ProjectContentDir() + FilePath;
			FileABPath = FPaths::ConvertRelativePathToFull(FileABPath);
			BP_DownloadFileFromServer(FString::Printf(TEXT("%sapi/file/download"), SERVER_URL), FileABPath, RequestType, UUID, PercentURL);
			DownloadThreadInfo[i] = MakeShared<FDownloadUploadThreadInfo>(UUID, FilePath);
			DownloadThreadInfo[i]->StartTime = CurrentTime;
			DownloadFilesQueue.Update(UUID, DownloadThreadInfo[i]->FilePath, NFileNetworkOpertator::EDownloadState::Downloading);
			return true;
		}
	}
	return false;
}

FString ACatalogPlayerController::UploadFileRequest(const FString& FilePath)
{
	FString FileMD5 = TEXT("");
	int64 FileSize = 0;
	const FString FileABPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + FilePath);
	{
		bool bValid = ACatalogPlayerController::GetFileMD5AndSize(FileABPath, FileMD5, FileSize);
		if (!bValid) return TEXT("");
	}
	FString RequestType = FString::Printf(TEXT("%s-%s"), UPLOAD_FILE_REQUEST, *FilePath);
	BP_UploadFileToServer(FString::Printf(TEXT("%sapi/file/upload?md5=%s&path=%s&size=%ld"), SERVER_URL, *FileMD5, *FPaths::GetPath(FilePath), FileSize), FileABPath, RequestType, FilePath);
	return FilePath;
}
#endif

bool ACatalogPlayerController::NetworkStatusCheck(const int32& NetCode, const FString& MessageName)
{
	if (NetCode != 0 && 200 != NetCode)
	{
		UE_LOG(LogTemp, Error, TEXT("Request %s error, error code is %d"), *MessageName, NetCode);
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
			FString(TEXT("NetworkError"))
			, FString(TEXT("NetworkError Code : ") + FString::FromInt(NetCode)));
		return false;
	}
	return true;
}

#ifdef USE_REF_LOCAL_FILE
#else
FString ACatalogPlayerController::SendQueryReleaseLogRequest(const int32& InPageNum, const int32& InPageSize, const int32& InType)
{
	FString URL = FString::Printf(TEXT("%sapi/releaseLog/releaseLogQueryByPage"), SERVER_URL);
	FString Content = FString::Printf(TEXT("{\"page\":\"%d\",\"size\":\"%d\", \"releasetype\":\"%d\"}"), InPageNum, InPageSize, InType);
	FString uuid = FGuid::NewGuid().ToString().ToLower();
	BP_SendJsonStringNetworkRequest(URL, Content, QUERY_RELEASE_LOG, uuid);
	return uuid;
}

void ACatalogPlayerController::OnQueryReleaseLogResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(LogTemp, Warning, TEXT("%s"), *InJson);

	if (!NetworkStatusCheck(NetworkStatus, TEXT("OnQueryReleaseLogResponseHandler")))
	{
		return;
	}
	ACatalogPlayerController* pThis = static_cast<ACatalogPlayerController*>(pData);
	if (pThis)
	{
		FReleaseLogDataMsg LogDataMsg;

		if (UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FReleaseLogDataMsg>(InJson, LogDataMsg))
		{
			pThis->ReleaseResponseDelegate.Broadcast(InUUID, LogDataMsg.resp);
		}
	}
}

FString ACatalogPlayerController::MergeInsertRequest(const FString& DBAfterUrl, const FString& DBAfterVersion, const FString& DBBeforeUrl, const FString& DBBeforeVersion, const FString& Id, const FString& MergeTime, const FString& Remark, const FString& UpdateTime, const FString& UserId, const FString& UserName, const FString& Operation)
{
	FString URL = FString::Printf(TEXT("%sapi/mergeLog/mergeInsert"), SERVER_URL);
	FString Content = FString::Printf(TEXT("{\"dbAfterUrl\" : \"%s\" , \"dbAfterVersion\" : \"%s\" , \"dbBeforeUrl\"  : \"%s\" , \"dbBeforeVersion\" : \"%s\" , \"id\" : \"%s\" , \"mergeTime\" : \"%s\" , \"operation\" : \"%s\", \"remark\" : \"%s\" , \"updateTime\" : \"%s\" , \"userId\" : \"%s\" , \"userName\" : \"%s\"}"), *DBAfterUrl, *DBAfterVersion, *DBBeforeUrl, *DBBeforeVersion, *Id, *MergeTime, *Operation, *Remark, *UpdateTime, *UserId, *UserName);
	FString UUID = FGuid::NewGuid().ToString();
	BP_SendJsonStringNetworkRequest(URL, Content, MERGE_INSERT_LOG, UUID);
	return UUID;
}

void ACatalogPlayerController::OnMergeInsertResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	FMergeInsertDataMsg DataMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FMergeInsertDataMsg>(InJson, DataMsg);
	MergeInsertLogResponseDelegate.Broadcast(InUUID, DataMsg.resp);
}

FString ACatalogPlayerController::SearchMergeLogRequset(const int32& Page, const int32& Size)
{
	FString URL = FString::Printf(TEXT("%sapi/mergeLog/mergeQueryByPage"), SERVER_URL, *AccessToken);
	FString Content = FString::Printf(TEXT("{\"page\" : \"%d\" , \"size\" : \"%d\"}"), Page, Size);
	FString UUID = FGuid::NewGuid().ToString();
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Content);
	BP_SendJsonStringNetworkRequest(URL, Content, SEARCH_MERGE_LOG, UUID);
	return UUID;
}

void ACatalogPlayerController::OnSearchMergeLogResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	FMergePageDataMsg DataMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FMergePageDataMsg>(InJson, DataMsg);
	SearchMergeLogResponseDelegate.Broadcast(InUUID, DataMsg.resp);
}

FString ACatalogPlayerController::ReleaseRequest(const FString& InMd5, const int32& InType)
{
	FString URL = FString::Printf(TEXT("%s/api/release/releaseFile"), SERVER_URL);
	FString Content = FString::Printf(TEXT("{\"dbMd5\":\"%s\", \"releasetype\":\"%d\"}"), *InMd5, InType);
	FString UUID = FGuid::NewGuid().ToString();
	BP_SendJsonStringNetworkRequest(URL, Content, RELEASE, UUID);
	return UUID;
}

void ACatalogPlayerController::OnReleaseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	if (!NetworkStatusCheck(NetworkStatus, TEXT("OnReleaseAllResponseHandler")))
	{
		return;
	}
	ACatalogPlayerController* pThis = static_cast<ACatalogPlayerController*>(pData);
	FResponsetDataMsg DataMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FResponsetDataMsg>(InJson, DataMsg);
	if (pThis)
	{
		pThis->ReleaseAllDelegate.Broadcast(InUUID, DataMsg.resp);
	}
}
#endif

void ACatalogPlayerController::Convert_CSDB_To_DSDB()
{
#ifdef WITH_EDITOR
	/*FString CSDBPath = FString(TEXT("Cache/local_cache_1.db"));
	FString CSServerDBPath = FString(TEXT("Cache/server_cache.db"));
	FString DSDBPath = FString(TEXT("Cache/cache_data.db"));
	FReleaseLocalDatabaseLibrary ReleaseLibrary;
	ReleaseLibrary.ReleaseLocalDatabase(
		FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + CSDBPath),
		FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + DSDBPath)
	);*/
#endif
}

bool ACatalogPlayerController::GetFileMD5AndSize(const FString& InFilePath, FString& OutMd5, int64& OutSize)
{
	FArchive* reader = IFileManager::Get().CreateFileReader(*InFilePath);
	if (!reader) {
		return false;
	}

	TArray<uint8> byteArrayTmp;
	OutSize = reader->TotalSize();
	if (OutSize <= 0)
	{//文件为空
		reader->Close();
		delete reader;

        OutMd5 = FMD5::HashAnsiString(TEXT(""));
        OutSize = 0;
		return false;
	}
	int64 loadedBytes = 0;
	int64 leftUploadBytes = 1024;


	if (OutSize < leftUploadBytes)
		leftUploadBytes = OutSize;


	uint8 Digest[16];
	FMD5 Md5Gen;

	while ((loadedBytes + leftUploadBytes) <= OutSize) {
		byteArrayTmp.Reset(leftUploadBytes);
		byteArrayTmp.AddUninitialized(leftUploadBytes);
		reader->Serialize(byteArrayTmp.GetData(), byteArrayTmp.Num());
		loadedBytes += leftUploadBytes;
		reader->Seek(loadedBytes);

		Md5Gen.Update(byteArrayTmp.GetData(), byteArrayTmp.Num());
	}

	leftUploadBytes = OutSize - loadedBytes;
	if (leftUploadBytes > 0) {
		byteArrayTmp.Reset(leftUploadBytes);
		byteArrayTmp.AddUninitialized(leftUploadBytes);
		reader->Serialize(byteArrayTmp.GetData(), byteArrayTmp.Num());
		loadedBytes += leftUploadBytes;
		Md5Gen.Update(byteArrayTmp.GetData(), byteArrayTmp.Num());
	}

	if (reader != nullptr) {
		reader->Close();
		delete reader;
	}

	if (OutSize != loadedBytes) {
		UE_LOG(LogTemp, Warning, TEXT("OutSize %d , loadedBytes %d"), OutSize, loadedBytes);
		reader->Close();
		delete reader;

		return false;
	}

	Md5Gen.Final(Digest);
	OutMd5.Empty();
	for (int32 i = 0; i < 16; i++) {
		OutMd5 += FString::Printf(TEXT("%02x"), Digest[i]);
	}
	return true;
}

#ifdef USE_REF_LOCAL_FILE
#else
#undef LOGIN_REQUEST
#undef UPLOAD_FILE_REQUEST
#undef DOWNLOAD_FILE_REQUEST
#undef QUERY_RELEASE_LOG
#undef MERGE_INSERT_LOG
#undef SEARCH_MERGE_LOG
#undef Release
#undef NET_STATE_OK
#endif