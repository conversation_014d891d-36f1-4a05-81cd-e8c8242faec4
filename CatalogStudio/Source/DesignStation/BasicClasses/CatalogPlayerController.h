// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "CatalogPlayerController.generated.h"

/**
 *
 */

#ifdef USE_REF_LOCAL_FILE
#else

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FLoginResponseDelegate, const FString&, UUID, bool, IsSucess, const FUserInfoTableData&, OutUserInfo);
//DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FLoginResponseDelegate, const FString&, UUID, const int32&, code, const FUserInfoTableData&, Userinfo);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FDownloadFileProcessResponseDelegate, const FString&, UUID, float, OutProcess, const FString&, OutFilePath);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FDownloadFileResponseDelegate, const FString&, UUID, bool, OutRes, const TArray<FString>&, OutFilePath);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FUploadFileProcessResponseDelegate, const FString&, UUID, float, OutProcess, const FString&, OutFilePath);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FUploadFileResponseDelegate, bool, OutRes, const FString&, OutFilePath);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FReleaseMultiComponentDelegate, const FString&, UUID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FReleaseComponentDelegate, const FString&, UUID,bool , bSuccess);


DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FReleaseResponseDelegate, const FString&, UUID, const FReleaseLogDataPage&, OutParam);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FMergeInsertLogResponseDelegate, const FString&, UUID, const int32&, InsertId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FSearchMergeLogResponseDelegate, const FString&, UUID, const FMergePageData&, PageData);

#endif

#ifdef USE_REF_LOCAL_FILE
#else

//UENUM(BlueprintType)
enum class ERequestType : uint8
{
	ENone = 0,
	ELoginRequest,
	EUploadFile,
	EDownloadFile,
	EQueryReleaseLog,
	EMergeInsertLog,
	ESearchMergeLog,
	ERelease
};


#define MAX_DOWNLOAD_UPLOAD_THREAD	(5)

#endif

struct FCSModelMatData;
class UCatalogNetworkSubsystem;

UCLASS()
class DESIGNSTATION_API ACatalogPlayerController : public APlayerController
{
	GENERATED_BODY()

private:
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameterMap;

	UPROPERTY()
	TArray<FParameterGroupTableData> ParameterGroup;

	bool bRightMouseButtonDown;

	bool bLeftMouseButtonDown;

#ifdef USE_REF_LOCAL_FILE
#else
	FGlobalData GlobalData;

	//UPROPERTY(BlueprintReadOnly, Category = "CatalogPlayerController", meta = (AllowPrivateAccess = true))
		FString AccessToken = TEXT("");

	TMap<FString, ERequestType>	RequestTypeMap;
#endif

	static ACatalogPlayerController* PCInstance;

	FString	ScreenShotPath;
	FDelegateHandle ScreenShotHandler;
	bool bReleaseLock;

#ifdef USE_REF_LOCAL_FILE
#else
	struct FDownloadUploadThreadInfo
	{
		FString	UUID;
		FString FilePath;
		float	StartTime;
		FDownloadUploadThreadInfo() :UUID(""), FilePath(""), StartTime(0.0f) {}
		FDownloadUploadThreadInfo(const FString& strUUID, const FString& strFilePath) :UUID(strUUID), FilePath(strFilePath) {}
		~FDownloadUploadThreadInfo() { }
	};

	TSharedPtr<FDownloadUploadThreadInfo>	DownloadThreadInfo[MAX_DOWNLOAD_UPLOAD_THREAD];
	NFileNetworkOpertator::FDownloadingFilesQueue	DownloadFilesQueue;

	int32 IndexOfUUID(const FString& InUUID) const {
		for (int32 i = 0; i < MAX_DOWNLOAD_UPLOAD_THREAD; ++i)
		{
			if (DownloadThreadInfo[i].IsValid() && InUUID == DownloadThreadInfo[i]->UUID)
			{
				return i;
			}
		}
		return INDEX_NONE;
	}
	bool StartDownloadFile(const FString& UUID, const FString& FilePath);
#endif

public:

	FSimpleDelegate	OnScreenShotCompletedDelegate;

public:
	ACatalogPlayerController();

	virtual void SetupInputComponent() override;

	void SetPostProcessType(const int32& InPPM);


	UFUNCTION(BlueprintCallable, Category = "CatalogPlayerController|Network")
		FString GetAccessToken() const;

	UFUNCTION(BlueprintCallable, Category = "CatalogPlayerController")
	UCatalogNetworkSubsystem* GetNetworkSubsystem();

	UFUNCTION(BlueprintCallable, Category = "CatalogPlayerController")
		bool IsAdminLogin() const;

	UFUNCTION(BlueprintCallable, Category = "CatalogPlayerController")
	TArray<FString> GetStyleCraft();


	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetGlobalParameterMap();
	TArray<FParameterGroupTableData> GetParamGroup();
	TArray<FParameterGroupTableData>& GetParamGroupRef();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GetGlobalParameterMapRef();
	TArray<FParameterData> GetGlobalParameterArr();
	void ClearGlobalParamData();

	TArray<FParameterData> ConstructGroupParam(const int32& GroupID);
	void InsertParam(const FParameterData& Parameters);
	TArray<FParameterData> SearchNameToParam(const FString& Str);

	bool HasGroupName(const FString& GroupName);
	bool IsDescriptionUnique(const FString& Des);
	bool IsNameUnique(const FString& Name);
	bool IsParamIDUnique(const FString& ID);


	void UpdateGroupData(const FParameterGroupTableData& GroupData);
	void UpdateParamData(const FParameterData& Parameters);

	//Network

	/*
	 *  @@ GET
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "CatalogPlayerController|Network")
		void BP_SendJsonNetworkRequest(const FString& InURL, const TArray<uint8>& InContent, const FString& InRequestType, const FString& InUUID);

	UFUNCTION(BlueprintImplementableEvent, Category = "CatalogPlayerController|Network")
		void BP_DownloadFileFromServer_Get(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID, const FString& InRelativeFilePath);

	/*
	 *  @@ POST
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "CatalogPlayerController|Network")
		void BP_SendJsonStringNetworkRequest(const FString& InURL, const FString& InContent, const FString& InRequestType, const FString& InUUID);

	UFUNCTION(BlueprintCallable, Category = "CatalogPlayerController|Network")
		bool OnNetworkRequestCompleted(const TArray<FString>& InJsonDataString, const TArray<FString>& InHeaders, const int32& InStatusCode, const TArray<uint8>& InDataBytes, const FString& InRequestType);

	UFUNCTION(BlueprintImplementableEvent, Category = "CatalogPlayerController|Network")
		void BP_UploadFileToServer(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID);

	UFUNCTION(BlueprintImplementableEvent, Category = "CatalogPlayerController|Network")
		void BP_DownloadFileFromServer(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID, const FString& InRelativeFilePath);


	GenerateGetConstRef(bool, bLeftMouseButtonDown)

#ifdef USE_REF_LOCAL_FILE
#else
	GenerateGetConstRef(FGlobalData, GlobalData)
	GenerateGetRef(FGlobalData, GlobalData)
#endif

	static ACatalogPlayerController* Get();

	// Called every frame
	virtual void Tick(float DeltaSeconds) override;

	void ExitGame();

	void WindowSizeChanged();

	bool TakeScreenShot(const FString& FilePath, bool ShowUI = false);

	void OnScreenShotCompletedHandler(int32 InWidth, int32 InHeight, const TArray<FColor>& InColor);

	//view module to parse the fbx or material to show
	void OpenViewModule(const FCSModelMatData& ViewData);

#pragma region CUSTOM_EXEC
	
	//test exec function
	UFUNCTION(Exec, Category = "Load dat File")
		void CatalogLoadDatFile(const FString& FileID);

	UFUNCTION(Exec, Category = "Upload dat File")
		void CatalogUploadDatFile(const FString& FileID);

	UFUNCTION(Exec, Category = "Remove Same Params In File")
		void CatalogRemoveSameParamsInDatFile(const FString& FileID);

	UFUNCTION(Exec, Category = "Load File Data To Check")
		void CatalogLoadDataFileBySelect();

	UFUNCTION(Exec, Category = "URL Download")
		void CatalogURLDownload();

	UFUNCTION(Exec, Category = "Sync DB And File Data")
		void SyncDBDataToFile();


	//old parameter test ---- abandon
	UFUNCTION(Exec, Category = "Multi Precision")
		void CatalogMultiPrecisionCalculate();
	UFUNCTION(Exec, Category = "Multi Precision")
		void CatalogCalculateSumTolerance();

#pragma endregion

protected:

	virtual void BeginPlay() override;

	void OnRightMousePressHandler();

	void OnRightMouseReleaseHandler();

	void OnLeftMousePressHandler();

	void OnLeftMouseReleaseHandler();

	UFUNCTION()
	void OnCtrlSSaveHandler();

	void OnMouseXMoveHandler(float Value);

	void OnMouseYMoveHandler(float Value);

	void OnMouseWheelScrollMoveHandler(float Value);

	void OnSwitchPawnCameraToXY();

	void OnSwitchPawnCameraToYZ();

	void OnSwitchPawnCameraToXZ();

	void OnSwitchPawnCameraToDollHouse();

	void OnChangeToArrow();

	void OnChangeToDrawPoint();

	void OnDeleteKeyReleaseHandler();

	void OnCtrlSKeyReleaseHandler();

	void OnCtrlOKeyReleaseHandler();

	void OnKeyEReleaseHandler();

	

	//测试
	void OnKeyMReleaseHandler();
	void OnKeyNReleaseHandler();
	void OnKeyLReleaseHandler();
	void OnKeyKReleaseHandler();
	void OnKeyJReleaseHandler();

public:

#ifdef USE_REF_LOCAL_FILE

#else
	FString SendLoginCheckRequest(const FString& InUserName, const FString& InPassword);
	void LoginCheckResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson);


	FString NetLogoutRequest();

	//UFUNCTION(BlueprintCallable)
		FString DownloadFileRequest(const FString& FilePath);
	FString DownloadMultiFilesRequest(const TArray<FString>& FilePaths);

	FString UploadFileRequest(const FString& FilePath);

#endif

	//获取文件的MD5值，当文件大于2GB时会失败
	//InFilePath是文件的绝对路径
	static bool GetFileMD5AndSize(const FString& InFilePath, FString& OutMd5, int64& OutSize);

	static bool NetworkStatusCheck(const int32& NetCode, const FString& MessageName);

#ifdef USE_REF_LOCAL_FILE
#else

	//发布日志
	FString SendQueryReleaseLogRequest(const int32& InPageNum, const int32& InPageSize, const int32& InType = 0);
	static void OnQueryReleaseLogResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson);

	FString MergeInsertRequest(const FString& DBAfterUrl, const FString& DBAfterVersion, const FString& DBBeforeUrl, const FString& DBBeforeVersion, const FString& Id, const FString& MergeTime, const FString& Remark, const FString& UpdateTime, const FString& UserId, const FString& UserName, const FString& Operation = TEXT("upload"));
	void OnMergeInsertResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson);

	FString SearchMergeLogRequset(const int32& Page, const int32& Size);
	void OnSearchMergeLogResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson);

	FString ReleaseRequest(const FString& InMd5,const int32& InType = 0);
	void OnReleaseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson);
#endif

	/**
	 *  @@ 用于直接转换发布数据库，不要使用
	 */
	UFUNCTION(BlueprintCallable, Category = " Test Func")
		void Convert_CSDB_To_DSDB();

	bool GetReleaseLock() { return bReleaseLock; }
	void SetReleaseLock(bool bLock) { bReleaseLock = bLock; }

#ifdef USE_REF_LOCAL_FILE
#else

public:
	FLoginResponseDelegate LoginResponseDelegate;
	FDownloadFileProcessResponseDelegate DownloadFileProcessResponseDelegate;
	//UPROPERTY(BlueprintAssignable)
		FDownloadFileResponseDelegate DownloadFileResponseDelegate;
	FUploadFileProcessResponseDelegate UploadFileProcessResponseDelegate;
	FUploadFileResponseDelegate UploadFileResponseDelegate;

	//release
	FReleaseMultiComponentDelegate ReleaseInsertDelegate;
	FReleaseResponseDelegate	 ReleaseResponseDelegate;
	FReleaseComponentDelegate ReleaseAllDelegate;

	FMergeInsertLogResponseDelegate MergeInsertLogResponseDelegate;
	FSearchMergeLogResponseDelegate SearchMergeLogResponseDelegate;

#endif
};
