// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogStudioGameEngine.h"
#include "CatalogStudioGameInstance.h"
#include "Runtime/Core/Public/Windows/WindowsPlatformMisc.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"


//void UCatalogStudioGameEngine::GameWindowClosed(const TSharedRef<SWindow>& WindowBeingClosed)
//{
//	UCatalogStudioGameInstance* GI = Cast<UCatalogStudioGameInstance>(GameInstance);
//	if (nullptr != GI && GI->IsValidLowLevel())
//	{
//		GI->ExitGame();
//	}
//	UE_LOG(LogTemp, Log, TEXT("UCatalogStudioGameEngine::GameWindowClose"));
//	UGameEngine::GameWindowClosed(WindowBeingClosed);
//}
//
//void UCatalogStudioGameEngine::GameWindowSizeChanged(const TSharedRef<SWindow>& WindowBeingChanged, const int32& Width, const int32& Height)
//{
//	UCatalogStudioGameInstance* GI = Cast<UCatalogStudioGameInstance>(GameInstance);
//	if (nullptr != GI && GI->IsValidLowLevel())
//	{
//		GI->WindowSizeChanged();
//	}
//	UGameEngine::GameWindowSizeChanged(WindowBeingChanged, Width, Height);
//}

void UCatalogStudioGameEngine::PreExit()
{
	UE_LOG(LogTemp, Log, TEXT("UCatalogStudioGameEngine::PreExit"));
	UCatalogStudioGameInstance* GI = Cast<UCatalogStudioGameInstance>(GameInstance);
	if (nullptr != GI && GI->IsValidLowLevel())
	{
		GI->UserCloseApp();
	}
}

void UCatalogStudioGameEngine::Init(class IEngineLoop* InEngineLoop)
{
	UGameEngine::Init(InEngineLoop);
}