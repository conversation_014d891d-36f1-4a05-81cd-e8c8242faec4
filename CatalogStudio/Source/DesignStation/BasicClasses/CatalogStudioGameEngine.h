// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameEngine.h"
#include "CatalogStudioGameEngine.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UCatalogStudioGameEngine : public UGameEngine
{
	GENERATED_BODY()
	
public:
	//virtual void GameWindowClosed(const TSharedRef<SWindow>& WindowBeingClosed) override;

	//virtual void GameWindowSizeChanged(const TSharedRef<SWindow>& WindowBeingChanged, const int32& Width, const int32& Height) override;

	virtual void PreExit() override;

	virtual void Init(class IEngineLoop* InEngineLoop) override;
	
};
