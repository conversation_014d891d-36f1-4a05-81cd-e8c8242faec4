// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogStudioGameInstance.h"
#include "CatalogPlayerController.h"

void UCatalogStudioGameInstance::Init()
{
	UGameInstance::Init();
	UE_LOG(LogTemp, Log, TEXT("--------------   UCatalogStudioGameInstance::Init()   --------------"));

}

void UCatalogStudioGameInstance::Shutdown()
{
	UE_LOG(LogTemp, Log, TEXT("--------------   UCatalogStudioGameInstance::Shutdown()   --------------"));
	UGameInstance::Shutdown();
}

void UCatalogStudioGameInstance::ExitGame()
{
	ACatalogPlayerController::Get()->ExitGame();
}

void UCatalogStudioGameInstance::WindowSizeChanged()
{
	ACatalogPlayerController::Get()->WindowSizeChanged();
}

void UCatalogStudioGameInstance::UserCloseApp()
{
	/*ULocalDatabaseSubsystem* LocalDBSubsystem = GetSubsystem<ULocalDatabaseSubsystem>();
	LocalDBSubsystem->CloseDatabase();
	LocalDBSubsystem->CloseServerDatabase();*/
}