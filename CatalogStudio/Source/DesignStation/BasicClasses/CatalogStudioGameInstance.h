// Fill out your copyright notice in the Description page of Project Settings.

#pragma once


#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "CatalogStudioGameInstance.generated.h"

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UCatalogStudioGameInstance : public UGameInstance
{
	GENERATED_BODY()

public:

	virtual void Init();

	virtual void Shutdown();

	void ExitGame();

	void WindowSizeChanged();

	//用户关闭程序时的清理
	void UserCloseApp();
};
