// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "FileNetworkOperator.generated.h"

namespace NFileNetworkOpertator
{
	enum EDownloadState
	{
		ReadyToDownload,
		Downloading,
		Downloaded
	};

	struct FDownloadFileInfo
	{
		FString	filePath;
		int64	downloadBytes;
		int64	totalBytes;
		EDownloadState	downloadState;
		FDownloadFileInfo(FString strFilePath) :filePath(strFilePath), downloadBytes(0), totalBytes(1), downloadState(EDownloadState::ReadyToDownload) { }

		bool operator==(const FDownloadFileInfo& Other) { return filePath == Other.filePath; }
		float DownloadProgress() const
		{
			switch (downloadState)
			{
			case EDownloadState::ReadyToDownload:return 0.0f;
			case EDownloadState::Downloading:return static_cast<float>(downloadBytes / totalBytes);
			case EDownloadState::Downloaded:return 1.0f;
			}
		}
	};

	struct FDownloadPackage
	{
		FString	UUID;
		TArray<FDownloadFileInfo>	FilesToDownload;
		FDownloadPackage(FString strUUID) :UUID(strUUID) { }
		int IndexOfFilePath(const FString& strFilePath) const
		{
			return FilesToDownload.IndexOfByPredicate([&strFilePath](const FDownloadFileInfo& InOther)->bool {return InOther.filePath.Equals(strFilePath); });
		}
		float DownloadProgress() const
		{
			float downloadProgress = 0.0f;
			for (const auto& File : FilesToDownload)
				downloadProgress += File.DownloadProgress();
			return downloadProgress / FilesToDownload.Num();
		}
		bool IsAllDownloaded() const
		{
			for (const auto& File : FilesToDownload)
				if (EDownloadState::Downloaded != File.downloadState)
					return false;
			return true;
		}
		void GetAllFilePath(TArray<FString>& outFilePath) const
		{
			for (const auto& File : FilesToDownload)
				outFilePath.Add(File.filePath);
		}
	};

	struct FDownloadingFilesQueue
	{
	private:
		TArray<FDownloadPackage>	FilePackages;
	public:
		void AddFile(FString strUUID, FString strFilePath)
		{
			for (auto& Package : FilePackages)
			{
				if (Package.UUID == strUUID)
				{
					int Index = Package.IndexOfFilePath(strFilePath);
					if (-1 != Index)return;
					Package.FilesToDownload.Add(FDownloadFileInfo(strFilePath));
					return;
				}
			}
			FilePackages.Add(strUUID);
			FilePackages[FilePackages.Num() - 1].FilesToDownload.Add(FDownloadFileInfo(strFilePath));
			return;
		}
		bool First(FString& outUUID, FString& outFilePath) const
		{
			for (const auto& Package : FilePackages)
			{
				for (const auto& File : Package.FilesToDownload)
				{
					if (EDownloadState::ReadyToDownload == File.downloadState)
					{
						outUUID = Package.UUID;
						outFilePath = File.filePath;
						return true;
					}
				}
			}
			return false;
		}
		bool IsEmpty() const
		{
			return 0 == FilePackages.Num();
		}
		void Update(const FString& inUUID, const FString& inFilePath, const EDownloadState& inState)
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
				{
					int Index = Package.IndexOfFilePath(inFilePath);
					if (-1 == Index)return;
					Package.FilesToDownload[Index].downloadState = inState;
					return;
				}
			}
		}
		void Update(const FString& inUUID, const FString& inFilePath, const int64& inDownloadBytes)
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
				{
					int Index = Package.IndexOfFilePath(inFilePath);
					if (-1 == Index)return;
					Package.FilesToDownload[Index].downloadBytes = inDownloadBytes;
					return;
				}
			}
		}
		void Update(const FString& inUUID, const FString& inFilePath, const int64& inDownloadBytes, const int64& inTotalBytes)
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
				{
					int Index = Package.IndexOfFilePath(inFilePath);
					if (-1 == Index)return;
					Package.FilesToDownload[Index].downloadBytes = inDownloadBytes;
					Package.FilesToDownload[Index].totalBytes = inTotalBytes;
					return;
				}
			}
		}
		float DownloadProgress(const FString& inUUID) const
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
					return Package.DownloadProgress();
			}
			return 0.0f;
		}
		void TryRemoveDownloadedPackage()
		{
			TArray<int> IndexToRemove;
			for (int i = FilePackages.Num() - 1; i >= 0; --i)
			{
				if (FilePackages[i].IsAllDownloaded())
					IndexToRemove.Add(i);
			}
			for (const int& Index : IndexToRemove)
			{
				FilePackages.RemoveAt(Index);
			}
		}
		bool IsDownloaded(const FString& inUUID) const
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
				{
					return Package.IsAllDownloaded();
				}
			}
			return true;
		}
		void GetFilePaths(const FString& inUUID, TArray<FString>& outPaths) const
		{
			for (auto& Package : FilePackages)
			{
				if (inUUID == Package.UUID)
				{
					return Package.GetAllFilePath(outPaths);
				}
			}
		}
		FString WhichPackageFileIn(const FString& FilePath) const
		{
			for (const auto& Package : FilePackages)
			{
				const int32 Index = Package.FilesToDownload.IndexOfByPredicate([FilePath](const FDownloadFileInfo& InOther) {return InOther.filePath == FilePath; });
				if (INDEX_NONE != Index) return Package.UUID;
			}
			return TEXT("");
		}
	};
}

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UFileNetworkOperator : public UObject
{
	GENERATED_BODY()

};
