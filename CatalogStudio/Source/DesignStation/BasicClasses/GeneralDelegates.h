// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "GeneralDelegates.generated.h"

DECLARE_DYNAMIC_DELEGATE_OneParam(FOneInt32Delegate, int32, IntValue);
DECLARE_DYNAMIC_DELEGATE_OneParam(FOneBoolDelegate, bool, BoolValue);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FTwoIntDelegate, const int32&, Type, const int32&, Index);
DECLARE_DYNAMIC_DELEGATE(FNoParamDelegate);

DECLARE_MULTICAST_DELEGATE_ThreeParams(FBoolAndIntResponseDelegate, const FString&, bool, const int32&);

//DECLARE_DYNAMIC_DELEGATE_ThreeParams(FWidgetSelectDelegate, const int32&, EditType, const int32&, PointId, bool, IsOver);
DECLARE_DYNAMIC_DELEGATE_FourParams(FWidgetSelectDelegate, const int32&, EditType, const int32&, PointId, bool, IsOver, bool, OnProperty);


/**
 *
 */
UCLASS()
class DESIGNSTATION_API UGeneralDelegates : public UObject
{
	GENERATED_BODY()




};
