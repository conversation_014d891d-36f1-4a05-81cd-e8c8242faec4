// Fill out your copyright notice in the Description page of Project Settings.

#include "GlobalData.h"

FLoginUserInfo::FLoginUserInfo(
	const int32& InID,
	const FString& InUserName, 
	const FString& InName,
	const FString& InUserPhone, 
	const FString& InUserProfilePhoto
)
	: ID(InID)
	, UserName(InUserName)
	, Name(InName)
	, UserPhone(InUserPhone)
	, UserProfilePhoto(InUserProfilePhoto)
{
}

FGlobalData::FGlobalData(): IsUserLogin(false)
{
}

FGlobalData::~FGlobalData()
{
}

void FGlobalData::SetLoginUserInfo(const bool& InIsUserLogin, const FLoginUserInfo& InLoginUserInfo)
{
	IsUserLogin = InIsUserLogin;
	LoginUserInfo = InLoginUserInfo;
}

int32 FGlobalData::GetUserID() const
{
	return LoginUserInfo.ID;
}

FString FGlobalData::GetUserIDStr() const
{
	return FString::FromInt(LoginUserInfo.ID);
}
