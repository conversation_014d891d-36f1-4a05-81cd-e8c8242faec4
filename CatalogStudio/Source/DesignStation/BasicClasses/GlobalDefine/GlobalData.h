// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/DesignStation.h"

struct FLoginUserInfo
{
	int32 ID;
	FString UserName;
	FString Name;
	FString UserPhone;
	FString UserProfilePhoto;
	FLoginUserInfo()
		: ID(INDEX_NONE)
		, UserName(TEXT(""))
		, Name(TEXT(""))
		, UserPhone(TEXT(""))
		, UserProfilePhoto(TEXT(""))
	{}
	FLoginUserInfo(
		const int32& InID,
		const FString& InUserName, 
		const FString& InName,
		const FString& InUserPhone, 
		const FString& InUserProfilePhoto
	);
};


/**
 *
 */
class DESIGNSTATION_API FGlobalData
{
private:

	bool IsUserLogin;
	FLoginUserInfo LoginUserInfo;

public:
	FGlobalData();
	~FGlobalData();

public:

	GenerateGetConstRef(bool, IsUserLogin)
	GenerateGetConstRef(FLoginUserInfo, LoginUserInfo)
	void SetLoginUserInfo(const bool& InIsUserLogin, const FLoginUserInfo& InLoginUserInfo);
	bool IsAdminUser() const { return LoginUserInfo.UserName.Equals(TEXT("admin")); }
	int32 GetUserID() const;
	FString GetUserIDStr() const;
	FString GetUserName() const { return LoginUserInfo.UserName; }
};
