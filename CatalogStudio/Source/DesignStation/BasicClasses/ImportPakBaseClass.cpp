// Fill out your copyright notice in the Description page of Project Settings.

#include "ImportPakBaseClass.h"

// Sets default values
AImportPakBaseClass::AImportPakBaseClass()
	:bIsActive(false)
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

}

bool AImportPakBaseClass::UpdateActionState_Implementation(bool Active)
{
	bIsActive = Active;

	return bIsActive;
}

bool AImportPakBaseClass::IsInteractive_Implementation()
{
	return false;
}

bool AImportPakBaseClass::ToggleActiveState()
{
	bool bNewActiveState = !bIsActive;
	UpdateActionState(bNewActiveState);
	return bNewActiveState;
}

FLinearColor AImportPakBaseClass::ParseLinearColorFromString(const FString& InStr)
{//��ʽΪ"RGB"��"R=12,G=21,B=12"
	FLinearColor LinearColor = FLinearColor::White;
	const FString CleanString = InStr.Mid(1, InStr.Len() - 2);
	if (CleanString.Contains(TEXT("="), ESearchCase::IgnoreCase))
	{//��¼¼��ı���ֵΪFLinearColor�ĸ�ʽ
		LinearColor.InitFromString(CleanString);
	}
	else if (6 == CleanString.Len())
	{//���ʦ�ֶ��޸ĵ�ֵΪRGB��ʽ
		FColor ColorRGB;
		ColorRGB = FColor::FromHex(CleanString);
		LinearColor = FLinearColor::FromSRGBColor(ColorRGB);
	}
	return LinearColor;
}

void AImportPakBaseClass::ChangeMatByComponentName_Implementation(const FString& ComponentName, UMaterialInstanceDynamic* NewMat)
{

}

// Called when the game starts or when spawned
void AImportPakBaseClass::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AImportPakBaseClass::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

