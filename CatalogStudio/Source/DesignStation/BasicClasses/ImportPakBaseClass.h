// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "ImportPakBaseClass.generated.h"


UENUM(BlueprintType)
enum class EFurnitureState : uint8
{
	ENormal = 0 UMETA(DisplayName = "正常家具"),
	EOverlay = 1 UMETA(DisplayName = "模型冲突"),
	EOutdate = 2 UMETA(DisplayName = "家具下架"),
	EError = 3 UMETA(DisplayName = "数据出错"),
	ECustom1 = 4 UMETA(DisplayName = "预留1"),
	ECustom2 = 5 UMETA(DisplayName = "预留2"),
};

UENUM(BlueprintType)
enum class EFurnitureSelectionState : uint8
{
	ENone = 0 UMETA(DisplayName = "正常"),     
	EHover = 1 UMETA(DisplayName = "悬停"),
	ESeclect = 2 UMETA(DisplayName = "选中"),
	EDrag = 3 UMETA(DisplayName = "拖动"),
	ECompSelect = 4 UMETA(DisplayName = "部件选中")
};

UCLASS(Blueprintable)
class DESIGNSTATION_API AImportPakBaseClass : public AActor
{
	GENERATED_BODY()

protected:

	//UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = ImportActor, meta = (AllowPrivateAccess = true))
	bool bIsActive;

public:
	// Sets default values for this actor's properties
	AImportPakBaseClass();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = ImportActor)
		bool UpdateSelectionState(bool Is2DMode, EFurnitureState FurnitureState, EFurnitureSelectionState SelectState);//更新选择状态

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = ImportActor)
		bool UpdateActionState(bool Active);//用户交互

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = ImportActor)
		bool IsInteractive();//是否可以交互

	bool ToggleActiveState();//翻转交互状态

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = ImportActor)
		bool ParameterValueChanged(const FString& InParameterName, const FString& InParameterValue);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = ImportActor)
		bool FurnitureCodeChanged(const FString& NewFurnitureCode);

	inline bool GetActiveState() const { return bIsActive; }//获取交互状态

	UFUNCTION(BlueprintCallable, Category = ImportActor)
		static FLinearColor ParseLinearColorFromString(const FString& InStr);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = ImportActor)
		void ChangeMatByComponentName(const FString& ComponentName, UMaterialInstanceDynamic* NewMat);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

};
