// Fill out your copyright notice in the Description page of Project Settings.

#include "JsonUtilitiesLibrary.h"
#include "Misc/FileHelper.h"
#include "Misc/SecureHash.h"
#include "Misc/DateTime.h"



FRequestValue::FRequestValue()
	:Value(TEXT(""))
	, bValueIsString(true)
{

}

FRequestValue::FRequestValue(const FString& InValue, bool ValueIsString)
	:Value(InValue)
	, bValueIsString(ValueIsString)
{

}

bool UJsonUtilitiesLibrary::ConvertKeyValuePairsToJsonString(const TMap<FString, FRequestValue>& InKeyValuePairs, FString& OutJsonString)
{
	FString ContentStr(TEXT(""));
	for (const auto& PairIter : InKeyValuePairs)
	{
		if (PairIter.Value.GetValueIsString())
		{
			ContentStr += FString::Printf(TEXT("\"%s\":\"%s\""), *PairIter.Key, *PairIter.Value.GetValue());
		}
		else
		{
			ContentStr += FString::Printf(TEXT("\"%s\":%s"), *PairIter.Key, *PairIter.Value.GetValue());
		}
		ContentStr += TEXT(",");
	}
	//仅当Key Value对不为空时才删除最后一个逗号
	if (InKeyValuePairs.Num() > 0)
		ContentStr.RemoveAt(ContentStr.Len() - 1);
	OutJsonString = FString::Printf(TEXT("{%s}"), *ContentStr);
	return true;
}

FString UJsonUtilitiesLibrary::GenerateUUID()
{
	return FGuid::NewGuid().ToString().ToLower();
}

FString UJsonUtilitiesLibrary::ConvertStringTimeToFormatTime(const FString& InUTCTime)
{
	if (!InUTCTime.IsNumeric())return TEXT("");
	TArray<int32> DateArray;
	bool Res = UJsonUtilitiesLibrary::ConvertStringTimeToTimeArray(InUTCTime, DateArray);
	if (!Res)return TEXT("");
	return FString::Printf(TEXT("%d-%02i-%02i"), DateArray[0], DateArray[1], DateArray[2]);
}

int64 UJsonUtilitiesLibrary::ConvertDateToUnixTimestamp(const int32& Year, const int32& Month, const int32& Day)
{
	int64 Ticks = FDateTime(Year, Month, Day).GetTicks() - FDateTime(1970, 1, 1).GetTicks();
	int64 UTCTime = Ticks / ETimespan::TicksPerSecond - (3600 << 3);//因为时区问题导致必须要加8个小时
	return UTCTime;
}

bool UJsonUtilitiesLibrary::ConvertStringTimeToTimeArray(const FString& InUTCTime, TArray<int32>& OutTime)
{
	if (!InUTCTime.IsNumeric())return false;
	//Fix bug DES-755 因为时区问题导致必须要加8个小时
	const int64 DateTick = FCString::Atoi64(*InUTCTime) * ETimespan::TicksPerMillisecond + FDateTime(1970, 1, 1).GetTicks() + 8 * ETimespan::TicksPerHour;
	FDateTime Date(DateTick);
	OutTime.AddZeroed(7);
	Date.GetDate(OutTime[0], OutTime[1], OutTime[2]);
	OutTime[3] = Date.GetHour();
	OutTime[4] = Date.GetMinute();
	OutTime[5] = Date.GetSecond();
	OutTime[6] = Date.GetMillisecond();
	return true;
}

int32 UJsonUtilitiesLibrary::GetStringTimeYear(const FString& InUTCTime)
{
	TArray<int32> OutTime;
	if (UJsonUtilitiesLibrary::ConvertStringTimeToTimeArray(InUTCTime, OutTime))
	{
		return OutTime[0];
	}
	return 0;
}

void UJsonUtilitiesLibrary::GetStringTimeYearAndMouth(const FString& InUTCTime, int32& OutYear, int32& OutMouth)
{
	TArray<int32> OutTime;
	if (UJsonUtilitiesLibrary::ConvertStringTimeToTimeArray(InUTCTime, OutTime))
	{
		OutYear = OutTime[0];
		OutMouth = OutTime[1];
	}
}

FString UJsonUtilitiesLibrary::ConvertToPercentURL(const FString& InURL)
{
	const TArray<TCHAR>& Chars = InURL.GetCharArray();
	UE_LOG(LogTemp, Log, TEXT("UJsonUtilitiesLibrary::ConvertToPercentURL Chars.Num=%d"), Chars.Num());
	FString CompletedStr(TEXT(""));
	const int32 CharCount = Chars.Num() - 1;
	for (int32 i = 0; i < CharCount; ++i)
	{
		FString CharStr = ((Chars[i] >= '0' && Chars[i] <= '9') || (Chars[i] >= 'a' && Chars[i] <= 'z') || (Chars[i] >= 'A' && Chars[i] <= 'Z') || '.' == Chars[i]) ? FString::Printf(TEXT("%c"), Chars[i]) : UJsonUtilitiesLibrary::ConvertToPercentURL(Chars[i]);
		UE_LOG(LogTemp, Log, TEXT("UJsonUtilitiesLibrary::ConvertToPercentURL CharStr=%s"), *CharStr);
		CompletedStr = CompletedStr + CharStr;
	}
	UE_LOG(LogTemp, Log, TEXT("UJsonUtilitiesLibrary::ConvertToPercentURL CompletedStr=%s"), *CompletedStr);
	return CompletedStr;
}

FString UJsonUtilitiesLibrary::ConvertToPercentURL(const TCHAR& InChar)
{
	if (0x80 > InChar) return FString::Printf(TEXT("%%%02x"), InChar);
	if (0x800 > InChar)
	{//两个字节编码
		const uint8 LowByte = 0x7f & (0x80 | InChar);
		const uint8 HighByte = 0xcf & (0xc0 | (InChar >> 6));
		return FString::Printf(TEXT("%%%02x%%%02x"), LowByte, HighByte);
	}
	if (0x10000 > InChar)
	{//三个字节编码
		const uint8 FirstByte = 0x80 | (InChar & 0x3f);
		const uint8 SecondByte = 0x80 | ((InChar >> 6) & 0x3f);
		const uint8 ThirdByte = 0xe0 | ((InChar >> 12) & 0x3f);
		UE_LOG(LogTemp, Log, TEXT("UJsonUtilitiesLibrary::ConvertToPercentURL FirstByte=%02x SecondByte=%02x ThirdByte=%02x"), FirstByte, SecondByte, ThirdByte);
		return FString::Printf(TEXT("%%%02x%%%02x%%%02x"), ThirdByte, SecondByte, FirstByte);
	}
	//if (0x110000 > InChar)
	//{//四个字节编码
	//	const uint8 FirstByte = 0x7f & (0x80 | InChar);
	//	const uint8 SecondByte = 0x7f & (0x80 | (InChar >> 6));
	//	const uint8 ThirdByte = 0x7f & (0x80 | (InChar >> 12));
	//	const uint8 ForthByte = 0xff & (0xf0 | (InChar >> 18));
	//	return FString::Printf(TEXT("%%%02x%02x%02x%02x"), FirstByte, SecondByte, ThirdByte, ForthByte);
	//}
	return TEXT("");
}