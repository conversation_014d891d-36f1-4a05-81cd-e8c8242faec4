// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "JsonObjectConverter.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "JsonUtilitiesLibrary.generated.h"

struct FRequestValue
{
private:
	FString	Value;
	bool	bValueIsString;//判断Value在转换成FString前的类型是否是字符串类型，在JSON转换时字符串类型与非字符串类型的转换不同

public:
	FRequestValue();
	FRequestValue(const FString& InValue, bool ValueIsString);
	const FString& GetValue() const { return Value; }
	bool GetValueIsString() const { return bValueIsString; }
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UJsonUtilitiesLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	template<class StructType>
	static bool ConvertStructToJsonString(const StructType& InStruct, FString& OutJsonString)
	{
		FString JsonString(TEXT(""));
		TSharedPtr<FJsonObject> JsonObjectPtr = FJsonObjectConverter::UStructToJsonObject(InStruct);
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
		bool bSerialized = FJsonSerializer::Serialize(JsonObjectPtr.ToSharedRef(), Writer);
		OutJsonString = JsonString;
		return bSerialized;
	}
	
	template<class StructType>
	static bool ConvertJsonStringToStruct(const FString& InJsonString, StructType& OutStruct)
	{
		bool bConvertSuccess = FJsonObjectConverter::JsonObjectStringToUStruct<StructType>(
			InJsonString,
			&OutStruct,
			0, 0);
		return bConvertSuccess;
	}

	static bool ConvertKeyValuePairsToJsonString(const TMap<FString, FRequestValue>& InKeyValuePairs, FString& OutJsonString);

	static FString GenerateUUID();

	UFUNCTION(BlueprintCallable, Category = "JsonUtilitiesLibrary")
	static FString ConvertStringTimeToFormatTime(const FString& InUTCTime);
	static int32 GetStringTimeYear(const FString& InUTCTime);
	static void GetStringTimeYearAndMouth(const FString& InUTCTime, int32& OutYear, int32& OutMouth);

	static int64 ConvertDateToUnixTimestamp(const int32& Year, const int32& Month, const int32& Day);

	static bool ConvertStringTimeToTimeArray(const FString& InUTCTime, TArray<int32>& OutTime);

	//将InURL指定的URL转换成百分号格式的URL
	//返回转换后的URL
	static FString ConvertToPercentURL(const FString& InURL);

	//将InChar指定的字符转换成百分号格式
	static FString ConvertToPercentURL(const TCHAR& InChar);
};
