// Fill out your copyright notice in the Description page of Project Settings.

#include "CustomMaterialEditManager.h"
#include "DesignStation/SQLite/MaterialRelated/CustomMaterialTableOperatorLibrary.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "Runtime/Engine/Classes/Engine/StaticMeshActor.h"
#include "DesignStation/SQLite/MaterialRelated/MaterialOperationLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "ImageProcess/Public/ImageProcess.h"
#include "DesignStation/UI/LoadUI/LoadPageWidget.h"
#include "CustomMaterialEdit/UI/MaterialImportWidget.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SubSystem/PakFileManagerSubsystem.h"


#define LOCTEXT_NAMESPACE "MaterialEditManager"
const extern FString PARAM_ZLHSW = TEXT("ZLHSW");

UCustomMaterialEditManager::UCustomMaterialEditManager()
	:UManagerBase(TEXT("MatMap"))
	, CustomMaterialToEdit(FCustomMaterialTableData())
	, MatFolderData(FFolderTableData())
	, UIManager(nullptr)
	, SphereMesh(nullptr)
{

}

void UCustomMaterialEditManager::InitializeManager()
{
	{
		//first init ui
		UIManager = NewObject<UMaterialEditUIManager>();
		UIManager->MaterialToolBarEditDelegate.BindUFunction(this, FName("OnClickToolBarHandler"));
		UIManager->MaterialFilePropertyChangeDelegate.BindUFunction(this, FName("OnMaterialFilePropertyChangedHandler"));
		UIManager->MaterialComponentPictureTypeDelegate.BindUFunction(this, FName(TEXT("TakePictureHandler")));
		UIManager->ImportCustomMaterialDelegate.BindUFunction(this, FName(TEXT("OnImportCustomMaterialHandler")));

		UIManager->InitMaterialEditUI();

		ACatalogPlayerController::Get()->OnScreenShotCompletedDelegate.BindUFunction(this, FName(TEXT("ProcessImageHandler")));

		UIManager->UpdateMaterialPropertyWidget(MatFolderData.folder_id, MatFolderData.folder_code, MatFolderData.folder_name, MatFolderData.visibility_exp, FString::SanitizeFloat(MatFolderData.visibility));

		TArray<FMaterialLevelInfo> FirstLevel;
		UIManager->InitLevelWidegt(FirstLevel);
	}
	LoadCustomMaterial();
}

void UCustomMaterialEditManager::UninitializeManager()
{
	if (IS_OBJECT_PTR_VALID(UIManager))
	{
		UIManager->Clear();
		UIManager = nullptr;
	}
	SphereMesh = nullptr;
}

void UCustomMaterialEditManager::OnMaterialFilePropertyChangedHandler(const int32& EditType, const FString& OutString)
{
	if (EditType == (int32)EMaterialPropertyType::Id)
	{
		MatFolderData.folder_id = OutString;
	}
	else if (EditType == (int32)EMaterialPropertyType::Coding)
	{
		MatFolderData.folder_code = OutString;
	}
	else if (EditType == (int32)EMaterialPropertyType::Name)
	{
		MatFolderData.folder_name = OutString;
	}
	else if (EditType == (int32)EMaterialPropertyType::VisiExpress)
	{
		MaterialFileVisibleEdit(OutString, true);
	}
	else if (EditType == (int32)EMaterialPropertyType::VisiValue)
	{
		MaterialFileVisibleEdit(OutString, false);
	}
	UFolderTableOperatorLibrary::UpdateFile(MatFolderData);
	UIManager->UpdateMaterialPropertyWidget(MatFolderData.folder_id, MatFolderData.folder_code, MatFolderData.folder_name, MatFolderData.visibility_exp, FString::SanitizeFloat(MatFolderData.visibility));
}

void UCustomMaterialEditManager::LoadCustomMaterial()
{
	SetMaterialActor(false, false);
	ULoadPageWidget::GetInstance()->SetLoadingPercent(0.7f);
	ULoadPageWidget::GetInstance()->SetLoadingPercent(1.0f);
	ULoadPageWidget::GetInstance()->LoadDone(true);
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Collapsed);
}

void UCustomMaterialEditManager::MaterialFileVisibleEdit(const FString& InString, bool IsExpression)
{
	if (IsExpression)
	{
		FExpressionValuePair VisiblePair;
		VisiblePair.Expression = InString;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		if (!FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, VisiblePair))
		{
			UE_LOG(LogTemp, Log, TEXT("caculate visible express error"));
		}
		MatFolderData.visibility_exp = VisiblePair.Expression;
		MatFolderData.visibility = FCString::Atof(*VisiblePair.Value);
	}
	else
	{
		FString FormatValue = UParameterPropertyData::FormatParameterValue(InString).ToString();
		MatFolderData.visibility_exp = FormatValue;
		MatFolderData.visibility = FCString::Atof(*FormatValue);
	}
}

void UCustomMaterialEditManager::SetMaterialActor(bool IsCloth, bool IsPhoto)
{
	TArray<AActor*> Sphere;
	FName HiddenActor1;
	FName HiddenActor2;
	FName HiddenActor3;
	FName VisibleActor;
	if (IsPhoto)
	{
		HiddenActor1 = IsCloth ? FName("SpherePhoto") : FName("ClothPhoto");
		HiddenActor2 = FName("SphereMesh");
		HiddenActor3 = FName("ClothMesh");

		VisibleActor = IsCloth ? FName("ClothPhoto") : FName("SpherePhoto");
	}
	else
	{
		HiddenActor1 = IsCloth ? FName("SphereMesh") : FName("ClothMesh");
		HiddenActor2 = FName("ClothPhoto");
		HiddenActor3 = FName("SpherePhoto");

		VisibleActor = IsCloth ? FName("ClothMesh") : FName("SphereMesh");
	}
	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), HiddenActor1, Sphere);
	if (Sphere.Num() > 0)
	{
		Sphere[0]->SetActorHiddenInGame(true);
	}
	Sphere.Empty();
	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), HiddenActor2, Sphere);
	if (Sphere.Num() > 0)
	{
		Sphere[0]->SetActorHiddenInGame(true);
	}
	Sphere.Empty();
	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), HiddenActor3, Sphere);

	if (Sphere.Num() > 0)
	{
		Sphere[0]->SetActorHiddenInGame(true);
	}
	Sphere.Empty();
	ULoadPageWidget::GetInstance()->SetLoadingPercent(0.7f);
	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), VisibleActor, Sphere);
	if (1 == Sphere.Num())
	{
		Sphere[0]->SetActorHiddenInGame(false);
		AStaticMeshActor* StaticMesh = Cast<AStaticMeshActor>(Sphere[0]);
		SphereMesh = StaticMesh->GetStaticMeshComponent();
		MaterialInstance = FMaterialOperationLibrary::LoadMaterialByMaterialRef(CustomMaterialToEdit.ref_path);

	
			TArray<FParameterData> Parameters;
			FLocalDatabaseParameterLibrary::RetriveFileParametersByID(CustomMaterialToEdit.folder_id, Parameters);
			SetMatParam(Parameters, IsPhoto);
		
		SphereMesh->SetMaterial(0, MaterialInstance);
	}
}

void UCustomMaterialEditManager::OnClickToolBarHandler(const EMaterialToolBarType& EditType)
{
	if (EMaterialToolBarType::Save == EditType)
	{

	}
	else if (EMaterialToolBarType::Exit == EditType)
	{
		OnBackToMain.ExecuteIfBound(MatFolderData);
	}
}

void UCustomMaterialEditManager::WindowSizeChanged()
{
	UE_LOG(LogTemp, Error, TEXT("UCustomMaterialEditManager::WindowSizeChanged"));
	if (UIManager)
	{
		UIManager->RefreshSceneSize();
	}
}

void UCustomMaterialEditManager::SetMatParam(const TArray<FParameterData>& Parameters,bool IsPhoto)
{
	int32 UVIndex = Parameters.IndexOfByPredicate([](const FParameterData& InData) {return InData.Data.name.Equals(PARAM_ZLHSW,ESearchCase::CaseSensitive); });
	if (MaterialInstance && UVIndex >= 0)
	{
		int32 MatUV = FCString::Atoi(*Parameters[UVIndex].Data.value);
		MaterialInstance->SetScalarParameterValue(FName(TEXT("UVRotation")), MatUV == 0 ? 0.0f :90.0f);
	}
}

void UCustomMaterialEditManager::ShotScreenHandler()
{
	FString FilePath = FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png");

	FilePath = FPaths::ConvertRelativePathToFull(FilePath);
	ACatalogPlayerController::Get()->TakeScreenShot(FilePath);
}

void UCustomMaterialEditManager::TakePictureHandler(const int32& PictureType)
{
	SetMaterialActor(false, true);
	TakePictureTypeDelegate.ExecuteIfBound(PictureType);

	if (PictureType == 9)
	{
		if (false == bImageSelecting)
		{
			bImageSelecting = true;
			FCatalogFunctionLibrary::OpenFileDialogForImage(OpenImage);
			bImageSelecting = false;
			if (!OpenImage.IsEmpty())
			{
				UIManager->SetCameraImage(OpenImage, true);
			}
			else
			{
				UIManager->SetCameraImage(OpenImage, false);
			}
		}
	}
	else if (PictureType == 10)
	{
		if (CurrentPictureType != 9)
		{
			ShotScreenHandler();
		}
		else
		{
			SaveImportImage(OpenImage);
			SetMaterialActor(false, false);
		}
		OpenImage.Empty();
	}
	else
	{
		UIManager->SetCameraImage(OpenImage, false);
		if (PictureType == 11)
		{
			SetMaterialActor(false, false);
		}
		OpenImage.Empty();
	}
	CurrentPictureType = PictureType;
}

void UCustomMaterialEditManager::ProcessImageHandler()
{
	FString FilePath = FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png");
	FilePath = FPaths::ConvertRelativePathToFull(FilePath);

	FString TempPath = FPaths::ProjectContentDir() + FString::Printf(TEXT("Materials/%s/Thumbnails"), *MatFolderData.id);

	TempPath = FPaths::ConvertRelativePathToFull(TempPath);

	FCatalogFunctionLibrary::CreateDirectoryRecursively(TempPath);

	FString SavePath = FString::Printf(TEXT("Materials/%s/Thumbnails/%s.%s"), *MatFolderData.id, *MatFolderData.id, *FPaths::GetExtension(FilePath));
	FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

	if (FImageProcessModule::Get()->ThumbnailConvert(FilePath, TargetPath))
	{
		MatFolderData.thumbnail_path = SavePath;
		UFolderTableOperatorLibrary::UpdateFile(MatFolderData);
	}
	SetMaterialActor(false, false);
}

void UCustomMaterialEditManager::SaveImportImage(const FString& SrcImage)
{
	if (SrcImage.IsEmpty())
	{
		return;
	}
	FString SavePath = FString::Printf(TEXT("Materials/%s/Thumbnails/%s.%s"), *MatFolderData.id, *MatFolderData.id, *FPaths::GetExtension(SrcImage));
	FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

	FCatalogFunctionLibrary::CopyFileTo(SrcImage, DataPath);
	MatFolderData.thumbnail_path = SavePath;
	UFolderTableOperatorLibrary::UpdateFile(MatFolderData);
}

void UCustomMaterialEditManager::OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath)
{
	FString DesPath = FPaths::ProjectContentDir() + SavePath;
	DesPath = FPaths::ConvertRelativePathToFull(DesPath);
	if (FPaths::FileExists(DesPath))
	{//如果目标文件已经存在则Unmount
		GetWorld()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->UnmountPakFile(DesPath);
	}
	ECopyFileErrorCode Res = FCatalogFunctionLibrary::CopyFileTo(OriginalFilePath, DesPath);
	UE_LOG(LogTemp, Log, TEXT("UCustomMaterialEditManager::OnImportCustomMaterialHandler copy file %d"), static_cast<int32>(Res));
	UMaterialImportWidget* ImportWidget = Cast<UMaterialImportWidget>(UIManager->GetImportMaterialWidget());
	if (ImportWidget)
	{
		ImportWidget->UpdateProgress(0.0f);
		ImportWidget->UpdateState(ECopyFileErrorCode::ESuccess == Res ? EMaterialImportState::Importing : EMaterialImportState::Failed);
	}
	if (ECopyFileErrorCode::ESuccess == Res)
	{
		UMaterialImportWidget* ImportWidget = Cast<UMaterialImportWidget>(UIManager->GetImportMaterialWidget());
		if (ImportWidget)
		{
			ImportWidget->UpdateProgress(1.0f);
			ImportWidget->UpdateState(EMaterialImportState::Importing);
		}
		{//pak上传成功
			GetWorld()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->MountPakFile(DesPath, true);
			CustomMaterialToEdit.import_path = SavePath;
			CustomMaterialToEdit.ref_path = RefPath;
		}
		if (ImportWidget)
		{
			ImportWidget->UpdateProgress(1.0f);
			ImportWidget->UpdateState(EMaterialImportState::Succeed);
			ImportWidget->RemoveFromParent();
		}
		CustomMaterialToEdit.folder_id = MatFolderData.id;
		if (CustomMaterialToEdit.IsValid())
			FCustomMaterialTableOperatorLibrary::UpdateCustomMaterial(CustomMaterialToEdit);
		else
			FCustomMaterialTableOperatorLibrary::InsertCustomMaterial(CustomMaterialToEdit);
		SetMaterialActor(false, false);
	}
}

#undef LOCTEXT_NAMESPACE
