// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ManagerBase.h"
#include "CustomMaterialEdit/UI/MaterialEditUIManager.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "Runtime/Engine/Classes/Components/StaticMeshComponent.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "CustomMaterialEditManager.generated.h"

/**
 *
 */

UENUM(BlueprintType)
enum class EMatLevelType : uint8
{
	FirstLevel = 0,
	SecondLevel
};

UENUM(BlueprintType)
enum class EBasicMatLevelType : uint8
{
	ChangeLevelID = 0,
	ChangeBasicMaterial,
	SearchBasicMaterial
};

UCLASS()
class DESIGNSTATION_API UCustomMaterialEditManager : public UManagerBase
{
	GENERATED_BODY()

protected:

	UPROPERTY()
		FCustomMaterialTableData	CustomMaterialToEdit;
	UPROPERTY()
		FFolderTableData MatFolderData;

	UPROPERTY()
		UMaterialEditUIManager* UIManager;

	UPROPERTY()
		UStaticMeshComponent* SphereMesh;

	UPROPERTY()
		UMaterialInstanceDynamic* MaterialInstance;

	//Fix bug CATALOG-1536
	bool bImageSelecting = false;

public:
	UCustomMaterialEditManager();

	inline void SetCustomMaterialToEdit(const FCustomMaterialTableData& InCM) { CustomMaterialToEdit = InCM; }

	inline void	SetMatFolderData(const FFolderTableData& InMatFolderData) { MatFolderData = InMatFolderData; }

	virtual void WindowSizeChanged() override;

	void SetMatParam(const TArray<FParameterData>& Parameters, bool IsPhoto);
protected:

	virtual void InitializeManager() override;

	virtual void UninitializeManager() override;

	UFUNCTION()
		void OnClickToolBarHandler(const EMaterialToolBarType& EditType);

	UFUNCTION()
		void OnMaterialFilePropertyChangedHandler(const int32& EditType, const FString& OutString);

	UFUNCTION()
		void OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath);

private:

	void MaterialFileVisibleEdit(const FString& InString, bool IsExpression);

	void SetMaterialActor(bool IsCloth, bool IsPhoto);

	void LoadCustomMaterial();


protected:
	UFUNCTION()
		void ShotScreenHandler();
	UFUNCTION()
		void TakePictureHandler(const int32& PictureType);
	UFUNCTION()
		void ProcessImageHandler();

	void SaveImportImage(const FString& SrcImage);

private:
	int32 CurrentPictureType;
	FString OpenImage;

};
