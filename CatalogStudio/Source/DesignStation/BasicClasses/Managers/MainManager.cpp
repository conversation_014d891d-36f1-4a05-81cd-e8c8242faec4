// Fill out your copyright notice in the Description page of Project Settings.

#include "MainManager.h"

#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SQLite/MaterialRelated/CustomMaterialTableOperatorLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "UIManager/MainUIManager.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"


UMainManager::UMainManager() 
	: UManagerBase(TEXT("MainPageMap"))
{
	
}

void UMainManager::OpenFile_Inner(const FFolderTableData& InData)
{
	MainUIManager->ResetViewport();
	EFolderType Type = static_cast<EFolderType>(InData.folder_type);

	FRefToLocalFileData RefData;
	UFolderWidget::Get()->GetCacheDataForFile(
		URefRelationFunction::GetMarkToBackendDirectory(InData),
		RefData);
	Type = static_cast<EFolderType>(RefData.FolderDBData.folder_type);


	if (EFolderType::ESingleComponent == Type)
	{
		FSingleComponentTableData SingleComponent;
		SingleComponent.id = 1;
		SingleComponent.data_path = RefData.FileData.file_data_path;
		SingleComponent.depend_files = RefData.FileData.depend_files;
		SingleComponent.folder_id = RefData.FolderDBData.id;

		OnChangeToSingleComponentEdit.ExecuteIfBound(SingleComponent, OpenFileData);
	}
	else if (EFolderType::EMultiComponents == Type)
	{
		MainUIManager->ShowMainUI(false);
		OnChangeToMultiComponentEdit.ExecuteIfBound(OpenFileData);
	}
	else if (EFolderType::EMaterial == Type)
	{
		FCustomMaterialTableData CM;
		FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(OpenFileData.id, CM);
		MainUIManager->ShowMainUI(false);
		OnChangeToMaterialEdit.ExecuteIfBound(CM, OpenFileData);
	}
	else
	{
		UI_POP_WINDOW_ERROR("Not support this type");
	}
}

void UMainManager::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UMainManager::OnCurrentDirectoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UMainManager::OnDownloadFileResponseHandler);
}

void UMainManager::SearchNewDirectoryInfo()
{
	if (OpenFileData.IsValid())
	{
		NetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest_ID({ OpenFileData.id });
	}
}

void UMainManager::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	if (UUID.Equals(NetUUID.DownloadFile, ESearchCase::IgnoreCase))
	{
		NetUUID.ResetDownloadFileAction();
		UE_LOG(LogTemp, Warning, TEXT("sync file when open, file data valid [%d]"), OpenFileData.IsValid());
		if (OpenFileData.IsValid())
		{
			OpenFile_Inner(OpenFileData);
			OpenFileData.SetNoValid();
		}
	}
}

void UMainManager::OnCurrentDirectoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (UUID.Equals(NetUUID.SearchUUID, ESearchCase::IgnoreCase))
	{
		NetUUID.ResetSearchAction();
		if (OpenFileData.IsValid())
		{
			if (Datas.IsValidIndex(0) && Datas[0].id.Equals(OpenFileData.id, ESearchCase::IgnoreCase))
			{
				URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], OpenFileData);

				if (URefRelationFunction::NeedDownloadFile(Datas[0]))
				{
					const FString FileRelPath = URefRelationFunction::GetRefFileRelativePath(Datas[0]);

					TArray<FString> NeedDownload;
					NeedDownload.Add(FileRelPath);
					if (!Datas[0].thumbnailPath.IsEmpty())
					{
						NeedDownload.Add(Datas[0].thumbnailPath);
					}
					NetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(NeedDownload);

				}
				else
				{
					OpenFile_Inner(OpenFileData);
				}
			}
			else
			{
				OpenFile_Inner(OpenFileData);
				OpenFileData.SetNoValid();
			}
		}
	}
}

void UMainManager::InitializeManager()
{
	BindDelegate();

	MainUIManager = NewObject<UMainUIManager>();
	MainUIManager->UserInfoDelegate.BindUFunction(this, FName(TEXT("OnUserLoginHandler")));
	MainUIManager->OpenFilesDelegate.BindUFunction(this, FName(TEXT("OnOpenFileHandler")));
	MainUIManager->MainToolBarDelegate.BindUFunction(this, FName(TEXT("OnClickMainWidgetHandler")));
	MainUIManager->MainParentParamDelegate.BindUFunction(this, FName(TEXT("GetFolderFileInheritParameter")));

#ifdef USE_REF_LOCAL_FILE
	const FGlobalData& GlobalData = UCatalogNetworkSubsystem::GetInstance()->GetGlobalDataConstRef();
#else
	const FGlobalData& GlobalData = ACatalogPlayerController::Get()->GetGlobalDataConstRef();
#endif
	if (GlobalData.GetIsUserLoginConstRef())
	{
		const FLoginUserInfo& UserInfo = GlobalData.GetLoginUserInfoConstRef();
		MainUIManager->UpdateMainUI(
			FUserInfoTableData(
				UserInfo.ID,
				UserInfo.UserName, 
				UserInfo.Name, 
				UserInfo.UserPhone, 
				UserInfo.UserProfilePhoto
			)
		);
	}
	else
	{
		FLoginRemeberInfo LoginRemeberInfo;
		bool Res = UProtobufOperatorFunctionLibrary::LoadLoginUserInfoFromFile(LoginRemeberInfo);
		MainUIManager->UpdateLoginInUI();
	}
}

void UMainManager::UninitializeManager()
{
	MainUIManager->Clear();
}

void UMainManager::BringBack()
{
	MainUIManager->UnClear();
}

void UMainManager::SyncSelectItem(const FFolderTableData& InFolderData)
{
	MainUIManager->SyncSelectItem(InFolderData);

}

void UMainManager::WindowSizeChanged()
{
	if (MainUIManager)
	{
		MainUIManager->RefreshSceneViewportSize();
	}
}

void UMainManager::OnUserLoginHandler(const FUserInfoTableData& UserInfo)
{
#ifdef USE_REF_LOCAL_FILE
	FGlobalData& GlobalData = UCatalogNetworkSubsystem::GetInstance()->GetGlobalDataRef();
#else
	FGlobalData& GlobalData = ACatalogPlayerController::Get()->GetGlobalDataRef();
#endif
	GlobalData.SetLoginUserInfo(
		true, 
		FLoginUserInfo(
			UserInfo.id,
			UserInfo.userName, 
			UserInfo.name, 
			UserInfo.phone, 
			UserInfo.profilePhoto
		)
	);
	MainUIManager->UpdateMainUI(UserInfo);
}

void UMainManager::OnOpenFileHandler(const FFolderTableData& FolderData)
{
	OpenFileData.CopyData(FolderData);

	SearchNewDirectoryInfo();

//	MainUIManager->ResetViewport();
//	OpenFileData.CopyData(FolderData);
//	EFolderType Type = static_cast<EFolderType>(FolderData.folder_type);
//
//#ifdef USE_REF_LOCAL_FILE
//	FRefToLocalFileData RefData;
//	UFolderWidget::Get()->GetCacheDataForFile(
//		URefRelationFunction::GetMarkToBackendDirectory(FolderData),
//		RefData);
//	Type = static_cast<EFolderType>(RefData.FolderDBData.folder_type);
//#endif
//
//
//	if (EFolderType::ESingleComponent == Type)
//	{
//		FSingleComponentTableData SingleComponent;
//#ifdef USE_REF_LOCAL_FILE
//		SingleComponent.id = 1; 
//		SingleComponent.data_path = RefData.FileData.file_data_path;
//		SingleComponent.depend_files = RefData.FileData.depend_files;
//		SingleComponent.folder_id = RefData.FolderDBData.id;
//#else
//		FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(FolderData.id, SingleComponent);
//#endif
//		OnChangeToSingleComponentEdit.ExecuteIfBound(SingleComponent, OpenFileData);
//	}
//	else if (EFolderType::EMultiComponents == Type)
//	{
//		MainUIManager->ShowMainUI(false);
//		OnChangeToMultiComponentEdit.ExecuteIfBound(OpenFileData);
//		/*FMultiComponentTableData MultiCompData;
//		if (UMultiComTableOperatorLibrary::SelectMultiComponentByFolderID(FolderData.id, MultiCompData))
//		{
//			MainUIManager->ShowMainUI(false);
//			OnChangeToMultiComponentEdit.ExecuteIfBound(MultiCompData);
//		}*/
//	}
//	else if (EFolderType::EMaterial == Type)
//	{
//		FCustomMaterialTableData CM;
//		FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(OpenFileData.id, CM);
//		MainUIManager->ShowMainUI(false);
//		OnChangeToMaterialEdit.ExecuteIfBound(CM, OpenFileData);
//	}
//	else
//	{
//		UI_POP_WINDOW_ERROR("Not support this type");
//	}
}

void UMainManager::OnClickMainWidgetHandler(const int32& InEditType)
{
	EMainToolBarType NewEditType = static_cast<EMainToolBarType>(InEditType);
	if (EMainToolBarType::Exit == NewEditType)
	{
#ifdef USE_REF_LOCAL_FILE
		UCatalogNetworkSubsystem::GetInstance()->NetLogoutRequest();
#else
		ACatalogPlayerController::Get()->NetLogoutRequest();
#endif
		FLoginRemeberInfo LoginRemeberInfo;
		bool Res = UProtobufOperatorFunctionLibrary::LoadLoginUserInfoFromFile(LoginRemeberInfo);
		MainUIManager->UpdateLoginInUI();
	}
}

void UMainManager::GetFolderFileInheritParameter(const FString& ParentFolderID, bool IsFolder)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  InheritParameters;
	TArray<FFolderTableData> OutProgramData;
	UFolderTableOperatorLibrary::FolderFileSearchByID(ParentFolderID, EFolderFileSearchType::EFolderAndFile, OutProgramData);
	if (OutProgramData.Num() == 1)
	{
		FFolderTableData& Data = OutProgramData[0];
		FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParametersNoSelf(false, Data.parent_id, InheritParameters);
	}

	MainUIManager->UpdateFolderParentParameter(InheritParameters);
}
