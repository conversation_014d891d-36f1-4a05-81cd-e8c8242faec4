// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ManagerBase.h"
#include "DesignStation/BasicClasses/Managers/UIManager/MainUIManager.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "DesignStation/SQLite/MultiComponentRelated/MultiComTableOperatorLibrary.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"

#include "MainManager.generated.h"

DECLARE_DYNAMIC_DELEGATE_TwoParams(FChangeToSingleComponentEdit, const FSingleComponentTableData&, SingleComponentData, const FFolderTableData&, FolderData);
DECLARE_DYNAMIC_DELEGATE_OneParam(FChangeToMultiComponentEdit, const FFolderTableData&, FolderData);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FChangeToMaterialEdit, const FCustomMaterialTableData&, MaterialData, const FFolderTableData&, FolderData);

/**
 *
 */

UCLASS()
class DESIGNSTATION_API UMainManager : public UManagerBase
{
	GENERATED_BODY()

public:
	UMainManager();

public:

	FChangeToSingleComponentEdit OnChangeToSingleComponentEdit;
	FChangeToMultiComponentEdit	OnChangeToMultiComponentEdit;
	FChangeToMaterialEdit		OnChangeToMaterialEdit;

public:

	void BringBack();
	void SyncSelectItem(const FFolderTableData& InFolderData);

	virtual void WindowSizeChanged() override;

	/*
	*  @@ Open file directory
	*  @@ file has already check
	*/
	void OpenFile_Inner(const FFolderTableData& InData);

#pragma region NET
public:
	void BindDelegate();

	void SearchNewDirectoryInfo();
	
	UFUNCTION()
	void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
	void OnCurrentDirectoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

private:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

#pragma endregion

protected:

	virtual void InitializeManager() override;

	virtual void UninitializeManager() override;

	UFUNCTION()
		void OnUserLoginHandler(const FUserInfoTableData& UserInfo);

	UFUNCTION()
		void OnOpenFileHandler(const FFolderTableData& FolderData);

	UFUNCTION()
		void OnClickMainWidgetHandler(const int32& InEditType);

	UFUNCTION()
		void GetFolderFileInheritParameter(const FString& ParentFolderID, bool IsFolder);

private:
	UPROPERTY()
		FFolderTableData OpenFileData;
	UPROPERTY()
		UMainUIManager* MainUIManager;



};
