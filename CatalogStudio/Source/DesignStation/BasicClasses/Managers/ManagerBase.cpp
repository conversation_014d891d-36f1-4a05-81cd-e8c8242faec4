// Fill out your copyright notice in the Description page of Project Settings.

#include "ManagerBase.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"


FVector UManagerBase::GetActorBox()
{
	return ActorBox;
}

FVector UManagerBase::GetActorCenter()
{
	return ActorCenter;
}

void UManagerBase::SetActorBox(const FVector& Cen, const FVector& Vec)
{
	ActorCenter = Cen;
	ActorBox = Vec;
}

UManagerBase::UManagerBase()
	: EnableShadowParameter(false)
	, MapName(TEXT(""))
{
}

UManagerBase::UManagerBase(const FString& InMapName)
	: EnableShadowParameter(false)
	, MapName(InMapName)
{
}

void UManagerBase::StartUp(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters)
{
 	OverrideParameters = InParameters;
	{
		FLatentActionInfo ActionInfo;
		ActionInfo.CallbackTarget = this;
		ActionInfo.ExecutionFunction = FName("OnMapLoadCompletedHandler");
		ActionInfo.Linkage = 0;
		ActionInfo.UUID = 12345;
		UGameplayStatics::LoadStreamLevel(ACatalogPlayerController::Get(), FName(*MapName), true, false, ActionInfo);
	}
}

void UManagerBase::Shutdown()
{
	if (MapName.IsEmpty())
	{
		OnMapUnloadCompletedHandler();
	}
	else
	{
		FLatentActionInfo ActionInfo;
		ActionInfo.CallbackTarget = this;
		ActionInfo.ExecutionFunction = FName("OnMapUnloadCompletedHandler");
		ActionInfo.Linkage = 0;
		ActionInfo.UUID = 12345;
		UGameplayStatics::UnloadStreamLevel(ACatalogPlayerController::Get(), FName(*MapName), ActionInfo, false);
	}
}

bool UManagerBase::IsLocalFileExist(const FString& FilePath)
{
	return FPlatformFileManager::Get().GetPlatformFile().FileExists(*(FPaths::ProjectContentDir().Append(FilePath)));
}

void UManagerBase::OnMapLoadCompletedHandler()
{
	UE_LOG(LogTemp, Log, TEXT("Stream load map %s completed!"), *MapName);
	InitializeManager();
}

void UManagerBase::OnMapUnloadCompletedHandler()
{
	UE_LOG(LogTemp, Log, TEXT("Unload map %s completed!"), *MapName);
	UninitializeManager();
}

void UManagerBase::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	CameraLocation = NewLocation;
	CameraWidth = NewWidth;
}

void UManagerBase::ShowAxis(bool IsShow)
{
}
