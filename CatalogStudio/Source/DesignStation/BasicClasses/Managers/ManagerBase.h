// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "DesignStation/UI/LoadUI/LoadPageWidget.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/Geometry/MultiComponent/ShowMultiComponentActor.h"
#include "CaseSensitiveKeyFuncs.h"
#include "ManagerBase.generated.h"

UENUM()
enum class EManagerType : uint8
{
	EMainManager,
	ESingleComponentManager,
	EMultiComponentManager,
	EMaterialManager,
	EViewManager
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FBackToMainDelegate, const FFolderTableData&, FolderData);

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UManagerBase : public UObject
{
	GENERATED_BODY()

protected:
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>	OverrideParameters;

	UPROPERTY()
		bool							EnableShadowParameter;

	UPROPERTY()
		FString							MapName;

	//
	FVector		CameraLocation;
	float		CameraWidth;

public:
	FBackToMainDelegate OnBackToMain;
	FCameraBtnTypeDelegate	TakePictureTypeDelegate;
	void SetCameraLocation(const FVector& InLocation) { CameraLocation = InLocation; }
	void SetCameraWidth(const float& InCameraWidth) { CameraWidth = InCameraWidth; }
	FVector GetActorBox();
	FVector GetActorCenter();
	void	SetActorBox(const FVector& Cen, const FVector& Box);

	//develop use
	virtual void SyncDBDataToFile() {}

public:
	UManagerBase();

	UManagerBase(const FString& InMapName);

	void StartUp(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParameters);
	void Shutdown();
	bool IsLocalFileExist(const FString& FilePath);

	virtual void ExitGame() {}
	virtual void WindowSizeChanged() {}
	virtual void OnMouseMove() {}
	virtual void OnLeftMouseButtonClick(const FVector& InMousePosition) {}
	virtual void OnRightMouseButtonClick(const FVector& InMousePosition) {}
	virtual void OnKeyClicked(const FKey& InClickKey) {}
	virtual void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth);
	virtual void ShowAxis(bool IsShow);

	virtual void SaveTempData() {}

	virtual AShowMultiComponentActor* GetShowActor() { return nullptr; }
protected:

	virtual void InitializeManager() {}

	virtual void UninitializeManager() {}

	UFUNCTION()
		void OnMapLoadCompletedHandler();

	UFUNCTION()
		void OnMapUnloadCompletedHandler();

private:
	FVector ActorBox;
	FVector ActorCenter;
};
