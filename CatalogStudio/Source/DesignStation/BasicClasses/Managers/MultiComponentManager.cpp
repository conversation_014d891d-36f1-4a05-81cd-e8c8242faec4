// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentManager.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/PopUI/SOneButtonWidget.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/SubSystem/ResourceSubsystem.h"
#include "DesignStation/UI/MacroDef.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/LoadPageWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "ImageProcess/Public/ImageProcess.h"
#include "GrammerAnalysisSubsystem.h"
#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"
#include <Operators/ProtobufOperatorFunctionLibrary.h>
#include <Kismet/KismetSystemLibrary.h>

#define LOCTEXT_NAMESPACE "MultiComponentManager"

void UMultiComponentManager::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnDownloadFileCompleteHandler);
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnUploadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnUpdateResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataArrForParseResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnGetFurnitureOrMatDataArrForParseResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnSearchBackDirctoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.AddUniqueDynamic(this, &UMultiComponentManager::OnGetFurnitureOrMatDataForParseResponseHandler);

}

void UMultiComponentManager::UnBindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.Remove(this, FName(TEXT("OnDownloadFileCompleteHandler")));
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.Remove(this, FName(TEXT("OnUploadFileResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.Remove(this, FName(TEXT("OnUpdateResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataArrForParseResponseDelegate.Remove(this, FName(TEXT("OnGetFurnitureOrMatDataArrForParseResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.Remove(this, FName(TEXT("OnSearchBackDirctoryResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.Remove(this, FName(TEXT("OnGetFurnitureOrMatDataForParseResponseHandler")));
}

void UMultiComponentManager::UpdateDataRequest(const FRefDirectoryData& InData)
{
	UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(InData);
}

void UMultiComponentManager::OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (UpdateUUID.Equals(UUID))
	{
		if (bSuccess && Datas.IsValidIndex(0))
		{
			FFolderTableData InData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], InData);

			const FString ParentPath = URefRelationFunction::GetFolderDirectory(Datas[0].backendFolderPath, false);
			UFolderWidget::Get()->SyncAddData(ParentPath, Datas[0]);
			UFolderWidget::Get()->SyncRefLocalData(Datas[0]);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);

		}
	}
}

void UMultiComponentManager::UploadFileRequest(const FString& FileRelativePath)
{
	UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void UMultiComponentManager::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (OutFilePath.Equals(UploadUUID, ESearchCase::IgnoreCase))
	{
		UploadUUID = TEXT("NoWay");
		if (OutRes)
		{
			UE_LOG(LogTemp, Log, TEXT("Upload file [%s] success!"), *OutFilePath);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Upload file [%s] failed!"), *OutFilePath);
		}

		if (bExitAfterUpload)
		{
			Exit_Inner();
		}
		else
		{
			bExitAfterUpload = true;
		}
	}
}

void UMultiComponentManager::SendDownloadNecessary(const TArray<FString>& Paths, bool Init)
{
	if (Init)
	{
		InitNetUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(Paths);
	}
	else
	{
		RefreshNetUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(Paths);
	}
}

void UMultiComponentManager::SendSearchBackDirctoryRequest_ByFolderID(const FString& InStr)
{
	BackNetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest_FolderID(InStr);
}

void UMultiComponentManager::SendSearchFrontDirctoryRequest_ByFolderID(const FString& InStr)
{
	FrontNetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendFurnitureOrMaterialDataForParseSearchRequest_FolderID(InStr);
}

void UMultiComponentManager::OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths)
{
	if (InitNetUUID.Equals(UUID))
	{
		if (Success)
		{
			if (FilePaths.Num() > 0)
			{
				AlreadyDownloadFiles.Append(FilePaths);
			}
			NeedDownloadPaths.Empty();

			if (FrontNetUUID.IsMatURLParseAdditionFinish())
			{
				Calculate_Inner();
			}
		}
		else
		{
			/*
			*  @@ custom needed download error, put error file to skip list
			*/
			for (const auto& FP : FilePaths)
			{
				const FString AbsPath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FP)
				);
				if (!FPaths::FileExists(AbsPath))
				{
					SkipDependFiles.AddUnique(FP);
				}
			}


			if (FrontNetUUID.IsMatURLParseAdditionFinish())
			{
				Calculate_Inner();
			}
			
		}
	}
	else if(RefreshNetUUID.Equals(UUID))
	{
		if (Success)
		{
			if (FilePaths.Num() > 0)
			{
				AlreadyDownloadFiles.Append(FilePaths);
			}
			NeedDownloadPaths.Empty();

			if (FrontNetUUID.IsMatURLParseAdditionFinish())
			{
				Refresh_Inner();
			}
		}
		else 
		{
			/*
			*  @@ custom needed download error, put error file to skip list
			*/
			for (const auto& FP : FilePaths)
			{
				const FString AbsPath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FP)
				);
				if (!FPaths::FileExists(AbsPath))
				{
					SkipDependFiles.AddUnique(FP);
				}
			}

			if (FrontNetUUID.IsMatURLParseAdditionFinish())
			{
				Refresh_Inner();
			}
		}
	}
	else if (UUID.Equals(FrontNetUUID.ModelDetailToDownloadUUID))
	{
		FrontNetUUID.ResetModelDetailToDownloadAction();
		if (FrontNetUUID.IsMatURLDownloadFiniesh())
		{
			UpdateProcess(0.8f);
			Refresh_Inner();
		}
	}
	else if (UUID.Equals(FrontNetUUID.MatDetailToDownloadUUID))
	{
		FrontNetUUID.ResetMatDetailToDownloadAction();
		if (FrontNetUUID.IsModelURLDownloadFiniesh())
		{
			UpdateProcess(0.8f);
			if (IsInitAction())
			{
				Init_Inner();
			}
			else
			{
				Refresh_Inner();
			}
		}
	}
	else if (UUID.Equals(BackNetUUID.DownloadFile))
	{
		BackNetUUID.ResetDownloadFileAction();
		if (FilePaths.Num() > 0)
		{
			AlreadyDownloadFiles.Append(FilePaths);
		}
		RewriteChange();
	}
	else if (UUID.Equals(FrontNetUUID.DownloadUUID))
	{
		FrontNetUUID.ResetDownloadAction();

		RewriteChange();
	}
	else if (UUID.Equals(FrontNetUUID.DownloadSingleUUID))
	{
		FrontNetUUID.ResetDownloadSingleAction();

		auto NewModel = CurEditDependData.GetModelDependFiles().Last();
		if (NewModel.IsValid() && FilePaths.IsValidIndex(0))
		{
			const FString FilePath = URefRelationFunction::GetZipRelativePath(FilePaths[0], true);
			const FString TargetPath = FPaths::GetPath(FilePath);
			FGraphEventRef GraphEventRef;
			CALL_THREAD_STATIC_OUT(
				GraphEventRef,
				NULL,
				ENamedThreads::AnyThread,
				&URefRelationFunction::UnPackFile,
				FilePath, TargetPath);

			auto LoadPath = FilePaths;
			FGraphEventRef GraphEventAfterRef;
			CALL_THREAD_UFUNCTION_OUT(
				GraphEventAfterRef,
				GraphEventRef,
				ENamedThreads::GameThread,
				this,
				FName(TEXT("RewriteChange_AllReady"))
			);
		}
		else
		{
			RewriteChange_AllReady();
		}
		
	}
}

void UMultiComponentManager::OnSearchBackDirctoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Data)
{
	if (UUID.Equals(BackNetUUID.SearchUUID))
	{
		BackNetUUID.ResetSearchAction();
		if (bSuccess)
		{
			if (Data.IsValidIndex(0))
			{// is custom file

				TArray<FString> ToDownloadPath;
				CustomRefDirectoryData = Data[0];
				if (URefRelationFunction::NeedDownloadFile(Data[0]))
				{//Need Download custom file

					ToDownloadPath.Add(URefRelationFunction::GetRefFileRelativePath(Data[0]));

					/*BackNetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(
						URefRelationFunction::GetRefFileRelativePath(Data[0])
					);*/
				}
				/*else
				{
					RewriteChange();
				}*/

				TArray<FString> UpperFolderIds = URefRelationFunction::GetUpperFolderDirectory(Data[0].backendFolderPath, false);
				for (auto& UFI : UpperFolderIds)
				{
					FString FolderDownloadPath = URefToFileData::GetFileRelativeAddress(UFI);
					ToDownloadPath.AddUnique(FolderDownloadPath);
				}

				if (ToDownloadPath.Num() > 0)
				{
					BackNetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(ToDownloadPath);
				}
				else
				{
					RewriteChange();
				}
			}
			else
			{// no custom file, query import file data
				CustomRefDirectoryData.NoValid();
				SendSearchFrontDirctoryRequest_ByFolderID(EditCompInfo.Value.Value);
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UMultiComponentManager::OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData)
{
	if (UUID.Equals(FrontNetUUID.SearchUUID))
	{
		FrontNetUUID.ResetSearchAction();
		if (bSuccess)
		{
			if (DirData.IsMaterialFile())
			{
				UI_POP_WINDOW_ERROR(
					FText::FromStringTable(FName("PosSt"), TEXT("Material file can't be used as a component!")).ToString()
				);

				//MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID = EditCompInfo.Value;
				RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
				//SkipDependFiles.AddUnique(EditCompInfo.Value.Value);
				SyncIndex = CurrentEditCompIndex;
				Refresh_Inner();
				return;
			}

			if (MultiComponentProperty.ComponentItems.IsValidIndex(EditCompInfo.Key))
			{
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = EditCompInfo.Value.Expression;
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = EditCompInfo.Value.Value;

				auto& MultiCom = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];
				MultiCom.ComponentType = ECompType::ImportModel;

				EmptyOldData(MultiCom);

				auto& ImportModelComp = MultiCom.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
				ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

				auto ModelData = DirData;
				ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

				MultiCom.ComponentName = ModelData.name;
				MultiCom.CodeExp = ModelData.folderCode;
				MultiCom.Code = ModelData.folderCode;
			}

			if (CurEditDependData.ModelAlreadyHas(DirData.folderId))
			{
				RewriteChange();
			}
			else
			{
				CurEditDependData.AddDependFolderID_Model(EditCompInfo.Value);
				CurEditDependData.AddDependFile(DirData);

				UResourceSubsystem::GetInstance()->AddModelWebData({ DirData });

				TArray<FString> ModelFilePath, ModelDownloadPath;
				const bool NeedDownloadModel = (static_cast<FCSModelMatData>(DirData)).AnalysisData(ModelFilePath, ModelDownloadPath);
				if (NeedDownloadModel)
				{
					FrontNetUUID.DownloadSingleUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
						ModelDownloadPath,
						0,
						ModelFilePath
					);
				}
				else
				{
					RewriteChange();
				}
			}
		}
		else
		{
			//UI_POP_WINDOW_ERROR(Msg);

			/*
			*  @@ this step mark ref id is no valid, so need reset this index conmponent data
			*  @@ keep this id
			*/

			//MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Clear();
			EmptyOldData(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID = EditCompInfo.Value;

			RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
			SkipDependFiles.AddUnique(EditCompInfo.Value.Value);
			SyncIndex = CurrentEditCompIndex;
			Refresh_Inner();
		}
	}
}

void UMultiComponentManager::OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData)
{
	if (UUID.Equals(FrontNetUUID.ModelDetailToParseUUID))
	{
		FrontNetUUID.ResetModelDetailToParseAction();
		if (bSuccess)
		{
			if (CurEditDependData.GetDependFolderID_Model().Num() != NetData.Num())
			{
				TArray<FExpressionValuePair> ToSkipIDs;
				for (auto& DependModelIDArr : CurEditDependData.GetDependFolderID_Model())
				{
					DependModelIDArr.FormatValue();

					const int32 DependIDIndex = NetData.IndexOfByPredicate(
						[DependModelIDArr](const FCSModelMatData& NetIter)->bool
						{
							return DependModelIDArr.Value.Equals(NetIter.folderId, ESearchCase::IgnoreCase);
						}
					);
					if (DependIDIndex == INDEX_NONE)
					{
						SkipDependFiles.AddUnique(DependModelIDArr.Value);
						ToSkipIDs.AddUnique(DependModelIDArr);
					}
				}
				CurEditDependData.RemoveDependFileIDs_Model(ToSkipIDs);
			}

			CurEditDependData.AddDependFiles(NetData);

			UResourceSubsystem::GetInstance()->AddModelWebData(NetData);

			if (FrontNetUUID.IsMatURLParseFiniesh())
			{
				ParseDependFiles();
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
	else if(UUID.Equals(FrontNetUUID.MatDetailToParseUUID))
	{
		FrontNetUUID.ResetMatDetailToParseAction();
		if (bSuccess)
		{
			if (CurEditDependData.GetDependFolderID_Mat().Num() != NetData.Num())
			{
				TArray<FExpressionValuePair> ToSkipIDs;
				for (auto& DependIDArr : CurEditDependData.GetDependFolderID_Mat())
				{
					DependIDArr.FormatValue();

					const int32 DependIDIndex = NetData.IndexOfByPredicate(
						[DependIDArr](const FCSModelMatData& NetIter)->bool
						{
							return DependIDArr.Value.Equals(NetIter.folderId, ESearchCase::IgnoreCase);
						}
					);
					if (DependIDIndex == INDEX_NONE)
					{
						SkipDependFiles.AddUnique(DependIDArr.Value);
						ToSkipIDs.AddUnique(DependIDArr);
					}
				}
				CurEditDependData.RemoveDependFileIDs_Model(ToSkipIDs);
			}

			CurEditDependData.AddDependFiles(NetData);

			UResourceSubsystem::GetInstance()->AddMaterialWebData(NetData, NetData);

			if (FrontNetUUID.IsModelURLParseFiniesh())
			{
				ParseDependFiles();
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
	else if (UUID.Equals(FrontNetUUID.MatDetailToParseAdditionUUID))
	{
		FrontNetUUID.ResetMatDetailToParseAdditionAction();
		if (bSuccess)
		{
			for (const auto& ND : NetData)
			{
				/*FMatAdditionInfo& Info = MatAdditionInfo.AddDefaulted_GetRef();
				Info.SetInfo(ND.name, ND.folderId, ND.folderCode, ND.structData);*/

				const int32 Index = MatAdditionInfo.IndexOfByPredicate(
					[&ND](const FMatAdditionInfo& Info)->bool 
					{
						return ND.folderId.Equals(Info.FolderID, ESearchCase::CaseSensitive);
					}
				);
				if (Index != INDEX_NONE)
				{
					MatAdditionInfo[Index].SetInfo(ND.name, ND.folderId, ND.folderCode, ND.structData);
				}
			}

		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}

		if (IsInitAction())
		{
			//ResetInitAction();
			Calculate_Inner();
		}
		else if (IsRefreshAction())
		{
			//ResetRefreshAction();
			Refresh_Inner();
		}
	}
}

void UMultiComponentManager::ParseDependFiles()
{
	TArray<FString> ModelFilePath, ModelDownloadPath;
	const bool NeedDownloadModel = CurEditDependData.GetNeedDownload_Model(ModelFilePath, ModelDownloadPath);

	TArray<FString> MatFilePath, MatDownloadPath;
	TArray<FCSModelMatData> NeedDownloadMatData;
	const bool NeedDownloadMat = CurEditDependData.GetNeedDownload_Mat(MatFilePath, MatDownloadPath, NeedDownloadMatData);
	UResourceSubsystem::GetInstance()->AddMaterialWebData(CurEditDependData.GetMatDependFiles(), NeedDownloadMatData);

	if (!NeedDownloadModel && !NeedDownloadMat)
	{
		UpdateProcess(0.5f);

		UE_LOG(LogTemp, Warning, TEXT("UMultiComponentManager::ParseDependFiles --- No Need Download Model Or Mat"));

		Refresh_Inner();
	}
	else
	{
		UpdateProcess(0.5f);
		if (NeedDownloadModel)
		{
			FrontNetUUID.ModelDetailToDownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
				ModelDownloadPath,
				0,
				ModelFilePath
			);
		}

		if (NeedDownloadMat)
		{
			TArray<FString> EncodeFileURL;
			for (const FString& NDF : MatDownloadPath)
			{
				FString EncodeURL = UUIFunctionLibrary::CatalogEncodeBase64(NDF);
				EncodeFileURL.Add(EncodeURL);
			}
			FrontNetUUID.MatDetailToDownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
				EncodeFileURL,
				1,
				MatFilePath
			);
		}
	}
}

void UMultiComponentManager::RewriteChange()
{
	if(MultiComponentProperty.ComponentItems.IsValidIndex(EditCompInfo.Key))
	{
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = EditCompInfo.Value.Expression;
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = EditCompInfo.Value.Value;

		if (CustomRefDirectoryData.IsValid())
		{
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentName = CustomRefDirectoryData.folderName;
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].CodeExp = CustomRefDirectoryData.folderCodeExp;
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Code = CustomRefDirectoryData.folderCode;

			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].RefFileUUID = CustomRefDirectoryData.md5;

			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentType = ECompType::MultiCom;
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description = CustomRefDirectoryData.description;

			CustomRefDirectoryData.NoValid();
		}

		FString Tips;
		if (SyncParamsFromFile(MultiComponentProperty.ComponentItems[CurrentEditCompIndex], EditCompInfo.Value.Expression, EditCompInfo.Value.Value, Tips))
		{
			SOneButtonWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Prompt"))
				, FText::FromString(Tips)
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm")));
		}

		/*MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentVisibility.Expression = TEXT("1");
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentVisibility.Value = TEXT("1");*/
	}

	if(CurEditDependData.GetModelDependFiles().Num() > 0)
		UResourceSubsystem::GetInstance()->MountDependentPakFiles({ CurEditDependData.GetModelDependFiles().Last() });
	//
	RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
	Refresh_Inner();

	EditCompInfo.Key = INDEX_NONE;
}

void UMultiComponentManager::RewriteChange_AllReady()
{
	if (MultiComponentProperty.ComponentItems.IsValidIndex(EditCompInfo.Key))
	{
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = EditCompInfo.Value.Expression;
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = EditCompInfo.Value.Value;
	}

	if (CurEditDependData.GetModelDependFiles().Num() > 0)
		UResourceSubsystem::GetInstance()->MountDependentPakFiles({ CurEditDependData.GetModelDependFiles().Last() });

	//
	RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
	Refresh_Inner();

	EditCompInfo.Key = INDEX_NONE;
}

UMultiComponentManager::UMultiComponentManager()
	: UManagerBase(TEXT("CompMap"))
	  , MultiComponentDisplayer(nullptr)
	  , MultiUIManager(nullptr)
	  , AxisActor(nullptr)
      , CurrentEditCompIndex(-1)
	  , CurrentPictureType(-1)
	  , OpenImage(TEXT(""))
{
}

AShowMultiComponentActor* UMultiComponentManager::GetShowActor()
{
	return MultiComponentDisplayer;
}

void UMultiComponentManager::Init_Inner()
{
	UE_LOG(LogTemp, Warning, TEXT("UMultiComponentManager::Init_Inner [SyncIndex : %d] --- Start"), SyncIndex);

	ResetInitAction();
	ResetRefreshAction();

	if (SyncIndex == INDEX_NONE)
	{
		MultiComponentProperty.GenerateID();
		this->RefreshMultiComponentActor(true);
		this->UpdateAxis();
		if (MultiUIManager)
		{
			MultiUIManager->SetMultiComponentParentParameters(OverrideParameters);
			MultiUIManager->UpdateContent(MultiComponentProperty);
		}
		EndProcess();
	}
	else
	{
		MultiComponentProperty.GenerateID();
		this->RefreshMultiComponentActor(true);
		if (MultiUIManager)
		{
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		}
		this->UpdateAxis();
	}

	SyncIndex = INDEX_NONE;
	UE_LOG(LogTemp, Warning, TEXT("UMultiComponentManager::Init_Inner --- End"));
}

void UMultiComponentManager::Calculate_Inner(bool IsInit /*= true*/)
{
	TArray<FExpressionValuePair> DependModelIDS;
	TArray<FExpressionValuePair> DependMatIDS;

	//内部解析时材质信息，主要为获取structData,影响结构
	TArray<FExpressionValuePair> InnerDependMatID;
	NeedDownloadPaths.Empty();
	const bool NeedDownload = URefRelationFunction::GetMultiComponentInfo(
		URefRelationFunction::GetMarkToBackendDirectory(MultiCompFolderData),
		ACatalogPlayerController::Get()->GetGlobalParameterMap(),
		OverrideParameters,
		RefFileData,
		NeedDownloadPaths,
		InnerDependMatID,
		DependModelIDS,
		DependMatIDS,
		CurEditDependData,
		SkipDependFiles,
		AlreadyDownloadFiles,
		MatAdditionInfo
	);
	if (NeedDownload)
	{
		if (NeedDownloadPaths.Num() > 0 || InnerDependMatID.Num() > 0)
		{
			//.dat file
			for (const auto& SDF : SkipDependFiles)
			{
				if (NeedDownloadPaths.Contains(SDF) || AlreadyDownloadFiles.Contains(SDF))
				{
					NeedDownloadPaths.Remove(SDF);
				}
			}
			if (NeedDownloadPaths.Num() > 0)
			{
				SendDownloadNecessary(NeedDownloadPaths, IsInit);
			}

			//mat structData
			TArray<FString> MatIDs;
			for (const auto& IDMI : InnerDependMatID)
			{
				const int32 Index = MatAdditionInfo.IndexOfByPredicate(
					[&IDMI](const FMatAdditionInfo& Info)->bool
					{
						return IDMI.Value.Equals(Info.FolderID, ESearchCase::CaseSensitive);
					}
				);
				if (Index == INDEX_NONE)
				{
					FMatAdditionInfo& Info = MatAdditionInfo.AddDefaulted_GetRef();
					Info.FolderID = IDMI.Value;

					MatIDs.AddUnique(IDMI.Value);
				}
			}

			if (MatIDs.Num() > 0)
			{
				FrontNetUUID.MatDetailToParseAdditionUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					MatIDs, 1
				);
			}
		}
		else if(DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0)
		{
			//level tree part are complete
			MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;

			TArray<FString> MatDepend;
			TArray<FString> ModelDepend;

			for (auto Iter : DependModelIDS)
			{
				ModelDepend.AddUnique(Iter.Value);
			}

			for (auto Iter : DependMatIDS)
			{
				MatDepend.AddUnique(Iter.Value);
			}

			CurEditDependData.SetDependFolderID_Model(DependModelIDS);
			CurEditDependData.SetDependFolderID_Mat(DependMatIDS);

			if (DependModelIDS.Num() > 0)
			{
				FrontNetUUID.ModelDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					ModelDepend, 1
				);
			}
			if (DependMatIDS.Num() > 0)
			{
				FrontNetUUID.MatDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					MatDepend, 1
				);
			}
		}
		else
		{
			UResourceSubsystem::GetInstance()->MountDependentPakFiles(CurEditDependData.GetModelDependFiles());

			MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;

			Init_Inner();
		}
	}
	else
	{
		UResourceSubsystem::GetInstance()->MountDependentPakFiles(CurEditDependData.GetModelDependFiles());

		MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;

		Init_Inner();
	}
}

void UMultiComponentManager::Refresh_Inner()
{
	//Calculate_Inner(false);

	TArray<FExpressionValuePair> DependModelIDS;
	TArray<FExpressionValuePair> DependMatIDS;

	TArray<FExpressionValuePair> InnerDependMatID;
	NeedDownloadPaths.Empty();

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GlobalParams = ACatalogPlayerController::Get()->GetGlobalParameterMap();

	if (OverrideParameters.Contains(TEXT("DZCZ")))
	{
		const FParameterData DZCZData = OverrideParameters[TEXT("DZCZ")];
		const int32& ReadyIndex = MatAdditionInfo.IndexOfByPredicate(
			[&DZCZData](const FMatAdditionInfo& InInfo)->bool
			{
				return DZCZData.Data.value.Equals(InInfo.FolderID, ESearchCase::CaseSensitive);
			}
		);
		if (ReadyIndex != INDEX_NONE)
		{
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> NoInnerParams;
			const FMatAdditionInfo& CurMatInfo = MatAdditionInfo[ReadyIndex];
			TArray<FString> AdditionMatStr = CurMatInfo.GetAdditionStrArr();
			for (const auto& AMS : AdditionMatStr)
			{
				FString Exp, Value;
				AMS.Split(TEXT("="), &Exp, &Value);
				if (!Exp.IsEmpty() && !Value.IsEmpty())
				{
					if (NoInnerParams.Contains(Exp))
					{
						NoInnerParams[Exp].Data.expression = Value;
						NoInnerParams[Exp].Data.value = Value;
					}
					else if (GlobalParams.Contains(Exp))
					{//no param, new param to add
						FParameterData ToAddParam = GlobalParams[Exp];
						ToAddParam.Data.expression = Value;
						ToAddParam.Data.value = Value;
						NoInnerParams.Add(Exp, ToAddParam);
					}
				}
			}

			UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, NoInnerParams, OverrideParameters);

		}
	}
	URefRelationFunction::ParseMatAdditionData(GlobalParams, OverrideParameters, MatAdditionInfo);

	const bool NeedDownload = URefRelationFunction::GetMultiComponentInfo(
		GlobalParams,
		OverrideParameters,
		RefFileData,
		NeedDownloadPaths,
		InnerDependMatID,
		DependModelIDS,
		DependMatIDS,
		CurEditDependData,
		SkipDependFiles,
		AlreadyDownloadFiles,
		MatAdditionInfo
	);

	/*
	*  @@ custom needed already download
	*/
	if (NeedDownload)
	{
		if (NeedDownloadPaths.Num() > 0 || InnerDependMatID.Num() > 0)
		{
			for (const auto& SDF : SkipDependFiles)
			{
				if (NeedDownloadPaths.Contains(SDF) || AlreadyDownloadFiles.Contains(SDF))
				{
					NeedDownloadPaths.Remove(SDF);
				}
			}
			if (NeedDownloadPaths.Num() > 0)
			{
				SendDownloadNecessary(NeedDownloadPaths, false);
			}

			//mat structData
			TArray<FString> MatIDs;
			for (const auto& IDMI : InnerDependMatID)
			{
				const int32 Index = MatAdditionInfo.IndexOfByPredicate(
					[&IDMI](const FMatAdditionInfo& Info)->bool
					{
						return IDMI.Value.Equals(Info.FolderID, ESearchCase::CaseSensitive);
					}
				);
				if (Index == INDEX_NONE)
				{
					FMatAdditionInfo& Info = MatAdditionInfo.AddDefaulted_GetRef();
					Info.FolderID = IDMI.Value;

					MatIDs.AddUnique(IDMI.Value);
				}
			}

			if (MatIDs.Num() > 0)
			{
				FrontNetUUID.MatDetailToParseAdditionUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					MatIDs, 1
				);
			}
		}
		else if (DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0)
		{
			//level tree part are complete
			MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;

			TArray<FString> MatDepend;
			TArray<FString> ModelDepend;

			for (auto Iter : DependModelIDS)
			{
				ModelDepend.AddUnique(Iter.Value);
			}

			for (auto Iter : DependMatIDS)
			{
				MatDepend.AddUnique(Iter.Value);
			}

			CurEditDependData.SetDependFolderID_Model(DependModelIDS);
			CurEditDependData.SetDependFolderID_Mat(DependMatIDS);

			if (DependModelIDS.Num() > 0)
			{
				FrontNetUUID.ModelDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					ModelDepend, 1
				);
			}
			if (DependMatIDS.Num() > 0)
			{
				FrontNetUUID.MatDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
					MatDepend, 1
				);
			}
		}
		else
		{
			UResourceSubsystem::GetInstance()->MountDependentPakFiles(CurEditDependData.GetModelDependFiles());

			MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;
			Init_Inner();
		}
	}
	else
	{
		UResourceSubsystem::GetInstance()->MountDependentPakFiles(CurEditDependData.GetModelDependFiles());

		MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;
		Init_Inner();
	}
}

//void UMultiComponentManager::Refresh_Inner_AllReady()
//{
//	TArray<FExpressionValuePair> DependModelIDS;
//	TArray<FExpressionValuePair> DependMatIDS;
//
//	TArray<FExpressionValuePair> InnerDependMatID;
//	const bool NeedDownload = URefRelationFunction::GetMultiComponentInfo(
//		ACatalogPlayerController::Get()->GetGlobalParameterMap(),
//		OverrideParameters,
//		RefFileData,
//		NeedDownloadPaths,
//		InnerDependMatID,
//		DependModelIDS,
//		DependMatIDS,
//		CurEditDependData,
//		SkipDependFiles,
//		AlreadyDownloadFiles,
//		MatAdditionInfo
//	);
//
//	/*
//	*  @@ custom needed already download
//	*/
//	MultiComponentProperty.ComponentItems = RefFileData.ChildComponent;
//
//	UResourceSubsystem::GetInstance()->MountDependentPakFiles(CurEditDependData.GetModelDependFiles());
//
//	Init_Inner();
//}

void UMultiComponentManager::EmptyOldData(FMultiComponentDataItem& EditCompoent)
{
	EditCompoent.ComponentName.Empty();
	EditCompoent.Code.Empty();
	EditCompoent.CodeExp.Empty();
	EditCompoent.Description.Empty();

	EditCompoent.ChildComponent.Empty();
	EditCompoent.SingleComponentData.ComponentItems.Empty();
}

void UMultiComponentManager::InitMultiComponentUI()
{
	MultiUIManager = NewObject<UMultiComponentUIManager>();
	MultiUIManager->InitMultiComponentLayout();
	//MultiUIManager->SetMultiComponentParentParameters(OverrideParameters);
	//MultiUIManager->UpdateContent(MultiComponentProperty);
	MultiUIManager->MultiComponentToolBarDelegate.BindUFunction(this, FName(TEXT("OnToolbarOperatorHandler")));
	MultiUIManager->MultiComponentPropertyDelegate.BindUFunction(this, FName(TEXT("OnComponentNameIDChangedHandler")));
	MultiUIManager->MultiComponentLocationDelegate.BindUFunction(this, FName(TEXT("OnLocationChangedHandler")));
	MultiUIManager->MultiComponentRotationDelegate.BindUFunction(this, FName(TEXT("OnRotationChangedHandler")));
	MultiUIManager->MultiComponentScaleDelegate.BindUFunction(this, FName(TEXT("OnScaleChangedHandler")));
	MultiUIManager->MultiComponentParamDelegate.BindUFunction(this, FName(TEXT("OnComponentParameterChangedHandler")));
	MultiUIManager->MultiComponentParamAddDelegate.BindUFunction(this, FName(TEXT("OnComponentParameterAddHandler")));
	MultiUIManager->MultiComponentTreeActionDelegate.BindUFunction(this, FName(TEXT("OnComponentTreeListActionHandler")));
	MultiUIManager->MultiComponentPictureTypeDelegate.BindUFunction(this, FName(TEXT("TakePictureHandler")));
	MultiUIManager->BtnUpDownClickDelegate.BindUObject(this, &UMultiComponentManager::OnParameterUPDown);
}

void UMultiComponentManager::RefreshActor()
{
#ifdef USE_REF_LOCAL_FILE
	
#else
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);
#endif

	MultiComponentProperty.GenerateID();
	this->RefreshMultiComponentActor();
}

bool UMultiComponentManager::ParseAffectedParameters(const TArray<FParameterData>& ParametersToCheck, TArray<FParameterData>& AffectedParameters, const FString& ModifyParameter)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	AffectedParameters.Empty();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = OverrideParameters;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(PeerParameters, ParametersToCheck);
	
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, ParentParameters, PeerParameters);
	for (const auto& iter : PeerParameters)
	{
		AffectedParameters.Add(iter.Value);
	}
	//auto OverrideParentParameters = OverrideParameters;
	//for (auto Parameter : ParametersToCheck)
	//{//同级引用，因此将本级变量与父级变量合并
	//	if (OverrideParentParameters.Contains(Parameter.Data.name))
	//		OverrideParentParameters[Parameter.Data.name] = Parameter;
	//	else
	//		OverrideParentParameters.Add(Parameter.Data.name, Parameter);
	//}
	//TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	//AffectedParameters.Empty();
	//{//需要解析同级中受影响的变量
	//	TArray<FString> AffectedParameterNames;
	//	FParameterEffectionParser Parser;
	//	Parser.FindParametersAffectBySpecificParameter(ParametersToCheck, ModifyParameter, AffectedParameterNames, true);
	//	while (AffectedParameterNames.Num() > 0)
	//	{//存在受新添加变量影响的变量，逐个计算并更新
	//		const FString ParameterName = AffectedParameterNames.Top();
	//		AffectedParameterNames.Pop();
	//		const int32 Index = ParametersToCheck.IndexOfByPredicate([ParameterName](const FParameterData& InOther) { return InOther.Data.name.Equals(ParameterName); });
	//		if (INDEX_NONE == Index) continue;
	//		auto ThisParam = OverrideParentParameters[ParameterName];
	//		OverrideParentParameters.Remove(ParameterName);
	//		bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, OverrideParentParameters, ThisParam);
	//		OverrideParentParameters.Add(ParameterName, ThisParam);
	//		if (!Res)
	//		{
	//			UE_LOG(LogTemp, Error, TEXT("UParameterDetailWidget::OnClickedBtnSave parameter[%s] affect [%s] calculate failed"), *ModifyParameter, *ParameterName);
	//			return false;
	//		}
	//		AffectedParameters.Add(OverrideParentParameters[ParameterName]);
	//	}
	//}
	return true;
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> UMultiComponentManager::GetFolderInheritParams(const FString& InID, bool NeedGetFile)
{
	FRefToLocalFileData CurFileData;
	if (!URefRelationFunction::GetCurrentRefRelationFromFile(InID, CurFileData))
	{
		UE_LOG(LogTemp, Error, TEXT("get [%s] dat file data error"), *InID);
	}

	TArray<FString> AleadyDownloadFiles;
	TArray<FString> NeedDownloadIDS;
	TArray<FString> UpperIDs;
	URefRelationFunction::SpliteUpperFolderDirectory(CurFileData.FolderDBData.backend_directory, CurFileData.FolderDBData.folder_id, NeedGetFile, AleadyDownloadFiles, UpperIDs, NeedDownloadIDS);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritParams = URefRelationFunction::GetUpperFolderInheritParams(UpperIDs);
	return InheritParams;
}

void UMultiComponentManager::InitShowManager()
{
	if (!IS_OBJECT_PTR_VALID(MultiComponentDisplayer))
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewActor = ACatalogPlayerController::Get()->GetWorld()->SpawnActor(AShowMultiComponentActor::StaticClass(), &tran);
		MultiComponentDisplayer = Cast<AShowMultiComponentActor>(NewActor);
	}

	if (!IS_OBJECT_PTR_VALID(AxisActor))
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewActor = ACatalogPlayerController::Get()->GetWorld()->SpawnActor(ABackgroundAxis::StaticClass(), &tran);
		AxisActor = Cast<ABackgroundAxis>(NewActor);
		this->UpdateAxis();
	}
	{
		ACatalogPlayerController::Get()->OnScreenShotCompletedDelegate.BindUFunction(this, FName(TEXT("ProcessImageHandler")));
	}
}

void UMultiComponentManager::InitializeManager()
{
	InitMultiComponentUI();

	BindDelegate();

	StartProcess();

	InitShowManager();

	UParameterDetailWidget::Get()->PreReadyParamType();

	CustomRefDirectoryData = FRefDirectoryData(TEXT(""));

	OverrideParameters.Empty();
	MultiComponentProperty.ComponentItems.Empty();

#ifdef USE_REF_LOCAL_FILE 

	//get global params
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();

	//get upper levels folder params 
	GET_UPPER_LEVEL_HERITPARAMETERS(MultiCompFolderData, OverrideParameters, true);
	//计算传递的参数
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, {}, OverrideParameters);

	Calculate_Inner();
	
#else

	FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParameters(true, MultiCompFolderData.id, OverrideParameters);
	FMultiComTableOperatorLibrary::RetriveFileMultiComponents(MultiCompFolderData.id, MultiComponentProperty.ComponentItems);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);

	MultiComponentProperty.GenerateID();
	InitMultiComponentUI();
	this->RefreshMultiComponentActor(true);
	this->UpdateAxis();
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Collapsed);

#endif
	
}

void UMultiComponentManager::UninitializeManager()
{
	UnBindDelegate();
	if (MultiUIManager)
	{
		MultiUIManager->Clear();
		MultiUIManager = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(MultiComponentDisplayer) && IsValid(MultiComponentDisplayer))
		MultiComponentDisplayer->DestroyDisplayer();
	if (IS_OBJECT_PTR_VALID(AxisActor) && IsValid(AxisActor))
		AxisActor->Destroy();
}

void UMultiComponentManager::WindowSizeChanged()
{
	if (MultiUIManager)
	{
		MultiUIManager->RefreshSceneViewportSize();
	}
}

void UMultiComponentManager::UpdateAxis()
{
	if (!IS_OBJECT_PTR_VALID(AxisActor))
		return;

	AxisActor->UpdateAxisType(EPlanPolygonBelongs::EUnknown);
	FVector CenterPoint = FVector::ZeroVector;
	FVector BoxExtent = FVector::ZeroVector;
	MultiComponentDisplayer->GetActorBounds(false, CenterPoint, BoxExtent);
	SetActorBox(CenterPoint, BoxExtent);
	AxisActor->ChangeSizeByContentSize(CenterPoint + BoxExtent);
	AxisActor->ChangeTagRotation(CameraLocation, CameraWidth);
}

bool UMultiComponentManager::RefreshMultiComponentActor(bool RefreshMesh)
{
	UE_LOG(LogTemp, Warning, TEXT("UMultiComponentManager::RefreshMultiComponentActor [ RefreshMesh : %d]"), RefreshMesh);
	if (RefreshMesh)
	{
		TArray<FShowMultiComponentActorProperty> MultiComProperty;
		MultiComProperty.AddZeroed(MultiComponentProperty.ComponentItems.Num());
		int32 i = 0;
		FString DMValue(TEXT(""));
		if (OverrideParameters.Contains(TEXT("DM")))
		{
			const FParameterData& DMParameter = OverrideParameters[TEXT("DM")];
			DMValue = DMParameter.Data.value;
		}
		UE_LOG(LogTemp, Warning, TEXT("MultiComponentProperty.ComponentItems [Num : %d] --- MultiComProperty [Num : %d]")
			, MultiComponentProperty.ComponentItems.Num(), MultiComProperty.Num());
		for (auto& Iter : MultiComponentProperty.ComponentItems)
		{
			UE_LOG(LogTemp, Warning, TEXT("MultiComProperty [Index : %d]"), i);
			if (!Iter.IsVisiable())
			{
				++i;
				continue;
			}
			FGeometryDatas::GenerateMesh(Iter, MultiComProperty[i++], DMValue);
		}
		if (IS_OBJECT_PTR_VALID(MultiComponentDisplayer) && IsValid(MultiComponentDisplayer))
		{
			MultiComponentDisplayer->RefreshComponentMesh(MultiComProperty);
			MultiComponentDisplayer->UpdateSelection(CurrentEditCompIndex, true);
			return true;
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("MultiComponentDisplayer No Valid"));
		}
	}
	return false;
}

void UMultiComponentManager::StartProcess()
{
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	ULoadPageWidget::GetInstance()->InitLoadPage();
	ULoadPageWidget::GetInstance()->SetLoadingPercent(0.1f);
}

void UMultiComponentManager::UpdateProcess(float InProcess)
{
	ULoadPageWidget::GetInstance()->SetLoadingPercent(InProcess);
}

void UMultiComponentManager::EndProcess()
{
	ULoadPageWidget::GetInstance()->SetLoadingPercent(1.0f);
	ULoadPageWidget::GetInstance()->LoadDone(true);
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Collapsed);
}

void UMultiComponentManager::OnToolbarOperatorHandler(const EMultiCompToolBarType& EditType)
{
	if (EMultiCompToolBarType::Exit == EditType)
	{
		this->OnClickExitsButtonHandler();
	}
	else if (EMultiCompToolBarType::Recover == EditType)
	{
		FString FilePath;
		FCatalogFunctionLibrary::OpenFileDialogForDatFile(FilePath);
		if (FPaths::FileExists(FilePath))
		{
			FRefToLocalFileData FileData;
			UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FilePath, FileData);

			RefFileData.ComponentParameters = FileData.ParamDatas;
			RefFileData.ChildComponent = URefToFileData::ConvertToMultiComponentData(FileData.ComponentDatas);
			Refresh_Inner();
		}
	}
}

bool UMultiComponentManager::SyncParamsFromFile(FMultiComponentDataItem& ComponentNode, const FString& NewFolderIDExpression, const FString& NewFolderID, FString& Tips)
{
	FRefToLocalFileData RefFileData;
	if (URefRelationFunction::GetCurrentRefRelationFromFile(NewFolderID, RefFileData))
	{
		TArray<FString> ExpressionCalName;
		if (!NewFolderIDExpression.IsNumeric())
		{
			FString ErrorMessage;
            if (UGrammerAnalysisSubsystem* GrammerAnalysis = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>())
			{
				GrammerAnalysis->LexicalAnalysis(NewFolderIDExpression, ExpressionCalName, ErrorMessage);
			}
		}
		
		TArray<FString> CalculateParamsInExpression;
		for (const auto& Parameter : RefFileData.ParamDatas)
		{
			int32 ParameterIndex = ComponentNode.ComponentParameters.IndexOfByPredicate(
				[&Parameter](const FParameterData& InParam)->bool 
				{
					return Parameter.Data.name.Equals(InParam.Data.name,ESearchCase::CaseSensitive);
				}
			);
			if (ParameterIndex != INDEX_NONE)
			{
				if (ExpressionCalName.ContainsByPredicate([&](const FString& InName) {return Parameter.Data.name.Equals(InName,ESearchCase::CaseSensitive);}))
				{
					CalculateParamsInExpression.AddUnique(Parameter.Data.name);
				}
				else
				{
					ComponentNode.ComponentParameters[ParameterIndex] = Parameter;
				}
			}
			else
			{
				//需求修改 --- 引用时不再拉取文件参数，文件参数带入节点解析中计算
				//ComponentNode.AddNewParameter(Parameter);
			}
		}

		if (CalculateParamsInExpression.Num() > 0)
		{
			Tips = TEXT("参数[ ");
			for (int32 i = 0; i < CalculateParamsInExpression.Num(); i++)
			{
				Tips += CalculateParamsInExpression[i];
				if (i != CalculateParamsInExpression.Num() - 1)
				{
					Tips += TEXT(", ");
				}
			}
			Tips += TEXT(" ]用于表达式计算，不替换");

			return !Tips.IsEmpty();
		}
		
		return false;
	}
	else
	{
		//Tips = FString::Printf(TEXT("读取文件%s.dat失败"), *NewFolderID);
		return false;
	}

}

void UMultiComponentManager::OnClickExitsButtonHandler()
{
	//if (OriginalMultiComponentProperty != MultiComponentProperty)
	//{
	//	EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
	//		FText::FromStringTable(FName("PosSt"), TEXT("Save Multi Component"))
	//		, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this multi component ?"))
	//		, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
	//		, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
	//	if (EPopButtonType::Confirm == ButtonType)
	//	{
	//		this->OnClickSaveButtonHandler();
	//	}
	//	else if(EPopButtonType::Close == ButtonType)
	//	{
	//		return;
	//	}
	//}

#ifdef USE_REF_LOCAL_FILE
	GET_CACHE_REF_DATA(MultiCompFolderData);
	bool bChange = URefRelationFunction::IsConstructChange(RefData.ComponentDatas, MultiComponentProperty.ComponentItems);

	if (URefRelationFunction::EnsureDefaultExpressionValid(RefData.ParamDatas) 
		|| URefRelationFunction::EnsureMultiComponentDefaultExpress(MultiComponentProperty.ComponentItems))
	{
		bChange = true;
	}

	if (bChange)
	{
		bExitAfterUpload = true;

		RefData.ComponentDatas.Empty();
		for (const auto Iter : MultiComponentProperty.ComponentItems)
		{
			auto& NewComp = RefData.ComponentDatas.AddDefaulted_GetRef();
			NewComp.Init(Iter);
		}

		{//sync file path
			RefData.FolderDBData.folder_id = MultiCompFolderData.folder_id;
		}    

		UFolderWidget::Get()->AddCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(MultiCompFolderData), RefData);
		UFolderWidget::Get()->SyncSelect(RefData);

		FRefDirectoryData CurRefData;
		if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(MultiCompFolderData.id, CurRefData))
		{
			URefRelationFunction::ConvertDBDataToDirctoryData(MultiCompFolderData, CurRefData);
		}

		URefRelationFunction::SaveFile(RefData);
		if (CurRefData.IsValid())
		{
			FVector MeshCenter, MeshExtension;
			MultiComponentDisplayer->GetDisplayerBound(MeshCenter, MeshExtension);
			//left down behind
			FVector LDBPoint = MeshCenter - MeshExtension;
			CurRefData.boxOffset = LDBPoint.ToString();
			
			int64 FileSize = 0;
			const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
				FPaths::ProjectContentDir(),
				URefRelationFunction::GetRefFileRelativePath(CurRefData)
			));
			ACatalogPlayerController::GetFileMD5AndSize(AbsPath, CurRefData.md5, FileSize);
			UpdateDataRequest(CurRefData);
			UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(RefData));
		}
		else
		{
			UI_POP_WINDOW_ERROR(TEXT("single component sync error"));
			Exit_Inner();
		}
	}
	else
	{
		Exit_Inner();
	}
#endif

	
}

void UMultiComponentManager::Exit_Inner()
{
	OnBackToMain.ExecuteIfBound(MultiCompFolderData);
}

void UMultiComponentManager::ExitGame()
{
	this->OnClickExitsButtonHandler();
}

void UMultiComponentManager::OnLeftMouseButtonClick(const FVector& InMousePosition)
{
	if (!IS_OBJECT_PTR_VALID(MultiComponentDisplayer))
	{
		return;

	}
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	FHitResult Res;
	FVector WorldLocation, WorldDir;
	PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
	//UKismetSystemLibrary::DrawDebugLine(PC, WorldLocation, WorldLocation+ WorldDir * 10000.f, FLinearColor::Red,10.f,1.f);

	FCollisionQueryParams QueryParams;
	QueryParams.bTraceComplex = true;
	FHitResult OutResult;

	bool bHit = PC->GetWorld()->LineTraceSingleByChannel(OutResult, WorldLocation, WorldLocation + WorldDir * 10000.f
		, ECollisionChannel::ECC_Visibility, QueryParams);
	if (!bHit)
	{
		return;
	}
	AActor* TargetActor = OutResult.GetActor();
	USceneComponent* ParentComponent = nullptr;
	if (TargetActor->IsA<AImportPakBaseClass>() && TargetActor->GetRootComponent())
	{
		ParentComponent = TargetActor->GetRootComponent()->GetAttachParent();
	}
	else
	{
		if (OutResult.GetActor() != MultiComponentDisplayer)
			return;
		ParentComponent = OutResult.GetComponent()->GetAttachParent();
	}
	int32 HitChildIndex = MultiComponentDisplayer->GetChildRootIndex(ParentComponent);
	if (MultiUIManager)
	{
		MultiUIManager->SetSelectComponentByHit(HitChildIndex);
	}
}

FString UMultiComponentManager::CombineIdAndDescription(const FString& Id, const FString& Description)
{
	int32 DotIndex = INDEX_NONE;
	Id.FindChar('.', DotIndex);
	if (INDEX_NONE != DotIndex)
	{
		return Id.Left(DotIndex) + TEXT("   ") + Description;
	}
	return Id + TEXT("   ") + Description;
}

void UMultiComponentManager::OnComponentNameIDChangedHandler(const EComponentIDType& EditType, const FString& InString)
{
	if (MultiComponentProperty.ComponentItems.IsValidIndex(CurrentEditCompIndex))
	{
		SyncIndex = CurrentEditCompIndex;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		FMultiComponentDataItem OriginalData = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];
		bool NeedUpdate = false;
		if (EComponentIDType::Name == EditType)
		{
			if (OriginalData.ComponentName != InString)
			{
				OriginalData.ComponentName = InString;
				NeedUpdate = true;
			}
		}
		else if (EComponentIDType::Expression == EditType)
		{
			FExpressionValuePair NewID(InString);
			//TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
			//UParameterRelativeLibrary::CombineParameters(OverrideParameters, OverrideParameters, PeerParameters);
			//UParameterRelativeLibrary::CombineParameters(PeerParameters, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
			//auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
			bool Res = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewID);
			if (Res && OriginalData.ComponentID != NewID)
			{
				int32 DotIndex = INDEX_NONE;
				NewID.Value.FindChar('.', DotIndex);

				auto Temp = MultiComponentProperty;
				if (DotIndex == INDEX_NONE)
				{
					//NewID.Value = NewID.Value.Len() > 8 ? NewID.Value.Left(8) : NewID.Value;
					Temp.ComponentItems[CurrentEditCompIndex].ComponentID = NewID;
				}
				else
				{
					Temp.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = NewID.Expression;
					//MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = NewID.Value.Left(DotIndex).Len() > 8 ? NewID.Value.Left(DotIndex).Left(8) : NewID.Value.Left(DotIndex);
					Temp.ComponentItems[CurrentEditCompIndex].ComponentID.Value = NewID.Value.Left(DotIndex);
					//OriginalData.ComponentID.Value = NewID.Value.Left(DotIndex);
				}
				//if (DotIndex == INDEX_NONE)
				//{
				//	//NewID.Value = NewID.Value.Len() > 8 ? NewID.Value.Left(8) : NewID.Value;
				//	MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID = NewID;
				//}
				//else
				//{
				//	MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = NewID.Expression;
				//	//MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = NewID.Value.Left(DotIndex).Len() > 8 ? NewID.Value.Left(DotIndex).Left(8) : NewID.Value.Left(DotIndex);
				//	MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = NewID.Value.Left(DotIndex);
				//	//OriginalData.ComponentID.Value = NewID.Value.Left(DotIndex);
				//}
				bool bValid = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), Temp);
				if (false == bValid)
				{
					SOneButtonWidget::PopupModalWindow(
						FText::FromStringTable(FName("PosSt"), TEXT("Error"))
						, FText::FromStringTable(FName("PosSt"), TEXT("Multicomponent has circle refrence!"))
						, FText::FromStringTable(FName("PosSt"), TEXT("Confirm")));
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex] = OriginalData;

					MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					this->UpdateAxis();
				}
				else
				{
#ifdef USE_REF_LOCAL_FILE

					NewID.FormatValue();
					EditCompInfo.Key = CurrentEditCompIndex;
					EditCompInfo.Value = NewID;

				/*	FString Tip;
					if (SyncParamsFromFile(MultiComponentProperty.ComponentItems[CurrentEditCompIndex], NewID.Expression, NewID.Value, Tip))
					{
						UE_LOG(LogTemp, Error, TEXT("Tip : %s"), *Tip);
						SOneButtonWidget::PopupModalWindow(
							FText::FromStringTable(FName("PosSt"), TEXT("Prompt"))
							, FText::FromString(Tip)
							, FText::FromStringTable(FName("PosSt"), TEXT("Confirm")));
					}*/

					if (CurEditDependData.ModelAlreadyHas(NewID.Value))
					{
						MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = EditCompInfo.Value.Expression;
						MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = EditCompInfo.Value.Value;

						auto& MultiCom = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];
						MultiCom.ComponentType = ECompType::ImportModel;

						EmptyOldData(MultiCom);

						auto& ImportModelComp = MultiCom.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
						ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

						FCSModelMatData ModelData;
						if (CurEditDependData.GetDependFile(MultiCom.ComponentID.Value, ModelData))
						{
							ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

							MultiCom.ComponentName = ModelData.name;
							MultiCom.CodeExp = ModelData.folderCode;
							MultiCom.Code = ModelData.folderCode;
						}
						else
						{
							MultiCom.ComponentName = TEXT("");
							MultiCom.CodeExp = TEXT("");
							MultiCom.Code = TEXT("");
						}

						RewriteChange();
					}
					else
					{
						if (NewID.Value.IsEmpty())
						{
							MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Clear();
							EditCompInfo.Key = INDEX_NONE;

							RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
							Refresh_Inner();
						}
						else
						{
							SendSearchBackDirctoryRequest_ByFolderID(NewID.Value);
						}
					}

				/*	RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
					Refresh_Inner();*/
					
#else
					FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					FGeometryDatas::LoadMultiComponent(GlobalParameters, OverrideParameters, MultiComponentProperty);
					MultiComponentProperty.GenerateID();
					this->RefreshMultiComponentActor(true);

					MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					this->UpdateAxis();
#endif

				}
				
			}
			else
			{
				MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
			}
			return;
		}
		else if (EComponentIDType::Value == EditType)
		{
			int32 DotIndex = -1;
			if (InString.IsNumeric())
			{
				auto Temp = MultiComponentProperty;
				FExpressionValuePair NewID(InString);

				Temp.ComponentItems[CurrentEditCompIndex].ComponentID = FExpressionValuePair(InString);
				bool bValid = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), Temp);
				if (false == bValid)
				{
					SOneButtonWidget::PopupModalWindow(
						FText::FromStringTable(FName("PosSt"), TEXT("Error"))
						, FText::FromStringTable(FName("PosSt"), TEXT("Multicomponent has circle refrence!"))
						, FText::FromStringTable(FName("PosSt"), TEXT("Confirm")));
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex] = OriginalData;

					MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					this->UpdateAxis();
				}
				else
				{
#ifdef USE_REF_LOCAL_FILE

					NewID.FormatValue();
					EditCompInfo.Key = CurrentEditCompIndex;
					EditCompInfo.Value = NewID;

					//FString Tip = TEXT("");
					//SyncParamsFromFile(MultiComponentProperty.ComponentItems[CurrentEditCompIndex], NewID.Expression, NewID.Value, Tip);

					if (CurEditDependData.ModelAlreadyHas(NewID.Value))
					{
						MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Expression = EditCompInfo.Value.Expression;
						MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value = EditCompInfo.Value.Value;

						auto& MultiCom = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];
						MultiCom.ComponentType = ECompType::ImportModel;

						auto& ImportModelComp = MultiCom.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
						ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

						FCSModelMatData ModelData;
						if (CurEditDependData.GetDependFile(MultiCom.ComponentID.Value, ModelData))
						{
							ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

							MultiCom.ComponentName = ModelData.name;
							MultiCom.CodeExp = ModelData.folderCode;
							MultiCom.Code = ModelData.folderCode;
						}

						RewriteChange();
			}
					else
					{
						if (NewID.Value.IsEmpty())
						{
							MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Clear();
							EditCompInfo.Key = INDEX_NONE;

							RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
							Refresh_Inner();
						}
						else
						{
							SendSearchBackDirctoryRequest_ByFolderID(NewID.Value);
						}
					}
#else
					FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					FGeometryDatas::LoadMultiComponent(GlobalParameters, OverrideParameters, MultiComponentProperty);
					MultiComponentProperty.GenerateID();
					this->RefreshMultiComponentActor(true);

					MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
					this->UpdateAxis();
#endif
					
				}
				
			}
			else
			{
				MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
			}
			return;
		}
		else if (EComponentIDType::VisibleExpression == EditType)
		{
			FExpressionValuePair NewVisibility(InString);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
			UParameterRelativeLibrary::CombineParameters(OverrideParameters, OverrideParameters, PeerParameters);
			auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
			
			//混合原始文件夹参数

			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = URefRelationFunction::ConvertParamsArrayToMap(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].InheritParams);
			/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ComponentMap = URefRelationFunction::ConvertParamsArrayToMap(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams;
			UParameterRelativeLibrary::CombineParameters(InheritMap, ComponentMap, InheritFolderParams);*/

			/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = GetFolderInheritParams(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, true);*/
			
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> TempLocal = LocalParamMap;
			UParameterRelativeLibrary::CombineParameters(InheritFolderParams, TempLocal, LocalParamMap);
			
			bool Res = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewVisibility);
			if (Res && OriginalData.ComponentVisibility != NewVisibility)
			{
				NeedUpdate = true;
				OriginalData.ComponentVisibility = NewVisibility;
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex] = OriginalData;

#ifdef USE_REF_LOCAL_FILE

				RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
				Refresh_Inner();
				/*URefRelationFunction::GetMultiComponentInfo(
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value,
					GlobalParameters,
					OverrideParameters,
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
				);*/
#else
				FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);
				FGeometryDatas::LoadMultiComponent(GlobalParameters, OverrideParameters, MultiComponentProperty);

				MultiComponentProperty.GenerateID();
				this->RefreshMultiComponentActor(true);
				MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				this->UpdateAxis();
#endif
				

				
			}
		}
		else if (EComponentIDType::VisibleValue == EditType)
		{
			FExpressionValuePair NewVisibility = FExpressionValuePair(InString);
			if (InString.IsNumeric() && NewVisibility != OriginalData.ComponentVisibility)
			{
				NeedUpdate = true;
				OriginalData.ComponentVisibility = NewVisibility;
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex] = OriginalData;

#ifdef USE_REF_LOCAL_FILE

				RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
				Refresh_Inner();
				/*URefRelationFunction::GetMultiComponentInfo(
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value,
					GlobalParameters,
					OverrideParameters,
					MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
				);*/
#else
				FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);
				FGeometryDatas::LoadMultiComponent(GlobalParameters, OverrideParameters, MultiComponentProperty);

				MultiComponentProperty.GenerateID();
				this->RefreshMultiComponentActor(true);
				MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
				this->UpdateAxis();
#endif

				
			}
		}
		else if (EComponentIDType::Explain == EditType)
		{
			NeedUpdate = InString.Len() <= 200 && OriginalData.Description != InString;
			if (NeedUpdate)
				OriginalData.Description = InString;
		}
		else if (EComponentIDType::Coding == EditType)
		{
			NeedUpdate = InString.Len() <= 50 && OriginalData.Code != InString;
			if (NeedUpdate)
				OriginalData.Code = InString;
		}
		if (NeedUpdate)
		{
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex] = OriginalData;
			//FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		}
		else
		{
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
		}
	}
}

void UMultiComponentManager::OnLocationChangedHandler(const EMultiCompPosType& EditType, const FLocationProperty& LocationData, const int32& InEditIndex)
{
	if (MultiComponentProperty.ComponentItems.IsValidIndex(InEditIndex))
	{
		//global
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();

		FLocationProperty NewLocation(LocationData);
		bool bLocationChange = false;
		auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
		
		//混合原始文件夹参数
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = URefRelationFunction::ConvertParamsArrayToMap(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].InheritParams);

		/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = GetFolderInheritParams(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, true);*/
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> TempLocal = LocalParamMap;
		UParameterRelativeLibrary::CombineParameters(InheritFolderParams, TempLocal, LocalParamMap);
		
		if (EMultiCompPosType::PosXExpression == EditType)
		{
			bLocationChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap,NewLocation.LocationX);
			if (bLocationChange)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationX = NewLocation.LocationX;
		}
		else if (EMultiCompPosType::PosXValue == EditType)
		{
			if (NewLocation.LocationX.Value.IsNumeric())
			{
				bLocationChange = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationX = FExpressionValuePair(NewLocation.LocationX.Value);
			}
		}
		else if (EMultiCompPosType::PosYExpression == EditType)
		{
			bLocationChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap,NewLocation.LocationY);
			if (bLocationChange)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationY = NewLocation.LocationY;
		}
		else if (EMultiCompPosType::PosYValue == EditType)
		{
			if (NewLocation.LocationY.Value.IsNumeric())
			{
				bLocationChange = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationY = FExpressionValuePair(NewLocation.LocationY.Value);
			}
		}
		else if (EMultiCompPosType::PosZExpression == EditType)
		{
			bLocationChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewLocation.LocationZ);
			if (bLocationChange)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationZ = NewLocation.LocationZ;
		}
		else if (EMultiCompPosType::PosZValue == EditType)
		{
			if (NewLocation.LocationZ.Value.IsNumeric())
			{
				bLocationChange = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentLocation.LocationZ = FExpressionValuePair(NewLocation.LocationZ.Value);
			}
		}
		if (bLocationChange)
		{
			//FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[InEditIndex]);
			MultiComponentDisplayer->SetCompLocation(CurrentEditCompIndex, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentLocation.GetLocation());
			this->UpdateAxis();
		}
		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
	}
}

void UMultiComponentManager::OnRotationChangedHandler(const EMultiCompPosType& EditType, const FRotationProperty& RotationData, const int32& InEditIndex)
{
	if (MultiComponentProperty.ComponentItems.IsValidIndex(InEditIndex))
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		FRotationProperty NewRotation(RotationData);
		bool bRotationChanged = false;
		auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
		
		//混合原始文件夹参数
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = URefRelationFunction::ConvertParamsArrayToMap(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].InheritParams);
		/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = GetFolderInheritParams(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, true);*/
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> TempLocal = LocalParamMap;
		UParameterRelativeLibrary::CombineParameters(InheritFolderParams, TempLocal, LocalParamMap);
		
		if (EMultiCompPosType::PosXExpression == EditType)
		{
			bRotationChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewRotation.Roll);
			if (bRotationChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Roll = NewRotation.Roll;
		}
		else if (EMultiCompPosType::PosXValue == EditType)
		{
			if (NewRotation.Roll.Value.IsNumeric() && FCString::Atof(*NewRotation.Roll.Value) >= 0.0f)
			{
				bRotationChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Roll = FExpressionValuePair(NewRotation.Roll.Value);
			}
		}
		else if (EMultiCompPosType::PosYExpression == EditType)
		{
			bRotationChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewRotation.Pitch);
			if (bRotationChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Pitch = NewRotation.Pitch;
		}
		else if (EMultiCompPosType::PosYValue == EditType)
		{
			if (NewRotation.Pitch.Value.IsNumeric() && FCString::Atof(*NewRotation.Pitch.Value) >= 0.0f)
			{
				bRotationChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Pitch = FExpressionValuePair(NewRotation.Pitch.Value);
			}
		}
		else if (EMultiCompPosType::PosZExpression == EditType)
		{
			bRotationChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap,NewRotation.Yaw);
			if (bRotationChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Yaw = NewRotation.Yaw;
		}
		else if (EMultiCompPosType::PosZValue == EditType)
		{
			if (NewRotation.Yaw.Value.IsNumeric() && FCString::Atof(*NewRotation.Yaw.Value) >= 0.0f)
			{
				bRotationChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentRotation.Yaw = FExpressionValuePair(NewRotation.Yaw.Value);
			}
		}
		if (bRotationChanged)
		{
			//FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[InEditIndex]);
			MultiComponentDisplayer->SetCompRotation(CurrentEditCompIndex, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentRotation.GetRotation());
			this->UpdateAxis();
		}
		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
	}
}

void UMultiComponentManager::OnScaleChangedHandler(const EMultiCompPosType& EditType, const FScaleProperty& ScaleData, const int32& InEditIndex)
{
	if (MultiComponentProperty.ComponentItems.IsValidIndex(InEditIndex))
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		FScaleProperty NewScale(ScaleData);
		bool bScaleChanged = false;
		auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);
		
		//混合原始文件夹参数
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = URefRelationFunction::ConvertParamsArrayToMap(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].InheritParams);
		/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritFolderParams = GetFolderInheritParams(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, true);*/
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> TempLocal = LocalParamMap;
		UParameterRelativeLibrary::CombineParameters(InheritFolderParams, TempLocal, LocalParamMap);
		
		if (EMultiCompPosType::PosXExpression == EditType)
		{
			bScaleChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap,NewScale.X);
			if (bScaleChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.X = NewScale.X;
		}
		else if (EMultiCompPosType::PosXValue == EditType)
		{
			if (NewScale.X.Value.IsNumeric() && FCString::Atof(*NewScale.X.Value) > 0.0f)
			{
				bScaleChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.X = FExpressionValuePair(NewScale.X.Value);
			}
		}
		else if (EMultiCompPosType::PosYExpression == EditType)
		{
			bScaleChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewScale.Y);
			if (bScaleChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.Y = NewScale.Y;
		}
		else if (EMultiCompPosType::PosYValue == EditType)
		{
			if (NewScale.Y.Value.IsNumeric() && FCString::Atof(*NewScale.Y.Value) >= 0.0f)
			{
				bScaleChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.Y = FExpressionValuePair(NewScale.Y.Value);
			}
		}
		else if (EMultiCompPosType::PosZExpression == EditType)
		{
			bScaleChanged = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, LocalParamMap, NewScale.Z);
			if (bScaleChanged)
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.Z = NewScale.Z;
		}
		else if (EMultiCompPosType::PosZValue == EditType)
		{
			if (NewScale.Z.Value.IsNumeric() && FCString::Atof(*NewScale.Z.Value) >= 0.0f)
			{
				bScaleChanged = true;
				MultiComponentProperty.ComponentItems[InEditIndex].ComponentScale.Z = FExpressionValuePair(NewScale.Z.Value);
			}
		}
		if (bScaleChanged)
		{
			//FMultiComTableOperatorLibrary::UpdateMultiComponentItem(MultiComponentProperty.ComponentItems[InEditIndex]);
			MultiComponentDisplayer->SetCompScale(CurrentEditCompIndex, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentScale.GetScale());
			this->UpdateAxis();
		}
		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		MultiUIManager->SyncTreeComponentWidget(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
	}
}

void UMultiComponentManager::OnRightMouseButtonClick(const FVector& InMousePosition)
{
	if (IS_OBJECT_PTR_VALID(MultiComponentDisplayer))
	{
		MultiComponentDisplayer->SwitchToNextOutlineType();
	}
}

void UMultiComponentManager::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	UManagerBase::OnCameraLocationOrWidthChanged(NewLocation, NewWidth);
	this->UpdateAxis();
}

void UMultiComponentManager::ShowAxis(bool IsShow)
{
	AxisActor->SetActorHiddenInGame(!IsShow);
}

void UMultiComponentManager::SyncDBDataToFile()
{
	GET_CACHE_REF_DATA(MultiCompFolderData);
	
	if (!RefData.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("SyncDBDataToFile --- RefData InValid"));
		return;
	}

	bool NeedSync = !RefData.FolderDBData.backend_directory.Equals(MultiCompFolderData.backend_folder_path);
	UE_LOG(LogTemp, Error, TEXT("SyncDBDataToFile --- RefData Need Sync [%d]"), NeedSync);
	
	if (!NeedSync)
		return;

	RefData.FolderDBData.backend_directory = MultiCompFolderData.backend_folder_path;

	UFolderWidget::Get()->AddCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(MultiCompFolderData), RefData);
	UFolderWidget::Get()->SyncSelect(RefData);

	FRefDirectoryData CurRefData;
	if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(MultiCompFolderData.id, CurRefData))
	{
		URefRelationFunction::ConvertDBDataToDirctoryData(MultiCompFolderData, CurRefData);
	}

	URefRelationFunction::SaveFile(RefData);
	if (CurRefData.IsValid())
	{
		int64 FileSize = 0;
		const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
			FPaths::ProjectContentDir(),
			URefRelationFunction::GetRefFileRelativePath(CurRefData)
		));
		ACatalogPlayerController::GetFileMD5AndSize(AbsPath, CurRefData.md5, FileSize);
		UpdateDataRequest(CurRefData);
		UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(RefData));
	}
}

void UMultiComponentManager::UpdateAndUploadImage(const FString& ImageRelativePath)
{
	if(ImageRelativePath.IsEmpty())
	{
		return;
	}

	MultiCompFolderData.thumbnail_path = ImageRelativePath;

	FRefDirectoryData DirectoryRefData;
	if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(MultiCompFolderData.id, DirectoryRefData))
	{
		URefRelationFunction::ConvertDBDataToDirctoryData(MultiCompFolderData, DirectoryRefData);
	}
	
	DirectoryRefData.thumbnailPath = ImageRelativePath;

	UpdateDataRequest(DirectoryRefData);

	bExitAfterUpload = false;
	UploadFileRequest(ImageRelativePath);

}

void UMultiComponentManager::ShotScreenHandler()
{
	FString FilePath = FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png");

	FilePath = FPaths::ConvertRelativePathToFull(FilePath);
	ACatalogPlayerController::Get()->TakeScreenShot(FilePath);
}

void UMultiComponentManager::TakePictureHandler(const int32& PictureType)
{

	if (PictureType == 9)
	{
		if (false == bImageSelecting)
		{
			bImageSelecting = true;
			FCatalogFunctionLibrary::OpenFileDialogForImage(OpenImage);
			bImageSelecting = false;
			if (!OpenImage.IsEmpty())
			{
				MultiUIManager->SetCameraImage(OpenImage, true);
			}
			else
			{
				MultiUIManager->SetCameraImage(OpenImage, false);
			}
			TakePictureTypeDelegate.ExecuteIfBound(PictureType);
		}
	}
	else if (PictureType == 10)
	{
		if (CurrentPictureType != 9)
		{
			ShotScreenHandler();
		}
		else
		{
			SaveImportImage(OpenImage);
			TakePictureTypeDelegate.ExecuteIfBound(PictureType);
		}
		OpenImage.Empty();
	}
	else
	{
		MultiUIManager->SetCameraImage(OpenImage, false);
		TakePictureTypeDelegate.ExecuteIfBound(PictureType);
		OpenImage.Empty();

	}
	CurrentPictureType = PictureType;
}

void UMultiComponentManager::ProcessImageHandler()
{
	const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png"));

	const FString TempPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + FString::Printf(TEXT("MultiComponents/%s/Thumbnails"), *MultiCompFolderData.id));

	FCatalogFunctionLibrary::CreateDirectoryRecursively(TempPath);
	const FString SavePath = FString::Printf(TEXT("MultiComponents/%s/Thumbnails/%s.%s"), *MultiCompFolderData.id, *MultiCompFolderData.id, *FPaths::GetExtension(FilePath));
	const FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);
	if (FImageProcessModule::Get()->ThumbnailConvert(FilePath, TargetPath))
	{
#ifdef USE_REF_LOCAL_FILE

		//FImageProcessModule::Get()->ResizeImage(TargetPath, 240, 240, TargetPath);
		UpdateAndUploadImage(SavePath);

#else

		MultiCompFolderData.thumbnail_path = SavePath;
		UFolderTableOperatorLibrary::UpdateFile(MultiCompFolderData);

#endif
	}
	TakePictureTypeDelegate.ExecuteIfBound(11);

}

void UMultiComponentManager::SaveImportImage(const FString& SrcImage)
{
	const FString SavePath = FString::Printf(TEXT("MultiComponents/%s/Thumbnails/%s.%s"), *MultiCompFolderData.id, *MultiCompFolderData.id, *FPaths::GetExtension(SrcImage));
	const FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

	FCatalogFunctionLibrary::CopyFileTo(SrcImage, DataPath);

#ifdef USE_REF_LOCAL_FILE

	UpdateAndUploadImage(SavePath);

#else

	MultiCompFolderData.thumbnail_path = SavePath;
	UFolderTableOperatorLibrary::UpdateFile(MultiCompFolderData);
#endif
}

void UMultiComponentManager::OnComponentParameterChangedHandler(const EParameterType& EditType, const FParameterData& ParamData)
{
	if (!MultiComponentProperty.ComponentItems.IsValidIndex(CurrentEditCompIndex))
		return;
	SyncIndex = CurrentEditCompIndex;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	bool NeedUpdateComList = false;
	if (EParameterType::Add == EditType)
	{
		const int32 ParamIndex = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate([ParamData](const FParameterData& InOther) { return InOther.Data.name.Equals(ParamData.Data.name, ESearchCase::CaseSensitive); });
		if (INDEX_NONE != ParamIndex)
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("This Param already Exist, can not add again")).ToString());
			return;
		}
		if (ParamData.IsValid())
		{
			auto NewParameter = ParamData;
			NewParameter.ReGenerateID();
			NewParameter.Data.FormatDefaultExpress();
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].AddNewParameter(NewParameter);

#ifdef USE_REF_LOCAL_FILE
			RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
			Refresh_Inner();
			/*URefRelationFunction::GetMultiComponentInfo(
				GlobalParameters,
				OverrideParameters,
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
			);*/

#else
			FLocalDatabaseParameterLibrary::InsertMultiParameters(NewParameter, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].KeyID);
			TArray<FParameterData> EffectParameters;
			ParseAffectedParameters(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters, EffectParameters, NewParameter.Data.name);
			FLocalDatabaseParameterLibrary::UpdateMultiParameters(EffectParameters);
			FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);
			
			this->RefreshMultiComponentActor();
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));

#endif

		}
		return;
	}
	if (EParameterType::Del == EditType)
	{
		FParameterData ParameterDeleted = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].DeleteParameter(ParamData.Data.id);

#ifdef USE_REF_LOCAL_FILE

		RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
		Refresh_Inner();

		return;
		/*URefRelationFunction::GetMultiComponentInfo(
			GlobalParameters,
			OverrideParameters,
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
		);*/

#else
		if (ParameterDeleted.IsValid())
		{
			FLocalDatabaseParameterLibrary::DeleteMultiParameters(ParamData.Data.id);
			TArray<FParameterData> EffectParameters;
			ParseAffectedParameters(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters, EffectParameters, ParameterDeleted.Data.name);
			FLocalDatabaseParameterLibrary::UpdateMultiParameters(EffectParameters);
		
			RefreshActor();
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
			this->UpdateAxis();
			return;
		}
#endif

		
	}


	FParameterData NewParameter(ParamData);
	if (EParameterType::Expression == EditType || EParameterType::Value == EditType)
	{
		if (EParameterType::Value == EditType)
		{
			if (NewParameter.Data.is_enum == 0)
			{
				NewParameter.Data.expression = NewParameter.Data.value;
			}
			else
			{
				TArray<TPair<FString, FString>> EnumArray;
				for (auto & En : NewParameter.EnumData)
				{
					EnumArray.Add({ En.expression,En.value });
				}

				for (auto& Pair : EnumArray)
				{
					if (UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(Pair.Value) 
						&& UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(NewParameter.Data.value)
						&& FCString::Atof(*Pair.Value) == FCString::Atof(*NewParameter.Data.value))
					{
						NewParameter.Data.expression = Pair.Key;
						break;
					}
					else if(Pair.Value.Equals(NewParameter.Data.value))
					{
						NewParameter.Data.expression = Pair.Key;
						break;
					}
				}
			}
		}

		auto ComponentParameters = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters;
		/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);*/
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = OverrideParameters;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
		UParameterRelativeLibrary::CombineParameters(PeerParameters, ComponentParameters);
		if (PeerParameters.Contains(NewParameter.Data.name))
		{
			PeerParameters[NewParameter.Data.name] = NewParameter;
		}
		bool Res = UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, ParentParameters, PeerParameters);
		if (false == Res)
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
				MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
			return;
		}
		if (PeerParameters.Contains(NewParameter.Data.name))
		{
			NewParameter = PeerParameters[NewParameter.Data.name];
		}
		int32 Index = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate(
			[NewParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(NewParameter.Data.name,ESearchCase::CaseSensitive); }
		);

		NewParameter.Data.FormatDefaultExpress();
		if(Index != INDEX_NONE)
		{
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters[Index] = NewParameter;
		}
	}
	//else if (EParameterType::Value == EditType)
	//{
	//	NewParameter.Data.expression = NewParameter.Data.value;
	//	auto ComponentParameters = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters;
	//	/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
	//	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);*/
	//	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = OverrideParameters;
	//	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	//	UParameterRelativeLibrary::CombineParameters(PeerParameters, ComponentParameters);
	//	if (PeerParameters.Contains(NewParameter.Data.name))
	//	{
	//		PeerParameters[NewParameter.Data.name] = NewParameter;
	//	}
	//	bool Res = UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, ParentParameters, PeerParameters);
	//	if (false == Res)
	//	{
	//		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
	//			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
	//		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
	//		MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
	//			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
	//		return;
	//	}
	//	if (PeerParameters.Contains(NewParameter.Data.name))
	//	{
	//		NewParameter = PeerParameters[NewParameter.Data.name];
	//	}
	//}
	else if (EParameterType::Modify == EditType)
	{//添加参数
		NewParameter.ReGenerateID();
		//FLocalDatabaseParameterLibrary::DeleteMultiParameters(ParamData.Data.id);
		const int32 Index = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate([NewParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(NewParameter.Data.name,ESearchCase::CaseSensitive); });
		if (INDEX_NONE == Index) return;
		NewParameter.Data.id = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters[Index].Data.id;
		NewParameter.Data.FormatDefaultExpress();
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters[Index] = NewParameter;
#ifdef USE_REF_LOCAL_FILE
		RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
		Refresh_Inner();
		/*URefRelationFunction::GetMultiComponentInfo(
			GlobalParameters,
			OverrideParameters,
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
		);*/


#else
		FLocalDatabaseParameterLibrary::UpdateMultiParameters(NewParameter);
		for (auto& ParameterIter : MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters)
			FLocalDatabaseParameterLibrary::UpdateMultiParameters(ParameterIter);
		
		RefreshActor();
		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
			MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
		this->UpdateAxis();
	
#endif

		
		return;
	}

#ifdef USE_REF_LOCAL_FILE

	RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
	Refresh_Inner();
	/*URefRelationFunction::GetMultiComponentInfo(
		GlobalParameters,
		OverrideParameters,
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex]
	);*/

#else
	TArray<FParameterData> EffectParameters;
	const int32 Index = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate([NewParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(NewParameter.Data.name); });
	MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters[Index] = NewParameter;
	ParseAffectedParameters(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters, EffectParameters, NewParameter.Data.name);
	EffectParameters.Add(NewParameter);
	FLocalDatabaseParameterLibrary::UpdateMultiParameters(EffectParameters);//更新数据库

	MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.Empty();
	FLocalDatabaseParameterLibrary::RetriveMultiParametersByID(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].KeyID, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters);

	RefreshActor();
	MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
	MultiUIManager->SyncTreeComponentID(CombineIdAndDescription(
		MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentID.Value, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].Description));
	this->UpdateAxis();
#endif

	
}

void UMultiComponentManager::OnComponentParameterAddHandler(const EParameterType& EditType, const TArray<FParameterData>& ParamDatas)
{
	if (EParameterType::Add == EditType)
	{
		/*
		for (auto Ite : ParamDatas)
		{
			const int32 ParamIndex = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate([&Ite](const FParameterData& InOther) { return InOther.Data.name.Equals(Ite.Data.name, ESearchCase::IgnoreCase); });
			if (INDEX_NONE != ParamIndex)
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("This Param already Exist, can not add again")).ToString());
				break;
			}
		}*/

		TArray<FParameterData> TempParamDatas = ParamDatas;
		TempParamDatas.RemoveAll([&](const FParameterData& InData) {
			const int32 ParamIndex = MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ComponentParameters.IndexOfByPredicate([&InData](const FParameterData& InOther) { return InOther.Data.name.Equals(InData.Data.name, ESearchCase::CaseSensitive); });
			return INDEX_NONE != ParamIndex;
			});

		for (auto Ite : TempParamDatas)
		{
			OnComponentParameterChangedHandler(EditType, Ite);
		}

	}
}

void UMultiComponentManager::OnParameterUPDown(const FParameterData& InData, bool bUp)
{
	if (!MultiComponentProperty.ComponentItems.IsValidIndex(CurrentEditCompIndex))
		return;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;

	auto& ComponentItem = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];

	int32 ParamTotal = ComponentItem.ComponentParameters.Num();
	const int32 ParamIndex = ComponentItem.ComponentParameters.IndexOfByPredicate([InData](const FParameterData& InOther) { return InOther.Data.name.Equals(InData.Data.name, ESearchCase::CaseSensitive); });
	if (INDEX_NONE != ParamIndex)
	{
		if (bUp && ParamIndex == 0)
			return;

		if (!bUp && ParamIndex == ParamTotal - 1)
			return;

		SyncIndex = CurrentEditCompIndex;

		MultiUIManager->SwapParameterUI(ComponentItem.ComponentParameters[bUp ? ParamIndex - 1 : ParamIndex + 1], ComponentItem.ComponentParameters[ParamIndex]);
		ComponentItem.ComponentParameters.Swap(bUp ? ParamIndex - 1 : ParamIndex + 1, ParamIndex);
		RefFileData.ChildComponent = MultiComponentProperty.ComponentItems;
		//Refresh_Inner();
		

		MultiUIManager->SelectParameterUI(InData);
	}
}

void UMultiComponentManager::OnComponentTreeListActionHandler(const int32& ActionType, const int32& ActionID)
{
	EMultiComponentTreeEditType TreeEditType = static_cast<EMultiComponentTreeEditType>(ActionType);
	if (EMultiComponentTreeEditType::Add == TreeEditType)
	{
		FMultiComponentDataItem DefaultData = FMultiComponentDataItem();
		
#ifdef USE_REF_LOCAL_FILE

#else
		DefaultData.KeyID = FGuid::NewGuid().ToString().ToLower();
		const int32 Order = FMultiComTableOperatorLibrary::RetriveMultiComponentMaxOrder(MultiCompFolderData.id) + 1;
		FMultiComTableOperatorLibrary::InsertMultiComponentItem(DefaultData, MultiCompFolderData.id, Order);
#endif

		CurrentEditCompIndex = MultiComponentProperty.ComponentItems.Add(DefaultData);
		MultiComponentProperty.GenerateID();
		MultiUIManager->UpdateContent(MultiComponentProperty, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ID);
		MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		MultiComponentDisplayer->AddDefaultComp();
		MultiComponentDisplayer->UpdateSelection(CurrentEditCompIndex, true);
	}
	else if (EMultiComponentTreeEditType::Delete == TreeEditType)
	{
		if (MultiComponentProperty.ComponentItems.IsValidIndex(CurrentEditCompIndex))
		{
#ifdef USE_REF_LOCAL_FILE

#else
			FMultiComTableOperatorLibrary::DeleteMultiComponentItem(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].KeyID);
#endif

			MultiComponentProperty.ComponentItems.RemoveAt(CurrentEditCompIndex);
			MultiComponentProperty.GenerateID();
			CurrentEditCompIndex = -1;
			MultiUIManager->UpdateContent(MultiComponentProperty);

			/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);*/

			this->RefreshMultiComponentActor();
			MultiUIManager->UpdateCompProperty();
			MultiUIManager->ResetState();
			this->UpdateAxis();
		}
	}
	else if (EMultiComponentTreeEditType::Copy == TreeEditType)
	{
		if (MultiComponentProperty.ComponentItems.IsValidIndex(CurrentEditCompIndex))
		{
			FMultiComponentDataItem DefaultData = MultiComponentProperty.ComponentItems[CurrentEditCompIndex];

#ifdef USE_REF_LOCAL_FILE

#else
			DefaultData.KeyID = FGuid::NewGuid().ToString().ToLower();
			const int32 Order = FMultiComTableOperatorLibrary::RetriveMultiComponentMaxOrder(MultiCompFolderData.id) + 1;
			FMultiComTableOperatorLibrary::InsertMultiComponentItem(DefaultData, MultiCompFolderData.id, Order);
			for (int32 i = 0; i < DefaultData.ComponentParameters.Num(); ++i)
			{
				auto& Parameter = DefaultData.ComponentParameters[i];
				Parameter.ReGenerateID();
				FLocalDatabaseParameterLibrary::InsertMultiParameters(Parameter, DefaultData.KeyID);
			}
#endif

			CurrentEditCompIndex = MultiComponentProperty.ComponentItems.Add(DefaultData);
			MultiComponentProperty.GenerateID();

			/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, FCString::Atoi64(*MultiCompFolderData.folder_id), MultiComponentProperty);*/

			this->RefreshMultiComponentActor();
			MultiUIManager->UpdateContent(MultiComponentProperty, CurrentEditCompIndex);
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
			MultiComponentDisplayer->UpdateSelection(CurrentEditCompIndex, true);
		}
	}
	else if (EMultiComponentTreeEditType::Select == TreeEditType)
	{
		if (MultiComponentProperty.ComponentItems.IsValidIndex(ActionID) && CurrentEditCompIndex != ActionID)
		{
			MultiComponentDisplayer->UpdateSelection(CurrentEditCompIndex, false);
			CurrentEditCompIndex = ActionID;
			MultiComponentDisplayer->UpdateSelection(CurrentEditCompIndex, true);
			MultiUIManager->UpdateCompProperty(MultiComponentProperty.ComponentItems[CurrentEditCompIndex]);
		}
	}
	else if (EMultiComponentTreeEditType::Down == TreeEditType)
	{
		/*FMultiComTableOperatorLibrary::SwapTwoMultiComponentItemOrder(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].KeyID
			, MultiComponentProperty.ComponentItems[CurrentEditCompIndex + 1].KeyID);*/
		if (MultiComponentProperty.SwapItem(CurrentEditCompIndex, CurrentEditCompIndex + 1))
		{
			MultiComponentDisplayer->SwapComp(CurrentEditCompIndex, CurrentEditCompIndex + 1);
			++CurrentEditCompIndex;
		}
		MultiComponentProperty.GenerateID();
		//MultiComponentDisplayer->MoveSelection(CurrentEditCompIndex, false);
		MultiUIManager->UpdateContent(MultiComponentProperty, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ID);
	}
	else if (EMultiComponentTreeEditType::Up == TreeEditType)
	{
		/*FMultiComTableOperatorLibrary::SwapTwoMultiComponentItemOrder(MultiComponentProperty.ComponentItems[CurrentEditCompIndex].KeyID
			, MultiComponentProperty.ComponentItems[CurrentEditCompIndex - 1].KeyID);*/
		if (MultiComponentProperty.SwapItem(CurrentEditCompIndex, CurrentEditCompIndex - 1))
		{
			MultiComponentDisplayer->SwapComp(CurrentEditCompIndex, CurrentEditCompIndex - 1);
			--CurrentEditCompIndex;
		}
		MultiComponentProperty.GenerateID();
		//MultiComponentDisplayer->MoveSelection(CurrentEditCompIndex, true);
		MultiUIManager->UpdateContent(MultiComponentProperty, MultiComponentProperty.ComponentItems[CurrentEditCompIndex].ID);
	}
}

#undef LOCTEXT_NAMESPACE
