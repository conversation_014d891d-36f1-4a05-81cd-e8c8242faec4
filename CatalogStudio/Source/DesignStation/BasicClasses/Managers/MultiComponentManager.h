// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ManagerBase.h"
#include "DesignStation/Geometry/MultiComponent/ShowMultiComponentActor.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/BasicClasses/Managers/UIManager/MultiComponentUIManager.h"
#include "DesignStation/BasicClasses/BackgroundAxis.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "MultiComponentManager.generated.h"

struct FMatAdditionInfo;

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UMultiComponentManager : public UManagerBase
{
	GENERATED_BODY()

#pragma region Net_Multi_Comp

private:
	void BindDelegate();
	void UnBindDelegate();

	void UpdateDataRequest(const FRefDirectoryData& InData);
	void UploadFileRequest(const FString& FileRelativePath);
	void SendDownloadNecessary(const TArray<FString>& Paths, bool Init);

	void SendSearchBackDirctoryRequest_ByFolderID(const FString& InStr);
	void SendSearchFrontDirctoryRequest_ByFolderID(const FString& InStr);

	UFUNCTION()
	void OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);
	UFUNCTION()
	void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);
	UFUNCTION()
	void OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths);
	 
	UFUNCTION()
	void OnSearchBackDirctoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Data);

	UFUNCTION()
	void OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData);

	UFUNCTION()
	void OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData);

	UFUNCTION()
		void OnNetSyncConflictResponseHandler(){}

	void ParseDependFiles();

	void RewriteChange();

	UFUNCTION()
	void RewriteChange_AllReady();

private:
	UPROPERTY()
	FString VersionUUID;

	UPROPERTY()
	FString UpdateUUID = TEXT("NoWay");

	UPROPERTY()
	FString UploadUUID = TEXT("NoWay");

	UPROPERTY()
	FString InitNetUUID = TEXT("NoWay");

	UPROPERTY()
	FString RefreshNetUUID = TEXT("NoWay");

	UPROPERTY()
	FBackendDirectoryNetUUID BackNetUUID;

	UPROPERTY()
	FFrontDirectoryNetUUID FrontNetUUID;

#pragma endregion

protected:
	UPROPERTY()
	FMultiComponentDataItem RefFileData;

	UPROPERTY()
	TArray<FString> NeedDownloadPaths;

	UPROPERTY()
	int32 SyncIndex = INDEX_NONE;

	UPROPERTY()
		FFolderTableData                MultiCompFolderData;

	FMultiComponentData				MultiComponentProperty;

	/*
	*  depend data
	*/
	UPROPERTY()
	FDependFileData CurEditDependData;

	/*
	*  @@ no need download or parse file 
	*  @@ for : no exist file ; download error file
	*/
	UPROPERTY()
	TArray<FString> SkipDependFiles;

	/*
	 * @@ cache data for download file mark
	 * @@ for custom file
	 * @@ value is custom file relative path
	 */
	UPROPERTY()
	TArray<FString> AlreadyDownloadFiles;

	UPROPERTY()
	TArray<FMatAdditionInfo> MatAdditionInfo;

	UPROPERTY()
	FRefDirectoryData CustomRefDirectoryData;

	/*
	*  @@ key : component index
	*  @@ value : component ID Pair
	*/
	TPair<int32, FExpressionValuePair> EditCompInfo;

	UPROPERTY()
		AShowMultiComponentActor* MultiComponentDisplayer;

	UPROPERTY()
		UMultiComponentUIManager* MultiUIManager;

	UPROPERTY()
		ABackgroundAxis* AxisActor;

	int32 CurrentEditCompIndex;

	//Fix bug CATALOG-1536
	bool bImageSelecting = false;

	UPROPERTY()
	bool bExitAfterUpload = true;

public:
	UMultiComponentManager();

	virtual AShowMultiComponentActor* GetShowActor() override;

	GenerateSet(FFolderTableData, MultiCompFolderData)

private:

	bool IsInitAction() const { return !InitNetUUID.Equals(TEXT("NoWay")); }
	void ResetInitAction() { InitNetUUID = TEXT("NoWay"); }
	bool IsRefreshAction() { return !RefreshNetUUID.Equals(TEXT("NoWay")); }
	void ResetRefreshAction() { RefreshNetUUID = TEXT("NoWay"); }

	void Init_Inner();
	void Calculate_Inner(bool IsInit = true);
	void Refresh_Inner();
	//void Refresh_Inner_AllReady();

	/**
	 * Empties old data from the given MultiComponentDataItem.
	 * This function is designed to clear any existing data in the EditComponent,
	 * ensuring that it is ready for new data to be assigned without any remnants of previous data.
	 * @@ param EditCompoent The MultiComponentDataItem to be cleared of old data.
	 */
	void EmptyOldData(FMultiComponentDataItem& EditCompoent);

	void InitMultiComponentUI();

	void RefreshActor();

	bool ParseAffectedParameters(const TArray<FParameterData>& ParametersToCheck, TArray<FParameterData>& AffectedParameters, const FString& ModifyParameter);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GetFolderInheritParams(const FString& InID, bool NeedGetFile);

protected:

	void InitShowManager();

	virtual void InitializeManager() override;

	virtual void UninitializeManager() override;

	virtual void WindowSizeChanged() override;

	void UpdateAxis();

	FString CombineIdAndDescription(const FString& Id, const FString& Description);

	//
	UFUNCTION()
		void OnComponentTreeListActionHandler(const int32& ActionType, const int32& ActionID);

	UFUNCTION()
		void OnToolbarOperatorHandler(const EMultiCompToolBarType& EditType);

	UFUNCTION()
		void OnComponentNameIDChangedHandler(const EComponentIDType& EditType, const FString& InString);

	UFUNCTION()
		void OnLocationChangedHandler(const EMultiCompPosType& EditType, const FLocationProperty& LocationData, const int32& InEditIndex);

	UFUNCTION()
		void OnRotationChangedHandler(const EMultiCompPosType& EditType, const FRotationProperty& RotationData, const int32& InEditIndex);

	UFUNCTION()
		void OnScaleChangedHandler(const EMultiCompPosType& EditType, const FScaleProperty& ScaleData, const int32& InEditIndex);

	UFUNCTION()
		void OnComponentParameterChangedHandler(const EParameterType& EditType, const FParameterData& ParamData);

		UFUNCTION()
		void OnComponentParameterAddHandler(const EParameterType& EditType, const TArray<FParameterData>& ParamData);

		UFUNCTION()
		void OnParameterUPDown(const FParameterData& InData, bool bUp);

	/*
	*  @@ 当引用发生变化时，将文件参数同步到节点
	*  @@ 规则：1、节点参数参与FolderID引用计算，不替换；2、不参与计算的参数，替换、增加
	*  @@ RetVal = true 时标识有Tip需要弹窗提示
	*/
	bool SyncParamsFromFile(FMultiComponentDataItem& ComponentNode, const FString& NewFolderIDExpression, const FString& NewFolderID, FString& Tips);

	void OnClickExitsButtonHandler();

	void Exit_Inner();

	bool RefreshMultiComponentActor(bool RefreshMesh = true);

	/*
	*  @@ proess
	*
	*/
	void StartProcess();
	void UpdateProcess(float InProcess);
	void EndProcess();

public:

	virtual void ExitGame() override;
	virtual void OnLeftMouseButtonClick(const FVector& InMousePosition) override;
	virtual void OnRightMouseButtonClick(const FVector& InMousePosition) override;
	virtual void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth) override;
	virtual void ShowAxis(bool IsShow);

	virtual void SyncDBDataToFile() override;

protected:
	void UpdateAndUploadImage(const FString& ImageRelativePath);

	UFUNCTION()
		void ShotScreenHandler();
	UFUNCTION()
		void TakePictureHandler(const int32& PictureType);
	UFUNCTION()
		void ProcessImageHandler();

	void SaveImportImage(const FString& SrcImage);

private:
	int32 CurrentPictureType;
	FString OpenImage;
};
