// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentManager.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/PopUI/STwoButtonsWidget.h"
#include "DesignStation/UI/PopUI/SOneButtonWidget.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "ImageProcess/Public/ImageProcess.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/SubSystem/PakFileManagerSubsystem.h"
#include "DesignStation/SubSystem/ResourceSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/LoadPageWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "DesignStation/UI/TempSaveRecover/RecoverShowWidget.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "EasyDXFLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "Parameter/ParameterRelativeLibrary.h"

//#include "EasyDXF/Public/EasyDXFLibrary.h"



#define LOCTEXT_NAMESPACE "SingleComponentManager"

USingleComponentManager::USingleComponentManager()
	:UManagerBase(TEXT("CompMap"))
	, EditType(ESingleComponentEditType::EView)
	, SingleComponentDisplayer(nullptr)
	, AxisActor(nullptr)
	, UIManager(nullptr)
	, CurrentEditSection(-1)
	, CurrentEditCurrentSection(-1)
	, CurrentSelectOperationIndex(FSectionOperationOrder(ESectionOperationType::ECutoutSection, -1))
{
}

void USingleComponentManager::WindowSizeChanged()
{
	if (UIManager)
	{
		UIManager->RefreshSceneViewportSize();
	}
}

AShowSingleComponentActor* USingleComponentManager::GetSingleShowActor()
{
	return SingleComponentDisplayer;
}

void USingleComponentManager::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &USingleComponentManager::OnUploadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &USingleComponentManager::OnUpdateResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &USingleComponentManager::OnDownloadFileCompleteHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.AddUniqueDynamic(this, &USingleComponentManager::OnGetFurnitureOrMatDataForParseResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataArrForParseResponseDelegate.AddUniqueDynamic(this, &USingleComponentManager::OnGetFurnitureOrMatDataArrForParseResponseHandler);

}

void USingleComponentManager::UnBindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.Remove(this, FName(TEXT("OnUploadFileResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.Remove(this, FName(TEXT("OnUpdateResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.Remove(this, FName(TEXT("OnDownloadFileCompleteHandler")));
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.Remove(this, FName(TEXT("OnGetFurnitureOrMatDataForParseResponseHandler")));
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataArrForParseResponseDelegate.Remove(this, FName(TEXT("OnGetFurnitureOrMatDataArrForParseResponseHandler")));
}

void USingleComponentManager::UploadFileRequest(const FString& FileRelativePath)
{
	NetUUID.UploadFile = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void USingleComponentManager::UpdateDataRequest(const FRefDirectoryData& InData, bool UploadFile)
{
	NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(InData);
	NeedUploadFile = UploadFile;
}

void USingleComponentManager::QueryModelOrMatDetailInfo(const FString& InFolderID)
{
	FrontNetUUID.CatagoryDetailFileToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendFurnitureOrMaterialDataForParseSearchRequest_FolderID(InFolderID);
}

void USingleComponentManager::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (OutFilePath.Equals(NetUUID.UploadFile))
	{
		NetUUID.ResetUploadFileAction();
		if (OutRes)
		{
			UE_LOG(LogTemp, Log, TEXT("Upload file [%s] success!"), *OutFilePath);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Upload file [%s] failed!"), *OutFilePath);
		}

		NeedUploadFile = false;

		if(bExitAfterUpload)
		{
			ExitToMain();
		}
		else
		{
			bExitAfterUpload = true;
		}
		
	}
}

void USingleComponentManager::OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.UpdateUUID.Equals(UUID))
	{
		if (bSuccess && Datas.IsValidIndex(0))
		{
			if (SingleCompFolderData.id != Datas[0].id)
			{
				UIManager->UpdateSingleComponentFileProperty(SingleCompFolderData);
				return;
			}

			FFolderTableData InData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], InData);

			URefRelationFunction::RenameLocalFile(
				URefRelationFunction::GetRefFileRelativePath(SingleCompFolderData),
				URefRelationFunction::GetRefFileRelativePath(InData)
			);

			SingleCompFolderData = InData;
			UIManager->UpdateSingleComponentFileProperty(SingleCompFolderData);
			const FString ParentPath = URefRelationFunction::GetFolderDirectory(Datas[0].backendFolderPath, false);
			UFolderWidget::Get()->SyncAddData(ParentPath, Datas[0]);
			if(NeedUploadFile)
				UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(Datas[0]));
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);

		}
	}
}

void USingleComponentManager::OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths)
{
	if (UUID.Equals(NetUUID.ModelURLDownloadUUID))
	{
		NetUUID.ResetModelURLDownloadAction();
		if (NetUUID.IsMatURLDownloadFinish())
		{
			LoadLocalFile(!SingleComponentToEdit.IsValid());
		}
	}
	else if (UUID.Equals(NetUUID.MatURLDownloadUUID))
	{
		NetUUID.ResetMatURLDownloadAction();
		if (NetUUID.IsModelURLDownloadFinish())
		{
			LoadLocalFile(!SingleComponentToEdit.IsValid());
		}
	}
	else if (UUID.Equals(FrontNetUUID.DownloadUUID))
	{
		FrontNetUUID.ResetDownloadAction();
		
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->ShowSingleComponentPropertyPannel();
	}
	else if (UUID.Equals(FrontNetUUID.ModelDetailToDownloadUUID))
	{
		FrontNetUUID.ResetModelDetailToDownloadAction();
		if (FrontNetUUID.IsModelURLDownloadFiniesh())
		{
			LoadLocalFile(!SingleComponentToEdit.IsValid());
		}
	}
	else if (UUID.Equals(FrontNetUUID.MatDetailToDownloadUUID))
	{
		FrontNetUUID.ResetMatDetailToDownloadAction();
		if (FrontNetUUID.IsMatURLDownloadFiniesh())
		{
			LoadLocalFile(!SingleComponentToEdit.IsValid());
		}
	}
}

void USingleComponentManager::OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData)
{
	if (UUID.Equals(FrontNetUUID.CatagoryDetailFileToParseUUID))
	{
		FrontNetUUID.ResetCatagoryDetailFileToParseAction();
		if (bSuccess)
		{
			if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
			{
				SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentMaterial = EditSectionMaterialProperty;
			}
			else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
			{
				SingleComponentProperty.ComponentItems[EditSectionData.Id].ImportMesh[EditSectionData.MaterialInfo.EditMaterialId].MaterialId = EditSectionMaterialProperty;
			}

			CurEditDependData.AddDependFile(DirData);
			CurEditDependData.AddDependFolderID_Mat(EditSectionMaterialProperty);

			UResourceSubsystem::GetInstance()->AddMaterialWebData({ DirData }, { DirData });

			TArray<FString> FilePath, DownloadPath;
			auto New = DirData;
			if (New.AnalysisData(FilePath, DownloadPath))
			{
				FrontNetUUID.DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
					DownloadPath,
					New.IsPakFile() ? 0 : 1,
					FilePath
				);
			}
			else
			{
				this->RefreshSingleComponentActor();
				this->ShowSingleComponentActor();
				this->ShowSingleComponentPropertyPannel();
			}
		}
		else
		{
			//UI_POP_WINDOW_ERROR(Msg);

			if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
			{
				SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentMaterial = EditSectionMaterialProperty;
			}
			else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
			{
				SingleComponentProperty.ComponentItems[EditSectionData.Id].ImportMesh[EditSectionData.MaterialInfo.EditMaterialId].MaterialId = EditSectionMaterialProperty;
			}

			this->RefreshSingleComponentActor();
			this->ShowSingleComponentActor();
			this->ShowSingleComponentPropertyPannel();
		}
	}
}

void USingleComponentManager::OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData)
{
	if (UUID.Equals(FrontNetUUID.ModelDetailToParseUUID))
	{
		FrontNetUUID.ResetModelDetailToParseAction();
		if (bSuccess)
		{
			CurEditDependData.AddDependFiles(NetData);
			if (FrontNetUUID.IsMatURLParseFiniesh())
			{
				ParseDependFiles();
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
	else if (UUID.Equals(FrontNetUUID.MatDetailToParseUUID))
	{
		FrontNetUUID.ResetMatDetailToParseAction();
		if (bSuccess)
		{
			UResourceSubsystem::GetInstance()->SyncModelWebData(CurEditDependData.GetDependFolderID_Mat(), NetData);

			CurEditDependData.AddDependFiles(NetData);
			if (FrontNetUUID.IsMatURLParseFiniesh())
			{
				ParseDependFiles();
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}

}

void USingleComponentManager::AddDependMatFileData(const FCSModelMatData& InData)
{
	
}

void USingleComponentManager::ParseDependFiles()
{
	TArray<FString> ModelFilePath, ModelDownloadPath;
	const bool NeedDownloadModel = CurEditDependData.GetNeedDownload_Model(ModelFilePath, ModelDownloadPath);

	TArray<FString> MatFilePath, MatDownloadPath;
	TArray<FCSModelMatData> NeedDownloadMatData;
	const bool NeedDownloadMat = CurEditDependData.GetNeedDownload_Mat(MatFilePath, MatDownloadPath, NeedDownloadMatData);
	UResourceSubsystem::GetInstance()->AddMaterialWebData(CurEditDependData.GetMatDependFiles(), NeedDownloadMatData);

	if (!NeedDownloadModel && !NeedDownloadMat)
	{
		UpdateProcess(0.5f);

		UE_LOG(LogTemp, Warning, TEXT("SingleComponent --- Load File --- No Need Download Model Or Mat"));

		LoadLocalFile(!SingleComponentToEdit.IsValid());
	}
	else
	{
		UpdateProcess(0.3f);
		if (NeedDownloadModel)
		{
			FrontNetUUID.ModelDetailToDownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
				ModelDownloadPath,
				0,
				ModelFilePath
			);
		}

		if (NeedDownloadMat)
		{
			FrontNetUUID.MatDetailToDownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
				MatDownloadPath,
				1,
				MatFilePath
			);
		}
	}
}

void USingleComponentManager::InitUIManager()
{
	{
		UIManager = NewObject<USingleComponentUIManager>();
		UIManager->UpdateSingleComponentUI();
		UIManager->OnCreateNewSection.BindUFunction(this, FName(TEXT("OnCreateCrossSectionHandler")));
		//UIManager->OnClickSaveButton.BindUFunction(this, FName(TEXT("SaveAction")));
		UIManager->OnClickSaveButton.BindUFunction(this, FName(TEXT("SaveVersion")));
		UIManager->OnClickLoadButton.BindUFunction(this, FName(TEXT("LoadVersion")));
		UIManager->OnClickExitButton.BindUFunction(this, FName(TEXT("ExitAction")));

		//Import section
		UIManager->ImportLocationChangeDelegate.BindUFunction(this, FName(TEXT("OnImportSectionLocationChangedHandler")));
		UIManager->ImportRotationChangeDelegate.BindUFunction(this, FName(TEXT("OnImportSectionRotationChangedHandler")));
		UIManager->ImportScaleChangeDelegate.BindUFunction(this, FName(TEXT("OnImportSectionScaleChangedHandler")));

		//File property 
		UIManager->SingleComponentFileDataDelegate.BindUFunction(this, FName(TEXT("OnSingleComponentFilePropertyChangedHandler")));

		UIManager->SelectSectionEditDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionPropertyChangedHandler")));
		UIManager->SingleComponentPropertyOperatorDelegate.BindUFunction(this, FName(TEXT("OnPropertyToolbarSectionPropertyChangedHandler")));
		UIManager->SingleComponentImportActionDelegate.BindUFunction(this, FName(TEXT("OnImportSectionStateChangedHandler")));
		UIManager->SingleComponentPointDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionPointPropertyChangedHandler")));
		UIManager->SingleComponentLineDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionLinePropertyChangedHandler")));
		UIManager->SectionOperatorDataPointDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionPointPropertyChangedHandler")));
		UIManager->SectionOperatorDataLineDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionLinePropertyChangedHandler")));
		UIManager->SingleComponentRectangleDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionRectanglePropertyChangedHandler")));
		UIManager->SingleComponentEllipseDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionEllipsePropertyChangedHandler")));
		UIManager->SingleComponentCuboidDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionCubePropertyChangedHandler")));
		UIManager->SingleComponentWidgetSelectDelegate.BindUFunction(this, FName(TEXT("OnSelectAnotherGeometryItemHandler")));
		UIManager->SectionOperatorLoftingDataDelegate.BindUFunction(this, FName(TEXT("OnSelectAnotherGeometryItemHandler")));
		UIManager->SectionToolBarDelegate.BindUFunction(this, FName(TEXT("OnClickToolbarHandler")));
		UIManager->SectionOperationToolBarDelegate.BindUFunction(this, FName(TEXT("OnSingleComponentOperationToolBarEditHandler")));
		UIManager->SectionOperatorTensileDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionDrawOperationPropertyChangedHandler")));
		UIManager->SectionOperatorShiftingDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionShiftOperationPropertyChangedHandler")));
		UIManager->SectionOperationZoomDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionZoomOperationPropertyChangedHandler")));
		UIManager->SectionOperatorLoftingDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionLoftRoutinePropertyChangedHandler")));
		UIManager->SectionOperatorCutOutDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionCutoutOperationPropertyChangedHandler")));
		UIManager->SectionOperatorDataRectangleDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionRectanglePropertyChangedHandler")));
		UIManager->SectionOperatorDataEllipseDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionEllipsePropertyChangedHandler")));

		//
		UIManager->WidgetOperationDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionEditionHandler")));
		UIManager->OperationSelectDelegate.BindUFunction(this, FName(TEXT("OnCrossSectionOperationSelectChangedHandler")));

		UIManager->SingleComponentPictureTypeDelegate.BindUFunction(this, FName(TEXT("TakePictureHandler")));

		UIManager->InitCameraButton();

		UIManager->SetCameraEnable(true);
	}
}

void USingleComponentManager::InitManagerShow()
{
	{
		SectionManager.OnSectionPropertyChanged.BindUFunction(this, FName(TEXT("OnSectionPropertyChangedHandler")));
		SectionManager.OnSelectionChanged.BindUFunction(this, FName(TEXT("OnCrossSectionSelectionChangedHandler")));
		if (!IS_OBJECT_PTR_VALID(SingleComponentDisplayer))
		{
			FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
			AActor* NewActor = ACatalogPlayerController::Get()->GetWorld()->SpawnActor(AShowSingleComponentActor::StaticClass(), &tran);
			SingleComponentDisplayer = Cast<AShowSingleComponentActor>(NewActor);
		}
		if (!IS_OBJECT_PTR_VALID(AxisActor))
		{
			FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
			AActor* NewActor = ACatalogPlayerController::Get()->GetWorld()->SpawnActor(ABackgroundAxis::StaticClass(), &tran);
			AxisActor = Cast<ABackgroundAxis>(NewActor);
			this->UpdateAxis();
		}
		ACatalogPlayerController::Get()->OnScreenShotCompletedDelegate.BindUFunction(this, FName(TEXT("ProcessImageHandler")));
	}
}

void USingleComponentManager::LoadLocalFile(bool UseDefault/* = false*/)
{
	UpdateProcess(0.7f);

	if (!UseDefault)
	{
#ifdef USE_REF_LOCAL_FILE

		GET_CACHE_REF_DATA(SingleCompFolderData);
		//GET_REF_DATA_FROM_FILE(SingleCompFolderData);
		SingleComponentProperty.ComponentItems = RefData.FileData.component_datas;

		//TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = URefRelationFunction::GetGlobalParameters();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, SingleComponentProperty);

#else
		FString SingleComponentFilePath = FPaths::ProjectContentDir() + SingleComponentToEdit.data_path;
		SingleComponentFilePath = FPaths::ConvertRelativePathToFull(SingleComponentFilePath);
		bool Res = UProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(SingleComponentFilePath, SingleComponentProperty);
		if (!Res)
			UE_LOG(LogTemp, Error, TEXT("Load single component data file [%s] failed!"), *SingleComponentToEdit.data_path);
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, SingleComponentProperty);
#endif

	}
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();

	EndProcess();

	{
		this->ShowSingleComponentPropertyPannel();
		UIManager->UpdateSingleComponentFileProperty(SingleCompFolderData);
	}
}

void USingleComponentManager::InitializeManager()
{
	BindDelegate();

	StartProcess();

	InitUIManager();

	InitManagerShow();

	TempBool = false;
	On2DSectionEdit = false;

	OverrideParameters.Empty();

#ifdef USE_REF_LOCAL_FILE

	GET_CACHE_REF_DATA(SingleCompFolderData);

	//top folder parameters
	//GET_UPPER_LEVEL_HERITPARAMETERS(SingleCompFolderData, OverrideParameters, true);
	const TArray<FString> UpperDirectoryID = URefRelationFunction::GetUpperFolderDirectory(SingleCompFolderData.backend_folder_path, true);
	TArray<FRefToLocalFileData> UpperRefDatas;
	UFolderWidget::Get()->GetCacheDataForFile(UpperDirectoryID, UpperRefDatas);
	URefRelationFunction::GetTopLevelFolderParameterData(UpperRefDatas, OverrideParameters);
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(ACatalogPlayerController::Get()->GetGlobalParameterMap(), {}, OverrideParameters);

	//depend files
	CurEditDependData.Clear();

	TArray<FExpressionValuePair> MatDependPair = RefData.MatModelDependFiles.GetDependFolderID_Mat();
	TArray<FExpressionValuePair> ModelDependPair = RefData.MatModelDependFiles.GetDependFolderID_Model();

	TArray<FString> MatDepend;
	TArray<FString> ModelDepend;
	for (FExpressionValuePair MD : MatDependPair)
	{
		if (!MD.Value.IsEmpty() && !MD.Expression.IsEmpty() && !MD.Expression.IsNumeric())
		{//need calculate
			
			FGeometryDatas::CalculateParameterValue(
				ACatalogPlayerController::Get()->GetGlobalParameterMap(),
				OverrideParameters,
				{},
				MD
			);

		}
		MD.FormatValue();
		MatDepend.AddUnique(MD.Value);
		CurEditDependData.AddDependFolderID_Mat(MD);
	}
	for (FExpressionValuePair MDP : ModelDependPair)
	{
		if (!MDP.Value.IsEmpty() && !MDP.Expression.IsEmpty() && !MDP.Expression.IsNumeric())
		{//need calculate

			FGeometryDatas::CalculateParameterValue(
				ACatalogPlayerController::Get()->GetGlobalParameterMap(),
				OverrideParameters,
				{},
				MDP
			);

		}
		MDP.FormatValue();
		ModelDepend.AddUnique(MDP.Value);
		CurEditDependData.AddDependFolderID_Model(MDP);
	}
	

	if (MatDepend.Num() > 0 || ModelDepend.Num() > 0)
	{
		if (MatDepend.Num() > 0)
		{
			FrontNetUUID.MatDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
				MatDepend, 1
			);
		}
		if (ModelDepend.Num() > 0)
		{
			FrontNetUUID.ModelDetailToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
				ModelDepend, 1
			);
		}
	}
	else
	{
		UpdateProcess(0.5f);

		UE_LOG(LogTemp, Warning, TEXT("SingleComponent --- Load File --- No Need Download Model Or Mat"));

		LoadLocalFile(!SingleComponentToEdit.IsValid());
	}

#else

	FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParameters(true, SingleCompFolderData.id, OverrideParameters);

	LoadLocalFile(!SingleComponentToEdit.IsValid());//所有数据均放在本地，直接从本地加载文件

#endif
}

void USingleComponentManager::UninitializeManager()
{
	UnBindDelegate();

	if (IS_OBJECT_PTR_VALID(SingleComponentDisplayer) && IsValid(SingleComponentDisplayer))
	{
		SingleComponentDisplayer->DestroyDisplayer();
	}
	if (IS_OBJECT_PTR_VALID(AxisActor) && IsValid(AxisActor))
		AxisActor->Destroy();
	SectionManager.ClearCrossSection();
	LoftRoutineManager.ClearLoftRoutine();
	CutoutSectionManager.ClearCutoutSection();
	if (IS_OBJECT_PTR_VALID(UIManager))
	{
		UIManager->Clear();
		UIManager = nullptr;
	}
}

void USingleComponentManager::UpdateAxis()
{
	if (!IS_OBJECT_PTR_VALID(AxisActor))
		return;

	if (ESingleComponentEditType::EView == EditType)
		AxisActor->UpdateAxisType(EPlanPolygonBelongs::EUnknown);
	else if (ESingleComponentEditType::ECrossSection == EditType)
	{
		AxisActor->UpdateAxisType(ESectionType::ECube != SectionManager.GetCurrentSectionType() ? SectionManager.GetCurrentPlanType() : EPlanPolygonBelongs::EUnknown);
		AxisActor->ChangeSizeByContentSize(SectionManager.GetSectionSize());
		AxisActor->ChangeTagRotation(CameraLocation, CameraWidth);
		FVector CenterPoint = FVector::ZeroVector;
		FVector BoxExtent = FVector::ZeroVector;
		SectionManager.SectionBound(CenterPoint, BoxExtent);
		SetActorBox(CenterPoint, BoxExtent);

		return;
	}
	else if (ESingleComponentEditType::EEditOperation == EditType)
		AxisActor->UpdateAxisType(EPlanPolygonBelongs::EUnknown);
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		AxisActor->UpdateAxisType(LoftRoutineManager.GetCurrentPlanType());
		AxisActor->ChangeSizeByContentSize(LoftRoutineManager.GetSectionSize());
		AxisActor->ChangeTagRotation(CameraLocation, CameraWidth);
		return;
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
		AxisActor->UpdateAxisType(CutoutSectionManager.GetCurrentPlanType());
	else if (ESingleComponentEditType::EEditImportSection == EditType)
		AxisActor->UpdateAxisType(EPlanPolygonBelongs::EUnknown);
	FVector CenterPoint = FVector::ZeroVector;
	FVector BoxExtent = FVector::ZeroVector;
	SingleComponentDisplayer->GetDisplayerBound(CenterPoint, BoxExtent);
	AxisActor->ChangeSizeByContentSize(CenterPoint + BoxExtent);
	AxisActor->ChangeTagRotation(CameraLocation, CameraWidth);
	SetActorBox(CenterPoint, BoxExtent);
}

void USingleComponentManager::ShowSingleComponentPropertyPannel()
{
	int32 Index = 0;
	SectionDatas.Empty();
	SectionDatas.AddDefaulted(SingleComponentProperty.ComponentItems.Num());
	for (auto& Iter : SingleComponentProperty.ComponentItems)
	{
		SectionDatas[Index].Id = Index;
		SectionDatas[Index].SectionName = Iter.SectionName;
		SectionDatas[Index].ThumbnailPath = Iter.ThumbnailPath;
		SectionDatas[Index].VisibleParam = Iter.VisibleParam;
		SectionDatas[Index].ComponentSource = Iter.ComponentSource;
		if (ESingleComponentSource::ECustom == Iter.ComponentSource)
		{
			//SectionDatas[Index].MaterialInfo.ComponentMaterial.Add(FSectionMaterialDataItem(0, Iter.ComponentMaterial));
			auto& ThisSectionMaterial = SectionDatas[Index].MaterialInfo.ComponentMaterial.AddDefaulted_GetRef();
			ThisSectionMaterial.Id = 0;
			ThisSectionMaterial.MaterialID = Iter.ComponentMaterial;
			ThisSectionMaterial.MaterialID.FormatValue();

			FCSModelMatData ThisSectionDependMat;
			CurEditDependData.GetDependFile(ThisSectionMaterial.MaterialID.Value, ThisSectionDependMat);
			ThisSectionMaterial.Name = ThisSectionDependMat.name;
			ThisSectionMaterial.ImgPath = ThisSectionDependMat.mapsImg;
		}
		else if (ESingleComponentSource::EImportFBX == Iter.ComponentSource)
		{
			int32 Offset = SectionDatas[Index].MaterialInfo.ComponentMaterial.AddDefaulted(Iter.ImportMesh.Num());
			int32 i = 0;
			for (auto& MeshIter : Iter.ImportMesh)
			{
				SectionDatas[Index].MaterialInfo.ComponentMaterial[Offset + i] = FSectionMaterialDataItem(i, MeshIter.MaterialId);
				++i;
			}
		}
		++Index;
	}
	UIManager->UpdateSingleComponentProperty_New(SectionDatas);
	UE_LOG(LogTemp, Log, TEXT("-------test %d"), SingleComponentToEdit.id);
}

void USingleComponentManager::OnCreateCrossSectionHandler(int32 PlanType)
{
	EPlanPolygonBelongs NewPlanType = static_cast<EPlanPolygonBelongs>(PlanType);
	if (EPlanPolygonBelongs::EUnknown != NewPlanType)
	{
		OnChangeCameraType.ExecuteIfBound(PlanType);
		SectionCameraType = PlanType;
		this->CreateCrossSection(NewPlanType);
	}
}

void USingleComponentManager::CreateCrossSection(const EPlanPolygonBelongs& PlanType)
{
	EditType = ESingleComponentEditType::ECrossSection;
	SectionManager.ClearCrossSection();
	SectionManager.Initialize(PlanType, ESectionType::ECustomPlan, CameraLocation, CameraWidth);
	CurrentEditSection = SingleComponentProperty.ComponentItems.AddDefaulted();
	UIManager->UpdateSectionToolBarWidget(/*true*/);
	this->ShowSingleComponentActor(false);
	SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.SectionType = ESectionType::ECustomPlan;
	this->ShowCrossSectionPropertyPannel(SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection);
	this->UpdateAxis();
	UE_LOG(LogTemp, Log, TEXT("========== CreateCrossSection:: CurrentEditSection is: %d  ============"), CurrentEditSection);
}

void USingleComponentManager::OnMouseMove()
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		SectionManager.OnMouseMove();
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		LoftRoutineManager.OnMouseMove();
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		CutoutSectionManager.OnMouseMove();
	}
}

void USingleComponentManager::OnLeftMouseButtonClick(const FVector& InMousePosition)
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		SectionManager.OnMouseLeftButtonClick(InMousePosition);
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		LoftRoutineManager.OnMouseLeftButtonClick(InMousePosition);
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		CutoutSectionManager.OnMouseLeftButtonClick(InMousePosition);
	}
}

void USingleComponentManager::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	UManagerBase::OnCameraLocationOrWidthChanged(NewLocation, NewWidth);
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		SectionManager.OnCameraLocationOrWidthChanged(NewLocation, NewWidth);
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		LoftRoutineManager.OnCameraLocationOrWidthChanged(NewLocation, NewWidth);
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		CutoutSectionManager.OnCameraLocationOrWidthChanged(NewLocation, NewWidth);
	}
	this->UpdateAxis();
}

void USingleComponentManager::ShowAxis(bool IsShow)
{
	AxisActor->SetActorHiddenInGame(!IsShow);
}

void USingleComponentManager::StartProcess()
{
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	ULoadPageWidget::GetInstance()->InitLoadPage();
	ULoadPageWidget::GetInstance()->SetLoadingPercent(0.1f);
}

void USingleComponentManager::UpdateProcess(float InProcess)
{
	ULoadPageWidget::GetInstance()->SetLoadingPercent(InProcess);
}

void USingleComponentManager::EndProcess()
{
	ULoadPageWidget::GetInstance()->SetLoadingPercent(1.0f);
	ULoadPageWidget::GetInstance()->LoadDone(true);
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Collapsed);
}

bool USingleComponentManager::RefreshSingleComponentActor()
{
	FSingleComponentProperty TempSingleComponent = SingleComponentProperty;
	if (ESingleComponentEditType::EEditOperation == EditType)
		TempSingleComponent.ComponentItems[CurrentEditSection].SectionOperation = TempSectionOperation;
	else if (ESingleComponentEditType::EEditImportSection == EditType)
	{
		TempSingleComponent.ComponentItems[CurrentEditSection].SingleComponentLocation = SingleComponentLocation;
		TempSingleComponent.ComponentItems[CurrentEditSection].SingleComponentRotation = SingleComponentRotation;
		TempSingleComponent.ComponentItems[CurrentEditSection].SingleComponentScale = SingleComponentScale;
	}

	FShowSingleComponentActorProperty ActorProperty;
	if (TempSingleComponent.ComponentItems.Num() > 0)
		ActorProperty.MeshInfo.AddDefaulted(TempSingleComponent.ComponentItems.Num());

	int32 i = 0;
	for (auto& Iter : TempSingleComponent.ComponentItems)
	{
		TArray<TPair<FVector, FVector>> Framework;
		FString DMValue(TEXT(""));
		if (OverrideParameters.Contains(TEXT("DM")))
		{
			DMValue = OverrideParameters[TEXT("DM")].Data.value;
		}

		bool bPathUV = OverrideParameters.Contains(TEXT("FYWLFX")) && FCString::Atoi(*OverrideParameters[TEXT("FYWLFX")].Data.value) == 0;

		FGeometryDatas::GenerateMesh(Iter, ActorProperty.MeshInfo[i].SingleMeshInfo, Framework, DMValue, ESingleComponentEditType::EView != EditType, SingleComponentDisplayer, bPathUV);
		ActorProperty.MeshInfo[i].CompSource = Iter.ComponentSource;
		ActorProperty.MeshInfo[i].MeshTransform.SetLocation(Iter.SingleComponentLocation.GetLocation());
		ActorProperty.MeshInfo[i].MeshTransform.SetRotation(FQuat(Iter.SingleComponentRotation.GetRotation()));
		ActorProperty.MeshInfo[i].MeshTransform.SetScale3D(Iter.SingleComponentScale.GetScale());
		ActorProperty.OutlinePairs.Append(Framework);
		++i;
	}
	SingleComponentDisplayer->RefreshComponentMesh(ActorProperty);
	this->UpdateAxis();
	return true;
}

void USingleComponentManager::ShowSingleComponentActor(bool ShowActor)
{
	SingleComponentDisplayer->SetDisplayerHiddenInGame(!ShowActor);
	SingleComponentDisplayer->SetDisplayerEnableCollision(ShowActor);
}

bool USingleComponentManager::SaveAction()
{
	bool NeedSync = false;

	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		{
			bool Res = SectionManager.ConstructArchiveData(SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection);
			checkf(Res, TEXT("USingleComponentManager::SaveAction() Save section failed!"));
		}
	}
	else if (ESingleComponentEditType::EEditOperation == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		{
			SingleComponentProperty.ComponentItems[CurrentEditSection].SectionOperation = TempSectionOperation;
		}
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		{
			LoftRoutineManager.ConstructArchiveData(TempSectionOperation.LoftingOperation);
		}
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection) && TempSectionOperation.CutoutOperations.IsValidIndex(CurrentEditCurrentSection))
		{
			CutoutSectionManager.ConstructArchiveData(TempSectionOperation.CutoutOperations[CurrentEditCurrentSection]);
		}
	}
	else if (ESingleComponentEditType::EEditImportSection == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		{
			SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentLocation = SingleComponentLocation;
			SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentRotation = SingleComponentRotation;
			SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentScale = SingleComponentScale;
		}
	}
	else if (ESingleComponentEditType::EView == EditType)
	{//将数据保存到本地
#ifdef USE_REF_LOCAL_FILE

		GET_CACHE_REF_DATA(SingleCompFolderData);

		auto OldSections = RefData.FileData.component_datas;
		bool bChange = URefRelationFunction::IsConstructChange(OldSections, SingleComponentProperty.ComponentItems);

		if (URefRelationFunction::EnsureDefaultExpressionValid(RefData.ParamDatas))
		{
			bChange = true;
		}

		if (bChange)
		{
			if (SingleComponentToEdit.data_path.IsEmpty())
				SingleComponentToEdit.GenerateDataFilePath(SingleComponentToEdit.data_path);
			RefData.FileData.file_data_path = SingleComponentToEdit.data_path;

			TArray<FString> DependFiles;
			SingleComponentProperty.GetAllDependFiles(DependFiles);
			RefData.FileData.depend_files = FSingleComponentTableData::ConvertFilePathArrayToString(DependFiles);

			RefData.FileData.Init(SingleComponentProperty);

			CurEditDependData.ClearDetailData();
			RefData.MatModelDependFiles = CurEditDependData;

			{//sync file path
				RefData.FolderDBData.folder_id = SingleCompFolderData.folder_id;
			}

			const bool SaveRes = URefRelationFunction::SaveFile(RefData);
			if(!SaveRes)
				UIManager->SaveDone(false);

			if(SaveRes)
			{
				//sync temp
				UFolderWidget::Get()->AddCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(SingleCompFolderData), RefData);
			}
			else
			{
				UI_POP_WINDOW_ERROR(TEXT("save single component error"));
			}

			NeedSync = true;
		}
		else
		{//special : has file, md5 is empty
			const FString RefFilePathIdentify = URefToFileData::GetFileAddress(URefRelationFunction::GetMarkToBackendDirectory(SingleCompFolderData));
			if (FPaths::FileExists(RefFilePathIdentify) && SingleCompFolderData.md5.IsEmpty())
			{
				NeedSync = true;
			}
		}
		

#else
		SingleComponentToEdit.folder_id = SingleCompFolderData.id;
		if (SingleComponentToEdit.id <= 0)
		{
			FSingleComponentOperatorLibrary::CreateSingleComponent(SingleComponentToEdit);
		}
		if (SingleComponentToEdit.data_path.IsEmpty())
			SingleComponentToEdit.GenerateDataFilePath(SingleComponentToEdit.data_path);

		const FString FullPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComponentToEdit.data_path);
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, SingleComponentProperty);
		bool Res = UProtobufOperatorFunctionLibrary::SaveSingleComponentToFile(FullPath, SingleComponentProperty);
		if (Res)
		{


			TArray<FString> DependFiles;
			SingleComponentProperty.GetAllDependFiles(DependFiles);
			SingleComponentToEdit.depend_files = FSingleComponentTableData::ConvertFilePathArrayToString(DependFiles);
			FSingleComponentOperatorLibrary::UpdateSingleComponent(SingleComponentToEdit);
		}

		UIManager->SaveDone(Res);
#endif
	}

	return NeedSync;
}

void USingleComponentManager::SaveVersion()
{
	UI_POP_WINDOW_TIP_CONFIRM(TEXT("save current version data?"));
	if(ConfirmRes)
	{
		GET_CACHE_REF_DATA(SingleCompFolderData);

		auto TemCompData = SingleComponentToEdit;
		if (TemCompData.data_path.IsEmpty())
			TemCompData.GenerateDataFilePath(TemCompData.data_path);
		RefData.FileData.file_data_path = TemCompData.data_path;

		TArray<FString> DependFiles;
		SingleComponentProperty.GetAllDependFiles(DependFiles);
		RefData.FileData.depend_files = FSingleComponentTableData::ConvertFilePathArrayToString(DependFiles);

		RefData.FileData.Init(SingleComponentProperty);

		auto TempDependData = CurEditDependData;
		TempDependData.ClearDetailData();
		RefData.MatModelDependFiles = TempDependData;

		{//sync file path
			RefData.FolderDBData.folder_id = SingleCompFolderData.folder_id;
		}

		if(!URefRelationFunction::SaveTempLocalFile(RefData))
		{
			UI_POP_WINDOW_ERROR_ST(TEXT("save current version error!"));
		}
	}
}

void USingleComponentManager::LoadVersion()
{
	UI_POP_WINDOW_TIP_CONFIRM(TEXT("load old version data?"));
	if (ConfirmRes)
	{
		URecoverShowWidget::GetInstance()->RecoverVersionDelegate.BindUFunction(this, FName(TEXT("OnLoadVersionResponseHandler")));
		URecoverShowWidget::GetInstance()->UpdateOldVersionInfo(SingleCompFolderData.id);
		URecoverShowWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	}
}

void USingleComponentManager::OnLoadVersionResponseHandler(const FString& RecoverFileMark)
{
	if(!RecoverFileMark.IsEmpty())
	{
		FString TempSaveFolder = URefToFileData::GetTempSaveFolderRelativeAddress(SingleCompFolderData.id);
		FString AbsPath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), TempSaveFolder, RecoverFileMark)
		);
		FRefToLocalFileData TempData;
		if(URefRelationFunction::GetRefRelationByPath(AbsPath, TempData))
		{
			SingleComponentProperty.ComponentItems = TempData.FileData.component_datas;

			FGeometryDatas::CalculateParameterValue(
				ACatalogPlayerController::Get()->GetGlobalParameterMap(), 
				OverrideParameters, 
				{},
				SingleComponentProperty
			);
			this->RefreshSingleComponentActor();
			this->ShowSingleComponentActor();
		}
		else
		{
			UI_POP_WINDOW_ERROR(TEXT("Load Temp File Error"));
		}
	}
}

void USingleComponentManager::OnClickToolbarHandler(const int32& PlanType)
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		if (SectionManager.ChangeDrawTool(static_cast<ECrossSectionToolType>(PlanType)))
		{
			if (ECrossSectionToolType::EDrawCube == static_cast<ECrossSectionToolType>(PlanType))
			{
				OnChangeCameraType.ExecuteIfBound(0);
				this->UpdateAxis();
			}
			else if (static_cast<ESectionToolBarEditType>(PlanType) == ESectionToolBarEditType::ImportCAD)
			{
				OnImportSectionStateChangedHandler(EImportActionType::ImportCADFile, TEXT(""), TEXT(""));
				return;
			}
			else if ((ECrossSectionToolType::EArrow != static_cast<ECrossSectionToolType>(PlanType)) || (ECrossSectionToolType::EDrawCube != SectionManager.GetToolType()))
			{
				OnChangeCameraType.ExecuteIfBound(static_cast<int32>(SectionManager.GetCurrentPlanType()));
			}

			UIManager->SetSingleComponentSectionToolBarSelectType(static_cast<ESectionToolBarEditType>(PlanType));
		}
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		UIManager->SetSingleComponentSectionToolBarSelectType(static_cast<ESectionToolBarEditType>(PlanType));
		if (LoftRoutineManager.ChangeDrawTool(static_cast<ELoftRoutineToolType>(PlanType)))
		{
			if (static_cast<ELoftRoutineToolType>(PlanType) == ELoftRoutineToolType::EImportCAD)
			{
				OnImportSectionStateChangedHandler(EImportActionType::ImportCADFile, TEXT(""), TEXT(""));
				return;
			}
		}
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		UIManager->SetSingleComponentSectionToolBarSelectType(static_cast<ESectionToolBarEditType>(PlanType));
		if (CutoutSectionManager.ChangeDrawTool(static_cast<ECutoutSectionToolType>(PlanType)))
		{
			if (static_cast<ECutoutSectionToolType>(PlanType) == ECutoutSectionToolType::EImportCAD)
			{
				OnImportSectionStateChangedHandler(EImportActionType::ImportCADFile, TEXT(""), TEXT(""));
				return;
			}
		}
	}
}

void USingleComponentManager::OnSelectAnotherGeometryItemHandler(const int32& Type, const int32& Index, bool IsOver, bool OnProperty)
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		UE_LOG(LogTemp, Log, TEXT("OnSelectAnotherGeometryItemHandler"));
		SectionManager.SelectAnotherGeometryItem(Type, Index, IsOver);
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		LoftRoutineManager.SelectAnotherGeometryItem(Type, Index, IsOver);
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		CutoutSectionManager.SelectAnotherGeometryItem(Type, Index, IsOver);
	}
	TempBool = OnProperty;
}

void USingleComponentManager::OnKeyClicked(const FKey& InClickKey)
{
	if (EKeys::A == InClickKey)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeDrawTool(ECrossSectionToolType::EArrow);
		else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
			LoftRoutineManager.ChangeDrawTool(ELoftRoutineToolType::EArrow);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeDrawTool(ECutoutSectionToolType::EArrow);
	}
	else if (EKeys::P == InClickKey)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeDrawTool(ECrossSectionToolType::EDrawPoint);
		else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
			LoftRoutineManager.ChangeDrawTool(ELoftRoutineToolType::EDrawPoint);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeDrawTool(ECutoutSectionToolType::EDrawPoint);
	}
	else if (EKeys::Delete == InClickKey)
	{
		FText ItemName;
		if (ESingleComponentEditType::ECrossSection == EditType)
			ItemName = SectionManager.GetSelectedItemName();
		else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
			ItemName = LoftRoutineManager.GetSelectedItemName();
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			ItemName = CutoutSectionManager.GetSelectedItemName();
		if (!ItemName.IsEmpty() && !TempBool)
		{
			FString Title = FText::FromStringTable(FName("PosSt"), TEXT("Delete")).ToString() + FText::FromStringTable(FName("PosSt"), ItemName.ToString()).ToString();
			FString Content = FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete this")).ToString() + FText::FromStringTable(FName("PosSt"), ItemName.ToString()).ToString();

			bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(Title, Content);
			/*EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "DeleteGeometryItemKey", FText::FromStringTable(FName("PosSt"), TEXT("Delete {0}")).ToString()), FText::FromStringTable(FName("PosSt"), ItemName).ToString())
				, FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "SaveSectionContentKey", FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete this {0}")).ToString(), FText::FromStringTable(FName("PosSt"), ItemName).ToString())
				, NSLOCTEXT(LOCTEXT_NAMESPACE, "DeleteKey", FText::FromStringTable(FName("PosSt"), TEXT("Confirm")).ToString())
				, NSLOCTEXT(LOCTEXT_NAMESPACE, "CancelKey", FText::FromStringTable(FName("PosSt"), TEXT("Cancel")).ToString());*/
			if (Res/*EPopButtonType::Confirm == ButtonType*/)
			{
				if (ESingleComponentEditType::ECrossSection == EditType)
					SectionManager.OnDeleteKeyClick();
				else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
					LoftRoutineManager.OnDeleteKeyClick();
				else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
					CutoutSectionManager.OnDeleteKeyClick();
			}
		}
	}
	else if (EKeys::S == InClickKey)
	{
		bool RightControlDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::RightControl);
		bool LeftControlDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::LeftControl);
		if (LeftControlDown || RightControlDown)
		{
			this->SaveAction();
		}
	}
	else if (EKeys::E == InClickKey)
	{
		bool RightControlDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::RightControl);
		bool LeftControlDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::LeftControl);
		if (LeftControlDown || RightControlDown)
		{
			this->ExitAction();
		}
	}
}

void USingleComponentManager::ExitAction()
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{//退出画截面
		if (!SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
			return;
		FCrossSectionData NewSection;
		SectionManager.ConstructArchiveData(NewSection);
		SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs = SectionManager.GetCurrentPlanType();
		if (SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection != NewSection)
		{
#ifdef USE_REF_LOCAL_FILE

			this->SaveAction();

#else
			EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Save Section"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this section ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
			if (EPopButtonType::Confirm == ButtonType)
			{
				this->SaveAction();
			}
			else if (EPopButtonType::Close == ButtonType)
			{
				return;
			}
#endif
		}
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection) && !SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.IsValid())
		{
			SingleComponentProperty.ComponentItems.RemoveAt(CurrentEditSection);
		}
		SectionManager.ClearCrossSection();
		OnChangeCameraType.ExecuteIfBound(0);
		UIManager->BackToSingleComponentPropertyContent();
		UIManager->InitCameraButton();
		On2DSectionEdit = false;
		//UIManager->UpdateSectionToolBarWidget(false);
		this->ShowSingleComponentPropertyPannel();
		EditType = ESingleComponentEditType::EView;
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
	}
	else if (ESingleComponentEditType::EEditOperation == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection) && SingleComponentProperty.ComponentItems[CurrentEditSection].SectionOperation != TempSectionOperation)
		{
#ifdef USE_REF_LOCAL_FILE

			this->SaveAction();

#else
			EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Save Section Operation"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save these operations ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
			if (EPopButtonType::Confirm == ButtonType)
			{
				this->SaveAction();
			}
			else if (EPopButtonType::Close == ButtonType)
			{
				return;
			}
#endif
		}
		EditType = ESingleComponentEditType::EView;
		UIManager->BackToSingleComponentPropertyContent();
		//UIManager->UpdateSectionToolBarWidget(false);
		this->ShowSingleComponentPropertyPannel();
		UIManager->UpdateSectionOperationToolBarWidget(false);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		CurrentSelectOperationIndex.Index = -1;
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		OnRightMouseButtonClick(FVector::ZeroVector);
		FSectionLoftOperation NewLoftOperation;
		LoftRoutineManager.ConstructArchiveData(NewLoftOperation);
		if (TempSectionOperation.LoftingOperation != NewLoftOperation)
		{
#ifdef USE_REF_LOCAL_FILE

			this->SaveAction();

#else
			EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Save Loft Routine"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this loft routine ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
			if (EPopButtonType::Confirm == ButtonType)
			{
				this->SaveAction();
			}
			else if (EPopButtonType::Close == ButtonType)
			{
				return;
			}
#endif
		}
		EditType = ESingleComponentEditType::EEditOperation;
		LoftRoutineManager.ClearLoftRoutine();
		OnChangeCameraType.ExecuteIfBound(0);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		FSectionOperation::RefreshOperationID(TempSectionOperation);
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		UIManager->UpdateSectionOperationToolBarWidget(true);
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		if (false == (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection) && TempSectionOperation.CutoutOperations.IsValidIndex(CurrentEditCurrentSection)))
			return;
		FSectionCutOutOperation CutoutOperation;
		CutoutSectionManager.ConstructArchiveData(CutoutOperation);
		CutoutOperation.CutOutValue = TempSectionOperation.CutoutOperations[CurrentEditCurrentSection].CutOutValue;
		if (!TempSectionOperation.CutoutOperations[CurrentEditCurrentSection].Equal_Precise(CutoutOperation))
		{
#ifdef USE_REF_LOCAL_FILE

			this->SaveAction();

#else
			EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Save Cutout Section"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this cutout section ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
			if (EPopButtonType::Confirm == ButtonType)
			{
				this->SaveAction();
			}
			else if (EPopButtonType::Close == ButtonType)
			{
				return;
			}
#endif
		}
		EditType = ESingleComponentEditType::EEditOperation;
		CutoutSectionManager.ClearCutoutSection();
		OnChangeCameraType.ExecuteIfBound(0);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		FSectionOperation::RefreshOperationID(TempSectionOperation);
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		UIManager->UpdateSectionOperationToolBarWidget(true);
	}
	else if (ESingleComponentEditType::EEditImportSection == EditType)
	{
		if (SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		{
			bool Location = SingleComponentLocation != SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentLocation;
			bool Rotation = SingleComponentRotation != SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentRotation;
			bool Scale = SingleComponentScale != SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentScale;
			if (Location || Rotation || Scale)
			{
#ifdef USE_REF_LOCAL_FILE

				this->SaveAction();

#else
				EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Save Import Section"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this import section ?"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
				if (EPopButtonType::Confirm == ButtonType)
				{
					this->SaveAction();
				}
				else if (EPopButtonType::Close == ButtonType)
				{
					return;
				}
#endif
			}
			EditType = ESingleComponentEditType::EView;
			this->ShowSingleComponentPropertyPannel();
			this->RefreshSingleComponentActor();
			this->ShowSingleComponentActor();
		}
	}
	else if (ESingleComponentEditType::EView == EditType)
	{//退出单部件编辑
#ifdef USE_REF_LOCAL_FILE

		if (SaveAction())
		{
			FRefDirectoryData RefData;
			if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(SingleCompFolderData.id, RefData))
			{
				URefRelationFunction::ConvertDBDataToDirctoryData(SingleCompFolderData, RefData);
			}

			if (RefData.IsValid())
			{
				FVector MeshCenter, MeshExtension;
				SingleComponentDisplayer->GetDisplayerBound(MeshCenter, MeshExtension);
				//left down behind
				FVector LDBPoint = MeshCenter - MeshExtension;
				RefData.boxOffset = LDBPoint.ToString();
				
				int64 FileSize = 0;
				const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
					FPaths::ProjectContentDir(),
					URefRelationFunction::GetRefFileRelativePath(RefData)
				));
				ACatalogPlayerController::GetFileMD5AndSize(AbsPath, RefData.md5, FileSize);
				UpdateDataRequest(RefData, true);
			}
			else
			{
				UI_POP_WINDOW_ERROR(TEXT("single component sync error"));
				ExitToMain();
			}
		}
		else
		{
			ExitToMain();
		}

#else
		FString NewFileMD5(TEXT(""));
		{//生成文件的MD5值
			int64 FileSize = 0;
			FString FullPath = FPaths::ProjectSavedDir() + TEXT("temp.data");
			FullPath = FPaths::ConvertRelativePathToFull(FullPath);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, SingleComponentProperty);
			bool Res = UProtobufOperatorFunctionLibrary::SaveSingleComponentToFile(FullPath, SingleComponentProperty);
			ACatalogPlayerController::GetFileMD5AndSize(FullPath, NewFileMD5, FileSize);
		}
		FString DataPath = FPaths::ProjectContentDir() + SingleComponentToEdit.data_path;
		DataPath = FPaths::ConvertRelativePathToFull(DataPath);

		FString PreMD5;
		int64 FileSize = 0;

		ACatalogPlayerController::GetFileMD5AndSize(DataPath, PreMD5, FileSize);


		//const FString PreMD5 = FDownloadFileDataLibrary::RetriveFileMD5(TEXT("other_file"), SingleComponentToEdit.data_path);
		if (PreMD5.Equals(NewFileMD5, ESearchCase::IgnoreCase))
		{
			OnBackToMain.ExecuteIfBound(SingleCompFolderData);
		}
		else
		{
			EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Save Single Component"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to save this single component ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
			if (EPopButtonType::Confirm == ButtonType)
			{
				this->SaveAction();
				OnBackToMain.ExecuteIfBound(SingleCompFolderData);
			}
			else if (EPopButtonType::Close == ButtonType)
			{
				return;
			}
			else if (EPopButtonType::Cancel == ButtonType)
			{
				OnBackToMain.ExecuteIfBound(SingleCompFolderData);
			}
		}
#endif
	}
	this->UpdateAxis();
	UIManager->SetCameraEnable(true);
}

void USingleComponentManager::ExitToMain()
{
	OnBackToMain.ExecuteIfBound(SingleCompFolderData);
}

void USingleComponentManager::OnRightMouseButtonClick(const FVector& InMousePosition)
{
	if (ULoadPageWidget::GetInstance()->GetCurrentPercent() != 1.0f) return;

	if (ESingleComponentEditType::EView == EditType || ESingleComponentEditType::EEditOperation == EditType)
	{
		if (IS_OBJECT_PTR_VALID(SingleComponentDisplayer))
			SingleComponentDisplayer->SwitchToNextOutlineType();
	}
	else if (ESingleComponentEditType::ECrossSection == EditType)
	{
		UIManager->SetSingleComponentSectionToolBarSelectType(ESectionToolBarEditType::Select);
		SectionManager.OnMouseRightButtonClick(InMousePosition);
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		UIManager->SetSingleComponentSectionToolBarSelectType(ESectionToolBarEditType::Select);
		LoftRoutineManager.OnMouseRightButtonClick(InMousePosition);
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		UIManager->SetSingleComponentSectionToolBarSelectType(ESectionToolBarEditType::Select);
		CutoutSectionManager.OnMouseRightButtonClick(InMousePosition);
	}
	else
	{
		return;
	}
}

void USingleComponentManager::OnSectionPropertyChangedHandler()
{
	if (ESingleComponentEditType::ECrossSection == EditType)
	{
		FCrossSectionData SectionData;
		if (SectionManager.ConstructArchiveData(SectionData))
		{
			this->ShowCrossSectionPropertyPannel(SectionData);
		}
	}
	else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
	{
		FSectionLoftOperation LoftRoutine;
		if (LoftRoutineManager.ConstructArchiveData(LoftRoutine))
		{
			UIManager->UpdateSectionOperationProperty(LoftRoutine);
		}
	}
	else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
	{
		FSectionCutOutOperation CutOutSection;
		if (CutoutSectionManager.ConstructArchiveData(CutOutSection))
		{
			UIManager->UpdateSectionOperationProperty(CutOutSection);
		}
	}
	this->UpdateAxis();
}

void USingleComponentManager::OnCrossSectionSelectionChangedHandler(const int32& Type, const int32& Index)
{
	UIManager->UpdateSectionWidgetSelectStyle(Type, Index, ESingleComponentEditType::ECrossSection == EditType);
}

void USingleComponentManager::ShowCrossSectionPropertyPannel(const FCrossSectionData& InCrossSection)
{
	FSectionProperty SectionProperty;
	SectionProperty.Type = InCrossSection.SectionType;
	if (ESectionType::ECustomPlan == SectionProperty.Type)
	{
		int32 Index = 0;
		On2DSectionEdit = true;
		SectionProperty.SectionPoints.AddDefaulted(InCrossSection.Points.Num());
		for (auto& Iter : InCrossSection.Points)
		{
			SectionProperty.SectionPoints[Index] = Iter;
			SectionProperty.SectionPoints[Index].ID = Index;
			++Index;
		}
		Index = 0;
		SectionProperty.SectionLines.AddDefaulted(InCrossSection.Lines.Num());
		for (auto& Iter : InCrossSection.Lines)
		{
			SectionProperty.SectionLines[Index] = Iter;
			SectionProperty.SectionLines[Index].ID = Index;
			++Index;
		}
		UIManager->SectionCameraButton();
	}
	else if (ESectionType::ERectangle == SectionProperty.Type)
	{
		On2DSectionEdit = true;
		SectionProperty.SectionRectangle = InCrossSection.Rectangle;
		UIManager->SectionCameraButton();
	}
	else if (ESectionType::EEllipse == SectionProperty.Type)
	{
		On2DSectionEdit = true;
		SectionProperty.SectionEllipse = InCrossSection.Ellipse;
		UIManager->SectionCameraButton();

	}
	else if (ESectionType::ECube == SectionProperty.Type)
	{
		On2DSectionEdit = false;
		SectionProperty.SectionCuboid = InCrossSection.Cube;
		UIManager->InitCameraButton();
	}
	else
	{
		checkNoEntry();
	}
	this->UpdateAxis();
	UE_LOG(LogTemp, Log, TEXT("%d  ---  %d "), (int32)SectionProperty.Type, SectionProperty.SectionPoints.Num());
	UIManager->UpdateSectionProperty(SectionProperty);
}

void USingleComponentManager::OnCrossSectionPointPropertyChangedHandler(const int32& Type, FGeomtryPointProperty& Data)
{
	EPointChangeType ChangeType = static_cast<EPointChangeType>(Type);
	FGeomtryPointProperty PointProperty(Data);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	switch (ChangeType)
	{
	case EPointChangeType::PositionX: NeedChange = PointProperty.LocationX.Value.IsNumeric(); PointProperty.LocationX.Expression = PointProperty.LocationX.Value; break;
	case EPointChangeType::PositionY:NeedChange = PointProperty.LocationY.Value.IsNumeric(); PointProperty.LocationY.Expression = PointProperty.LocationY.Value; break;
	case EPointChangeType::PositionZ:NeedChange = PointProperty.LocationZ.Value.IsNumeric(); PointProperty.LocationZ.Expression = PointProperty.LocationZ.Value; break;
	case EPointChangeType::Relative:break;
	case EPointChangeType::PositionXExpress:NeedChange = !PointProperty.LocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, PointProperty.LocationX); ChangeType = EPointChangeType::PositionX; break;
	case EPointChangeType::PositionYExpress:NeedChange = !PointProperty.LocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, PointProperty.LocationY); ChangeType = EPointChangeType::PositionY; break;
	case EPointChangeType::PositionZExpress:NeedChange = !PointProperty.LocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, PointProperty.LocationZ); ChangeType = EPointChangeType::PositionZ; break;
	default: checkNoEntry(); break;
	}
	if (NeedChange)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeCrossSectionProperty(static_cast<int32>(ChangeType), PointProperty);
		else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
			LoftRoutineManager.ChangeLoftRoutineProperty(static_cast<int32>(ChangeType), PointProperty);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeCutoutSectionProperty(static_cast<int32>(ChangeType), PointProperty);
		this->UpdateAxis();
	}
	else
	{
		OnSectionPropertyChangedHandler();
	}
}

void USingleComponentManager::OnCrossSectionLinePropertyChangedHandler(const int32& Type, FGeomtryLineProperty& Data)
{
	ELineChangeType ChangeType = static_cast<ELineChangeType>(Type);
	FGeomtryLineProperty LineProperty(Data);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	if (ELineChangeType::RadiusOrHighExpress == ChangeType)
	{
		NeedChange = !LineProperty.RadiusOrHeightData.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, LineProperty.RadiusOrHeightData);
		ChangeType = ELineChangeType::RadiusOrHigh;
	}
	else if (ELineChangeType::RadiusOrHigh == ChangeType)
	{
		NeedChange = LineProperty.RadiusOrHeightData.Value.IsNumeric();
		LineProperty.RadiusOrHeightData.Expression = LineProperty.RadiusOrHeightData.Value;
	}
	else if (ELineChangeType::PointNUmExpress == ChangeType)
	{
		NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, LineProperty.InterpPointCountData);
		ChangeType = ELineChangeType::PointNum;
	}
	else if (ELineChangeType::PointNum == ChangeType)
	{
		NeedChange = LineProperty.InterpPointCountData.Value.IsNumeric();
		LineProperty.InterpPointCountData.Expression = LineProperty.InterpPointCountData.Value;
	}
	if (NeedChange)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeCrossSectionProperty(static_cast<int32>(ChangeType), LineProperty);
		else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
			LoftRoutineManager.ChangeLoftRoutineProperty(static_cast<int32>(ChangeType), LineProperty);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeCutoutSectionProperty(static_cast<int32>(ChangeType), LineProperty);
		this->UpdateAxis();
	}
	else
	{
		OnSectionPropertyChangedHandler();
	}
}

void USingleComponentManager::OnCrossSectionRectanglePropertyChangedHandler(const int32& Type, const FGeomtryRectanglePlanProperty& Data)
{
	ERectangleChangeType ChangeType = static_cast<ERectangleChangeType>(Type);
	FGeomtryRectanglePlanProperty RectangleProperty(Data);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	switch (ChangeType)
	{
	case ERectangleChangeType::StartLocationX:NeedChange = RectangleProperty.StartLocationX.Value.IsNumeric(); RectangleProperty.StartLocationX.Expression = RectangleProperty.StartLocationX.Value; break;
	case ERectangleChangeType::StartLocationY:NeedChange = RectangleProperty.StartLocationY.Value.IsNumeric(); RectangleProperty.StartLocationY.Expression = RectangleProperty.StartLocationY.Value; break;
	case ERectangleChangeType::StartLocationZ:NeedChange = RectangleProperty.StartLocationZ.Value.IsNumeric(); RectangleProperty.StartLocationZ.Expression = RectangleProperty.StartLocationZ.Value; break;
	case ERectangleChangeType::EndLocationX:NeedChange = RectangleProperty.EndLocationX.Value.IsNumeric(); RectangleProperty.EndLocationX.Expression = RectangleProperty.EndLocationX.Value; break;
	case ERectangleChangeType::EndLocationY:NeedChange = RectangleProperty.EndLocationY.Value.IsNumeric(); RectangleProperty.EndLocationY.Expression = RectangleProperty.EndLocationY.Value; break;
	case ERectangleChangeType::EndLocationZ:NeedChange = RectangleProperty.EndLocationZ.Value.IsNumeric(); RectangleProperty.EndLocationZ.Expression = RectangleProperty.EndLocationZ.Value; break;
	case ERectangleChangeType::StartLocationXExpress:NeedChange = !RectangleProperty.StartLocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.StartLocationX); ChangeType = ERectangleChangeType::StartLocationX; break;
	case ERectangleChangeType::StartLocationYExpress:NeedChange = !RectangleProperty.StartLocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.StartLocationY); ChangeType = ERectangleChangeType::StartLocationY; break;
	case ERectangleChangeType::StartLocationZExpress:NeedChange = !RectangleProperty.StartLocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.StartLocationZ); ChangeType = ERectangleChangeType::StartLocationZ; break;
	case ERectangleChangeType::EndLocationXExpress:NeedChange = !RectangleProperty.EndLocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.EndLocationX); ChangeType = ERectangleChangeType::EndLocationX; break;
	case ERectangleChangeType::EndLocationYExpress:NeedChange = !RectangleProperty.EndLocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.EndLocationY); ChangeType = ERectangleChangeType::EndLocationY; break;
	case ERectangleChangeType::EndLocationZExpress:NeedChange = !RectangleProperty.EndLocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, RectangleProperty.EndLocationZ); ChangeType = ERectangleChangeType::EndLocationZ; break;
	default: checkNoEntry(); break;
	}
	if (NeedChange)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeCrossSectionProperty(static_cast<int32>(ChangeType), RectangleProperty);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeCutoutSectionProperty(static_cast<int32>(ChangeType), RectangleProperty);
		this->UpdateAxis();
	}
	else
	{
		OnSectionPropertyChangedHandler();
	}
}

void USingleComponentManager::OnCrossSectionEllipsePropertyChangedHandler(const int32& Type, const FGeomtryEllipsePlanProperty& Data)
{
	EEllipseChangeType ChangeType = static_cast<EEllipseChangeType>(Type);
	FGeomtryEllipsePlanProperty EllipseProperty(Data);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	switch (ChangeType)
	{
	case EEllipseChangeType::CenterLocationX:NeedChange = EllipseProperty.CenterLocationX.Value.IsNumeric(); EllipseProperty.CenterLocationX.Expression = EllipseProperty.CenterLocationX.Value; break;
	case EEllipseChangeType::CenterLocationY:NeedChange = EllipseProperty.CenterLocationY.Value.IsNumeric(); EllipseProperty.CenterLocationY.Expression = EllipseProperty.CenterLocationY.Value; break;
	case EEllipseChangeType::CenterLocationZ:NeedChange = EllipseProperty.CenterLocationZ.Value.IsNumeric(); EllipseProperty.CenterLocationZ.Expression = EllipseProperty.CenterLocationZ.Value; break;
	case EEllipseChangeType::ShortRadius:NeedChange = EllipseProperty.ShortRadiusData.Value.IsNumeric(); EllipseProperty.ShortRadiusData.Expression = EllipseProperty.ShortRadiusData.Value; break;
	case EEllipseChangeType::LongRadius:NeedChange = EllipseProperty.LongRadiusData.Value.IsNumeric(); EllipseProperty.LongRadiusData.Expression = EllipseProperty.LongRadiusData.Value; break;
	case EEllipseChangeType::PointNum: NeedChange = EllipseProperty.InterpPointCountData.Value.IsNumeric(); EllipseProperty.InterpPointCountData.Expression = EllipseProperty.InterpPointCountData.Value; break;
	case EEllipseChangeType::PointNumExpress: NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.InterpPointCountData); ChangeType = EEllipseChangeType::PointNum; break;
	case EEllipseChangeType::CenterLocationXExpress:NeedChange = !EllipseProperty.CenterLocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.CenterLocationX); ChangeType = EEllipseChangeType::CenterLocationX; break;
	case EEllipseChangeType::CenterLocationYExpress:NeedChange = !EllipseProperty.CenterLocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.CenterLocationY); ChangeType = EEllipseChangeType::CenterLocationY; break;
	case EEllipseChangeType::CenterLocationZExpress:NeedChange = !EllipseProperty.CenterLocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.CenterLocationZ); ChangeType = EEllipseChangeType::CenterLocationZ; break;
	case EEllipseChangeType::ShortRadiusExpress:NeedChange = !EllipseProperty.ShortRadiusData.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.ShortRadiusData); ChangeType = EEllipseChangeType::ShortRadius; break;
	case EEllipseChangeType::LongRadiusExpress:NeedChange = !EllipseProperty.LongRadiusData.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, EllipseProperty.LongRadiusData); ChangeType = EEllipseChangeType::LongRadius; break;
	default: checkNoEntry(); break;
	}
	if (NeedChange)
	{
		if (ESingleComponentEditType::ECrossSection == EditType)
			SectionManager.ChangeCrossSectionProperty(static_cast<int32>(ChangeType), EllipseProperty);
		else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
			CutoutSectionManager.ChangeCutoutSectionProperty(static_cast<int32>(ChangeType), EllipseProperty);
		this->UpdateAxis();
	}
	else
	{
		OnSectionPropertyChangedHandler();
	}
}

void USingleComponentManager::OnCrossSectionCubePropertyChangedHandler(const int32& Type, const FGeomtryCubeProperty& Data)
{
	ECuboidChangeType ChangeType = static_cast<ECuboidChangeType>(Type);
	FGeomtryCubeProperty CubeProperty(Data);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	switch (ChangeType)
	{
	case ECuboidChangeType::StartLocationX:NeedChange = CubeProperty.StartLocationX.Value.IsNumeric(); CubeProperty.StartLocationX.Expression = CubeProperty.StartLocationX.Value; break;
	case ECuboidChangeType::StartLocationY:NeedChange = CubeProperty.StartLocationY.Value.IsNumeric(); CubeProperty.StartLocationY.Expression = CubeProperty.StartLocationY.Value; break;
	case ECuboidChangeType::StartLocationZ:NeedChange = CubeProperty.StartLocationZ.Value.IsNumeric(); CubeProperty.StartLocationZ.Expression = CubeProperty.StartLocationZ.Value; break;
	case ECuboidChangeType::EndLocationX:NeedChange = CubeProperty.EndLocationX.Value.IsNumeric(); CubeProperty.EndLocationX.Expression = CubeProperty.EndLocationX.Value; break;
	case ECuboidChangeType::EndLocationY:NeedChange = CubeProperty.EndLocationY.Value.IsNumeric(); CubeProperty.EndLocationY.Expression = CubeProperty.EndLocationY.Value; break;
	case ECuboidChangeType::EndLocationZ:NeedChange = CubeProperty.EndLocationZ.Value.IsNumeric(); CubeProperty.EndLocationZ.Expression = CubeProperty.EndLocationZ.Value; break;
	case ECuboidChangeType::StartLocationXExpress:NeedChange = !CubeProperty.StartLocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.StartLocationX); ChangeType = ECuboidChangeType::StartLocationX; break;
	case ECuboidChangeType::StartLocationYExpress:NeedChange = !CubeProperty.StartLocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.StartLocationY); ChangeType = ECuboidChangeType::StartLocationY; break;
	case ECuboidChangeType::StartLocationZExpress:NeedChange = !CubeProperty.StartLocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.StartLocationZ); ChangeType = ECuboidChangeType::StartLocationZ; break;
	case ECuboidChangeType::EndLocationXExpress:NeedChange = !CubeProperty.EndLocationX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.EndLocationX); ChangeType = ECuboidChangeType::EndLocationX; break;
	case ECuboidChangeType::EndLocationYExpress:NeedChange = !CubeProperty.EndLocationY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.EndLocationY); ChangeType = ECuboidChangeType::EndLocationY; break;
	case ECuboidChangeType::EndLocationZExpress:NeedChange = !CubeProperty.EndLocationZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CubeProperty.EndLocationZ); ChangeType = ECuboidChangeType::EndLocationZ; break;
	default: checkNoEntry(); break;
	}
	if (NeedChange)
	{
		SectionManager.ChangeCrossSectionProperty(static_cast<int32>(ChangeType), CubeProperty);
		this->UpdateAxis();
	}
	else
	{
		OnSectionPropertyChangedHandler();
	}
}

void USingleComponentManager::OnCrossSectionEditionHandler(const int32& WidgetOperationType, const int32& OperationId)
{
	if (!TempSectionOperation.OperatorOrder.IsValidIndex(OperationId))
		return;

	EWidgetOperationType OperationType = static_cast<EWidgetOperationType>(WidgetOperationType);
	if (EWidgetOperationType::OperationDelete == OperationType)
	{
		TempSectionOperation.DeleteOperation(OperationId);
		CurrentSelectOperationIndex.Index = -1;
	}
	else if (EWidgetOperationType::OperationDown == OperationType)
	{
		if (TempSectionOperation.MoveOperationDown(OperationId))
		{
			CurrentSelectOperationIndex.Index = OperationId + 1;
		}
	}
	else if (EWidgetOperationType::OperationUp == OperationType)
	{
		if (TempSectionOperation.MoveOperationUp(OperationId))
		{
			CurrentSelectOperationIndex.Index = OperationId - 1;
		}
	}
	FSectionOperation::RefreshOperationID(TempSectionOperation);
	UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnCrossSectionDrawOperationPropertyChangedHandler(const int32& Type, const FSectionDrawOperation& Data)
{
	ETensileOperatorType OperatorType = static_cast<ETensileOperatorType>(Type);
	bool NeedChange = true;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	if (TempSectionOperation.OperatorOrder.IsValidIndex(Data.ID))
	{
		FSectionDrawOperation NewDrawData(Data);
		if (ETensileOperatorType::TensileXExpression == OperatorType)
		{
			NeedChange = !NewDrawData.DrawOffsetX.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters,
				{}, NewDrawData.DrawOffsetX);
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetX = NewDrawData.DrawOffsetX;
		}
		else if (ETensileOperatorType::TensileYExpression == OperatorType)
		{
			NeedChange = !NewDrawData.DrawOffsetY.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewDrawData.DrawOffsetY);
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetY = NewDrawData.DrawOffsetY;
		}
		else if (ETensileOperatorType::TensileZExpression == OperatorType)
		{
			NeedChange = !NewDrawData.DrawOffsetZ.Expression.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewDrawData.DrawOffsetZ);
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetZ = NewDrawData.DrawOffsetZ;
		}
		else if (ETensileOperatorType::TensileXValue == OperatorType)
		{
			NeedChange = Data.DrawOffsetX.Value.IsNumeric();
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetX = FExpressionValuePair(Data.DrawOffsetX.Value);
		}
		else if (ETensileOperatorType::TensileYValue == OperatorType)
		{
			NeedChange = Data.DrawOffsetY.Value.IsNumeric();
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetY = FExpressionValuePair(Data.DrawOffsetY.Value);
		}
		else if (ETensileOperatorType::TensileZValue == OperatorType)
		{
			NeedChange = Data.DrawOffsetZ.Value.IsNumeric();
			if (NeedChange)
				TempSectionOperation.DrawOperations[TempSectionOperation.OperatorOrder[Data.ID].Index].DrawOffsetZ = FExpressionValuePair(Data.DrawOffsetZ.Value);
		}
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
	}
}

void USingleComponentManager::OnCrossSectionShiftOperationPropertyChangedHandler(const int32& InType, const FSectionShiftingOperation& ShiftingData)
{
	EShiftingOrZoomType Type = static_cast<EShiftingOrZoomType>(InType);
	if (EShiftingOrZoomType::DataChange == Type)
	{
		if (TempSectionOperation.OperatorOrder.IsValidIndex(ShiftingData.ID))
		{
			bool Res = false;
			FSectionShiftingOperation NewShiftingOperations = ShiftingData;
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
			//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			for (int32 i = 0; i < NewShiftingOperations.ShiftValue.Num(); ++i)
			{
				Res = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {},NewShiftingOperations.ShiftValue[i]);
				if (!Res)
					break;
			}
			if (Res)
				TempSectionOperation.ShiftOperations[TempSectionOperation.OperatorOrder[ShiftingData.ID].Index] = NewShiftingOperations;
		}
	}
	UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnCrossSectionZoomOperationPropertyChangedHandler(const int32& InType, const FSectionZoomOperation& ZoomData)
{
	EShiftingOrZoomType Type = static_cast<EShiftingOrZoomType>(InType);
	if (EShiftingOrZoomType::DataChange == Type)
	{
		if (TempSectionOperation.OperatorOrder.IsValidIndex(ZoomData.ID))
		{
			FSectionZoomOperation NewZoomData = ZoomData;
			bool Res = false;
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
			//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			for (int32 i = 0; i < NewZoomData.ZoomValue.Num(); ++i)
			{
				Res = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewZoomData.ZoomValue[i]);
				if (!Res)
					break;
			}
			if (Res)
				TempSectionOperation.ZoomOperations[TempSectionOperation.OperatorOrder[ZoomData.ID].Index] = NewZoomData;
		}
	}
	UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnCrossSectionCutoutOperationPropertyChangedHandler(const int32& InType, const FSectionCutOutOperation& CutoutData)
{
	ECutOutOperatorType Type = static_cast<ECutOutOperatorType>(InType);
	bool NeedChange = true;
	if (ECutOutOperatorType::CutOutExpression == Type)
	{
		if (TempSectionOperation.OperatorOrder.IsValidIndex(CutoutData.ID))
		{
			FSectionCutOutOperation NewCutoutData(CutoutData);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
			//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewCutoutData.CutOutValue);
			if (NeedChange)
				TempSectionOperation.CutoutOperations[TempSectionOperation.OperatorOrder[CutoutData.ID].Index].CutOutValue = NewCutoutData.CutOutValue;
		}
	}
	else if (ECutOutOperatorType::CutOutValue == Type)
	{
		NeedChange = CutoutData.CutOutValue.Value.IsNumeric();
		if (NeedChange)
			TempSectionOperation.CutoutOperations[TempSectionOperation.OperatorOrder[CutoutData.ID].Index].CutOutValue = FExpressionValuePair(CutoutData.CutOutValue.Value);
	}
	else if (ECutOutOperatorType::CutOutSection == Type)
	{
		if (TempSectionOperation.CutoutOperations.IsValidIndex(TempSectionOperation.OperatorOrder[CutoutData.ID].Index))
		{
			EditType = ESingleComponentEditType::ECutoutSectionEdit;
			CutoutSectionManager.ClearCutoutSection();
			CurrentEditCurrentSection = TempSectionOperation.OperatorOrder[CutoutData.ID].Index;
			CutoutSectionManager.LoadFromArchiveData(TempSectionOperation.CutoutOperations[CurrentEditCurrentSection]);
			CutoutSectionManager.OnCutoutSectionPropertyChanged.BindUFunction(this, FName(TEXT("OnSectionPropertyChangedHandler")));
			CutoutSectionManager.OnSelectionChanged.BindUFunction(this, FName(TEXT("OnCrossSectionSelectionChangedHandler")));
			if (EPlanPolygonBelongs::EUnknown != SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs)
				OnChangeCameraType.ExecuteIfBound(static_cast<int32>(SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs));
			UIManager->UpdateSectionToolBarWidget(true, (int32)EToolBarState::DrawCutOut);
			FSectionOperation::RefreshOperationID(TempSectionOperation);
			UIManager->UpdateSectionOperationProperty(TempSectionOperation.CutoutOperations[CurrentEditCurrentSection]);
			this->ShowSingleComponentActor(false);
			this->UpdateAxis();
		}
	}
	if (ECutOutOperatorType::CutOutSection != Type)
	{
		FSectionOperation::RefreshOperationID(TempSectionOperation);
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
	}
}

void USingleComponentManager::OnCrossSectionLoftRoutinePropertyChangedHandler(const int32& InType)
{
	if (ESingleComponentEditType::EEditOperation == EditType)
	{
		ELoftingOperatorType Type = static_cast<ELoftingOperatorType>(InType);
		if (ELoftingOperatorType::SectionEdit == Type)
		{
			EditType = ESingleComponentEditType::ELoftRoutineEdit;
			LoftRoutineManager.ClearLoftRoutine();
			LoftRoutineManager.LoadFromArchiveData(TempSectionOperation.LoftingOperation);
			LoftRoutineManager.OnLoftRoutinePropertyChanged.BindUFunction(this, FName(TEXT("OnSectionPropertyChangedHandler")));
			LoftRoutineManager.OnSelectionChanged.BindUFunction(this, FName(TEXT("OnCrossSectionSelectionChangedHandler")));
			if (EPlanPolygonBelongs::EUnknown != TempSectionOperation.LoftingOperation.PlanBelongs)
				OnChangeCameraType.ExecuteIfBound(static_cast<int32>(TempSectionOperation.LoftingOperation.PlanBelongs));
			UIManager->UpdateSectionToolBarWidget(/*true, */(int32)EToolBarState::DrawLofting);
			FSectionOperation::RefreshOperationID(TempSectionOperation);
			UIManager->SetSingleComponentSectionToolBarSelectType(ESectionToolBarEditType::Select);
			UIManager->UpdateSectionOperationProperty(TempSectionOperation.LoftingOperation);
			this->ShowSingleComponentActor(false);
		}
	}
}

void USingleComponentManager::OnCrossSectionOperationSelectChangedHandler(const FSectionOperationOrder& OrderData)
{
	CurrentSelectOperationIndex = OrderData;
}

void USingleComponentManager::OnSingleComponentOperationToolBarEditHandler(const int32& InOperationType)
{
	if (ESingleComponentEditType::EEditOperation == EditType && SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
	{
		auto CheckHasLoftOperation = [&]()->bool {
			if (1 == TempSectionOperation.OperatorOrder.Num() && ESectionOperationType::ELoftingSection == TempSectionOperation.OperatorOrder[0].OperatorType /*&& EOperatorToolBarType::CutOut != static_cast<EOperatorToolBarType>(InOperationType)*/)
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Clear Loft"))
					, FText::FromStringTable(FName("PosSt"), TEXT("You have to delete loft first !"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return true;
			}
			return false;
		};

		int32 InserIndex = INDEX_NONE;//UIManager->JudgeOperationInsert(); 0.1.2取消此功能
		EOperatorToolBarType OperationType = static_cast<EOperatorToolBarType>(InOperationType);
		if (EOperatorToolBarType::Tensile == OperationType)
		{
			bool HasLoft = CheckHasLoftOperation();
			UE_LOG(LogTemp, Log, TEXT("USingleComponentManager::OnSingleComponentOperationToolBarEditHandler HasLoft=%d"), HasLoft);
			if (HasLoft)
				return;
			int32 ID = TempSectionOperation.DrawOperations.AddDefaulted();
			if (INDEX_NONE == InserIndex)
			{
				TempSectionOperation.DrawOperations[ID].ID = TempSectionOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EDrawSection, ID));
			}
			else
			{
				TempSectionOperation.OperatorOrder.Insert(FSectionOperationOrder(ESectionOperationType::EDrawSection, ID), InserIndex);
				FSectionOperation::RefreshOperationID(TempSectionOperation);
			}
		}
		else if (EOperatorToolBarType::Shifting == OperationType)
		{
			bool HasLoft = CheckHasLoftOperation();
			if (HasLoft)
				return;
			int32 ID = TempSectionOperation.ShiftOperations.AddDefaulted();
			if (INDEX_NONE == InserIndex)
			{
				TempSectionOperation.ShiftOperations[ID].ID = TempSectionOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EShiftSection, ID));
			}
			else
			{
				TempSectionOperation.OperatorOrder.Insert(FSectionOperationOrder(ESectionOperationType::EShiftSection, ID), InserIndex);
				FSectionOperation::RefreshOperationID(TempSectionOperation);
			}
		}
		else if (EOperatorToolBarType::Zoom == OperationType)
		{
			bool HasLoft = CheckHasLoftOperation();
			if (HasLoft)
				return;
			int32 ID = TempSectionOperation.ZoomOperations.AddDefaulted();
			if (INDEX_NONE == InserIndex)
			{
				TempSectionOperation.ZoomOperations[ID].ID = TempSectionOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EZoomSection, ID));
			}
			else
			{
				TempSectionOperation.OperatorOrder.Insert(FSectionOperationOrder(ESectionOperationType::EZoomSection, ID), InserIndex);
				FSectionOperation::RefreshOperationID(TempSectionOperation);
			}
		}
		else if (EOperatorToolBarType::CutOut == OperationType)
		{
			bool HasLoft = CheckHasLoftOperation();
			if (HasLoft)
				return;
			int32 ID = TempSectionOperation.CutoutOperations.AddDefaulted();
			TempSectionOperation.CutoutOperations[ID].PlanBelongs = SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs;
			if (INDEX_NONE == InserIndex)
			{
				TempSectionOperation.CutoutOperations[ID].ID = TempSectionOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::ECutoutSection, ID));
			}
			else
			{
				TempSectionOperation.OperatorOrder.Insert(FSectionOperationOrder(ESectionOperationType::ECutoutSection, ID), InserIndex);
				FSectionOperation::RefreshOperationID(TempSectionOperation);
			}
		}
		else if (EOperatorToolBarType::Lofting == OperationType)
		{
			if (0 != TempSectionOperation.OperatorOrder.Num())
			{
				//SOneButtonWidget::PopupModalWindow(NSLOCTEXT(LOCTEXT_NAMESPACE, "WarnningKey", "Warning"), NSLOCTEXT(LOCTEXT_NAMESPACE, "ClearAotherOperationKey", "You have to clear all other operations before loft"), NSLOCTEXT(LOCTEXT_NAMESPACE, "OKKey", "OK"));
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Warning"))
					, FText::FromStringTable(FName("PosSt"), TEXT("You have to clear all other operations before loft"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return;
			}
			TempSectionOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::ELoftingSection, 0));
			TempSectionOperation.LoftingOperation.ID = 0;
			switch (SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs)
			{
			case EPlanPolygonBelongs::EXY_Plan:TempSectionOperation.LoftingOperation.PlanBelongs = EPlanPolygonBelongs::EYZ_Plan; break;
			case EPlanPolygonBelongs::EXZ_Plan:TempSectionOperation.LoftingOperation.PlanBelongs = EPlanPolygonBelongs::EXY_Plan; break;
			case EPlanPolygonBelongs::EYZ_Plan:TempSectionOperation.LoftingOperation.PlanBelongs = EPlanPolygonBelongs::EXZ_Plan; break;
			}
		}
		else if (EOperatorToolBarType::Clear == OperationType)
		{
			//EPopButtonType ClickButton = STwoButtonsWidget::PopupModalWindow(NSLOCTEXT(LOCTEXT_NAMESPACE, "WarnningKey", "Warnning"), NSLOCTEXT(LOCTEXT_NAMESPACE, "ClearOperationsKey", *Msg), NSLOCTEXT(LOCTEXT_NAMESPACE, "ConfirmKey", "Confirm"), NSLOCTEXT(LOCTEXT_NAMESPACE, "CancelKey", "Cancel"));
			EPopButtonType ClickButton = STwoButtonsWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Warning"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to clear all operations ?"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));

			if (EPopButtonType::Confirm == ClickButton)
				TempSectionOperation.Empty();
			CurrentSelectOperationIndex.Index = -1;
		}
		else if (EOperatorToolBarType::Save == OperationType)
		{
			this->SaveAction();
			return;
		}
		else if (EOperatorToolBarType::Exit == OperationType)
		{
			this->ExitAction();
			return;
		}
		FSectionOperation::RefreshOperationID(TempSectionOperation);
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
	}
}

void USingleComponentManager::OnCrossSectionPropertyChangedHandler(const int32& Type, FSectionData& Data)
{
	checkf(SingleComponentProperty.ComponentItems.IsValidIndex(Data.Id), TEXT("Single component %d has %d sections, section index %d is unvalid"), SingleComponentToEdit.id, SingleComponentProperty.ComponentItems.Num(), Data.Id);
	if (SectionEditType::EditSection == static_cast<SectionEditType>(Type))
	{//Edit section
		CurrentEditSection = Data.Id;
		if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[CurrentEditSection].ComponentSource)
		{
			EditType = ESingleComponentEditType::ECrossSection;
			SectionManager.ClearCrossSection();
			SectionManager.LoadFromArchiveData(SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection);
			UIManager->UpdateSectionToolBarWidget(/*true*/);
			this->ShowSingleComponentActor(false);
			this->ShowCrossSectionPropertyPannel(SingleComponentProperty.ComponentItems[Data.Id].OperatorSection);

			if (EPlanPolygonBelongs::EUnknown != SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs)
			{
				if (ESectionType::ECube == SingleComponentProperty.ComponentItems[Data.Id].OperatorSection.SectionType)
					OnChangeCameraType.ExecuteIfBound(0);
				else
					OnChangeCameraType.ExecuteIfBound(static_cast<int32>(SingleComponentProperty.ComponentItems[CurrentEditSection].OperatorSection.PlanBelongs));
			}
		}
		else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[CurrentEditSection].ComponentSource)
		{
			EditType = ESingleComponentEditType::EEditImportSection;
			SingleComponentLocation = SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentLocation;
			SingleComponentRotation = SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentRotation;
			SingleComponentScale = SingleComponentProperty.ComponentItems[CurrentEditSection].SingleComponentScale;
			UIManager->UpdateImportSectionProperty(SingleComponentLocation, SingleComponentRotation, SingleComponentScale);
			UIManager->SetCameraEnable(false);
		}
		this->UpdateAxis();
	}
	else if (SectionEditType::VisibilityExpression == static_cast<SectionEditType>(Type) || SectionEditType::VisibilityValue == static_cast<SectionEditType>(Type))
	{//Delete section
		if (!SingleComponentProperty.ComponentItems.IsValidIndex(Data.Id))
			return;
		FExpressionValuePair VisiblityData(Data.VisibleParam);
		if (SectionEditType::VisibilityExpression == static_cast<SectionEditType>(Type))
		{
			TArray<TPair<int32, FString>> Comments;
			const FString CleanExp = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(VisiblityData.Expression, Comments);

			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
			//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			if (!CleanExp.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, VisiblityData))
			{
				SingleComponentProperty.ComponentItems[Data.Id].VisibleParam = VisiblityData;
			}
			else
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
					FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
			}

		}
		else if (SectionEditType::VisibilityValue == static_cast<SectionEditType>(Type))
		{
			if (!VisiblityData.Value.IsEmpty() && VisiblityData.Value.IsNumeric())
				SingleComponentProperty.ComponentItems[Data.Id].VisibleParam = FExpressionValuePair(VisiblityData.Value);
		}
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->ShowSingleComponentPropertyPannel();
	}
	else if (SectionEditType::ChangeName == static_cast<SectionEditType>(Type))
	{//Edit section name
		SingleComponentProperty.ComponentItems[Data.Id].SectionName = Data.SectionName;
	}
	else if (SectionEditType::ChangeThumbnail == static_cast<SectionEditType>(Type))
	{//Edit section thumbnail
		FString SourcePath = Data.ThumbnailPath;
		FString Extention = FPaths::GetExtension(Data.ThumbnailPath);
		Data.ThumbnailPath = FString::Printf(TEXT("SingleComponents/%d/thumbnails/%d.%s"), SingleComponentToEdit.id, Data.Id, *Extention);
		//Data.ThumbnailPath = FString::Printf(TEXT("ShotScreen/ProcessImage/%d_%d_image.png"), SingleComponentToEdit.folder_id, CurrentEditSection);

		FString DestinationPath = FPaths::ProjectContentDir() + Data.ThumbnailPath;
		DestinationPath = FPaths::ConvertRelativePathToFull(DestinationPath);
		if (ECopyFileErrorCode::ESuccess == FCatalogFunctionLibrary::CopyFileTo(SourcePath, DestinationPath))
		{
			SingleComponentProperty.ComponentItems[Data.Id].ThumbnailPath = Data.ThumbnailPath;
			this->ShowSingleComponentPropertyPannel();
		}
	}
	else if (SectionEditType::MaterialExpression == static_cast<SectionEditType>(Type) || SectionEditType::MaterialValue == static_cast<SectionEditType>(Type))
	{
		if (Data.MaterialInfo.ComponentMaterial.IsValidIndex(Data.MaterialInfo.EditMaterialId))
		{
			bool NeedChange = true;
			FExpressionValuePair NewProperty;
			if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[Data.Id].ComponentSource)
			{
				NewProperty = FExpressionValuePair(Data.MaterialInfo.ComponentMaterial[Data.MaterialInfo.EditMaterialId].MaterialID);
			}
			else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[Data.Id].ComponentSource)
			{
				NewProperty = FExpressionValuePair(Data.MaterialInfo.ComponentMaterial[Data.MaterialInfo.EditMaterialId].MaterialID);
			}
			if (SectionEditType::MaterialExpression == static_cast<SectionEditType>(Type))
			{
				TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
				//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
				if (NewProperty.Expression.IsEmpty())
				{
					FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewProperty);
					NeedChange = true;
				}
				else if (FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewProperty))
				{
					NeedChange = true;
				}
				else
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
						FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
					NeedChange = false;
				}
				/*!NewProperty.Expression.IsEmpty() &&*/
			}
			else if (SectionEditType::MaterialValue == static_cast<SectionEditType>(Type))
			{
				NeedChange = NewProperty.Value.IsNumeric() || NewProperty.Value.IsEmpty();
				NewProperty.Expression = NewProperty.Value;
			}
			if (NeedChange)
			{
				NewProperty.Value = URefRelationFunction::FormatFolderID(NewProperty.Value);
				EditSectionData = Data;
				EditSectionMaterialProperty = NewProperty;
				/*if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[Data.Id].ComponentSource)
				{
					SingleComponentProperty.ComponentItems[Data.Id].ComponentMaterial = NewProperty;
				}
				else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[Data.Id].ComponentSource)
				{
					SingleComponentProperty.ComponentItems[Data.Id].ImportMesh[Data.MaterialInfo.EditMaterialId].MaterialId = NewProperty;
				}*/
				

				/*
				*  @@ depend on file already has material
				*  @@ set material to null
				*/
				if (CurEditDependData.MatAlreadyHas(NewProperty.Value)
					|| NewProperty.Expression.IsEmpty() || NewProperty.Value.IsEmpty())
				{
					if (ESingleComponentSource::ECustom == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
					{
						SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentMaterial = EditSectionMaterialProperty;
					}
					else if (ESingleComponentSource::EImportFBX == SingleComponentProperty.ComponentItems[EditSectionData.Id].ComponentSource)
					{
						SingleComponentProperty.ComponentItems[EditSectionData.Id].ImportMesh[EditSectionData.MaterialInfo.EditMaterialId].MaterialId = EditSectionMaterialProperty;
					}

					this->RefreshSingleComponentActor();
					this->ShowSingleComponentActor();
					this->ShowSingleComponentPropertyPannel();
				}
				else
				{
					QueryModelOrMatDetailInfo(NewProperty.Value);
				}
				
			}
		}
		/*this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->ShowSingleComponentPropertyPannel();*/
	}
	else
	{
		checkNoEntry();
	}
}

//Section property

void USingleComponentManager::OnPropertyToolbarSectionPropertyChangedHandler(const int32& InEditType, const int32& InSectionId)
{
	EPropertyEditType SectionEditType = static_cast<EPropertyEditType>(InEditType);
	if (EPropertyEditType::Copy == SectionEditType)
	{
		FSingleComponentItem NewSection;
		SingleComponentProperty.ComponentItems[InSectionId].CopyData(NewSection);

		if (!NewSection.ThumbnailPath.IsEmpty())
		{
			NewSection.ThumbnailPath = FString::Printf(TEXT("SingleComponents/%d/thumbnails/%s.%s"), SingleComponentToEdit.id, *FGuid::NewGuid().ToString().ToLower(), *FPaths::GetExtension(NewSection.ThumbnailPath));
			FString DestinationPath = FPaths::ProjectContentDir() + NewSection.ThumbnailPath;
			FString SourcePath = FPaths::ProjectContentDir() + SingleComponentProperty.ComponentItems[InSectionId].ThumbnailPath;
			SourcePath = FPaths::ConvertRelativePathToFull(SourcePath);
			DestinationPath = FPaths::ConvertRelativePathToFull(DestinationPath);

			if (ECopyFileErrorCode::ESuccess != FCatalogFunctionLibrary::CopyFileTo(SourcePath, DestinationPath))
				return;
		}

		SingleComponentProperty.ComponentItems.Add(NewSection);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->ShowSingleComponentPropertyPannel();
	}
	else if (EPropertyEditType::Delete == SectionEditType)
	{
		CurrentEditSection = -1;
		SingleComponentProperty.ComponentItems.RemoveAt(InSectionId);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->ShowSingleComponentPropertyPannel();
	}
	else if (EPropertyEditType::Import == SectionEditType)
	{
		this->OnImportSectionStateChangedHandler(EImportActionType::ImportFile, TEXT(""), TEXT(""));
	}
	else if (EPropertyEditType::Operator == SectionEditType)
	{
		CurrentEditSection = InSectionId;
		EditType = ESingleComponentEditType::EEditOperation;
		TempSectionOperation = SingleComponentProperty.ComponentItems[CurrentEditSection].SectionOperation;
		int32 Index = 0;
		for (auto& Iter : TempSectionOperation.OperatorOrder)
		{
			switch (Iter.OperatorType)
			{
			case ESectionOperationType::EDrawSection:TempSectionOperation.DrawOperations[Iter.Index].ID = Index; break;
			case ESectionOperationType::EShiftSection:TempSectionOperation.ShiftOperations[Iter.Index].ID = Index; break;
			case ESectionOperationType::EZoomSection:TempSectionOperation.ZoomOperations[Iter.Index].ID = Index; break;
			case ESectionOperationType::ECutoutSection:TempSectionOperation.CutoutOperations[Iter.Index].ID = Index; break;
			}
			++Index;
		}
		FSectionOperation::RefreshOperationID(TempSectionOperation);
		UIManager->UpdateSectionOperationProperty(TempSectionOperation, CurrentSelectOperationIndex);
		UIManager->UpdateSectionOperationToolBarWidget(true);
		UIManager->SetCameraEnable(true);
		this->RefreshSingleComponentActor();
		this->ShowSingleComponentActor();
		this->UpdateAxis();
	}
	this->UpdateAxis();
}

void USingleComponentManager::OnImportSectionStateChangedHandler(const EImportActionType& ActionType, const FString& InSavePath, const FString& InRefPath)
{
	FString ImagePath;
	if (EImportActionType::ImportFile == ActionType)
	{
		FString SourceFilePath;
		FCatalogFunctionLibrary::OpenFileDialogForImport(SourceFilePath);
		if (!SourceFilePath.IsEmpty())
		{
			UIManager->UpdateSectionImportWidget(0.0f, EImportStateType::Normal);

			if (!IS_OBJECT_PTR_VALID(FbxConvert))
				FbxConvert = NewObject<UFbxConvertTask>();
			FbxConvert->FbxConvertProgress.BindUFunction(this, FName(TEXT("OnFbxConvertProgressHandler")));
			FbxConvert->FbxConvertComplete.BindUFunction(this, FName(TEXT("OnFbxConvertCompletedHandler")));

			UIManager->UpdateImportName(FPaths::GetBaseFilename(SourceFilePath));
			UIManager->UpdateSectionImportWidget(0.0f, EImportStateType::Importing);
			FbxConvert->StartConvert(SourceFilePath);
		}
	}
	else if (EImportActionType::Sure == ActionType)
	{
		const FString FileExtension = FPaths::GetExtension(FbxConvert->GetFilePath());
		if (FileExtension.Equals(TEXT("pak"), ESearchCase::IgnoreCase))
		{//导入的文件为Pak
			ImportSection->ComponentSource = ESingleComponentSource::EImportPAK;
			ImportSection->PakRefPath = InRefPath;
			ImportSection->PakRelativeFilePath = InSavePath;

			{//Copy file and mount pak
				const FString DesPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + ImportSection->PakRelativeFilePath);
				if (FPaths::FileExists(DesPath))
				{//如果目标文件已经存在则Unmount
					GetWorld()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->UnmountPakFile(DesPath);
				}
				//GetWorld()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->MountPakFile(DesPath,false);
				FString SourcePath = FbxConvert->GetFilePath();
				ECopyFileErrorCode Res = FCatalogFunctionLibrary::CopyFileTo(SourcePath, DesPath);
				if (ECopyFileErrorCode::ESuccess != Res)
				{
					ImportSection.Reset();
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
						FString(TEXT("Import PAK"))
						, FString(TEXT("Copy pak file failed!")));
					return;
				}
				else
				{
					GetWorld()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->MountPakFile(DesPath, true);
					SingleComponentProperty.ComponentItems.Add(*ImportSection);
					this->RefreshSingleComponentActor();
					this->ShowSingleComponentActor();
					this->ShowSingleComponentPropertyPannel();
					SaveImportImage(OpenImage, true);
					this->UpdateAxis();
					OpenImage.Empty();
					this->ShowSingleComponentPropertyPannel();
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
						FString(TEXT("Import PAK"))
						, FString(TEXT("Import pak file succeed!")));
				}
			}
		}
		else
		{//导入的文件为Fbx
			const FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s.%s"), *SingleCompFolderData.id, *FGuid::NewGuid().ToString().ToLower(), *FPaths::GetExtension(OpenImage));
			if (!OpenImage.IsEmpty())
			{//用于导入时选择了头图
				const FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);
				ECopyFileErrorCode CopyResult = FCatalogFunctionLibrary::CopyFileTo(OpenImage, DataPath);
				if (ECopyFileErrorCode::ESuccess != CopyResult)
				{
					UE_LOG(LogTemp, Log, TEXT("Copy fbx image from %s to %s failed!"), *OpenImage, *DataPath);
					return;
				}
				SingleCompFolderData.thumbnail_path = SavePath;
				FDownloadFileData File(SavePath);
				ACatalogPlayerController::Get()->GetFileMD5AndSize(DataPath, File.md5, File.size);
				FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
			}
			ImportSection->ComponentSource = ESingleComponentSource::EImportFBX;
			TArray<FPMCSection> MeshInfo;
			if (!FbxConvert->GetMeshInfo(MeshInfo))
			{
				UE_LOG(LogTemp, Log, TEXT("Convert fbx file failed!"));
				return;
			}
			ImportSection->ImportMesh.AddDefaulted(MeshInfo.Num());
			int32 Index = 0;
			for (auto& Iter : MeshInfo)
			{
				ImportSection->ImportMesh[Index].SectionMesh = Iter;
				++Index;
			}
			SingleComponentProperty.ComponentItems.Add(*ImportSection);
			this->RefreshSingleComponentActor();
			this->ShowSingleComponentActor();
			this->ShowSingleComponentPropertyPannel();
			SingleComponentProperty.ComponentItems[SingleComponentProperty.ComponentItems.Num() - 1].ThumbnailPath = SavePath;
			this->UpdateAxis();
			OpenImage.Empty();
			this->ShowSingleComponentPropertyPannel();
		}
	}
	else if (EImportActionType::Cancel == ActionType)
	{
		if (IS_OBJECT_PTR_VALID(FbxConvert))
		{
			FbxConvert->StopConvert();
			FbxConvert = nullptr;
		}
		OpenImage.Empty();
	}
	else if (EImportActionType::ChangeImage == ActionType)
	{
		if (ImportSection.IsValid())
		{
			if (false == bImageSelecting)
			{
				bImageSelecting = true;
				FCatalogFunctionLibrary::OpenFileDialogForImage(OpenImage);
				bImageSelecting = false;
				if (!OpenImage.IsEmpty())
				{
					UIManager->UpdateImportImage(OpenImage);
				}
			}
		}
	}
	else if (EImportActionType::ImportCADFile == ActionType)
	{
		FString SourceFilePath;
		FCatalogFunctionLibrary::OpenFileDialogForDXF(SourceFilePath);
		if (!SourceFilePath.IsEmpty())
		{
			FEasyDXFLibrary::InitializeCAD();
			FEasyDXFLibrary::LoadDXFEntities(SourceFilePath);
			TArray<DxfLib::FDxfLWPolyLineData*> OutLWPolyLines;
			FEasyDXFLibrary::GetLWPolyLineEntities(OutLWPolyLines);
			if (OutLWPolyLines.Num() > 0)
			{
				TArray<FVector> Points;
				TArray<double>  Bulges;

				//如果首尾点是一个点，则去掉尾点
				TArray<FVector> PointInCADs = OutLWPolyLines[0]->GetPoints();
				if (PointInCADs.Num() > 1)
				{
					if (FMath::IsNearlyEqual(PointInCADs[0].X, PointInCADs.Last().X, 0.001f)
						&& FMath::IsNearlyEqual(PointInCADs[0].Y, PointInCADs.Last().Y, 0.001f))
					{
						PointInCADs.RemoveAt(PointInCADs.Num() - 1);
					}
				}

				TArray<FVector> Point2Ds;
				for (auto& PointIte : PointInCADs)
				{
					Point2Ds.Add(FVector(PointIte.X, -PointIte.Y, 0.0));
				}
				bool bIsCCW = FPolygon3DLibrary::IsPolygonCCWWinding(Point2Ds, FVector::ZAxisVector);
				//得到所有线
				TArray<FGeomtryLineProperty> Lines;
				for(int32 i = 0 ; i< PointInCADs.Num(); ++i)
				{
					FGeomtryLineProperty NewLine;
					int32 NextI = (i + 1) % PointInCADs.Num();
					NewLine.StartLocation = PointInCADs[i];
					NewLine.EndLocation = PointInCADs[NextI];

					double bulge = NewLine.StartLocation.Z;
					NewLine.StartLocation.Z = 0;
					NewLine.EndLocation.Z = 0;

					if (bulge != 0.0)
					{
						NewLine.LineType = ELineType::ERadiusArc;
					    //NewLine.BigArc = NewLine.StartLocation.Z > 0;
						double sita = FMath::Atan(FMath::Abs(bulge)) * 4;
						double Radius = (FVector::Distance(NewLine.StartLocation, NewLine.EndLocation) / 2) / FMath::Sin(sita / 2);
						bIsCCW ? Radius *= (bulge < 0 ? 1 : -1) : Radius *= (bulge < 0 ? -1 : 1);
						NewLine.BigArc = FMath::Sin(sita) < 0;
						NewLine.RadiusOrHeightData.Value = NewLine.RadiusOrHeightData.Expression = FString::Printf(TEXT("%lf"), Radius);
					}
					NewLine.StartLocation = FEasyDXFLibrary::CADToUE4(NewLine.StartLocation) * 0.1f;
					NewLine.EndLocation = FEasyDXFLibrary::CADToUE4(NewLine.EndLocation) * 0.1f;

					Lines.Add(MoveTemp(NewLine));
				}

				OnClickToolbarHandler(1);

				if (ESingleComponentEditType::ECrossSection == EditType)
				{
					SectionManager.AddGeomtryPointsFromCAD(Lines);
				}
				else if (ESingleComponentEditType::ELoftRoutineEdit == EditType)
				{
					LoftRoutineManager.AddGeomtryPointsFromCAD(Lines);
				}
				else if (ESingleComponentEditType::ECutoutSectionEdit == EditType)
				{
					CutoutSectionManager.AddGeomtryPointsFromCAD(Lines);
				}
			}

			FEasyDXFLibrary::DeinitializeCAD();

			
		}
	}
}

void USingleComponentManager::OnFbxConvertProgressHandler(const float& Progress)
{
	UIManager->UpdateSectionImportWidget(Progress / 100.0f, EImportStateType::Importing);
}

void USingleComponentManager::OnFbxConvertCompletedHandler(bool Success)
{
	ImportSection = MakeShared<FSingleComponentItem>();
	UIManager->UpdateSectionImportWidget(1.0f, Success ? EImportStateType::Succeed : EImportStateType::Failed);
	bool EditPathCanSeen = false;
	if (IS_OBJECT_PTR_VALID(FbxConvert))
	{
		const FString Extension = FPaths::GetExtension(FbxConvert->GetFilePath());
		EditPathCanSeen = Extension.Equals(TEXT("pak"), ESearchCase::IgnoreCase);
	}
	UIManager->ShowEdtPath(EditPathCanSeen);
}

void USingleComponentManager::OnImportSectionLocationChangedHandler(const int32& InEditType, const FString& OutString)
{
	if (!SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		return;
	if (ESingleComponentSource::EImportFBX != SingleComponentProperty.ComponentItems[CurrentEditSection].ComponentSource)
		return;
	EImportLocationType Type = static_cast<EImportLocationType>(InEditType);
	FExpressionValuePair NewExpressionValue;
	bool NeedChange = false;
	if (EImportLocationType::CoordinateXExpress == Type || EImportLocationType::CoordinateYExpress == Type || EImportLocationType::CoordinateZExpress == Type)
	{
		NewExpressionValue.Expression = OutString;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewExpressionValue);
	}
	else if (OutString.IsNumeric())
	{
		NeedChange = true;
		NewExpressionValue.Expression = OutString;
		NewExpressionValue.Value = OutString;
	}
	if (!NeedChange)
		return;
	if (EImportLocationType::CoordinateXExpress == Type || EImportLocationType::CoordinateXValue == Type)
	{
		SingleComponentLocation.LocationX = NewExpressionValue;
	}
	else if (EImportLocationType::CoordinateYExpress == Type || EImportLocationType::CoordinateYValue == Type)
	{
		SingleComponentLocation.LocationY = NewExpressionValue;
	}
	else if (EImportLocationType::CoordinateZExpress == Type || EImportLocationType::CoordinateZValue == Type)
	{
		SingleComponentLocation.LocationZ = NewExpressionValue;
	}
	UIManager->UpdateImportSectionProperty(SingleComponentLocation, SingleComponentRotation, SingleComponentScale);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnImportSectionRotationChangedHandler(const int32& InEditType, const FString& OutString)
{
	if (!SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		return;
	if (ESingleComponentSource::EImportFBX != SingleComponentProperty.ComponentItems[CurrentEditSection].ComponentSource)
		return;
	EImportRotationType Type = static_cast<EImportRotationType>(InEditType);
	FExpressionValuePair NewExpressionValue;
	bool NeedChange = false;
	if (EImportRotationType::RotatingXExpress == Type || EImportRotationType::RotatingYExpress == Type || EImportRotationType::RotatingZExpress == Type)
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		NewExpressionValue.Expression = OutString;
		NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewExpressionValue);
	}
	else if (OutString.IsNumeric())
	{
		NeedChange = true;
		NewExpressionValue.Expression = OutString;
		NewExpressionValue.Value = OutString;
	}
	if (!NeedChange)
		return;
	if (EImportRotationType::RotatingXExpress == Type || EImportRotationType::RotatingXValue == Type)
	{
		SingleComponentRotation.Roll = NewExpressionValue;
	}
	else if (EImportRotationType::RotatingYExpress == Type || EImportRotationType::RotatingYValue == Type)
	{
		SingleComponentRotation.Pitch = NewExpressionValue;
	}
	else if (EImportRotationType::RotatingZExpress == Type || EImportRotationType::RotatingZValue == Type)
	{
		SingleComponentRotation.Yaw = NewExpressionValue;
	}
	UIManager->UpdateImportSectionProperty(SingleComponentLocation, SingleComponentRotation, SingleComponentScale);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnImportSectionScaleChangedHandler(const int32& InEditType, const FString& OutString)
{
	if (!SingleComponentProperty.ComponentItems.IsValidIndex(CurrentEditSection))
		return;
	if (ESingleComponentSource::EImportFBX != SingleComponentProperty.ComponentItems[CurrentEditSection].ComponentSource)
		return;
	EImportScaleType Type = static_cast<EImportScaleType>(InEditType);
	FExpressionValuePair NewExpressionValue;
	bool NeedChange = false;
	if (EImportScaleType::ZoomXExpress == Type || EImportScaleType::ZoomYExpress == Type || EImportScaleType::ZoomZExpress == Type)
	{
		NewExpressionValue.Expression = OutString;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		NeedChange = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, NewExpressionValue);
	}
	else if (OutString.IsNumeric())
	{
		NeedChange = true;
		NewExpressionValue.Expression = OutString;
		NewExpressionValue.Value = OutString;
	}
	if (!NeedChange)
		return;
	if (EImportScaleType::ZoomXExpress == Type || EImportScaleType::ZoomXValue == Type)
	{
		SingleComponentScale.X = NewExpressionValue;
	}
	else if (EImportScaleType::ZoomYExpress == Type || EImportScaleType::ZoomYValue == Type)
	{
		SingleComponentScale.Y = NewExpressionValue;
	}
	else if (EImportScaleType::ZoomZExpress == Type || EImportScaleType::ZoomZValue == Type)
	{
		SingleComponentScale.Z = NewExpressionValue;
	}
	UIManager->UpdateImportSectionProperty(SingleComponentLocation, SingleComponentRotation, SingleComponentScale);
	this->RefreshSingleComponentActor();
	this->ShowSingleComponentActor();
}

void USingleComponentManager::OnSingleComponentFilePropertyChangedHandler(const int32& InEditTyp, const FString& InData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	bool NeedSaveFileProperty = false;
	FString CleanData = InData.TrimStart();
	CleanData = CleanData.TrimEnd();
	auto NewData = SingleCompFolderData;
	if (EFilePropertyType::Id == static_cast<EFilePropertyType>(InEditTyp))
	{
		if (CleanData.Len() <= 0)
		{
			NeedSaveFileProperty = true;
			NewData.folder_id = CleanData;
		}
		else
		{
			NeedSaveFileProperty = CleanData.IsNumeric() && CleanData.Len() <= 8 && !CleanData.Equals(SingleCompFolderData.folder_id);

			if (!NeedSaveFileProperty && CleanData.Len() > 0)
			{
				FRegexPattern Pattern(TEXT("[.]"));
				FRegexMatcher RegMatcher(Pattern, CleanData);
				RegMatcher.SetLimits(0, CleanData.Len());
				if (!RegMatcher.FindNext())
				{
					int ToInt = FCString::Atoi(*CleanData);
					CleanData = FString::FromInt(ToInt);
					NeedSaveFileProperty = CleanData.Equals(SingleCompFolderData.folder_id);
				}
			}
			if (NeedSaveFileProperty)
			{
				CleanData = FString::FromInt(FCString::Atoi(*CleanData));
				NeedSaveFileProperty = UFolderTableOperatorLibrary::IsFolderIdUnique(CleanData);
				if (NeedSaveFileProperty)
				{
					NewData.folder_id = CleanData;
				}
			}
		}
	}
	else if ((EFilePropertyType::CodingExp == static_cast<EFilePropertyType>(InEditTyp)) || (EFilePropertyType::Coding == static_cast<EFilePropertyType>(InEditTyp)))
	{
		FExpressionValuePair CodeExp;
		CodeExp.Expression = CleanData;
		CodeExp.Value = SingleCompFolderData.folder_code;
		NeedSaveFileProperty = FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, CodeExp);
		if (NeedSaveFileProperty)
		{
			NewData.folder_code_exp = CodeExp.Expression;
			NewData.folder_code = CodeExp.Value;
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		}
	}
	else if (EFilePropertyType::Name == static_cast<EFilePropertyType>(InEditTyp))
	{
		NeedSaveFileProperty = CleanData.Len() <= 20 && CleanData.Len() > 0;
		if (NeedSaveFileProperty)
			NewData.folder_name = CleanData;
	}
	else if (EFilePropertyType::NameExpress == static_cast<EFilePropertyType>(InEditTyp))
	{
		TArray<TPair<int32, FString>> Comments;
		const FString CleanExp = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(CleanData, Comments);
		FString OutMaxValue;
		FString OutMaxExpression;
		bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, {}, CleanExp, OutMaxValue, OutMaxExpression);
		NeedSaveFileProperty = !CleanExp.IsEmpty() && Res;

		if (NeedSaveFileProperty)
		{
			NewData.folder_name_exp = OutMaxExpression;
			NewData.folder_name = OutMaxValue;
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		}
	}
	else if (EFilePropertyType::VisiExpress == static_cast<EFilePropertyType>(InEditTyp))
	{
		TArray<TPair<int32, FString>> Comments;
		const FString CleanExp = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(CleanData, Comments);

		FExpressionValuePair Visi(CleanData);
		NeedSaveFileProperty = !CleanExp.IsEmpty() && FGeometryDatas::CalculateParameterValue(GlobalParameters, OverrideParameters, {}, Visi);
		if (NeedSaveFileProperty)
		{
			NewData.visibility_exp = Visi.Expression;
			NewData.visibility = FCString::Atof(*Visi.Value);
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		}
	}
	else if (EFilePropertyType::VisiValue == static_cast<EFilePropertyType>(InEditTyp))
	{
		NeedSaveFileProperty = !CleanData.IsEmpty() && CleanData.IsNumeric();
		if (NeedSaveFileProperty)
		{
			NewData.visibility_exp = FString::SanitizeFloat(SingleCompFolderData.visibility = FCString::Atof(*CleanData));
			NewData.visibility= FCString::Atof(*CleanData);
		}
	}
	if (NeedSaveFileProperty)
	{
		FRefDirectoryData RefData;
		if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(SingleCompFolderData.id, RefData))
		{
			URefRelationFunction::ConvertDBDataToDirctoryData(SingleCompFolderData, RefData);
		}

		NewData.backend_folder_path = FPaths::Combine(
			URefRelationFunction::GetFolderDirectory(NewData.backend_folder_path, false), NewData.folder_id.IsEmpty() ? NewData.id : NewData.folder_id
		);

		RefData.folderCode = NewData.folder_code;
		RefData.folderCodeExp = NewData.folder_code_exp;
		RefData.folderId = NewData.folder_id;
		RefData.folderName = NewData.folder_name;
		RefData.folderNameExp = NewData.folder_name_exp;
		RefData.visibility = NewData.visibility;
		RefData.visibilityExp = NewData.visibility_exp;
		RefData.backendFolderPath = NewData.backend_folder_path;

		UpdateDataRequest(RefData, false);

		//UFolderTableOperatorLibrary::UpdateFile(SingleCompFolderData);
	}
	else
	{
		UIManager->UpdateSingleComponentFileProperty(SingleCompFolderData);
	}
	//UIManager->UpdateSingleComponentFileProperty(SingleCompFolderData);
}

void USingleComponentManager::TakePictureHandler(const int32& PictureType)
{

	if (On2DSectionEdit)
	{
		if (PictureType != 10)
		{
			if (PictureType == 9 || PictureType == 11)
			{
				SectionManager.HidePoints(false);
				TakePictureTypeDelegate.ExecuteIfBound(PictureType);
			}
			else
			{
				SectionManager.HidePoints(true);
				SectionManager.ClearDrawState();
				UIManager->SingleComponentResetToolState();
				TakePictureTypeDelegate.ExecuteIfBound(-2);
			}
		}
	}
	else
	{
		if (PictureType != 10)
		{
			TakePictureTypeDelegate.ExecuteIfBound(PictureType);
		}
	}
	if (PictureType == 9)
	{
		if (false == bImageSelecting)
		{
			bImageSelecting = true;
			FCatalogFunctionLibrary::OpenFileDialogForImage(OpenImage);
			bImageSelecting = false;
			if (OpenImage.IsEmpty())
			{
				UE_LOG(LogTemp, Log, TEXT("User selected none image files!"));
				return;
			}
			UIManager->SetCameraImage(OpenImage, !OpenImage.IsEmpty());
		}
	}
	else if (PictureType == 10)
	{
		if (CurrentPictureType != 9)
		{
			
			ShotScreenHandler();
		}
		else
		{
			SaveImportImage(OpenImage, false);
			TakePictureTypeDelegate.ExecuteIfBound(PictureType);
		}
		SectionManager.HidePoints(false);
		OpenImage.Empty();
		this->UpdateAxis();

	}
	else
	{
		UIManager->SetCameraImage(OpenImage, false);
	}
	if (PictureType == 11)
	{
		OpenImage.Empty();
		this->UpdateAxis();
	}
	CurrentPictureType = PictureType;
}

void USingleComponentManager::UpdateAndUploadImage(const FString& ImageRelativePath)
{
	if (ImageRelativePath.IsEmpty())
	{
		return;
	}

	SingleCompFolderData.thumbnail_path = ImageRelativePath;

	FRefDirectoryData RefData;
	if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(SingleCompFolderData.id, RefData))
	{
		URefRelationFunction::ConvertDBDataToDirctoryData(SingleCompFolderData, RefData);
	}

	RefData.thumbnailPath = ImageRelativePath;

	UpdateDataRequest(RefData, false);

	bExitAfterUpload = false;
	UploadFileRequest(ImageRelativePath);
}

void USingleComponentManager::ShotScreenHandler()
{
	FString FilePath = FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png");

	FilePath = FPaths::ConvertRelativePathToFull(FilePath);
	ACatalogPlayerController::Get()->TakeScreenShot(FilePath);
}

void USingleComponentManager::ProcessImageHandler()
{
	const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("ShotScreen/Temp/Test.png"));

	{//确保头图文件夹存在
		const FString TempPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + FString::Printf(TEXT("SingleComponents/%s/Thumbnails"), *SingleCompFolderData.id));
		if (false == FPaths::DirectoryExists(TempPath))
		{
			FCatalogFunctionLibrary::CreateDirectoryRecursively(TempPath);
		}
	}
	if (ESingleComponentEditType::ECrossSection == EditType || ESingleComponentEditType::EEditOperation == EditType)
	{
		FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s-%d.%s"), *SingleCompFolderData.id, *SingleCompFolderData.id, CurrentEditSection, *FPaths::GetExtension(FilePath));
		FString TargetPath = FPaths::ProjectContentDir() + SavePath;
		if (FImageProcessModule::Get()->ThumbnailConvert(FilePath, TargetPath))
		{
			SingleComponentProperty.ComponentItems[CurrentEditSection].ThumbnailPath = SavePath;
		}
	}
	else
	{
		FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s.%s"), *SingleCompFolderData.id, *SingleCompFolderData.id, *FPaths::GetExtension(FilePath));
		FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

		if (FImageProcessModule::Get()->ThumbnailConvert(FilePath, TargetPath))
		{
#ifdef USE_REF_LOCAL_FILE

			UpdateAndUploadImage(SavePath);

#else

			SingleCompFolderData.thumbnail_path = SavePath;
			UFolderTableOperatorLibrary::UpdateFile(SingleCompFolderData);

#endif
		}
	}
	TakePictureTypeDelegate.ExecuteIfBound(11);

}

void USingleComponentManager::SaveImportImage(const FString& SrcImage, bool IsNew)
{
	if (SrcImage.IsEmpty())
	{
		return;
	}
	if (!IsNew)
	{
		if (SingleComponentToEdit.folder_id.IsEmpty())
		{
			SingleComponentToEdit.folder_id = SingleCompFolderData.id;
		}
		if (ESingleComponentEditType::ECrossSection == EditType || ESingleComponentEditType::EEditOperation == EditType)
		{
			FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s-%d.%s"), *SingleCompFolderData.id, *SingleCompFolderData.id, CurrentEditSection, *FPaths::GetExtension(SrcImage));
			FString DataPath = FPaths::ProjectContentDir() + SavePath;
			DataPath = FPaths::ConvertRelativePathToFull(DataPath);
			FCatalogFunctionLibrary::CopyFileTo(SrcImage, DataPath);
			{
				SingleComponentProperty.ComponentItems[CurrentEditSection].ThumbnailPath = SavePath;
			}
		}
		else if (ESingleComponentEditType::EView == EditType)
		{
			const FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s.%s"), *SingleCompFolderData.id, *SingleCompFolderData.id, *FPaths::GetExtension(SrcImage));
			const FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

			//UE_LOG(LogTemp, Log, TEXT("%s"), *DataPath);

			FCatalogFunctionLibrary::CopyFileTo(SrcImage, DataPath);
			{
#ifdef USE_REF_LOCAL_FILE

				UpdateAndUploadImage(SavePath);

#else

				SingleCompFolderData.thumbnail_path = SavePath;
				UFolderTableOperatorLibrary::UpdateFile(SingleCompFolderData);

#endif
			}
		}
	}
	else
	{
		const FString SavePath = FString::Printf(TEXT("SingleComponents/%s/Thumbnails/%s.%s"), *SingleCompFolderData.id, *FGuid::NewGuid().ToString().ToLower(), *FPaths::GetExtension(SrcImage));
		const FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);
		FCatalogFunctionLibrary::CopyFileTo(SrcImage, DataPath);
		{
			SingleComponentProperty.ComponentItems[SingleComponentProperty.ComponentItems.Num() - 1].ThumbnailPath = SavePath;
		}
	}
}

#undef LOCTEXT_NAMESPACE
