// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/Geometry/CrossSection/CrossSectionManager.h"
#include "DesignStation/Geometry/CrossSection/LoftRoutineManager.h"
#include "DesignStation/Geometry/CrossSection/CutoutSectionManager.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "DesignStation/Geometry/SingleComponent/ShowSingleComponentActor.h"
#include "DesignStation/BasicClasses/Managers/UIManager/SingleComponentUIManager.h"
#include "FbxFileImport/FbxConvert/FbxConvertTask.h"
#include "DesignStation/BasicClasses/BackgroundAxis.h"
#include "ManagerBase.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentSection.h"
#include "SingleComponentManager.generated.h"



/**
 *
 */
UCLASS()
class DESIGNSTATION_API USingleComponentManager : public UManagerBase
{
	GENERATED_BODY()

protected:
	enum class ESingleComponentEditType : uint8
	{
		EView,
		ECrossSection,
		EEditOperation,
		ELoftRoutineEdit,
		ECutoutSectionEdit,
		EEditImportSection
	};

	ESingleComponentEditType EditType;
	FCrossSectionManager SectionManager;

	//Import section info
	FLocationProperty				SingleComponentLocation;
	FRotationProperty				SingleComponentRotation;
	FScaleProperty					SingleComponentScale;

	//
	FLoftRoutineManager LoftRoutineManager;

	//
	FCutoutSectionManager CutoutSectionManager;

	UPROPERTY()
	FSingleComponentProperty SingleComponentProperty;

	/*
	*  depend data
	*/
	UPROPERTY()
	FDependFileData CurEditDependData;

	/* 
	*  section data edit
	*/
	UPROPERTY()
	FSectionData EditSectionData;
	UPROPERTY()
	FExpressionValuePair EditSectionMaterialProperty;

	FSectionOperation TempSectionOperation;

	FSingleComponentItem	TempComponentItem;

	FSingleComponentTableData SingleComponentToEdit;
	UPROPERTY()
		FFolderTableData SingleCompFolderData;

	UPROPERTY()
		AShowSingleComponentActor* SingleComponentDisplayer;

	UPROPERTY()
		ABackgroundAxis* AxisActor;

	//ui
	UPROPERTY()
		USingleComponentUIManager* UIManager;

	int32 CurrentEditSection;

	int32 CurrentEditCurrentSection;

	FSectionOperationOrder CurrentSelectOperationIndex;

	UPROPERTY()
		UFbxConvertTask* FbxConvert;

	TSharedPtr<FSingleComponentItem> ImportSection;

	UPROPERTY()
	TArray<FSectionData> SectionDatas;

	//Fix bug CATALOG-1536
	bool bImageSelecting = false;

public:

	FOneInt32Delegate OnChangeCameraType;

public:
	USingleComponentManager();

	GenerateSet(FSingleComponentTableData, SingleComponentToEdit)
	GenerateSet(FFolderTableData, SingleCompFolderData)

	void CreateCrossSection(const EPlanPolygonBelongs& PlanType);

	virtual void WindowSizeChanged() override;

	virtual AShowSingleComponentActor* GetSingleShowActor();

#pragma region Net_Single_Comp

private:
	/*
	 *  @@ 网络相关
	 *	@@ TODO
	 */
	void BindDelegate();
	void UnBindDelegate();


	void UploadFileRequest(const FString& FileRelativePath);
	void UpdateDataRequest(const FRefDirectoryData& InData, bool UploadFile);
	void QueryModelOrMatDetailInfo(const FString& InFolderID);

	UFUNCTION()
	void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);
	UFUNCTION()
	void OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	UFUNCTION()
	void OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths);

	UFUNCTION()
	void OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData);

	UFUNCTION()
	void OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData);

	UFUNCTION()
	void OnNetSyncConflictResponseHandler(){}

	void AddDependMatFileData(const FCSModelMatData& InData);

	void ParseDependFiles();

private:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

	UPROPERTY()
	FFrontDirectoryNetUUID FrontNetUUID;

	UPROPERTY()
	bool NeedUploadFile;

	UPROPERTY()
	bool bExitAfterUpload = true;

#pragma endregion

protected:
	void InitUIManager();
	void InitManagerShow();

	void LoadLocalFile(bool UseDefault = false);

	virtual void InitializeManager() override;

	virtual void UninitializeManager() override;

	void UpdateAxis();

	UFUNCTION()
		void OnCreateCrossSectionHandler(int32 PlanType);

	UFUNCTION()
		void OnClickToolbarHandler(const int32& PlanType);

	UFUNCTION()
		void OnCrossSectionOperationSelectChangedHandler(const FSectionOperationOrder& OrderData);

	//Section property 
	UFUNCTION()
		void OnPropertyToolbarSectionPropertyChangedHandler(const int32& InEditType, const int32& InSectionId);

	UFUNCTION()
		void OnImportSectionStateChangedHandler(const EImportActionType& ActionType, const FString& InSavePath, const FString& InRefPath);

	UFUNCTION()
		void OnCrossSectionEditionHandler(const int32& WidgetOperationType, const int32& OperationId);

	//Import property

	UFUNCTION()
		void OnImportSectionLocationChangedHandler(const int32& InEditType, const FString& OutString);

	UFUNCTION()
		void OnImportSectionRotationChangedHandler(const int32& InEditType, const FString& OutString);

	UFUNCTION()
		void OnImportSectionScaleChangedHandler(const int32& InEditType, const FString& OutString);

	//Fbx import
	UFUNCTION()
		void OnFbxConvertProgressHandler(const float& Progress);

	UFUNCTION()
		void OnFbxConvertCompletedHandler(bool Success);

	//File property

	UFUNCTION()
		void OnSingleComponentFilePropertyChangedHandler(const int32& InEditTyp, const FString& InData);

	UFUNCTION()
		void OnSingleComponentOperationToolBarEditHandler(const int32& EditType);

	UFUNCTION()
		void OnCrossSectionPropertyChangedHandler(const int32& Type, FSectionData& Data);

	UFUNCTION()
		void OnCrossSectionPointPropertyChangedHandler(const int32& Type, FGeomtryPointProperty& Data);

	UFUNCTION()
		void OnCrossSectionLinePropertyChangedHandler(const int32& Type, FGeomtryLineProperty& Data);

	UFUNCTION()
		void OnCrossSectionRectanglePropertyChangedHandler(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleProperty);

	UFUNCTION()
		void OnCrossSectionEllipsePropertyChangedHandler(const int32& Type, const FGeomtryEllipsePlanProperty& Data);

	UFUNCTION()
		void OnCrossSectionCubePropertyChangedHandler(const int32& Type, const FGeomtryCubeProperty& Data);

	//Operation
	UFUNCTION()
		void OnCrossSectionDrawOperationPropertyChangedHandler(const int32& Type, const FSectionDrawOperation& Data);

	UFUNCTION()
		void OnCrossSectionShiftOperationPropertyChangedHandler(const int32& InType, const FSectionShiftingOperation& ShiftingData);

	UFUNCTION()
		void OnCrossSectionZoomOperationPropertyChangedHandler(const int32& InType, const FSectionZoomOperation& ZoomData);

	UFUNCTION()
		void OnCrossSectionCutoutOperationPropertyChangedHandler(const int32& InType, const FSectionCutOutOperation& CutoutData);

	UFUNCTION()
		void OnCrossSectionLoftRoutinePropertyChangedHandler(const int32& InType);

	//End operation 

	UFUNCTION()
		void OnSelectAnotherGeometryItemHandler(const int32& Type, const int32& Index, bool IsOver, bool OnProperty);

	UFUNCTION()
		void OnCrossSectionSelectionChangedHandler(const int32& Type, const int32& Index);

	bool RefreshSingleComponentActor();

	void ShowSingleComponentActor(bool ShowActor = true);

	void ShowSingleComponentPropertyPannel();

	void ShowCrossSectionPropertyPannel(const FCrossSectionData& InCrossSection);

	UFUNCTION()
		void OnSectionPropertyChangedHandler();

	//auto save
	UFUNCTION()
		bool SaveAction();

	//save load temp data
	UFUNCTION()
	void SaveVersion();
	UFUNCTION()
	void LoadVersion();

	UFUNCTION()
		void OnLoadVersionResponseHandler(const FString& RecoverFileMark);

	UFUNCTION()
		void ExitAction();

		void ExitToMain();

public:

	virtual void OnMouseMove() override;
	virtual void OnLeftMouseButtonClick(const FVector& InMousePosition) override;
	virtual void OnRightMouseButtonClick(const FVector& InMousePosition) override;
	virtual void OnKeyClicked(const FKey& InClickKey) override;
	virtual void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth) override;
	virtual void ShowAxis(bool IsShow);

	/*
	*  @@ proess
	*
	*/
	void StartProcess();
	void UpdateProcess(float InProcess);
	void EndProcess();

private:
	bool TempBool;

protected:
	void UpdateAndUploadImage(const FString& ImageRelativePath);

	UFUNCTION()
		void ShotScreenHandler();
	UFUNCTION()
		void TakePictureHandler(const int32& PictureType);
	UFUNCTION()
		void ProcessImageHandler();

	void SaveImportImage(const FString& SrcImage, bool IsNew);


private:
	UPROPERTY()
	int32		CurrentPictureType;

	UPROPERTY()
	bool		On2DSectionEdit;

	UPROPERTY()
	int32		SectionCameraType;

	UPROPERTY()
	FString		OpenImage;
};
