// Fill out your copyright notice in the Description page of Project Settings.

#include "MainUIManager.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/SubSystem/UIManagerSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/Widget_Login.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_FolderItem.h"
#include "DesignStation/UI/GeneralWidgets/StyleLayoutWidget.h"
#include "DesignStation/UI/MainUI/FolderAndFileListWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"

#pragma optimize("", off)

#define LOCTEXT_NAMESPACE "MainUI Manager"


extern const int MainUIZOrder = 5;
extern const int PopUIZOrder = 10;

extern const FString RootParentID = TEXT("-1");

void UMainUIManager::UpdateLoginInUI()
{
#ifdef USE_REF_LOCAL_FILE
	UCatalogNetworkSubsystem::GetInstance()->LoginResponseDelegate.AddUniqueDynamic(this, &UMainUIManager::OnUserLoginResponse);
#else
	ACatalogPlayerController::Get()->LoginResponseDelegate.AddUniqueDynamic(this, &UMainUIManager::OnUserLoginResponse);
#endif
	if (!IS_OBJECT_PTR_VALID(LoginUIWidget))
	{
		UE_LOG(LogTemp, Warning, TEXT("create login ui"));
		UClass* LoginUIBp = LoadClass<UUserWidget>(NULL, TEXT("WidgetBlueprint'/Game/UI/LoginUI.LoginUI_C'"));
		LoginUIWidget = CreateWidget<UWidget_Login>(GWorld.GetReference(), LoginUIBp);
		LoginUIWidget->AddToViewport(MainUIZOrder);
		LoginUIWidget->LoginDelegate.AddUniqueDynamic(this, &UMainUIManager::UserLogin);
		LoginUIWidget->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetLoginWidget(LoginUIWidget);
		ACatalogPlayerController* PC = ACatalogPlayerController::Get();
		FInputModeGameAndUI InputMode;
		PC->SetInputMode(InputMode);
	}
	if (IS_OBJECT_PTR_VALID(LoginUIWidget))
	{
		UpdateLastestUserInfoToUI();
	}
	FlipShowMainAndLoginUI(false);
}

void UMainUIManager::UpdateProperty(const FFolderTableData& FolderData, bool IsRequest)
{
	UE_LOG(LogTemp, Log, TEXT("update property ---- folder/file id : %s, need request : %d"), *FolderData.id, IsRequest);

#ifdef USE_REF_LOCAL_FILE

	GET_CACHE_REF_DATA(FolderData);
	TArray<FString> UpperDirectoryID = URefRelationFunction::GetUpperFolderDirectory(FolderData.backend_folder_path, false);
	TArray<FRefToLocalFileData> UpperRefDatas;
	UFolderWidget::Get()->GetCacheDataForFile(UpperDirectoryID, UpperRefDatas);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  HeritParameters;
	URefRelationFunction::GetTopLevelFolderParameterData(UpperRefDatas, HeritParameters);
	UpdatePropertyInner(HeritParameters, FolderData);

#else
	if (IsRequest)
	{
		MainParentParamDelegate.ExecuteIfBound(FolderData.id, FolderData.can_add_subfolder);
	}
	else
	{

		UpdateFolderAndFileProperty(FolderData);
	}
#endif
}

void UMainUIManager::UserLogin(const FString& UserID, const FString& PassWord)
{
	UE_LOG(LogTemp, Log, TEXT("login request : %s,%s"), *UserID, *PassWord);
	LastestUserInfo.UserName = UserID;
	LastestUserInfo.Password = PassWord;

#ifdef USE_REF_LOCAL_FILE
	LoginUUID = UCatalogNetworkSubsystem::GetInstance()->SendLoginRequest(UserID, PassWord);
#else
	ACatalogPlayerController* PC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (IS_OBJECT_PTR_VALID(PC))
	{
		LoginUUID = PC->SendLoginCheckRequest(UserID, PassWord);
	}
#endif
}

void UMainUIManager::OnUserLoginResponse(const FString& UUID, bool IsSuccess, const FUserInfoTableData& OutUserInfo)
{
	bool IsSucess = IsSuccess;
	if (LoginUUID != UUID)
	{
		UE_LOG(LogTemp, Log, TEXT("login failed, uuid wrong!"));
		IsSucess = false;
	}
	if (IsSucess)
	{
		UE_LOG(LogTemp, Log, TEXT("login succeed"));

		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
		UParameterDetailWidget::Get()->PreReadyParamTypeData();

		UCatalogNetworkSubsystem::GetInstance()->InitStyleCraft();

		//download style dat file
		UFolderWidget::Get()->DownloadStyle();
		//search place rule data
		UFolderWidget::Get()->InitPlaceRuleData();
		
		LastestUserInfo.RemeberMe = LoginUIWidget->IsRemeberChecked();
		if (!LoginUIWidget->IsRemeberChecked())
		{
			LastestUserInfo.UserName = TEXT("");
			LastestUserInfo.Password = TEXT("");
		}

		UUIFunctionLibrary::SaveUserInfo(LastestUserInfo);
		LoginUIWidget->SetVisibility(ESlateVisibility::Collapsed);
		UserInfoDelegate.ExecuteIfBound(OutUserInfo);

		FlipShowMainAndLoginUI(true);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("login failed"));
		if (IS_OBJECT_PTR_VALID(LoginUIWidget))
		{
			LoginUIWidget->UpdateLoginErrorState();
		}
	}
}

void UMainUIManager::ShowMainUI(bool InShow)
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetVisibility(InShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		/*if (InShow)
		{
			MainUILayout->AddFolderWidget(UFolderWidget::Get());
			MainUILayout->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			UFolderWidget::Get()->RemoveFromParent();
			MainUILayout->SetVisibility(ESlateVisibility::Collapsed);
		}*/
	}
}

void UMainUIManager::EnableAddFolderOrFile(bool IsEnable)
{
	if (IS_OBJECT_PTR_VALID(UFolderWidget::Get()))
	{
		UFolderWidget::Get()->IsEnableAddFolderOrFile(IsEnable, IsEnable);
	}
}

void UMainUIManager::UpdateFolderAndFileProperty(const FFolderTableData& FolderData)
{
	checkf(MainUILayout, TEXT("main ui layout is null"));
	if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		FolderAndFilePropertyUI = Cast<UFolderAndFilePropertyWidget>(UUIFunctionLibrary::CreatePropertyUI((int)EPropertyUIType::FolderAndFileProperty));
		MainUILayout->AddPropertyWidget(FolderAndFilePropertyUI);
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
	if (IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		MainUILayout->SetDetailPanelIsShow(true);
		//FFolderTableData FolderData = GetFolderDataFromDB(FolderId);
		if (FolderData.parent_id.Equals(RootParentID))
		{
			//选中根文件夹时清空父类数据
			FolderAndFilePropertyUI->UpdateParentsParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ());
		}
		FolderAndFilePropertyUI->FolderPropertyChangeDelegate.BindUFunction(this, FName(TEXT("UpdateSelectViewFolderData")));
		FolderAndFilePropertyUI->FolderParentParemDelegate.BindUFunction(this, FName(TEXT("OnFolderParentParameterHandler")));
		FolderAndFilePropertyUI->UpdateContent(FolderData);
		FolderAndFilePropertyUI->SetVisibility(ESlateVisibility::Visible);
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
}

void UMainUIManager::UpdateFolderParentParameter(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters)
{
	if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		FolderAndFilePropertyUI = Cast<UFolderAndFilePropertyWidget>(UUIFunctionLibrary::CreatePropertyUI((int)EPropertyUIType::FolderAndFileProperty));
		MainUILayout->AddPropertyWidget(FolderAndFilePropertyUI);
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
	if (IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		MainUILayout->SetDetailPanelIsShow(true);
		FolderAndFilePropertyUI->FolderPropertyChangeDelegate.BindUFunction(this, FName(TEXT("UpdateSelectViewFolderData")));
		FolderAndFilePropertyUI->FolderParentParemDelegate.BindUFunction(this, FName(TEXT("OnFolderParentParameterHandler")));
		FolderAndFilePropertyUI->UpdateParentsParameters(InParentParameters);
		bool NeedUpdateProperty = !CurrentSelectData.id.Equals(RootParentID);
		if (NeedUpdateProperty)
			FolderAndFilePropertyUI->UpdateContent(CurrentSelectData);
		FolderAndFilePropertyUI->SetVisibility(NeedUpdateProperty ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
}

void UMainUIManager::UpdatePropertyInner(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const FFolderTableData& FolderData)
{
	if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		FolderAndFilePropertyUI = Cast<UFolderAndFilePropertyWidget>(UUIFunctionLibrary::CreatePropertyUI(static_cast<int>(EPropertyUIType::FolderAndFileProperty)));
		MainUILayout->AddPropertyWidget(FolderAndFilePropertyUI);
		FolderAndFilePropertyUI->InitPlaceRuleComboBox(UFolderWidget::Get()->GetPlaceRuleCombineStr());
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
	if (IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		MainUILayout->SetDetailPanelIsShow(true);
		FolderAndFilePropertyUI->UpdateParentsParameters(InParentParameters);
		FolderAndFilePropertyUI->FolderPropertyChangeDelegate.BindUFunction(this, FName(TEXT("UpdateSelectViewFolderData")));
		FolderAndFilePropertyUI->FolderParentParemDelegate.BindUFunction(this, FName(TEXT("OnFolderParentParameterHandler")));
		FolderAndFilePropertyUI->UpdateContent(FolderData);
		FolderAndFilePropertyUI->SetVisibility(ESlateVisibility::Visible);
		FolderAndFilePropertyUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFilePropertyWidget(FolderAndFilePropertyUI);
	}
}

void UMainUIManager::UpdateStyleWidget()
{
	if (!IS_OBJECT_PTR_VALID(StyleLayoutUI))
	{
		StyleLayoutUI = UStyleLayoutWidget::Create();
		StyleLayoutUI->AddToViewport(8);
		StyleLayoutUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetStyleLayoutWidget(StyleLayoutUI);
	}
	if (IS_OBJECT_PTR_VALID(StyleLayoutUI))
	{
		StyleLayoutUI->ResetStyleSelectState();
		StyleLayoutUI->SetVisibility(ESlateVisibility::Visible);
		StyleLayoutUI->InitSubWidget();
		StyleLayoutUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetStyleLayoutWidget(StyleLayoutUI);
	}
}

void UMainUIManager::SyncSelectItem(const FFolderTableData& InFolderData)
{
	UFolderWidget::Get()->ToMainRefreshSelectFile(InFolderData);
}

void UMainUIManager::RefreshSceneViewportSize()
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->RefreshSceneViewportSize();
	}
}

void UMainUIManager::ResetViewport()
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->ResizeSceneViewport(FVector2D(0.0f, 0.0f), FVector2D(0.0f, 0.0f), FVector2D(0.0f, 0.0f));
	}
}

void UMainUIManager::JudgePropertyExpression()
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		if (MainUILayout->GetDetailPanelState() == ESlateVisibility::Visible)
		{
			FolderAndFilePropertyUI->RefreshFolderProperty();
		}
	}
}

void UMainUIManager::Clear()
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UMainUIManager::UnClear()
{
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetVisibility(ESlateVisibility::Visible);
	}
	/*if (IS_OBJECT_PTR_VALID(UFolderWidget::Get()))
	{
		UFolderWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}*/
}

void UMainUIManager::OpenFile(FString FolderId, int FolderType)
{
	UE_LOG(LogTemp, Log, TEXT("MainUIManager,the file folderId/folderType : %s,%d"), *FolderId, FolderType);
	//OpenFilesDelegate.ExecuteIfBound(FolderId, FolderType);
}

void UMainUIManager::UpdateLastestUserInfoToUI()
{
	/*FString ErrorMessage;
	FString LastestUserId;
	bool Result = URememberMeTableOperatorLibrary::GetLastestLoginUserID(LastestUserId, ErrorMessage);
	UE_LOG(LogTemp, Log, TEXT("lastest id,error message : %s,%s"), *LastestUserId, *ErrorMessage);
	if (Result && IS_OBJECT_PTR_VALID(LoginUIWidget))
	{
		UE_LOG(LogTemp, Log, TEXT("update user id"));
		LoginUIWidget->UpdateLastestUserID(LastestUserId);
		LoginUIWidget->ResetPasswordState();
	}*/
	FUserInfoRemebered LastestUserInfo = UUIFunctionLibrary::GetUserInfo();
	if (IS_OBJECT_PTR_VALID(LoginUIWidget))
	{
		LoginUIWidget->ResetPasswordState();
		LoginUIWidget->UpdateLastestUserID(LastestUserInfo.UserName, LastestUserInfo.Password, LastestUserInfo.RemeberMe);
	}
}

void UMainUIManager::ExpandFolderLayout(const FString& FloderItemId)
{
	if (!IS_OBJECT_PTR_VALID(UFolderWidget::Get()))
	{
		return;
	}

	TArray<FString> OutPaths;
	bool Res = UFolderTableOperatorLibrary::SelectFolderFilePath(FloderItemId, true, OutPaths);
	if (OutPaths.Num() > 0)
	{
		UFolderWidget::Get()->OpenFolderPathRecurse(OutPaths);
	}

	//if (!UFolderWidget::Get()->GetCurrentSelectItem())
	//{
	//	TArray<FString> OutPaths;
	//	bool Res = UFolderTableOperatorLibrary::SelectFolderFilePath(FloderItemId, true, OutPaths);
	//	if (OutPaths.Num() > 0)
	//	{
	//		UFolderWidget::Get()->OpenFolderPathRecurse(OutPaths);
	//	}
	//	return;
	//}
	////UWidget_FolderItem* NewSelectFolder = UFolderWidget::Get()->GetCurrentSelectItem()->ParentFolderWidget.Get()->GetChildFolders()[FloderItemId];
	////UWidget_FolderItem* NewSelectFolder = UFolderWidget::Get()->GetCurrentSelectItem()->GetChildFolders()[FloderItemId];
	//auto TempMap = UFolderWidget::Get()->GetCurrentSelectItem()->GetChildFolders();
	//if (TempMap.Contains(FloderItemId) && IS_OBJECT_PTR_VALID(TempMap[FloderItemId]))
	//{
	//	TempMap[FloderItemId]->SelectSelf();
	//}

}

void UMainUIManager::UpdatePersonalCenterWidget()
{
	if (!IS_OBJECT_PTR_VALID(PersonalCenterWidget))
	{
		PersonalCenterWidget = UPersonalCenterWidget::Create();
		PersonalCenterWidget->AddToViewport(PopUIZOrder);
		PersonalCenterWidget->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetPersonalCenterWidget(PersonalCenterWidget);
	}
	if (IS_OBJECT_PTR_VALID(PersonalCenterWidget))
	{
		PersonalCenterWidget->SetVisibility(ESlateVisibility::Visible);
		PersonalCenterWidget->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetPersonalCenterWidget(PersonalCenterWidget);
	}
}

void UMainUIManager::ClearMainActionState()
{
	if (UFolderWidget::Get())
	{
		UFolderWidget::Get()->ClearSelectWidget();
		UFolderWidget::Get()->RefreshFolderTreeLayout();
	}
	if (IS_OBJECT_PTR_VALID(FolderAndFileListUI))
	{
		FolderAndFileListUI->ClearListContent();
	}
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetDetailPanelIsShow(false);
		MainUILayout->SetNavigationBarShow(false);
	}
}

int32 UMainUIManager::GetToolBarActionTypeFromOrder(const TArray<FFolderTableData>& SubFolders, const int32& SelectOrder, const FString& SelectID)
{
	if (SubFolders.Num() <= 1)
	{
		return static_cast<int32>(EMainToolBarState::NoneEnable);
	}
	else
	{
		int32 SelectIndex = SubFolders.IndexOfByPredicate(
			[&SelectID](const FFolderTableData& Iter)->bool { return Iter.id.Equals(SelectID, ESearchCase::IgnoreCase); }
		);
		if(SelectIndex == INDEX_NONE)
			return static_cast<int32>(EMainToolBarState::NoneEnable);
		int32 UpIndex = (SelectIndex - 1);
		int32 DownIndex = (SelectIndex + 1);
		bool IsUpValid = SubFolders.IsValidIndex(UpIndex) && (SubFolders[UpIndex].can_add_subfolder == SubFolders[SelectIndex].can_add_subfolder);
		bool IsDownValid = SubFolders.IsValidIndex(DownIndex) && (SubFolders[DownIndex].can_add_subfolder == SubFolders[SelectIndex].can_add_subfolder);
		if (IsUpValid && IsDownValid)
		{
			return static_cast<int32>(EMainToolBarState::AllEnable);
		}
		else if (IsUpValid)
		{
			return static_cast<int32>(EMainToolBarState::UpEnable);
		}
		else if (IsDownValid)
		{
			return static_cast<int32>(EMainToolBarState::DownEnable);
		}
		else
		{
			return static_cast<int32>(EMainToolBarState::NoneEnable);
		}
		//ActionType = (InFolderOrder == OutFolders[0].folder_order) ? 1 : ((InFolderOrder == OutFolders.Last().folder_order) ? 0 : 2);
	}
}

void UMainUIManager::SelectViewItemEdit(const FFolderTableData& FolderData)
{
	UE_LOG(LogTemp, Log, TEXT("MainUIManager---select view folder id : %d, name : %s"), *FolderData.id, *FolderData.folder_name);
	ShowMainUI(true);
	CurrentSelectData = FolderData;
	StopRefreshList = true;
	//if (CurrentSelectData.parent_id.Equals(RootParentID))
	//{
	//	//选中根文件夹时清空父类数据
	//	if (IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	//		FolderAndFilePropertyUI->UpdateParentsParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ());
	//	UpdateProperty(CurrentSelectData, false);
	//	//return;
	//}
	//else if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	//{
	//	UpdateProperty(CurrentSelectData, true);
	//}
	//else
	//{
	//	if (CurrentSelectData.id != FolderAndFilePropertyUI->GetCurrentFolderId())
	//	{
	//		UpdateProperty(CurrentSelectData, true);
	//	}
	//	else
	//	{
	//		UpdateProperty(CurrentSelectData, false);
	//	}
	//}
	//UpdateFolderAndFileProperty(FolderData);
	UFolderWidget::Get()->SyncViewSelectFolderOrFile(FolderData.id, FolderData.can_add_subfolder);
}

void UMainUIManager::OpenViewItemEdit(const FFolderTableData& ViewItemData)
{
	UE_LOG(LogTemp, Log, TEXT("MainUIManager---open view item id : %s, name : %s"), *ViewItemData.id, *ViewItemData.folder_name);
	//FFolderTableData OpenFolderData = GetFolderDataFromDB(FolderID);
	if (ViewItemData.can_add_subfolder)
	{
		UE_LOG(LogTemp, Log, TEXT("MainUIManager---open folder"));
		//RefreshViewListContent(ViewItemData.id);
		//ExpandFolderLayout(ViewItemData.id);

		StopRefreshList = false;
		const TArray<FString> DirectoryArr = URefRelationFunction::GetUpperFolderDirectory(ViewItemData.backend_folder_path, true);
		if (DirectoryArr.Num() > 0)
		{
			UFolderWidget::Get()->OpenFolderPathRecurse(DirectoryArr);
		}
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("MainUIManager---open file"));
		OpenFilesDelegate.ExecuteIfBound(ViewItemData);
	}
}

void UMainUIManager::OnViewListRightActionHandler(const FFolderTableData& ViewItemData, const int32& ActionType)
{
	if (ActionType == static_cast<int32>(ERightMenuType::Delete))
	{
		if (!URefRelationFunction::DeleteLocalFile(ViewItemData))
		{
			UE_LOG(LogTemp, Error, TEXT("delete local file[uuid:%s][folderID:%s] failed!"), *ViewItemData.id, *ViewItemData.folder_id);
		}

		if (IS_OBJECT_PTR_VALID(MainUILayout))
		{
			MainUILayout->SetDetailPanelIsShow(false);
		}
		UpdateToolBarActionState(nullptr, 0, TEXT(""));
		SyncBreadNavigation(nullptr);
	}
	UFolderWidget::Get()->SyncViewListRightAction(ViewItemData, ActionType);
}

void UMainUIManager::OnClearSelect()
{
	if (MainUILayout)
	{
		MainUILayout->ClearPropertyWidget();
		MainUILayout->ClearFileListWidget();
		ToolBarUI->UpdateToolBarState(3);
	}
}

void UMainUIManager::SelectFolderEdit(UWidget_FolderItem* SelectFolder, bool IsFolderExpand)
{
	if (UFolderWidget::Get()->IsMultiSelect())
	{
		CurrentSelectData = FFolderTableData();
		OnClearSelect();
		return;
	}

	if (!URefRelationFunction::IsSameFolderDirectory(SelectFolder->GetItemData().backend_folder_path, CurrentSelectData.backend_folder_path))
	{
		StopRefreshList = false;
	}

	CurrentSelectData = SelectFolder->GetItemData();
	if(!SelectFolder->GetSubFolderSelectID().Equals(RootParentID))
	{
		CurrentSelectData = SelectFolder->GetSubFolderSelectData();
	}

	if (CurrentSelectData.parent_id.Equals(RootParentID))
	{

		UpdateProperty(CurrentSelectData, false);
		//return;
	}
	else if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		UpdateProperty(CurrentSelectData, true);
	}
	else
	{
		if (!CurrentSelectData.id.Equals(FolderAndFilePropertyUI->GetCurrentFolderId()))
		{
			UpdateProperty(CurrentSelectData, true);
		}
		else
		{
			UpdateProperty(CurrentSelectData, false);
		}
	}
	//UpdateFolderAndFileProperty(SelectFolder->GetItemData());
	if (IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI) && IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->AddPropertyWidget(FolderAndFilePropertyUI);
		MainUILayout->SetDetailPanelIsShow(true);
	}
	if (IsFolderExpand && !StopRefreshList)
	{
		FolderAndFileListUI->UpdateParentFolder(SelectFolder);
		//RefreshViewListContent(SelectFolder->GetItemData().id);
		FString SubSelectFolder = SelectFolder->GetSubFolderSelectID();
		RefreshViewListContent(SelectFolder, SubSelectFolder);
	}
	StopRefreshList = false;
	UpdateToolBarActionState(
		SelectFolder->ParentFolderWidget.Get(),
		SelectFolder->GetItemData().folder_order,
		SelectFolder->GetItemData().id
	);
	SyncBreadNavigation(SelectFolder);
}

void UMainUIManager::SyncBreadNavigation(UWidget_FolderItem* SelectFolder, UWidget_FileItem* SelectFile)
{
	if (IS_OBJECT_PTR_VALID(BreadNavigationUI))
	{
		BreadNavigationUI->UpdateContent(SelectFolder, SelectFile);
	}
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetNavigationBarShow(IS_OBJECT_PTR_VALID(SelectFolder));
	}
}

void UMainUIManager::OnBreadNavigationHandler(UWidget_FolderItem* SelectFolder)
{
	UFolderWidget::Get()->SyncBreadNavigation(SelectFolder);
}

void UMainUIManager::SelectFileEdit(UWidget_FileItem* SelectFile)
{
	if (UFolderWidget::Get()->IsMultiSelect())
	{
		CurrentSelectData = FFolderTableData();
		OnClearSelect();
		return;
	}

	CurrentSelectData = SelectFile->GetItemData();
	if (CurrentSelectData.parent_id.Equals(RootParentID))
	{
		UpdateProperty(CurrentSelectData, false);
	}
	else if (!IS_OBJECT_PTR_VALID(FolderAndFilePropertyUI))
	{
		UpdateProperty(CurrentSelectData, true);
	}
	else
	{
		if (CurrentSelectData.id != FolderAndFilePropertyUI->GetCurrentFolderId())
		{
			UpdateProperty(CurrentSelectData, true);
		}
		else
		{
			UpdateProperty(CurrentSelectData, false);
		}
	}
	//UpdateFolderAndFileProperty(SelectFile->GetItemData());
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->AddPropertyWidget(FolderAndFilePropertyUI);
		MainUILayout->SetDetailPanelIsShow(true);
	}
	if (!StopRefreshList)
	{
		FolderAndFileListUI->UpdateParentFolder(SelectFile->ParentFolderWidget.Get());
		//RefreshViewListContent(SelectFile->ParentFolderWidget.Get()->GetItemData().id, SelectFile->GetItemData().id);
		RefreshViewListContent(SelectFile->ParentFolderWidget.Get(), SelectFile->GetItemData().id);
	}
	StopRefreshList = false;

	if (IS_OBJECT_PTR_VALID(SelectFile))
	{
		SyncBreadNavigation(SelectFile->ParentFolderWidget.Get(), SelectFile);
	}
	UpdateToolBarActionState(
		SelectFile->ParentFolderWidget.Get(), 
		SelectFile->GetItemData().folder_order,
		SelectFile->GetItemData().id
	);
}

void UMainUIManager::FolderRightMenuActionEdit(const FString& FolderId, bool IsFolder, bool IsSelected, const int32& ActionType)
{
	UE_LOG(LogTemp, Log, TEXT("action id : %s, is folder : %d, is selected : %d"), *FolderId, IsFolder, IsSelected);
	if (IsSelected)
	{
		if (IS_OBJECT_PTR_VALID(MainUILayout))
		{
			MainUILayout->SetDetailPanelIsShow(false);
		}
	}
	/*if (FolderId != -1)
	{
		if (IS_OBJECT_PTR_VALID(FolderAndFileListUI))
		{
			FolderAndFileListUI->UpdateContent(-1);
		}
		if (IS_OBJECT_PTR_VALID(MainUILayout))
		{
			MainUILayout->SetDetailPanelIsShow(false);
		}
	}*/
	if (ActionType == static_cast<int32>(ERightMenuType::Delete))
	{
		if (IS_OBJECT_PTR_VALID(FolderAndFileListUI))
		{
			FolderAndFileListUI->DeleteViewItem(FolderId, IsFolder, IsSelected);
		}
		if (!IsSelected)
		{
			if (IS_OBJECT_PTR_VALID(UFolderWidget::Get()->GetCurrentSelectFile()))
			{
				UpdateToolBarActionState(
					UFolderWidget::Get()->GetCurrentSelectFile()->ParentFolderWidget.Get(),
					UFolderWidget::Get()->GetCurrentSelectFile()->GetItemData().folder_order,
					UFolderWidget::Get()->GetCurrentSelectFile()->GetItemData().id
				);
			}
			else if (IS_OBJECT_PTR_VALID(UFolderWidget::Get()->GetCurrentSelectItem()))
			{
				UpdateToolBarActionState(
					UFolderWidget::Get()->GetCurrentSelectItem()->ParentFolderWidget.Get(),
					UFolderWidget::Get()->GetCurrentSelectItem()->GetItemData().folder_order,
					UFolderWidget::Get()->GetCurrentSelectItem()->GetItemData().id
				);
			}
		}
		else
		{
			UpdateToolBarActionState(nullptr, 0, TEXT(""));
			SyncBreadNavigation(nullptr);
			//UFolderWidget::Get()->ClearSelectWidget();
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Copy))
	{
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Shear))
	{
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Paste))
	{
	}
}

void UMainUIManager::UpdateToolBarActionState(UWidget_FolderItem* InParent, const int32& InFolderOrder, const FString& InFolderID)
{
	//TArray<FFolderTableData> OutFolders;
	//FString ErrorMessage;
	if (!IS_OBJECT_PTR_VALID(ToolBarUI))
	{
		return;
	}
	if (!IS_OBJECT_PTR_VALID(InParent))
	{
		ToolBarUI->UpdateToolBarState(3);
		return;
	}
	TArray<FFolderTableData> OutFolders = InParent->GetSubFolderDatas();
	int32 ActionType = GetToolBarActionTypeFromOrder(OutFolders, InFolderOrder, InFolderID);
	/*if (OutFolders.Num() <= 1)
	{
		ActionType = 3;
	}
	else
	{
		ActionType = (InFolderOrder == OutFolders[0].folder_order) ? 1 : ((InFolderOrder == OutFolders.Last().folder_order) ? 0 : 2);
	}*/
	if (IS_OBJECT_PTR_VALID(ToolBarUI))
	{
		ToolBarUI->UpdateToolBarState(ActionType);
	}
	/*if (UFolderTableOperatorLibrary::SelectSubFolder(InParentId, OutFolders, ErrorMessage))
	{
		int32 ActionType;
		if (OutFolders.Num() <= 1)
		{
			ActionType = 3;
		}
		else
		{
			ActionType = (InFolderOrder == OutFolders[0].folder_order) ? 1 : ((InFolderOrder == OutFolders.Last().folder_order) ? 0 : 2);
		}
		if (IS_OBJECT_PTR_VALID(ToolBarUI))
		{
			ToolBarUI->UpdateToolBarState(ActionType);
		}
	}*/
}

void UMainUIManager::ToolBarEdit(const int32& EditType)
{
	switch (static_cast<EMainToolBarType>(EditType))
	{
	case EMainToolBarType::GlobalParams:
	{
		//UpdateGlobalParamsList();
		FParameterData DataTemp;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParamData;
		UParameterDetailWidget::Get()->UpdateContent(DataTemp, 0);
		UParameterDetailWidget::Get()->SetFolderOrFileParentParams(GlobalParamData);
		UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnGlobalParamAddHandler")));
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
		UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		break;
	}
	case EMainToolBarType::UserSetting:
	{
		UpdatePersonalCenterWidget();
		break;
	}
	case EMainToolBarType::Style:
	{
		UpdateStyleWidget();
		break;
	}
	case EMainToolBarType::Set:
	{
		break;
	}
	case EMainToolBarType::Exit:
	{
		bool Result = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
			, FText::FromStringTable(FName("PosSt"), TEXT("Make sure logout ?")).ToString());
		if (Result)
		{
			ClearMainActionState();
			MainToolBarDelegate.ExecuteIfBound(EditType);
		}
		break;
	}
	case EMainToolBarType::Up:
	{
		UFolderWidget::Get()->FolderOrFileUpDown(EFolderOrFileActionType::Up);
		break;
	}
	case EMainToolBarType::Down:
	{
		UFolderWidget::Get()->FolderOrFileUpDown(EFolderOrFileActionType::Down);
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void UMainUIManager::ToolBarSearch(const FString& InString)
{
	UFolderWidget::Get()->SearchFile(InString);
}

void UMainUIManager::OnToolBarSearchResetHandler()
{
	if (IS_OBJECT_PTR_VALID(ToolBarUI))
	{
		ToolBarUI->ResetSearchWidget();
	}
}

void UMainUIManager::OnGlobalParamAddHandler(const FParameterData& ParamData)
{
	UE_LOG(LogTemp, Log, TEXT("UMainUIManager::OnGlobalParamAddHandler ParamData=%s"), *ParamData.Data.name);
}

void UMainUIManager::UpdateSelectViewFolderData(const FString& FolderId, const FString& NewName, bool IsFolder, bool IsVisibility)
{
	if (IS_OBJECT_PTR_VALID(FolderAndFileListUI))
	{
		FolderAndFileListUI->SyncSelectViewItemName(FolderId, NewName, IsFolder, IsVisibility);
	}
	//RefreshViewListContent(FolderId);
	/*if (FolderAndFileListUI)
	{
		FolderAndFileListUI->UpdateCurrentViewWidget(InName, IsFolder);
	}*/
}

void UMainUIManager::OnFolderParentParameterHandler(const FString& FolderID, bool IsFolder)
{
	MainParentParamDelegate.ExecuteIfBound(FolderID, IsFolder);
}

void UMainUIManager::RefreshViewListContent(const FString& FolderId, const FString& SyncId)
{
	if (IS_OBJECT_PTR_VALID(FolderAndFileListUI) && IS_OBJECT_PTR_VALID(MainUILayout))
	{
		FolderAndFileListUI->UpdateContent(FolderId, SyncId);
		MainUILayout->AddFileListWidget(FolderAndFileListUI);
	}
}

void UMainUIManager::RefreshViewListContent(UWidget_FolderItem* SelectFolder, const FString& SyncId)
{
	if (IS_OBJECT_PTR_VALID(FolderAndFileListUI) && IS_OBJECT_PTR_VALID(MainUILayout))
	{
		FolderAndFileListUI->UpdateContent(SelectFolder, SyncId);
		MainUILayout->AddFileListWidget(FolderAndFileListUI);
	}
}

void UMainUIManager::FlipShowMainAndLoginUI(bool IsMainShow)
{
	if (IS_OBJECT_PTR_VALID(LoginUIWidget))
	{
		LoginUIWidget->SetVisibility(IsMainShow ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetVisibility(IsMainShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

void UMainUIManager::UpdateMainUI(const FUserInfoTableData& UserInfoData)
{
	UE_LOG(LogTemp, Log, TEXT("MainUIManager---update main ui"));
	if (!IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout = UMainLayoutWidget::Create();
		MainUILayout->AddToViewport(MainUIZOrder);
		MainUILayout->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetMainUILayoutWidget(MainUILayout);
	}
	if (IS_OBJECT_PTR_VALID(MainUILayout))
	{
		MainUILayout->SetVisibility(ESlateVisibility::Visible);
		MainUILayout->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetMainUILayoutWidget(MainUILayout);
	}

	if (!IS_OBJECT_PTR_VALID(ToolBarUI))
	{
		ToolBarUI = UMainToolBarWidget::Create();
		MainUILayout->AddToolBar(ToolBarUI);
		ToolBarUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetMainToolBarWidget(ToolBarUI);
	}
	if (IS_OBJECT_PTR_VALID(ToolBarUI))
	{
		ToolBarUI->MainToolBarEditDelegate.BindUFunction(this, FName(TEXT("ToolBarEdit")));
		ToolBarUI->MainSearchDelegate.BindUFunction(this, FName(TEXT("ToolBarSearch")));
		ToolBarUI->UpdateToolBarInfo(UserInfoData);
		ToolBarUI->SetVisibility(ESlateVisibility::Visible);
		ToolBarUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetMainToolBarWidget(ToolBarUI);
		//MainUILayout->AddToolBar(ToolBarUI);
	}

	if (!IS_OBJECT_PTR_VALID(BreadNavigationUI))
	{
		BreadNavigationUI = UBreadNavigationWidget::Create();
		BreadNavigationUI->UpdateContent(nullptr);
		MainUILayout->AddBreadNavigation(BreadNavigationUI);
		BreadNavigationUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetNavigationWidget(BreadNavigationUI);
	}
	if (IS_OBJECT_PTR_VALID(BreadNavigationUI))
	{
		BreadNavigationUI->BreadClickDelegate.BindUFunction(this, FName(TEXT("OnBreadNavigationHandler")));
		BreadNavigationUI->SetVisibility(ESlateVisibility::Visible);
		BreadNavigationUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetNavigationWidget(BreadNavigationUI);
	}

	{
		UFolderWidget* FolderLayoutUI = UFolderWidget::Get();
		MainUILayout->AddFolderWidget(FolderLayoutUI);
		FolderLayoutUI->SelectFolderDelegate.BindUFunction(this, FName("SelectFolderEdit"));
		FolderLayoutUI->SelectFileDelegate.BindUFunction(this, FName(TEXT("SelectFileEdit")));
		FolderLayoutUI->FolderRightClickActionDelegate.BindUFunction(this, FName(TEXT("FolderRightMenuActionEdit")));
		FolderLayoutUI->ToolBarResetDelegate.BindUFunction(this, FName(TEXT("OnToolBarSearchResetHandler")));
		FolderLayoutUI->ClearSelectDelegate.BindUFunction(this, FName(TEXT("OnClearSelect")));
		FolderLayoutUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderWidget(FolderLayoutUI);
	}

	if (!IS_OBJECT_PTR_VALID(FolderAndFileListUI))
	{
		FolderAndFileListUI = UUIFunctionLibrary::CreateFolderAndFileListUI();
		MainUILayout->AddFileListWidget(FolderAndFileListUI);
		FolderAndFileListUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFileListWidget(FolderAndFileListUI);
	}
	if (IS_OBJECT_PTR_VALID(FolderAndFileListUI))
	{
		FolderAndFileListUI->OpenFileDelegate.BindUFunction(this, FName(TEXT("OpenViewItemEdit")));
		FolderAndFileListUI->SelectFileDelegate.BindUFunction(this, FName(TEXT("SelectViewItemEdit")));
		FolderAndFileListUI->ViewListRightActionDelegate.BindUFunction(this, FName(TEXT("OnViewListRightActionHandler")));
		FolderAndFileListUI->SetVisibility(ESlateVisibility::Visible);
		FolderAndFileListUI->GetWorld()->GetGameInstance()->GetSubsystem<UUIManagerSubsystem>()->SetFolderAndFileListWidget(FolderAndFileListUI);
		/*FFolderTableData SelectedFolder;
		if (UFolderWidget::Get()->GetSelectedFolder(SelectedFolder))
		{
			FolderAndFileListUI->UpdateContent(SelectedFolder.id);
		}	*/
	}
}

#undef LOCTEXT_NAMESPACE

#pragma optimize("", on)
