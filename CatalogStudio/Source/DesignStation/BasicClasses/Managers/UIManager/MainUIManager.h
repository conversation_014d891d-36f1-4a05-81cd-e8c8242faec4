// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UIManagerBase.h"
#include "DesignStation/SaveAndLoad/RemeberUserName.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/UI/GeneralWidgets/StyleLayoutWidget.h"
#include "DesignStation/UI/MainUI/BreadNavigationWidget.h"
#include "DesignStation/UI/MainUI/FolderAndFilePropertyWidget.h"
#include "DesignStation/UI/MainUI/MainToolBarWidget.h"
#include "DesignStation/UI/Personal/PersonalCenterWidget.h"
#include "MainUIManager.generated.h"

/**
 *
 */

class UMainLayoutWidget;
class UWidget_Login;

DECLARE_DYNAMIC_DELEGATE_OneParam(FUserLoginInfoDelegate, const FUserInfoTableData&, UserInfo);
DECLARE_DYNAMIC_DELEGATE_OneParam(FOpenFilesDelegate, const FFolderTableData&, FolderData);

UCLASS()
class DESIGNSTATION_API UMainUIManager : public UUIManagerBase
{
	GENERATED_BODY()
public:
	void UpdateMainUI(const FUserInfoTableData& UserInfoData);
	void UpdateLoginInUI();
	void Clear();
	void UnClear();
	void ShowMainUI(bool InShow = true);
	void EnableAddFolderOrFile(bool IsEnable = true);
	//void UpdateFolderAndFileProperty(const int32 & FolderId);
	void UpdateFolderAndFileProperty(const FFolderTableData& FolderData);
	void UpdateFolderParentParameter(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters);
	void UpdatePropertyInner(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const FFolderTableData& FolderData);
	void UpdateStyleWidget();
	void SyncSelectItem(const FFolderTableData& InFolderData);
	void ResetViewport();
	void JudgePropertyExpression();

	virtual void RefreshSceneViewportSize() override;

private:

	UFUNCTION()
		void OnUserLoginResponse(const FString& UUID, bool IsSuccess, const FUserInfoTableData& OutUserInfo);

private:
	void UpdateProperty(const FFolderTableData& FolderData, bool IsRequest);
	UFUNCTION()
		void UserLogin(const FString& UserID, const FString& PassWord);

	UFUNCTION()
		void OpenFile(FString FolderId, int FolderType);
	UFUNCTION()
		void SelectFolderEdit(UWidget_FolderItem* SelectFolder, bool IsFolderExpand);
	void SyncBreadNavigation(UWidget_FolderItem* SelectFolder, UWidget_FileItem* SelectFile = nullptr);
	UFUNCTION()
		void OnBreadNavigationHandler(UWidget_FolderItem* SelectFolder);

	UFUNCTION()
		void SelectFileEdit(UWidget_FileItem* SelectFile);
	UFUNCTION()
		void FolderRightMenuActionEdit(const FString& FolderId, bool IsFolder, bool IsSelected, const int32& ActionType);

	void UpdateToolBarActionState(UWidget_FolderItem* InParent, const int32& InFolderOrder, const FString& InFolderID);
	UFUNCTION()
		void ToolBarEdit(const int32& EditType);
	UFUNCTION()
		void ToolBarSearch(const FString& InString);
	UFUNCTION()
		void OnToolBarSearchResetHandler();

	UFUNCTION()
		void OnGlobalParamAddHandler(const FParameterData& ParamData);

	UFUNCTION()
		void UpdateSelectViewFolderData(const FString& FolderId, const FString& NewName, bool IsFolder, bool IsVisibility);

	UFUNCTION()
		void OnFolderParentParameterHandler(const FString& FolderID, bool IsFolder);

	void RefreshViewListContent(const FString& FolderId, const FString& SyncId = TEXT("-1"));
	void RefreshViewListContent(UWidget_FolderItem* SelectFolder, const FString& SyncId = TEXT("-1"));

	void FlipShowMainAndLoginUI(bool IsMainShow);
	void UpdateLastestUserInfoToUI();
	void ExpandFolderLayout(const FString& FloderItemId);
	void UpdatePersonalCenterWidget();
	void ClearMainActionState();

	int32 GetToolBarActionTypeFromOrder(const TArray<FFolderTableData>& SubFolders, const int32& SelectOrder, const FString& SelectID);

protected:
	UFUNCTION()
		void SelectViewItemEdit(const FFolderTableData& FolderData);
	UFUNCTION()
		void OpenViewItemEdit(const FFolderTableData& ViewItemData);
	UFUNCTION()
		void OnViewListRightActionHandler(const FFolderTableData& ViewItemData, const int32& ActionType);

		UFUNCTION()
		void OnClearSelect();

private:
	FString LoginUUID;
	FString UpdateParamUUID;

	FUserInfoRemebered LastestUserInfo;
	bool StopRefreshList;

	UPROPERTY()
		FFolderTableData CurrentSelectData;

	UPROPERTY()
		UWidget_Login* LoginUIWidget;
	UPROPERTY()
		UMainLayoutWidget* MainUILayout;
	UPROPERTY()
		UMainToolBarWidget* ToolBarUI;
	UPROPERTY()
		UBreadNavigationWidget* BreadNavigationUI;
	UPROPERTY()
		UFolderAndFileListWidget* FolderAndFileListUI;
	UPROPERTY()
		UFolderAndFilePropertyWidget* FolderAndFilePropertyUI;
	UPROPERTY()
		UStyleLayoutWidget* StyleLayoutUI;
	/*UPROPERTY()
		UDesignStyleWidget* DesignStyleWidget;*/
	UPROPERTY()
		UPersonalCenterWidget* PersonalCenterWidget;

public:
	FUserLoginInfoDelegate UserInfoDelegate;
	FOpenFilesDelegate OpenFilesDelegate;
	FMainToolBarDelegate MainToolBarDelegate;
	FFolderParentParamDelegate MainParentParamDelegate;
};
