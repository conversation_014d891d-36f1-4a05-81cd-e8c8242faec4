// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentUIManager.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

extern const int PopUIZOrder;
extern const int MainUIZOrder;

void UMultiComponentUIManager::InitMultiComponentLayout()
{
	if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
	{
		MultiCompLayoutWidget->SetVisibility(ESlateVisibility::Visible);
	}
	UpdateCompLayout();
	UpdateToolBarWidget();
	//UpdatePartListWidget(InData.ComponentItems);
	//UpdateCompProperty();
}

//void UMultiComponentUIManager::UpdateContent(const FMultiComponentProperty& InData, const int32& SelectTreeItemID)
//{
//	MultiComponentData = InData;
//	ComponentTreeSelectId = SelectTreeItemID;
//	/*UpdateCompLayout();
//	UpdateToolBarWidget();*/
//	//UpdatePartListWidget(InData.ComponentItems);
//	UpdateComponentTreeWidget(InData.ComponentItems, SelectTreeItemID);
//	if (SelectTreeItemID == -1)
//	{
//		UpdateCompProperty();
//	}
//	else
//	{
//		UpdateCompProperty(InData.ComponentItems[SelectTreeItemID]);
//	}
//}

void UMultiComponentUIManager::UpdateContent(const FMultiComponentData & InData, const int32 & SelectTreeItemID)
{
	MultiComponentData = InData;
	ComponentTreeSelectId = SelectTreeItemID;
	UpdateComponentTreeWidget(InData.ComponentItems, SelectTreeItemID);
	if (SelectTreeItemID == -1)
	{
		UpdateCompProperty();
	}
	else
	{
		UpdateCompProperty(InData.ComponentItems[SelectTreeItemID]);
	}
}

void UMultiComponentUIManager::UpdateCompProperty(const FMultiComponentDataItem & ItemData)
{
	if (!IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPropertyWidget>();
		MultiCompPropertyWidget->SetParentParameters(ParentParameters);
		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
		{
			MultiCompLayoutWidget->AddPropertyWidget(MultiCompPropertyWidget);
		}
	}
	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget->UpdateContent(ItemData);
		MultiCompPropertyWidget->MultiCompPropertyEditDelegate.BindUFunction(this, FName(TEXT("MultiCompPropertyEdit")));
		MultiCompPropertyWidget->MultiComponentParamEditDelegate.BindUFunction(this, FName(TEXT("MultiComponentParamsEdit")));
		MultiCompPropertyWidget->MultiComponentParamAddDelegate.BindUFunction(this, FName(TEXT("MultiComponentParamsAddEdit")));
		MultiCompPropertyWidget->MultiCompLocationDelegate.BindUFunction(this, FName(TEXT("MultiComponentLocationEdit")));
		MultiCompPropertyWidget->MultiCompRotationDelegate.BindUFunction(this, FName(TEXT("MultiComponentRotationEdit")));
		MultiCompPropertyWidget->MultiCompScaleDelegate.BindUFunction(this, FName(TEXT("MultiComponentScaleEdit")));
		MultiCompPropertyWidget->MultiCompParentParametersDelegate.BindUFunction(this, FName(TEXT("OnGetParentParameterHandler")));
		MultiCompPropertyWidget->BtnUpDownClickDelegate.BindUObject(this, &UMultiComponentUIManager::OnClickBtnUPDown);
		//MultiCompPropertyWidget->MultiComponentItemParamEditDelegate.BindUFunction(this, FName(TEXT("MultiComponentItemParamEdit")));
		MultiCompPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
		{
			MultiCompLayoutWidget->SetDetailPanelIsShow(true);
		}
	}
}

void UMultiComponentUIManager::UpdateCompProperty()
{
	if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
	{
		MultiCompLayoutWidget->SetDetailPanelIsShow(false);
	}
}

void UMultiComponentUIManager::UpdateAddParamWidget(const TArray<FParameterData>& InData)
{
	/*if (!IS_OBJECT_PTR_VALID(AddParamWidget))
	{
		AddParamWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompAddParamWidget>();
		AddParamWidget->AddToViewport(PopUIZOrder);
	}
	if (IS_OBJECT_PTR_VALID(AddParamWidget))
	{
		AddParamWidget->UpdateContent(InData);
		AddParamWidget->SearchParamEditDelegate.BindUFunction(this, FName(TEXT("SearchParamEdit")));
		AddParamWidget->AddParamEditDelegate.BindUFunction(this, FName(TEXT("AddParamEdit")));
		AddParamWidget->SetVisibility(ESlateVisibility::Visible);
	}*/
}

void UMultiComponentUIManager::SetMultiComponentParentParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameter)
{
	ParentParameters = InParentParameter;
}

void UMultiComponentUIManager::SyncTreeComponentWidget(const FMultiComponentDataItem & InData)
{
	if (IS_OBJECT_PTR_VALID(UMultiComponentTreeWidget::Get()))
	{
		UMultiComponentTreeWidget::Get()->SyncWidgetForMultiComponent(InData);
	}
}

void UMultiComponentUIManager::SyncTreeComponentID(const FString& InNewName)
{
	if (IS_OBJECT_PTR_VALID(UMultiComponentTreeWidget::Get()))
	{
		UMultiComponentTreeWidget::Get()->SyncSelectItem(InNewName);
	}
}

void UMultiComponentUIManager::Clear()
{
	if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
	{
		MultiCompLayoutWidget->SetVisibility(ESlateVisibility::Collapsed);
		MultiCompLayoutWidget->RemoveFromParent();
		MultiCompLayoutWidget = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
	{
		MultiCompToolBarWidget->SetVisibility(ESlateVisibility::Collapsed);
		MultiCompToolBarWidget->RemoveFromParent();
		MultiCompToolBarWidget = nullptr;
	}
	/*if (IS_OBJECT_PTR_VALID(MultiCompPartListWidget))
	{
		MultiCompPartListWidget->SetVisibility(ESlateVisibility::Collapsed);
		MultiCompPartListWidget->RemoveFromParent();
		MultiCompPartListWidget = nullptr;
	}*/
	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget->SetVisibility(ESlateVisibility::Collapsed);
		MultiCompPropertyWidget->RemoveFromParent();
		MultiCompPropertyWidget = nullptr;
	}
	/*if (IS_OBJECT_PTR_VALID(FolderPropertyWidget))
	{
		FolderPropertyWidget->SetVisibility(ESlateVisibility::Collapsed);
		FolderPropertyWidget->RemoveFromParent();
		FolderPropertyWidget = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(AddParamWidget))
	{
		AddParamWidget->SetVisibility(ESlateVisibility::Collapsed);
		AddParamWidget->RemoveFromParent();
		AddParamWidget = nullptr;
	}*/
}

void UMultiComponentUIManager::ResetState()
{
	if (IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
	{
		MultiCompToolBarWidget->SetUnSelectButton(-1);
	}
	UMultiComponentTreeWidget::Get()->UpdateTreeActionButtonState(false);
}

void UMultiComponentUIManager::RefreshSceneViewportSize()
{
	if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
	{
		MultiCompLayoutWidget->RefreshSceneViewportSize();
	}
}

void UMultiComponentUIManager::SetSelectComponentByHit(int32 HitComponentIndex)
{
	if (IS_OBJECT_PTR_VALID(UMultiComponentTreeWidget::Get()))
	{
		UMultiComponentTreeWidget::Get()->SetItemSelectedByIndex(HitComponentIndex);
	}
}

void UMultiComponentUIManager::SelectParameterUI(const FParameterData& InParamData)
{
	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget->SelectParameterUI(InParamData);
	}
}

void UMultiComponentUIManager::SwapParameterUI(const FParameterData& InParamA, const FParameterData& InParamB)
{
	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget->SwapParameterUI(InParamA, InParamB);
	}
}

void UMultiComponentUIManager::UpdateCompLayout()
{
	if (!IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
	{
		MultiCompLayoutWidget = UUIFunctionLibrary::CreateUIWidget<UMultiComponentLayoutWidget>();
		MultiCompLayoutWidget->AddToViewport(MainUIZOrder);
		MultiCompLayoutWidget->PictureTypeDelegate.BindUFunction(this, FName(TEXT("MultiComponentPictureType")));
	}
}

void UMultiComponentUIManager::UpdateToolBarWidget()
{
	if (!IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
	{
		MultiCompToolBarWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompToolBarWidget>();
		if (IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
		{
			MultiCompLayoutWidget->AddToolBar(MultiCompToolBarWidget);
		}
	}
	if (IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
	{
		MultiCompToolBarWidget->MultiCompToolBarEditDelegate.BindUFunction(this, FName(TEXT("MultiCompToolBarEdit")));
		MultiCompToolBarWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMultiComponentUIManager::UpdateComponentTreeWidget(const TArray<FMultiComponentDataItem>& InTreeItems, const int32& SelectTreeItemID)
{
	/*if (!IS_OBJECT_PTR_VALID(MultiComponentTreeWidget))
	{
		MultiComponentTreeWidget = UMultiComponentTreeWidget::Create();
		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
		{
			MultiCompLayoutWidget->AddFolderWidget(MultiComponentTreeWidget);
		}
	}*/
	if (IS_OBJECT_PTR_VALID(UMultiComponentTreeWidget::Get()))
	{
		UMultiComponentTreeWidget::Get()->UpdateContent(InTreeItems, SelectTreeItemID);
		UMultiComponentTreeWidget::Get()->TreeActionDelegate.BindUFunction(this, FName(TEXT("OnComponentTreeActionHandler")));
		UMultiComponentTreeWidget::Get()->TreeSelectDelegate.BindUFunction(this, FName(TEXT("OnToolBarButtonHander")));
		UMultiComponentTreeWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
		{
			MultiCompLayoutWidget->AddFolderWidget(UMultiComponentTreeWidget::Get());
		}
	}
}

//void UMultiComponentUIManager::UpdatePartListWidget(const TArray<FMultiComponentPropertyItem>& PartItems)
//{
//	/*if (!IS_OBJECT_PTR_VALID(MultiCompPartListWidget))
//	{
//		MultiCompPartListWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPartListWidget>();
//		if (IS_OBJECT_PTR_VALID(MultiCompPartListWidget))
//		{
//			MultiCompLayoutWidget->AddPropertyWidget(MultiCompPartListWidget);
//		}
//	}
//	if (IS_OBJECT_PTR_VALID(MultiCompPartListWidget))
//	{
//		MultiCompPartListWidget->UpdateContent(PartItems);
//		MultiCompPartListWidget->MultiCompPartListEditDelegate.BindUFunction(this, FName(TEXT("MultiCompPartListEdit")));
//		MultiCompPartListWidget->SetVisibility(ESlateVisibility::Visible);
//	}*/
//}

//void UMultiComponentUIManager::UpdateFolderPropertyWidget(const FFolderTableData& FolderData)
//{
//	//if (!IS_OBJECT_PTR_VALID(FolderPropertyWidget))
//	//{
//	//	FolderPropertyWidget = Cast<UFolderAndFilePropertyWidget>(UUIFunctionLibrary::CreatePropertyUI((int)EPropertyUIType::FolderAndFileProperty));
//	//}
//	//if (IS_OBJECT_PTR_VALID(FolderPropertyWidget))
//	//{
//	//	FolderPropertyWidget->UpdateContent(FolderData);
//	//	FolderPropertyWidget->SetVisibility(ESlateVisibility::Visible);
//	//	if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
//	//	{
//	//		//MultiCompLayoutWidget->AddSingleComponentPropertyWidget(FolderPropertyWidget);
//	//	}
//	//}
//}

//void UMultiComponentUIManager::UpdateCompProperty(const FMultiComponentPropertyItem& ItemData)
//{
//	if (!IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
//	{
//		MultiCompPropertyWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPropertyWidget>();
//		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
//		{
//			MultiCompLayoutWidget->AddPropertyWidget(MultiCompPropertyWidget);
//		}
//	}
//	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
//	{
//		MultiCompPropertyWidget->UpdateContent(ItemData);
//		MultiCompPropertyWidget->MultiCompPropertyEditDelegate.BindUFunction(this, FName(TEXT("MultiCompPropertyEdit")));
//		MultiCompPropertyWidget->MultiComponentParamEditDelegate.BindUFunction(this, FName(TEXT("MultiComponentParamsEdit")));
//		MultiCompPropertyWidget->MultiCompLocationDelegate.BindUFunction(this, FName(TEXT("MultiComponentLocationEdit")));
//		MultiCompPropertyWidget->MultiCompRotationDelegate.BindUFunction(this, FName(TEXT("MultiComponentRotationEdit")));
//		MultiCompPropertyWidget->MultiCompScaleDelegate.BindUFunction(this, FName(TEXT("MultiComponentScaleEdit")));
//		MultiCompPropertyWidget->MultiCompParentParametersDelegate.BindUFunction(this, FName(TEXT("OnGetParentParameterHandler")));
//		//MultiCompPropertyWidget->MultiComponentItemParamEditDelegate.BindUFunction(this, FName(TEXT("MultiComponentItemParamEdit")));
//		MultiCompPropertyWidget->SetVisibility(ESlateVisibility::Visible);
//		if (IS_OBJECT_PTR_VALID(MultiCompLayoutWidget))
//		{
//			MultiCompLayoutWidget->SetDetailPanelIsShow(true);
//		}
//	}
//}

FMultiComponentDataItem UMultiComponentUIManager::FindSelectItem(const int32 & ItemId)
{
	for (auto& ItemData : MultiComponentData.ComponentItems)
	{
		if (ItemData.ID == ItemId)
		{
			return ItemData;
		}
	}
	return FMultiComponentDataItem();
}

//void UMultiComponentUIManager::FolderLayoutSelectEdit(UWidget_FolderItem * SelectFolder)
//{
//	UpdateFolderPropertyWidget(SelectFolder->GetItemData());
//}

void UMultiComponentUIManager::MultiCompToolBarEdit(const EMultiCompToolBarType & EditType)
{
	if (/*EditType == EMultiCompToolBarType::Save || */EditType == EMultiCompToolBarType::Exit)
	{
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Do you want to exit multi component edit?")).ToString()))
		{
			UMultiComponentTreeWidget::Get()->ClearData();
			MultiComponentToolBarDelegate.ExecuteIfBound(EditType);
		}	
	}
	else if (EditType == EMultiCompToolBarType::Up && ComponentTreeSelectId != -1)
	{
		MultiComponentTreeActionDelegate.ExecuteIfBound(static_cast<int32>(EMultiComponentTreeEditType::Up), ComponentTreeSelectId);
	}
	else if (EditType == EMultiCompToolBarType::Down && ComponentTreeSelectId != -1)
	{
		MultiComponentTreeActionDelegate.ExecuteIfBound(static_cast<int32>(EMultiComponentTreeEditType::Down), ComponentTreeSelectId);
	}
	else if (EditType == EMultiCompToolBarType::Recover)
	{
		MultiComponentToolBarDelegate.ExecuteIfBound(EditType);
	}
}

void UMultiComponentUIManager::OnComponentTreeActionHandler(const int32 & ActionType, const int32 & ActionID)
{
	ComponentTreeSelectId = ActionID;
	MultiComponentTreeActionDelegate.ExecuteIfBound(ActionType, ComponentTreeSelectId);
}

//void UMultiComponentUIManager::MultiCompPartListEdit(const EMultiCompPartListType& EditType, const int32& PartId)
//{
//	MultiCompPartListDelegate.ExecuteIfBound(EditType, PartId);
//}

void UMultiComponentUIManager::MultiCompPropertyEdit(const EComponentIDType& EditType, const FString& InString)
{
	/*if (IS_OBJECT_PTR_VALID(MultiCompPartListWidget) && EditType == EComponentIDType::Name)
	{
		MultiCompPartListWidget->UpdateSelectName(InString);
	}*/
	MultiComponentPropertyDelegate.ExecuteIfBound(EditType, InString);
}

void UMultiComponentUIManager::MultiComponentItemParamEdit(const EMultiComponentParamType & EditType, const FParameterData & ParamData)
{
	//MultiComponentItemParamDelegate.ExecuteIfBound(EditType, ParamData);
}

void UMultiComponentUIManager::MultiComponentParamsEdit(const EParameterType & EditType, const FParameterData & ParamData)
{
	MultiComponentParamDelegate.ExecuteIfBound(EditType, ParamData);
}

void UMultiComponentUIManager::MultiComponentParamsAddEdit(const EParameterType& EditType, const TArray<FParameterData>& ParamData)
{
	MultiComponentParamAddDelegate.ExecuteIfBound(EditType, ParamData);
}

void UMultiComponentUIManager::MultiComponentLocationEdit(const EMultiCompPosType & EditType, const FLocationProperty & LocationData, const int32& InEditIndex)
{
	MultiComponentLocationDelegate.ExecuteIfBound(EditType, LocationData, InEditIndex);
}

void UMultiComponentUIManager::MultiComponentRotationEdit(const EMultiCompPosType & EditType, const FRotationProperty & RotationData, const int32& InEditIndex)
{
	MultiComponentRotationDelegate.ExecuteIfBound(EditType, RotationData, InEditIndex);
}

void UMultiComponentUIManager::MultiComponentScaleEdit(const EMultiCompPosType & EditType, const FScaleProperty & ScaleData, const int32& InEditIndex)
{
	MultiComponentScaleDelegate.ExecuteIfBound(EditType, ScaleData, InEditIndex);
}

void UMultiComponentUIManager::OnGetParentParameterHandler()
{
	if (IS_OBJECT_PTR_VALID(MultiCompPropertyWidget))
	{
		MultiCompPropertyWidget->SetParentParameters(ParentParameters);
	}
}

void UMultiComponentUIManager::OnToolBarButtonHander(const int32 & state)
{
	if (IS_OBJECT_PTR_VALID(MultiCompToolBarWidget))
	{
		MultiCompToolBarWidget->SetUnSelectButton(state);
	}
}

void UMultiComponentUIManager::MultiComponentPictureType(const int32 & Type)
{
	MultiComponentPictureTypeDelegate.ExecuteIfBound(Type);
}

void UMultiComponentUIManager::OnClickBtnUPDown(const FParameterData& InData, bool bUp)
{
	BtnUpDownClickDelegate.ExecuteIfBound(InData, bUp);
}

void UMultiComponentUIManager::SetCameraImage(const FString & ImagePath, bool IsShow)
{

	if (MultiCompLayoutWidget)
	{
			MultiCompLayoutWidget->SetCameraImage(ImagePath,IsShow);
	}
}



//void UMultiComponentUIManager::AddParamEdit(const FParameterData & ParamData)
//{
//	MultiComponentParamDelegate.ExecuteIfBound(EParameterType::Add, ParamData);
//}
//
//void UMultiComponentUIManager::SearchParamEdit(const FString & ParamName)
//{
//	MultiComponentSearchParamDelegate.ExecuteIfBound(ParamName);
//}

