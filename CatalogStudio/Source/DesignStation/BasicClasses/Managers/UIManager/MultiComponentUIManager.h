// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UIManagerBase.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "DesignStation/UI/GeneralWidgets/MultiComponentLayoutWidget.h"
#include "DesignStation/UI/MultiComponentUI/MultiComponentTreeWidget.h"
#include "DesignStation/UI/MultiComponentUI/MultiCompPropertyWidget.h"
#include "DesignStation/UI/MultiComponentUI/MultiCompToolBarWidget.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "MultiComponentUIManager.generated.h"

/**
 *
 */

class UWidget_FolderItem;
class UFolderAndFilePropertyWidget;

UENUM(BlueprintType)
enum class EMultiComponentTreeEditType : uint8
{
	Add = 0,
	Copy,
	Delete,
	Select,
	Up,
	Down
};

UCLASS()
class DESIGNSTATION_API UMultiComponentUIManager : public UUIManagerBase
{
	GENERATED_BODY()

public:
	void InitMultiComponentLayout();
	//void UpdateContent(const FMultiComponentProperty& InData, const int32& SelectTreeItemID = -1);
	void UpdateContent(const FMultiComponentData& InData, const int32& SelectTreeItemID = -1);
	//void UpdateCompProperty(const FMultiComponentPropertyItem& ItemData);
	void UpdateCompProperty(const FMultiComponentDataItem& ItemData);
	void UpdateCompProperty(); //clear property panel
	void UpdateAddParamWidget(const TArray<FParameterData>& InData);
	void SetMultiComponentParentParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameter);

	void SyncTreeComponentWidget(const FMultiComponentDataItem& InData);
	void SyncTreeComponentID(const FString& InNewName);

	void Clear();
	void ResetState();

	virtual void RefreshSceneViewportSize() override;


	void SetSelectComponentByHit(int32 HitComponentIndex);
	void SelectParameterUI(const FParameterData& InParamData);
	void SwapParameterUI(const FParameterData& InParamA, const FParameterData& InParamB);
private:
	void UpdateCompLayout();
	void UpdateToolBarWidget();
	void UpdateComponentTreeWidget(const TArray<FMultiComponentDataItem>& InTreeItems, const int32& SelectTreeItemID);
	//void UpdatePartListWidget(const TArray<FMultiComponentPropertyItem>& PartItems);
	//void UpdateFolderPropertyWidget(const FFolderTableData& FolderData);

	FMultiComponentDataItem FindSelectItem(const int32& ItemId);
	/*UFUNCTION()
		void FolderLayoutSelectEdit(UWidget_FolderItem* SelectFolder);*/

	UFUNCTION()
		void MultiCompToolBarEdit(const EMultiCompToolBarType& EditType);
	/*UFUNCTION()
		void MultiCompPartListEdit(const EMultiCompPartListType& EditType, const int32& PartId);*/

	UFUNCTION()
		void OnComponentTreeActionHandler(const int32& ActionType, const int32& ActionID);

	UFUNCTION()
		void MultiCompPropertyEdit(const EComponentIDType& EditType, const FString& InString);
	UFUNCTION()
		void MultiComponentItemParamEdit(const EMultiComponentParamType& EditType, const FParameterData& ParamData);
	UFUNCTION()
		void MultiComponentParamsEdit(const EParameterType& EditType, const FParameterData& ParamData);
		UFUNCTION()
		void MultiComponentParamsAddEdit(const EParameterType& EditType, const TArray<FParameterData>& ParamData);
	UFUNCTION()
		void MultiComponentLocationEdit(const EMultiCompPosType& EditType, const FLocationProperty& LocationData, const int32& InEditIndex);
	UFUNCTION()
		void MultiComponentRotationEdit(const EMultiCompPosType& EditType, const FRotationProperty& RotationData, const int32& InEditIndex);
	UFUNCTION()
		void MultiComponentScaleEdit(const EMultiCompPosType& EditType, const FScaleProperty& ScaleData, const int32& InEditIndex);
	UFUNCTION()
		void OnGetParentParameterHandler();

	/*UFUNCTION()
		void AddParamEdit(const FParameterData& ParamData);
	UFUNCTION()
		void SearchParamEdit(const FString& ParamName);*/
	UFUNCTION()
		void OnToolBarButtonHander(const int32& State);
	UFUNCTION()
		void MultiComponentPictureType(const int32& Type);

		UFUNCTION()
		void OnClickBtnUPDown(const FParameterData& InData, bool bUp);
public:
	FMultiCompToolBarEditDelegate MultiComponentToolBarDelegate;
	//FMultiCompPartListEditDelegate MultiCompPartListDelegate;

	FMultiCompTreeActionDelegate MultiComponentTreeActionDelegate;

	FMultiComponentParamDelegate MultiComponentParamDelegate;
	FMultiComponentParamAddDelegate MultiComponentParamAddDelegate;
	FMultiCompPropertyEditDelegate MultiComponentPropertyDelegate;
	FMultiCompLocationEditDelegate MultiComponentLocationDelegate;
	FMultiCompRotationEditDelegate MultiComponentRotationDelegate;
	FMultiCompScaleEditDelegate MultiComponentScaleDelegate;
	FCameraBtnTypeDelegate	MultiComponentPictureTypeDelegate;
	FMultiParamsUpDownDelegate BtnUpDownClickDelegate;
	//FSearchParamDelegate MultiComponentSearchParamDelegate;

private:
	/*UPROPERTY()
		FMultiComponentProperty MultiComponentData;*/
	FMultiComponentData MultiComponentData;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;

	int32 ComponentTreeSelectId;

private:
	UPROPERTY()
		UMultiComponentLayoutWidget* MultiCompLayoutWidget;
	UPROPERTY()
		UMultiCompToolBarWidget* MultiCompToolBarWidget;
	/*UPROPERTY()
		UMultiComponentTreeWidget* MultiComponentTreeWidget;*/
		/*UPROPERTY()
			UMultiCompPartListWidget* MultiCompPartListWidget;*/
	UPROPERTY()
		UMultiCompPropertyWidget* MultiCompPropertyWidget;
	/*UPROPERTY()
		UFolderAndFilePropertyWidget* FolderPropertyWidget;
	UPROPERTY()
		UMultiCompAddParamWidget* AddParamWidget;*/

public:
	void SetCameraImage(const FString& ImagePath, bool IsShow);
};
