// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentUIManager.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

extern const int PopUIZOrder;
extern const int MainUIZOrder;

#define LOCTEXT_NAMESPACE "SingleComponentUIManagr"

void USingleComponentUIManager::UpdateSingleComponentUI()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---update single component ui"));
	if (!IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		//EditLayoutWidget = Cast<UEditorLayoutWidget>(UUIFunctionLibrary::CreateLayoutUI((int)EUILayoutType::EditUILayout));
		EditLayoutWidget = UEditorLayoutWidget::Create();
		EditLayoutWidget->AddToViewport(MainUIZOrder);
		EditLayoutWidget->PictureTypeDelegate.BindUFunction(this, FName(TEXT("SingleComponentPictureType")));

	}
	if (!IS_OBJECT_PTR_VALID(SingleCompToolBarWidget))
	{
		SingleCompToolBarWidget = Cast<USingleComponentToolBar>(UUIFunctionLibrary::CreateToolBarUI((int)EToolBarType::SingleComponentToolBar));
		SingleCompToolBarWidget->SingleComponentEditDelegate.BindUFunction(this, FName(TEXT("ToolBarEdit")));
		EditLayoutWidget->AddToolBar(SingleCompToolBarWidget);
	}
	/*if (IS_OBJECT_PTR_VALID(UFolderWidget::Get()))
	{
		UFolderWidget::Get()->SelectFolderDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
		EditLayoutWidget->AddFolderWidget(UFolderWidget::Get());
	}*/
	//UpdateSingleComponentProperty();
	if (IS_OBJECT_PTR_VALID(EditLayoutWidget) && IS_OBJECT_PTR_VALID(SingleCompToolBarWidget))
	{
		EditLayoutWidget->SetVisibility(ESlateVisibility::Visible);
	}
}


void USingleComponentUIManager::UpdateSingleComponentProperty(const TArray<FSectionData>& SectionDatas)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---update single component property ui"));
	if (!IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget))
	{
		SingleComponentPropertyWidget = Cast<USingleComponentPropertyWidget>(UUIFunctionLibrary::CreatePropertyUI((int)EPropertyUIType::SingleComponentProperty));
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{

		EditLayoutWidget->AddPropertyWidget(SingleComponentPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SingleComponentPropertyWidget->SingleComponentPropertyDelegate.BindUFunction(this, FName(TEXT("SingleComponentPropertyEdit")));
		SingleComponentPropertyWidget->SectionDataChangeDelegate.BindUFunction(this, FName(TEXT("SingleComponentSectionEdit")));
		SingleComponentPropertyWidget->SingleComponentFilePropertyDelegate.BindUFunction(this, FName(TEXT("SingleComponentFilePropertyEdit")));
		SingleComponentPropertyWidget->UpdateContent(SectionDatas);
		SingleComponentPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateSingleComponentProperty_New(const TArray<FSectionData>& SectionDatas)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---update single component property ui with depen files"));
	if (!IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget))
	{
		SingleComponentPropertyWidget = Cast<USingleComponentPropertyWidget>(UUIFunctionLibrary::CreatePropertyUI(static_cast<int>(EPropertyUIType::SingleComponentProperty)));
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{

		EditLayoutWidget->AddPropertyWidget(SingleComponentPropertyWidget);
		EditLayoutWidget->ShowViewButton(true);
		SingleComponentPropertyWidget->SingleComponentPropertyDelegate.BindUFunction(this, FName(TEXT("SingleComponentPropertyEdit")));
		SingleComponentPropertyWidget->SectionDataChangeDelegate.BindUFunction(this, FName(TEXT("SingleComponentSectionEdit")));
		SingleComponentPropertyWidget->SingleComponentFilePropertyDelegate.BindUFunction(this, FName(TEXT("SingleComponentFilePropertyEdit")));
		SingleComponentPropertyWidget->UpdateContent_New(SectionDatas);
		SingleComponentPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::SetSectionImage(const FString& ThumbnailPath)
{
	if (SingleComponentPropertyWidget)
	{
		SingleComponentPropertyWidget->SetSectionImage(ThumbnailPath);
	}
}

void USingleComponentUIManager::SetCameraButton(const int32& btn, bool IsEnable)
{
	if (EditLayoutWidget)
	{
		EditLayoutWidget->SetCameraBtnState(btn, IsEnable);
	}
}

void USingleComponentUIManager::SectionCameraButton()
{
	if (EditLayoutWidget)
	{
		for (int32 i = 0; i < 9; ++i)
		{
			EditLayoutWidget->SetCameraBtnState(i, false);
		}
	}
}

void USingleComponentUIManager::InitCameraButton()
{
	if (EditLayoutWidget)
	{
		for (int32 i = 0; i < 12; ++i)
		{
			EditLayoutWidget->SetCameraBtnState(i, true);
		}
	}
}

void USingleComponentUIManager::SetCameraEnable(bool IsEnable)
{
	EditLayoutWidget->SetCameraEnable(IsEnable);
}

void USingleComponentUIManager::SetSingleComponentSaveState(bool bLoading)
{
	SingleCompToolBarWidget->ChangeSaveState(bLoading);
	EditLayoutWidget->ShowToast(bLoading);

}

void USingleComponentUIManager::SetSectionOperatorSaveState(bool bLoading)
{
	SectionOperatorToolBarWidget->ChangeSaveState(bLoading);
	if (bLoading)
	{
		EditLayoutWidget->ShowToast();
	}
}
void USingleComponentUIManager::SetSavingPercent(const float& prec)
{
	EditLayoutWidget->SetLoadingPercent(prec);
}
void USingleComponentUIManager::SaveDone(bool bSuccess)
{
	EditLayoutWidget->ShowToast();
	EditLayoutWidget->SetToast(bSuccess);
}
void USingleComponentUIManager::UpdateSingleComponentFileProperty(const FFolderTableData& InFileData)
{
	if (IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget))
	{
		SingleComponentPropertyWidget->UpdateFileProperty(InFileData);
	}
}

void USingleComponentUIManager::UpdateSectionProperty(const FSectionProperty& SectionProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---section property"));
	if (!IS_OBJECT_PTR_VALID(SingleComponentSectionPropertyWidget))
	{
		//SingleComponentSectionPropertyWidget = Cast<USingleComponentSectionProperty>(UUIFunctionLibrary::CreatePropertyUI((int)EPropertyUIType::SingleComponenetSectionProperty));
		SingleComponentSectionPropertyWidget = UUIFunctionLibrary::CreatePropertyView<USingleComponentSectionProperty, FSectionProperty>(SectionProperty);
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentSectionPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->AddPropertyWidget(SingleComponentSectionPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SingleComponentSectionPropertyWidget->SectionPointChangedDelegate.BindUFunction(this, FName(TEXT("SectionPointInfoChanged")));
		SingleComponentSectionPropertyWidget->SectionLineChangedDelegate.BindUFunction(this, FName(TEXT("SectionLineInfoChanged")));
		SingleComponentSectionPropertyWidget->SectionRectangleChangedDelegate.BindUFunction(this, FName(TEXT("SectionRectangleInfoChanged")));
		SingleComponentSectionPropertyWidget->SectionEllipseChangedDelegate.BindUFunction(this, FName(TEXT("SectionEllipseInfoChanged")));
		SingleComponentSectionPropertyWidget->SectionCuboidChangedDelegate.BindUFunction(this, FName(TEXT("SectionCuboidInfoChanged")));
		SingleComponentSectionPropertyWidget->WidgetSelectDelegate.BindUFunction(this, FName(TEXT("SingleComponentWidgetSelectEdit")));
		SingleComponentSectionPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		SingleComponentSectionPropertyWidget->UpdateContent(SectionProperty);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateImportSectionProperty(const FLocationProperty& InLocation, const FRotationProperty& InRotation, const FScaleProperty& InScale)
{
	if (!IS_OBJECT_PTR_VALID(SingleComponentImportPropertyWidget))
	{
		SingleComponentImportPropertyWidget = USingleComponentImportSection::Create();
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentImportPropertyWidget))
	{
		EditLayoutWidget->AddPropertyWidget(SingleComponentImportPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SingleComponentImportPropertyWidget->UpdateContent(InLocation, InRotation, InScale);
		SingleComponentImportPropertyWidget->ImportSectionLocationDelegate.BindUFunction(this, FName(TEXT("OnSectionImportLocationHandler")));
		SingleComponentImportPropertyWidget->ImportSectionRotationDelegate.BindUFunction(this, FName(TEXT("OnSectionImportRotationHandler")));
		SingleComponentImportPropertyWidget->ImportSectionScaleDelegate.BindUFunction(this, FName(TEXT("OnSectionImportScaleHandler")));
		SingleComponentImportPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateSectionOperationProperty(const FSectionOperation& InSectionOperation, const FSectionOperationOrder& _SelectOrder)
{
	if (!IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
	{
		SectionOperationPropertyWidget = UUIFunctionLibrary::CreateUIWidget<USectionOperationPropertyWidget>();
	}
	if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->AddPropertyWidget(SectionOperationPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SectionOperationPropertyWidget->UpdateContent(InSectionOperation, _SelectOrder);
		SectionOperationPropertyWidget->TensileOperatorEditDelegate.BindUFunction(this, FName(TEXT("OperatorTensileEdit")));
		SectionOperationPropertyWidget->VisibleChangeDelegate.BindUFunction(this, FName(TEXT("OperatorVisibleEdit")));
		SectionOperationPropertyWidget->ShiftingOperationEditDelegate.BindUFunction(this, FName(TEXT("OperatorShiftingEdit")));
		SectionOperationPropertyWidget->ZoomOperationEditDelegate.BindUFunction(this, FName(TEXT("OperatorZoomEdit")));
		SectionOperationPropertyWidget->CutOutOperatorEditDelegate.BindUFunction(this, FName(TEXT("OperatorCutOutEdit")));
		SectionOperationPropertyWidget->LoftingOperatorEditDelegate.BindUFunction(this, FName(TEXT("OperatorLoftingEdit")));
		SectionOperationPropertyWidget->RotationOperatorEditDelegate.BindUFunction(this, FName(TEXT("OperatorRotationEdit")));
		SectionOperationPropertyWidget->WidgetOperationDelegate.BindUFunction(this, FName(TEXT("BindOperationId")));
		SectionOperationPropertyWidget->OperationSelectDelegate.BindUFunction(this, FName(TEXT("UpdateOperationSelect")));
		SectionOperationPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateSectionOperationProperty(const FSectionLoftOperation& InLoftingOperation)
{
	if (!IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
	{
		SectionOperationPropertyWidget = UUIFunctionLibrary::CreateUIWidget<USectionOperationPropertyWidget>();
	}
	if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		SingleComponentSectionToolBarWidget->LoftingOperationMode();
		EditLayoutWidget->AddPropertyWidget(SectionOperationPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SectionOperationPropertyWidget->UpdateContent(InLoftingOperation);
		SectionOperationPropertyWidget->PointDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorPointEdit")));
		SectionOperationPropertyWidget->LineDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorLineEdit")));
		SectionOperationPropertyWidget->DataWidgetSelectDelegate.BindUFunction(this, FName(TEXT("SectionOperatorLoftingWidgetSelectEdit")));
		SectionOperationPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateSectionOperationProperty(const FSectionCutOutOperation& InCutOutOperation)
{
	if (!IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
	{
		SectionOperationPropertyWidget = UUIFunctionLibrary::CreateUIWidget<USectionOperationPropertyWidget>();
	}
	if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		SingleComponentSectionToolBarWidget->CutoutOperationMode();
		EditLayoutWidget->AddPropertyWidget(SectionOperationPropertyWidget);
		EditLayoutWidget->ShowViewButton(false);
		SectionOperationPropertyWidget->UpdateContent(InCutOutOperation);
		SectionOperationPropertyWidget->PointDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorPointEdit")));
		SectionOperationPropertyWidget->LineDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorLineEdit")));
		SectionOperationPropertyWidget->RectangleDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorRectangleEdit")));
		SectionOperationPropertyWidget->EllipseDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorEllipseEdit")));
		SectionOperationPropertyWidget->DataWidgetSelectDelegate.BindUFunction(this, FName(TEXT("SectionOperatorLoftingWidgetSelectEdit")));
		SectionOperationPropertyWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->SetDetailPanelIsShow(true);
	}
}

void USingleComponentUIManager::UpdateSectionOperationToolBarWidget(bool IsShow)
{
	if (IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->SetSectionToolBarPanelShow(IsShow);
		if (!IsShow)
		{
			return;
		}
	}
	if (!IS_OBJECT_PTR_VALID(SectionOperatorToolBarWidget))
	{
		//SectionOperatorToolBarWidget = UUIFunctionLibrary::CreateUIWidget<USectionOperatorToolBarWidget>();
		SectionOperatorToolBarWidget = USectionOperatorToolBarWidget::Create();
	}
	if (IS_OBJECT_PTR_VALID(SectionOperatorToolBarWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		SectionOperatorToolBarWidget->OperatorToolBarEditDelegate.BindUFunction(this, FName(TEXT("OperationToolBarEdit")));
		SectionOperatorToolBarWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->AddToolBar(SectionOperatorToolBarWidget);
	}
}

int32 USingleComponentUIManager::JudgeOperationInsert()
{
	if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
	{
		return SectionOperationPropertyWidget->JudgeOperationInsert();
	}
	return -1;
}

void USingleComponentUIManager::UpdateSectionToolBarWidget(bool IsCustom, const int32& ToolBarState)
{
	if (!IS_OBJECT_PTR_VALID(SingleComponentSectionToolBarWidget))
	{
		SingleComponentSectionToolBarWidget = USingleComponentSectionToolBar::Create();
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentSectionToolBarWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		SingleComponentSectionToolBarWidget->NormalMode();
		SingleComponentSectionToolBarWidget->SectionToolBarDelegate.BindUFunction(this, FName(TEXT("SectionToolBarEdit")));
		/*SingleComponentSectionToolBarWidget->ResetToolBarState();*/
		/*SingleComponentSectionToolBarWidget->UpdateToolBarState(ToolBarState);*/
		SingleComponentSectionToolBarWidget->UpdateDrawState(IsCustom);
		SingleComponentSectionToolBarWidget->SetVisibility(ESlateVisibility::Visible);
		EditLayoutWidget->AddToolBar(SingleComponentSectionToolBarWidget);
		//SingleComponentSectionToolBarWidget->SectionSelectState();
	}
	//if (IS_OBJECT_PTR_VALID(EditLayoutWidget))
	//{
	//	EditLayoutWidget->SetSectionToolBarPanelShow(IsSectionToolBarShow);
	//	if (!IsSectionToolBarShow)
	//	{
	//		return;
	//	}
	//}
	//UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---single component section tool bar"));
	//if (!IS_OBJECT_PTR_VALID(SingleComponentSectionToolBarWidget))
	//{
	//	SingleComponentSectionToolBarWidget = UUIFunctionLibrary::CreateUIWidget<USingleComponentSectionToolBar>();
	//}
	//if (IS_OBJECT_PTR_VALID(SingleComponentSectionToolBarWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	//{
	//	SingleComponentSectionToolBarWidget->SectionToolBarDelegate.BindUFunction(this, FName(TEXT("SectionToolBarEdit")));
	//	/*SingleComponentSectionToolBarWidget->ResetToolBarState();*/
	//	/*SingleComponentSectionToolBarWidget->UpdateToolBarState(ToolBarState);*/
	//	SingleComponentSectionToolBarWidget->SetVisibility(ESlateVisibility::Visible);
	//	EditLayoutWidget->AddSectionToolBar(SingleComponentSectionToolBarWidget);
	//}
}

void USingleComponentUIManager::UpdateSectionWidgetSelectStyle(const int32& EditType, const int32& WidgetId, bool IsSectionData)
{
	if (IsSectionData)
	{
		if (IS_OBJECT_PTR_VALID(SingleComponentSectionPropertyWidget))
		{
			SingleComponentSectionPropertyWidget->UpdateSectionWidgetSelectStyle(EditType, WidgetId);
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
		{
			SectionOperationPropertyWidget->UpdateSelectSectionWidgetStyle(EditType, WidgetId);
		}
	}
}

void USingleComponentUIManager::UpdateHoverSectionWidgetStyle(const int32& EditType, const int32& WidgetId, bool IsHovered, bool IsSectionData)
{
	if (IsSectionData)
	{
		if (IS_OBJECT_PTR_VALID(SingleComponentSectionPropertyWidget))
		{
			SingleComponentSectionPropertyWidget->UpdateHoverSectionWidgetStyle(EditType, WidgetId, IsHovered);
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(SectionOperationPropertyWidget))
		{
			SectionOperationPropertyWidget->UpdateHoverSectionWidgetStyle(EditType, WidgetId, IsHovered);
		}
	}
}

void USingleComponentUIManager::BackToSingleComponentPropertyContent()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---backspace the property ui"));
	if (IS_OBJECT_PTR_VALID(SingleComponentSectionPropertyWidget))
	{
		SingleComponentSectionPropertyWidget->RemoveFromParent();
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget) && IS_OBJECT_PTR_VALID(SingleCompToolBarWidget) && IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->AddPropertyWidget(SingleComponentPropertyWidget);
		EditLayoutWidget->ShowViewButton(true);
		EditLayoutWidget->AddToolBar(SingleCompToolBarWidget);
		SingleComponentPropertyWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void USingleComponentUIManager::Clear()
{
	if (IS_OBJECT_PTR_VALID(SingleCompToolBarWidget))
	{
		SingleCompToolBarWidget->SetVisibility(ESlateVisibility::Collapsed);
		SingleCompToolBarWidget->RemoveFromParent();
		SingleCompToolBarWidget = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(SingleComponentPropertyWidget))
	{
		SingleComponentPropertyWidget->SetVisibility(ESlateVisibility::Collapsed);
		SingleComponentPropertyWidget->RemoveFromParent();
		SingleComponentPropertyWidget = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->SetVisibility(ESlateVisibility::Collapsed);
		EditLayoutWidget->RemoveFromParent();
		EditLayoutWidget = nullptr;
	}
}

ESingleComponentToolBarType USingleComponentUIManager::GetToolBarAction()
{
	return SingleCompToolBarWidget->GetCurrentActionType();
}

void USingleComponentUIManager::RefreshSceneViewportSize()
{
	if (IS_OBJECT_PTR_VALID(EditLayoutWidget))
	{
		EditLayoutWidget->RefreshSceneViewportSize();
	}
}

void USingleComponentUIManager::BindOperationId(const int32& type, const FSectionOperationOrder& _SelectOrder)
{
	WidgetOperationDelegate.ExecuteIfBound(type, _SelectOrder);
	UE_LOG(LogTemp, Log, TEXT("%d,%d"), type, _SelectOrder.Index);
}

void USingleComponentUIManager::SingleComponentPictureType(const int32& Type)
{
	SingleComponentPictureTypeDelegate.ExecuteIfBound(Type);

}

void USingleComponentUIManager::SetCameraImage(const FString& ImagePath, bool IsShow)
{
	if (EditLayoutWidget)
	{
		EditLayoutWidget->SetCameraImage(ImagePath, IsShow);
	}
}

void USingleComponentUIManager::UpdateNewSectionPanelWidget()
{
	if (!IS_OBJECT_PTR_VALID(AddSectionPanelSelectWidget))
	{
		AddSectionPanelSelectWidget = UWidget_SelectSection::Create();
		AddSectionPanelSelectWidget->AddToViewport(PopUIZOrder);
	}
	if (IS_OBJECT_PTR_VALID(AddSectionPanelSelectWidget))
	{
		AddSectionPanelSelectWidget->ResetState();
		AddSectionPanelSelectWidget->SelectSureDelegate.BindUFunction(this, FName(TEXT("OnNewSectionPanelSelectHandler")));
		AddSectionPanelSelectWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void USingleComponentUIManager::OnNewSectionPanelSelectHandler(const int32& PanelSelect)
{
	OnCreateNewSection.ExecuteIfBound(PanelSelect == (int32)ESelectView::Sideview ? 2 :
		PanelSelect == (int32)ESelectView::Verticalview ? 1 : PanelSelect == (int32)ESelectView::Frontview ? 3 : 0);
}

void USingleComponentUIManager::UpdateSectionImportWidget(float InProgress, const EImportStateType& InType)
{
	if (!IS_OBJECT_PTR_VALID(SectionImportWidget))
	{
		SectionImportWidget = USingleComponentImportWidget::Create();
		SectionImportWidget->AddToViewport(PopUIZOrder);
	}
	if (IS_OBJECT_PTR_VALID(SectionImportWidget))
	{
		SectionImportWidget->UpdateContent(InProgress);
		SectionImportWidget->UpdateState(InType);
		SectionImportWidget->ImportActionDelegate.BindUFunction(this, FName(TEXT("OnSectionImportHandler")));
		SectionImportWidget->SetVisibility(ESlateVisibility::Visible);

	}
}

void USingleComponentUIManager::UpdateImportImage(const FString& ThumbnailFullPath)
{
	if (IS_OBJECT_PTR_VALID(SectionImportWidget))
	{
		SectionImportWidget->UpdateImage(ThumbnailFullPath);
	}
}

void USingleComponentUIManager::UpdateImportName(const FString& InName)
{
	if (IS_OBJECT_PTR_VALID(SectionImportWidget))
	{
		SectionImportWidget->UpdateImportName(InName);
	}
}

void USingleComponentUIManager::ShowEdtPath(bool IsShow)
{
	if (IS_OBJECT_PTR_VALID(SectionImportWidget))
	{
		SectionImportWidget->ShowEdtPath(IsShow);
	}
}

void USingleComponentUIManager::SetSingleComponentSectionToolBarSelectType(const ESectionToolBarEditType& SelectType)
{
	if (IS_OBJECT_PTR_VALID(SingleComponentSectionToolBarWidget))
	{
		SingleComponentSectionToolBarWidget->SetSelectType(SelectType);
		SingleComponentSectionToolBarWidget->SectionSelectState();
	}
}

void USingleComponentUIManager::OnSectionImportHandler(const EImportActionType& ActionType, const FString& InSavePath, const FString& InRefPath)
{
	SingleComponentImportActionDelegate.ExecuteIfBound(ActionType, InSavePath, InRefPath);
}

void USingleComponentUIManager::OnSectionImportLocationHandler(const int32& EditType, const FString& OutString)
{
	ImportLocationChangeDelegate.ExecuteIfBound(EditType, OutString);
}

void USingleComponentUIManager::OnSectionImportRotationHandler(const int32& EditType, const FString& OutString)
{
	ImportRotationChangeDelegate.ExecuteIfBound(EditType, OutString);
}

void USingleComponentUIManager::OnSectionImportScaleHandler(const int32& EditType, const FString& OutString)
{
	ImportScaleChangeDelegate.ExecuteIfBound(EditType, OutString);
}

void USingleComponentUIManager::SingleComponentWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty)
{
	SingleComponentWidgetSelectDelegate.ExecuteIfBound(EditType, WidgetId, IsOver, OnProperty);
}

void USingleComponentUIManager::SectionOperatorLoftingWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty)
{
	SectionOperatorLoftingDataDelegate.ExecuteIfBound(EditType, WidgetId, IsOver, OnProperty);
}

void USingleComponentUIManager::UpdateOperationSelect(const FSectionOperationOrder& OrderData)
{
	OperationSelectDelegate.ExecuteIfBound(OrderData);
	UE_LOG(LogTemp, Log, TEXT("operation in selected : %d"), OrderData.Index);
}

void USingleComponentUIManager::ToolBarEdit(const int32& EditType)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---tool bar edit"));
	switch (static_cast<ESingleComponentToolBarType>(EditType))
	{
	case ESingleComponentToolBarType::Exit:
	{
		OnClickExitButton.ExecuteIfBound(); break;
	}
	case ESingleComponentToolBarType::Save:
	{
		OnClickSaveButton.ExecuteIfBound(); break;
	}
	case ESingleComponentToolBarType::Load:
	{
		OnClickLoadButton.ExecuteIfBound();
		break;
	}
	default:
		break;
	}
}

void USingleComponentUIManager::UpdateSelectViewFolderData(const FString& InName, bool IsFolder)
{

}

void USingleComponentUIManager::SingleComponentPropertyEdit(const int32& PropertyEditType, const int32& SectionId)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---single component property edit type//section id : %d//%d"), PropertyEditType, SectionId);
	if (PropertyEditType == (int32)EPropertyEditType::AddSection)
	{
		UpdateNewSectionPanelWidget();
	}
	else
	{
		SingleComponentPropertyOperatorDelegate.ExecuteIfBound(PropertyEditType, SectionId);
	}
	/*if (PropertyEditType == (int32)EPropertyEditType::InsertOperate)
	{

	}
	else if (PropertyEditType == (int32)EPropertyEditType::AddSection)
	{
		UpdateNewSectionPanelWidget();
	}
	else if (PropertyEditType == (int32)EPropertyEditType::Operator)
	{
	}
	else if (PropertyEditType == (int32)EPropertyEditType::Import)
	{
		UpdateSectionImportWidget(0.0f);
	}
	else if (PropertyEditType == (int32)EPropertyEditType::Copy)
	{
	}
	else if (PropertyEditType == (int32)EPropertyEditType::Delete)
	{
	}*/
}

void USingleComponentUIManager::SingleComponentSectionToolBarEdit(const int32& EditType)
{

}

void USingleComponentUIManager::SingleComponentSectionEdit(const int32& EditType, FSectionData& SectionData)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---section edit type : %d"), EditType);
	SelectSectionEditDelegate.ExecuteIfBound(EditType, SectionData);
}

void USingleComponentUIManager::SingleComponentFilePropertyEdit(const int32& EditType, const FString& OutString)
{
	SingleComponentFileDataDelegate.ExecuteIfBound(EditType, OutString);
}

void USingleComponentUIManager::SingleComponentResetToolState()
{
	SingleComponentSectionToolBarWidget->ResetToolBarState();
}

void USingleComponentUIManager::SectionPointInfoChanged(const int32& EditType, const FGeomtryPointProperty& PointInfo)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---point info change"));
	SingleComponentPointDelegate.ExecuteIfBound(EditType, PointInfo);
}

void USingleComponentUIManager::SectionLineInfoChanged(const int32& EditType, const FGeomtryLineProperty& LineInfo)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---line info change"));
	SingleComponentLineDelegate.ExecuteIfBound(EditType, LineInfo);
}

void USingleComponentUIManager::SectionRectangleInfoChanged(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleInfo)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---rectangle info change"));
	SingleComponentRectangleDelegate.ExecuteIfBound(EditType, RectangleInfo);
}

void USingleComponentUIManager::SectionEllipseInfoChanged(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseInfo)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---ellipse info change"));
	SingleComponentEllipseDelegate.ExecuteIfBound(EditType, EllipseInfo);
}

void USingleComponentUIManager::SectionCuboidInfoChanged(const int32& EditType, const FGeomtryCubeProperty& CuboidInfo)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---cuboid info change"));
	SingleComponentCuboidDelegate.ExecuteIfBound(EditType, CuboidInfo);
}

void USingleComponentUIManager::SectionToolBarEdit(const int32& EditType)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentUIManager---section tool bar edit"));
	if (EditType == (int32)ESectionToolBarEditType::Save)
	{
		OnClickSaveButton.ExecuteIfBound();
	}
	else if (EditType == (int32)ESectionToolBarEditType::Exit)
	{
		/*if (IS_OBJECT_PTR_VALID(EditLayoutWidget))
		{
			EditLayoutWidget->SetDetailPanelIsShow(false);
		}*/
		OnClickExitButton.ExecuteIfBound();
	}
	else
	{
		SectionToolBarDelegate.ExecuteIfBound(EditType);
	}
}

void USingleComponentUIManager::OperationToolBarEdit(const int32& EditType)
{
	SectionOperationToolBarDelegate.ExecuteIfBound(EditType);
}

void USingleComponentUIManager::OperatorVisibleEdit(const int32& EditType, const FExpressionValuePair& VisibleParam)
{
	SectionOperatorVisibleDelegate.ExecuteIfBound(EditType, VisibleParam);
}

void USingleComponentUIManager::OperatorTensileEdit(const int32& EditType, const FSectionDrawOperation& TensileData)
{
	SectionOperatorTensileDelegate.ExecuteIfBound(EditType, TensileData);
}

void USingleComponentUIManager::OperatorShiftingEdit(const int32& EditType, const FSectionShiftingOperation& ShiftingData)
{
	SectionOperatorShiftingDelegate.ExecuteIfBound(EditType, ShiftingData);
}

void USingleComponentUIManager::OperatorZoomEdit(const int32& EditType, const FSectionZoomOperation& ShiftingData)
{
	SectionOperationZoomDelegate.ExecuteIfBound(EditType, ShiftingData);
}

void USingleComponentUIManager::OperatorCutOutEdit(const int32& EditType, const FSectionCutOutOperation& CutOutData)
{
	SectionOperatorCutOutDelegate.ExecuteIfBound(EditType, CutOutData);
}

void USingleComponentUIManager::OperatorLoftingEdit(const int32& EditType)
{
	SectionOperatorLoftingDelegate.ExecuteIfBound(EditType);
}

void USingleComponentUIManager::OperatorRotationEdit(const int32& EditType, const FSectionRotatorOperation& RotationData)
{
	SectionOperatorRotationDelegate.ExecuteIfBound(EditType, RotationData);
}

void USingleComponentUIManager::OperatorPointEdit(const int32& EditType, const FGeomtryPointProperty& PointData)
{
	SectionOperatorDataPointDelegate.ExecuteIfBound(EditType, PointData);
}

void USingleComponentUIManager::OperatorLineEdit(const int32& EditType, const FGeomtryLineProperty& LineProperty)
{
	SectionOperatorDataLineDelegate.ExecuteIfBound(EditType, LineProperty);
}

void USingleComponentUIManager::OperatorRectangleEdit(const int32& EditType, const FGeomtryRectanglePlanProperty& RectProperty)
{
	SectionOperatorDataRectangleDelegate.ExecuteIfBound(EditType, RectProperty);
}

void USingleComponentUIManager::OperatorEllipseEdit(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseProperty)
{
	SectionOperatorDataEllipseDelegate.ExecuteIfBound(EditType, EllipseProperty);
}

void USingleComponentUIManager::SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name)
{
	SingleComponentPropertyWidget->SetMatImageAndName(MatIndex, ThumbnailPath, Name);
}

#undef LOCTEXT_NAMESPACE