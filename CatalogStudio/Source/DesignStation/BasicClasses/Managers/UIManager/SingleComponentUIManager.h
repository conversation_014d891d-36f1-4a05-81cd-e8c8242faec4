// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UIManagerBase.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "DesignStation/UI/GeneralWidgets/EditorLayoutWidget.h"
#include "DesignStation/UI/Operation/SectionOperationPropertyWidget.h"
#include "DesignStation/UI/Operation/SectionOperatorToolBarWidget.h"
#include "DesignStation/UI/PopUI/Widget_SelectSection.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentCuboid.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentEllipse.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentImportSection.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentImportWidget.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentLine.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentPoint.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentPropertyWidget.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentRectangle.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentSection.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentSectionProperty.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentSectionToolBar.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentToolBar.h"
#include "SingleComponentUIManager.generated.h"

/**
 *
 */

class UFolderWidget;

DECLARE_DYNAMIC_DELEGATE_OneParam(FSingleComponentSectionToolBarDelegate, const int32&, EditType);
DECLARE_DYNAMIC_DELEGATE_OneParam(FSectionOperationToolBarDelegate, const int32&, EditType);

UCLASS()
class DESIGNSTATION_API USingleComponentUIManager : public UUIManagerBase
{
	GENERATED_BODY()

public:
	void UpdateSingleComponentUI();
	void UpdateSingleComponentProperty(const TArray<FSectionData>& SectionDatas);
	void UpdateSingleComponentProperty_New(const TArray<FSectionData>& SectionDatas);
	void UpdateSingleComponentFileProperty(const FFolderTableData& InFileData);
	void UpdateSectionProperty(const FSectionProperty& SectionProperty);

	void UpdateImportSectionProperty(const FLocationProperty& InLocation, const FRotationProperty& InRotation, const FScaleProperty& InScale);

	void UpdateSectionImportWidget(float InProgress, const EImportStateType& InType = EImportStateType::Normal);
	void UpdateImportImage(const FString& ThumbnailFullPath);
	void UpdateImportName(const FString& InName);
	void ShowEdtPath(bool IsShow);


	void SetSingleComponentSectionToolBarSelectType(const ESectionToolBarEditType& SelectType);

	//section operation
	void UpdateSectionOperationProperty(const FSectionOperation& InSectionOperation, const FSectionOperationOrder& _SelectOrder = FSectionOperationOrder());
	void UpdateSectionOperationProperty(const FSectionLoftOperation& InLoftingOperation);
	void UpdateSectionOperationProperty(const FSectionCutOutOperation& InCutOutOperation);
	void UpdateSectionOperationToolBarWidget(bool IsShow);
	int32 JudgeOperationInsert();

	void UpdateSectionToolBarWidget(bool IsCustom = true, const int32& ToolBarState = (int32)EToolBarState::DrawSection);
	void UpdateSectionWidgetSelectStyle(const int32& EditType, const int32& WidgetId, bool IsSectionData = true);
	void UpdateHoverSectionWidgetStyle(const int32& EditType, const int32& WidgetId, bool IsHovered, bool IsSectionData = true);

	void BackToSingleComponentPropertyContent();
	void Clear();

	ESingleComponentToolBarType GetToolBarAction();

	virtual void RefreshSceneViewportSize() override;

	void SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name);

	void SingleComponentResetToolState();
public:

	FOneInt32Delegate OnCreateNewSection;
	FNoParamDelegate OnClickSaveButton;
	FNoParamDelegate OnClickExitButton;
	FNoParamDelegate OnClickLoadButton;
	FSectionInfoChangeDelegate SelectSectionEditDelegate;
	FSingleComponentSectionToolBarDelegate SectionToolBarDelegate;

	FSingleComponentPropertyDelegate SingleComponentPropertyOperatorDelegate;
	FSingleComponentFilePropertyDelegate SingleComponentFileDataDelegate;

	FImportActionDelegate SingleComponentImportActionDelegate;
	FImportSectionLocationDelegate ImportLocationChangeDelegate;
	FImportSectionRotationDelegate ImportRotationChangeDelegate;
	FImportSectionScaleDelegate ImportScaleChangeDelegate;

	FWidgetSelectDelegate SingleComponentWidgetSelectDelegate;

	FSectionOperationToolBarDelegate SectionOperationToolBarDelegate;
	FVisibleChangeDelegate SectionOperatorVisibleDelegate;
	FTensileOperatorEditDelegate SectionOperatorTensileDelegate;
	FShiftingOperationDelegate SectionOperatorShiftingDelegate;
	FZoomOperationDelegate SectionOperationZoomDelegate;
	FCutOutOperatorEditDelegate SectionOperatorCutOutDelegate;
	FLoftingOperatorEditDelegate SectionOperatorLoftingDelegate;
	FRotationOperatorEditDelegate SectionOperatorRotationDelegate;
	FPointDataChangedDelegate SectionOperatorDataPointDelegate;
	FLinePropertyChangeDelegate SectionOperatorDataLineDelegate;
	FRectanglePropertyChangeDelegate SectionOperatorDataRectangleDelegate;
	FEllipsePropertyChangeDelegate SectionOperatorDataEllipseDelegate;
	FWidgetSelectDelegate SectionOperatorLoftingDataDelegate;
	FWidgetOperationDelegate WidgetOperationDelegate;
	FOperationSelectDelegate OperationSelectDelegate;

private:
	void UpdateNewSectionPanelWidget();
	UFUNCTION()
		void OnNewSectionPanelSelectHandler(const int32& PanelSelect);
	UFUNCTION()
		void OnSectionImportHandler(const EImportActionType& ActionType, const FString& InSavePath, const FString& InRefPath);

	UFUNCTION()
		void OnSectionImportLocationHandler(const int32& EditType, const FString& OutString);
	UFUNCTION()
		void OnSectionImportRotationHandler(const int32& EditType, const FString& OutString);
	UFUNCTION()
		void OnSectionImportScaleHandler(const int32& EditType, const FString& OutString);

	UFUNCTION()
		void SingleComponentWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty);
	UFUNCTION()
		void SectionOperatorLoftingWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty);

protected:
	UFUNCTION()
		void UpdateOperationSelect(const FSectionOperationOrder& OrderData);
	UFUNCTION()
		void ToolBarEdit(const int32& EditType);
	UFUNCTION()
		void UpdateSelectViewFolderData(const FString& InName, bool IsFolder);
	UFUNCTION()
		void SingleComponentPropertyEdit(const int32& PropertyEditType, const int32& SectionId);
	UFUNCTION()
		void SingleComponentSectionToolBarEdit(const int32& EditType);
	UFUNCTION()
		void SingleComponentSectionEdit(const int32& EditType, FSectionData& SectionData);
	UFUNCTION()
		void SingleComponentFilePropertyEdit(const int32& EditType, const FString& OutString);

	UFUNCTION()
		void SectionPointInfoChanged(const int32& EditType, const FGeomtryPointProperty& PointInfo);
	UFUNCTION()
		void SectionLineInfoChanged(const int32& EditType, const FGeomtryLineProperty& LineInfo);
	UFUNCTION()
		void SectionRectangleInfoChanged(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleInfo);
	UFUNCTION()
		void SectionEllipseInfoChanged(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseInfo);
	UFUNCTION()
		void SectionCuboidInfoChanged(const int32& EditType, const FGeomtryCubeProperty& CuboidInfo);
	UFUNCTION()
		void SectionToolBarEdit(const int32& EditType);
	//section operator
	UFUNCTION()
		void OperationToolBarEdit(const int32& EditType);
	UFUNCTION()
		void OperatorVisibleEdit(const int32& EditType, const FExpressionValuePair& VisibleParam);
	UFUNCTION()
		void OperatorTensileEdit(const int32& EditType, const FSectionDrawOperation& TensileData);
	UFUNCTION()
		void OperatorShiftingEdit(const int32& EditType, const FSectionShiftingOperation& ShiftingData);
	UFUNCTION()
		void OperatorZoomEdit(const int32& EditType, const FSectionZoomOperation& ShiftingData);
	UFUNCTION()
		void OperatorCutOutEdit(const int32& EditType, const FSectionCutOutOperation& CutOutData);
	UFUNCTION()
		void OperatorLoftingEdit(const int32& EditType);
	UFUNCTION()
		void OperatorRotationEdit(const int32& EditType, const FSectionRotatorOperation& RotationData);
	UFUNCTION()
		void OperatorPointEdit(const int32& EditType, const FGeomtryPointProperty& PointData);
	UFUNCTION()
		void OperatorLineEdit(const int32& EditType, const FGeomtryLineProperty& LineProperty);
	UFUNCTION()
		void OperatorRectangleEdit(const int32& EditType, const FGeomtryRectanglePlanProperty& RectProperty);
	UFUNCTION()
		void OperatorEllipseEdit(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseProperty);
	UFUNCTION()
		void BindOperationId(const int32& type, const FSectionOperationOrder& _SelectOrder);
	UFUNCTION()
		void SingleComponentPictureType(const int32& Type);
private:
	UPROPERTY()
		UEditorLayoutWidget* EditLayoutWidget;
	UPROPERTY()
		USingleComponentToolBar* SingleCompToolBarWidget;
	UPROPERTY()
		USingleComponentSectionToolBar* SingleComponentSectionToolBarWidget;
	UPROPERTY()
		USingleComponentPropertyWidget* SingleComponentPropertyWidget;
	UPROPERTY()
		USingleComponentSectionProperty* SingleComponentSectionPropertyWidget;
	UPROPERTY()
		USingleComponentImportSection* SingleComponentImportPropertyWidget;

	//section operator
	UPROPERTY()
		USectionOperationPropertyWidget* SectionOperationPropertyWidget;
	UPROPERTY()
		USectionOperatorToolBarWidget* SectionOperatorToolBarWidget;

	UPROPERTY()
		UWidget_SelectSection* AddSectionPanelSelectWidget;
	UPROPERTY()
		USingleComponentImportWidget* SectionImportWidget;

	//static FSectionOperationOrder DefaultOperation;

public:
	FPointDataChangedDelegate			SingleComponentPointDelegate;
	FLinePropertyChangeDelegate			SingleComponentLineDelegate;
	FRectanglePropertyChangeDelegate	SingleComponentRectangleDelegate;
	FEllipsePropertyChangeDelegate		SingleComponentEllipseDelegate;
	FCuboidPropertyChangeDelegate		SingleComponentCuboidDelegate;
	FCameraBtnTypeDelegate				SingleComponentPictureTypeDelegate;
public:
	void SetCameraImage(const FString& ImagePath, bool IsShow);
	void SetSectionImage(const FString& ThumbnailPath);
	void SetCameraButton(const int32& btn, bool IsEnable);
	void SectionCameraButton();
	void InitCameraButton();
	void SetCameraEnable(bool IsEnable);
	void SetSingleComponentSaveState(bool bLoading);
	void SetSectionOperatorSaveState(bool bLoading);
	void SetSavingPercent(const float& perc);
	void SaveDone(bool bSuccess);
};
