#include "ViewManager.h"

#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/SubSystem/PakFileManagerSubsystem.h"
#include "DesignStation/SubSystem/ResourceSubsystem.h"
#include "DesignStation/UI/MacroDef.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/LoadPageWidget.h"
#include "Engine/StaticMeshActor.h"
#include "Kismet/GameplayStatics.h"

extern const int MainUIZOrder;

FCriticalSection ViewMutex;
#define VIEW_MUTEX_SCOPE_LOCK FScopeLock ScopeLock(&ViewMutex)

void UViewManager::Init(const FCSModelMatData& InData)
{
	StartProcess();

	ViewData = InData;

	//init view module type
	InitViewType(InData);

	//set load level map
	MapName = CurViewType == ECatalogViewItemType::E_Material ? TEXT("MatMap") : TEXT("CompMap");
}

void UViewManager::InitManager(const FCSModelMatData& InData)
{
	//TArray<FString> ParseFiles;
	TArray<FString> NeedDownloadFiles;
	if (AnalysisDataToGetNeedDownloadFile(ParsePath, NeedDownloadFiles))
	{//Download file 
		TArray<FString> EncodeFileURL;
		for (const FString& NDF : NeedDownloadFiles)
		{
			FString EncodeURL = UUIFunctionLibrary::CatalogEncodeBase64(NDF);
			EncodeFileURL.Add(EncodeURL);
		}
		DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendMultiDownloadURLRequest(
			EncodeFileURL,
			CurViewType == ECatalogViewItemType::E_PAK ? 0 : 1,
			ParsePath
		);
	}
	else
	{
		LoadMesh(ParsePath);
		EndProcess();
	}

}

bool UViewManager::IsModelType() const
{
	return CurViewType == ECatalogViewItemType::E_PAK || CurViewType == ECatalogViewItemType::E_FBX;
}

void UViewManager::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UViewManager::OnDownloadFileCompleteHandler);
}

void UViewManager::UnBindDelegate()
{
}

bool UViewManager::AnalysisDataToGetNeedDownloadFile(
	TArray<FString>& FilePath,
	TArray<FString>& DownloadFilePath)
{
	if (CurViewType == ECatalogViewItemType::E_PAK)
	{
		AnalysisData_Pak(ViewData.modelList, ViewData.pakFilePath, FilePath, DownloadFilePath);
	}
	else if (CurViewType == ECatalogViewItemType::E_FBX)
	{
		
	}
	else if (CurViewType == ECatalogViewItemType::E_Material)
	{
		AnalysisData_Material(ViewData.manageMapsList, ViewData.id,
			ViewData.ue5Param, ViewData.md5, FilePath, DownloadFilePath);
	}
	return DownloadFilePath.Num() > 0;
}

void UViewManager::SendDownloadNecessary(const TArray<FString>& Paths)
{
}

void UViewManager::OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths)
{
	if(UUID.Equals(DownloadUUID))
	{
		DownloadUUID = TEXT("NoWay");
		if(Success)
		{
			UpdateProcess(0.7f);
			if(CurViewType == ECatalogViewItemType::E_PAK)
			{
				VIEW_MUTEX_SCOPE_LOCK;

				if (!FilePaths.IsValidIndex(0)) return;
				//FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FilePaths[0]));
				const FString FilePath = URefRelationFunction::GetZipRelativePath(FilePaths[0], true);
				const FString TargetPath = FPaths::GetPath(FilePath);
				FGraphEventRef GraphEventRef;
				CALL_THREAD_STATIC_OUT(
					GraphEventRef,
					NULL,
					ENamedThreads::AnyThread,
					&URefRelationFunction::UnPackFile,
					FilePath, TargetPath);

				auto LoadPath = FilePaths;
				FGraphEventRef GraphEventAfterRef;
				CALL_THREAD_UFUNCTION_OUT(
					GraphEventAfterRef,
					GraphEventRef,
					ENamedThreads::GameThread,
					this,
					FName(TEXT("LoadMesh_Pak")),
					ParsePath
				);
			}
			else
			{
				LoadMesh(ParsePath);
			}
		}
		else
		{
			EndProcess();
			UI_POP_WINDOW_ERROR(TEXT("download error"));
		}
	}
}

void UViewManager::OnViewLayoutActionHandler(EViewLayoutActionType InAction)
{
	if (InAction == EViewLayoutActionType::E_Exit)
	{
		OnBackToMain.ExecuteIfBound(FFolderTableData());
	}
}

void UViewManager::InitViewType(const FCSModelMatData& InData)
{
	if (InData.IsValid())
	{
		if (!InData.refPath.IsEmpty())
		{
			CurViewType = ECatalogViewItemType::E_PAK;
		}
		else
		{
			CurViewType = ECatalogViewItemType::E_FBX;
		}
		if (InData.manageMapsList.Num() > 0)
		{
			CurViewType = ECatalogViewItemType::E_Material;

			FString Mark = InData.folderId.IsEmpty() ? FString::FromInt(InData.id) : InData.folderId;
			UResourceSubsystem::GetInstance()->AddMaterialWebData({ViewData}, {ViewData});
		}
	}
	else
	{
		CurViewType = ECatalogViewItemType::E_None;
	}
}

void UViewManager::InitActorAxis()
{
	if (!IS_OBJECT_PTR_VALID(AxisActor))
		return;

	AxisActor->UpdateAxisType(EPlanPolygonBelongs::EUnknown);
	const FVector CenterPoint = FVector::ZeroVector;
	const FVector BoxExtent = FVector(100, 100, 0);
	SetActorBox(CenterPoint, BoxExtent);
	AxisActor->ChangeSizeByContentSize(CenterPoint + BoxExtent);
	AxisActor->ChangeTagRotation(CameraLocation, CameraWidth);
}

#define OSS_MODEL_MAT_PATH TEXT("bk_file/design/manager/")
bool UViewManager::AnalysisData_Pak(const TArray<FCSModelInfo>& Datas, const FString& PakPath, TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	for (const auto PM : Datas)
	{
		FString FolderPath = PakPath;
		FolderPath.RemoveAt(0);
		//path folder
		//zip file unpack has same name folder 

		FolderPath = FPaths::Combine(FolderPath, TEXT("D"));
		FString AbsFolderPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FolderPath));
		if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*AbsFolderPath))
		{
			FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*AbsFolderPath);
		}
		
		//FString RelPath = FPaths::Combine(FolderPath, PM.name + (PM.name.EndsWith(TEXT(".pak")) ? TEXT("") : TEXT(".pak")));
		FString PakName = TEXT("D");
		FString RelPath = FPaths::Combine(FolderPath, PakName + (PakName.EndsWith(TEXT(".pak")) ? TEXT("") : TEXT(".pak")));
		FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(),RelPath));
		UE_LOG(LogTemp, Warning, TEXT("view pak file path : [%s]"), *RelPath);
		FilePath.Add(RelPath);
		if (!FPaths::FileExists(AbsPath) /*|| !PM.md5.IsEmpty()*/)
		{//need Download
			DownloadFilePath.Add(PM.fbxFilePath);
		}
		else
		{
			if (!PM.md5.IsEmpty())
			{
				FString OutMd5;
				int64 OutSize;
				if (ACatalogPlayerController::GetFileMD5AndSize(AbsPath, OutMd5, OutSize))
				{
					if (!OutMd5.Equals(PM.md5))
					{
						DownloadFilePath.Add(PM.fbxFilePath);
					}
				}
			}
		}
		
	}

	return DownloadFilePath.Num() > 0;
}

#define MEB_MATERIAL_TOP_PATH TEXT("ImportMaterial")
bool UViewManager::AnalysisData_Material(
	const TArray<FCSMapData>& Datas, const int64& FileID,
	const FString& NetMatParamFilePath, const FString& MatParamFileMd5,
	TArray<FString>& FilePath, TArray<FString>& DownloadFilePath
)
{
	FString RelFolder = FPaths::Combine(MEB_MATERIAL_TOP_PATH, FString::FromInt(FileID));
	const FString AbsFolderPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelFolder));
	if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*AbsFolderPath))
	{
		FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*AbsFolderPath);
	}

	//param file 
	FString ParamFileName = FPaths::GetCleanFilename(NetMatParamFilePath);
	FString RelParamFilePath = FPaths::Combine(RelFolder, ParamFileName);
	FString AbsParamFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelParamFilePath));
	bool bAllDownload = false;
	FilePath.AddUnique(RelParamFilePath);
	if (!FPaths::FileExists(AbsParamFilePath))
	{
		bAllDownload = true;
		//FilePath.Add(RelParamFilePath);
		DownloadFilePath.AddUnique(NetMatParamFilePath);
	}
	else
	{
		if (!MatParamFileMd5.IsEmpty())
		{//check md5
			FString OutMd5;
			int64 OutSize;
			if (ACatalogPlayerController::GetFileMD5AndSize(AbsParamFilePath, OutMd5, OutSize))
			{
				if (!OutMd5.Equals(MatParamFileMd5))
				{
					bAllDownload = true;
					DownloadFilePath.AddUnique(NetMatParamFilePath);
				}
			}
		}
	}

#define CATALOG_NET_FLAG TEXT("http")
	UResourceSubsystem* Resource = UResourceSubsystem::GetInstance();
	if (Resource == nullptr)
	{
		return DownloadFilePath.Num() > 0;
	}
	for (const auto PM : Datas)
	{
		bool IsMat = Resource->IsMatFile(PM.materialValue);
		if ((PM.materialValue.IsEmpty() || !PM.materialValue.Contains(CATALOG_NET_FLAG)) && !IsMat) continue;

		//FString MatFileName = FPaths::GetCleanFilename(PM.materialValue);
		//FString MatFileName = FPaths::GetCleanFilename(ViewData.mapsImg);
		FString MatFileName = FPaths::GetCleanFilename(PM.materialValue);
		FString RelMatFilePath = FPaths::Combine(RelFolder, MatFileName);
		FString AbsFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelMatFilePath));
		FilePath.Insert(RelMatFilePath, 0);
		if (bAllDownload || !FPaths::FileExists(AbsFilePath) || PM.md5.IsEmpty())
		{//check md5
			DownloadFilePath.Insert(PM.materialValue, 0);
		}
		else
		{
			FString OutMd5;
			int64 OutSize;
			if (ACatalogPlayerController::GetFileMD5AndSize(AbsFilePath, OutMd5, OutSize))
			{
				if (!OutMd5.Equals(PM.md5))
				{
					DownloadFilePath.Insert(PM.materialValue, 0);
				}
			}
		}
	}
#undef CATALOG_NET_FLAG

	return DownloadFilePath.Num() > 0;
}
#undef MEB_MATERIAL_TOP_PATH

bool UViewManager::AnalysisData_Fbx(const TArray<FCSModelInfo>& Datas, const FString& FileName, TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	//TODO : FBX
	for (const auto PM : Datas)
	{
		if (PM.fbxFilePath.IsEmpty()) continue;
		FString FolderPath = PM.fbxFilePath;
		//path folder
		FolderPath.RemoveFromStart(OSS_MODEL_MAT_PATH);
		FString AbsFolderPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FolderPath));
		if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*AbsFolderPath))
		{
			FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*AbsFolderPath);
		}
		FString RelPath = FolderPath + FileName + TEXT("-D.pak");
		FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + RelPath);
		UE_LOG(LogTemp, Warning, TEXT("view pak file path : [%s][%s]"), *RelPath, *AbsPath);
		FilePath.Add(RelPath);
		if (!FPaths::FileExists(AbsPath) || PM.md5.IsEmpty())
		{//need Download
			DownloadFilePath.Add(PM.fbxFilePath);
		}

	}
	return DownloadFilePath.Num() > 0;
}

void UViewManager::LoadMesh(const TArray<FString>& ParseFilePaths)
{
	UpdateProcess(0.9f);

	if (CurViewType == ECatalogViewItemType::E_PAK)
	{
		LoadMesh_Pak(ParseFilePaths);
	}
	else if (CurViewType == ECatalogViewItemType::E_FBX)
	{
		LoadMesh_Fbx(ParseFilePaths);
	}
	else if (CurViewType == ECatalogViewItemType::E_Material)
	{
		LoadMesh_Material(ParseFilePaths);
	}
}

void UViewManager::LoadMesh_Pak(const TArray<FString>& ParseFilePaths)
{
	for (const auto PFP : ParseFilePaths)
	{
		FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + PFP);
		UPakFileManagerSubsystem::GetInstance()->MountPakFile(AbsPath, true);

		if (AImportPakBaseClass* NewActor = FCatalogFunctionLibrary::LoadActorFormPak<AImportPakBaseClass, AImportPakBaseClass>(this->GetWorld(), ViewData.refPath))
		{
			NewActor->SetActorLocation(FVector::ZeroVector);
			NewActor->SetActorHiddenInGame(false);
			PakActors.Add(NewActor);
		}
	}

	EndProcess();

}

void UViewManager::LoadMesh_Pak_Inner(FString FilePath)
{
	UClass* ClassFound = FCatalogFunctionLibrary::PhraseClassFromRefPath<AImportPakBaseClass>(ViewData.refPath);
	if (ClassFound != nullptr)
	{
		AImportPakBaseClass* NewActor = GWorld->SpawnActor<AImportPakBaseClass>(ClassFound);
		UE_LOG(LogClass, Log, TEXT("LoadMesh_Pak -- Editor -- Success"));
	}

}

void UViewManager::LoadMesh_Fbx(const TArray<FString>& ParseFilePaths)
{
}

void UViewManager::LoadMesh_Material(const TArray<FString>& ParseFilePaths)
{
	FString Mark = ViewData.folderId.IsEmpty() ? FString::FromInt(ViewData.id) : ViewData.folderId;
	MatInstance = UResourceSubsystem::GetInstance()->GetMaterialInsDynamic_Web(Mark);
	MatShowComp->SetMaterial(0, MatInstance);

	EndProcess();
	
#if 0
#define BASE_MATERIAL_PATH TEXT("Material'/Game/Materials/OLOMaterial/M_BaseMatLib.M_BaseMatLib'")
#define DIFFUSE_MARK TEXT("diffuse")
	/*
	*  @@ analysis material data
	*  @@ load basic material 
	*  @@ load diffuse image
	*  @@ set diffuse image and params to material
	*/

	UMaterial* BaseMat = LoadObject<UMaterial>(nullptr, BASE_MATERIAL_PATH);
	MatInstance = UMaterialInstanceDynamic::Create(BaseMat, nullptr);

	
	/*
	*  @@ set diffuse image and params to material
	*  @@ index last : params file  path
	*  @@ index 0 ~ (last -1) : one in array diffuse image path 
	*/

	if(ParseFilePaths.Num() > 1)
	{
		const FString JsonFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), ParseFilePaths.Last()));
		FString JsonStr = TEXT("");
		if (FFileHelper::LoadFileToString(JsonStr, *JsonFileAbsPath))
		{
			TSharedPtr<FJsonObject> JsonObject;
			TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonStr);
			if (FJsonSerializer::Deserialize(Reader, JsonObject))
			{
				const TSharedPtr<FJsonValue> RoughnessJSON = JsonObject->GetField<EJson::Number>(FString(TEXT("roughness")));
				if (RoughnessJSON.IsValid())
				{
					const double CurRoughness = RoughnessJSON->AsNumber();
					UE_LOG(LogTemp, Warning, TEXT("[%s] RoughnessJSON [%f]"), *JsonFileAbsPath, CurRoughness);
					MatInstance->SetScalarParameterValue(TEXT("Roughness"), CurRoughness);
				}
			}
			else
			{
				const FString JsonFileError = FString::Printf(TEXT("Json File [%s] "), *ParseFilePaths.Last()).Append(TEXT("Format Error!"));
				UI_POP_WINDOW_ERROR(JsonFileError);
			}
		}
	}
	else
		MatInstance->SetScalarParameterValue(TEXT("Roughness"), 0.5f);

	//if (ParseFilePaths.IsValidIndex(0))
	for(int32 i = 0; i < ParseFilePaths.Num(); ++i)
	{
		FString TextPath = (ParseFilePaths[i].EndsWith(TEXT(".jpg")) || ParseFilePaths[i].EndsWith(TEXT(".png"))) ? 
			ParseFilePaths[i] : TEXT("");
		if(TextPath.IsEmpty() || !TextPath.Contains(DIFFUSE_MARK))
			continue;

		const FString AbsPath = FPaths::Combine(FPaths::ProjectContentDir(), TextPath);
		UTexture2D* DiffuseTex = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);

		if (DiffuseTex)
		{
			MatInstance->SetTextureParameterValue(TEXT("Tex_BaseColor"), DiffuseTex);
		}

		if(ParseFilePaths.Num() > 1)
		{
			const FString JsonFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), ParseFilePaths.Last()));
			FString JsonStr = TEXT("");
			if (FFileHelper::LoadFileToString(JsonStr, *JsonFileAbsPath))
			{
				TSharedPtr<FJsonObject> JsonObject;
				TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonStr);
				if (FJsonSerializer::Deserialize(Reader, JsonObject))
				{
					const TSharedPtr<FJsonValue> RoughnessJSON = JsonObject->GetField<EJson::Number>(FString(TEXT("roughness")));
					if (RoughnessJSON.IsValid())
					{
						const double CurRoughness = RoughnessJSON->AsNumber();
						UE_LOG(LogTemp, Warning, TEXT("[%s] RoughnessJSON [%f]"), *JsonFileAbsPath, CurRoughness);
						MatInstance->SetScalarParameterValue(TEXT("Roughness"), CurRoughness);
					}
				}
				else
				{
					const FString JsonFileError = FString::Printf(TEXT("Json File [%s] "), *ParseFilePaths.Last()).Append(TEXT("Format Error!"));
					UI_POP_WINDOW_ERROR(JsonFileError);
				}
			}
		}
		else
			MatInstance->SetScalarParameterValue(TEXT("Roughness"), 0.5f);

		MatShowComp->SetMaterial(0, MatInstance);
	}
	

	EndProcess();

#undef BASE_MATERIAL_PATH
#undef DIFFUSE_MARK

#endif
}

void UViewManager::StartProcess()
{
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	ULoadPageWidget::GetInstance()->InitLoadPage();
	ULoadPageWidget::GetInstance()->SetLoadingPercent(0.1f);
}

void UViewManager::UpdateProcess(float InProcess)
{
	ULoadPageWidget::GetInstance()->SetLoadingPercent(InProcess);
}

void UViewManager::EndProcess()
{
	ULoadPageWidget::GetInstance()->SetVisibility(ESlateVisibility::Collapsed);
}

void UViewManager::UpdateShowActorStateForMaterialMap()
{
	TArray<AActor*> Sphere;

	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), FName("SphereMesh"), Sphere);
	if (1 == Sphere.Num())
	{
		Sphere[0]->SetActorHiddenInGame(false);
		const AStaticMeshActor* StaticMesh = Cast<AStaticMeshActor>(Sphere[0]);
		if (IS_OBJECT_PTR_VALID(StaticMesh))
		{
			MatShowComp = StaticMesh->GetStaticMeshComponent();
			MatShowComp->SetMaterial(0, MatInstance);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Mat SphereMesh is not valid"));
		}
	}
}

void UViewManager::ClearPakShow()
{
	for (auto& PA : PakActors)
	{
		if (IS_ACTOR_PTR_VALID(PA))
		{
			PA->SetActorHiddenInGame(true);
			PA->Destroy();
		}
	}
	PakActors.Empty();
}

void UViewManager::HiddenMatShow()
{
	TArray<AActor*> Sphere;

	UGameplayStatics::GetAllActorsWithTag(ACatalogPlayerController::Get(), FName("SphereMesh"), Sphere);
	if (Sphere.Num() > 0)
	{
		Sphere[0]->SetActorHiddenInGame(true);
	}
	Sphere.Empty();
}

#undef OSS_MODEL_MAT_PATH

void UViewManager::InitializeManager()
{
	//Init View Layout
	if (!ViewLayoutWidget)
	{
		ViewLayoutWidget = UViewLayoutWidget::Create();
		ViewLayoutWidget->AddToViewport(MainUIZOrder);
		ViewLayoutWidget->SetVisibility(ESlateVisibility::Visible);
	}
	if (ViewLayoutWidget)
	{
		ViewLayoutWidget->SetVisibility(ESlateVisibility::Visible);
		if(!ViewLayoutWidget->ViewLayoutExitDelegate.IsBound())
			ViewLayoutWidget->ViewLayoutExitDelegate.BindUObject(this, &UViewManager::OnViewLayoutActionHandler);
	}

	BindDelegate();

	UpdateProcess(0.5f);
	if (CurViewType == ECatalogViewItemType::E_Material)
	{
		UpdateShowActorStateForMaterialMap();
	}
	else
	{
		HiddenMatShow();
	}

	InitManager(ViewData);

	WindowSizeChanged();
}

void UViewManager::UninitializeManager()
{
	if (ViewLayoutWidget)
	{
		ViewLayoutWidget->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (CurViewType == ECatalogViewItemType::E_PAK || CurViewType == ECatalogViewItemType::E_FBX)
	{
		ClearPakShow();
	}
	else if (CurViewType == ECatalogViewItemType::E_Material)
	{
		HiddenMatShow();
	}
}

void UViewManager::WindowSizeChanged()
{
	ULayoutBaseWidget::ResizeSceneViewport(FVector2D::ZeroVector, FVector2D::ZeroVector, FVector2D::ZeroVector);
}
