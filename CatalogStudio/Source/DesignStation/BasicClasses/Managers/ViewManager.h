// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ManagerBase.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "DesignStation/BasicClasses/BackgroundAxis.h"
#include "DesignStation/BasicClasses/ImportPakBaseClass.h"
#include "DesignStation/UI/ViewUI/ViewLayoutWidget.h"
#include "ViewManager.generated.h"


UENUM(BlueprintType)
enum class ECatalogViewItemType : uint8
{
	E_None = 0		UMETA(DisplayName = "None"),
	E_PAK			UMETA(DisplayName = "Pak File"),
	E_FBX			UMETA(DisplayName = "FBX File"),
	E_Material		UMETA(DisplayName = "Material File")
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UViewManager : public UManagerBase
{
	GENERATED_BODY()

public:
	void Init(const FCSModelMatData& InData);

	void InitManager(const FCSModelMatData& InData);
	bool IsModelType() const;

#pragma region Net_View_Manager

private:
	void BindDelegate();
	void UnBindDelegate();

	/*
	*   @@ analysis data to get need download files
	*   @@ FilePath : file to parse
	*   @@ DownloadFilePath : need download file
	*   @@ RetVal : need download file ?
	*/
	bool AnalysisDataToGetNeedDownloadFile(
		TArray<FString>& FilePath, 
		TArray<FString>& DownloadFilePath
	);
	 
	// download
	void SendDownloadNecessary(const TArray<FString>& Paths);
	UFUNCTION()
	void OnDownloadFileCompleteHandler(const FString& UUID, bool Success, const TArray<FString>& FilePaths);

	//bind function to layout widget
	UFUNCTION()
	void OnViewLayoutActionHandler(EViewLayoutActionType InAction);

private:
	void InitViewType(const FCSModelMatData& InData);

	void InitActorAxis();

	bool AnalysisData_Pak(const TArray<FCSModelInfo>& Datas, const FString& PakPath,
		TArray<FString>& FilePath,
		TArray<FString>& DownloadFilePath
	);
	bool AnalysisData_Material(const TArray<FCSMapData>& Datas, const int64& FileID,
		const FString& NetMatParamFilePath, const FString& MatParamFileMd5,
		TArray<FString>& FilePath,
		TArray<FString>& DownloadFilePath
	);
	bool AnalysisData_Fbx(const TArray<FCSModelInfo>& Datas, const FString& FileName,
		TArray<FString>& FilePath,
		TArray<FString>& DownloadFilePath
	);

	void LoadMesh(const TArray<FString>& ParseFilePaths);
	UFUNCTION()
	void LoadMesh_Pak(const TArray<FString>& ParseFilePaths);
	UFUNCTION()
	void LoadMesh_Pak_Inner(FString FilePath);
	void LoadMesh_Fbx(const TArray<FString>& ParseFilePaths);
	void LoadMesh_Material(const TArray<FString>& ParseFilePaths);

	/*
	*  @@ proess
	* 	
	*/
	void StartProcess();
	void UpdateProcess(float InProcess);
	void EndProcess();

	/*
	*  @@ Material map set
	*/
	void UpdateShowActorStateForMaterialMap();

	/*
	*  @@ clear show
	*/
	void ClearPakShow();
	void HiddenMatShow();

private:
	//pak
	UPROPERTY()
	TArray<AImportPakBaseClass*> PakActors;

	//material
	UPROPERTY()
	UStaticMeshComponent* MatShowComp;
	UPROPERTY()
	UMaterialInstanceDynamic* MatInstance;

	UPROPERTY()
	ABackgroundAxis* AxisActor;

	UPROPERTY()
	UViewLayoutWidget* ViewLayoutWidget;

	UPROPERTY()
	FCSModelMatData ViewData;

	UPROPERTY()
	ECatalogViewItemType CurViewType;

	UPROPERTY()
	TArray<FString> ParsePath;

	UPROPERTY()
	FString DownloadUUID;
#pragma endregion

protected:

	virtual void InitializeManager() override;

	virtual void UninitializeManager() override;

	virtual void WindowSizeChanged() override;

};


