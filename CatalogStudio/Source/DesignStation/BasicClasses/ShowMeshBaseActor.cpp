// Fill out your copyright notice in the Description page of Project Settings.

#include "ShowMeshBaseActor.h"
#include "DesignStation/SQLite/MaterialRelated/MaterialOperationLibrary.h"
#include "DesignStation/SQLite/MaterialRelated/CustomMaterialTableOperatorLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"

const extern FString PARAM_ZLHSW;

// Sets default values
AShowMeshBaseActor::AShowMeshBaseActor()
	:PostVolume(nullptr)
	, WhiteMaterial(nullptr)
	, CurrentOultineType(EOutlineType::EOutlineAndMaterial)
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;
	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
}

// Called when the game starts or when spawned
void AShowMeshBaseActor::BeginPlay()
{
	AActor::BeginPlay();
	WhiteMaterial = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/WhiteM.WhiteM'"));
	//UEnvironmentConfigLibrary::LoadEnvironmentConfig(EnvironmentConfig);
	TArray<AActor*> PostVolumes;
	UGameplayStatics::GetAllActorsOfClass(this, APostProcessVolume::StaticClass(), PostVolumes);
	if (PostVolumes.Num() > 0)
	{
		PostVolume = Cast<APostProcessVolume>(PostVolumes[0]);
	}
}

void AShowMeshBaseActor::SwitchToNextOutlineType()
{
	int32 NextOutlineType = static_cast<int32>(CurrentOultineType) + 1;
	NextOutlineType %= 4;
	CurrentOultineType = static_cast<EOutlineType>(NextOutlineType);
	this->ShowOutline();
	//this->ApplyEnvironmentConfig();
}

void AShowMeshBaseActor::ApplyEnvironmentConfig()
{
	//if (EOutlineType::EOutlineAndWhiteMaterial == CurrentOultineType)
	//{
	//	this->ApplyEnvironmentConfig(EnvironmentConfig[TEXT("FramworkWhite")]);
	//}
	//else if (EOutlineType::EOnlyOutline == CurrentOultineType)
	//{
	//	this->ApplyEnvironmentConfig(EnvironmentConfig[TEXT("OnlyFramwork")]);
	//}
	//else if (EOutlineType::EOutlineAndMaterial == CurrentOultineType)
	//{
	//	this->ApplyEnvironmentConfig(EnvironmentConfig[TEXT("OutlineAndMaterial")]);
	//}
	//else if (EOutlineType::EOnlyMaterial == CurrentOultineType)
	//{
	//	this->ApplyEnvironmentConfig(EnvironmentConfig[TEXT("OnlyMaterial")]);
	//}
}

void AShowMeshBaseActor::ApplyEnvironmentConfig(const FEnvironmentConfigModuleItem& InEnvironmentSetting)
{
	for (auto& Iter : InEnvironmentSetting.CommandLineParameters)
	{
		FString CommandLine = FString::Printf(TEXT("%s %s"), *Iter.ParameterName, *Iter.ParameterValue);
		ACatalogPlayerController::Get()->ConsoleCommand(CommandLine);
	}
	for (auto& Iter : InEnvironmentSetting.PostVolumeParameters)
	{
		if (nullptr != PostVolume)
		{
			//Film
			if (Iter.ParameterName.Equals(TEXT("Film_Slop"), ESearchCase::IgnoreCase))
				PostVolume->Settings.FilmSlope = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Film_Toe"), ESearchCase::IgnoreCase))
				PostVolume->Settings.FilmToe = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Film_Shoulder"), ESearchCase::IgnoreCase))
				PostVolume->Settings.FilmShoulder = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Film_BlackClip"), ESearchCase::IgnoreCase))
				PostVolume->Settings.FilmBlackClip = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Film_WhiteClip"), ESearchCase::IgnoreCase))
				PostVolume->Settings.FilmWhiteClip = FCString::Atof(*Iter.ParameterValue);
			//Lens | Chromatic Aberration
			else if (Iter.ParameterName.Equals(TEXT("ChromaticAberration_Intensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.SceneFringeIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("ChromaticAberration_StartOffset"), ESearchCase::IgnoreCase))
				PostVolume->Settings.ChromaticAberrationStartOffset = FCString::Atof(*Iter.ParameterValue);
			//Lens | Bloom
			else if (Iter.ParameterName.Equals(TEXT("Bloom_Method"), ESearchCase::IgnoreCase))
				PostVolume->Settings.BloomMethod = static_cast<EBloomMethod>(FCString::Atoi(*Iter.ParameterValue));
			else if (Iter.ParameterName.Equals(TEXT("Bloom_Intensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.BloomIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Bloom_Threshold"), ESearchCase::IgnoreCase))
				PostVolume->Settings.BloomThreshold = FCString::Atof(*Iter.ParameterValue);
			//Lens | Exposure
			else if (Iter.ParameterName.Equals(TEXT("Exposure_MeteringMode"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AutoExposureMethod = static_cast<EAutoExposureMethod>(FCString::Atoi(*Iter.ParameterValue));
			else if (Iter.ParameterName.Equals(TEXT("Exposure_MinBrightness"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AutoExposureMinBrightness = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Exposure_MaxBrightness"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AutoExposureMaxBrightness = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Exposure_SpeedUp"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AutoExposureSpeedUp = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("Exposure_SpeedDown"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AutoExposureSpeedDown = FCString::Atof(*Iter.ParameterValue);
			//Lens | Lens Flares
			else if (Iter.ParameterName.Equals(TEXT("LensFlares_Intensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.LensFlareIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("LensFlares_Tint"), ESearchCase::IgnoreCase))
				PostVolume->Settings.LensFlareTint = FLinearColor(FColor::FromHex(Iter.ParameterValue));
			else if (Iter.ParameterName.Equals(TEXT("LensFlares_BokehSize"), ESearchCase::IgnoreCase))
				PostVolume->Settings.LensFlareBokehSize = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("LensFlares_Threshold"), ESearchCase::IgnoreCase))
				PostVolume->Settings.LensFlareThreshold = FCString::Atof(*Iter.ParameterValue);
			//Lens | Lens Flares
			else if (Iter.ParameterName.Equals(TEXT("ImageEffects_VignetteIntensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.VignetteIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("ImageEffects_GrainJitter"), ESearchCase::IgnoreCase))
				PostVolume->Settings.GrainIntensity_DEPRECATED = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("ImageEffects_GrainIntensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.GrainIntensity_DEPRECATED = FCString::Atof(*Iter.ParameterValue);
			//RenderingFeatures | Ambient Occlusion
			else if (Iter.ParameterName.Equals(TEXT("AmbientOcclusion_Intensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AmbientOcclusionIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("AmbientOcclusion_Radius"), ESearchCase::IgnoreCase))
				PostVolume->Settings.AmbientOcclusionRadius = FCString::Atof(*Iter.ParameterValue);
			//RenderingFeatures | Ambient Occlusion
			else if (Iter.ParameterName.Equals(TEXT("GlobalIllumination_IndirectLightingColor"), ESearchCase::IgnoreCase))
				PostVolume->Settings.IndirectLightingColor = FLinearColor(FColor::FromHex(Iter.ParameterValue));
			else if (Iter.ParameterName.Equals(TEXT("GlobalIllumination_IndirectLightingIntensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.IndirectLightingIntensity = FCString::Atof(*Iter.ParameterValue);
			//RenderingFeatures | Motion Blur
			else if (Iter.ParameterName.Equals(TEXT("MotionBlur_Amount"), ESearchCase::IgnoreCase))
				PostVolume->Settings.MotionBlurAmount = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("MotionBlur_Max"), ESearchCase::IgnoreCase))
				PostVolume->Settings.MotionBlurMax = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("MotionBlur_PerObjectSize"), ESearchCase::IgnoreCase))
				PostVolume->Settings.MotionBlurPerObjectSize = FCString::Atof(*Iter.ParameterValue);
			//RenderingFeatures | Motion Blur
			else if (Iter.ParameterName.Equals(TEXT("ScreenSpaceReflections_Intensity"), ESearchCase::IgnoreCase))
				PostVolume->Settings.ScreenSpaceReflectionIntensity = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("ScreenSpaceReflections_Quality"), ESearchCase::IgnoreCase))
				PostVolume->Settings.ScreenSpaceReflectionQuality = FCString::Atof(*Iter.ParameterValue);
			else if (Iter.ParameterName.Equals(TEXT("ScreenSpaceReflections_MaxRoughness"), ESearchCase::IgnoreCase))
				PostVolume->Settings.ScreenSpaceReflectionMaxRoughness = FCString::Atof(*Iter.ParameterValue);
			else
				UE_LOG(LogTemp, Log, TEXT("-------  Unknown environment %s  ------"), *Iter.ParameterName);
		}
	}
}

UMaterialInstanceDynamic* AShowMeshBaseActor::GetMaterial(const FString& InFolderId)
{
	if (MaterialResource.Contains(InFolderId) && IS_OBJECT_PTR_VALID(MaterialResource[InFolderId])) return MaterialResource[InFolderId];
	FFolderTableData MaterialFile;
	UFolderTableOperatorLibrary::RetriveFileByFolderId(InFolderId, MaterialFile);
	if (false == MaterialFile.IsValid()) return nullptr;
	FCustomMaterialTableData CM;
	FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(MaterialFile.id, CM);
	UMaterialInstanceDynamic* MeshMaterial = nullptr;
	if (false == CM.ref_path.IsEmpty())
	{
		MeshMaterial = FMaterialOperationLibrary::LoadMaterialByMaterialRef(CM.ref_path);
		TArray<FParameterData> Parameters;
		FLocalDatabaseParameterLibrary::RetriveFileParametersByID(CM.folder_id, Parameters);
		int32 UVIndex = Parameters.IndexOfByPredicate([](const FParameterData& InData) {return InData.Data.name.Equals(PARAM_ZLHSW,ESearchCase::CaseSensitive); });
		if (UVIndex >= 0)
		{
			int32 MatUV = FCString::Atoi(*Parameters[UVIndex].Data.value);
			MeshMaterial->SetScalarParameterValue(FName(TEXT("UVRotation")), MatUV == 0 ? 0.0f : 90.0f);
		}

		MaterialResource.Add(InFolderId, MeshMaterial);
		return MeshMaterial;
	}
	return nullptr;
}