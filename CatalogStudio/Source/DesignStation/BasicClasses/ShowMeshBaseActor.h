// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DesignStation/Geometry/ShowComponentDisplayUnitItem.h"
#include "CustomConfig/Environment/EnvironmentConfigLibrary.h"
#include "Runtime/Engine/Classes/Engine/PostProcessVolume.h"
#include "ShowMeshBaseActor.generated.h"

typedef UMaterialInstanceDynamic* UMaterialInstanceDynamicPtr;

UCLASS()
class DESIGNSTATION_API AShowMeshBaseActor : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AShowMeshBaseActor();

	void SwitchToNextOutlineType();

	virtual void DestroyDisplayer() { this->Destroy(); }

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	virtual void ShowOutline() {}

	void ApplyEnvironmentConfig();

	void ApplyEnvironmentConfig(const FEnvironmentConfigModuleItem& InEnvironmentSetting);

	UMaterialInstanceDynamic* GetMaterial(const FString& InFolderId);

protected:

	UPROPERTY()
		TMap<FString, UMaterialInstanceDynamic*> MaterialResource;

	UPROPERTY()
		APostProcessVolume* PostVolume;

	UPROPERTY()
		UMaterial* WhiteMaterial;

	EOutlineType CurrentOultineType;

};
