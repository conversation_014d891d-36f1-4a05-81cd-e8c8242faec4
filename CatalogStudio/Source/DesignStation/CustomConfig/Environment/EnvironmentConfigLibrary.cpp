// Fill out your copyright notice in the Description page of Project Settings.

#include "EnvironmentConfigLibrary.h"
#include <iostream>
#include <fstream>
#include <string>
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"


FEnvironmentConfigItem::FEnvironmentConfigItem()
	:ParameterName(TEXT(""))
	, ParameterValue(TEXT(""))
{
}

FEnvironmentConfigItem::FEnvironmentConfigItem(const FString& InParameterName, const FString& InParameterValue)
:ParameterName(InParameterName)
,ParameterValue(InParameterValue)
{
}

FEnvironmentConfigModuleItem::FEnvironmentConfigModuleItem()
{}

FEnvironmentConfigModuleItem::FEnvironmentConfigModuleItem(const FString& InModuleName)
{}

bool UEnvironmentConfigLibrary::LoadEnvironmentConfig(TMap<FString, FEnvironmentConfigModuleItem>& OutModuleConfig)
{
	TArray<FString> ConfigLines;
	bool Res = UEnvironmentConfigLibrary::ReadConfigFileLineByLine(TEXT("EnvironmentConfig.bin"), ConfigLines);
	if (!Res)
		return false;
	FString ModuleName(TEXT(""));
	for (auto& Iter : ConfigLines)
	{
		Iter.RemoveSpacesInline();
		if (UEnvironmentConfigLibrary::IsModuleName(Iter))
		{
			ModuleName = UEnvironmentConfigLibrary::PharseModuleName(Iter);
			if (!OutModuleConfig.Contains(ModuleName))
				OutModuleConfig.Add(ModuleName, FEnvironmentConfigModuleItem());
		}
		else
		{
			FString ParameterType(TEXT(""));
			FString ParameterName(TEXT(""));
			FString ParameterValue(TEXT(""));
			Res = UEnvironmentConfigLibrary::PharseParameterInfo(Iter, ParameterType, ParameterName, ParameterValue);
			if (!Res)
				continue;
			UE_LOG(LogTemp, Log, TEXT("Parameter name is: %s  value is:  %s  "), *ParameterName, *ParameterValue);
			if (ParameterType.Equals(TEXT("Cmd"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig[ModuleName].CommandLineParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("DLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig[ModuleName].DirectLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("PLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig[ModuleName].PointLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("SLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig[ModuleName].SpotLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("PPV"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig[ModuleName].PostVolumeParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
		}
	}
	return true;
}

bool UEnvironmentConfigLibrary::LoadEnvironmentConfig(const FString& InModuleName, FEnvironmentConfigModuleItem& OutModuleConfig)
{
	TArray<FString> ConfigLines;
	bool Res = UEnvironmentConfigLibrary::ReadConfigFileLineByLine(TEXT("EnvironmentConfig.bin"), ConfigLines);
	if (!Res)
		return false;
	Res = false;
	for (auto& Iter : ConfigLines)
	{
		Iter.RemoveSpacesInline();
		if (UEnvironmentConfigLibrary::IsModuleName(Iter))
		{
			if (InModuleName.Equals(UEnvironmentConfigLibrary::PharseModuleName(Iter)))
			{
				Res = true;
				continue;
			}
			else if (Res)
			{
				return true;
			}
		}
		else if (Res)
		{
			FString ParameterType(TEXT(""));
			FString ParameterName(TEXT(""));
			FString ParameterValue(TEXT(""));
			Res = UEnvironmentConfigLibrary::PharseParameterInfo(Iter, ParameterType, ParameterName, ParameterValue);
			if (!Res)
				continue;
			UE_LOG(LogTemp, Log, TEXT("Parameter name is: %s  value is:  %s  "), *ParameterName, *ParameterValue);
			if (ParameterType.Equals(TEXT("Cmd"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig.CommandLineParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("DLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig.DirectLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("PLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig.PointLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("SLight"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig.SpotLightParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
			else if (ParameterType.Equals(TEXT("PPV"), ESearchCase::IgnoreCase))
			{
				OutModuleConfig.PostVolumeParameters.Add(FEnvironmentConfigItem(ParameterName, ParameterValue));
			}
		}
	}
	return Res;
}

bool UEnvironmentConfigLibrary::ReadConfigFileLineByLine(const FString& InFilePath, TArray<FString>& OutFileContent)
{
	FString ConfigFilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectConfigDir() + InFilePath);
	if (FPaths::FileExists(ConfigFilePath))
	{
		std::string FilePath(TCHAR_TO_UTF8(*ConfigFilePath));
		std::ifstream infile;
		infile.open(FilePath.data());   //将文件流对象与文件连接起来 
		if (infile.is_open())
		{
			OutFileContent.Empty();
			std::string s;
			while (getline(infile, s))
			{
				if (s.length() <= 2)
					continue;
				OutFileContent.Push(FString(s.c_str()));
			}
			infile.close();             //关闭文件输入流 
			return true;
		}
	}
	return false;
}

bool UEnvironmentConfigLibrary::IsModuleName(const FString& InStr)
{
	return InStr.Len() > 2 && '[' == InStr[0] && ']' == InStr[InStr.Len() - 1];
}

FString UEnvironmentConfigLibrary::PharseModuleName(const FString& InStr)
{
	if (InStr.Len() > 2)
		return InStr.Mid(1, InStr.Len() - 2);
	return TEXT("");
}

bool UEnvironmentConfigLibrary::PharseParameterInfo(const FString& InStr, FString& OutType, FString&OutName, FString& OutValue)
{
	FString Right(TEXT(""));
	bool Res = InStr.Split(TEXT("_"), &OutType, &Right);
	if (!Res)
		return false;
	Res = Right.Split(TEXT("="), &OutName, &OutValue);
	return Res;
}