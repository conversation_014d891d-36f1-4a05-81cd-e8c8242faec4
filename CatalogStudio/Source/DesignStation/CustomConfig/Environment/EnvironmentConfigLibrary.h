// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "EnvironmentConfigLibrary.generated.h"


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FEnvironmentConfigItem
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY()
		FString						ParameterName;

	UPROPERTY()
		FString						ParameterValue;

public:

	FEnvironmentConfigItem();

	explicit FEnvironmentConfigItem(const FString& InParameterName, const FString& InParameterValue);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FEnvironmentConfigModuleItem
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY()
		TArray<struct FEnvironmentConfigItem>	CommandLineParameters;

	UPROPERTY()
		TArray<struct FEnvironmentConfigItem>	SpotLightParameters;

	UPROPERTY()
		TArray<struct FEnvironmentConfigItem>	PointLightParameters;

	UPROPERTY()
		TArray<struct FEnvironmentConfigItem>	DirectLightParameters;

	UPROPERTY()
		TArray<struct FEnvironmentConfigItem>	PostVolumeParameters;

public:

	FEnvironmentConfigModuleItem();

	explicit FEnvironmentConfigModuleItem(const FString& InModuleName);
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UEnvironmentConfigLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:

	static bool LoadEnvironmentConfig(TMap<FString, FEnvironmentConfigModuleItem>& OutModuleConfig);
	
	static bool LoadEnvironmentConfig(const FString& InModuleName, FEnvironmentConfigModuleItem& OutModuleConfig);

	static bool ReadConfigFileLineByLine(const FString& InFilePath, TArray<FString>& OutFileContents);

private:

	static bool IsModuleName(const FString& InStr);

	static FString PharseModuleName(const FString& InStr);

	static bool PharseParameterInfo(const FString& InStr, FString& OutType, FString&OutName, FString& OutValue);
};
