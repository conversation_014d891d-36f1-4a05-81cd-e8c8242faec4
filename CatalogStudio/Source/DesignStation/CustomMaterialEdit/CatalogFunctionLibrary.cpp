// Fill out your copyright notice in the Description page of Project Settings.

#include "CatalogFunctionLibrary.h"

#include "DesktopPlatformModule.h"
#include "Runtime/Core/Public/Misc/CoreDelegates.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"
#include "Runtime/Core/Public/Modules/ModuleManager.h"
#include "Runtime/ImageWrapper/Public/IImageWrapper.h"
#include "Runtime/ImageWrapper/Public/IImageWrapperModule.h"
#include "Runtime/Slate/Public/Framework/Application/SlateApplication.h"


#define LOCTEXT_NAMESPACE "CatalogFunctionLibrary"

UStaticMesh* FCatalogFunctionLibrary::LoadStaticMeshFromPak(const FString& PakFilePath, const FString& RefPath)
{
	FString ResourceType(TEXT(""));
	FString ResourcePath(TEXT(""));
	bool Res = FCatalogFunctionLibrary::PhraseResourceTypeAndPackagePathFromRefPath(RefPath, ResourceType, ResourcePath);
	if (!(Res && ResourceType.Equals(TEXT("StaticMesh"), ESearchCase::IgnoreCase))) return nullptr;
	UObject* LoadedObj = StaticLoadObject(UObject::StaticClass(), nullptr, *ResourcePath);
	UStaticMesh* SM = Cast<UStaticMesh>(LoadedObj);
	UE_LOG(LogTemp, Log, TEXT("FCatalogFunctionLibrary::LoadStaticMeshFromPak from %s %s"), *ResourcePath, nullptr == SM ? TEXT("failed") : TEXT("success"));
	return SM;
}

bool FCatalogFunctionLibrary::PhraseClassNameAndPackagePathFromRefPath(const FString& InRefPath, FString& ClassName, FString& PackagePath)
{
	if (InRefPath.IsEmpty()) return false;
	FString LefStr(TEXT(""));
	FString RightStr(TEXT(""));
	bool Res = InRefPath.Split(TEXT("."), &LefStr, &RightStr);
	if (!Res) return false;
	FString LefPathStr(TEXT(""));
	FString RightPathStr(TEXT(""));
	Res = LefStr.Split(TEXT("'"), &LefPathStr, &RightPathStr);
	if (!Res) return false;
	PackagePath = RightPathStr;
	ClassName = RightStr.Left(RightStr.Len() - 1) + TEXT("_C");
	return true;
}

bool FCatalogFunctionLibrary::PhraseResourceTypeAndPackagePathFromRefPath(const FString& InRefPath, FString& ResourceType, FString& PackagePath)
{
	if (InRefPath.IsEmpty()) return false;
	FString LefStr(TEXT(""));
	FString RightStr(TEXT(""));
	bool Res = InRefPath.Split(TEXT("."), &LefStr, &RightStr);
	if (!Res) return false;
	FString LefPathStr(TEXT(""));
	FString RightPathStr(TEXT(""));
	Res = RightStr.Split(TEXT("'"), &LefPathStr, &RightPathStr);
	if (!Res) return false;
	if (LefPathStr.Equals(TEXT("Blueprint"), ESearchCase::IgnoreCase))
	{//引用路径以Blueprint开头说明是蓝图类
		PackagePath = RightStr;
		PackagePath.InsertAt(PackagePath.Len() - 1, TEXT("_C"));
	}
	else
	{
		PackagePath = RightPathStr;
	}
	ResourceType = LefPathStr;
	return true;
}

int32 FCatalogFunctionLibrary::Floor(const int32& InValue)
{
	FString Str = UKismetStringLibrary::Conv_IntToString(InValue);
	for (int32 i = 1; i < Str.Len(); ++i)
	{
		Str[i] = '0';
	}
	return FCString::Atoi(*Str);
}

bool FCatalogFunctionLibrary::CreateDirectoryRecursively(FString DirectoryToMake)
{
	const int32 MAX_LOOP_ITR = 3000; //limit of 3000 directories in the structure

									 // Normalize all / and \ to TEXT("/") and remove any trailing TEXT("/") 
									 //if the character before that is not a TEXT("/") or a colon
	FPaths::NormalizeDirectoryName(DirectoryToMake);

	//Normalize removes the last "/", but my algorithm wants it
	DirectoryToMake += "/";

	FString Base;
	FString Left;
	FString Remaining;

	//Split off the Root
	DirectoryToMake.Split(TEXT("/"), &Base, &Remaining);
	Base += "/"; //add root text to Base

	int32 LoopItr = 0;
	while (Remaining != "" && LoopItr < MAX_LOOP_ITR)
	{
		Remaining.Split(TEXT("/"), &Left, &Remaining);

		//Add to the Base
		Base += Left + FString("/"); //add left and split text to Base

									 //Create Incremental Directory Structure!
		FPlatformFileManager::Get().GetPlatformFile().CreateDirectory(*Base);

		LoopItr++;
	}
	return true;
}

ECopyFileErrorCode FCatalogFunctionLibrary::CopyFileTo(const FString& AbsoluteSourcePath, const FString& AbsoluteDestinationPath, bool bStillCopyWhileExits)
{
	if (!FPaths::FileExists(AbsoluteSourcePath))
	{
		return ECopyFileErrorCode::ESourceFileNotExits;
	}
	if (FPaths::FileExists(AbsoluteDestinationPath))
	{
		if (bStillCopyWhileExits)
		{
			if (!FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*AbsoluteDestinationPath))
			{
				return ECopyFileErrorCode::EUnkonwnError;
			}
		}
		else
		{
			return AbsoluteSourcePath == AbsoluteDestinationPath ? ECopyFileErrorCode::ESuccess : ECopyFileErrorCode::EUnkonwnError;;
		}
	}
	//生成目录，防止目录不存在造成的失败
	FCatalogFunctionLibrary::CreateDirectoryRecursively(FPaths::GetPath(AbsoluteDestinationPath));
	if (!FPlatformFileManager::Get().GetPlatformFile().CopyFile(*AbsoluteDestinationPath, *AbsoluteSourcePath))
	{
		return ECopyFileErrorCode::ECanNotDeleteDestinationFile;
	}
	return ECopyFileErrorCode::ESuccess;
}
#pragma optimize("", off)
UTexture2D* FCatalogFunctionLibrary::LoadTextureFromJPG(const FString& InTextureAbsoluteFilePath)
{
	if (!FPaths::FileExists(InTextureAbsoluteFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG can't find %s"), *InTextureAbsoluteFilePath);
		return nullptr;
	}
	// Represents the entire file in memory.
	TArray<uint8> RawFileData;
	bool Res = FFileHelper::LoadFileToArray(RawFileData, *InTextureAbsoluteFilePath);
	if (false == Res)
	{
		UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG load file %s failed"), *InTextureAbsoluteFilePath);
		return nullptr;
	}

	const FString Extension = FPaths::GetExtension(InTextureAbsoluteFilePath);
	EImageFormat ImageFormat;

	if (Extension.Equals("jpeg", ESearchCase::IgnoreCase) || Extension.Equals("jpg", ESearchCase::IgnoreCase))
	{
		ImageFormat = EImageFormat::JPEG;
	}
	else if (Extension.Equals("png", ESearchCase::IgnoreCase))
	{
		ImageFormat = EImageFormat::PNG;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG unsupport file %s type"), *Extension);
		return nullptr;
	}

	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));

	TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(ImageFormat);

	if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(RawFileData.GetData(), RawFileData.Num()))
	{
		TArray<uint8> UncompressedBGRA;
		Res = ImageWrapper->GetRaw(ERGBFormat::BGRA, 8, UncompressedBGRA);
		if (false == Res)
		{
			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG unable get raw data from %s "), *InTextureAbsoluteFilePath);
			return nullptr;
		}
		UTexture2D* Texture = UTexture2D::CreateTransient(ImageWrapper->GetWidth(), ImageWrapper->GetHeight(), PF_B8G8R8A8);

		if (!Texture)
		{
			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG create texture2d failed"));
			return nullptr;
		}
		Texture->SRGB = 1;
		Texture->NeverStream = true;
		Texture->VirtualTextureStreaming = false;
		void* TextureData = Texture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
		FMemory::Memcpy(TextureData, UncompressedBGRA.GetData(), UncompressedBGRA.Num());
		Texture->GetPlatformData()->Mips[0].BulkData.Unlock();

		Texture->UpdateResource();
		UE_LOG(LogTemp, Log, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG unable get raw data from %d Width=%d Height=%d"), RawFileData.Num(), ImageWrapper->GetWidth(), ImageWrapper->GetHeight());
		UE_LOG(LogTemp, Log, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG laod texture from %s success"), *InTextureAbsoluteFilePath);
		return Texture;
	}
	UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG png image %s with unkwon file type"), *InTextureAbsoluteFilePath);
	return nullptr;

	//IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));
	//{// Note: PNG format.  Other formats are supported
	//	TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::PNG);
	//	if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(RawFileData.GetData(), RawFileData.Num()))
	//	{
	//		EImageFormat OtherFormat = EImageFormat::PNG;
	//		// Select the texture's source format
	//		ETextureSourceFormat TextureFormat = TSF_Invalid;
	//		int32 BitDepth = ImageWrapper->GetBitDepth();
	//		ERGBFormat Format = ImageWrapper->GetFormat();
	//		if (Format == ERGBFormat::Gray)
	//		{
	//			if (BitDepth <= 8)
	//			{
	//				TextureFormat = TSF_G8;
	//				Format = ERGBFormat::Gray;
	//				BitDepth = 8;
	//			}
	//			else if (BitDepth == 16)
	//			{
	//				TextureFormat = TSF_G16;
	//				Format = ERGBFormat::Gray;
	//				BitDepth = 16;
	//			}
	//		}
	//		else if (Format == ERGBFormat::RGBA || Format == ERGBFormat::BGRA)
	//		{
	//			if (BitDepth <= 8)
	//			{
	//				TextureFormat = TSF_BGRA8;
	//				Format = ERGBFormat::BGRA;
	//				BitDepth = 8;
	//			}
	//			else if (BitDepth == 16)
	//			{
	//				TextureFormat = TSF_RGBA16;
	//				Format = ERGBFormat::RGBA;
	//				BitDepth = 16;
	//			}
	//		}
	//		if (TextureFormat != TSF_Invalid)
	//		{
	//			TArray<uint8> UncompressedRGBA;
	//			Res = ImageWrapper->GetRaw(Format, BitDepth, UncompressedRGBA);
	//			if (Res)
	//			{
	//				UTexture2D* MyTexture = UTexture2D::CreateTransient(ImageWrapper->GetWidth(), ImageWrapper->GetHeight(), PF_B8G8R8A8);
	//				if (nullptr == MyTexture)
	//				{
	//					UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG create texture2d failed"));
	//					return nullptr;
	//				}

	//				//MyTexture->SRGB = BitDepth < 16;
	//				//MyTexture->NeverStream = 1;
	//				// Fill in the source data from the file
	//				void* TextureData = MyTexture->PlatformData->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
	//				FMemory::Memcpy(TextureData, UncompressedRGBA.GetData(), UncompressedRGBA.Num());
	//				MyTexture->PlatformData->Mips[0].BulkData.Unlock();
	//				MyTexture->UpdateResource();
	//				UE_LOG(LogTemp, Log, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG create texture2d from png success"));
	//				return MyTexture;
	//			}
	//			else
	//			{
	//				UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG unable get raw data from %s "), *InTextureAbsoluteFilePath);
	//			}
	//		}
	//		else
	//		{
	//			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG png image %s with unkwon file type"), *InTextureAbsoluteFilePath);
	//		}
	//	}
	//}
	//{// Note: PNG format.  Other formats are supported
	//	TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::JPEG);
	//	if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(RawFileData.GetData(), RawFileData.Num()))
	//	{
	//		// Select the texture's source format
	//		ETextureSourceFormat TextureFormat = TSF_Invalid;
	//		int32 BitDepth = ImageWrapper->GetBitDepth();
	//		ERGBFormat Format = ImageWrapper->GetFormat();
	//		if (Format == ERGBFormat::Gray)
	//		{
	//			if (BitDepth <= 8)
	//			{
	//				TextureFormat = TSF_G8;
	//				Format = ERGBFormat::Gray;
	//				BitDepth = 8;
	//			}
	//		}
	//		else if (Format == ERGBFormat::RGBA)
	//		{
	//			if (BitDepth <= 8)
	//			{
	//				TextureFormat = TSF_BGRA8;
	//				Format = ERGBFormat::BGRA;
	//				BitDepth = 8;
	//			}
	//		}
	//		if (TextureFormat == TSF_Invalid)
	//		{
	//			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG jpg image %s with unkwon file type"), *InTextureAbsoluteFilePath);
	//			return nullptr;
	//		}
	//		TArray<uint8> UncompressedRGBA;
	//		Res = ImageWrapper->GetRaw(Format, BitDepth, UncompressedRGBA);
	//		if (false == Res)
	//		{
	//			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG unable get raw data from %s "), *InTextureAbsoluteFilePath);
	//			return nullptr;
	//		}
	//		UTexture2D* MyTexture = UTexture2D::CreateTransient(ImageWrapper->GetWidth(), ImageWrapper->GetHeight(), PF_B8G8R8A8);
	//		if (nullptr == MyTexture)
	//		{
	//			UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG create texture2d failed"));
	//			return nullptr;
	//		}

	//		//MyTexture->SRGB = BitDepth < 16;
	//		//MyTexture->NeverStream = 1;
	//		// Fill in the source data from the file
	//		void* TextureData = MyTexture->PlatformData->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
	//		FMemory::Memcpy(TextureData, UncompressedRGBA.GetData(), UncompressedRGBA.Num());
	//		MyTexture->PlatformData->Mips[0].BulkData.Unlock();
	//		MyTexture->UpdateResource();
	//		UE_LOG(LogTemp, Log, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG create texture2d from jpg success"));
	//		return MyTexture;
	//	}
	//}
	//UE_LOG(LogTemp, Error, TEXT("FCatalogFunctionLibrary::LoadTextureFromJPG file extention and image type not match!"));
	//return nullptr;
}
#pragma optimize("", on)
void FCatalogFunctionLibrary::OpenFileDialogForImport(FString& OutputString)
{
	TArray<FString> OutFileNames;
	FString FileTypes = TEXT("Model File (fbx or pak file)|*.fbx; *.pak");

	IDesktopPlatform* DP = FDesktopPlatformModule::Get();
	bool bSuccess = DP->OpenFileDialog(nullptr, TEXT("Open Import File"), TEXT(""), TEXT(""), *FileTypes, EFileDialogFlags::None, OutFileNames);
	if (bSuccess && (1 == OutFileNames.Num()))
	{
		OutputString = FPaths::ConvertRelativePathToFull(OutFileNames[0]);
	}
}

void FCatalogFunctionLibrary::OpenFileDialogForImage(FString& OutputString)
{
	TArray<FString> OutFileNames;
	FString FileTypes = TEXT("JPG, BMP, PNG, JPEG files | *.jpg; *.png; *.bmp; *.jpeg");

	IDesktopPlatform* DP = FDesktopPlatformModule::Get();
	bool bSuccess = DP->OpenFileDialog(nullptr, TEXT("Open Import File"), TEXT(""), TEXT(""), *FileTypes, EFileDialogFlags::None, OutFileNames);
	if (bSuccess && (1 == OutFileNames.Num()))
	{
		OutputString = FPaths::ConvertRelativePathToFull(OutFileNames[0]);
	}
}

void FCatalogFunctionLibrary::OpenFileDialogForDatFile(FString& OutputString)
{
	TArray<FString> OutFileNames;
	FString FileTypes = TEXT("*.dat");

	IDesktopPlatform* DP = FDesktopPlatformModule::Get();
	bool bSuccess = DP->OpenFileDialog(nullptr, TEXT("Open Dat File"), TEXT(""), TEXT(""), *FileTypes, EFileDialogFlags::None, OutFileNames);
	if (bSuccess && (1 == OutFileNames.Num()))
	{
		OutputString = FPaths::ConvertRelativePathToFull(OutFileNames[0]);
	}
}

void FCatalogFunctionLibrary::OpenFileDialogForDXF(FString& OutputString)
{
	TArray<FString> OutFileNames;
	FString FileTypes = TEXT("DXF files | *.dxf");

	IDesktopPlatform* DP = FDesktopPlatformModule::Get();
	bool bSuccess = DP->OpenFileDialog(nullptr, TEXT("Open Dat File"), TEXT(""), TEXT(""), *FileTypes, EFileDialogFlags::None, OutFileNames);
	if (bSuccess && (1 == OutFileNames.Num()))
	{
		OutputString = FPaths::ConvertRelativePathToFull(OutFileNames[0]);
	}
}

bool FCatalogFunctionLibrary::DeleteFile(const FString& AbsoluteSourcePath)
{
	return FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*AbsoluteSourcePath);
}

bool FCatalogFunctionLibrary::WriteDataToFile(const FString& FilePath, TArray<uint8> InData)
{
	FCatalogFunctionLibrary::DeleteFile(FilePath);
	FCatalogFunctionLibrary::CreateDirectoryRecursively(FPaths::GetPath(FilePath));
	FArchive* writer = IFileManager::Get().CreateFileWriter(*FilePath, EFileWrite::FILEWRITE_Append);

	//write test
	/*if (FilePath.EndsWith(TEXT("json")))
	{
		const FString FileName = FPaths::GetBaseFilename(FilePath).Append(TEXT("_1"));
		const FString FileDir = FPaths::GetPath(FilePath);
		const FString FileExt = FPaths::GetExtension(FilePath, true);
		const FString FileTestPath = FPaths::Combine(FileDir, FileName + FileExt);
		bool Res = FFileHelper::SaveArrayToFile(InData, *FileTestPath);
	}*/
	//end write test

	if (!writer)
	{
		UE_LOG(LogTemp, Error, TEXT("Write file %s failed"), *FilePath);
		return false;
	}

	writer->Seek(writer->TotalSize());
	writer->Serialize(InData.GetData(), InData.Num());
	writer->Close();
	delete writer;
	InData.Empty();
	return true;
}

#undef LOCTEXT_NAMESPACE
