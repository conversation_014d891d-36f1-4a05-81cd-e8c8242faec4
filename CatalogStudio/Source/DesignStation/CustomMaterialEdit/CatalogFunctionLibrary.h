// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Runtime/Engine/Classes/Engine/Texture2D.h"
#include "Runtime/Engine/Classes/Engine/StaticMesh.h"

enum class ECopyFileErrorCode : uint8
{
	ESourceFileNotExits,
	ESuccess,
	ECanNotDeleteDestinationFile,
	EUnkonwnError
};

/**
 *
 */
class DESIGNSTATION_API FCatalogFunctionLibrary
{

public:

	static UStaticMesh* LoadStaticMeshFromPak(const FString& PakFilePath, const FString& RefPath);

	template<class ClassType, class BaseClass>
	static ClassType* LoadActorFormPak(UWorld* World, const FString& RefPath)
	{
		UClass* LoadedClass = FCatalogFunctionLibrary::PhraseClassFromRefPath<BaseClass>(RefPath);
		if (LoadedClass != nullptr && nullptr != World)
		{
			BaseClass* NewActor = World->SpawnActor<BaseClass>(LoadedClass);
			UE_LOG(LogClass, Log, TEXT("Success"));
			return Cast<ClassType>(NewActor);
		}
		UE_LOG(LogTemp, Log, TEXT("LoadActorFormPak phrase class failed"));
		return nullptr;
	}

	static bool PhraseClassNameAndPackagePathFromRefPath(const FString& InRefPath, FString& ClassName, FString& PackagePath);

	static bool PhraseResourceTypeAndPackagePathFromRefPath(const FString& InRefPath, FString& ResourceType, FString& PackagePath);

	template<class BaseClass>
	static UClass* PhraseClassFromRefPath(const FString& InRefPath)
	{
		FString ResourceType(TEXT(""));
		FString ResourcePath(TEXT(""));
		bool Res = FCatalogFunctionLibrary::PhraseResourceTypeAndPackagePathFromRefPath(InRefPath, ResourceType, ResourcePath);
		UE_LOG(LogTemp, Log, TEXT("PhraseClassFromRefPath : ResourcePath is %s ResourceType is %s Res is %d"), *ResourcePath, *ResourceType, Res);
		if (!(Res && ResourceType.Equals(TEXT("Blueprint"), ESearchCase::IgnoreCase))) return nullptr;
		UClass* ClassFound = StaticLoadClass(BaseClass::StaticClass(), NULL, *ResourcePath);
		return ClassFound;
	}

	//Floor positive integer Floor(567)=500,Floor(1234)=1000
	static int32 Floor(const int32& InValue);

	static bool CreateDirectoryRecursively(FString DirectoryToMake);

	static ECopyFileErrorCode CopyFileTo(const FString& AbsoluteSourcePath, const FString& AbsoluteDestinationPath, bool bStillCopyWhileExits = true);

	static UTexture2D* LoadTextureFromJPG(const FString& InTextureAbsoluteFilePath);

	static void OpenFileDialogForImport(FString& OutputString);

	static void OpenFileDialogForImage(FString& OutputString);

	static void OpenFileDialogForDatFile(FString& OutputString);

	static void OpenFileDialogForDXF(FString& OutputString);

	static bool DeleteFile(const FString& AbsoluteSourcePath);

	static bool WriteDataToFile(const FString& FilePath, TArray<uint8> InData);

};
