// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialParameterBasicData.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"


int32 FCustomMatParameterTableData::GetCustomMaterialDependFiles(const TArray<FCustomMatParameterTableData>& InCustomMatParameters, TArray<FString>& OutDependFiles)
{
	for (auto& ParameterIter : InCustomMatParameters)
	{
		const EMatParameterType ParameterType = static_cast<EMatParameterType>(ParameterIter.parameter_type);
		if (EMatParameterType::ETexture == ParameterType && ParameterIter.parameter_value.Len() > 3)
		{
			OutDependFiles.Add(ParameterIter.parameter_value);
		}
	}
	return OutDependFiles.Num();
}

FBasicMatParameterTableData::FBasicMatParameterTableData(const FCustomMatParameterTableData& InCustomMatParameter)
	:id(InCustomMatParameter.id)
	, parameter_order(InCustomMatParameter.parameter_order)
	, parameter_name(InCustomMatParameter.parameter_name)
	, display_name(InCustomMatParameter.display_name)
	, parameter_type(InCustomMatParameter.parameter_type)
	, default_value(InCustomMatParameter.parameter_value)
	, max_value(InCustomMatParameter.max_value)
	, min_value(InCustomMatParameter.min_value)
	, is_enable(InCustomMatParameter.is_enable)
	, group_name(InCustomMatParameter.group_name)
{
}

EMatParameterType FBasicMatParameterTableData::ConvertIntToEnum(const int32& InValue)
{
	switch (InValue)
	{
	case 0:return EMatParameterType::EFloat;
	case 1:return EMatParameterType::ETexture;
	case 2:return EMatParameterType::EColor;
	case 3:return EMatParameterType::EBoolean;
	default:return EMatParameterType::EGroup;
	}
}
void FBasicMatParameterTableData::ConvertToCustomMaterialParameter(FCustomMatParameterTableData& OutCustomMatParameter) const
{
	OutCustomMatParameter.parameter_name = this->parameter_name;
	OutCustomMatParameter.display_name = this->display_name;
	OutCustomMatParameter.parameter_order = this->parameter_order;
	OutCustomMatParameter.parameter_type = this->parameter_type;
	OutCustomMatParameter.parameter_value = this->default_value;
	OutCustomMatParameter.max_value = this->max_value;
	OutCustomMatParameter.min_value = this->min_value;
	OutCustomMatParameter.is_enable = this->is_enable;
	OutCustomMatParameter.group_name = this->group_name;
	OutCustomMatParameter.id = this->id;
}

void FBasicMatParameterTableData::ConvertToCustomMaterialParameter(const TArray<FBasicMatParameterTableData>& InBasicMatParameters, TArray<FCustomMatParameterTableData>& OutCustomMatParameters)
{
	OutCustomMatParameters.AddDefaulted(InBasicMatParameters.Num());
	for (int32 i = 0; i < InBasicMatParameters.Num(); ++i)
		InBasicMatParameters[i].ConvertToCustomMaterialParameter(OutCustomMatParameters[i]);
}

void UMaterialParameterBasicData::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	this->ID = InParameterInfo.id;
	this->IsEnable = 0 != InParameterInfo.is_enable;
	this->ParameterName = InParameterInfo.display_name;
}

void UMaterialParameterBasicData::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	OutParameterInfo.id = this->ID;
	OutParameterInfo.is_enable = static_cast<int32>(this->IsEnable);
	OutParameterInfo.parameter_name = this->ParameterName;
	OutParameterInfo.parameter_type = static_cast<int32>(this->ParameterType);
}

void UMatParameterTexture::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	if (ParameterType == static_cast<EMatParameterType>(InParameterInfo.parameter_type))
	{
		UMaterialParameterBasicData::InitFromParameterInfo(InParameterInfo);
		this->TexturePath = InParameterInfo.default_value;
	}
}

void UMatParameterTexture::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	UMaterialParameterBasicData::ConstructParameterInfo(OutParameterInfo);
	OutParameterInfo.default_value = this->TexturePath;
}

void UMatParameterBoolean::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	if (ParameterType == static_cast<EMatParameterType>(InParameterInfo.parameter_type))
	{
		UMaterialParameterBasicData::InitFromParameterInfo(InParameterInfo);
		this->BooleanValue = InParameterInfo.default_value.Equals(TEXT("1"));
	}
}

void UMatParameterBoolean::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	UMaterialParameterBasicData::ConstructParameterInfo(OutParameterInfo);
	OutParameterInfo.default_value = UKismetStringLibrary::Conv_IntToString(static_cast<int32>(this->BooleanValue));
}

void UMatParameterFloat::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	if (ParameterType == static_cast<EMatParameterType>(InParameterInfo.parameter_type))
	{
		UMaterialParameterBasicData::InitFromParameterInfo(InParameterInfo);
		this->FloatValue = FCString::Atof(*InParameterInfo.default_value);
		this->MaxValue = FCString::Atof(*InParameterInfo.max_value);
		this->MinValue = FCString::Atof(*InParameterInfo.min_value);
	}
}

void UMatParameterFloat::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	UMaterialParameterBasicData::ConstructParameterInfo(OutParameterInfo);
	OutParameterInfo.default_value = FString::SanitizeFloat(this->FloatValue);
	OutParameterInfo.max_value = FString::SanitizeFloat(this->MaxValue);
	OutParameterInfo.min_value = FString::SanitizeFloat(this->MinValue);
}

void UMatParameterColor::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	if (ParameterType == static_cast<EMatParameterType>(InParameterInfo.parameter_type))
	{
		UMaterialParameterBasicData::InitFromParameterInfo(InParameterInfo);
		this->ColorValue = FColor::FromHex(InParameterInfo.default_value);
	}
}

void UMatParameterColor::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	UMaterialParameterBasicData::ConstructParameterInfo(OutParameterInfo);
	OutParameterInfo.default_value = this->ColorValue.ToHex();
}

void UMatParameterGroup::InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo)
{
	if (ParameterType == static_cast<EMatParameterType>(InParameterInfo.parameter_type))
	{
		UMaterialParameterBasicData::InitFromParameterInfo(InParameterInfo);
		this->IsExtent = false;
		//this->IsExtent = InParameterInfo.default_value.Equals(TEXT("1"));
	}
}

void UMatParameterGroup::ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo)
{
	UMaterialParameterBasicData::ConstructParameterInfo(OutParameterInfo);
	OutParameterInfo.default_value = UKismetStringLibrary::Conv_IntToString(static_cast<int32>(this->IsExtent));
}


UMaterialParameterBasicData* UMatParameterFactory::CreateMaterialParameterData(const FBasicMatParameterTableData& InParameterInfo)
{
	EMatParameterType ParameterType = static_cast<EMatParameterType>(InParameterInfo.parameter_type);
	UMaterialParameterBasicData* NewParameterItem = nullptr;
	switch (ParameterType)
	{
	case EMatParameterType::ETexture:NewParameterItem = NewObject<UMatParameterTexture>();break;
	case EMatParameterType::EBoolean:NewParameterItem = NewObject<UMatParameterBoolean>();break;
	case EMatParameterType::EFloat:NewParameterItem = NewObject<UMatParameterFloat>();break;
	case EMatParameterType::EColor:NewParameterItem = NewObject<UMatParameterColor>();break;
	case EMatParameterType::EGroup:NewParameterItem = NewObject<UMatParameterGroup>();break;
	}
	NewParameterItem->InitFromParameterInfo(InParameterInfo);
	return NewParameterItem;
}