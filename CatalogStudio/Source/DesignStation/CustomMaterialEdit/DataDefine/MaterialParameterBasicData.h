// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "MaterialParameterBasicData.generated.h"

UENUM(BlueprintType)
enum class EMatParameterType : uint8
{
	ETexture,
	EBoolean,
	EFloat,
	EColor,
	EGroup
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCustomMatParameterTableData
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 id;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 parameter_order;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString parameter_name;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString display_name;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 parameter_type;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString parameter_value;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString max_value;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString min_value;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 is_enable;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		FString group_name;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 custom_mat_id;

public:
	FCustomMatParameterTableData() :id(0), parameter_order(0), parameter_name(TEXT("")), parameter_type(0), parameter_value(TEXT("")), max_value(TEXT("")), min_value(TEXT("")), is_enable(0), group_name(TEXT("")), custom_mat_id(-1) {}

	static int32 GetCustomMaterialDependFiles(const TArray<FCustomMatParameterTableData>& InCustomMatParameters, TArray<FString>& OutDependFiles);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FBasicMatParameterTableData
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		int32 id;

	UPROPERTY(BlueprintReadWrite, Category = CustomMatParameter)
		int32 parameter_order;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString parameter_name;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString display_name;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		int32 parameter_type;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString default_value;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString max_value;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString min_value;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		int32 is_enable;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		FString group_name;

	UPROPERTY(BlueprintReadWrite, Category = MaterialParameter)
		int32 basic_material_id;

public:
	FBasicMatParameterTableData()
		:id(0)
		, parameter_order(0)
		, parameter_name(TEXT(""))
		, display_name(TEXT(""))
		, parameter_type(0)
		, default_value(TEXT(""))
		, max_value(TEXT(""))
		, min_value(TEXT(""))
		, is_enable(0)
		, group_name(TEXT(""))
		, basic_material_id(-1)
	{}
	FBasicMatParameterTableData(const FCustomMatParameterTableData& InCustomMatParameter);

	void ConvertToCustomMaterialParameter(FCustomMatParameterTableData& OutCustomMatParameter) const;

	static void ConvertToCustomMaterialParameter(const TArray<FBasicMatParameterTableData>& InBasicMatParameters, TArray<FCustomMatParameterTableData>& OutCustomMatParameters);

	static EMatParameterType ConvertIntToEnum(const int32& InValue);
};

USTRUCT()
struct FCustomMaterialTableData
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY()
		int32 id;

	UPROPERTY()
		FString ref_path;

	UPROPERTY()
		FString import_path;

	UPROPERTY()
		FString folder_id;

public:
	FCustomMaterialTableData() :id(0), ref_path(TEXT("")), import_path(TEXT("")), folder_id(TEXT("")) {}

	inline bool IsValid() const
	{
		return id > 0 && !folder_id.IsEmpty();
	}

	bool IsRefPathValid() const
	{
		bool Invalid = ref_path.IsEmpty() || ref_path.Contains(TEXT(".Null"));
		return !Invalid;
	}
};

/**
 *
 */
UCLASS(BlueprintType)
class DESIGNSTATION_API UMaterialParameterBasicData : public UObject
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterBasic)
		int32	ID;

	UPROPERTY(BlueprintReadWrite, Category = MatParameterBasic)
		EMatParameterType ParameterType;

	UPROPERTY(BlueprintReadWrite, Category = "MatParameterBasic")
		bool IsEnable;

	UPROPERTY(BlueprintReadWrite, Category = "MatParameterBasic")
		FString ParameterName;

public:

	UMaterialParameterBasicData() :ID(-1), ParameterType(EMatParameterType::EFloat), IsEnable(false), ParameterName(TEXT("")) {}
	UMaterialParameterBasicData(const EMatParameterType& InParameterType) :ParameterType(InParameterType), IsEnable(false), ParameterName(TEXT("")) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo);

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMaterialLevelInfo
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY(BlueprintReadWrite, Category = "MaterialLevelInfo")
		int32	id;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialLevelInfo")
		FString level_name;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialLevelInfo")
		int32 level_num;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialLevelInfo")
		int32 parent_level_id;

public:
	FMaterialLevelInfo() :id(-1), level_name(TEXT("")), level_num(0), parent_level_id(0) {}

	void operator=(const FMaterialLevelInfo& InInfo)
	{
		id = InInfo.id;
		level_name = InInfo.level_name;
		level_num = InInfo.level_num;
		parent_level_id = InInfo.parent_level_id;
	}
};

UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterTexture : public UMaterialParameterBasicData
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterTexture)
		FString TexturePath;

public:

	UMatParameterTexture() :UMaterialParameterBasicData(EMatParameterType::ETexture), TexturePath(TEXT("")) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo) override;

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};

UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterBoolean : public UMaterialParameterBasicData
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterBoolean)
		bool BooleanValue;

public:

	UMatParameterBoolean() :UMaterialParameterBasicData(EMatParameterType::EBoolean), BooleanValue(false) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo) override;

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};

UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterFloat : public UMaterialParameterBasicData
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterFloat)
		float FloatValue;

	UPROPERTY(BlueprintReadWrite, Category = MatParameterFloat)
		float MaxValue;

	UPROPERTY(BlueprintReadWrite, Category = MatParameterFloat)
		float MinValue;

public:

	UMatParameterFloat() :UMaterialParameterBasicData(EMatParameterType::EFloat), FloatValue(0.0f), MaxValue(1.0f), MinValue(0.0f) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo) override;

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};

UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterColor : public UMaterialParameterBasicData
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterColor)
		FColor ColorValue;

public:

	UMatParameterColor() :UMaterialParameterBasicData(EMatParameterType::EColor), ColorValue(FColor::White) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo) override;

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};


UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterGroup : public UMaterialParameterBasicData
{
	GENERATED_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = MatParameterGroup)
		bool IsExtent;

	UPROPERTY(BlueprintReadWrite, Category = MatParameterGroup)
		TArray<UMaterialParameterBasicData*> GroupItems;

public:

	UMatParameterGroup() :UMaterialParameterBasicData(EMatParameterType::EGroup), IsExtent(false) {}

	virtual void InitFromParameterInfo(const FBasicMatParameterTableData& InParameterInfo) override;

	virtual void ConstructParameterInfo(FBasicMatParameterTableData& OutParameterInfo);
};


UCLASS(BlueprintType)
class DESIGNSTATION_API UMatParameterFactory : public UObject
{
	GENERATED_BODY()
public:

	static UMaterialParameterBasicData* CreateMaterialParameterData(const FBasicMatParameterTableData& InParameterInfo);
};