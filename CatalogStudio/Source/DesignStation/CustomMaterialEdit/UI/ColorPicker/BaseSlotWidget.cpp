// Fill out your copyright notice in the Description page of Project Settings.

#include "BaseSlotWidget.h"
#include "Runtime/UMG/Public/Components/NamedSlot.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"

FString UBaseSlotWidget::BaseSlotPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/ColorPicker/BaseSlotUI.BaseSlotUI_C'");

bool UBaseSlotWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(NSContent, NS_Content);

	return true;
}

UBaseSlotWidget* UBaseSlotWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UBaseSlotWidget>(UBaseSlotWidget::BaseSlotPath);
}
