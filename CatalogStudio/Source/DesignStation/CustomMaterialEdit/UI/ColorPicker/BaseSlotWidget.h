// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "BaseSlotWidget.generated.h"

/**
 * 
 */

class UNamedSlot;

UCLASS()
class DESIGNSTATION_API UBaseSlotWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	static UBaseSlotWidget* Create();

private:
	static FString BaseSlotPath;
	
private:
	UPROPERTY()
		UNamedSlot* NSContent;
};
