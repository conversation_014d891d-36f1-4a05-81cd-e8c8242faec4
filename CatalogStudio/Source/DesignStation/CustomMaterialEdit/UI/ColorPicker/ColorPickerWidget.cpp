// Fill out your copyright notice in the Description page of Project Settings.

#include "ColorPickerWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"

const float SaftyZoomSize = 5.0f;

FString UColorPickerWidget::ColorPickerPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/ColorPicker/ColorPickerUI.ColorPickerUI_C'");

bool UColorPickerWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(VBContent, VB_Content);
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_Header);
	BIND_SLATE_WIDGET_FUNCTION(BorHeader, OnMouseButtonDownEvent, FName(TEXT("LeftMouseButtonDownOnBorHeaderToDrag")));
	BIND_SLATE_WIDGET_FUNCTION(BorH<PERSON>er, OnMouseButtonUpEvent, FName(TEXT("NativeOnMouseButtonUp")));

	BIND_PARAM_CPP_TO_UMG(CPWheel, CP_Wheel);
	BIND_WIDGET_FUNCTION(CPWheel, OnColorChanged, UColorPickerWidget::OnColorChangedCPWheel);

	BIND_PARAM_CPP_TO_UMG(BtnOK, Btn_OK);
	BIND_WIDGET_FUNCTION(BtnOK, OnClicked, UColorPickerWidget::OnClickedBtnOK);

	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UColorPickerWidget::OnClickedBtnCancel);

	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, UColorPickerWidget::OnClickedBtnClose);

	IsMouseButtonDown = false;
	LastMousePosition = FVector2D::ZeroVector;

	return true;
}

void UColorPickerWidget::UpdateCPWheelColor(const FLinearColor& InColor)
{
	if (CPWheel)
	{
		CPWheel->SetColor(InColor, true);
		//CPWheel->ColorUpdated(InColor);
	}
}

FLinearColor UColorPickerWidget::GetColor()
{
	if (CPWheel)
	{
		return CPWheel->GetColor();
	}
	return FLinearColor();
}

FReply UColorPickerWidget::OnMouseUpInParentWidget(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	return NativeOnMouseButtonUp(InGeometry, InMouseEvent);
}

UColorPickerWidget* UColorPickerWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UColorPickerWidget>(UColorPickerWidget::ColorPickerPath);
}

void UColorPickerWidget::GetParentSlot()
{
	ParentSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(this);
}

void UColorPickerWidget::SetWidgetShow(bool IsShow)
{
	if (VBContent)
	{
		VBContent->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
}

FVector2D UColorPickerWidget::GetMouseMoveVector(const FPointerEvent& MouseEvent)
{
	FVector2D LocalPixelPosition;// no use
	FVector2D LocalViewportPosition;
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	return LocalViewportPosition - LastMousePosition;
}

void UColorPickerWidget::MouseButtonUpToDrop()
{
	IsMouseButtonDown = false;
	SetWidgetShow(true);
}

bool UColorPickerWidget::CheckMouseIsOutScreen(const FPointerEvent& MouseEvent)
{
	FVector2D LocalPixelPosition;
	FVector2D LocalViewportPosition;// no use
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	FVector2D ViewportSize = UWidgetLayoutLibrary::GetViewportSize(GWorld);
	return (LocalPixelPosition.X < SaftyZoomSize) || (LocalPixelPosition.Y < SaftyZoomSize)
		|| (LocalPixelPosition.X > (ViewportSize.X - SaftyZoomSize)) || (LocalPixelPosition.Y > (ViewportSize.Y - SaftyZoomSize));
}

void UColorPickerWidget::OnClickedBtnClose()
{
	ColorPickerDelegate.ExecuteIfBound(EColorPickerType::EClose);
}

void UColorPickerWidget::OnClickedBtnOK()
{
	ColorPickerDelegate.ExecuteIfBound(EColorPickerType::EOK);
}

void UColorPickerWidget::OnClickedBtnCancel()
{
	ColorPickerDelegate.ExecuteIfBound(EColorPickerType::ECancel);
}

void UColorPickerWidget::OnColorChangedCPWheel(const FLinearColor& NewColor)
{
	ColorChangedEditDelegate.ExecuteIfBound(NewColor);
}

FEventReply UColorPickerWidget::LeftMouseButtonDownOnBorHeaderToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		UE_LOG(LogTemp, Log, TEXT("left mouse click header border to drag"));
		if (!ParentSlot)
		{
			GetParentSlot();
		}
		IsMouseButtonDown = true;
		FVector2D PixelPosition;// no use
		USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), PixelPosition, LastMousePosition);
		UE_LOG(LogTemp, Log, TEXT("begin drag Position : %s"), *LastMousePosition.ToString());
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::CaptureMouse(DetectReply, nullptr);
	}
	return FEventReply();
}

FEventReply UColorPickerWidget::LeftMouseButtonUpOnBorHeaderToDrop(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	return FEventReply();
}

FReply UColorPickerWidget::NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		UE_LOG(LogTemp, Log, TEXT("left mouse up to drop"));
		MouseButtonUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}
	return FReply::Unhandled();
}

FReply UColorPickerWidget::NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (IsMouseButtonDown)
	{
		if (CheckMouseIsOutScreen(InMouseEvent))
		{
			UE_LOG(LogTemp, Log, TEXT("mouse out screen"));
			MouseButtonUpToDrop();
			return FReply::Handled();
		}
		else
		{
			FVector2D MouseMove = GetMouseMoveVector(InMouseEvent);
			//UE_LOG(LogTemp, Log, TEXT("mouse move : %s"), *MouseMove.ToString());
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
			if (ParentSlot)
			{
				ParentSlot->SetPosition(ParentSlot->GetPosition() + MouseMove);
			}
			SetWidgetShow(false);
			FVector2D PixelPositionTemp;
			USlateBlueprintLibrary::AbsoluteToViewport(GWorld, InMouseEvent.GetScreenSpacePosition(), PixelPositionTemp, LastMousePosition);
			UE_LOG(LogTemp, Log, TEXT("mouse moveing position on draging : %s"), *LastMousePosition.ToString());
			return UWidgetBlueprintLibrary::CaptureMouse(DetectReply, nullptr).NativeReply;
		}
	}
	return FReply::Unhandled();
}
