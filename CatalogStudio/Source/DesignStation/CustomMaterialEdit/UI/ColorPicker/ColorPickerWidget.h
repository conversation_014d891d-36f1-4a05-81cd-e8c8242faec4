// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"
#include "VictoryUMG/Public/JoyColorWheel.h"
#include "ColorPickerWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EColorPickerType : uint8
{
	EOK = 0,
	ECancel,
	EClose
};

class UBorder;
class UButton;
class UCanvasPanelSlot;
class UVerticalBox;

DECLARE_DYNAMIC_DELEGATE_OneParam(FColorChangedEditDelegate, const FLinearColor&, NewColor);
DECLARE_DYNAMIC_DELEGATE_OneParam(FColorPickerDelegate, const EColorPickerType&, CPType);

UCLASS()
class DESIGNSTATION_API UColorPickerWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateCPWheelColor(const FLinearColor& InColor);
	FLinearColor GetColor();

	FORCEINLINE bool GetMouseButtonDownState() const { return IsMouseButtonDown; }
	FReply OnMouseUpInParentWidget(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent);

	static UColorPickerWidget* Create();

private:
	void GetParentSlot();
	void SetWidgetShow(bool IsShow);
	FVector2D GetMouseMoveVector(const FPointerEvent& MouseEvent);
	void MouseButtonUpToDrop();
	bool CheckMouseIsOutScreen(const FPointerEvent& MouseEvent);

public:
	FColorChangedEditDelegate ColorChangedEditDelegate;
	FColorPickerDelegate ColorPickerDelegate;

private:
	bool IsMouseButtonDown;
	FVector2D LastMousePosition;

	static FString ColorPickerPath;

protected:
	UFUNCTION()
		void OnClickedBtnClose();
	UFUNCTION()
		void OnClickedBtnOK();
	UFUNCTION()
		void OnClickedBtnCancel();
	UFUNCTION()
		void OnColorChangedCPWheel(const FLinearColor& NewColor);
	UFUNCTION()
		FEventReply LeftMouseButtonDownOnBorHeaderToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseButtonUpOnBorHeaderToDrop(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	
private:
	UPROPERTY()
		UButton* BtnClose;
	UPROPERTY()
		UVerticalBox* VBContent;
	UPROPERTY()
		UBorder* BorHeader;
	UPROPERTY()
		UJoyColorWheel* CPWheel;
	UPROPERTY()
		UButton* BtnOK;
	UPROPERTY()
		UButton* BtnCancel;

	UPROPERTY()
		UCanvasPanelSlot* ParentSlot;
};
