// Fill out your copyright notice in the Description page of Project Settings.

#include "MoveableColorPickerWidget.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"

FString UMoveableColorPickerWidget::MoveableCPWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/ColorPicker/MoveableColorPickerUI.MoveableColorPickerUI_C'");

bool UMoveableColorPickerWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CPMove, CP_Move);

	/*BIND_PARAM_CPP_TO_UMG(CPWidget, CP_Widget);
	BIND_SLATE_WIDGET_FUNCTION(CPWidget, ColorChangedEditDelegate, FName(TEXT("ColorChangedEdit")));
	BIND_SLATE_WIDGET_FUNCTION(CPWidget, ColorPickerDelegate, FName(TEXT("ColorPickerEdit")));*/
	InitColorPicker();
	OriginColor = FLinearColor::White;
	return true;
}

void UMoveableColorPickerWidget::UpdateColorPicker(const FLinearColor& InColor)
{
	OriginColor = InColor;
	if (CPWidget)
	{
		CPWidget->UpdateCPWheelColor(InColor);
	}
}

UMoveableColorPickerWidget* UMoveableColorPickerWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMoveableColorPickerWidget>(UMoveableColorPickerWidget::MoveableCPWidgetPath);
}

void UMoveableColorPickerWidget::InitColorPicker()
{
	CPWidget = UColorPickerWidget::Create();
	CPWidget->ColorChangedEditDelegate.BindUFunction(this, FName(TEXT("ColorChangedEdit")));
	CPWidget->ColorPickerDelegate.BindUFunction(this, FName(TEXT("ColorPickerEdit")));
	CPWidget->SetVisibility(ESlateVisibility::Visible);
	if (CPMove)
	{
		CPMove->AddChild(CPWidget);
		UCanvasPanelSlot* ColorPickerSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(CPWidget);
		if (ColorPickerSlot)
		{
			ColorPickerSlot->SetAlignment(FVector2D::ZeroVector);
			ColorPickerSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 0.0f));
			//ColorPickerSlot->SetSize(FVector2D(520.0f, 495.0f));
			ColorPickerSlot->bAutoSize = true;
			ColorPickerSlot->SetOffsets(FMargin());
			ColorPickerSlot->SetPosition(FVector2D(650.0f, 300.0f));
		}
	}
}

void UMoveableColorPickerWidget::ColorChangedEdit(const FLinearColor& NewColor)
{
	UE_LOG(LogTemp, Log, TEXT("MoveableColorPickerWidget---change color : %s"), *NewColor.ToString());
	ColorChangedDelegate.ExecuteIfBound(NewColor);
}

void UMoveableColorPickerWidget::ColorPickerEdit(const EColorPickerType& CPType)
{
	if (CPType == EColorPickerType::ECancel)
	{
		ColorChangedDelegate.ExecuteIfBound(OriginColor);
	}
	else if (CPType == EColorPickerType::EOK)
	{
		if (CPWidget)
		{
			ColorDBWriteDelegate.ExecuteIfBound(CPWidget->GetColor());
		}
	}
	this->SetVisibility(ESlateVisibility::Collapsed);
}

FReply UMoveableColorPickerWidget::NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton && CPWidget && CPWidget->GetMouseButtonDownState())
	{
		return CPWidget->OnMouseUpInParentWidget(InGeometry, InMouseEvent);
	}
	return FReply::Unhandled();
}
