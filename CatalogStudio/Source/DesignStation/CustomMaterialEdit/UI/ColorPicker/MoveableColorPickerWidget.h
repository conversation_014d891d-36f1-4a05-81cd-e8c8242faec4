// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ColorPickerWidget.h"
#include "MoveableColorPickerWidget.generated.h"

/**
 * 
 */

class UCanvasPanel;

DECLARE_DYNAMIC_DELEGATE_OneParam(FColorDBWriteDelegate, const FLinearColor&, OutColor);

UCLASS()
class DESIGNSTATION_API UMoveableColorPickerWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateColorPicker(const FLinearColor& InColor);
	
	static UMoveableColorPickerWidget* Create();

private:
	void InitColorPicker();
	UFUNCTION()
		void ColorChangedEdit(const FLinearColor& NewColor);
	UFUNCTION()
		void ColorPickerEdit(const EColorPickerType& CPType);

public:
	FColorChangedEditDelegate ColorChangedDelegate;
	FColorDBWriteDelegate ColorDBWriteDelegate;

private:
	FLinearColor OriginColor;

	static FString MoveableCPWidgetPath;

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	UPROPERTY()
		UCanvasPanel* CPMove;
	UPROPERTY()
		UColorPickerWidget* CPWidget;
};
