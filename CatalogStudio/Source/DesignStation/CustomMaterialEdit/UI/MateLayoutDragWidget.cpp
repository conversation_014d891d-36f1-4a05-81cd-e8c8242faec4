// Fill out your copyright notice in the Description page of Project Settings.

#include "MateLayoutDragWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "Runtime/UMG/Public/Blueprint/WidgetBlueprintLibrary.h"
#include "MaterialFunctionLibrary.h"

FString UMateLayoutDragWidget::MateDragBorderPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MateLayoutDragUI.MateLayoutDragUI_C'");

bool UMateLayoutDragWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorDrag, Bor_Drag);
	BIND_SLATE_WIDGET_FUNCTION(BorDrag, OnMouseButtonDownEvent, FName(TEXT("LeftMouseDownToDrag")));
	BIND_SLATE_WIDGET_FUNCTION(BorDrag, OnMouseButtonUpEvent, FName(TEXT("LeftMouseUpToRelease")));

	return true;
}

UMateLayoutDragWidget * UMateLayoutDragWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMateLayoutDragWidget>(UMateLayoutDragWidget::MateDragBorderPath);
}

void UMateLayoutDragWidget::ClickToDragChangeMouseCursor(bool IsDrag)
{
	UE_LOG(LogTemp, Log, TEXT("change mouse cursor"));
	auto PC = UGameplayStatics::GetPlayerController(GWorld, 0);
	if (PC)
	{
		PC->CurrentMouseCursor = IsDrag ? EMouseCursor::ResizeLeftRight : EMouseCursor::Default;
		//PC->SetMouseCursorWidget(IsDrag ? EMouseCursor::Type::ResizeLeftRight : EMouseCursor::Type::Default, nullptr);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("get pc error"));
	}
}

FEventReply UMateLayoutDragWidget::LeftMouseDownToDrag(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ClickToDragChangeMouseCursor(true);
		MateDragBorderDelegate.ExecuteIfBound(true, MouseEvent);
		return FEventReply(true);
	}

	return FEventReply();
}

FEventReply UMateLayoutDragWidget::LeftMouseUpToRelease(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ClickToDragChangeMouseCursor(false);
		MateDragBorderDelegate.ExecuteIfBound(false, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

void UMateLayoutDragWidget::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("mouse enter drag widget"));
	UWidgetBlueprintLibrary::SetFocusToGameViewport();
	ClickToDragChangeMouseCursor(true);
}

void UMateLayoutDragWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	UWidgetBlueprintLibrary::SetFocusToGameViewport();
	ClickToDragChangeMouseCursor(false);
}


