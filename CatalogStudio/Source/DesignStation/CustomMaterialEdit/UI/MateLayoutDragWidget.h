// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MateLayoutDragWidget.generated.h"

/**
 * 
 */

class UBorder;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FMateDragBorderDelegate, bool, IsDrag, const FPointerEvent&, MouseEvent);

UCLASS()
class DESIGNSTATION_API UMateLayoutDragWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	static UMateLayoutDragWidget* Create();

private:
	void ClickToDragChangeMouseCursor(bool IsDrag);

public:
	FMateDragBorderDelegate MateDragBorderDelegate;

private:
	static FString MateDragBorderPath;

protected:
	UFUNCTION()
		FEventReply LeftMouseDownToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseUpToRelease(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent);
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent);

private:
	UPROPERTY()
		UBorder* BorDrag;
};
