// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCameraWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "MaterialFunctionLibrary.h"

FString UMaterialCameraWidget::CameraWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialCameraUI.MaterialCameraUI_C'");

bool UMaterialCameraWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnFrontView, Btn_FrontView);
	BIND_PARAM_CPP_TO_UMG(BtnRearView, Btn_RearView);
	BIND_PARAM_CPP_TO_UMG(BtnLeftView, Btn_LeftView);
	BIND_PARAM_CPP_TO_UMG(BtnRightView, Btn_RightView);
	BIND_PARAM_CPP_TO_UMG(BtnTopView, Btn_TopView);
	BIND_PARAM_CPP_TO_UMG(BtnTLFView, Btn_TLFView);
	BIND_PARAM_CPP_TO_UMG(BtnTLRView, Btn_TLRView);
	BIND_PARAM_CPP_TO_UMG(BtnTRFView, Btn_TRFView);
	BIND_PARAM_CPP_TO_UMG(BtnTRRView, Btn_TRRView);
	BIND_PARAM_CPP_TO_UMG(BtnImport, Btn_Import);
	BIND_PARAM_CPP_TO_UMG(BtnSure, Btn_Sure);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);

	BIND_PARAM_CPP_TO_UMG(TxtFrontView, Txt_FrontView);
	BIND_PARAM_CPP_TO_UMG(TxtRearView, Txt_RearView);
	BIND_PARAM_CPP_TO_UMG(TxtLeftView, Txt_LeftView);
	BIND_PARAM_CPP_TO_UMG(TxtRightView, Txt_RightView);
	BIND_PARAM_CPP_TO_UMG(TxtTopView, Txt_TopView);
	BIND_PARAM_CPP_TO_UMG(TxtTLFView, Txt_TLFView);
	BIND_PARAM_CPP_TO_UMG(TxtTLRView, Txt_TLRView);
	BIND_PARAM_CPP_TO_UMG(TxtTRFView, Txt_TRFView);
	BIND_PARAM_CPP_TO_UMG(TxtTRRView, Txt_TRRView);
	BIND_PARAM_CPP_TO_UMG(TxtImport, Txt_Import);
	BIND_PARAM_CPP_TO_UMG(TxtCancel, Txt_Cancel);

	BIND_PARAM_CPP_TO_UMG(ImgImport, Img_Import);

	BIND_WIDGET_FUNCTION(BtnFrontView, OnClicked, UMaterialCameraWidget::OnClickedFrontView);
	BIND_WIDGET_FUNCTION(BtnFrontView, OnReleased, UMaterialCameraWidget::OnReleasedFrontView);
	BIND_WIDGET_FUNCTION(BtnFrontView, OnPressed, UMaterialCameraWidget::OnPressedFrontView);


	BIND_WIDGET_FUNCTION(BtnRearView, OnClicked, UMaterialCameraWidget::OnClickedRearView);
	BIND_WIDGET_FUNCTION(BtnRearView, OnReleased, UMaterialCameraWidget::OnReleasedBtnRearView);
	BIND_WIDGET_FUNCTION(BtnRearView, OnPressed, UMaterialCameraWidget::OnPressedBtnRearView);

	BIND_WIDGET_FUNCTION(BtnLeftView, OnClicked, UMaterialCameraWidget::OnClickedLeftView);
	BIND_WIDGET_FUNCTION(BtnLeftView, OnReleased, UMaterialCameraWidget::OnReleasedBtnLeftView);
	BIND_WIDGET_FUNCTION(BtnLeftView, OnPressed, UMaterialCameraWidget::OnPressedBtnLeftView);

	BIND_WIDGET_FUNCTION(BtnRightView, OnClicked, UMaterialCameraWidget::OnClickedRightView);
	BIND_WIDGET_FUNCTION(BtnRightView, OnReleased, UMaterialCameraWidget::OnReleasedBtnRightView);
	BIND_WIDGET_FUNCTION(BtnRightView, OnPressed, UMaterialCameraWidget::OnPressedBtnRightView);

	BIND_WIDGET_FUNCTION(BtnTopView, OnClicked, UMaterialCameraWidget::OnClickedTopView);
	BIND_WIDGET_FUNCTION(BtnTopView, OnReleased, UMaterialCameraWidget::OnReleasedBtnTopView);
	BIND_WIDGET_FUNCTION(BtnTopView, OnPressed, UMaterialCameraWidget::OnPressedBtnTopView);

	BIND_WIDGET_FUNCTION(BtnTLFView, OnClicked, UMaterialCameraWidget::OnClickedTLFView);
	BIND_WIDGET_FUNCTION(BtnTLFView, OnReleased, UMaterialCameraWidget::OnReleasedBtnTLFView);
	BIND_WIDGET_FUNCTION(BtnTLFView, OnPressed, UMaterialCameraWidget::OnPressedBtnTLFView);

	BIND_WIDGET_FUNCTION(BtnTLRView, OnClicked, UMaterialCameraWidget::OnClickedTLRView);
	BIND_WIDGET_FUNCTION(BtnTLRView, OnReleased, UMaterialCameraWidget::OnReleasedBtnTLRView);
	BIND_WIDGET_FUNCTION(BtnTLRView, OnPressed, UMaterialCameraWidget::OnPressedBtnTLRView);

	BIND_WIDGET_FUNCTION(BtnTRFView, OnClicked, UMaterialCameraWidget::OnClickedTRFView);
	BIND_WIDGET_FUNCTION(BtnTRFView, OnReleased, UMaterialCameraWidget::OnReleasedBtnTRFView);
	BIND_WIDGET_FUNCTION(BtnTRFView, OnPressed, UMaterialCameraWidget::OnPressedBtnTRFView);

	BIND_WIDGET_FUNCTION(BtnTRRView, OnClicked, UMaterialCameraWidget::OnClickedTRRView);
	BIND_WIDGET_FUNCTION(BtnTRRView, OnReleased, UMaterialCameraWidget::OnReleasedBtnTRRView);
	BIND_WIDGET_FUNCTION(BtnTRRView, OnPressed, UMaterialCameraWidget::OnPressedBtnTRRView);

	BIND_WIDGET_FUNCTION(BtnImport, OnClicked, UMaterialCameraWidget::OnClickedImport);
	BIND_WIDGET_FUNCTION(BtnImport, OnReleased, UMaterialCameraWidget::OnReleasedBtnImport);
	BIND_WIDGET_FUNCTION(BtnImport, OnPressed, UMaterialCameraWidget::OnPressedBtnImport);

	BIND_WIDGET_FUNCTION(BtnSure, OnClicked, UMaterialCameraWidget::OnClickedSure);

	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UMaterialCameraWidget::OnClickedCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnReleased, UMaterialCameraWidget::OnReleasedBtnCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnPressed, UMaterialCameraWidget::OnPressedBtnCancel);


	BtnFrontView->SetIsEnabled(false);
	BtnRearView->SetIsEnabled(false);
	BtnLeftView->SetIsEnabled(false);
	BtnRightView->SetIsEnabled(false);
	BtnTopView->SetIsEnabled(false);
	BtnTLFView->SetIsEnabled(false);
	BtnTLRView->SetIsEnabled(false);
	BtnTRFView->SetIsEnabled(false);
	BtnTRRView->SetIsEnabled(false);
	BtnImport->SetIsEnabled(true);
	BtnSure->SetIsEnabled(true);
	BtnCancel->SetIsEnabled(true);

	return true;
}

UMaterialCameraWidget* UMaterialCameraWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCameraWidget>(UMaterialCameraWidget::CameraWidgetPath);
}

void UMaterialCameraWidget::OnClickedFrontView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::FrontView);
}

void UMaterialCameraWidget::OnClickedRearView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::RearView);
}

void UMaterialCameraWidget::OnClickedLeftView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::LeftView);
}

void UMaterialCameraWidget::OnClickedRightView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::RightView);
}

void UMaterialCameraWidget::OnClickedTopView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::TopView);
}

void UMaterialCameraWidget::OnClickedTLFView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::TLFView);
}

void UMaterialCameraWidget::OnClickedTLRView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::TLRView);
}

void UMaterialCameraWidget::OnClickedTRFView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::TRFView);
}

void UMaterialCameraWidget::OnClickedTRRView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::TRRView);
}

void UMaterialCameraWidget::OnClickedImport()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::Import);
}

void UMaterialCameraWidget::OnClickedSure()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::Sure);
}

void UMaterialCameraWidget::OnClickedCancel()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)EWidgetCameraBtnType::Cancel);
}

void UMaterialCameraWidget::OnReleasedFrontView()
{
	if (TxtFrontView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtFrontView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnRearView()
{
	if (TxtRearView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtRearView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnLeftView()
{
	if (TxtLeftView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtLeftView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnRightView()
{
	if (TxtRightView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtRightView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnTopView()
{
	if (TxtTopView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtTopView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnTLFView()
{
	if (TxtTLFView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtTLFView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnTLRView()
{
	if (TxtTLRView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtTLRView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnTRFView()
{
	if (TxtTRFView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtTRFView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnTRRView()
{
	if (TxtTRRView)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtTRRView);
	}
}

void UMaterialCameraWidget::OnReleasedBtnImport()
{
	if (TxtImport)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtImport);
	}
}

void UMaterialCameraWidget::OnReleasedBtnCancel()
{
	if (TxtCancel)
	{
		UMaterialFunctionLibrary::SetTextColor(MaterialTextNormal, TxtCancel);
	}
}

void UMaterialCameraWidget::OnPressedFrontView()
{
	if (TxtFrontView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtFrontView);
	}
}

void UMaterialCameraWidget::OnPressedBtnRearView()
{
	if (TxtRearView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtRearView);
	}
}

void UMaterialCameraWidget::OnPressedBtnLeftView()
{
	if (TxtLeftView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtLeftView);
	}
}

void UMaterialCameraWidget::OnPressedBtnRightView()
{
	if (TxtRightView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtRightView);
	}
}

void UMaterialCameraWidget::OnPressedBtnTopView()
{
	if (TxtTopView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtTopView);
	}
}

void UMaterialCameraWidget::OnPressedBtnTLFView()
{
	if (TxtTLFView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtTLFView);
	}
}

void UMaterialCameraWidget::OnPressedBtnTLRView()
{
	if (TxtTLRView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtTLRView);
	}
}

void UMaterialCameraWidget::OnPressedBtnTRFView()
{
	if (TxtTRFView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtTRFView);
	}
}

void UMaterialCameraWidget::OnPressedBtnTRRView()
{
	if (TxtTRRView)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtTRRView);
	}
}

void UMaterialCameraWidget::OnPressedBtnImport()
{
	if (TxtImport)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtImport);
	}
}

void UMaterialCameraWidget::OnPressedBtnCancel()
{
	if (TxtCancel)
	{
		UMaterialFunctionLibrary::SetTextColor(FLinearColor::White, TxtCancel);
	}
}

void UMaterialCameraWidget::SetBtnState(const int32& Type, bool IsEnable)
{
	switch (Type)
	{
	case (int32)EWidgetCameraBtnType::FrontView:
		BtnFrontView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::RearView:
		BtnRearView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::LeftView:
		BtnLeftView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::RightView:
		BtnRightView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::TopView:
		BtnTopView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::TLFView:
		BtnTLFView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::TLRView:
		BtnTLRView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::TRFView:
		BtnTRFView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::TRRView:
		BtnTRRView->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::Import:
		BtnImport->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::Sure:
		BtnSure->SetIsEnabled(IsEnable);
		break;
	case (int32)EWidgetCameraBtnType::Cancel:
		BtnCancel->SetIsEnabled(IsEnable);
		break;
	default:
		break;
	}
}

void UMaterialCameraWidget::SetImage(const FString& ImagePath)
{
	UTexture2D* NewTex = FCatalogFunctionLibrary::LoadTextureFromJPG(ImagePath);
	ImgImport->SetBrushFromTexture(NewTex);
}

void UMaterialCameraWidget::ShowImage(bool IsShow)
{
	if (IsShow)
	{
		ImgImport->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ImgImport->SetVisibility(ESlateVisibility::Collapsed);
	}
}


