// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialCameraWidget.generated.h"

/**
 * 
 */

DECLARE_DYNAMIC_DELEGATE_OneParam(FMaterialCameraBtnTypeDelegate, const int32&, BtnType);

class UButton;
class UTextBlock;
class UImage;

UENUM(BlueprintType)
enum class EWidgetCameraBtnType : uint8
{
	FrontView = 0,
	RearView,
	LeftView,
	RightView,
	TopView,
	TLFView,
	TLRView,
	TRFView,
	TRRView,
	Import,
	Sure,
	Cancel
};

const FLinearColor MaterialTextNormal = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);

UCLASS()
class DESIGNSTATION_API UMaterialCameraWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UMaterialCameraWidget* Create();
private:
	static FString CameraWidgetPath;

protected:
	UFUNCTION()
		void OnClickedFrontView();
	UFUNCTION()
		void OnClickedRearView();
	UFUNCTION()
		void OnClickedLeftView();
	UFUNCTION()
		void OnClickedRightView();
	UFUNCTION()
		void OnClickedTopView();
	UFUNCTION()
		void OnClickedTLFView();
	UFUNCTION()
		void OnClickedTLRView();
	UFUNCTION()
		void OnClickedTRFView();
	UFUNCTION()
		void OnClickedTRRView();
	UFUNCTION()
		void OnClickedImport();
	UFUNCTION()
		void OnClickedSure();
	UFUNCTION()
		void OnClickedCancel();

	UFUNCTION()
		void OnPressedFrontView();
	UFUNCTION()
		void OnPressedBtnRearView();
	UFUNCTION()
		void OnPressedBtnLeftView();
	UFUNCTION()
		void OnPressedBtnRightView();
	UFUNCTION()
		void OnPressedBtnTopView();
	UFUNCTION()
		void OnPressedBtnTLFView();
	UFUNCTION()
		void OnPressedBtnTLRView();
	UFUNCTION()
		void OnPressedBtnTRFView();
	UFUNCTION()
		void OnPressedBtnTRRView();
	UFUNCTION()
		void OnPressedBtnImport();
	UFUNCTION()
		void OnPressedBtnCancel();

	UFUNCTION()
		void OnReleasedFrontView();
	UFUNCTION()
		void OnReleasedBtnRearView();
	UFUNCTION()
		void OnReleasedBtnLeftView();
	UFUNCTION()
		void OnReleasedBtnRightView();
	UFUNCTION()
		void OnReleasedBtnTopView();
	UFUNCTION()
		void OnReleasedBtnTLFView();
	UFUNCTION()
		void OnReleasedBtnTLRView();
	UFUNCTION()
		void OnReleasedBtnTRFView();
	UFUNCTION()
		void OnReleasedBtnTRRView();
	UFUNCTION()
		void OnReleasedBtnImport();
	UFUNCTION()
		void OnReleasedBtnCancel();
private:
	UPROPERTY()
		UButton*	BtnFrontView;
	UPROPERTY()
		UButton*	BtnRearView;
	UPROPERTY()
		UButton*	BtnLeftView;
	UPROPERTY()
		UButton*	BtnRightView;
	UPROPERTY()
		UButton*	BtnTopView;
	UPROPERTY()
		UButton*	BtnTLFView;
	UPROPERTY()
		UButton*	BtnTLRView;
	UPROPERTY()
		UButton*	BtnTRFView;
	UPROPERTY()
		UButton*	BtnTRRView;
	UPROPERTY()
		UButton*	BtnImport;
	UPROPERTY()
		UButton*	BtnSure;
	UPROPERTY()
		UButton*	BtnCancel;
	UPROPERTY()
		UTextBlock* TxtFrontView;
	UPROPERTY()
		UTextBlock* TxtRearView;
	UPROPERTY()
		UTextBlock* TxtLeftView;
	UPROPERTY()
		UTextBlock* TxtRightView;
	UPROPERTY()
		UTextBlock* TxtTopView;
	UPROPERTY()
		UTextBlock* TxtTLFView;
	UPROPERTY()
		UTextBlock* TxtTLRView;
	UPROPERTY()
		UTextBlock* TxtTRFView;
	UPROPERTY()
		UTextBlock* TxtTRRView;
	UPROPERTY()
		UTextBlock* TxtImport;
	UPROPERTY()
		UTextBlock* TxtCancel;
	UPROPERTY()
		UImage*		ImgImport;

public:
	void SetBtnState(const int32& Type, bool IsBan);
	void SetImage(const FString& ImagePath);
	void ShowImage(bool IsShow);
public:
	FMaterialCameraBtnTypeDelegate	CameraBtnTypeDelegate;
};
