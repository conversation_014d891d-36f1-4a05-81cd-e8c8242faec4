// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCategoryMenuWidget.h"
#include "MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/WrapBox.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/MenuAnchor.h"



FString UMaterialCategoryMenuWidget::MaterialCategoryMenuWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialCategoryUI.MaterialCategoryUI_C'");

bool UMaterialCategoryMenuWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(TxtCategory, Txt_Category);
	BIND_PARAM_CPP_TO_UMG(EdtSearch, Edt_Search);
	BIND_PARAM_CPP_TO_UMG(BtnSearch, Btn_Search);
	BIND_PARAM_CPP_TO_UMG(SBCategoryFirstMenu, SB_CategoryName);
	BIND_PARAM_CPP_TO_UMG(WPMaterialData, WP_Content);
	BIND_PARAM_CPP_TO_UMG(MASecondMenu, MA_SecondMenu);
	BIND_SLATE_WIDGET_FUNCTION(MASecondMenu, OnGetUserMenuContentEvent, FName(TEXT("CreateSecondMenu")));

	BIND_PARAM_CPP_TO_UMG(SBSecondMenuBox, SB_SecondMenuBox);

	BIND_WIDGET_FUNCTION(EdtSearch, OnTextCommitted, UMaterialCategoryMenuWidget::OnTextCommittedSearch);
	BIND_WIDGET_FUNCTION(EdtSearch, OnTextChanged, UMaterialCategoryMenuWidget::OnTextChangedEdtSearch);
	BIND_WIDGET_FUNCTION(BtnSearch,OnClicked, UMaterialCategoryMenuWidget::OnClickedSearchBtn);

	//MaterialCategoryFirstMenu = UMaterialCategoryFirstMenu::Create();
	//SBCategoryFirstMenu->AddChild(MaterialCategoryFirstMenu);

	//MaterialCategorySecondMenu = UMaterialCategorySecondMenuWidget::Create();
	//SBSecondMenu->AddChild(MaterialCategorySecondMenu);
	//SBSecondMenu->AddChild(MaterialCategorySecondMenuWidget);


	if (!MaterialCategorySecondMenuWidget)
	{
		MaterialCategorySecondMenuWidget = UMaterialCategorySecondMenuWidget::Create();
		MaterialCategorySecondMenuWidget->MaterialCategoryIdDelegate.BindUFunction(this, FName(TEXT("OpenMaterialList")));
	}

	//MASecondMenu->SetContent(MaterialCategorySecondMenuWidget);

	CurrentLevelId = -1;
	return true;
}

UMaterialCategoryMenuWidget * UMaterialCategoryMenuWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCategoryMenuWidget>(UMaterialCategoryMenuWidget::MaterialCategoryMenuWidgetPath);
}

void UMaterialCategoryMenuWidget::NativeTick(const FGeometry & MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	//GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, TEXT("URoMWidget::NativeTick()"));


	for (auto& iter : MaterialCategoryFirstMenuName)
	{
		if (iter->GetId()== SelectFirstId && (MASecondMenu->IsOpen()||MASecondMenu->GetVisibility()==ESlateVisibility::Collapsed|| MenuIsClicked||SecMenuIsSelected))
		{
			iter->SetSelectColor();
		}
		else
		{
			iter->SetUnSelectColor();
		}
	}
	
}

void UMaterialCategoryMenuWidget::UpdateContent(const TArray<FMaterialLevelInfo> &FirstMenu)
{
	SBCategoryFirstMenu->ClearChildren();
	MaterialCategoryFirstMenuName.Empty();
	AddAllOption();
	for (auto& iter: FirstMenu)
	{
		AddFirstMenuChild(iter);
	}
}

void UMaterialCategoryMenuWidget::SetCurrentCategory(const FString & Text)
{
	TxtCategory->SetText(FText::FromString(Text));
}

void UMaterialCategoryMenuWidget::OnTextChangedEdtSearch(const FText & Text)
{
	if (Text.IsEmpty())
	{
		MaterialCategoryIdDelegate.ExecuteIfBound(CurrentLevelId,false);
	}
	else
	{
		MaterialSearchDelegate.ExecuteIfBound(CurrentLevelId, Text.ToString());
	}
}

void UMaterialCategoryMenuWidget::OnTextCommittedSearch(const FText & Text, ETextCommit::Type CommitMethod)
{
}

void UMaterialCategoryMenuWidget::OnClickedSearchBtn()
{
}

void UMaterialCategoryMenuWidget::SetSecondMenu(const TArray<FMaterialLevelInfo> &SecondMenu)
{
	MaterialCategorySecondMenuWidget->UpdateContent(SecondMenu);
}





void UMaterialCategoryMenuWidget::SetMaterialData(const TMap<int32, FString>& MaterialData)
{
	MaterialDataTemp.Empty();
	MaterialDataTemp = MaterialData;
	UpdateMaterialData(MaterialData);
}

void UMaterialCategoryMenuWidget::UpdateMaterialData(const TMap<int32, FString>& MaterialData)
{
	WPMaterialData->ClearChildren();
	for (auto&iter : MaterialData)
	{
		AddMaterialDataChild(iter.Key, iter.Value);
	}
}
void UMaterialCategoryMenuWidget::OpenSecondMenu(const int32& id, bool state, const float& height)
{
	UE_LOG(LogTemp, Log, TEXT("%d"), id);
	MASecondMenu->Close();
	MaterialCategoryIdDelegate.ExecuteIfBound(id, true);

	SBSecondMenuBox->SetHeightOverride(height);
	SelectFirstId = id;
	MASecondMenu->Open(!state);
	MenuIsClicked = state;
	if (state)
	{
		if (MASecondMenu->IsOpen())
		{
			MASecondMenu->Close();
		}
		UpdateMaterialData(MaterialDataTemp);
	}

	//for (auto& iter: MaterialCategoryFirstMenuName)
	//{

	//	if (SelectFirstId == iter->GetId())
	//	{
	//		iter->SetSelectColor();
	//		TxtCategory->SetText(FText::FromString(iter->GetName()));
	//	}
	//	else
	//	{
	//		iter->SetSelectState(false);
	//		iter->SetUnSelectColor();
	//	}

	//}
	CurrentLevelId = id;
	//SBSecondMenu->SetVisibility(ESlateVisibility::Visible);
	
}

void UMaterialCategoryMenuWidget::OpenMaterialData(const int32& id, bool IsFirst)
{
	MaterialDateIdDelegate.ExecuteIfBound(id,false);
}

void UMaterialCategoryMenuWidget::AddAllOption()
{
	UMaterialCategoryFirstMenu* AllOption = UMaterialCategoryFirstMenu::Create();
	AllOption->SetCategoryInfo(-1, FText::FromStringTable(FName("PosSt"), TEXT("All")).ToString());
	AllOption->MaterialCategoryDelegate.BindUFunction(this, FName(TEXT("OpenSecondMenu")));
	SBCategoryFirstMenu->AddChild(AllOption);
	MaterialCategoryFirstMenuName.Add(AllOption);
}

void UMaterialCategoryMenuWidget::AddFirstMenuChild(const FMaterialLevelInfo& MatInfo)
{
	if (SBCategoryFirstMenu)
	{
		UMaterialCategoryFirstMenu* MaterialCategoryFirstMenuWidget = UMaterialCategoryFirstMenu::Create();
		MaterialCategoryFirstMenuWidget->SetCategoryInfo(MatInfo.id, MatInfo.level_name);
		MaterialCategoryFirstMenuWidget->MaterialCategoryDelegate.BindUFunction(this, FName(TEXT("OpenSecondMenu")));
		SBCategoryFirstMenu->AddChild(MaterialCategoryFirstMenuWidget);
		MaterialCategoryFirstMenuName.Add(MaterialCategoryFirstMenuWidget);
	}
}

void UMaterialCategoryMenuWidget::AddSecondMenuChild(const FMaterialLevelInfo & MatInfo)
{
	if (MASecondMenu)
	{
		UMaterialCategoryTextWidget* MaterialCategoryTextWidget = UMaterialCategoryTextWidget::Create();
		MaterialCategoryTextWidget->SetCategortInfo(MatInfo.id, MatInfo.level_name);
		
		MaterialCategoryTextWidget->MaterialCategoryIdDelegate.BindUFunction(this, FName(TEXT("OpenMaterialList")));
		
	}
}

void UMaterialCategoryMenuWidget::AddMaterialDataChild(const int32& Id,const FString& ImgPath)
{	
	if (WPMaterialData)
	{
		UMaterialCategoryImageWidget* MaterialCategoryImageWidget = UMaterialCategoryImageWidget::Create();
		MaterialCategoryImageWidget->SetMaterialData(Id, ImgPath);
		WPMaterialData->AddChild(MaterialCategoryImageWidget);
		MaterialCategoryImageWidget->MaterialCategoryIdDelegate.BindUFunction(this, FName(TEXT("OpenMaterialData")));
	}
}

void UMaterialCategoryMenuWidget::SetSecondMenuContent()
{
	UE_LOG(LogTemp, Log, TEXT("SetSecondMenuContent"));
	MASecondMenu->SetContent(MaterialCategorySecondMenuWidget);
}

UMaterialCategorySecondMenuWidget * UMaterialCategoryMenuWidget::CreateSecondMenu()
{	
	UE_LOG(LogTemp, Log, TEXT("create SecondMenuContent %d"), MaterialCategorySecondMenuWidget->GetChildNum());
	
	if (MaterialCategorySecondMenuWidget->GetChildNum()<=0 || SelectFirstId == -1)
	{
		MASecondMenu->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		MASecondMenu->SetVisibility(ESlateVisibility::Visible);
	}
	return MaterialCategorySecondMenuWidget;
}

void UMaterialCategoryMenuWidget::OpenMaterialList(const int32& id,bool IsFirst)
{
	UE_LOG(LogTemp, Log, TEXT("%d"),id);
	CurrentLevelId = id;
	MaterialCategoryIdDelegate.ExecuteIfBound(id,false);
	UpdateMaterialData(MaterialDataTemp);
	MASecondMenu->Close();
}


