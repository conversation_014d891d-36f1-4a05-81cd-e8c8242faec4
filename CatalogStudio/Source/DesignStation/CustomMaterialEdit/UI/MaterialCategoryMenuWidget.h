// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CustomMaterialEdit/UI/MaterialItam/MaterialCategoryFirstMenu.h"
#include "CustomMaterialEdit/UI/MaterialItam/MaterialCategorySecondMenuWidget.h"
#include "CustomMaterialEdit/UI/MaterialItam/MaterialCategoryImageWidget.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "MaterialCategoryMenuWidget.generated.h"

/**
 *
 */

class UButton;
class UTextBlock;
class UEditableText;
class UScrollBox;
class UWrapBox;
class USizeBox;
class UCanvasPanel;
class UMenuAnchor;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FMaterialSearchDelegate, const int32&, LevelId, const FString&, MateName);

UCLASS()
class DESIGNSTATION_API UMaterialCategoryMenuWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	static UMaterialCategoryMenuWidget* Create();
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
private:
	static FString MaterialCategoryMenuWidgetPath;
public:
	void UpdateContent(const TArray<FMaterialLevelInfo>& FirstMenu);
	void SetSecondMenu(const TArray<FMaterialLevelInfo>& SecondMenu);
	void SetMaterialData(const TMap<int32, FString>& MaterialData);
protected:
	UFUNCTION()
		void SetCurrentCategory(const FString& Text);
	UFUNCTION()
		void OnTextChangedEdtSearch(const FText& Text);
	UFUNCTION()
		void OnTextCommittedSearch(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedSearchBtn();
	UFUNCTION()
		void OpenSecondMenu(const int32& id, bool state, const float& height);

	UFUNCTION()
		void OpenMaterialList(const int32& id, bool IsFirst);
	UFUNCTION()
		void OpenMaterialData(const int32& id, bool IsFirst);
	UFUNCTION()
		void AddAllOption();
protected:
	void AddFirstMenuChild(const FMaterialLevelInfo& MatInfo);
	void AddSecondMenuChild(const FMaterialLevelInfo& MatInfo);
	void AddMaterialDataChild(const int32& Id, const FString& ImgPath);
	UFUNCTION()
		void SetSecondMenuContent();
	UFUNCTION()
		UMaterialCategorySecondMenuWidget* CreateSecondMenu();
	UFUNCTION()
		void UpdateMaterialData(const TMap<int32, FString>& MaterialData);

private:
	int32	SelectFirstId;
	bool	SecMenuIsSelected;
	int32	CurrentLevelId;
	bool	MenuIsClicked;
	TMap<int32, FString>  MaterialDataTemp;
private:
	UPROPERTY()
		UTextBlock* TxtCategory;
	UPROPERTY()
		UButton* BtnSearch;
	UPROPERTY()
		UEditableText* EdtSearch;
	UPROPERTY()
		UScrollBox* SBCategoryFirstMenu;
	UPROPERTY()
		UWrapBox* WPMaterialData;
	UPROPERTY()
		USizeBox* SBSecondMenu;
	UPROPERTY()
		USizeBox* SBSecondMenuBox;
	UPROPERTY()
		UMenuAnchor* MASecondMenu;
private:
	UPROPERTY()
		TArray<UMaterialCategoryFirstMenu*>	MaterialCategoryFirstMenuName;
	UPROPERTY()
		UMaterialCategorySecondMenuWidget* MaterialCategorySecondMenuWidget;
public:
	FMaterialCategoryIdDelegate	MaterialCategoryIdDelegate;

	FMaterialCategoryIdDelegate MaterialDateIdDelegate;

	//FMaterialCategoryIdDelegate	MaterialDateIdDelegate;
	FMaterialSearchDelegate MaterialSearchDelegate;


};
