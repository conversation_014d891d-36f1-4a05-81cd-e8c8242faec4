// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialEditPropertyWidget.h"
#include "MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "MaterialFunctionLibrary.h"

FString UMaterialEditPropertyWidget::MaterialPropertyPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialPropertyUI.MaterialPropertyUI_C'");

bool UMaterialEditPropertyWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(SCBContent, SCB_Content);
	BIND_PARAM_CPP_TO_UMG(EdtId, Edt_Id);
	BIND_PARAM_CPP_TO_UMG(EdtCoding, Edt_Coding);
	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_PARAM_CPP_TO_UMG(EdtVisibleExpress, Edt_Express);
	BIND_PARAM_CPP_TO_UMG(BtnVisibleExpress, Btn_Express);
	BIND_PARAM_CPP_TO_UMG(EdtVisibleValue, Edt_Value);

	BIND_WIDGET_FUNCTION(EdtId, OnTextCommitted, UMaterialEditPropertyWidget::OnTextCommittedEdtId);
	BIND_WIDGET_FUNCTION(EdtCoding, OnTextCommitted, UMaterialEditPropertyWidget::OnTextCommittedEdtCoding);
	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, UMaterialEditPropertyWidget::OnTextCommittedEdtName);
	BIND_WIDGET_FUNCTION(EdtVisibleExpress, OnTextCommitted, UMaterialEditPropertyWidget::OnTextCommittedEdtVisibleExpress);
	BIND_WIDGET_FUNCTION(BtnVisibleExpress, OnClicked, UMaterialEditPropertyWidget::OnClickedBtnVisibleExpress);
	BIND_WIDGET_FUNCTION(EdtVisibleValue, OnTextCommitted, UMaterialEditPropertyWidget::OnTextCommittedEdtVisibleValue);


	return true;
}

void UMaterialEditPropertyWidget::UpdateContent(const FString & InID, const FString & InCoding, const FString & InName, const FString & InVisiExpress, const FString & InVisiValue)
{
	MaterialId = InID;
	MaterialCoding = InCoding;
	MaterialName = InName;
	MaterialVisiExpress = InVisiExpress;
	MaterialVisiValue = InVisiValue;
	if (EdtId && EdtCoding && EdtName && EdtVisibleExpress && EdtVisibleValue)
	{
		EdtId->SetText(FText::FromString(InID));
		EdtCoding->SetText(FText::FromString(InCoding));
		EdtName->SetText(FText::FromString(InName));
		EdtVisibleExpress->SetText(FText::FromString(InVisiExpress));
		EdtVisibleValue->SetText(FText::FromString(InVisiValue));
	}
}

void UMaterialEditPropertyWidget::UpdateContent(const TArray<UMatParameterGroup*> InGroups)
{
	checkf(SCBContent, TEXT("property content is null"));
	SCBContent->ClearChildren();
	MaterialGroups.Empty();
	for (auto& ParamGroup : InGroups)
	{
		if (ParamGroup->GroupItems.Num() == 0)
		{
			continue;
		}
		UMaterialItemGroupWidget* GroupWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemGroupWidget>();
		GroupWidget->MaterialPropertyItemDelegate.BindUFunction(this, FName(TEXT("MaterialItemPropertyEdit")));
		GroupWidget->MaterialFloatDataItemDelegate.BindUFunction(this, FName(TEXT("OnFloatItemDataUpdateHandler")));
		GroupWidget->MaterialSyncImageDelegate.BindUFunction(this, FName(TEXT("OnSyncMaterialImageHandler")));
		GroupWidget->MaterialGroupExtentDelegate.BindUFunction(this, FName(TEXT("OnGroupExtentHandler")));
		GroupWidget->UpdateContent(ParamGroup->GroupItems);
		GroupWidget->SetGroupName(ParamGroup->ParameterName);
		GroupWidget->SetIsExpand(ParamGroup->IsExtent);
		GroupWidget->SetVisibility(ESlateVisibility::Visible);
		SCBContent->AddChild(GroupWidget);
		MaterialGroups.Add(GroupWidget);
	}
}

void UMaterialEditPropertyWidget::Clear()
{
	for (auto& GroupData : MaterialGroups)
	{
		GroupData->Clear();
	}
	MaterialGroups.Empty();
	if (SCBContent)
	{
		SCBContent->ClearChildren();
		SCBContent = nullptr;
	}
	if (EdtId && EdtCoding && EdtName && EdtVisibleExpress && BtnVisibleExpress && EdtVisibleValue)
	{
		EdtId = nullptr;
		EdtCoding = nullptr;
		EdtName = nullptr;
		EdtVisibleExpress = nullptr;
		BtnVisibleExpress = nullptr;
		EdtVisibleValue = nullptr;
	}
}

UMaterialEditPropertyWidget * UMaterialEditPropertyWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialEditPropertyWidget>(UMaterialEditPropertyWidget::MaterialPropertyPath);
}

void UMaterialEditPropertyWidget::MaterialItemPropertyEdit(const int32 & ParamId, const FString & ItemValue, bool IsEnableFlip)
{
	MaterialPropertyDataDelegate.ExecuteIfBound(ParamId, ItemValue, IsEnableFlip);
}

void UMaterialEditPropertyWidget::OnFloatItemDataUpdateHandler(const int32 & FloatId, const FString & FloatValue)
{
	MaterialFloatNodeDBWriteDelegate.ExecuteIfBound(FloatId, FloatValue);
}

void UMaterialEditPropertyWidget::OnSyncMaterialImageHandler(const int32& ImageId, const FString& ImagePath)
{
	MaterialGroupSyncImageDelegate.ExecuteIfBound(ImageId, ImagePath);
}

void UMaterialEditPropertyWidget::OnGroupExtentHandler(const FString & GroupName, bool IsExtent)
{
	MaterialGroupExtentPropertyDelegate.ExecuteIfBound(GroupName, IsExtent);
}

void UMaterialEditPropertyWidget::OnTextCommittedEdtId(const FText & Text, ETextCommit::Type CommitMethod)
{

	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString	InputText;
		InputText = Text.ToString();
		InputText = InputText.TrimStart();
		FRegexPattern Pattern(TEXT("[.]"));
		FRegexMatcher RegMatcher(Pattern, InputText);
		RegMatcher.SetLimits(0, InputText.Len());
		if (!RegMatcher.FindNext()&& (InputText.IsNumeric()||InputText.IsEmpty())&& InputText.Len()<=8)
		{
			MaterialId = InputText;
			MaterialFilePropertyDelegate.ExecuteIfBound((int32)EMaterialPropertyType::Id, MaterialId);
			EdtId->SetText(FText::FromString(MaterialId));
		}
		else
		{
			EdtId->SetText(FText::FromString(MaterialId));

		}
	}
}

void UMaterialEditPropertyWidget::OnTextCommittedEdtCoding(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString	InputText;
		InputText = Text.ToString();
		InputText = InputText.TrimStart();
		InputText = InputText.TrimEnd();
		FRegexPattern Pattern(TEXT("[\u4e00-\u9fa5]"));
		FRegexMatcher RegMatcher(Pattern, InputText);
		RegMatcher.SetLimits(0, InputText.Len());
		if (!RegMatcher.FindNext() && InputText.Len()<=20)
		{
			MaterialCoding = InputText;
			MaterialFilePropertyDelegate.ExecuteIfBound((int32)EMaterialPropertyType::Coding, MaterialCoding);
			EdtCoding->SetText(FText::FromString(MaterialCoding));
		}
		else
		{
			EdtCoding->SetText(FText::FromString(MaterialCoding));
		}
	}
}

void UMaterialEditPropertyWidget::OnTextCommittedEdtName(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString	InputText;
	InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared && InputText.Len()<=20)
	{
		MaterialName = InputText;
		MaterialFilePropertyDelegate.ExecuteIfBound((int32)EMaterialPropertyType::Name, MaterialName);
		EdtName->SetText(FText::FromString(MaterialName));

	}
	else if (EdtName && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtName->SetText(FText::FromString(MaterialName));
	}
}

void UMaterialEditPropertyWidget::OnTextCommittedEdtVisibleExpress(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		MaterialFilePropertyDelegate.ExecuteIfBound((int32)EMaterialPropertyType::VisiExpress, Text.ToString());
	}
	else if (EdtVisibleExpress && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtVisibleExpress->SetText(FText::FromString(MaterialVisiExpress));
	}
}

void UMaterialEditPropertyWidget::OnClickedBtnVisibleExpress()
{
	//if (EdtVisibleExpress)
	//{
	//	BIND_EXPRESSION_WIDGET((int32)EPropertyExpressType::Visible, EdtVisibleExpress->GetText().ToString(), FName(TEXT("OnExpressionHandler")));
	//}
}

void UMaterialEditPropertyWidget::OnTextCommittedEdtVisibleValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		MaterialFilePropertyDelegate.ExecuteIfBound((int32)EMaterialPropertyType::VisiValue, Text.ToString());
	}
	else if (EdtVisibleValue && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtVisibleValue->SetText(FText::FromString(MaterialVisiValue));
	}
}

void UMaterialEditPropertyWidget::OnExpressionHandler(const int32 & EditType, const FString & OutExpression)
{
	//if (EditType == (int32)EPropertyExpressType::Visible)
	//{
	//	OnTextCommittedEdtVisibleExpress(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	//}
}