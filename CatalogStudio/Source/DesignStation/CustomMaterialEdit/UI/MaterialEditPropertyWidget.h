// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialItam/MaterialItemGroupWidget.h"
#include "MaterialEditPropertyWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMaterialPropertyType : uint8
{
	Id = 0,
	Coding,
	Name,
	VisiExpress,
	VisiValue
};

class UEditableText;
class UButton;
class UBorder;
class UScrollBox;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FMaterialPropertyDelegate, const int32&, EditType, const FString&, OutString);

UCLASS()
class DESIGNSTATION_API UMaterialEditPropertyWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FString& InID, const FString& InCoding, const FString& InName, const FString& InVisiExpress, const FString& InVisiValue);
	void UpdateContent(const TArray<UMatParameterGroup*> InGroups);
	void Clear();

	static UMaterialEditPropertyWidget* Create();

public:
	UFUNCTION()
		void MaterialItemPropertyEdit(const int32& ParamId, const FString& ItemValue, bool IsEnableFlip);
	UFUNCTION()
		void OnFloatItemDataUpdateHandler(const int32& FloatId, const FString& FloatValue);
	UFUNCTION()
		void OnSyncMaterialImageHandler(const int32& ImageId, const FString& ImagePath);
	UFUNCTION()
		void OnGroupExtentHandler(const FString& GroupName, bool IsExtent);

protected:

	UFUNCTION()
		void OnTextCommittedEdtId(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtVisibleExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisibleExpress();
	UFUNCTION()
		void OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnExpressionHandler(const int32 & EditType, const FString & OutExpression);

public:
	FMaterialPropertyDelegate MaterialFilePropertyDelegate;
	FMaterialPropertyItemDelegate MaterialPropertyDataDelegate;
	FFloatNodeDBWriteDelegate MaterialFloatNodeDBWriteDelegate;
	FGroupExtentDelegate MaterialGroupExtentPropertyDelegate;
	FSyncMaterialImageDelegate MaterialGroupSyncImageDelegate;
	
private:
	FString MaterialId;
	FString MaterialCoding;
	FString MaterialName;
	FString MaterialVisiExpress;
	FString MaterialVisiValue;
	UPROPERTY()
		TArray<UMaterialItemGroupWidget*> MaterialGroups;

	static FString MaterialPropertyPath;

private:
	UPROPERTY()
		UScrollBox*		SCBContent;
	UPROPERTY()
		UEditableText*	EdtId;
	UPROPERTY()
		UEditableText*	EdtCoding;
	UPROPERTY()
		UEditableText*	EdtName;
	UPROPERTY()
		UEditableText*	EdtVisibleExpress;
	UPROPERTY()
		UEditableText*	EdtVisibleValue;
	UPROPERTY()
		UButton*		BtnVisibleExpress;
};
