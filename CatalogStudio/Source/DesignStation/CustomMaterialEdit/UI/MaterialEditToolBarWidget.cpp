// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialEditToolBarWidget.h"
#include "MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Button.h"

FString UMaterialEditToolBarWidget::MaterialToolBarPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialToolBarUI.MaterialToolBarUI_C'");

bool UMaterialEditToolBarWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, UMaterialEditToolBarWidget::OnClickedBtnSave);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, UMaterialEditToolBarWidget::OnClickedBtnExit);
	BtnSave->SetVisibility(ESlateVisibility::Collapsed);
	

	return true;
}

void UMaterialEditToolBarWidget::Clear()
{
	if (BtnSave && BtnExit)
	{
		BtnSave = nullptr;
		BtnExit = nullptr;
	}
}

UMaterialEditToolBarWidget * UMaterialEditToolBarWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialEditToolBarWidget>(UMaterialEditToolBarWidget::MaterialToolBarPath);
}

void UMaterialEditToolBarWidget::OnClickedBtnSave()
{
	UE_LOG(LogTemp, Log, TEXT("material edit click save"));
	MaterialToolBarDelegate.ExecuteIfBound(EMaterialToolBarType::Save);
}

void UMaterialEditToolBarWidget::OnClickedBtnExit()
{
	UE_LOG(LogTemp, Log, TEXT("material edit click exit"));
	MaterialToolBarDelegate.ExecuteIfBound(EMaterialToolBarType::Exit);
}
