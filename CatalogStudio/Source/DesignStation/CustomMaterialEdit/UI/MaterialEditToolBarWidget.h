// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialEditToolBarWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMaterialToolBarType : uint8
{
	Save = 0,
	Exit
};

class UButton;

DECLARE_DYNAMIC_DELEGATE_OneParam(FMaterialToolBarDelegate, const EMaterialToolBarType&, EditType);

UCLASS()
class DESIGNSTATION_API UMaterialEditToolBarWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void Clear();

	static UMaterialEditToolBarWidget* Create();

public:
	FMaterialToolBarDelegate MaterialToolBarDelegate;

private:
	static FString MaterialToolBarPath;

protected:
	UFUNCTION()
		void OnClickedBtnSave();
	UFUNCTION()
		void OnClickedBtnExit();
	
private:
	UPROPERTY()
		UButton* BtnSave;
	UPROPERTY()
		UButton* BtnExit;
};
