// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialEditUIManager.h"

const int MaterialZOrder = 5;

void UMaterialEditUIManager::InitMaterialEditUI()
{
	UpdateMaterialToolBarWidget();
	UpdateMaterialPropertyWidget();
	InitLevelWidegt();
	UpdateMaterialLayoutWidget();
}

void UMaterialEditUIManager::InitMaterialEditUI(const TArray<UMatParameterGroup*> Parameters, const TArray<FMaterialLevelInfo>& FirstMenu)
{
	//UpdateMaterialToolBarWidget();
	UpdateMaterialPropertyWidget(Parameters);
	InitLevelWidegt(FirstMenu);
	//UpdateMaterialLayoutWidget();
}

void UMaterialEditUIManager::UpdateMaterialLayoutWidget()
{
	if (!MaterialLayoutWidget)
	{
		MaterialLayoutWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialLayoutWidget>();
		MaterialLayoutWidget->AddToViewport(MaterialZOrder);
		MaterialLayoutWidget->PictureTypeDelegate.BindUFunction(this, FName(TEXT("MaterialComponentPictureType")));
		MaterialLayoutWidget->ImportCustomMaterialDelegate.BindUFunction(this, FName(TEXT("OnImportCustomMaterialHandler")));
	}
	if (MaterialLayoutWidget)
	{
		MaterialLayoutWidget->SetVisibility(ESlateVisibility::Visible);
		if (MaterialToolBarWidget && MaterialPropertyWidget && MaterialCategoryMenuWidget)
		{
			MaterialLayoutWidget->AddToolBarWidget(MaterialToolBarWidget);
			MaterialLayoutWidget->AddPropertyWidget(MaterialPropertyWidget);
			MaterialLayoutWidget->AddMaterialLevelWidget(MaterialCategoryMenuWidget);
		}
	}
}

UUserWidget* UMaterialEditUIManager::GetImportMaterialWidget()
{
	return MaterialLayoutWidget ? MaterialLayoutWidget->GetImportWidget() : nullptr;
}

void UMaterialEditUIManager::UpdateImportButtonVisibility(ESlateVisibility NewVisibility)
{
	if (MaterialLayoutWidget)
	{
		MaterialLayoutWidget->UpdateImportButtonVisibility(NewVisibility);
	}
}

void UMaterialEditUIManager::OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath)
{
	ImportCustomMaterialDelegate.ExecuteIfBound(OriginalFilePath, SavePath, RefPath);
}

void UMaterialEditUIManager::UpdateMaterialToolBarWidget()
{
	if (!MaterialToolBarWidget)
	{
		MaterialToolBarWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialEditToolBarWidget>();
	}
	if (MaterialToolBarWidget)
	{
		MaterialToolBarWidget->MaterialToolBarDelegate.BindUFunction(this, FName(TEXT("MaterialToolBarEdit")));
		MaterialToolBarWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMaterialEditUIManager::InitLevelWidegt()
{
	if (!MaterialCategoryMenuWidget)
	{
		MaterialCategoryMenuWidget = UMaterialCategoryMenuWidget::Create();
	}
}

void UMaterialEditUIManager::InitLevelWidegt(const TArray<FMaterialLevelInfo>& FirstMenu)
{
	if (!MaterialCategoryMenuWidget)
	{
		MaterialCategoryMenuWidget = UMaterialCategoryMenuWidget::Create();
	}
	MaterialCategoryMenuWidget->UpdateContent(FirstMenu);
	MaterialCategoryMenuWidget->MaterialCategoryIdDelegate.BindUFunction(this, FName(TEXT("OnMaterialLevelHandler")));
	MaterialCategoryMenuWidget->MaterialDateIdDelegate.BindUFunction(this, FName(TEXT("OnBasicMaterialSelectHandler")));
	MaterialCategoryMenuWidget->MaterialSearchDelegate.BindUFunction(this, FName(TEXT("OnBasicMaterialSearchHandler")));
	MaterialCategoryMenuWidget->SetVisibility(ESlateVisibility::Visible);
}

void UMaterialEditUIManager::UpdateMaterialPropertyWidget(const FString& InID, const FString& InCoding, const FString& InName, const FString& InVisiExpress, const FString& InVisiValue)
{
	if (MaterialPropertyWidget)
	{
		MaterialPropertyWidget->UpdateContent(InID, InCoding, InName, InVisiExpress, InVisiValue);
	}
}

void UMaterialEditUIManager::UpdateMaterialPropertyWidget(const TArray<UMatParameterGroup*> Parameters)
{
	if (!MaterialPropertyWidget)
	{
		MaterialPropertyWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialEditPropertyWidget>();
	}
	if (MaterialPropertyWidget)
	{
		MaterialPropertyWidget->UpdateContent(Parameters);
		MaterialPropertyWidget->MaterialFilePropertyDelegate.BindUFunction(this, FName(TEXT("OnMaterialFilePropertyHandler")));
		MaterialPropertyWidget->MaterialPropertyDataDelegate.BindUFunction(this, FName(TEXT("MaterialItemPropertyEdit")));
		MaterialPropertyWidget->MaterialFloatNodeDBWriteDelegate.BindUFunction(this, FName(TEXT("OnMaterialFloatItemDataHandler")));
		MaterialPropertyWidget->MaterialGroupSyncImageDelegate.BindUFunction(this, FName(TEXT("OnMaterialSyncImageDataHandler")));
		MaterialPropertyWidget->MaterialGroupExtentPropertyDelegate.BindUFunction(this, FName(TEXT("OnMaterialGroupExtentHandler")));
		MaterialPropertyWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMaterialEditUIManager::UpdateMaterialPropertyWidget()
{
	if (!MaterialPropertyWidget)
	{
		MaterialPropertyWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialEditPropertyWidget>();
	}
}

void UMaterialEditUIManager::UpdateMaterialCategorySecondLevel(const TArray<FMaterialLevelInfo>& SecondMenu)
{
	if (MaterialCategoryMenuWidget)
	{
		MaterialCategoryMenuWidget->SetSecondMenu(SecondMenu);
	}
}

void UMaterialEditUIManager::UpdateMaterialCategoryContent(const TMap<int32, FString>& BasicMaterialData)
{
	if (MaterialCategoryMenuWidget)
	{
		MaterialCategoryMenuWidget->SetMaterialData(BasicMaterialData);
	}
}

void UMaterialEditUIManager::RefreshSceneSize()
{
	if (MaterialLayoutWidget)
	{
		MaterialLayoutWidget->RefreshSceneViewport();
	}
}

void UMaterialEditUIManager::Clear()
{
	if (MaterialToolBarWidget)
	{
		MaterialToolBarWidget->SetVisibility(ESlateVisibility::Collapsed);
		MaterialToolBarWidget->RemoveFromParent();
		MaterialToolBarWidget->Clear();
		MaterialToolBarWidget = nullptr;
	}
	if (MaterialPropertyWidget)
	{
		MaterialPropertyWidget->SetVisibility(ESlateVisibility::Collapsed);
		MaterialPropertyWidget->RemoveFromParent();
		MaterialPropertyWidget->Clear();
		MaterialPropertyWidget = nullptr;
	}
	if (MaterialLayoutWidget)
	{
		MaterialLayoutWidget->SetVisibility(ESlateVisibility::Collapsed);
		MaterialLayoutWidget->RemoveFromParent();
		MaterialLayoutWidget->Clear();
		MaterialLayoutWidget = nullptr;
	}
}

void UMaterialEditUIManager::MaterialToolBarEdit(const EMaterialToolBarType& EditType)
{
	UE_LOG(LogTemp, Log, TEXT("material tool bar type : %d"), (int32)EditType);
	MaterialToolBarEditDelegate.ExecuteIfBound(EditType);
}

void UMaterialEditUIManager::MaterialItemPropertyEdit(const int32& ParamId, const FString& ItemValue, bool IsEnableFlip)
{
	UE_LOG(LogTemp, Log, TEXT("material proeprty edit id/type/enable flip : %d/%s/%d"), ParamId, *ItemValue, IsEnableFlip);
	MaterialItemPropertyChangeDelegate.ExecuteIfBound(ParamId, ItemValue, IsEnableFlip);
}

void UMaterialEditUIManager::OnMaterialFilePropertyHandler(const int32& EditType, const FString& OutString)
{
	MaterialFilePropertyChangeDelegate.ExecuteIfBound(EditType, OutString);
}

void UMaterialEditUIManager::OnMaterialFloatItemDataHandler(const int32& FloatId, const FString& FloatValue)
{
	MaterialFloatPropertyChangeDelegate.ExecuteIfBound(FloatId, FloatValue);
}

void UMaterialEditUIManager::OnMaterialSyncImageDataHandler(const int32& ImageId, const FString& ImagePath)
{
	MaterialImageSyncDelegate.ExecuteIfBound(ImageId, ImagePath);
}

void UMaterialEditUIManager::OnMaterialGroupExtentHandler(const FString& GroupName, bool IsExtent)
{
	MaterialGroupExtentChangeDelegate.ExecuteIfBound(GroupName, IsExtent);
}

void UMaterialEditUIManager::OnMaterialLevelHandler(const int32& InLevelId, bool IsFirst)
{
	MaterialCategoryLevelDelegate.ExecuteIfBound(InLevelId, IsFirst);
}

void UMaterialEditUIManager::OnBasicMaterialSelectHandler(const int32& InBasicId, bool IsFirst)
{
	MaterialCategoryBasicIdDelegate.ExecuteIfBound(InBasicId, IsFirst);
}

void UMaterialEditUIManager::OnBasicMaterialSearchHandler(const int32& LevelId, const FString& MateName)
{
	MaterialCategorySearchDelegate.ExecuteIfBound(LevelId, MateName);
}

void UMaterialEditUIManager::MaterialComponentPictureType(const int32& Type)
{
	MaterialComponentPictureTypeDelegate.ExecuteIfBound(Type);
}

void UMaterialEditUIManager::SetCameraImage(const FString& ImagePath, bool IsShow)
{
	if (MaterialLayoutWidget)
	{
		MaterialLayoutWidget->SetCameraImage(ImagePath, IsShow);
	}
}
