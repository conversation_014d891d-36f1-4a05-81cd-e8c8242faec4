// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "MaterialLayoutWidget.h"
#include "MaterialEditToolBarWidget.h"
#include "MaterialEditPropertyWidget.h"
#include "MaterialCategoryMenuWidget.h"
#include "MaterialCameraWidget.h"
#include "MaterialEditUIManager.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UMaterialEditUIManager : public UObject
{
	GENERATED_BODY()
	
public:
	void InitMaterialEditUI();
	void InitMaterialEditUI(const TArray<UMatParameterGroup*> Parameters, const TArray<FMaterialLevelInfo>& FirstMenu);
	void UpdateMaterialPropertyWidget(const FString& InID, const FString& InCoding, const FString& InName, const FString& InVisiExpress, const FString& InVisiValue);
	void UpdateMaterialPropertyWidget(const TArray<UMatParameterGroup*> Parameters);
	void UpdateMaterialPropertyWidget();
	void InitLevelWidegt(const TArray<FMaterialLevelInfo>& FirstMenu);
	void UpdateMaterialCategorySecondLevel(const TArray<FMaterialLevelInfo>& SecondMenu);
	void UpdateMaterialCategoryContent(const TMap<int32, FString>& BasicMaterialData);
	void RefreshSceneSize();
	void Clear();

	UUserWidget* GetImportMaterialWidget();

	void UpdateImportButtonVisibility(ESlateVisibility NewVisibility);

private:
	void UpdateMaterialLayoutWidget();
	void UpdateMaterialToolBarWidget();
	void InitLevelWidegt();
	UFUNCTION()
		void MaterialToolBarEdit(const EMaterialToolBarType& EditType);
	UFUNCTION()
		void MaterialItemPropertyEdit(const int32& ParamId, const FString& ItemValue, bool IsEnableFlip);
	UFUNCTION()
		void OnMaterialFilePropertyHandler(const int32& EditType, const FString& OutString);
	UFUNCTION()
		void OnMaterialFloatItemDataHandler(const int32& FloatId, const FString& FloatValue);
	UFUNCTION()
		void OnMaterialSyncImageDataHandler(const int32& ImageId, const FString& ImagePath);
	UFUNCTION()
		void OnMaterialGroupExtentHandler(const FString& GroupName, bool IsExtent);

	UFUNCTION()
		void OnMaterialLevelHandler(const int32& InLevelId, bool IsFirst);
	UFUNCTION()
		void OnBasicMaterialSelectHandler(const int32& InBasicId, bool IsFirst);
	UFUNCTION()
		void OnBasicMaterialSearchHandler(const int32& LevelId, const FString& MateName);
	UFUNCTION()
		void MaterialComponentPictureType(const int32& Type);

	UFUNCTION()
	void OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath);

public:
	FMaterialPropertyItemDelegate MaterialItemPropertyChangeDelegate;
	FMaterialToolBarDelegate MaterialToolBarEditDelegate;
	FMaterialPropertyDelegate MaterialFilePropertyChangeDelegate;
	FFloatNodeDBWriteDelegate MaterialFloatPropertyChangeDelegate;
	FSyncMaterialImageDelegate MaterialImageSyncDelegate;
	FGroupExtentDelegate MaterialGroupExtentChangeDelegate;
	FMaterialCategoryIdDelegate MaterialCategoryLevelDelegate;
	FMaterialCategoryIdDelegate MaterialCategoryBasicIdDelegate;
	FMaterialSearchDelegate MaterialCategorySearchDelegate;
	FMaterialCameraBtnTypeDelegate	MaterialComponentPictureTypeDelegate;
	FImportCustomMaterialDelegate	ImportCustomMaterialDelegate;
	
private:
	UPROPERTY()
		UMaterialLayoutWidget* MaterialLayoutWidget;
	UPROPERTY()
		UMaterialEditToolBarWidget* MaterialToolBarWidget;
	UPROPERTY()
		UMaterialEditPropertyWidget* MaterialPropertyWidget;
	UPROPERTY()
		UMaterialCategoryMenuWidget* MaterialCategoryMenuWidget;
public:
	void SetCameraImage(const FString & ImagePath, bool IsShow);
};
