// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"


void UMaterialFunctionLibrary::SetBorderBrush(const FLinearColor & InColor, UBorder * InBorder)
{
	FSlateBrush BorderBrush;
	BorderBrush.DrawAs = ESlateBrushDrawType::Image;
	BorderBrush.Tiling = ESlateBrushTileType::NoTile;
	BorderBrush.Mirroring = ESlateBrushMirrorType::NoMirror;
	BorderBrush.ImageType = ESlateBrushImageType::NoImage;
	BorderBrush.ImageSize = FVector2D(32.0f, 32.0f);
	BorderBrush.TintColor = InColor;
	if (InBorder)
	{
		InBorder->SetBrush(BorderBrush);
	}
}

FVector2D UMaterialFunctionLibrary::GetMouseMoveVector(const FPointerEvent & MouseEvent, const FVector2D & LastMousePos)
{
	FVector2D LocalPixelPosition;// no use
	FVector2D LocalViewportPosition;
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	return LocalViewportPosition - LastMousePos;
}

void UMaterialFunctionLibrary::SetTextColor(const FLinearColor & InColor, UTextBlock * InText)
{
	if (InText)
	{
		InText->SetColorAndOpacity(InColor);
	}
}

void UMaterialFunctionLibrary::SetBorderBrushColor(const FLinearColor & InColor, UBorder * InBorder)
{
	if (InBorder)
	{
		InBorder->SetBrushColor(InColor);
	}
}
