// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "MaterialFunctionLibrary.generated.h"

/**
*
*/

class UBorder;
class UTextBlock;
//
//#define DECL_PARAM_TYPE(X) decltype(X)
//
//#define BIND_PARAM_CPP_TO_UMG(TCPPName, TUMGName)\
//do{TCPPName = static_cast<DECL_PARAM_TYPE(TCPPName)>(this->GetWidgetFromName(#TUMGName));} while (0)
//
//#define BIND_WIDGET_FUNCTION(TWidgetName, TWidgetAction, TWidgetInvokerFunc)\
//do{TWidgetName->##TWidgetAction##.AddUniqueDynamic(this, &TWidgetInvokerFunc);} while(0)
//
//#define BIND_SLATE_WIDGET_FUNCTION(TWidgetName, TWidgetAction, InvokerFuncName)\
//do{TWidgetName->##TWidgetAction##.BindUFunction(this, InvokerFuncName);} while(0)

UCLASS()
class DESIGNSTATION_API UMaterialFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:	
	static void SetBorderBrush(const FLinearColor& InColor, UBorder* InBorder);
	static void SetBorderBrushColor(const FLinearColor& InColor, UBorder* InBorder);

	template<class T>
	static T* CreateUIWidget()
	{
		return T::Create();
	}

	template<class T>
	static T* CreateUIWidget(const FString& BpPath)
	{
		UClass* ItemBp = LoadClass<UUserWidget>(NULL, *BpPath);
		checkf(ItemBp, TEXT("load widget bp error"));
		T* WidgetItem = CreateWidget<T>(GWorld.GetReference(), ItemBp);
		checkf(WidgetItem, TEXT("create widget error"));
		return WidgetItem;
	}

	template<class T1, class T2>
	static T1* CreateUIWidget(T2* InData)
	{
		T1* NewWidget = T1::Create();
		NewWidget->UpdateContent(InData);
		return NewWidget;
	}	

	static FVector2D GetMouseMoveVector(const FPointerEvent & MouseEvent, const FVector2D& LastMousePos);

	static void SetTextColor(const FLinearColor& InColor, UTextBlock * InText);

};
