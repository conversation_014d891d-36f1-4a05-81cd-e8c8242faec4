// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialImportWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/EditableTextBox.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "MaterialFunctionLibrary.h"



bool UMaterialImportWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Btn_Close, OnClicked, UMaterialImportWidget::OnClickedBtnClose);
	BIND_WIDGET_FUNCTION(Btn_ImportFile, OnClicked, UMaterialImportWidget::OnClickedBtnImportFile);
	BIND_WIDGET_FUNCTION(Btn_Cancel, OnClicked, UMaterialImportWidget::OnClickedBtnClose);
	BIND_WIDGET_FUNCTION(Btn_Sure, OnClicked, UMaterialImportWidget::OnClickedBtnSure);

	BIND_WIDGET_FUNCTION(Edt_Save, OnTextChanged, UMaterialImportWidget::OnTextChangedEdtSave);
	BIND_WIDGET_FUNCTION(Edt_Ref, OnTextChanged, UMaterialImportWidget::OnTextChangedEdtRef);
	BIND_WIDGET_FUNCTION(Edt_Save, OnTextCommitted, UMaterialImportWidget::OnTextCommittedEdtSave);
	BIND_WIDGET_FUNCTION(Edt_Ref, OnTextCommitted, UMaterialImportWidget::OnTextCommittedEdtRef);

	PB_Import->SetPercent(0.0f);
	Btn_Sure->SetIsEnabled(false);
	Edt_Save->SetText(FText::FromString(TEXT("")));
	Edt_Ref->SetText(FText::FromString(TEXT("")));
	GetTexture();
	return true;
}

void UMaterialImportWidget::UpdateProgress(const float& InProgress)
{
	PB_Import->SetPercent(FMath::Clamp(InProgress, 0.0f, 1.0f));
}

void UMaterialImportWidget::UpdateState(const EMaterialImportState& InState)
{
	const FLinearColor NormalOrImportingColor = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
	const FLinearColor SucceedColor = FLinearColor(0.093059f, 0.679543f, 0.14996f, 1.0f);
	const FLinearColor FailedColor = FLinearColor(0.651406f, 0.03434f, 0.009134f, 1.0f);
	if (InState == EMaterialImportState::Normal)
	{
		PB_Import->SetFillColorAndOpacity(NormalOrImportingColor);
		Img_ImportResult->SetBrushFromTexture(NormalOrImportingTexture);
		Btn_ImportFile->SetIsEnabled(true);
		Btn_Sure->SetIsEnabled(false);
	}
	else if (InState == EMaterialImportState::Importing)
	{
		PB_Import->SetFillColorAndOpacity(NormalOrImportingColor);
		Img_ImportResult->SetBrushFromTexture(NormalOrImportingTexture);
		Btn_ImportFile->SetIsEnabled(false);
		Btn_Sure->SetIsEnabled(false);
	}
	else if (InState == EMaterialImportState::Succeed)
	{
		PB_Import->SetFillColorAndOpacity(SucceedColor);
		Img_ImportResult->SetBrushFromTexture(SucceedTexture);
		Btn_ImportFile->SetIsEnabled(true);
		Btn_Sure->SetIsEnabled(true);
	}
	else if (InState == EMaterialImportState::Failed)
	{
		PB_Import->SetFillColorAndOpacity(FailedColor);
		Img_ImportResult->SetBrushFromTexture(FailedTexture);
		Btn_ImportFile->SetIsEnabled(true);
		Btn_Sure->SetIsEnabled(false);
	}
}

UMaterialImportWidget* UMaterialImportWidget::Create()
{
	const FString WidgetRefPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/UMG_MaterialImport.UMG_MaterialImport_C'");
	UMaterialImportWidget* NewImportWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialImportWidget>(WidgetRefPath);
	return NewImportWidget;
}

void UMaterialImportWidget::GetTexture()
{
	NormalOrImportingTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/midd.midd'")));
	SucceedTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/complete.complete'")));
	FailedTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/mistake.mistake'")));
}

void UMaterialImportWidget::ResetImportImage()
{
	//if (!IS_OBJECT_PTR_VALID(DefaultImportImage))
	//{
	//	DefaultImportImage = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/IMG.IMG'")));
	//}
	//if (IS_OBJECT_PTR_VALID(ImgSection))
	//{
	//	ImgSection->SetBrushFromTexture(DefaultImportImage);
	//}
}

void UMaterialImportWidget::OnClickedBtnClose()
{
	this->RemoveFromParent();
}

void UMaterialImportWidget::OnClickedBtnImportFile()
{
	ResetImportImage();
	FString NewFilePath = TEXT("");
	FCatalogFunctionLibrary::OpenFileDialogForImport(NewFilePath);
	if (NewFilePath.IsEmpty()) return;
	LocalFilePath = FPaths::ConvertRelativePathToFull(NewFilePath);
	Txt_FileName->SetText(FText::FromString(FPaths::GetBaseFilename(LocalFilePath)));
	UpdateProgress(0.0f);
	UpdateState(EMaterialImportState::Normal);
}

void UMaterialImportWidget::OnClickedBtnSure()
{
	const FString RefPath = Edt_Ref->GetText().ToString();
	const FString SavePath = Edt_Save->GetText().ToString();
	ImportCustomMaterialDelegate.ExecuteIfBound(LocalFilePath, SavePath, RefPath);
}

void UMaterialImportWidget::OnTextChangedEdtSave(const FText& Text)
{
	Btn_Sure->SetIsEnabled(!Edt_Ref->GetText().IsEmpty() && !Text.IsEmpty());
}

void UMaterialImportWidget::OnTextCommittedEdtSave(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		OnTextChangedEdtSave(Text);
	}
}

void UMaterialImportWidget::OnTextChangedEdtRef(const FText& Text)
{
	Btn_Sure->SetIsEnabled(!Edt_Save->GetText().IsEmpty() && !Text.IsEmpty());
}

void UMaterialImportWidget::OnTextCommittedEdtRef(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		const FString RefPath = Text.ToString();
		//加入依据引用路径自动计算PAK文件保存路径的功能
		//引用路径形如Blueprint'/Game/SoftLoading/HomeDecoration/Ornament/26/BP_1.BP_1'
		//从Game后现第一个/处开始截取(不包含/)，至最后一个/结束(不包含/)
		FString Left(TEXT(""));
		FString Right(TEXT(""));
		bool bValidPath = RefPath.Split(TEXT("/Game/"), &Left, &Right, ESearchCase::CaseSensitive);
		if (!bValidPath) return;
		int32 PathEndPos = INDEX_NONE;
		bValidPath = Right.FindLastChar('/', PathEndPos);
		if (!bValidPath) return;
		const FString SavePath = FString::Printf(TEXT("%s/1.PAK"), *Right.Left(PathEndPos));
		Edt_Save->SetText(FText::FromString(SavePath));
	}
}
