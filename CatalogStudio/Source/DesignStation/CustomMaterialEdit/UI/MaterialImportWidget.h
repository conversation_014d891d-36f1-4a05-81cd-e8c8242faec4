// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialImportWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;
class UTextBlock;
class UProgressBar;
class UEditableText;
class USizeBox;
class UEditableTextBox;


enum class EMaterialImportState : uint8
{
	Normal = 0,
	Importing,
	Succeed,
	Failed
};


DECLARE_DYNAMIC_DELEGATE_ThreeParams(FImportCustomMaterialDelegate, const FString&, OriginFilePath,const FString&,InSavePath,const FString&,InRefPath);

UCLASS()
class DESIGNSTATION_API UMaterialImportWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	//���½��ȣ�0<=InProgress<=1.0
	void UpdateProgress(const float& InProgress);

	void UpdateState(const EMaterialImportState& InState);

	static UMaterialImportWidget* Create();

private:

	void GetTexture();

	void ResetImportImage();

public:

	FImportCustomMaterialDelegate	ImportCustomMaterialDelegate;

private:

	UPROPERTY()
		UTexture2D* DefaultImportImage;
	UPROPERTY()
		UTexture2D* NormalOrImportingTexture;
	UPROPERTY()
		UTexture2D* SucceedTexture;
	UPROPERTY()
		UTexture2D* FailedTexture;
	
	static FString SingleComponentImportPath;

	FString LocalFilePath = TEXT("");

protected:
	UFUNCTION()
		void OnClickedBtnClose();

	UFUNCTION()
		void OnClickedBtnImportFile();

	UFUNCTION()
		void OnClickedBtnSure();

	UFUNCTION()
		void OnTextChangedEdtSave(const FText& Text);

	UFUNCTION()
		void OnTextCommittedEdtSave(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextChangedEdtRef(const FText& Text);

	UFUNCTION()
		void OnTextCommittedEdtRef(const FText& Text, ETextCommit::Type CommitMethod);

private:
	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UButton* Btn_Close;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* Txt_FileName;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UProgressBar* PB_Import;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UImage* Img_ImportResult;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UButton* Btn_ImportFile;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UButton* Btn_Cancel;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UButton* Btn_Sure;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UEditableTextBox* Edt_Save;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialImportWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UEditableTextBox* Edt_Ref;
};
