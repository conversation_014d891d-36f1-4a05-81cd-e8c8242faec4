// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCategoryFirstMenu.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/MenuAnchor.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Border.h"





FString UMaterialCategoryFirstMenu::MaterialCategoryTextWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialCategoryFirstMenuUI.MaterialCategoryFirstMenuUI_C'");

bool UMaterialCategoryFirstMenu::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BtnCategory, Btn_Category);
	BIND_PARAM_CPP_TO_UMG(BorCategory, Bor_Category);
	BIND_PARAM_CPP_TO_UMG(TxtCategory, Txt_Category);



	BIND_WIDGET_FUNCTION(BtnCategory, OnClicked, UMaterialCategoryFirstMenu::OnClickedCategoryBtn);
	BIND_WIDGET_FUNCTION(BtnCategory, OnHovered, UMaterialCategoryFirstMenu::OnHoveredCategoryBtn);
	BIND_WIDGET_FUNCTION(BtnCategory, OnUnhovered, UMaterialCategoryFirstMenu::OnUnHoveredCategoryBtn);



	return true;
}

UMaterialCategoryFirstMenu* UMaterialCategoryFirstMenu::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCategoryFirstMenu>(UMaterialCategoryFirstMenu::MaterialCategoryTextWidgetPath);
}


void UMaterialCategoryFirstMenu::SetTextColor(const FLinearColor& color)
{
	TxtCategory->SetColorAndOpacity(color);
}

void UMaterialCategoryFirstMenu::SetCategoryInfo(const int32& _Id, const FString& _Name)
{
	Id = _Id;
	Name = _Name;
	TxtCategory->SetText(FText::FromString(Name));
}

void UMaterialCategoryFirstMenu::SetSelectColor()
{
	SetTextColor(TextHoverColor);
}

void UMaterialCategoryFirstMenu::SetUnSelectColor()
{
	SetTextColor(TextNormalColor);
}

int32 UMaterialCategoryFirstMenu::GetId()
{
	return Id;
}

bool UMaterialCategoryFirstMenu::GetSelectState()
{
	return IsSelect;
}

void UMaterialCategoryFirstMenu::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
}

FString UMaterialCategoryFirstMenu::GetName()
{
	return Name;
}
void UMaterialCategoryFirstMenu::OnClickedCategoryBtn()
{
	FVector2D thisMouse;
	APlayerController* PController = GetWorld()->GetFirstPlayerController();
	PController->GetMousePosition(thisMouse.X, thisMouse.Y);
	IsSelect = true;
	MaterialCategoryDelegate.ExecuteIfBound(Id, true, thisMouse.Y - 30);
}
void UMaterialCategoryFirstMenu::OnHoveredCategoryBtn()
{
	SetTextColor(TextHoverColor);
	FVector2D thisMouse;
	APlayerController* PController = GetWorld()->GetFirstPlayerController();
	PController->GetMousePosition(thisMouse.X, thisMouse.Y);
	IsSelect = true;
	MaterialCategoryDelegate.ExecuteIfBound(Id, false, thisMouse.Y - 30);
}
void UMaterialCategoryFirstMenu::OnUnHoveredCategoryBtn()
{
	if (!IsSelect)
	{
		SetTextColor(TextNormalColor);
	}
}
