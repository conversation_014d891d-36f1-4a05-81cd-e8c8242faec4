// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialCategorySecondMenuWidget.h"
#include "MaterialCategoryFirstMenu.generated.h"



/**
 * 
 */
class USizeBox;
class UTextBlock;
class UButton;
class UBorder;
class UCanvasPanel;

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMaterialCategoryDelegate, const int32&, Id, bool, State, const float&, Height);

UCLASS()
class DESIGNSTATION_API UMaterialCategoryFirstMenu : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UMaterialCategoryFirstMenu* Create();

private:
	static FString MaterialCategoryTextWidgetPath;

public:
	void SetTextColor(const FLinearColor& color);
	UFUNCTION()
		void SetCategoryInfo(const int32& Id, const FString& Text);
	UFUNCTION()
		void SetSelectColor();
	UFUNCTION()
		void SetUnSelectColor();
	int32 GetId();
	bool GetSelectState();
	void SetSelectState(bool IsSelect);
	FString GetName();
protected:
	UFUNCTION()
		void OnClickedCategoryBtn();
	UFUNCTION()
		void OnHoveredCategoryBtn();
	UFUNCTION()
		void OnUnHoveredCategoryBtn();
private:
	UPROPERTY()
		UTextBlock*		TxtCategory;
	UPROPERTY()
		UButton*		BtnCategory;
	UPROPERTY()
		UBorder*		BorCategory;
private:
	int32	Id;
	FString	Name;
	bool	IsSelect;
public:
	FMaterialCategoryDelegate MaterialCategoryDelegate;
};
