// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCategoryImageWidget.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"

FString UMaterialCategoryImageWidget::MaterialCategoryImageWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialCategoryImageUI.MaterialCategoryImageUI_C'");

bool UMaterialCategoryImageWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnMaterial, Btn_Material);
	BIND_PARAM_CPP_TO_UMG(ImgMaterial, Img_Material);

	BIND_WIDGET_FUNCTION(BtnMaterial, OnClicked, UMaterialCategoryImageWidget::OnClickedMaterialBtn);

	return true;
}

UMaterialCategoryImageWidget* UMaterialCategoryImageWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCategoryImageWidget>(UMaterialCategoryImageWidget::MaterialCategoryImageWidgetPath);
}

void UMaterialCategoryImageWidget::SetMaterialData(const int32& _Id, const FString& _ImgPath)
{
	Id = _Id;
	ImgPath = _ImgPath;
	DownloadBasicMatImage(_ImgPath);
}

void UMaterialCategoryImageWidget::LoadBasicMaterialImage(const FString& ImagePath)
{
	FString FullPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + ImagePath);
	if (FPaths::FileExists(FullPath))
	{
		UTexture2D* NewTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FullPath);
		if (nullptr != NewTex)
		{
			ImgMaterial->SetBrushFromTexture(NewTex);
		}
	}
}

void UMaterialCategoryImageWidget::OnClickedMaterialBtn()
{
	MaterialCategoryIdDelegate.ExecuteIfBound(Id, false);
}

