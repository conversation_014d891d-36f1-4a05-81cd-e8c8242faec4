// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialCategoryTextWidget.h"
#include "MaterialCategoryImageWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;

UCLASS()
class DESIGNSTATION_API UMaterialCategoryImageWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UMaterialCategoryImageWidget* Create();

	void SetMaterialData(const int32& id,const FString& path);

public:
	UFUNCTION(BlueprintImplementableEvent, Category = "MaterialEdit")
		void DownloadBasicMatImage(const FString& ImagePath);

	UFUNCTION(BlueprintCallable, Category = "MaterialEdit")
		void LoadBasicMaterialImage(const FString& ImagePath);

private:
	FString	 DownloadUUID;

private:
	static FString MaterialCategoryImageWidgetPath;

	int32	Id;
	FString ImgPath;

protected:
	UFUNCTION()
		void OnClickedMaterialBtn();
private:
	UPROPERTY()
		UButton*	BtnMaterial;
	UPROPERTY()
		UImage*		ImgMaterial;
public:
	FMaterialCategoryIdDelegate	MaterialCategoryIdDelegate;
};
