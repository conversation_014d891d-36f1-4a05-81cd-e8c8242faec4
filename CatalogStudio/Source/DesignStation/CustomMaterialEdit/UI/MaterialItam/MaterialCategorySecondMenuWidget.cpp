// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCategorySecondMenuWidget.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/WrapBox.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"


FString UMaterialCategorySecondMenuWidget::MaterialCategorySecondMenuWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialCategorySecondMenuUI.MaterialCategorySecondMenuUI_C'");
bool UMaterialCategorySecondMenuWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(WBSecMenu, WB_SecMenu);
	/*BIND_PARAM_CPP_TO_UMG(SBSecMenu,SB_SecMenu);*/
	return true;
}
UMaterialCategorySecondMenuWidget* UMaterialCategorySecondMenuWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCategorySecondMenuWidget>(UMaterialCategorySecondMenuWidget::MaterialCategorySecondMenuWidgetPath);
}

void UMaterialCategorySecondMenuWidget::UpdateContent(const TArray<FMaterialLevelInfo>& SecondMenu)
{
	WBSecMenu->ClearChildren();
	ChildNum = 0;
	for (auto& Iter : SecondMenu)
	{
		AddSecondMenuChild(Iter);
	}
	UE_LOG(LogTemp, Log, TEXT("%d"), ChildNum);
}

void UMaterialCategorySecondMenuWidget::AddSecondMenuChild(const FMaterialLevelInfo& SecondMatInfo)
{
	UMaterialCategoryTextWidget* MaterialCategoryTextWidget = UMaterialCategoryTextWidget::Create();
	MaterialCategoryTextWidget->SetCategortInfo(SecondMatInfo.id, SecondMatInfo.level_name);
	MaterialCategoryTextWidget->MaterialCategoryIdDelegate.BindUFunction(this, FName(TEXT("OpenMaterialData")));
	WBSecMenu->AddChild(MaterialCategoryTextWidget);
	MaterialCategorySecondMenuName.Add(MaterialCategoryTextWidget);
	++ChildNum;
}


void UMaterialCategorySecondMenuWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
}

int32 UMaterialCategorySecondMenuWidget::GetChildNum()
{
	return ChildNum;
}

void UMaterialCategorySecondMenuWidget::OpenMaterialData(const int32& _id, bool IsFirst)
{
	MaterialCategoryIdDelegate.ExecuteIfBound(_id, false);
	SelectId = _id;
	for (auto& iter : MaterialCategorySecondMenuName)
	{
		if (SelectId == iter->GetId())
		{
			iter->SetSelectColor();
		}
		else
		{
			iter->SetSelectState(false);
			iter->SetUnSelectColor();
		}
	}
}
