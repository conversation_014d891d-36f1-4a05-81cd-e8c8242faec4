// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialCategoryTextWidget.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "MaterialCategorySecondMenuWidget.generated.h"

/**
 *
 */

class UWrapBox;
class UScrollBox;

UCLASS()
class DESIGNSTATION_API UMaterialCategorySecondMenuWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	static UMaterialCategorySecondMenuWidget* Create();
public:
	void UpdateContent(const TArray<FMaterialLevelInfo>& SecondMenu);
	void AddSecondMenuChild(const FMaterialLevelInfo& SecondMatInfo);
	void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	int32 GetChildNum();
private:
	static FString MaterialCategorySecondMenuWidgetPath;

protected:
	UFUNCTION()
		void OpenMaterialData(const int32& _id, bool IsFirst);
private:
	UPROPERTY()
		UWrapBox* WBSecMenu;
	UPROPERTY()
		UScrollBox* SBSecMenu;
	UPROPERTY()
		TArray<UMaterialCategoryTextWidget*> MaterialCategorySecondMenuName;
	int32 ChildNum;
	int32 SelectId;
public:
	FMaterialCategoryIdDelegate	MaterialCategoryIdDelegate;
	FGetWidget					OnGetMenuWidget;
};
