// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialCategoryTextWidget.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"

FString UMaterialCategoryTextWidget::MaterialCategoryTextWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialCategoryTextUI.MaterialCategoryTextUI_C'");

bool UMaterialCategoryTextWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnCategory, Btn_Category);
	BIND_PARAM_CPP_TO_UMG(TxtCategory, Txt_Category);

	BIND_WIDGET_FUNCTION(BtnCategory, OnClicked, UMaterialCategoryTextWidget::OnClickedCategoryBtn);
	BIND_WIDGET_FUNCTION(BtnCategory, OnHovered, UMaterialCategoryTextWidget::OnHoveredCategoryBtn);
	BIND_WIDGET_FUNCTION(BtnCategory, OnUnhovered, UMaterialCategoryTextWidget::OnUnHoveredCategoryBtn);


	return true;
}
UMaterialCategoryTextWidget* UMaterialCategoryTextWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialCategoryTextWidget>(UMaterialCategoryTextWidget::MaterialCategoryTextWidgetPath);
}

void UMaterialCategoryTextWidget::OnClickedCategoryBtn()
{
	MaterialCategoryIdDelegate.ExecuteIfBound(Id, true);
	IsSelect = true;
}

void UMaterialCategoryTextWidget::SetTextColor(const FLinearColor& color)
{
	TxtCategory->SetColorAndOpacity(color);
}

void UMaterialCategoryTextWidget::SetCategortInfo(const int32& _Id, const FString& _Name)
{
	Id = _Id;
	Name = _Name;
	TxtCategory->SetText(FText::FromString(_Name));
}

void UMaterialCategoryTextWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
}

bool UMaterialCategoryTextWidget::GetSelectState()
{
	return IsSelect;
}

void UMaterialCategoryTextWidget::SetSelectColor()
{
	SetTextColor(TextHoverColor);
}

void UMaterialCategoryTextWidget::SetUnSelectColor()
{
	SetTextColor(TextNormalColor);
}

int32 UMaterialCategoryTextWidget::GetId()
{
	return Id;
}

void UMaterialCategoryTextWidget::OnHoveredCategoryBtn()
{
	SetTextColor(TextHoverColor);
}
void UMaterialCategoryTextWidget::OnUnHoveredCategoryBtn()
{
	if (!IsSelect)
	{
		SetTextColor(TextNormalColor);
	}
}

