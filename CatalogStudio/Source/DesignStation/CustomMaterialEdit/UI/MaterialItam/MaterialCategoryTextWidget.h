// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialCategoryTextWidget.generated.h"

/**
 * 
 */

const FLinearColor TextHoverColor = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
const FLinearColor TextNormalColor = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);

class UButton;
class UTextBlock;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FMaterialCategoryIdDelegate, const int32&, _Id, bool, IsFirst);

UCLASS()
class DESIGNSTATION_API UMaterialCategoryTextWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UMaterialCategoryTextWidget* Create();

private:
	static FString MaterialCategoryTextWidgetPath;

public:
	virtual void SetTextColor(const FLinearColor& color);
	virtual void SetCategortInfo(const int32& _Id,const FString& _Name);
	virtual void SetSelectState(bool IsSelect);
	virtual bool GetSelectState();
	virtual void SetSelectColor();
	virtual void SetUnSelectColor();
	virtual int32 GetId();
private:
	int32	Id;
	FString Name;
	bool	IsSelect;
protected:
	UFUNCTION()
		virtual	void OnClickedCategoryBtn();
	UFUNCTION()
		virtual void OnHoveredCategoryBtn();
	UFUNCTION()
		virtual void OnUnHoveredCategoryBtn();
private:
	UPROPERTY()
		UButton*	BtnCategory;
	UPROPERTY()
		UTextBlock*	TxtCategory;
public:
	FMaterialCategoryIdDelegate	MaterialCategoryIdDelegate;
};
