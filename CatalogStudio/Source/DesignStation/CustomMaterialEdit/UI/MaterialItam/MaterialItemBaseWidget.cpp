// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemBaseWidget.h"

bool UMaterialItemBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	ParamId = -1;
	ParamValue = TEXT("");

	return true;
}

void UMaterialItemBaseWidget::SwitchWidgetState(bool IsEditable)
{
	if (TxtName && CkbEdit)
	{
		CkbEdit->SetIsChecked(IsEditable);
		//TxtName->SetColorAndOpacity(IsEditable ? FLinearColor(0.0f, 0.0f, 0.0f, 1.0f) : FLinearColor(0.0f, 0.0f, 0.0f, 0.6f));
	}
}

void UMaterialItemBaseWidget::Clear()
{
	if (CkbEdit && TxtName)
	{
		CkbEdit = nullptr;
		TxtName = nullptr;
	}
}
