// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "MaterialItemBaseWidget.generated.h"

/**
 *
 */

const FString BoolCheck = TEXT("1");
const FString BoolUnCheck = TEXT("0");

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMaterialPropertyItemDelegate, const int32&, ParamId, const FString&, ItemValue, bool, IsEnableFlip);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFloatNodeDBWriteDelegate, const int32&, FloatId, const FString&, FloatValue);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FSyncMaterialImageDelegate, const int32&, ParamId, const FString&, ImagePath);

UCLASS()
class DESIGNSTATION_API UMaterialItemBaseWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void SwitchWidgetState(bool IsEditable);
	virtual void Clear();

public:
	FMaterialPropertyItemDelegate MaterialPropertyDelegate;
	FFloatNodeDBWriteDelegate FloatNodeDBWriteDelegate;
	FSyncMaterialImageDelegate SyncMaterialImageDelegate;

public:
	int32 ParamId;
	FString ParamValue;

public:
	UPROPERTY()
		UCheckBox* CkbEdit;
	UPROPERTY()
		UTextBlock* TxtName;
};
