// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemBoolWidget.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
FString UMaterialItemBoolWidget::MaterialItemBoolPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialItemBoolUI.MaterialItemBoolUI_C'");

bool UMaterialItemBoolWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CkbEdit, Ckb_Edit);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(CkbValue, Ckb_Value);
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>);
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>);

	BIND_WIDGET_FUNCTION(CkbEdit, OnCheckStateChanged, UMaterialItemBoolWidget::OnStateChangedCkbEdit);
	BIND_WIDGET_FUNCTION(CkbValue, OnCheckStateChanged, UMaterialItemBoolWidget::OnStateChangedCkbValue);

	return true;
}

void UMaterialItemBoolWidget::SwitchWidgetState(bool IsEditable)
{
	Super::SwitchWidgetState(IsEditable);
	if (CkbValue)
	{
		CkbValue->SetIsEnabled(IsEditable);
	}
}

void UMaterialItemBoolWidget::Clear()
{
	Super::Clear();
	if (CkbValue)
	{
		CkbValue = nullptr;
	}
}

void UMaterialItemBoolWidget::UpdateContent(UMatParameterBoolean* InData)
{
	SwitchWidgetState(InData->IsEnable);
	ValueSwitchState(InData->BooleanValue);
	ParamId = InData->ID;
	ParamValue = InData->BooleanValue ? BoolCheck : BoolUnCheck;
	if (CkbValue && TxtName)
	{
		CkbValue->SetIsChecked(InData->BooleanValue);
		TxtName->SetText(FText::FromString(InData->ParameterName));
	}
}

UMaterialItemBoolWidget* UMaterialItemBoolWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemBoolWidget>(UMaterialItemBoolWidget::MaterialItemBoolPath);
}

void UMaterialItemBoolWidget::OnStateChangedCkbEdit(bool IsCheck)
{
	SwitchWidgetState(IsCheck);
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, true);
}

void UMaterialItemBoolWidget::OnStateChangedCkbValue(bool IsCheck)
{
	ParamValue = IsCheck ? BoolCheck : BoolUnCheck;
	ValueSwitchState(IsCheck);
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, false);
}

void UMaterialItemBoolWidget::ValueSwitchState(bool IsCheck)
{
	if (IsCheck)
	{
		BorOn->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		BorOff->SetVisibility(ESlateVisibility::Hidden);
	}
	else
	{
		BorOn->SetVisibility(ESlateVisibility::Hidden);
		BorOff->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
}
