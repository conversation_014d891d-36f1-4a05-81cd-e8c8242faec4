// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MaterialItemBaseWidget.h"
#include "MaterialItemBoolWidget.generated.h"

/**
 *
 */
class UBorder;

UCLASS()
class DESIGNSTATION_API UMaterialItemBoolWidget : public UMaterialItemBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void SwitchWidgetState(bool IsEditable) override;
	virtual void Clear() override;
	void UpdateContent(UMatParameterBoolean* InData);

	static UMaterialItemBoolWidget* Create();

private:
	static FString MaterialItemBoolPath;

protected:
	UFUNCTION()
		void OnStateChangedCkbEdit(bool IsCheck);
	UFUNCTION()
		void OnStateChangedCkbValue(bool IsCheck);
	UFUNCTION()
		void ValueSwitchState(bool IsCheck);
private:
	UPROPERTY()
		UCheckBox* CkbValue;
	UPROPERTY()
		UBorder* BorOn;
	UPROPERTY()
		UBorder* BorOff;

};
