// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemColorWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Button.h"

FString UMaterialItemColorWidget::MaterialItemColorPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialItemColorUI.MaterialItemColorUI_C'");

bool UMaterialItemColorWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CkbEdit, Ckb_Edit);
	BIND_WIDGET_FUNCTION(CkbEdit, OnCheckStateChanged, UMaterialItemColorWidget::OnCheckStateChangedCkbEdit);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(BorColor, Bor_Color);
	BIND_PARAM_CPP_TO_UMG(BtnChangeColor, Btn_ChangeColor);
	BIND_WIDGET_FUNCTION(BtnChangeColor, OnClicked, UMaterialItemColorWidget::OnClickedBtnChangeColor);

	return true;
}

void UMaterialItemColorWidget::SwitchWidgetState(bool IsEditable)
{
	Super::SwitchWidgetState(IsEditable);
	if (BtnChangeColor)
	{
		BtnChangeColor->SetIsEnabled(IsEditable);
	}
}

void UMaterialItemColorWidget::Clear()
{
	Super::Clear();
	if (BorColor && BtnChangeColor)
	{
		BorColor = nullptr;
		BtnChangeColor = nullptr;
	}
}

void UMaterialItemColorWidget::UpdateContent(UMatParameterColor* InColorData)
{
	SwitchWidgetState(InColorData->IsEnable);
	ParamId = InColorData->ID;
	ParamValue = InColorData->ColorValue.ToString();
	if (BorColor && TxtName)
	{
		UE_LOG(LogTemp, Log, TEXT("param color : %s"), *FLinearColor(InColorData->ColorValue).ToString());
		BorColor->SetBrushColor(FLinearColor(InColorData->ColorValue));
		UMaterialFunctionLibrary::SetBorderBrushColor(FLinearColor(InColorData->ColorValue), BorColor);
		TxtName->SetText(FText::FromString(InColorData->ParameterName));
	}
}

void UMaterialItemColorWidget::UpdateColorPickerWidget(const FLinearColor & InColor)
{
	UE_LOG(LogTemp, Log, TEXT("update color picker widget"));
	if (!MoveableCPWidget || !MoveableCPWidget->IsValidLowLevel())
	{
		MoveableCPWidget = UMoveableColorPickerWidget::Create();
		MoveableCPWidget->AddToViewport(10);
	}
	if (MoveableCPWidget)
	{
		MoveableCPWidget->UpdateColorPicker(InColor);
		MoveableCPWidget->ColorChangedDelegate.BindUFunction(this, FName(TEXT("ColorPickerChangedEdit")));
		MoveableCPWidget->ColorDBWriteDelegate.BindUFunction(this, FName(TEXT("OnColorDBWriteHandler")));
		MoveableCPWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

UMaterialItemColorWidget * UMaterialItemColorWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemColorWidget>(UMaterialItemColorWidget::MaterialItemColorPath);
}

void UMaterialItemColorWidget::ColorPickerChangedEdit(const FLinearColor & NewColor)
{
	UE_LOG(LogTemp, Log, TEXT("color : %s, convert color : %s"), *NewColor.ToString(), *NewColor.ToFColor(true).ToHex());
	if (BorColor)
	{
		UMaterialFunctionLibrary::SetBorderBrushColor(NewColor, BorColor);
	}
	ParamValue = NewColor.ToFColor(true).ToString();
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, NewColor.ToFColor(true).ToHex(), false);
}

void UMaterialItemColorWidget::OnColorDBWriteHandler(const FLinearColor & NewColor)
{
	FloatNodeDBWriteDelegate.ExecuteIfBound(ParamId, NewColor.ToFColor(true).ToHex());
}

void UMaterialItemColorWidget::OnCheckStateChangedCkbEdit(bool IsChecked)
{
	SwitchWidgetState(IsChecked);
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, true);
}

void UMaterialItemColorWidget::OnClickedBtnChangeColor()
{
	FColor ParamColor;
	ParamColor.InitFromString(ParamValue);
	UpdateColorPickerWidget(FLinearColor(ParamColor));
}
