// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MaterialItemBaseWidget.h"
#include "CustomMaterialEdit/UI/ColorPicker/MoveableColorPickerWidget.h"
#include "MaterialItemColorWidget.generated.h"

/**
 *
 */

class UBorder;
class UButton;

UCLASS()
class DESIGNSTATION_API UMaterialItemColorWidget : public UMaterialItemBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void SwitchWidgetState(bool IsEditable) override;
	virtual void Clear() override;
	void UpdateContent(UMatParameterColor* InColorData);
	void UpdateColorPickerWidget(const FLinearColor& InColor);

	static UMaterialItemColorWidget* Create();

private:
	UFUNCTION()
		void ColorPickerChangedEdit(const FLinearColor& NewColor);
	UFUNCTION()
		void OnColorDBWriteHandler(const FLinearColor& NewColor);

private:
	UPROPERTY()
		UMoveableColorPickerWidget* MoveableCPWidget;

	static FString MaterialItemColorPath;

protected:
	UFUNCTION()
		void OnCheckStateChangedCkbEdit(bool IsChecked);
	UFUNCTION()
		void OnClickedBtnChangeColor();

private:
	UPROPERTY()
		UBorder* BorColor;
	UPROPERTY()
		UButton* BtnChangeColor;
};
