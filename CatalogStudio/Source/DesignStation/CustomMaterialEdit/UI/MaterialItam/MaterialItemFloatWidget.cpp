// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemFloatWidget.h"
#include "Runtime/UMG/Public/Components/Slider.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"

FString UMaterialItemFloatWidget::MaterialItemFloatPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialItemFloatUI.MaterialItemFloatUI_C'");

bool UMaterialItemFloatWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CkbEdit, Ckb_Edit);
	BIND_WIDGET_FUNCTION(CkbEdit, OnCheckStateChanged, UMaterialItemFloatWidget::OnStateChangedCkbEdit);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UMaterialItemFloatWidget::OnTextCommittedEdtValue);
	BIND_PARAM_CPP_TO_UMG(SldValue, Sld_Value);
	BIND_WIDGET_FUNCTION(SldValue, OnValueChanged, UMaterialItemFloatWidget::OnValueChangedSldValue);
	BIND_WIDGET_FUNCTION(SldValue, OnMouseCaptureBegin, UMaterialItemFloatWidget::OnMouseCaptureSldValue);
	BIND_WIDGET_FUNCTION(SldValue, OnMouseCaptureEnd, UMaterialItemFloatWidget::OnMouseReleaseSldValue);

	return true;
}

void UMaterialItemFloatWidget::SwitchWidgetState(bool IsEditable)
{
	Super::SwitchWidgetState(IsEditable);
	if (EdtValue && SldValue)
	{
		EdtValue->SetIsEnabled(IsEditable);
		SldValue->SetIsEnabled(IsEditable);
	}
}

void UMaterialItemFloatWidget::Clear()
{
	Super::Clear();
	if (SldValue && EdtValue)
	{
		SldValue = nullptr;
		EdtValue = nullptr;
	}
}

void UMaterialItemFloatWidget::UpdateContent(UMatParameterFloat* InFloatData)
{
	SwitchWidgetState(InFloatData->IsEnable);
	MaxValue = InFloatData->MaxValue;
	MinValue = InFloatData->MinValue;
	ParamId = InFloatData->ID;
	ParamValue = FString::Printf(TEXT("%.2f"), InFloatData->FloatValue);
	//ParamValue = FString::SanitizeFloat(InFloatData->FloatValue);
	//ParamValue = FString::Printf(TEXT("%.2f"), InValue * (MaxValue - MinValue));
	if (SldValue && EdtValue && TxtName)
	{
		SldValue->SetValue(InFloatData->FloatValue / (MaxValue - MinValue));
		EdtValue->SetText(FText::FromString(ParamValue));
		//EdtValue->SetText(FText::FromString(ParamValue));
		TxtName->SetText(FText::FromString(InFloatData->ParameterName));
	}
}

UMaterialItemFloatWidget * UMaterialItemFloatWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemFloatWidget>(UMaterialItemFloatWidget::MaterialItemFloatPath);
}

void UMaterialItemFloatWidget::OnStateChangedCkbEdit(bool IsCheck)
{
	SwitchWidgetState(IsCheck);
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, true);
}

void UMaterialItemFloatWidget::OnMouseCaptureSldValue()
{
}

void UMaterialItemFloatWidget::OnValueChangedSldValue(float InValue)
{
	ParamValue = FString::Printf(TEXT("%.2f"), InValue * (MaxValue - MinValue));
	if (EdtValue)
	{
		EdtValue->SetText(FText::FromString(ParamValue));
	}
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, false);
}

void UMaterialItemFloatWidget::OnMouseReleaseSldValue()
{
	FString CurrentValue;
	if (SldValue)
	{
		CurrentValue = FString::Printf(TEXT("%.2f"), SldValue->GetValue() * (MaxValue - MinValue));
	}
	FloatNodeDBWriteDelegate.ExecuteIfBound(ParamId, CurrentValue);
}

void UMaterialItemFloatWidget::OnTextCommittedEdtValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	checkf(SldValue, TEXT("material float sld value is null"));
	checkf(EdtValue, TEXT("material float edt value is null"));
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		if (FCString::Atof(*Text.ToString()) <= MinValue)
		{
			SldValue->SetValue(0);
			EdtValue->SetText(FText::FromString(FString::SanitizeFloat(MinValue)));
			ParamValue = FString::SanitizeFloat(MinValue);
		}
		else if (FCString::Atof(*Text.ToString()) >= MaxValue)
		{
			SldValue->SetValue(1);
			EdtValue->SetText(FText::FromString(FString::SanitizeFloat(MaxValue)));
			ParamValue = FString::SanitizeFloat(MaxValue);
		}
		else
		{
			SldValue->SetValue(FCString::Atof(*Text.ToString()) / (MaxValue - MinValue));
			ParamValue = FString::Printf(TEXT("%.2f"), FCString::Atof(*Text.ToString()));
		}
		MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, false);
		FloatNodeDBWriteDelegate.ExecuteIfBound(ParamId, ParamValue);
	}
}
