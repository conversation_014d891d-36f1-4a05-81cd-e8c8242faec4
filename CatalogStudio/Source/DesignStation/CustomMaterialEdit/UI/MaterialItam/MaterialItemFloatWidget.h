// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MaterialItemBaseWidget.h"
#include "MaterialItemFloatWidget.generated.h"

/**
 *
 */

class USlider;

UCLASS()
class DESIGNSTATION_API UMaterialItemFloatWidget : public UMaterialItemBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void SwitchWidgetState(bool IsEditable) override;
	virtual void Clear() override;
	void UpdateContent(UMatParameterFloat* InFloatData);

	static UMaterialItemFloatWidget* Create();

private:
	float MaxValue;
	float MinValue;

	static FString MaterialItemFloatPath;

protected:
	UFUNCTION()
		void OnStateChangedCkbEdit(bool IsCheck);

	UFUNCTION()
		void OnMouseCaptureSldValue();
	UFUNCTION()
		void OnValueChangedSldValue(float InValue);
	UFUNCTION()
		void OnMouseReleaseSldValue();

	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);

private:
	UPROPERTY()
		USlider* SldValue;
	UPROPERTY()
		UEditableText* EdtValue;
};
