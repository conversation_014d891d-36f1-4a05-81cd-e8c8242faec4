// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemGroupWidget.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"

FString UMaterialItemGroupWidget::MaterialGroupPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialItemGroupUI.MaterialItemGroupUI_C'");

bool UMaterialItemGroupWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CkbEdit, Ckb_ExpandChild);
	BIND_WIDGET_FUNCTION(CkbEdit, OnCheckStateChanged, UMaterialItemGroupWidget::OnCheckStateChangedCkbExpandChild);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_GroupName);
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>);

	return true;
}

void UMaterialItemGroupWidget::UpdateContent(const TArray<UMaterialParameterBasicData*> GroupParams)
{
	checkf(ScbChild, TEXT("property content is null"));
	ScbChild->ClearChildren();
	ParamsWidgets.Empty();
	for (auto& GroupData : GroupParams)
	{
		switch (GroupData->ParameterType)
		{
		case EMatParameterType::EBoolean:MaterialItemWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemBoolWidget>(Cast<UMatParameterBoolean>(GroupData)); break;
		case EMatParameterType::EColor:MaterialItemWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemColorWidget>(Cast<UMatParameterColor>(GroupData)); MaterialItemWidget->FloatNodeDBWriteDelegate.BindUFunction(this, FName(TEXT("OnFloatItemDataUpdateHandler"))); break;
		case EMatParameterType::EFloat:MaterialItemWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemFloatWidget>(Cast<UMatParameterFloat>(GroupData)); MaterialItemWidget->FloatNodeDBWriteDelegate.BindUFunction(this, FName(TEXT("OnFloatItemDataUpdateHandler"))); break;
		case EMatParameterType::ETexture:MaterialItemWidget = UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemImageWidget>(Cast<UMatParameterTexture>(GroupData)); MaterialItemWidget->SyncMaterialImageDelegate.BindUFunction(this, FName(TEXT("OnSyncMaterialImageHandler"))); break;
		default:checkNoEntry(); break;
		}
		MaterialItemWidget->MaterialPropertyDelegate.BindUFunction(this, FName(TEXT("MaterialItemPropertyEdit")));
		MaterialItemWidget->SetVisibility(ESlateVisibility::Visible);
		ScbChild->AddChild(MaterialItemWidget);
		ParamsWidgets.Add(MaterialItemWidget);
	}
}

void UMaterialItemGroupWidget::SetGroupName(const FString& InName)
{
	if (TxtName)
	{
		TxtName->SetText(FText::FromString(InName));
	}
}

void UMaterialItemGroupWidget::SetIsExpand(bool IsExpande)
{
	if (CkbEdit)
	{
		CkbEdit->SetIsChecked(IsExpande);
	}
	OnCheckStateChangedCkbExpandChild(IsExpande);
}

void UMaterialItemGroupWidget::Clear()
{
	Super::Clear();
	for (auto& MatItem : ParamsWidgets)
	{
		MatItem->Clear();
	}
	ParamsWidgets.Empty();
	if (MaterialItemWidget)
	{
		MaterialItemWidget->Clear();
		MaterialItemWidget = nullptr;
	}
	if (ScbChild)
	{
		ScbChild->ClearChildren();
		ScbChild = nullptr;
	}
}

UMaterialItemGroupWidget* UMaterialItemGroupWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemGroupWidget>(UMaterialItemGroupWidget::MaterialGroupPath);
}

void UMaterialItemGroupWidget::MaterialItemPropertyEdit(const int32& InParamId, const FString& ItemValue, bool IsEnableFlip)
{
	MaterialPropertyItemDelegate.ExecuteIfBound(InParamId, ItemValue, IsEnableFlip);
}

void UMaterialItemGroupWidget::OnFloatItemDataUpdateHandler(const int32& FloatId, const FString& FloatValue)
{
	MaterialFloatDataItemDelegate.ExecuteIfBound(FloatId, FloatValue);
}

void UMaterialItemGroupWidget::OnSyncMaterialImageHandler(const int32& ImageId, const FString& ImagePath)
{
	MaterialSyncImageDelegate.ExecuteIfBound(ImageId, ImagePath);
}

void UMaterialItemGroupWidget::OnCheckStateChangedCkbExpandChild(bool IsChecked)
{
	if (ScbChild)
	{
		ScbChild->SetVisibility(IsChecked ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (TxtName)
	{
		MaterialGroupExtentDelegate.ExecuteIfBound(TxtName->GetText().ToString(), IsChecked);
	}

}
