// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MaterialItemBoolWidget.h"
#include "MaterialItemColorWidget.h"
#include "MaterialItemFloatWidget.h"
#include "MaterialItemImageWidget.h"
#include "MaterialItemGroupWidget.generated.h"

/**
 *
 */

class UCheckBox;
class UTextBlock;
class UScrollBox;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FGroupExtentDelegate, const FString&, GroupName, bool, IsExtent);

UCLASS()
class DESIGNSTATION_API UMaterialItemGroupWidget : public UMaterialItemBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	void UpdateContent(const TArray<UMaterialParameterBasicData*> GroupParams);
	void SetGroupName(const FString& InName);
	void SetIsExpand(bool IsExpande);
	virtual void Clear() override;

	static UMaterialItemGroupWidget* Create();

private:
	UFUNCTION()
		void MaterialItemPropertyEdit(const int32& InParamId, const FString& ItemValue, bool IsEnableFlip);
	UFUNCTION()
		void OnFloatItemDataUpdateHandler(const int32& FloatId, const FString& FloatValue);
	UFUNCTION()
		void OnSyncMaterialImageHandler(const int32& ImageId, const FString& ImagePath);

public:
	FMaterialPropertyItemDelegate MaterialPropertyItemDelegate;
	FFloatNodeDBWriteDelegate MaterialFloatDataItemDelegate;
	FGroupExtentDelegate MaterialGroupExtentDelegate;
	FSyncMaterialImageDelegate MaterialSyncImageDelegate;

private:
	UPROPERTY()
		TArray<UMaterialItemBaseWidget*>  ParamsWidgets;
	UPROPERTY()
		UMaterialItemBaseWidget* MaterialItemWidget;

	static FString MaterialGroupPath;

protected:
	UFUNCTION()
		void OnCheckStateChangedCkbExpandChild(bool IsChecked);

private:
	UPROPERTY()
		UScrollBox* ScbChild;
};
