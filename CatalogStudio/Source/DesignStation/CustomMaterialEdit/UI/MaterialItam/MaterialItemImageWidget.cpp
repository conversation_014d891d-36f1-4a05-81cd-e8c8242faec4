// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialItemImageWidget.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "CustomMaterialEdit/UI/MaterialFunctionLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"

FString UMaterialItemImageWidget::MaterialItemImagePath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialItem/MaterialItemImageUI.MaterialItemImageUI_C'");

bool UMaterialItemImageWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CkbEdit, Ckb_Edit);
	BIND_WIDGET_FUNCTION(CkbEdit, OnCheckStateChanged, UMaterialItemImageWidget::OnCheckStateChangedCkbEdit);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(BtnImageMaterial, Btn_ImageMaterial);
	BIND_WIDGET_FUNCTION(BtnImageMaterial, OnClicked, UMaterialItemImageWidget::OnClickedBtnImageMaterial);
	BIND_PARAM_CPP_TO_UMG(ImgMaterial, Img_Material);

	return true;
}

void UMaterialItemImageWidget::SwitchWidgetState(bool IsEditable)
{
	Super::SwitchWidgetState(IsEditable);
	if (BtnImageMaterial && ImgMaterial)
	{
		BtnImageMaterial->SetIsEnabled(IsEditable);
		ImgMaterial->SetIsEnabled(IsEditable);
	}
}

void UMaterialItemImageWidget::Clear()
{
	Super::Clear();
	if (BtnImageMaterial && ImgMaterial)
	{
		BtnImageMaterial = nullptr;
		ImgMaterial = nullptr;
	}
}

void UMaterialItemImageWidget::UpdateContent(UMatParameterTexture* InTextureData)
{
	SwitchWidgetState(InTextureData->IsEnable);
	ParamId = InTextureData->ID;
	ParamValue = InTextureData->TexturePath;
	if (TxtName)
	{
		TxtName->SetText(FText::FromString(InTextureData->ParameterName));
	}
	LoadCustomMaterialImage(ParamValue);
}

UMaterialItemImageWidget* UMaterialItemImageWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialItemImageWidget>(UMaterialItemImageWidget::MaterialItemImagePath);
}

void UMaterialItemImageWidget::LoadCustomMaterialImage(const FString& ImagePath)
{
	FString FullFilePath = FPaths::ProjectContentDir() + ImagePath;
	FullFilePath = FPaths::ConvertRelativePathToFull(FullFilePath);
	UTexture2D* NewTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FullFilePath);
	if (nullptr != NewTex)
	{
		ImgMaterial->SetBrushFromTexture(NewTex);
	}
	SyncMaterialImageDelegate.ExecuteIfBound(ParamId, ImagePath);
}

void UMaterialItemImageWidget::OnCheckStateChangedCkbEdit(bool IsChecked)
{
	SwitchWidgetState(IsChecked);
	MaterialPropertyDelegate.ExecuteIfBound(ParamId, ParamValue, true);
}

void UMaterialItemImageWidget::OnClickedBtnImageMaterial()
{
	if (bImageSelecting) return;
	bImageSelecting = true;
	FString NewFile;
	FCatalogFunctionLibrary::OpenFileDialogForImage(NewFile);
	bImageSelecting = false;
	if (!NewFile.IsEmpty())
	{
		UE_LOG(LogTemp, Log, TEXT("ParamId=%d NewFile=%s"), ParamId, *NewFile);
		MaterialPropertyDelegate.ExecuteIfBound(ParamId, NewFile, false);
	}
}
