// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MaterialItemBaseWidget.h"
#include "MaterialItemImageWidget.generated.h"

/**
 *
 */

class UButton;
class UImage;

UCLASS()
class DESIGNSTATION_API UMaterialItemImageWidget : public UMaterialItemBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void SwitchWidgetState(bool IsEditable) override;
	virtual void Clear() override;
	void UpdateContent(UMatParameterTexture* InTextureData);

	static UMaterialItemImageWidget* Create();

public:
	UFUNCTION(BlueprintImplementableEvent, Category = "MaterialEdit")
		void DownloadMaterialNodeImage(const FString& ImagePath);

	UFUNCTION(BlueprintCallable, Category = "MaterialEdit")
		void LoadCustomMaterialImage(const FString& ImagePath);

private:
	static FString MaterialItemImagePath;

protected:
	UFUNCTION()
		void OnCheckStateChangedCkbEdit(bool IsChecked);
	UFUNCTION()
		void OnClickedBtnImageMaterial();
private:
	UPROPERTY()
		UButton* BtnImageMaterial;
	UPROPERTY()
		UImage* ImgMaterial;

	//Fix bug CATALOG-1536
	bool bImageSelecting = false;
};
