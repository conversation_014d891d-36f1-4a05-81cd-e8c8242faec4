// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialLayoutWidget.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"


FString UMaterialLayoutWidget::MaterialLayoutPath = TEXT("WidgetBlueprint'/Game/UI/Material/CustomMaterialModule/MaterialLayoutUI.MaterialLayoutUI_C'");

bool UMaterialLayoutWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(Bor<PERSON>oolBar, Bor_ToolBar);

	BIND_PARAM_CPP_TO_UMG(CPMateLevel, CP_MateLevel);
	BIND_PARAM_CPP_TO_UMG(SZLeftResize, SZ_LeftResize);
	BIND_PARAM_CPP_TO_UMG(CPMateProperty, CP_MateProperty);
	BIND_PARAM_CPP_TO_UMG(SZRightResize, SZ_RightResize);
	BIND_PARAM_CPP_TO_UMG(SBCamera, SB_Camera);
	BIND_PARAM_CPP_TO_UMG(BtnCamera, Btn_Camera);
	BIND_PARAM_CPP_TO_UMG(SBTop, SB_Top);
	BIND_PARAM_CPP_TO_UMG(SBLeft, SB_Left);
	BIND_PARAM_CPP_TO_UMG(SBRight, SB_Right);
	BIND_WIDGET_FUNCTION(BtnCamera, OnClicked, UMaterialLayoutWidget::OnClickedCamera);
	BIND_WIDGET_FUNCTION(BTN_ImportMaterial, OnClicked, UMaterialLayoutWidget::OnClickButtonImportMaterialHandler);


	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
	InitResizeWidget();

	CameraWidget = UMaterialCameraWidget::Create();
	SBCamera->AddChild(CameraWidget);
	CameraWidget->CameraBtnTypeDelegate.BindUFunction(this, FName(TEXT("TakePicture")));
	OpenCameraWidget(false);

	ImportWidget = nullptr;

	return true;
}

void UMaterialLayoutWidget::Clear()
{
	BorToolBar = nullptr;
	ToolBarSlot = nullptr;
	CPMateLevel = nullptr;
	SZLeftResize = nullptr;
	FolderTreeSlot = nullptr;
	CPMateProperty = nullptr;
	SZRightResize = nullptr;
	PropertySlot = nullptr;
}

void UMaterialLayoutWidget::AddToolBarWidget(UUserWidget * InToolBarWidget)
{
	/*if (BorToolBar)
	{
		BorToolBar->AddChild(InToolBarWidget);
	}*/
	if (BorToolBar && InToolBarWidget)
	{
		BorToolBar->AddChild(InToolBarWidget);
		/*ToolBarSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(BorToolBar);
		ResizeSceneViewport(ToolBarSlot ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
			, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);*/
	}
}

void UMaterialLayoutWidget::AddMaterialLevelWidget(UUserWidget * InMateLevelWidget)
{
	if (CPMateLevel && InMateLevelWidget)
	{
		if (CPMateLevel->GetChildrenCount() != 0)
		{
			return;
		}
		CPMateLevel->AddChild(InMateLevelWidget);
		FolderTreeSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InMateLevelWidget);
		SetSlotToSuitContent(FolderTreeSlot);
		ResizeSceneViewport(FVector2D::ZeroVector
			, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
			, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

void UMaterialLayoutWidget::AddPropertyWidget(UUserWidget * InPropertyWidget)
{
	/*if (BorProperty)
	{
		BorProperty->AddChild(InPropertyWidget);
	}*/
	if (CPMateProperty && InPropertyWidget)
	{
		if (InPropertyWidget == CPMateProperty->GetChildAt(0))
		{
			return;
		}
		CPMateProperty->ClearChildren();
		CPMateProperty->AddChild(InPropertyWidget);
		PropertySlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InPropertyWidget);
		SetSlotToSuitContent(PropertySlot, false);
		ResizeSceneViewport(FVector2D::ZeroVector
			, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
			, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

void UMaterialLayoutWidget::RefreshSceneViewport()
{
	ResizeSceneViewport(FVector2D::ZeroVector
		, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
		, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
}

UMaterialLayoutWidget * UMaterialLayoutWidget::Create()
{
	return UMaterialFunctionLibrary::CreateUIWidget<UMaterialLayoutWidget>(UMaterialLayoutWidget::MaterialLayoutPath);
}

void UMaterialLayoutWidget::InitResizeWidget()
{
	LeftDragWidget = UMateLayoutDragWidget::Create();
	LeftDragWidget->MateDragBorderDelegate.BindUFunction(this, FName(TEXT("OnLeftResizeHandler")));
	LeftDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (SZLeftResize)
	{
		SZLeftResize->AddChild(LeftDragWidget);
	}

	RightDragWidget = UMateLayoutDragWidget::Create();
	RightDragWidget->MateDragBorderDelegate.BindUFunction(this, FName(TEXT("OnRightResizeHandler")));
	RightDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (SZRightResize)
	{
		SZRightResize->AddChild(RightDragWidget);
	}
}

void UMaterialLayoutWidget::SetSlotToSuitContent(UCanvasPanelSlot * InSlot, bool IsFolderTree)
{
	if (InSlot)
	{
		InSlot->SetAlignment(FVector2D::ZeroVector);
		if (IsFolderTree)
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(310.0f, 0.0f));
		}
		else
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(320.0f, 0.0f));
		}
	}
}

void UMaterialLayoutWidget::LeftMouseUpToDrop()
{
	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
}

void UMaterialLayoutWidget::ResizeSceneViewport(const FVector2D & ToolbarSize, const FVector2D & FolderSize, const FVector2D & PropertySize)
{
	if (GWorld && GWorld->WorldType == EWorldType::Type::Game && GEngine && GEngine->GameViewport)
	{
		FVector2D ViewportOringinSize;
		GEngine->GameViewport->GetViewportSize(ViewportOringinSize);
		FVector2D ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
		UE_LOG(LogTemp, Log, TEXT("----------------------brgin----------------------------"));
		UE_LOG(LogTemp, Log, TEXT("viewport origin size:%f,%f"), ViewportOringinSize.X, ViewportOringinSize.Y);
		UE_LOG(LogTemp, Log, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);
		if (ViewportOringinSize.X > 0 && ViewportOringinSize.Y > 0)
		{
			//FVector2D ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
			FVector2D ToolSizeRelativeToViewport = ToolbarSize / ViewportOringinSize;
			FVector2D FolderSizeRelativeToViewport = FolderSize / ViewportOringinSize;
			FVector2D PropertySizeRelativeToViewport = PropertySize / ViewportOringinSize;
			UE_LOG(LogTemp, Log, TEXT("ToolBar Relative size:%f,%f"), ToolSizeRelativeToViewport.X, ToolSizeRelativeToViewport.Y);
			UE_LOG(LogTemp, Log, TEXT("folder Relative size:%f,%f"), FolderSizeRelativeToViewport.X, FolderSizeRelativeToViewport.Y);
			UE_LOG(LogTemp, Log, TEXT("property Relative size:%f,%f"), PropertySizeRelativeToViewport.X, PropertySizeRelativeToViewport.Y);
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderSizeRelativeToViewport.X;
			//GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X - PropertySizeRelativeToViewport.X;
			//GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
			FVector2D ViewportResizeSize;
			GEngine->GameViewport->GetViewportSize(ViewportResizeSize);
			UE_LOG(LogTemp, Log, TEXT("viewport resize size:%f,%f"), ViewportResizeSize.X, ViewportResizeSize.Y);
			ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
			UE_LOG(LogTemp, Log, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);
			UE_LOG(LogTemp, Log, TEXT("----------------------------end-----------------------"));
		}
	}
}

FEventReply UMaterialLayoutWidget::UpdateLastMousePos(const FPointerEvent & MouseEvent)
{
	FVector2D PixelPosTemp; // no use
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), PixelPosTemp, LastMousePosition);
	UE_LOG(LogTemp, Log, TEXT("left mouse to resize , last pos : %s"), *LastMousePosition.ToString());
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
	return UWidgetBlueprintLibrary::CaptureMouse(DetectReply, nullptr);
}

void UMaterialLayoutWidget::OnLeftResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("material level widget resize"));
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsLeftResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UMaterialLayoutWidget::OnRightResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("material property resize"));
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsRightResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

FReply UMaterialLayoutWidget::NativeOnMouseButtonUp(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		LeftMouseUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}

	return FReply::Unhandled();
}

FReply UMaterialLayoutWidget::NativeOnMouseMove(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (IsLeftMouseDown)
	{
		FVector2D MoveVector = UMaterialFunctionLibrary::GetMouseMoveVector(InMouseEvent, LastMousePosition);
		UE_LOG(LogTemp, Log, TEXT("multi component Move vector : %s, last pos : %s"), *MoveVector.ToString(), *LastMousePosition.ToString());
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		int ResolutionX = 0;
		if (GWorld && GWorld->GetGameViewport() && GWorld->GetGameViewport()->Viewport)
		{
			ResolutionX = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
		}
		if (IsLeftResize)
		{
			float NewSizeX = UKismetMathLibrary::FMin(PropertySlot ? (ResolutionX - PropertySlot->GetSize().X - 10.0f) : (ResolutionX - 10.0f), UKismetMathLibrary::FMax(290.0f, LastMousePosition.X));
			UE_LOG(LogTemp, Log, TEXT("ResolutionX : %d,Left : %f, Mouse : %f, new right x size : %f"), ResolutionX, FolderTreeSlot->GetSize().X, LastMousePosition.X, NewSizeX);
			FolderTreeSlot->SetSize(FVector2D(NewSizeX, FolderTreeSlot->GetSize().Y));
			ResizeSceneViewport(FVector2D::ZeroVector
				, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
				, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
		else if (IsRightResize)
		{
			float NewSizeX = UKismetMathLibrary::FMax(290.0f, ResolutionX - (FolderTreeSlot ? UKismetMathLibrary::FMax(FolderTreeSlot->GetSize().X + 10.0f, LastMousePosition.X) : LastMousePosition.X));
			UE_LOG(LogTemp, Log, TEXT("ResolutionX : %d,Left : %f, Mouse : %f, new right x size : %f"), ResolutionX, PropertySlot->GetSize().X, LastMousePosition.X, NewSizeX);
			PropertySlot->SetSize(FVector2D(NewSizeX, PropertySlot->GetSize().Y));
			ResizeSceneViewport(FVector2D::ZeroVector
				, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
				, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
	}

	return FReply::Unhandled();
}

void UMaterialLayoutWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	LeftMouseUpToDrop();
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
	UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply);
}

void UMaterialLayoutWidget::OnClickedCamera()
{
	PictureTypeDelegate.ExecuteIfBound(-1);
	OpenCameraWidget(true);
}

void UMaterialLayoutWidget::OnClickButtonImportMaterialHandler()
{
	if (ImportWidget)
	{
		ImportWidget->RemoveFromParent();
		ImportWidget = nullptr;
	}
	ImportWidget = UMaterialImportWidget::Create();
	ImportWidget->ImportCustomMaterialDelegate.BindUFunction(this, FName("OnImportCustomMaterialHandler"));
	ImportWidget->AddToViewport(100);
}

void UMaterialLayoutWidget::OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath)
{
	ImportCustomMaterialDelegate.ExecuteIfBound(OriginalFilePath, SavePath, RefPath);
}

void UMaterialLayoutWidget::OpenCameraWidget(bool IsOpen)
{
	if (IsOpen&&CameraWidget)
	{
		SBCamera->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBTop->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBLeft->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBRight->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
	else
	{
		SBCamera->SetVisibility(ESlateVisibility::Collapsed);
		SBTop->SetVisibility(ESlateVisibility::Collapsed);
		SBLeft->SetVisibility(ESlateVisibility::Collapsed);
		SBRight->SetVisibility(ESlateVisibility::Collapsed);

	}
}

void UMaterialLayoutWidget::TakePicture(const int32& Type)
{
	switch (Type)
	{
	case 0:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 1:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 2:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 3:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 4:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 5:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 6:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 7:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 8:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 9:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 10:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	case 11:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	default:
		break;
	}
}

void UMaterialLayoutWidget::SetCameraBtnState(const int32& Type, bool IsEnable)
{
	if (CameraWidget)
	{
		CameraWidget->SetBtnState(Type, IsEnable);
	}
}

void UMaterialLayoutWidget::SetCameraImage(const FString & ImagePath, bool IsShow)
{
	if (CameraWidget)
	{
		CameraWidget->SetImage(ImagePath);
		CameraWidget->ShowImage(IsShow);
	}
}

void UMaterialLayoutWidget::UpdateImportButtonVisibility(ESlateVisibility NewVisibility)
{
	BTN_ImportMaterial->SetVisibility(NewVisibility);
}