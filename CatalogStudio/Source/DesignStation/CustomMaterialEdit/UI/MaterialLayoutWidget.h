// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MaterialFunctionLibrary.h"
#include "MateLayoutDragWidget.h"
#include "MaterialCameraWidget.h"
#include "CustomMaterialEdit/UI/MaterialImportWidget.h"
#include "MaterialLayoutWidget.generated.h"

/**
 * 
 */

class UBorder;
class UCanvasPanel;
class UCanvasPanelSlot;
class USizeBox;
class USizeBox;
class UCameraWidget;

UCLASS()
class DESIGNSTATION_API UMaterialLayoutWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void Clear();

	void AddToolBarWidget(UUserWidget* InToolBarWidget);
	void AddMaterialLevelWidget(UUserWidget* InMateLevelWidget);
	void AddPropertyWidget(UUserWidget* InPropertyWidget);
	void RefreshSceneViewport();

	static UMaterialLayoutWidget* Create();

	UUserWidget* GetImportWidget() { return ImportWidget; }

	void UpdateImportButtonVisibility(ESlateVisibility NewVisibility);

private:
	void InitResizeWidget();
	void SetSlotToSuitContent(UCanvasPanelSlot* InSlot, bool IsFolderTree = true);
	void LeftMouseUpToDrop();
	void ResizeSceneViewport(const FVector2D& ToolbarSize, const FVector2D& FolderSize, const FVector2D& PropertySize);
	FEventReply UpdateLastMousePos(const FPointerEvent & MouseEvent);
	UFUNCTION()
		void OnLeftResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnRightResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnClickedCamera();
	UFUNCTION()
		void OpenCameraWidget(bool IsOpen);
	UFUNCTION()
		void TakePicture(const int32& Type);

	UFUNCTION()
	void OnClickButtonImportMaterialHandler();

	UFUNCTION()
	void OnImportCustomMaterialHandler(const FString& OriginalFilePath, const FString& SavePath, const FString& RefPath);

private:
	UPROPERTY()
		UMateLayoutDragWidget* LeftDragWidget;
	UPROPERTY()
		UMateLayoutDragWidget* RightDragWidget;

	FVector2D LastMousePosition;
	bool IsLeftMouseDown;
	bool IsLeftResize;
	bool IsRightResize;

	static FString MaterialLayoutPath;

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;

private:	
	UPROPERTY()
		UBorder* BorToolBar;
	UPROPERTY()
		UCanvasPanelSlot* ToolBarSlot;

	UPROPERTY()
		UCanvasPanel* CPMateLevel;
	UPROPERTY()
		USizeBox* SZLeftResize;
	UPROPERTY()
		UCanvasPanelSlot* FolderTreeSlot;

	UPROPERTY()
		UCanvasPanel* CPMateProperty;
	UPROPERTY()
		USizeBox* SZRightResize;
	UPROPERTY()
		UCanvasPanelSlot* PropertySlot;
	UPROPERTY()
		USizeBox*	SBCamera;
	UPROPERTY()
		UButton*	BtnCamera;
	UPROPERTY()
		USizeBox*	SBTop;
	UPROPERTY()
		USizeBox*	SBLeft;
	UPROPERTY()
		USizeBox*	SBRight;

	UPROPERTY(BlueprintReadWrite, Category = "MaterialLayoutWidget", meta=(AllowPrivateAccess = true, BindWidget = true))
	UButton* BTN_ImportMaterial;

	UMaterialCameraWidget*	CameraWidget;

	UPROPERTY()
	UMaterialImportWidget* ImportWidget;

public:
	void SetCameraBtnState(const int32& Type, bool IsEnable);
	void SetCameraImage(const FString & ImagePath, bool IsShow);
public:

	FMaterialCameraBtnTypeDelegate	PictureTypeDelegate;

	FImportCustomMaterialDelegate	ImportCustomMaterialDelegate;
};
