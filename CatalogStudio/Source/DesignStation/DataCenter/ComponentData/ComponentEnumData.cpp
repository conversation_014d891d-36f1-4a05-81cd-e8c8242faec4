// Fill out your copyright notice in the Description page of Project Settings.

#include "ComponentEnumData.h"




FVector NSPlanType::GetPlanNormal(EPlanPolygonBelongs PlanType)
{
	switch (PlanType)
	{
	case EPlanPolygonBelongs::EUnknown:
	case EPlanPolygonBelongs::EXY_Plan: return FVector::UpVector;
	case EPlanPolygonBelongs::EYZ_Plan: return FVector::ForwardVector;
	case EPlanPolygonBelongs::EXZ_Plan: return FVector::RightVector;
	default: return FVector::UpVector;
	}
}

FVector NSPlanType::GetPlanTangentX(EPlanPolygonBelongs PlanType)
{
	switch (PlanType)
	{
	case EPlanPolygonBelongs::EUnknown:
	case EPlanPolygonBelongs::EXY_Plan: return FVector::ForwardVector;
	case EPlanPolygonBelongs::EYZ_Plan: return FVector::RightVector;
	case EPlanPolygonBelongs::EXZ_Plan: return FVector::ForwardVector;
	default: return FVector::UpVector;
	}
}

FVector NSPlanType::GetPlanTangentY(EPlanPolygonBelongs PlanType)
{
	FVector TangentY = NSPlanType::GetPlanNormal(PlanType) ^ NSPlanType::GetPlanTangentX(PlanType);
	TangentY = TangentY.GetSafeNormal();
	return TangentY;
}
