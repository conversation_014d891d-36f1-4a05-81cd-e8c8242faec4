// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ComponentEnumData.generated.h"

UENUM(BlueprintType)
enum class EPolygonVerticesOrder : uint8
{
	EUnknown,
	ECounterClockWise,
	EClockWise
};

UENUM(BlueprintType)
enum class EPlanPolygonBelongs : uint8
{
	EUnknown,
	EXY_Plan,
	EYZ_Plan,
	EXZ_Plan
};

namespace NSPlanType
{
	DESIGNSTATION_API FVector GetPlanNormal(EPlanPolygonBelongs PlanType);

	DESIGNSTATION_API FVector GetPlanTangentX(EPlanPolygonBelongs PlanType);

	DESIGNSTATION_API FVector GetPlanTangentY(EPlanPolygonBelongs PlanType);
}

UENUM(BlueprintType)
enum class EVerticeType : uint8
{
	EUnknown,
	EConvex,
	EConcave
};

UENUM(BlueprintType)
enum class EPositionType : uint8
{
	EAbsolute,
	ERelative
};

UENUM(BlueprintType)
enum class ELineType : uint8
{
	ELineSegment,
	EHeightArc,
	ERadiusArc
};

UENUM(BlueprintType)
enum class ESectionType : uint8
{
	ECustomPlan,
	ECube,
	EEllipse,
	ERectangle
};

UENUM(BlueprintType)
enum class EGeomtryItemType : uint8
{
	EGeomtryPoint,
	EGeomtryLine,
	EGeomtryCustomPlan,
	EGeometryRectangle,
	EGeometryEllipse,
	EGeometryCube
};

UENUM(BlueprintType)
enum class ESectionOperationType : uint8
{
	EDrawSection = 0,
	EShiftSection,
	EZoomSection,
	ECutoutSection,
	ELoftingSection,
	ERotatorSection
};

UENUM(BlueprintType)
enum class ENormalType : uint8
{
	Outter,
	Inner
};

UENUM(BlueprintType)
enum class ESingleComponentSource : uint8
{
	ECustom,
	EImportFBX,
	EImportPAK
};

UENUM(BlueprintType)
enum class ESingleComponentState : uint8
{
	ECreate,
	ENeedConfirm,
	EConfirmed
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UComponentEnumData : public UObject
{
	GENERATED_BODY()

};
