// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ParameterPropertyData.h"
#include "ComponentPropertyData.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FLocationProperty
{
	GENERATED_BODY()

public:
	UPROPERTY()
		FExpressionValuePair LocationX;
	UPROPERTY()
		FExpressionValuePair LocationY;
	UPROPERTY()
		FExpressionValuePair LocationZ;

public:
	FLocationProperty() :LocationX(FExpressionValuePair(0.0f)), LocationY(FExpressionValuePair(0.0f)), LocationZ(FExpressionValuePair(0.0f)) {}

	FLocationProperty(const FExpressionValuePair& X, const FExpressionValuePair& Y, const FExpressionValuePair& Z)
		:LocationX(X)
		, LocationY(Y)
		, LocationZ(Z)
	{
	}

	void Reset()
	{
		LocationX.Reset();
		LocationY.Reset();
		LocationZ.Reset();
	}

	bool Equal_Precise(const FLocationProperty& InData)
	{
		return LocationX.Equals_Precise(InData.LocationX) 
			&& LocationY.Equals_Precise(InData.LocationY) 
			&& LocationZ.Equals_Precise(InData.LocationZ);
	}

	FVector GetLocation() const
	{
		FVector Location = FVector(FCString::Atof(*LocationX.Value), FCString::Atof(*LocationY.Value), FCString::Atof(*LocationZ.Value));
		return UParameterPropertyData::ConvertToUeValue(Location);
	}

	bool operator!=(const FLocationProperty& InData) const
	{
		return LocationX != InData.LocationX || LocationY != InData.LocationY || LocationZ != InData.LocationZ;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FLocationProperty& LocationPropertyToSave)
	{
		Ar << LocationPropertyToSave.LocationX;
		Ar << LocationPropertyToSave.LocationY;
		Ar << LocationPropertyToSave.LocationZ;
		return Ar;
	}

	void operator=(const FLocationProperty& InData)
	{
		LocationX = InData.LocationX;
		LocationY = InData.LocationY;
		LocationZ = InData.LocationZ;
	}

	bool Equals_Precise(const FLocationProperty& InData)
	{
		return LocationX.Equals_Precise(InData.LocationX) 
			&& LocationY.Equals_Precise(InData.LocationY) 
			&& LocationZ.Equals_Precise(InData.LocationZ);
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRotationProperty
{
	GENERATED_BODY()

public:
	UPROPERTY()
		FExpressionValuePair Roll;
	UPROPERTY()
		FExpressionValuePair Pitch;
	UPROPERTY()
		FExpressionValuePair Yaw;

public:
	FRotationProperty() :Roll(FExpressionValuePair(0.0f)), Pitch(FExpressionValuePair(0.0f)), Yaw(FExpressionValuePair(0.0f)) {}

	FRotationProperty(const FExpressionValuePair& InRoll, const FExpressionValuePair& InPitch, const FExpressionValuePair& InYaw)
		:Roll(InRoll)
		, Pitch(InPitch)
		, Yaw(InYaw)
	{
	}

	void Reset()
	{
		Roll.Reset();
		Pitch.Reset();
		Yaw.Reset();
	}

	bool Equal_Precise(const FRotationProperty& InData)
	{
		return Roll.Equals_Precise(InData.Roll)
			&& Pitch.Equals_Precise(InData.Pitch)
			&& Yaw.Equals_Precise(InData.Yaw);
	}

	FRotator GetRotation() const
	{
		float X = FCString::Atof(*Roll.Value);
		float Y = FCString::Atof(*Pitch.Value);
		float Z = FCString::Atof(*Yaw.Value);
		return FRotator(Y, Z, X);
	}

	friend FArchive& operator<<(FArchive& Ar, struct FRotationProperty& RotationPropertyToSave)
	{
		Ar << RotationPropertyToSave.Roll;
		Ar << RotationPropertyToSave.Pitch;
		Ar << RotationPropertyToSave.Yaw;
		return Ar;
	}

	bool operator!=(const FRotationProperty& InData) const
	{
		return Roll != InData.Roll || Pitch != InData.Pitch || Yaw != InData.Yaw;
	}

	void operator=(const FRotationProperty& InData)
	{
		Roll = InData.Roll;
		Pitch = InData.Pitch;
		Yaw = InData.Yaw;
	}

	bool Equals_Precise(const FRotationProperty& InData)
	{
		return Roll.Equals_Precise(InData.Roll)
			&& Pitch.Equals_Precise(InData.Pitch)
			&& Yaw.Equals_Precise(InData.Yaw);
	}

};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FScaleProperty
{
	GENERATED_BODY()

public:
	UPROPERTY()
		FExpressionValuePair X;
	UPROPERTY()
		FExpressionValuePair Y;
	UPROPERTY()
		FExpressionValuePair Z;

public:
	FScaleProperty() :X(FExpressionValuePair(1.0f)), Y(FExpressionValuePair(1.0f)), Z(FExpressionValuePair(1.0f)) {}

	FScaleProperty(const FExpressionValuePair& InX, const FExpressionValuePair& InY, const FExpressionValuePair& InZ)
		:X(InX)
		, Y(InY)
		, Z(InZ)
	{
	}

	void Reset()
	{
		X.ResetUnit();
		Y.ResetUnit();
		Z.ResetUnit();
	}

	bool Equal_Precise(const FScaleProperty& InData)
	{
		return X.Equals_Precise(InData.X)
			&& Y.Equals_Precise(InData.Y)
			&& Z.Equals_Precise(InData.Z);
	}

	FVector GetScale() const
	{
		float ScaleX = FCString::Atof(*X.Value);
		float ScaleY = FCString::Atof(*Y.Value);
		float ScaleZ = FCString::Atof(*Z.Value);
		return FVector(ScaleX, ScaleY, ScaleZ);
	}

	friend FArchive& operator<<(FArchive& Ar, struct FScaleProperty& ScalePropertyToSave)
	{
		Ar << ScalePropertyToSave.X;
		Ar << ScalePropertyToSave.Y;
		Ar << ScalePropertyToSave.Z;
		return Ar;
	}

	bool operator!=(const FScaleProperty& InData) const
	{
		return X != InData.X || Y != InData.Y || Z != InData.Z;
	}

	void operator=(const FScaleProperty& InData)
	{
		X = InData.X;
		Y = InData.Y;
		Z = InData.Z;
	}

	bool Equals_Precise(const FScaleProperty& InData)
	{
		return X.Equals_Precise(InData.X)
			&& Y.Equals_Precise(InData.Y)
			&& Z.Equals_Precise(InData.Z);
	}
};


/**
 *
 */
UCLASS()
class DESIGNSTATION_API UComponentPropertyData : public UObject
{
	GENERATED_BODY()

public:
	UComponentPropertyData();
	~UComponentPropertyData();
};
