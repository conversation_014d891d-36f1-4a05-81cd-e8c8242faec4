

#include "CrossSectionDataDefine.h"

#include "GeometryEdit/Public/Geometry3DLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "GeometryEdit/Public/PolygonSheareLibrary.h"
#include "GeometryEdit/Public/GeometryEditLibrary.h"
#include "GeometryEdit/Public/PolygonLoftLibrary.h"
#include "GeometricCalculate/Library/GeometryLibrary.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DataCenter/Libraries/GeomtryMathmaticLibrary.h"
#include "DataCenter/MultiThread/MultiThreadFunctionLibrary.h"
#include "DesignStation/Geometry/SingleComponent/ShowSingleComponentActor.h"

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif

bool FCrossSectionData::Equal_Precise(const FCrossSectionData& InData)
{
	const bool EqualType = SectionType == InData.SectionType;
	const bool EqualPlanBelongs = PlanBelongs == InData.PlanBelongs;

	bool EqualPoints = true;
	{
		if (Points.Num() != InData.Points.Num())
		{
			EqualPoints = false;
		}
		else
		{
			for (int32 i = 0; i < Points.Num(); ++i)
			{
				if (!Points[i].Equal_Precise(InData.Points[i]))
				{
					EqualPoints = false;
					break;
				}
			}
		}
	}

	bool EqualLine = true;
	{
		if (Lines.Num() != InData.Lines.Num())
		{
			EqualLine = false;
		}
		else
		{
			for (int32 i = 0; i < Lines.Num(); ++i)
			{
				if (!Lines[i].Equal_Precise(InData.Lines[i]))
				{
					EqualLine = false;
					break;
				}
			}
		}
	
	}

	const bool EqualRectangle = Rectangle.Equal_Precise(InData.Rectangle);

	const bool EqualEllipse = Ellipse.Equal_Precise(InData.Ellipse);

	const bool EqualCube = Cube.Equal_Precise(InData.Cube);

	const bool EqualNeedDisplay = NeedDisplay == InData.NeedDisplay;
	const bool EqualGenerateGap = bGenerateGap == InData.bGenerateGap;
	const bool EqualCapInner = bCapInner == InData.bCapInner;

	const bool EqualSectionNormal = (SectionNormal - InData.SectionNormal).Size() < 0.1;

	const bool EqualHoleSection = HoleSection.Equal_Precise(InData.HoleSection);

	const bool EqualPostive = Postive == InData.Postive;

	const bool EqualCrossType = Type == InData.Type;

	bool EqualVertexCache = vertexCache.Num() == InData.vertexCache.Num();
	if (EqualVertexCache)
	{
		for (int32 i = 0; i < vertexCache.Num(); ++i)
		{
			if((vertexCache[i] - InData.vertexCache[i]).Size() > 0.1)
			{
				EqualVertexCache = false;
				break;
			}
		}
	}

	bool EqualVertexCacheMap = vertexCacheMap.Num() == InData.vertexCacheMap.Num();
	if (EqualVertexCacheMap)
	{
		for (const auto& VCM : vertexCacheMap)
		{
			if (!InData.vertexCacheMap.Contains(VCM.Key) || 
				(VCM.Value - InData.vertexCacheMap[VCM.Key]).Size() > 0.1)
			{
				EqualVertexCacheMap = false;
				break;
			}
		}
	}

	return EqualType && EqualPlanBelongs && EqualPoints && EqualLine && EqualRectangle 
		&& EqualEllipse && EqualCube && EqualNeedDisplay && EqualGenerateGap && EqualCapInner 
		&& EqualSectionNormal && EqualHoleSection && EqualPostive && EqualCrossType && EqualVertexCache && EqualVertexCacheMap;
}

bool FCrossSectionData::GetCrossSectionPoints(TArray<TPair<FVector, bool>>& SectionPoints) const
{
	if (ESectionType::ECustomPlan == SectionType)
	{
		if (Points.Num() != Lines.Num() || Points.Num() <= 0) return false;
		FVector PlanNormal = GetSectionArcNormal();
		int32 Index = 0;
		for (auto& Iter : Points)
		{
			FVector PointLocation = EPositionType::EAbsolute == Iter.PositionType ? Iter.PointLocation() : Iter.PointLocation() + Iter.PrePointLocation;
			SectionPoints.Add(TPair<FVector, bool>(PointLocation, true));
			TArray<FVector> LinePoints;
			bool Res = false;
			if (ELineType::EHeightArc == Lines[Index].LineType)
			{
				if (FMath::IsNearlyZero(Lines[Index].RadiusOrHeight()))
				{
					Res = true;
				}
				else
				{
					const float MinmalChordLength = Lines[Index].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
					Res = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Lines[Index].StartLocation, Lines[Index].EndLocation, PlanNormal, Lines[Index].RadiusOrHeight(), MinmalChordLength, LinePoints);
					//Res = UGeometryRelativeLibrary::CalculateArcLineByHeight(this->PlanBelongs, Lines[Index].StartLocation, Lines[Index].EndLocation, Lines[Index].RadiusOrHeight(), Lines[Index].InterpPointCount(), LinePoints);
				}
			}
			else if (ELineType::ERadiusArc == Lines[Index].LineType)
			{
				const float MinmalChordLength = Lines[Index].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
				Res = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Lines[Index].StartLocation, Lines[Index].EndLocation, PlanNormal, Lines[Index].RadiusOrHeight(), Lines[Index].BigArc, MinmalChordLength, LinePoints);
				//Res = UGeometryRelativeLibrary::CalculateArcLineByRadius(this->PlanBelongs, Lines[Index].StartLocation, Lines[Index].EndLocation, Lines[Index].RadiusOrHeight(), Lines[Index].InterpPointCount(), LinePoints, !Lines[Index].BigArc);
			}
			else
			{
				Res = true;
			}
			if (!Res) return false;
			if (LinePoints.Num() > 0)
			{
				int32 Offset = SectionPoints.AddDefaulted(LinePoints.Num());
				for (int32 i = 0; i < LinePoints.Num(); ++i, ++Offset)
				{
					SectionPoints[Offset].Key = LinePoints[i];
					SectionPoints[Offset].Value = false;
				}
			}
			++Index;
		}
		return true;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		FPMCSection MeshInfo;
		if (UGeometryRelativeLibrary::GenerateRectangleMesh(this->PlanBelongs, this->Rectangle.StartLocation(), this->Rectangle.EndLocation(), MeshInfo))
		{
			int32 Offset = SectionPoints.AddDefaulted(MeshInfo.Vertexes.Num());
			for (; Offset < MeshInfo.Vertexes.Num(); ++Offset)
				SectionPoints[Offset].Key = MeshInfo.Vertexes[Offset];
			return true;
		}
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		FPMCSection MeshInfo;
		if (UGeometryRelativeLibrary::GenerateEllipseMesh(this->PlanBelongs, this->Ellipse.CenterLocation(), this->Ellipse.ShortRadius(), this->Ellipse.LongRadius(), this->Ellipse.InterPointCount(), MeshInfo))
		{
			int32 Offset = SectionPoints.AddDefaulted(MeshInfo.Vertexes.Num());
			for (; Offset < MeshInfo.Vertexes.Num(); ++Offset)
				SectionPoints[Offset].Key = MeshInfo.Vertexes[Offset];
			return true;
		}
	}
	return false;
}

FVector FCrossSectionData::GetSectionArcNormal() const
{
	FVector PlanNormal = NSPlanType::GetPlanNormal(PlanBelongs);
	{//当前多边形为顺时针需要把法线方向反转
		TArray<FVector> PolygonPoints;
		PolygonPoints.Init(FVector::ZeroVector, Points.Num());
		for (int32 i = 0; i < Points.Num(); ++i)
		{
			const auto& Iter = Points[i];
			PolygonPoints[i] = EPositionType::EAbsolute == Iter.PositionType ? Iter.PointLocation() : Iter.PointLocation() + Iter.PrePointLocation;
		}
		if (Points.Num() >= 3)
		{
			bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(PolygonPoints, PlanNormal);
			if (bCCWWinding) PlanNormal *= -1.0f;
		}
	}
	return PlanNormal;
}


void FCrossSectionData::clockwise()
{


	FVector PlanNormal = NSPlanType::GetPlanNormal(PlanBelongs);
	TArray<FVector> PolygonPoints;
	PolygonPoints.Init(FVector::ZeroVector, Points.Num());
	for (int32 i = 0; i < Points.Num(); ++i)
	{
		const auto& Iter = Points[i];
		PolygonPoints[i] = EPositionType::EAbsolute == Iter.PositionType ? Iter.PointLocation() : Iter.PointLocation() + Iter.PrePointLocation;
	}
	if (FPolygon3DLibrary::IsPolygonCCWWinding(PolygonPoints, PlanNormal))
		if (Points.Num() >= 3)
		{
			bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(PolygonPoints, PlanNormal);
			if (bCCWWinding)
			{
				FArrayOperatorLibrary::ReverseArray(Points);

			}
		}
}
FVector FCrossSectionData::GetPolygonOutsideNormal(EPlanPolygonBelongs PolygonPlan, TArray<FVector>& InPolygon)
{
	FVector PlanNormal = NSPlanType::GetPlanNormal(PolygonPlan);
	FPolygon3DLibrary::RemoveCoincidencePoints(InPolygon, true);
	if (InPolygon.Num() >= 3)
	{
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(InPolygon, PlanNormal);
		if (bCCWWinding)
		{

			FArrayOperatorLibrary::ReverseArray(InPolygon);
			auto first = InPolygon.Pop();
			InPolygon.Insert(first, 0);

		}
	}
	return PlanNormal;
}

bool FCrossSectionData::ConvertToPolygon(FPolygonData& OutPolygon) const
{
	const FVector Normal = NSPlanType::GetPlanNormal(PlanBelongs);
	const FVector TangentX = NSPlanType::GetPlanTangentX(PlanBelongs);
	OutPolygon.PolygonPlan = this->PlanBelongs;

	if (ESectionType::ECustomPlan == SectionType)
	{

		if (Points.Num() > 0)
		{
			if (Points.Num() != Lines.Num())
				return false;
			//FVector PlanNormal = GetSectionArcNormal();
			int32 Index = 0;

			for (auto& Iter : Points)
			{
				OutPolygon.PolygonVertice.Add(EPositionType::EAbsolute == Iter.PositionType ? Iter.PointLocation() : Iter.PointLocation() + Iter.PrePointLocation);
				TArray<FVector> LinePoints;
				bool Res = false;
				if (ELineType::EHeightArc == Lines[Index].LineType)
				{
					if (FMath::IsNearlyZero(Lines[Index].RadiusOrHeight()))
					{
						Res = true;
					}
					else
					{
						Res = FGeometryLibrary::createArcSegmentByHeight(Lines[Index].RadiusOrHeight(), Lines[Index].StartLocation, Lines[Index].EndLocation, 50, SectionNormal, FTransform(), LinePoints);
					}
				}
				else if (ELineType::ERadiusArc == Lines[Index].LineType)
				{
					Res = FGeometryLibrary::createArcSegmentByRadius(Lines[Index].RadiusOrHeight(), Lines[Index].StartLocation, Lines[Index].EndLocation, 5, SectionNormal, Lines[Index].BigArc, FTransform(), LinePoints);
				}
				else
				{
					Res = true;
				}
				if (!Res)
					return false;
				OutPolygon.PolygonVertice.Append(LinePoints);
				++Index;
			}
			return true;
		}
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		TArray<FVector> RectPoints;
		bool Res = FGeometry3DLibrary::GenerateRectanglePlanPoints(Rectangle.StartLocation(), Rectangle.EndLocation(), Normal, TangentX, RectPoints);

		if (RectPoints.Num() >= 3)
		{
			bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(RectPoints, Normal);
			if (bCCWWinding)
			{

				FArrayOperatorLibrary::ReverseArray(RectPoints);
				auto first = RectPoints.Pop();
				RectPoints.Insert(first, 0);
			}
		}

		if (Res)
			OutPolygon.PolygonVertice.Append(RectPoints);
		return Res;
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		TArray<FVector> EllipsePoints;
		bool Res = FGeometry3DLibrary::GenerateEllipsePlanPoints(Ellipse.CenterLocation(), Ellipse.LongRadius(), Ellipse.ShortRadius(), Normal, TangentX, EllipsePoints, Ellipse.InterPointCount());
		if (EllipsePoints.Num() >= 3)
		{
			bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(EllipsePoints, Normal);
			if (bCCWWinding)
			{

				FArrayOperatorLibrary::ReverseArray(EllipsePoints);
				auto first = EllipsePoints.Pop();
				EllipsePoints.Insert(first, 0);

			}
		}
		if (Res)
			OutPolygon.PolygonVertice.Append(EllipsePoints);
		return Res;
	}
	return false;
}

void FCrossSectionData::reversal()
{
	SectionNormal = -SectionNormal;
	if (Points.Num() > 0)
	{
		FArrayOperatorLibrary::ReverseArray(Points);
		auto first = Points.Pop();
		Points.Insert(first, 0);
		for (auto& iter : Lines)
		{
			auto temp = iter.StartLocation;
			iter.StartLocation = iter.EndLocation;
			iter.EndLocation = temp;
		}
		FArrayOperatorLibrary::ReverseArray(Lines);
	}
}

bool FCrossSectionData::TransformSection(const FSectionOperation& InOperations, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	if (ESectionType::ECube != SectionType)
	{
		if (1 == InOperations.OperatorOrder.Num() && ESectionOperationType::ELoftingSection == InOperations.OperatorOrder[0].OperatorType)
		{
			TArray<FVector> LoftRoutinePoints;//新的放样逻辑
			InOperations.LoftingOperation.LoftRoutine(LoftRoutinePoints);
			if (LoftRoutinePoints.Num() < 1)
			{
				bool Res = true;
				FPolygonData OriginalPolygon;
				Res = this->ConvertToPolygon(OriginalPolygon);
				Res = Res && OriginalPolygon.GenerateMesh(OutMeshInfo);
				Res = Res && OriginalPolygon.GenerateOutlineFramwork(OutFramwork);
				return Res;
			}
			bool bLoop = LoftRoutinePoints.Num() > 2 && (LoftRoutinePoints[0] - LoftRoutinePoints[LoftRoutinePoints.Num() - 1]).IsNearlyZero(THRESH_POINTS_ARE_SAME);
			FLoftRoutine LoftRoutine(LoftRoutinePoints, NSPlanType::GetPlanNormal(InOperations.LoftingOperation.PlanBelongs), NSPlanType::GetPlanTangentX(InOperations.LoftingOperation.PlanBelongs), bLoop);
			TArray<TPair<FVector, bool>> SectionPoints;
			this->GetCrossSectionPoints(SectionPoints);
			TArray<FPolygonPoint> LoftSectionPoints;
			LoftSectionPoints.Init(FPolygonPoint(), SectionPoints.Num());
			for (int32 i = 0; i < SectionPoints.Num(); ++i)
			{
				LoftSectionPoints[i].Point = SectionPoints[i].Key;
				LoftSectionPoints[i].bOutline = SectionPoints[i].Value;
			}
			//FVolatileLoftSection LoftSection(LoftSectionPoints, NSPlanType::GetPlanTangentY(PlanBelongs), NSPlanType::GetPlanTangentX(PlanBelongs), NSPlanType::GetPlanNormal(PlanBelongs), 100.0f);
			//FPolygonLoftLibrary::GenerateLoftMeshWithOutline(LoftRoutine, LoftSection, OutMeshInfo, OutFramwork);
			FCatalogLoftSection LoftSection(LoftSectionPoints, NSPlanType::GetPlanNormal(PlanBelongs), NSPlanType::GetPlanTangentX(PlanBelongs), 100.0f);
			FPolygonLoftLibrary::GenerateLoftMeshWithOutline(LoftRoutine, LoftSection, OutMeshInfo, OutFramwork);
			return true;


			//FPolygonData LoftRoutine;
			//if (InOperations.LoftingOperation.LoftRoutine(LoftRoutine))
			//{
			//	if (0 == LoftRoutine.PolygonVertice.Num())
			//	{
			//		bool Res = true;
			//		FPolygonData OriginalPolygon;
			//		Res = this->ConvertToPolygon(OriginalPolygon);
			//		if (!Res)
			//			return false;
			//		Res = OriginalPolygon.GenerateMesh(OutMeshInfo);
			//		if (!Res)
			//			return false;
			//		Res = OriginalPolygon.GenerateOutlineFramwork(OutFramwork);
			//		return Res;
			//	}
			//	FVector RoutineNormal = FVector::UpVector;
			//	switch (InOperations.LoftingOperation.PlanBelongs)
			//	{
			//	case EPlanPolygonBelongs::EXY_Plan:break;
			//	case EPlanPolygonBelongs::EXZ_Plan:RoutineNormal = FVector::RightVector; break;
			//	case EPlanPolygonBelongs::EYZ_Plan:RoutineNormal = -FVector::ForwardVector; break;
			//	}
			//	FVector SectionUpVector = FVector::UpVector;
			//	if (EPlanPolygonBelongs::EXY_Plan == this->PlanBelongs)
			//		SectionUpVector = FVector::RightVector;
			//	else if (EPlanPolygonBelongs::EXZ_Plan == this->PlanBelongs)
			//		SectionUpVector = FVector::ForwardVector;
			//	return this->LoftSection(LoftRoutine, SectionUpVector, RoutineNormal, OutMeshInfo, OutFramwork);
			//}
			//return false;
		}
		TArray<FCrossSectionData> SectionsAfterTransform;
		FSectionOperation FormatOperation;
		SectionNormal = NSPlanType::GetPlanNormal(PlanBelongs);
		InOperations.FormatSectionOperation(FormatOperation);
		bool Res = this->SlicesAfterTransform(FormatOperation, SectionsAfterTransform);
		if (Res)
		{
			Res = this->JointSlices(FormatOperation, SectionsAfterTransform, OutMeshInfo, OutFramwork);
		}
		return Res;
	}
	else
	{
		this->Cube.GenerateMeshAndFramwork(OutMeshInfo, OutFramwork);
		return true;
	}
	//return false;
}

bool FCrossSectionData::SlicesAfterTransform(const FSectionOperation& InOperation, TArray<FCrossSectionData>& OutSlices)
{
	OutSlices.Empty();
	bool Res = true;
	this->NeedDisplay = true;
	this->bGenerateGap = true;
	this->bCapInner = false;
	this->Type = ECrossSectionType::EHousing;
	OutSlices.Push(*this);
	for (int32 OperatorIndex = 0; OperatorIndex < InOperation.OperatorOrder.Num(); ++OperatorIndex)
	{
		const auto& Iter = InOperation.OperatorOrder[OperatorIndex];
		const int32 NextOperatorIndex = OperatorIndex + 1;
		if (ESectionOperationType::EDrawSection == Iter.OperatorType)
		{
			const FVector DrawOffset = InOperation.DrawOperations[Iter.Index].DrawOffset();
			if (DrawOffset.IsNearlyZero(THRESH_SPLIT_POLY_PRECISELY)) continue;
			FCrossSectionData NewSection;
			Res = OutSlices.Top().DrawSection(DrawOffset, NewSection);
			if (!Res)return false;
			//仅连续拉伸时最后一次拉伸产生的面可见
			NewSection.NeedDisplay = !(InOperation.OperatorOrder.IsValidIndex(NextOperatorIndex) && (ESectionOperationType::EDrawSection == InOperation.OperatorOrder[NextOperatorIndex].OperatorType));
			NewSection.bGenerateGap = true;
			NewSection.bCapInner = false;
			const auto PlanNormal = NSPlanType::GetPlanNormal(NewSection.PlanBelongs);
			NewSection.SectionNormal = ((PlanNormal | InOperation.DrawOperations[Iter.Index].DrawOffset()) * PlanNormal).GetSafeNormal();
			//OutSlices.Top().SectionNormal = -1.0f * NewSection.SectionNormal;
			OutSlices.Top().reversal();
			NewSection.Type = ECrossSectionType::EHousing;
			OutSlices.Push(NewSection);
		}
		else if (ESectionOperationType::EShiftSection == Iter.OperatorType)
		{
			FCrossSectionData NewSection;
			TArray<float> ShiftValue;
			bool bValidShift = InOperation.ShiftOperations[Iter.Index].ShiftValues(ShiftValue);
			if (false == bValidShift) continue;
			Res = OutSlices.Top().ShiftSection(ShiftValue, NewSection);
			if (!Res) return false;
			NewSection.bGenerateGap = InOperation.OperatorOrder.IsValidIndex(NextOperatorIndex) && (ESectionOperationType::EZoomSection != InOperation.OperatorOrder[NextOperatorIndex].OperatorType);
			NewSection.bCapInner = false;
			OutSlices.Top().NeedDisplay = false;//偏移后之前的面不显示，新旧面之间的空隙用cap填充
			OutSlices.Top().bGenerateGap = true;
			OutSlices.Top().bCapInner = false;
			NewSection.NeedDisplay = true;
			NewSection.Type = EAnd;
			OutSlices.Push(NewSection);
		}
		else if (ESectionOperationType::EZoomSection == Iter.OperatorType)
		{
			TArray<float> ZoomValue;
			bool bValidZoom = InOperation.ZoomOperations[Iter.Index].ScaleValues(ZoomValue);
			if (false == bValidZoom) continue;
			Res = OutSlices.Top().ZoomSection(ZoomValue);
			if (!Res) return false;
		}
		else if (ESectionOperationType::ECutoutSection == Iter.OperatorType)
		{
			float CutoutValue = InOperation.CutoutOperations[Iter.Index].CutoutValue();
			float SectionHeight = OutSlices.Top().GetHeight();
			if (OutSlices.Num() > 1 && !FMath::IsNearlyZero(CutoutValue) && CutoutValue * SectionHeight <= 0.0f)
			{
				if (!InOperation.CutoutOperations[Iter.Index].IsEmpty())
				{//使用用户画的截面进行挖空
					FCrossSectionData CutOutSection;
					Res = InOperation.CutoutOperations[Iter.Index].ConvertToCrossSection(CutOutSection);
					if (!Res)
						return false;
					if (!OutSlices.Top().IsAnotherSectionInside(CutOutSection))
						return false;
					CutOutSection.MoveSectionTo(SectionHeight);
					CutOutSection.ConvertToPolygon(OutSlices.Top().HoleSection);
					OutSlices.Top().bGenerateGap = false;//Fix bug CATALOG-1380
					CutOutSection.Type = CutoutValue > 0 ? ECrossSectionType::EAnd : ECrossSectionType::ENot;
					OutSlices.Push(CutOutSection);
				}
				else if (OutSlices.IsValidIndex(OutSlices.Num() - 2))
				{//使用上一个界面进行挖减
					if (!OutSlices[OutSlices.Num() - 2].IsAnotherSectionInside(OutSlices[OutSlices.Num() - 1]))
					{
						return false;
					}
					//Fix bug CATALOG-1376
					if (OutSlices.Num() > 2)
					{
						OutSlices[OutSlices.Num() - 1].ConvertToPolygon(OutSlices[OutSlices.Num() - 2].HoleSection);
					}
				}
				else
				{
					return false;
				}



				//const float AbSectionHeight = FMath::Abs(SectionHeight);
				//const float AbCutoutValue = FMath::Abs(CutoutValue);
				//bool bCutThrough = FMath::IsNearlyEqual(AbSectionHeight, AbCutoutValue, 0.01f);
				//if (bCutThrough || (AbSectionHeight > AbCutoutValue))
				//{
				//	if (!OutSlices[0].IsAnotherSectionInside(OutSlices.Top()))
				//		return false;
				//}
				//FVector DrawDir = FVector::UpVector;
				//switch (PlanBelongs)
				//{
				//case EPlanPolygonBelongs::EYZ_Plan:DrawDir = FVector::ForwardVector; break;
				//case EPlanPolygonBelongs::EXZ_Plan:DrawDir = FVector::RightVector; break;
				//}
				//FCrossSectionData NewSection;
				//NewSection.NeedDisplay = AbSectionHeight > AbCutoutValue;
				//NewSection.bGenerateGap = InOperation.OperatorOrder.IsValidIndex(NextOperatorIndex) && (ESectionOperationType::EZoomSection != InOperation.OperatorOrder[NextOperatorIndex].OperatorType);
				//NewSection.bCapInner = true;
				//DrawDir *= AbSectionHeight > AbCutoutValue ? CutoutValue : -SectionHeight;
				//Res = OutSlices.Top().DrawSection(DrawDir, NewSection);
				//if (!Res) return false;
				//OutSlices.Top().NeedDisplay = false;
				//OutSlices.Top().bGenerateGap = true;
				//OutSlices.Top().bCapInner = true;
				//if ((AbSectionHeight < AbCutoutValue) || bCutThrough)
				//{
				//	if (!NewSection.ConvertToPolygon(OutSlices[0].HoleSection))
				//		return false;
				//}
				//NewSection.SectionNormal = NSPlanType::GetPlanNormal(NewSection.PlanBelongs);
				//OutSlices.Push(NewSection);
			}
		}
	}
	return Res;
}

bool FCrossSectionData::JointSlices(const FSectionOperation& InFormatOperation, TArray<FCrossSectionData>& InSlices, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	//bool Res = false;
	struct FMeshFramworkPair
	{
		FPMCSection MeshInfo;
		TArray<TPair<FVector, FVector>> Framwork;
	};
	TArray<FMeshFramworkPair> MeshFramwork;
	TArray<FCrossSectionData> HouseSlices;
	TArray<FCrossSectionData> AndSlices;
	TArray<FCrossSectionData> NotSlices;

	for (auto& iter : InSlices)
	{
		if (iter.Type == ECrossSectionType::EHousing)
		{
			HouseSlices.Add(iter);
		}
		else if (iter.Type == ECrossSectionType::EAnd)
		{
			AndSlices.Add(iter);
		}
		else if (iter.Type == ECrossSectionType::ENot)
		{
			NotSlices.Add(iter);
		}
	}
	FGeoMesh HouseMesh;
	if (HouseSlices.Num() == 1)
	{
		FPMCSection section;
		TArray<FVector> Vertexs;
		FPolygonData polygon;
		HouseSlices[0].ConvertToPolygon(polygon);
		Vertexs.Append(polygon.PolygonVertice);
		FGeometryLibrary::createPlanarPolygonMesh(Vertexs, HouseSlices[0].SectionNormal, HouseMesh);
		FGeometryEditLibrary::GenerateMeshFromTriangles(HouseMesh.vertexs, HouseMesh.indices, HouseSlices[0].SectionNormal, NSPlanType::GetPlanTangentX(HouseSlices[0].PlanBelongs), section);
		OutMeshInfo += section;

	}
	else if (HouseSlices.Num() == 2)
	{
		TArray<FPMCSection> sections;
		sections.SetNumZeroed(2);

		FGeoMesh HouseMesh0;
		TArray<FVector> Vertexs0;
		FPolygonData polygon0;
		HouseSlices[0].ConvertToPolygon(polygon0);
		Vertexs0.Append(polygon0.PolygonVertice);
		FGeometryLibrary::createPlanarPolygonMesh(Vertexs0, HouseSlices[0].SectionNormal, HouseMesh0);
		FGeometryEditLibrary::GenerateMeshFromTriangles(HouseMesh0.vertexs, HouseMesh0.indices, HouseSlices[0].SectionNormal, NSPlanType::GetPlanTangentX(HouseSlices[0].PlanBelongs), sections[0]);
		OutMeshInfo += sections[0];

		FGeoMesh HouseMesh1;
		TArray<FVector> Vertexs1;
		FPolygonData polygon1;
		HouseSlices[1].ConvertToPolygon(polygon1);
		Vertexs1.Append(polygon1.PolygonVertice);
		FGeometryLibrary::createPlanarPolygonMesh(Vertexs1, HouseSlices[1].SectionNormal, HouseMesh1);
		FGeometryEditLibrary::GenerateMeshFromTriangles(HouseMesh1.vertexs, HouseMesh1.indices, HouseSlices[1].SectionNormal, NSPlanType::GetPlanTangentX(HouseSlices[1].PlanBelongs), sections[1]);
		OutMeshInfo += sections[1];

		TMap<int32, FGeoMesh> geoMeshes;
		TArray<FPMCSection> tempSections;

		//CreateMeshBetweenCrossSection(HouseSlices[0], HouseSlices[1], geoMeshes, tempSections);

		for (auto& iter : tempSections)
		{
			OutMeshInfo += iter;

		}
	}
	else
	{
		return false;
	}
	FGeoMesh AddMesh;
	if (AndSlices.Num() > 0)
	{
		for (auto& iter : AndSlices)
		{
			FPMCSection section;
			TArray<FVector> Vertexs;
			FPolygonData polygon;
			HouseSlices[0].ConvertToPolygon(polygon);
			Vertexs.Append(polygon.PolygonVertice);
			FGeometryLibrary::createPlanarPolygonMesh(Vertexs, HouseSlices[0].SectionNormal, HouseMesh);
			FGeometryEditLibrary::GenerateMeshFromTriangles(HouseMesh.vertexs, HouseMesh.indices, HouseSlices[0].SectionNormal, NSPlanType::GetPlanTangentX(HouseSlices[0].PlanBelongs), section);
			OutMeshInfo += section;
		}

	}
	FGeoMesh NotMesh;

	//auto GenerateMeshAndFramwork = [&](const int32& Index, bool InOnlyFramwork = false)->bool
	//{
	//	FPolygonData PolygonData;
	//	bool bRes = InSlices[Index].ConvertToPolygon(PolygonData);
	//	if (false == bRes) return false;

	//	FMeshFramworkPair NewMeshFramwork;
	//	bRes = PolygonData.GenerateOutlineFramwork(NewMeshFramwork.Framwork);
	//	if (false == bRes) return false;
	//	if (InOnlyFramwork)
	//	{
	//		MeshFramwork.Push(NewMeshFramwork);
	//		return true;
	//	}

	//	if (InSlices[Index].HasHole())
	//	{
	//		bRes = InSlices[Index].HoleSection.GenerateOutlineFramwork(NewMeshFramwork.Framwork);
	//		if (false == bRes) return false;
	//		auto TangentX = NSPlanType::GetPlanTangentX(PolygonData.PolygonPlan);
	//		FShearePolygon ShearePolygon(PolygonData.PolygonVertice, InSlices[Index].SectionNormal, true, TangentX, PolygonData.PolygonVertice[0], 100.0f);
	//		TArray<FPMCTriangle> Triangles;
	//		bRes = FPolygonSheareLibrary::ShearePolygon(ShearePolygon, InSlices[Index].HoleSection.PolygonVertice, Triangles);
	//		if (false == bRes) return false;
	//		FGeometryEditLibrary::GenerateMeshFromTriangles(Triangles, NewMeshFramwork.MeshInfo);
	//		/*int32 OutPolygonIndex = 0;
	//		int32 InPolygonIndex = 0;
	//		UGeometryRelativeLibrary::JudgePolygonVerticesOrder(PolygonData);
	//		UGeometryRelativeLibrary::JudgePolygonVerticesOrder(InSlices[Index].HoleSection);
	//		FPolygonData InnerPolygonData;
	//		if (PolygonData.PolygonVerticesOrder == InSlices[Index].HoleSection.PolygonVerticesOrder)
	//		{
	//			UGeometryRelativeLibrary::ReversePolygonVerticesOrder(InSlices[Index].HoleSection, InnerPolygonData);
	//		}
	//		else
	//		{
	//			InnerPolygonData = InSlices[Index].HoleSection;
	//		}
	//		if (false == UGeometryRelativeLibrary::FindFirstMutuallyVisibleVertice(PolygonData, InnerPolygonData, OutPolygonIndex, InPolygonIndex))
	//			return false;

	//		FPolygonData MeltPolygonData;
	//		if (false == UGeometryRelativeLibrary::CombineOutterAndInnerPolygonIntoOne(PolygonData, InnerPolygonData, OutPolygonIndex, InPolygonIndex, MeltPolygonData))
	//			return false;
	//		if (false == MeltPolygonData.GenerateMesh(NewMeshFramwork.MeshInfo))
	//			return false;*/
	//	}
	//	else
	//	{
	//		FPolygon3DLibrary::GenerateMeshFromPolygon2D(PolygonData.PolygonVertice, InSlices[Index].SectionNormal, NSPlanType::GetPlanTangentX(PolygonData.PolygonPlan), NewMeshFramwork.MeshInfo);
	//	}
	//	MeshFramwork.Push(NewMeshFramwork);
	//	return true;
	//};
	//Res = GenerateMeshAndFramwork(0);
	//if (!Res)
	//	return false;
	//const int32 LastSliceIndex = InSlices.Num() - 1;
	//for (int32 i = 1; i < InSlices.Num(); ++i)
	//{
	//	if (InSlices[i - 1].bGenerateGap)
	//	{
	//		FMeshFramworkPair NewMeshFramwork;
	//		Res = FCrossSectionData::GenerateJointFacesBetweenCrossSection(InSlices[i - 1], InSlices[i], NewMeshFramwork.MeshInfo, NewMeshFramwork.Framwork);
	//		if (!Res) return false;
	//		MeshFramwork.Push(NewMeshFramwork);
	//	}
	//	Res = GenerateMeshAndFramwork(i);
	//	if (!Res) return false;
	//	if (!InSlices[i].NeedDisplay)
	//	{
	//		MeshFramwork.Top().MeshInfo.Empty();
	//	}
	//}
	//if (!InSlices[0].NeedDisplay)
	//{
	//	MeshFramwork[0].MeshInfo.Empty();
	//}

	FGeoMesh outMesh;
	//FGeometryLibrary::ballPivotingMesh(Vertexs, outMesh);

	//OutMeshInfo.Vertexes = outMesh.vertexs;
	//OutMeshInfo.Triangles = outMesh.indices;
	//OutMeshInfo.Normals.SetNumZeroed(outMesh.normals.Num());
	//for (int32 i = 0; i < outMesh.normals.Num(); ++i)
	//{
	//	OutMeshInfo.Normals[i].X = 0;//outMesh.normals[i].X;
	//	OutMeshInfo.Normals[i].Y = 0;// outMesh.normals[i].Y;
	//	OutMeshInfo.Normals[i].Z = 1;//outMesh.normals[i].Z;
	//}
	//OutMeshInfo.UV.SetNumZeroed(outMesh.uv.Num());

	//for (int32 i = 0; i < outMesh.uv.Num(); ++i)
	//{
	//	OutMeshInfo.UV[i].X = outMesh.uv[i].X;
	//	OutMeshInfo.UV[i].Y = outMesh.uv[i].Y;
	//}
	//for (auto& Iter : MeshFramwork)
	//{



	//	OutMeshInfo += Iter.MeshInfo;
	//	OutFramwork += Iter.Framwork;
	//}
	return true;
}



bool FCrossSectionData::GenerateJointFacesBetweenCrossSection(const FCrossSectionData& InFirstSection, const FCrossSectionData& InSecondSection, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	if (InFirstSection.SectionType != InSecondSection.SectionType)
		return false;
	if (ESectionType::ECustomPlan == InFirstSection.SectionType)
	{
		int32 Offset = OutFramwork.AddDefaulted(InFirstSection.Points.Num());
		TArray<struct FCustomPoint> Plan1Points;
		TArray<struct FCustomPoint> Plan2Points;
		const FVector Plan1Normal = InFirstSection.GetSectionArcNormal();
		const FVector Plan2Normal = InSecondSection.GetSectionArcNormal();
		TArray<FVector> OriginPoints;
		OriginPoints.Init(FVector::ZeroVector, InFirstSection.Points.Num());
		for (int32 i = 0; i < InFirstSection.Points.Num(); ++i)
		{
			OriginPoints[i] = EPositionType::EAbsolute == InFirstSection.Points[i].PositionType ? InFirstSection.Points[i].PointLocation() : InFirstSection.Points[i].PointLocation() + InFirstSection.Points[i].PrePointLocation;
			OutFramwork[Offset + i].Key = OriginPoints[i];
			OutFramwork[Offset + i].Value = EPositionType::EAbsolute == InSecondSection.Points[i].PositionType ? InSecondSection.Points[i].PointLocation() : InSecondSection.Points[i].PointLocation() + InSecondSection.Points[i].PrePointLocation;
			TArray<FVector> FirstLinePoints;
			TArray<FVector> SecondLinePoints;
			bool Res1 = true;
			bool Res2 = true;
			ELineType EditLineType = InFirstSection.Lines[i].LineType;
			if (ELineType::EHeightArc == EditLineType)
			{
				if (FMath::IsNearlyZero(InFirstSection.Lines[i].RadiusOrHeight(), 0.01f) && FMath::IsNearlyZero(InFirstSection.Lines[i].RadiusOrHeight(), 0.01f))
				{//如果弧形线的半径为0侧依照线段计算
					EditLineType = ELineType::ELineSegment;
				}
				else
				{
					const auto& Plan1Lines = InFirstSection.Lines;
					const auto& Plan2Lines = InSecondSection.Lines;
					if (Plan1Lines[i].RadiusOrHeight() >= Plan2Lines[i].RadiusOrHeight())
					{
						const float MinmalChordLength1 = Plan1Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res1 = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Plan1Lines[i].StartLocation, Plan1Lines[i].EndLocation, Plan1Normal, Plan1Lines[i].RadiusOrHeight(), MinmalChordLength1, FirstLinePoints);

						const float MinmalChordLength2 = Plan2Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res2 = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Plan2Lines[i].StartLocation, Plan2Lines[i].EndLocation, Plan2Normal, Plan2Lines[i].RadiusOrHeight(), MinmalChordLength2, SecondLinePoints, FirstLinePoints.Num());
					}
					else
					{
						const float MinmalChordLength2 = Plan2Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res2 = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Plan2Lines[i].StartLocation, Plan2Lines[i].EndLocation, Plan2Normal, Plan2Lines[i].RadiusOrHeight(), MinmalChordLength2, SecondLinePoints);

						const float MinmalChordLength1 = Plan1Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res1 = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Plan1Lines[i].StartLocation, Plan1Lines[i].EndLocation, Plan1Normal, Plan1Lines[i].RadiusOrHeight(), MinmalChordLength1, FirstLinePoints, SecondLinePoints.Num());
					}
				}
			}
			else if (ELineType::ERadiusArc == EditLineType)
			{
				if (FMath::IsNearlyZero(InFirstSection.Lines[i].RadiusOrHeight(), 0.01f) && FMath::IsNearlyZero(InFirstSection.Lines[i].RadiusOrHeight(), 0.01f))
				{//如果弧形线的半径为0侧依照线段计算
					EditLineType = ELineType::ELineSegment;
				}
				else
				{
					const auto& Plan1Lines = InFirstSection.Lines;
					const auto& Plan2Lines = InSecondSection.Lines;

					if (Plan1Lines[i].RadiusOrHeight() >= Plan2Lines[i].RadiusOrHeight())
					{
						const float MinmalChordLength1 = Plan1Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res1 = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Plan1Lines[i].StartLocation, Plan1Lines[i].EndLocation, Plan1Normal, Plan1Lines[i].RadiusOrHeight(), Plan1Lines[i].BigArc, MinmalChordLength1, FirstLinePoints);

						const float MinmalChordLength2 = Plan2Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res2 = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Plan2Lines[i].StartLocation, Plan2Lines[i].EndLocation, Plan2Normal, Plan2Lines[i].RadiusOrHeight(), Plan2Lines[i].BigArc, MinmalChordLength2, SecondLinePoints, FirstLinePoints.Num());

					}
					else
					{
						const float MinmalChordLength2 = Plan2Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res2 = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Plan2Lines[i].StartLocation, Plan2Lines[i].EndLocation, Plan2Normal, Plan2Lines[i].RadiusOrHeight(), Plan2Lines[i].BigArc, MinmalChordLength2, SecondLinePoints);

						const float MinmalChordLength1 = Plan1Lines[i].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
						Res1 = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Plan1Lines[i].StartLocation, Plan1Lines[i].EndLocation, Plan1Normal, Plan1Lines[i].RadiusOrHeight(), Plan1Lines[i].BigArc, MinmalChordLength1, FirstLinePoints, SecondLinePoints.Num());
					}
				}
			}
			if (ELineType::ELineSegment == EditLineType)
			{
				FCustomPoint NewPoint;
				NewPoint.NeedOutline = true;
				NewPoint.PointLocation = EPositionType::EAbsolute == InFirstSection.Points[i].PositionType ? InFirstSection.Points[i].PointLocation() : InFirstSection.Points[i].PointLocation() + InFirstSection.Points[i].PrePointLocation;
				Plan1Points.Add(NewPoint);

				NewPoint.PointLocation = EPositionType::EAbsolute == InSecondSection.Points[i].PositionType ? InSecondSection.Points[i].PointLocation() : InSecondSection.Points[i].PointLocation() + InSecondSection.Points[i].PrePointLocation;
				Plan2Points.Add(NewPoint);
			}
			if (!Res1 || !Res2)
				return false;
			if (ELineType::ERadiusArc == InFirstSection.Lines[i].LineType || ELineType::EHeightArc == InFirstSection.Lines[i].LineType)
			{
				TArray<FCustomPoint> NewPoints;
				FCustomPoint::ConvertPointsToCustomPoints(FirstLinePoints, false, NewPoints);
				Plan1Points.Append(NewPoints);
				NewPoints.Empty();
				FCustomPoint::ConvertPointsToCustomPoints(SecondLinePoints, false, NewPoints);
				Plan2Points.Append(NewPoints);
			}
		}
		{
			auto NormalType = InFirstSection.bCapInner ? ENormalType::Inner : ENormalType::Outter;
			bool bCCW = FPolygon3DLibrary::IsPolygonCCWWinding(OriginPoints, NSPlanType::GetPlanNormal(InFirstSection.PlanBelongs));
			if ((bCCW && (ENormalType::Outter == NormalType)) || (!bCCW && (ENormalType::Inner == NormalType)))
			{
				FArrayOperatorLibrary::ReverseArray(Plan1Points);
				FArrayOperatorLibrary::ReverseArray(Plan2Points);
			}
		}
		FPMCSection NewSectionMesh;
		bool Res = UGeomtryMathmaticLibrary::GenerateMeshOfTwoParallelPlans(NSPlanType::GetPlanNormal(InFirstSection.PlanBelongs), Plan1Points, Plan2Points, NewSectionMesh, OutFramwork, InFirstSection.PlanBelongs);
		OutMeshInfo += NewSectionMesh;
		return Res;
	}
	else if (ESectionType::ERectangle == InFirstSection.SectionType)
	{
		FGeomtryRectanglePlanProperty FirstRect = InFirstSection.Rectangle;
		FirstRect.PlanBelongs = InFirstSection.PlanBelongs;
		FGeomtryRectanglePlanProperty SecondRect = InSecondSection.Rectangle;
		SecondRect.PlanBelongs = InFirstSection.PlanBelongs;
		return FGeomtryRectanglePlanProperty::GenerateJointFacesBetweenRectangle(FirstRect, SecondRect, InFirstSection.bCapInner ? ENormalType::Inner : ENormalType::Outter, OutMeshInfo, OutFramwork);
	}
	else if (ESectionType::EEllipse == InFirstSection.SectionType)
	{
		FGeomtryEllipsePlanProperty FirstEllipse = InFirstSection.Ellipse;
		FirstEllipse.PlanBelongs = InFirstSection.PlanBelongs;
		FGeomtryEllipsePlanProperty SecondEllipse = InSecondSection.Ellipse;
		SecondEllipse.PlanBelongs = InFirstSection.PlanBelongs;
		return FGeomtryEllipsePlanProperty::GenerateJointFacesBetweenEllipse(InFirstSection.bCapInner ? ENormalType::Inner : ENormalType::Outter, FirstEllipse, SecondEllipse, OutMeshInfo, OutFramwork);
	}
	return false;
}

bool FCrossSectionData::DrawSection(const FVector& InDrawOffset, FCrossSectionData& OutSection)
{
	bool Res = false;
	OutSection.SectionType = this->SectionType;
	OutSection.PlanBelongs = this->PlanBelongs;
	if (ESectionType::ECustomPlan == SectionType)
	{
		int32 Index = 0;
		OutSection.Points = this->Points;
		OutSection.Lines = this->Lines;
		for (auto& Iter : Points)
		{
			if (EPositionType::EAbsolute == OutSection.Points[Index].PositionType)
			{
				OutSection.Points[Index].LocationX += UParameterPropertyData::ConvertToUIValue(InDrawOffset.X);
				OutSection.Points[Index].LocationY += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Y);
				OutSection.Points[Index].LocationZ += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Z);
			}
			OutSection.Points[Index].PrePointLocation += InDrawOffset;
			if (!OutSection.Lines.IsValidIndex(Index))
				return false;
			OutSection.Lines[Index].StartLocation += InDrawOffset;
			OutSection.Lines[Index].EndLocation += InDrawOffset;
			++Index;
		}
		Res = true;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		OutSection.Rectangle = this->Rectangle;
		OutSection.Rectangle.StartLocationX += UParameterPropertyData::ConvertToUIValue(InDrawOffset.X);
		OutSection.Rectangle.StartLocationY += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Y);
		OutSection.Rectangle.StartLocationZ += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Z);
		OutSection.Rectangle.EndLocationX += UParameterPropertyData::ConvertToUIValue(InDrawOffset.X);
		OutSection.Rectangle.EndLocationY += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Y);
		OutSection.Rectangle.EndLocationZ += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Z);
		Res = true;
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		OutSection.Ellipse = this->Ellipse;
		OutSection.Ellipse.CenterLocationX += UParameterPropertyData::ConvertToUIValue(InDrawOffset.X);
		OutSection.Ellipse.CenterLocationY += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Y);
		OutSection.Ellipse.CenterLocationZ += UParameterPropertyData::ConvertToUIValue(InDrawOffset.Z);
		Res = true;
	}
	return Res;
}

bool FCrossSectionData::ShiftSection(const TArray<float>& InShiftValue, FCrossSectionData& OutSection)
{
	OutSection.SectionType = this->SectionType;
	OutSection.PlanBelongs = this->PlanBelongs;
	OutSection.SectionNormal = this->SectionNormal;
	TArray<float> FormatShiftValue;
	if (ESectionType::ECustomPlan == SectionType)
	{
		OutSection.Points = this->Points;
		OutSection.Lines = this->Lines;
		FSectionShiftingOperation::FormatShiftValue(InShiftValue, FormatShiftValue, OutSection.Lines.Num());
		FPolygonData Plan1;
		Plan1.PolygonPlan = this->PlanBelongs;
		Plan1.PolygonVertice.AddZeroed(this->Points.Num());
		for (int32 i = 0; i < this->Points.Num(); ++i)
		{
			Plan1.PolygonVertice[i] = EPositionType::EAbsolute == this->Points[i].PositionType ? this->Points[i].PointLocation() : this->Points[i].PointLocation() + this->Points[i].PrePointLocation;
		}
		FVector Normal = FVector::UpVector;
		switch (PlanBelongs)
		{
		case EPlanPolygonBelongs::EXY_Plan:Normal = FVector::UpVector; break;
		case EPlanPolygonBelongs::EYZ_Plan:Normal = FVector::ForwardVector; break;
		case EPlanPolygonBelongs::EXZ_Plan:Normal = FVector::RightVector; break;
		case EPlanPolygonBelongs::EUnknown:break;
		}
		TArray<FVector> Plan2;
		TArray<FVector> ShiftOffset;
		TArray<float> DeltaHeight;

		bool Res = UGeomtryMathmaticLibrary::ShiftPlanEdges(FormatShiftValue, OutSection.Lines, OutSection.SectionNormal, Plan1, Plan2, ShiftOffset, DeltaHeight);
		if (!Res)
			return false;
		for (int32 i = 0; i < OutSection.Points.Num(); ++i)
		{
			if (EPositionType::EAbsolute == OutSection.Points[i].PositionType)
			{
				OutSection.Points[i].LocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].X);
				OutSection.Points[i].LocationX.Expression = OutSection.Points[i].LocationX.Value;
				OutSection.Points[i].LocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].Y);
				OutSection.Points[i].LocationY.Expression = OutSection.Points[i].LocationY.Value;
				OutSection.Points[i].LocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].Z);
				OutSection.Points[i].LocationZ.Expression = OutSection.Points[i].LocationZ.Value;
			}
			if (i > 0)
			{
				OutSection.Points[i].PrePointLocation = OutSection.Points[i - 1].PointLocation();
			}
		}
		for (int32 j = 0; j < OutSection.Lines.Num(); ++j)
		{
			FVector NewStartLocation = OutSection.Points[j].PointLocation();
			FVector NewEndLocation = OutSection.Points[(j + 1) % OutSection.Points.Num()].PointLocation();
			float ShiftRaiudsOrHeight = FormatShiftValue[j];

			if (ELineType::EHeightArc == OutSection.Lines[j].LineType || ELineType::ERadiusArc == OutSection.Lines[j].LineType)
			{
				bool bOldH = FCString::Atof(*OutSection.Lines[j].RadiusOrHeightData.Value) > 0;
				OutSection.Lines[j].RadiusOrHeightData += UParameterPropertyData::ConvertToUIValue(DeltaHeight[j]);
				bool bNewH = FCString::Atof(*OutSection.Lines[j].RadiusOrHeightData.Value) > 0;
				//if ((bOldH && !bNewH)  || (!bOldH && bNewH))
				//{
				//	OutSection.Lines[j].RadiusOrHeightData.Expression = TEXT("0");
				//	OutSection.Lines[j].RadiusOrHeightData.Value = TEXT("0");
				//}
			}
			OutSection.Lines[j].StartLocation = NewStartLocation;
			OutSection.Lines[j].EndLocation = NewEndLocation;
		}
		return true;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		this->Rectangle.PlanBelongs = this->PlanBelongs;
		return this->Rectangle.ShiftSection(InShiftValue, OutSection.Rectangle);
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		this->Ellipse.PlanBelongs = this->PlanBelongs;
		return this->Ellipse.ShiftSection(InShiftValue, OutSection.Ellipse);
	}
	return false;
}

bool FCrossSectionData::ZoomSection(const TArray<float>& InZoomValue)
{
	TArray<float> FormatShiftValue;
	if (ESectionType::ECustomPlan == SectionType)
	{
		FSectionShiftingOperation::FormatShiftValue(InZoomValue, FormatShiftValue, this->Lines.Num());
		FPolygonData Plan1;
		Plan1.PolygonPlan = this->PlanBelongs;
		Plan1.PolygonVertice.AddZeroed(this->Points.Num());
		for (int32 i = 0; i < this->Points.Num(); ++i)
		{
			Plan1.PolygonVertice[i] = EPositionType::EAbsolute == this->Points[i].PositionType ? this->Points[i].PointLocation() : this->Points[i].PointLocation() + this->Points[i].PrePointLocation;
		}
		FVector Normal = FVector::UpVector;
		switch (PlanBelongs)
		{
		case EPlanPolygonBelongs::EXY_Plan:Normal = FVector::UpVector; break;
		case EPlanPolygonBelongs::EYZ_Plan:Normal = FVector::ForwardVector; break;
		case EPlanPolygonBelongs::EXZ_Plan:Normal = FVector::RightVector; break;
		case EPlanPolygonBelongs::EUnknown:break;
		}
		TArray<FVector> Plan2;
		TArray<FVector> ShiftOffset;
		TArray<float> DeltaHeight;

		bool Res = UGeomtryMathmaticLibrary::ShiftPlanEdges(FormatShiftValue, this->Lines, this->SectionNormal, Plan1, Plan2, ShiftOffset, DeltaHeight);
		if (!Res)
			return false;
		for (int32 i = 0; i < this->Points.Num(); ++i)
		{
			if (EPositionType::EAbsolute == this->Points[i].PositionType)
			{
				this->Points[i].LocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].X);
				this->Points[i].LocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].Y);
				this->Points[i].LocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[i].Z);
			}
			if (i > 0)
			{
				this->Points[i].PrePointLocation = this->Points[i - 1].PointLocation();
			}
		}
		for (int32 j = 0; j < this->Lines.Num(); ++j)
		{
			FVector NewStartLocation = this->Points[j].PointLocation();
			FVector NewEndLocation = this->Points[(j + 1) % this->Points.Num()].PointLocation();
			float ShiftRaiudsOrHeight = FormatShiftValue.IsValidIndex(j) ? FormatShiftValue[j] : (1 == FormatShiftValue.Num() ? FormatShiftValue[0] : 0.0f);
			if (ELineType::EHeightArc == Lines[j].LineType || ELineType::ERadiusArc == Lines[j].LineType)
			{
				bool bOldH = FCString::Atof(*Lines[j].RadiusOrHeightData.Value) > 0;
				Lines[j].RadiusOrHeightData += UParameterPropertyData::ConvertToUIValue(DeltaHeight[j]);
				bool bNewH = FCString::Atof(*Lines[j].RadiusOrHeightData.Value) > 0;
				if ((bOldH && !bNewH) || (!bOldH && bNewH))
				{
					Lines[j].RadiusOrHeightData.Expression = TEXT("0");
					Lines[j].RadiusOrHeightData.Value = TEXT("0");
				}
			}
			this->Lines[j].StartLocation = NewStartLocation;
			this->Lines[j].EndLocation = NewEndLocation;
		}
		return true;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		this->Rectangle.PlanBelongs = this->PlanBelongs;
		return this->Rectangle.ZoomSection(InZoomValue);
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		this->Ellipse.PlanBelongs = this->PlanBelongs;
		return this->Ellipse.ZoomSection(InZoomValue);
	}
	return false;
}

bool FCrossSectionData::LoftSection(FPolygonData& InLoftRoutine, const FVector& InSectionUpVector, const FVector& InRoutineNormal, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	FPolygonData OriginalPolygon;
	if (this->ConvertToPolygon(OriginalPolygon))
	{
		UGeometryRelativeLibrary::JudgePolygonVerticesOrder(OriginalPolygon);
		FPMCSection OriginalMesh;
		if (OriginalPolygon.GenerateMesh(OriginalMesh))
		{
			TArray<FPMCSection> LoftMeshs;
			bool NeedLoop = false;
			if (UGeomtryMathmaticLibrary::LoftSection(OriginalMesh, InSectionUpVector, InLoftRoutine, InRoutineNormal, LoftMeshs, NeedLoop))
			{
				OutMeshInfo.Empty();
				for (auto& Iter : LoftMeshs)
				{
					OutMeshInfo.Normals.AddZeroed(Iter.Vertexes.Num());
					OutMeshInfo.Vertexes.Append(Iter.Vertexes);
				}
				auto GetAllPointLocation = [&](TArray<int32>& OutPointIndex)
				{
					TArray<FVector> FramPoints;
					if (ESectionType::ECustomPlan == SectionType)
					{
						int32 Index = 0;
						FramPoints.AddZeroed(Points.Num());
						for (auto& Iter : Points)
							FramPoints[Index++] = (EPositionType::EAbsolute == Iter.PositionType ? Iter.PointLocation() : Iter.PointLocation() + Iter.PrePointLocation);
					}
					else if (ESectionType::ERectangle == SectionType)
					{
						FPMCSection MeshInfo;
						UGeometryRelativeLibrary::GenerateRectangleMesh(this->PlanBelongs, this->Rectangle.StartLocation(), this->Rectangle.EndLocation(), MeshInfo);
						FramPoints.Append(MeshInfo.Vertexes);
					}
					for (auto& Iter : FramPoints)
					{
						int32 Index = OriginalMesh.Vertexes.Find(Iter);
						if (INDEX_NONE != Index)
							OutPointIndex.Add(Index);
					}
				};
				TArray<int32> FramPoints;
				GetAllPointLocation(FramPoints);
				for (int32 i = 0; i < OriginalMesh.Vertexes.Num(); ++i)
				{
					const int32 NextPoint = (i + 1) % OriginalMesh.Vertexes.Num();
					const int32 PrePoint = (i - 1 + OriginalMesh.Vertexes.Num()) % OriginalMesh.Vertexes.Num();
					EVerticeType VerticeType;
					UGeometryRelativeLibrary::JudgePolygonVerticesType(OriginalMesh.Vertexes[PrePoint], OriginalMesh.Vertexes[NextPoint], OriginalMesh.Vertexes[i], OriginalPolygon.PolygonPlan, OriginalPolygon.PolygonVerticesOrder, VerticeType);
					int32 EndIndex = NeedLoop ? LoftMeshs.Num() : LoftMeshs.Num() - 1;
					for (int32 j = 0; j < EndIndex; ++j)
					{
						int32 Offset = OutMeshInfo.Triangles.AddZeroed(6);
						int32 NextMeshIndex = (j + 1) % LoftMeshs.Num();
						int32 P1 = j * OriginalMesh.Vertexes.Num() + i;
						int32 P2 = NextMeshIndex * OriginalMesh.Vertexes.Num() + i;
						int32 P3 = j * OriginalMesh.Vertexes.Num() + NextPoint;
						int32 P4 = NextMeshIndex * OriginalMesh.Vertexes.Num() + NextPoint;
						int32 P5 = j * OriginalMesh.Vertexes.Num() + PrePoint;
						int32 P6 = NextMeshIndex * OriginalMesh.Vertexes.Num() + PrePoint;

						OutMeshInfo.Triangles[Offset + 0] = P1;
						OutMeshInfo.Triangles[Offset + 1] = P2;
						OutMeshInfo.Triangles[Offset + 2] = P4;
						OutMeshInfo.Triangles[Offset + 3] = P1;
						OutMeshInfo.Triangles[Offset + 4] = P4;
						OutMeshInfo.Triangles[Offset + 5] = P3;

						{
							FVector V1 = OutMeshInfo.Vertexes[P3] - OutMeshInfo.Vertexes[P1];
							V1.Normalize();
							FVector V2 = OutMeshInfo.Vertexes[P5] - OutMeshInfo.Vertexes[P1];
							V2.Normalize();
							OutMeshInfo.Normals[P1] = EVerticeType::EConcave == VerticeType ? (V1 + V2) : (-V1 - V2);
							OutMeshInfo.Normals[P1].Normalize();

							V1 = OutMeshInfo.Vertexes[P4] - OutMeshInfo.Vertexes[P2];
							V1.Normalize();
							V2 = OutMeshInfo.Vertexes[P6] - OutMeshInfo.Vertexes[P2];
							V2.Normalize();
							OutMeshInfo.Normals[P2] = EVerticeType::EConcave == VerticeType ? (V1 + V2) : (-V1 - V2);
							OutMeshInfo.Normals[P2].Normalize();
						}

						FVector V1 = OutMeshInfo.Vertexes[P2] - OutMeshInfo.Vertexes[P1];
						V1.Normalize();
						FVector V2 = OutMeshInfo.Vertexes[P4] - OutMeshInfo.Vertexes[P2];
						V2.Normalize();
						FVector Normal = FVector::CrossProduct(V1, V2);
						if (FVector::DotProduct(Normal, OutMeshInfo.Normals[P1]) > 0.0f)
						{
							OutMeshInfo.Triangles[Offset + 0] = P4;
							OutMeshInfo.Triangles[Offset + 2] = P1;
							OutMeshInfo.Triangles[Offset + 3] = P3;
							OutMeshInfo.Triangles[Offset + 5] = P1;
						}

						OutFramwork.Add(TPair<FVector, FVector>(OutMeshInfo.Vertexes[P1], OutMeshInfo.Vertexes[P3]));
						if (INDEX_NONE != FramPoints.Find(i))
							OutFramwork.Add(TPair<FVector, FVector>(OutMeshInfo.Vertexes[P1], OutMeshInfo.Vertexes[P2]));
					}
				}
				if (!NeedLoop)
				{
					{
						FVector V1 = LoftMeshs[0].Vertexes[LoftMeshs[0].Triangles[1]] - LoftMeshs[0].Vertexes[LoftMeshs[0].Triangles[0]];
						V1.Normalize();
						FVector V2 = LoftMeshs[0].Vertexes[LoftMeshs[0].Triangles[2]] - LoftMeshs[0].Vertexes[LoftMeshs[0].Triangles[1]];
						V2.Normalize();
						FVector Normal = FVector::CrossProduct(V1, V2);
						int32 Index = FVector::DotProduct(Normal, LoftMeshs[0].Normals[LoftMeshs[0].Triangles[1]]) > 0.0f ? 0 : LoftMeshs.Num() - 1;
						int32 i = 0;
						UE_LOG(LogTemp, Log, TEXT("Normal is: %s index is: %d vertex normal is : %s "), *Normal.ToString(), Index, *LoftMeshs[0].Normals[LoftMeshs[0].Triangles[1]].ToString());
						while (i < LoftMeshs[Index].Triangles.Num())
						{
							int32 a = LoftMeshs[Index].Triangles[i];
							LoftMeshs[Index].Triangles[i] = LoftMeshs[Index].Triangles[i + 2];
							LoftMeshs[Index].Triangles[i + 2] = a;
							i += 3;
						}
					}

					OutMeshInfo.Triangles.Append(LoftMeshs[0].Triangles);
					int32 Offset = OutMeshInfo.Triangles.Num();
					OutMeshInfo.Triangles.Append(LoftMeshs[LoftMeshs.Num() - 1].Triangles);
					int32 StartIndex = (LoftMeshs.Num() - 1) * OriginalMesh.Vertexes.Num();
					for (auto& Iter : LoftMeshs[0].Triangles)
						OutMeshInfo.Triangles[Offset++] += StartIndex;
					int32 FramworkOffset = OutFramwork.AddDefaulted(OriginalMesh.Vertexes.Num());
					for (int32 i = 0; i < OriginalMesh.Vertexes.Num(); ++i)
						OutFramwork[FramworkOffset + i] = TPair<FVector, FVector>(LoftMeshs[LoftMeshs.Num() - 1].Vertexes[i], LoftMeshs[LoftMeshs.Num() - 1].Vertexes[(i + 1) % LoftMeshs[LoftMeshs.Num() - 1].Vertexes.Num()]);
				}
				return true;
			}
			UE_LOG(LogTemp, Error, TEXT("Loft section failed"));
			return false;
		}
		UE_LOG(LogTemp, Error, TEXT("FCrossSectionData::LoftSection OriginalPolygon.GenerateMesh"));
		return false;
	}
	UE_LOG(LogTemp, Error, TEXT("FCrossSectionData::LoftSection  this->ConvertToPolygon"));
	return false;
}

FBox FCrossSectionData::GetBoundingBox() const
{
	FBox BoundingBox;
	FPolygonData Polygon;
	if (this->ConvertToPolygon(Polygon))
	{
		if (0 == Polygon.PolygonVertice.Num())
			return BoundingBox;

		FVector MinPoint(99999.0f);
		FVector MaxPoint(-99999.0f);
		for (auto& Iter : Polygon.PolygonVertice)
		{
			MinPoint = FVector(FMath::Min<float>(MinPoint.X, Iter.X), FMath::Min<float>(MinPoint.Y, Iter.Y), FMath::Min<float>(MinPoint.Z, Iter.Z));
			MaxPoint = FVector(FMath::Max<float>(MaxPoint.X, Iter.X), FMath::Max<float>(MaxPoint.Y, Iter.Y), FMath::Max<float>(MaxPoint.Z, Iter.Z));
		}
		BoundingBox.Max = MaxPoint;
		BoundingBox.Min = MinPoint;
	}
	return BoundingBox;
}



bool FCrossSectionData::IsAnotherSectionInside(const FCrossSectionData& InOtherSection) const
{
	FBox ThisBox = this->GetBoundingBox();
	FBox OtherBox = InOtherSection.GetBoundingBox();
	if (EPlanPolygonBelongs::EXY_Plan == PlanBelongs)
	{
		return ThisBox.Min.X <= OtherBox.Min.X && ThisBox.Min.Y <= OtherBox.Min.Y && ThisBox.Max.X >= OtherBox.Max.X && ThisBox.Max.Y >= OtherBox.Max.Y;
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == PlanBelongs)
	{
		return ThisBox.Min.X <= OtherBox.Min.X && ThisBox.Min.Z <= OtherBox.Min.Z && ThisBox.Max.X >= OtherBox.Max.X && ThisBox.Max.Z >= OtherBox.Max.Z;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs)
	{
		return ThisBox.Min.Y <= OtherBox.Min.Y && ThisBox.Min.Z <= OtherBox.Min.Z && ThisBox.Max.Y >= OtherBox.Max.Y && ThisBox.Max.Z >= OtherBox.Max.Z;
	}
	return false;
}

float FCrossSectionData::GetHeight() const
{
	FPolygonData Polygon;
	if (this->ConvertToPolygon(Polygon))
	{
		if (EPlanPolygonBelongs::EXY_Plan == PlanBelongs)
		{
			return Polygon.PolygonVertice[0].Z;
		}
		else if (EPlanPolygonBelongs::EXZ_Plan == PlanBelongs)
		{
			return Polygon.PolygonVertice[0].Y;
		}
		else if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs)
		{
			return Polygon.PolygonVertice[0].X;
		}
	}
	return 0.0f;
}

void FCrossSectionData::MoveSectionTo(const float& InDeltaOffset)
{
	if (ESectionType::ECustomPlan == SectionType)
	{
		for (int32 i = 0; i < Points.Num(); ++i)
		{
			switch (PlanBelongs)
			{
			case EPlanPolygonBelongs::EXY_Plan:Points[i].LocationZ += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Points[i].PrePointLocation.Z += InDeltaOffset; break;
			case EPlanPolygonBelongs::EYZ_Plan:Points[i].LocationX += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Points[i].PrePointLocation.X += InDeltaOffset; break;
			case EPlanPolygonBelongs::EXZ_Plan:Points[i].LocationY += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Points[i].PrePointLocation.Y += InDeltaOffset; break;
			}
		}
		for (int32 i = 0; i < Lines.Num(); ++i)
		{
			switch (PlanBelongs)
			{
			case EPlanPolygonBelongs::EXY_Plan:Lines[i].StartLocation.Z += InDeltaOffset; Lines[i].EndLocation.Z += InDeltaOffset; break;
			case EPlanPolygonBelongs::EYZ_Plan:Lines[i].StartLocation.X += InDeltaOffset; Lines[i].EndLocation.X += InDeltaOffset; break;
			case EPlanPolygonBelongs::EXZ_Plan:Lines[i].StartLocation.Y += InDeltaOffset; Lines[i].EndLocation.Y += InDeltaOffset; break;
			}
		}
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		switch (PlanBelongs)
		{
		case EPlanPolygonBelongs::EXY_Plan:Rectangle.StartLocationZ += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Rectangle.EndLocationZ += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		case EPlanPolygonBelongs::EYZ_Plan:Rectangle.StartLocationX += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Rectangle.EndLocationX += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		case EPlanPolygonBelongs::EXZ_Plan:Rectangle.StartLocationY += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); Rectangle.EndLocationY += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		}
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		switch (PlanBelongs)
		{
		case EPlanPolygonBelongs::EXY_Plan:Ellipse.CenterLocationZ += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		case EPlanPolygonBelongs::EYZ_Plan:Ellipse.CenterLocationX += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		case EPlanPolygonBelongs::EXZ_Plan:Ellipse.CenterLocationY += UParameterPropertyData::ConvertToUIValue(InDeltaOffset); break;
		}
	}
}

FCrossSectionGenerator::FCrossSectionGenerator()
{

}

int32 FCrossSectionGenerator::addCrossSectionStump(const FCrossSectionData& inCrossSection)
{
	int32 num = crossSectionStumps.Num();
	FCrossSectionStump stump;
	stump.crossSectons.Add(inCrossSection);
	if (inCrossSection.SectionType == ESectionType::ECustomPlan)
	{
		crossSectionVertex(inCrossSection, stump.vertex);
	}
	else if (inCrossSection.SectionType == ESectionType::ERectangle)
	{
		TArray<FVector> points;
		const FVector tangentX = NSPlanType::GetPlanTangentX(inCrossSection.PlanBelongs);
		FGeometry3DLibrary::GenerateRectanglePlanPoints(inCrossSection.Rectangle.StartLocation(), inCrossSection.Rectangle.EndLocation()
			, inCrossSection.SectionNormal, tangentX, points);
		for (int32 i = 0; i < points.Num(); i++)
		{
			int32 j = (i + 1) % points.Num();
			TArray<FVector> temp;
			temp.Add(points[i]);
			temp.Add(points[j]);
			stump.vertex.Add(i, temp);
		}

	}
	else if (inCrossSection.SectionType == ESectionType::EEllipse)
	{
		TArray<FVector> points;

		const FVector tangentX = NSPlanType::GetPlanTangentX(inCrossSection.PlanBelongs);
		FGeometry3DLibrary::GenerateEllipsePlanPoints(inCrossSection.Ellipse.CenterLocation(), inCrossSection.Ellipse.LongRadius()
			, inCrossSection.Ellipse.ShortRadius(), inCrossSection.SectionNormal, tangentX, points, inCrossSection.Ellipse.InterPointCount());
		stump.vertex.Add(0, points);
	}
	else if (inCrossSection.SectionType == ESectionType::ECube)
	{
		stump.bCube = true;
		stump.growPath.Add(FVector::ZeroVector);
		auto h = inCrossSection.SectionNormal.Dot(inCrossSection.Cube.EndLocation() * 0.1);
		stump.growPath.Add(h * inCrossSection.SectionNormal);
	}
	crossSectionStumps.Add(num, stump);
	return num;
}

int32 FCrossSectionGenerator::addCrossSectionStump(const FCrossSectionStump& inCrossSection)
{
	int32 count = crossSectionStumps.Num();
	crossSectionStumps.Add(count, inCrossSection);
	return count;
}
void FCrossSectionGenerator::setBaseCrossSection(const FCrossSectionData& inCrossSection)
{
	baseCrossSection = inCrossSection;
	baseCrossSection.SectionNormal = NSPlanType::GetPlanNormal(inCrossSection.PlanBelongs);

	addCrossSectionStump(baseCrossSection);
	//FCrossSectionData data0 = baseCrossSection;
	//FCrossSectionStump stump;
	//data0.Postive = true;
	//stump.crossSectons.Add(data0);
	//data0.reversal();
	//data0.Postive = false;
	//stump.crossSectons.Add(data0);
	//crossSectionStumps.Add(0,stump);
}

bool FCrossSectionGenerator::generateMesh(const FSectionOperation& sectionOperation, FPMCSection& outMeshInfo, TArray<TPair<FVector, FVector>>& outFramework, AShowSingleComponentActor* calculateActor, bool bPathUV)
{
	editActor = calculateActor;
	TArray<FCrossSectionData> sectionsAfterTransform;
	FSectionOperation formatOperation;
	sectionOperation.FormatSectionOperation(formatOperation);
	crossSectionOperation(formatOperation);
	process(outMeshInfo, outFramework, bPathUV);
	return true;
}

void FCrossSectionGenerator::crossSectionOperation(const FSectionOperation& sectionOperation)
{
	FSectionOperation formatOperation;
	sectionOperation.FormatSectionOperation(formatOperation);
	int32 stumpIndex = 0;
	if (crossSectionStumps.Num() <= 0)
	{
		return;
	}
	if (sectionOperation.LoftingOperation.Points.Num() > 0)
	{
		TArray<FVector> path;
		//sectionOperation.LoftingOperation.LoftRoutine(path);
		FCrossSectionData loftPath;
		sectionOperation.LoftingOperation.ConvertToCrossSection(loftPath);
		loftPath.SectionNormal = NSPlanType::GetPlanNormal(loftPath.PlanBelongs);
		crossSectionVertexUnLoop(loftPath, path);

		TArray<FVector> orgPoints;
		for (auto iter : loftPath.Points)
		{
			orgPoints.Add(iter.PointLocation());
		}
		if (FPolygon3DLibrary::IsPolygonCCWWinding(orgPoints, loftPath.SectionNormal))
		{
			FArrayOperatorLibrary::ReverseArray(path);
		}
		loftingCrossSection(crossSectionStumps[stumpIndex], path);
	}
	auto nor = baseCrossSection.SectionNormal.GetSafeNormal();
	for (int32 i = 0; i < formatOperation.OperatorOrder.Num(); i++)
	{
		const auto& iter = formatOperation.OperatorOrder[i];
		const int32 next = i + 1;
		if (ESectionOperationType::EDrawSection == iter.OperatorType)
		{
			const FVector DrawOffset = formatOperation.DrawOperations[iter.Index].DrawOffset();
			if (DrawOffset.IsNearlyZero(THRESH_SPLIT_POLY_PRECISELY)) continue;


			stumpIndex = addCrossSectionStump(crossSectionStumps[stumpIndex]);
			crossSectionStumps[stumpIndex].growPath.Empty();
			FVector growDir = FVector::DotProduct(nor, DrawOffset) * nor;

			if (crossSectionStumps[stumpIndex].growPath.Num() == 0)
			{
				auto StartPlane = FVector::ZeroVector;
				auto BasePoint = FVector::ZeroVector;
				if (baseCrossSection.SectionType == ESectionType::ECustomPlan)
				{
					
					if (getGrowPath().Num() > 1)
					{
						BasePoint = getPathTop();
					}
					else
					{
						BasePoint = baseCrossSection.Points[0].PointLocation();
					}
				}
				else if (baseCrossSection.SectionType == ESectionType::EEllipse)
				{
					if (getGrowPath().Num() > 1)
					{
						BasePoint += getPathTop();
					}
					else
					{
						BasePoint = baseCrossSection.Ellipse.CenterLocation();
					}
				}
				else if (baseCrossSection.SectionType == ESectionType::ERectangle)
				{
					if (getGrowPath().Num() > 1)
					{
						BasePoint += getPathTop();
					}
					else
					{
						BasePoint = baseCrossSection.Rectangle.StartLocation();
					}
				}

				if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXY_Plan)
				{

					StartPlane += BasePoint * FVector::ZAxisVector;
				}
				else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXZ_Plan)
				{
					StartPlane += BasePoint * FVector::YAxisVector;
				}
				else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EYZ_Plan)
				{
					StartPlane += BasePoint * FVector::XAxisVector;
				}
				crossSectionStumps[stumpIndex].growPath.Add(StartPlane);
				crossSectionStumps[stumpIndex].growPath.Add(getPathTop() + growDir);
			}
			else
			{
				auto newPath = crossSectionStumps[stumpIndex].growPath.Last(0) + DrawOffset;
				crossSectionStumps[stumpIndex].growPath.Add(newPath);
			}
			auto tempStump = crossSectionStumps[stumpIndex];
			if (tempStump.scalePolygonMap.Num() > 0)
			{
				crossSectionStumps[stumpIndex].vertex = tempStump.scalePolygonMap;
			}
			crossSectionStumps[stumpIndex].scalePolygon = crossSectionStumps[stumpIndex].getVertex();
			crossSectionStumps[stumpIndex].scalePolygonMap = crossSectionStumps[stumpIndex].vertex;
			auto shift = DrawOffset - growDir;

			for (auto& iter2 : crossSectionStumps[stumpIndex].scalePolygonMap)
			{
				for (auto& v : iter2.Value)
				{
					v += shift;
				}
			}
			for (auto& iter3 : crossSectionStumps[stumpIndex].scalePolygon)
			{
				iter3 += shift;
			}
			crossSectionStumps[stumpIndex].scaleABMap.Empty();
			for (auto v : crossSectionStumps[stumpIndex].getVertex())
			{
				crossSectionStumps[stumpIndex].scaleABMap.Add(v, v);

			}
			//moveCrossSection(crossSectionStumps[stumpIndex].crossSectons[0],DrawOffset);
			crossSectionStumps[stumpIndex].bShowBody = true;
			crossSectionStumps[stumpIndex].bOutline = false;

		}
		else if (ESectionOperationType::EShiftSection == iter.OperatorType)
		{
			TArray<float> shift;
			if (formatOperation.ShiftOperations.Num() < iter.Index)
			{
				continue;
			}
			bool bValidShift = formatOperation.ShiftOperations[iter.Index].ShiftValues(shift);
			if (false == bValidShift)
				continue;
			if (stumpIndex > crossSectionStumps.Num() - 1 || !crossSectionStumps.Contains(stumpIndex))
			{
				continue;
			}

			auto newStump = crossSectionStumps[stumpIndex];
			stumpIndex = addCrossSectionStump(newStump);
			crossSectionStumps[stumpIndex].bShowBody = false;
			crossSectionStumps[stumpIndex].bOutline = true;
			crossSectionStumps[stumpIndex].growPath.Empty();
			crossSectionStumps[stumpIndex].growPath.Add(getPathTop());
			bool scale = crossSectionStumps[stumpIndex].scalePolygon.Num() > 0;

			if (scale)
			{
				crossSectionStumps[stumpIndex].vertex = crossSectionStumps[stumpIndex].scalePolygonMap;
			}
			crossSectionStumps[stumpIndex].scalePolygon.Empty();
			crossSectionStumps[stumpIndex].scalePolygonMap.Empty();
			// ReSharper disable once CppMsExtBindingRValueToLvalueReference
			TMap<FVector, FVector> abMap;
			shiftCrossSection(crossSectionStumps[stumpIndex], shift, abMap);
			TMap<int32, TArray<FVector>> newVec;
			for (const auto& l : crossSectionStumps[stumpIndex].vertex)
			{
				TArray<FVector> newV;
				for (const auto& p : l.Value)
				{
					newV.AddUnique(p);
				}
				newVec.Add(l.Key, newV);
			}
			crossSectionStumps[stumpIndex].vertex = newVec;
		}
		else if (ESectionOperationType::ECutoutSection == iter.OperatorType)
		{
			float CutoutValue = sectionOperation.CutoutOperations[iter.Index].CutoutValue();
			if (!sectionOperation.CutoutOperations[iter.Index].IsEmpty())
			{
				FCrossSectionData CutOutSection;
				bool Res = sectionOperation.CutoutOperations[iter.Index].ConvertToCrossSection(CutOutSection);
				if (!Res)
					return;

				CutOutSection.SectionNormal = baseCrossSection.SectionNormal;
				//TArray<double> ShiftValues;
				//ShiftValues.SetNumZeroed(CutOutSection.Points);
				//for (auto &  SV : ShiftValues)
				//{
				//	SV = 0.1;
				//}
				//TArray<FVector> outKeyPointMap;
				//TMap<FVector, FVector> outAllPointMap;

				//FGeometryLibrary::scalePolygonMesh(CutOutSection.Points, ShiftValues, CutOutSection.SectionNormal, outKeyPointMap
				//	, outAllPointMap, CutOutSection.SectionType == ESectionType::ECustomPlan);

				stumpIndex = addCrossSectionStump(CutOutSection); 
				//TMap<FVector, FVector> abMap;
				//shiftCrossSection(crossSectionStumps[stumpIndex], TArray<float>{0.1f}, abMap);

			}
			else
			{
				auto newCross = crossSectionStumps[stumpIndex];
				TArray<float> shift;
				//shift.Add(0.1f);
				//shiftCrossSection(newCross, shift);
				newCross.growPath.Empty();
				if (newCross.scalePolygon.Num() > 0)
				{
					newCross.vertex = newCross.scalePolygonMap;
					newCross.scalePolygon.Empty();
					newCross.scalePolygonMap.Empty();
				}
				stumpIndex = addCrossSectionStump(newCross);
				TMap<FVector, FVector> abMap;
				shiftCrossSection(crossSectionStumps[stumpIndex], TArray<float>{0.1f}, abMap);

			}
			crossSectionStumps[stumpIndex].bShowBody = false;
			cutOutCrossSection(crossSectionStumps[stumpIndex], CutoutValue);
			crossSectionStumps[stumpIndex].scaleABMap.Empty();
			boolStumps.Add(stumpIndex - 1, crossSectionStumps[stumpIndex]);
		}
		else if (ESectionOperationType::EZoomSection == iter.OperatorType)
		{
			TArray<float> ZoomValue;
			bool bValidZoom = formatOperation.ZoomOperations[iter.Index].ScaleValues(ZoomValue);
			if (false == bValidZoom)
				continue;
			auto tempStump = crossSectionStumps[stumpIndex];
			TMap<FVector, FVector> abMap;
			shiftCrossSection(tempStump, ZoomValue, abMap);
			if (abMap.Num() > 0)
			{
				crossSectionStumps[stumpIndex].scaleABMap = abMap;
			}
			crossSectionStumps[stumpIndex].scalePolygon = tempStump.getVertex();
			crossSectionStumps[stumpIndex].scalePolygonMap = tempStump.vertex;
			if (boolStumps.Contains(stumpIndex - 1))
			{
				if (!boolStumps[stumpIndex - 1].scalePolygon.IsEmpty())
				{
					if (abMap.Num() > 0)
					{
						boolStumps[stumpIndex - 1].scaleABMap = abMap;
					}
					boolStumps[stumpIndex - 1].scalePolygon = tempStump.getVertex();
					boolStumps[stumpIndex - 1].scalePolygonMap = tempStump.vertex;
				}
			}
			//auto points = crossSectionStumps[stumpIndex].getVertex();
			//FGeometryLibrary::uniformVertexOfTwoPlan(points, baseCrossSection.SectionNormal,crossSectionStumps[stumpIndex].scalePolygon, baseCrossSection.SectionNormal);
		}
	}
}

bool FCrossSectionGenerator::moveCrossSection(FCrossSectionData& inCrossSection, const FVector& inOffset)
{
	bool Res = false;


	if (ESectionType::ECustomPlan == inCrossSection.SectionType)
	{

		if (inCrossSection.vertexCache.Num() > 0)
		{
			for (auto& iter : inCrossSection.vertexCache)
			{
				iter += inOffset;
			}
		}

		int32 Index = 0;
		for (auto& Iter : inCrossSection.Points)
		{
			if (EPositionType::EAbsolute == inCrossSection.Points[Index].PositionType)
			{
				inCrossSection.Points[Index].LocationX += UParameterPropertyData::ConvertToUIValue(inOffset.X);
				inCrossSection.Points[Index].LocationY += UParameterPropertyData::ConvertToUIValue(inOffset.Y);
				inCrossSection.Points[Index].LocationZ += UParameterPropertyData::ConvertToUIValue(inOffset.Z);
			}
			inCrossSection.Points[Index].PrePointLocation += inOffset;
			if (!inCrossSection.Lines.IsValidIndex(Index))
				return false;
			inCrossSection.Lines[Index].StartLocation += inOffset;
			inCrossSection.Lines[Index].EndLocation += inOffset;
			++Index;
		}
		Res = true;
	}
	else if (ESectionType::ERectangle == inCrossSection.SectionType)
	{
		inCrossSection.Rectangle.StartLocationX += UParameterPropertyData::ConvertToUIValue(inOffset.X);
		inCrossSection.Rectangle.StartLocationY += UParameterPropertyData::ConvertToUIValue(inOffset.Y);
		inCrossSection.Rectangle.StartLocationZ += UParameterPropertyData::ConvertToUIValue(inOffset.Z);
		inCrossSection.Rectangle.EndLocationX += UParameterPropertyData::ConvertToUIValue(inOffset.X);
		inCrossSection.Rectangle.EndLocationY += UParameterPropertyData::ConvertToUIValue(inOffset.Y);
		inCrossSection.Rectangle.EndLocationZ += UParameterPropertyData::ConvertToUIValue(inOffset.Z);
		Res = true;
	}
	else if (ESectionType::EEllipse == inCrossSection.SectionType)
	{
		if (inCrossSection.vertexCache.Num() > 0)
		{
			for (auto& iter : inCrossSection.vertexCache)
			{
				iter += inOffset;
			}
		}

		inCrossSection.Ellipse.CenterLocationX += UParameterPropertyData::ConvertToUIValue(inOffset.X);
		inCrossSection.Ellipse.CenterLocationY += UParameterPropertyData::ConvertToUIValue(inOffset.Y);
		inCrossSection.Ellipse.CenterLocationZ += UParameterPropertyData::ConvertToUIValue(inOffset.Z);
		Res = true;
	}
	return Res;
}


bool FCrossSectionGenerator::shiftCrossSection(FCrossSectionStump& operateCossSection, const TArray<float>& inShiftValue, TMap<FVector, FVector>& InOutOldPToNewP) const
{

	TArray<float> FormatShiftValue;
	FSectionShiftingOperation::FormatShiftValue(inShiftValue, FormatShiftValue, operateCossSection.vertex.Num());


	TArray<FVector> outKeyPointMap;
	TMap<FVector, FVector> outAllPointMap;

	TArray<double> shiftValues = static_cast<TArray<double>>(FormatShiftValue);

	if (!operateCossSection.crossSectons.IsEmpty())
	{
		FGeometryLibrary::scalePolygonMesh(operateCossSection.vertex, shiftValues, baseCrossSection.SectionNormal, outKeyPointMap, outAllPointMap, operateCossSection.crossSectons[0].SectionType == ESectionType::ECustomPlan);
	}
	else
	{
		FGeometryLibrary::scalePolygonMesh(operateCossSection.vertex, shiftValues, baseCrossSection.SectionNormal, outKeyPointMap, outAllPointMap, baseCrossSection.SectionType == ESectionType::ECustomPlan);
	}
	auto OldPoint = operateCossSection.vertex;
	// key -> oldPoint, chart index, tri index
	TMap<FVector, FVector > OldPToNewP;
	// TMap<FVector, FVector> OldPToNewP;
	// Note that new points map to old point, the number of old point will decrease;
	//TMap<FVector, FVector> NewPToOldP;
	for (auto& iter : operateCossSection.vertex)
	{
		TArray<FVector> temp;
		for (int j = 0; j < iter.Value.Num(); j++)
		{
			auto& v = iter.Value[j];
			if (outAllPointMap.Contains(v))
			{
				temp.Add(outAllPointMap[v]);
				/*if(!NewPToOldP.Contains(outAllPointMap[v]))
				{
					NewPToOldP.Add(outAllPointMap[v], v);
				}*/
				//temp.Last() += getPathTop();
			}
		}
		iter.Value.Empty();
		iter.Value = temp;

	}
	//moveCrossSection(operateCossSection.crossSectons[1], operateCossSection.crossSectons[1].SectionNormal * 1.f);
	bool bCheck = true;


	if (operateCossSection.crossSectons.IsEmpty() || operateCossSection.crossSectons[0].SectionType != ESectionType::ECustomPlan)
	{

	}
	else if (baseCrossSection.SectionType == ESectionType::ECustomPlan)
	{
		// -----------------------------
		const int32 VerNum = operateCossSection.vertex.Num();
		//  Key->相交线段的Index Key[0]:交点所属线段1 Key[1]：交点所属线段2 Key[2]:曲线的总Num Key[3]：交点在曲线的哪个Index中
		//  Value->相交点坐标
		TArray<TPair<TArray<int32>, FVector >> Result;
		TMap<int32, TPair<TArray<int32>, FVector>> NeedRemove;



		for (TTuple<int, TArray<UE::Math::TVector<double>>>& Pair : operateCossSection.vertex)
		{
			const int32 Next = (Pair.Key + 1) % VerNum;
			const int32 Prev = (Pair.Key - 1 + VerNum) % VerNum;

			// 如果是曲线
			if (Pair.Value.Num() > 2)
			{
				auto& CurvePoint = Pair.Value;
				// 如果只有两条线，有一天是曲线，单独判断
				if (Next == Prev)
				{
					if (operateCossSection.vertex[Next].Num() == 2)
					{
						bool bIsFirstInter = true;
						for (int i = 1; i < CurvePoint.Num() - 2; i++)
						{
							FVector CurveStart = CurvePoint[i];
							FVector CurveEnd = CurvePoint[i + 1];
							// 判断这个直线与曲线的小直边是否相交
							FVector IntersectionPoint;
							bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart, CurveEnd,
								operateCossSection.vertex[Next][0], operateCossSection.vertex[Next][1], IntersectionPoint);
							if (bIsIntersect)
							{
								TPair<TArray<int32>, FVector> SideIndexToInterPoint;
								SideIndexToInterPoint.Key = TArray<int>{ Pair.Key, Next, Pair.Value.Num(), i };
								SideIndexToInterPoint.Value = IntersectionPoint;
								Result.Add(SideIndexToInterPoint);
								UE_LOG(LogTemp, Warning, TEXT("Find the Interaction betwen the <<Curve Line and it Next>> {%d}<-->{%d}, the IntersectionPoint is X:{%f}, Y:{%f}, Z:{%f}, In the Curve index:{%d}"),
									Pair.Key, Next, IntersectionPoint.X, IntersectionPoint.Y, IntersectionPoint.Z, i);
							}
						}
					}
					else
					{
						// 两个曲线相交
						auto& AnotherCurvePoint = operateCossSection.vertex[Next];
						TArray<TPair<TPair<int, int>, FVector>> CurRes;
						for (int i = 1; i < CurvePoint.Num() - 2; i++)
						{
							const auto& CurveStart1 = CurvePoint[i];
							const auto& CurveEnd1 = CurvePoint[i + 1];

							for (int j = 1; j < AnotherCurvePoint.Num() - 2; j++)
							{
								const auto& CurveStart2 = AnotherCurvePoint[j];
								const auto& CurveEnd2 = AnotherCurvePoint[j + 1];
								FVector IntersectionPoint;
								bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart1, CurveEnd1,
									CurveStart2, CurveEnd2, IntersectionPoint);
								if (bIsIntersect)
								{
									CurRes.Add(TPair<TPair<int, int>, FVector>{ TPair<int, int>{i, j}, IntersectionPoint});
								}
							}
							if (CurRes.Num() == 2) break;
						}
						auto& Int1 = CurRes[0];
						auto& Int2 = CurRes[1];
						if (Int1.Key.Key > Int2.Key.Key)
						{
							for (int i = 0; i <= Int2.Key.Key; i++)
							{
								CurvePoint[i] = Int2.Value;
							}
							for (int i = Int1.Key.Key; i < CurvePoint.Num(); i++)
							{
								CurvePoint[i] = Int1.Value;
							}
						}
						else
						{
							for (int i = 0; i <= Int1.Key.Key; i++)
							{
								CurvePoint[i] = Int1.Value;
							}
							for (int i = Int2.Key.Key; i < CurvePoint.Num(); i++)
							{
								CurvePoint[i] = Int2.Value;
							}
						}

						if (Int1.Key.Value > Int2.Key.Value)
						{
							for (int i = 0; i <= Int2.Key.Value; i++)
							{
								AnotherCurvePoint[i] = Int2.Value;
							}
							for (int i = Int1.Key.Value; i < AnotherCurvePoint.Num(); i++)
							{
								AnotherCurvePoint[i] = Int1.Value;
							}
						}
						else
						{
							for (int i = 0; i <= Int1.Key.Value; i++)
							{
								AnotherCurvePoint[i] = Int1.Value;
							}
							for (int i = Int2.Key.Value; i < AnotherCurvePoint.Num(); i++)
							{
								AnotherCurvePoint[i] = Int2.Value;
							}
						}
					}
					break;
				}

				// todo 不需要遍历所有的边，只要判断前后边是否有交点，需要修改
				for (TTuple<int, TArray<UE::Math::TVector<double>>>& Vertex : operateCossSection.vertex)
				{
					// 自己或者自己前一个边或者后一个边，继续
					/*if (Vertex.Key == Pair.Key || Vertex.Key == Next || Vertex.Key == Prev)
					{
						continue;
					}*/
					if (Vertex.Key == Pair.Key)
					{
						continue;
					}
					if (Vertex.Key == Next)
					{
						// 判断曲线的下一个线是直线还是曲线，直线，判断是不是会有除了连接点外其余交点
						if (Vertex.Value.Num() == 2)
						{
							// 计算除第一个交点外是否有其他交点
							for (int32 i = 0; i < CurvePoint.Num() - 2; i++)
							{
								FVector CurveStart = CurvePoint[i];
								FVector CurveEnd = CurvePoint[i + 1];
								// 判断这个直线与曲线的小直边是否相交
								FVector IntersectionPoint;
								bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart, CurveEnd, Vertex.Value[0], Vertex.Value[1], IntersectionPoint);

								if (bIsIntersect)
								{
									TPair<TArray<int32>, FVector> SideIndexToInterPoint;
									SideIndexToInterPoint.Key = TArray<int>{ Pair.Key, Vertex.Key, Pair.Value.Num(), i };
									SideIndexToInterPoint.Value = IntersectionPoint;
									Result.Add(SideIndexToInterPoint);
									UE_LOG(LogTemp, Warning, TEXT("Find the Interaction betwen the <<Curve Line and it Next>> {%d}<-->{%d}, the IntersectionPoint is X:{%f}, Y:{%f}, Z:{%f}, In the Curve index:{%d}"),
										Pair.Key, Vertex.Key, IntersectionPoint.X, IntersectionPoint.Y, IntersectionPoint.Z, i);
									// 一般与曲线就一条交线
									break;
								}
							}
						}
						else
						{ // 下一个是曲线
							auto& AnotherCurvePoint = operateCossSection.vertex[Next];
							//TArray<TPair<TPair<int, int>, FVector>> CurRes;
							TPair<TPair<int, int>, FVector> CurRes = TPair<TPair<int, int>, FVector>{ TPair<int, int>{ CurvePoint.Num() - 1, 0}, CurvePoint.Last() };;
							bool bIsFind = false;
							for (int i = 1; i < CurvePoint.Num() - 1; i++)
							{
								const auto& CurveStart1 = CurvePoint[i];
								const auto& CurveEnd1 = CurvePoint[i + 1];

								for (int j = 1; j < AnotherCurvePoint.Num() - 1; j++)
								{
									const auto& CurveStart2 = AnotherCurvePoint[j];
									const auto& CurveEnd2 = AnotherCurvePoint[j + 1];
									FVector IntersectionPoint;
									//bool bIsIntersect = FMath::SegmentIntersection2D(CurveStart1, CurveEnd1, CurveStart2, CurveEnd2, IntersectionPoint);
									bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart1, CurveEnd1,
										CurveStart2, CurveEnd2, IntersectionPoint);
									if (bIsIntersect)
									{
										CurRes = TPair<TPair<int, int>, FVector>{ TPair<int, int>{i, j}, IntersectionPoint };
										bIsFind = true;
										break;
									}
								}
								if (bIsFind) break;
							}
							for (int i = CurRes.Key.Key + 1; i < CurvePoint.Num(); i++)
							{
								CurvePoint[i] = CurRes.Value;
							}
							for (int i = 0; i <= CurRes.Key.Value; i++)
							{
								AnotherCurvePoint[i] = CurRes.Value;
							}
						}
						continue;
					}
					if (Vertex.Key == Prev)
					{
						if (Vertex.Value.Num() == 2)
						{
							// 计算除第一个交点外是否有其他交点
							for (int32 i = 1; i < CurvePoint.Num() - 1; i++)
							{
								FVector CurveStart = CurvePoint[i];
								FVector CurveEnd = CurvePoint[i + 1];
								// 判断这个直线与曲线的小直边是否相交
								FVector IntersectionPoint;
								bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart, CurveEnd, Vertex.Value[0], Vertex.Value[1], IntersectionPoint);

								if (bIsIntersect)
								{
									TPair<TArray<int32>, FVector> SideIndexToInterPoint;
									SideIndexToInterPoint.Key = TArray<int>{ Pair.Key, Vertex.Key, Pair.Value.Num(), i };
									SideIndexToInterPoint.Value = IntersectionPoint;
									Result.Add(SideIndexToInterPoint);
									UE_LOG(LogTemp, Warning, TEXT("Find the Interaction betwen the <<Curve Line and it Next>> {%d}<-->{%d}, the IntersectionPoint is X:{%f}, Y:{%f}, Z:{%f}, In the Curve index:{%d}"),
										Pair.Key, Vertex.Key, IntersectionPoint.X, IntersectionPoint.Y, IntersectionPoint.Z, i);
									// 一般与曲线就一条交线
									break;
								}
							}
						}
						else
						{ // 下一个是曲线
							auto& AnotherCurvePoint = operateCossSection.vertex[Prev];
							//TArray<TPair<TPair<int, int>, FVector>> CurRes;
							TPair<TPair<int, int>, FVector> CurRes = TPair<TPair<int, int>, FVector>{ TPair<int, int>{ 0, AnotherCurvePoint.Num() - 1}, CurvePoint[0] };
							bool bIsFind = false;
							for (int i = 1; i < CurvePoint.Num() - 1; i++)
							{
								const auto& CurveStart1 = CurvePoint[i];
								const auto& CurveEnd1 = CurvePoint[i + 1];

								for (int j = 1; j < AnotherCurvePoint.Num() - 1; j++)
								{
									const auto& CurveStart2 = AnotherCurvePoint[j];
									const auto& CurveEnd2 = AnotherCurvePoint[j];
									FVector IntersectionPoint;
									//bool bIsIntersect = FMath::SegmentIntersection2D(CurveStart1, CurveEnd1, CurveStart2, CurveEnd2, IntersectionPoint);
									bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart1, CurveEnd1,
										CurveStart2, CurveEnd2, IntersectionPoint);
									if (bIsIntersect)
									{
										CurRes = TPair<TPair<int, int>, FVector>{ TPair<int, int>{i, j}, IntersectionPoint };
										bIsFind = true;
										break;
									}
								}
								if (bIsFind) break;
							}
							for (int i = 0; i <= CurRes.Key.Key; i++)
							{
								CurvePoint[i] = CurRes.Value;
							}
							for (int i = CurRes.Key.Value + 1; i < AnotherCurvePoint.Num(); i++)
							{
								AnotherCurvePoint[i] = CurRes.Value;
							}
						}
						continue;
					}

					// 如果是其他边,且这个边是直线
					if (Vertex.Value.Num() == 2)
					{
						for (int32 i = 0; i < CurvePoint.Num() - 1; i++)
						{
							FVector CurveStart = CurvePoint[i];
							FVector CurveEnd = CurvePoint[i + 1];
							// 判断这个直线与曲线的小直边是否相交
							FVector IntersectionPoint;
							bool bIsIntersect = FGeometryLibrary::SegmentIntersection3D(CurveStart, CurveEnd, Vertex.Value[0], Vertex.Value[1], IntersectionPoint);

							/*UE_LOG(LogTemp, Warning, TEXT("The CurveStart at X:{%f}, Y:{%f}, Z:{%f}, and CurveEnd at X:{%f}, Y:{%f}, Z:{%f}, the Target Segmemt at X:{%f}, Y:{%f}, Z:{%f}, [{%d}]"),
								CurveStart.X, CurveStart.Y, CurveStart.Z, CurveEnd.X, CurveEnd.Y, CurveEnd.Z, Vertex.Value[0].X, Vertex.Value[0].Y, Vertex.Value[0].Z, bIsIntersect);*/

							if (bIsIntersect)
							{
								TPair<TArray<int32>, FVector> SideIndexToInterPoint;
								SideIndexToInterPoint.Key = TArray<int>{ Pair.Key, Vertex.Key, Pair.Value.Num(), i };
								SideIndexToInterPoint.Value = IntersectionPoint;
								Result.Add(SideIndexToInterPoint);
								UE_LOG(LogTemp, Warning, TEXT("Find the Interaction betwen the Line {%d}<-->{%d}, the IntersectionPoint is X:{%f}, Y:{%f}, Z:{%f}, In the Curve index:{%d}"),
									Pair.Key, Vertex.Key, IntersectionPoint.X, IntersectionPoint.Y, IntersectionPoint.Z, i);
								// 一般与曲线就一条交线
								break;
							}
						}
					}
					else
					{
						// 如果曲边与曲边？ 不可能？
					}
				}
			}
		}

		TArray<bool> WhetherMoveContains;
		WhetherMoveContains.Init(false, VerNum);

		if (VerNum == 2 && !Result.IsEmpty())
		{
			auto Item1 = Result[0];
			auto Item2 = Result[1];
			auto Value1 = operateCossSection.vertex.Find(0);
			auto Value2 = operateCossSection.vertex.Find(1);
			if (Value1->Num() > 2 && Value2->Num() == 2)
			{
				if (Item1.Key[3] > Item2.Key[3])
				{
					for (int i = Item1.Key[3]; i < Value1->Num(); i++)
					{
						/*FVector oldPoint1 = OldPoint[0][i];
						OldPToNewP[oldPoint1] = Item1.Value;*/
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[i]);
						outAllPointMap[oldPoint1] = Item1.Value;
						NewPToOldP.Remove((*Value1)[i]);
						NewPToOldP.Add(Item1.Value, oldPoint1);*/
						(*Value1)[i] = Item1.Value;
					}
					/*FVector  oldPoint2 = OldPoint[0][0];
					OldPToNewP[oldPoint2] = Item1.Value;*/
					/*FVector& oldPoint2 = *NewPToOldP.Find((*Value2)[0]);
					outAllPointMap[oldPoint2] = Item1.Value;
					NewPToOldP.Remove((*Value2)[0]);
					NewPToOldP.Add(Item1.Value, oldPoint2);*/
					(*Value2)[0] = Item1.Value;

					for (int i = 0; i <= Item2.Key[3]; i++)
					{
						/*FVector oldPoint3 = OldPoint[1][i];
						OldPToNewP[oldPoint3] = Item2.Value;*/
						/*FVector& oldPoint3 = *NewPToOldP.Find((*Value1)[i]);
						outAllPointMap[oldPoint3] = Item2.Value;
						NewPToOldP.Remove((*Value1)[i]);
						NewPToOldP.Add(Item2.Value, oldPoint3);*/
						(*Value1)[i] = Item2.Value;
					}
					/*FVector oldPoint4 = OldPoint[1][1];
					OldPToNewP[oldPoint4] = Item2.Value;*/
					/*FVector& oldPoint4 = *NewPToOldP.Find((*Value2)[1]);
					outAllPointMap[oldPoint4] = Item2.Value;
					NewPToOldP.Remove((*Value2)[1]);
					NewPToOldP.Add(Item2.Value, oldPoint4);*/
					(*Value2)[1] = Item2.Value;
				}
				else
				{
					for (int i = Item2.Key[3]; i < Value1->Num(); i++)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[i]);
						outAllPointMap[oldPoint1] = Item2.Value;
						NewPToOldP.Remove((*Value1)[i]);
						NewPToOldP.Add(Item2.Value, oldPoint1);*/
						(*Value1)[i] = Item2.Value;
					}
					/*FVector& oldPoint2 = *NewPToOldP.Find((*Value2)[0]);
					outAllPointMap[oldPoint2] = Item2.Value;
					NewPToOldP.Remove((*Value2)[0]);
					NewPToOldP.Add(Item2.Value, oldPoint2);*/
					(*Value2)[0] = Item2.Value;

					for (int i = 0; i <= Item1.Key[3]; i++)
					{
						/*FVector& oldPoint3 = *NewPToOldP.Find((*Value1)[i]);
						outAllPointMap[oldPoint3] = Item1.Value;
						NewPToOldP.Remove((*Value1)[i]);
						NewPToOldP.Add(Item1.Value, oldPoint3);*/
						(*Value1)[i] = Item1.Value;
					}
					/*FVector& oldPoint4 = *NewPToOldP.Find((*Value2)[1]);
					outAllPointMap[oldPoint4] = Item1.Value;
					NewPToOldP.Remove((*Value2)[1]);
					NewPToOldP.Add(Item1.Value, oldPoint4);*/
					(*Value2)[1] = Item1.Value;
				}
			}
			if (Value1->Num() == 2 && Value2->Num() > 2)
			{
				if (Item1.Key[3] > Item2.Key[3])
				{
					for (int i = Item1.Key[3]; i < Value2->Num(); i++)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[i]);
						outAllPointMap[oldPoint1] = Item1.Value;
						NewPToOldP.Remove((*Value2)[i]);
						NewPToOldP.Add(Item1.Value, oldPoint1);*/
						(*Value2)[i] = Item1.Value;
					}
					/*FVector& oldPoint2 = *NewPToOldP.Find((*Value1)[0]);
					outAllPointMap[oldPoint2] = Item1.Value;
					NewPToOldP.Remove((*Value1)[0]);
					NewPToOldP.Add(Item1.Value, oldPoint2);*/
					(*Value1)[0] = Item1.Value;

					for (int i = 0; i < Item2.Key[3]; i++)
					{
						/*FVector& oldPoint3 = *NewPToOldP.Find((*Value2)[i]);
						outAllPointMap[oldPoint3] = Item2.Value;
						NewPToOldP.Remove((*Value2)[i]);
						NewPToOldP.Add(Item2.Value, oldPoint3);*/
						(*Value2)[i] = Item2.Value;
					}
					/*FVector& oldPoint4 = *NewPToOldP.Find((*Value1)[1]);
					outAllPointMap[oldPoint4] = Item2.Value;
					NewPToOldP.Remove((*Value1)[1]);
					NewPToOldP.Add(Item2.Value, oldPoint4);*/
					(*Value1)[1] = Item2.Value;
				}
				else
				{
					for (int i = Item2.Key[3]; i < Value2->Num(); i++)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[i]);
						outAllPointMap[oldPoint1] = Item2.Value;
						NewPToOldP.Remove((*Value2)[i]);
						NewPToOldP.Add(Item2.Value, oldPoint1);*/
						(*Value2)[i] = Item2.Value;
					}
					/*FVector& oldPoint2 = *NewPToOldP.Find((*Value1)[0]);
					outAllPointMap[oldPoint2] = Item2.Value;
					NewPToOldP.Remove((*Value1)[0]);
					NewPToOldP.Add(Item2.Value, oldPoint2);*/
					(*Value1)[0] = Item2.Value;

					for (int i = 0; i < Item1.Key[3]; i++)
					{
						/*FVector& oldPoint3 = *NewPToOldP.Find((*Value2)[i]);
						outAllPointMap[oldPoint3] = Item1.Value;
						NewPToOldP.Remove((*Value2)[i]);
						NewPToOldP.Add(Item1.Value, oldPoint3);*/
						(*Value2)[i] = Item1.Value;
					}
					/*FVector& oldPoint4 = *NewPToOldP.Find((*Value1)[1]);
					outAllPointMap[oldPoint4] = Item1.Value;
					NewPToOldP.Remove((*Value1)[1]);
					NewPToOldP.Add(Item1.Value, oldPoint4);*/
					(*Value1)[1] = Item1.Value;
				}
			}
			if (Value1->Num() > 2 && Value2->Num() > 2)
			{

			}
			return operateCossSection.vertex.Num() > 0;
		}

		// 找到相交点,设置直线的端点
		for (TPair<TArray<int32>, FVector>& Tuple : Result)
		{
			int32 Index1 = Tuple.Key[0];
			const int32 Index1_Next = (Index1 + 1) % VerNum;
			const int32 Index1_Prev = (Index1 - 1 + VerNum) % VerNum;
			int32 Index2 = Tuple.Key[1];
			const int32 Index2_Next = (Index2 + 1) % VerNum;
			const int32 Index2_Prev = (Index2 - 1 + VerNum) % VerNum;
			if (!WhetherMoveContains[Index1] && !WhetherMoveContains[Index2]) {
				if (Index1_Prev == Index2 || Index1 > Index2)
				{
					// Index1改变起点
					auto Value1 = operateCossSection.vertex.Find(Index1);
					// 判断是不是曲线
					if (Value1->Num() == 2)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[0]);
						outAllPointMap[oldPoint1] = Tuple.Value;
						NewPToOldP.Remove((*Value1)[1]);
						NewPToOldP.Add(Tuple.Value, oldPoint1);*/
						(*Value1)[0] = Tuple.Value;
					}
					else
					{
						for (int i = 0; i <= Tuple.Key[3]; i++)
						{
							/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[i]);
							outAllPointMap[oldPoint1] = Tuple.Value;
							NewPToOldP.Remove((*Value1)[i]);
							NewPToOldP.Add(Tuple.Value, oldPoint1);*/
							(*Value1)[i] = Tuple.Value;
						}
						/*(*Value1).RemoveAt(0, Tuple.Key[3]);
						(*Value1)[0] = Tuple.Value;*/
					}

					// Index2改变终点
					auto Value2 = operateCossSection.vertex.Find(Index2);
					if (Value2->Num() == 2)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[1]);
						outAllPointMap[oldPoint1] = Tuple.Value;
						NewPToOldP.Remove((*Value2)[1]);
						NewPToOldP.Add(Tuple.Value, oldPoint1);*/
						(*Value2)[1] = Tuple.Value;
					}
					else
					{
						// 如果输入值过大会倒转，Index越界
						//todo 修改非删除
						if (Tuple.Key[3] - (Tuple.Key[2] - Value2->Num()) >= 0)
						{
							for (int i = Tuple.Key[3] + 1; i < Value2->Num(); i++)
							{
								/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[i]);
								outAllPointMap[oldPoint1] = Tuple.Value;
								NewPToOldP.Remove((*Value2)[i]);
								NewPToOldP.Add(Tuple.Value, oldPoint1);*/
								(*Value2)[i] = Tuple.Value;
							}
							/*	Value2.RemoveAt(Tuple.Key[3] - (Tuple.Key[2] - Value2.Num()), Tuple.Key[2] - Tuple.Key[3]);
								Value2.Push(Tuple.Value);*/
						}
						else
						{
							bCheck = false;
							break;
						}
					}

					// 改变后判断之前被连接的点是否孤立了 Index1的前一个终点和Index1起点比， Index2的后一个的起点和Index2的终点比
					const int32 Index1_Prev_1 = (Index1 - 1 + VerNum) % VerNum;
					const int32 Index2_Next_1 = (Index2 + 1) % VerNum;
					if (operateCossSection.vertex[Index1_Prev_1].Last() != operateCossSection.vertex[Index1][0]
						&& operateCossSection.vertex[Index2_Next_1][0] != operateCossSection.vertex[Index2].Last())
					{
						for (int i = Index2_Next_1; i <= Index1_Prev_1; i++)
						{
							NeedRemove.Add({ i, Tuple });
							WhetherMoveContains[i] = true;
						}
					}
				}
				else
				{
					// Index1改变终点
					auto Value1 = operateCossSection.vertex.Find(Index1);
					// 判断是不是曲线
					if (Value1->Num() == 2)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[1]);
						outAllPointMap[oldPoint1] = Tuple.Value;
						NewPToOldP.Remove((*Value1)[1]);
						NewPToOldP.Add(Tuple.Value, oldPoint1);*/
						(*Value1)[1] = Tuple.Value;
					}
					else
					{
						if (Tuple.Key[3] - (Tuple.Key[2] - Value1->Num()) >= 0)
						{
							for (int i = Tuple.Key[3] + 1; i < Value1->Num(); i++)
							{
								/*FVector& oldPoint1 = *NewPToOldP.Find((*Value1)[i]);
								outAllPointMap[oldPoint1] = Tuple.Value;
								NewPToOldP.Remove((*Value1)[i]);
								NewPToOldP.Add(Tuple.Value, oldPoint1);*/
								(*Value1)[i] = Tuple.Value;
							}
							/*Value1.RemoveAt(Tuple.Key[3] - (Tuple.Key[2] - Value1.Num()), Tuple.Key[2] - Tuple.Key[3]);
							Value1.Push(Tuple.Value);*/
						}
						else
						{
							bCheck = false;
							break;
						}
					}

					// Index2改变起点
					auto Value2 = operateCossSection.vertex.Find(Index2);
					if (Value2->Num() == 2)
					{
						/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[0]);
						outAllPointMap[oldPoint1] = Tuple.Value;
						NewPToOldP.Remove((*Value2)[0]);
						NewPToOldP.Add(Tuple.Value, oldPoint1);*/
						(*Value2)[0] = Tuple.Value;
					}
					else
					{
						for (int i = 0; i <= Tuple.Key[3]; i++)
						{
							/*FVector& oldPoint1 = *NewPToOldP.Find((*Value2)[i]);
							outAllPointMap[oldPoint1] = Tuple.Value;
							NewPToOldP.Remove((*Value2)[i]);
							NewPToOldP.Add(Tuple.Value, oldPoint1);*/
							(*Value2)[i] = Tuple.Value;
						}
						/*Value1.RemoveAt(0, Tuple.Key[3]);
						Value1[1] = Tuple.Value;*/
					}

					// 改变后判断之前被连接的点是否孤立了 Index2的前一个终点和Index2起点比， Index1的后一个的起点和Index1的终点比
					const int32 Index2_Prev_2 = (Index2 - 1 + VerNum) % VerNum;
					const int32 Index1_Next_2 = (Index1 + 1) % VerNum;
					if (operateCossSection.vertex[Index2_Prev_2].Last() != operateCossSection.vertex[Index2][0]
						&& operateCossSection.vertex[Index1_Next_2][0] != operateCossSection.vertex[Index1].Last())
					{
						for (int i = Index1_Next_2; i <= Index2_Prev_2; i++)
						{
							NeedRemove.Add(i, Tuple);
							WhetherMoveContains[i] = true;
						}
					}
				}
			}
		}

		// 找到孤立的线段删除
		for (auto& Tuple : NeedRemove)
		{
			for (UE::Math::TVector<double>& Vector : operateCossSection.vertex[Tuple.Key])
			{
				/*FVector& oldPoint1 = *NewPToOldP.Find(Vector);
				outAllPointMap[oldPoint1] = Tuple.Value.Value;
				NewPToOldP.Remove(Vector);
				NewPToOldP.Add(Tuple.Value.Value, oldPoint1);*/
				Vector = Tuple.Value.Value;
			}

			//operateCossSection.vertex.Remove(Tuple.Key);
		}
		for (TTuple<int, TArray<UE::Math::TVector<double>>>& Point : OldPoint)
		{
			auto& NewPoint = operateCossSection.vertex[Point.Key];
			if (Point.Value.Num() != NewPoint.Num())
			{
				continue;
			}
			for (int i = 0; i < NewPoint.Num(); i++)
			{
				OldPToNewP.Add(Point.Value[i], NewPoint[i]);
			}
		}
		InOutOldPToNewP = OldPToNewP;
	}
	//// -----------------------------

	return bCheck && operateCossSection.vertex.Num() > 0;
}


bool FCrossSectionGenerator::cutOutCrossSection(FCrossSectionStump& operateCossSection, const float& inCutValue)
{
	if (operateCossSection.crossSectons.Num() < 0 || inCutValue == 0)
	{
		return false;
	}
	//if (inCutValue < 0)
	{
		FVector normal = baseCrossSection.SectionNormal;
		normal.Normalize();

		//FVector startPos = FVector::ZeroVector;
		//if (crossSectionStumps.Num() == 3 && baseCrossSection.SectionType == ESectionType::ERectangle || baseCrossSection.SectionType == ESectionType::EEllipse)
		//{
		//	switch (baseCrossSection.PlanBelongs)
		//	{
		//	case EPlanPolygonBelongs::EXY_Plan:
		//		startPos = FVector(0, 0, baseCrossSection.Rectangle.StartLocation().Z);
		//		break;
		//	case EPlanPolygonBelongs::EXZ_Plan:
		//		startPos = FVector(0, baseCrossSection.Rectangle.StartLocation().Y, 0);
		//		break;
		//	case EPlanPolygonBelongs::EYZ_Plan:
		//		startPos = FVector(baseCrossSection.Rectangle.StartLocation().X, 0, 0);
		//		break;
		//	default:
		//		break;
		//	}
		//}

		FVector offset = normal * inCutValue;
		if (operateCossSection.growPath.Num() == 0)
		{
			auto top = getPathTop() /*+ startPos */- offset.GetSafeNormal() * 0.1f;
			auto bottom = getPathTop() + offset /*+ startPos*/;

			operateCossSection.growPath.Add(top);
			operateCossSection.growPath.Add(bottom);
			operateCossSection.scalePolygonMap = operateCossSection.vertex;
			operateCossSection.scalePolygon = operateCossSection.getVertex();

		}
		else
		{
			return false;
		}


	}
	//else
	//{
	//	return false;
	//}
	return false;
}

void FCrossSectionGenerator::geoMeshToSecionMesh(const FGeoMesh& inMesh, FPMCSection& section)
{
	section.Vertexes = inMesh.vertexs;
	section.Triangles = inMesh.indices;
	section.Normals.SetNumZeroed(inMesh.normals.Num());
	for (int32 i = 0; i < inMesh.normals.Num(); ++i)
	{
		section.Normals[i].X = inMesh.normals[i].X;
		section.Normals[i].Y = inMesh.normals[i].Y;
		section.Normals[i].Z = inMesh.normals[i].Z;
	}
	section.UV.SetNumZeroed(inMesh.uv.Num());

	for (int32 i = 0; i < inMesh.uv.Num(); ++i)
	{
		section.UV[i].X = inMesh.uv[i].X;
		section.UV[i].Y = inMesh.uv[i].Y;
	}
}

void FCrossSectionGenerator::secionMeshToGeoMesh(const FPMCSection& inSection, FGeoMesh& outMesh)
{
	outMesh.vertexs = inSection.Vertexes;
	outMesh.indices = inSection.Triangles;
	outMesh.normals.SetNumZeroed(inSection.Normals.Num());
	for (int32 i = 0; i < inSection.Normals.Num(); ++i)
	{
		outMesh.normals[i].X = inSection.Normals[i].X;
		outMesh.normals[i].Y = inSection.Normals[i].Y;
		outMesh.normals[i].Z = inSection.Normals[i].Z;
	}
	outMesh.uv.SetNumZeroed(inSection.UV.Num());

	for (int32 i = 0; i < inSection.UV.Num(); ++i)
	{
		outMesh.uv[i].X = inSection.UV[i].X;
		outMesh.uv[i].Y = inSection.UV[i].Y;
	}
}

bool FCrossSectionGenerator::shiftCrossSection(FCrossSectionData& operateCossSection, const TArray<float>& inShiftValue)
{

	TArray<float> FormatShiftValue;
	if (ESectionType::ECustomPlan == operateCossSection.SectionType)
	{
		FSectionShiftingOperation::FormatShiftValue(inShiftValue, FormatShiftValue, operateCossSection.Lines.Num());

		int32 index = 0;
		TMap<int32, TArray<FVector>> linePointsMap;

		crossSectionVertex(operateCossSection, linePointsMap);

		TArray<FVector> outKeyPointMap;
		TMap<FVector, FVector> outAllPointMap;

		TArray<double> shiftValues = (TArray<double>)FormatShiftValue;
		FGeometryLibrary::scalePolygonMesh(linePointsMap, shiftValues, operateCossSection.SectionNormal, outKeyPointMap, outAllPointMap);


		if (outKeyPointMap.Num() != operateCossSection.Points.Num())
		{
			return false;
		}

		int32 numOfPoint = outAllPointMap.Num();
		int32 i = 0;
		for (auto& iter : operateCossSection.Lines)
		{
			auto oldStart = iter.StartLocation;
			auto oldEnd = iter.EndLocation;

			int32 startIndex = INDEX_NONE;
			int32 endIndex = INDEX_NONE;

			int32 indexOld = 0;
			for (auto& itMap : outAllPointMap)
			{
				if (itMap.Key.Equals(oldStart, 0.01f))
				{
					iter.StartLocation = itMap.Value;
					startIndex = indexOld;

					operateCossSection.Points[i].LocationX = FExpressionValuePair((float)iter.StartLocation.X * 10.f);
					operateCossSection.Points[i].LocationY = FExpressionValuePair((float)iter.StartLocation.Y * 10.f);
					operateCossSection.Points[i].LocationZ = FExpressionValuePair((float)iter.StartLocation.Z * 10.f);

				}
				if (itMap.Key.Equals(oldEnd, 0.01f))
				{
					iter.EndLocation = itMap.Value;
					endIndex = indexOld;
				}
				++indexOld;
			}


			if (iter.LineType == ELineType::EHeightArc)
			{
				float maxDis = 0;

				TArray<FVector> tempNew;
				outAllPointMap.GenerateValueArray(tempNew);

				if (startIndex == INDEX_NONE || endIndex == INDEX_NONE)
				{
					continue;
				}

				int32 curIndex = startIndex;
				bool bDir = endIndex > startIndex;
				while (curIndex != endIndex)
				{
					auto dis = FMath::Abs(FMath::PointDistToLine(tempNew[curIndex], iter.EndLocation - iter.StartLocation, iter.StartLocation));
					if (dis > maxDis)
					{
						maxDis = dis;
					}
					if (bDir)
					{
						++curIndex;
						if (curIndex == tempNew.Num())
						{
							curIndex = 0;
						}
					}
					else
					{
						--curIndex;
						if (curIndex == -1)
						{
							curIndex = tempNew.Num() - 1;
						}
					}

				}
				if (iter.RadiusOrHeight() < 0)
				{
					maxDis = -maxDis;
				}
				iter.RadiusOrHeightData = FExpressionValuePair(maxDis * 10.f);
			}
			else if (iter.LineType == ELineType::ERadiusArc)
			{
				iter.RadiusOrHeightData = FExpressionValuePair((iter.RadiusOrHeight() + (float)shiftValues[i]) * 10.f);
			}
			++i;

			//int32 cur = 0;

			//for (auto& iter : operateCossSection.Lines)
			//{
			//	int32 next = (cur + 1) % operateCossSection.Lines.Num();
			//	
			//	auto oldStart = linePointsMap[cur][0];
			//	auto oldEnd = linePointsMap[cur].Last(0);
			//	if (!outAllPointMap.Contains(oldStart) || !outAllPointMap.Contains(oldEnd))
			//	{
			//		continue;
			//	}
			//	auto newStart	= outAllPointMap[oldStart];
			//	auto newEnd		= outAllPointMap[oldEnd];
			//	iter.StartLocation	= newStart;
			//	iter.EndLocation	= newEnd;
			//	if (iter.LineType == ELineType::EHeightArc)
			//	{
			//		float maxDis = 0;

			//		TArray<FVector> tempNew;
			//		outAllPointMap.GenerateValueArray(tempNew);

			//		int32 startIndex	= tempNew.Find(newStart);
			//		int32 endIndex		= tempNew.Find(newEnd);
			//		if (startIndex == INDEX_NONE || endIndex == INDEX_NONE)
			//		{
			//			continue;
			//		}

			//		int32 curIndex = startIndex;
			//		while(curIndex != endIndex)
			//		{
			//			auto dis = FMath::Abs(FMath::PointDistToLine(tempNew[curIndex], iter.EndLocation - iter.StartLocation, iter.StartLocation));
			//			if (dis > maxDis)
			//			{
			//				maxDis = dis;
			//			}
			//			++curIndex;
			//			if (curIndex == tempNew.Num())
			//			{
			//				curIndex = 0;
			//			}
			//		}
			//		if (iter.RadiusOrHeight() < 0)
			//		{
			//			maxDis = -maxDis;
			//		}
			//		iter.RadiusOrHeightData = FExpressionValuePair(maxDis * 10.f);
			//	}
			//	else if (iter.LineType == ELineType::ERadiusArc)
			//	{
			//		iter.RadiusOrHeightData = FExpressionValuePair((iter.RadiusOrHeight() + (float)shiftValues[cur]) * 10.f);
			//	}
			//	++cur;
			//for (int32 i = 0; i < outKeyPointMap.Num(); ++i)
			//{
			//	operateCossSection.Points[i].LocationX = FExpressionValuePair((float)outKeyPointMap[i].X * 10.f);
			//	operateCossSection.Points[i].LocationY = FExpressionValuePair((float)outKeyPointMap[i].Y * 10.f);
			//	operateCossSection.Points[i].LocationZ = FExpressionValuePair((float)outKeyPointMap[i].Z * 10.f);
			//}
		}

	}
	else if (ESectionType::ERectangle == operateCossSection.SectionType)
	{
		FCrossSectionData tempCossSection = operateCossSection;
		FSectionShiftingOperation::FormatShiftValue(inShiftValue, FormatShiftValue, 4);

		return tempCossSection.Rectangle.ShiftSection(FormatShiftValue, operateCossSection.Rectangle);
	}
	else if (ESectionType::EEllipse == operateCossSection.SectionType)
	{
		FCrossSectionData tempCossSection = operateCossSection;
		return tempCossSection.Ellipse.ShiftSection(inShiftValue, operateCossSection.Ellipse);
	}
	return true;
}


void FCrossSectionGenerator::createHouse(const FCrossSectionStump& inStump, bool bReverse, FGeoMesh& outMesh, bool bExtension,bool bPathUV)
{

	TArray<FPMCSection> sections;
	sections.SetNumZeroed(2);
	FGeoMesh HouseMesh0;
	TArray<FVector> Vertexs0;
	FPolygonData polygon0;

	if (inStump.scalePolygon.Num() > 0)
	{
		auto scalePoly = inStump.scalePolygon;
		auto basePoly = inStump.getVertex();
		auto abMap = inStump.scaleABMap;

		if (baseCrossSection.SectionType == ESectionType::ECustomPlan && inStump.scaleABMap.Num() > 0)
		{
			basePoly.Empty();
			scalePoly.Empty();

			abMap.GenerateKeyArray(basePoly);
			abMap.GenerateKeyArray(scalePoly);
		}


		/*TSet<FVector> UniqueSet;
		for(const auto& it : basePoly1)
		{
			UniqueSet.Add(it);
		}
		TArray<TSet<FVector>::ElementType> basePoly = UniqueSet.Array();*/

		//面位置由PATH确定
		FVector Move = FVector::ZeroVector;
		if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXY_Plan)
		{

			for (auto& iter : basePoly)
			{
				iter.Z = 0.0f;
			}
			for (auto& iter : scalePoly)
			{
				iter.Z = 0.0f;
			}
			for (auto& iter : abMap)
			{
				iter.Key.Z = 0.0f;
				iter.Value.Z = 0.0f;
			}
		}
		else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXZ_Plan)
		{
			for (auto& iter : basePoly)
			{
				iter.Y = 0.0f;
			}
			for (auto& iter : scalePoly)
			{
				iter.Y = 0.0f;
			}
			for (auto& iter : abMap)
			{
				iter.Key.Y = 0.0f;
				iter.Value.Y = 0.0f;
			}
		}
		else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EYZ_Plan)
		{
			for (auto& iter : basePoly)
			{
				iter.X = 0.0f;
			}
			for (auto& iter : scalePoly)
			{
				iter.X = 0.0f;
			}
			for (auto& iter : abMap)
			{
				iter.Key.X = 0.0f;
				iter.Value.X = 0.0f;
			}
		}

		if (inStump.growPath.Num() > 1)
		{
			for (auto& iter : basePoly)
			{
				iter += inStump.growPath[0];
			}
			for (auto& iter : scalePoly)
			{
				iter += inStump.growPath.Last();
			}
			for (auto& iter : abMap)
			{
				iter.Key += inStump.growPath[0];
				iter.Value += inStump.growPath.Last();
			}
		}
		auto normal = inStump.growPath.Num() > 0 ? inStump.growPath.Last() - inStump.growPath[0] : baseCrossSection.SectionNormal;
		normal.Normalize();
		if (normal.Dot(baseCrossSection.SectionNormal) < 0)
		{
			FArrayOperatorLibrary::ReverseArray(basePoly);
			FArrayOperatorLibrary::ReverseArray(scalePoly);
		}
		else if (FPolygon3DLibrary::IsPolygonCCWWinding(basePoly, baseCrossSection.SectionNormal))
		{
			FArrayOperatorLibrary::ReverseArray(basePoly);
			FArrayOperatorLibrary::ReverseArray(scalePoly);
		}

		if (abMap.Num() > 0 && baseCrossSection.SectionType == ESectionType::ECustomPlan)
		{
			auto pNum = basePoly.Num();
			TArray<FVector> newPoly;
			for (int32 cur = 0; cur < basePoly.Num(); ++cur)
			{
				int32 next = (cur + 1) % basePoly.Num();
				int32 pre = (cur - 1 + basePoly.Num()) % basePoly.Num();

				auto A = (basePoly[cur] - basePoly[pre]).GetSafeNormal();
				auto B = (basePoly[next] - basePoly[cur]).GetSafeNormal();

				if (!A.Cross(B).Equals(FVector::ZeroVector, 0.0000001f))
				{
					newPoly.Add(basePoly[cur]);
				}
			}
			FGeometryLibrary::createMeshByTwoPolygon(newPoly, abMap, normal, outMesh);
		}
		else
		{
			if (basePoly.Num() == scalePoly.Num() && !basePoly.IsEmpty())
			{
				FVector Normal = FVector::UpVector;
				if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXY_Plan)
				{
					if (basePoly[0].Z > scalePoly[0].Z)
					{
						auto Temp = basePoly;
						basePoly = scalePoly;
						scalePoly = Temp;
					}
					for (int32 i = 0; i < basePoly.Num(); i++)
					{
						basePoly[i].Z -= 0.02;
						scalePoly[i].Z += 0.02;
					}
				}
				else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EXZ_Plan)
				{
					if(basePoly[0].Y > scalePoly[0].Y)
					{
						auto Temp = basePoly;
						basePoly = scalePoly;
						scalePoly = Temp;
					}
					for (int32 i = 0; i < basePoly.Num(); i++)
					{
						basePoly[i].Y -= 0.02;
						scalePoly[i].Y += 0.02;
					}
					Normal = FVector::YAxisVector;
				}
				else if (baseCrossSection.PlanBelongs == EPlanPolygonBelongs::EYZ_Plan)
				{
					if (basePoly[0].X > scalePoly[0].X)
					{
						auto Temp = basePoly;
						basePoly = scalePoly;
						scalePoly = Temp;
					}

					for (int32 i = 0; i < basePoly.Num(); i++)
					{
						basePoly[i].X -= 0.02;
						scalePoly[i].X += 0.02;
					}

					Normal = FVector::XAxisVector;
				}
				if (FGeometryLibrary::clockwise(scalePoly, Normal))
				{
					FArrayOperatorLibrary::ReverseArray(basePoly);
					FArrayOperatorLibrary::ReverseArray(scalePoly);
				}

				if (bExtension)
				{
					basePoly = FGeometryLibrary::OffsetPolygon(basePoly, 0.02, Normal);
					scalePoly = FGeometryLibrary::OffsetPolygon(scalePoly, 0.02, Normal);
				}

				FGeometryLibrary::createMeshByTwoPolygon(basePoly, scalePoly, Normal, outMesh);
			}
		}
	}
	else if (inStump.growPath.Num() > 1)
	{
		if (inStump.growPath.Num() == 50)
		{
			FGeometryLibrary::CreateSplineMesh(inStump.getVertex(), baseCrossSection.SectionNormal, inStump.growPath, outMesh);
		}
		else
		{
			FGeometryLibrary::createMeshByPolyonPath(inStump.getVertex(), baseCrossSection.SectionNormal, inStump.growPath, inStump.scalePolygon, bReverse, bPathUV,outMesh);
		}
	}
	else
	{
		TArray<FVector> temp;
		for (const auto& iter : inStump.getVertex())
		{
			temp.AddUnique(iter);
		}
		if (temp.Num() > 2)
		{
			FGeoMesh a;
			auto aPlane = inStump.getVertex();
			auto pos = inStump.growPath.Num() > 0 ? inStump.growPath[0] : FVector::ZeroVector;
			for (auto& iter : aPlane)
			{
				iter += pos;
			}
			FGeometryLibrary::createPlanarPolygonMesh(aPlane, baseCrossSection.SectionNormal, a);
			outMesh.addMesh(a);
			FGeoMesh b;
			auto bPlane = aPlane;
			for (auto& iter : bPlane)
			{
				iter -= baseCrossSection.SectionNormal * 0.01;
			}
			FGeometryLibrary::createPlanarPolygonMesh(bPlane, -baseCrossSection.SectionNormal, b);
			outMesh.addMesh(b);
		}
	}
}


void FCrossSectionGenerator::process(FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& outline,bool bPathUV)
{

	FGeoMesh allMesh;
	allMesh.empty();
	int32 index = 0;
	TArray<TPair<FVector, FVector>> newOutline;
	for (const auto& iter : crossSectionStumps)
	{
		if (index == 0 && crossSectionStumps.Num() > 1 && !iter.Value.bCube)
		{
			++index;
			continue;
		}

		FGeoMesh houseMesh;
		if (iter.Value.bCube)
		{
			FPMCSection sectionCube;
			baseCrossSection.Cube.GenerateMeshAndFramwork(sectionCube, newOutline);
			//outline.Append(newOutline);
			FGeoMesh resMesh;
			secionMeshToGeoMesh(sectionCube, resMesh);
			allMesh.empty();
			allMesh.addMesh(resMesh);
		}
		else
		{
			if (iter.Value.bShowBody)
			{ 
				createHouse(iter.Value, false, houseMesh,false, bPathUV);
				FGeoMesh resMesh;

				if (allMesh.vertexs.Num() > 0)
				{
					FGeometryLibrary::meshBoolean(houseMesh, allMesh, UE::Geometry::FMeshBoolean::EBooleanOp::Union, resMesh);
				}
				else
				{
					resMesh = houseMesh;
				}
				allMesh.empty();
				allMesh.addMesh(resMesh);
			}
		}

		if (boolStumps.Contains(index))
		{
			FGeoMesh boolMesh;
			createHouse(boolStumps[index], true, boolMesh,true);
			FGeoMesh resMesh;
			FGeometryLibrary::meshBoolean(allMesh, boolMesh, UE::Geometry::FMeshBoolean::EBooleanOp::Difference, resMesh);
			allMesh.empty();
			allMesh.addMesh(resMesh);
		}
		++index;
	}

	//{
	//	if (!uvMesh.IsValid())
	//	{
	//		uvMesh = MakeShared<FGeoMesh>(allMesh);

	//	}
	//	const TArray<FVector> gpath = getGrowPath();
	//	FGraphEventRef calculateEvent;
	//	UMultiThreadFunctionLibrary::CallThreadBindStatic(
	//		calculateEvent, 
	//		NULL, 
	//		ENamedThreads::AnyThread, 
	//		FCrossSectionGenerator::calculateUV, 
	//		gpath,
	//		uvMesh);

	//	FGraphEventRef refreshUVEvent;
	//	UMultiThreadFunctionLibrary::CallThreadBindStatic(
	//		refreshUVEvent, 
	//		calculateEvent, 
	//		ENamedThreads::GameThread, 
	//		FCrossSectionGenerator::refreshUVMesh, 
	//		uvMesh,
	//		editActor);

	//	
	//}

	{

		//if (getGrowPath().Num() == 2)
		//{
		//	TArray<FVector> path;
		//	path.Add(FVector::ZeroVector);
		//	path.Add(FVector(0, 0, 1000.0f));
		//	FGeometryLibrary::uvUnwrap(path, allMesh);
		//}
		//else
		TArray<FVector> temp;
		for (const auto& iter : allMesh.vertexs)
		{
			temp.AddUnique(iter);
		}
		if (temp.Num() > 2)
		{
			FGeometryLibrary::uvUnwrap(bPathUV ? getGrowPath() : TArray<FVector>{}, allMesh);
		}
	}

	geoMeshToSecionMesh(allMesh, OutMeshInfo);
	FGeometryLibrary::RotateUV(OutMeshInfo.UV, -PI / 2);
	outline.Append(allMesh.edges);
}

bool FCrossSectionGenerator::loftingCrossSection(FCrossSectionStump& inCrossSection, const TArray<FVector>& inPath)
{
	inCrossSection.growPath = inPath;
	//int32 numOfPathPoint = inPath.Num();

	//FVector startPlanNormal = inPath[0] - inPath[1];
	//startPlanNormal.Normalize();

	//TArray<FVector> endPlanNormals;

	//FCrossSectionData orginSection = inCrossSection.crossSectons[0];
	//FCrossSectionData orginSectionB = inCrossSection.crossSectons[1];
	//bool bClose = inPath[0].Equals(inPath.Last(0));
	//for (int32 cur = 0; cur < numOfPathPoint; cur++)
	//{	
	//	if (cur == 0)
	//	{
	//		transformation(inCrossSection.crossSectons[0], startPlanNormal);
	//		transformation(inCrossSection.crossSectons[1], -startPlanNormal);
	//		moveCrossSection(inCrossSection.crossSectons[0], inPath[0]);
	//		crossSectionVertex(inCrossSection.crossSectons[0], inCrossSection.crossSectons[0].vertexCache);
	//		moveCrossSection(inCrossSection.crossSectons[1], inPath[0]);
	//		continue;
	//	}

	//	int32 pre = cur - 1;
	//	int32 next = cur + 1;

	//	if (cur != numOfPathPoint - 1)
	//	{
	//		FVector preDir = inPath[pre] - inPath[cur];
	//		preDir.Normalize();
	//		FVector nextDir = inPath[cur] - inPath[next];
	//		nextDir.Normalize();
	//		FVector endDir = preDir + nextDir;
	//		endDir.Normalize();
	//		FCrossSectionData tempA = orginSection;
	//		FCrossSectionData tempB = orginSection;

	//		transformation(tempA, endDir);
	//		transformation(tempB, preDir);
	//		projectSection(tempA, tempB);
	//		moveCrossSection(tempB, inPath[cur]);
	//		tempB.reversal();
	//		
	//		inCrossSection.slices.Add(tempB);
	//	}
	//	else
	//	{
	//		if (bClose)
	//		{
	//			pre = cur - 1;
	//			next = 1;

	//			FVector preDir = inPath[pre] - inPath[cur];
	//			preDir.Normalize();
	//			FVector nextDir = inPath[cur] - inPath[next];
	//			nextDir.Normalize();
	//			FVector endDir = preDir + nextDir;
	//			endDir.Normalize();
	//			FCrossSectionData tempA = orginSection;
	//			FCrossSectionData tempB = orginSection;

	//			transformation(tempA, endDir);
	//			transformation(tempB, preDir);
	//			projectSection(tempA, tempB);
	//			moveCrossSection(tempB, inPath[cur]);
	//			inCrossSection.crossSectons[0] = tempB;
	//			tempB.reversal();
	//			inCrossSection.crossSectons[1] = tempB;

	//		}
	//		else
	//		{
	//			FCrossSectionData temp = orginSection;
	//			FVector endDir = inPath[pre] - inPath[cur];
	//			endDir.Normalize();
	//			transformation(temp, endDir);
	//			moveCrossSection(temp, inPath[cur]);
	//			temp.reversal();
	//			crossSectionVertex(temp, temp.vertexCache);
	//			inCrossSection.crossSectons[1] = temp;
	//		}

	//	}

	//}

	return true;
}



void FCrossSectionGenerator::generateSectionMeshFromGeoMesh(const FGeoMesh& inMesh, const FVector& InTangentX, FPMCSection& Mesh)
{
	if (inMesh.vertexs.Num() < 3 || inMesh.indices.Num() < 3) return;
	if (0 != (inMesh.indices.Num() % 3)) return;
	{
		Mesh.Vertexes = inMesh.vertexs;
		Mesh.Triangles = inMesh.indices;
		for (const auto& iter : inMesh.normals)
		{
			FVector nor = FVector(iter.X, iter.Y, iter.Z);
			Mesh.Normals.Add(nor);
		}

		FProcMeshTangent Tangent;
		Tangent.TangentX = InTangentX;
		Tangent.bFlipTangentY = false;
		Mesh.Tangents.Init(Tangent, inMesh.vertexs.Num());

		//TArray<FVector>TangentY;
		const auto& UVZeroPoint = inMesh.vertexs[0];
		int32 index = 0;
		Mesh.UV.SetNumZeroed(inMesh.vertexs.Num());
		for (const auto& iter : Mesh.Normals)
		{
			FVector TangentY = iter ^ InTangentX.GetSafeNormal();
			FVector2D UVMin(BIG_NUMBER, BIG_NUMBER);
			const auto& vertex = inMesh.vertexs[index];
			Mesh.UV[index].X = (vertex | Tangent.TangentX) * 0.01f;
			Mesh.UV[index].Y = (vertex | TangentY) * 0.01f;
		}
	}
}

void FCrossSectionGenerator::transformation(FCrossSectionData& inCrossSection, const FVector& targetNormal)
{
	Eigen::Vector3d vecBefore;
	vecBefore << inCrossSection.SectionNormal.X, inCrossSection.SectionNormal.Y, inCrossSection.SectionNormal.Z;

	Eigen::Vector3d vecAfter;
	vecAfter << targetNormal.X, targetNormal.Y, targetNormal.Z;

	Eigen::Matrix3d tMat;
	FGeometryLibrary::transformationMaterix(vecBefore, vecAfter, tMat);
	if (inCrossSection.SectionType == ESectionType::ECustomPlan)
	{
		for (auto& iter : inCrossSection.Points)
		{
			Eigen::Vector3d oldVec;
			oldVec << iter.PointLocation().X, iter.PointLocation().Y, iter.PointLocation().Z;
			auto newVec = tMat * oldVec;
			iter.LocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.x()));
			iter.LocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.y()));
			iter.LocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.z()));
		}
		for (auto& iter : inCrossSection.Lines)
		{
			Eigen::Vector3d oldStartVec;
			oldStartVec << iter.StartLocation.X, iter.StartLocation.Y, iter.StartLocation.Z;

			Eigen::Vector3d oldEndVec;
			oldEndVec << iter.EndLocation.X, iter.EndLocation.Y, iter.EndLocation.Z;

			auto newStartVec = tMat * oldStartVec;
			auto newEndVec = tMat * oldEndVec;

			iter.StartLocation = FVector(newStartVec.x(), newStartVec.y(), newStartVec.z());
			iter.EndLocation = FVector(newEndVec.x(), newEndVec.y(), newEndVec.z());
		}
	}
	else if (inCrossSection.SectionType == ESectionType::ERectangle)
	{
		const FVector tangentX = NSPlanType::GetPlanTangentX(inCrossSection.PlanBelongs);
		TArray<FVector> points;
		bool res = FGeometry3DLibrary::GenerateRectanglePlanPoints(inCrossSection.Rectangle.StartLocation(), inCrossSection.Rectangle.EndLocation(), inCrossSection.SectionNormal, tangentX, points);
		if (!res)
			return;

		FVector originPoint = points[0];
		int32 index = 0;
		for (auto& iter : points)
		{
			Eigen::Vector3d oldVec;
			oldVec << iter.X, iter.Y, iter.Z;
			auto newVec = tMat * oldVec;

			if (index == 0)
			{
				inCrossSection.Rectangle.StartLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.x()));
				inCrossSection.Rectangle.StartLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.y()));
				inCrossSection.Rectangle.StartLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.z()));
			}
			else if (index == 2)
			{
				inCrossSection.Rectangle.EndLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.x()));
				inCrossSection.Rectangle.EndLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.y()));
				inCrossSection.Rectangle.EndLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newVec.z()));
			}
			++index;
		}
	}
	else if (inCrossSection.SectionType == ESectionType::EEllipse)
	{

	}
	inCrossSection.SectionNormal = targetNormal;
}

bool FCrossSectionGenerator::CreateMeshBetweenCrossSection(const FCrossSectionData& InFirstSection, const FCrossSectionData& InSecondSection, TMap<int32, FGeoMesh>& OutGeoMesh, TArray<FPMCSection>& OutMeshInfos)
{
	if (InFirstSection.SectionType != InSecondSection.SectionType)
		return false;

	if (ESectionType::ECustomPlan == InFirstSection.SectionType)
	{

		const FVector Plan1Normal = InFirstSection.SectionNormal;
		const FVector Plan2Normal = InSecondSection.SectionNormal;
		TArray<FVector> OriginPoints;
		OriginPoints.Init(FVector::ZeroVector, InFirstSection.Points.Num());

		//TArray<TPair<int32, TArray<FVector>>> plans;
		OutMeshInfos.Empty();

		TArray<FVector> FirstLinePoints;
		TArray<FVector> SecondLinePoints;
		if (InFirstSection.vertexCache.Num() > 0)
		{
			FirstLinePoints = InFirstSection.vertexCache;
		}
		else
		{
			crossSectionVertex(InFirstSection, FirstLinePoints);
		}

		if (InSecondSection.vertexCache.Num() > 0)
		{
			SecondLinePoints = InSecondSection.vertexCache;
		}
		else
		{
			crossSectionVertex(InSecondSection, SecondLinePoints);
		}

		if (FPolygon3DLibrary::IsPolygonCCWWinding(FirstLinePoints, InFirstSection.SectionNormal))
		{
			FArrayOperatorLibrary::ReverseArray(FirstLinePoints);
		}
		if (FPolygon3DLibrary::IsPolygonCCWWinding(SecondLinePoints, InSecondSection.SectionNormal))
		{
			FArrayOperatorLibrary::ReverseArray(SecondLinePoints);
		}
		if (InFirstSection.vertexCache.Num() == 0 && InSecondSection.vertexCache.Num())
			FGeometryLibrary::uniformVertexOfTwoPlan(FirstLinePoints, Plan1Normal, SecondLinePoints, Plan2Normal);

		//if (InFirstSection.SectionNormal != InSecondSection.SectionNormal)
		//{
		//	auto first = SecondLinePoints[0];
		//	SecondLinePoints.RemoveAt(0);
		//	SecondLinePoints.Add(first);
		//}

		if (FirstLinePoints.Num() > 1 && SecondLinePoints.Num() > 1)
		{
			FVector dir = *FirstLinePoints.end() - *FirstLinePoints.begin();
			FVector nor = FVector::CrossProduct(InFirstSection.SectionNormal, dir);
			TArray<FVector> planVers = FirstLinePoints;
			FGeoMesh planMesh;

			int32 num = FirstLinePoints.Num();
			FGeometryLibrary::createCurvedSurfaceWithTwoArcSegment(FirstLinePoints, SecondLinePoints, planMesh);

			OutGeoMesh.Add(0, planMesh);
			FPMCSection newSection;



			geoMeshToSecionMesh(planMesh, newSection);
			OutMeshInfos.Add(newSection);
		}
	}
	else if (ESectionType::ERectangle == InFirstSection.SectionType)
	{
		const FVector PlanANormal = InFirstSection.SectionNormal;
		const FVector PlanBNormal = InSecondSection.SectionNormal;
		const FVector PlanATangentX = NSPlanType::GetPlanTangentX(InFirstSection.PlanBelongs);
		const FVector PlanBTangentX = NSPlanType::GetPlanTangentX(InSecondSection.PlanBelongs);

		TArray<FVector> RectAPoints;
		TArray<FVector> RectBPoints;

		bool ResA = FGeometry3DLibrary::GenerateRectanglePlanPoints(InFirstSection.Rectangle.StartLocation(), InFirstSection.Rectangle.EndLocation(), PlanANormal, PlanATangentX, RectAPoints);
		bool ResB = FGeometry3DLibrary::GenerateRectanglePlanPoints(InSecondSection.Rectangle.StartLocation(), InSecondSection.Rectangle.EndLocation(), PlanBNormal, PlanBTangentX, RectBPoints);

		if (RectAPoints.Num() > 1 && RectBPoints.Num() > 1)
		{
			FGeometryLibrary::uniformVertexOfTwoPlan(RectAPoints, PlanANormal, RectBPoints, PlanBNormal);
			FGeoMesh planMesh;
			FGeometryLibrary::createCurvedSurfaceWithTwoArcSegment(RectAPoints, RectBPoints, planMesh);
			FPMCSection newSection;
			geoMeshToSecionMesh(planMesh, newSection);
			OutMeshInfos.Add(newSection);
			OutGeoMesh.Add(0, planMesh);
		}
	}
	else if (ESectionType::EEllipse == InFirstSection.SectionType)
	{
		const FVector PlanANormal = InFirstSection.SectionNormal;
		const FVector PlanBNormal = InSecondSection.SectionNormal;
		const FVector PlanATangentX = NSPlanType::GetPlanTangentX(InFirstSection.PlanBelongs);
		const FVector PlanBTangentX = NSPlanType::GetPlanTangentX(InFirstSection.PlanBelongs);

		TArray<FVector> RectAPoints;
		TArray<FVector> RectBPoints;

		bool ResA = false;
		bool ResB = false;
		if (InFirstSection.vertexCache.Num() > 0)
		{
			RectAPoints = InFirstSection.vertexCache;
			ResA = true;
		}
		else
		{
			ResA = FGeometry3DLibrary::GenerateEllipsePlanPoints(InFirstSection.Ellipse.CenterLocation(), InFirstSection.Ellipse.LongRadius(), InFirstSection.Ellipse.ShortRadius(), PlanANormal, PlanATangentX, RectAPoints, InFirstSection.Ellipse.InterPointCount());
		}
		if (InSecondSection.vertexCache.Num() > 0)
		{
			RectBPoints = InSecondSection.vertexCache;
			ResB = true;
		}
		else
		{
			ResB = FGeometry3DLibrary::GenerateEllipsePlanPoints(InSecondSection.Ellipse.CenterLocation(), InSecondSection.Ellipse.LongRadius(), InSecondSection.Ellipse.ShortRadius(), PlanBNormal, PlanBTangentX, RectBPoints, InSecondSection.Ellipse.InterPointCount());
		}

		FVector endA = RectAPoints[0];
		FVector endB = RectBPoints[0];

		RectAPoints.Add(endA);
		RectBPoints.Add(endB);

		if (RectAPoints.Num() > 1 && RectBPoints.Num() > 1)
		{
			FGeometryLibrary::uniformVertexOfTwoPlan(RectAPoints, PlanANormal, RectBPoints, PlanBNormal);

			FGeoMesh planMesh;
			FGeometryLibrary::createCurvedSurfaceWithTwoArcSegment(RectAPoints, RectBPoints, planMesh);
			FPMCSection newSection;
			geoMeshToSecionMesh(planMesh, newSection);
			OutMeshInfos.Add(newSection);
			OutGeoMesh.Add(0, planMesh);
		}

	}
	return true;
}
void FCrossSectionGenerator::projectSection(const FCrossSectionData& planSection, FCrossSectionData& inCrossSection)
{

	FVector normal = planSection.SectionNormal;
	const FVector tangentX = NSPlanType::GetPlanTangentX(planSection.PlanBelongs);
	if (ESectionType::ECustomPlan == planSection.SectionType)
	{
		TArray<FVector> points;
		crossSectionVertex(planSection, points);

		FVector originPoint = points[0];
		for (auto& iter : points)
		{
			FVector oldPointStart = iter;
			FVector oldPointEnd = iter + inCrossSection.SectionNormal * 10.0f;
			iter = FMath::LinePlaneIntersection<double>(oldPointStart, oldPointEnd, originPoint, normal);

		}
		inCrossSection.vertexCache = points;
	}
	else if (ESectionType::ERectangle == planSection.SectionType)
	{
		TArray<FVector> points;
		bool res = FGeometry3DLibrary::GenerateRectanglePlanPoints(planSection.Rectangle.StartLocation(), planSection.Rectangle.EndLocation(), normal, tangentX, points);
		if (!res)
			return;

		FVector originPoint = points[0];
		int32 index = 0;
		for (auto& iter : points)
		{
			FVector oldPointStart = iter;
			FVector oldPointEnd = iter + inCrossSection.SectionNormal * 10.0f;
			FVector newPoint = FMath::LinePlaneIntersection<double>(oldPointStart, oldPointEnd, originPoint, normal);
			if (index == 0)
			{
				inCrossSection.Rectangle.StartLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.X));
				inCrossSection.Rectangle.StartLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.Y));
				inCrossSection.Rectangle.StartLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.Z));
			}
			else if (index == 2)
			{
				inCrossSection.Rectangle.EndLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.X));
				inCrossSection.Rectangle.EndLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.Y));
				inCrossSection.Rectangle.EndLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(newPoint.Z));
			}
			++index;
		}
	}
	else if (ESectionType::EEllipse == planSection.SectionType)
	{
		TArray<FVector> points;
		bool res = FGeometry3DLibrary::GenerateEllipsePlanPoints(planSection.Ellipse.CenterLocation(), planSection.Ellipse.LongRadius(), planSection.Ellipse.ShortRadius(), normal, tangentX, points, planSection.Ellipse.InterPointCount());
		if (!res)
			return;

		FVector originPoint = points[0];
		for (auto& iter : points)
		{
			FVector oldPointStart = iter;
			FVector oldPointEnd = iter + inCrossSection.SectionNormal * 10.0f;
			iter = FMath::LinePlaneIntersection<double>(oldPointStart, oldPointEnd, originPoint, normal);
		}
		inCrossSection.vertexCache = points;
	}
}

void FCrossSectionGenerator::crossSectionVertex(const FCrossSectionData& inCrossSection, TArray<FVector>& outVertex)
{
	FGeometryPointGenerator pointGenerator;
	FVector planNormal = inCrossSection.SectionNormal;
	pointGenerator.setNormal(planNormal);
	for (auto& iter : inCrossSection.Points)
	{
		FGeomtryPointProperty PointProperty(iter);
		PointProperty.PlanBelongs = inCrossSection.PlanBelongs;

		FVector newPointLocation(iter.PointLocation());
		pointGenerator.addPoint(newPointLocation, PointProperty);
	}
	pointGenerator.computeStruct();

	auto pointDatas = pointGenerator.getPoints();
	FGeometryLineGenerator lineGenerator;
	lineGenerator.setNormal(planNormal);
	lineGenerator.initializeLines(pointDatas, pointGenerator.getPointCount() >= 2);
	if (inCrossSection.Lines.Num() == lineGenerator.getLines().Num())
	{
		for (int32 i = 0; i < inCrossSection.Lines.Num(); ++i)
		{
			auto line = lineGenerator.getLines()[i];
			line->lineProperty.CopyData(inCrossSection.Lines[i]);
		}
	}
	else
	{
		return;
	}

	pointDatas.Sort([](TSharedPtr<FGeometryPointData> a, TSharedPtr<FGeometryPointData>b) {return a->structSort < b->structSort; });

	int32 pointCount = pointGenerator.getPointCount();
	for (int32 cur = 0; cur < pointCount; ++cur)
	{
		int32 next = (cur + 1) % pointCount;

		TArray<FVector> newVertex;
		if (pointCount <= 2)
		{
			lineGenerator.getLineVertexOrder(pointDatas[cur], pointDatas[next], 20, newVertex);
		}
		else
		{
			lineGenerator.getLineVertex(pointDatas[cur], pointDatas[next], 20, newVertex);
		}
		for (auto& iter : newVertex)
		{
			outVertex.AddUnique(iter);
		}
	}
}

void FCrossSectionGenerator::crossSectionVertex(const FCrossSectionData& inCrossSection, TMap<int32, TArray<FVector>>& outVertex)
{
	FGeometryPointGenerator pointGenerator;
	FVector planNormal = inCrossSection.SectionNormal;
	pointGenerator.setNormal(planNormal);
	for (auto& iter : inCrossSection.Points)
	{
		FGeomtryPointProperty PointProperty(iter);
		PointProperty.PlanBelongs = inCrossSection.PlanBelongs;

		FVector newPointLocation(iter.PointLocation());
		pointGenerator.addPoint(newPointLocation, PointProperty);
	}
	pointGenerator.computeStruct();

	auto pointDatas = pointGenerator.getPoints();
	FGeometryLineGenerator lineGenerator;
	lineGenerator.setNormal(planNormal);
	lineGenerator.initializeLines(pointDatas, pointGenerator.getPointCount() >= 2);
	if (inCrossSection.Lines.Num() == lineGenerator.getLines().Num())
	{
		for (int32 i = 0; i < inCrossSection.Lines.Num(); ++i)
		{
			auto line = lineGenerator.getLines()[i];
			line->lineProperty.CopyData(inCrossSection.Lines[i]);
		}
	}
	else
	{
		return;
	}

	pointDatas.Sort([](TSharedPtr<FGeometryPointData> a, TSharedPtr<FGeometryPointData>b) {return a->structSort < b->structSort; });

	int32 pointCount = pointGenerator.getPointCount();
	for (int32 cur = 0; cur < pointCount; ++cur)
	{
		int32 next = (cur + 1) % pointCount;

		TArray<FVector> newVertex;

		auto Dis = FVector::Distance(pointDatas[cur]->position, pointDatas[next]->position);
		auto A = 2;
		if (Dis <= 5 && Dis> 0.5f)
		{
			Dis = 1;
			A = 5;
		}
		else if (Dis <= 0.5)
		{
			Dis = 1.0f;
			A =3;
		}
		if (pointCount <= 2)
		{
			lineGenerator.getLineVertexOrder(pointDatas[cur], pointDatas[next], static_cast<int32>(Dis) * A, newVertex);
			//lineGenerator.getLineVertexOrder(pointDatas[cur], pointDatas[next], /*(int32)Dis * A*/15, newVertex);
		}
		else
		{
			lineGenerator.getLineVertex(pointDatas[cur], pointDatas[next], static_cast<int32>(Dis) * A, newVertex);
			//lineGenerator.getLineVertex(pointDatas[cur], pointDatas[next], /*(int32)Dis * A*/15, newVertex);
		}
		outVertex.Add(cur, newVertex);
	}

	int32 numOfLine = outVertex.Num();
	if (numOfLine <= 0)
	{
		return;
	}
	for (int32 cur = 1; cur < numOfLine; ++cur)
	{
		int32 next = (cur + 1) % numOfLine;
		int32 pre = (cur - 1 + numOfLine) % numOfLine;
		outVertex[cur][0] = outVertex[pre].Last();
		outVertex[cur].Last() = outVertex[next][0];
	}
}

TArray<FVector> FCrossSectionGenerator::getGrowPath()
{
	TArray<FVector> temp;
	if (crossSectionStumps.Num() < 0)
	{
		return temp;
	}
	for (auto& s : crossSectionStumps)
	{
		int32 i = 0;
		for (auto& p : s.Value.growPath)
		{
			if (temp.Num() > 0 && i == 0)
			{
				++i;
				continue;
			}
			temp.Add(p);
			++i;
		}
	}

	return temp;
}

void FCrossSectionGenerator::refreshUVMesh(TSharedPtr<FGeoMesh> inOutMesh, AShowSingleComponentActor* inActor)
{
	if (inOutMesh.IsValid() && inActor)
	{
		auto tempProperty = inActor->getTempProperty();
		FPMCSection newSection;
		newSection.Vertexes = inOutMesh->vertexs;
		newSection.Triangles = inOutMesh->indices;
		newSection.Normals.SetNumZeroed(inOutMesh->normals.Num());
		for (int32 i = 0; i < inOutMesh->normals.Num(); ++i)
		{
			newSection.Normals[i].X = inOutMesh->normals[i].X;
			newSection.Normals[i].Y = inOutMesh->normals[i].Y;
			newSection.Normals[i].Z = inOutMesh->normals[i].Z;
		}
		newSection.UV.SetNumZeroed(inOutMesh->uv.Num());

		for (int32 i = 0; i < inOutMesh->uv.Num(); ++i)
		{
			newSection.UV[i].X = inOutMesh->uv[i].X;
			newSection.UV[i].Y = inOutMesh->uv[i].Y;
		}
		if (tempProperty.MeshInfo.Num() > 0 && tempProperty.MeshInfo[0].SingleMeshInfo.Num() > 0)
		{
			tempProperty.MeshInfo[0].SingleMeshInfo[0].MeshInfo = newSection;
		}
		inActor->RefreshComponentMesh(tempProperty);
		inOutMesh = nullptr;
	}
}
void FCrossSectionGenerator::calculateUV(const TArray<FVector> unwrapPath, TSharedPtr<FGeoMesh> inOutMesh)
{
	FGeometryLibrary::uvUnwrap(unwrapPath, *inOutMesh);
}
FVector FCrossSectionGenerator::getPathTop()
{
	return getGrowPath().Num() <= 0 ? FVector::ZeroVector : getGrowPath().Last();
}

FVector FCrossSectionGenerator::getPathDir()
{
	return getGrowPath().Num() <= 0 ? FVector::ZeroVector : (getGrowPath().Last() - getGrowPath()[0]).GetSafeNormal();
}
TArray<FVector> FCrossSectionStump::getVertex() const
{
	TArray<FVector> allVertex;
	for (auto& iter : vertex)
	{
		allVertex.Append(iter.Value);
	}
	return allVertex;
}


void FCrossSectionGenerator::crossSectionVertexUnLoop(const FCrossSectionData& inCrossSection, TArray<FVector>& outVertex)
{
	FGeometryPointGenerator pointGenerator;
	FVector planNormal = inCrossSection.SectionNormal;
	pointGenerator.setNormal(planNormal);
	for (auto& iter : inCrossSection.Points)
	{
		FGeomtryPointProperty PointProperty(iter);
		PointProperty.PlanBelongs = inCrossSection.PlanBelongs;

		FVector newPointLocation(iter.PointLocation());
		pointGenerator.addPoint(newPointLocation, PointProperty);
	}
	pointGenerator.computeStruct();

	auto pointDatas = pointGenerator.getPoints();
	FGeometryLineGenerator lineGenerator;
	lineGenerator.setNormal(planNormal);
	lineGenerator.initializeLines(pointDatas, false);
	if (inCrossSection.Lines.Num() == lineGenerator.getLines().Num())
	{
		for (int32 i = 0; i < inCrossSection.Lines.Num(); ++i)
		{
			auto line = lineGenerator.getLines()[i];
			line->lineProperty.CopyData(inCrossSection.Lines[i]);
		}
	}
	else
	{
		return;
	}

	pointDatas.Sort([](TSharedPtr<FGeometryPointData> a, TSharedPtr<FGeometryPointData>b) {return a->structSort < b->structSort; });

	int32 pointCount = pointGenerator.getPointCount();

	for (int32 cur = 0; cur < pointCount - 1; ++cur)
	{
		int32 next = cur + 1;

		TArray<FVector> newVertex;
		if (pointCount == 2)
		{
			lineGenerator.getLineVertex(pointDatas[next], pointDatas[cur], 50, newVertex);
		}
		else
		{
			lineGenerator.getLineVertex(pointDatas[cur], pointDatas[next], 50, newVertex);
		}
		if (newVertex.Num() <= 0)
		{
			newVertex.Add(pointDatas[cur]->position);
		}
		for (auto& iter : newVertex)
		{
			outVertex.AddUnique(iter);
		}
		if (next == (pointCount - 1) && outVertex.Num() > 2 && newVertex.Last() != outVertex.Last())
		{
			outVertex.Add(newVertex.Last());
		}
	}
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif