#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ParameterPropertyData.h"
#include "ComponentEnumData.h"
#include "GeomtryItemData.h"
#include "CrossSectionOperandData.h"
#include "MagicCore/Public/PMCSection.h"
#include "CrossSectionDataDefine.generated.h"

class AShowSingleComponentActor;

enum ECrossSectionType
{
	EHousing	= 0,
	EAnd,
	ENot,
	EOR,
	EXOR
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCrossSectionData
{
	GENERATED_USTRUCT_BODY()
public:

	ESectionType					SectionType;
	EPlanPolygonBelongs				PlanBelongs;
	TArray<FGeomtryPointProperty>	Points;
	TArray<FGeomtryLineProperty>	Lines;
	FGeomtryRectanglePlanProperty	Rectangle;
	FGeomtryEllipsePlanProperty		Ellipse;
	FGeomtryCubeProperty			Cube;
	bool							NeedDisplay;//用于标记此截面是否生成
	bool							bGenerateGap;//用于标记此截面是否与后面的界面生成四周的包围
	bool							bCapInner;//用于标记包围面是否向内，仅挖减的界面向内
	FVector							SectionNormal;
	FPolygonData					HoleSection;
	bool							Postive;

	ECrossSectionType				Type;
	TArray<FVector>					vertexCache;
	TMap<FVector, FVector>			vertexCacheMap;

	FCrossSectionData()
		:SectionType(ESectionType::ECustomPlan)
		, PlanBelongs(EPlanPolygonBelongs::EXY_Plan)
		, NeedDisplay(true)
		, bGenerateGap(true)
		, bCapInner(false)
	{}

	bool Equal_Precise(const FCrossSectionData& InData);

	bool IsValid() const { return EPlanPolygonBelongs::EXY_Plan == PlanBelongs || EPlanPolygonBelongs::EXZ_Plan == PlanBelongs || EPlanPolygonBelongs::EYZ_Plan == PlanBelongs; }

	bool GetCrossSectionPoints(TArray<TPair<FVector, bool>>& SectionPoints) const;

	//用于生成弧形线时标定弧线方向的法线
	FVector GetSectionArcNormal() const;
	void clockwise();
	void reversal();
	static FVector GetPolygonOutsideNormal(EPlanPolygonBelongs PolygonPlan, TArray<FVector>& InPolygon);

	bool ConvertToPolygon(FPolygonData& OutPolygon) const;

	bool DrawSection(const FVector& InDrawOffset, FCrossSectionData& OutSection);

	bool ShiftSection(const TArray<float>& InShiftValue, FCrossSectionData& OutSection);

	bool ZoomSection(const TArray<float>& InZoomValue);

	bool LoftSection(FPolygonData& InLoftRoutine, const FVector& InSectionUpVector, const FVector& InRoutineNormal, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	bool TransformSection(const FSectionOperation& InOperation, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	bool SlicesAfterTransform(const FSectionOperation& InOperation, TArray<FCrossSectionData>& OutSlices);

	bool JointSlices(const FSectionOperation& InFormatOperation, TArray<FCrossSectionData>& InSlices, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	static bool GenerateJointFacesBetweenCrossSection(const FCrossSectionData& InFirstSection, const FCrossSectionData& InSecondSection, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	FBox GetBoundingBox() const;

	float GetHeight() const;

	bool IsAnotherSectionInside(const FCrossSectionData& InOtherSection) const;

	void MoveSectionTo(const float& InDeltaOffset);

	bool HasHole() const
	{
		return HoleSection.PolygonVertice.Num() >= 3;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FCrossSectionData& CrossSectionToSave)
	{
		Ar << CrossSectionToSave.SectionType;
		Ar << CrossSectionToSave.PlanBelongs;
		Ar << CrossSectionToSave.Points;
		Ar << CrossSectionToSave.Lines;
		Ar << CrossSectionToSave.Rectangle;
		Ar << CrossSectionToSave.Ellipse;
		Ar << CrossSectionToSave.Cube;
		return Ar;
	}

	bool operator!=(const FCrossSectionData& InData) const
	{
		if (SectionType != InData.SectionType || PlanBelongs != InData.PlanBelongs)
			return true;
		switch (SectionType)
		{
		case ESectionType::ECustomPlan:
		{
			if (Points.Num() != InData.Points.Num() || Lines.Num() != InData.Lines.Num())
				return true;
			int32 i = 0;
			for (auto& Iter : Points)
			{
				if (Iter != InData.Points[i++])
					return true;
			}
			i = 0;
			for (auto& Iter : Lines)
			{
				if (Iter != InData.Lines[i++])
					return true;
			}
			return false;
		}
		case ESectionType::ECube:return Cube != InData.Cube;
		case ESectionType::EEllipse:return Ellipse != InData.Ellipse;
		case ESectionType::ERectangle:return Rectangle != InData.Rectangle;
		}
		return true;
	}

	void operator=(const FCrossSectionData& InData)
	{
		SectionType		= InData.SectionType;
		PlanBelongs		= InData.PlanBelongs;
		Points			= InData.Points;
		Lines			= InData.Lines;
		Rectangle		= InData.Rectangle;
		Ellipse			= InData.Ellipse;
		Cube			= InData.Cube;
		NeedDisplay		= InData.NeedDisplay;
		bGenerateGap	= InData.bGenerateGap;
		bCapInner		= InData.bCapInner;
		SectionNormal	= InData.SectionNormal;
		HoleSection		= InData.HoleSection;
		Postive			= InData.Postive;
		Type			= InData.Type;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FImportMeshSection
{
	GENERATED_USTRUCT_BODY()
public:
	int32				ID;

	FPMCSection		SectionMesh;

	FExpressionValuePair MaterialId;

	FImportMeshSection() :ID(0), SectionMesh(FPMCSection()), MaterialId(FExpressionValuePair(TEXT(""), TEXT(""))) { }

	bool Equal_Precise(const FImportMeshSection& InData)
	{
		const bool EqualID = ID == InData.ID;
		const bool EqualMesh = SectionMesh.Equal_Precise(InData.SectionMesh);
		const bool EqualMaterial = MaterialId.Equals_Precise(InData.MaterialId);

		return EqualID && EqualMesh && EqualMaterial;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FImportMeshSection& MeshSectionToSave)
	{
		//Ar << MeshSectionToSave.SectionMesh;
		Ar << MeshSectionToSave.MaterialId;
		return Ar;
	}

	bool operator!=(const FImportMeshSection& InData) const
	{
		return SectionMesh != InData.SectionMesh ||
			MaterialId != InData.MaterialId;
	}

	void operator=(const FImportMeshSection& InData)
	{
		SectionMesh = InData.SectionMesh;
		MaterialId = InData.MaterialId;
	}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UCrossSectionDataDefine : public UObject
{
	GENERATED_BODY()

};



struct DESIGNSTATION_API FCrossSectionStump
{
public:
	//Two cross section posstive and negative
	TArray<FCrossSectionData>		crossSectons;
	TArray<FCrossSectionData>		slices;

	TMap<int32,TArray<FVector>>		vertex;

	TArray<FVector>					growPath;
	TArray<FVector>					scalePolygon;			//end polygon
	TMap<int32, TArray<FVector>>	scalePolygonMap;

	TMap<FVector, FVector>			scaleABMap;

	bool bCube = false;
	bool bShowBody = true;
	bool bOutline = false;


public:
	FCrossSectionStump()
		:crossSectons(TArray<FCrossSectionData>()),
		slices(TArray<FCrossSectionData>()),
		vertex(TMap<int32, TArray<FVector>>()),
		growPath(TArray<FVector>()),
		scalePolygon(TArray<FVector>()),
		scalePolygonMap(TMap<int32, TArray<FVector>>()),
		scaleABMap(TMap<FVector, FVector>()),
		bCube(false),
		bShowBody(true),
		bOutline(false)
	{

	}

	FCrossSectionStump(const TArray<FCrossSectionData>& inDatas)
	{
		if (inDatas.Num() == 2)
		{
			crossSectons = inDatas;
		}
	}
	TArray<FVector> getVertex() const;
};


class DESIGNSTATION_API FCrossSectionGenerator
{
public:
	FCrossSectionGenerator();

public:
	bool generateMesh(const FSectionOperation& sectionOperation,FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, AShowSingleComponentActor* calculateActor = nullptr,bool bPathUV = false);
	
	void setBaseCrossSection(const FCrossSectionData& inCrossSection);

private:
	int32 addCrossSectionStump(const FCrossSectionData& inCrossSection);
	int32 addCrossSectionStump(const FCrossSectionStump& inCrossSection);
	void crossSectionOperation(const FSectionOperation& sectionOperation);
	bool moveCrossSection(FCrossSectionData& inCrossSection,const FVector& inOffset);
	bool shiftCrossSection(FCrossSectionData& inCrossSection, const TArray<float>& inShiftValue);
	bool shiftCrossSection(FCrossSectionStump& inCrossSection, const TArray<float>& inShiftValue, TMap<FVector, FVector>& InOutOldPToNewP) const;
	

	bool cutOutCrossSection(FCrossSectionStump& inCrossSection,const float& inCutValue);

	bool loftingCrossSection(FCrossSectionStump& inCrossSection,const TArray<FVector>& inPath);

	bool  CreateMeshBetweenCrossSection(const FCrossSectionData& InFirstSection, const FCrossSectionData& InSecondSection, TMap<int32, FGeoMesh>& OutGeoMesh, TArray<FPMCSection>& OutMeshInfos);

	void process(FPMCSection& sections, TArray<TPair<FVector, FVector>>& outline, bool bPathUV = false);

	void geoMeshToSecionMesh(const FGeoMesh& inMesh, FPMCSection& sections);
	void secionMeshToGeoMesh(const FPMCSection& inSection, FGeoMesh& outMesh);

	void createHouse(const FCrossSectionStump& inStump, bool bReverse, FGeoMesh& outMesh,bool bExtension = false, bool bPathUV = false);

	void transformation(FCrossSectionData& inCrossSection,const FVector& targetNormal);

	void projectSection(const FCrossSectionData& planSection,FCrossSectionData& section);

	void generateSectionMeshFromGeoMesh(const FGeoMesh& inMesh, const FVector& InTangentX, FPMCSection& Mesh);

	void crossSectionVertex(const FCrossSectionData& inCrossSection, TArray<FVector>& outVertex);

	void crossSectionVertex(const FCrossSectionData& inCrossSection, TMap<int32,TArray<FVector>>& outVertex);

	void crossSectionVertexUnLoop(const FCrossSectionData& inCrossSection, TArray<FVector>& outVertex);

	TArray<FVector> getGrowPath();
	FVector getPathTop();
	FVector getPathDir();
protected:

	static void calculateUV(const TArray<FVector> unwrapPath, TSharedPtr<FGeoMesh> inOutMesh);
	static void refreshUVMesh(TSharedPtr<FGeoMesh> inOutMesh, AShowSingleComponentActor* inActor);

	
	TSharedPtr<FGeoMesh> uvMesh;
	AShowSingleComponentActor* editActor;
private:
	TMap<int32, FCrossSectionStump> crossSectionStumps;
	TMap<int32, FCrossSectionStump> boolStumps;
	FCrossSectionData baseCrossSection;
};