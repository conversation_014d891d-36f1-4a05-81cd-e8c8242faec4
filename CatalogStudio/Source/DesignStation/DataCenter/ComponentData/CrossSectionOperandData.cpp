// Fill out your copyright notice in the Description page of Project Settings.

#include "CrossSectionOperandData.h"
#include "CrossSectionDataDefine.h"
#include "GeometryEdit/Public/Geometry3DLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"


bool FSectionDrawOperation::operator!=(const FSectionDrawOperation& InAnother) const
{
	return DrawOffsetX != InAnother.DrawOffsetX || DrawOffsetY != InAnother.DrawOffsetY || DrawOffsetZ != InAnother.DrawOffsetZ;
}

bool FSectionDrawOperation::operator==(const FSectionDrawOperation& InAnother) const
{
	return DrawOffsetX == InAnother.DrawOffsetX && DrawOffsetY == InAnother.DrawOffsetY && DrawOffsetZ == InAnother.DrawOffsetZ;
}

bool FSectionShiftingOperation::operator!=(const FSectionShiftingOperation& InAnother) const
{
	return this->ShiftValue != InAnother.ShiftValue;
}

bool FSectionShiftingOperation::operator==(const FSectionShiftingOperation& InAnother) const
{
	return this->ShiftValue == InAnother.ShiftValue;
}

void FSectionShiftingOperation::operator=(const FSectionShiftingOperation& InAnother)
{
	ID = InAnother.ID;
	ShiftValue = InAnother.ShiftValue;
}


bool FSectionZoomOperation::operator!=(const FSectionZoomOperation& InAnother) const
{
	return this->ZoomValue != InAnother.ZoomValue;
}

bool FSectionZoomOperation::operator==(const FSectionZoomOperation& InAnother) const
{
	return this->ZoomValue == InAnother.ZoomValue;
}

void FSectionZoomOperation::operator=(const FSectionZoomOperation& InAnother)
{
	ID = InAnother.ID;
	ZoomValue = InAnother.ZoomValue;
}

bool FSectionLoftOperation::operator!=(const FSectionLoftOperation& InAnother) const
{
	if (this->PlanBelongs != InAnother.PlanBelongs || Points.Num() != InAnother.Points.Num() || Lines.Num() != InAnother.Lines.Num())
		return true;
	int32 i = 0;
	for (auto& Iter : Points)
	{
		if (Points[i] != InAnother.Points[i])
			return true;
		++i;
	}
	i = 0;
	for (auto& Iter : Lines)
	{
		if (Lines[i] != InAnother.Lines[i])
			return true;
		++i;
	}
	return false;
}

bool FSectionLoftOperation::operator==(const FSectionLoftOperation& InAnother) const
{
	return !(*this != InAnother);
}

void FSectionLoftOperation::operator=(const FSectionLoftOperation& InAnother)
{
	ID = InAnother.ID;
	PlanBelongs = InAnother.PlanBelongs;
	Points = InAnother.Points;
	Lines = InAnother.Lines;
}

FVector FSectionLoftOperation::GetRoutineNormal() const
{
	switch (PlanBelongs)
	{
	case EPlanPolygonBelongs::EUnknown:
	case EPlanPolygonBelongs::EXY_Plan: return FVector::UpVector;
	case EPlanPolygonBelongs::EYZ_Plan: return -FVector::ForwardVector;
	case EPlanPolygonBelongs::EXZ_Plan: return FVector::RightVector;
	default:return FVector::UpVector;
	}
}

FVector FSectionLoftOperation::GetRoutineForward() const
{
	switch (PlanBelongs)
	{
	case EPlanPolygonBelongs::EUnknown:
	case EPlanPolygonBelongs::EXY_Plan: return FVector::ForwardVector;
	case EPlanPolygonBelongs::EYZ_Plan: return FVector::RightVector;
	case EPlanPolygonBelongs::EXZ_Plan: return FVector::UpVector;
	default:return FVector::UpVector;
	}
}

bool FSectionLoftOperation::LoftRoutine(TArray<FVector>& OutRoutine) const
{
	if (Points.Num() <= 0)
		return true;
	OutRoutine.Add(EPositionType::EAbsolute == Points[0].PositionType ? Points[0].PointLocation() : Points[0].PointLocation() + Points[0].PrePointLocation);
	if (1 == Points.Num())return true;
	FVector RoutineNormal = GetRoutineNormal();
	if (Points.Num() > 3)
	{
		TArray<FVector> RoutinePoints;
		RoutinePoints.Init(FVector::ZeroVector, Points.Num());
		for (int32 i = 0; i < Points.Num(); ++i)
			RoutinePoints[i] = EPositionType::EAbsolute == Points[i].PositionType ? Points[i].PointLocation() : Points[i].PointLocation() + Points[i].PrePointLocation;
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(RoutinePoints, RoutineNormal);
		if (bCCWWinding) RoutineNormal *= -1.0f;
	}
	int32 Index = 0;
	for (auto& Iter : Lines)
	{
		TArray<FVector> LinePoints;
		bool Res = true;
		if (Lines.IsValidIndex(Index))
		{
			if (ELineType::EHeightArc == Lines[Index].LineType && !FMath::IsNearlyZero(FCString::Atof(*Lines[Index].RadiusOrHeightData.Value)))
			{
				const float MinmalChordLength = Lines[Index].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
				Res = FGeometry3DLibrary::GenerateArcByCamberWithoutStartAndEnd(Lines[Index].StartLocation, Lines[Index].EndLocation, RoutineNormal, Lines[Index].RadiusOrHeight(), MinmalChordLength, LinePoints);
				//Res = UGeometryRelativeLibrary::CalculateArcLineByHeight(this->PlanBelongs, Lines[Index].StartLocation, Lines[Index].EndLocation, Lines[Index].RadiusOrHeight(), Lines[Index].InterpPointCount(), LinePoints);
			}
			else if (ELineType::ERadiusArc == Lines[Index].LineType && !FMath::IsNearlyZero(FCString::Atof(*Lines[Index].RadiusOrHeightData.Value)))
			{
				const float MinmalChordLength = Lines[Index].RadiusOrHeight() < 1.0f ? 0.1f : 0.5f;
				Res = FGeometry3DLibrary::GenerateArcByRadiusWithoutStartAndEnd(Lines[Index].StartLocation, Lines[Index].EndLocation, RoutineNormal, Lines[Index].RadiusOrHeight(), Lines[Index].BigArc, MinmalChordLength, LinePoints);
				//Res = UGeometryRelativeLibrary::CalculateArcLineByRadius(this->PlanBelongs, Lines[Index].StartLocation, Lines[Index].EndLocation, Lines[Index].RadiusOrHeight(), Lines[Index].InterpPointCount(), LinePoints, !Lines[Index].BigArc);
			}
		}
		if (!Res)return false;
		OutRoutine.Append(LinePoints);
		++Index;
		OutRoutine.Add(EPositionType::EAbsolute == Points[Index].PositionType ? Points[Index].PointLocation() : Points[Index].PointLocation() + Points[Index].PrePointLocation);
	}
	return true;
}

bool FSectionLoftOperation::LoftRoutine(FPolygonData& OutRoutine) const
{
	OutRoutine.PolygonPlan = PlanBelongs;
	OutRoutine.PolygonVerticesOrder = EPolygonVerticesOrder::EUnknown;
	return this->LoftRoutine(OutRoutine.PolygonVertice);
}

bool FSectionLoftOperation::ConvertToCrossSection(FCrossSectionData& OutSection) const
{
	OutSection.PlanBelongs = this->PlanBelongs;

	OutSection.Points = this->Points;
	OutSection.Lines = this->Lines;
	return true;
}

bool FSectionCutOutOperation::operator!=(const FSectionCutOutOperation& InAnother) const
{
	if (this->CutOutValue != InAnother.CutOutValue || this->SectionType != InAnother.SectionType || this->PlanBelongs != InAnother.PlanBelongs)
		return true;
	if (ESectionType::ECustomPlan == this->SectionType)
	{
		return this->Points != InAnother.Points || this->Lines != InAnother.Lines;
	}
	else if (ESectionType::ERectangle == this->SectionType)
	{
		return this->Rectangle != InAnother.Rectangle;
	}
	else if (ESectionType::EEllipse == this->SectionType)
	{
		return this->Ellipse != InAnother.Ellipse;
	}
	return false;
}

bool FSectionCutOutOperation::operator==(const FSectionCutOutOperation& InAnother) const
{
	return !(*this != InAnother);
}

void FSectionCutOutOperation::operator=(const FSectionCutOutOperation& InAnother)
{
	ID = InAnother.ID;
	CutOutValue = InAnother.CutOutValue;
	this->SectionType = InAnother.SectionType;
	this->PlanBelongs = InAnother.PlanBelongs;
	if (ESectionType::ECustomPlan == this->SectionType)
	{
		this->Points = InAnother.Points;
		this->Lines = InAnother.Lines;
	}
	else if (ESectionType::ERectangle == this->SectionType)
	{
		this->Rectangle = InAnother.Rectangle;
	}
	else if (ESectionType::EEllipse == this->SectionType)
	{
		this->Ellipse = InAnother.Ellipse;
	}
}

bool FSectionCutOutOperation::IsEmpty() const
{
	if (ESectionType::ECustomPlan == SectionType)
	{
		return Points.Num() <= 0 && Lines.Num() <= 0;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		return EPlanPolygonBelongs::EUnknown == this->PlanBelongs;
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		return EPlanPolygonBelongs::EUnknown == this->PlanBelongs;
	}
	return true;
}

bool FSectionCutOutOperation::ConvertToCrossSection(FCrossSectionData& OutSection) const
{
	OutSection.PlanBelongs = this->PlanBelongs;
	OutSection.SectionType = this->SectionType;
	if (ESectionType::ECustomPlan == SectionType)
	{
		OutSection.Points = this->Points;
		OutSection.Lines = this->Lines;
		return true;
	}
	else if (ESectionType::ERectangle == SectionType)
	{
		OutSection.Rectangle = this->Rectangle;
		return true;
	}
	else if (ESectionType::EEllipse == SectionType)
	{
		OutSection.Ellipse = this->Ellipse;
		return true;
	}
	return false;
}

bool FSectionOperationOrder::operator!=(const FSectionOperationOrder& InAnother) const
{
	return OperatorType != InAnother.OperatorType || Index != InAnother.Index;
}

bool FSectionOperationOrder::operator==(const FSectionOperationOrder& InAnother) const
{
	return OperatorType == InAnother.OperatorType && Index == InAnother.Index;
}

void FSectionOperationOrder::operator=(const FSectionOperationOrder& InData)
{
	Index = InData.Index;
	OperatorType = InData.OperatorType;
}

bool FSectionOperation::operator!=(const FSectionOperation& InOtherOperation) const
{
	return !(this->operator==(InOtherOperation));
}

bool FSectionOperation::operator==(const FSectionOperation& InOtherOperation) const
{
	if (OperatorOrder.Num() != InOtherOperation.OperatorOrder.Num())
		return false;
	for (int32 i = 0; i < OperatorOrder.Num(); ++i)
	{
		if (OperatorOrder[i] != InOtherOperation.OperatorOrder[i])
			return false;
		if (ESectionOperationType::EDrawSection == OperatorOrder[i].OperatorType)
		{
			if (!DrawOperations.IsValidIndex(OperatorOrder[i].Index) || !InOtherOperation.DrawOperations.IsValidIndex(OperatorOrder[i].Index))
				return false;
			if (DrawOperations[OperatorOrder[i].Index] != InOtherOperation.DrawOperations[OperatorOrder[i].Index])
				return false;
		}
		else if (ESectionOperationType::EShiftSection == OperatorOrder[i].OperatorType)
		{
			if (!ShiftOperations.IsValidIndex(OperatorOrder[i].Index) || !InOtherOperation.ShiftOperations.IsValidIndex(OperatorOrder[i].Index))
				return false;
			if (ShiftOperations[OperatorOrder[i].Index] != InOtherOperation.ShiftOperations[OperatorOrder[i].Index])
				return false;
		}
		else if (ESectionOperationType::EZoomSection == OperatorOrder[i].OperatorType)
		{
			if (!ZoomOperations.IsValidIndex(OperatorOrder[i].Index) || !InOtherOperation.ZoomOperations.IsValidIndex(OperatorOrder[i].Index))
				return false;
			if (ZoomOperations[OperatorOrder[i].Index] != InOtherOperation.ZoomOperations[OperatorOrder[i].Index])
				return false;
		}
		else if (ESectionOperationType::ECutoutSection == OperatorOrder[i].OperatorType)
		{
			if (!CutoutOperations.IsValidIndex(OperatorOrder[i].Index) || !InOtherOperation.CutoutOperations.IsValidIndex(OperatorOrder[i].Index))
				return false;
			if (CutoutOperations[OperatorOrder[i].Index] != InOtherOperation.CutoutOperations[OperatorOrder[i].Index])
				return false;
		}
		else if (ESectionOperationType::ELoftingSection == OperatorOrder[i].OperatorType)
		{
			if (LoftingOperation != InOtherOperation.LoftingOperation)
				return false;
		}
	}
	return true;
}

bool FSectionOperation::IsValidOperationOrder(const FSectionOperationOrder& InOrder) const
{
	if (OperatorOrder.Num() <= 0)
		return false;
	switch (InOrder.OperatorType)
	{
	case ESectionOperationType::ECutoutSection: return CutoutOperations.IsValidIndex(InOrder.Index);
	case ESectionOperationType::EDrawSection: return DrawOperations.IsValidIndex(InOrder.Index);
	case ESectionOperationType::EShiftSection: return ShiftOperations.IsValidIndex(InOrder.Index);
	case ESectionOperationType::EZoomSection: return ZoomOperations.IsValidIndex(InOrder.Index);
	case ESectionOperationType::ELoftingSection: return 0 == InOrder.Index;
	default:return false;
	}
}

bool FSectionOperation::RefreshOperationID(FSectionOperation& InOutOperations)
{
	int32 DrawOffset = 0;
	int32 ShiftOffset = 0;
	int32 ZoomOffset = 0;
	int32 CutoutOffset = 0;
	for (int32 i = 0; i < InOutOperations.OperatorOrder.Num(); ++i)
	{
		if (ESectionOperationType::EDrawSection == InOutOperations.OperatorOrder[i].OperatorType)
		{
			InOutOperations.OperatorOrder[i].Index = DrawOffset;
			InOutOperations.DrawOperations[DrawOffset].ID = i;
			++DrawOffset;
		}
		else if (ESectionOperationType::EShiftSection == InOutOperations.OperatorOrder[i].OperatorType)
		{
			InOutOperations.OperatorOrder[i].Index = ShiftOffset;
			InOutOperations.ShiftOperations[ShiftOffset].ID = i;
			++ShiftOffset;
		}
		else if (ESectionOperationType::EZoomSection == InOutOperations.OperatorOrder[i].OperatorType)
		{
			InOutOperations.OperatorOrder[i].Index = ZoomOffset;
			InOutOperations.ZoomOperations[ZoomOffset].ID = i;
			++ZoomOffset;
		}
		else if (ESectionOperationType::ECutoutSection == InOutOperations.OperatorOrder[i].OperatorType)
		{
			InOutOperations.OperatorOrder[i].Index = CutoutOffset;
			InOutOperations.CutoutOperations[CutoutOffset].ID = i;
			++CutoutOffset;
		}
		else if (ESectionOperationType::ELoftingSection == InOutOperations.OperatorOrder[i].OperatorType)
		{
			InOutOperations.LoftingOperation.ID = 0;
			for (int32 j = 0; j < InOutOperations.LoftingOperation.Points.Num(); ++j)
			{
				InOutOperations.LoftingOperation.Points[j].ID = j;
			}
			for (int32 j = 0; j < InOutOperations.LoftingOperation.Lines.Num(); ++j)
			{
				InOutOperations.LoftingOperation.Lines[j].ID = j;
			}
		}
	}
	return true;
}

bool FSectionOperation::DeleteOperation(const int32& InOperationIndex)
{
	if (OperatorOrder.IsValidIndex(InOperationIndex))
	{
		FSectionOperationOrder OperationToDelete = OperatorOrder[InOperationIndex];
		OperatorOrder.RemoveAt(InOperationIndex);
		FSectionOperation::RefreshOperationID(*this);
		switch (OperationToDelete.OperatorType)
		{
		case ESectionOperationType::EDrawSection:DrawOperations.RemoveAt(OperationToDelete.Index); break;
		case ESectionOperationType::EShiftSection:ShiftOperations.RemoveAt(OperationToDelete.Index); break;
		case ESectionOperationType::EZoomSection:ZoomOperations.RemoveAt(OperationToDelete.Index); break;
		case ESectionOperationType::ECutoutSection:CutoutOperations.RemoveAt(OperationToDelete.Index); break;
		case ESectionOperationType::ELoftingSection:LoftingOperation.Empty(); break;
		}
		return true;
	}
	return false;
}

bool FSectionOperation::MoveOperationUp(const int32& InOperationIndex)
{
	return SwapTwoOperation(InOperationIndex, InOperationIndex - 1);
}

bool FSectionOperation::MoveOperationDown(const int32& InOperationIndex)
{
	return SwapTwoOperation(InOperationIndex, InOperationIndex + 1);
}

bool FSectionOperation::SwapTwoOperation(const int32& First, const int32& Second)
{
	if (OperatorOrder.IsValidIndex(First) && OperatorOrder.IsValidIndex(Second))
	{
		this->OperatorOrder.Swap(First, Second);
		if (OperatorOrder[First].OperatorType == OperatorOrder[Second].OperatorType)
		{
			switch (OperatorOrder[First].OperatorType)
			{
			case ESectionOperationType::EDrawSection:DrawOperations.Swap(OperatorOrder[First].Index, OperatorOrder[Second].Index); break;
			case ESectionOperationType::EShiftSection:ShiftOperations.Swap(OperatorOrder[First].Index, OperatorOrder[Second].Index); break;
			case ESectionOperationType::EZoomSection:ZoomOperations.Swap(OperatorOrder[First].Index, OperatorOrder[Second].Index); break;
			case ESectionOperationType::ECutoutSection:CutoutOperations.Swap(OperatorOrder[First].Index, OperatorOrder[Second].Index); break;
			default:
				break;
			}
		}
		return true;
	}
	return false;
}

void FSectionOperation::FormatSectionOperation(FSectionOperation& FormatOperation) const
{
	FormatOperation.Empty();
	for (auto& Operation : OperatorOrder)
	{
		switch (Operation.OperatorType)
		{
		case ESectionOperationType::EDrawSection:
		{
			if (DrawOperations.IsValidIndex(Operation.Index))
			{
				if (DrawOperations[Operation.Index].DrawOffset().IsNearlyZero(THRESH_SPLIT_POLY_PRECISELY)) continue;
				const int32 Index = FormatOperation.DrawOperations.Add(DrawOperations[Operation.Index]);
				FormatOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EDrawSection, Index));
			}
			break;
		}
		case ESectionOperationType::EShiftSection:
		{
			if (ShiftOperations.IsValidIndex(Operation.Index))
			{
				TArray<float> ShiftValue;
				ShiftOperations[Operation.Index].ShiftValues(ShiftValue);
				uint32 ZeroCount = 0;
				for (auto& ShiftIter : ShiftValue)
					if (FMath::IsNearlyZero(ShiftIter, THRESH_SPLIT_POLY_PRECISELY)) ++ZeroCount;
				if (ZeroCount == ShiftValue.Num()) continue;
				const int32 Index = FormatOperation.ShiftOperations.Add(ShiftOperations[Operation.Index]);
				FormatOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EShiftSection, Index));
			}
			break;
		}
		case ESectionOperationType::EZoomSection:
		{
			if (ZoomOperations.IsValidIndex(Operation.Index))
			{
				TArray<float> ZoomValue;
				ZoomOperations[Operation.Index].ScaleValues(ZoomValue);
				uint32 ZeroCount = 0;
				for (auto& ShiftIter : ZoomValue)
					if (FMath::IsNearlyZero(ShiftIter, THRESH_SPLIT_POLY_PRECISELY)) ++ZeroCount;
				if (ZeroCount == ZoomValue.Num()) continue;
				const int32 Index = FormatOperation.ZoomOperations.Add(ZoomOperations[Operation.Index]);
				FormatOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::EZoomSection, Index));
			}
			break;
		}
		case ESectionOperationType::ECutoutSection:
		{
			if (CutoutOperations.IsValidIndex(Operation.Index))
			{
				const float CutoutValue = CutoutOperations[Operation.Index].CutoutValue();
				if (FMath::IsNearlyZero(CutoutValue, THRESH_SPLIT_POLY_PRECISELY)) continue;
				const int32 Index = FormatOperation.CutoutOperations.Add(CutoutOperations[Operation.Index]);
				FormatOperation.OperatorOrder.Add(FSectionOperationOrder(ESectionOperationType::ECutoutSection, Index));
			}
			break;
		}
		case ESectionOperationType::ELoftingSection:
		{
			FormatOperation.LoftingOperation = LoftingOperation;
			break;
		}
		case ESectionOperationType::ERotatorSection:
		default:
			break;
		}
	}
}
