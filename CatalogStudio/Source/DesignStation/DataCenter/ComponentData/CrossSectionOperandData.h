// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ParameterPropertyData.h"
#include "ComponentEnumData.h"
#include "GeomtryItemData.h"
#include "CrossSectionOperandData.generated.h"

struct FCrossSectionData;

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionDrawOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		FExpressionValuePair DrawOffsetX;
	UPROPERTY()
		FExpressionValuePair DrawOffsetY;
	UPROPERTY()
		FExpressionValuePair DrawOffsetZ;

public:

	bool Equal_Precise(const FSectionDrawOperation& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		const bool EqualOffsetX = DrawOffsetX.Equals_Precise(InAnother.DrawOffsetX);
		const bool EqualOffsetY = DrawOffsetY.Equals_Precise(InAnother.DrawOffsetY);
		const bool EqualOffsetZ = DrawOffsetZ.Equals_Precise(InAnother.DrawOffsetZ);

		return EqualID && EqualOffsetX && EqualOffsetY && EqualOffsetZ;
	}


	bool operator!=(const FSectionDrawOperation& InAnother) const;

	bool operator==(const FSectionDrawOperation& InAnother) const;

	FVector DrawOffset() const
	{
		return UParameterPropertyData::ConvertToUeValue(FVector(FCString::Atof(*DrawOffsetX.Value), FCString::Atof(*DrawOffsetY.Value), FCString::Atof(*DrawOffsetZ.Value)));
	}

	FSectionDrawOperation() :ID(-1), DrawOffsetX(FExpressionValuePair(0.0f)), DrawOffsetY(FExpressionValuePair(0.0f)), DrawOffsetZ(FExpressionValuePair(0.0f)) {}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionDrawOperation& SectionDrawOperationToSave)
	{
		Ar << SectionDrawOperationToSave.ID;
		Ar << SectionDrawOperationToSave.DrawOffsetX;
		Ar << SectionDrawOperationToSave.DrawOffsetY;
		Ar << SectionDrawOperationToSave.DrawOffsetZ;
		return Ar;
	}

	void CopyData(const FSectionDrawOperation& InData)
	{
		ID = InData.ID;
		DrawOffsetX.CopyData(InData.DrawOffsetX);
		DrawOffsetY.CopyData(InData.DrawOffsetY);
		DrawOffsetZ.CopyData(InData.DrawOffsetZ);
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionShiftingOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		TArray<FExpressionValuePair> ShiftValue;

public:

	bool Equal_Precise(const FSectionShiftingOperation& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		bool EqualShiftValue = ShiftValue.Num() == InAnother.ShiftValue.Num();
		if (EqualShiftValue)
		{
			for (int32 i = 0; i < ShiftValue.Num(); ++i)
			{
				if (!ShiftValue[i].Equals_Precise(InAnother.ShiftValue[i]))
				{
					EqualShiftValue = false;
					break;
				}
			}
		}

		return EqualID && EqualShiftValue;
	}


	bool operator!=(const FSectionShiftingOperation& InAnother) const;

	bool operator==(const FSectionShiftingOperation& InAnother) const;

	void operator=(const FSectionShiftingOperation& InAnother);

	FSectionShiftingOperation() :ID(-1) { ShiftValue.Add(FExpressionValuePair(0.0f)); }

	bool ShiftValues(TArray<float>& OutShiftValues) const
	{
		if (0 == ShiftValue.Num()) return false;
		OutShiftValues.AddZeroed(ShiftValue.Num());
		for (int32 i = 0; i < ShiftValue.Num(); ++i)
		{
			OutShiftValues[i] = GetShiftValueByIndex(i);
		}
		return true;
	}

	static bool FormatShiftValue(const TArray<float>& InShiftValue, TArray<float>& OutShiftValue, const int32& InCountNeeded)
	{
		if (InCountNeeded < 1)
			return false;
		OutShiftValue.AddZeroed(InCountNeeded);
		if (1 == InShiftValue.Num())
		{
			for (int32 i = 0; i < OutShiftValue.Num(); ++i)
				OutShiftValue[i] = InShiftValue[0];
		}
		else if (1 < InShiftValue.Num())
		{
			for (int32 i = 0; i < InShiftValue.Num() && i < OutShiftValue.Num(); ++i)
				OutShiftValue[i] = InShiftValue[i];
		}
		return true;
	}

	float GetShiftValueByIndex(const int32& InIndex) const
	{
		return ShiftValue.IsValidIndex(InIndex) && ShiftValue[InIndex].Value.IsNumeric() ? UParameterPropertyData::ConvertToUeValue(FCString::Atof(*ShiftValue[InIndex].Value)) : 0.0f;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionShiftingOperation& SectionShiftOperationToSave)
	{
		Ar << SectionShiftOperationToSave.ID;
		Ar << SectionShiftOperationToSave.ShiftValue;
		return Ar;
	}

	void CopyData(const FSectionShiftingOperation& InData)
	{
		ID = InData.ID;
		ShiftValue.Empty();
		for (auto& ShiftData : InData.ShiftValue)
		{
			ShiftValue.Add(ShiftData);
		}
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionZoomOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		TArray<FExpressionValuePair> ZoomValue;

public:

	bool Equal_Precise(const FSectionZoomOperation& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		bool EqualZoomValue = ZoomValue.Num() == InAnother.ZoomValue.Num();
		if (EqualZoomValue)
		{
			for (int32 i = 0; i < ZoomValue.Num(); ++i)
			{
				if (!ZoomValue[i].Equals_Precise(InAnother.ZoomValue[i]))
				{
					EqualZoomValue = false;
					break;
				}
			}
		}

		return EqualID && EqualZoomValue;
	}

	bool operator!=(const FSectionZoomOperation& InAnother) const;

	bool operator==(const FSectionZoomOperation& InAnother) const;

	void operator=(const FSectionZoomOperation& InAnother);

	FSectionZoomOperation() :ID(-1) { ZoomValue.Add(FExpressionValuePair(0.0f)); }

	bool ScaleValues(TArray<float>& OutShiftValues) const
	{
		if (0 == ZoomValue.Num()) return false;
		OutShiftValues.AddZeroed(ZoomValue.Num());
		for (int32 i = 0; i < ZoomValue.Num(); ++i)
		{
			OutShiftValues[i] = GetScaleValueByIndex(i);
		}
		return true;
	}

	float GetScaleValueByIndex(const int32& InIndex) const
	{
		return ZoomValue.IsValidIndex(InIndex) && ZoomValue[InIndex].Value.IsNumeric() ? UParameterPropertyData::ConvertToUeValue(FCString::Atof(*ZoomValue[InIndex].Value)) : 0.0f;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionZoomOperation& SectionZoomOperationToSave)
	{
		Ar << SectionZoomOperationToSave.ID;
		Ar << SectionZoomOperationToSave.ZoomValue;
		return Ar;
	}

	void CopyData(const FSectionZoomOperation& InData)
	{
		ID = InData.ID;
		ZoomValue.Empty();
		for (auto& ScaleData : InData.ZoomValue)
		{
			ZoomValue.Add(ScaleData);
		}
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionLoftOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		EPlanPolygonBelongs PlanBelongs;
	UPROPERTY()
		TArray<FGeomtryPointProperty>	Points;
	UPROPERTY()
		TArray<FGeomtryLineProperty>	Lines;

public:
	bool Equal_Precise(const FSectionLoftOperation& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		bool EqualPoints = Points.Num() == InAnother.Points.Num();
		if (EqualPoints)
		{
			for (int32 i = 0; i < Points.Num(); ++i)
			{
				if (!Points[i].Equal_Precise(InAnother.Points[i]))
				{
					EqualPoints = false;
					break;
				}
			}
		}

		bool EqualLines = Lines.Num() == InAnother.Lines.Num();
		if (EqualLines)
		{
			for (int32 i = 0; i < Lines.Num(); ++i)
			{
				if (!Lines[i].Equal_Precise(InAnother.Lines[i]))
				{
					EqualLines = false;
					break;
				}
			}
		}

		return EqualID && EqualPlanBelongs && EqualPoints && EqualLines;
	}


	bool operator!=(const FSectionLoftOperation& InAnother) const;

	bool operator==(const FSectionLoftOperation& InAnother) const;

	void operator=(const FSectionLoftOperation& InAnother);

	bool LoftRoutine(TArray<FVector>& OutRoutine) const;

	bool LoftRoutine(FPolygonData& OutRoutine) const;

	bool ConvertToCrossSection(FCrossSectionData& OutSection) const;

	FVector GetRoutineNormal() const;

	FVector GetRoutineForward() const;

	FSectionLoftOperation() :ID(-1) {}

	void Empty()
	{
		PlanBelongs = EPlanPolygonBelongs::EUnknown;
		Points.Empty();
		Lines.Empty();
	}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionLoftOperation& SectionLoftOperationToSave)
	{
		Ar << SectionLoftOperationToSave.PlanBelongs;
		Ar << SectionLoftOperationToSave.Points;
		Ar << SectionLoftOperationToSave.Lines;
		return Ar;
	}

	void CopyData(const FSectionLoftOperation& InData)
	{
		ID = InData.ID;
		Points = InData.Points;
		Lines = InData.Lines;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionCutOutOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		FExpressionValuePair CutOutValue;
	UPROPERTY()
		ESectionType SectionType;
	UPROPERTY()
		EPlanPolygonBelongs PlanBelongs;
	UPROPERTY()
		TArray<FGeomtryPointProperty>	Points;
	UPROPERTY()
		TArray<FGeomtryLineProperty>	Lines;
	UPROPERTY()
		FGeomtryRectanglePlanProperty	Rectangle;
	UPROPERTY()
		FGeomtryEllipsePlanProperty		Ellipse;

public:

	bool Equal_Precise(const FSectionCutOutOperation& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		const bool EqualCutOutValue = CutOutValue.Equals_Precise(InAnother.CutOutValue);
		const bool EqualSectionType = SectionType == InAnother.SectionType;
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		bool EqualPoints = Points.Num() == InAnother.Points.Num();
		if (EqualPoints)
		{
			for (int32 i = 0; i < Points.Num(); ++i)
			{
				if (!Points[i].Equal_Precise(InAnother.Points[i]))
				{
					EqualPoints = false;
					break;
				}
			}
		}

		bool EqualLines = Lines.Num() == InAnother.Lines.Num();
		if (EqualLines)
		{
			for (int32 i = 0; i < Lines.Num(); ++i)
			{
				if (!Lines[i].Equal_Precise(InAnother.Lines[i]))
				{
					EqualLines = false;
					break;
				}
			}
		}

		const bool EqualRectangle = Rectangle.Equal_Precise(InAnother.Rectangle);
		const bool EqualEllipse = Ellipse.Equal_Precise(InAnother.Ellipse);

		return EqualID && EqualCutOutValue && EqualSectionType && EqualPlanBelongs && EqualPoints && EqualLines && EqualRectangle && EqualEllipse;
	}

	bool operator!=(const FSectionCutOutOperation& InAnother) const;

	bool operator==(const FSectionCutOutOperation& InAnother) const;

	void operator=(const FSectionCutOutOperation& InAnother);

	FSectionCutOutOperation()
		:ID(-1)
		, CutOutValue(FExpressionValuePair(0.0f))
		, SectionType(ESectionType::ECustomPlan)
		, PlanBelongs(EPlanPolygonBelongs::EUnknown)
	{}

	bool IsEmpty() const;

	bool ConvertToCrossSection(FCrossSectionData& OutSection) const;

	float CutoutValue() const
	{
		return UParameterPropertyData::ConvertToUeValue(FCString::Atof(*CutOutValue.Value));
	}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionCutOutOperation& SectionCutOutOperationToSave)
	{
		Ar << SectionCutOutOperationToSave.ID;
		Ar << SectionCutOutOperationToSave.CutOutValue;
		Ar << SectionCutOutOperationToSave.SectionType;
		Ar << SectionCutOutOperationToSave.PlanBelongs;
		Ar << SectionCutOutOperationToSave.Points;
		Ar << SectionCutOutOperationToSave.Lines;
		Ar << SectionCutOutOperationToSave.Rectangle;
		Ar << SectionCutOutOperationToSave.Ellipse;
		return Ar;
	}

	void CopyData(const FSectionCutOutOperation& InData)
	{
		ID = InData.ID;
		CutOutValue = InData.CutOutValue;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionOperationOrder
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 Index;
	UPROPERTY()
		ESectionOperationType OperatorType;

public:

	bool operator!=(const FSectionOperationOrder& InAnother) const;
	bool operator==(const FSectionOperationOrder& InAnother) const;
	void operator=(const FSectionOperationOrder& InData);

	FSectionOperationOrder() :Index(-1), OperatorType(ESectionOperationType::EDrawSection) {}
	FSectionOperationOrder(const ESectionOperationType& InOperationType, const int32& InIndex) :Index(InIndex), OperatorType(InOperationType) {}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionOperationOrder& OperatorTypeToSave)
	{
		Ar << OperatorTypeToSave.Index;
		Ar << OperatorTypeToSave.OperatorType;
		return Ar;
	}

	void CopyData(const FSectionOperationOrder& InData)
	{
		Index = InData.Index;
		OperatorType = InData.OperatorType;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSectionOperation
{
	GENERATED_USTRUCT_BODY()
public:
	TArray<FSectionOperationOrder> OperatorOrder;
	TArray<FSectionDrawOperation> DrawOperations;
	TArray<FSectionShiftingOperation> ShiftOperations;
	TArray<FSectionZoomOperation> ZoomOperations;
	TArray<FSectionCutOutOperation> CutoutOperations;
	FSectionLoftOperation LoftingOperation;

public:

	FSectionOperation() {}

	bool Equals_Precise(const FSectionOperation& InData)
	{
		bool EqualOpeOrder = OperatorOrder.Num() == InData.OperatorOrder.Num();
		if (EqualOpeOrder)
		{
			for (int32 i = 0; i < OperatorOrder.Num(); ++i)
			{
				if (OperatorOrder[i] != InData.OperatorOrder[i])
				{
					EqualOpeOrder = false;
					break;
				}
			}
		}

		bool EqualDrawOpe = DrawOperations.Num() == InData.DrawOperations.Num();
		if (EqualDrawOpe)
		{
			for (int32 i = 0; i < DrawOperations.Num(); ++i)
			{
				if (!DrawOperations[i].Equal_Precise(InData.DrawOperations[i]))
				{
					EqualDrawOpe = false;
					break;
				}
			}
		}

		bool EqualShiftOpe = ShiftOperations.Num() == InData.ShiftOperations.Num();
		if (EqualShiftOpe)
		{
			for (int32 i = 0; i < ShiftOperations.Num(); ++i)
			{
				if (!ShiftOperations[i].Equal_Precise(InData.ShiftOperations[i]))
				{
					EqualShiftOpe = false;
					break;
				}
			}
		}

		bool EqualZoomOpe = ZoomOperations.Num() == InData.ZoomOperations.Num();
		if(EqualZoomOpe)
		{
			for (int32 i = 0; i < ZoomOperations.Num(); ++i)
			{
				if (!ZoomOperations[i].Equal_Precise(InData.ZoomOperations[i]))
				{
					EqualZoomOpe = false;
					break;
				}
			}
		}

		bool EqualCutoutOpe = CutoutOperations.Num() == InData.CutoutOperations.Num();
		if (EqualCutoutOpe)
		{
			for (int32 i = 0; i < CutoutOperations.Num(); ++i)
			{
				if (!CutoutOperations[i].Equal_Precise(InData.CutoutOperations[i]))
				{
					EqualCutoutOpe = false;
					break;
				}
			}
		}

		const bool EqualLoftOpe = LoftingOperation.Equal_Precise(InData.LoftingOperation);

		return EqualOpeOrder && EqualDrawOpe && EqualShiftOpe && EqualZoomOpe && EqualCutoutOpe && EqualLoftOpe;
	}

	bool operator!=(const FSectionOperation& InOtherOperation) const;

	bool operator==(const FSectionOperation& InOtherOperation) const;

	bool IsValidOperationOrder(const FSectionOperationOrder& InOrder) const;

	friend FArchive& operator<<(FArchive& Ar, struct FSectionOperation& SectionOperationToSave)
	{
		Ar << SectionOperationToSave.OperatorOrder;
		Ar << SectionOperationToSave.DrawOperations;
		Ar << SectionOperationToSave.ShiftOperations;
		Ar << SectionOperationToSave.ZoomOperations;
		Ar << SectionOperationToSave.CutoutOperations;
		Ar << SectionOperationToSave.LoftingOperation;
		return Ar;
	}

	void Empty()
	{
		OperatorOrder.Empty();
		DrawOperations.Empty();
		ShiftOperations.Empty();
		ZoomOperations.Empty();
		CutoutOperations.Empty();
		LoftingOperation.Points.Empty();
		LoftingOperation.Lines.Empty();
	}

	static bool RefreshOperationID(FSectionOperation& InOutOperations);

	bool DeleteOperation(const int32& InOperationIndex);

	bool MoveOperationUp(const int32& InOperationIndex);

	bool MoveOperationDown(const int32& InOperationIndex);

	bool SwapTwoOperation(const int32& First, const int32& Second);

	//格式化操作，去除不合法的操作或者操作之后没有影响的操作，例如拉伸的长度为0、偏移或缩放的值为0、挖剪的值为0
	void FormatSectionOperation(FSectionOperation& FormatOperation) const;

	void CopyData(const FSectionOperation& InData)
	{
		OperatorOrder.Empty();
		DrawOperations.Empty();
		for (auto& OrderData : InData.OperatorOrder)
		{
			OperatorOrder.Add(OrderData);
		}
		for (auto& DrawData : InData.DrawOperations)
		{
			DrawOperations.Add(DrawData);
		}
		LoftingOperation = InData.LoftingOperation;
	}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UCrossSectionOperandData : public UObject
{
	GENERATED_BODY()




};
