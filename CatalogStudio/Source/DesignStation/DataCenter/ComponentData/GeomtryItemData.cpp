// Fill out your copyright notice in the Description page of Project Settings.

#include "GeomtryItemData.h"

#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DataCenter/Libraries/GeomtryMathmaticLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "GeometryEdit/Public/GeometryEditLibrary.h"
#include "GeometryEdit/Public/Geometry3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "GeometricCalculate/Library/GeometryLibrary.h"

bool FPolygonData::GenerateMesh(FPMCSection& OutMeshInfo)
{
	bool Res = true;
	
	auto Polygon = PolygonVertice;
	FPolygon3DLibrary::RemoveConcurrentPoints(Polygon, true);
	TArray<int32> Triangles;
	TArray<FVector> OutPolygon2D;
	//FPolygon3DLibrary::TriangulatePolygon2D(Polygon, NSPlanType::GetPlanNormal(PolygonPlan),OutPolygon2D, Triangles);
	FGeoMesh geoMesh;
	FGeometryLibrary::createPlanarPolygonMesh(Polygon, NSPlanType::GetPlanNormal(PolygonPlan), geoMesh);

	FPMCSection MeshInfo;
	//fromGeoMeshToPMCSection(geoMesh, MeshInfo);

	FGeometryEditLibrary::GenerateMeshFromTriangles(geoMesh.vertexs, geoMesh.indices, NSPlanType::GetPlanNormal(PolygonPlan), NSPlanType::GetPlanTangentX(PolygonPlan), MeshInfo);
	OutMeshInfo += MeshInfo;
	return true;
}

void FPolygonData::fromGeoMeshToPMCSection(const FGeoMesh& inMesh, FPMCSection& outMesh)
{

	outMesh.Vertexes = inMesh.vertexs;
	outMesh.Triangles = inMesh.indices;
	outMesh.Normals.SetNumZeroed(inMesh.normals.Num());
	for (int32 i = 0; i < inMesh.normals.Num(); ++i)
	{
		outMesh.Normals[i].X = inMesh.normals[i].X;
		outMesh.Normals[i].Y = inMesh.normals[i].Y;
		outMesh.Normals[i].Z = inMesh.normals[i].Z;
	}
	outMesh.UV.SetNumZeroed(inMesh.uv.Num());

	for (int32 i = 0; i < inMesh.uv.Num(); ++i)
	{
		outMesh.UV[i].X = inMesh.uv[i].X;
		outMesh.UV[i].Y = inMesh.uv[i].Y;
	}
}
bool FPolygonData::GenerateOutlineFramwork(TArray<TPair<FVector, FVector>>& OutFramework)
{
	if (PolygonVertice.Num() <= 1)
		return false;
	int32 VerticeCount = PolygonVertice.Num();
	int32 Offset = OutFramework.AddZeroed(VerticeCount);
	for (int32 i = 0; i < VerticeCount; ++i)
	{
		OutFramework[Offset + i] = TPair<FVector, FVector>(PolygonVertice[i % VerticeCount], PolygonVertice[(i + 1) % VerticeCount]);
	}
	return true;
}

FVector FPolygonData::GetSectionArcNormal() const
{
	FVector PlanNormal = NSPlanType::GetPlanNormal(PolygonPlan);
	auto Polygon = PolygonVertice;
	FPolygon3DLibrary::RemoveConcurrentPoints(Polygon, true);
	if (PolygonVertice.Num() >= 3)
	{//当前多边形为顺时针需要把法线方向反转
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(Polygon, PlanNormal);
		if (bCCWWinding) PlanNormal *= -1.0f;
	}
	return PlanNormal;
}

FGeomtryLineProperty::FGeomtryLineProperty(const ELineType& InLineType, const EPlanPolygonBelongs& InPlanType, const FExpressionValuePair& InRadiusOrHeight, const FExpressionValuePair& InPointCount, const FVector& InStartLocation, const FVector& InEndLocation)
	: ID(-1)
	, LineType(InLineType)
	, PlanBelongs(InPlanType)
	, RadiusOrHeightData(InRadiusOrHeight)
	, InterpPointCountData(InPointCount)
	, StartLocation(InStartLocation)
	, EndLocation(InEndLocation)
	, BigArc(false)
{
}

bool FGeomtryLineProperty::operator!=(const FGeomtryLineProperty& InAnother) const
{
	return LineType != InAnother.LineType || RadiusOrHeightData != InAnother.RadiusOrHeightData || InterpPointCountData != InAnother.InterpPointCountData || !StartLocation.Equals(InAnother.StartLocation,0.001f) || !EndLocation.Equals(InAnother.EndLocation,0.001f) || BigArc != InAnother.BigArc;
}

bool FGeomtryLineProperty::operator==(const FGeomtryLineProperty& InAnother) const
{
	return LineType == InAnother.LineType && RadiusOrHeightData == InAnother.RadiusOrHeightData && InterpPointCountData == InAnother.InterpPointCountData || StartLocation == InAnother.StartLocation && EndLocation == InAnother.EndLocation && BigArc == InAnother.BigArc;
}

FGeomtryPointProperty::FGeomtryPointProperty(const EPositionType& InPositionType, const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InPointLocationX, const FExpressionValuePair& InPointLocationY, const FExpressionValuePair& InPointLocationZ, const FVector& InPreLocation)
	:ID(-1)
	, PositionType(InPositionType)
	, PlanBelongs(InPlan)
	, LocationX(InPointLocationX)
	, LocationY(InPointLocationY)
	, LocationZ(InPointLocationZ)
	, PrePointLocation(InPreLocation)
{
}

bool FGeomtryPointProperty::operator!=(const FGeomtryPointProperty& InAnother) const
{
	return PositionType != InAnother.PositionType || LocationX != InAnother.LocationX || LocationY != InAnother.LocationY || LocationZ != InAnother.LocationZ || PrePointLocation != InAnother.PrePointLocation;
}

bool FGeomtryPointProperty::operator==(const FGeomtryPointProperty& InAnother) const
{
	return PositionType == InAnother.PositionType && LocationX == InAnother.LocationX && LocationY == InAnother.LocationY && LocationZ == InAnother.LocationZ && PrePointLocation == InAnother.PrePointLocation;
}

FGeomtryRectanglePlanProperty::FGeomtryRectanglePlanProperty(const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InStartLocationX, const FExpressionValuePair& InStartLocationY, const FExpressionValuePair& InStartLocationZ, const FExpressionValuePair& InEndLocationX, const FExpressionValuePair& InEndLocationY, const FExpressionValuePair& InEndLocationZ)
	:PlanBelongs(InPlan)
	, StartLocationX(InStartLocationX)
	, StartLocationY(InStartLocationY)
	, StartLocationZ(InStartLocationZ)
	, EndLocationX(InEndLocationX)
	, EndLocationY(InEndLocationY)
	, EndLocationZ(InEndLocationZ)
{
}

bool FGeomtryRectanglePlanProperty::GenerateJointFacesBetweenRectangle(const FGeomtryRectanglePlanProperty& InFirstRect, const FGeomtryRectanglePlanProperty& InSecondRect, const ENormalType& InNormalType, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	const FVector Normal = NSPlanType::GetPlanNormal(InFirstRect.PlanBelongs);
	const FVector TangentX = NSPlanType::GetPlanTangentX(InFirstRect.PlanBelongs);
	TArray<FVector> Plan1;
	FGeometry3DLibrary::GenerateRectanglePlanPoints(InFirstRect.StartLocation(), InFirstRect.EndLocation(), Normal, TangentX, Plan1);
	TArray<FVector> Plan2;
	FGeometry3DLibrary::GenerateRectanglePlanPoints(InSecondRect.StartLocation(), InSecondRect.EndLocation(), Normal, TangentX, Plan2);

	bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(Plan1, Normal);
	if ((bCCWWinding && (ENormalType::Outter == InNormalType)) || (!bCCWWinding && (ENormalType::Inner == InNormalType)))
	{
		FArrayOperatorLibrary::ReverseArray(Plan1);
		FArrayOperatorLibrary::ReverseArray(Plan2);
	}
	TArray<struct FCustomPoint> Plan1Points;
	TArray<struct FCustomPoint> Plan2Points;
	FCustomPoint::ConvertPointsToCustomPoints(Plan1, true, Plan1Points);
	FCustomPoint::ConvertPointsToCustomPoints(Plan2, true, Plan2Points);
	return UGeomtryMathmaticLibrary::GenerateMeshOfTwoParallelPlans(Normal, Plan1Points, Plan2Points, OutMeshInfo, OutFramwork, InFirstRect.PlanBelongs);
}

bool FGeomtryRectanglePlanProperty::ShiftSection(const TArray<float>& InShiftValue, FGeomtryRectanglePlanProperty& OutRect)
{
	OutRect = *this;
	FPolygonData Plan1;
	Plan1.PolygonPlan = this->PlanBelongs;
	Plan1.PolygonVertice.AddZeroed(4);
	Plan1.PolygonVertice[0] = this->StartLocation();
	Plan1.PolygonVertice[2] = this->EndLocation();
	FVector Normal = FVector::UpVector;
	switch (PlanBelongs)
	{
	case EPlanPolygonBelongs::EXY_Plan:Normal = FVector::UpVector; Plan1.PolygonVertice[1] = FVector(FMath::Max<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), FMath::Min<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), Plan1.PolygonVertice[0].Z); Plan1.PolygonVertice[3] = FVector(FMath::Min<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), FMath::Max<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), Plan1.PolygonVertice[0].Z); break;
	case EPlanPolygonBelongs::EYZ_Plan:Normal = FVector::ForwardVector; Plan1.PolygonVertice[1] = FVector(Plan1.PolygonVertice[0].X, FMath::Max<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), FMath::Min<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z)); Plan1.PolygonVertice[3] = FVector(Plan1.PolygonVertice[0].X, FMath::Min<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), FMath::Max<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z)); break;
	case EPlanPolygonBelongs::EXZ_Plan:Normal = FVector::RightVector; Plan1.PolygonVertice[1] = FVector(FMath::Max<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), Plan1.PolygonVertice[0].Y, FMath::Min<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z)); Plan1.PolygonVertice[3] = FVector(FMath::Min<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), Plan1.PolygonVertice[0].Y, FMath::Max<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z)); break;
	case EPlanPolygonBelongs::EUnknown:break;
	}
	TArray<FVector> Plan2;
	TArray<FVector> ShiftOffset;
	TArray<FGeomtryLineProperty> InLins;
	TArray<float> Temps;
	TMap<int32, TArray<FVector>> linePointsMap;
	for (int32 i = 0; i < Plan1.PolygonVertice.Num(); ++i)
	{
		TArray<FVector> points;
		points.Add(Plan1.PolygonVertice[i]);
		points.Add(Plan1.PolygonVertice[(i + 1) >= Plan1.PolygonVertice.Num() ? 0 : (i + 1)]);
		linePointsMap.Add(i, points);
	}
	TArray<double> shiftValues = (TArray<double>)InShiftValue;
	TArray<FVector> outKeyPointMap;
	TMap<FVector, FVector> outAllPointMap;
	FGeometryLibrary::scalePolygonMesh(linePointsMap, shiftValues, -Normal, outKeyPointMap, outAllPointMap);
	//bool Res = UGeomtryMathmaticLibrary::ShiftPlanEdges(InShiftValue, InLins, Normal,Plan1, Plan2, ShiftOffset, Temps);
	//if (!Res)
	//	return false;

	OutRect.StartLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[0].X));
	OutRect.StartLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[0].Y));
	OutRect.StartLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[0].Z));

	OutRect.EndLocationX = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[2].X));
	OutRect.EndLocationY = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[2].Y));
	OutRect.EndLocationZ = FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(outKeyPointMap[2].Z));

	//OutRect.StartLocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].X);
	//OutRect.StartLocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].Y);
	//OutRect.StartLocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].Z);
	//OutRect.EndLocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].X);
	//OutRect.EndLocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].Y);
	//OutRect.EndLocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].Z);
	return true;
}

bool FGeomtryRectanglePlanProperty::ZoomSection(const TArray<float>& InZoomValue)
{
	FPolygonData Plan1;
	Plan1.PolygonPlan = this->PlanBelongs;
	Plan1.PolygonVertice.AddZeroed(4);
	Plan1.PolygonVertice[0] = this->StartLocation();
	Plan1.PolygonVertice[2] = this->EndLocation();
	FVector Normal = FVector::UpVector;
	if (EPlanPolygonBelongs::EXY_Plan == this->PlanBelongs)
	{
		Normal = FVector::UpVector;
		Plan1.PolygonVertice[1] = FVector(FMath::Max<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), FMath::Min<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), Plan1.PolygonVertice[0].Z);
		Plan1.PolygonVertice[3] = FVector(FMath::Min<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), FMath::Max<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), Plan1.PolygonVertice[0].Z);
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == this->PlanBelongs)
	{
		Normal = FVector::ForwardVector;
		Plan1.PolygonVertice[1] = FVector(Plan1.PolygonVertice[0].X, FMath::Max<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), FMath::Min<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z));
		Plan1.PolygonVertice[3] = FVector(Plan1.PolygonVertice[0].X, FMath::Min<float>(Plan1.PolygonVertice[0].Y, Plan1.PolygonVertice[2].Y), FMath::Max<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z));
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == this->PlanBelongs)
	{
		Normal = FVector::RightVector;
		Plan1.PolygonVertice[1] = FVector(FMath::Max<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), Plan1.PolygonVertice[0].Y, FMath::Min<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z));
		Plan1.PolygonVertice[3] = FVector(FMath::Min<float>(Plan1.PolygonVertice[0].X, Plan1.PolygonVertice[2].X), Plan1.PolygonVertice[0].Y, FMath::Max<float>(Plan1.PolygonVertice[0].Z, Plan1.PolygonVertice[2].Z));
	}
	TArray<FVector> Plan2;
	TArray<FVector> ShiftOffset;
	TArray<FGeomtryLineProperty> InLins;
	TArray<float> Temps;
	bool Res = UGeomtryMathmaticLibrary::ShiftPlanEdges(InZoomValue, InLins, Normal, Plan1, Plan2, ShiftOffset, Temps);
	if (!Res)
		return false;
	this->StartLocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].X);
	this->StartLocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].Y);
	this->StartLocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[0].Z);
	this->EndLocationX += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].X);
	this->EndLocationY += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].Y);
	this->EndLocationZ += UParameterPropertyData::ConvertToUIValue(ShiftOffset[2].Z);
	return true;
}

FGeomtryEllipsePlanProperty::FGeomtryEllipsePlanProperty(const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InCenterLocationX, const FExpressionValuePair& InCenterLocationY, const FExpressionValuePair& InCenterLocationZ, const FExpressionValuePair& InShortRadius, const FExpressionValuePair& InLongRadius, const FExpressionValuePair& InInterpPointCount)
	:PlanBelongs(InPlan)
	, CenterLocationX(InCenterLocationX)
	, CenterLocationY(InCenterLocationY)
	, CenterLocationZ(InCenterLocationZ)
	, ShortRadiusData(InShortRadius)
	, LongRadiusData(InLongRadius)
	, InterpPointCountData(InInterpPointCount)
{
}

bool FGeomtryEllipsePlanProperty::GenerateJointFacesBetweenEllipse(const ENormalType& InNormalType, const FGeomtryEllipsePlanProperty& InFirstEllipse, const FGeomtryEllipsePlanProperty& InSecondEllipse, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
{
	const FVector Normal = NSPlanType::GetPlanNormal(InFirstEllipse.PlanBelongs);
	const FVector TangentX = NSPlanType::GetPlanTangentX(InFirstEllipse.PlanBelongs);
	TArray<FVector> Plan1;
	FGeometry3DLibrary::GenerateEllipsePlanPoints(InFirstEllipse.CenterLocation(), InFirstEllipse.LongRadius(), InFirstEllipse.ShortRadius(), Normal, TangentX, Plan1, InFirstEllipse.InterPointCount());
	TArray<FVector> Plan2;
	FGeometry3DLibrary::GenerateEllipsePlanPoints(InSecondEllipse.CenterLocation(), InSecondEllipse.LongRadius(), InSecondEllipse.ShortRadius(), Normal, TangentX, Plan2, InSecondEllipse.InterPointCount());

	bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(Plan1, Normal);
	if ((bCCWWinding && (ENormalType::Outter == InNormalType)) || (!bCCWWinding && (ENormalType::Inner == InNormalType)))
	{
		FArrayOperatorLibrary::ReverseArray(Plan1);
		FArrayOperatorLibrary::ReverseArray(Plan2);
	}
	TArray<struct FCustomPoint> Plan1Points;
	TArray<struct FCustomPoint> Plan2Points;
	FCustomPoint::ConvertPointsToCustomPoints(Plan1, false, Plan1Points);
	FCustomPoint::ConvertPointsToCustomPoints(Plan2, false, Plan2Points);
	return UGeomtryMathmaticLibrary::GenerateMeshOfTwoParallelPlans(Normal, Plan1Points, Plan2Points, OutMeshInfo, OutFramwork, InFirstEllipse.PlanBelongs);
}

bool FGeomtryEllipsePlanProperty::ShiftSection(const TArray<float>& InShiftValue, FGeomtryEllipsePlanProperty& OutEllipse)
{
	OutEllipse = *this;
	if (InShiftValue.Num() <= 0)
		return true;
	OutEllipse.ShortRadiusData += UParameterPropertyData::ConvertToUIValue(InShiftValue[0]);
	OutEllipse.LongRadiusData += UParameterPropertyData::ConvertToUIValue(InShiftValue[0]);
	return true;
}

bool FGeomtryEllipsePlanProperty::ZoomSection(const TArray<float>& InZoomValue)
{
	if (InZoomValue.Num() <= 0)
		return true;
	this->ShortRadiusData += UParameterPropertyData::ConvertToUIValue(InZoomValue[0]);
	this->LongRadiusData += UParameterPropertyData::ConvertToUIValue(InZoomValue[0]);
	return true;
}

FGeomtryCubeProperty::FGeomtryCubeProperty(const FExpressionValuePair& InStartLocationX, const FExpressionValuePair& InStartLocationY, const FExpressionValuePair& InStartLocationZ, const FExpressionValuePair& InEndLocationX, const FExpressionValuePair& InEndLocationY, const FExpressionValuePair& InEndLocationZ)
	: StartLocationX(InStartLocationX)
	, StartLocationY(InStartLocationY)
	, StartLocationZ(InStartLocationZ)
	, EndLocationX(InEndLocationX)
	, EndLocationY(InEndLocationY)
	, EndLocationZ(InEndLocationZ)
{
}

void FGeomtryCubeProperty::GenerateMeshAndFramwork(FPMCSection& OutMesh, TArray<TPair<FVector, FVector>>& OutFramwork) const
{
	UGeometryRelativeLibrary::GenerateCubeMesh(UParameterPropertyData::ConvertToUeValue(this->StartLocation()), UParameterPropertyData::ConvertToUeValue(this->EndLocation()), OutMesh);
	OutFramwork.AddZeroed(12);
	int32 Index = 0;
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[0], OutMesh.Vertexes[1]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[1], OutMesh.Vertexes[2]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[2], OutMesh.Vertexes[3]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[3], OutMesh.Vertexes[0]);

	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[4], OutMesh.Vertexes[5]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[5], OutMesh.Vertexes[6]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[6], OutMesh.Vertexes[7]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[7], OutMesh.Vertexes[4]);

	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[0], OutMesh.Vertexes[6]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[1], OutMesh.Vertexes[5]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[2], OutMesh.Vertexes[4]);
	OutFramwork[Index++] = TPair<FVector, FVector>(OutMesh.Vertexes[3], OutMesh.Vertexes[7]);
}


bool FGeometryPointGenerator::setProperty(const int32& structIndex, const FGeomtryPointProperty& inProperty)
{
	for (auto& iter : points)
	{
		if (iter->structSort == structIndex)
		{
			iter->pointProperty.CopyData(inProperty);
			return true;
		}
	}
	return false;
}

TSharedPtr <FGeometryPointData> FGeometryPointGenerator::setPropertyByDisplaySort(const int32& index, const FGeomtryPointProperty& inProperty)
{
	if (index < 0 || index >= points.Num())
		return nullptr;
	for (auto iter : points)
	{
		if (iter->displaySort == index)
		{
			iter->position = inProperty.PointLocation();
			iter->pointProperty.CopyData(inProperty);
			return iter;
		}
	}
	return nullptr;
}

void FGeometryPointGenerator::setNormal(const FVector& inNormal)
{
	normal = inNormal;
}

void FGeometryPointGenerator::addPoint(const FVector& inPos)
{
	TSharedPtr<FGeometryPointData> dataPtr = MakeShared<FGeometryPointData>();

	dataPtr->position = inPos;
	dataPtr->structSort = points.Num();
	dataPtr->displaySort = points.Num();

	points.Add(dataPtr);
}

void FGeometryPointGenerator::addPoint(const FVector& inPos, const FGeomtryPointProperty& inProperty)
{
	TSharedPtr<FGeometryPointData> dataPtr = MakeShared<FGeometryPointData>();

	dataPtr->position = inPos;
	dataPtr->structSort = points.Num();
	dataPtr->displaySort = points.Num();
	dataPtr->pointProperty.CopyData(inProperty);

	points.Add(dataPtr);
}

void FGeometryPointGenerator::addPoint(const FVector& inPos, const int32& inDisplaySort)
{
	TSharedPtr<FGeometryPointData> dataPtr = MakeShared<FGeometryPointData>();

	dataPtr->position = inPos;
	dataPtr->displaySort = inDisplaySort;
	dataPtr->structSort = points.Num();



	points.Add(dataPtr);
}

void FGeometryPointGenerator::addPoint(const FGeometryPointData& inPoint)
{
	TSharedPtr<FGeometryPointData> dataPtr = MakeShared<FGeometryPointData>(inPoint);
	points.Add(dataPtr);
}

void FGeometryPointGenerator::addPoint(TSharedPtr<FGeometryPointData> inPoint)
{
	points.Add(inPoint);
}

void FGeometryPointGenerator::initializePoints(const TArray<FVector>& inPoints)
{
	for (auto& iter : inPoints)
	{
		addPoint(iter);
	}
}

TArray<TSharedPtr<FGeometryPointData>> FGeometryPointGenerator::getPoints()
{
	return points;
}

int32 FGeometryPointGenerator::getPointCount()
{
	return points.Num();
}


void FGeometryPointGenerator::computeStruct()
{
	//conter clock wise
	if (points.Num() <  3)
	{
		int32 index = 0;
		for (auto& iter : points)
		{
			iter->displaySort = index;
			++index;
		}
	}
	if (points.Num() >= 3)
	{
		TArray<FVector> tempPoints;
		tempPoints.SetNumZeroed(points.Num());
		int32 index = 0;
		for (auto& iter : points)
		{
			iter->displaySort = index;
			tempPoints[index] = iter->position;
			++index;
		}
		bool bCCWWinding = FPolygon3DLibrary::IsPolygonCCWWinding(tempPoints, normal);
		if (bCCWWinding)
		{
			for (int32 i = 0; i < points.Num(); i++)
			{
				points.Last(i)->structSort = i;
			}
		}
	}
}

void FGeometryPointGenerator::deletePoint(TSharedPtr<FGeometryPointData> inPoint)
{
	auto structSort = inPoint->structSort;
	auto displaySort = inPoint->displaySort;
	points.Remove(inPoint);
	for (auto & iter : points)
	{
		if (iter->displaySort > displaySort)
		{
			--iter->displaySort;
		}
		if (iter->structSort > structSort)
		{
			--iter->structSort;
		}
	}
}

void FGeometryPointGenerator::clear()
{
	points.Empty();
}

void FGeometryLineGenerator::initializeLines(const TArray<TSharedPtr<FGeometryPointData>>& inPoints, bool bIsLoop)
{
	bLoop = bIsLoop;
	int32 numOfPoint = inPoints.Num();
	for (int32 cur = 0; cur < numOfPoint; ++cur)
	{
		if (!bLoop && cur == numOfPoint - 1)
		{
			break;
		}
		int32 next = (cur + 1) % numOfPoint;
		TSharedPtr<FGeometryLineData> lineData = MakeShared<FGeometryLineData>();
		lineData->startPointData = inPoints[cur];
		lineData->endPointData = inPoints[next];
		lineData->lineProperty = FGeomtryLineProperty();
		lineData->lineProperty.StartLocation = lineData->startPointData->position;
		lineData->lineProperty.EndLocation = lineData->endPointData->position;
		lineData->lineProperty.PlanBelongs = inPoints[cur]->pointProperty.PlanBelongs;

		lines.Add(lineData);
	}
}

void FGeometryLineGenerator::initializeLines(const TArray<TSharedPtr<FGeometryLineData>>& inLines, bool bIsLoop)
{
	lines = inLines;
	bLoop = bIsLoop;
}

void FGeometryLineGenerator::setNormal(const FVector& inNormal)
{
	normal = inNormal;
}

void FGeometryLineGenerator::addPoint(const TSharedPtr<FGeometryPointData>& inPoint, bool bIsLoop)
{
	bLoop = bIsLoop;
	int32 numOfLine = lines.Num();

	if (numOfLine < 1)
	{
		return;
	}

	if (bIsLoop)
	{
		if (numOfLine == 1)
		{
			auto start0Ptr = lines[0]->endPointData;
			TSharedPtr<FGeometryLineData> lineData0 = MakeShared<FGeometryLineData>();
			lineData0->startPointData = start0Ptr;
			lineData0->endPointData = inPoint;
			lineData0->lineProperty = FGeomtryLineProperty();

			lineData0->lineProperty.StartLocation = lineData0->startPointData->position;
			lineData0->lineProperty.EndLocation = lineData0->endPointData->position;
			lineData0->lineProperty.PlanBelongs = inPoint->pointProperty.PlanBelongs;
			lineData0->lineProperty.ID = lines.Num();

			auto end1Ptr = lines[0]->startPointData;
			TSharedPtr<FGeometryLineData> lineData1 = MakeShared<FGeometryLineData>();
			lineData1->startPointData = inPoint;
			lineData1->endPointData = end1Ptr;
			lineData1->lineProperty = FGeomtryLineProperty();
			lineData1->lineProperty.StartLocation = lineData1->startPointData->position;
			lineData1->lineProperty.EndLocation = lineData1->endPointData->position;
			lineData1->lineProperty.PlanBelongs = inPoint->pointProperty.PlanBelongs;
			lineData1->lineProperty.ID = lines.Num() + 1;

			lines.Add(lineData0);
			lines.Add(lineData1);

		}
		else
		{
			lines.Last(0)->endPointData = inPoint;
			lines.Last(0)->lineProperty.EndLocation = lines.Last(0)->endPointData->position;

			auto end1Ptr = lines[0]->startPointData;
			TSharedPtr<FGeometryLineData> lineData = MakeShared<FGeometryLineData>();
			lineData->startPointData = inPoint;
			lineData->endPointData = end1Ptr;
			lineData->lineProperty = FGeomtryLineProperty();
			lineData->lineProperty.StartLocation = lineData->startPointData->position;
			lineData->lineProperty.EndLocation = lineData->endPointData->position;
			lineData->lineProperty.PlanBelongs = inPoint->pointProperty.PlanBelongs;
			lineData->lineProperty.ID = lines.Num();

			lines.Add(lineData);
		}

	}
	else
	{
		auto startPtr = lines.Last(0)->endPointData;
		TSharedPtr<FGeometryLineData> lineData = MakeShared<FGeometryLineData>();
		lineData->startPointData = startPtr;
		lineData->endPointData = inPoint;
		lineData->lineProperty = FGeomtryLineProperty();
		lineData->lineProperty.StartLocation = lineData->startPointData->position;
		lineData->lineProperty.EndLocation = lineData->endPointData->position;
		lineData->lineProperty.PlanBelongs = inPoint->pointProperty.PlanBelongs;
		lineData->lineProperty.ID = lines.Num();

		lines.Add(lineData);
	}

}

bool FGeometryLineGenerator::getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex)
{
	bool res = false;
	for (auto& iter : lines)
	{
		if (iter->containPoint(startPoint) && iter->containPoint(endPoint))
		{
			auto startPos = startPoint->position;
			auto endPos = endPoint->position;


/*			if ((ELineType::ELineSegment == iter->lineProperty.LineType) || FMath::IsNearlyZero(iter->lineProperty.RadiusOrHeight(), THRESH_POINTS_ARE_NEAR))
			{
				outVertex.AddDefaulted(2);
				outVertex[0] = startPos;
				outVertex[1] = endPos;
			}
			else */
			
			if (ELineType::ERadiusArc == iter->lineProperty.LineType)
			{
				res = FGeometryLibrary::createArcSegmentByRadius(iter->lineProperty.RadiusOrHeight(), startPos, endPos, stepNum, normal, iter->lineProperty.BigArc, FTransform(), outVertex);
			}
			else if (ELineType::EHeightArc == iter->lineProperty.LineType)
			{
				res = FGeometryLibrary::createArcSegmentByHeight(iter->lineProperty.RadiusOrHeight(), startPos, endPos, stepNum, normal, FTransform(), outVertex);
			}
			if (outVertex.IsEmpty())
			{
				outVertex.AddDefaulted(2);
				outVertex[0] = startPos;
				outVertex[1] = endPos;
			}
		}
	}
	return res;
}

bool FGeometryLineGenerator::getLineVertexOrder(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex)
{
	bool res = false;
	for (auto& iter : lines)
	{
		if (iter->startPointData == startPoint && iter->endPointData == endPoint)
		{
			auto startPos = startPoint->position;
			auto endPos = endPoint->position;


			/*			if ((ELineType::ELineSegment == iter->lineProperty.LineType) || FMath::IsNearlyZero(iter->lineProperty.RadiusOrHeight(), THRESH_POINTS_ARE_NEAR))
						{
							outVertex.AddDefaulted(2);
							outVertex[0] = startPos;
							outVertex[1] = endPos;
						}
						else */

			if (ELineType::ERadiusArc == iter->lineProperty.LineType)
			{
				res = FGeometryLibrary::createArcSegmentByRadius(iter->lineProperty.RadiusOrHeight(), startPos, endPos, stepNum, normal, iter->lineProperty.BigArc, FTransform(), outVertex);
			}
			else if (ELineType::EHeightArc == iter->lineProperty.LineType)
			{
				res = FGeometryLibrary::createArcSegmentByHeight(iter->lineProperty.RadiusOrHeight(), startPos, endPos, stepNum, normal, FTransform(), outVertex);
			}
			if (outVertex.IsEmpty())
			{
				outVertex.AddDefaulted(2);
				outVertex[0] = startPos;
				outVertex[1] = endPos;
			}
		}
	}
	return res;
}

TSharedPtr<FGeometryLineData> FGeometryLineGenerator::setLineProperty(const int32& index, const FGeomtryLineProperty& inProperty)
{
	if (index < 0 || index >= lines.Num())
		return nullptr;
	lines[index]->lineProperty.CopyData(inProperty);
	return lines[index];
}

void FGeometryLineGenerator::deletePoint(TSharedPtr<FGeometryPointData> inPoint, bool bIsLoop)
{
	if (lines.Num() <= 0)
	{
		return;
	}
	int32 removeIndex = 0;
	bool bDeleteAll = false;

	for (int32 cur = 0; cur < lines.Num(); ++cur)
	{
		int32 next = (cur + 1) % lines.Num();
		if (lines[cur]->endPointData == inPoint)
		{
			if (bIsLoop || next != 0)
			{
				if (lines.Num() > 2)
				{
					lines[cur]->endPointData = lines[next]->endPointData;
				}
				else
				{
					bDeleteAll = true;
				}
				removeIndex = next;
			}
			else
			{
				removeIndex = cur;
			}
			break;
		}
	}
	if (bDeleteAll)
	{
		lines.Empty();
	}
	else
	{
		lines.RemoveAt(removeIndex);
	}
}

void FGeometryLineGenerator::clear()
{
	lines.Empty();
}

TArray<TSharedPtr<FGeometryLineData>> FGeometryLineGenerator::getLines()
{
	return lines;
}

int32 FGeometryLineData::getStructSort()
{
	return startPointData->structSort;
}

int32 FGeometryLineData::getDisplaySort()
{
	return startPointData->displaySort;
}

bool FGeometryLineData::containPoint(TSharedPtr<FGeometryPointData> inPoint)
{
	return (startPointData == inPoint || endPointData == inPoint);
}
