// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ComponentEnumData.h"
#include "ParameterPropertyData.h"
#include "MagicCore/Public/PMCSection.h"
#include "GeomtryItemData.generated.h"


struct FGeoMesh;
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FPolygonData
{
	GENERATED_USTRUCT_BODY()
public:
	TArray<FVector> PolygonVertice;//No need loop
	EPlanPolygonBelongs PolygonPlan;
	EPolygonVerticesOrder PolygonVerticesOrder;
	
	FPolygonData() :PolygonPlan(EPlanPolygonBelongs::EUnknown), PolygonVerticesOrder(EPolygonVerticesOrder::EUnknown) {}
	FPolygonData(const EPlanPolygonBelongs& InPolygonPlan, const EPolygonVerticesOrder& InVerticesOrder) :PolygonPlan(InPolygonPlan), PolygonVerticesOrder(InVerticesOrder) {}
	FPolygonData(const TArray<FVector>& InVertices, const EPlanPolygonBelongs& InPolygonPlan, const EPolygonVerticesOrder& InVerticesOrder) :PolygonVertice(InVertices), PolygonPlan(InPolygonPlan), PolygonVerticesOrder(InVerticesOrder) {}
	
	bool Equal_Precise(const FPolygonData& InAnother)
	{
		const bool EqualPlan = PolygonPlan == InAnother.PolygonPlan;
		const bool EqualOrder = PolygonVerticesOrder == InAnother.PolygonVerticesOrder;
		bool EqualVertices = PolygonVertice.Num() == InAnother.PolygonVertice.Num();
		if (EqualVertices)
		{
			for (int32 i = 0; i < PolygonVertice.Num(); ++i)
			{
				if ((PolygonVertice[i] - InAnother.PolygonVertice[i]).Size() > 0.1)
				{
					EqualVertices = false;
					break;
				}
			}
		}

		return EqualPlan && EqualOrder && EqualVertices;
	}
	
	bool GenerateOutlineFramwork(TArray<TPair<FVector, FVector>>& OutFramework);
	bool GenerateMesh(FPMCSection& OutMeshInfo);
	void fromGeoMeshToPMCSection(const FGeoMesh& inMesh, FPMCSection& outMesh);

	//用于生成弧形线时标定弧线方向的法线
	FVector GetSectionArcNormal() const;
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FGeomtryPointProperty
{
	GENERATED_BODY()
public:
		int32 ID;
	UPROPERTY()
		EPositionType PositionType;
	UPROPERTY()
		EPlanPolygonBelongs PlanBelongs;
	UPROPERTY()
		FExpressionValuePair LocationX;
	UPROPERTY()
		FExpressionValuePair LocationY;
	UPROPERTY()
		FExpressionValuePair LocationZ;
	UPROPERTY()
		FVector PrePointLocation;

	FGeomtryPointProperty() :ID(-1), PositionType(EPositionType::EAbsolute), PlanBelongs(EPlanPolygonBelongs::EUnknown), LocationX(FExpressionValuePair()), LocationY(FExpressionValuePair()), LocationZ(FExpressionValuePair()), PrePointLocation(FVector::ZeroVector) {}
	FGeomtryPointProperty(const EPositionType& InPositionType, const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InPointLocationX, const FExpressionValuePair& InPointLocationY, const FExpressionValuePair& InPointLocationZ, const FVector& InPreLocation);
	
	bool Equal_Precise(const FGeomtryPointProperty& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		const bool EqualPositionType = PositionType == InAnother.PositionType;
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		const bool EqualLocX = LocationX.Equals_Precise(InAnother.LocationX);
		const bool EqualLocY = LocationY.Equals_Precise(InAnother.LocationY);
		const bool EqualLocZ = LocationZ.Equals_Precise(InAnother.LocationZ);
		const bool EqualPreLoc = (PrePointLocation - InAnother.PrePointLocation).Size() < 0.1;

		return EqualID && EqualPositionType && EqualPlanBelongs && EqualLocX && EqualLocY && EqualLocZ && EqualPreLoc;
	}
	
	void CopyData(const FGeomtryPointProperty& InData)
	{
		ID = InData.ID;
		PositionType = InData.PositionType;
		PlanBelongs = InData.PlanBelongs;
		LocationX = InData.LocationX;
		LocationY = InData.LocationY;
		LocationZ = InData.LocationZ;
		PrePointLocation = InData.PrePointLocation;
	}

	static FName GetName()
	{
		return FName(TEXT("FGeomtryPointProperty"));
	}

	bool operator!=(const FGeomtryPointProperty& InAnother) const;

	bool operator==(const FGeomtryPointProperty& InAnother) const;

	FVector PointLocation() const
	{
		FVector Location = FVector(FCString::Atof(*LocationX.Value), FCString::Atof(*LocationY.Value), FCString::Atof(*LocationZ.Value));
		//FGeometryDatas::FormatLocation(this->PlanBelongs, Location);
		return UParameterPropertyData::ConvertToUeValue(Location);
	}

	FVector PointLocation(const FVector& PreLocation) const
	{
		FVector WorldLocation = PointLocation();
		if (EPositionType::ERelative == PositionType) WorldLocation += PreLocation;
		return WorldLocation;
	}

	static void PointsLocation(const TArray<FGeomtryPointProperty>& PointProperties, TArray<FVector>& Points)
	{
		if (PointProperties.Num() <= 0) return;
		Points.Init(FVector::ZeroVector, PointProperties.Num());
		Points[0] = PointProperties[0].PointLocation();
		for (int32 i = 1; i < Points.Num(); ++i)
		{
			Points[i] = PointProperties[i].PointLocation(Points[i - 1]);
		}
	}

	friend FArchive& operator<<(FArchive& Ar, struct FGeomtryPointProperty& PointPropertyToSave)
	{
		Ar << PointPropertyToSave.ID;
		Ar << PointPropertyToSave.PlanBelongs;
		Ar << PointPropertyToSave.PositionType;
		Ar << PointPropertyToSave.LocationX;
		Ar << PointPropertyToSave.LocationY;
		Ar << PointPropertyToSave.LocationZ;
		Ar << PointPropertyToSave.PrePointLocation;
		return Ar;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FGeomtryLineProperty
{
	GENERATED_USTRUCT_BODY()
public:
	int32 ID;
	ELineType LineType;
	EPlanPolygonBelongs PlanBelongs;
	FExpressionValuePair RadiusOrHeightData;
	FExpressionValuePair InterpPointCountData;
	FVector StartLocation;
	FVector EndLocation;
	bool BigArc;

	FGeomtryLineProperty()
		:ID(-1)
		, LineType(ELineType::ELineSegment)
		, PlanBelongs(EPlanPolygonBelongs::EXY_Plan)
		, RadiusOrHeightData(FExpressionValuePair(0.0f))
		, InterpPointCountData(0)
		, StartLocation(FVector::ZeroVector)
		, EndLocation(FVector::ZeroVector)
		, BigArc(false)
	{}
	FGeomtryLineProperty(const ELineType& InLineType, const EPlanPolygonBelongs& InPlanType, const FExpressionValuePair& InRadiusOrHeight, const FExpressionValuePair& InPointCount, const FVector& InStartLocation, const FVector& InEndLocation);
	
	bool Equal_Precise(const FGeomtryLineProperty& InAnother)
	{
		const bool EqualID = ID == InAnother.ID;
		const bool EqualLineType = LineType == InAnother.LineType;
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		const bool EqualLocRad = RadiusOrHeightData.Equals_Precise(InAnother.RadiusOrHeightData);
		const bool EqualLocIterp = InterpPointCountData.Equals_Precise(InAnother.InterpPointCountData);
		const bool EqualStart = (StartLocation - InAnother.StartLocation).Size() < 0.1;
		const bool EqualEnd = (EndLocation - InAnother.EndLocation).Size() < 0.1;
		const bool EqualBigArc = BigArc == InAnother.BigArc;

		return EqualID && EqualLineType && EqualPlanBelongs && EqualLocRad && EqualLocIterp && EqualStart && EqualEnd && EqualBigArc;
	}

	void CopyData(const FGeomtryLineProperty& InData)
	{
		ID = InData.ID;
		LineType = InData.LineType;
		PlanBelongs = InData.PlanBelongs;
		RadiusOrHeightData = InData.RadiusOrHeightData;
		InterpPointCountData = InData.InterpPointCountData;
		StartLocation = InData.StartLocation;
		EndLocation = InData.EndLocation;
		BigArc = InData.BigArc;
	}

	bool operator!=(const FGeomtryLineProperty& InAnother) const;

	bool operator==(const FGeomtryLineProperty& InAnother) const;

	float RadiusOrHeight() const
	{
		return UParameterPropertyData::ConvertToUeValue(FCString::Atof(*RadiusOrHeightData.Value));
	}

	int32 InterpPointCount() const
	{
		return FCString::Atoi(*InterpPointCountData.Value);
	}

	static FName GetName()
	{
		return FName(TEXT("FGeomtryLineProperty"));
	}

	friend FArchive& operator<<(FArchive& Ar, struct FGeomtryLineProperty& LinePropertyToSave)
	{
		Ar << LinePropertyToSave.ID;
		Ar << LinePropertyToSave.PlanBelongs;
		Ar << LinePropertyToSave.LineType;
		Ar << LinePropertyToSave.BigArc;
		Ar << LinePropertyToSave.RadiusOrHeightData;
		Ar << LinePropertyToSave.InterpPointCountData;
		Ar << LinePropertyToSave.StartLocation;
		Ar << LinePropertyToSave.EndLocation;
		return Ar;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FGeomtryRectanglePlanProperty
{
	GENERATED_BODY()
public:
	UPROPERTY()
		EPlanPolygonBelongs PlanBelongs;
	UPROPERTY()
		FExpressionValuePair StartLocationX;
	UPROPERTY()
		FExpressionValuePair StartLocationY;
	UPROPERTY()
		FExpressionValuePair StartLocationZ;
	UPROPERTY()
		FExpressionValuePair EndLocationX;
	UPROPERTY()
		FExpressionValuePair EndLocationY;
	UPROPERTY()
		FExpressionValuePair EndLocationZ;

	FGeomtryRectanglePlanProperty() :PlanBelongs(EPlanPolygonBelongs::EUnknown), StartLocationX(FExpressionValuePair(0.0f)), StartLocationY(FExpressionValuePair(0.0f)), StartLocationZ(FExpressionValuePair(0.0f)), EndLocationX(FExpressionValuePair(500.0f)), EndLocationY(FExpressionValuePair(500.0f)), EndLocationZ(FExpressionValuePair(500.0f)) {}
	FGeomtryRectanglePlanProperty(const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InStartLocationX, const FExpressionValuePair& InStartLocationY, const FExpressionValuePair& InStartLocationZ, const FExpressionValuePair& InEndLocationX, const FExpressionValuePair& InEndLocationY, const FExpressionValuePair& InEndLocationZ);
	
	bool Equal_Precise(const FGeomtryRectanglePlanProperty& InAnother)
	{
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		const bool EqualStartX = StartLocationX.Equals_Precise(InAnother.StartLocationX);
		const bool EqualStartY = StartLocationY.Equals_Precise(InAnother.StartLocationY);
		const bool EqualStartZ = StartLocationZ.Equals_Precise(InAnother.StartLocationZ);
		const bool EqualEndX = EndLocationX.Equals_Precise(InAnother.EndLocationX);
		const bool EqualEndY = EndLocationY.Equals_Precise(InAnother.EndLocationY);
		const bool EqualEndZ = EndLocationZ.Equals_Precise(InAnother.EndLocationZ);

		return EqualPlanBelongs && EqualStartX && EqualStartY && EqualStartZ && EqualEndX && EqualEndY && EqualEndZ;
	}
	
	void CopyData(const FGeomtryRectanglePlanProperty& InData)
	{
		PlanBelongs = InData.PlanBelongs;
		StartLocationX = InData.StartLocationX;
		StartLocationY = InData.StartLocationY;
		StartLocationZ = InData.StartLocationZ;
		EndLocationX = InData.EndLocationX;
		EndLocationY = InData.EndLocationY;
		EndLocationZ = InData.EndLocationZ;
	}

	static bool GenerateJointFacesBetweenRectangle(const FGeomtryRectanglePlanProperty& InFirstRect, const FGeomtryRectanglePlanProperty& InSecondRect, const ENormalType& InNormalType, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	bool ShiftSection(const TArray<float>& InShiftValue, FGeomtryRectanglePlanProperty& OutRect);

	bool ZoomSection(const TArray<float>& InZoomValue);

	static FName GetName()
	{
		return FName(TEXT("FGeomtryRectanglePlanProperty"));
	}

	FVector StartLocation() const
	{
		FVector Location = FVector(FCString::Atof(*StartLocationX.Value), FCString::Atof(*StartLocationY.Value), FCString::Atof(*StartLocationZ.Value));
		//FGeometryDatas::FormatLocation(this->PlanBelongs, Location);
		return UParameterPropertyData::ConvertToUeValue(Location);
	}

	FVector EndLocation() const
	{
		FVector Location = FVector(FCString::Atof(*EndLocationX.Value), FCString::Atof(*EndLocationY.Value), FCString::Atof(*EndLocationZ.Value));
		//FGeometryDatas::FormatLocation(this->PlanBelongs, Location);
		return UParameterPropertyData::ConvertToUeValue(Location);
	}

	friend FArchive& operator<<(FArchive& Ar, struct FGeomtryRectanglePlanProperty& RectanglePropertyToSave)
	{
		Ar << RectanglePropertyToSave.PlanBelongs;
		Ar << RectanglePropertyToSave.StartLocationX;
		Ar << RectanglePropertyToSave.StartLocationY;
		Ar << RectanglePropertyToSave.StartLocationZ;
		Ar << RectanglePropertyToSave.EndLocationX;
		Ar << RectanglePropertyToSave.EndLocationY;
		Ar << RectanglePropertyToSave.EndLocationZ;
		return Ar;
	}

	bool operator!=(const FGeomtryRectanglePlanProperty& InData) const
	{
		return PlanBelongs != InData.PlanBelongs ||
			StartLocationX != InData.StartLocationX ||
			StartLocationY != InData.StartLocationY ||
			StartLocationZ != InData.StartLocationZ ||
			EndLocationX != InData.EndLocationX ||
			EndLocationY != InData.EndLocationY ||
			EndLocationZ != InData.EndLocationZ;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FGeomtryEllipsePlanProperty
{
	GENERATED_BODY()
public:
	UPROPERTY()
		EPlanPolygonBelongs PlanBelongs;
	UPROPERTY()
		FExpressionValuePair CenterLocationX;
	UPROPERTY()
		FExpressionValuePair CenterLocationY;
	UPROPERTY()
		FExpressionValuePair CenterLocationZ;
	UPROPERTY()
		FExpressionValuePair ShortRadiusData;
	UPROPERTY()
		FExpressionValuePair LongRadiusData;
	UPROPERTY()
		FExpressionValuePair InterpPointCountData;

	TArray<FVector> vertexCache;		//for lofting

public:

	FVector CenterLocation() const
	{
		FVector Location = FVector(FCString::Atof(*CenterLocationX.Value), FCString::Atof(*CenterLocationY.Value), FCString::Atof(*CenterLocationZ.Value));
		//FGeometryDatas::FormatLocation(this->PlanBelongs, Location);
		return UParameterPropertyData::ConvertToUeValue(Location);
	}
	float ShortRadius() const
	{
		return UParameterPropertyData::ConvertToUeValue(FCString::Atof(*ShortRadiusData.Value));
	}
	float LongRadius() const
	{
		return UParameterPropertyData::ConvertToUeValue(FCString::Atof(*LongRadiusData.Value));
	}
	int32 InterPointCount() const
	{
		return FCString::Atoi(*InterpPointCountData.Value);
	}

	FGeomtryEllipsePlanProperty() :PlanBelongs(EPlanPolygonBelongs::EUnknown), CenterLocationX(FExpressionValuePair(0.0f)), CenterLocationY(FExpressionValuePair(0.0f)), CenterLocationZ(FExpressionValuePair(0.0f)), ShortRadiusData(FExpressionValuePair(500.0f)), LongRadiusData(FExpressionValuePair(500.0f)), InterpPointCountData(FExpressionValuePair(80)) {}
	FGeomtryEllipsePlanProperty(const EPlanPolygonBelongs& InPlan, const FExpressionValuePair& InCenterLocationX, const FExpressionValuePair& InCenterLocationY, const FExpressionValuePair& InCenterLocationZ, const FExpressionValuePair& InShortRadius, const FExpressionValuePair& InLongRadius, const FExpressionValuePair& InInterpPointCount);
	
	bool Equal_Precise(const FGeomtryEllipsePlanProperty& InAnother)
	{
		const bool EqualPlanBelongs = PlanBelongs == InAnother.PlanBelongs;
		const bool EqualCenterX = CenterLocationX.Equals_Precise(InAnother.CenterLocationX);
		const bool EqualCenterY = CenterLocationY.Equals_Precise(InAnother.CenterLocationY);
		const bool EqualCenterZ = CenterLocationZ.Equals_Precise(InAnother.CenterLocationZ);
		const bool EqualShort = ShortRadiusData.Equals_Precise(InAnother.ShortRadiusData);
		const bool EqualLong = LongRadiusData.Equals_Precise(InAnother.LongRadiusData);
		const bool EqualInterp = InterpPointCountData.Equals_Precise(InAnother.InterpPointCountData);

		bool EqualVertex = true;
		{
			if (vertexCache.Num() != InAnother.vertexCache.Num())
			{
				EqualVertex = false;
			}
			else
			{
				for (int32 i = 0; i < vertexCache.Num(); ++i)
				{
					if ((vertexCache[i] - InAnother.vertexCache[i]).Size() > 0.1)
					{
						EqualVertex = false;
						break;
					}
				}
			}
		}

		return EqualPlanBelongs && EqualCenterX && EqualCenterY && EqualCenterZ && EqualShort && EqualLong && EqualInterp && EqualVertex;
	}

	void CopyData(const FGeomtryEllipsePlanProperty& InData)
	{
		PlanBelongs = InData.PlanBelongs;
		CenterLocationX = InData.CenterLocationX;
		CenterLocationY = InData.CenterLocationY;
		CenterLocationZ = InData.CenterLocationZ;
		ShortRadiusData = InData.ShortRadiusData;
		LongRadiusData = InData.LongRadiusData;
		InterpPointCountData = InData.InterpPointCountData;
	}

	static bool GenerateJointFacesBetweenEllipse(const ENormalType& InNormalType, const FGeomtryEllipsePlanProperty& InFirstEllipse, const FGeomtryEllipsePlanProperty& InSecondEllipse, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork);

	bool ShiftSection(const TArray<float>& InShiftValue, FGeomtryEllipsePlanProperty& OutEllipse);

	bool ZoomSection(const TArray<float>& InZoomValue);

	static FName GetName()
	{
		return FName(TEXT("FGeomtryEllipsePlanProperty"));
	}

	friend FArchive& operator<<(FArchive& Ar, struct FGeomtryEllipsePlanProperty& EllipsePropertyToSave)
	{
		Ar << EllipsePropertyToSave.PlanBelongs;
		Ar << EllipsePropertyToSave.CenterLocationX;
		Ar << EllipsePropertyToSave.CenterLocationY;
		Ar << EllipsePropertyToSave.CenterLocationZ;
		Ar << EllipsePropertyToSave.ShortRadiusData;
		Ar << EllipsePropertyToSave.LongRadiusData;
		Ar << EllipsePropertyToSave.InterpPointCountData;
		return Ar;
	}

	bool operator!=(const FGeomtryEllipsePlanProperty& InData) const
	{
		return PlanBelongs != InData.PlanBelongs ||
			CenterLocationX != InData.CenterLocationX ||
			CenterLocationY != InData.CenterLocationY ||
			CenterLocationZ != InData.CenterLocationZ ||
			ShortRadiusData != InData.ShortRadiusData ||
			InterpPointCountData != InData.InterpPointCountData ||
			LongRadiusData != InData.LongRadiusData;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FGeomtryCubeProperty
{
	GENERATED_BODY()
public:
	UPROPERTY()
		FExpressionValuePair StartLocationX;
	UPROPERTY()
		FExpressionValuePair StartLocationY;
	UPROPERTY()
		FExpressionValuePair StartLocationZ;
	UPROPERTY()
		FExpressionValuePair EndLocationX;
	UPROPERTY()
		FExpressionValuePair EndLocationY;
	UPROPERTY()
		FExpressionValuePair EndLocationZ;

public:

	FVector StartLocation() const
	{
		return FVector(FCString::Atof(*StartLocationX.Value), FCString::Atof(*StartLocationY.Value), FCString::Atof(*StartLocationZ.Value));
	}
	FVector EndLocation() const
	{
		return FVector(FCString::Atof(*EndLocationX.Value), FCString::Atof(*EndLocationY.Value), FCString::Atof(*EndLocationZ.Value));
	}
	FGeomtryCubeProperty() :StartLocationX(FExpressionValuePair(0.0f)), StartLocationY(FExpressionValuePair(0.0f)), StartLocationZ(FExpressionValuePair(0.0f)), EndLocationX(FExpressionValuePair(500.0f)), EndLocationY(FExpressionValuePair(500.0f)), EndLocationZ(FExpressionValuePair(500.0f)) {}
	FGeomtryCubeProperty(const FExpressionValuePair& InStartLocationX, const FExpressionValuePair& InStartLocationY, const FExpressionValuePair& InStartLocationZ, const FExpressionValuePair& InEndLocationX, const FExpressionValuePair& InEndLocationY, const FExpressionValuePair& InEndLocationZ);

	bool Equal_Precise(const FGeomtryCubeProperty& InAnother)
	{
		const bool EqualStartX = StartLocationX.Equals_Precise(InAnother.StartLocationX);
		const bool EqualStartY = StartLocationY.Equals_Precise(InAnother.StartLocationY);
		const bool EqualStartZ = StartLocationZ.Equals_Precise(InAnother.StartLocationZ);
		const bool EqualEndX = EndLocationX.Equals_Precise(InAnother.EndLocationX);
		const bool EqualEndY = EndLocationY.Equals_Precise(InAnother.EndLocationY);
		const bool EqualEndZ = EndLocationZ.Equals_Precise(InAnother.EndLocationZ);

		return EqualStartX && EqualStartY && EqualStartZ && EqualEndX && EqualEndY && EqualEndZ;
	}

	void GenerateMeshAndFramwork(FPMCSection& OutMesh, TArray<TPair<FVector, FVector>>& OutFramwork) const;

	static FName GetName()
	{
		return FName(TEXT("FGeomtryCubeProperty"));
	}

	friend FArchive& operator<<(FArchive& Ar, struct FGeomtryCubeProperty& CubePropertyToSave)
	{
		Ar << CubePropertyToSave.StartLocationX;
		Ar << CubePropertyToSave.StartLocationY;
		Ar << CubePropertyToSave.StartLocationZ;
		Ar << CubePropertyToSave.EndLocationX;
		Ar << CubePropertyToSave.EndLocationY;
		Ar << CubePropertyToSave.EndLocationZ;
		return Ar;
	}

	bool operator!=(const FGeomtryCubeProperty& InData) const
	{
		return StartLocationX != InData.StartLocationX ||
			StartLocationY != InData.StartLocationY ||
			StartLocationZ != InData.StartLocationZ ||
			EndLocationX != InData.EndLocationX ||
			EndLocationY != InData.EndLocationY ||
			EndLocationZ != InData.EndLocationZ;
	}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UGeomtryItemData : public UObject
{
	GENERATED_BODY()

};

class DESIGNSTATION_API FGeometryPointData
{

public:
	FGeomtryPointProperty pointProperty;
	FVector position;
	int32	structSort;
	int32	displaySort;

public:
	FGeometryPointData()
		:pointProperty(FGeomtryPointProperty()), position(FVector()), structSort(-1), displaySort(-1)
	{}
	FGeometryPointData(const FGeometryPointData& inData)
	{
		pointProperty = inData.pointProperty;
		position = inData.position;
		structSort = inData.displaySort;
		displaySort = inData.displaySort;
	}
};


class DESIGNSTATION_API FGeometryPointGenerator
{

private:
	TArray<TSharedPtr<FGeometryPointData>> points;

	FVector normal;
public:
	FGeometryPointGenerator() :normal(FVector::ZAxisVector) {}
public:
	void setNormal(const FVector& inNormal);
	void addPoint(const FVector& inPos);
	void addPoint(const FVector& inPos, const FGeomtryPointProperty& inProperty);

	void addPoint(const FVector& inPos, const int32& inDisplaySort);
	void addPoint(const FGeometryPointData& inPoint);
	void addPoint(TSharedPtr <FGeometryPointData> inPoint);

	void initializePoints(const TArray<FVector>& inPoints);
	bool setProperty(const int32& structIndex, const FGeomtryPointProperty& inProperty);
	TSharedPtr <FGeometryPointData> setPropertyByDisplaySort(const int32& index, const FGeomtryPointProperty& inProperty);

	//void generatePointsActor(TArray<AGeometryPoint*>& pointActors);

	TArray<TSharedPtr<FGeometryPointData>> getPoints();

	int32 getPointCount();

	void computeStruct();
	void deletePoint(TSharedPtr <FGeometryPointData> inPoint);
	void clear();
};


class DESIGNSTATION_API FGeometryLineData
{

public:
	FGeomtryLineProperty lineProperty;
	TSharedPtr<FGeometryPointData> startPointData;
	TSharedPtr<FGeometryPointData> endPointData;

public:
	int32	getStructSort();
	int32	getDisplaySort();
	bool	containPoint(TSharedPtr<FGeometryPointData> inPoint);
};

class DESIGNSTATION_API FGeometryLineGenerator
{

private:
	TArray<TSharedPtr<FGeometryLineData>> lines;

	bool bLoop;
	FVector normal;
public:
	FGeometryLineGenerator() :bLoop(true), normal(FVector::ZeroVector) {}

public:
	void initializeLines(const TArray<TSharedPtr<FGeometryPointData>>& inPoints, bool bIsLoop);
	void initializeLines(const TArray<TSharedPtr<FGeometryLineData>>& inLines, bool bIsLoop);
	void setNormal(const FVector& inNormal);
	void addPoint(const TSharedPtr<FGeometryPointData>& inPoints, bool bIsLoop);
	bool getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex);
	bool getLineVertexOrder(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex);

	//void generateLinesActor(TArray<AGeometryLine*>& lineActors);
	TSharedPtr<FGeometryLineData> setLineProperty(const int32& index, const FGeomtryLineProperty& inProperty);
	void deletePoint(TSharedPtr<FGeometryPointData> inPoint,bool bIsLoop = true);
	void clear();
	TArray<TSharedPtr<FGeometryLineData>> getLines();
};
