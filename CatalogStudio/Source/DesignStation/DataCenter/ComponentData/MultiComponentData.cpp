#include "MultiComponentData.h"

FMultiComponentDataItem::FMultiComponentDataItem()
	: ID(-1)
	, KeyID(TEXT(""))
	, ComponentName(NSLOCTEXT("LOCTEXT_NAMESPACE", "DefaultCompNameKey", "None").ToString())
	, ComponentVisibility(FExpressionValuePair("1"))
	, ComponentID(FExpressionValuePair(""))
	, Description(TEXT(""))
	, Code(TEXT(""))
	, CodeExp(TEXT(""))
	, ComponentType(ECompType::None)
	, RefFileUUID(TEXT(""))
{}

FMultiComponentDataItem::FMultiComponentDataItem(const FMultiComponentDataDB& InOther)
	: ID(-1)
	, KeyID(InOther.id)
	, ComponentName(NSLOCTEXT("LOCTEXT_NAMESPACE", "DefaultCompNameKey", "None").ToString())
	, ComponentNameExp(NSLOCTEXT("LOCTEXT_NAMESPACE", "DefaultCompNameKey", "None").ToString())
	, ComponentVisibility(FExpressionValuePair(InOther.visibility_exp, InOther.visibility_value))
	, ComponentID(FExpressionValuePair(InOther.component_id_exp, InOther.component_id_value))
	, Description(InOther.description)
	, Code(TEXT(""))
	, CodeExp(TEXT(""))
	, ComponentType(ECompType::None)
	, ComponentLocation(FLocationProperty(FExpressionValuePair(InOther.location_x_exp, InOther.location_x_value), FExpressionValuePair(InOther.location_y_exp, InOther.location_y_value), FExpressionValuePair(InOther.location_z_exp, InOther.location_z_value)))
	, ComponentRotation(FRotationProperty(FExpressionValuePair(InOther.rotation_roll_exp, InOther.rotation_roll_value), FExpressionValuePair(InOther.rotation_pitch_exp, InOther.rotation_pitch_value), FExpressionValuePair(InOther.rotation_yaw_exp, InOther.rotation_pitch_exp)))
	, ComponentScale(FScaleProperty(FExpressionValuePair(InOther.scale_x_exp, InOther.scale_x_value), FExpressionValuePair(InOther.scale_y_exp, InOther.scale_y_value), FExpressionValuePair(InOther.scale_z_exp, InOther.scale_z_value)))
{

}

FMultiComponentDataItem::FMultiComponentDataItem(const FMultiComponentDataItem& InOther)
	:ID(InOther.ID)
	, KeyID(InOther.KeyID)
	, ComponentName(InOther.ComponentName)
	, ComponentNameExp(InOther.ComponentNameExp)
	, ComponentVisibility(InOther.ComponentVisibility)
	, ComponentID(InOther.ComponentID)
	, Description(InOther.Description)
	, Code(InOther.Code)
	, CodeExp(InOther.CodeExp)
	, ComponentType(InOther.ComponentType)
	, SingleComponentPath(InOther.SingleComponentPath)
	, ComponentLocation(InOther.ComponentLocation)
	, ComponentRotation(InOther.ComponentRotation)
	, ComponentScale(InOther.ComponentScale)
	, ComponentParameters(InOther.ComponentParameters)
	, SingleComponentData(InOther.SingleComponentData)
	, ChildComponent(InOther.ChildComponent)
	, RefFileUUID(InOther.RefFileUUID)
	, InheritParams(InOther.InheritParams)
{

}

bool FMultiComponentDataItem::Equal_Precise(const FMultiComponentDataItem& InOther)
{
	const bool EqualID = KeyID.Equals(InOther.KeyID);
	const bool EqualKeyID = KeyID.Equals(InOther.KeyID);
	const bool EqualComponentName = ComponentName.Equals(InOther.ComponentName);
	const bool EqualComponentNameExp = ComponentName.Equals(InOther.ComponentNameExp);
	const bool EqualComponentVisibility = ComponentVisibility.Equals_Precise(InOther.ComponentVisibility);
	const bool EqualComponentID = ComponentID.Equals_Precise(InOther.ComponentID);
	const bool EqualDescription = Description.Equals(InOther.Description);

	bool EqualCode = true;
	if (Code.IsNumeric())
	{
		const double CodeValue = FCString::Atod(*Code);
		const double OtherCodeValue = FCString::Atod(*InOther.Code);
		EqualCode = FMath::IsNearlyEqual(CodeValue, OtherCodeValue, 0.1f);
	}
	else
	{
		EqualCode = Code.Equals(InOther.Code);
	}

	bool EqualCodeExp = true;
	if (CodeExp.IsNumeric())
	{
	const double CodeExpValue = FCString::Atod(*CodeExp);
	const double OtherCodeExpValue = FCString::Atod(*InOther.CodeExp);
	EqualCodeExp = FMath::IsNearlyEqual(CodeExpValue, OtherCodeExpValue, 0.1f);
	}
	else
	{
	EqualCodeExp = CodeExp.Equals(InOther.CodeExp);
	}

	const bool EqualComponentType = ComponentType == InOther.ComponentType;
	const bool EqualSingleComponentPath = SingleComponentPath.Equals(InOther.SingleComponentPath);

	const bool EqualComponentLocation = ComponentLocation.Equals_Precise(InOther.ComponentLocation);
	const bool EqualComponentRotation = ComponentRotation.Equals_Precise(InOther.ComponentRotation);
	const bool EqualComponentScale = ComponentScale.Equals_Precise(InOther.ComponentScale);

	bool EqualComponentParameters = ComponentParameters.Num() == InOther.ComponentParameters.Num();
	if (EqualComponentParameters)
	{
		for (int32 i = 0; i < ComponentParameters.Num(); ++i)
		{
			if (!ComponentParameters[i].Equal_Precise(InOther.ComponentParameters[i]))
			{
				EqualComponentParameters = false;
				break;
			}
		}
	}

	const bool EqualSingleComponentData = SingleComponentData.Equals_Precise(InOther.SingleComponentData);

	bool EqualChildComponent = ChildComponent.Num() == InOther.ChildComponent.Num();
	if (EqualChildComponent)
	{
		for (int32 i = 0; i < ChildComponent.Num(); ++i)
		{
			if (!ChildComponent[i].Equal_Precise(InOther.ChildComponent[i]))
			{
				EqualChildComponent = false;
				break;
			}
		}
	}

	const bool EqualRefFileUUID = RefFileUUID.Equals(InOther.RefFileUUID);

	return EqualID && EqualKeyID && EqualComponentName && EqualComponentVisibility && EqualComponentID 
		&& EqualDescription && EqualCode && EqualCodeExp && EqualComponentType && EqualSingleComponentPath 
		&& EqualComponentLocation && EqualComponentRotation && EqualComponentScale && EqualComponentParameters 
		&& EqualSingleComponentData && EqualChildComponent && EqualRefFileUUID;
}

bool FMultiComponentDataItem::Equal_Precise_NoChild(const FMultiComponentDataItem& InOther)
{
	const bool EqualID = KeyID.Equals(InOther.KeyID);
	const bool EqualKeyID = KeyID.Equals(InOther.KeyID);
	const bool EqualComponentName = ComponentName.Equals(InOther.ComponentName);
	const bool EqualComponentNameExp = ComponentNameExp.Equals(InOther.ComponentNameExp);
	const bool EqualComponentVisibility = ComponentVisibility.Equals_Precise(InOther.ComponentVisibility);
	const bool EqualComponentID = ComponentID.Equals_Precise(InOther.ComponentID);
	const bool EqualDescription = Description.Equals(InOther.Description);

	bool EqualCode = true;
	if (Code.IsNumeric())
	{
		const double CodeValue = FCString::Atod(*Code);
		const double OtherCodeValue = FCString::Atod(*InOther.Code);
		EqualCode = FMath::IsNearlyEqual(CodeValue, OtherCodeValue, 0.1f);
	}
	else
	{
		EqualCode = Code.Equals(InOther.Code);
	}

	bool EqualCodeExp = true;
	if (CodeExp.IsNumeric())
	{
		const double CodeExpValue = FCString::Atod(*CodeExp);
		const double OtherCodeExpValue = FCString::Atod(*InOther.CodeExp);
		EqualCodeExp = FMath::IsNearlyEqual(CodeExpValue, OtherCodeExpValue, 0.1f);
	}
	else
	{
		EqualCodeExp = CodeExp.Equals(InOther.CodeExp);
	}

	const bool EqualComponentType = ComponentType == InOther.ComponentType;
	const bool EqualSingleComponentPath = true /*SingleComponentPath.Equals(InOther.SingleComponentPath)*/;

	const bool EqualComponentLocation = ComponentLocation.Equals_Precise(InOther.ComponentLocation);
	const bool EqualComponentRotation = ComponentRotation.Equals_Precise(InOther.ComponentRotation);
	const bool EqualComponentScale = ComponentScale.Equals_Precise(InOther.ComponentScale);

	bool EqualComponentParameters = ComponentParameters.Num() == InOther.ComponentParameters.Num();
	if (EqualComponentParameters)
	{
		for (int32 i = 0; i < ComponentParameters.Num(); ++i)
		{
			if (!ComponentParameters[i].Equal_Precise(InOther.ComponentParameters[i]))
			{
				EqualComponentParameters = false;
				break;
			}
		}
	}

	const bool EqualSingleComponentData = true/*SingleComponentData.Equals_Precise(InOther.SingleComponentData)*/;

	const bool EqualRefFileUUID = RefFileUUID.Equals(InOther.RefFileUUID);

	return EqualID && EqualKeyID && EqualComponentName && EqualComponentVisibility && EqualComponentID
		&& EqualDescription && EqualCode && EqualCodeExp && EqualComponentType && EqualSingleComponentPath
		&& EqualComponentLocation && EqualComponentRotation && EqualComponentScale && EqualComponentParameters
		&& EqualSingleComponentData && EqualRefFileUUID && EqualComponentNameExp;
}

bool FMultiComponentDataItem::CopyData(FMultiComponentDataItem& OutNewComponent) const
{
	OutNewComponent.KeyID = this->KeyID;
	OutNewComponent.ComponentName = this->ComponentName;
	OutNewComponent.ComponentNameExp = this->ComponentNameExp;
	OutNewComponent.ComponentVisibility = this->ComponentVisibility;
	OutNewComponent.ComponentID = this->ComponentID;
	OutNewComponent.Description = this->Description;
	OutNewComponent.Code = this->Code;
	OutNewComponent.CodeExp = this->CodeExp;
	OutNewComponent.ComponentType = this->ComponentType;
	OutNewComponent.SingleComponentPath = this->SingleComponentPath;
	OutNewComponent.SingleComponentData = this->SingleComponentData;
	OutNewComponent.ComponentLocation = this->ComponentLocation;
	OutNewComponent.ComponentRotation = this->ComponentRotation;
	OutNewComponent.ComponentScale = this->ComponentScale;
	OutNewComponent.ComponentParameters = this->ComponentParameters;
	OutNewComponent.ChildComponent.Empty();
	OutNewComponent.ChildComponent.AddZeroed(this->ChildComponent.Num());
	int32 i = 0;
	for (auto& Iter : this->ChildComponent)
	{
		if (false == Iter.CopyData(OutNewComponent.ChildComponent[i++]))
			return false;
	}
	OutNewComponent.RefFileUUID = this->RefFileUUID;
	OutNewComponent.InheritParams = this->InheritParams;
	return true;
}

int32 FMultiComponentDataItem::IsParameterExists(const FString& InParameterName) const
{
	int32 Index = ComponentParameters.IndexOfByPredicate([&](const FParameterData& InOther)->bool { return InOther.Data.name.Equals(InParameterName,ESearchCase::CaseSensitive); });
	UE_LOG(LogTemp, Log, TEXT("------- Index is:  %d  parameter name is: %s  --------"), Index, *InParameterName);
	return Index;
}

bool FMultiComponentDataItem::AddNewParameter(const FParameterData& InNewParameter)
{
	int32 Index = this->IsParameterExists(InNewParameter.Data.name);
	if (INDEX_NONE == Index)
	{
		this->ComponentParameters.Add(InNewParameter);
	}
	return INDEX_NONE == Index;
}

FParameterData FMultiComponentDataItem::DeleteParameter(const FString& InParameterID)
{
	FParameterData Result;
	int32 Index = ComponentParameters.IndexOfByPredicate([&](const FParameterData& InOther)->bool { return InOther.Data.id.Equals(InParameterID,ESearchCase::CaseSensitive); });
	if (INDEX_NONE != Index)
	{
		Result = ComponentParameters[Index];
		this->ComponentParameters.RemoveAt(Index);
	}
	return Result;
}

bool FMultiComponentDataItem::UpdateParameterExpressionAndValue(const FParameterData& InParameter)
{
	int32 Index = this->IsParameterExists(InParameter.Data.name);
	if (INDEX_NONE != Index)
	{
		this->ComponentParameters[Index].Data.expression = InParameter.Data.expression;
		this->ComponentParameters[Index].Data.value = InParameter.Data.value;
	}
	return INDEX_NONE != Index;
}

bool FMultiComponentDataItem::IsVisiable() const
{
	const float VisiableValue = FCString::Atof(*ComponentVisibility.Value);
	return !FMath::IsNearlyZero(VisiableValue, 0.01f);
}

bool FMultiComponentDataItem::IsVaild() const
{
	return false == KeyID.IsEmpty();
}

int64 FMultiComponentDataItem::GetComponentID() const
{
	if (ComponentID.Value.IsEmpty()) return INDEX_NONE;
	return FCString::Atoi64(*ComponentID.Value);
}

bool FMultiComponentDataItem::IsConstructComponent() const
{
#define CONSTRUCT_PARAMETER	TEXT("HDBJ")
	const int32 ParameterIndex = ComponentParameters.IndexOfByPredicate([](const FParameterData& InParameter) { return InParameter.Data.name.Equals(CONSTRUCT_PARAMETER,ESearchCase::CaseSensitive); });
#undef CONSTRUCT_PARAMETER
	if (INDEX_NONE != ParameterIndex)
	{
		const float ConstructValue = FCString::Atof(*ComponentParameters[ParameterIndex].Data.value);
		return FMath::IsNearlyZero(ConstructValue, 0.01f);
	}
	return true;
}

void FMultiComponentDataItem::GetComponentDataByFileInfo(FMultiComponentDataItem& OutCompData, FString InFileInfo) const
{
	if (InFileInfo.IsEmpty())
	{
		OutCompData.ShallowCopy(*this);
	}
	else if (0 == InFileInfo.Len() % 2)
	{
		const int32 ChildIndex = FCString::Atoi(*InFileInfo.Left(2));
		if (!ChildComponent.IsValidIndex(ChildIndex)) return;
		ChildComponent[ChildIndex].GetComponentDataByFileInfo(OutCompData, InFileInfo.Right(InFileInfo.Len() - 2));
	}
}

void FMultiComponentDataItem::SetComponentDataByFileInfo(const FMultiComponentDataItem& InCompData, FString InFileInfo)
{
	if (InFileInfo.IsEmpty())
	{
		*this = InCompData;
	}
	else if (0 == InFileInfo.Len() % 2)
	{
		const int32 ChildIndex = FCString::Atoi(*InFileInfo.Left(2));
		if (!ChildComponent.IsValidIndex(ChildIndex)) return;
		ChildComponent[ChildIndex].SetComponentDataByFileInfo(InCompData, InFileInfo.Right(InFileInfo.Len() - 2));
	}
}

int32 FMultiComponentData::CopyComponent(const int32& InSourceIndex)
{
	if (ComponentItems.IsValidIndex(InSourceIndex))
	{
		int32 NewIndex = ComponentItems.AddDefaulted();
		return ComponentItems[InSourceIndex].CopyData(ComponentItems[NewIndex]) ? NewIndex : -1;
	}
	return -1;
}

bool FMultiComponentData::SwapItem(const int32& Index1, const int32& Index2)
{
	if (ComponentItems.IsValidIndex(Index1) && ComponentItems.IsValidIndex(Index2))
	{
		ComponentItems.Swap(Index1, Index2);
		return true;
	}
	return false;
}

bool FMultiComponentData::RemoveComponent(const FString& InID)
{
	int32 Index = ComponentItems.IndexOfByPredicate([&](const FMultiComponentDataItem& InOther)->bool {return InOther.KeyID == InID; });
	if (INDEX_NONE != Index)
		ComponentItems.RemoveAt(Index);
	return INDEX_NONE != Index;
}

int32 FMultiComponentData::UpdateComponent(const FMultiComponentDataItem& InOther)
{
	int32 Index = ComponentItems.IndexOfByPredicate([&](const FMultiComponentDataItem& Another)->bool {return Another.KeyID == InOther.KeyID; });
	if (INDEX_NONE != Index)
	{
		if (ComponentItems[Index].ComponentID.Value.Equals(InOther.ComponentID.Value))
		{
			ComponentItems[Index].ShallowCopy(InOther);
		}
		else
		{
			ComponentItems[Index] = InOther;
		}
	}
	return Index;
}

void FMultiComponentData::GenerateID()
{
	int32 i = 0;
	for (auto& Iter : ComponentItems)
		Iter.ID = i++;
}
