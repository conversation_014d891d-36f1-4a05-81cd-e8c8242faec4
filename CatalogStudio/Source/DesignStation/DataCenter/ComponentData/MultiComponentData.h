#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CrossSectionDataDefine.h"
#include "ComponentPropertyData.h"
#include "SingleComponentDataDefine.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "MultiComponentData.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMultiComponentDataDB
{
	GENERATED_BODY()

public:

	UPROPERTY()
		FString id = TEXT("");

	UPROPERTY()
		FString visibility_exp = TEXT("");

	UPROPERTY()
		FString visibility_value = TEXT("");

	UPROPERTY()
		FString component_id_exp = TEXT("");

	UPROPERTY()
		FString component_id_value = TEXT("");

	UPROPERTY()
		FString description = TEXT("");

	UPROPERTY()
		FString location_x_exp = TEXT("");

	UPROPERTY()
		FString location_x_value = TEXT("");

	UPROPERTY()
		FString location_y_exp = TEXT("");

	UPROPERTY()
		FString location_y_value = TEXT("");

	UPROPERTY()
		FString location_z_exp = TEXT("");

	UPROPERTY()
		FString location_z_value = TEXT("");

	UPROPERTY()
		FString rotation_roll_exp = TEXT("");

	UPROPERTY()
		FString rotation_roll_value = TEXT("");

	UPROPERTY()
		FString rotation_pitch_exp = TEXT("");

	UPROPERTY()
		FString rotation_pitch_value = TEXT("");

	UPROPERTY()
		FString rotation_yaw_exp = TEXT("");

	UPROPERTY()
		FString rotation_yaw_value = TEXT("");

	UPROPERTY()
		FString scale_x_exp = TEXT("");

	UPROPERTY()
		FString scale_x_value = TEXT("");

	UPROPERTY()
		FString scale_y_exp = TEXT("");

	UPROPERTY()
		FString scale_y_value = TEXT("");

	UPROPERTY()
		FString scale_z_exp = TEXT("");

	UPROPERTY()
		FString scale_z_value = TEXT("");

	UPROPERTY()
		int32	component_order = 0;

	UPROPERTY()
		FString folder_id = TEXT("");

};

UENUM(BlueprintType)
enum class ECompType : uint8
{
	None = 0				UMETA(DisplayName = "Empty Component"),
	SingleCom				UMETA(DisplayName = "Single Component"),
	MultiCom				UMETA(DisplayName = "Multi Component"),
	ImportModel				UMETA(DisplayName = "Import Model from web"),
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMultiComponentDataItem
{
	GENERATED_BODY()
public:
	int32							ID;

	FString							KeyID;

	FString							ComponentName;

	FString							ComponentNameExp;

	FExpressionValuePair			ComponentVisibility;

	FExpressionValuePair			ComponentID;

	FString							Description;

	FString							Code;

	FString							CodeExp;

	ECompType						ComponentType;

	FString							SingleComponentPath;

	FLocationProperty				ComponentLocation;

	FRotationProperty				ComponentRotation;

	FScaleProperty					ComponentScale;

	TArray<FParameterData>			ComponentParameters;

	FSingleComponentProperty		SingleComponentData;

	TArray<FMultiComponentDataItem>	ChildComponent;

	FString RefFileUUID;

	TArray<FParameterData> InheritParams;

public:

	FMultiComponentDataItem();

	FMultiComponentDataItem(const FMultiComponentDataDB& InOther);

	FMultiComponentDataItem(const FMultiComponentDataItem& InOther);

	bool Equal_Precise(const FMultiComponentDataItem& InOther);

	//used for compare this node data ( dat file convert)
	bool Equal_Precise_NoChild(const FMultiComponentDataItem& InOther);

	bool CopyData(FMultiComponentDataItem& OutNewComponent) const;

	int32 IsParameterExists(const FString& InParameterName) const;

	bool AddNewParameter(const FParameterData& InNewParameter);

	FParameterData DeleteParameter(const FString& InParameterID);

	bool UpdateParameterExpressionAndValue(const FParameterData& InParameter);

	bool IsVaild() const;

	bool IsVisiable() const;

	int64 GetComponentID() const;

	bool IsConstructComponent() const;

	void SetInheritParams(const TArray<FParameterData>& InParams) 
	{ 
		InheritParams = InParams; 
	}

	friend FArchive& operator<<(FArchive& Ar, struct FMultiComponentDataItem& MultiComponentDataToSave)
	{
		Ar << MultiComponentDataToSave.ComponentName;
		Ar << MultiComponentDataToSave.ComponentNameExp;
		Ar << MultiComponentDataToSave.ComponentID;
		Ar << MultiComponentDataToSave.ComponentVisibility;
		Ar << MultiComponentDataToSave.Description;
		Ar << MultiComponentDataToSave.Code;
		Ar << MultiComponentDataToSave.CodeExp;
		Ar << MultiComponentDataToSave.ComponentLocation;
		Ar << MultiComponentDataToSave.ComponentRotation;
		Ar << MultiComponentDataToSave.ComponentScale;
		Ar << MultiComponentDataToSave.ComponentParameters;
		Ar << MultiComponentDataToSave.RefFileUUID;
		return Ar;
	}

	bool operator!=(const FMultiComponentDataItem& InData) const
	{
		if (KeyID != InData.KeyID ||
			ComponentVisibility != InData.ComponentVisibility ||
			ComponentID != InData.ComponentID ||
			Description != InData.Description ||
			Code != InData.Code ||
			CodeExp != InData.CodeExp ||
			ComponentLocation != InData.ComponentLocation ||
			ComponentRotation != InData.ComponentRotation ||
			ComponentScale != InData.ComponentScale)
		{
			return true;
		}
		if (ComponentParameters.Num() != InData.ComponentParameters.Num())
			return true;
		for (int32 i = 0; i < ComponentParameters.Num(); ++i)
		{
			if (ComponentParameters[i] != InData.ComponentParameters[i])
			{
				return true;
			}
		}
		return false;
	}

	void ShallowCopy(const FMultiComponentDataItem& InData)
	{
		ID = InData.ID;
		KeyID = InData.KeyID;
		ComponentName = InData.ComponentName;
		ComponentNameExp = InData.ComponentNameExp;
		ComponentVisibility = InData.ComponentVisibility;
		ComponentID = InData.ComponentID;
		Description = InData.Description;
		Code = InData.Code;
		CodeExp = InData.CodeExp;
		ComponentLocation = InData.ComponentLocation;
		ComponentRotation = InData.ComponentRotation;
		ComponentScale = InData.ComponentScale;
		ComponentParameters = InData.ComponentParameters;
		ComponentType = InData.ComponentType;
		RefFileUUID = InData.RefFileUUID;
	}

	void operator=(const FMultiComponentDataItem& InData)
	{
		ID = InData.ID;
		KeyID = InData.KeyID;
		ComponentName = InData.ComponentName;
		ComponentNameExp = InData.ComponentNameExp;
		ComponentVisibility = InData.ComponentVisibility;
		ComponentID = InData.ComponentID;
		Description = InData.Description;
		Code = InData.Code;
		CodeExp = InData.CodeExp;
		ComponentType = InData.ComponentType;
		SingleComponentPath = InData.SingleComponentPath;
		SingleComponentData = InData.SingleComponentData;
		ComponentLocation = InData.ComponentLocation;
		ComponentRotation = InData.ComponentRotation;
		ComponentScale = InData.ComponentScale;
		ComponentParameters = InData.ComponentParameters;
		ChildComponent = InData.ChildComponent;
		RefFileUUID = InData.RefFileUUID;
		InheritParams = InData.InheritParams;
	}

	void ChangeItem(const FMultiComponentDataItem& InData)
	{
		ComponentName = InData.ComponentName;
		ComponentNameExp = InData.ComponentNameExp;
		ComponentType = InData.ComponentType;
		Code = InData.Code;
		CodeExp = InData.CodeExp;
		SingleComponentPath = InData.SingleComponentPath;
		SingleComponentData = InData.SingleComponentData;
		ChildComponent = InData.ChildComponent;
	}

	void ResetRefrence()
	{
		ComponentName.Empty();
		ComponentType = ECompType::None;
		Code.Empty();
		CodeExp.Empty();
		SingleComponentPath.Empty();
		SingleComponentData.ComponentItems.Empty();
		ChildComponent.Empty();
	}
	
	void Clear()
	{
		ID = INDEX_NONE;
		KeyID.Empty();
		ComponentName.Empty();
		ComponentNameExp.Empty();
		ComponentVisibility.ResetUnit();
		ComponentID.Expression = TEXT("");
		ComponentID.Value = TEXT("");
		Description.Empty();
		Code.Empty();
		CodeExp.Empty();
		ComponentType = ECompType::None;
		SingleComponentPath.Empty();
		ComponentLocation.Reset();
		ComponentRotation.Reset();
		ComponentScale.Reset();
		ComponentParameters.Empty();
		SingleComponentData.ComponentItems.Empty();
		ChildComponent.Empty();
		RefFileUUID.Empty();
		InheritParams.Reset();
	}

	void GetComponentDataByFileInfo(FMultiComponentDataItem& OutCompData, FString InFileInfo) const;

	void SetComponentDataByFileInfo(const FMultiComponentDataItem& InCompData, FString InFileInfo);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMultiComponentData
{
	GENERATED_BODY()
public:
	TArray<FMultiComponentDataItem>	ComponentItems;

	bool SwapItem(const int32& Index1, const int32& Index2);

	int32 CopyComponent(const int32& InSourceIndex);

	bool RemoveComponent(const FString& InID);

	int32 UpdateComponent(const FMultiComponentDataItem& InOther);

	void GenerateID();

	friend FArchive& operator<<(FArchive& Ar, struct FMultiComponentData& MultiComponentPropertyToSave)
	{
		Ar << MultiComponentPropertyToSave.ComponentItems;
		return Ar;
	}

	bool operator!=(const FMultiComponentData& InData) const
	{
		if (ComponentItems.Num() != InData.ComponentItems.Num())
			return true;
		for (int32 i = 0; i < ComponentItems.Num(); ++i)
		{
			if (ComponentItems[i] != InData.ComponentItems[i])
				return true;
		}
		return false;
	}

	void operator=(const FMultiComponentData& InData)
	{
		ComponentItems = InData.ComponentItems;
	}
};
