// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterPropertyData.h"
#include "Runtime/Engine/Classes/Kismet/KismetTextLibrary.h"



FExpressionValuePair FExpressionValuePair::operator+(const float& InValue) const
{
	float NewValue = FCString::Atof(*this->Value) + InValue;
	NewValue = UParameterPropertyData::Round(NewValue, 1);
	return FExpressionValuePair(NewValue);
}

FExpressionValuePair FExpressionValuePair::operator+=(const float& InValue)
{
	float NewValue = FCString::Atof(*this->Value) + InValue;
	/*NewValue = UParameterPropertyData::Round(NewValue, 1);
	this->Value = FString::SanitizeFloat(NewValue);*/
	this->Expression = FString::Printf(TEXT("%.000001f"), NewValue);
	this->Value = FString::Printf(TEXT("%.000001f"), NewValue);
	return *this;
}

bool FExpressionValuePair::operator!=(const FExpressionValuePair& InAnother) const
{
	return !Expression.Equals(InAnother.Expression) || !Value.Equals(InAnother.Value);
}

bool FExpressionValuePair::operator==(const FExpressionValuePair& InAnother) const
{
	return Expression.Equals(InAnother.Expression) && Value.Equals(InAnother.Value);
}

bool FExpressionValuePair::Equals_Precise(const FExpressionValuePair& InData)
{
	bool ExpressEqual = false;
	{//key
		if (Expression.IsNumeric() && InData.Expression.IsNumeric())
		{
			const double LValue = FCString::Atod(*Expression);
			const double RValue = FCString::Atod(*InData.Expression);
			if (FMath::IsNearlyEqual(LValue, RValue, 0.01))
			{
				ExpressEqual = true;
			}
		}
		else
		{
			if (Expression.Equals(InData.Expression))
			{
				ExpressEqual = true;
			}
		}
	}

	bool ValueEquals = false;
	{//value
		if (Value.IsNumeric())
		{
			const double LValue = FCString::Atod(*Value);
			const double RValue = FCString::Atod(*InData.Value);
			if (FMath::IsNearlyEqual(LValue, RValue, 0.01))
			{
				ValueEquals = true;
			}
		}
		else
		{
			if (Value.Equals(InData.Value))
			{
				ValueEquals = true;
			}
		}
	}

	return ExpressEqual && ValueEquals;
}

float UParameterPropertyData::Round(const float& InValue, const int32& InDigits)
{
	if (InDigits > 0)
	{
		float Integer = FMath::FloorToFloat(InValue);
		float R = FMath::Pow(10.0f, static_cast<float>(InDigits));
		float Fraction = (InValue - Integer) * R;
		Fraction = FMath::RoundHalfToEven(Fraction) / R;
		return Integer + Fraction;
	}
	else
	{
		return FMath::CeilToFloat(InValue);
	}
}

float UParameterPropertyData::ConvertToUeValue(const float& InValue)
{
	return InValue / 10.0f;
}

float UParameterPropertyData::ConvertToUIValue(const float& InValue)
{
	return InValue * 10.0f;
}

FVector UParameterPropertyData::ConvertToUeValue(const FVector& InValue)
{
	return InValue * 0.1f;
}

FVector UParameterPropertyData::ConvertToUIValue(const FVector& InValue)
{
	return InValue * 10.0f;
}

float UParameterPropertyData::FormatParameterValue(const FText& InValue)
{
	float Value = FCString::Atof(*InValue.ToString());
	Value = UParameterPropertyData::Round(Value, 1);
	return Value;
}

FText UParameterPropertyData::FormatParameterValue(const FString& InValue)
{
	if (InValue.IsNumeric())
	{

		FString LStr = TEXT("");
		FString RStr = TEXT("");
		if (InValue.Contains(TEXT(".")))
		{
			InValue.Split(TEXT("."), &LStr, &RStr);
		}
		else
		{
			LStr = InValue;
		}
		if (LStr.Len() >= 8)
		{
			return FText::FromString(LStr);
		}

		float Value = FCString::Atof(*InValue);
		//Value = UParameterPropertyData::Round(Value, 1);
		FString ValueStr = FString::Printf(TEXT("%.1f"), Value);
		//FString ValueStr = FString::SanitizeFloat(Value, 1);
		//UE_LOG(LogTemp, Log, TEXT("Value=%f  ValueStr=%s TestStr=%s"), Value, *ValueStr, *TestStr);
		//int32 DotLocation = ValueStr.Find(TEXT("."));
		//if (INDEX_NONE != DotLocation)
			//ValueStr = ValueStr.Left(DotLocation + 2);
		return FText::FromString(ValueStr);
	}
	else
	{
		return FText::FromString(InValue);
	}
}