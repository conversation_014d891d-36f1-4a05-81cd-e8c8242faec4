// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "ParameterPropertyData.generated.h"


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FExpressionValuePair
{
	GENERATED_BODY()

public:
	UPROPERTY()
		FString Expression;
	UPROPERTY()
		FString Value;
public:
	FExpressionValuePair() :Expression(TEXT("0")), Value(TEXT("0")) {  }
	explicit FExpressionValuePair(const FString& InExpression, const FString& InValue) :Expression(InExpression), Value(InValue) {  }
	explicit FExpressionValuePair(const FString& InValue) :Expression(InValue), Value(InValue) {  }
	explicit FExpressionValuePair(const float& InValue) :Expression(FString::SanitizeFloat(InValue)), Value(FString::SanitizeFloat(InValue)) {  }
	explicit FExpressionValuePair(const int32& InValue) :Expression(UKismetStringLibrary::Conv_IntToString(InValue)), Value(UKismetStringLibrary::Conv_IntToString(InValue)) {  }
	FExpressionValuePair operator+(const float& InValue) const;
	FExpressionValuePair operator+=(const float& InValue);
	bool operator!=(const FExpressionValuePair& InAnother) const;
	bool operator==(const FExpressionValuePair& InAnother) const;

	void Reset()
	{
		Expression = TEXT("0");
		Value = TEXT("0");
	}
	void ResetUnit()
	{
		Expression = TEXT("1");
		Value = TEXT("1");
	}

	void FormatValue()
	{
		int32 DotIndex = INDEX_NONE;
		Value.FindChar('.', DotIndex);
		if (DotIndex != INDEX_NONE)
		{
			Value = Value.Left(DotIndex);
		}
	}

	FString FormatValueRet()
	{
		FormatValue();
		return Value;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FExpressionValuePair& ExpressionValuePair)
	{
		Ar << ExpressionValuePair.Expression;
		Ar << ExpressionValuePair.Value;
		return Ar;
	}

	void CopyData(const FExpressionValuePair& InData)
	{
		Expression = InData.Expression;
		Value = InData.Value;
	}

	void operator=(const FExpressionValuePair& InData)
	{
		Expression = InData.Expression;
		Value = InData.Value;
	}

	FString ToString() const
	{
		return FString::Printf(TEXT("Expression=%s;Value=%s"), *Expression, *Value);
	}

	bool IsValid() const
	{
		return !Value.IsEmpty();
	}

	bool Equals_Precise(const FExpressionValuePair& InData);
};

/**
	*
	*/
UCLASS()
class DESIGNSTATION_API UParameterPropertyData : public UObject
{
	GENERATED_BODY()

public:

	static float Round(const float& InValue, const int32& InDigits);

	static FText FormatParameterValue(const FString& InValue);

	static float FormatParameterValue(const FText& InValue);

	static float ConvertToUeValue(const float& InValue);

	static FVector ConvertToUeValue(const FVector& InValue);

	static FVector ConvertToUIValue(const FVector& InValue);

	static float ConvertToUIValue(const float& InValue);
};
