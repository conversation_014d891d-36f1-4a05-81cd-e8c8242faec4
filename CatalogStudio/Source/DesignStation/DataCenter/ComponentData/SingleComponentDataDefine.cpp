#include "SingleComponentDataDefine.h"


void FSingleComponentItem::CopyData(struct FSingleComponentItem& OutComponent)
{
	OutComponent.SectionName = this->SectionName;
	OutComponent.ComponentMaterial = this->ComponentMaterial;
	OutComponent.OperatorSection = this->OperatorSection;
	OutComponent.SectionOperation = this->SectionOperation;
	OutComponent.VisibleParam = this->VisibleParam;
	OutComponent.ThumbnailPath = this->ThumbnailPath;
	OutComponent.ComponentSource = this->ComponentSource;
	OutComponent.ImportMesh = this->ImportMesh;
	OutComponent.SingleComponentLocation = this->SingleComponentLocation;
	OutComponent.SingleComponentRotation = this->SingleComponentRotation;
	OutComponent.SingleComponentScale = this->SingleComponentScale;
	OutComponent.PakRefPath = this->PakRefPath;
	OutComponent.PakRelativeFilePath = this->PakRelativeFilePath;
	OutComponent.RefFileUUID = this->RefFileUUID;
}

bool FSingleComponentItem::Equal_Precise(const FSingleComponentItem& InData)
{
	const bool EqualName = SectionName.Equals(InData.SectionName);
	const bool EqualThumbnail = ThumbnailPath.Equals(InData.ThumbnailPath);

	const bool EqualOperatorSection = OperatorSection.Equal_Precise(InData.OperatorSection);

	const bool EqualMaterial = ComponentMaterial.Equals_Precise(InData.ComponentMaterial);

	const bool EqualSectionOperation = SectionOperation.Equals_Precise(InData.SectionOperation);

	const bool EqualVisibleParam = VisibleParam.Equals_Precise(InData.VisibleParam);

	const bool EqualComponentSource = ComponentSource == InData.ComponentSource;

	bool EqualImportMesh = ImportMesh.Num() == InData.ImportMesh.Num();
	if (EqualImportMesh)
	{
		for (int32 Index = 0; Index < ImportMesh.Num(); ++Index)
		{
			if (!ImportMesh[Index].Equal_Precise(InData.ImportMesh[Index]))
			{
				EqualImportMesh = false;
				break;
			}
		}
	}

	const bool EqualRefPath = PakRefPath.Equals(InData.PakRefPath);
	const bool EqualRelativeFilePath = PakRelativeFilePath.Equals(InData.PakRelativeFilePath);

	const bool EqualLocation = SingleComponentLocation.Equal_Precise(InData.SingleComponentLocation);
	const bool EqualRotation = SingleComponentRotation.Equal_Precise(InData.SingleComponentRotation);
	const bool EqualScale = SingleComponentScale.Equal_Precise(InData.SingleComponentScale);

	const bool EqualRefFileUUID = RefFileUUID.Equals(InData.RefFileUUID);

	return EqualName && EqualThumbnail && EqualOperatorSection && EqualMaterial && EqualSectionOperation 
		&& EqualVisibleParam && EqualComponentSource && EqualImportMesh && EqualRefPath && EqualRelativeFilePath
		&& EqualLocation && EqualRotation && EqualScale && EqualRefFileUUID;

}

int32 FSingleComponentProperty::GetAllDependFiles(TArray<FString>& OutDependFiles) const
{
	for (auto& ComponentIter : ComponentItems)
	{
		if (false == ComponentIter.ThumbnailPath.IsEmpty())
			OutDependFiles.AddUnique(ComponentIter.ThumbnailPath);
		if (ESingleComponentSource::EImportPAK == ComponentIter.ComponentSource && !ComponentIter.PakRelativeFilePath.IsEmpty())
			OutDependFiles.AddUnique(ComponentIter.PakRelativeFilePath);
	}
	return OutDependFiles.Num();
}

void FSingleComponentProperty::Empty()
{
	ComponentItems.Empty();
}