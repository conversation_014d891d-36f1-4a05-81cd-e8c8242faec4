#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CrossSectionDataDefine.h"
#include "ComponentEnumData.h"
#include "ComponentPropertyData.h"
#include "SingleComponentDataDefine.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSingleComponentItem
{
	GENERATED_USTRUCT_BODY()
public:
	FString SectionName;
	FString ThumbnailPath;
	FCrossSectionData OperatorSection;
	FExpressionValuePair ComponentMaterial;
	FSectionOperation SectionOperation;
	FExpressionValuePair VisibleParam;
	ESingleComponentSource		ComponentSource;


	//For import section
	TArray<FImportMeshSection>	ImportMesh;
	//For pak import section
	FString	PakRefPath;//pak文件的引用路径
	FString PakRelativeFilePath;//pak文件的相对路径,上传及下载时使用

	FLocationProperty				SingleComponentLocation;
	FRotationProperty				SingleComponentRotation;
	FScaleProperty					SingleComponentScale;

	FString RefFileUUID;

	FSingleComponentItem() 
		:SectionName(TEXT(""))
		, ThumbnailPath(TEXT(""))
		, OperatorSection()
		, ComponentMaterial(FExpressionValuePair(TEXT(""),TEXT("")))
		, VisibleParam(1)
		, ComponentSource(ESingleComponentSource::ECustom)
		, PakRefPath(TEXT(""))
		, PakRelativeFilePath(TEXT(""))
		, SingleComponentLocation(FLocationProperty())
		, SingleComponentRotation(FRotationProperty())
		, SingleComponentScale(FScaleProperty())
		, RefFileUUID(TEXT(""))
	{}

	void CopyData(struct FSingleComponentItem& OutComponent);

	friend FArchive& operator<<(FArchive& Ar, struct FSingleComponentItem& SingleComponentItemToSave)
	{
		Ar << SingleComponentItemToSave.SectionName;
		Ar << SingleComponentItemToSave.ThumbnailPath;
		Ar << SingleComponentItemToSave.OperatorSection;
		Ar << SingleComponentItemToSave.SectionOperation;
		Ar << SingleComponentItemToSave.ComponentMaterial;
		Ar << SingleComponentItemToSave.VisibleParam;
		Ar << SingleComponentItemToSave.ComponentSource;
		Ar << SingleComponentItemToSave.ImportMesh;
		Ar << SingleComponentItemToSave.SingleComponentLocation;
		Ar << SingleComponentItemToSave.SingleComponentRotation;
		Ar << SingleComponentItemToSave.SingleComponentScale;
		Ar << SingleComponentItemToSave.PakRefPath;
		Ar << SingleComponentItemToSave.PakRelativeFilePath;
		Ar << SingleComponentItemToSave.RefFileUUID;
		return Ar;
	}

	bool operator!=(const FSingleComponentItem& InData) const
	{
		if (SectionName != InData.SectionName || ThumbnailPath != InData.ThumbnailPath || VisibleParam != InData.VisibleParam || ComponentSource != InData.ComponentSource)
			return true;
		if (ESingleComponentSource::ECustom == ComponentSource)
		{
			return OperatorSection != InData.OperatorSection ||
				SectionOperation != InData.SectionOperation ||
				ComponentMaterial != InData.ComponentMaterial;
		}
		else if (ESingleComponentSource::EImportFBX == ComponentSource)
		{
			if (ImportMesh.Num() != InData.ImportMesh.Num())
				return true;
			int32 i = 0;
			while (i < ImportMesh.Num())
			{
				if (ImportMesh[i] != InData.ImportMesh[i])
					return true;
				++i;
			}
		}
		else if (ESingleComponentSource::EImportPAK == ComponentSource)
		{
			bool bEqual = PakRefPath.Equals(InData.PakRefPath) && PakRelativeFilePath.Equals(InData.PakRelativeFilePath);
			if (!bEqual) return true;
		}
		return SingleComponentLocation != InData.SingleComponentLocation ||
			SingleComponentRotation != InData.SingleComponentRotation ||
			SingleComponentScale != InData.SingleComponentScale;
	}

	bool Equal_Precise(const FSingleComponentItem& InData);
	

	//void operator==(const FSingleComponentItem& InData)
	//{
	//	SectionName = InData.SectionName;
	//	ThumbnailPath = InData.ThumbnailPath;
	//	VisibleParam = InData.VisibleParam;
	//	ComponentSource = InData.ComponentSource;
	//	if (ESingleComponentSource::ECustom == ComponentSource)
	//	{
	//		OperatorSection = InData.OperatorSection;
	//		SectionOperation = InData.SectionOperation;
	//		ComponentMaterial = InData.ComponentMaterial;
	//	}
	//	else if (ESingleComponentSource::EImportFBX == ComponentSource)
	//	{
	//		ImportMesh = InData.ImportMesh;
	//		SingleComponentLocation = InData.SingleComponentLocation;
	//		SingleComponentRotation = InData.SingleComponentRotation;
	//		SingleComponentScale = InData.SingleComponentScale;
	//	}
	//	else if (ESingleComponentSource::EImportPAK == ComponentSource)
	//	{

	//	}
	//}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FSingleComponentProperty
{
	GENERATED_USTRUCT_BODY()
public:
	TArray<FSingleComponentItem> ComponentItems;

	bool Equals_Precise(const FSingleComponentProperty& InData)
	{
		bool bEqual = ComponentItems.Num() == InData.ComponentItems.Num();
		if (bEqual)
		{
			for (int32 i = 0; i < ComponentItems.Num(); ++i)
			{
				if (!ComponentItems[i].Equal_Precise(InData.ComponentItems[i]))
				{
					bEqual = false;
					break;
				}
			}
		}

		return bEqual;
	}

	bool IsValid() const
	{
		return 0 != ComponentItems.Num();
	}

	int32 GetAllDependFiles(TArray<FString>& OutDependFiles) const;

	friend FArchive& operator<<(FArchive& Ar, struct FSingleComponentProperty& SingleComponentPropertyToSave)
	{
		Ar << SingleComponentPropertyToSave.ComponentItems;
		return Ar;
	}

	bool operator!=(const FSingleComponentProperty& InData) const
	{
		if (ComponentItems.Num() != InData.ComponentItems.Num())
			return true;
		int32 i = 0;
		while (i < ComponentItems.Num())
		{
			if (ComponentItems[i] != InData.ComponentItems[i])
				return true;
			++i;
		}
		return false;
	}

	void operator=(const FSingleComponentProperty& InData)
	{
		ComponentItems = InData.ComponentItems;
	}

	void Empty();
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API USingleComponentDataDefine : public UObject
{
	GENERATED_BODY()
	
};