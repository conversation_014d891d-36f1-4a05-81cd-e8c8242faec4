// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryRelativeLibrary.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"

DEFINE_LOG_CATEGORY(GeometryRelativeLibraryLog);

bool UGeometryRelativeLibrary::CalculatePolygonNormal(const TArray<FVector>& InPlan, FVector& OutNormal)
{
	if (InPlan.Num() < 3)
		return false;
	float MaxForward = 0.0f;
	int32 MaxForwardIndex = 0;
	FVector ForwardDir = InPlan[1] - InPlan[0];
	ForwardDir.Normalize();
	for (int32 i = 1; i < InPlan.Num(); ++i)
	{
		float Dis = FMath::Abs(FVector::DotProduct(InPlan[i] - InPlan[0], ForwardDir));
		if (Dis > MaxForward)
		{
			MaxForward = Dis;
			MaxForwardIndex = i;
		}
	}
	int32 PreIndex = (MaxForwardIndex - 1 + InPlan.Num()) % InPlan.Num();
	int32 NexIndex = (MaxForwardIndex + 1) % InPlan.Num();
	UE_LOG(LogTemp, Log, TEXT("%d - %d  -  %d"), NexIndex, PreIndex, MaxForwardIndex);
	OutNormal = FVector::CrossProduct(InPlan[MaxForwardIndex] - InPlan[PreIndex], InPlan[NexIndex] - InPlan[MaxForwardIndex]);
	OutNormal.Normalize();
	return true;
}

bool UGeometryRelativeLibrary::JudgePolygonVerticesOrder(FPolygonData& InPolygon)
{
	if (InPolygon.PolygonVertice.Num() >= 3)
	{
		float MaxX = -999999.0f;
		int32 MaxXVerticeIndex = -1;
		for (int32 i = 0; i < InPolygon.PolygonVertice.Num(); ++i)
		{
			const float Axis = (EPlanPolygonBelongs::EXY_Plan == InPolygon.PolygonPlan) ? InPolygon.PolygonVertice[i].X : ((EPlanPolygonBelongs::EYZ_Plan == InPolygon.PolygonPlan) ? InPolygon.PolygonVertice[i].Y : InPolygon.PolygonVertice[i].Z);
			if (Axis > MaxX)
			{
				MaxX = Axis;
				MaxXVerticeIndex = i;
			}
		}
		if (-1 != MaxXVerticeIndex)
		{
			const int32 PreVerticeIndex = (0 == MaxXVerticeIndex) ? InPolygon.PolygonVertice.Num() - 1 : MaxXVerticeIndex - 1;
			const int32 NextVerticeIndex = (MaxXVerticeIndex + 1) % InPolygon.PolygonVertice.Num();
			FVector VectorPre = InPolygon.PolygonVertice[MaxXVerticeIndex] - InPolygon.PolygonVertice[PreVerticeIndex];
			FVector VectorNext = InPolygon.PolygonVertice[NextVerticeIndex] - InPolygon.PolygonVertice[MaxXVerticeIndex];
			FVector T = FVector::CrossProduct(VectorPre, VectorNext);
			if (EPlanPolygonBelongs::EXY_Plan == InPolygon.PolygonPlan)
			{
				return (T.Z > 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::EClockWise, true) : ((T.Z < 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::ECounterClockWise, true) : false);
			}
			else if (EPlanPolygonBelongs::EYZ_Plan == InPolygon.PolygonPlan)
			{
				return (T.X > 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::EClockWise, true) : ((T.X < 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::ECounterClockWise, true) : false);
			}
			else if (EPlanPolygonBelongs::EXZ_Plan == InPolygon.PolygonPlan)
			{
				return (T.Y > 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::EClockWise, true) : ((T.Y < 0) ? (InPolygon.PolygonVerticesOrder = EPolygonVerticesOrder::ECounterClockWise, true) : false);
			}
		}
	}
	return false;
}

#define JUDGE_POLYGON_VERTICE_TYPE(AXIS) 			if (EPolygonVerticesOrder::EClockWise == InPolygonOrder)\
{\
	OutVerticeType = VectorCross.AXIS > 0 ? EVerticeType::EConvex : EVerticeType::EConcave;\
	Res = true;\
}\
else if (EPolygonVerticesOrder::ECounterClockWise == InPolygonOrder)\
{\
	OutVerticeType = VectorCross.AXIS < 0 ? EVerticeType::EConvex : EVerticeType::EConcave;\
	Res = true;\
}\

bool UGeometryRelativeLibrary::JudgePolygonVerticesType(const FVector& InPrePoint, const FVector& InNextPoint, const FVector& InTestPoint, EPlanPolygonBelongs InPlanPolygonBelongs, const EPolygonVerticesOrder& InPolygonOrder, EVerticeType& OutVerticeType)
{
	FVector VectorPre = InTestPoint - InPrePoint;
	FVector VectorNext = InNextPoint - InTestPoint;
	FVector VectorCross = FVector::CrossProduct(VectorPre, VectorNext);
	if (!VectorCross.IsNearlyZero())
	{
		bool Res = false;
		if (EPlanPolygonBelongs::EXY_Plan == InPlanPolygonBelongs)
		{
			JUDGE_POLYGON_VERTICE_TYPE(Z)
		}
		else if (EPlanPolygonBelongs::EYZ_Plan == InPlanPolygonBelongs)
		{
			JUDGE_POLYGON_VERTICE_TYPE(X)
		}
		else if (EPlanPolygonBelongs::EXZ_Plan == InPlanPolygonBelongs)
		{
			JUDGE_POLYGON_VERTICE_TYPE(Y)
		}
		return Res;
	}
	return false;
}

#undef JUDGE_POLYGON_VERTICE_TYPE

bool UGeometryRelativeLibrary::GenerateLineMesh(const FVector& InPointA, const FVector& InPointB, const float& InLineThickness, FPMCSection& OutLineMesh)
{
	FVector Offset = InPointB - InPointA;
	FVector RightVector = Offset.Rotation().RotateVector(FVector::RightVector);
	RightVector.Normalize();
	FVector UpVector = Offset.Rotation().RotateVector(FVector::UpVector);
	UpVector.Normalize();
	const float HalfSize = InLineThickness / 2.0f;
	OutLineMesh.Vertexes.AddZeroed(8);
	OutLineMesh.Vertexes[0] = HalfSize * (RightVector + UpVector) + InPointA;
	OutLineMesh.Vertexes[1] = HalfSize * (RightVector - UpVector) + InPointA;
	OutLineMesh.Vertexes[2] = HalfSize * (-RightVector - UpVector) + InPointA;
	OutLineMesh.Vertexes[3] = HalfSize * (-RightVector + UpVector) + InPointA;
	OutLineMesh.Vertexes[4] = OutLineMesh.Vertexes[0] + Offset;
	OutLineMesh.Vertexes[5] = OutLineMesh.Vertexes[1] + Offset;
	OutLineMesh.Vertexes[6] = OutLineMesh.Vertexes[2] + Offset;
	OutLineMesh.Vertexes[7] = OutLineMesh.Vertexes[3] + Offset;
	OutLineMesh.Triangles.AddZeroed(36);
	OutLineMesh.Triangles[0] = 0;
	OutLineMesh.Triangles[1] = 2;
	OutLineMesh.Triangles[2] = 1;
	OutLineMesh.Triangles[3] = 0;
	OutLineMesh.Triangles[4] = 3;
	OutLineMesh.Triangles[5] = 2;
	OutLineMesh.Triangles[6] = 0;
	OutLineMesh.Triangles[7] = 4;
	OutLineMesh.Triangles[8] = 7;
	OutLineMesh.Triangles[9] = 0;
	OutLineMesh.Triangles[10] = 7;
	OutLineMesh.Triangles[11] = 3;
	OutLineMesh.Triangles[12] = 4;
	OutLineMesh.Triangles[13] = 5;
	OutLineMesh.Triangles[14] = 6;
	OutLineMesh.Triangles[15] = 4;
	OutLineMesh.Triangles[16] = 6;
	OutLineMesh.Triangles[17] = 7;
	OutLineMesh.Triangles[18] = 1;
	OutLineMesh.Triangles[19] = 2;
	OutLineMesh.Triangles[20] = 6;
	OutLineMesh.Triangles[21] = 1;
	OutLineMesh.Triangles[22] = 6;
	OutLineMesh.Triangles[23] = 5;
	OutLineMesh.Triangles[24] = 0;
	OutLineMesh.Triangles[25] = 1;
	OutLineMesh.Triangles[26] = 5;
	OutLineMesh.Triangles[27] = 0;
	OutLineMesh.Triangles[28] = 5;
	OutLineMesh.Triangles[29] = 4;
	OutLineMesh.Triangles[30] = 7;
	OutLineMesh.Triangles[31] = 6;
	OutLineMesh.Triangles[32] = 2;
	OutLineMesh.Triangles[33] = 7;
	OutLineMesh.Triangles[34] = 2;
	OutLineMesh.Triangles[35] = 3;
	return true;
}

bool UGeometryRelativeLibrary::GeneratePlanLineMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InLineThickness, FPMCSection& OutLineMesh)
{
	FVector UpVector = FVector::UpVector;
	switch (InPlanType)
	{
	case EPlanPolygonBelongs::EXZ_Plan:UpVector = FVector(0.0f, 1.0f, 0.0f); break;
	case EPlanPolygonBelongs::EYZ_Plan:UpVector = FVector(-1.0f, 0.0f, 0.0f); break;
	}
	FVector Offset = InPointB - InPointA;
	FVector Right = FVector::CrossProduct(UpVector, Offset);
	Right.Normalize();
	OutLineMesh.Vertexes.AddZeroed(4);
	const float HalfThickness = InLineThickness / 2.0f;
	OutLineMesh.Vertexes[0] = Right * HalfThickness + InPointA;
	OutLineMesh.Vertexes[1] = OutLineMesh.Vertexes[0] + Offset;
	OutLineMesh.Vertexes[3] = -Right * HalfThickness + InPointA;
	OutLineMesh.Vertexes[2] = OutLineMesh.Vertexes[3] + Offset;
	OutLineMesh.Triangles.AddZeroed(6);
	OutLineMesh.Triangles[0] = 0;
	OutLineMesh.Triangles[1] = 1;
	OutLineMesh.Triangles[2] = 3;
	OutLineMesh.Triangles[3] = 1;
	OutLineMesh.Triangles[4] = 2;
	OutLineMesh.Triangles[5] = 3;
	return true;
}

bool UGeometryRelativeLibrary::CalculateStartAndEndAngle(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const bool& InExceptLittleOne, float& OutStartAngle, float& OutEndAngle)
{
	FVector PlanUpVector = FVector::UpVector;
	switch (InPlanType)
	{
	case EPlanPolygonBelongs::EYZ_Plan:PlanUpVector = FVector(-1.0, 0.0, 0.0); break;
	case EPlanPolygonBelongs::EXZ_Plan:PlanUpVector = FVector(0.0, 1.0, 0.0); break;
	}
	FVector CenterPointLocation = FVector::ZeroVector;
	if (UGeometryRelativeLibrary::CalculateCenterPointLocationByRadius(InPointA, InPointB, InRadius, PlanUpVector, CenterPointLocation))
	{
		FVector A = InPointA - CenterPointLocation;
		A.Normalize();
		FVector B = InPointB - CenterPointLocation;
		B.Normalize();
		UE_LOG(LogTemp, Log, TEXT("------ OutCenterPoint: %s ------"), *CenterPointLocation.ToString());
		float AngleA = UGeometryRelativeLibrary::CalculateVectorAngleWithin360(InPlanType, A);
		float AngleB = UGeometryRelativeLibrary::CalculateVectorAngleWithin360(InPlanType, B);
		if (InRadius > 0)
		{
			AngleA += 180.f;
			AngleA = FMath::Fmod(AngleA, 360.0f);
			AngleB += 180.f;
			AngleB = FMath::Fmod(AngleB, 360.0f);
		}
		OutStartAngle = AngleA;
		OutEndAngle = AngleB;
		UE_LOG(LogTemp, Log, TEXT("------ Forward AngleA: %f AngleB: %f ------"), AngleA, AngleB);
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::PerpareForAcrLineCalculate(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InSliceCount, const bool& InExceptLittleOne, float& OutStartAngle, float& OutStepAngle)
{
	float AngleA = 0.0f;
	float AngleB = 0.0f;
	if (UGeometryRelativeLibrary::CalculateStartAndEndAngle(InPlanType, InPointA, InPointB, InRadius, InExceptLittleOne, AngleA, AngleB))
	{
		OutStartAngle = AngleA;
		OutStepAngle = 0.0f;
		return CalculateStartAngleAndStep(AngleA, AngleB, InSliceCount, InExceptLittleOne, OutStartAngle, OutStepAngle);
	}
	return false;
}

bool UGeometryRelativeLibrary::GenerateArcLineMeshByRadius(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InInterpPointCount, const float& InLineThickness, FPMCSection& OutLineMesh, bool InExceptLittleOne)
{
	float StartAngle = 0.0f;
	float StepAngle = 0.0f;
	int32 InterpPointCount = InInterpPointCount;
	if (InInterpPointCount <= 0)
	{
		float AngleA = 0.0f;
		float AngleB = 0.0f;
		if (UGeometryRelativeLibrary::CalculateStartAndEndAngle(InPlanType, InPointA, InPointB, InRadius, InExceptLittleOne, AngleA, AngleB))
		{
			float DeltaTheta = FMath::RadiansToDegrees(FMath::Atan(DELTA_DISTANCE / FMath::Abs(InRadius)));
			UE_LOG(LogTemp, Log, TEXT("AngleA is: %f  AngleB is: %f  DeltaTheta is: %f  "), AngleA, AngleB, DeltaTheta);
			InterpPointCount = FMath::CeilToInt(FMath::Abs(AngleA - AngleB) / DeltaTheta) + 1;
		}
		else
		{
			return false;
		}
	}
	if (UGeometryRelativeLibrary::PerpareForAcrLineCalculate(InPlanType, InPointA, InPointB, InRadius, InterpPointCount, InExceptLittleOne, StartAngle, StepAngle))
	{
		bool Res = UGeometryRelativeLibrary::GenerateRingMesh(InPlanType, InRadius, InLineThickness, InterpPointCount, StartAngle, StepAngle, OutLineMesh.Vertexes, OutLineMesh.Triangles);
		if (Res)
		{
			return UGeometryRelativeLibrary::AfterAcrLineCalculate(InPlanType, InPointA, OutLineMesh.Vertexes);
		}
	}
	return false;
}

bool UGeometryRelativeLibrary::AfterAcrLineCalculate(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, TArray<FVector>& InOutLineLocations)
{
	if (EPlanPolygonBelongs::EXY_Plan == InPlanType)
	{
		for (auto& Iter : InOutLineLocations)
		{
			Iter += InPointA;
			Iter.Z = -0.05f;
		}
		return true;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == InPlanType)
	{
		for (auto& Iter : InOutLineLocations)
		{
			Iter += InPointA;
			Iter.X = 0.05f;
		}
		return true;
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == InPlanType)
	{
		for (auto& Iter : InOutLineLocations)
		{
			Iter += InPointA;
			Iter.Y = -0.05f;
		}
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::CalculateArcLineByRadius(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InInterpPointCount, TArray<FVector>& OutLineLocations, bool InExceptLittleOne)
{
	float StartAngle = 0.0f;
	float StepAngle = 0.0f;
	int32 InterpPointCount = InInterpPointCount;
	if (InInterpPointCount <= 0)
	{
		float AngleA = 0.0f;
		float AngleB = 0.0f;
		if (UGeometryRelativeLibrary::CalculateStartAndEndAngle(InPlanType, InPointA, InPointB, InRadius, InExceptLittleOne, AngleA, AngleB))
		{
			float DeltaTheta = FMath::RadiansToDegrees(FMath::Atan(DELTA_DISTANCE / FMath::Abs(InRadius)));
			UE_LOG(LogTemp, Log, TEXT("AngleA is: %f  AngleB is: %f  DeltaTheta is: %f  "), AngleA, AngleB, DeltaTheta);
			InterpPointCount = FMath::CeilToInt(FMath::Abs(AngleA - AngleB) / DeltaTheta) + 1;
		}
		else
		{
			return false;
		}
	}
	if (UGeometryRelativeLibrary::PerpareForAcrLineCalculate(InPlanType, InPointA, InPointB, InRadius, InterpPointCount, InExceptLittleOne, StartAngle, StepAngle))
	{
		OutLineLocations.AddZeroed(InterpPointCount - 1);
		FVector OffsetVector = FVector::ZeroVector;
		for (int32 i = 0; i < InterpPointCount; ++i)
		{
			float Angle = StepAngle * i + StartAngle;
			FVector RadiusDir = FVector::ZeroVector;
			switch (InPlanType)
			{
			case EPlanPolygonBelongs::EXY_Plan:RadiusDir.X = UKismetMathLibrary::DegCos(Angle); RadiusDir.Y = UKismetMathLibrary::DegSin(360.0f - Angle); break;
			case EPlanPolygonBelongs::EYZ_Plan:RadiusDir.Y = UKismetMathLibrary::DegCos(Angle); RadiusDir.Z = UKismetMathLibrary::DegSin(Angle); break;
			case EPlanPolygonBelongs::EXZ_Plan:RadiusDir.X = UKismetMathLibrary::DegCos(Angle); RadiusDir.Z = UKismetMathLibrary::DegSin(Angle); break;
			}
			if (0 == i)
			{
				OffsetVector = RadiusDir * InRadius;
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("------ CurreDirection : %s  Angle : %f  ------"), *RadiusDir.ToString(), Angle);
				OutLineLocations[i - 1] = InRadius * RadiusDir - OffsetVector + InPointA;
				UE_LOG(LogTemp, Log, TEXT("------ OutLineLocations[%d -1] : %s   ------"), i, *OutLineLocations[i - 1].ToString());
			}
		}
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::GenerateArcLineMeshByHeight(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, const int32& InInterpPointCount, const float& InLineThickness, FPMCSection& OutLineMesh)
{
	bool ExceptLittleOne = true;
	float Radius = 0.0f;
	if (CalculateRadiusByArcHeight(InPointA, InPointB, InArcHeight, ExceptLittleOne, Radius))
	{
		UE_LOG(LogTemp, Log, TEXT("------ ExceptLittleOne : %d Radius: %f ------"), ExceptLittleOne, Radius);
		if (!ExceptLittleOne)
			Radius *= -1.0f;
		return UGeometryRelativeLibrary::GenerateArcLineMeshByRadius(InPlanType, InPointA, InPointB, Radius, InInterpPointCount, InLineThickness, OutLineMesh, ExceptLittleOne);
	}
	return false;
}

bool UGeometryRelativeLibrary::CalculateArcLineByHeight(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, const int32& InInterpPointCount, TArray<FVector>& OutLineLocations)
{
	bool ExceptLittleOne = true;
	float Radius = 0.0f;
	if (CalculateRadiusByArcHeight(InPointA, InPointB, InArcHeight, ExceptLittleOne, Radius))
	{
		UE_LOG(LogTemp, Log, TEXT("------ ExceptLittleOne : %d Radius: %f ------"), ExceptLittleOne, Radius);
		if (!ExceptLittleOne)
			Radius *= -1.0f;
		return UGeometryRelativeLibrary::CalculateArcLineByRadius(InPlanType, InPointA, InPointB, Radius, InInterpPointCount, OutLineLocations, ExceptLittleOne);
	}
	return false;
}

bool UGeometryRelativeLibrary::CalculateCenterPointLocationByRadius(const FVector& InPointA, const FVector& InPointB, const float& InRadius, const FVector& InUpVector, FVector& OutCenterLocation)
{
	float DisSquared = InRadius * InRadius - FVector::DistSquared(InPointA, InPointB) / 4.0f;
	if (DisSquared >= 0)
	{
		FVector Offset = InPointB - InPointA;
		FVector Right3D = FVector::CrossProduct(InUpVector, Offset);
		Right3D.Normalize();
		UE_LOG(LogTemp, Log, TEXT("-----  Right3D is: %s  -----"), *Right3D.ToString());
		FVector MidPoint = (InPointA + InPointB) / 2.0f;
		float Dis = FMath::Sqrt(DisSquared);
		float CenterDir = InRadius > 0 ? 1.0f : -1.0f;
		OutCenterLocation = MidPoint + Right3D * CenterDir * Dis;
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::CalculateStartAngleAndStep(const float& InAngleA, const float& InAngleB, const int32& InSliceCount, const bool& InExpectLittleOne, float& OutStartAngle, float& OutStep)
{
	if (0 == InSliceCount)
		return false;

	float DeltaAngle = InAngleB - InAngleA;
	UE_LOG(LogTemp, Log, TEXT("------------ DeltaAngle is:  %f "), DeltaAngle);
	if (FMath::Abs(DeltaAngle) > 180.0f)
	{
		if (InExpectLittleOne)
		{
			DeltaAngle = DeltaAngle > 0 ? DeltaAngle - 360.f : 360.f + DeltaAngle;
		}
	}
	else
	{
		if (!InExpectLittleOne)
		{
			DeltaAngle = DeltaAngle > 0 ? DeltaAngle - 360.f : 360.f + DeltaAngle;
		}
	}
	OutStep = DeltaAngle / InSliceCount;
	return true;
}

bool UGeometryRelativeLibrary::GenerateRingMesh(const EPlanPolygonBelongs& InPlanType, const float& InRadius, const float& InRingThickness, const int32& InSliceCount, const float& InStartAngle, const float& InStep, TArray<FVector>& OutLocations, TArray<int32>& OutIndices)
{
	float HalfThickness = InRingThickness / 2.0f;
	float InnerRadius = InRadius - HalfThickness;
	float OutterRadius = InRadius + HalfThickness;
	OutLocations.AddZeroed((InSliceCount + 1) << 1);
	FVector OffsetVector = FVector::ZeroVector;
	for (int32 i = 0; i <= InSliceCount; ++i)
	{
		float Angle = InStep * i + InStartAngle;
		FVector RadiusDir = FVector::ZeroVector;
		switch (InPlanType)
		{
		case EPlanPolygonBelongs::EXY_Plan:RadiusDir.X = UKismetMathLibrary::DegCos(Angle); RadiusDir.Y = UKismetMathLibrary::DegSin(360.0f - Angle); break;
		case EPlanPolygonBelongs::EYZ_Plan:RadiusDir.Y = UKismetMathLibrary::DegCos(Angle); RadiusDir.Z = UKismetMathLibrary::DegSin(Angle); break;
		case EPlanPolygonBelongs::EXZ_Plan:RadiusDir.X = UKismetMathLibrary::DegCos(Angle); RadiusDir.Z = UKismetMathLibrary::DegSin(Angle); break;
		}
		if (0 == i)
			OffsetVector = RadiusDir * InRadius;

		UE_LOG(LogTemp, Log, TEXT("------ CurreDirection : %s  Angle : %f  ------"), *RadiusDir.ToString(), Angle);
		FVector InnerPoint = InnerRadius * RadiusDir - OffsetVector;
		//FVector2D MidPoint = InRadius*CurreDirection + InPointA;
		FVector OutterPoint = OutterRadius * RadiusDir - OffsetVector;
		UE_LOG(LogTemp, Log, TEXT("------ InnerPoint : %s  OutterPoint : %s  ------"), *InnerPoint.ToString(), *OutterPoint.ToString());
		OutLocations[i << 1] = InnerPoint;
		OutLocations[(i << 1) + 1] = OutterPoint;
	}
	UGeometryRelativeLibrary::GenerateRingIndices(InSliceCount, OutIndices);
	return true;
}

bool UGeometryRelativeLibrary::GenerateRingIndices(const int32& InSliceCount, TArray<int32>& OutIndices)
{
	OutIndices.AddZeroed(3 * (InSliceCount << 1));
	for (int32 i = 0; i < InSliceCount; ++i)
	{
		int32 K = i * 6;
		int32 StartIndex = i << 1;
		OutIndices[K] = StartIndex;
		OutIndices[K + 1] = StartIndex + 3;
		OutIndices[K + 2] = StartIndex + 1;
		OutIndices[K + 3] = StartIndex;
		OutIndices[K + 4] = StartIndex + 2;
		OutIndices[K + 5] = StartIndex + 3;
	}
	return true;
}

bool UGeometryRelativeLibrary::CalculateRadiusByArcHeight(const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, bool& OutExpectLittleOne, float& OutRadius)
{
	if (FMath::IsNearlyZero(InArcHeight))
		return false;

	OutRadius = FVector::DistSquared(InPointA, InPointB) / (8.0f * FMath::Abs(InArcHeight)) + FMath::Abs(InArcHeight) / 2.0f;
	float Dir = InArcHeight < 0 ? 1.0f : -1.0f;
	OutExpectLittleOne = OutRadius >= FMath::Abs(InArcHeight);
	OutRadius *= Dir;
	UE_LOG(LogTemp, Log, TEXT("------ ExceptLittleOne : %d Radius: %f ------"), OutExpectLittleOne, OutRadius);
	return true;
}

float UGeometryRelativeLibrary::CalculateVector2DAngleWithin360(const FVector2D& InNormalizedVector)
{
	return (InNormalizedVector.Y >= 0.0f) ? UKismetMathLibrary::DegAcos(InNormalizedVector.X) : 360.0f - UKismetMathLibrary::DegAcos(InNormalizedVector.X);
}

float UGeometryRelativeLibrary::CalculateVectorAngleWithin360(const EPlanPolygonBelongs& InPlanType, FVector& InNormalizedVector)
{
	FVector2D V = FVector2D::ZeroVector;
	if (EPlanPolygonBelongs::EYZ_Plan == InPlanType)
	{
		V = FVector2D(InNormalizedVector.Y, InNormalizedVector.Z);
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == InPlanType)
	{
		V = FVector2D(InNormalizedVector.X, InNormalizedVector.Z);
	}
	else if (EPlanPolygonBelongs::EXY_Plan == InPlanType)
	{
		V = FVector2D(InNormalizedVector.X, -InNormalizedVector.Y);
	}
	V.Normalize();
	float Angle = 180.0f + UGeometryRelativeLibrary::CalculateVector2DAngleWithin360(V);
	return FMath::IsNearlyEqual(Angle, 360.0f, 0.05f) ? 360.0f : FMath::Fmod(Angle, 360.f);
}

bool UGeometryRelativeLibrary::GenerateRectangleMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InStartLocation, const FVector& InEndLocation, FPMCSection& OutLineMesh)
{
	OutLineMesh.Vertexes.AddZeroed(4);
	OutLineMesh.UV.AddZeroed(4);
	OutLineMesh.Triangles.AddZeroed(6);
	OutLineMesh.Triangles[0] = 0;
	OutLineMesh.Triangles[1] = 1;
	OutLineMesh.Triangles[2] = 2;
	OutLineMesh.Triangles[3] = 0;
	OutLineMesh.Triangles[4] = 2;
	OutLineMesh.Triangles[5] = 3;
	OutLineMesh.UV[1] = FVector2D(1.0f, 0.0f);
	OutLineMesh.UV[2] = FVector2D::UnitVector;
	OutLineMesh.UV[3] = FVector2D(0.0f, 1.0f);
	if (EPlanPolygonBelongs::EXY_Plan == InPlanType)
	{
		OutLineMesh.Vertexes[0] = FVector(FMath::Min<float>(InStartLocation.X, InEndLocation.X), FMath::Min<float>(InStartLocation.Y, InEndLocation.Y), InStartLocation.Z);
		OutLineMesh.Vertexes[1] = FVector(FMath::Max<float>(InStartLocation.X, InEndLocation.X), FMath::Min<float>(InStartLocation.Y, InEndLocation.Y), InStartLocation.Z);
		OutLineMesh.Vertexes[2] = FVector(FMath::Max<float>(InStartLocation.X, InEndLocation.X), FMath::Max<float>(InStartLocation.Y, InEndLocation.Y), InEndLocation.Z);
		OutLineMesh.Vertexes[3] = FVector(FMath::Min<float>(InStartLocation.X, InEndLocation.X), FMath::Max<float>(InStartLocation.Y, InEndLocation.Y), InEndLocation.Z);
		return true;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == InPlanType)
	{
		OutLineMesh.Vertexes[0] = FVector(InStartLocation.X, FMath::Min<float>(InStartLocation.Y, InEndLocation.Y), FMath::Max<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[1] = FVector(InStartLocation.X, FMath::Max<float>(InStartLocation.Y, InEndLocation.Y), FMath::Max<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[2] = FVector(InEndLocation.X, FMath::Max<float>(InStartLocation.Y, InEndLocation.Y), FMath::Min<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[3] = FVector(InEndLocation.X, FMath::Min<float>(InStartLocation.Y, InEndLocation.Y), FMath::Min<float>(InStartLocation.Z, InEndLocation.Z));
		return true;
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == InPlanType)
	{
		OutLineMesh.Vertexes[0] = FVector(FMath::Min<float>(InStartLocation.X, InEndLocation.X), InStartLocation.Y, FMath::Max<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[1] = FVector(FMath::Max<float>(InStartLocation.X, InEndLocation.X), InStartLocation.Y, FMath::Max<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[2] = FVector(FMath::Max<float>(InStartLocation.X, InEndLocation.X), InEndLocation.Y, FMath::Min<float>(InStartLocation.Z, InEndLocation.Z));
		OutLineMesh.Vertexes[3] = FVector(FMath::Min<float>(InStartLocation.X, InEndLocation.X), InEndLocation.Y, FMath::Min<float>(InStartLocation.Z, InEndLocation.Z));
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::GenerateEllipseMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InCenterLocation, const float& InShortRadius, const float& InLongRadius, const int32& InInterpPointCount, TArray<FVector>& OutVertices)
{
	int32 PointCount = 0 == InInterpPointCount ? PI * (InShortRadius + InLongRadius) / 5.0f : InInterpPointCount;
	if (PointCount < 3)
		return false;
	const float Step = 360.0f / PointCount;
	OutVertices.AddZeroed(PointCount);
	for (int32 i = 0; i < PointCount; ++i)
	{
		const float X = InLongRadius * UKismetMathLibrary::DegCos(i * Step);
		const float Y = InShortRadius * UKismetMathLibrary::DegSin(i * Step);
		switch (InPlanType)
		{
		case EPlanPolygonBelongs::EXY_Plan: OutVertices[i].X = X + InCenterLocation.X; OutVertices[i].Y = Y + InCenterLocation.Y; OutVertices[i].Z = InCenterLocation.Z; break;
		case EPlanPolygonBelongs::EYZ_Plan: OutVertices[i].X = InCenterLocation.X; OutVertices[i].Y = Y + InCenterLocation.Y; OutVertices[i].Z = X + InCenterLocation.Z; break;
		case EPlanPolygonBelongs::EXZ_Plan: OutVertices[i].X = X + InCenterLocation.X; OutVertices[i].Y = InCenterLocation.Y; OutVertices[i].Z = Y + InCenterLocation.Z; break;
		default: checkNoEntry(); break;
		}
	}
	return true;
}

bool UGeometryRelativeLibrary::GenerateEllipseMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InCenterLocation, const float& InShortRadius, const float& InLongRadius, const int32& InInterpPointCount, FPMCSection& OutEllipseMesh)
{
	if (FMath::IsNearlyZero(InShortRadius) || FMath::IsNearlyZero(InLongRadius))
		return false;
	int32 InterPointCount = InInterpPointCount;
	if (0 == InInterpPointCount)
		InterPointCount = FMath::FloorToInt((2 * PI * FMath::Max<int32>(InShortRadius, InLongRadius) + 4 * (FMath::Max<int32>(InShortRadius, InLongRadius) - FMath::Min<int32>(InShortRadius, InLongRadius))) * 0.3);

	if (UGeometryRelativeLibrary::GenerateEllipseMesh(InPlanType, InCenterLocation, InShortRadius, InLongRadius, InterPointCount, OutEllipseMesh.Vertexes))
	{
		OutEllipseMesh.Triangles.AddZeroed((OutEllipseMesh.Vertexes.Num() - 2) * 3);
		for (int32 i = 0; i < InterPointCount - 2; ++i)
		{
			const int32 StartIndex = i * 3;
			OutEllipseMesh.Triangles[StartIndex + 1] = i + 1;
			OutEllipseMesh.Triangles[StartIndex + 2] = i + 2;
		}
		return true;
	}
	return false;
}

bool UGeometryRelativeLibrary::GenerateCubeMesh(const FVector& InStartLocation, const FVector& InEndLocation, FPMCSection& OutCubeMesh)
{
	const FVector	MinPoint = FVector(FMath::Min<float>(InStartLocation.X, InEndLocation.X), FMath::Min<float>(InStartLocation.Y, InEndLocation.Y), FMath::Min<float>(InStartLocation.Z, InEndLocation.Z));
	const FVector	MaxPoint = FVector(FMath::Max<float>(InStartLocation.X, InEndLocation.X), FMath::Max<float>(InStartLocation.Y, InEndLocation.Y), FMath::Max<float>(InStartLocation.Z, InEndLocation.Z));
	auto GenerateUV = [](const TArray<FVector>& Points, const FVector& InNormal, const FVector& InTangetX, const FVector& ZeroPoint, TArray<FVector2D>& OutUV)
	{
		OutUV.Init(FVector2D::ZeroVector, Points.Num());
		const FVector TangetY = (InNormal ^ InTangetX).GetSafeNormal();
		for (int32 i = 0; i < Points.Num(); ++i)
		{
			OutUV[i].X = ((Points[i] - ZeroPoint) | InTangetX) * 0.01f;
			OutUV[i].Y = ((Points[i] - ZeroPoint) | TangetY) * 0.01f;
		}
	};
	const TArray<int32> Triangles = { 0,1,2,0,2,3 };
	{//Bottom
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		TemSection.Vertexes[Offset++] = MinPoint;
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MaxPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MinPoint.Z);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(-FVector::UpVector, 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX = FVector::ForwardVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes, -FVector::UpVector, FVector::ForwardVector, FVector(MaxPoint.X, MinPoint.Y, MinPoint.Z), TemSection.UV);
		OutCubeMesh += TemSection;
	}
	{//Top
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		TemSection.Vertexes[Offset++] = MaxPoint;
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MinPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MaxPoint.Z);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(FVector::UpVector, 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX = FVector::ForwardVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes, FVector::UpVector, FVector::ForwardVector, FVector(MinPoint.X, MinPoint.Y, MaxPoint.Z), TemSection.UV);
		OutCubeMesh += TemSection;
	}
	{//Left
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		TemSection.Vertexes[Offset++] = MinPoint;
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MinPoint.Y, MaxPoint.Z);

		int32 Dir = FVector::DotProduct(TemSection.Vertexes[0] - TemSection.Vertexes[1]
			, TemSection.Vertexes[1] - TemSection.Vertexes[2]);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(-FVector::ForwardVector, 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX = FVector::RightVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes, -FVector::ForwardVector , TangentX.TangentX, FVector(MinPoint.X, MinPoint.Y, MaxPoint.Z), TemSection.UV);
		OutCubeMesh += TemSection;
	}
	{//Back
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		TemSection.Vertexes[Offset++] = MinPoint;
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MinPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MinPoint.Z);

		int32 Dir = FVector::DotProduct(TemSection.Vertexes[0] - TemSection.Vertexes[1]
			, TemSection.Vertexes[1] - TemSection.Vertexes[2]);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(FVector::RightVector, 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX = FVector::ForwardVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes,  FVector::RightVector , TangentX.TangentX, FVector(MaxPoint.X, MinPoint.Y, MaxPoint.Z), TemSection.UV);
		OutCubeMesh += TemSection;
	}
	{//Right
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MaxPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MinPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = MaxPoint;

		int32 Dir = FVector::DotProduct(TemSection.Vertexes[0] - TemSection.Vertexes[1]
			, TemSection.Vertexes[1] - TemSection.Vertexes[2]);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(FVector::ForwardVector, 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX =  -FVector::RightVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes, FVector::ForwardVector , TangentX.TangentX, MaxPoint, TemSection.UV);
		OutCubeMesh += TemSection;
	}
	{//Front
		FPMCSection TemSection;
		int32 Offset = TemSection.Vertexes.AddZeroed(4);
		TemSection.Vertexes[Offset++] = MaxPoint;
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MaxPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MinPoint.X, MaxPoint.Y, MinPoint.Z);
		TemSection.Vertexes[Offset++] = FVector(MaxPoint.X, MaxPoint.Y, MinPoint.Z);

		int32 Dir = FVector::DotProduct(TemSection.Vertexes[0] - TemSection.Vertexes[1]
			, TemSection.Vertexes[1] - TemSection.Vertexes[2]);

		TemSection.Triangles = Triangles;
		TemSection.Normals.Init(FVector::RightVector , 4);
		FProcMeshTangent TangentX;
		TangentX.TangentX =  FVector::ForwardVector;
		TemSection.Tangents.Init(TangentX, 4);
		GenerateUV(TemSection.Vertexes, FVector::RightVector, TangentX.TangentX, FVector(MinPoint.X, MaxPoint.Y, MaxPoint.Z), TemSection.UV);
		OutCubeMesh += TemSection;
	}
	return true;
}
