// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "MagicCore/Public/PMCSection.h"
#include "GeometryRelativeLibrary.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(GeometryRelativeLibraryLog, Log, Log);

#define DELTA_DISTANCE	(8.0f) //两个点的最小可分辨距离，单位为毫米

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UGeometryRelativeLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	static bool CalculatePolygonNormal(const TArray<FVector>& InPlan, FVector& OutNormal);

	static bool JudgePolygonVerticesOrder(FPolygonData& InPolygon);

	static bool JudgePolygonVerticesType(const FVector& InPrePoint, const FVector& InNextPoint, const FVector& InTestPoint, EPlanPolygonBelongs InPlanPolygonBelongs, const EPolygonVerticesOrder& InPolygonOrder, EVerticeType& OutVerticeType);

public:

	static bool GenerateLineMesh(const FVector& InPointA, const FVector& InPointB, const float& InLineThickness, FPMCSection& OutLineMesh);

	static bool GenerateArcLineMeshByRadius(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InInterpPointCount, const float& InLineThickness, FPMCSection& OutLineMesh, bool InExceptLittleOne = true);

	static bool GenerateArcLineMeshByHeight(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, const int32& InInterpPointCount, const float& InLineThickness, FPMCSection& OutLineMesh);

	static bool GeneratePlanLineMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InLineThickness, FPMCSection& OutLineMesh);

	static bool CalculateArcLineByRadius(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InInterpPointCount, TArray<FVector>& OutLineLocations, bool InExceptLittleOne = true);

	static bool CalculateArcLineByHeight(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, const int32& InInterpPointCount, TArray<FVector>& OutLineLocations);

	static bool GenerateRectangleMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InStartLocation, const FVector& InEndLocation, FPMCSection& OutLineMesh);

	static bool GenerateEllipseMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InCenterLocation, const float& InShortRadius, const float& InLongRadius, const int32& InInterpPointCount, FPMCSection& OutEllipseMesh);

	static bool GenerateEllipseMesh(const EPlanPolygonBelongs& InPlanType, const FVector& InCenterLocation, const float& InShortRadius, const float& InLongRadius, const int32& InInterpPointCount, TArray<FVector>& OutVertices);

	static bool GenerateCubeMesh(const FVector& InStartLocation, const FVector& InEndLocation, FPMCSection& OutLineMesh);

private:

	static bool CalculateCenterPointLocationByRadius(const FVector& InPointA, const FVector& InPointB, const float& InRadius, const FVector& InUpVector, FVector& OutCenterLocation);

	static bool CalculateStartAngleAndStep(const float& InAngleA, const float& InAngleB, const int32& InSliceCount, const bool& InExpectLittleOne, float& OutStartAngle, float& OutStep);

	static bool CalculateStartAndEndAngle(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const bool& InExceptLittleOne, float& OutStartAngle, float& OutEndAngle);

	static bool GenerateRingMesh(const EPlanPolygonBelongs& InPlanType, const float& InRadius, const float& InRingThickness, const int32& InSliceCount, const float& InStartAngle, const float& InStep, TArray<FVector>& OutLocations, TArray<int32>& OutIndices);

	static bool GenerateRingIndices(const int32& InSliceCount, TArray<int32>& OutIndices);

	static bool CalculateRadiusByArcHeight(const FVector& InPointA, const FVector& InPointB, const float& InArcHeight, bool& OutExpectLittleOne, float& OutRadius);

	static float CalculateVector2DAngleWithin360(const FVector2D& InNormalizedVector);

	static float CalculateVectorAngleWithin360(const EPlanPolygonBelongs& InPlanType, FVector& InNormalizedVector);

	static bool PerpareForAcrLineCalculate(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, const FVector& InPointB, const float& InRadius, const int32& InSliceCount, const bool& InExceptLittleOne, float& OutStartAngle, float& OutStepAngle);

	static bool AfterAcrLineCalculate(const EPlanPolygonBelongs& InPlanType, const FVector& InPointA, TArray<FVector>& InOutLineLocations);
};
