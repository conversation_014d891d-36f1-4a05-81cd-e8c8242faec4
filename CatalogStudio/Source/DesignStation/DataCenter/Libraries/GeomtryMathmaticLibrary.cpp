// Fill out your copyright notice in the Description page of Project Settings.

#include "GeomtryMathmaticLibrary.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"

bool FCustomPoint::ConvertPointsToCustomPoints(const TArray<FVector>& InPoints, const bool& InNeedOutline, TArray<FCustomPoint>& OutCustomPoints)
{
	const int32 Offset = OutCustomPoints.AddDefaulted(InPoints.Num());
	int32 i = 0;
	for (auto& Iter : InPoints)
	{
		OutCustomPoints[Offset + i].PointLocation = Iter;
		OutCustomPoints[Offset + i].NeedOutline = InNeedOutline;
		++i;
	}
	return true;
}

bool FCustomPoint::ConvertCustomPointsToPoints(const TArray<FCustomPoint>& InCustomPoints, TArray<FVector>& OutPoints)
{
	const int32 Offset = OutPoints.AddZeroed(InCustomPoints.Num());
	for (int32 i = 0; i < InCustomPoints.Num(); ++i)
		OutPoints[Offset + i] = InCustomPoints[i].PointLocation;
	return true;
}

bool UGeomtryMathmaticLibrary::GenerateMeshOfTwoParallelPlans(const FVector& InNormal, const TArray<FCustomPoint>& InPlan1, const TArray<FCustomPoint>& InPlan2, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork, const EPlanPolygonBelongs& InPlan)
{

	UE_LOG(LogTemp, Warning, TEXT("InPlan1 %d"), InPlan1.Num());
	UE_LOG(LogTemp, Warning, TEXT("InPlan2 %d"), InPlan2.Num());
	if ((InPlan1.Num() != InPlan2.Num()) || (InPlan1.Num() <= 1)) return true;
	const FVector Dir = (InPlan2[0].PointLocation - InPlan1[0].PointLocation).GetSafeNormal();
	const float DotValue = (Dir | InNormal);
	bool bSameDir = (DotValue > 0.0f) || FMath::IsNearlyZero(DotValue, THRESH_NORMALS_ARE_ORTHOGONAL);
	const auto& BottomPlan = bSameDir ? InPlan1 : InPlan2;
	const auto& TopPlan = bSameDir ? InPlan2 : InPlan1;
	const int32 Count = InPlan1.Num();
	OutMeshInfo.Vertexes.Init(FVector::ZeroVector, Count << 2);
	OutMeshInfo.Tangents.AddDefaulted(OutMeshInfo.Vertexes.Num());
	OutMeshInfo.Normals.Init(FVector::ZeroVector, OutMeshInfo.Vertexes.Num());
	OutMeshInfo.UV.Init(FVector2D::ZeroVector, OutMeshInfo.Vertexes.Num());
	OutMeshInfo.Triangles.Init(0, Count * 6);
	int32 Offset = OutFramwork.AddDefaulted(Count);
	enum EUVType
	{
		None,
		Plane
	};
	EUVType UVType = EUVType::None;

	FVector Vec0 = BottomPlan[0].PointLocation - TopPlan[0].PointLocation;
	FVector Vec1 = BottomPlan[Count - 1].PointLocation - TopPlan[Count - 1].PointLocation;
	FVector Temp = FVector::CrossProduct(Vec0, Vec1);
	if (!Temp.Equals(FVector::ZeroVector, 0.0001f))
	{
		Temp.Normalize();
		if (Temp.Equals(InNormal, 0.0001f))
		{
			UVType = EUVType::Plane;
		}
		else
		{
			UVType = EUVType::None;
		}
	}
	for (int32 i = 0; i < Count; ++i)
	{
		const int32 Next = (i + 1) % Count;
		const int32 V0 = i << 2;
		const int32 V1 = V0 + 1;
		const int32 V2 = V1 + 1;
		const int32 V3 = V2 + 1;

		OutMeshInfo.Vertexes[V0] = BottomPlan[i].PointLocation;
		OutMeshInfo.Vertexes[V1] = TopPlan[i].PointLocation;
		OutMeshInfo.Vertexes[V2] = TopPlan[Next].PointLocation;
		OutMeshInfo.Vertexes[V3] = BottomPlan[Next].PointLocation;

		int32 TriangleBase = i * 6;
		OutMeshInfo.Triangles[TriangleBase++] = V0;
		OutMeshInfo.Triangles[TriangleBase++] = V3;
		OutMeshInfo.Triangles[TriangleBase++] = V2;

		OutMeshInfo.Triangles[TriangleBase++] = V0;
		OutMeshInfo.Triangles[TriangleBase++] = V2;
		OutMeshInfo.Triangles[TriangleBase++] = V1;


		
		FVector Vec1_1 = FVector(OutMeshInfo.Vertexes[V1] - OutMeshInfo.Vertexes[V0]).GetSafeNormal();
		FVector Vec2 = FVector(OutMeshInfo.Vertexes[V3] - OutMeshInfo.Vertexes[V2]).GetSafeNormal();

		FVector Vec3 = FVector(OutMeshInfo.Vertexes[V3] - OutMeshInfo.Vertexes[V0]).GetSafeNormal();
		FVector Vec4 = FVector(OutMeshInfo.Vertexes[V2] - OutMeshInfo.Vertexes[V1]).GetSafeNormal();

		FVector Vec5 = FVector(OutMeshInfo.Vertexes[V2] - OutMeshInfo.Vertexes[V0]).GetSafeNormal();
		bool bP = FVector::Parallel(Vec1_1, Vec2) && FVector::Parallel(Vec3, Vec4);

		const FVector Normal = -((OutMeshInfo.Vertexes[V2] - OutMeshInfo.Vertexes[V1]) ^ (OutMeshInfo.Vertexes[V1] - OutMeshInfo.Vertexes[V0])).GetSafeNormal();
		FVector TangentY = (BottomPlan[Next].PointLocation - BottomPlan[i].PointLocation).GetSafeNormal();
		FVector TangentX = -(Normal ^ TangentY).GetSafeNormal();

		if (FVector::CrossProduct(InNormal,FVector::ZAxisVector).Equals(FVector::ZeroVector,0.0000001f))
		{
			TangentX = (BottomPlan[Next].PointLocation - BottomPlan[i].PointLocation).GetSafeNormal();
			TangentY = (Normal ^ TangentX).GetSafeNormal();
		}


		if (UVType == EUVType::None)
		{
			if (i > 0)
			{

				{
					OutMeshInfo.UV[V0] = OutMeshInfo.UV[V0 - 1];
					OutMeshInfo.UV[V1] = OutMeshInfo.UV[V0 - 2];
					//if (OutMeshInfo.UV[V0].X != OutMeshInfo.UV[V1].X)
					//{
					//	OutMeshInfo.UV[V1].X = OutMeshInfo.UV[V0].X;
					//}

					OutMeshInfo.UV[V2] = OutMeshInfo.UV[V1] - FVector2D(Vec4 | TangentX, Vec4 | TangentY) * FVector::Distance(OutMeshInfo.Vertexes[V2], OutMeshInfo.Vertexes[V1]) * 0.01f;
					OutMeshInfo.UV[V3] = OutMeshInfo.UV[V0] - FVector2D(Vec3 | TangentX, Vec3 | TangentY) * FVector::Distance(OutMeshInfo.Vertexes[V3], OutMeshInfo.Vertexes[V0]) * 0.01f;

				}
			}
			else
			{
				for (int32 j = V0; j <= V3; ++j)
				{
					const FVector DeltaPos = OutMeshInfo.Vertexes[j] - OutMeshInfo.Vertexes[V0];
					OutMeshInfo.UV[j].X = (DeltaPos | -TangentX) * 0.01f;
					OutMeshInfo.UV[j].Y = (DeltaPos | TangentY) * 0.01f;
				}
			}
		}
		else if(UVType == EUVType::Plane)
		{
			if (InPlan == EPlanPolygonBelongs::EXY_Plan)
			{
				TangentX = FVector::XAxisVector;
				TangentY = FVector::YAxisVector;
			}
			else if (InPlan == EPlanPolygonBelongs::EXZ_Plan)
			{
				TangentX = FVector::XAxisVector;
				TangentY = FVector::ZAxisVector;

			}
			else if (InPlan == EPlanPolygonBelongs::EYZ_Plan)
			{
				TangentX = FVector::YAxisVector;
				TangentY = FVector::ZAxisVector;
			}
			 
				for (int32 j = V0; j <= V3; ++j)
				{
					const FVector DeltaPos = OutMeshInfo.Vertexes[j] - OutMeshInfo.Vertexes[0];
					OutMeshInfo.UV[j].X = (DeltaPos | TangentX) * 0.01f;
					OutMeshInfo.UV[j].Y = (DeltaPos | -TangentY) * 0.01f;
				}

		}
		OutMeshInfo.Tangents[V0].TangentX = TangentX;
		OutMeshInfo.Tangents[V1].TangentX = TangentX;
		OutMeshInfo.Tangents[V2].TangentX = TangentX;
		OutMeshInfo.Tangents[V3].TangentX = TangentX;

		OutMeshInfo.Normals[V0] = Normal;
		OutMeshInfo.Normals[V1] = Normal;
		OutMeshInfo.Normals[V2] = Normal;
		OutMeshInfo.Normals[V3] = Normal;

		if (BottomPlan[i].NeedOutline)
		{
			OutFramwork[Offset].Key = BottomPlan[i].PointLocation;
			OutFramwork[Offset].Value = TopPlan[i].PointLocation;
			++Offset;
		}
	}
	if (Offset < OutFramwork.Num()) OutFramwork.RemoveAt(Offset, OutFramwork.Num() - Offset);
	return true;
}

//bool UGeomtryMathmaticLibrary::GenerateMeshOfTwoParallelPlans(const FVector& InNormal, const TArray<FCustomPoint>& InPlan1, const TArray<FCustomPoint>& InPlan2, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork)
//{
//
//	UE_LOG(LogTemp, Warning, TEXT("InPlan1 %d"), InPlan1.Num());
//	UE_LOG(LogTemp, Warning, TEXT("InPlan2 %d"), InPlan2.Num());
//
//
//	if ((InPlan1.Num() <= 1) || (InPlan2.Num() <= 1)) return true;
//	const FVector Dir = (InPlan2[0].PointLocation - InPlan1[0].PointLocation).GetSafeNormal();
//	const float DotValue = (Dir | InNormal);
//	bool bSameDir = (DotValue > 0.0f) || FMath::IsNearlyZero(DotValue, THRESH_NORMALS_ARE_ORTHOGONAL);
//	const auto& BottomPlan = bSameDir ? InPlan1 : InPlan2;
//	const auto& TopPlan = bSameDir ? InPlan2 : InPlan1;
//	const int32& BotCount = BottomPlan.Num();
//	const int32& TopCount = TopPlan.Num();
//	int32 BotIndex = 0;
//	int32 TopIndex = 0;
//	TPair<bool, int32> PlanVec = TPair<bool, int32>(true, BotIndex);
//
//	int32 IndexNum = -1;
//	int32 Index = -1;
//	auto VecFun = [&](const TPair<bool, int32>& InVec)
//	{
//		if (InVec.Key)
//			return BottomPlan[InVec.Value];
//		return TopPlan[InVec.Value];
//	};
//
//	auto JumpVecFun = [&](const TPair<bool, int32>& InVec, TPair<bool, int32>& OutStart)
//	{
//		bool Key = !InVec.Key;
//		if (Key)
//		{
//			OutStart = TPair<bool, int32>(Key, BotIndex);
//			return VecFun(OutStart);
//		}
//		OutStart = TPair<bool, int32>(Key, TopIndex);
//		return VecFun(TPair<bool, int32>(OutStart));
//
//	};
//
//	int32 BotNext = 0;
//	int32 TopNext = 0;
//
//	bool bComplete = false;
//	bool bCountEqual = false;
//	while (!bComplete)
//	{
//		BotNext = (BotIndex + 1) % BotCount;
//		TopNext = (TopIndex + 1) % TopCount;
//
//		if (BotNext == 0 && TopNext == 0)
//		{
//			bComplete = true;
//		}
//		else if (BotNext == 0)
//		{
//			PlanVec = TPair<bool, int32>(false, TopIndex);
//		}
//		else if (TopNext == 0)
//		{
//			PlanVec = TPair<bool, int32>(true, BotIndex);
//		}
//		TPair<bool, int32> NextStart;
//
//		FCustomPoint P0 = VecFun(PlanVec);
//		FCustomPoint P1 = VecFun(TPair<bool, int32>(PlanVec.Key, PlanVec.Key ? BotNext : TopNext));
//		FCustomPoint P2 = JumpVecFun(PlanVec, NextStart);
//
//		OutMeshInfo.Vertexes.Add(P0.PointLocation);
//		OutMeshInfo.Vertexes.Add(P1.PointLocation);
//		OutMeshInfo.Vertexes.Add(P2.PointLocation);
//
//
//		if (PlanVec.Key)
//		{
//			OutMeshInfo.Triangles.Add(++IndexNum);
//			OutMeshInfo.Triangles.Add(++IndexNum);
//			OutMeshInfo.Triangles.Add(++IndexNum);
//		}
//		else
//		{
//			IndexNum += 3;
//			OutMeshInfo.Triangles.Add(IndexNum - 2);
//			OutMeshInfo.Triangles.Add(IndexNum);
//			OutMeshInfo.Triangles.Add(IndexNum - 1);
//		}
//
//
//		const FVector Normal = ((P1.PointLocation - P0.PointLocation) ^ (P2.PointLocation - P0.PointLocation)).GetSafeNormal();
//		OutMeshInfo.Normals.Add(Normal);
//		OutMeshInfo.Normals.Add(Normal);
//		OutMeshInfo.Normals.Add(Normal);
//
//		const FVector TangentX = (P1.PointLocation - P0.PointLocation).GetSafeNormal();
//		const FVector TangentY = (P2.PointLocation - P0.PointLocation).GetSafeNormal();
//
//
//		++Index;
//
//		{
//			FVector2D UV0(0.0);// = Index == 0 ? FVector2D(0, 0) : (bCountEqual ? OutMeshInfo.UV[OutMeshInfo.UV.Num() - 2] : OutMeshInfo.UV.Top());
//			FVector2D UV1(0, 0);
//			FVector2D UV2(0, 0);
//			//UV1 = UV0 + FVector2D(TangentX) * FVector::Distance(P1.PointLocation,P0.PointLocation) * 0.01f;
//			//UV2 = UV0 + FVector2D(TangentY) * FVector::Distance(P2.PointLocation, P0.PointLocation) * 0.01f;
//
//			//if (PlanVec.Key)
//			//{
//			//	UV1.X = UV0.X + ((P1.PointLocation - P0.PointLocation) | TangentX) * 0.01f;
//			//	UV1.Y = UV0.Y + ((P1.PointLocation - P0.PointLocation) | TangentY) * 0.01f;
//			//	UV2.X = UV0.X + ((P2.PointLocation - P0.PointLocation) | TangentX) * 0.01f;
//			//	UV2.Y = UV0.Y + ((P2.PointLocation - P0.PointLocation) | TangentY) * 0.01f;
//			//}
//			//else
//			//{
//			//	UV1.X = UV0.X + ((P1.PointLocation - P0.PointLocation) | TangentX) * 0.01f;
//			//	UV1.Y = UV0.Y + ((P1.PointLocation - P0.PointLocation) | -TangentY) * 0.01f;
//			//	UV2.X = UV0.X + ((P2.PointLocation - P0.PointLocation) | TangentX) * 0.01f;
//			//	UV2.Y = UV0.Y + ((P2.PointLocation - P0.PointLocation) | -TangentY) * 0.01f;
//			//}
//
//			OutMeshInfo.UV.Add(UV0);
//			OutMeshInfo.UV.Add(UV1);
//			OutMeshInfo.UV.Add(UV2);
//		}
//
//		//if (bComplete)
//		//{
//
//		//	PlanVec = NextStart;
//		//	FCustomPoint P3 = VecFun(PlanVec);
//		//	FCustomPoint P4 = VecFun(TPair<bool, int32>(PlanVec.Key, PlanVec.Key ? BotNext : TopNext));
//		//	FCustomPoint P5 = VecFun(TPair<bool, int32>(!PlanVec.Key, 0));
//
//		//	OutMeshInfo.Vertexes.Add(P3.PointLocation);
//		//	OutMeshInfo.Vertexes.Add(P4.PointLocation);
//		//	OutMeshInfo.Vertexes.Add(P5.PointLocation);
//
//
//		//	if (PlanVec.Key)
//		//	{
//		//		OutMeshInfo.Triangles.Add(++IndexNum);
//		//		OutMeshInfo.Triangles.Add(++IndexNum);
//		//		OutMeshInfo.Triangles.Add(++IndexNum);
//		//	}
//		//	else
//		//	{
//		//		IndexNum += 3;
//		//		OutMeshInfo.Triangles.Add(IndexNum - 2);
//		//		OutMeshInfo.Triangles.Add(IndexNum);
//		//		OutMeshInfo.Triangles.Add(IndexNum - 1);
//		//	}
//
//
//		//	const FVector NormalC = ((P3.PointLocation - P4.PointLocation) ^ (P3.PointLocation - P5.PointLocation)).GetSafeNormal();
//		//	OutMeshInfo.Normals.Add(NormalC);
//		//	OutMeshInfo.Normals.Add(NormalC);
//		//	OutMeshInfo.Normals.Add(NormalC);
//
//		//	const FVector TangentXC = (P4.PointLocation - P3.PointLocation).GetSafeNormal();
//		//	const FVector TangentYC = (NormalC ^ TangentX).GetSafeNormal();
//
//		//	//FVector2D UV0 = Index == 0 ? FVector2D(0, 0) : OutMeshInfo.UV.Top();
//		//	//FVector2D UV1(0, 0);
//		//	//FVector2D UV2(0, 0);
//		//	//if (PlanVec.Key)
//		//	//{
//		//	//	UV1.X = UV0.X + ((P4.PointLocation - P3.PointLocation) | TangentX) * 0.01f;
//		//	//	UV1.Y = UV0.Y + ((P4.PointLocation - P3.PointLocation) | -TangentY) * 0.01f;
//		//	//	UV2.X = UV0.X + ((P5.PointLocation - P3.PointLocation) | TangentX) * 0.01f;
//		//	//	UV2.Y = UV0.Y + ((P5.PointLocation - P0.PointLocation) | -TangentY) * 0.01f;
//		//	//}
//		//	//else
//		//	//{
//		//	//	UV1.X = UV0.X + ((P4.PointLocation - P3.PointLocation) | TangentX) * 0.01f;
//		//	//	UV1.Y = UV0.Y + ((P4.PointLocation - P3.PointLocation) | TangentY) * 0.01f;
//		//	//	UV2.X = UV0.X + ((P5.PointLocation - P3.PointLocation) | TangentX) * 0.01f;
//		//	//	UV2.Y = UV0.Y + ((P5.PointLocation - P3.PointLocation) | TangentY) * 0.01f;
//		//	//}
//		//	//OutMeshInfo.UV.Add(UV0);
//		//	//OutMeshInfo.UV.Add(UV1);
//		//	//OutMeshInfo.UV.Add(UV2);
//
//		//}
//		PlanVec = NextStart;
//
//		//if (P0.NeedOutline && P2.NeedOutline)
//		//{
//		//	OutFramwork.Add(TPair<FVector, FVector>(P0.PointLocation, P2.PointLocation));
//		//}
//
//		if (NextStart.Key && TopIndex < TopCount - 1)
//			++TopIndex;
//		else if(BotIndex < BotCount - 1)
//			++BotIndex;
//
//		bCountEqual = BotNext == 0 || TopNext == 0;
//	}
//
//	return true;
//}




bool UGeomtryMathmaticLibrary::ShiftPlanEdges(const TArray<float>& InShiftValue, TArray<FGeomtryLineProperty> InLine, const FVector& InNormal, FPolygonData& InPlan, TArray<FVector>& OutPlan, TArray<FVector>& OutOffsetPerPoint, TArray<float>& DeltaHeight)
{
	OutPlan = InPlan.PolygonVertice;
	OutOffsetPerPoint.AddZeroed(OutPlan.Num());
	DeltaHeight.AddZeroed(OutPlan.Num());
	if (0 == InShiftValue.Num())
		return true;

	if (InLine.Num() == 0)
	{
		for (int32 i = 0; i < InPlan.PolygonVertice.Num(); ++i)
		{
			const int32 NextIndex = (i + 1) % InPlan.PolygonVertice.Num();
			FGeomtryLineProperty NewLine;
			NewLine.LineType = ELineType::ELineSegment;
			NewLine.StartLocation = InPlan.PolygonVertice[i];
			NewLine.EndLocation = InPlan.PolygonVertice[NextIndex];
			InLine.Add(NewLine);
		}
	}

	TArray<float> ShiftValue;
	{
		int32 i = 0;
		ShiftValue.AddZeroed(InPlan.PolygonVertice.Num());
		for (auto& Iter : InPlan.PolygonVertice)
		{
			ShiftValue[i] = 1 == InShiftValue.Num() ? InShiftValue[0] : (InShiftValue.IsValidIndex(i) ? InShiftValue[i] : 0.0f);
			++i;
		}
	}
	FVector Normal = InNormal;
	for (int32 i = 0; i < InLine.Num(); ++i)
	{
		const int32 NextIndex = (i + 1) % InLine.Num();
		FVector A = FVector(InLine[i].EndLocation - InLine[i].StartLocation).GetSafeNormal();
		FVector B = FVector(InLine[NextIndex].EndLocation - InLine[NextIndex].StartLocation).GetSafeNormal();
		if (A != B)
		{
			Normal = FVector::CrossProduct(A, B);
			Normal.Normalize();
			break;
		}
	}

	auto GetCircleCenter = [&](const FGeomtryLineProperty& InL)
	{
		if (InL.LineType == ELineType::EHeightArc)
		{
			const float HalfChordLength = FVector::Dist(InL.StartLocation, InL.EndLocation) * 0.5f;
			float X = FMath::Sqrt(HalfChordLength * HalfChordLength + InL.RadiusOrHeight() * InL.RadiusOrHeight());

			bool bBig = InL.RadiusOrHeight() >= HalfChordLength;
			float Cos = InL.RadiusOrHeight() / X;
			float Rad = FMath::Abs(X * 0.5f / Cos);
			FVector DirC = InL.EndLocation - InL.StartLocation;
			FVector DirR = FVector::CrossProduct(DirC, Normal).GetSafeNormal();

			FVector ChordCenter = (InL.StartLocation + InL.EndLocation) * 0.5f;

			if (InL.RadiusOrHeight() >= 0)
				return bBig ? ChordCenter + DirR * (InL.RadiusOrHeight() - Rad) : ChordCenter - DirR * (FMath::Abs(Rad- FMath::Abs(InL.RadiusOrHeight())));
			else
				return bBig ? ChordCenter - DirR * (InL.RadiusOrHeight() - Rad) : ChordCenter + DirR * (Rad - InL.RadiusOrHeight());

		}
		else
		{
			const float HalfChordLength = FVector::Dist(InL.StartLocation, InL.EndLocation) * 0.5f;
			float X = FMath::Sqrt(InL.RadiusOrHeight() * InL.RadiusOrHeight() - HalfChordLength * HalfChordLength);
			FVector DirC = InL.EndLocation - InL.StartLocation;
			FVector DirR = InL.BigArc ? FVector::CrossProduct(DirC, Normal).GetSafeNormal() : -FVector::CrossProduct(DirC, Normal).GetSafeNormal();
			FVector ChordCenter = (InL.StartLocation + InL.EndLocation) * 0.5f;
			if (InL.RadiusOrHeight() >= 0)
				return ChordCenter + DirR * X;
			else
				return ChordCenter - DirR * X;
		}
	};
	auto GetCircleCenterMoveOffset = [&](const FGeomtryLineProperty& InL, FVector& Offset)
	{
		FVector C0 = GetCircleCenter(InL);

		bool bBig0 = InL.RadiusOrHeight() >= (FVector::Dist(InL.StartLocation, InL.EndLocation) * 0.5f);
		FVector HC0 = (InL.StartLocation + InL.EndLocation) * 0.5f;
		FVector Dir0 = bBig0 ? C0 - HC0 : HC0 - C0;
		Dir0 = FVector::CrossProduct(InL.EndLocation - InL.StartLocation, Normal);

		if (InL.RadiusOrHeight() < 0)
		{
			Dir0 = -Dir0;
		}

		Dir0.Normalize();

		FVector Top0 = Dir0 * (InL.RadiusOrHeight()) + HC0;
		if(InL.LineType == ELineType::ERadiusArc)
			Offset =  C0 - Top0;
		else if (InL.LineType == ELineType::EHeightArc)
			Offset = Top0 - C0;
	};
	auto GetEndPointDir = [&](const FGeomtryLineProperty& InL)
	{
		if (InL.LineType != ELineType::ELineSegment)
		{
			FVector CirPoint = GetCircleCenter(InL);
			if (InL.RadiusOrHeight() >= 0)
				return FVector::CrossProduct(FVector(CirPoint - InL.EndLocation).GetSafeNormal(), Normal).GetSafeNormal();
			else
				return - FVector::CrossProduct(FVector(CirPoint - InL.EndLocation).GetSafeNormal(), Normal).GetSafeNormal();
		}
		else
		{
			FVector Dir = InL.EndLocation - InL.StartLocation;

			return FVector::CrossProduct(Dir, Normal).GetSafeNormal();
		}
	};
	auto GetStartPointDir = [&](const FGeomtryLineProperty& InL)
	{
		if (InL.LineType != ELineType::ELineSegment)
		{
			FVector CirPoint = GetCircleCenter(InL);
			if (InL.RadiusOrHeight() >= 0)
				return FVector::CrossProduct(FVector(CirPoint - InL.StartLocation).GetSafeNormal(), Normal).GetSafeNormal();
			else
				return -FVector::CrossProduct(FVector(CirPoint - InL.StartLocation).GetSafeNormal(), Normal).GetSafeNormal();

		}
		else
		{
			FVector Dir = InL.EndLocation - InL.StartLocation;
			return -FVector::CrossProduct(Dir, Normal).GetSafeNormal();
		}
	};

	auto GetStartPointDirAndBig = [&](const FGeomtryLineProperty& InL,FVector& V, bool & T)
	{
		if (InL.LineType == ELineType::EHeightArc)
		{
			V = GetStartPointDir(InL);
			T = InL.RadiusOrHeight() >= FVector::Dist(InL.StartLocation, InL.EndLocation) * 0.5f;
		}
		else if (InL.LineType == ELineType::ERadiusArc)
		{
			V = GetStartPointDir(InL);
			T = InL.BigArc;
		}
		else
		{
			V = FVector(InL.EndLocation - InL.StartLocation).GetSafeNormal();
		}
	};

	auto GetEndPointDirAndBig = [&](const FGeomtryLineProperty& InL, FVector& V, bool& T)
	{
		if (InL.LineType == ELineType::EHeightArc)
		{
			V = -GetEndPointDir(InL);
			T = InL.RadiusOrHeight() >= FVector::Dist(InL.StartLocation, InL.EndLocation) * 0.5f;
		}
		else if (InL.LineType == ELineType::ERadiusArc)
		{
			V = -GetEndPointDir(InL);
			T = InL.BigArc;
		}
		else
		{
			V = FVector(InL.StartLocation - InL.EndLocation).GetSafeNormal();
		}
	};
	TArray<int32> MirrorLeft;
	TArray<int32> MirrorRight;
	int32 Count = InLine.Num();
	for (int32 i = 0; i < InLine.Num(); ++i)
	{
		if (i < --Count)
		{
			MirrorLeft.Add(i);
			MirrorRight.Add(Count);
		}
		
		const int32 PreIndex = (i - 1 + InLine.Num()) % InLine.Num();
		//ELineType Type0 = InLine[i].LineType;
		FVector V0 = FVector::ZeroVector;
		bool bBig = false;
		GetStartPointDirAndBig(InLine[i],V0, bBig);

		FVector V1 = FVector::ZeroVector;
		GetEndPointDirAndBig(InLine[PreIndex], V1, bBig);
		FVector NorVec = FVector::CrossProduct(V0, V1 - V0).GetSafeNormal();
		FVector N0 = FVector::CrossProduct(V0, NorVec).GetSafeNormal();
		FVector N1 = FVector::CrossProduct(V1, NorVec).GetSafeNormal();

		OutOffsetPerPoint[i] = N0 * ShiftValue[i] - N1 * ShiftValue[PreIndex];
		float cos = FVector::DotProduct(N0,-N1) / N0.Size() * N1.Size();

		if (cos > 0 && cos < 1)
		{
			OutOffsetPerPoint[i] = OutOffsetPerPoint[i] * 0.5;
		}



		//

		if ((InLine[i].RadiusOrHeight() > 0 || InLine[PreIndex].RadiusOrHeight() > 0))
		{
			//if ((NorVec + Normal).IsNearlyZero(0.001f))
			{
				OutOffsetPerPoint[i] = -OutOffsetPerPoint[i];
			}
		}
		else if (InLine[i].RadiusOrHeight() < 0 || InLine[PreIndex].RadiusOrHeight() < 0)
		{
			//if ((NorVec + Normal).IsNearlyZero(0.001f))
			{
				OutOffsetPerPoint[i] = -OutOffsetPerPoint[i];
			}
		}
		
	}


	// check lines
	TArray<FVector> NewOffset = OutOffsetPerPoint;

	//for (int32 i = 0; i < InLine.Num(); ++i)
	//{
	//	const int32 PreIndex = (i - 1 + InLine.Num()) % InLine.Num();
	//	const int32 NextIndex = (i + 1) % InLine.Num();
	//	FVector A = FVector(InLine[i].EndLocation - InLine[i].StartLocation).GetSafeNormal();
	//	FVector B = FVector((InLine[i].EndLocation + NewOffset[NextIndex])  -  (InLine[i].StartLocation + NewOffset[i])).GetSafeNormal();


	//	if (InLine[i].LineType == ELineType::ELineSegment)
	//	{
	//		if (A.Equals(B,0.1f))
	//			continue;
	//		FVector Anchor = FVector::ZeroVector;
	//		FVector Offset = FVector::ZeroVector;
	//		FVector V0 = FVector::ZeroVector;
	//		FVector V1 = FVector::ZeroVector;
	//		bool bBig = false;
	//		//GetStartPointDirAndBig(InLine[NextIndex], V0, bBig);
	//		V0 = FVector(InLine[NextIndex].EndLocation - InLine[NextIndex].StartLocation).GetSafeNormal();
	//		V1 = FVector(InLine[PreIndex].StartLocation - InLine[PreIndex].EndLocation).GetSafeNormal();
	//		//GetEndPointDirAndBig(InLine[PreIndex], V1, bBig);
	//		bool bLeft = false;
	//		if (MirrorLeft.Contains(i))
	//		{
	//			GetCircleCenterMoveOffset(InLine[NextIndex], Offset);
	//			Anchor = InLine[i].StartLocation;
	//			bLeft = true;
	//		}
	//		else if (MirrorRight.Contains(i))
	//		{
	//			GetCircleCenterMoveOffset(InLine[PreIndex], Offset);
	//			Anchor = InLine[i].EndLocation;
	//		}
	//		else
	//			continue;

	//		FVector NewPos = FVector::ZeroVector;
	//		float Sin = FVector::CrossProduct(V0, V1).Size() / (V0.Size() * V1.Size());
	//		if (Sin != 0)
	//			NewPos = -ShiftValue[i] / Sin * (V0 + V1) + Offset;

	//		OutOffsetPerPoint[i] = Anchor + NewPos - InLine[i].StartLocation;
	//		OutOffsetPerPoint[NextIndex] = Anchor + NewPos - InLine[i].EndLocation;
	//	}
	//}


	for (int32 i = 0; i < InLine.Num(); ++i)
	{
		const int32 NextIndex = (i + 1) % InLine.Num();
		if (InLine[i].LineType == ELineType::ERadiusArc)
		{
			DeltaHeight[i] = - ShiftValue[i];
		}
		else if (InLine[i].LineType == ELineType::EHeightArc)
		{
			FVector C0 = GetCircleCenter(InLine[i]);

			FGeomtryLineProperty NewLine = InLine[i];
			NewLine.StartLocation = InLine[i].StartLocation + OutOffsetPerPoint[i];
			NewLine.EndLocation = InLine[NextIndex].StartLocation + OutOffsetPerPoint[NextIndex];
			if (FMath::IsNearlyEqual(InLine[i].RadiusOrHeight(), FVector::Dist(InLine[i].StartLocation, InLine[i].EndLocation) * 0.5f))
			{
				DeltaHeight[i] = ShiftValue[i];
				return true;
			}

			bool bBig0 = InLine[i].RadiusOrHeight() >= (FVector::Dist(InLine[i].StartLocation, InLine[i].EndLocation) * 0.5f);
			FVector HC0 = (InLine[i].StartLocation + InLine[i].EndLocation) * 0.5f;
			FVector Dir0 = bBig0 ? C0 - HC0 : HC0 - C0;
			Dir0 = FVector::CrossProduct(NewLine.EndLocation - NewLine.StartLocation, Normal);

			if (bBig0 || InLine[i].RadiusOrHeight() < 0)
			{
				Dir0 = -Dir0;
			}

			Dir0.Normalize();

			FVector Top0 = Dir0 * (InLine[i].RadiusOrHeight()) + HC0;
			FVector HC1 = (NewLine.StartLocation + NewLine.EndLocation) * 0.5f;
			FVector C1 = GetCircleCenter(NewLine);

			bool bBig1 = NewLine.RadiusOrHeight() >= (FVector::Dist(NewLine.StartLocation, NewLine.EndLocation) * 0.5f);
			FVector Dir1 = FVector::CrossProduct(NewLine.EndLocation - NewLine.StartLocation, Normal);;

			if (bBig1 || NewLine.RadiusOrHeight() < 0)
			{
				Dir1 = -Dir1;
			}
		
			Dir1.Normalize();

			FVector Top1 = Dir1 * (NewLine.RadiusOrHeight()) + HC1;
			UE_LOG(LogTemp, Error, TEXT("Dir1   % s"), *Dir1.ToString());
			UE_LOG(LogTemp, Error, TEXT("Top1   % s"), *Top1.ToString());

			float TopDis = FVector::Dist(Top0, Top1);
			UE_LOG(LogTemp, Error, TEXT("TopDis   % f"), TopDis);


			if (InLine[i].RadiusOrHeight() > 0)
			{
				if (bBig0)
				{
					DeltaHeight[i] = TopDis + FMath::Abs(ShiftValue[i]);
					if (ShiftValue[i] < 0)
						DeltaHeight[i] = -DeltaHeight[i];
				}
				else
				{
					DeltaHeight[i] = FMath::Abs(TopDis  - FMath::Abs(ShiftValue[i]));
					if (ShiftValue[i] < 0)
						DeltaHeight[i] = -DeltaHeight[i];
				}
			}
			else
			{
				if (bBig0)
				{
					DeltaHeight[i] = TopDis + FMath::Abs(ShiftValue[i]) ;
					if (ShiftValue[i] < 0)
						DeltaHeight[i] = -DeltaHeight[i];
				}
				else
				{
					DeltaHeight[i] = FMath::Abs(TopDis - FMath::Abs(ShiftValue[i]));
					if (ShiftValue[i] < 0)
						DeltaHeight[i] = -DeltaHeight[i];
				}
			}

		}
	}
	return true;
}

bool UGeomtryMathmaticLibrary::LoftSection(const FPMCSection& InSectionMesh, const FVector& InSectionUpVector, FPolygonData& InLoftRoutine, const FVector& InLoftRoutinePlanNormal, TArray<FPMCSection>& OutSectionMeshs, bool& OutNeedLoop)
{
	if (0 == InLoftRoutine.PolygonVertice.Num())
		return true;
	TArray<FVector> LoftRoutineFormatted;
	OutNeedLoop = false;
	UGeomtryMathmaticLibrary::FormatLoftRoutine(InLoftRoutine.PolygonVertice, LoftRoutineFormatted, OutNeedLoop);
	OutSectionMeshs.AddDefaulted(LoftRoutineFormatted.Num());
	if (1 == LoftRoutineFormatted.Num())
	{
		OutSectionMeshs[0] = InSectionMesh;
		UGeomtryMathmaticLibrary::MoveSection(OutSectionMeshs[0], LoftRoutineFormatted[0]);
		return true;
	}
	if (2 == LoftRoutineFormatted.Num())
	{
		FVector RightVector = LoftRoutineFormatted[1] - LoftRoutineFormatted[0];
		RightVector.Normalize();
		FVector ForwordVector = FVector::CrossProduct(RightVector, InLoftRoutinePlanNormal);
		ForwordVector.Normalize();
		for (int32 i = 0; i < OutSectionMeshs.Num(); ++i)
		{
			OutSectionMeshs[i] = InSectionMesh;
			for (int32 j = 0; j < OutSectionMeshs[i].Vertexes.Num(); ++j)
			{
				OutSectionMeshs[i].Vertexes[j] = UGeomtryMathmaticLibrary::TransformPoint(OutSectionMeshs[i].Vertexes[j], InSectionUpVector, ForwordVector, InLoftRoutinePlanNormal, RightVector);
			}
			UGeomtryMathmaticLibrary::MoveSection(OutSectionMeshs[i], LoftRoutineFormatted[i]);
		}
		{
			int32 i = 0;
			OutSectionMeshs[0].Normals.Init(FVector::ZeroVector, OutSectionMeshs[0].Vertexes.Num());
			OutSectionMeshs[1].Normals.Init(FVector::ZeroVector, OutSectionMeshs[1].Vertexes.Num());
			while (i < OutSectionMeshs[0].Normals.Num())
			{//Reserve the first plan
				OutSectionMeshs[0].Normals[i] = -RightVector;
				OutSectionMeshs[0].Normals[i] = -RightVector;
				OutSectionMeshs[0].Normals[i] = -RightVector;
				OutSectionMeshs[1].Normals[i] = RightVector;
				OutSectionMeshs[1].Normals[i] = RightVector;
				OutSectionMeshs[1].Normals[i] = RightVector;
				++i;
			}
		}
		return true;
	}
	{
		InLoftRoutine.PolygonVertice = LoftRoutineFormatted;
		bool Res = UGeometryRelativeLibrary::JudgePolygonVerticesOrder(InLoftRoutine);
		if (!Res)
			return false;
		int32 i = OutNeedLoop ? 0 : 1;
		int32 Count = OutNeedLoop ? OutSectionMeshs.Num() : OutSectionMeshs.Num() - 1;
		bool RemoveFirst = false;
		while (i < Count)
		{
			OutSectionMeshs[i] = InSectionMesh;
			int32 StartIndex = i;
			int32 EndIndex = (0 == StartIndex ? OutSectionMeshs.Num() : StartIndex) - 1;
			int32 PreIndex = (StartIndex + 1) % OutSectionMeshs.Num();
			FVector RightVector = LoftRoutineFormatted[EndIndex] - LoftRoutineFormatted[StartIndex];
			RightVector.Normalize();
			FVector ForwordVector = LoftRoutineFormatted[PreIndex] - LoftRoutineFormatted[StartIndex];
			ForwordVector.Normalize();
			if (FVector::Parallel(RightVector, ForwordVector) && 0 == StartIndex)
			{
				++i;
				RemoveFirst = true;
				continue;
			}
			ForwordVector += RightVector;
			EVerticeType VerticeType;
			Res = UGeometryRelativeLibrary::JudgePolygonVerticesType(LoftRoutineFormatted[PreIndex], LoftRoutineFormatted[EndIndex], LoftRoutineFormatted[StartIndex], InLoftRoutine.PolygonPlan, InLoftRoutine.PolygonVerticesOrder, VerticeType);
			if (!Res)
				return false;
			if (EVerticeType::EConvex == VerticeType)
				ForwordVector *= -1.0f;
			ForwordVector.Normalize();
			for (int32 j = 0; j < OutSectionMeshs[i].Vertexes.Num(); ++j)
			{
				OutSectionMeshs[i].Normals.Init(FVector::ZeroVector, OutSectionMeshs[i].Vertexes.Num());
				OutSectionMeshs[i].Vertexes[j] = UGeomtryMathmaticLibrary::TransformPoint(OutSectionMeshs[i].Vertexes[j], InSectionUpVector, ForwordVector, InLoftRoutinePlanNormal, RightVector);
			}
			UGeomtryMathmaticLibrary::MoveSection(OutSectionMeshs[i], LoftRoutineFormatted[i]);
			++i;
		}
		if (RemoveFirst)
			OutSectionMeshs.RemoveAt(0);
	}

	if (!OutNeedLoop)
	{
		//Start point
		OutSectionMeshs[0] = InSectionMesh;
		FVector RightVector = LoftRoutineFormatted[0] - LoftRoutineFormatted[1];
		RightVector.Normalize();
		OutSectionMeshs[0].Normals.Init(RightVector, OutSectionMeshs[0].Vertexes.Num());
		FVector ForwordVector = LoftRoutineFormatted[2] - LoftRoutineFormatted[1];
		FVector LocalForward = FVector::CrossProduct(RightVector, InLoftRoutinePlanNormal);
		ForwordVector = FVector::DotProduct(LocalForward, ForwordVector) * LocalForward;
		ForwordVector.Normalize();
		for (int32 j = 0; j < OutSectionMeshs[0].Vertexes.Num(); ++j)
		{
			OutSectionMeshs[0].Vertexes[j] = UGeomtryMathmaticLibrary::TransformPoint(OutSectionMeshs[0].Vertexes[j], InSectionUpVector, ForwordVector, InLoftRoutinePlanNormal, RightVector);
		}
		UGeomtryMathmaticLibrary::MoveSection(OutSectionMeshs[0], LoftRoutineFormatted[0]);

		//End point
		OutSectionMeshs[OutSectionMeshs.Num() - 1] = InSectionMesh;
		RightVector = LoftRoutineFormatted[OutSectionMeshs.Num() - 1] - LoftRoutineFormatted[OutSectionMeshs.Num() - 2];
		RightVector.Normalize();
		OutSectionMeshs[OutSectionMeshs.Num() - 1].Normals.Init(RightVector, OutSectionMeshs[OutSectionMeshs.Num() - 1].Vertexes.Num());
		ForwordVector = LoftRoutineFormatted[OutSectionMeshs.Num() - 3] - LoftRoutineFormatted[OutSectionMeshs.Num() - 2];
		LocalForward = FVector::CrossProduct(RightVector, InLoftRoutinePlanNormal);
		ForwordVector = FVector::DotProduct(LocalForward, ForwordVector) * LocalForward;
		ForwordVector.Normalize();
		for (int32 j = 0; j < OutSectionMeshs[OutSectionMeshs.Num() - 1].Vertexes.Num(); ++j)
		{
			OutSectionMeshs[OutSectionMeshs.Num() - 1].Vertexes[j] = UGeomtryMathmaticLibrary::TransformPoint(OutSectionMeshs[OutSectionMeshs.Num() - 1].Vertexes[j], InSectionUpVector, ForwordVector, InLoftRoutinePlanNormal, RightVector);
		}
		UGeomtryMathmaticLibrary::MoveSection(OutSectionMeshs[OutSectionMeshs.Num() - 1], LoftRoutineFormatted[OutSectionMeshs.Num() - 1]);
	}
	return true;
}

void UGeomtryMathmaticLibrary::FormatLoftRoutine(const TArray<FVector>& InLoftRoutine, TArray<FVector>& OutLoftRoutine, bool& OutNeedLoop)
{
	OutLoftRoutine.Add(InLoftRoutine[0]);
	for (int32 i = 1; i < InLoftRoutine.Num(); ++i)
	{
		if (InLoftRoutine.IsValidIndex(i + 1))
		{
			FVector V1 = InLoftRoutine[i] - InLoftRoutine[i - 1];
			V1.Normalize();
			FVector V2 = InLoftRoutine[i + 1] - InLoftRoutine[i];
			V2.Normalize();
			UE_LOG(LogTemp, Log, TEXT("cosine is :  %f "), FMath::Abs(FVector::DotProduct(V1, V2)));
			if (!FMath::IsNearlyEqual(FMath::Abs(FVector::DotProduct(V1, V2)), 1.0f))
			{
				OutLoftRoutine.AddUnique(InLoftRoutine[i]);
			}
		}
		else
		{
			OutLoftRoutine.AddUnique(InLoftRoutine[i]);
		}
	}
	OutNeedLoop = OutLoftRoutine.Num() > 2 && InLoftRoutine[0].Equals(InLoftRoutine[InLoftRoutine.Num() - 1], 0.01f);
}

void UGeomtryMathmaticLibrary::MoveSection(FPMCSection& InOutSectionMesh, const FVector& InTargetLocation)
{
	for (auto& Iter : InOutSectionMesh.Vertexes)
	{
		Iter += InTargetLocation;
	}
}

FVector UGeomtryMathmaticLibrary::TransformPoint(const FVector& InPoint, const FVector& InPointUpVector, const FVector& InForward, const FVector& InNormal, const FVector& InRight)
{
	const float D = FVector::DotProduct(InPoint, InPointUpVector);
	FVector TP = FVector::DotProduct(InPoint, InNormal) * InNormal;
	const float K = D / FMath::Sin(FMath::Acos(FVector::DotProduct(InForward, InRight)));
	FVector P = K * InForward + TP;
	return P;
}

bool UGeomtryMathmaticLibrary::RotatorSection(const FPMCSection& InSectionMesh, EPlanPolygonBelongs PlanType, const FVector& InRotatorAxis, const FVector& InSectionUpVector, const FVector& InSectionNormal, const float& InStartAngle, const float& InEndAngle, TArray<FPMCSection>& OutSectionMeshs)
{
	FVector SectionRight = FVector::CrossProduct(InSectionUpVector, InSectionNormal);
	SectionRight.Normalize();
	int32 StepCount = static_cast<int32>((InEndAngle - InStartAngle) / 360.f);
	OutSectionMeshs.AddDefaulted(StepCount + 1);
	auto GenerateArcPoints = [PlanType](const float& Radius, const float& StartAngle, const float& EndAngle, const int32& StepCount, const FVector& CenterPoint, TArray<FVector>& OutAcrPoints)
	{
		float DeltaAngle = (EndAngle - StartAngle) / StepCount;
		int32 i = 0;
		OutAcrPoints.AddZeroed(StepCount + 1);
		while (i <= StepCount)
		{
			float Angle = StartAngle + i * DeltaAngle;
			FVector NewPoint = FVector::ZeroVector;
			const float X = Radius * UKismetMathLibrary::DegCos(Angle);
			const float Y = Radius * UKismetMathLibrary::DegSin(Angle);
			switch (PlanType)
			{
			case EPlanPolygonBelongs::EXY_Plan: NewPoint.X = X + CenterPoint.X; NewPoint.Y = Y + CenterPoint.Y; NewPoint.Z = CenterPoint.Z; break;
			case EPlanPolygonBelongs::EYZ_Plan: NewPoint.X = CenterPoint.X; NewPoint.Y = X + CenterPoint.Y; NewPoint.Z = Y + CenterPoint.Z; break;
			case EPlanPolygonBelongs::EXZ_Plan: NewPoint.X = X + CenterPoint.X; NewPoint.Y = CenterPoint.Y; NewPoint.Z = Y + CenterPoint.Z; break;
			default: checkNoEntry(); break;
			}
			OutAcrPoints[i] = NewPoint;
			++i;
		}
	};
	int32 i = 0;
	while (i <= StepCount)
		OutSectionMeshs[i++].Vertexes.AddZeroed(InSectionMesh.Vertexes.Num());
	i = 0;
	while (i < InSectionMesh.Vertexes.Num())
	{
		TArray<FVector> ArcPoint;
		float Radius = FMath::Abs(FVector::DotProduct(SectionRight, InSectionMesh.Vertexes[i] - InRotatorAxis));
		GenerateArcPoints(Radius, InStartAngle, InEndAngle, StepCount, InRotatorAxis, ArcPoint);
		int32 j = 0;
		while (j < ArcPoint.Num())
		{
			OutSectionMeshs[j].Vertexes[i] = ArcPoint[j];
			++j;
		}
		++i;
	}
	return true;
}

UGeomtryMathmaticLibrary::FCubeMap::FCubeMap(const FBox& InBox)
	:Box(InBox)
{
	UVScale[0].X = -InBox.GetExtent().Y / 50.0f;
	UVScale[0].Y = InBox.GetExtent().Z / 50.0f;

	UVScale[1].X = -InBox.GetExtent().Y / 50.0f;
	UVScale[1].Y = InBox.GetExtent().Z / 50.0f;

	UVScale[2].X = -InBox.GetExtent().X / 50.0f;
	UVScale[2].Y = InBox.GetExtent().Z / 50.0f;

	UVScale[3].X = -InBox.GetExtent().X / 50.0f;
	UVScale[3].Y = InBox.GetExtent().Z / 50.0f;

	UVScale[4].X = -InBox.GetExtent().X / 50.0f;
	UVScale[4].Y = InBox.GetExtent().Y / 50.0f;

	UVScale[5].X = InBox.GetExtent().X / 50.0f;
	UVScale[5].Y = InBox.GetExtent().Y / 50.0f;
}

void UGeomtryMathmaticLibrary::FCubeMap::ScaleUV(const int32& InIndex, FVector2D& InOutUV) const
{
	InOutUV.X *= UVScale[InIndex].X;
	InOutUV.Y *= UVScale[InIndex].Y;
}

FVector UGeomtryMathmaticLibrary::FCubeMap::MapToCubeLocation(const int32& InIndex, const FVector& InLocation) const
{
	FVector LocationOnCube = InLocation - Box.GetCenter();
	LocationOnCube.X /= Box.GetExtent().X;
	LocationOnCube.Y /= Box.GetExtent().Y;
	LocationOnCube.Z /= Box.GetExtent().Z;
	switch (InIndex)
	{
	case 0:LocationOnCube.X = 1.0f; break;
	case 1:LocationOnCube.X = -1.0f; break;
	case 2:LocationOnCube.Y = 1.0f; break;
	case 3:LocationOnCube.Y = -1.0f; break;
	case 4:LocationOnCube.Z = 1.0f; break;
	case 5:LocationOnCube.Z = -1.0f; break;
	}
	return LocationOnCube;
}

FVector2D UGeomtryMathmaticLibrary::FCubeMap::ConvertLocationToUV(const FVector& InLocation, const int32& InIndex) const
{
	float absX = fabs(InLocation.X);
	float absY = fabs(InLocation.Y);
	float absZ = fabs(InLocation.Z);

	float maxAxis = 0.0f;
	float uc = 0.0f;
	float vc = 0.0f;

	// POSITIVE X
	if (0 == InIndex/*isXPositive && absX >= absY && absX >= absZ*/) {
		// u (0 to 1) goes from +z to -z
		// v (0 to 1) goes from -y to +y
		maxAxis = absX;
		uc = InLocation.Y;
		vc = -InLocation.Z;
	}
	// NEGATIVE X
	if (1 == InIndex/*!isXPositive && absX >= absY && absX >= absZ*/) {
		// u (0 to 1) goes from -z to +z
		// v (0 to 1) goes from -y to +y
		maxAxis = absX;
		uc = -InLocation.Y;
		vc = -InLocation.Z;
	}
	// POSITIVE Y
	if (2 == InIndex/*isYPositive && absY >= absX && absY >= absZ*/) {
		// u (0 to 1) goes from -x to +x
		// v (0 to 1) goes from +z to -z
		maxAxis = absY;
		uc = -InLocation.X;
		vc = -InLocation.Z;
	}
	// NEGATIVE Y
	if (3 == InIndex/*!isYPositive && absY >= absX && absY >= absZ*/) {
		// u (0 to 1) goes from -x to +x
		// v (0 to 1) goes from -z to +z
		maxAxis = absY;
		uc = InLocation.X;
		vc = -InLocation.Z;
	}
	// POSITIVE Z
	if (4 == InIndex/*isZPositive && absZ >= absX && absZ >= absY*/) {
		// u (0 to 1) goes from -x to +x
		// v (0 to 1) goes from -y to +y
		maxAxis = absZ;
		uc = -InLocation.X;
		vc = InLocation.Y;
	}
	// NEGATIVE Z
	if (5 == InIndex/*!isZPositive && absZ >= absX && absZ >= absY*/) {
		// u (0 to 1) goes from +x to -x
		// v (0 to 1) goes from -y to +y
		maxAxis = absZ;
		uc = -InLocation.X;
		vc = InLocation.Y;
	}

	// Convert range from -1 to 1 to 0 to 1
	return FVector2D(0.5f * (uc / maxAxis + 1.0f), 0.5f * (vc / maxAxis + 1.0f));
}

void UGeomtryMathmaticLibrary::GenerateMeshUV(const FPMCSection& InMesh, FPMCSection& OutMesh)
{
	FBox BoundingBox;
	InMesh.GetBoundingBox(BoundingBox);
	FCubeMap CubeMap(BoundingBox);
	TArray<FMeshFace> FacesAfterSort[6];
	int32 i = 0;
	while (i < InMesh.Triangles.Num())
	{
		if (InMesh.Triangles.IsValidIndex(i))
		{
			FMeshFace NewFace(InMesh.Vertexes[InMesh.Triangles[i]], InMesh.Vertexes[InMesh.Triangles[i + 1]], InMesh.Vertexes[InMesh.Triangles[i + 2]]);
			FVector OutNormalDir = InMesh.Normals[InMesh.Triangles[i]];
			OutNormalDir.Normalize();
			//if (FVector::DotProduct(OutNormalDir, NewFace.FaceNormal) < 0.0f)
			//{
			//	NewFace.FaceNormal = -NewFace.FaceNormal;
			//}
			//else
			//{
			//	//NewFace.SwapPoint();
			//}
			NewFace.SetPointNormal(OutNormalDir);
			int32 FaceIndex = UGeomtryMathmaticLibrary::MapToCubeFaceIndex(NewFace.FaceNormal);
			for (int32 j = 0; j < 3; ++j)
			{
				FVector NewLocation = CubeMap.MapToCubeLocation(FaceIndex, NewFace.FaceLocations[j]);
				NewFace.UV[j] = CubeMap.ConvertLocationToUV(NewLocation, FaceIndex);
				CubeMap.ScaleUV(FaceIndex, NewFace.UV[j]);
			}
			FacesAfterSort[FaceIndex].Add(NewFace);
		}
		i += 3;
	}
	FPMCSection CustomMesh[6];
	for (int32 j = 0; j < 6; ++j)
	{
		for (int32 m = 0; m < FacesAfterSort[j].Num(); ++m)
		{
			int32 IndiceOffset = CustomMesh[j].Triangles.AddZeroed(3);
			for (int32 n = 0; n < 3; ++n)
			{
				int32 Num = CustomMesh[j].Vertexes.Num();
				int32 Index = CustomMesh[j].Vertexes.AddUnique(FacesAfterSort[j][m].FaceLocations[n]);
				if (Num == Index)
				{
					CustomMesh[j].UV.Add(FacesAfterSort[j][m].UV[n]);
					CustomMesh[j].Normals.Add(FacesAfterSort[j][m].PointNormal[n]);
				}
				else
				{
					CustomMesh[j].Normals[Index] += FacesAfterSort[j][m].PointNormal[n];
					CustomMesh[j].Normals[Index].Normalize();
				}

				CustomMesh[j].Triangles[IndiceOffset + n] = Index;
			}
		}
	}
	for (int32 j = 0; j < 6; ++j)
	{
		int32 Offset = OutMesh.Vertexes.Num();
		OutMesh.Vertexes.Append(CustomMesh[j].Vertexes);
		OutMesh.UV.Append(CustomMesh[j].UV);
		OutMesh.Normals.Append(CustomMesh[j].Normals);
		int32 k = OutMesh.Triangles.Num();
		OutMesh.Triangles.Append(CustomMesh[j].Triangles);
		for (int32 m = 0; m < CustomMesh[j].Triangles.Num(); ++m)
		{
			OutMesh.Triangles[k + m] = CustomMesh[j].Triangles[m] + Offset;
		}
	}
}

int32 UGeomtryMathmaticLibrary::MapToCubeFaceIndex(const FVector& InFaceNormal)
{
	float Angle0 = FVector::DotProduct(InFaceNormal, FVector::ForwardVector);
	float Angle1 = FVector::DotProduct(InFaceNormal, -FVector::ForwardVector);
	float Angle2 = FVector::DotProduct(InFaceNormal, FVector::RightVector);
	float Angle3 = FVector::DotProduct(InFaceNormal, -FVector::RightVector);
	float Angle4 = FVector::DotProduct(InFaceNormal, FVector::UpVector);
	float Angle5 = FVector::DotProduct(InFaceNormal, -FVector::UpVector);
	float MinAngle = FMath::Min3<float>(Angle0, Angle1, Angle2);
	{
		float K = FMath::Min3<float>(Angle3, Angle4, Angle5);
		MinAngle = FMath::Min<float>(MinAngle, K);
	}
	if (FMath::IsNearlyEqual(MinAngle, Angle0)) return 0;
	if (FMath::IsNearlyEqual(MinAngle, Angle1)) return 1;
	if (FMath::IsNearlyEqual(MinAngle, Angle2)) return 2;
	if (FMath::IsNearlyEqual(MinAngle, Angle3)) return 3;
	if (FMath::IsNearlyEqual(MinAngle, Angle4)) return 4;
	return 5;
}
