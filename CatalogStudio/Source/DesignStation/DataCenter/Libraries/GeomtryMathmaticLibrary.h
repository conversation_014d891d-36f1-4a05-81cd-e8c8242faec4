// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PMCSection.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "GeomtryMathmaticLibrary.generated.h"

struct FCustomPoint
{
	FVector PointLocation;
	bool NeedOutline;
	FCustomPoint() :PointLocation(FVector::ZeroVector), NeedOutline(false) {}
	FCustomPoint(const FVector& InLocation, const bool& InNeedOutline) :PointLocation(InLocation), NeedOutline(InNeedOutline) {}

	static bool ConvertPointsToCustomPoints(const TArray<FVector>& InPoints, const bool& InNeedOutline, TArray<FCustomPoint>& OutCustomPoints);

	static bool ConvertCustomPointsToPoints(const TArray<FCustomPoint>& InCustomPoints, TArray<FVector>& OutPoints);
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UGeomtryMathmaticLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

		class FCubeMap
	{
	public:
		FCubeMap(const FBox& InBox);
		FVector MapToCubeLocation(const int32& InIndex, const FVector& InLocation) const;
		FVector2D ConvertLocationToUV(const FVector& InLocation, const int32& InIndex) const;
		void ScaleUV(const int32& InIndex, FVector2D& InOutUV) const;
	private:
		FVector2D	UVScale[6];
		FBox Box;
	};

	struct FMeshFace
	{
		TArray<FVector> FaceLocations;
		FVector			FaceNormal;
		FVector2D		UV[3];
		FVector			PointNormal[3];
		FMeshFace(TArray<FVector>& InFaceLocations)
		{
			FaceLocations.Append(InFaceLocations);
			FaceNormal = FVector::CrossProduct((FaceLocations[1] - FaceLocations[0]), (FaceLocations[2] - FaceLocations[1]));
			FaceNormal.Normalize();
			UV[0] = FVector2D::ZeroVector;
			UV[1] = FVector2D::ZeroVector;
			UV[2] = FVector2D::ZeroVector;
			PointNormal[0] = FVector::ZeroVector;
			PointNormal[1] = FVector::ZeroVector;
			PointNormal[2] = FVector::ZeroVector;
		}
		FMeshFace(const FVector& InLocation0, const FVector& InLocation1, const FVector& InLocation2)
		{
			FaceLocations.AddZeroed(3);
			FaceLocations[0] = InLocation0;
			FaceLocations[1] = InLocation1;
			FaceLocations[2] = InLocation2;
			FaceNormal = FVector::CrossProduct((FaceLocations[1] - FaceLocations[0]), (FaceLocations[2] - FaceLocations[1]));
			FaceNormal.Normalize();
			UV[0] = FVector2D::ZeroVector;
			UV[1] = FVector2D::ZeroVector;
			UV[2] = FVector2D::ZeroVector;
			PointNormal[0] = FVector::ZeroVector;
			PointNormal[1] = FVector::ZeroVector;
			PointNormal[2] = FVector::ZeroVector;
		}
		void SetPointNormal(const FVector& InNormal)
		{
			PointNormal[0] = InNormal;
			PointNormal[1] = InNormal;
			PointNormal[2] = InNormal;
		}
		void SwapPoint()
		{
			FVector PointLocation = FaceLocations[0];
			FaceLocations[0] = FaceLocations[2];
			FaceLocations[2] = PointLocation;
		}
	};

public:

	static bool GenerateMeshOfTwoParallelPlans(const FVector& InNormal, const TArray<FCustomPoint>& InPlan1, const TArray<FCustomPoint>& InPlan2, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramwork, const EPlanPolygonBelongs& InPlan = EPlanPolygonBelongs::EUnknown);

	static bool ShiftPlanEdges(const TArray<float>& InShiftValue, TArray<FGeomtryLineProperty> InLine,const FVector& Normal, FPolygonData& InPlan, TArray<FVector>& OutPlan, TArray<FVector>& OutOffsetPerPoint, TArray<float> & DeltaHeight);

	static bool LoftSection(const FPMCSection& InSectionMesh, const FVector& InSectionUpVector, FPolygonData& InLoftRoutine, const FVector& InLoftRoutinePlanNormal, TArray<FPMCSection>& OutSectionMeshs, bool& OutNeedLoop);

	static bool RotatorSection(const FPMCSection& InSectionMesh, EPlanPolygonBelongs PlanType, const FVector& InRotatorAxis, const FVector& InSectionUpVector, const FVector& InSectionNormal, const float& InStartAngle, const float& InEndAngle, TArray<FPMCSection>& OutSectionMeshs);

	static void GenerateMeshUV(const FPMCSection& InMesh, FPMCSection& OutMesh);
private:

	static int32 MapToCubeFaceIndex(const FVector& InFaceNormal);

	static void FormatLoftRoutine(const TArray<FVector>& InLoftRoutine, TArray<FVector>& OutLoftRoutine, bool& OutNeedLoop);

	static FVector TransformPoint(const FVector& InPoint, const FVector& InPointUpVector, const FVector& InForward, const FVector& InNormal, const FVector& InRight);

	static void MoveSection(FPMCSection& InOutSectionMesh, const FVector& InTargetLocation);
};
