// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "LoginData.generated.h"

struct DESIGNSTATION_API FLoginRemeberInfo
{
	FString UserName;
	FString Password;
	bool	RemeberMe;
	FLoginRemeberInfo():UserName(TEXT("")),Password(TEXT("")),RemeberMe(false){}
	FLoginRemeberInfo(const FString& InUserName, const FString& InPassword, bool InRemeberMe);
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API ULoginData : public UObject
{
	GENERATED_BODY()
	
public:

	
};
