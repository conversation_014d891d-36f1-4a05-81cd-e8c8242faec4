// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "MultiThreadFunctionLibrary.generated.h"

#define CALL_THREAD_INNER(EventRef, InTaskDelegate, OtherTask, CallThreadName) \
EventRef = FSimpleDelegateGraphTask::CreateAndDispatchWhenReady(InTaskDelegate, TStatId(), OtherTask, CallThreadName)

#define CALL_THREAD_LAMBDA_FUNC(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::Create<PERSON><PERSON>bda(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_STATIC_FUNC(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateStatic(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UFUNCTION_FUNC(EventRef, OtherTask, CallThreadName, Object, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateUFunction(Object, Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UOBJECT_FUNC(EventRef, OtherTask, CallThreadName, Object, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateUObject(Object, Method,##__VA_ARGS__), OtherTask, CallThreadName)

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UMultiThreadFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 *  Bind Function RetVal Is void
	 */

	template<typename FunctorType, typename... VarTypes>
	static void CallThreadBindLambda(FGraphEventRef& OutEventRef, const FGraphEventRef& InPreEvent, ENamedThreads::Type InDesiredThread
		, FunctorType&& InFunc, VarTypes... Vars)
	{
		CALL_THREAD_LAMBDA_FUNC(OutEventRef, InPreEvent, InDesiredThread, InFunc, Vars...);
	}

	template<typename FunctorType, typename... VarTypes>
	static void CallThreadBindLambda(FGraphEventRef& OutEventRef, const FGraphEventArray* InPreEvent, ENamedThreads::Type InDesiredThread
		, FunctorType&& InFunc, VarTypes... Vars)
	{
		CALL_THREAD_LAMBDA_FUNC(OutEventRef, InPreEvent, InDesiredThread, InFunc, Vars...);
	}
	
	template<typename... VarTypes>
	static void CallThreadBindStatic(FGraphEventRef& OutEventRef, const FGraphEventRef& InPreEvent, ENamedThreads::Type InDesiredThread
		, typename TIdentity<void(*)(VarTypes...)>::Type InFunc, VarTypes... Vars)
	{
		CALL_THREAD_STATIC_FUNC(OutEventRef, InPreEvent, InDesiredThread, InFunc, Vars...);
	}

	template<typename... VarTypes>
	static void CallThreadBindStatic(FGraphEventRef& OutEventRef, const FGraphEventArray* InPreEvent, ENamedThreads::Type InDesiredThread
		, typename TIdentity<void(*)(VarTypes...)>::Type InFunc, VarTypes... Vars)
	{
		CALL_THREAD_STATIC_FUNC(OutEventRef, InPreEvent, InDesiredThread, InFunc, Vars...);
	}

	template<typename UObjectTemplate, typename... VarTypes>
	static void CallThreadBindUFunction(FGraphEventRef& OutEventRef, const FGraphEventRef& InPreEvent, ENamedThreads::Type InDesiredThread
		, UObjectTemplate* InUserObject, const FName& InFunctionName, VarTypes... Vars)
	{
		CALL_THREAD_UFUNCTION_FUNC(OutEventRef, InPreEvent, InDesiredThread, InUserObject, InFunctionName, Vars...);
	}

	template<typename UObjectTemplate, typename... VarTypes>
	static void CallThreadBindUFunction(FGraphEventRef& OutEventRef, const FGraphEventArray* InPreEvent, ENamedThreads::Type InDesiredThread
		, UObjectTemplate* InUserObject, const FName& InFunctionName, VarTypes... Vars)
	{
		CALL_THREAD_UFUNCTION_FUNC(OutEventRef, InPreEvent, InDesiredThread, InUserObject, InFunctionName, Vars...);
	}

	template<typename UserClass, typename... VarTypes>
	static void CallThreadBindUObject(FGraphEventRef& OutEventRef, const FGraphEventRef& InPreEvent, ENamedThreads::Type InDesiredThread
		, UserClass* InUserObject, typename TMemFunPtrType<true, UserClass, void(VarTypes...)>::Type InFunc, VarTypes... Vars)
	{
		CALL_THREAD_UOBJECT_FUNC(OutEventRef, InPreEvent, InDesiredThread, InUserObject, InFunc, Vars...);
	}

	template<typename UserClass, typename... VarTypes>
	static void CallThreadBindUObject(FGraphEventRef& OutEventRef, const FGraphEventArray* InPreEvent, ENamedThreads::Type InDesiredThread
		, UserClass* InUserObject, typename TMemFunPtrType<true, UserClass, void(VarTypes...)>::Type InFunc, VarTypes... Vars)
	{
		CALL_THREAD_UOBJECT_FUNC(OutEventRef, InPreEvent, InDesiredThread, InUserObject, InFunc, Vars...);
	}
};
