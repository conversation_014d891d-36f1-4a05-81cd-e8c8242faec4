// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterData.h"

FParameterTableData::FParameterTableData()
	:id(TEXT("-1"))
	, name(TEXT(""))
	, description(TEXT(""))
	, classific_id(0)
	, value(TEXT(""))
	, expression(TEXT(""))
	, max_value(TEXT(""))
	, max_expression(TEXT(""))
	, min_value(TEXT(""))
	, min_expression(TEXT(""))
	, visibility(TEXT(""))
	, visibility_exp(TEXT(""))
	, editable(TEXT(""))
	, editable_exp(TEXT(""))
	, is_enum(0)
	, param_id(TEXT(""))
	, main_id(TEXT("-1"))
	, Special(TEXT("1"))
	, Special_exp(TEXT("1"))
	, Must(TEXT("1"))
	, Must_exp(TEXT("1"))
	, DefaultExpress(TEXT(""))
{
}

FEnumParameterTableData::FEnumParameterTableData()
	:id(TEXT("-1"))
	, value(TEXT(""))
	, name_for_display(TEXT(""))
	, visibility(TEXT("1"))
	, visibility_exp(TEXT("1"))
	, priority(TEXT("0"))
	, main_id(TEXT("-1"))
	, force_select_condition(TEXT(""))
{
}

bool FEnumParameterTableData::UseParameterName(const FString& InName) const
{
	return visibility_exp.Contains(InName,ESearchCase::CaseSensitive);
}



#define STR_EXPRESS_VALUE_EQUALS(Str1, Str2, RetVal) \
if (Str1.IsNumeric() && Str2.IsNumeric()) \
{ \
	const double D_Str1 = FCString::Atod(*Str1); \
	const double D_Str2 = FCString::Atod(*Str2); \
	if (FMath::IsNearlyEqual(D_Str1, D_Str2, 0.01f)) \
	{ \
		RetVal = true; \
	} \
} \
else \
{ \
	if (Str1.Equals(Str2, ESearchCase::IgnoreCase)) \
	{ \
		RetVal = true; \
	} \
}

#define STR_EXPRESS_VALUE_EQUALS_HAS_BOOL(Str1, Str2, RetVal) \
bool RetVal = false; \
STR_EXPRESS_VALUE_EQUALS(Str1, Str2, RetVal)

bool FParameterTableData::Equal_Precise(const FParameterTableData& InData) const
{
	bool EqualID = false;
	{
		STR_EXPRESS_VALUE_EQUALS(id, InData.id, EqualID);
	}

	const bool EqualName = name.Equals(InData.name,ESearchCase::CaseSensitive);
	const bool EqualDescription = description.Equals(InData.description, ESearchCase::CaseSensitive);
	const bool EqualClassificID = classific_id == InData.classific_id;

	bool EqualValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(value, InData.value, EqualValue);
	}

	bool EqualExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(expression, InData.expression, EqualExpress);
	}

	bool EqualMaxValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(max_value, InData.max_value, EqualMaxValue);
	}

	bool EqualMaxExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(max_expression, InData.max_expression, EqualMaxExpress);
	}

	bool EqualMinValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(min_value, InData.min_value, EqualMinValue);
	}

	bool EqualMinExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(min_expression, InData.min_expression, EqualMinExpress);
	}

	bool EqualVisibility = false;
	{
		STR_EXPRESS_VALUE_EQUALS(visibility, InData.visibility, EqualVisibility);
	}

	bool EqualVisibilityExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(visibility_exp, InData.visibility_exp, EqualVisibilityExpress);
	}

	bool EqualEditValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(editable, InData.editable, EqualEditValue);
	}

	bool EqualEditExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(editable_exp, InData.editable_exp, EqualEditExpress);
	}

	bool EqualSpecialValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(Special, InData.Special, EqualSpecialValue);
	}

	bool EqualSpecialExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(Special_exp, InData.Special_exp, EqualSpecialExpress);
	}

	bool EqualMustValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(Must, InData.Must, EqualMustValue);
	}

	bool EqualMustExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(Must_exp, InData.Must_exp, EqualMustExpress);
	}

	bool EqualDefaultExpress = false;
	{
        STR_EXPRESS_VALUE_EQUALS(DefaultExpress, InData.DefaultExpress, EqualDefaultExpress);
	}

	const bool EqualIsEnum = is_enum == InData.is_enum;

	const bool EqualParamID = param_id.Equals(InData.param_id);

	const bool EqualMainID = main_id.Equals(InData.main_id);

	const bool EqualNoMatchEnum = no_match_enum_data == InData.no_match_enum_data;

	const bool EqualIsGridValueCeil = grid_value_mark == InData.grid_value_mark;
	const bool EqualGridExpression = grid_expression.Equals(InData.grid_expression, ESearchCase::CaseSensitive);
	const bool EqualGridValue = grid_value.Equals(InData.grid_value, ESearchCase::CaseSensitive);

	return EqualID && EqualName && EqualDescription && EqualClassificID && EqualValue && EqualExpress
		&& EqualMaxValue && EqualMaxExpress && EqualMinValue && EqualMinExpress && EqualVisibility && EqualVisibilityExpress
		&& EqualEditValue && EqualEditExpress && EqualIsEnum && EqualParamID && EqualMainID && EqualSpecialValue 
		&& EqualSpecialExpress && EqualMustValue && EqualMustExpress && EqualDefaultExpress && EqualNoMatchEnum
		&& EqualIsGridValueCeil && EqualGridExpression && EqualGridValue;
}

bool FEnumParameterTableData::Equal_Precise(const FEnumParameterTableData& InData) const
{
	bool EqualID = false;
	{
		STR_EXPRESS_VALUE_EQUALS(id, InData.id, EqualID);
	}

	bool EqualValue = false;
	{
		STR_EXPRESS_VALUE_EQUALS(value, InData.value, EqualValue);
	}

	bool EqualExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(expression, InData.expression, EqualExpress);
	}

	const bool EqualNameForDisplay = name_for_display.Equals(InData.name_for_display,ESearchCase::CaseSensitive);
	const bool EqualImageForDisplay = image_for_display.Equals(InData.image_for_display, ESearchCase::CaseSensitive);

	bool EqualVisibility = false;
	{
		STR_EXPRESS_VALUE_EQUALS(visibility, InData.visibility, EqualVisibility);
	}

	bool EqualVisibilityExpress = false;
	{
		STR_EXPRESS_VALUE_EQUALS(visibility_exp, InData.visibility_exp, EqualVisibilityExpress);
	}

	bool EqualPriority = false;
	{
		STR_EXPRESS_VALUE_EQUALS(priority, InData.priority, EqualPriority);
	}

	const bool EqualMainID = main_id.Equals(InData.main_id);

	const bool EqualForceCondition = force_select_condition.Equals(InData.force_select_condition, ESearchCase::CaseSensitive);

	return EqualID && EqualValue && EqualExpress && EqualNameForDisplay && EqualImageForDisplay
		&& EqualVisibility && EqualVisibilityExpress && EqualPriority && EqualMainID && EqualForceCondition;
}

bool FParameterTableData::IsValid() const
{
	return !name.IsEmpty() && !id.IsEmpty();
}

bool FParameterTableData::IsValidMax(const FString& InNewMax) const
{
	if (!InNewMax.IsNumeric())
		return false;
	if (!this->min_value.IsEmpty())
	{
		float Min = FCString::Atof(*this->min_value);
		float NewMax = FCString::Atof(*InNewMax);
		return NewMax >= Min;
	}
	return true;
}

bool FParameterTableData::IsValidMin(const FString& InNewMin) const
{
	if (!InNewMin.IsNumeric())
		return false;
	if (!this->max_value.IsEmpty())
	{
		float Max = FCString::Atof(*this->max_value);
		float NewMin = FCString::Atof(*InNewMin);
		return NewMin <= Max;
	}
	return true;
}

bool FParameterTableData::EnsureDefaultExpress()
{
	STR_EXPRESS_VALUE_EQUALS_HAS_BOOL(DefaultExpress, expression, IsDefaultValid);
	if (!IsDefaultValid)
	{
		DefaultExpress = expression;
	}

    return IsDefaultValid;
}

void FParameterTableData::FormatDefaultExpress()
{
	DefaultExpress = expression;
}

bool FParameterData::IsValid() const
{
	return Data.IsValid();
}

bool FParameterData::IsValueValid(const FString& InNewValue) const
{
	if (!this->Data.is_enum)
	{
		if (this->Data.max_value.IsEmpty() && this->Data.min_value.IsEmpty() && InNewValue.Len() >= 2 && '"' == InNewValue[0] && '"' == InNewValue[InNewValue.Len() - 1])
			return true;//max and min is empty new value is a string
		if (InNewValue.IsNumeric())
		{
			float Max = this->Data.max_value.IsEmpty() ? BIG_NUMBER : FCString::Atof(*this->Data.max_value);
			float Min = this->Data.min_value.IsEmpty() ? -BIG_NUMBER : FCString::Atof(*this->Data.min_value);
			float NewValue = FCString::Atof(*InNewValue);
			if (NewValue >= Min && NewValue <= Max)
				return true;
		}
		return false;
	}
	else
	{
		for (auto& Iter : EnumData)
		{
			if (InNewValue.Equals(Iter.value))
				return true;
		}
		return false;
	}
}

bool FParameterTableData::IsValidDescription(const FString& InNewDescription)
{
	if (InNewDescription.IsEmpty()) return false;
	int32 StartIndex = INDEX_NONE;
	InNewDescription.FindChar('[', StartIndex);
	int32 EndIndex = INDEX_NONE;
	InNewDescription.FindLastChar(']', EndIndex);
	if (INDEX_NONE == StartIndex && INDEX_NONE == EndIndex) return true;//不存在方括号
	if ((INDEX_NONE != StartIndex && INDEX_NONE == EndIndex) || (INDEX_NONE == StartIndex && INDEX_NONE != EndIndex)) return false;//仅有左括号或右括号
	if (EndIndex != (InNewDescription.Len() - 1) || 0 == StartIndex) return false;//右方括号不是最后一个或者或方括号是第一个
	FString CleanDescription = InNewDescription.Mid(StartIndex + 1, EndIndex - StartIndex - 1);
	UE_LOG(LogTemp, Log, TEXT("FParameterTableData::IsValidDescription CleanDescription %s StartIndex %d EndIndex %d"), *CleanDescription, StartIndex, EndIndex);
	if (CleanDescription.IsEmpty()) return true;

	while (!CleanDescription.IsEmpty())
	{
		int32 SplitPos = INDEX_NONE;
		CleanDescription.FindChar(';', SplitPos);
		FString KeyValuePair = INDEX_NONE == SplitPos ? CleanDescription : CleanDescription.Left(SplitPos);
		{//获取键值，比较键与目标Tag是否相同
			FString Key = TEXT("");
			FString Value = TEXT("");
			KeyValuePair.Split(TEXT("="), &Key, &Value);
			Key.TrimStartAndEndInline();
			Value.TrimStartAndEndInline();
			if (Key.IsEmpty() || Value.IsEmpty()) return false;//Key或Value为空
			{//校验Key，Key只能是大小写英文字母
				FRegexPattern Pattern(TEXT("^[a-zA-Z]+$"));
				FRegexMatcher RegMatcher(Pattern, Key);
				RegMatcher.SetLimits(0, Key.Len());
				bool Res = RegMatcher.FindNext();
				UE_LOG(LogTemp, Log, TEXT("FParameterTableData::IsValidDescription Key %d"), Res);
				if (!Res) return false;
			}
			{//校验Value，Value可以是中文英文大小写字母及数字或/
				FRegexPattern Pattern(TEXT("^[\u4e00-\u9fa5_a-zA-Z0-9/]+$"));
				FRegexMatcher RegMatcher(Pattern, Value);
				RegMatcher.SetLimits(0, Value.Len());
				bool Res = RegMatcher.FindNext();
				UE_LOG(LogTemp, Log, TEXT("FParameterTableData::IsValidDescription Value %d"), Res);
				if (!Res) return false;
			}
		}
		CleanDescription = INDEX_NONE == SplitPos ? TEXT("") : CleanDescription.Right(CleanDescription.Len() - SplitPos - 1);
	}
	return true;
}

FString FParameterTableData::GetCleanDataWithoutAdditionMsg(const FString& InOriginData)
{
	int32 StartIndex = INDEX_NONE;
	InOriginData.FindChar('[', StartIndex);
	//返回的附加信息不包含[]因此减2
	return INDEX_NONE == StartIndex ? InOriginData : InOriginData.Left(StartIndex);
}

FParameterData::FParameterData()
	:Data(FParameterTableData())
{

}

void FParameterData::GetAllExpressions(TArray<FString>& Expressions) const
{
	if (false == Data.expression.IsEmpty())Expressions.Add(Data.expression);
	/*if (false == Data.max_expression.IsEmpty())Expressions.Add(Data.max_expression);
	if (false == Data.min_expression.IsEmpty())Expressions.Add(Data.min_expression);
	if (false == Data.visibility_exp.IsEmpty())Expressions.Add(Data.visibility_exp);
	if (false == Data.editable_exp.IsEmpty())Expressions.Add(Data.editable_exp);*/
	/*if (Data.is_enum && (EnumData.Num() > 0))
	{
		for (auto& EnumIter : EnumData)
		{
			if (false == EnumIter.expression.IsEmpty())Expressions.Add(EnumIter.expression);
			if (false == EnumIter.visibility_exp.IsEmpty())Expressions.Add(EnumIter.visibility_exp);
		}
	}*/
}

void FParameterData::GetAllExpressionsForEvery(TArray<FString>& Expressions) const
{
	if (false == Data.expression.IsEmpty())Expressions.Add(Data.expression);
	if (false == Data.max_expression.IsEmpty())Expressions.Add(Data.max_expression);
	if (false == Data.min_expression.IsEmpty())Expressions.Add(Data.min_expression);
	if (false == Data.visibility_exp.IsEmpty())Expressions.Add(Data.visibility_exp);
	if (false == Data.editable_exp.IsEmpty())Expressions.Add(Data.editable_exp);
	if (Data.is_enum && (EnumData.Num() > 0))
	{
		for (auto& EnumIter : EnumData)
		{
			if (false == EnumIter.expression.IsEmpty())Expressions.Add(EnumIter.expression);
			if (false == EnumIter.visibility_exp.IsEmpty())Expressions.Add(EnumIter.visibility_exp);
		}
	}
}

void FParameterData::GetAllExpressionsOnlyEnum(TArray<FString>& Expressions) const
{
	if (Data.is_enum && (EnumData.Num() > 0))
	{
		for (auto& EnumIter : EnumData)
		{
			if (false == EnumIter.expression.IsEmpty())Expressions.Add(EnumIter.expression);
			if (false == EnumIter.visibility_exp.IsEmpty())Expressions.Add(EnumIter.visibility_exp);
		}
	}
}

//用于参数自身检查极值有效性，与另一个同名函数功能相同。
bool FParameterData::IsMaxNMinValueVaild(TArray<float>& EnumArray) const
{
	EnumArray.Empty();
	FString MaxValue = this->Data.max_value;
	FString MinValue = this->Data.min_value;
	float fMaxValue = FCString::Atof(*MaxValue);
	float fMinValue = FCString::Atof(*MinValue);

	if (!MaxValue.IsEmpty() && !MinValue.IsEmpty())//两值皆存
	{
		if (FCString::Atof(*MaxValue) < FCString::Atof(*MinValue))
		{
			/*for (int32 i = 0; i < EnumData.Num(); i++)
			{
				EnumArray.Add(FCString::Atof(*EnumData[i].value));
			}*/
			return false;
		}
		else
		{
			for (int32 i = 0; i < EnumData.Num(); i++)
			{
				float CurValue = FCString::Atof(*EnumData[i].value);
				if (CurValue >= fMinValue
					&& CurValue <= fMaxValue)
				{
					EnumArray.Add(CurValue);
				}
			}
		}
		return true;
		//if (EnumArray.Num() <= 0) return false;

	}
	else if (!MaxValue.IsEmpty() && MinValue.IsEmpty())//仅最大值
	{
		for (int32 i = 0; i < EnumData.Num(); i++)
		{
			float CurValue = FCString::Atof(*EnumData[i].value);
			if (CurValue <= fMaxValue)
			{
				EnumArray.Add(CurValue);
			}
		}
		return true;
		//if (EnumArray.Num() <= 0) return false;
	}
	else if (!MinValue.IsEmpty() && MaxValue.IsEmpty())//仅最小值
	{
		for (int32 i = 0; i < EnumData.Num(); i++)
		{
			float CurValue = FCString::Atof(*EnumData[i].value);
			if (CurValue >= fMinValue)
			{
				EnumArray.Add(CurValue);
			}
		}
		return true;
		//if (EnumArray.Num() <= 0) return false;
	}
	else//无极值
	{
		for (int32 i = 0; i < EnumData.Num(); i++)
		{
			EnumArray.Add(FCString::Atof(*EnumData[i].value));
		}
		return false;
	}
}

FString FParameterData::GetCorrectParamValue() const
{
	FString OutValue = Data.value;
	FString MaxValue = Data.max_value;
	FString MinValue = Data.min_value;
	if (!MaxValue.IsEmpty() && !MinValue.IsEmpty())
	{
		if (FCString::Atof(*MaxValue) < FCString::Atof(*MinValue))
		{
			return Data.value;
		}
	}
	if (!MaxValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) < FCString::Atof(*MaxValue))
			? Data.value : MaxValue;
	}
	if (!MinValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) > FCString::Atof(*MinValue))
			? OutValue : MinValue;
	}
	return OutValue;
}

void FParameterData::ReGenerateID()
{
	Data.id = FGuid::NewGuid().ToString().ToLower();
	for (int32 i = 0; i < EnumData.Num(); ++i)
	{
		EnumData[i].id = FGuid::NewGuid().ToString().ToLower();
		EnumData[i].main_id = Data.id;
	}
}