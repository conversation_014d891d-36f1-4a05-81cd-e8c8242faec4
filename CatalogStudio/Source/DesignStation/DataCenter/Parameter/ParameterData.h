// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ParameterData.generated.h"

UENUM(BlueprintType)
enum class EParamGridMarkType : uint8
{
	E_Grid_Ceil = 0						UMETA(DisplayName = "向上取整"),
	E_Grid_Floor					UMETA(DisplayName = "向下取整")
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FParameterTableData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString id;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString name;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString description;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		int32 classific_id;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString value;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString expression;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString max_value;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString max_expression;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString min_value;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString min_expression;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString visibility;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString editable;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString editable_exp;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		int32 is_enum;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString param_id;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString main_id;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString Special;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString Special_exp;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString Must;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString Must_exp;

	//默认表达式，不开放编辑，默认与值表达式相等
    UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString DefaultExpress;

	//是否不匹配枚举值
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		bool no_match_enum_data = false;

	//
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		EParamGridMarkType grid_value_mark;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString grid_expression;
	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FString grid_value;

public:

	friend FArchive& operator<<(FArchive& Ar, struct FParameterTableData& ParameterTableDataToSave)
	{
		Ar << ParameterTableDataToSave.id;
		Ar << ParameterTableDataToSave.name;
		Ar << ParameterTableDataToSave.description;
		Ar << ParameterTableDataToSave.classific_id;
		Ar << ParameterTableDataToSave.value;
		Ar << ParameterTableDataToSave.expression;
		Ar << ParameterTableDataToSave.max_value;
		Ar << ParameterTableDataToSave.max_expression;
		Ar << ParameterTableDataToSave.min_value;
		Ar << ParameterTableDataToSave.min_expression;
		Ar << ParameterTableDataToSave.visibility;
		Ar << ParameterTableDataToSave.visibility_exp;
		Ar << ParameterTableDataToSave.editable;
		Ar << ParameterTableDataToSave.editable_exp;
		Ar << ParameterTableDataToSave.is_enum;
		Ar << ParameterTableDataToSave.param_id;
		Ar << ParameterTableDataToSave.main_id;
		Ar << ParameterTableDataToSave.Special;
		Ar << ParameterTableDataToSave.Special_exp;
		Ar << ParameterTableDataToSave.Must;
		Ar << ParameterTableDataToSave.Must_exp;
        Ar << ParameterTableDataToSave.DefaultExpress;
		Ar << ParameterTableDataToSave.no_match_enum_data;
		Ar << ParameterTableDataToSave.grid_value_mark;
		Ar << ParameterTableDataToSave.grid_expression;
		Ar << ParameterTableDataToSave.grid_value;
		return Ar;
	}

	bool operator!=(const FParameterTableData& InData) const
	{
		return id != InData.id ||
			!name.Equals(name, ESearchCase::CaseSensitive) ||
			description != InData.description ||
			classific_id != InData.classific_id ||
			value != InData.value ||
			!expression.Equals(expression, ESearchCase::CaseSensitive) ||
			max_value != InData.max_value ||
			!max_expression.Equals(max_expression, ESearchCase::CaseSensitive) ||
			min_value != InData.min_value ||
			!min_expression.Equals(min_expression, ESearchCase::CaseSensitive) ||
			visibility != InData.visibility ||
			!visibility_exp.Equals(visibility_exp, ESearchCase::CaseSensitive) ||
			editable != InData.editable ||
			!editable_exp.Equals(editable_exp, ESearchCase::CaseSensitive) ||
			is_enum != InData.is_enum ||
			param_id != InData.param_id ||
			main_id != InData.main_id ||
			Special != InData.Special ||
			!Special_exp.Equals(Special_exp, ESearchCase::CaseSensitive) ||
			Must != InData.Must ||
			!Must_exp.Equals(InData.Must_exp, ESearchCase::CaseSensitive) || !DefaultExpress.Equals(InData.DefaultExpress, ESearchCase::CaseSensitive) ||
			no_match_enum_data != InData.no_match_enum_data ||
			grid_value_mark != InData.grid_value_mark || !grid_expression.Equals(InData.grid_expression, ESearchCase::CaseSensitive) || !grid_value.Equals(InData.grid_value, ESearchCase::CaseSensitive);
	}

	bool Equal_Precise(const FParameterTableData& InData) const;

	void operator=(const FParameterTableData& InData)
	{
		id = InData.id;
		name = InData.name;
		description = InData.description;
		classific_id = InData.classific_id;
		value = InData.value;
		expression = InData.expression;
		max_value = InData.max_value;
		max_expression = InData.max_expression;
		min_value = InData.min_value;
		min_expression = InData.min_expression;
		visibility = InData.visibility;
		visibility_exp = InData.visibility_exp;
		editable = InData.editable;
		editable_exp = InData.editable_exp;
		is_enum = InData.is_enum;
		param_id = InData.param_id;
		main_id = InData.main_id;
		Special = InData.Special;
		Special_exp = InData.Special_exp;
		Must = InData.Must;
		Must_exp = InData.Must_exp;
        DefaultExpress = InData.DefaultExpress;
		no_match_enum_data = InData.no_match_enum_data;
		grid_value_mark = InData.grid_value_mark;
		grid_expression = InData.grid_expression;
		grid_value = InData.grid_value;
	}

	bool IsValid() const;

	bool IsValidMax(const FString& InNewMax) const;

	bool IsValidMin(const FString& InNewMin) const;

	bool EnsureDefaultExpress();
	void FormatDefaultExpress();

	static bool IsValidDescription(const FString& InNewDescription);

	static FString GetCleanDataWithoutAdditionMsg(const FString& InOriginData);

	void CopyData(const FParameterTableData& InData)
	{
		id = InData.id;
		name = InData.name;
		description = InData.description;
		classific_id = InData.classific_id;
		value = InData.value;
		expression = InData.expression;
		max_value = InData.max_value;
		max_expression = InData.max_expression;
		min_value = InData.min_value;
		min_expression = InData.min_expression;
		visibility = InData.visibility;
		visibility_exp = InData.visibility_exp;
		editable = InData.editable;
		editable_exp = InData.editable_exp;
		is_enum = InData.is_enum;
		param_id = InData.param_id;
		main_id = InData.main_id;
		Special = InData.Special;
		Special_exp = InData.Special_exp;
		Must = InData.Must;
		Must_exp = InData.Must_exp;
        DefaultExpress = InData.DefaultExpress;
		no_match_enum_data = InData.no_match_enum_data;
		grid_value_mark = InData.grid_value_mark;
		grid_expression = InData.grid_expression;
		grid_value = InData.grid_value;
	}

	void CopyParentData(const FParameterTableData& InData)
	{
		id = InData.id;
		//name = InData.name;
		//description = InData.description;
		classific_id = InData.classific_id;
		value = InData.value;
		expression = InData.expression;
		max_value = InData.max_value;
		max_expression = InData.max_expression;
		min_value = InData.min_value;
		min_expression = InData.min_expression;
		visibility = InData.visibility;
		visibility_exp = InData.visibility_exp;
		editable = InData.editable;
		editable_exp = InData.editable_exp;
		is_enum = InData.is_enum;
		Special = InData.Special;
		Special_exp = InData.Special_exp;
		Must = InData.Must;
		Must_exp = InData.Must_exp;
        DefaultExpress = InData.DefaultExpress;
		no_match_enum_data = InData.no_match_enum_data;
		grid_value_mark = InData.grid_value_mark;
		grid_expression = InData.grid_expression;
		grid_value = InData.grid_value;
		//param_id = InData.param_id;
		//main_id = InData.main_id;
	}

public:
	FParameterTableData();
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FEnumParameterTableData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString id;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString value;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString expression;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString name_for_display;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString image_for_display;//显示图片
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString visibility;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString priority;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString main_id;
	UPROPERTY(BlueprintReadWrite, Category = EnumParameter)
		FString force_select_condition;

public:
	FEnumParameterTableData();

public:
	friend FArchive& operator<<(FArchive& Ar, struct FEnumParameterTableData& ParameterTableDataToSave)
	{
		Ar << ParameterTableDataToSave.id;
		Ar << ParameterTableDataToSave.value;
		Ar << ParameterTableDataToSave.expression;
		Ar << ParameterTableDataToSave.name_for_display;
		Ar << ParameterTableDataToSave.image_for_display;
		Ar << ParameterTableDataToSave.visibility;
		Ar << ParameterTableDataToSave.visibility_exp;
		Ar << ParameterTableDataToSave.priority;
		Ar << ParameterTableDataToSave.main_id;
		Ar << ParameterTableDataToSave.force_select_condition;

		return Ar;
	}

	bool operator!=(const FEnumParameterTableData& InData) const
	{
		return id != InData.id ||
			value != InData.value ||
			expression != InData.expression;
	}

	bool Equal_Precise(const FEnumParameterTableData& InData) const;

	void operator=(const FEnumParameterTableData& InData)
	{
		id = InData.id;
		name_for_display = InData.name_for_display;
		value = InData.value;
		priority = InData.priority;
		main_id = InData.main_id;
		visibility = InData.visibility;
		visibility_exp = InData.visibility_exp;
		expression = InData.expression;
		image_for_display = InData.image_for_display;
		force_select_condition = InData.force_select_condition;
	}

	void CopyData(const FEnumParameterTableData& InData)
	{
		id = InData.id;
		value = InData.value;
		name_for_display = InData.name_for_display;
		visibility = InData.visibility;
		priority = InData.priority;
		main_id = InData.main_id;
		visibility_exp = InData.visibility_exp;
		expression = InData.expression;
		image_for_display = InData.image_for_display;
		force_select_condition = InData.force_select_condition;
	}

	bool operator == (const FEnumParameterTableData& InData) const
	{
		if (/*name_for_display == InData.name_for_display && */id == InData.id)
		{
			return true;
		}
		return false;
	}

	bool CompareData(const FEnumParameterTableData& InData)
	{
		return value == InData.value && name_for_display == InData.name_for_display &&
			visibility == InData.visibility && priority == InData.priority && main_id == InData.main_id &&
			visibility_exp.Equals(InData.visibility_exp, ESearchCase::CaseSensitive) && 
			expression.Equals(InData.expression,ESearchCase::CaseSensitive) && image_for_display == InData.image_for_display;
	}

	bool UseParameterName(const FString& InName) const;

	bool IsVisiable() const
	{
		if (!visibility.IsEmpty())
		{
			float Visi = FCString::Atof(*visibility);
			return !FMath::IsNearlyZero(Visi, 0.01);
		}
		return true;
	}

};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FParameterData
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		FParameterTableData Data;

	UPROPERTY(BlueprintReadWrite, Category = Parameter)
		TArray<FEnumParameterTableData> EnumData;

	bool IsValid() const;

public:

	FParameterData();

	friend FArchive& operator<<(FArchive& Ar, struct FParameterData& ParameterDataToSave)
	{
		Ar << ParameterDataToSave.Data;
		Ar << ParameterDataToSave.EnumData;
		return Ar;
	}

	bool operator!=(const FParameterData& InData) const
	{
		if (Data != InData.Data)
			return true;
		if (Data.is_enum)
		{
			if (EnumData.Num() != InData.EnumData.Num())
				return true;
			int32 i = 0;
			while (i < EnumData.Num())
			{
				if (EnumData[i] != InData.EnumData[i])
					return true;
				++i;
			}
		}
		return false;
	}

	bool Equal_Precise(const FParameterData& InData) const
	{
		if (!Data.Equal_Precise(InData.Data))
		{
			return false;
		}
		if (Data.is_enum)
		{
			if (EnumData.Num() != InData.EnumData.Num())
				return false;
			
			for (int32 i = 0; i < EnumData.Num(); ++i)
			{
				if (!EnumData[i].Equal_Precise(InData.EnumData[i]))
					return false;
			}
		}
		return true;
	}

	bool operator ==(const FParameterData& InData) const
	{
		return Data.id.Equals(InData.Data.id);
	}

	void operator=(const FParameterData& InData)
	{
		Data = InData.Data;
		if (Data.is_enum)
		{
			EnumData = InData.EnumData;
		}
	}

	bool IsValueValid(const FString& InNewValue) const;

	void CopyData(const FParameterData& InData)
	{
		Data.CopyData(InData.Data);
		if (Data.is_enum)
		{
			EnumData = InData.EnumData;
		}
		else if (EnumData.Num() > 0)
		{
			EnumData.Empty();
		}
		/*EnumData.Empty();
		for (auto& DataTemp : InData.EnumData)
		{
			EnumData.Add(DataTemp);
		}*/
	}

	void CopyParentData(const FParameterData& InData)
	{
		Data.CopyParentData(InData.Data);
		if (Data.is_enum)
		{
			EnumData = InData.EnumData;
		}
		else
		{
			EnumData.Empty();
		}
	}

	int32 FindMaxID()
	{
		int32 MaxNum = -1;
		for (auto iter : EnumData)
		{
			if (MaxNum < FCString::Atoi(*iter.priority))
			{
				MaxNum = FCString::Atoi(*iter.priority);
			}
		}
		return MaxNum;
	}

	void GetAllExpressions(TArray<FString>& Expressions) const;

	void GetAllExpressionsForEvery(TArray<FString>& Expressions) const;

	void GetAllExpressionsOnlyEnum(TArray<FString>& Expressions) const;

	bool IsMaxNMinValueVaild(TArray<float>& EnumArray) const;

	FString GetCorrectParamValue() const;

	//重新生成变量的ID与枚举的ID
	void ReGenerateID();

};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FParameterGroupTableData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = GroupParameter)
		int32 id = -1;
	UPROPERTY(BlueprintReadWrite, Category = GroupParameter)
		FString group_name;
	UPROPERTY(BlueprintReadWrite, Category = GroupParameter)
		FString description;
};