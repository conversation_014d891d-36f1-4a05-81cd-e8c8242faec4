// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterEffectionParser.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

struct FParameterAffectNode
{
	FString ParameterName;
	TArray<TSharedPtr<struct FParameterAffectNode>> Children;

	bool ConstructTreeFromNode(const TMap<FString, TArray<FString>>& InItems, const FString& ParentNode)
	{
		TArray<FString> CheckNodes;
		bool Res = ConstructTreeFromNode(InItems, ParentNode, CheckNodes);
		return Res;
	}

	bool ConstructTreeFromNodeLevel(const TMap<FString, TArray<FString>>& InItems, const FString& ParentNode, TArray<FString>& CheckNodes)
	{
		CheckNodes.Empty();
		bool Res = ConstructTreeFromNode(InItems, ParentNode, CheckNodes);
		return Res;
	}

	void CollectAllUniqueLeaves(TArray<FString>& Leaves)
	{
		if (ParameterName.IsEmpty()) return;

		for (int32 i = Children.Num() - 1; i >= 0; --i)
		{
			bool bLeaf = Children[i]->IsLeafNode();
			if (bLeaf)
			{
				bool bExist = false;
				if (!Children[i]->ParameterName.IsEmpty())
				{
					for (const auto & PN : Leaves)
					{
						if (PN.Equals(Children[i]->ParameterName,ESearchCase::CaseSensitive))
						{
							bExist = true;
							break;
						}
					}
				}

				if (!bExist)
				{
					Leaves.Add(Children[i]->ParameterName);
				}


				Children.RemoveAt(i);
			}
			else
			{
				Children[i]->CollectAllUniqueLeaves(Leaves);
			}
		}
	}

private:

	bool IsLeafNode() const
	{
		for (auto Child : Children)
		{
			if (Child->ParameterName.IsEmpty()) continue;
			return false;
		}
		return true;
	}

	bool ConstructTreeFromNode(const TMap<FString, TArray<FString>>& InItems, const FString& ParentNode, TArray<FString>& ParentNodes)
	{
		if (ParentNodes.ContainsByPredicate([&](const FString& InName) {return ParentNode.Equals(InName, ESearchCase::CaseSensitive); })) return false;
		ParameterName = ParentNode;
		ParentNodes.Add(ParentNode);
		for (auto& Item : InItems)
		{
			const int32 Index = Item.Value.IndexOfByPredicate([ParentNode](const FString& InOther) { return InOther.Equals(ParentNode, ESearchCase::CaseSensitive); });
			if (INDEX_NONE == Index) continue;//当前变量没有引用ParentNode
			TSharedPtr<FParameterAffectNode> NewChild = MakeShared<FParameterAffectNode>();
			bool Res = NewChild->ConstructTreeFromNode(InItems, Item.Key, ParentNodes);
			if (false == Res) return false;
			Children.Add(NewChild);
			ParentNodes.Pop();
		}
		return true;
	}
};

bool FParameterEffectionParser::FindParametersAffectBySpecificParameter(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf/* = true*/)
{
	TMap<FString, TArray<FString>> RefrenceMap;
	bool Res = ParseRefrenceMap(AllParameters, RefrenceMap, IgnoreSelf);
	if (!Res) return false;
	TSharedPtr<FParameterAffectNode> TreeRoot = MakeShared<FParameterAffectNode>();
	Res = TreeRoot->ConstructTreeFromNode(RefrenceMap, ChangedParameter);
	if (Res)
	{
		while (TreeRoot->Children.Num() > 0) TreeRoot->CollectAllUniqueLeaves(ParametersAffected);
	}
	return Res;
}

bool FParameterEffectionParser::FindParametersAffectBySpecificParameterWithAllEXP(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf)
{
	TMap<FString, TArray<FString>> RefrenceMap;
	bool Res = ParseRefrenceMapWithAllExp(AllParameters, RefrenceMap, IgnoreSelf);
	if (!Res) return false;
	TSharedPtr<FParameterAffectNode> TreeRoot = MakeShared<FParameterAffectNode>();
	Res = TreeRoot->ConstructTreeFromNode(RefrenceMap, ChangedParameter);
	if (Res)
	{
		while (TreeRoot->Children.Num() > 0) TreeRoot->CollectAllUniqueLeaves(ParametersAffected);
	}
	return Res;
}

bool FParameterEffectionParser::FindParametersCircleRef(const TArray<FParameterData>& AllParameters, const FString& ChangedParameter, TArray<FString>& CheckNodes, bool IgnoreSelf)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParameterMap;
	for (auto& Parameter : AllParameters)
		ParameterMap.Add(Parameter.Data.name, Parameter);
	TMap<FString, TArray<FString>> RefrenceMap;
	bool Res = ParseRefrenceMap(ParameterMap, RefrenceMap, IgnoreSelf);
	if (!Res) return false;
	TSharedPtr<FParameterAffectNode> TreeRoot = MakeShared<FParameterAffectNode>();
	CheckNodes.Empty();
	Res = TreeRoot->ConstructTreeFromNodeLevel(RefrenceMap, ChangedParameter, CheckNodes);
	
	return Res;
}

bool FParameterEffectionParser::FindParametersAffectBySpecificParameter(const TArray<FParameterData>& AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf/* = true*/)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParameterMap;
	for (auto& Parameter : AllParameters)
		ParameterMap.Add(Parameter.Data.name, Parameter);
	bool Res = FindParametersAffectBySpecificParameter(ParameterMap, ChangedParameter, ParametersAffected, IgnoreSelf);
	return Res;
}

bool FParameterEffectionParser::ParseRefrenceMap(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, TMap<FString, TArray<FString>>& RefrenceMap, bool IgnoreSelf)
{
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (AllParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			for (auto& ParameterPair : AllParameters)
			{
				auto& Parameter = ParameterPair.Value;
				TArray<FString> AllExpressions;
				Parameter.GetAllExpressions(AllExpressions);
				TArray<FString> AllRefParameters;
				for (auto& ExpressionIter : AllExpressions)
				{
					TArray<TPair<int32, FString>> Comments;
					const FString CleanExp = GrammerAnalysis->RemoveComment(ExpressionIter, Comments);
					if (CleanExp.IsEmpty() || CleanExp.IsNumeric())
					{//变量的表达式为空或数字不需要解析引用
						continue;
					}
					TArray<FString> RefParameters;
					FString Message(TEXT(""));
					bool Res = GrammerAnalysis->GetParametersInExpression(CleanExp, RefParameters, Message);

					if (!Res || (RefParameters.Num() <= 0))
					{//解析引用变量错误，此种情况不应当发生
						UE_LOG(LogTemp, Log, TEXT("FParameterEffectionParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *ExpressionIter);
						continue;
					}
					if (IgnoreSelf)
					{
						RefParameters.RemoveAll([&](const FString& InName) {return Parameter.Data.name.Equals(InName, ESearchCase::CaseSensitive); });
					}
					else
					{
						const int32 SelfIndex = RefParameters.IndexOfByPredicate([Parameter](const FString& InOther) { return InOther.Equals(Parameter.Data.name, ESearchCase::CaseSensitive); });
						if (INDEX_NONE != SelfIndex)
						{
							for (size_t i = 0; i < RefParameters.Num(); i++)
							{
								UE_LOG(LogTemp, Error, TEXT("%s"), *RefParameters[i]);
							}
							return false;//不能引用自身的情况下引用了自身，返回错误
						}
					}
					if (RefParameters.Num() <= 0) continue;
					for (auto iter : RefParameters)
					{
						AllRefParameters.AddUnique(iter);
					}

				}
				if (AllRefParameters.Num() > 0)RefrenceMap.Add(Parameter.Data.name, AllRefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterEffectionParser::ParseRefrenceMapWithAllExp(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, TMap<FString, TArray<FString>>& RefrenceMap, bool IgnoreSelf)
{
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (AllParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			for (auto& ParameterPair : AllParameters)
			{
				auto& Parameter = ParameterPair.Value;
				TArray<FString> AllExpressions;
				Parameter.GetAllExpressionsForEvery(AllExpressions);
				TArray<FString> AllRefParameters;
				for (auto& ExpressionIter : AllExpressions)
				{
					TArray<TPair<int32, FString>> Comments;
					const FString CleanExp = GrammerAnalysis->RemoveComment(ExpressionIter, Comments);
					if (CleanExp.IsEmpty() || CleanExp.IsNumeric())
					{//变量的表达式为空或数字不需要解析引用
						continue;
					}
					TArray<FString> RefParameters;
					FString Message(TEXT(""));
					bool Res = GrammerAnalysis->GetParametersInExpression(CleanExp, RefParameters, Message);

					if (!Res || (RefParameters.Num() <= 0))
					{//解析引用变量错误，此种情况不应当发生
						UE_LOG(LogTemp, Log, TEXT("FParameterEffectionParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *ExpressionIter);
						continue;
					}
					if (IgnoreSelf)
					{
						RefParameters.Remove(Parameter.Data.name);
					}
					else
					{
						const int32 SelfIndex = RefParameters.IndexOfByPredicate([Parameter](const FString& InOther) { return InOther.Equals(Parameter.Data.name, ESearchCase::IgnoreCase); });
						if (INDEX_NONE != SelfIndex)
						{
							for (size_t i = 0; i < RefParameters.Num(); i++)
							{
								UE_LOG(LogTemp, Error, TEXT("%s"), *RefParameters[i]);
							}
							return false;//不能引用自身的情况下引用了自身，返回错误
						}
					}
					if (RefParameters.Num() <= 0) continue;
					for (auto iter : RefParameters)
					{
						AllRefParameters.AddUnique(iter);
					}

				}
				if (AllRefParameters.Num() > 0)RefrenceMap.Add(Parameter.Data.name, AllRefParameters);
			}
			return true;
		}
	}
	return false;
}
