// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ParameterData.h"
#include "UObject/NoExportTypes.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"

//解析被某个变量影响的变量
struct DESIGNSTATION_API FParameterEffectionParser
{
private:

	TArray<FParameterData> PotentialParameters;

public:

	bool FindParametersAffectBySpecificParameter(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf = true);

	bool FindParametersAffectBySpecificParameterWithAllEXP(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf = true);

	bool FindParametersCircleRef(const TArray<FParameterData>& AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf = true);

	bool FindParametersAffectBySpecificParameter(const TArray<FParameterData>& AllParameters, const FString& ChangedParameter, TArray<FString>& ParametersAffected, bool IgnoreSelf = true);

private:

	bool ParseRefrenceMap(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, TMap<FString, TArray<FString>>& RefrenceMap, bool IgnoreSelf);

	bool ParseRefrenceMapWithAllExp(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & AllParameters, TMap<FString, TArray<FString>>& RefrenceMap, bool IgnoreSelf);
};
