// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterRefrenceParser.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "DataCenter/Parameter/ParameterEffectionParser.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

struct FParameterRefNode : TSharedFromThis<FParameterRefNode>
{
	FString ParameterName;
	TWeakPtr<struct FParameterRefNode> Parent;
	TArray<TSharedPtr<struct FParameterRefNode>> Children;

	TSharedPtr<struct FParameterRefNode> FindRootNode()
	{
		if (Parent.IsValid())
		{
			return Parent.Pin()->FindRootNode();
		}
		return SharedThis(this);
	}

	void ConstructTreeFromArray(const TMap<FString, TArray<FString>>& InItems, const TArray<FString>& ChildrenItems)
	{
		TWeakPtr<FParameterRefNode> pThis = SharedThis(this);
		for (auto Child : ChildrenItems)
		{
			bool bParent = IsParentNode(Child);
			if (bParent) continue;//此节点是某个父节点，存在循环引用，跳过此节点以打破循环引用
			TSharedPtr<struct FParameterRefNode> ChildNode = MakeShared<FParameterRefNode>();
			ChildNode->Parent = pThis;
			ChildNode->ParameterName = Child;
			this->Children.Add(ChildNode);
			if (InItems.Contains(Child))
			{
				ChildNode->ConstructTreeFromArray(InItems, InItems[Child]);
			}
		}
	}

	void ShaveTree()
	{
		TMap<FString, TPair<TSharedPtr<FParameterRefNode>, int32>> EmptyNodeDepth;
		ShaveTree(0, EmptyNodeDepth);
	}

	//后序遍历输出树的节点
	void PostorderTraversal(TArray<FString>& NodeItems) const
	{
		if (ParameterName.IsEmpty()) return;
		for (auto& Child : Children)
		{
			if (Child->ParameterName.IsEmpty()) continue;
			Child->PostorderTraversal(NodeItems);
		}
		NodeItems.Add(ParameterName);
	}

private:

	void EmptySubtree()
	{
		ParameterName.Empty();
		for (auto Child : Children) Child->EmptySubtree(); 
	}

	bool IsParentNode(const FString& InNode) const
	{
		bool Res = false;
		if (Parent.IsValid())
		{
			Res = Parent.Pin()->ParameterName.Equals(InNode,ESearchCase::CaseSensitive);
			if (Res) return true;
			Res = Parent.Pin()->IsParentNode(InNode);
		}
		return Res;
	}

	void ShaveTree(const int32& InDepth, TMap<FString, TPair<TSharedPtr<FParameterRefNode>, int32>>& NodeDepth)
	{
		const int32 NextDepth = InDepth + 1;
		for (int32 i = Children.Num() - 1; i >= 0; --i)
		{
			if (NodeDepth.Contains(Children[i]->ParameterName))
			{//防止删除导致引用逻辑错误，改为清空数据
				if (NodeDepth[Children[i]->ParameterName].Value >= NextDepth)
				{//当前子节点需要被删除
					Children[i]->ParameterName.Empty();
					Children[i]->EmptySubtree();
					continue;
				}
				else
				{//删除之前的节点
					NodeDepth[Children[i]->ParameterName].Key->ParameterName.Empty();
					NodeDepth[Children[i]->ParameterName].Key = Children[i];
					NodeDepth[Children[i]->ParameterName].Value = NextDepth;
				}
			}
			else
			{
				TPair<TSharedPtr<FParameterRefNode>, int32> NodeDepthItem(Children[i], NextDepth);
				NodeDepth.Add(Children[i]->ParameterName, NodeDepthItem);
			}
			Children[i]->ShaveTree(NextDepth, NodeDepth);
		}
	}
};

bool FParameterRefrenceParser::SortParameterByRefrence(const TArray<FParameterData>& InParameters)
{
	OriginalParameters = InParameters;
	TMap<FString, TArray<FString>> RefrenceMap;
	bool Res = ParseRefrenceMap(RefrenceMap);
	if (!Res)
	{
		SortedParameters.Empty();
		return false;
	}
	if (RefrenceMap.Num() <= 0)
	{
		SortedParameters = InParameters;
		return true;
	}
	TArray<FString> RootNodes;
	Res = ParseRootNodes(RefrenceMap, RootNodes);
	if (!Res)
	{
		SortedParameters.Empty();
		return false;
	}
	for (const auto& RootNodeIter : RootNodes)
	{
		TArray<FString> SortedNodes;
		Res = ParsePostorderRefrenceNode(RefrenceMap, RootNodeIter, SortedNodes);
		if (!Res)
		{
			SortedParameters.Empty();
			return false;
		}
		for (auto Node : SortedNodes)
		{
			auto FindNodeHandler = [Node](const FParameterData& InOther) { return InOther.Data.name.Equals(Node, ESearchCase::CaseSensitive); };
			int32 Index = SortedParameters.IndexOfByPredicate(FindNodeHandler);
			if (INDEX_NONE == Index && SortedParameters.IsValidIndex(Offset))
			{
				Index = OriginalParameters.IndexOfByPredicate(FindNodeHandler);
				if (INDEX_NONE == Index) continue;//此节点没有在同级出现，是上一级的变量，跳过。
				//{//此种情况不应该出现
				//	UE_LOG(LogTemp, Error, TEXT("FParameterRefrenceParser::SortParameterByRefrence parameter %s not found"), *Node);
				//	SortedParameters.Empty();
				//	return false;
				//}
				SortedParameters[Offset++] = OriginalParameters[Index];
			}
		}
	}
	if (SortedParameters.Num() > Offset) SortedParameters.RemoveAt(Offset, SortedParameters.Num() - Offset);
	return true;
}

bool FParameterRefrenceParser::SortParameterByParamRefrence(const TArray<FParameterData>& InParameters)
{
	OriginalParameters = InParameters;
	TMap<FString, TArray<FString>> RefrenceMap;
	bool Res = ParseRefrenceMap(RefrenceMap);
	if (!Res)
	{
		SortedParameters.Empty();
		return false;
	}
	if (RefrenceMap.Num() <= 0)
	{
		SortedParameters = InParameters;
		return true;
	}

	//按引用参数多少排序
	RefrenceMap.ValueSort(
		[](const TArray<FString>& Array1, const TArray<FString>& Array2)->bool
	{
		return Array1.Num() <= Array2.Num();
	}
	);

	TArray<FString> KeyArray;
	RefrenceMap.GenerateKeyArray(KeyArray);
	//冒泡法将多层引用放入数组后面
	int32 ConsiderNum = KeyArray.Num();
	for (int32 i = 0; i < ConsiderNum; ++i)
	{
		for (int32 j = 0; j < (ConsiderNum - i); ++j)
		{
			TArray<FString> CurRef = RefrenceMap[KeyArray[j]];
			int32 MaxRefIndex = INDEX_NONE;
			for (int32 K = 0; K < CurRef.Num(); ++K)
			{
				FString CurName = CurRef[K];
				int32 CurRefIndex = KeyArray.IndexOfByPredicate(
					[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::CaseSensitive); }
				);
				if (MaxRefIndex < CurRefIndex)
				{
					MaxRefIndex = CurRefIndex;
				}
			}
			if (MaxRefIndex != INDEX_NONE)
			{//交换位置
				FString Temp = KeyArray[j];
				KeyArray[j] = KeyArray[MaxRefIndex];
				KeyArray[MaxRefIndex] = Temp;
			}
		}
	}

	for (auto& PA : KeyArray)
	{
		auto FindNodeHandler = [PA](const FParameterData& InOther) { return InOther.Data.name.Equals(PA, ESearchCase::CaseSensitive); };
		int32 Index = SortedParameters.IndexOfByPredicate(FindNodeHandler);
		if (INDEX_NONE == Index && SortedParameters.IsValidIndex(Offset))
		{
			Index = OriginalParameters.IndexOfByPredicate(FindNodeHandler);
			if (INDEX_NONE == Index) continue;
			SortedParameters[Offset++] = OriginalParameters[Index];
		}
	}
	if (SortedParameters.Num() > Offset) SortedParameters.RemoveAt(Offset, SortedParameters.Num() - Offset);
	return true;
}

bool FParameterRefrenceParser::SortParameterByParamRefrence(const TArray<FParameterData>& InParameters, TArray<FParameterData>& ConstantExp, TArray<FParameterData>& SameLevel, TArray<FParameterData>& SameLevelCir)
{
	ConstantExp.Empty();
	SameLevel.Empty();
	SameLevelCir.Empty();
	OriginalParameters = InParameters;//CurrentLevel
	TMap<FString, TArray<FString>> RefrenceMap;//GetExpWithRef
	bool Res = ParseRefrenceMap(RefrenceMap);
	if (!Res)
	{//完全不存在引用关系
		SortedParameters.Empty();
		return false;
	}
	TArray<FParameterData> NoneRefrenceParmeter;//GetNoneRefrenceParmeter
	TMap<FString, TArray<FString>> ValueOnlyRefrenceMap;//GetValueExpWithRef
	TMap<FString, TArray<FString>> OtherOnlyRefrenceMap;//GetOtherExpWithRef
	TMap<FString, TArray<FString>> BothRefrenceMap;//GeBothtExpWithRef
	TMap<FString, TArray<FString>> BothOtherRefrenceMap;//GeBothtExpWithRef
	Res = ParseParmeterRefrenceMap(NoneRefrenceParmeter, ValueOnlyRefrenceMap, OtherOnlyRefrenceMap, BothRefrenceMap, BothOtherRefrenceMap);
	ConstantExp = NoneRefrenceParmeter;

	TArray<FParameterData> TempSameLevel; 
	TArray<FParameterData> TempSameLevelCir;
	DetermineRefEitherCircleOrNot(OriginalParameters, ValueOnlyRefrenceMap, TempSameLevel, TempSameLevelCir);
	SameLevel.Append(TempSameLevel);
	SameLevelCir.Append(TempSameLevelCir);

	DetermineRefEitherCircleOrNot(OriginalParameters, OtherOnlyRefrenceMap, TempSameLevel, TempSameLevelCir);
 
	DetermineRefEitherCircleOrNot(OriginalParameters, BothRefrenceMap, TempSameLevel, TempSameLevelCir);

	//bool Res = ParseRefrenceMap(RefrenceMap);
	//if (!Res)
	//{
	//	SortedParameters.Empty();
	//	return false;
	//}
	//if (RefrenceMap.Num() <= 0)
	//{
	//	SortedParameters = InParameters;
	//	return true;
	//}
	//else
	//{
	//	for (const auto& iter : OriginalParameters)
	//	{
	//		if (RefrenceMap.Contains(iter.Data.name)) continue;
	//		ConstantExp.Add(iter);//SaveAllConstantParameter
	//	}
	//}
	////判断是否成环
	//TArray<FString> Circle = TArray<FString>();
	//for (auto& Iter : RefrenceMap)
	//{
	//	if (Circle.Contains(Iter.Key))
	//		continue;

	//	TArray<FString> RefPath;
	//	bool Res = IsCircleRef(Iter.Key, RefrenceMap, RefPath);
	//	if (Res)
	//	{
	//		for (auto& PathP : RefPath)
	//		{
	//			Circle.AddUnique(PathP);
	//		}
	//	}
	//}

	//for (const auto& iter : Circle)
	//{
	//	for (const auto& RefIter : RefrenceMap)
	//	{
	//		if (RefIter.Key.Equals(iter, ESearchCase::IgnoreCase))
	//		{
	//			int32 CurRefIndex = OriginalParameters.IndexOfByPredicate(
	//				[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::IgnoreCase); }
	//			);
	//			SameLevelCir.AddUnique(OriginalParameters[CurRefIndex]);
	//			RefrenceMap.Remove(iter);
	//			break;
	//		}
	//	}

	//	/*int32 CurRefIndex = RefrenceMap.IndexOfByPredicate(
	//		[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::IgnoreCase); }
	//	);
	//	if (CurRefIndex != INDEX_NONE)
	//	{
	//		SameLevelCir.Add(RefrenceMap[CurRefIndex]);
	//		RefrenceMap.Remove(iter);
	//	}*/
	//}
	//

	////SortByNumOfRef
	//RefrenceMap.ValueSort(
	//	[](const TArray<FString>& Array1, const TArray<FString>& Array2)->bool
	//{
	//	return Array1.Num() <= Array2.Num();
	//}
	//);

	//TArray<FString> KeyArray;
	//RefrenceMap.GenerateKeyArray(KeyArray);
	//
	////冒泡法将多层引用放入数组后面
	//int32 ConsiderNum = KeyArray.Num();
	//for (int32 i = 0; i < ConsiderNum; ++i)
	//{
	//	for (int32 j = 0; j < (ConsiderNum - i); ++j)
	//	{
	//		TArray<FString> CurRef = RefrenceMap[KeyArray[j]];
	//		int32 MaxRefIndex = INDEX_NONE;
	//		for (int32 K = 0; K < CurRef.Num(); ++K)
	//		{
	//			FString CurName = CurRef[K];
	//			int32 CurRefIndex = KeyArray.IndexOfByPredicate(
	//				[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::IgnoreCase); }
	//			);
	//			if (MaxRefIndex < CurRefIndex)
	//			{
	//				MaxRefIndex = CurRefIndex;
	//			}
	//		}
	//		if (MaxRefIndex != INDEX_NONE)
	//		{//交换位置
	//			FString Temp = KeyArray[j];
	//			KeyArray[j] = KeyArray[MaxRefIndex];
	//			KeyArray[MaxRefIndex] = Temp;
	//		}
	//	}
	//}

	//for (const auto& iter : KeyArray)
	//{
	//	int32 CurRefIndex = OriginalParameters.IndexOfByPredicate(
	//		[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::IgnoreCase); }
	//	);
	//	if (CurRefIndex != INDEX_NONE)
	//	{
	//		SameLevel.Add(OriginalParameters[CurRefIndex]);
	//	}
	//}
	
	
	return true;
}

bool FParameterRefrenceParser::SortParameterByParamRefrence(const TArray<FParameterData>& InParameters, TArray<FParameterData>& ConstantExp, TMap<FString, TArray<FString>>& ValueOnlyMap, TMap<FString, TArray<FString>>& OtherOnlyMap, TMap<FString, TArray<FString>>&BothValueMap, TMap<FString, TArray<FString>>& BothOtherMap)
{
	ConstantExp.Empty();
	ValueOnlyMap.Empty();
	OtherOnlyMap.Empty();
	BothValueMap.Empty();
	BothOtherMap.Empty();
	OriginalParameters = InParameters;//CurrentLevel
	TMap<FString, TArray<FString>> RefrenceMap;//GetExpWithRef
	bool Res = ParseRefrenceMap(RefrenceMap);
	if (!Res || RefrenceMap.Num() <= 0)
	{//完全不存在引用关系
		ConstantExp = InParameters;
		SortedParameters.Empty();
		return false;
	}
	/*if (RefrenceMap.Num() <= 0)
	{
		ConstantExp = InParameters;
		SortedParameters.Empty();
		return false;
	}*/
	TArray<FParameterData> NoneRefrenceParmeter;//GetNoneRefrenceParmeter
	TMap<FString, TArray<FString>> ValueOnlyRefrenceMap;//GetValueExpWithRef
	TMap<FString, TArray<FString>> OtherOnlyRefrenceMap;//GetOtherExpWithRef
	TMap<FString, TArray<FString>> BothValueRefrenceMap;//GeBothtExpWithRef
	TMap<FString, TArray<FString>> BothOtherRefrenceMap;//GeBothtExpWithRef
	Res = ParseParmeterRefrenceMap(NoneRefrenceParmeter, ValueOnlyRefrenceMap, OtherOnlyRefrenceMap, BothValueRefrenceMap, BothOtherRefrenceMap);
	ConstantExp = NoneRefrenceParmeter;
	ValueOnlyMap = ValueOnlyRefrenceMap;
	OtherOnlyMap = OtherOnlyRefrenceMap;
	BothValueMap = BothValueRefrenceMap;
	BothOtherMap = BothOtherRefrenceMap;
	return true;
}

bool FParameterRefrenceParser::ParseRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap)
{
	SortedParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (OriginalParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			SortedParameters.Init(FParameterData(), OriginalParameters.Num());
			Offset = 0;

			for (auto& Parameter : OriginalParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = GrammerAnalysis->GetParametersInExpression(Parameter.Data.expression, RefParameters, Message);

				auto GetRefFunc = [GrammerAnalysis](const FString& InExpression, TArray<FString>& OutRef, FString& OutMsg, bool& OutRes)->void
				{
					TArray<FString> Cur_Ref;
					bool Res_Cur = GrammerAnalysis->GetParametersInExpression(InExpression, Cur_Ref, OutMsg);
					for (auto& Cur : Cur_Ref)
					{
						OutRef.AddUnique(Cur);
					}
					OutRes = Res_Cur || OutRes;
				};

				//对所有可能的表达式进行判断
				if (!Parameter.Data.max_expression.IsEmpty() && !Parameter.Data.max_expression.IsNumeric())
				{//最大值
					GetRefFunc(Parameter.Data.max_expression, RefParameters, Message, Res);
				}
				if (!Parameter.Data.min_expression.IsEmpty() && !Parameter.Data.min_expression.IsNumeric())
				{//最小值
					GetRefFunc(Parameter.Data.min_expression, RefParameters, Message, Res);
				}//CATALOG-1812 可见可编不参与循环判断
				//if (!Parameter.Data.visibility_exp.IsEmpty() && !Parameter.Data.visibility_exp.IsNumeric())
				//{//可见性
				//	GetRefFunc(Parameter.Data.visibility_exp, RefParameters, Message, Res);
				//}
				//if (!Parameter.Data.editable_exp.IsEmpty() && !Parameter.Data.editable_exp.IsNumeric())
				//{//可编辑性
				//	GetRefFunc(Parameter.Data.editable_exp, RefParameters, Message, Res);
				//}
				//if (Parameter.EnumData.Num() > 0)
				//{//枚举
				//	for (auto& Enum : Parameter.EnumData)
				//	{
				//		if (!Enum.visibility_exp.IsEmpty() && !Enum.visibility_exp.IsNumeric())
				//		{
				//			GetRefFunc(Enum.visibility_exp, RefParameters, Message, Res);
				//		}
				//	}
				//}

				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					SortedParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					SortedParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					SortedParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = OriginalParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					SortedParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::ParseRootNodes(const TMap<FString, TArray<FString>>& RefrenceMap, TArray<FString>& RootNodes)
{
	for (auto& First : RefrenceMap)
	{
		bool bRoot = true;
		for (auto& Second : RefrenceMap)
		{
			if (First.Key.Equals(Second.Key)) continue;
			const int32 Index = Second.Value.IndexOfByPredicate([First](const FString& InOther) { return InOther.Equals(First.Key); });
			if (INDEX_NONE != Index)
			{
				bRoot = false;
				break;
			}
		}
		if (bRoot) RootNodes.Add(First.Key);
	}
	return RootNodes.Num() > 0;
}

bool FParameterRefrenceParser::ParsePostorderRefrenceNode(const TMap<FString, TArray<FString>>& RefrenceMap, const FString& InRootNode, TArray<FString>& PostorderRefrence)
{
	TSharedPtr<FParameterRefNode> RefrenceTree = MakeShared<FParameterRefNode>();
	RefrenceTree->ParameterName = InRootNode;
	RefrenceTree->ConstructTreeFromArray(RefrenceMap, RefrenceMap[InRootNode]);
	RefrenceTree = RefrenceTree->FindRootNode();
	RefrenceTree->ShaveTree();
	RefrenceTree->PostorderTraversal(PostorderRefrence);
	return PostorderRefrence.Num() > 0;
}

bool FParameterRefrenceParser::IsCircleRef(FString Origin, FString Name, TMap<FString, TArray<FString>> AllRef, TArray<FString>& RefName)
{
	if (RefName.Contains(Origin))
	{
		return true;
	}
	/*if (RefName.Num() > 1 && Origin.Equals(Name))
	{
		return true;
	}*/
	if (AllRef.Contains(Name))
	{
		if (AllRef[Name].Contains(Origin))
		{
			RefName.Add(Name);
			return true;
		}
		if (RefName.Contains(Name))
		{
			return false;
		}
		for (auto& ResP : AllRef[Name])
		{
			if(!Name.Equals(Origin,ESearchCase::CaseSensitive))
				RefName.AddUnique(Name);
			bool Res = IsCircleRef(Origin, ResP, AllRef, RefName);
			if (Res)
			{
				RefName.Insert(Origin, 0);
				return true;
			}
			else
			{
				RefName.Remove(Name);
			}
		}
	}
	else
	{
		RefName.Remove(Name);
	}

	return false;
}

bool FParameterRefrenceParser::ParseValueExpRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters)
{
	NoneRefParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (OriginalParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			NoneRefParameters.Init(FParameterData(), OriginalParameters.Num());
			Offset = 0;

			for (auto& Parameter : OriginalParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = GrammerAnalysis->GetParametersInExpression(Parameter.Data.expression, RefParameters, Message);

				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = OriginalParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::ParseOtherExpRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters)
{
	NoneRefParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (OriginalParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			NoneRefParameters.Init(FParameterData(), OriginalParameters.Num());
			Offset = 0;

			for (auto& Parameter : OriginalParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = true;

				auto GetRefFunc = [GrammerAnalysis](const FString& InExpression, TArray<FString>& OutRef, FString& OutMsg, bool& OutRes)->void
				{
					TArray<FString> Cur_Ref;
					bool Res_Cur = GrammerAnalysis->GetParametersInExpression(InExpression, Cur_Ref, OutMsg);
					for (auto& Cur : Cur_Ref)
					{
						OutRef.AddUnique(Cur);
					}
					OutRes = Res_Cur || OutRes;
				};

				//对所有可能的表达式进行判断
				if (!Parameter.Data.max_expression.IsEmpty() && !Parameter.Data.max_expression.IsNumeric())
				{//最大值
					GetRefFunc(Parameter.Data.max_expression, RefParameters, Message, Res);
				}
				if (!Parameter.Data.min_expression.IsEmpty() && !Parameter.Data.min_expression.IsNumeric())
				{//最小值
					GetRefFunc(Parameter.Data.min_expression, RefParameters, Message, Res);
				}
				//if (!Parameter.Data.visibility_exp.IsEmpty() && !Parameter.Data.visibility_exp.IsNumeric())
				//{//可见性
				//	GetRefFunc(Parameter.Data.visibility_exp, RefParameters, Message, Res);
				//}
				//if (!Parameter.Data.editable_exp.IsEmpty() && !Parameter.Data.editable_exp.IsNumeric())
				//{//可编辑性
				//	GetRefFunc(Parameter.Data.editable_exp, RefParameters, Message, Res);
				//}
				//if (Parameter.EnumData.Num() > 0)
				//{//枚举
				//	for (auto& Enum : Parameter.EnumData)
				//	{
				//		if (!Enum.visibility_exp.IsEmpty() && !Enum.visibility_exp.IsNumeric())
				//		{
				//			GetRefFunc(Enum.visibility_exp, RefParameters, Message, Res);
				//		}
				//	}
				//}

				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = OriginalParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::ParseParmeterRefrenceMap(TArray<FParameterData>& NoneRefrenceParmeter, TMap<FString, TArray<FString>>& ValueOnlyRefrenceMap, TMap<FString, TArray<FString>>& OtherOnlyRefrenceMap, TMap<FString, TArray<FString>>& BothValueRefrenceMap, TMap<FString, TArray<FString>>& BothOtherRefrenceMap)
{
	TMap<FString, TArray<FString>> ValueRefrenceMap = TMap<FString, TArray<FString>>();
	TArray<FParameterData> ValueNoneRefParameters = TArray<FParameterData>();
	ParseValueExpRefrenceMap(ValueRefrenceMap, ValueNoneRefParameters);
	TMap<FString, TArray<FString>> OtherRefrenceMap = TMap<FString, TArray<FString>>();
	TArray<FParameterData> OtherNoneRefParameters = TArray<FParameterData>();
	ParseOtherExpRefrenceMap(OtherRefrenceMap, OtherNoneRefParameters);

	if (ValueRefrenceMap.Num() <= 0 && OtherRefrenceMap.Num() <= 0) return false;

	auto MapContainsParamName = [&](const TMap<FString, TArray<FString>>& InMap, const FString& InName)
		{
			for (const auto & Iter : InMap)
			{
				if (Iter.Key.Equals(InName,ESearchCase::CaseSensitive))
				{
					return true;
				}
			}
			return false;
		};

	for (const auto& iter : OriginalParameters)
	{
		if (!MapContainsParamName(ValueRefrenceMap, iter.Data.name) && !MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			NoneRefrenceParmeter.AddUnique(iter);
		}
		else if (MapContainsParamName(ValueRefrenceMap, iter.Data.name) && !MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			ValueOnlyRefrenceMap.Add(iter.Data.name,ValueRefrenceMap[iter.Data.name]);
		}
		else if (!MapContainsParamName(ValueRefrenceMap, iter.Data.name) && MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			OtherOnlyRefrenceMap.Add(iter.Data.name, OtherRefrenceMap[iter.Data.name]);
		}
		else
		{
			BothValueRefrenceMap.Add(iter.Data.name, ValueRefrenceMap[iter.Data.name]);
			BothOtherRefrenceMap.Add(iter.Data.name, OtherRefrenceMap[iter.Data.name]);
		}
	}
	return true;
}

void FParameterRefrenceParser::DetermineRefEitherCircleOrNot(const TArray<FParameterData>& InOriginParameters, const TMap<FString, TArray<FString>>& InRefrenceMap, TArray<FParameterData>& OutSameLevel, TArray<FParameterData>& OutSameLevelCir)
{
	//判断是否成环
	TMap<FString, TArray<FString>> RefrenceMap = InRefrenceMap;
	if (RefrenceMap.Num() <= 0) return;

	TArray<FString> Circle = TArray<FString>();
	for (auto& Iter : RefrenceMap)
	{
		if (Circle.Contains(Iter.Key))
			continue;

		TArray<FString> RefPath;
		bool Res = IsCircleRef(Iter.Key, Iter.Key, RefrenceMap, RefPath);
		if (Res)
		{
			for (auto& PathP : RefPath)
			{
				Circle.AddUnique(PathP);
			}
		}
	}

	for (const auto& iter : Circle)
	{
		for (const auto& RefIter : RefrenceMap)
		{
			if (RefIter.Key.Equals(iter, ESearchCase::CaseSensitive))
			{
				int32 CurRefIndex = OriginalParameters.IndexOfByPredicate(
					[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::CaseSensitive); }
				);
				OutSameLevelCir.AddUnique(OriginalParameters[CurRefIndex]);
				RefrenceMap.Remove(iter);
				break;
			}
		}
	}

	//SortByNumOfRef
	RefrenceMap.ValueSort(
		[](const TArray<FString>& Array1, const TArray<FString>& Array2)->bool
	{
		return Array1.Num() <= Array2.Num();
	}
	);

	TArray<FString> KeyArray;
	RefrenceMap.GenerateKeyArray(KeyArray);

	//冒泡法将多层引用放入数组后面
	int32 ConsiderNum = KeyArray.Num();
	for (int32 i = 0; i < ConsiderNum; ++i)
	{
		for (int32 j = 0; j < (ConsiderNum - i); ++j)
		{
			TArray<FString> CurRef = RefrenceMap[KeyArray[j]];
			int32 MaxRefIndex = INDEX_NONE;
			for (int32 K = 0; K < CurRef.Num(); ++K)
			{
				FString CurName = CurRef[K];
				int32 CurRefIndex = KeyArray.IndexOfByPredicate(
					[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::CaseSensitive); }
				);
				if (MaxRefIndex < CurRefIndex)
				{
					MaxRefIndex = CurRefIndex;
				}
			}
			if (MaxRefIndex != INDEX_NONE)
			{//交换位置
				FString Temp = KeyArray[j];
				KeyArray[j] = KeyArray[MaxRefIndex];
				KeyArray[MaxRefIndex] = Temp;
			}
			else
			{//引用的同级不需要考虑顺序的优先算出
				FString Temp = KeyArray[j];
				KeyArray.RemoveAt(j);
				KeyArray.Insert(Temp,0);
			}
		}
	}

	for (const auto& iter : KeyArray)
	{
		int32 CurRefIndex = InOriginParameters.IndexOfByPredicate(
			[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::CaseSensitive); }
		);
		if (CurRefIndex != INDEX_NONE)
		{
			OutSameLevel.AddUnique(InOriginParameters[CurRefIndex]);
		}
	}
}

bool FParameterRefrenceParser::ParseValueExpRefrenceMap(const TArray<FParameterData>& InOriginParameters, TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters)
{
	NoneRefParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (InOriginParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			NoneRefParameters.Init(FParameterData(), InOriginParameters.Num());
			Offset = 0;

			for (auto& Parameter : InOriginParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = GrammerAnalysis->GetParametersInExpression(Parameter.Data.expression, RefParameters, Message);

				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = OriginalParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::ParseOtherExpRefrenceMap(const TArray<FParameterData>& InOriginParameters, TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters)
{
	NoneRefParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (InOriginParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			NoneRefParameters.Init(FParameterData(), InOriginParameters.Num());
			Offset = 0;

			for (auto& Parameter : InOriginParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = true;

				auto GetRefFunc = [GrammerAnalysis](const FString& InExpression, TArray<FString>& OutRef, FString& OutMsg, bool& OutRes)->void
				{
					TArray<FString> Cur_Ref;
					bool Res_Cur = GrammerAnalysis->GetParametersInExpression(InExpression, Cur_Ref, OutMsg);
					for (auto& Cur : Cur_Ref)
					{
						OutRef.AddUnique(Cur);
					}
					OutRes = Res_Cur || OutRes;
				};

				//对所有可能的表达式进行判断
				if (!Parameter.Data.max_expression.IsEmpty() && !Parameter.Data.max_expression.IsNumeric())
				{//最大值
					GetRefFunc(Parameter.Data.max_expression, RefParameters, Message, Res);
				}
				if (!Parameter.Data.min_expression.IsEmpty() && !Parameter.Data.min_expression.IsNumeric())
				{//最小值
					GetRefFunc(Parameter.Data.min_expression, RefParameters, Message, Res);
				}
				if (!Parameter.Data.visibility_exp.IsEmpty() && !Parameter.Data.visibility_exp.IsNumeric())
				{//可见性
					GetRefFunc(Parameter.Data.visibility_exp, RefParameters, Message, Res);
				}
				if (!Parameter.Data.editable_exp.IsEmpty() && !Parameter.Data.editable_exp.IsNumeric())
				{//可编辑性
					GetRefFunc(Parameter.Data.editable_exp, RefParameters, Message, Res);
				}
				if (Parameter.EnumData.Num() > 0)
				{//枚举
					for (auto& Enum : Parameter.EnumData)
					{
						if (!Enum.visibility_exp.IsEmpty() && !Enum.visibility_exp.IsNumeric())
						{
							GetRefFunc(Enum.visibility_exp, RefParameters, Message, Res);
						}
					}
				}

				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = OriginalParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					NoneRefParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::ParseParmeterRefrenceMap(const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& NoneRefrenceParmeter, TMap<FString, TArray<FString>>& ValueOnlyRefrenceMap, TMap<FString, TArray<FString>>& OtherOnlyRefrenceMap, TMap<FString, TArray<FString>>& BothRefrenceMap)
{
	TMap<FString, TArray<FString>> ValueRefrenceMap = TMap<FString, TArray<FString>>();
	TArray<FParameterData> ValueNoneRefParameters = TArray<FParameterData>();
	ParseValueExpRefrenceMap(InOriginParameters, ValueRefrenceMap, ValueNoneRefParameters);
	TMap<FString, TArray<FString>> OtherRefrenceMap = TMap<FString, TArray<FString>>();
	TArray<FParameterData> OtherNoneRefParameters = TArray<FParameterData>();
	ParseOtherExpRefrenceMap(InOriginParameters, OtherRefrenceMap, OtherNoneRefParameters);

	auto MapContainsParamName = [&](const TMap<FString, TArray<FString>>& InMap, const FString& InName)
		{
			for (const auto& Iter : InMap)
			{
				if (Iter.Key.Equals(InName, ESearchCase::CaseSensitive))
				{
					return true;
				}
			}
			return false;
		};

	for (const auto& iter : OriginalParameters)
	{
		if (!MapContainsParamName(ValueRefrenceMap, iter.Data.name) && !MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			NoneRefrenceParmeter.AddUnique(iter);
		}
		else if (MapContainsParamName(ValueRefrenceMap, iter.Data.name) && !MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			ValueOnlyRefrenceMap.Add(iter.Data.name, ValueRefrenceMap[iter.Data.name]);
		}
		else if (!MapContainsParamName(ValueRefrenceMap, iter.Data.name) && MapContainsParamName(OtherRefrenceMap, iter.Data.name))
		{
			OtherOnlyRefrenceMap.Add(iter.Data.name, OtherRefrenceMap[iter.Data.name]);
		}
		else
		{
			BothRefrenceMap.Add(iter.Data.name, ValueRefrenceMap[iter.Data.name]);
		}
	}


	return true;
}

bool FParameterRefrenceParser::ParseRefrenceMap(const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& OutSortedParameters, TMap<FString, TArray<FString>>& RefrenceMap)
{
	OutSortedParameters.Empty();
	UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GWorld);
	if (GameInstance && (InOriginParameters.Num() > 0))
	{
		UGrammerAnalysisSubsystem* GrammerAnalysis = GameInstance->GetSubsystem<UGrammerAnalysisSubsystem>();
		if (GrammerAnalysis)
		{
			OutSortedParameters.Init(FParameterData(), InOriginParameters.Num());
			Offset = 0;

			for (auto& Parameter : InOriginParameters)
			{

				TArray<FString> RefParameters;
				FString Message(TEXT(""));
				bool Res = GrammerAnalysis->GetParametersInExpression(Parameter.Data.expression, RefParameters, Message);

				auto GetRefFunc = [GrammerAnalysis](const FString& InExpression, TArray<FString>& OutRef, FString& OutMsg, bool& OutRes)->void
				{
					TArray<FString> Cur_Ref;
					bool Res_Cur = GrammerAnalysis->GetParametersInExpression(InExpression, Cur_Ref, OutMsg);
					for (auto& Cur : Cur_Ref)
					{
						OutRef.AddUnique(Cur);
					}
					OutRes = Res_Cur || OutRes;
				};

				//对所有可能的表达式进行判断
				if (!Parameter.Data.max_expression.IsEmpty() && !Parameter.Data.max_expression.IsNumeric())
				{//最大值
					GetRefFunc(Parameter.Data.max_expression, RefParameters, Message, Res);
				}
				if (!Parameter.Data.min_expression.IsEmpty() && !Parameter.Data.min_expression.IsNumeric())
				{//最小值
					GetRefFunc(Parameter.Data.min_expression, RefParameters, Message, Res);
				}//CATALOG-1812
				//if (!Parameter.Data.visibility_exp.IsEmpty() && !Parameter.Data.visibility_exp.IsNumeric())
				//{//可见性
				//	GetRefFunc(Parameter.Data.visibility_exp, RefParameters, Message, Res);
				//}
				//if (!Parameter.Data.editable_exp.IsEmpty() && !Parameter.Data.editable_exp.IsNumeric())
				//{//可编辑性
				//	GetRefFunc(Parameter.Data.editable_exp, RefParameters, Message, Res);
				//}
				//if (Parameter.EnumData.Num() > 0)
				//{//枚举可见性
				//	for (auto& Enum : Parameter.EnumData)
				//	{
				//		if (!Enum.visibility_exp.IsEmpty() && !Enum.visibility_exp.IsNumeric())
				//		{
				//			GetRefFunc(Enum.visibility_exp, RefParameters, Message, Res);
				//		}
				//	}
				//}
				if (!Res)
				{//变量的表达式为空或数字不需要解析引用
					OutSortedParameters[Offset++] = Parameter;
					continue;
				}

				if (!Res || (RefParameters.Num() <= 0))
				{//解析引用变量错误，此种情况不应当发生
					//UE_LOG(LogTemp, Log, TEXT("FParameterRefrenceParser::ParseRefrenceMap parameter [%s] has invalid expression [%s]"), *Parameter.Data.name, *Parameter.Data.expression);
					OutSortedParameters[Offset++] = Parameter;
					continue;
				}
				//对自身的引用实为对上一级的引用，为了防止成环这里先移除
				RefParameters.Remove(Parameter.Data.name);
				if (RefParameters.Num() <= 0)
				{//变量没有引用任何变量不需要排序
					OutSortedParameters[Offset++] = Parameter;
					continue;
				}
				int32 PeerIndex = INDEX_NONE;
				for (auto& OtherParameter : RefParameters)
				{
					PeerIndex = InOriginParameters.IndexOfByPredicate([&](const FParameterData& InOther) { return OtherParameter.Equals(InOther.Data.name, ESearchCase::CaseSensitive); });
					if (INDEX_NONE != PeerIndex) break;
				}
				if (INDEX_NONE == PeerIndex)
				{//不存在统计引用，不需要排序
					OutSortedParameters[Offset++] = Parameter;
					continue;
				}
				RefrenceMap.Add(Parameter.Data.name, RefParameters);
			}
			return true;
		}
	}
	return false;
}

bool FParameterRefrenceParser::SortSameLevelParmeter(const TMap<FString, TArray<FString>>& InRefrenceMap, const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& OutParametersList)
{
	//判断是否成环，并排除成环
	TMap<FString, TArray<FString>> RefrenceMap = InRefrenceMap;
	if (RefrenceMap.Num() <= 0) return false;

	TArray<FString> Circle = TArray<FString>();
	for (auto& Iter : RefrenceMap)
	{
		if (Circle.Contains(Iter.Key))
			continue;

		TArray<FString> RefPath;
		bool Res = IsCircleRef(Iter.Key, Iter.Key, RefrenceMap, RefPath);
		if (Res)
		{
			for (auto& PathP : RefPath)
			{
				Circle.AddUnique(PathP);
			}
		}
	}
	for (const auto& iter : Circle)
	{
		for (const auto& RefIter : RefrenceMap)
		{
			if (RefIter.Key.Equals(iter, ESearchCase::CaseSensitive))
			{
				int32 CurRefIndex = OriginalParameters.IndexOfByPredicate(
					[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::CaseSensitive); }
				);
				RefrenceMap.Remove(iter);
				break;
			}
		}
	}

	//依照表达式引用数量排序
	RefrenceMap.ValueSort(
		[](const TArray<FString>& Array1, const TArray<FString>& Array2)->bool
	{
		return Array1.Num() <= Array2.Num();
	}
	);

	TArray<FString> KeyArray;
	RefrenceMap.GenerateKeyArray(KeyArray);

	
	//根据同级引用顺序计算
	TArray<FString> SortedRef = TArray<FString>();
	for (int32 i = 0; i < KeyArray.Num(); ++i)
	{
		FString CurName = KeyArray[i];
		TArray<FString> CurRefList = RefrenceMap[CurName];
		SortedRef.AddUnique(CurName);
		bool IsAllNoneSamelevel = true;
		for (int32 j = 0; j < CurRefList.Num(); ++j)
		{
			FString CurRefName = CurRefList[j];
			int32 CurRefIndex = KeyArray.IndexOfByPredicate(
				[CurRefName](const FString& Name)->bool { return Name.Equals(CurRefName, ESearchCase::CaseSensitive); }
			);
			if (CurRefIndex != INDEX_NONE)
			{
				int32 CurNameIndex = SortedRef.IndexOfByPredicate(
					[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::CaseSensitive); }
				);
				int32 ExistIndex = SortedRef.IndexOfByPredicate(
					[CurRefName](const FString& Name)->bool { return Name.Equals(CurRefName, ESearchCase::CaseSensitive); }
				);
				if (ExistIndex == INDEX_NONE)
				{
					SortedRef.Insert(CurRefName, CurNameIndex);
				}
				else
				{
					if (ExistIndex > CurNameIndex)
					{
						FString Temp = SortedRef[ExistIndex];
						SortedRef.RemoveAt(ExistIndex);
						int32 NewRefIndex = SortedRef.IndexOfByPredicate(
							[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::CaseSensitive); }
						);
						SortedRef.Insert(Temp, NewRefIndex);
					}
				}
				IsAllNoneSamelevel = false;
			}
		}
		if (IsAllNoneSamelevel)
		{
			int32 CurNameIndex = SortedRef.IndexOfByPredicate(
				[CurName](const FString& Name)->bool { return Name.Equals(CurName, ESearchCase::CaseSensitive); }
			);
			if (CurNameIndex != INDEX_NONE)
			{
				FString Temp = SortedRef[CurNameIndex];
				SortedRef.RemoveAt(CurNameIndex);
				SortedRef.Insert(Temp, 0);
			}
			else
			{
				SortedRef.Insert(CurName, 0);
			}
		}
	}
	//依照排序转换成参数
	for (const auto& iter : SortedRef)
	{
		int32 CurRefIndex = InOriginParameters.IndexOfByPredicate(
			[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter, ESearchCase::CaseSensitive); }
		);
		if (CurRefIndex != INDEX_NONE)
		{
			OutParametersList.AddUnique(InOriginParameters[CurRefIndex]);
		}
	}
	return true;
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif
