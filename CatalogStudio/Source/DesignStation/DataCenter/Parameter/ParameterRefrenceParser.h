// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ParameterData.h"
#include "UObject/NoExportTypes.h"

//解析变量间的相互引用
struct DESIGNSTATION_API FParameterRefrenceParser
{
private:

	TArray<FParameterData> OriginalParameters;

	TArray<FParameterData> SortedParameters;

	int32 Offset = 0;

public:

	bool SortParameterByRefrence(const TArray<FParameterData>& InParameters);

	bool SortParameterByParamRefrence(const TArray<FParameterData>& InParameters);

	bool SortParameterByParamRefrence(const TArray<FParameterData>& InParameters, TArray<FParameterData>& ConstantExp, TArray<FParameterData>& SameLevel, TArray<FParameterData>& SameLevelCir);//TMap<FString, TArray<FString>>

	bool SortParameterByParamRefrence(const TArray<FParameterData>& InParameters, TArray<FParameterData>& ConstantExp, TMap<FString, TArray<FString>>& ValueOnlyMap, TMap<FString, TArray<FString>>& OtherOnlyMap, TMap<FString, TArray<FString>>& BothValueMap, TMap<FString, TArray<FString>>& BothOtherMap);

	inline const TArray<FParameterData>& GetOriginalParameters() const { return OriginalParameters; }

	//返回的数组为空表示引用排序失败
	inline const TArray<FParameterData>& GetSortedParameters() const { return SortedParameters; }

	void DetermineRefEitherCircleOrNot(const TArray<FParameterData>& InOriginParameters, const TMap<FString, TArray<FString>>& InRefrenceMap, TArray<FParameterData>& OutSameLevel, TArray<FParameterData>& OutSameLevelCir);

	bool ParseValueExpRefrenceMap(const TArray<FParameterData>& InOriginParameters, TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters);

	bool ParseOtherExpRefrenceMap(const TArray<FParameterData>& InOriginParameters, TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters);

	bool ParseParmeterRefrenceMap(const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& NoneRefrenceParmeter, TMap<FString, TArray<FString>>& ValueOnlyRefrenceMap, TMap<FString, TArray<FString>>& OtherOnlyRefrenceMap, TMap<FString, TArray<FString>>& BothRefrenceMap);

	bool ParseRefrenceMap(const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& OutSortedParameters, TMap<FString, TArray<FString>>& RefrenceMap);

	bool SortSameLevelParmeter(const TMap<FString, TArray<FString>>& InRefrenceMap, const TArray<FParameterData>& InOriginParameters, TArray<FParameterData>& OutParametersList);

private:

	bool ParseRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap);

	bool ParseRootNodes(const TMap<FString, TArray<FString>>& RefrenceMap, TArray<FString>& RootNodes);

	bool ParsePostorderRefrenceNode(const TMap<FString, TArray<FString>>& RefrenceMap, const FString& InRootNode, TArray<FString>& PostorderRefrence);

	bool IsCircleRef(FString Origin, FString Name, TMap<FString, TArray<FString>> AllRef, TArray<FString>& RefName);

	bool ParseValueExpRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters);

	bool ParseOtherExpRefrenceMap(TMap<FString, TArray<FString>>& RefrenceMap, TArray<FParameterData>& NoneRefParameters);

	bool ParseParmeterRefrenceMap(TArray<FParameterData>& NoneRefrenceParmeter, TMap<FString, TArray<FString>>& ValueOnlyRefrenceMap, TMap<FString, TArray<FString>>& OtherOnlyRefrenceMap, TMap<FString, TArray<FString>>& BothValueRefrenceMap, TMap<FString, TArray<FString>>& BothOtherRefrenceMap);

	
};
