// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontFolderDataLibrary.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include <SubSystem/ResourceSubsystem.h>

void FCSModelMatData::GetPakFileMountInfo(FString& RefPath, FString& RelativePath)
{
	RefPath = refPath;

	FString FolderPath = pakFilePath;
	if(FolderPath.StartsWith(TEXT("/")))
		FolderPath.RemoveAt(0);
	FolderPath = FPaths::Combine(FolderPath, TEXT("D"));
	const FString PakName = TEXT("D");

	RelativePath = FPaths::Combine(FolderPath, PakName + (PakName.EndsWith(TEXT(".pak")) ? TEXT("") : TEXT(".pak")));
}

bool FCSModelMatData::AnalysisData_Pak(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	for (const auto PM : modelList)
	{
		FString FolderPath = pakFilePath;
		FolderPath.RemoveAt(0);
		//path folder
		//zip file unpack has same name folder 

		FolderPath = FPaths::Combine(FolderPath, TEXT("D"));
		FString AbsFolderPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FolderPath));
		if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*AbsFolderPath))
		{
			FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*AbsFolderPath);
		}

		//FString RelPath = FPaths::Combine(FolderPath, PM.name + (PM.name.EndsWith(TEXT(".pak")) ? TEXT("") : TEXT(".pak")));
		FString PakName = TEXT("D");
		FString RelPath = FPaths::Combine(FolderPath, PakName + (PakName.EndsWith(TEXT(".pak")) ? TEXT("") : TEXT(".pak")));
		FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelPath));
		UE_LOG(LogTemp, Warning, TEXT("view pak file path : [%s]"), *RelPath);
		FilePath.Add(RelPath);
		if (!FPaths::FileExists(AbsPath) /*|| !PM.md5.IsEmpty()*/)
		{//need Download
			DownloadFilePath.Add(PM.fbxFilePath);
		}
		else
		{
			if (!PM.md5.IsEmpty())
			{
				FString OutMd5;
				int64 OutSize;
				if (ACatalogPlayerController::GetFileMD5AndSize(AbsPath, OutMd5, OutSize))
				{
					if (!OutMd5.Equals(PM.md5))
					{
						DownloadFilePath.Add(PM.fbxFilePath);
					}
				}
			}
		}
	}

	return DownloadFilePath.Num() > 0;
}

#define WEB_MATERIAL_PATH TEXT("ImportMaterial")
bool FCSModelMatData::AnalysisData_Material(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	FString RelFolder = FPaths::Combine(WEB_MATERIAL_PATH, FString::FromInt(id));
	const FString AbsFolderPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelFolder));
	if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*AbsFolderPath))
	{
		FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*AbsFolderPath);
	}

	//param file 
	FString ParamFileName = FPaths::GetCleanFilename(ue5Param);
	FString RelParamFilePath = FPaths::Combine(RelFolder, ParamFileName);
	FString AbsParamFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelParamFilePath));
	bool bAllDownload = false;
	FilePath.AddUnique(RelParamFilePath);
	if (!FPaths::FileExists(AbsParamFilePath))
	{
		bAllDownload = true;
		//FilePath.Add(RelParamFilePath);
		DownloadFilePath.AddUnique(ue5Param);
	}
	else
	{
		if (!md5.IsEmpty())
		{//check md5
			FString OutMd5;
			int64 OutSize;
			if (ACatalogPlayerController::GetFileMD5AndSize(AbsParamFilePath, OutMd5, OutSize))
			{
				if (!OutMd5.Equals(md5))
				{
					bAllDownload = true;
					DownloadFilePath.AddUnique(ue5Param);
				}
			}
		}
	}

#define CATALOG_WEB_NET_FLAG TEXT("http")

	UResourceSubsystem* Resource = UResourceSubsystem::GetInstance();
	if (Resource == nullptr)
	{
		return DownloadFilePath.Num() > 0;
	}

	for (const auto PM : manageMapsList)
	{
		bool IsMat = Resource->IsMatFile(PM.materialValue);
		if ((PM.materialValue.IsEmpty() || !PM.materialValue.Contains(CATALOG_WEB_NET_FLAG)) && !IsMat) continue;

		//FString MatFileName = FPaths::GetCleanFilename(PM.materialValue);
		//FString MatFileName = FPaths::GetCleanFilename(ViewData.mapsImg);
		FString MatFileName = FPaths::GetCleanFilename(PM.materialValue);
		FString RelMatFilePath = FPaths::Combine(RelFolder, MatFileName);
		FString AbsFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelMatFilePath));
		FilePath.Insert(RelMatFilePath, 0);
		if (bAllDownload || !FPaths::FileExists(AbsFilePath) || PM.md5.IsEmpty())
		{//check md5
			DownloadFilePath.Insert(PM.materialValue, 0);
		}
		else
		{
			FString OutMd5;
			int64 OutSize;
			if (ACatalogPlayerController::GetFileMD5AndSize(AbsFilePath, OutMd5, OutSize))
			{
				if (!OutMd5.Equals(PM.md5))
				{
					DownloadFilePath.Insert(PM.materialValue, 0);
				}
			}
		}
	}
#undef CATALOG_WEB_NET_FLAG

	return DownloadFilePath.Num() > 0;
}

bool FCSModelMatData::AnalysisData(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	if (IsPakFile())
	{
		return AnalysisData_Pak(FilePath, DownloadFilePath);
	}
	else if (IsMaterialFile())
	{
		return AnalysisData_Material(FilePath, DownloadFilePath);
	}
	else if (IsFBXFile())
	{
		return AnalysisData_Fbx(FilePath, DownloadFilePath);
	}

	return false;
}
#undef WEB_MATERIAL_PATH
