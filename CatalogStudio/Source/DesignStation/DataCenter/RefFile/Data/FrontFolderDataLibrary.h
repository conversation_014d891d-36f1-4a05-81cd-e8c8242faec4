// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Marco.h"
#include "RefToDirectoryDataLibrary.h"
#include "FrontFolderDataLibrary.generated.h"


/*
 *  @@ 前端目录数据
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FFrontDirectoryData
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int64 id;

	//分类代码
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString name;

	//分类代码
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString code;

	//是否封存;0为封存 1为启用
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 isSeal;

	//是否末级分类;0是，1否
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 isSubCate;

	//父级级别
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 plevel;

	//当前级别
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 level;

	//示例图
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString resourceImg;

	//分类项所属
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString dictGroupValue;

	//分类所属
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString dictValue;

	//父级ID
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 pid;

	//主分类id
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int64 hostId;

	//删除标记，1为删除
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 delFlag;

	//创建人
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString createdBy;

	//创建时间
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString createdTime;

	//更新人
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString updatedBy;

	//更新时间
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString updatedTime;

public:
	FFrontDirectoryData()
		: id(0)
		, name(TEXT(""))
		, code(TEXT(""))
		, isSeal(1)
		, isSubCate(1)
		, plevel(0)
		, level(0)
		, resourceImg(TEXT(""))
		, dictGroupValue(TEXT(""))
		, dictValue(TEXT(""))
		, pid(0)
		, hostId(0)
		, delFlag(0)
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
	{}

	bool IsLastDirectory() const { return isSubCate == 0; }

};

USTRUCT()
struct DESIGNSTATION_API FFrontDirectoryDataArr
{
	GENERATED_USTRUCT_BODY()

public:
	TArray<FFrontDirectoryData> DataArr;

public:
	FFrontDirectoryDataArr()
	{}
};

USTRUCT()
struct DESIGNSTATION_API FFrontDirectoryNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	TArray<FFrontDirectoryData> resp;

	UPROPERTY()
	bool success;

public:
	FFrontDirectoryNetMsg()
		: code(TEXT(""))
		, resp(TArray<FFrontDirectoryData>())
		, success(false)
	{
	}
};

UENUM(BlueprintType)
enum class EFrontDirectoryDataType : uint8
{
	E_None = 0,
	E_Catalog,
	E_Furniture,
	E_Material,
	E_Mapping
};


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FFrontDirectoryConbine
{
	GENERATED_USTRUCT_BODY()

public:
	FFrontDirectoryData FrontDirectoryData;

	EFrontDirectoryDataType DataType;

public:
	FFrontDirectoryConbine()
		: FrontDirectoryData(FFrontDirectoryData())
		, DataType(EFrontDirectoryDataType::E_None)
	{}
};

USTRUCT()
struct DESIGNSTATION_API FFrontDirectoryNetUUID
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString UpdateUUID;

	UPROPERTY()
	FString SearchUUID;

	UPROPERTY()
	FString CategoryDetailUUID;

	UPROPERTY()
	FString CatagoryDetailFileToParseUUID;

	UPROPERTY()
	FString DownloadUUID;

	UPROPERTY()
	FString DownloadSingleUUID;

	UPROPERTY()
	FString ModelDetailToParseUUID;
	
	UPROPERTY()
	FString ModelDetailToDownloadUUID;

	UPROPERTY()
	FString MatDetailToParseUUID;

	UPROPERTY()
	FString MatDetailToDownloadUUID;

	//用于获取材质信息，主要为挂载的数据（structData）
	UPROPERTY()
	FString MatDetailToParseAdditionUUID;

public:
	FFrontDirectoryNetUUID()
		: UpdateUUID(EMPTY_NET_MARK)
		, SearchUUID(EMPTY_NET_MARK)
		, CategoryDetailUUID(EMPTY_NET_MARK)
		, CatagoryDetailFileToParseUUID(EMPTY_NET_MARK)
		, DownloadUUID(EMPTY_NET_MARK)
		, DownloadSingleUUID(EMPTY_NET_MARK)
		, ModelDetailToParseUUID(EMPTY_NET_MARK)
		, ModelDetailToDownloadUUID(EMPTY_NET_MARK)
		, MatDetailToParseUUID(EMPTY_NET_MARK)
		, MatDetailToDownloadUUID(EMPTY_NET_MARK)
		, MatDetailToParseAdditionUUID(EMPTY_NET_MARK)
	{
	}

	void ResetUpdateAction() { UpdateUUID = EMPTY_NET_MARK; }

	void ResetSearchAction() { SearchUUID = EMPTY_NET_MARK; }

	void ResetCategoryDetailAction() { CategoryDetailUUID = EMPTY_NET_MARK; }

	void ResetCatagoryDetailFileToParseAction() { CatagoryDetailFileToParseUUID = EMPTY_NET_MARK; }

	void ResetDownloadAction() { DownloadUUID = EMPTY_NET_MARK; }

	void ResetDownloadSingleAction() { DownloadSingleUUID = EMPTY_NET_MARK; }

	void ResetModelDetailToParseAction() { ModelDetailToParseUUID = EMPTY_NET_MARK; }
	bool IsModelURLParseFiniesh() { return ModelDetailToParseUUID.Equals(EMPTY_NET_MARK); }

	void ResetModelDetailToDownloadAction() { ModelDetailToDownloadUUID = EMPTY_NET_MARK; }
	bool IsModelURLDownloadFiniesh() { return ModelDetailToDownloadUUID.Equals(EMPTY_NET_MARK); }

	void ResetMatDetailToParseAction() { MatDetailToParseUUID = EMPTY_NET_MARK; }
	bool IsMatURLParseFiniesh() { return MatDetailToParseUUID.Equals(EMPTY_NET_MARK); }

	void ResetMatDetailToDownloadAction() { MatDetailToDownloadUUID = EMPTY_NET_MARK; }
	bool IsMatURLDownloadFiniesh() { return MatDetailToDownloadUUID.Equals(EMPTY_NET_MARK); }

	void ResetMatDetailToParseAdditionAction() { MatDetailToParseAdditionUUID = EMPTY_NET_MARK; }
	bool IsMatURLParseAdditionFinish() { return MatDetailToParseAdditionUUID.Equals(EMPTY_NET_MARK); }

};

/*
 *  @@ 模型、材质列表数据
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FModelMatItemData
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int64 id;

	//item name
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString name;

	//item code
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString code;

	//item category
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 attrCate;

	//file path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString refPath;

	//pak ref file
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString pakFilePath;

	//0 : self furniture, 1 : other furniture
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 isSelf;

	//item img
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString productImg;

	//item brand
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString brand;

	//item category main table id; 
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int64 cateId;

	//item category name; 
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString cateName;

	//item material category
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString materialCate;

	//texture img path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString mapsImg;

	//item price
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	double price;

	//item depth
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString depth;

	//item width
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString width;

	//item height
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString height;

	//item snap or place rules
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString placementRules;

	//item category group
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString dictGroupValue;

	//item category value
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString dictValue;

	//item material sphere
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString shader;

	//item vrscene file path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString vrscene;

	//item ue5 param file path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString ue5Param;

	//item material main table id; only for material texture
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int64 templateId;

	//render table main id
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 renderingId;

	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderCode;

	//folder_id; for ref use
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderId;

	//item mark
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString mark;

	//1 : delete
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 delFlag;

	//label
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString label;

	//create by user
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString createdBy;

	//create time
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString createdTime;

	//update by user
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString updatedBy;

	//update time
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString updatedTime;

public:
	FModelMatItemData()
		: id(0)
		, name(TEXT(""))
		, code(TEXT(""))
		, attrCate(0)
		, refPath(TEXT(""))
		, pakFilePath(TEXT(""))
		, isSelf(0)
		, productImg(TEXT(""))
		, brand(TEXT(""))
		, cateId(0)
		, cateName(TEXT(""))
		, materialCate(TEXT(""))
		, mapsImg(TEXT(""))
		, price(0.0)
		, depth(TEXT(""))
		, width(TEXT(""))
		, height(TEXT(""))
		, placementRules(TEXT(""))
		, dictGroupValue(TEXT(""))
		, dictValue(TEXT(""))
		, shader(TEXT(""))
		, vrscene(TEXT(""))
		, ue5Param(TEXT(""))
		, templateId(0)
		, renderingId(0)
		, folderCode(TEXT(""))
		, folderId(TEXT(""))
		, mark(TEXT(""))
		, delFlag(0)
		, label(TEXT(""))
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FModelMatSingleNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	FModelMatItemData resp;

	UPROPERTY()
	bool success;

public:
	FModelMatSingleNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(FModelMatItemData())
		, success(false)
	{
	}
};


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FModelMatItemContent
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	TArray<FRefDirectoryData> bkDirectoryList;

	UPROPERTY()
	TArray<FModelMatItemData> resourceAdminListResp;

public:
	FModelMatItemContent()
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FModelMatItemResp
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	int32 total;

	UPROPERTY()
	FModelMatItemContent content;

public:
	FModelMatItemResp()
		: total(0)
		, content(FModelMatItemContent())
	{
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FModelMatItemNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	FModelMatItemResp resp;

	UPROPERTY()
	bool success;

public:
	FModelMatItemNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(FModelMatItemResp())
		, success(false)
	{
	}
};

//texture map data
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSMapData
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY()
	int64 id;

	UPROPERTY()
	int64 materialId;

	UPROPERTY()
	int64 adminId;

	UPROPERTY()
	FString md5;

	UPROPERTY()
	int64 attrId;

	UPROPERTY()
	FString attrName;

	UPROPERTY()
	FString materialValue;

	UPROPERTY()
	int32 delFlag;

	//create by user
	UPROPERTY()
	FString createdBy;

	//create time
	UPROPERTY()
	FString createdTime;

	//update by user
	UPROPERTY()
	FString updatedBy;

	//update time
	UPROPERTY()
	FString updatedTime;

public:
	FCSMapData()
		: id(0)
		, materialId(0)
		, adminId(0)
		, md5(TEXT(""))
		, attrId(0)
		, attrName(TEXT(""))
		, materialValue(TEXT(""))
		, delFlag(0)
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
	{
	}

};

/*
 *  @@ furniture or material data to parse load
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSModelMatLable
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY()
	int64 id;

	//lable name
	UPROPERTY()
	FString name;

public:
	FCSModelMatLable()
		: id(0)
		, name(TEXT(""))
	{}

};

/*
 *  @@ furniture model data to parse load
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSModelInfo
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY()
	int32 id;

	//model quality , 0 : lower ; 1 : middle ; 2 : high
	UPROPERTY()
	int32 type;

	//model code 
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString md5;

	//model name
	UPROPERTY()
	FString name;

	//model fbx file path
	UPROPERTY()
	FString fbxFilePath;

public:
	FCSModelInfo()
		: id(0)
		, type(0)
		, code(TEXT(""))
		, md5(TEXT(""))
		, name(TEXT(""))
		, fbxFilePath(TEXT(""))
	{}

};

/*
 *  @@ all data for furniture or mat
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSModelMatData
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY()
	int64 id;

	//item name
	UPROPERTY()
	FString name;

	//item code
	UPROPERTY()
	FString code;

	//item category
	UPROPERTY()
	int32 attrCate;

	//file path
	UPROPERTY()
	FString refPath;

	//pak ref file
	UPROPERTY()
	FString pakFilePath;

	//0 : self furniture, 1 : other furniture
	UPROPERTY()
	int32 isSelf;

	//item img
	UPROPERTY()
	FString productImg;

	//item brand
	UPROPERTY()
	FString brand;

	//item category main table id; 
	UPROPERTY()
	int64 cateId;

	//item category name; 
	UPROPERTY()
	FString cateName;

	//item material category
	UPROPERTY()
	FString materialCate;

	//texture img path
	UPROPERTY()
	FString mapsImg;

	//item price
	UPROPERTY()
	double price;

	//item depth
	UPROPERTY()
	FString depth;

	//item width
	UPROPERTY()
	FString width;

	//item height
	UPROPERTY()
	FString height;

	//item snap or place rules
	UPROPERTY()
	FString placementRules;

	//item category group
	UPROPERTY()
	FString dictGroupValue;

	//item category value
	UPROPERTY()
	FString dictValue;

	//item material sphere
	UPROPERTY()
	FString shader;

	//item vrscene file path
	UPROPERTY()
	FString vrscene;

	//item ue5 param file path
	UPROPERTY()
	FString ue5Param;

	//item material main table id; only for material texture
	UPROPERTY()
	int64 templateId;

	//render table main id
	UPROPERTY()
	int32 renderingId;

	//folder_id; for ref use
	UPROPERTY()
	FString folderId;

	UPROPERTY()
	FString folderCode;

	//item mark
	UPROPERTY()
	FString mark;

	//1 : delete
	UPROPERTY()
	int32 delFlag;

	//label
	UPROPERTY()
	FString md5;

	//create by user
	UPROPERTY()
	FString createdBy;

	//create time
	UPROPERTY()
	FString createdTime;

	//update by user
	UPROPERTY()
	FString updatedBy;

	//update time
	UPROPERTY()
	FString updatedTime;

	UPROPERTY()
	TArray<FCSMapData> manageMapsList;

	UPROPERTY()
	TArray<FCSModelMatLable> labelList;

	UPROPERTY()
	TArray<FCSModelInfo> modelList;

	/**
	 *	@@材质、模型等挂载的额外信息，来源自素材管理平台
	 *	@@材质：换行的字符串组
	 *	@@模型：XML格式数据
	 */
	UPROPERTY()
	FString structData;

public:
	FCSModelMatData()
		: id(0)
		, name(TEXT(""))
		, code(TEXT(""))
		, attrCate(0)
		, refPath(TEXT(""))
		, pakFilePath(TEXT(""))
		, isSelf(0)
		, productImg(TEXT(""))
		, brand(TEXT(""))
		, cateId(0)
		, cateName(TEXT(""))
		, materialCate(TEXT(""))
		, mapsImg(TEXT(""))
		, price(0.0)
		, depth(TEXT(""))
		, width(TEXT(""))
		, height(TEXT(""))
		, placementRules(TEXT(""))
		, dictGroupValue(TEXT(""))
		, dictValue(TEXT(""))
		, shader(TEXT(""))
		, vrscene(TEXT(""))
		, ue5Param(TEXT(""))
		, templateId(0)
		, renderingId(0)
		, folderId(TEXT(""))
		, folderCode(TEXT(""))
		, mark(TEXT(""))
		, delFlag(0)
		, md5(TEXT(""))
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
		, manageMapsList(TArray<FCSMapData>())
		, labelList(TArray<FCSModelMatLable>())
		, modelList(TArray<FCSModelInfo>())
		, structData(TEXT(""))
	{
	}

	bool IsValid() const { return id > 0; }

	bool IsPakFile() const { return !refPath.IsEmpty(); }

	bool IsMaterialFile() const { return !manageMapsList.IsEmpty(); }

	bool IsFBXFile() const { return !IsPakFile() && !IsMaterialFile(); }

	void GetPakFileMountInfo(FString& RefPath, FString& RelativePath);

	bool AnalysisData_Pak(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath);
	bool AnalysisData_Material(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath);
	bool AnalysisData_Fbx(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath) { return false; }

	bool AnalysisData(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath);
};


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMatAdditionInfo
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString Name;

	UPROPERTY()
	FString FolderID;

	UPROPERTY()
	FString FolderCode;

	UPROPERTY()
	FString AdditionData;    //structData

public:
	FMatAdditionInfo()
		: Name(TEXT("")), FolderID(TEXT("")), FolderCode(TEXT("")), AdditionData(TEXT(""))
	{
	}

	void SetInfo(const FString& InName, const FString& InFolderID, const FString& InFolderCode, const FString& InAdditionData)
	{
		Name = InName;
		FolderID = InFolderID;
		FolderCode = InFolderCode;
		AdditionData = InAdditionData;
	}

	TArray<FString> GetAdditionStrArr() const
	{
		TArray<FString> Res;
		AdditionData.ParseIntoArray(Res, TEXT("\n"));
		return Res;
	}

	bool IsCurMat(const FString& InMatFolderID)
	{
		if (InMatFolderID.IsNumeric())
		{
			double MatID = FCString::Atod(*InMatFolderID);
			double CurMat = FCString::Atod(*FolderID);
			return FMath::IsNearlyEqual(MatID, CurMat, 0.1);
		}
		else
		{
			return FolderID.Equals(InMatFolderID, ESearchCase::CaseSensitive);
		}
	}
};

/*
 *  @@ all data for furniture or mat
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSModelMatDataNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	FCSModelMatData resp;

	UPROPERTY()
	bool success;

public:
	FCSModelMatDataNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(FCSModelMatData())
		, success(false)
	{
	}

};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCSModelMatArrDataNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FCSModelMatData> resp;

	UPROPERTY()
	bool success;

public:
	FCSModelMatArrDataNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(TArray<FCSModelMatData>())
		, success(false)
	{
	}

};

/*
 *  @@ release net data
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCatalogReleaseData
{
	GENERATED_USTRUCT_BODY()

public:
	//main key
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString id;

	//folder_id
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderId;

	//folder_name
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderName;

	//folder_code
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderCode;

	//folder_code exp
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString folderCodeExp;

	//img path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString thumbnailPath;

	//backend_path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString backendFolderPath;

	//front path
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString fontFolderPath;

	//visi
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 visibility;

	//visi exp
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString visibilityExp;

	//order
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 dirOrder;

	//0 : file; 1 : folder
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 isFolder;

	//0 : false; 1 : new(true)
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	int32 isNew;

	//md5
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString md5;

	//create by user
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString createdBy;

	//create time
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString createdTime;

	//update by user
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString updatedTime;

	//update time
	UPROPERTY(BlueprintReadWrite, Category = "ModelOrMatListItem")
	FString updatedBy;

public:
	FCatalogReleaseData()
		: id(TEXT(""))
		, folderId(TEXT(""))
		, folderName(TEXT(""))
		, folderCode(TEXT(""))
		, folderCodeExp(TEXT(""))
		, thumbnailPath(TEXT(""))
		, backendFolderPath(TEXT(""))
		, fontFolderPath(TEXT(""))
		, visibility(0)
		, visibilityExp(TEXT(""))
		, dirOrder(0)
		, isFolder(0)
		, isNew(0)
		, md5(TEXT(""))
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedTime(TEXT(""))
		, updatedBy(TEXT(""))
	{}


};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCatalogReleaseNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FCatalogReleaseData> resp;

	UPROPERTY()
	bool success;

public:
	FCatalogReleaseNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(TArray<FCatalogReleaseData>())
		, success(false)
	{
	}
};


/**
 *
 */
UCLASS()
class DESIGNSTATION_API UFrontFolderDataLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:


};
