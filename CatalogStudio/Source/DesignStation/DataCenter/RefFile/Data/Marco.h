// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "CoreMinimal.h"

#define EMPTY_NET_MARK TEXT("NoWay")

#define REF_FILE_ROOT_FOLDER TEXT("RefFiles")
#define CONVERT_UE_FULL_PATH(Path) FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), Path))
#define REF_ROOT_FOLDER_FULL CONVERT_UE_FULL_PATH(REF_FILE_ROOT_FOLDER)

#define CHECK_FOLDER_VALID_CREATE(FolderPath) \
if (!FPaths::DirectoryExists(FolderPath))\
{\
	UE_LOG(LogTemp, Error, TEXT("FolderPath is not exist! --- need create"));\
	FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*FolderPath);\
}

#define CHECK_FOLDER_VALID(InFolderPath) CHECK_FOLDER_VALID_CREATE(InFolderPath)
#define CHECK_REF_ROOT_FOLDER() CHECK_FOLDER_VALID(REF_ROOT_FOLDER_FULL)

#define GET_CACHE_REF_DATA(FolderData) \
FRefToLocalFileData RefData; \
UFolderWidget::Get()->GetCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(FolderData), RefData)

#define GET_REF_DATA_FROM_FILE(FolderData) \
FRefToLocalFileData RefData; \
bool bSuccess = URefRelationFunction::GetCurrentRefRelationFromFile(URefRelationFunction::GetMarkToBackendDirectory(FolderData), RefData); 

#define GET_UPPER_LEVEL_HERITPARAMETERS(FolderData, HeritParams, HasLastDir) \
GET_CACHE_REF_DATA(FolderData); \
TArray<FString> UpperDirectoryID = URefRelationFunction::GetUpperFolderDirectory(FolderData.backend_folder_path, HasLastDir); \
TArray<FRefToLocalFileData> UpperRefDatas; \
UFolderWidget::Get()->GetCacheDataForFile(UpperDirectoryID, UpperRefDatas); \
URefRelationFunction::GetTopLevelFolderParameterData(UpperRefDatas, HeritParams)

#define EXIT_EDIT_TO_SYNC_LOCAL_NETWORK(InRefData) \
bool SaveRes = URefRelationFunction::SaveFile(InRefData); \
URefRelationFunction::SyncFileNetwork(InRefData); \
UFolderWidget::Get()->UpdateCacheDataForFile(InRefData)

#define EXIT_EDIT_STYLE_TO_SYNC_LOCAL_NETWORK(InRefData) \
bool SaveRes = URefRelationFunction::SaveStyleRefRelationToFile(InRefData); \
UFolderWidget::Get()->UploadStyle()

#define INIT_STYLE_REF_DATA() \
UFolderWidget::Get()->InitStyleRefData()

#define GET_STYLE_REF_FILE_DATA() \
FRefToStyleFile StyleRefData = UFolderWidget::Get()->GetStyleRefData()

#define GET_STYLE_REF_FILE_DATA_REF() \
FRefToStyleFile& StyleRefData = UFolderWidget::Get()->GetStyleRefDataRef()

#define PARSE_STYLE_REF_DATA_TO_ACTION_DATA(ActionData, RefData) \
ActionData.id = RefData.style_id; \
ActionData.checked = RefData.is_checked; \
ActionData.code = RefData.style_code; \
ActionData.craft = RefData.style_craft; \
ActionData.description = RefData.style_description; \
ActionData.thumbnail_path = RefData.style_thumbnail_path; \
ActionData.thumbnail_md5 = RefData.style_thumbnail_md5; \
ActionData.sort_order = RefData.style_sort_order; \
ActionData.style_group = static_cast<int32>(RefData.style_group)

#define PARSE_ACTION_DATA_TO_REF_DATA(RefData, ActionData, ThumbnailMd5) \
RefData.style_id = ActionData.id; \
RefData.is_checked = ActionData.checked; \
RefData.style_code = ActionData.code; \
RefData.style_craft = ActionData.craft; \
RefData.style_description = ActionData.description; \
RefData.style_thumbnail_path = ActionData.thumbnail_path; \
RefData.style_thumbnail_md5 = ActionData.thumbnail_md5; \
RefData.style_sort_order = ActionData.sort_order; \
RefData.style_group = static_cast<ECSStyleGeoup>(ActionData.style_group); \
RefData.style_thumbnail_md5 = ThumbnailMd5

#define PARSE_CONTENT_REF_DATA_TO_ACTION_DATA(ActionData, RefData) \
ActionData.id = RefData.content_id; \
ActionData.name = RefData.content_name; \
ActionData.sort_order = RefData.content_sort_order; \
ActionData.RelationCode = RefData.content_relation_code; \
ActionData.BaseType = static_cast<EDecorateContentBaseType>(RefData.content_base_type)

#define PARSE_CONTENT_ACTION_DATA_TO_REF_DATA(ActionData, RefData) \
RefData.content_id = ActionData.id; \
RefData.content_name = ActionData.name; \
RefData.content_sort_order = ActionData.sort_order; \
RefData.content_relation_code = ActionData.RelationCode; \
RefData.content_base_type = static_cast<int32>(ActionData.BaseType)

#define PARSE_OPTION_REF_DATA_TO_ACTION_DATA(ActionData, RefData) \
ActionData.id = RefData.option_id; \
ActionData.code = RefData.option_code; \
ActionData.description = RefData.option_description; \
ActionData.thumbnail_path = RefData.option_thumbnail_url; \
ActionData.sort_order = RefData.option_sort_order; \
ActionData.visibility = RefData.option_visibility; \
ActionData.visibility_exp = RefData.option_visibility_exp; \
ActionData.parent_id = RefData.option_content_id;\
ActionData.data_source = RefData.option_data_source;\
ActionData.custom_id = RefData.option_custom_id

#define PARSE_OPTION_ACTION_DATA_TO_REF_DATA(ActionData, RefData) \
RefData.option_id = ActionData.id; \
RefData.option_code = ActionData.code; \
RefData.option_description = ActionData.description; \
RefData.option_thumbnail_url = ActionData.thumbnail_path; \
RefData.option_sort_order = ActionData.sort_order; \
RefData.option_visibility = ActionData.visibility; \
RefData.option_visibility_exp = ActionData.visibility_exp; \
RefData.option_content_id = ActionData.parent_id; \
RefData.option_data_source = ActionData.data_source; \
RefData.option_custom_id = ActionData.custom_id

#define PARSE_CHECK_ACTION_DATA_TO_REF_DATA(ActionData, RefData) \
RefData.option_id = ActionData.selection_id; \
RefData.content_id = ActionData.content_id; \
RefData.style_id = ActionData.style_id; \
RefData.is_prime = ActionData.is_prime

#define SWAPE_ITEM_ACTION(ArrContain, ArrClass, FlagName, ActionID, IsUp) \
const int32 Index = ArrContain.IndexOfByPredicate([ActionID](const ArrClass& InData){ return InData.FlagName.Equals(ActionID); }); \
if (Index != INDEX_NONE) \
{ \
	auto Data = ArrContain[Index]; \
	if (IsUp) \
	{ \
		int32 AnotherIndex = Index - 1; \
		auto AnotherData = ArrContain[AnotherIndex]; \
		ArrContain.Insert(Data, AnotherIndex); \
		ArrContain.RemoveAt(Index + 1); \
	} \
	else \
	{ \
		int32 AnotherIndex = Index + 1; \
		auto AnotherData = ArrContain[AnotherIndex]; \
		ArrContain.RemoveAt(AnotherIndex); \
		ArrContain.Insert(AnotherData, Index); \
	} \
}

#define GET_CONTENT_REF_NO_REF_FILE(ContentID) \
int32 Content_Index = StyleRefData.content_datas.IndexOfByPredicate([this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(ContentID); }); \
bool GetContentRes = Content_Index != INDEX_NONE; \


#define GET_PARAMS_WITH_REF_FILE(ContentID, OptionID, OutParams) \
GET_STYLE_REF_FILE_DATA_REF(); \
int32 Index = StyleRefData.content_datas.IndexOfByPredicate([this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(ContentID); }); \
if (Index != INDEX_NONE) \
{ \
	for (const auto Iter : StyleRefData.content_datas[Index].option_datas) \
	{ \
		if (Iter.option_id.Equals(OptionID)) \
		{ \
			OutParams = Iter.option_params; \
			break; \
		} \
	} \
}

#define GET_PARAMS_NO_REF_FILE(ContentID, OptionID, OutParams) \
int32 Index = StyleRefData.content_datas.IndexOfByPredicate([this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(ContentID); }); \
if (Index != INDEX_NONE) \
{ \
	for (const auto Iter : StyleRefData.content_datas[Index].option_datas) \
	{ \
		if (Iter.option_id.Equals(OptionID)) \
		{ \
			OutParams = Iter.option_params; \
			break; \
		} \
	} \
}


/*
 *  @@  NetWork Response 
 */

#define PARSE_BACKEND_DIRECTORY_NET_RESPONSE(CallDelagate, UUID, NetStatus, JsonStr) \
FBackDirectoryNetMsg NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FBackDirectoryNetMsg>(JsonStr, NetMsg); \
bool Success = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
CallDelagate.Broadcast(UUID, Success, NetMsg.resp)

#define PARSE_FRONT_DIRECTORY_NET_RESPONSE(CallDelagate, UUID, NetStatus, JsonStr) \
FFrontDirectoryNetMsg NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FFrontDirectoryNetMsg>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
CallDelagate.Broadcast(UUID, NetSuccess, NetMsg.resp)

#define PARSE_NET_JSON_DATA(MsgType, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg)

#define PARSE_NET_RESPONSE(MsgType, CallDelagate, UUID, NetStatus, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
UE_LOG(CatalogNetworkLog, Log, TEXT("PARSE_NET_RESPONSE --- success : %d"), NetSuccess); \
CallDelagate.Broadcast(UUID, NetSuccess, NetMsg.resp)

#define PARSE_NET_RESPONSE_MSG(MsgType, CallDelagate, UUID, NetStatus, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
UE_LOG(CatalogNetworkLog, Log, TEXT("PARSE_NET_RESPONSE_MSG --- success : %d"), NetSuccess); \
CallDelagate.Broadcast(UUID, NetSuccess, NetMsg.msg)

#define PARSE_NET_RESPONSE_RESP_MSG(MsgType, CallDelagate, UUID, NetStatus, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
UE_LOG(CatalogNetworkLog, Log, TEXT("PARSE_NET_RESPONSE_MSG --- success : %d"), NetSuccess); \
CallDelagate.Broadcast(UUID, NetSuccess, NetMsg.msg, NetMsg.resp)

#define PARSE_NET_RESPONSE_CONTENT_RESP_MSG(MsgType, CallDelagate, UUID, NetStatus, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
UE_LOG(CatalogNetworkLog, Log, TEXT("PARSE_NET_RESPONSE_MSG --- success : %d"), NetSuccess); \
CallDelagate.Broadcast(UUID, NetSuccess, NetMsg.msg, NetMsg.resp.total, NetMsg.resp.content)

#define PARSE_NET_RESPONSE_RESP_NO_SUCCESS(MsgType, CallDelagate, UUID, NetStatus, JsonStr) \
MsgType NetMsg; \
UJsonUtilitiesLibrary::ConvertJsonStringToStruct<MsgType>(JsonStr, NetMsg); \
bool NetSuccess = (NetStatus == CATALOG_NET_STATE_OK) && NetMsg.success; \
UE_LOG(CatalogNetworkLog, Log, TEXT("PARSE_NET_RESPONSE_RESP_NO_SUCCESS --- success : %d"), NetSuccess); \
CallDelagate.Broadcast(UUID, NetMsg.resp)