// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Marco.h"
#include "RefToAssociateLibrary.generated.h"

USTRUCT()
struct DESIGNSTATION_API FAssociateNetUUID
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString CreateUUID;

	UPROPERTY()
	FString DeleteUUID;

	UPROPERTY()
	FString UpdateUUID;

	UPROPERTY()
	FString QueryUUID;

	UPROPERTY()
	FString QueryShowUUID;

	UPROPERTY()
	FString QueryCateListUUID;

	UPROPERTY()
	FString QueryCateAssociateUUID;

public:
	FAssociateNetUUID()
		: CreateUUID(EMPTY_NET_MARK)
		, DeleteUUID(EMPTY_NET_MARK)
		, UpdateUUID(EMPTY_NET_MARK)
		, QueryUUID(EMPTY_NET_MARK)
		, QueryShowUUID(EMPTY_NET_MARK)
		, QueryCateListUUID(EMPTY_NET_MARK)
		, QueryCateAssociateUUID(EMPTY_NET_MARK)
	{}

	void ResetCreateAction() { CreateUUID = EMPTY_NET_MARK; }

	void ResetDeleteAction() { DeleteUUID = EMPTY_NET_MARK; }

	void ResetUpdateAction() { UpdateUUID = EMPTY_NET_MARK; }

	void ResetQueryAction() { QueryUUID = EMPTY_NET_MARK; }

	void ResetQueryShowAction() { QueryShowUUID = EMPTY_NET_MARK; }

	void ResetQueryCateListAction() { QueryCateListUUID = EMPTY_NET_MARK; }

	void ResetQueryCateAssociateAction() { QueryCateAssociateUUID = EMPTY_NET_MARK; }

};


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefAssociateData
{
	GENERATED_USTRUCT_BODY()

public:
	//数据库ID
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 id;

	//关联的数据名称
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString name;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString meetCondition;

	//0目录 1文件
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int32 type;

	//0材质 1定制模型 2成品模型
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int32 isMate;

	//关联类型
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString dictValue;

	//关联的数据ID
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString belongId;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 associationId;

	//关联给的定制文件ID
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString bkId;

public:
	FRefAssociateData()
		: id(INDEX_NONE)
		, name(TEXT(""))
		, meetCondition(TEXT(""))
		, type(INDEX_NONE)
		, isMate(INDEX_NONE)
		, dictValue(TEXT(""))
		, belongId(TEXT(""))
		, associationId(INDEX_NONE)
		, bkId(TEXT(""))
	{}
};

USTRUCT()
struct DESIGNSTATION_API FRefAssociateData_NetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FRefAssociateData> resp;

	UPROPERTY()
	bool success;

public:
	FRefAssociateData_NetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{}
};

/**
 * Represents the display data for an associated item.
 * This structure holds information necessary for displaying an associated item in the UI, including its ID, name, and images.
 * It is used to convey the visual representation of an item, such as a product or a map, along with its association ID for further reference.
 *
 * @param id The unique identifier for the associated item.
 * @param name The name of the associated item.
 * @param productImg The URL or path to the product image.
 * @param mapsImg The URL or path to the maps image.
 * @param associationId The ID of the association linked to the item. This can be used to fetch more detailed information or related items.
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FAssociateShowData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString id;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString name;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString productImg;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString mapsImg;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 associationId;

public:
	FAssociateShowData()
		: id(TEXT(""))
		, name(TEXT(""))
		, productImg(TEXT(""))
		, mapsImg(TEXT(""))
		, associationId(INDEX_NONE)
	{}
};

USTRUCT()
struct DESIGNSTATION_API FAssociateShowData_NetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FAssociateShowData> resp;

	UPROPERTY()
	bool success;

public:
	FAssociateShowData_NetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{}
};

/**
 * Represents a category list data for associated items.
 * This structure holds information about a category, including its ID, name, parent ID, and children categories.
 * It is used to organize associated items into a hierarchical structure, allowing for easier navigation and management of categories.
 *
 * @param id The unique identifier for the category.
 * @param name The name of the category.
 * @param pid The parent ID of the category, indicating the parent category it belongs to. A pid of INDEX_NONE indicates a top-level category.
 * @param children An array of FAssociateCategoryListData, representing the child categories under this category.
 */

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FAssociateCategoryListData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 id;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString name;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 pid;

    UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int64 associationId;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString updatedTime;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString createdTime;

public:
	FAssociateCategoryListData()
		: id(INDEX_NONE)
		, name(TEXT(""))
		, pid(INDEX_NONE)
		, associationId(0)
		, updatedTime(TEXT(""))
		, createdTime(TEXT(""))
	{
	}
};


USTRUCT()
struct DESIGNSTATION_API FAssociateCategoryListData_NetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FAssociateCategoryListData> resp;

	UPROPERTY()
	bool success;

public:
	FAssociateCategoryListData_NetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FAssociateCategoryData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString correlationType;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString correlationTypeName;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int32 isMate;

public:
	FAssociateCategoryData()
		: correlationType(TEXT(""))
		, correlationTypeName(TEXT(""))
		, isMate(0)
	{}

	FAssociateCategoryData(const FString& InType, const FString& InName, const int32& InModelMatFlag)
		: correlationType(InType)
		, correlationTypeName(InName)
		, isMate(InModelMatFlag)
	{}

};

/**
 * Represents the list data for associated items with additional correlation information.
 * This structure is used to hold a list of associated items, including their correlation type and material status.
 * It is particularly useful for organizing and displaying associated items based on their relationship to a primary entity.
 *
 * @param correlationType The type of correlation, indicating the relationship between the primary entity and the associated items.
 * @param correlationTypeName The human-readable name of the correlation type.
 * @param isMate The material status of the item, where 0 is material, 1 is a custom model, and 2 is a finished model.
 * @param bkAssociationDetailList An array of FRefAssociateData, representing the detailed list of associated items.
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FAssociateListData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString correlationType;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	FString correlationTypeName;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	int32 isMate;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Associate")
	TArray<FRefAssociateData> bkAssociationDetailList;

public:
	FAssociateListData()
		: correlationType(TEXT(""))
		, correlationTypeName(TEXT(""))
		, isMate(INDEX_NONE)
	{}
};


USTRUCT()
struct DESIGNSTATION_API FAssociateListData_NetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FAssociateListData> resp;

	UPROPERTY()
	bool success;

public:
	FAssociateListData_NetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API URefToAssociateLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
};