// Fill out your copyright notice in the Description page of Project Settings.

#include "RefToDirectoryDataLibrary.h"



bool URefToDirectoryDataLibrary::InsertDirectoryInfo(const FRefDirectoryData& InData)
{
	/*const FString SQL = FString::Printf(TEXT("INSERT INTO directory (ID, FOLDER_ID, FOLDER_NAME, FOLDER_CODE, FOLDER_CODE_EXP, THUMBNAIL_PATH, BACKEND_FOLDER_PATH, FRONT_FOLDER_PATH, VISIBILITY, VISIBILITY_EXP, DIR_ORDER, IS_FOLDER, IS_NEW, MD5, UPDATE_TIME) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s' , '%s', %f, '%s', %d, %d, %d, '%s', '%s')")
		, *InData.id, *InData.folderId, *InData.folderName, *InData.folderCode, *InData.folderCodeExp, *InData.thumbnailPath, *InData.backendFolderPath, *InData.fontFolderPath, InData.visibility, *InData.visibilityExp, InData.dirOrder, InData.isFolder, InData.isNew, *InData.md5, *InData.update_time);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
	return Res;*/
	return false;
}

bool URefToDirectoryDataLibrary::UpdateDirectoryInfo(const FRefDirectoryData& InData)
{
	return true;
}

bool URefToDirectoryDataLibrary::SelectDirectoryInfo(const FString& InID, FRefDirectoryData& OutData)
{
	/*const FString SQL = FString::Printf(TEXT("SELECT * FROM directory WHERE ID = '%s'"), *InID);
	TArray<FRefDirectoryData> Datas;
	if(FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FRefDirectoryData>(SQL, Datas) && Datas.Num() >0)
	{
		OutData = Datas[0];
	}*/
	return OutData.IsValid();
}
