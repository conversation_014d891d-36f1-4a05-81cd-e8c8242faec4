// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Marco.h"
#include "RefToDirectoryDataLibrary.generated.h"


struct FFolderTableData;

/*
 *  @@ 新的地址DB数据
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefDirectoryData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString id;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString folderId;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString folderName;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString folderNameExp;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString folderCode;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString folderCodeExp;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString thumbnailPath;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString backendFolderPath;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString fontFolderPath;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	float visibility;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString visibilityExp;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 dirOrder;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 isFolder;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 isNew;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString md5;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString createdBy;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString createdTime;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString updatedBy;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString updatedTime;

	//create user show
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString createdByName;

	//update user show
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString updatedByName;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString width;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString height;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString depth;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	FString boxOffset;

	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	int32 placeRule;


	//类型字段、值
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
    FString RefTypeCode;

    UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
    FString RefType;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString description;   //工艺描述

public:
	FRefDirectoryData()
		: id(FGuid::NewGuid().ToString())
		, folderId(TEXT(""))
		, folderName(TEXT(""))
		, folderNameExp(TEXT(""))
		, folderCode(TEXT(""))
		, folderCodeExp(TEXT(""))
		, thumbnailPath(TEXT(""))
		, backendFolderPath(TEXT(""))
		, fontFolderPath(TEXT(""))
		, visibility(1.0f)
		, visibilityExp(TEXT("1.0"))
		, dirOrder(0)
		, isFolder(0)
		, isNew(0)
		, md5(TEXT(""))
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
		, createdByName(TEXT(""))
		, updatedByName(TEXT(""))
		, width(TEXT("0"))
		, height(TEXT("0"))
		, depth(TEXT("0"))
		, boxOffset(FVector::ZeroVector.ToString())
		, placeRule(INDEX_NONE)
		, RefTypeCode(TEXT(""))
        , RefType(TEXT(""))
		, description(TEXT(""))
	{}

	FRefDirectoryData(const FString& InID)
		: id(InID)
		, folderId(TEXT(""))
		, folderName(TEXT(""))
		, folderNameExp(TEXT(""))
		, folderCode(TEXT(""))
		, folderCodeExp(TEXT(""))
		, thumbnailPath(TEXT(""))
		, backendFolderPath(TEXT(""))
		, fontFolderPath(TEXT(""))
		, visibility(1.0f)
		, visibilityExp(TEXT("1.0"))
		, dirOrder(0)
		, isFolder(0)
		, isNew(0)
		, md5(TEXT(""))
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
		, createdByName(TEXT(""))
		, updatedByName(TEXT(""))
		, width(TEXT("0"))
		, height(TEXT("0"))
		, depth(TEXT("0"))
		, boxOffset(FVector::ZeroVector.ToString())
		, placeRule(INDEX_NONE)
		, RefTypeCode(TEXT(""))
        , RefType(TEXT(""))
		, description(TEXT(""))
	{}

	bool IsValid() const { return !id.IsEmpty(); }

	void NoValid() { id.Empty(); }
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefDirectoryDataArr
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Ref Directory")
	TArray<FRefDirectoryData> DirectoryArr;

public:
	FRefDirectoryDataArr() {}

	FRefDirectoryDataArr(const TArray<FRefDirectoryData>& InDatas)
		: DirectoryArr(InDatas)
	{}

	void Sort()
	{
		DirectoryArr.Sort(
			[](const FRefDirectoryData& A, const FRefDirectoryData& B)->bool 
			{
				return A.dirOrder <= B.dirOrder;
			}
		);
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefDirectoryDataContent
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString PID;

	UPROPERTY()
	TArray<FRefDirectoryData> CDataArr;
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API URefToDirectoryDataLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/*
	 *  @@ 规范化后台目录的字段, 暂不使用
	 *	@@ 文件用folder_id代替id
	 */
	static FString FormatBackendFolderPath(const FRefDirectoryData& InData) { return TEXT(""); }

public:
	//测试使用的DB逻辑, TODO : 接口
	static bool InsertDirectoryInfo(const FRefDirectoryData& InData);

	static bool UpdateDirectoryInfo(const FRefDirectoryData& InData);

	static bool SelectDirectoryInfo(const FString& InID, FRefDirectoryData& OutData);
};

USTRUCT()
struct DESIGNSTATION_API FBackDirectoryNetMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FRefDirectoryData> resp;

	UPROPERTY()
	bool success;

public:
	FBackDirectoryNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseContent
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	int32 total;

	UPROPERTY()
	TArray<FRefDirectoryData> content;

public:
	FReleaseContent()
		: total(0)
		, content(TArray<FRefDirectoryData>())
	{
	}
};

USTRUCT()
struct DESIGNSTATION_API FReleaseNetMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	FReleaseContent resp;

	UPROPERTY()
	bool success;

public:
	FReleaseNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{
	}
};

USTRUCT()
struct DESIGNSTATION_API FBackendDirectoryNetUUID
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString AddUUID;

	UPROPERTY()
	FString DeleteUUID;

	UPROPERTY()
	FString UpdateUUID;

	UPROPERTY()
	FString SearchUUID;

	UPROPERTY()
	FString CopyUUID;

	UPROPERTY()
	FString CutUUID;

	UPROPERTY()
	FString DownloadImage;

	UPROPERTY()
	FString UploadImage;

	UPROPERTY()
	FString DownloadFile;

	UPROPERTY()
	FString UploadFile;

	UPROPERTY()
	FString SwapUUID;

	UPROPERTY()
	FString ReleaseInfoUUID;

	UPROPERTY()
	FString ReleaseSelectUUID;

	UPROPERTY()
	TArray<FString> SyncUUIDs;

	UPROPERTY()
	FString ModelURLDownloadUUID;

	UPROPERTY()
	FString MatURLDownloadUUID;

	UPROPERTY()
	FString DownloadStyleUUID;

	UPROPERTY()
	FString ObsurceSearchUUID;
	UPROPERTY()
	FString ObsurceSearchExpandUUID;

	//place rule
	UPROPERTY()
	FString PlaceRuleSearchUUID;

public:
	FBackendDirectoryNetUUID()
		: AddUUID(EMPTY_NET_MARK)
		, DeleteUUID(EMPTY_NET_MARK)
		, UpdateUUID(EMPTY_NET_MARK)
		, SearchUUID(EMPTY_NET_MARK)
		, CopyUUID(EMPTY_NET_MARK)
		, CutUUID(EMPTY_NET_MARK)
		, DownloadImage(EMPTY_NET_MARK)
		, UploadImage(EMPTY_NET_MARK)
		, DownloadFile(EMPTY_NET_MARK)
		, UploadFile(EMPTY_NET_MARK)
		, SwapUUID(EMPTY_NET_MARK)
		, ReleaseInfoUUID(EMPTY_NET_MARK)
		, ReleaseSelectUUID(EMPTY_NET_MARK)
		, SyncUUIDs(TArray<FString>())
		, ModelURLDownloadUUID(EMPTY_NET_MARK)
		, MatURLDownloadUUID(EMPTY_NET_MARK)
		, DownloadStyleUUID(EMPTY_NET_MARK)
		, ObsurceSearchUUID(EMPTY_NET_MARK)
		, ObsurceSearchExpandUUID(EMPTY_NET_MARK)
		, PlaceRuleSearchUUID(EMPTY_NET_MARK)
	{
	}


	void ResetAddAction() { AddUUID = EMPTY_NET_MARK; }

	void ResetDeleteAction() { DeleteUUID = EMPTY_NET_MARK; }

	void ResetUpdateAction() { UpdateUUID = EMPTY_NET_MARK; }

	void ResetSearchAction() { SearchUUID = EMPTY_NET_MARK; }

	void ResetCopyAction() { CopyUUID = EMPTY_NET_MARK; }

	void ResetCutAction() { CutUUID = EMPTY_NET_MARK; }

	void ResetDownloadImageAction() { DownloadImage = EMPTY_NET_MARK; }

	void ResetUploadImageAction() { UploadImage = EMPTY_NET_MARK; }

	void ResetDownloadFileAction() { DownloadFile = EMPTY_NET_MARK; }

	void ResetUploadFileAction() { UploadFile = EMPTY_NET_MARK; }

	void ResetSwapAction() { SwapUUID = EMPTY_NET_MARK; }

	void ResetReleaseInfoAction() { ReleaseInfoUUID = EMPTY_NET_MARK; }

	void ResetReleaseSelectAction() { ReleaseSelectUUID = EMPTY_NET_MARK; }

	void ResetModelURLDownloadAction() { ModelURLDownloadUUID = EMPTY_NET_MARK; }
	bool IsModelURLDownloadFinish() { return  ModelURLDownloadUUID.Equals(EMPTY_NET_MARK); }

	void ResetMatURLDownloadAction() { MatURLDownloadUUID = EMPTY_NET_MARK; }
	bool IsMatURLDownloadFinish() { return  MatURLDownloadUUID.Equals(EMPTY_NET_MARK); }

	void ResetDownloadStyleAction() { DownloadStyleUUID = EMPTY_NET_MARK; }

	void ResetObsurceSearchAction() { ObsurceSearchUUID = EMPTY_NET_MARK; }

	void ResetObsurceSearchExpandAction() { ObsurceSearchExpandUUID = EMPTY_NET_MARK; }

	void ResetPlaceRuleSearchAction() { PlaceRuleSearchUUID = EMPTY_NET_MARK; }

};

USTRUCT()
struct DESIGNSTATION_API FCatalogParamNetUUID
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString DownloadUUID;

	UPROPERTY()
	FString UploadUUID;

	UPROPERTY()
	FString ReleaseUUID;

public:
	FCatalogParamNetUUID()
		: DownloadUUID(EMPTY_NET_MARK)
		, UploadUUID(EMPTY_NET_MARK)
		, ReleaseUUID(EMPTY_NET_MARK)
	{
	}

	void ResetDownloadAction() { DownloadUUID = EMPTY_NET_MARK; }

	void ResetUploadAction() { UploadUUID = EMPTY_NET_MARK; }

	void ResetReleaseAction() { ReleaseUUID = EMPTY_NET_MARK; }
};

