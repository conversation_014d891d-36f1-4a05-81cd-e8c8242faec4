// Fill out your copyright notice in the Description page of Project Settings.


#include "RefToFileData.h"

void FComponentFileData::Init(const TArray<FSingleComponentItem>& InComponentDatas)
{
	component_datas = InComponentDatas;
}

void FComponentFileData::Init(const FSingleComponentProperty& InComponentData)
{
	component_datas = InComponentData.ComponentItems;
}

void FRefToFileComponentData::Init(const FMultiComponentDataItem& MultiRefData)
{
	component_id = MultiRefData.ComponentID;
	component_name = MultiRefData.ComponentName;
	component_description = MultiRefData.Description;
	component_code.Expression = MultiRefData.CodeExp;
	component_code.Value = MultiRefData.Code;
	component_visibility = MultiRefData.ComponentVisibility;
	component_location = MultiRefData.ComponentLocation;
	component_rotation = MultiRefData.ComponentRotation;
	component_scale = MultiRefData.ComponentScale;
	component_params = MultiRefData.ComponentParameters;
	component_type = static_cast<int32>(MultiRefData.ComponentType);
	component_ref_uuid = MultiRefData.RefFileUUID;
	component_name_exp = MultiRefData.ComponentNameExp;
}

FMultiComponentDataItem FRefToFileComponentData::ConvertToMultiComponentDataItem() const
{
	FMultiComponentDataItem OutComponentData;
	OutComponentData.ComponentID = component_id;
	OutComponentData.ComponentName = component_name;
	OutComponentData.Description = component_description;
	OutComponentData.CodeExp = component_code.Expression;
	OutComponentData.Code = component_code.Value;
	OutComponentData.ComponentVisibility = component_visibility;
	OutComponentData.ComponentLocation = component_location;
	OutComponentData.ComponentRotation = component_rotation;
	OutComponentData.ComponentScale = component_scale;
	OutComponentData.ComponentParameters = component_params;

	OutComponentData.ComponentType = static_cast<ECompType>(component_type);

	OutComponentData.RefFileUUID = component_ref_uuid;

	OutComponentData.ComponentNameExp = component_name_exp;

	return OutComponentData;
}

bool FRefToFileComponentData::IsValid() const
{
	return !component_id.Value.IsEmpty() && component_id.Value.IsNumeric();
}

void FDependFileData::AddDependFile_Model(const FCSModelMatData& InDependFile)
{
	const int32 Index = ModelDependFiles.IndexOfByPredicate(
		[InDependFile](const FCSModelMatData& ArrItem)->bool
		{
			return InDependFile.id == ArrItem.id;
		}
	);
	if (Index == INDEX_NONE)
	{
		ModelDependFiles.Add(InDependFile);
	}
	else
	{
		ModelDependFiles[Index] = InDependFile;
	}
}

bool FDependFileData::GetDependFile_Model(const FString& InFodlerID, FCSModelMatData& OutData) const
{
	const int32 Index = ModelDependFiles.IndexOfByPredicate(
		[InFodlerID](const FCSModelMatData& ArrItem)->bool
		{
			return InFodlerID.Equals(ArrItem.folderId);
		}
	);
	if (Index != INDEX_NONE)
	{
		OutData = ModelDependFiles[Index];
		return true;
	}
	return false;
}

bool FDependFileData::GetDependFile_Mat(const FString& InFodlerID, FCSModelMatData& OutData) const
{
	const int32 Index = MatDependFiles.IndexOfByPredicate(
		[InFodlerID](const FCSModelMatData& ArrItem)->bool
		{
			return InFodlerID.Equals(ArrItem.folderId);
		}
	);
	if (Index != INDEX_NONE)
	{
		OutData = MatDependFiles[Index];
		return true;
	}
	return false;
}

void FDependFileData::AddDependFolderID_Model(const FExpressionValuePair& InFolderID)
{
	ModelDependFolderID.AddUnique(InFolderID);
}

TArray<FExpressionValuePair> FDependFileData::GetDependFolderID_Model()
{
	return ModelDependFolderID;
}

void FDependFileData::RemoveDependFileIDs_Model(const TArray<FExpressionValuePair>& InIDs)
{
	for (auto II : InIDs)
	{
		const int32 Index = ModelDependFolderID.IndexOfByPredicate(
			[II](FExpressionValuePair FEVP)->bool
			{
				return FEVP.Equals_Precise(II);
			}
		);
		if (Index != INDEX_NONE)
		{
			ModelDependFolderID.RemoveAt(Index);
		}
	}
}

bool FDependFileData::ModelAlreadyHas(const FString& InFolderID)
{
	const int32 Index = ModelDependFolderID.IndexOfByPredicate(
		[InFolderID](const FExpressionValuePair& ArrItem)->bool
		{
			const double InID = FCString::Atod(*InFolderID);
			const double ArrID = FCString::Atod(*ArrItem.Value);
			return InFolderID.Equals(ArrItem.Value) || FMath::IsNearlyEqual(InID, ArrID, 0.1f);
		}
	);

	return Index != INDEX_NONE;
}

void FDependFileData::AddDependFolderID_Mat(const FExpressionValuePair& InFolderID)
{
	MatDependFolderID.AddUnique(InFolderID);
}

TArray<FExpressionValuePair> FDependFileData::GetDependFolderID_Mat()
{
	return MatDependFolderID;
}

bool FDependFileData::MatAlreadyHas(const FString& InFolderID)
{
	const int32 Index = MatDependFolderID.IndexOfByPredicate(
		[InFolderID](const FExpressionValuePair& ArrItem)->bool
		{
			const double InID = FCString::Atod(*InFolderID);
			const double ArrID = FCString::Atod(*ArrItem.Value);
			return InFolderID.Equals(ArrItem.Value) || FMath::IsNearlyEqual(InID, ArrID, 0.1f);
		}
	);

	return Index != INDEX_NONE;
}

bool FDependFileData::MatFileAlreadyHas(const FString& InFolderID)
{
	const int32 Index = MatDependFiles.IndexOfByPredicate(
		[InFolderID](const FCSModelMatData& ArrItem)->bool
		{
			const double InID = FCString::Atod(*InFolderID);
			const double ArrID = FCString::Atod(*ArrItem.folderId);
			return InFolderID.Equals(ArrItem.folderId) || FMath::IsNearlyEqual(InID, ArrID, 0.1f);
		}
	);

	return Index != INDEX_NONE;
}

void FDependFileData::AddDependFile_Mat(const FCSModelMatData& InDependFile)
{
	const int32 Index = MatDependFiles.IndexOfByPredicate(
		[InDependFile](const FCSModelMatData& ArrItem)->bool
		{
			return InDependFile.id == ArrItem.id;
		}
	);
	if (Index == INDEX_NONE)
	{
		MatDependFiles.Add(InDependFile);
	}
	else
	{
		MatDependFiles[Index] = InDependFile;
	}

	MatDependFolderID.Add(FExpressionValuePair(InDependFile.folderId, InDependFile.folderId));
}

void FDependFileData::AddDependFile(const FCSModelMatData& InDependFile)
{
	if (InDependFile.IsPakFile())
	{
		AddDependFile_Model(InDependFile);
	}
	else if (InDependFile.IsMaterialFile())
	{
		AddDependFile_Mat(InDependFile);
	}
}

void FDependFileData::AddDependFiles(const TArray<FCSModelMatData>& InDependFiles)
{
	for (const auto& DependFile : InDependFiles)
	{
		AddDependFile(DependFile);
	}
}

void FDependFileData::Clear()
{
	ModelDependFiles.Empty();
	MatDependFiles.Empty();
	ModelDependFolderID.Empty();
	MatDependFolderID.Empty();
}

void FDependFileData::ClearDetailData()
{
	ModelDependFiles.Empty();
	MatDependFiles.Empty();
}

bool FDependFileData::GetDependFile(const FString& InFodlerID, FCSModelMatData& OutData) const
{
	bool Res = GetDependFile_Model(InFodlerID, OutData);
	if (!Res)
	{
		Res = GetDependFile_Mat(InFodlerID, OutData);
	}

	return Res;
}

bool FDependFileData::GetNeedDownload_Model(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath)
{
	for (auto MDF : ModelDependFiles)
	{
		TArray<FString> TempFilePath;
		TArray<FString> TempDownloadFilePath;
		if (MDF.AnalysisData(TempFilePath, TempDownloadFilePath))
		{
			FilePath.Append(TempFilePath);
			DownloadFilePath.Append(TempDownloadFilePath);
		}
	}

	return DownloadFilePath.Num() > 0;
}

bool FDependFileData::GetNeedDownload_Mat(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath, TArray<FCSModelMatData>& NeedDownload)
{
	for (auto MDF : MatDependFiles)
	{
		TArray<FString> TempFilePath;
		TArray<FString> TempDownloadFilePath;
		if (MDF.AnalysisData(TempFilePath, TempDownloadFilePath))
		{
			FilePath.Append(TempFilePath);
			DownloadFilePath.Append(TempDownloadFilePath);

			NeedDownload.Add(MDF);
		}	
	}

	TArray<FString> FinalFilePath;
	for (const auto& TDFP : DownloadFilePath)
	{
		FString FileName = FPaths::GetBaseFilename(TDFP);

		TArray<FString> PathStr;
		TDFP.ParseIntoArray(PathStr, TEXT("/"));
		if (PathStr.Num() >= 2)
		{
			FileName = PathStr.Last(1) + TEXT("/") + PathStr.Last();
		}

		int32 Index = FilePath.IndexOfByPredicate(
			[FileName](const FString& InPath)->bool
			{ 
				return InPath.Contains(FileName); 
			}
		);
		if (Index != INDEX_NONE)
		{
			FinalFilePath.Add(FilePath[Index]);
		}
	}
	FilePath = FinalFilePath;

	return DownloadFilePath.Num() > 0;
}

FRefToLocalFileData::FRefToLocalFileData()
{
}

FRefToLocalFileData::FRefToLocalFileData(const FCatalogFolderDataDB& InFolderData, const TArray<FParameterData>& InParameterDatas)
	: FolderDBData(InFolderData)
	, ParamDatas(InParameterDatas)
{
}

void FRefToLocalFileData::Init(const FCatalogFolderDataDB& InFolderData, const TArray<FParameterData>& InParameterDatas)
{
	FolderDBData = InFolderData;
	ParamDatas = InParameterDatas;
}

void FRefToLocalFileData::Init(const TArray<FRefToFileComponentData>& RefComponentsData)
{
	ComponentDatas = RefComponentsData;
}

void FRefToLocalFileData::Init(const FComponentFileData& InFileData)
{
	FileData = InFileData;
}

int32 FRefToLocalFileData::GetComponentTypeByData()
{
	//return static_cast<int32>(ECompType::...);
	if (ComponentDatas.Num() > 0)
	{
		return static_cast<int32>(ECompType::MultiCom);
	}
	else if (FileData.HasData())
	{
		return static_cast<int32>(ECompType::SingleCom);
	}

	return static_cast<int32>(ECompType::None);
}

TArray<FRefToFileComponentData> URefToFileData::ConvertToRefComponentsData(const TArray<FMultiComponentDataItem>& InComponentDatas)
{
	TArray<FRefToFileComponentData> OutComponentDatas;
	OutComponentDatas.Reserve(InComponentDatas.Num());
	for (const auto& ComponentData : InComponentDatas)
	{
		OutComponentDatas.Emplace();
		OutComponentDatas.Last().Init(ComponentData);
	}
	return OutComponentDatas;
}

TArray<FMultiComponentDataItem> URefToFileData::ConvertToMultiComponentData(const TArray<FRefToFileComponentData>& InRefComponentsData)
{
	TArray<FMultiComponentDataItem> OutComponentDatas;
	OutComponentDatas.Reserve(InRefComponentsData.Num());
	for (const auto& ComponentData : InRefComponentsData)
	{
		OutComponentDatas.Emplace();
		OutComponentDatas.Last() = ComponentData.ConvertToMultiComponentDataItem();
	}
	return OutComponentDatas;
}

