// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontFolderDataLibrary.h"
#include "RefToPlaceDataLibrary.h"
#include "DataCenter/ComponentData/ComponentPropertyData.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "RefToFileData.generated.h"

//dat文件中关于文件夹或文件的外层的数据
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FCatalogFolderDataDB
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")    //数据库id
		FString id;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")    //文件id，引用
		FString folder_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString folder_name;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString folder_name_exp;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString folder_code;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString folder_code_exp;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		int32 folder_type;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString thumbnail_path;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString parent_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		float visibility;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		int32 folder_order;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		bool is_new;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		bool is_folder;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString backend_directory;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString front_directory;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		int32 place_rule;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString ref_type_code;
    UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
        FString ref_type;

	//备注说明
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | FolderOrFile")
		FString description;

public:
	FCatalogFolderDataDB()
	: id(TEXT(""))
	, folder_id(TEXT(""))
	, folder_name(TEXT(""))
	, folder_name_exp(TEXT(""))
	, folder_code(TEXT(""))
	, folder_code_exp(TEXT(""))
	, folder_type(INDEX_NONE)
	, thumbnail_path(TEXT(""))
	, parent_id(TEXT(""))
	, visibility_exp(TEXT(""))
	, visibility(0.0f)
	, folder_order(INDEX_NONE)
	, is_new(false)
	, is_folder(false)
	, backend_directory(TEXT(""))
	, front_directory(TEXT(""))
	, place_rule(INDEX_NONE)
	, ref_type_code(TEXT(""))
	, ref_type(TEXT(""))
	, description(TEXT(""))
	{}

	FCatalogFolderDataDB(
		const FString& InID,
		const FString& InFolderID = TEXT(""),
		const FString& InFolderName = TEXT(""),
		const FString& InFolderCode = TEXT(""),
		const FString& InFolderCodeExp = TEXT(""),
		const int32& InFolderType = INDEX_NONE,
		const FString& InThumbnailPath = TEXT(""),
		const FString& InParentID = TEXT(""),
		const FString& InVisibilityExp = TEXT(""),
		const float& InVisibility = 0.0f,
		const int32& InFolderOrder = INDEX_NONE,
		const bool& InIsNew = false,
		const bool& InIsFolder = false,
		const FString& InBackendDirectory = TEXT(""),
		const FString& InFrontDirectory = TEXT(""),
		const int32& InPlaceRule = INDEX_NONE,
		const FString& InRefTypeCode = TEXT(""),
		const FString& InRefType = TEXT(""),
		const FString& InDes = TEXT("")
		)
	: id(InID)
	, folder_id(InFolderID)
	, folder_name(InFolderName)
	, folder_name_exp(InFolderName)
	, folder_code(InFolderCode)
	, folder_code_exp(InFolderCodeExp)
	, folder_type(InFolderType)
	, thumbnail_path(InThumbnailPath)
	, parent_id(InParentID)
	, visibility_exp(InVisibilityExp)
	, visibility(InVisibility)
	, folder_order(InFolderOrder)
	, is_new(InIsNew)
	, is_folder(InIsFolder)
	, backend_directory(InBackendDirectory)
	, front_directory(InFrontDirectory)
	, place_rule(InPlaceRule)
	, ref_type_code(InRefTypeCode)
	, ref_type(InRefType)
	, description(InDes)
	{}

	void CopyData(
		const FString& InID,
		const FString& InFolderID = TEXT(""),
		const FString& InFolderName = TEXT(""),
		const FString& InFolderNameExp = TEXT(""),
		const FString& InFolderCode = TEXT(""),
		const FString& InFolderCodeExp = TEXT(""),
		const int32& InFolderType = INDEX_NONE,
		const FString& InThumbnailPath = TEXT(""),
		const FString& InParentID = TEXT(""),
		const FString& InVisibilityExp = TEXT(""),
		const float& InVisibility = 0.0f,
		const int32& InFolderOrder = INDEX_NONE,
		const bool& InIsNew = false,
		const bool& InIsFolder = false,
		const FString& InBackendDirectory = TEXT(""),
		const FString& InFrontDirectory = TEXT(""),
		const int32& InPlaceRule = INDEX_NONE,
		const FString& InRefTypeCode = TEXT(""),
		const FString& InRefType = TEXT(""),
		const FString& InDes = TEXT("")
	)
	{
		id = InID;
		folder_id = InFolderID;
		folder_name = InFolderName;
		folder_name_exp = InFolderNameExp;
		folder_code = InFolderCode;
		folder_code_exp = InFolderCodeExp;
		folder_type = InFolderType;
		thumbnail_path = InThumbnailPath;
		parent_id = InParentID;
		visibility_exp = InVisibilityExp;
		visibility = InVisibility;
		folder_order = InFolderOrder;
		is_new = InIsNew;
		is_folder = InIsFolder;
		backend_directory = InBackendDirectory;
		front_directory = InFrontDirectory;
		place_rule = InPlaceRule;
        ref_type_code = InRefTypeCode;
        ref_type = InRefType;
		description = InDes;
	}

	bool IsValid() const
	{
		return !id.IsEmpty();
	}
	bool IsValidForFileRef() const
	{
		return IsValid() &&!folder_id.IsEmpty();
	}

	void UpdateProperty(
		const FString& InFolderID,
		const FString& InFolderName,
		const FString& InFolderNameExp,
		const FString& InFolderCode,
		const FString& InFolderCodeExp,
		const FString& InVisibilityExp,
		const float& InVisibility,
		const bool& InIsNew,
		const FString& InFrontDirectory,
		const FString& InBackendDirectory,
		const FString& InDescription
	)
	{
		folder_id = InFolderID;
		folder_name = InFolderName;
		folder_name_exp = InFolderNameExp;
		folder_code = InFolderCode;
		folder_code_exp = InFolderCodeExp;
		visibility_exp = InVisibilityExp;
		visibility = InVisibility;
		is_new = InIsNew;
		front_directory = InFrontDirectory;
		backend_directory = InBackendDirectory;
		description = InDescription;
	}

};

//具体数据（原单部件文件引用及数据）
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FComponentFileData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component File")
		FString file_data_path;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component File")
		FString depend_files;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component File")
		TArray<FSingleComponentItem> component_datas;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component File")
		FString file_ref_uuid;


public:
	void Init(const TArray<FSingleComponentItem>& InComponentDatas);
	void Init(const FSingleComponentProperty& InComponentData);

	bool HasData()
	{
		return !component_datas.IsEmpty();
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToFileComponentData
{
	GENERATED_USTRUCT_BODY()

public:
	//引用关系
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FExpressionValuePair component_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FString component_name;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FString component_description;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FExpressionValuePair component_code;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FExpressionValuePair component_visibility;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FLocationProperty component_location;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FRotationProperty component_rotation;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FScaleProperty component_scale;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		TArray<FParameterData> component_params;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		int32 component_type;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FString component_ref_uuid;

	UPROPERTY(BlueprintReadWrite, Category = "RefFile | Component")
		FString component_name_exp;

public:
	FRefToFileComponentData()
	: component_id(FExpressionValuePair(TEXT("")))
	, component_name(TEXT(""))
	, component_description(TEXT(""))
	, component_code(FExpressionValuePair(TEXT("")))
	, component_visibility(FExpressionValuePair(TEXT("")))
	, component_location(FLocationProperty(FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT(""))))
	, component_rotation(FRotationProperty(FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT(""))))
	, component_scale(FScaleProperty(FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT("")), FExpressionValuePair(TEXT(""))))
	, component_params(TArray<FParameterData>())
	, component_type(INDEX_NONE)
	, component_ref_uuid(TEXT(""))
	, component_name_exp(TEXT(""))
	{}

	void Init(const FMultiComponentDataItem& MultiRefData);

	FMultiComponentDataItem ConvertToMultiComponentDataItem() const;

	bool IsValid() const;
	
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FDependFileData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
		TArray<FCSModelMatData> ModelDependFiles;

	UPROPERTY()
		TArray<FCSModelMatData> MatDependFiles;

	UPROPERTY()
		TArray<FExpressionValuePair> ModelDependFolderID;

	UPROPERTY()
		TArray<FExpressionValuePair> MatDependFolderID;

private:
	void AddDependFile_Model(const FCSModelMatData& InDependFile);
	bool GetDependFile_Model(const FString& InFodlerID, FCSModelMatData& OutData) const;
	void AddDependFile_Mat(const FCSModelMatData& InDependFile);
	bool GetDependFile_Mat(const FString& InFodlerID, FCSModelMatData& OutData) const;

public:
	FDependFileData()
	: ModelDependFiles(TArray<FCSModelMatData>())
	, MatDependFiles(TArray<FCSModelMatData>())
	{}

	void AddDependFile(const FCSModelMatData& InDependFile);
	void AddDependFiles(const TArray<FCSModelMatData>& InDependFiles);

	void Clear();
	void ClearDetailData();

	bool GetDependFile(const FString& InFodlerID, FCSModelMatData& OutData) const;

	TArray<FCSModelMatData> GetModelDependFiles() const { return ModelDependFiles; }
	TArray<FCSModelMatData> GetMatDependFiles() const { return MatDependFiles; }

	bool GetNeedDownload_Model(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath);
	bool GetNeedDownload_Mat(TArray<FString>& FilePath, TArray<FString>& DownloadFilePath, TArray<FCSModelMatData>& NeedDownload);

	void AddDependFolderID_Model(const FExpressionValuePair& InFolderID);
	void SetDependFolderID_Model(const TArray<FExpressionValuePair>& InFolderIDs) { ModelDependFolderID = InFolderIDs;}
	TArray<FExpressionValuePair> GetDependFolderID_Model();
	void RemoveDependFileIDs_Model(const TArray<FExpressionValuePair>& InIDs);

	bool ModelAlreadyHas(const FString& InFolderID);
	void AddDependFolderID_Mat(const FExpressionValuePair& InFolderID);
	void SetDependFolderID_Mat(const TArray<FExpressionValuePair>& InFolderIDs) { MatDependFolderID = InFolderIDs; }
	TArray<FExpressionValuePair> GetDependFolderID_Mat();
	bool MatAlreadyHas(const FString& InFolderID);
	bool MatFileAlreadyHas(const FString& InFolderID);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToLocalFileData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		FCatalogFolderDataDB FolderDBData;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		TArray<FParameterData> ParamDatas;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		TArray<FRefToFileComponentData> ComponentDatas;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		FComponentFileData FileData;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		FDependFileData MatModelDependFiles;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		FRefPlaceRuleCustomData PlaceRuleCustomData;
	UPROPERTY(BlueprintReadWrite, Category = "RefFile")
		TArray<FAssociateListData> AssociateListData;

public:
	FRefToLocalFileData();
	FRefToLocalFileData(const FCatalogFolderDataDB& InFolderData, const TArray<FParameterData>& InParameterDatas);
	void Init(const FCatalogFolderDataDB& InFolderData, const TArray<FParameterData>& InParameterDatas);
	void Init(const TArray<FRefToFileComponentData>& RefComponentsData);
	void Init(const FComponentFileData& InFileData);

	bool IsValid() const
	{
		return FolderDBData.IsValid();
	}

	bool HasPlaceRuleUserCustom() const
	{
		if (PlaceRuleCustomData.IsValid())
		{
			return PlaceRuleCustomData.IsCustomByUser();
		}
		return false;
	}

	int32 UserCustomPlaceRuleConfigFalg() const
	{
		return PlaceRuleCustomData.isConfig;
	}

	void Clear()
	{
		FolderDBData = FCatalogFolderDataDB();
		ParamDatas.Empty();
		ComponentDatas.Empty();
		FileData = FComponentFileData();
		MatModelDependFiles.Clear();
	}

	int32 GetComponentTypeByData();
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API URefToFileData : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static FString GetFileRelativeAddress(const FString& InID)
	{
		FString RefFolderName = TEXT("RefFiles");
		const FString RefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefFolderName));
		if (!IFileManager::Get().DirectoryExists(*RefFolder))
		{
			IFileManager::Get().MakeDirectory(*RefFolder, true);
		}
		return FPaths::Combine(RefFolderName, FString::Printf(TEXT("%s.dat"), *InID));
	}

	//get address 
	static FString GetFileAddress(const FString& InID)
	{
		FString RelativePath = URefToFileData::GetFileRelativeAddress(InID);
		return FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelativePath)); 
	}

	//get temp save
	static FString GetTempSaveFolderRelativeAddress(const FString& InID)
	{
		return FPaths::Combine(TEXT("RefFiles"), TEXT("TempSave"), InID);
	}
	static FString GetTempSaveFolderRelativeAddress(const FString& InID, const FString& InData, const FString& InTime)
	{
		FString RefFolderName = FPaths::Combine(TEXT("RefFiles"), TEXT("TempSave"), InID);
		const FString RefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefFolderName));
		if (!IFileManager::Get().DirectoryExists(*RefFolder))
		{
			IFileManager::Get().MakeDirectory(*RefFolder, true);
		}
		const FString TempFileName = FString::Printf(TEXT("%s-%s.dat"), *InID, *InTime);;
		return FPaths::Combine(RefFolderName, TempFileName);
	}
	static FString GetAutoTempSaveFileRelativeAddress(const FString& InID, const FString& InFolderID, const FString& InName, const FString& InData, const FString& InTime )
	{
		FString RefFolderName = FPaths::Combine(TEXT("CatalogAutoSave"), InData);
		const FString RefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), RefFolderName));
		if (!IFileManager::Get().DirectoryExists(*RefFolder))
		{
			IFileManager::Get().MakeDirectory(*RefFolder, true);
		}
		const FString TempFileName = FString::Printf(TEXT("%s-%s-%s-%s.dat"), *InID, *InFolderID, *InName, *InTime);;
		return FPaths::Combine(RefFolderName, TempFileName);
	}

	//转换部件引用关系数组
	static TArray<FRefToFileComponentData> ConvertToRefComponentsData(const TArray<FMultiComponentDataItem>& InComponentDatas);
	static TArray<FMultiComponentDataItem> ConvertToMultiComponentData(const TArray<FRefToFileComponentData>& InRefComponentsData);
	
};
