// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "RefToParamDataLibrary.generated.h"


/*
*  @@ Global Params
*/
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefParamData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	TArray<FParameterGroupTableData> ParamGroups;

	UPROPERTY()
	TArray<FParameterData> ParamDatas;

public:
	void Init(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Params)
	{
		Params.GenerateValueArray(ParamDatas);
	}
	void Init(const TArray<FParameterGroupTableData>& Group)
	{
		ParamGroups = Group;
	}

	void Init(const TArray<FParameterData>& Params)
	{
		ParamDatas = Params;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GenerateParamsMap()
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParamsMap;
		for (const FParameterData& Param : ParamDatas)
		{
			ParamsMap.Add(Param.Data.name, Param);
		}
		return ParamsMap;
	}
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API URefToParamDataLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static FString GetRelativePath()
	{
		FString RefFolderName = TEXT("RefFiles");
		const FString RefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefFolderName));
		if (!IFileManager::Get().DirectoryExists(*RefFolder))
		{
			IFileManager::Get().MakeDirectory(*RefFolder, true);
		}
		return FPaths::Combine(RefFolderName, TEXT("Param.dat"));
	}

	static FString GetRefParamsAddress()
	{
		return FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), GetRelativePath()));
	}

	static FString GetAutoTempSaveAddress(const FString& InData, const FString& InTime)
	{
		FString BackupFolderName = FPaths::Combine(TEXT("CatalogAutoSave"), InData);
		const FString BackupRefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), BackupFolderName));
		if (!IFileManager::Get().DirectoryExists(*BackupRefFolder))
		{
			IFileManager::Get().MakeDirectory(*BackupRefFolder, true);
		}
		FString BackupFileName = FString::Printf(TEXT("Param-%s.dat"), *InTime);
		return FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), BackupFolderName, BackupFileName));
	}
	
};
