// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "RefToPlaceDataLibrary.generated.h"


/**
 *  @@ place rule data struct
 *  @@ all control flag : 0 is true; 1 is false
 */
USTRUCT(BlueprintType, Blueprintable)
struct DESIGNSTATION_API FRefPlaceRuleData
{
	GENERATED_USTRUCT_BODY()

public:
	//主键
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 id;

	//规则名称
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString name;

	//是否启用
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isEnable;

	//是否支持配置设置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isConfig;

	//是否左配置, 左配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isLeft;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString leftConfig;

	//是否右配置, 右配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isRight;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString rightConfig;

	//是否上配置, 上配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isUpper;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString upperConfig;

	//是否下配置, 下配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isDown;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString downConfig;

	//是否前配置, 前配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isFront;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString frontConfig;

	//是否后配置, 后配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isAfter;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString afterConfig;

	//create update
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString createdBy;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString createdTime;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString updatedBy;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString updatedTime;

	//1为删除
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 delFlag;

public:
	FRefPlaceRuleData()
		: id(0)
		, name(TEXT(""))
		, isEnable(1)
		, isConfig(1)
		, isLeft(0)
		, leftConfig(TEXT("0"))
		, isRight(0)
		, rightConfig(TEXT("0"))
		, isUpper(0)
		, upperConfig(TEXT("0"))
		, isDown(0)
		, downConfig(TEXT("0"))
		, isFront(0)
		, frontConfig(TEXT("0"))
		, isAfter(0)
		, afterConfig(TEXT("0"))
		, createdBy(TEXT("0"))
		, createdTime(TEXT("0"))
		, updatedBy(TEXT("0"))
		, updatedTime(TEXT("0"))
		, delFlag(0)
	{}

	bool IsValid() const
	{
		return id > 0 && !name.IsEmpty() && delFlag != 1; 
	}

	bool IsEnableForUser() const
	{
		return isEnable != 1;
	}

	bool IsUserCanCustom() const
	{
		return isConfig != 1;
	}

	FString CombineStrForShow() const
	{
		return FString::Printf(TEXT("%d/%s/%d"), id, *name, isConfig);
	}
};

USTRUCT(BlueprintType, Blueprintable)
struct DESIGNSTATION_API FRefPlaceRuleCustomData
{
	GENERATED_USTRUCT_BODY()

public:
	//主键
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 id;

	//规则名称
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString name;

	//是否启用
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isEnable;

	//是否支持配置设置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isConfig;

	//是否左配置, 左配置表达式, 左配置值
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isLeft;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString leftConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString leftConfig;

	//是否右配置, 右配置表达式, 右配置值
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isRight;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString rightConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString rightConfig;

	//是否上配置, 上配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isUpper;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString upperConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString upperConfig;

	//是否下配置, 下配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isDown;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString downConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString downConfig;

	//是否前配置, 前配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isFront;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString frontConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString frontConfig;

	//是否后配置, 后配置
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 isAfter;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString afterConfigExpression;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString afterConfig;

	//create update
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString createdBy;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString createdTime;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString updatedBy;
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	FString updatedTime;

	//1为删除
	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	int32 delFlag;

	UPROPERTY(BlueprintReadWrite, Category = "Place Rule")
	bool IsUserCustom;

public:
	FRefPlaceRuleCustomData()
		: id(0)
		, name(TEXT(""))
		, isEnable(1)
		, isConfig(1)
		, isLeft(0)
		, leftConfigExpression(TEXT("0"))
		, leftConfig(TEXT("0"))
		, isRight(0)
		, rightConfigExpression(TEXT("0"))
		, rightConfig(TEXT("0"))
		, isUpper(0)
		, upperConfigExpression(TEXT("0"))
		, upperConfig(TEXT("0"))
		, isDown(0)
		, downConfigExpression(TEXT("0"))
		, downConfig(TEXT("0"))
		, isFront(0)
		, frontConfigExpression(TEXT("0"))
		, frontConfig(TEXT("0"))
		, isAfter(0)
		, afterConfigExpression(TEXT("0"))
		, afterConfig(TEXT("0"))
		, createdBy(TEXT("0"))
		, createdTime(TEXT("0"))
		, updatedBy(TEXT("0"))
		, updatedTime(TEXT("0"))
		, delFlag(0)
		, IsUserCustom(false)
	{}

	bool IsValid() const
	{
		return id > 0 && !name.IsEmpty() && delFlag != 1; 
	}

	bool IsDelete() const { return delFlag == 1; }

	bool IsCustomByUser() const { return IsUserCustom; }
	void SetUserToCustom() { IsUserCustom = true; }

	void ConstructFromPlaceRuleData(const FRefPlaceRuleData& RuleData)
	{
		id = RuleData.id;
		name = RuleData.name;
		isEnable = RuleData.isEnable;
		isConfig = RuleData.isConfig;
		
		isLeft = RuleData.isLeft;
		leftConfigExpression = RuleData.leftConfig;
		leftConfig = RuleData.leftConfig;

		isRight = RuleData.isRight;
		rightConfigExpression = RuleData.rightConfig;
		rightConfig = RuleData.rightConfig;

		isUpper = RuleData.isUpper;
		upperConfigExpression = RuleData.upperConfig;
		upperConfig = RuleData.upperConfig;

		isDown = RuleData.isDown;
		downConfigExpression = RuleData.downConfig;
		downConfig = RuleData.downConfig;

		isFront = RuleData.isFront;
		frontConfigExpression = RuleData.frontConfig;
		frontConfig = RuleData.frontConfig;

		isAfter = RuleData.isAfter;
		afterConfigExpression = RuleData.afterConfig;
		afterConfig = RuleData.afterConfig;

		delFlag = RuleData.delFlag;

		IsUserCustom = false;
	}
};


USTRUCT()
struct DESIGNSTATION_API FRefPlaceDataNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FRefPlaceRuleData> resp;

	UPROPERTY()
	bool success;

public:
	FRefPlaceDataNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, success(false)
	{
	}
	
};

/**
 *   
 */
UCLASS()
class DESIGNSTATION_API URefToPlaceDataLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
};
