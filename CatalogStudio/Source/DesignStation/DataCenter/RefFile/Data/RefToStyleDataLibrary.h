// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "RefToStyleDataLibrary.generated.h"

/*
 *  @@ 风格选项数据
 *	@@ 不再作为单独数据，移于风格内容数据中
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToOptionData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_code;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_description;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_thumbnail_url;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_visibility;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	int32 option_sort_order;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_content_id;

	/*
	*  @@ data source
	*  @@ -1 : no valid ; 0 : web import; 1 : custom; 2 : unknown,need search two
	*  @@ if 1 , need option_custom_id ( custom file id in serve db )
	*/
	UPROPERTY()
	int64 option_data_source;
	UPROPERTY()
	FString option_custom_id;

	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TArray<FParameterData> option_params;

public:
	FRefToOptionData()
		: option_id(FGuid::NewGuid().ToString())
		, option_code(TEXT(""))
		, option_description(TEXT(""))
		, option_thumbnail_url(TEXT(""))
		, option_visibility(TEXT(""))
		, option_visibility_exp(TEXT(""))
		, option_sort_order(INDEX_NONE)
		, option_content_id(TEXT(""))
		, option_data_source(INDEX_NONE)
		, option_custom_id(TEXT(""))
		, option_params(TArray<FParameterData>())
	{}

	void Init(
		const FString& InOptionID, 
		const FString& InOptionCode, 
		const FString& InOptionDescription, 
		const FString& InOptionThumbnailURL,
		const FString& InOptionVisibility, 
		const FString& InOptionVisibilityExp, 
		const int32& InOptionSortOrder, 
		const FString& InOptionContentID,
		const int64& InOptionDataSource,
		const FString& InOptionCustomID
	)
	{
		option_id = InOptionID;
		option_code = InOptionCode;
		option_description = InOptionDescription;
		option_thumbnail_url = InOptionThumbnailURL;
		option_visibility = InOptionVisibility;
		option_visibility_exp = InOptionVisibilityExp;
		option_sort_order = InOptionSortOrder;
		option_content_id = InOptionContentID;
		option_data_source = InOptionDataSource;
		option_custom_id = InOptionCustomID;
	}
	void Init(const TArray<FParameterData>& InParams)
	{
		option_params = InParams;
	}

	bool IsVisible() const
	{
		return !FMath::IsNearlyZero(FMath::Abs(FCString::Atod(*option_visibility)), 0.01f);
	}
};

/*
 *   @@ 风格选项check
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToOptionCheck
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString option_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString content_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	bool is_prime;

public:
	FRefToOptionCheck()
		: option_id(TEXT(""))
		, content_id(TEXT(""))
		, style_id(TEXT(""))
		, is_prime(false)
	{}

	FRefToOptionCheck(const FString& InOptionID, const FString& InContentID, const FString& InStyleID, const bool& InIsPrime)
		: option_id(InOptionID)
		, content_id(InContentID)
		, style_id(InStyleID)
		, is_prime(InIsPrime)
	{}

	void Init(const FString& InOptionID, const FString& InContentID, const FString& InStyleID, const bool& InIsPrime)
	{
		option_id = InOptionID;
		content_id = InContentID;
		style_id = InStyleID;
		is_prime = InIsPrime;
	}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FOptionCheckArr
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TArray<FRefToOptionCheck> option_checks;

public:
	FOptionCheckArr()
		: option_checks(TArray<FRefToOptionCheck>())
	{}



	void Add(const FRefToOptionCheck& InCheck)
	{
		option_checks.Add(InCheck);
	}
};

/*
 *  @@ 风格内容数据
 *	@@ 所有风格共用
 *	@@ option_datas : 风格选项数据
 *	@@ style_option_checks : 风格选项check数据 ( style_id ---- checksArr )
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToContentData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString content_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString content_name;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	int32 content_sort_order;
    UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString content_relation_code;
    UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	int32 content_base_type;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TArray<FRefToOptionData> option_datas;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TMap<FString, FOptionCheckArr> style_option_checks;

public:
	FRefToContentData()
		: content_id(FGuid::NewGuid().ToString())
		, content_name(TEXT(""))
		, content_sort_order(INDEX_NONE)
		, content_relation_code(TEXT(""))
        , content_base_type(0)
		, option_datas(TArray<FRefToOptionData>())
		, style_option_checks(TMap<FString, FOptionCheckArr>())
	{}

	void ClearContentOption()
	{
		option_datas.Empty();
		style_option_checks.Empty();
	}

	void Init(const FString& InContentID, const FString& InContentName, const int32& InContentSortOrder, const FString& InContentRelationCode, const int32& InContentBaseType)
	{
		content_id = InContentID;
		content_name = InContentName;
		content_sort_order = InContentSortOrder;
        content_relation_code = InContentRelationCode;
        content_base_type = InContentBaseType;
	}

	void InitOptions(const TArray<FRefToOptionData>& InOptions)
	{
		option_datas = InOptions;
	}

	void InitChecks(const TMap<FString, FOptionCheckArr>& InChecks)
	{
		style_option_checks = InChecks;
	}

	void ReSortOption()
	{
		for(int32 i = 0; i < option_datas.Num(); ++i)
		{
			option_datas[i].option_sort_order = i;
		}
	}

	bool IsValid() const
	{
		return !content_id.IsEmpty();
	}

	void CheckStyleOptionState(const FString& InStyle);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FStyleCraft
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	int32 id;

	UPROPERTY()
	FString dictGroupValue;

	UPROPERTY()
	FString dictName;

	UPROPERTY()
	FString dictValue;

	UPROPERTY()
	int32 sort;

	UPROPERTY()
	FString mark;

	UPROPERTY()
	int32 delFlag;

	UPROPERTY()
	FString createdBy;

	UPROPERTY()
	FString createdTime;

	UPROPERTY()
	FString updatedBy;

	UPROPERTY()
	FString updatedTime;

public:
	FStyleCraft()
		: id(0)
		, dictGroupValue(TEXT(""))
		, dictName(TEXT(""))
		, dictValue(TEXT(""))
		, sort(0)
		, mark(TEXT(""))
		, delFlag(0)
		, createdBy(TEXT(""))
		, createdTime(TEXT(""))
		, updatedBy(TEXT(""))
		, updatedTime(TEXT(""))
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FStyleCraftNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FStyleCraft> resp;

	UPROPERTY()
	bool success;

public:
	FStyleCraftNetMsg()
		: code(TEXT("")), msg(TEXT("")), resp(TArray<FStyleCraft>()), success(false)
	{}
};

UENUM()
enum class ECSStyleGeoup : uint8
{
	E_WholeHouse = 0						UMETA(DispalyName = "全屋"),
	E_Cupboard								UMETA(DispalyName = "橱柜"),
	E_Invalid
};

/*
 *  @@ 风格数据
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToStyleData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_id;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_code;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_craft;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_description;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_thumbnail_path;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	FString style_thumbnail_md5;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	int32 style_sort_order;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	bool is_checked;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	ECSStyleGeoup style_group;

public:
	FRefToStyleData()
		: style_id(FGuid::NewGuid().ToString())
		, style_code(TEXT(""))
		, style_craft(TEXT(""))
		, style_description(TEXT(""))
		, style_thumbnail_path(TEXT(""))
		, style_thumbnail_md5(TEXT(""))
		, style_sort_order(INDEX_NONE)
		, is_checked(false)
		, style_group(ECSStyleGeoup::E_WholeHouse)
	{}

	void Init(
		const FString& InStyleID,
		const FString& InStyleCode,
		const FString& InStyleCraft,
		const FString& InStyleDescription,
		const FString& InStyleThumbnailPath,
		const FString& InStyleThumbnailMD5,
		const int32& InStyleSortOrder,
		const bool& InIsChecked,
		const int32& InStyleGroup
	)
	{
		style_id = InStyleID;
		style_code = InStyleCode;
		style_craft = InStyleCraft;
		style_description = InStyleDescription;
		style_thumbnail_path = InStyleThumbnailPath;
		style_thumbnail_md5 = InStyleThumbnailMD5;
		style_sort_order = InStyleSortOrder;
		is_checked = InIsChecked;
		style_group = static_cast<ECSStyleGeoup>(InStyleGroup);
	}
};

/*
 *  @@ 风格文件数据
 *
 */
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FRefToStyleFile
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TArray<FRefToStyleData> style_datas;
	UPROPERTY(BlueprintReadWrite, Category = "RefToStyle")
	TArray<FRefToContentData> content_datas;

public:
	FRefToStyleFile()
		: style_datas(TArray<FRefToStyleData>())
		, content_datas(TArray<FRefToContentData>())
	{}

	void ReGenerateStyleOrder();
	void ReGenerateContentOrder();
	void ReGenerateOptionOrder(const FString& OptionID);

	bool IsStyleCodeExist(const FString& InCode);
	bool IsContentNameExist(const FString& InName);
	

	void DeleteStyle(const FString& InID)
	{
		const int32 StyleIndex = style_datas.IndexOfByPredicate([InID](const FRefToStyleData& InData) { return InData.style_id.Equals(InID); });
		if(StyleIndex != INDEX_NONE)
		{
			style_datas.RemoveAt(StyleIndex);
		}

		for(auto& Iter : content_datas)
		{
			auto& StyleChecks = Iter.style_option_checks;
			if(StyleChecks.Contains(InID))
			{
				StyleChecks.Remove(InID);
			}
		}
	}

	void DeleteContent(const FString& InID)
	{}

	void DeleteOption(const FString& InID)
	{}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API URefToStyleDataLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static FString GetStyleRelativeAddress()
	{
		FString RefFolderName = TEXT("RefFiles");
		const FString RefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefFolderName));
		if (!IFileManager::Get().DirectoryExists(*RefFolder))
		{
			IFileManager::Get().MakeDirectory(*RefFolder, true);
		}
		return FPaths::Combine(RefFolderName, TEXT("style.dat"));
	}

	static FString GetStyleFileAddress()
	{
		return FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), GetStyleRelativeAddress()));
	}

	static FString GetAutoTempSaveAddress(const FString& InData, const FString& InTime)
	{
		FString BackupFolderName = FPaths::Combine(TEXT("CatalogAutoSave"), InData);
		const FString BackupRefFolder = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), BackupFolderName));
		if (!IFileManager::Get().DirectoryExists(*BackupRefFolder))
		{
			IFileManager::Get().MakeDirectory(*BackupRefFolder, true);
		}
		
		FString BackupFileName = FString::Printf(TEXT("style-%s.dat"), *InTime);
		return FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), BackupFolderName, BackupFileName));
	}
};
