#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "NetworkFileInfoData.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FNetworkFileInfoData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	bool	exist;
	UPROPERTY()
	FString fileMd5;
	UPROPERTY()
	FString fileSite;
	UPROPERTY()
	int64	fileSize;

public:
	FNetworkFileInfoData()
		: exist(true)
		, fileMd5(TEXT(""))
		, fileSite(TEXT(""))
		, fileSize(0)
	{}

};


USTRUCT(BlueprintType)
struct DESIGNSTATION_API FNetworkFileInfoDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 code;
	UPROPERTY()
		TArray<FNetworkFileInfoData> data;
	UPROPERTY()
		FString msg;
	UPROPERTY()
		bool success;

public:
	FNetworkFileInfoDataMsg()
		: code(0)
		, data(TArray<FNetworkFileInfoData>())
		, msg(TEXT(""))
		, success(false)
	{}

};