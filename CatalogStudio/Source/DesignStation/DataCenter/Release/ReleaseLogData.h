#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ReleaseLogData.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseLogData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	int32	id;

	UPROPERTY()
	FString name;

	UPROPERTY()
	FString releaseTime;

	UPROPERTY()
	int32	releaseType;

	UPROPERTY()
	int32 releaseResult;

	UPROPERTY()
	FString releaseName;

public:
	FReleaseLogData()
		: id(0)
		, name(TEXT(""))
		, releaseTime(TEXT(""))
		, releaseType(0)
		, releaseResult(0)
		, releaseName(TEXT(""))
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMergeLogData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	int32	id;
	UPROPERTY()
	int32	userId;
	UPROPERTY()
	FString userName;
	UPROPERTY()
	FString	mergeTime;
	UPROPERTY()
	FString	dbBeforeVersion;
	UPROPERTY()
	FString	dbBeforeUrl;
	UPROPERTY()
	FString	dbAfterVersion;
	UPROPERTY()
	FString	dbAfterUrl;
	UPROPERTY()
	FString	updateTime;
	UPROPERTY()
	FString	remark;
	UPROPERTY()
	FString	operation;
public:
	FMergeLogData()
		: id(0)
		, userId(0)
		, userName(TEXT(""))
		, mergeTime(TEXT(""))
		, dbBeforeVersion(TEXT(""))
		, dbBeforeUrl(TEXT(""))
		, dbAfterVersion(TEXT(""))
		, dbAfterUrl(TEXT(""))
		, updateTime(TEXT(""))
		, remark(TEXT(""))
		, operation(TEXT(""))
	{}
};



USTRUCT(BlueprintType)
struct DESIGNSTATION_API FPageInfo
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	int32 currentPage;
	UPROPERTY()
	int32 isMore;
	UPROPERTY()
	int32 pageSize;
	UPROPERTY()
	int32 startIndex;
	UPROPERTY()
	int32 totalNum;
	UPROPERTY()
	int32 totalPage;

public:
	FPageInfo()
		: currentPage(0)
		, isMore(0)
		, pageSize(0)
		, startIndex(0)
		, totalNum(0)
		, totalPage(0)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseLogDataPage
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	TArray<FReleaseLogData> list;
	UPROPERTY()
	int32 total;
	UPROPERTY()
	int32 pageTotal;
	UPROPERTY()
	int32 page;
	UPROPERTY()
	int32 pageSize;

public:
	FReleaseLogDataPage()
		: list(TArray<FReleaseLogData>())
		, total(0)
		, pageTotal(0)
		, page(0)
		, pageSize(0)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMergePageData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	TArray<FMergeLogData> list;
	UPROPERTY()
	int32 total;
	UPROPERTY()
	int32 pageTotal;
	UPROPERTY()
	int32 page;
	UPROPERTY()
	int32 pageSize;
public:
	FMergePageData()
		: list(TArray<FMergeLogData>())
		, total(0)
		, pageTotal(0)
		, page(0)
		, pageSize(0)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseLogDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;
	UPROPERTY()
	FReleaseLogDataPage resp;
	UPROPERTY()
	bool success;
public:
	FReleaseLogDataMsg()
		:code(TEXT(""))
		, resp(FReleaseLogDataPage())
		, success(false)
	{}
};
USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMergePageDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;
	UPROPERTY()
	FMergePageData resp;
	UPROPERTY()
	bool success;
public:
	FMergePageDataMsg()
		:code(TEXT(""))
		, resp(FMergePageData())
		, success(false)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FMergeInsertDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;
	UPROPERTY()
	int32 resp;
	UPROPERTY()
	bool success;
public:
	FMergeInsertDataMsg()
		:code(TEXT(""))
		, resp(-1)
		, success(false)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FResponsetDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
	FString code;
	UPROPERTY()
	bool resp;
	UPROPERTY()
	bool success;
public:
	FResponsetDataMsg()
		:code(TEXT(""))
		, resp(false)
		, success(false)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseDetail
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	int64 id;

	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	int64 mainId;

	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	FString bkDirectoryId;

	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	FString folderId;

	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	FString folderName;

	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	FString folderCode;

	//0 : success; 1 : fail
	UPROPERTY(BlueprintReadWrite, Category = "ReleaseDetail")
	int32 result;

public:
	FReleaseDetail()
		: id(0)
		, mainId(0)
		, bkDirectoryId(TEXT(""))
		, folderId(TEXT(""))
		, folderName(TEXT(""))
		, folderCode(TEXT(""))
		, result(0)
	{}
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FReleaseDetailNetMsg
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString code;

	UPROPERTY()
	FString msg;

	UPROPERTY()
	TArray<FReleaseDetail> resp;

	UPROPERTY()
	bool success;

public:
	FReleaseDetailNetMsg()
		: code(TEXT(""))
		, msg(TEXT(""))
		, resp(TArray<FReleaseDetail>())
		, success(false)
	{}

};