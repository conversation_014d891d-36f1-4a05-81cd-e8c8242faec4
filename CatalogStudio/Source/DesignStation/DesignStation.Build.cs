// Fill out your copyright notice in the Description page of Project Settings.

using UnrealBuildTool;
using System.IO;

public class DesignStation : ModuleRules
{
	private string ModulePath
	{
		get { return ModuleDirectory; }
	}

	private string ThirdPartyPath
	{
		get { return Path.GetFullPath(Path.Combine(ModulePath, "../../ThirdParty/")); }
	}

	public DesignStation(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicIncludePaths.AddRange(new string[] 
		{
            "DesignStation",
            "DesignStation/CustomConfig",
            "DesignStation/CustomMaterialEdit",
            "DesignStation/DataCenter",
            "DesignStation/ProtobufOperator"
        });

		PublicDependencyModuleNames.AddRange(new string[]
		{
			"Core", 
			"CoreUObject", 
			"SQLiteCore", 
			"SQLiteSupport", 
			"Engine", 
			"InputCore", 
			"Slate", 
			"SlateCore", 
			"UMG", 
			"<PERSON>son", 
			"JsonUtilities", 
			"PakFile", 
			"ProceduralMeshComponent",
			"zlib",
			"HTTP",
			"DesktopPlatform",
            "ApplicationCore"
        });

		PublicDependencyModuleNames.AddRange(new string[]
		{
			"VictoryUMG", 
			"GrammerAnalysis", 
			"ImageProcess", 
			"WebCommunication", 
			"Protobuf", 
			"CatalogExpression", 
			"MagicCore", 
			"GeometryEdit", 
			"DecimalNumber", 
			"FbxFileImport", 
			"GeometricCalculate",
            "EasyDXF"
        });

		PublicIncludePathModuleNames.AddRange(new string[]
		{
			"MagicCore", "FbxFileImport", "GeometryEdit", "GeometricCalculate","EasyDXF"
        });

		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// Protobuf source integrationg
		ShadowVariableWarningLevel = WarningLevel.Off;
        UndefinedIdentifierWarningLevel = WarningLevel.Off;

		bUseRTTI = true;
		bEnableExceptions = true;

		//close optomize
		OptimizeCode = CodeOptimization.Never;

		PublicDefinitions.Add("_CRT_SECURE_NO_WARNINGS");

	}
}