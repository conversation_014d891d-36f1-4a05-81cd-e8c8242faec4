// Fill out your copyright notice in the Description page of Project Settings.

#include "DesignStation.h"
#include "Modules/ModuleManager.h"
#include "DesignStation/UI/Styles/GeneralStyle.h"
#include "Internationalization/StringTableRegistry.h"


class FDesignStationGameModule : public FDefaultGameModuleImpl
{
	// Called whenever the module is starting up. In here, we unregister any style sets 
	// (which may have been added by other modules) sharing our 
	// style set's name, then initialize our style set. 
	virtual void StartupModule() override
	{
		//Hot reload hack
		FSlateStyleRegistry::UnRegisterSlateStyle(FGeneralStyle::GetStyleSetName());
		FGeneralStyle::Initialize();
		LOCTABLE_FROMFILE_GAME("PosSt", "PosST", "UI/LocST/PosSt.csv");
	}

	// Called whenever the module is shutting down. Here, we simply tell our MenuStyles to shut down.
	virtual void ShutdownModule() override
	{
		FGeneralStyle::Shutdown();
	}

};

IMPLEMENT_PRIMARY_GAME_MODULE(FDesignStationGameModule, DesignStation, "DesignStation" );
