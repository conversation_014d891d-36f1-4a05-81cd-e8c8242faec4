// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "DesignStationAPI.hpp"

#include "CoreMinimal.h"

#define IS_OBJECT_PTR_VALID(ObjPtr) ((nullptr != (ObjPtr)) && ((ObjPtr)->IsValidLowLevel()))

#define IS_ACTOR_PTR_VALID(ObjPtr) ((nullptr != (ObjPtr)) && ((ObjPtr)->IsValidLowLevel()) && IsValid(ObjPtr))

#define GenerateGetConstRef(Type, PropertyName) const Type& Get##PropertyName##ConstRef() const\
{\
	return this->PropertyName;\
}\


#define GenerateGetRef(Type, PropertyName)  Type& Get##PropertyName##Ref()\
{\
	return this->PropertyName;\
}\

#define GenerateGet(Type, PropertyName) Type Get##PropertyName() const\
{\
	return this->PropertyName;\
}\


#define GenerateSet(Type, PropertyName) void Set##PropertyName(const Type& InData)\
{\
	this->PropertyName = InData;\
}\


enum NetworkError {
	NoError = 0,

	// network layer errors [relating to the destination server] (1-99):
	ConnectionRefusedError = 1,
	RemoteHostClosedError,
	HostNotFoundError,
	TimeoutError,
	OperationCanceledError,
	SslHandshakeFailedError,
	TemporaryNetworkFailureError,
	NetworkSessionFailedError,
	BackgroundRequestNotAllowedError,
	TooManyRedirectsError,
	InsecureRedirectError,
	UnknownNetworkError = 99,

	// proxy errors (101-199):
	ProxyConnectionRefusedError = 101,
	ProxyConnectionClosedError,
	ProxyNotFoundError,
	ProxyTimeoutError,
	ProxyAuthenticationRequiredError,
	UnknownProxyError = 199,

	// content errors (201-299):
	ContentAccessDenied = 201,
	ContentOperationNotPermittedError,
	ContentNotFoundError,
	AuthenticationRequiredError,
	ContentReSendError,
	ContentConflictError,
	ContentGoneError,
	UnknownContentError = 299,

	// protocol errors
	ProtocolUnknownError = 301,
	ProtocolInvalidOperationError,
	ProtocolFailure = 399,

	// Server side errors (401-499)
	InternalServerError = 401,
	OperationNotImplementedError,
	ServiceUnavailableError,
	UnknownServerError = 499
};

//使用本地关系文件
#define USE_REF_LOCAL_FILE
//使用folder_id代替id，便于内部引用计算测试
#define USE_FOLDER_ID_REPLACE_ID
//使用接口
//#define USE_INTERFACE_GET_DIRECTORY

#define TEST_SERVER_URL

#ifdef DEV_SERVER_URL
#define SERVER_URL (TEXT("http://192.168.2.202:8188/newbk/"))//开发环境
#endif

#ifdef LOCAL_SERVER_URL
#define SERVER_URL (TEXT("http://10.201.1.188:8188/newbk/"))//本地测试环境
#endif

#ifdef TEST_SERVER_URL
#define SERVER_URL (TEXT("http://192.168.2.202:8188/newbk/"))//测试环境
#endif

#ifdef STAGE_SERVER_URL
#define SERVER_URL (TEXT("https://bkdesign.olo-home.com:6080/newbk/"))//生产环境
#endif