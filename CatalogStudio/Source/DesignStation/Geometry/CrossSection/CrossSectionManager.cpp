// Fill out your copyright notice in the Description page of Project Settings.

#include "CrossSectionManager.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryEllipsePlan.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryRectanglePlan.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryCube.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/PopUI/SOneButtonWidget.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/UI/PopUI/STwoButtonsWidget.h"
#include "Runtime/Engine/Classes/Kismet/KismetTextLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"

#define LOCTEXT_NAMESPACE "CrossSectionManager"

FVector FCrossSectionManager::GetSectionSize()
{
	FVector CenterPoint = FVector::ZeroVector;
	FVector BoxExtent = FVector::ZeroVector;
	FVector SectionSize = FVector::ZeroVector;
	for (auto& section : geomtryPointList)
	{

		section->GetActorBounds(false, CenterPoint, BoxExtent);
		SectionSize = FVector(FMath::Max<float>(SectionSize.X, CenterPoint.X + BoxExtent.X),
			FMath::Max<float>(SectionSize.Y, CenterPoint.Y + BoxExtent.Y),
			FMath::Max<float>(SectionSize.Z, CenterPoint.Z + BoxExtent.Z));
	}
	return SectionSize;
}

void FCrossSectionManager::SectionBound(FVector& Center, FVector& Box)
{
	FBox Bound(ForceInit);
	for (auto& section : GeomtryList)
	{
		TArray<FVector> Points;
		section->CollectPoints(Points);
		for (auto& PointIter : Points)
			Bound += PointIter;
	}
	for (auto& Point : geomtryPointList)
	{
			Bound += Point->GetPointLocation();
	}
	Center = Bound.GetCenter();
	Box = Bound.GetExtent();
}

FCrossSectionManager::FCrossSectionManager()
	:CurrentHover(nullptr)
	, CurrentSelected(nullptr)
	, CustomPlan(nullptr)
	, CurrentPlanType(EPlanPolygonBelongs::EUnknown)
	, CurrentSectionType(ESectionType::ECustomPlan)
	, ToolType(ECrossSectionToolType::EArrow)
{
}

FCrossSectionManager::~FCrossSectionManager()
{
}

void FCrossSectionManager::Initialize(const EPlanPolygonBelongs& InPlanType, const ESectionType& InSetionType, const FVector& InCameraLocation, const float& InCameraWidth)
{
	this->SetCurrentPlanType(InPlanType);
	this->SetCurrentSectionType(InSetionType);
	CameraLocation = InCameraLocation;
	CameraWidth = InCameraWidth;
	pointGenerator = FGeometryPointGenerator();
	FVector planNormal = NSPlanType::GetPlanNormal(CurrentPlanType);


	pointGenerator.setNormal(planNormal);

	lineGenerator = FGeometryLineGenerator();
	lineGenerator.setNormal(planNormal);
}

bool FCrossSectionManager::AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines)
{
	clearPointsAndLinesActor();

	for (auto& LineIte : InLines)
	{
		FVector StartPos = LineIte.StartLocation;
		//FVector EndPos = LineIte.EndLocation;
		switch (CurrentPlanType)
		{
		case EPlanPolygonBelongs::EXY_Plan:StartPos.Z = 0.0; break;
			case EPlanPolygonBelongs::EXZ_Plan:
			{
				StartPos.X = LineIte.StartLocation.X;
				StartPos.Y = 0.0;
				StartPos.Z = LineIte.StartLocation.Y;
			}
			break;
			case EPlanPolygonBelongs::EYZ_Plan:
			{
				StartPos.X = 0.0;
				StartPos.Y = LineIte.StartLocation.Y;
				StartPos.Z = LineIte.StartLocation.X;
			}
			break;
			case EPlanPolygonBelongs::EUnknown:break;
			default:checkNoEntry(); break;
		}
		FGeometryDatas::FormatLocation(this->CurrentPlanType, StartPos);
		//FGeometryDatas::FormatLocation(this->CurrentPlanType, EndPos);

		FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(StartPos.X));
		FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(StartPos.Y));
		FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(StartPos.Z));
		auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);

		auto pointPtr = MakeShared<FGeometryPointData>();
		pointPtr->pointProperty.CopyData(pointProperty);
		pointPtr->position = StartPos;
		pointPtr->structSort = pointGenerator.getPointCount();
		pointPtr->displaySort = pointGenerator.getPointCount();
		pointGenerator.addPoint(pointPtr);
	}

	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	TArray<TSharedPtr<FGeometryPointData>> Points = pointGenerator.getPoints();
	TArray<TSharedPtr<FGeometryLineData>> Lines;
	for (int32 i = 0; i < InLines.Num(); ++i)
	{
		int32 NextI = (i + 1) % InLines.Num();
		TSharedPtr<FGeometryLineData> NewLine = MakeShared<FGeometryLineData>();
		NewLine->startPointData = Points[i];
		NewLine->endPointData = Points[NextI];
		NewLine->lineProperty.CopyData(InLines[i]);
		NewLine->lineProperty.StartLocation = NewLine->startPointData->position;
		NewLine->lineProperty.EndLocation = NewLine->endPointData->position;
		NewLine->lineProperty.PlanBelongs = CurrentPlanType;
		NewLine->lineProperty.ID = i;
		Lines.Add(NewLine);
	}
	lineGenerator.initializeLines(Lines, true);

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);



	if (geomtryPointList.Num() > 0)
	{
		RefreshCustomPlan();
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CustomPlan))
		{
			CustomPlan->ClearPlan();
		}
	}
	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);

	OnSectionPropertyChanged.ExecuteIfBound();

	return true;
}

bool FCrossSectionManager::AddGeomtryPoint(const FVector& InPointLocation)
{
	TArray<FGeomtryLineProperty> tempLinesProperty; //by display sort
	for (auto & iter : lineGenerator.getLines())
	{
		tempLinesProperty.Add(iter->lineProperty);
	}
	clearPointsAndLinesActor();

	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	bool Res = false;
	FVector NewPointLocation(InPointLocation);
	FGeometryDatas::FormatLocation(this->CurrentPlanType, NewPointLocation);

	FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(NewPointLocation.X));
	FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Y));
	FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Z));
	auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);
	
	auto pointPtr = MakeShared<FGeometryPointData>();
	pointPtr->pointProperty.CopyData(pointProperty);
	pointPtr->position = NewPointLocation;
	pointPtr->structSort = pointGenerator.getPointCount();
	pointPtr->displaySort = pointGenerator.getPointCount();
	pointGenerator.addPoint(pointPtr);
	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	if (pointGenerator.getPointCount() == 2)
	{
		lineGenerator.initializeLines(pointGenerator.getPoints(),true);
	}
	else if(pointGenerator.getPointCount() > 2)
	{
		lineGenerator.addPoint(pointPtr,true);
	}

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);


	if (geomtryPointList.Num() > 0)
	{
		RefreshCustomPlan();
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CustomPlan))
		{
			CustomPlan->ClearPlan();
		}
	}
	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
	return true;
}

void FCrossSectionManager::OnMouseMove()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	FHitResult Res;
	FVector WorldLocation, WorldDir;
	PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	unionItems.Append(GeomtryList);
	unionItems.Add(CustomPlan);

	bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
	if (bHit)
	{
		if (CurrentHover != Res.GetActor())
		{
			if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
				CurrentHover->OnMouseLeave();
			AGeometryItemBase* HoverActor = Cast<AGeometryItemBase>(Res.GetActor());
			if (IS_OBJECT_PTR_VALID(HoverActor))
				HoverActor->OnMouseOver();
			CurrentHover = HoverActor;
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
			CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
}

void FCrossSectionManager::OnMouseLeftButtonClick(const FVector& InMousePosition)
{
	if (ECrossSectionToolType::EArrow == ToolType)
	{
		ACatalogPlayerController* PC = ACatalogPlayerController::Get();
		FHitResult Res;
		FVector WorldLocation, WorldDir;
		PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
		TArray<AGeometryItemBase*> unionItems;
		unionItems.Append(geomtryLineList);
		unionItems.Append(geomtryPointList);
		unionItems.Append(GeomtryList);
		unionItems.Add(CustomPlan);

		bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
		if (bHit)
		{
			AGeometryItemBase* SelectActor = Cast<AGeometryItemBase>(Res.GetActor());
			this->SelectAnotherGeometryItem(SelectActor);
		}
		else
		{
			this->SelectAnotherGeometryItem(nullptr);
		}
		int32 Type = 0;
		int32 Index = -1;
		if (ESectionType::ECustomPlan == CurrentSectionType)
		{
			if (IS_OBJECT_PTR_VALID(CurrentSelected))
			{
				Type = EGeomtryItemType::EGeomtryPoint == CurrentSelected->GetGeomtryItemTypeConstRef() ? 0 : 1;
				pointGenerator.computeStruct();
				if (Type == 0 && Cast<AGeometryPoint>(CurrentSelected))
				{
					Index = Cast<AGeometryPoint>(CurrentSelected)->getPointData()->displaySort;
				}
				else if(Type == 1 && Cast<AGeometryLine>(CurrentSelected))
				{
					Index = Cast<AGeometryLine>(CurrentSelected)->getLineData()->getDisplaySort();
				}
			}
		}
		else if (ESectionType::ERectangle == CurrentSectionType)
		{
			Type = 2;
			Index = 0;
		}
		else if (ESectionType::EEllipse == CurrentSectionType)
		{
			Type = 3;
			Index = 0;
		}
		else if (ESectionType::ECube == CurrentSectionType)
		{
			Type = 4;
			Index = 0;
		}
		OnSelectionChanged.ExecuteIfBound(Type, Index);
	}
	else if (ECrossSectionToolType::EDrawPoint == ToolType)
	{
		if (ESectionType::ECustomPlan == CurrentSectionType)
		{
			if (this->AddGeomtryPoint(InMousePosition))
			{
				OnSectionPropertyChanged.ExecuteIfBound();
			}
		}
		else
		{
			SOneButtonWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Error"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
				, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
		}
	}
}

void FCrossSectionManager::OnMouseRightButtonClick(const FVector& InMousePosition)
{
	ToolType = ECrossSectionToolType::EArrow;
	ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
}

bool FCrossSectionManager::AddGeometryRectangle()
{
	CurrentSectionType = ESectionType::ERectangle;
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	FGeomtryRectanglePlanProperty DefaultRect;
	FVector NewLocation = DefaultRect.EndLocation();
	FGeometryDatas::FormatLocation(this->CurrentPlanType, NewLocation);
	AGeometryRectanglePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryRectanglePlan, FGeomtryRectanglePlanProperty>(Wrold, FGeomtryRectanglePlanProperty(CurrentPlanType, FExpressionValuePair(0.0f), FExpressionValuePair(0.0f), FExpressionValuePair(0.0f), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.X)), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.Y)), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.Z))));
	GeomtryList.Add(NewRectangle);
	return true;
}

bool FCrossSectionManager::AddGeometryEllipse()
{
	CurrentSectionType = ESectionType::EEllipse;
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();

	FGeomtryEllipsePlanProperty DefaultEllipse;
	DefaultEllipse.PlanBelongs = CurrentPlanType;
	AGeometryEllipsePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryEllipsePlan, FGeomtryEllipsePlanProperty>(Wrold, DefaultEllipse);
	GeomtryList.Add(NewRectangle);
	return true;
}

bool FCrossSectionManager::AddGeometryCube()
{
	CurrentSectionType = ESectionType::ECube;
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	FGeomtryCubeProperty DefaultCube;
	AGeometryCube* NewCube = FGeometryItemList::Get().CreateGeomtryItem<AGeometryCube, FGeomtryCubeProperty>(Wrold, DefaultCube);
	GeomtryList.Add(NewCube);
	return true;
}

FText FCrossSectionManager::GetSelectedItemName() const
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		return CurrentSelected->GetItemTypeName();
	}
	return FText();
}

void FCrossSectionManager::Reset()
{
	if (IS_OBJECT_PTR_VALID(CurrentHover))
	{
		CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelected))
	{
		CurrentSelected->OnSlectionChanged(false);
		CurrentSelected = nullptr;
	}
}

void FCrossSectionManager::HidePoints(bool NewHiden)
{
	for (int32 i = 0; i < geomtryPointList.Num(); ++i)
	{
		if (geomtryPointList[i])
		{
			geomtryPointList[i]->SetActorHiddenInGame(NewHiden);
		}
	}
}

void FCrossSectionManager::generatePointActors(TArray<AGeometryPoint*>& pointActors)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	for (auto iter : pointGenerator.getPoints())
	{
		AGeometryPoint* newPoint = FGeometryItemList::Get().CreateGeomtryItem<AGeometryPoint, FGeomtryPointProperty>(Wrold, iter->pointProperty);
		newPoint->setPointData(iter);
		pointActors.Add(newPoint);
	}
}

void FCrossSectionManager::generateLineActors(TArray<AGeometryLine*>& lineActors, bool bLoop)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	auto num = lineGenerator.getLines().Num();
	int32 i = 0;

	for (auto& iter : lineGenerator.getLines())
	{
		auto newProperty = iter->lineProperty;
		AGeometryLine* newLine = FGeometryItemList::Get().CreateGeomtryItem<AGeometryLine, FGeomtryLineProperty>(Wrold, newProperty);
		newLine->SetLineData(iter);

		auto startPoint = newLine->getLineData()->startPointData;
		auto endPoint = newLine->getLineData()->endPointData;
		if (num > 2)
		{
			if (startPoint->structSort > endPoint->structSort && startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
			{

			}
			else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
			{
				startPoint = newLine->getLineData()->endPointData;
				endPoint = newLine->getLineData()->startPointData;
			}
		}
		TArray<FVector> newVertex;
		int32 step = 100;
		getLineVertex(startPoint, endPoint, step, newVertex);
		newLine->setVertex(newVertex);

		lineActors.Add(newLine);
		++i;
	}
}
void FCrossSectionManager::getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex)
{
	if (pointGenerator.getPointCount() <=2)
	{
		lineGenerator.getLineVertexOrder(startPoint, endPoint, stepNum, outVertex);
	}
	else
	{
		lineGenerator.getLineVertex(startPoint, endPoint, stepNum, outVertex);
	}
}

void FCrossSectionManager::SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver)
{
	AGeometryItemBase* NewSelection = nullptr;
	if (IsOver)
	{
		int32 NewSelectionIndex = -1;
		if (0 == InType)
		{//Point
			NewSelectionIndex = InIndex;
			for (auto & iter :  geomtryPointList)
			{
				auto ptr = iter->getPointData();
				if (ptr->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}

		}
		else if (1 == InType)
		{//Line
			NewSelectionIndex = InIndex;
			for (auto& iter : geomtryLineList)
			{
				auto ptr = iter->getLineData();
				if (ptr->startPointData->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}
		}
		//else
		//{//Others
		//	NewSelectionIndex = InIndex;
		//}
		//if (GeomtryList.IsValidIndex(NewSelectionIndex))
		//	NewSelection = GeomtryList[NewSelectionIndex];
	}
	this->SelectAnotherGeometryItem(NewSelection);
}

void FCrossSectionManager::SelectAnotherGeometryItem(AGeometryItemBase* NewSelection)
{
	if (CurrentSelected != NewSelection)
	{
		if (IS_OBJECT_PTR_VALID(CurrentSelected))
		{
			CurrentSelected->OnSlectionChanged(false);
		}
		if (IS_OBJECT_PTR_VALID(NewSelection))
		{
			NewSelection->OnSlectionChanged(true);
		}
		CurrentSelected = NewSelection;
	}
}

void FCrossSectionManager::OnDeleteKeyClick()
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		CurrentSelected->DeleteGeomtryItem();
		int32 Pos = GeomtryList.Find(CurrentSelected);



		int32 pointIndex = geomtryPointList.Find(Cast<AGeometryPoint>(CurrentSelected));
		int32 lindIndex = geomtryLineList.Find(Cast<AGeometryLine>(CurrentSelected));
		if (pointIndex != INDEX_NONE)
		{
			auto ptr = geomtryPointList[pointIndex]->getPointData();
			pointGenerator.deletePoint(ptr);
			lineGenerator.deletePoint(ptr);

		}
		else if (lindIndex != INDEX_NONE)
		{
			auto ptrLine = geomtryLineList[lindIndex]->getLineData();
			auto ptrPointA = ptrLine->startPointData;
			auto ptrPointB = ptrLine->endPointData;
			pointGenerator.deletePoint(ptrPointA);
			pointGenerator.deletePoint(ptrPointB);
			lineGenerator.deletePoint(ptrPointA);
			lineGenerator.deletePoint(ptrPointB);
		}

		clearPointsAndLinesActor();
		generatePointActors(geomtryPointList);
		auto pointDatas = pointGenerator.getPoints();
		generateLineActors(geomtryLineList);
		SelectAnotherGeometryItem(nullptr);
		CurrentHover = nullptr;
		RefreshCustomPlan();
		OnSectionPropertyChanged.ExecuteIfBound();
	}
}

void FCrossSectionManager::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	UE_LOG(LogTemp, Log, TEXT("Camera width is :  %f  "), NewWidth);
	CameraLocation = NewLocation;
	CameraWidth = NewWidth;
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	for (auto& Iter : unionItems)
	{
		const EGeomtryItemType& ItemType = Iter->GetGeomtryItemTypeConstRef();
		float Scale = -1.0f;
		if (EGeomtryItemType::EGeomtryPoint == ItemType)
		{
			Scale = FMath::Max<float>(NewWidth * 0.00005f, 0.0005);
			Scale = FMath::Min<float>(Scale, 0.005);
		}
		else if (EGeomtryItemType::EGeomtryLine == ItemType)
			Scale = FMath::Max<float>((NewWidth / 400.0f) * 1.6f, 0.1);
		if (Scale > 0.0f)
			Iter->ScaleGeomtryItem(Scale);

		UE_LOG(LogTemp, Warning, TEXT("Scale-----> %f"), Scale);
	}
}

void FCrossSectionManager::RefreshCustomPlan()
{
	FGeomtryCustomPlanProperty CustomPlanProperty;
	bool Res = true;
	bool bClockwise = true;
	if (ESectionType::ECube != CurrentSectionType)
	{
		CustomPlanProperty.PolygonData.PolygonPlan = CurrentPlanType;
		pointGenerator.computeStruct();
		if (pointGenerator.getPointCount() < 2)
		{
			return;
		}
		TArray<TSharedPtr<FGeometryPointData>> tempPoints = pointGenerator.getPoints();
		tempPoints.Sort([](TSharedPtr<FGeometryPointData> a, TSharedPtr<FGeometryPointData>b) {return a->structSort < b->structSort; });
		int32 num = tempPoints.Num();
		for (int32 cur = 0; cur < num; ++cur)
		{
			TArray<FVector> newVertex;
			int32 next = (cur + 1) % num;
			getLineVertex(tempPoints[cur], tempPoints[next], 50, newVertex);
			for (auto & iter : newVertex)
			{
				CustomPlanProperty.PolygonData.PolygonVertice.AddUnique(iter);
			}
			if (next == 0)
			{
				if(!newVertex.IsEmpty())
					CustomPlanProperty.PolygonData.PolygonVertice.Add(newVertex.Last(0));
			}
		}
	}
	else
	{
		if ((1 == GeomtryList.Num()) && (EGeomtryItemType::EGeometryCube == GeomtryList[0]->GetGeomtryItemTypeConstRef()))
		{
			FGeomtryCubeProperty CubeData;
			GeomtryList[0]->ConstructSaveData(static_cast<void*>(&CubeData));
			FPMCSection MeshInfo;
			Res = UGeometryRelativeLibrary::GenerateCubeMesh(CubeData.StartLocation(), CubeData.EndLocation(), MeshInfo);
			if (Res)
			{
				CustomPlanProperty = FGeomtryCustomPlanProperty(MeshInfo);
			}
		}
	}
	if (!Res)
		return;

	if (!IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan = FGeometryItemList::Get().CreateGeomtryItem<AGeometryCustomPlan, FGeomtryCustomPlanProperty>(ACatalogPlayerController::Get()->GetWorld(), CustomPlanProperty);
	}
	else
	{

		CustomPlan->SetCustomPlanProperty(CustomPlanProperty);
		CustomPlan->Refresh();
	}
}

void FCrossSectionManager::ClearCrossSection()
{
	for (auto& Iter : GeomtryList)
	{
		Iter->DeleteGeomtryItem();
	}
	for (auto & iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	GeomtryList.Empty();

	geomtryPointList.Empty();
	geomtryLineList.Empty();
	pointGenerator.clear();
	lineGenerator.clear();
	CurrentHover = nullptr;
	CurrentSelected = nullptr;
	if (IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan->DeleteGeomtryItem();
		CustomPlan = nullptr;
	}
	ToolType = ECrossSectionToolType::EArrow;
	ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
}

void FCrossSectionManager::clearPointsAndLinesActor()
{
	for (auto& iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	geomtryPointList.Empty();
	geomtryLineList.Empty();
	if (IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan->DeleteGeomtryItem();
		CustomPlan = nullptr;
	}
}

bool FCrossSectionManager::ConstructArchiveData(FCrossSectionData& OutCrossSectionData)
{
	OutCrossSectionData.PlanBelongs = this->CurrentPlanType;
	OutCrossSectionData.SectionType = CurrentSectionType;
	OutCrossSectionData.Points.Empty();
	OutCrossSectionData.Lines.Empty();

	if (OutCrossSectionData.SectionType == ESectionType::ECustomPlan)
	{
		for (auto& iter : geomtryPointList)
		{
			FGeomtryPointProperty pointData;
			bool Res = iter->ConstructSaveData(static_cast<void*>(&pointData));
			if (!Res)
				return false;
			OutCrossSectionData.Points.Add(pointData);
		}
		for (auto& iter : geomtryLineList)
		{
			FGeomtryLineProperty lineData;
			bool Res = iter->ConstructSaveData(static_cast<void*>(&lineData));
			if (!Res)
				return false;
			OutCrossSectionData.Lines.Add(lineData);
		}

		return true;
	}

	for (auto& Iter : GeomtryList)
	{
		if (EGeomtryItemType::EGeomtryPoint == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryPointProperty PointData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&PointData));
			if (!Res)
				return false;
			OutCrossSectionData.Points.Add(PointData);
		}
		else if (EGeomtryItemType::EGeomtryLine == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryLineProperty LineData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&LineData));
			if (!Res)
				return false;
			OutCrossSectionData.Lines.Add(LineData);
		}
		else if (EGeomtryItemType::EGeometryRectangle == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryRectanglePlanProperty RectangleData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&RectangleData));
			if (!Res)
				return false;
			OutCrossSectionData.Rectangle = RectangleData;
		}
		else if (EGeomtryItemType::EGeometryEllipse == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryEllipsePlanProperty EllipseData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&EllipseData));
			if (!Res)
				return false;
			OutCrossSectionData.Ellipse = EllipseData;
		}
		else if (EGeomtryItemType::EGeometryCube == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryCubeProperty CubeData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&CubeData));
			if (!Res)
				return false;
			OutCrossSectionData.Cube = CubeData;
		}
	}
	return true;
}

bool FCrossSectionManager::ChangeDrawTool(const ECrossSectionToolType& InNewToolType)
{
	if (ECrossSectionToolType::EClear == InNewToolType)
	{
		EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
			FText::FromStringTable(FName("PosSt"), TEXT("Clear Section"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Do you want to clear this section ?"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
		if (EPopButtonType::Confirm == ButtonType)
		{
			//ECrossSectionToolType CurrentType = ToolType;
			this->ClearCrossSection();
			ToolType = ECrossSectionToolType::EArrow;			
			CurrentSectionType = ESectionType::ECustomPlan;
			OnSectionPropertyChanged.ExecuteIfBound();
			return true;
		}
		return false;
	}
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryPointList);
	unionItems.Append(geomtryLineList);
	unionItems.Append(GeomtryList);
	if (ToolType != InNewToolType)
	{
		if (ECrossSectionToolType::EArrow == InNewToolType)
		{
			ToolType = InNewToolType;
			ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
			return true;
		}
		else if (ECrossSectionToolType::EDrawPoint == InNewToolType)
		{
			bool CanChange = 0 == unionItems.Num();
			if (!CanChange)
				CanChange = EGeomtryItemType::EGeomtryPoint == unionItems[0]->GetGeomtryItemTypeConstRef();
			if (CanChange)
			{
				ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Crosshairs;
				ToolType = InNewToolType;
				return true;
			}
		}
		else if (ECrossSectionToolType::EDrawRectangle == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				if (this->AddGeometryRectangle())
				{
					OnSectionPropertyChanged.ExecuteIfBound();
					return true;
				}
			}
		}
		else if (ECrossSectionToolType::EDrawEllipse == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				if (this->AddGeometryEllipse())
				{
					OnSectionPropertyChanged.ExecuteIfBound();
					return true;
				}
			}
		}
		else if (ECrossSectionToolType::EDrawCube == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				if (this->AddGeometryCube())
				{
					OnSectionPropertyChanged.ExecuteIfBound();
					return true;
				}
			}
		}else if (ECrossSectionToolType::EImportCAD == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				return true;
			}
		}
	}
	SOneButtonWidget::PopupModalWindow(
		FText::FromStringTable(FName("PosSt"), TEXT("Error"))
		, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
		, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
	return false;
}

bool FCrossSectionManager::ClearDrawState()
{
	ToolType = ECrossSectionToolType::EArrow;
	ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
	return true;
}

bool FCrossSectionManager::LoadFromArchiveData(const FCrossSectionData& InCrossSectionData)
{
	ClearCrossSection();

	CurrentSectionType = InCrossSectionData.SectionType;
	CurrentPlanType = InCrossSectionData.PlanBelongs;
	FVector planNormal = NSPlanType::GetPlanNormal(CurrentPlanType);
	pointGenerator.setNormal(planNormal);
	lineGenerator.setNormal(planNormal);

	if (ESectionType::ECustomPlan == CurrentSectionType)
	{
		if (InCrossSectionData.Points.Num() <= 0 || (InCrossSectionData.Points.Num() > 1 && InCrossSectionData.Lines.Num() != InCrossSectionData.Points.Num()))
			return false;


		for (auto& iter : InCrossSectionData.Points)
		{
			FGeomtryPointProperty PointProperty(iter);
			PointProperty.PlanBelongs = InCrossSectionData.PlanBelongs;

			FVector newPointLocation(iter.PointLocation());
			FGeometryDatas::FormatLocation(this->CurrentPlanType, newPointLocation);
			pointGenerator.addPoint(newPointLocation, PointProperty);
		}

		pointGenerator.computeStruct();
		generatePointActors(geomtryPointList);

		auto pointDatas = pointGenerator.getPoints();
		lineGenerator.initializeLines(pointDatas, true);
		generateLineActors(geomtryLineList);
		//crosssection save by displayIndex
		for (int32 displayIndex = 0; displayIndex < InCrossSectionData.Lines.Num(); ++displayIndex)	
		{
			for (auto &  iter : geomtryLineList)
			{
				auto ptr = iter->getLineData();
				auto startIndex = ptr->getDisplaySort();
				if (startIndex == displayIndex)
				{
					auto newProperty = InCrossSectionData.Lines[displayIndex];
					iter->setProperty(static_cast<void*>(&newProperty));


					auto startPoint = iter->getLineData()->startPointData;
					auto endPoint = iter->getLineData()->endPointData;

					if(pointGenerator.getPointCount() > 2)
					{
						if (startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
						{

						}
						else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
						{
							startPoint = iter->getLineData()->endPointData;
							endPoint = iter->getLineData()->startPointData;
						}
					}

					TArray<FVector> newVertex;
					getLineVertex(startPoint, endPoint, 100, newVertex);
					iter->setVertex(newVertex);
					break;
				}
			}
		}

		this->RefreshCustomPlan();
	}
	else if (ESectionType::ERectangle == CurrentSectionType)
	{
		UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
		FGeomtryRectanglePlanProperty NewRectangleProperty(InCrossSectionData.Rectangle);
		NewRectangleProperty.PlanBelongs = InCrossSectionData.PlanBelongs;
		AGeometryRectanglePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryRectanglePlan, FGeomtryRectanglePlanProperty>(Wrold, NewRectangleProperty);
		GeomtryList.Add(NewRectangle);
	}
	else if (ESectionType::EEllipse == CurrentSectionType)
	{
		UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
		FGeomtryEllipsePlanProperty EllipseProperty(InCrossSectionData.Ellipse);
		EllipseProperty.PlanBelongs = InCrossSectionData.PlanBelongs;
		AGeometryEllipsePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryEllipsePlan, FGeomtryEllipsePlanProperty>(Wrold, EllipseProperty);
		GeomtryList.Add(NewRectangle);
	}
	else if (ESectionType::ECube == CurrentSectionType)
	{
		UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
		AGeometryCube* NewCube = FGeometryItemList::Get().CreateGeomtryItem<AGeometryCube, FGeomtryCubeProperty>(Wrold, FGeomtryCubeProperty(InCrossSectionData.Cube));
		GeomtryList.Add(NewCube);
	}
	return true;
}

void FCrossSectionManager::clearLineActor()
{
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	geomtryLineList.Empty();
	if (IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan->DeleteGeomtryItem();
		CustomPlan = nullptr;
	}
}

bool FCrossSectionManager::ConstructPolygon(FPolygonData& OutPolygon)
{
	FCrossSectionData SectionData;
	bool Res = this->ConstructArchiveData(SectionData);
	if (Res)
	{
		return SectionData.ConvertToPolygon(OutPolygon);
	}
	return false;
}

bool FCrossSectionManager::ChangeCrossSectionProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty)
{
	auto changePoint = pointGenerator.setPropertyByDisplaySort(InPointProperty.ID, InPointProperty);
	for (auto iter : geomtryPointList)
	{
		if (iter->getPointData() == changePoint)
		{
			FGeomtryPointProperty newProperty = InPointProperty;
			iter->ChangeProperty(InType,static_cast<void*>(&newProperty));
		}
	}
	pointGenerator.computeStruct();
	for (auto iter : geomtryLineList)
	{
		if (iter->getLineData()->containPoint(changePoint))
		{
			auto startPoint = iter->getLineData()->startPointData;
			auto endPoint = iter->getLineData()->endPointData;
			if (pointGenerator.getPointCount() > 2)
			{
				if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
				{
					startPoint = iter->getLineData()->endPointData;
					endPoint = iter->getLineData()->startPointData;
				}
			}
			TArray<FVector> newVertex;
			getLineVertex(startPoint, endPoint, 100, newVertex);
			iter->setVertex(newVertex);
		}
	}
	this->RefreshCustomPlan();
	OnSectionPropertyChanged.ExecuteIfBound();
	return true;
}

bool FCrossSectionManager::ChangeCrossSectionProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty)
{
	pointGenerator.computeStruct();
	auto changeLine = lineGenerator.setLineProperty(InLineProperty.ID, InLineProperty);
	auto startPoint = changeLine->startPointData;
	auto endPoint	= changeLine->endPointData;
	if (pointGenerator.getPointCount() > 2)
	{
		if (startPoint->structSort > endPoint->structSort && startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
		{

		}
		else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
		{
			startPoint = changeLine->endPointData;
			endPoint = changeLine->startPointData;
		}
	}

	TArray<FVector> newVertex;
	getLineVertex(startPoint, endPoint, 100, newVertex);
	if (changeLine==nullptr)
	{
		return false;
	}
	for (auto & iter : geomtryLineList)
	{
		if (iter->getData() == changeLine)
		{
			FGeomtryLineProperty NewProperty = InLineProperty;
			iter->setProperty(InType, static_cast<void*>(&NewProperty));
			iter->setVertex(newVertex);
			break;
		}
	}
	//if (res)
	{
		this->RefreshCustomPlan();
		OnSectionPropertyChanged.ExecuteIfBound();
	}
	return true;
}

bool FCrossSectionManager::ChangeCrossSectionProperty(const int32& InType, const FGeomtryRectanglePlanProperty& InRectangleProperty)
{
	checkf(1 == GeomtryList.Num() && EGeomtryItemType::EGeometryRectangle == GeomtryList[0]->GetGeomtryItemTypeConstRef(), TEXT("GeomtryList index 0 is not rectangle!"));
	FGeomtryRectanglePlanProperty NewRectangleProperty(InRectangleProperty);
	bool Res = GeomtryList[0]->ChangeProperty(InType, static_cast<void*>(&NewRectangleProperty));
	if (Res)
	{
		this->RefreshCustomPlan();
		OnSectionPropertyChanged.ExecuteIfBound();
	}
	return Res;
}

bool FCrossSectionManager::ChangeCrossSectionProperty(const int32& InType, const FGeomtryEllipsePlanProperty& InEllipseProperty)
{
	checkf(1 == GeomtryList.Num() && EGeomtryItemType::EGeometryEllipse == GeomtryList[0]->GetGeomtryItemTypeConstRef(), TEXT("GeomtryList index 0 is not ellipse!"));
	FGeomtryEllipsePlanProperty NewEllipseProperty(InEllipseProperty);
	bool Res = GeomtryList[0]->ChangeProperty(InType, static_cast<void*>(&NewEllipseProperty));
	if (Res)
	{
		//this->RefreshCustomPlan();
		OnSectionPropertyChanged.ExecuteIfBound();
	}
	return Res;
}

bool FCrossSectionManager::ChangeCrossSectionProperty(const int32& InType, const FGeomtryCubeProperty& InCubeProperty)
{
	checkf(1 == GeomtryList.Num() && EGeomtryItemType::EGeometryCube == GeomtryList[0]->GetGeomtryItemTypeConstRef(), TEXT("GeomtryList index 0 is not cube!"));
	FGeomtryCubeProperty NewEllipseProperty(InCubeProperty);
	bool Res = GeomtryList[0]->ChangeProperty(InType, static_cast<void*>(&NewEllipseProperty));
	if (Res)
	{
		this->RefreshCustomPlan();
		OnSectionPropertyChanged.ExecuteIfBound();
	}
	return Res;
}

#undef LOCTEXT_NAMESPACE
