// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemList.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryCustomPlan.h"

enum class ECrossSectionToolType : uint8
{
	EArrow,
	EDrawPoint,
	EDrawRectangle,
	EDrawEllipse,
	EDrawCube,
	EClear,
	EImportCAD
};

/**
 *
 */
class DESIGNSTATION_API FCrossSectionManager
{
private:
	AGeometryItemBase* CurrentHover;
	AGeometryItemBase* CurrentSelected;
	TArray<AGeometryItemBase*> GeomtryList;
	TArray<AGeometryPoint*> geomtryPointList;
	TArray<AGeometryLine*> geomtryLineList;

	TArray<FVector>	currentPoints;

	//
	AGeometryCustomPlan* CustomPlan;
	EPlanPolygonBelongs CurrentPlanType;
	ESectionType		CurrentSectionType;

	//
	ECrossSectionToolType ToolType;

	//
	FVector		CameraLocation;
	float		CameraWidth;

public:

	GenerateSet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateSet(ESectionType, CurrentSectionType)
		GenerateGet(ESectionType, CurrentSectionType)
		GenerateGet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateGet(ECrossSectionToolType, ToolType)

public:

	FNoParamDelegate OnSectionPropertyChanged;
	FTwoIntDelegate OnSelectionChanged;
	FVector	GetSectionSize();
	void SectionBound(FVector& Center, FVector& Box);
public:
	FCrossSectionManager();
	virtual ~FCrossSectionManager();

	void Initialize(const EPlanPolygonBelongs& InPlanType, const ESectionType& InSetionType, const FVector& InCameraLocation, const float& InCameraWidth);

	FText GetSelectedItemName() const;

	void SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver);

	//
	bool ChangeCrossSectionProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty);
	bool ChangeCrossSectionProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty);
	bool ChangeCrossSectionProperty(const int32& InType, const FGeomtryRectanglePlanProperty& InRectangleProperty);
	bool ChangeCrossSectionProperty(const int32& InType, const FGeomtryEllipsePlanProperty& InEllipseProperty);
	bool ChangeCrossSectionProperty(const int32& InType, const FGeomtryCubeProperty& InCubeProperty);
	//

	bool ChangeDrawTool(const ECrossSectionToolType& InNewToolType);

	bool ClearDrawState();


	void OnMouseMove();

	void OnMouseLeftButtonClick(const FVector& InMousePosition);

	void OnMouseRightButtonClick(const FVector& InMousePosition);

	void OnDeleteKeyClick();

	void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth);

	void Reset();

	void ClearCrossSection();

	void clearPointsAndLinesActor();

	void clearLineActor();
	bool ConstructPolygon(FPolygonData& OutPolygon);

	bool ConstructArchiveData(FCrossSectionData& OutCrossSectionData);

	bool LoadFromArchiveData(const FCrossSectionData& InCrossSectionData);

	//隐藏所有点
	void HidePoints(bool NewHiden);
	
	void generatePointActors(TArray<AGeometryPoint*>& pointActors);

	void generateLineActors(TArray<AGeometryLine*>& lineActors,bool bLoop = true);

	void getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex);

	bool AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines);
private:

	void SelectAnotherGeometryItem(AGeometryItemBase* NewSelection);

	bool AddGeomtryPoint(const FVector& InPointLocation);

	bool AddGeometryRectangle();

	bool AddGeometryEllipse();

	bool AddGeometryCube();

	void RefreshCustomPlan();

private:
	FGeometryPointGenerator pointGenerator;
	FGeometryLineGenerator	lineGenerator;

};
