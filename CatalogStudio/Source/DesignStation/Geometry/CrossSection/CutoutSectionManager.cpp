// Fill out your copyright notice in the Description page of Project Settings.

#include "CutoutSectionManager.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryEllipsePlan.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryRectanglePlan.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/UI/PopUI/SOneButtonWidget.h"
#include "DesignStation/UI/PopUI/STwoButtonsWidget.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"

#define LOCTEXT_NAMESPACE "CutoutSectionManager"

FCutoutSectionManager::FCutoutSectionManager()
	:CurrentHover(nullptr)
	, CurrentSelected(nullptr)
	, CurrentPlanType(EPlanPolygonBelongs::EUnknown)
	, ToolType(ECutoutSectionToolType::EArrow)
{
}

FCutoutSectionManager::~FCutoutSectionManager()
{
}

bool FCutoutSectionManager::AddGeomtryPoint(const FVector& InPointLocation)
{
	clearPointsAndLinesActor();

	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	FVector NewPointLocation(InPointLocation);
	FGeometryDatas::FormatLocation(this->CurrentPlanType, NewPointLocation);

	FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(NewPointLocation.X));
	FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Y));
	FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Z));
	auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);

	auto pointPtr = MakeShared<FGeometryPointData>();
	pointPtr->pointProperty.CopyData(pointProperty);
	pointPtr->position = NewPointLocation;
	pointPtr->structSort = pointGenerator.getPointCount();
	pointPtr->displaySort = pointGenerator.getPointCount();
	pointGenerator.addPoint(pointPtr);
	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	if (pointGenerator.getPointCount() == 2)
	{
		lineGenerator.initializeLines(pointGenerator.getPoints(), true);
	}
	else if (pointGenerator.getPointCount() > 2)
	{
		lineGenerator.addPoint(pointPtr, true);
	}

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);

	if (geomtryPointList.Num() > 0)
	{
		RefreshCustomPlan();
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CustomPlan))
		{
			CustomPlan->ClearPlan();
		}
	}
	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
	return geomtryPointList.Num() > 0;
}

bool FCutoutSectionManager::AddGeometryRectangle()
{
	CurrentSectionType = ESectionType::ERectangle;
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	FGeomtryRectanglePlanProperty DefaultRect;
	FVector NewLocation = DefaultRect.EndLocation();
	FGeometryDatas::FormatLocation(this->CurrentPlanType, NewLocation);
	AGeometryRectanglePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryRectanglePlan, FGeomtryRectanglePlanProperty>(Wrold, FGeomtryRectanglePlanProperty(CurrentPlanType, FExpressionValuePair(0.0f), FExpressionValuePair(0.0f), FExpressionValuePair(0.0f), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.X)), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.Y)), FExpressionValuePair(UParameterPropertyData::ConvertToUIValue(NewLocation.Z))));
	GeomtryList.Add(NewRectangle);
	return true;
}

bool FCutoutSectionManager::AddGeometryEllipse()
{
	CurrentSectionType = ESectionType::EEllipse;
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();

	FGeomtryEllipsePlanProperty DefaultEllipse;
	DefaultEllipse.PlanBelongs = CurrentPlanType;
	AGeometryEllipsePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryEllipsePlan, FGeomtryEllipsePlanProperty>(Wrold, DefaultEllipse);
	GeomtryList.Add(NewRectangle);
	return true;
}

void FCutoutSectionManager::OnMouseMove()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	FHitResult Res;
	FVector WorldLocation, WorldDir;
	PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
	if (bHit)
	{
		if (CurrentHover != Res.GetActor())
		{
			if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
				CurrentHover->OnMouseLeave();
			AGeometryItemBase* HoverActor = Cast<AGeometryItemBase>(Res.GetActor());
			if (IS_OBJECT_PTR_VALID(HoverActor))
				HoverActor->OnMouseOver();
			CurrentHover = HoverActor;
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
			CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
}

void FCutoutSectionManager::OnMouseLeftButtonClick(const FVector& InMousePosition)
{
	if (ECutoutSectionToolType::EArrow == ToolType)
	{
		ACatalogPlayerController* PC = ACatalogPlayerController::Get();
		FHitResult Res;
		FVector WorldLocation, WorldDir;
		PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
		TArray<AGeometryItemBase*> unionItems;
		unionItems.Append(geomtryLineList);
		unionItems.Append(geomtryPointList);
		bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
		if (bHit)
		{
			AGeometryItemBase* SelectActor = Cast<AGeometryItemBase>(Res.GetActor());
			this->SelectAnotherGeometryItem(SelectActor);
		}
		else
		{
			this->SelectAnotherGeometryItem(nullptr);
		}
		int32 Type = 0;
		int32 Index = -1;
		if (ESectionType::ECustomPlan == CurrentSectionType)
		{
			if (IS_OBJECT_PTR_VALID(CurrentSelected))
			{
				Type = EGeomtryItemType::EGeomtryPoint == CurrentSelected->GetGeomtryItemTypeConstRef() ? 0 : 1;
				pointGenerator.computeStruct();
				if (Type == 0 && Cast<AGeometryPoint>(CurrentSelected))
				{
					Index = Cast<AGeometryPoint>(CurrentSelected)->getPointData()->displaySort;
				}
				else if (Type == 1 && Cast<AGeometryLine>(CurrentSelected))
				{
					Index = Cast<AGeometryLine>(CurrentSelected)->getLineData()->getDisplaySort();
				}
			}
		}
		else if (ESectionType::ERectangle == CurrentSectionType)
		{
			Type = 2;
			Index = 0;
		}
		else if (ESectionType::EEllipse == CurrentSectionType)
		{
			Type = 3;
			Index = 0;
		}
		OnSelectionChanged.ExecuteIfBound(Type, Index);
	}
	else if (ECutoutSectionToolType::EDrawPoint == ToolType)
	{
		if (ESectionType::ECustomPlan == CurrentSectionType)
		{
			if (this->AddGeomtryPoint(InMousePosition))
			{
				OnCutoutSectionPropertyChanged.ExecuteIfBound();
			}
		}
		else
		{
			SOneButtonWidget::PopupModalWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Error"))
				, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
				, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
		}
	}
}

void FCutoutSectionManager::OnMouseRightButtonClick(const FVector& InMousePosition)
{
	this->ChangeDrawTool(ECutoutSectionToolType::EArrow);
}

FText FCutoutSectionManager::GetSelectedItemName() const
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		return CurrentSelected->GetItemTypeName();
	}
	return FText();
}

void FCutoutSectionManager::Reset()
{
	if (IS_OBJECT_PTR_VALID(CurrentHover))
	{
		CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelected))
	{
		CurrentSelected->OnSlectionChanged(false);
		CurrentSelected = nullptr;
	}
}

void FCutoutSectionManager::SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver)
{
	AGeometryItemBase* NewSelection = nullptr;
	if (IsOver)
	{
		int32 NewSelectionIndex = -1;
		if (0 == InType)
		{//Point
			NewSelectionIndex = InIndex;
			for (auto& iter : geomtryPointList)
			{
				auto ptr = iter->getPointData();
				if (ptr->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}

		}
		else if (1 == InType)
		{//Line
			NewSelectionIndex = InIndex;
			for (auto& iter : geomtryLineList)
			{
				auto ptr = iter->getLineData();
				if (ptr->startPointData->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}
		}
	}
	this->SelectAnotherGeometryItem(NewSelection);
}

void FCutoutSectionManager::SelectAnotherGeometryItem(AGeometryItemBase* NewSelection)
{
	if (CurrentSelected != NewSelection)
	{
		if (IS_OBJECT_PTR_VALID(CurrentSelected))
		{
			CurrentSelected->OnSlectionChanged(false);
		}
		if (IS_OBJECT_PTR_VALID(NewSelection))
		{
			NewSelection->OnSlectionChanged(true);
		}
		CurrentSelected = NewSelection;
	}
}

void FCutoutSectionManager::OnDeleteKeyClick()
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		CurrentSelected->DeleteGeomtryItem();
		int32 Pos = GeomtryList.Find(CurrentSelected);



		int32 pointIndex = geomtryPointList.Find(Cast<AGeometryPoint>(CurrentSelected));
		int32 lindIndex = geomtryLineList.Find(Cast<AGeometryLine>(CurrentSelected));
		if (pointIndex != INDEX_NONE)
		{
			auto ptr = geomtryPointList[pointIndex]->getPointData();
			pointGenerator.deletePoint(ptr);
			lineGenerator.deletePoint(ptr);

		}
		else if (lindIndex != INDEX_NONE)
		{
			auto ptrLine = geomtryLineList[lindIndex]->getLineData();
			auto ptrPointA = ptrLine->startPointData;
			auto ptrPointB = ptrLine->endPointData;
			pointGenerator.deletePoint(ptrPointA);
			pointGenerator.deletePoint(ptrPointB);
			lineGenerator.deletePoint(ptrPointA);
			lineGenerator.deletePoint(ptrPointB);
		}
		clearPointsAndLinesActor();
		generatePointActors(geomtryPointList);
		auto pointDatas = pointGenerator.getPoints();
		generateLineActors(geomtryLineList);

		RefreshCustomPlan();
		OnCutoutSectionPropertyChanged.ExecuteIfBound();
	}
}

void FCutoutSectionManager::ClearCutoutSection()
{
	for (auto& Iter : GeomtryList)
	{
		Iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	GeomtryList.Empty();
	geomtryPointList.Empty();
	geomtryLineList.Empty();
	pointGenerator.clear();
	lineGenerator.clear();
	currentPoints.Empty();
	CurrentHover = nullptr;
	CurrentSelected = nullptr;
	CurrentSectionType = ESectionType::ECustomPlan;
	if (IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan->DeleteGeomtryItem();
		CustomPlan = nullptr;
	}
	ToolType = ECutoutSectionToolType::EArrow;
}

void FCutoutSectionManager::clearPointsAndLinesActor()
{
	for (auto& iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	geomtryPointList.Empty();
	geomtryLineList.Empty();
	if (IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan->DeleteGeomtryItem();
		CustomPlan = nullptr;
	}
}

void FCutoutSectionManager::generatePointActors(TArray<AGeometryPoint*>& pointActors)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	for (auto iter : pointGenerator.getPoints())
	{
		AGeometryPoint* newPoint = FGeometryItemList::Get().CreateGeomtryItem<AGeometryPoint, FGeomtryPointProperty>(Wrold, iter->pointProperty);
		newPoint->setPointData(iter);
		pointActors.Add(newPoint);
	}
}

void FCutoutSectionManager::generateLineActors(TArray<AGeometryLine*>& lineActors)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	auto num = lineGenerator.getLines().Num();
	int32 i = 0;
	for (auto iter : lineGenerator.getLines())
	{
		AGeometryLine* newLine = FGeometryItemList::Get().CreateGeomtryItem<AGeometryLine, FGeomtryLineProperty>(Wrold, iter->lineProperty);
		newLine->SetLineData(iter);

		auto startPoint = newLine->getLineData()->startPointData;
		auto endPoint = newLine->getLineData()->endPointData;
		if (num > 2)
		{
			if (startPoint->structSort > endPoint->structSort && startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
			{

			}
			else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
			{
				startPoint = newLine->getLineData()->endPointData;
				endPoint = newLine->getLineData()->startPointData;
			}
		}
		TArray<FVector> newVertex;
		getLineVertex(startPoint, endPoint, 100, newVertex);
		newLine->setVertex(newVertex);

		lineActors.Add(newLine);
		++i;
	}
}

bool FCutoutSectionManager::ConstructArchiveData(FSectionCutOutOperation& OutCutoutData)
{
	OutCutoutData.PlanBelongs = this->CurrentPlanType;
	OutCutoutData.SectionType = CurrentSectionType;
	OutCutoutData.Points.Empty();
	OutCutoutData.Lines.Empty();
	int32 i = 0;

	if (OutCutoutData.SectionType == ESectionType::ECustomPlan)
	{
		for (auto& iter : geomtryPointList)
		{
			FGeomtryPointProperty pointData;
			bool Res = iter->ConstructSaveData(static_cast<void*>(&pointData));
			if (!Res)
				return false;
			OutCutoutData.Points.Add(pointData);
		}
		for (auto& iter : geomtryLineList)
		{
			FGeomtryLineProperty lineData;
			bool Res = iter->ConstructSaveData(static_cast<void*>(&lineData));
			if (!Res)
				return false;
			OutCutoutData.Lines.Add(lineData);
		}

		return true;
	}

	for (auto& Iter : GeomtryList)
	{
		if (EGeomtryItemType::EGeomtryPoint == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryPointProperty PointData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&PointData));
			if (!Res)
				return false;
			PointData.ID = i;
			OutCutoutData.Points.Add(PointData);
		}
		else if (EGeomtryItemType::EGeomtryLine == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryLineProperty LineData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&LineData));
			if (!Res)
				return false;
			LineData.ID = i++;
			OutCutoutData.Lines.Add(LineData);
		}
		else if (EGeomtryItemType::EGeometryRectangle == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryRectanglePlanProperty RectangleData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&RectangleData));
			if (!Res)
				return false;
			OutCutoutData.Rectangle = RectangleData;
		}
		else if (EGeomtryItemType::EGeometryEllipse == Iter->GetGeomtryItemTypeConstRef())
		{
			FGeomtryEllipsePlanProperty EllipseData;
			bool Res = Iter->ConstructSaveData(static_cast<void*>(&EllipseData));
			if (!Res)
				return false;
			OutCutoutData.Ellipse = EllipseData;
		}
	}
	return true;
}

bool FCutoutSectionManager::ChangeDrawTool(const ECutoutSectionToolType& InNewToolType)
{
	if (ECutoutSectionToolType::EClear == InNewToolType)
	{
		EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
			FText::FromStringTable(FName("PosSt"), TEXT("Warning"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Make sure to clear this cutout section ?"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
		if (EPopButtonType::Confirm != ButtonType)
			return false;

		ECutoutSectionToolType CurrentType = ToolType;
		this->ClearCutoutSection();
		ToolType = CurrentType;
		OnCutoutSectionPropertyChanged.ExecuteIfBound();
		return true;
	}
	if (ToolType != InNewToolType)
	{
		TArray<AGeometryItemBase*> unionItems;
		unionItems.Append(geomtryPointList);
		unionItems.Append(geomtryLineList);
		unionItems.Append(GeomtryList);

		if (ECutoutSectionToolType::EArrow == InNewToolType)
		{
			ToolType = InNewToolType;
			ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
		}
		else if (ECutoutSectionToolType::EDrawPoint == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Crosshairs;
				ToolType = InNewToolType;
			}
			else
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return false;
			}
		}
		else if (ECutoutSectionToolType::EDrawRectangle == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				if (this->AddGeometryRectangle())
				{
					OnCutoutSectionPropertyChanged.ExecuteIfBound();
				}
			}
			else
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return false;
			}
		}
		else if (ECutoutSectionToolType::EDrawEllipse == InNewToolType)
		{
			if (0 == unionItems.Num())
			{
				if (this->AddGeometryEllipse())
				{
					OnCutoutSectionPropertyChanged.ExecuteIfBound();
				}
			}
			else
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return false;
			}
		}

		else if (ECutoutSectionToolType::EImportCAD == InNewToolType)
		{
			if (unionItems.Num() > 0)
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
				return false;
			}
		}
	}

	return true;
}

bool FCutoutSectionManager::LoadFromArchiveData(const FSectionCutOutOperation& InCutoutData)
{
	ClearCutoutSection();
	CurrentSectionType = InCutoutData.SectionType;
	CurrentPlanType = InCutoutData.PlanBelongs;
	FVector planNormal = NSPlanType::GetPlanNormal(CurrentPlanType);
	pointGenerator.setNormal(planNormal);
	lineGenerator.setNormal(planNormal);

	if (ESectionType::ECustomPlan == CurrentSectionType)
	{
		if (InCutoutData.Points.Num() <= 0 || (InCutoutData.Points.Num() > 1 && InCutoutData.Lines.Num() != InCutoutData.Points.Num()))
			return false;


		for (auto& iter : InCutoutData.Points)
		{
			FGeomtryPointProperty PointProperty(iter);
			PointProperty.PlanBelongs = InCutoutData.PlanBelongs;

			FVector newPointLocation(iter.PointLocation());
			FGeometryDatas::FormatLocation(this->CurrentPlanType, newPointLocation);
			pointGenerator.addPoint(newPointLocation, PointProperty);
		}

		pointGenerator.computeStruct();
		generatePointActors(geomtryPointList);

		auto pointDatas = pointGenerator.getPoints();
		lineGenerator.initializeLines(pointDatas, true);
		generateLineActors(geomtryLineList);
		//crosssection save by displayIndex
		for (int32 displayIndex = 0; displayIndex < InCutoutData.Lines.Num(); ++displayIndex)
		{
			for (auto& iter : geomtryLineList)
			{
				auto ptr = iter->getLineData();
				auto startIndex = ptr->getDisplaySort();
				if (startIndex == displayIndex)
				{
					auto newProperty = InCutoutData.Lines[displayIndex];
					iter->setProperty(static_cast<void*>(&newProperty));


					auto startPoint = iter->getLineData()->startPointData;
					auto endPoint = iter->getLineData()->endPointData;
					if (pointGenerator.getPointCount() > 2)
					{
						if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
						{
							startPoint = iter->getLineData()->endPointData;
							endPoint = iter->getLineData()->startPointData;
						}
					}
					TArray<FVector> newVertex;
					getLineVertex(startPoint, endPoint, 100, newVertex);
					iter->setVertex(newVertex);
					break;
				}
			}
		}
		/*currentPoints.Empty();

		FGeomtryPointProperty::PointsLocation(InCutoutData.Points, currentPoints);
		FVector normal = FCrossSectionData::GetPolygonOutsideNormal(CurrentPlanType, currentPoints);

		int32 numOfPoints = currentPoints.Num();
		for (int32 cur = 0; cur < numOfPoints; cur++)
		{
			FGeomtryPointProperty PointProperty(InCutoutData.Points[cur]);
			AGeometryPoint* NewPoint = FGeometryItemList::Get().CreateGeomtryItem<AGeometryPoint, FGeomtryPointProperty>(ACatalogPlayerController::Get()->GetWorld(), PointProperty);
			GeomtryList.Add(NewPoint);
			int32 pre = (cur - 1 + numOfPoints) % numOfPoints;
			if (numOfPoints < 2)
			{
				continue;
			}
			if (numOfPoints == 2 && cur == 0)
			{

			}
			else
			{
				NewPoint->SetPrePointLocation(currentPoints[pre]);
			}
			FGeomtryLineProperty LineProperty(InCutoutData.Lines[cur]);
			LineProperty.PlanBelongs = InCutoutData.PlanBelongs;
			LineProperty.StartLocation = currentPoints[cur];
			LineProperty.EndLocation = currentPoints[(cur + 1) % currentPoints.Num()];
			AGeometryLine* NewLine = FGeometryItemList::Get().CreateGeomtryItem<AGeometryLine, FGeomtryLineProperty>(ACatalogPlayerController::Get()->GetWorld(), LineProperty);
			GeomtryList.Add(NewLine);
		}
		if (GeomtryList.Num() < 0)
		{
			return false;
		}*/

		//TArray<FVector> WorldPoints;
		//FGeomtryPointProperty::PointsLocation(InCutoutData.Points, WorldPoints);

		//int32 Offset = GeomtryList.AddZeroed(InCutoutData.Points.Num() + InCutoutData.Lines.Num());
		//int32 Index = 0;
		//for (auto& Iter : InCutoutData.Points)
		//{
		//	FGeomtryPointProperty PointProperty(Iter);
		//	PointProperty.PlanBelongs = InCutoutData.PlanBelongs;
		//	AGeometryPoint* NewPoint = FGeometryItemList::Get().CreateGeomtryItem<AGeometryPoint, FGeomtryPointProperty>(ACatalogPlayerController::Get()->GetWorld(), PointProperty);
		//	GeomtryList[Offset + (Index << 1)] = NewPoint;
		//	++Index;
		//}
		//Index = 0;
		//for (auto& LineIter : InCutoutData.Lines)
		//{
		//	FGeomtryLineProperty LineProperty(LineIter);
		//	LineProperty.PlanBelongs = InCutoutData.PlanBelongs;
		//	LineProperty.StartLocation = WorldPoints[Index];
		//	LineProperty.EndLocation = WorldPoints[(Index + 1) % WorldPoints.Num()];
		//	AGeometryLine* NewLine = FGeometryItemList::Get().CreateGeomtryItem<AGeometryLine, FGeomtryLineProperty>(ACatalogPlayerController::Get()->GetWorld(), LineProperty);
		//	GeomtryList[Offset + (Index << 1) + 1] = NewLine;
		//	++Index;
		//}
		this->RefreshCustomPlan();
	}
	else if (ESectionType::ERectangle == CurrentSectionType)
	{
		UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
		FGeomtryRectanglePlanProperty NewRectangleProperty(InCutoutData.Rectangle);
		NewRectangleProperty.PlanBelongs = InCutoutData.PlanBelongs;
		AGeometryRectanglePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryRectanglePlan, FGeomtryRectanglePlanProperty>(Wrold, NewRectangleProperty);
		GeomtryList.Add(NewRectangle);
	}
	else if (ESectionType::EEllipse == CurrentSectionType)
	{
		UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
		FGeomtryEllipsePlanProperty EllipseProperty(InCutoutData.Ellipse);
		EllipseProperty.PlanBelongs = InCutoutData.PlanBelongs;
		AGeometryEllipsePlan* NewRectangle = FGeometryItemList::Get().CreateGeomtryItem<AGeometryEllipsePlan, FGeomtryEllipsePlanProperty>(Wrold, EllipseProperty);
		GeomtryList.Add(NewRectangle);
	}
	return true;
}

void FCutoutSectionManager::getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex)
{
	if (pointGenerator.getPointCount() <= 2)
	{
		lineGenerator.getLineVertexOrder(startPoint, endPoint, stepNum, outVertex);
	}
	else
	{
		lineGenerator.getLineVertex(startPoint, endPoint, stepNum, outVertex);
	}
}

bool FCutoutSectionManager::ChangeCutoutSectionProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty)
{
	auto changePoint = pointGenerator.setPropertyByDisplaySort(InPointProperty.ID, InPointProperty);
	for (auto iter : geomtryPointList)
	{
		if (iter->getPointData() == changePoint)
		{
			FGeomtryPointProperty newProperty = InPointProperty;
			iter->ChangeProperty(InType, static_cast<void*>(&newProperty));
		}
	}
	pointGenerator.computeStruct();
	for (auto iter : geomtryLineList)
	{
		if (iter->getLineData()->containPoint(changePoint))
		{
			auto startPoint = iter->getLineData()->startPointData;
			auto endPoint = iter->getLineData()->endPointData;

			if (pointGenerator.getPointCount() > 2)
			{
				if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
				{
					startPoint = iter->getLineData()->endPointData;
					endPoint = iter->getLineData()->startPointData;
				}
			}
			TArray<FVector> newVertex;
			getLineVertex(startPoint, endPoint, 100, newVertex);
			iter->setVertex(newVertex);
		}
	}
	this->RefreshCustomPlan();
	OnCutoutSectionPropertyChanged.ExecuteIfBound();
	return true;
}

bool FCutoutSectionManager::ChangeCutoutSectionProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty)
{
	pointGenerator.computeStruct();

	auto changeLine = lineGenerator.setLineProperty(InLineProperty.ID, InLineProperty);
	if (changeLine == nullptr)
	{
		return false;
	}
	auto startPoint = changeLine->startPointData;
	auto endPoint = changeLine->endPointData;

	if (pointGenerator.getPointCount() > 2)
	{
		if (startPoint->structSort > endPoint->structSort && startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
		{

		}
		else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
		{
			startPoint = changeLine->endPointData;
			endPoint = changeLine->startPointData;
		}
	}
	TArray<FVector> newVertex;
	getLineVertex(startPoint, endPoint, 100, newVertex);

	for (auto& iter : geomtryLineList)
	{
		if (iter->getData() == changeLine)
		{
			FGeomtryLineProperty NewProperty = InLineProperty;
			iter->setProperty(InType, static_cast<void*>(&NewProperty));
			iter->setVertex(newVertex);
			break;
		}
	}
	//if (res)
	{
		this->RefreshCustomPlan();
		OnCutoutSectionPropertyChanged.ExecuteIfBound();
	}
	return true;

	//checkf(GeomtryList.IsValidIndex((InLineProperty.ID << 1) + 1), TEXT("Invalid point index %d"), (InLineProperty.ID << 1) + 1);
	//FGeomtryLineProperty NewProperty = InLineProperty;
	//bool Res = GeomtryList[(InLineProperty.ID << 1) + 1]->ChangeProperty(InType, static_cast<void*>(&NewProperty));
	//if (Res)
	//{
	//	this->RefreshCustomPlan();
	//	OnCutoutSectionPropertyChanged.ExecuteIfBound();
	//}
	//return Res;
}

bool FCutoutSectionManager::ChangeCutoutSectionProperty(const int32& InType, const FGeomtryRectanglePlanProperty& InRectangleProperty)
{
	checkf(1 == GeomtryList.Num() && EGeomtryItemType::EGeometryRectangle == GeomtryList[0]->GetGeomtryItemTypeConstRef(), TEXT("GeomtryList index 0 is not rectangle!"));
	FGeomtryRectanglePlanProperty NewRectangleProperty(InRectangleProperty);
	bool Res = GeomtryList[0]->ChangeProperty(InType, static_cast<void*>(&NewRectangleProperty));
	if (Res)
	{
		this->RefreshCustomPlan();
		OnCutoutSectionPropertyChanged.ExecuteIfBound();
	}
	return Res;
}

bool FCutoutSectionManager::ChangeCutoutSectionProperty(const int32& InType, const FGeomtryEllipsePlanProperty& InEllipseProperty)
{
	checkf(1 == GeomtryList.Num() && EGeomtryItemType::EGeometryEllipse == GeomtryList[0]->GetGeomtryItemTypeConstRef(), TEXT("GeomtryList index 0 is not ellipse!"));
	FGeomtryEllipsePlanProperty NewEllipseProperty(InEllipseProperty);
	bool Res = GeomtryList[0]->ChangeProperty(InType, static_cast<void*>(&NewEllipseProperty));
	if (Res)
	{
		//this->RefreshCustomPlan();
		OnCutoutSectionPropertyChanged.ExecuteIfBound();
	}
	return Res;
}

void FCutoutSectionManager::RefreshCustomPlan()
{
	FGeomtryCustomPlanProperty CustomPlanProperty;
	bool Res = true;
	CustomPlanProperty.PolygonData.PolygonPlan = CurrentPlanType;
	pointGenerator.computeStruct();
	if (pointGenerator.getPointCount() >= 2)
	{
		TArray<TSharedPtr<FGeometryPointData>> tempPoints = pointGenerator.getPoints();
		tempPoints.Sort([](TSharedPtr<FGeometryPointData> a, TSharedPtr<FGeometryPointData>b) {return a->structSort < b->structSort; });

		for (int32 cur = 0; cur < tempPoints.Num(); ++cur)
		{
			TArray<FVector> newVertex;
			int32 next = (cur + 1) % tempPoints.Num();
			getLineVertex(tempPoints[cur], tempPoints[next], 100, newVertex);
			for (auto& iter : newVertex)
			{
				CustomPlanProperty.PolygonData.PolygonVertice.AddUnique(iter);
			}
			if (next == 0 && newVertex.Num() > 0)
			{
				CustomPlanProperty.PolygonData.PolygonVertice.Add(newVertex.Last(0));
			}
		}
	}
	if (!Res)
		return;

	if (!IS_OBJECT_PTR_VALID(CustomPlan))
	{
		CustomPlan = FGeometryItemList::Get().CreateGeomtryItem<AGeometryCustomPlan, FGeomtryCustomPlanProperty>(ACatalogPlayerController::Get()->GetWorld(), CustomPlanProperty);
	}
	else
	{
		CustomPlan->SetCustomPlanProperty(CustomPlanProperty);
		CustomPlan->Refresh();
	}
	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
}

void FCutoutSectionManager::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	CameraLocation = NewLocation;
	CameraWidth = NewWidth;
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	for (auto& Iter : unionItems)
	{
		const EGeomtryItemType& ItemType = Iter->GetGeomtryItemTypeConstRef();
		float Scale = -1.0f;
		if (EGeomtryItemType::EGeomtryPoint == ItemType)
		{
			Scale = FMath::Max<float>(NewWidth * 0.00005f, 0.0005);
			Scale = FMath::Min<float>(Scale, 0.005);
		}
		else if (EGeomtryItemType::EGeomtryLine == ItemType)
			Scale = FMath::Max<float>((NewWidth / 400.0f) * 1.6f, 0.1);
		if (Scale > 0.0f)
			Iter->ScaleGeomtryItem(Scale);
	}
}

bool FCutoutSectionManager::AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines)
{
	clearPointsAndLinesActor();

	for (auto& LineIte : InLines)
	{
		FVector StartPos = LineIte.StartLocation;
		//FVector EndPos = LineIte.EndLocation;
		switch (CurrentPlanType)
		{
		case EPlanPolygonBelongs::EXY_Plan:StartPos.Z = 0.0; break;
		case EPlanPolygonBelongs::EXZ_Plan:
		{
			StartPos.X = LineIte.StartLocation.X;
			StartPos.Y = 0.0;
			StartPos.Z = LineIte.StartLocation.Y;
		}
		break;
		case EPlanPolygonBelongs::EYZ_Plan:
		{
			StartPos.X = 0.0;
			StartPos.Y = LineIte.StartLocation.Y;
			StartPos.Z = LineIte.StartLocation.X;
		}
		break;
		case EPlanPolygonBelongs::EUnknown:break;
		default:checkNoEntry(); break;
		}
		FGeometryDatas::FormatLocation(this->CurrentPlanType, StartPos);
		//FGeometryDatas::FormatLocation(this->CurrentPlanType, EndPos);

		FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(StartPos.X));
		FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(StartPos.Y));
		FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(StartPos.Z));
		auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);

		auto pointPtr = MakeShared<FGeometryPointData>();
		pointPtr->pointProperty.CopyData(pointProperty);
		pointPtr->position = StartPos;
		pointPtr->structSort = pointGenerator.getPointCount();
		pointPtr->displaySort = pointGenerator.getPointCount();
		pointGenerator.addPoint(pointPtr);
	}

	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	TArray<TSharedPtr<FGeometryPointData>> Points = pointGenerator.getPoints();
	TArray<TSharedPtr<FGeometryLineData>> Lines;
	for (int32 i = 0; i < InLines.Num(); ++i)
	{
		int32 NextI = (i + 1) % InLines.Num();
		TSharedPtr<FGeometryLineData> NewLine = MakeShared<FGeometryLineData>();
		NewLine->startPointData = Points[i];
		NewLine->endPointData = Points[NextI];
		NewLine->lineProperty.CopyData(InLines[i]);
		NewLine->lineProperty.StartLocation = NewLine->startPointData->position;
		NewLine->lineProperty.EndLocation = NewLine->endPointData->position;
		NewLine->lineProperty.PlanBelongs = CurrentPlanType;
		NewLine->lineProperty.ID = i;
		Lines.Add(NewLine);
	}
	lineGenerator.initializeLines(Lines, true);

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);



	if (geomtryPointList.Num() > 0)
	{
		RefreshCustomPlan();
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CustomPlan))
		{
			CustomPlan->ClearPlan();
		}
	}
	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);

	OnCutoutSectionPropertyChanged.ExecuteIfBound();

	return true;
}


#undef LOCTEXT_NAMESPACE