// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemList.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryCustomPlan.h"

enum class ECutoutSectionToolType : uint8
{
	EArrow,
	EDrawPoint,
	EDrawRectangle,
	EDrawEllipse,
	EClear = 5,
	EImportCAD
};

/**
 *
 */
class DESIGNSTATION_API FCutoutSectionManager
{
private:
	AGeometryItemBase* CurrentHover;
	AGeometryItemBase* CurrentSelected;
	TArray<AGeometryItemBase*> GeomtryList;
	TArray<AGeometryPoint*> geomtryPointList;
	TArray<AGeometryLine*> geomtryLineList;

	//
	EPlanPolygonBelongs CurrentPlanType;
	ESectionType		CurrentSectionType;
	AGeometryCustomPlan* CustomPlan;

	//
	FVector		CameraLocation;
	float		CameraWidth;

	//
	ECutoutSectionToolType ToolType;

	TArray<FVector>	currentPoints;

public:

	GenerateSet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateGet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateGet(ECutoutSectionToolType, ToolType)

public:

	FNoParamDelegate OnCutoutSectionPropertyChanged;
	FTwoIntDelegate OnSelectionChanged;

public:
	FCutoutSectionManager();
	virtual ~FCutoutSectionManager();

	FText GetSelectedItemName() const;

	void SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver);

	//
	bool ChangeCutoutSectionProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty);
	bool ChangeCutoutSectionProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty);
	bool ChangeCutoutSectionProperty(const int32& InType, const FGeomtryRectanglePlanProperty& InRectangleProperty);
	bool ChangeCutoutSectionProperty(const int32& InType, const FGeomtryEllipsePlanProperty& InEllipseProperty);
	//

	void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth);

	bool ChangeDrawTool(const ECutoutSectionToolType& InNewToolType);

	void OnMouseMove();

	void OnMouseLeftButtonClick(const FVector& InMousePosition);

	void OnMouseRightButtonClick(const FVector& InMousePosition);

	void OnDeleteKeyClick();

	void Reset();

	void ClearCutoutSection();

	void clearPointsAndLinesActor();

	void generatePointActors(TArray<AGeometryPoint*>& pointActors);

	void generateLineActors(TArray<AGeometryLine*>& lineActors);

	bool ConstructArchiveData(FSectionCutOutOperation& OutCutoutData);

	bool LoadFromArchiveData(const FSectionCutOutOperation& InCutoutData);

	void getLineVertex(TSharedPtr<FGeometryPointData> startPoint, TSharedPtr<FGeometryPointData> endPoint, const int32 stepNum, TArray<FVector>& outVertex);
	
	bool AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines);
private:

	void SelectAnotherGeometryItem(AGeometryItemBase* NewSelection);

	bool AddGeomtryPoint(const FVector& InPointLocation);

	bool AddGeometryRectangle();

	bool AddGeometryEllipse();

	void RefreshCustomPlan();

private:
	FGeometryPointGenerator pointGenerator;
	FGeometryLineGenerator	lineGenerator;
};
