// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryItemList.h"


TSharedPtr<FGeometryItemList> FGeometryItemList::GeomtryActorPool = nullptr;

FGeometryItemList& FGeometryItemList::Get()
{
	if (!FGeometryItemList::GeomtryActorPool.IsValid())
	{
		UE_LOG(LogTemp, Log, TEXT(" FGeometryItemList::GeomtryActorPool is nullptr "));
		FGeometryItemList::GeomtryActorPool = MakeShareable<FGeometryItemList>(new FGeometryItemList());
	}
	return *FGeometryItemList::GeomtryActorPool;
}

FGeometryItemList::FGeometryItemList()
{
}

FGeometryItemList::~FGeometryItemList()
{
	for (auto Iter : GeomtryItemPool)
	{
		if (IS_OBJECT_PTR_VALID(Iter) && IsValid(Iter))
			Iter->Destroy();
	}
	GeomtryItemPool.Empty();
}
