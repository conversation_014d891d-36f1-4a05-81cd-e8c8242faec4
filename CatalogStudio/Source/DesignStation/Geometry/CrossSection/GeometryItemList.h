// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryItemBase.h"
/**
 *
 */
class DESIGNSTATION_API FGeometryItemList
{
public:
	FGeometryItemList();
	virtual ~FGeometryItemList();

public:

	template<class T, class T2>
	T* CreateGeomtryItem(UWorld* InWorld, const T2& InProperty)
	{
		T* res = nullptr;
		for (auto Iter : GeomtryItemPool)
		{
			if (Iter->GetIsDeletedConstRef() && Iter->IsCorrectPropertyData(T2::GetName()))
			{
				res = Cast<T>(Iter);
				Iter->BringBack((void*)&InProperty);
				break;
			}
		}
		if (nullptr == res)
		{
			UE_LOG(LogTemp, Log, TEXT("------ Create new actor ------"));
			res = T::Create(InWorld, InProperty);
			GeomtryItemPool.Add(res);
		}
		return res;
	}

	static FGeometryItemList& Get();

private:

	static TSharedPtr<FGeometryItemList> GeomtryActorPool;

	TArray<AGeometryItemBase*> GeomtryItemPool;
};
