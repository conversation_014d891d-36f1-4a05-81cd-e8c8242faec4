// Fill out your copyright notice in the Description page of Project Settings.

#include "LoftRoutineManager.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/UI/PopUI/STwoButtonsWidget.h"
#include "Runtime/Engine/Classes/Kismet/KismetTextLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "MagicCore/Public/ArrayOperatorLibrary.h"
#include "DesignStation/UI/PopUI/SOneButtonWidget.h"

#define LOCTEXT_NAMESPACE "LoftRoutineManager"

FLoftRoutineManager::FLoftRoutineManager()
	:CurrentHover(nullptr)
	, CurrentSelected(nullptr)
	, CurrentPlanType(EPlanPolygonBelongs::EUnknown)
	, ToolType(ELoftRoutineToolType::EArrow)
{
}

FLoftRoutineManager::~FLoftRoutineManager()
{
}

bool FLoftRoutineManager::AddGeomtryPoint(const FVector& InPointLocation)
{
	TArray<FGeomtryLineProperty> tempLinesProperty; //by display sort
	for (auto& iter : lineGenerator.getLines())
	{
		tempLinesProperty.Add(iter->lineProperty);
	}
	clearPointsAndLinesActor();

	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	bool Res = false;
	FVector NewPointLocation(InPointLocation);
	FGeometryDatas::FormatLocation(this->CurrentPlanType, NewPointLocation);

	FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(NewPointLocation.X));
	FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Y));
	FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(NewPointLocation.Z));
	auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);

	auto pointPtr = MakeShared<FGeometryPointData>();
	pointPtr->pointProperty.CopyData(pointProperty);
	pointPtr->position = NewPointLocation;
	pointPtr->structSort = pointGenerator.getPointCount();
	pointPtr->displaySort = pointGenerator.getPointCount();
	pointGenerator.addPoint(pointPtr);
	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	if (pointGenerator.getPointCount() == 2)
	{
		lineGenerator.initializeLines(pointGenerator.getPoints(), false);
	}
	else if (pointGenerator.getPointCount() > 2)
	{
		lineGenerator.addPoint(pointPtr, false);
	}

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);

	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
	return true;
}

void FLoftRoutineManager::OnMouseMove()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	FHitResult Res;
	FVector WorldLocation, WorldDir;
	PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
	if (bHit)
	{
		if (CurrentHover != Res.GetActor())
		{
			if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
				CurrentHover->OnMouseLeave();
			AGeometryItemBase* HoverActor = Cast<AGeometryItemBase>(Res.GetActor());
			if (IS_OBJECT_PTR_VALID(HoverActor))
				HoverActor->OnMouseOver();
			CurrentHover = HoverActor;
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CurrentHover) && CurrentSelected != CurrentHover)
			CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
}

void FLoftRoutineManager::OnMouseLeftButtonClick(const FVector& InMousePosition)
{
	if (ELoftRoutineToolType::EArrow == ToolType)
	{
		ACatalogPlayerController* PC = ACatalogPlayerController::Get();
		FHitResult Res;
		FVector WorldLocation, WorldDir;
		PC->DeprojectMousePositionToWorld(WorldLocation, WorldDir);
		TArray<AGeometryItemBase*> unionItems;
		unionItems.Append(geomtryLineList);
		unionItems.Append(geomtryPointList);
		bool bHit = AGeometryItemBase::LineTraceForSingleItem(unionItems, WorldLocation, WorldLocation + WorldDir * 10000.0f, Res);
		if (bHit)
		{
			AGeometryItemBase* SelectActor = Cast<AGeometryItemBase>(Res.GetActor());
			this->SelectAnotherGeometryItem(SelectActor);
		}
		else
		{
			this->SelectAnotherGeometryItem(nullptr);
		}
		int32 Type = 0;
		int32 Index = -1;
		if (IS_OBJECT_PTR_VALID(CurrentSelected))
		{
			Type = EGeomtryItemType::EGeomtryPoint == CurrentSelected->GetGeomtryItemTypeConstRef() ? 0 : 1;
			pointGenerator.computeStruct();
			if (Type == 0 && Cast<AGeometryPoint>(CurrentSelected))
			{
				Index = Cast<AGeometryPoint>(CurrentSelected)->getPointData()->displaySort;
			}
			else if (Type == 1 && Cast<AGeometryLine>(CurrentSelected))
			{
				Index = Cast<AGeometryLine>(CurrentSelected)->getLineData()->getDisplaySort();
			}
		}
		OnSelectionChanged.ExecuteIfBound(Type, Index);
	}
	else if (ELoftRoutineToolType::EDrawPoint == ToolType)
	{
		if (this->AddGeomtryPoint(InMousePosition))
		{
			OnLoftRoutinePropertyChanged.ExecuteIfBound();
		}
	}
}

void FLoftRoutineManager::OnMouseRightButtonClick(const FVector& InMousePosition)
{
	this->ChangeDrawTool(ELoftRoutineToolType::EArrow);
}

FText FLoftRoutineManager::GetSelectedItemName() const
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		return CurrentSelected->GetItemTypeName();
	}
	return FText();
}

void FLoftRoutineManager::Reset()
{
	if (IS_OBJECT_PTR_VALID(CurrentHover))
	{
		CurrentHover->OnMouseLeave();
		CurrentHover = nullptr;
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelected))
	{
		CurrentSelected->OnSlectionChanged(false);
		CurrentSelected = nullptr;
	}
}

void FLoftRoutineManager::SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver)
{
	AGeometryItemBase* NewSelection = nullptr;
	if (IsOver)
	{
		int32 NewSelectionIndex = -1;
		if (0 == InType)
		{//Point
			NewSelectionIndex = InIndex;
			for (auto& iter : geomtryPointList)
			{
				auto ptr = iter->getPointData();
				if (ptr->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}

		}
		else if (1 == InType)
		{//Line
			NewSelectionIndex = InIndex;
			for (auto& iter : geomtryLineList)
			{
				auto ptr = iter->getLineData();
				if (ptr->startPointData->displaySort == InIndex)
				{
					NewSelection = iter;
				}
			}
		}
		//else
		//{//Others
		//	NewSelectionIndex = InIndex;
		//}
		//if (GeomtryList.IsValidIndex(NewSelectionIndex))
		//	NewSelection = GeomtryList[NewSelectionIndex];
	}
	this->SelectAnotherGeometryItem(NewSelection);
}

void FLoftRoutineManager::SelectAnotherGeometryItem(AGeometryItemBase* NewSelection)
{
	if (CurrentSelected != NewSelection)
	{
		if (IS_OBJECT_PTR_VALID(CurrentSelected))
		{
			CurrentSelected->OnSlectionChanged(false);
		}
		if (IS_OBJECT_PTR_VALID(NewSelection))
		{
			NewSelection->OnSlectionChanged(true);
		}
		CurrentSelected = NewSelection;
	}
}

void FLoftRoutineManager::OnDeleteKeyClick()
{
	if (IS_OBJECT_PTR_VALID(CurrentSelected) && EGeomtryItemType::EGeomtryLine != CurrentSelected->GetGeomtryItemTypeConstRef())
	{
		CurrentSelected->DeleteGeomtryItem();
		int32 Pos = GeomtryList.Find(CurrentSelected);

		int32 pointIndex = geomtryPointList.Find(Cast<AGeometryPoint>(CurrentSelected));
		int32 lindIndex = geomtryLineList.Find(Cast<AGeometryLine>(CurrentSelected));
		
		if (pointIndex != INDEX_NONE)
		{
			auto ptr = geomtryPointList[pointIndex]->getPointData();
			pointGenerator.deletePoint(ptr);
			lineGenerator.deletePoint(ptr,false);

		}
		else if (lindIndex != INDEX_NONE)
		{
			auto ptrLine = geomtryLineList[lindIndex]->getLineData();
			auto ptrPointA = ptrLine->startPointData;
			auto ptrPointB = ptrLine->endPointData;
			pointGenerator.deletePoint(ptrPointA);
			pointGenerator.deletePoint(ptrPointB);
			lineGenerator.deletePoint(ptrPointA);
			lineGenerator.deletePoint(ptrPointB);
		}
		clearPointsAndLinesActor();
		generatePointActors(geomtryPointList);
		auto pointDatas = pointGenerator.getPoints();
		generateLineActors(geomtryLineList);

		/*if (INDEX_NONE != Pos)
		{
			GeomtryList.RemoveAt(Pos);
			if (GeomtryList.IsValidIndex(Pos))
			{
				GeomtryList[Pos]->DeleteGeomtryItem();
				GeomtryList.RemoveAt(Pos);

				if (GeomtryList.Num() >= 2)
				{
					int32 PrePointIndex = Pos - 2;
					int32 PreLineIndex = PrePointIndex + 1;
					int32 NexPointIndex = Pos % GeomtryList.Num();
					if (GeomtryList.IsValidIndex(PrePointIndex) && GeomtryList.IsValidIndex(PreLineIndex) && GeomtryList.IsValidIndex(NexPointIndex))
					{
						AGeometryPoint* PrePoint = Cast<AGeometryPoint>(GeomtryList[PrePointIndex]);
						AGeometryLine* PreLine = Cast<AGeometryLine>(GeomtryList[PreLineIndex]);
						AGeometryPoint* NextPoint = Cast<AGeometryPoint>(GeomtryList[NexPointIndex]);
						if (IS_OBJECT_PTR_VALID(PrePoint) && IS_OBJECT_PTR_VALID(PreLine) && IS_OBJECT_PTR_VALID(NextPoint))
						{
							FGeomtryLineProperty NewProperty(PreLine->GetLineTypeConstRef(), PreLine->GetPlanBelongsConstRef(), FExpressionValuePair(PreLine->GetRadiusOrHeightConstRef()), PreLine->GetInterpPointCountDataConstRef(), PrePoint->GetPointLocation(), NextPoint->GetPointLocation());
							PreLine->SetLineProperty(NewProperty);
							PreLine->Refresh();
						}
					}
				}
			}
			else if (GeomtryList.IsValidIndex(Pos - 1))
			{
				GeomtryList[Pos - 1]->DeleteGeomtryItem();
				GeomtryList.RemoveAt(Pos - 1);
			}
		}*/
		OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
		OnLoftRoutinePropertyChanged.ExecuteIfBound();
	}
}

void FLoftRoutineManager::ClearLoftRoutine()
{
	for (auto& Iter : GeomtryList)
	{
		Iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	GeomtryList.Empty();
	currentPoints.Empty();
	geomtryPointList.Empty();
	geomtryLineList.Empty();
	pointGenerator.clear();
	lineGenerator.clear();
	CurrentHover = nullptr;
	CurrentSelected = nullptr;
	ToolType = ELoftRoutineToolType::EArrow;
}

bool FLoftRoutineManager::ConstructArchiveData(FSectionLoftOperation& OutLoftRoutineData)
{
	OutLoftRoutineData.PlanBelongs = this->CurrentPlanType;
	OutLoftRoutineData.Points.Empty();
	OutLoftRoutineData.Lines.Empty();

	int32 indexPoint = 0;
	for (auto& iter : geomtryPointList)
	{
		FGeomtryPointProperty pointData;
		pointData.ID = indexPoint;
		bool Res = iter->ConstructSaveData(static_cast<void*>(&pointData));
		if (!Res)
			return false;
		++indexPoint;
		OutLoftRoutineData.Points.Add(pointData);
	}
	int32 indexLine = 0;

	for (auto& iter : geomtryLineList)
	{
		FGeomtryLineProperty lineData;
		bool Res = iter->ConstructSaveData(static_cast<void*>(&lineData));
		if (!Res)
			return false;
		OutLoftRoutineData.Lines.Add(lineData);
	}

	return true;
}


bool FLoftRoutineManager::ChangeDrawTool(const ELoftRoutineToolType& InNewToolType)
{
	if (ELoftRoutineToolType::EClear == InNewToolType)
	{
		EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
			FText::FromStringTable(FName("PosSt"), TEXT("Warning"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Make sure to delete this loft routine ?"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
			, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));
		if (EPopButtonType::Confirm != ButtonType)
			return false;

		ELoftRoutineToolType CurrentType = ToolType;
		this->ClearLoftRoutine();
		ToolType = CurrentType;
		OnLoftRoutinePropertyChanged.ExecuteIfBound();
		return true;
	}
	if (ToolType != InNewToolType)
	{
		if (ELoftRoutineToolType::EArrow == InNewToolType)
		{
			ToolType = InNewToolType;
			ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Default;
		}
		else if (ELoftRoutineToolType::EDrawPoint == InNewToolType)
		{
			if (0 == geomtryLineList.Num())
			{
				ACatalogPlayerController::Get()->CurrentMouseCursor = EMouseCursor::Crosshairs;
				ToolType = InNewToolType;
			}
			else
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));

				return false;

			}
		}
		else if (ELoftRoutineToolType::EImportCAD == InNewToolType)
		{
			if (geomtryLineList.Num() > 0)
			{
				SOneButtonWidget::PopupModalWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error"))
					, FText::FromStringTable(FName("PosSt"), TEXT("Please delete other geometry items first!"))
					, FText::FromStringTable(FName("PosSt"), TEXT("OK")));

				return false;
			}
		}

	}
	return true;
}

bool FLoftRoutineManager::LoadFromArchiveData(const FSectionLoftOperation& InLoftRoutineData)
{
	
	CurrentPlanType = InLoftRoutineData.PlanBelongs;
	FVector planNormal = NSPlanType::GetPlanNormal(CurrentPlanType);
	pointGenerator.setNormal(planNormal);
	lineGenerator.setNormal(planNormal);

	FGeomtryPointProperty::PointsLocation(InLoftRoutineData.Points, currentPoints);

	if (InLoftRoutineData.Points.Num() <= 0)
		return false;


	for (auto& iter : InLoftRoutineData.Points)
	{
		FGeomtryPointProperty PointProperty(iter);
		PointProperty.PlanBelongs = InLoftRoutineData.PlanBelongs;

		FVector newPointLocation(iter.PointLocation());
		FGeometryDatas::FormatLocation(this->CurrentPlanType, newPointLocation);
		pointGenerator.addPoint(newPointLocation, PointProperty);
	}

	pointGenerator.computeStruct();
	generatePointActors(geomtryPointList);

	auto pointDatas = pointGenerator.getPoints();
	lineGenerator.initializeLines(pointDatas, false);
	generateLineActors(geomtryLineList);
	//crosssection save by displayIndex
	for (int32 displayIndex = 0; displayIndex < InLoftRoutineData.Lines.Num(); ++displayIndex)
	{
		for (auto& iter : geomtryLineList)
		{
			auto ptr = iter->getLineData();
			auto startIndex = ptr->getDisplaySort();
			if (startIndex == displayIndex)
			{
				auto newProperty = InLoftRoutineData.Lines[displayIndex];
				iter->setProperty(static_cast<void*>(&newProperty));


				auto startPoint = iter->getLineData()->startPointData;
				auto endPoint = iter->getLineData()->endPointData;

				if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
				{
					startPoint = iter->getLineData()->endPointData;
					endPoint = iter->getLineData()->startPointData;
				}

				TArray<FVector> newVertex;
				lineGenerator.getLineVertex(startPoint, endPoint, 100, newVertex);
				iter->setVertex(newVertex);
				break;
			}
		}
	}
	return true;
}

FVector FLoftRoutineManager::GetSectionSize()
{

	FVector CenterPoint = FVector::ZeroVector;
	FVector BoxExtent = FVector::ZeroVector;
	FVector SectionSize = FVector::ZeroVector;
	for (auto& section : GeomtryList)
	{
		section->GetActorBounds(false, CenterPoint, BoxExtent);
		SectionSize = FVector(FMath::Max<float>(SectionSize.X, CenterPoint.X + BoxExtent.X),
			FMath::Max<float>(SectionSize.Y, CenterPoint.Y + BoxExtent.Y),
			FMath::Max<float>(SectionSize.Z, CenterPoint.Z + BoxExtent.Z));
	}
	return SectionSize;

}

void FLoftRoutineManager::generatePointActors(TArray<AGeometryPoint*>& pointActors)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	for (auto iter : pointGenerator.getPoints())
	{
		AGeometryPoint* newPoint = FGeometryItemList::Get().CreateGeomtryItem<AGeometryPoint, FGeomtryPointProperty>(Wrold, iter->pointProperty);
		newPoint->setPointData(iter);
		pointActors.Add(newPoint);
	}
}

void FLoftRoutineManager::generateLineActors(TArray<AGeometryLine*>& lineActors)
{
	UWorld* Wrold = ACatalogPlayerController::Get()->GetWorld();
	for (auto iter : lineGenerator.getLines())
	{
		AGeometryLine* newLine = FGeometryItemList::Get().CreateGeomtryItem<AGeometryLine, FGeomtryLineProperty>(Wrold, iter->lineProperty);
		newLine->SetLineData(iter);

		auto startPoint = newLine->getLineData()->startPointData;
		auto endPoint = newLine->getLineData()->endPointData;

		if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
		{
			startPoint = newLine->getLineData()->endPointData;
			endPoint = newLine->getLineData()->startPointData;
		}

		TArray<FVector> newVertex;
		lineGenerator.getLineVertex(startPoint, endPoint, 100, newVertex);
		newLine->setVertex(newVertex);

		lineActors.Add(newLine);
	}
}

void FLoftRoutineManager::clearPointsAndLinesActor()
{
	for (auto& iter : geomtryPointList)
	{
		iter->DeleteGeomtryItem();
	}
	for (auto& iter : geomtryLineList)
	{
		iter->DeleteGeomtryItem();
	}
	geomtryPointList.Empty();
	geomtryLineList.Empty();
}

bool FLoftRoutineManager::ChangeLoftRoutineProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty)
{
	auto changePoint = pointGenerator.setPropertyByDisplaySort(InPointProperty.ID, InPointProperty);
	for (auto iter : geomtryPointList)
	{
		if (iter->getPointData() == changePoint)
		{
			FGeomtryPointProperty newProperty = InPointProperty;
			iter->ChangeProperty(InType, static_cast<void*>(&newProperty));
		}
	}
	pointGenerator.computeStruct();
	for (auto iter : geomtryLineList)
	{
		if (iter->getLineData()->containPoint(changePoint))
		{
			auto startPoint = iter->getLineData()->startPointData;
			auto endPoint = iter->getLineData()->endPointData;

			if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
			{
				startPoint = iter->getLineData()->endPointData;
				endPoint = iter->getLineData()->startPointData;
			}

			TArray<FVector> newVertex;
			lineGenerator.getLineVertex(startPoint, endPoint, 100, newVertex);
			iter->setVertex(newVertex);
		}
	}
	OnLoftRoutinePropertyChanged.ExecuteIfBound();
	return false;
}

bool FLoftRoutineManager::ChangeLoftRoutineProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty)
{
	pointGenerator.computeStruct();
	auto changeLine = lineGenerator.setLineProperty(InLineProperty.ID, InLineProperty);
	if (changeLine == nullptr)
	{
		return false;
	}
	auto startPoint = changeLine->startPointData;
	auto endPoint = changeLine->endPointData;
	if (startPoint->structSort > endPoint->structSort && startPoint->structSort == pointGenerator.getPointCount() - 1 && endPoint->structSort == 0)
	{

	}
	else if (startPoint->structSort > endPoint->structSort || (startPoint->structSort == 0 && endPoint->structSort == pointGenerator.getPointCount() - 1))
	{
		startPoint = changeLine->endPointData;
		endPoint = changeLine->startPointData;
	}
	TArray<FVector> newVertex;
	lineGenerator.getLineVertex(startPoint, endPoint, 100, newVertex);
	if (changeLine == nullptr)
	{
		return false;
	}
	for (auto& iter : geomtryLineList)
	{
		if (iter->getData() == changeLine)
		{
			FGeomtryLineProperty NewProperty = InLineProperty;
			iter->setProperty(InType, static_cast<void*>(&NewProperty));
			iter->setVertex(newVertex);
			break;
		}
	}
	OnLoftRoutinePropertyChanged.ExecuteIfBound();
	return true;
}

void FLoftRoutineManager::OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth)
{
	UE_LOG(LogTemp, Log, TEXT("Camera width is :  %f  "), NewWidth);
	CameraLocation = NewLocation;
	CameraWidth = NewWidth;
	TArray<AGeometryItemBase*> unionItems;
	unionItems.Append(geomtryLineList);
	unionItems.Append(geomtryPointList);
	for (auto& Iter : unionItems)
	{
		const EGeomtryItemType& ItemType = Iter->GetGeomtryItemTypeConstRef();
		float Scale = -1.0f;
		if (EGeomtryItemType::EGeomtryPoint == ItemType)
		{
			Scale = FMath::Max<float>(NewWidth * 0.00005f, 0.0005);
			Scale = FMath::Min<float>(Scale, 0.005);
		}
		else if (EGeomtryItemType::EGeomtryLine == ItemType)
			Scale = FMath::Max<float>((NewWidth / 400.0f) * 1.6f, 0.1);
		if (Scale > 0.0f)
			Iter->ScaleGeomtryItem(Scale);
	}
}

bool FLoftRoutineManager::AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines)
{
	clearPointsAndLinesActor();

	for (auto& LineIte : InLines)
	{
		FVector StartPos = LineIte.StartLocation;
		//FVector EndPos = LineIte.EndLocation;
		switch (CurrentPlanType)
		{
		case EPlanPolygonBelongs::EXY_Plan:StartPos.Z = 0.0; break;
		case EPlanPolygonBelongs::EXZ_Plan:
		{
			StartPos.X = LineIte.StartLocation.X;
			StartPos.Y = 0.0;
			StartPos.Z = LineIte.StartLocation.Y;
		}
		break;
		case EPlanPolygonBelongs::EYZ_Plan:
		{
			StartPos.X = 0.0;
			StartPos.Y = LineIte.StartLocation.Y;
			StartPos.Z = LineIte.StartLocation.X;
		}
		break;
		case EPlanPolygonBelongs::EUnknown:break;
		default:checkNoEntry(); break;
		}
		FGeometryDatas::FormatLocation(this->CurrentPlanType, StartPos);
		//FGeometryDatas::FormatLocation(this->CurrentPlanType, EndPos);

		FExpressionValuePair LocationX(UParameterPropertyData::ConvertToUIValue(StartPos.X));
		FExpressionValuePair LocationY(UParameterPropertyData::ConvertToUIValue(StartPos.Y));
		FExpressionValuePair LocationZ(UParameterPropertyData::ConvertToUIValue(StartPos.Z));
		auto pointProperty = FGeomtryPointProperty(EPositionType::EAbsolute, CurrentPlanType, LocationX, LocationY, LocationZ, FVector::ZeroVector);

		auto pointPtr = MakeShared<FGeometryPointData>();
		pointPtr->pointProperty.CopyData(pointProperty);
		pointPtr->position = StartPos;
		pointPtr->structSort = pointGenerator.getPointCount();
		pointPtr->displaySort = pointGenerator.getPointCount();
		pointGenerator.addPoint(pointPtr);
	}

	TArray<AGeometryPoint*> newPoints;
	generatePointActors(newPoints);
	geomtryPointList.Append(newPoints);

	TArray<TSharedPtr<FGeometryPointData>> Points = pointGenerator.getPoints();
	TArray<TSharedPtr<FGeometryLineData>> Lines;
	for (int32 i = 0; i < InLines.Num() - 1; ++i)
	{
		int32 NextI = (i + 1);
		TSharedPtr<FGeometryLineData> NewLine = MakeShared<FGeometryLineData>();
		NewLine->startPointData = Points[i];
		NewLine->endPointData = Points[NextI];
		NewLine->lineProperty.CopyData(InLines[i]);
		NewLine->lineProperty.StartLocation = NewLine->startPointData->position;
		NewLine->lineProperty.EndLocation = NewLine->endPointData->position;
		NewLine->lineProperty.PlanBelongs = CurrentPlanType;
		NewLine->lineProperty.ID = i;
		Lines.Add(NewLine);
	}
	lineGenerator.initializeLines(Lines, false);

	//refresh point sort
	pointGenerator.computeStruct();

	TArray<AGeometryLine*> linePoints;
	generateLineActors(linePoints);
	geomtryLineList.Append(linePoints);


	this->OnCameraLocationOrWidthChanged(CameraLocation, CameraWidth);
	OnLoftRoutinePropertyChanged.ExecuteIfBound();

	return true;
}


#undef LOCTEXT_NAMESPACE