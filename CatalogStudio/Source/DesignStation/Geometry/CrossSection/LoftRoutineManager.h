// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemList.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryPoint.h"
#include "DesignStation/Geometry/GeomtryItems/GeometryLine.h"

enum class ELoftRoutineToolType : uint8
{
	EArrow,
	EDrawPoint,
	EClear = 5,
	EImportCAD
};

/**
 *
 */
class DESIGNSTATION_API FLoftRoutineManager
{
private:
	AGeometryItemBase* CurrentHover;
	AGeometryItemBase* CurrentSelected;
	TArray<AGeometryItemBase*> GeomtryList;
	TArray<AGeometryPoint*> geomtryPointList;
	TArray<AGeometryLine*> geomtryLineList;

	//
	EPlanPolygonBelongs CurrentPlanType;

	//
	ELoftRoutineToolType ToolType;

	//
	FVector		CameraLocation;
	float		CameraWidth;

	TArray<FVector> currentPoints;
public:

	GenerateSet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateGet(EPlanPolygonBelongs, CurrentPlanType)
		GenerateGet(ELoftRoutineToolType, ToolType)

public:

	FNoParamDelegate OnLoftRoutinePropertyChanged;
	FTwoIntDelegate OnSelectionChanged;

public:
	FLoftRoutineManager();
	virtual ~FLoftRoutineManager();

	FText GetSelectedItemName() const;

	void SelectAnotherGeometryItem(const int32& InType, const int32& InIndex, bool IsOver);

	//
	bool ChangeLoftRoutineProperty(const int32& InType, const FGeomtryPointProperty& InPointProperty);
	bool ChangeLoftRoutineProperty(const int32& InType, const FGeomtryLineProperty& InLineProperty);
	//

	void OnCameraLocationOrWidthChanged(const FVector& NewLocation, const float& NewWidth);

	bool ChangeDrawTool(const ELoftRoutineToolType& InNewToolType);

	void OnMouseMove();

	void OnMouseLeftButtonClick(const FVector& InMousePosition);

	void OnMouseRightButtonClick(const FVector& InMousePosition);

	void OnDeleteKeyClick();

	void Reset();

	void ClearLoftRoutine();

	bool ConstructArchiveData(FSectionLoftOperation& OutLoftRoutineData);

	bool LoadFromArchiveData(const FSectionLoftOperation& InLoftRoutineData);

	FVector GetSectionSize();

	void generatePointActors(TArray<AGeometryPoint*>& pointActors);

	void generateLineActors(TArray<AGeometryLine*>& lineActors);

	void clearPointsAndLinesActor();

	bool AddGeomtryPointsFromCAD(const TArray<FGeomtryLineProperty>& InLines);
private:

	void SelectAnotherGeometryItem(AGeometryItemBase* NewSelection);

	bool AddGeomtryPoint(const FVector& InPointLocation);

private:
	FGeometryPointGenerator pointGenerator;
	FGeometryLineGenerator	lineGenerator;
};
