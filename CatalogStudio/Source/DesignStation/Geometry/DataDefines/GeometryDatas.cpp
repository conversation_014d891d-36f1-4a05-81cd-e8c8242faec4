// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryDatas.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "DataCenter/Libraries/GeomtryMathmaticLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"
#include "DesignStation/SQLite/MultiComponentRelated/MultiComTableOperatorLibrary.h"
#include "DesignStation/Geometry/ShowComponentDisplayUnitItem.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "RefRelation/RefRelationFunction.h"


void FGeometryDatas::FormatLocation(const EPlanPolygonBelongs& InPlanType, FVector& InOutLocation)
{
	switch (InPlanType)
	{
	case EPlanPolygonBelongs::EXY_Plan:InOutLocation.Z = 0.0f; break;
	case EPlanPolygonBelongs::EXZ_Plan:InOutLocation.Y = 0.0f; break;
	case EPlanPolygonBelongs::EYZ_Plan:InOutLocation.X = 0.0f; break;
	case EPlanPolygonBelongs::EUnknown:break;
	default:checkNoEntry(); break;
	}
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FExpressionValuePair& InOutParameter)
{
	TArray<TPair<int32, FString>> Comments;
	if (ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(InOutParameter.Expression, Comments).IsEmpty())
	{
		InOutParameter.Value = TEXT("");
		return true;
	}
	FString ErrorMessage;
	FString FormatExpress;
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(InGlobalParameter, InParentParameters, InLocalParameters, InOutParameter.Expression, InOutParameter.Value, FormatExpress);
	if (Res)
	{
		InOutParameter.Expression = FormatExpress;
		InOutParameter.Value = UParameterPropertyData::FormatParameterValue(InOutParameter.Value).ToString();
	}
	return Res;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FLocationProperty& InOutLocation)
{
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLocation.LocationX))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLocation.LocationY))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters,InOutLocation.LocationZ))
		return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FRotationProperty& InOutRotation)
{
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRotation.Roll))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRotation.Pitch))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRotation.Yaw))
		return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FScaleProperty& InOutScale)
{
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutScale.X))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutScale.Y))
		return false;
	if (!FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutScale.Z))
		return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FGeomtryPointProperty& InOutPoint)
{
	bool ResX = FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutPoint.LocationX);
	bool ResY = FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutPoint.LocationY);
	bool ResZ = FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutPoint.LocationZ);
	return ResX && ResY && ResZ;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FGeomtryLineProperty& InOutLine)
{
	bool Res1 = FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLine.RadiusOrHeightData);
	bool Res2 = FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLine.InterpPointCountData);
	return Res1 && Res2;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FGeomtryRectanglePlanProperty& InOutRectangle)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRectangle.StartLocationX))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRectangle.StartLocationY))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters,InOutRectangle.StartLocationZ))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters,InOutRectangle.EndLocationX))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutRectangle.EndLocationY))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters,InOutRectangle.EndLocationZ))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FGeomtryEllipsePlanProperty& InOutEllipse)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.CenterLocationX))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.CenterLocationY))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.CenterLocationZ))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.ShortRadiusData))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.LongRadiusData))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutEllipse.InterpPointCountData))return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FGeomtryCubeProperty& InOutCube)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.StartLocationX))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.StartLocationY))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.StartLocationZ))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.EndLocationX))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.EndLocationY))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCube.EndLocationZ))return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionDrawOperation& InOutDrawData)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutDrawData.DrawOffsetX))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutDrawData.DrawOffsetY))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutDrawData.DrawOffsetZ))return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionShiftingOperation& InOutShiftData)
{
	for (int32 i = 0; i < InOutShiftData.ShiftValue.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutShiftData.ShiftValue[i]))return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionZoomOperation& InOutZoomData)
{
	for (int32 i = 0; i < InOutZoomData.ZoomValue.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutZoomData.ZoomValue[i]))return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionLoftOperation& InOutLoftData)
{
	for (int32 i = 0; i < InOutLoftData.Points.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLoftData.Points[i]))return false;
	}
	for (int32 i = 0; i < InOutLoftData.Lines.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutLoftData.Lines[i]))return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionCutOutOperation& InOutCutoutData)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCutoutData.CutOutValue))
	{
		return false;
	}
	for (int32 i = 0; i < InOutCutoutData.Points.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters,InOutCutoutData.Points[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutCutoutData.Lines.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCutoutData.Lines[i]))
		{
			return false;
		}
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCutoutData.Rectangle))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCutoutData.Ellipse))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSectionOperation& InOutOperationData)
{
	for (int32 i = 0; i < InOutOperationData.DrawOperations.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutOperationData.DrawOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.ShiftOperations.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutOperationData.ShiftOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.ZoomOperations.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutOperationData.ZoomOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.CutoutOperations.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutOperationData.CutoutOperations[i]))
		{
			return false;
		}
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutOperationData.LoftingOperation))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FCrossSectionData& InOutCrossSectionData)
{
	for (int32 i = 0; i < InOutCrossSectionData.Points.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCrossSectionData.Points[i]))return false;
	}
	for (int32 i = 0; i < InOutCrossSectionData.Lines.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCrossSectionData.Lines[i]))return false;
		if (ELineType::ELineSegment != InOutCrossSectionData.Lines[i].LineType)
		{
			FVector PreLocation = InOutCrossSectionData.Points[(i - 1 + InOutCrossSectionData.Points.Num()) % InOutCrossSectionData.Points.Num()].PointLocation();
			InOutCrossSectionData.Lines[i].StartLocation = InOutCrossSectionData.Points[i].PointLocation(PreLocation);
			FVector NextLocation = InOutCrossSectionData.Points[(i + 1) % InOutCrossSectionData.Points.Num()].PointLocation(InOutCrossSectionData.Lines[i].StartLocation);
			InOutCrossSectionData.Lines[i].EndLocation = NextLocation;
		}
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCrossSectionData.Rectangle))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue section rectanngle"));
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCrossSectionData.Ellipse))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutCrossSectionData.Cube))return false;
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSingleComponentItem& InOutSingleComponentData)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.OperatorSection))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.ComponentMaterial))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.VisibleParam))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.SectionOperation))
	{
		return false;
	}
	for (auto& Iter : InOutSingleComponentData.ImportMesh)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, Iter.MaterialId))
		{
			return false;
		}
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.SingleComponentLocation))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.SingleComponentRotation))
	{
		return false;
	}
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.SingleComponentScale))
	{
		return false;
	}
	return true;

}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FSingleComponentProperty& InOutSingleComponentData)
{
	for (int32 i = 0; i < InOutSingleComponentData.ComponentItems.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, InLocalParameters, InOutSingleComponentData.ComponentItems[i]))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, TArray<int64>& ParentNodes, FMultiComponentDataItem& InOutMultiComponentData)
{
	{
		auto ComponentParameters = InOutMultiComponentData.ComponentParameters;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
		UParameterRelativeLibrary::CombineParameters(PeerParameters, ComponentParameters);
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(InGlobalParameter, InParentParameters, PeerParameters);
		for (const auto& iter : PeerParameters)
		{
			auto& EditParameter = iter.Value;
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive); });
			if (INDEX_NONE != Index) InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
		}
	}
	auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(InOutMultiComponentData.ComponentParameters);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParameters;
	UParameterRelativeLibrary::CombineParameters(InParentParameters, InOutMultiComponentData.ComponentParameters, OverrideParameters);
	{//计算ID引用是否发生改变
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap,InOutMultiComponentData.ComponentID))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 2"));
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue Error Occur Where Express Is %s"), *InOutMultiComponentData.ComponentID.Expression);
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue Where Vaule Is %s"), *InOutMultiComponentData.ComponentID.Value);
			return false;
		}
		const int64 NewComponentID = InOutMultiComponentData.GetComponentID();
		//Fix bug CATALOG-1422 begin
		if (0 != NewComponentID && ParentNodes.Contains(NewComponentID))
			return false;
		else
			ParentNodes.Push(NewComponentID);
		//Fix bug CATALOG-1422 end		
		{
			InOutMultiComponentData.ComponentType = ECompType::None;
			InOutMultiComponentData.ChildComponent.Empty();
			InOutMultiComponentData.SingleComponentPath.Empty();
			InOutMultiComponentData.SingleComponentData.Empty();
			FFolderTableData FileData;
			UFolderTableOperatorLibrary::RetriveFileByFolderId(FString::Printf(TEXT("%d"), NewComponentID), FileData);
			InOutMultiComponentData.CodeExp = FileData.folder_code_exp;
			InOutMultiComponentData.Code = FileData.folder_code;
			InOutMultiComponentData.ComponentName = FileData.folder_name;
			InOutMultiComponentData.ComponentNameExp = FileData.folder_name_exp;
			if (EFolderType::ESingleComponent == FileData.folder_type)
			{
				InOutMultiComponentData.ComponentType = ECompType::SingleCom;
				FSingleComponentTableData SingleComp;
				FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(FileData.id, SingleComp);
				InOutMultiComponentData.SingleComponentPath = SingleComp.data_path;
				if (!SingleComp.data_path.IsEmpty())
				{
					const FString FullPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComp.data_path);
					FGeometryDatas::LoadSingleComponentByFilePath(SingleComp.data_path, InOutMultiComponentData.SingleComponentData);
				}
			}
			else if (EFolderType::EMultiComponents == FileData.folder_type)
			{
				InOutMultiComponentData.ComponentType = ECompType::MultiCom;
				FMultiComTableOperatorLibrary::RetriveFileMultiComponents(FileData.id, InOutMultiComponentData.ChildComponent);
			}
		}
	}
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentVisibility);
	if (InOutMultiComponentData.CodeExp.Contains(TEXT("\"")))
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}
	else
	{
		InOutMultiComponentData.Code = InOutMultiComponentData.CodeExp;
	}
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentLocation);
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentRotation);
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentScale);
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, OverrideParameters, {}, InOutMultiComponentData.SingleComponentData);

	for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
	{
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, OverrideParameters, ParentNodes, InOutMultiComponentData.ChildComponent[i]);
	}
	ParentNodes.Pop();
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, const int64& ThisFolderID, FMultiComponentData& InOutMultiComponentData)
{
	TArray<int64> ParentNodes;
	ParentNodes.Add(ThisFolderID);
	for (int32 i = 0; i < InOutMultiComponentData.ComponentItems.Num(); ++i)
	{
		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, ParentNodes, InOutMultiComponentData.ComponentItems[i]))return false;
	}
	return true;
}

bool FGeometryDatas::CalculateCurMultiComponentData(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, FMultiComponentDataItem& InOutMultiComponentData)
{
	{
		auto ComponentParameters = InOutMultiComponentData.ComponentParameters;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
		UParameterRelativeLibrary::CombineParameters(PeerParameters, ComponentParameters);
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(InGlobalParameter, InParentParameters, PeerParameters);
		for (const auto& iter : PeerParameters)
		{
			auto& EditParameter = iter.Value;
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive); });
			if (INDEX_NONE != Index) InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
		}
	}
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParameters;
	UParameterRelativeLibrary::CombineParameters(InParentParameters, InOutMultiComponentData.ComponentParameters, OverrideParameters);

	auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(InOutMultiComponentData.ComponentParameters);
	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentID);

	if (InOutMultiComponentData.ComponentNameExp.IsEmpty() && !InOutMultiComponentData.ComponentName.IsEmpty())
	{
		InOutMultiComponentData.ComponentNameExp = TEXT("\"") + InOutMultiComponentData.ComponentName + TEXT("\"");
	}
	else
	{
		FExpressionValuePair NamePair;
		NamePair.Expression = InOutMultiComponentData.ComponentNameExp;
		NamePair.Value = InOutMultiComponentData.ComponentName;
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, NamePair);
		InOutMultiComponentData.ComponentName = NamePair.Value;
	}


	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentVisibility);
	

	if (InOutMultiComponentData.CodeExp.IsEmpty() && !InOutMultiComponentData.Code.IsEmpty())
	{
		InOutMultiComponentData.CodeExp = TEXT("\"") + InOutMultiComponentData.Code + TEXT("\"");
	}
	else
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentLocation);

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentRotation);

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, LocalParamMap, InOutMultiComponentData.ComponentScale);
	//FGeometryDatas::CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.SingleComponentData);
	return true;

}

bool FGeometryDatas::CalculateCurMultiComponentData(
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, 
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters,
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InHeritParams,
	FMultiComponentDataItem& InOutMultiComponentData)
{
	//合并文件夹参数和本层参数，计算
	auto ComponentParameters = InOutMultiComponentData.ComponentParameters;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CurCompParamMap = URefRelationFunction::ConvertParamsArrayToMap(InOutMultiComponentData.ComponentParameters);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(InHeritParams, CurCompParamMap, PeerParameters);

	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(InGlobalParameter, InParentParameters, PeerParameters);
	for (const auto& iter : PeerParameters)
	{
		auto& EditParameter = iter.Value;
		const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive); });
		if (INDEX_NONE != Index) InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
	}

	if (InOutMultiComponentData.ComponentNameExp.IsEmpty() && !InOutMultiComponentData.ComponentName.IsEmpty())
	{
		InOutMultiComponentData.ComponentNameExp = TEXT("\"") + InOutMultiComponentData.ComponentName + TEXT("\"");
	}
	else
	{
		FExpressionValuePair NamePair;
		NamePair.Expression = InOutMultiComponentData.ComponentNameExp;
		NamePair.Value = InOutMultiComponentData.ComponentName;
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, NamePair);
		InOutMultiComponentData.ComponentName = NamePair.Value;
	}


	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, InOutMultiComponentData.ComponentVisibility);


	if (InOutMultiComponentData.CodeExp.IsEmpty() && !InOutMultiComponentData.Code.IsEmpty())
	{
		InOutMultiComponentData.CodeExp = TEXT("\"") + InOutMultiComponentData.Code + TEXT("\"");
	}
	else
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, InOutMultiComponentData.ComponentLocation);

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, InOutMultiComponentData.ComponentRotation);

	FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParentParameters, PeerParameters, InOutMultiComponentData.ComponentScale);

	return true;
}

bool FGeometryDatas::GenerateMesh(FSingleComponentItem& InComponentData, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, AShowSingleComponentActor* calculateActor, bool bPathUV)
{
	FCrossSectionGenerator crossSectionGenerator;
	crossSectionGenerator.setBaseCrossSection(InComponentData.OperatorSection);
	return crossSectionGenerator.generateMesh(InComponentData.SectionOperation, OutMeshInfo, OutFramework, calculateActor, bPathUV);
	//return InComponentData.OperatorSection.TransformSection(InComponentData.SectionOperation, OutMeshInfo, OutFramework);
}

bool FGeometryDatas::GenerateMesh(FSingleComponentItem& InComponentData, TArray<FShowSingleComponentActorPropertyItem>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible, AShowSingleComponentActor* calculateActor, bool bPathUV)
{
	if (!InForceVisible && FMath::IsNearlyZero(FCString::Atof(*InComponentData.VisibleParam.Value)))
		return true;
	if (ESingleComponentSource::ECustom == InComponentData.ComponentSource)
	{
		FPMCSection MeshInfo;
		TArray<TPair<FVector, FVector>> Framework;
		if (false == FGeometryDatas::GenerateMesh(InComponentData, MeshInfo, Framework, calculateActor, bPathUV))
			return false;
		FShowSingleComponentActorPropertyItem MeshMaterial;
		MeshMaterial.MaterialFolderID = FString::Printf(TEXT("%d"), FCString::Atoi64(*InComponentData.ComponentMaterial.Value));
		MeshInfo.SectionName = FName(*MeshMaterial.MaterialFolderID);
		MeshMaterial.MeshInfo = MoveTemp(MeshInfo);
		OutMeshInfo.Push(MeshMaterial);
		OutFramework.Append(Framework);
	}
	else if (ESingleComponentSource::EImportFBX == InComponentData.ComponentSource)
	{
		for (auto& MeshIter : InComponentData.ImportMesh)
		{
			FShowSingleComponentActorPropertyItem MeshMaterial;
			MeshMaterial.MaterialFolderID = MeshIter.MaterialId.Value;
			MeshMaterial.MeshInfo = MeshIter.SectionMesh;
			MeshMaterial.MeshInfo.SectionName = FName(*MeshMaterial.MaterialFolderID);
			OutMeshInfo.Push(MeshMaterial);
		}
	}
	else if (ESingleComponentSource::EImportPAK == InComponentData.ComponentSource)
	{
		FShowSingleComponentActorPropertyItem ImportMesh;
		ImportMesh.PakRefPath = InComponentData.PakRefPath;
		ImportMesh.PakCode = InDMValue;
		OutMeshInfo.Push(ImportMesh);
	}
	return true;
}

bool FGeometryDatas::GenerateMesh(FSingleComponentProperty& InComponentData, TArray<FPMCSection>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible)
{
	if (0 == InComponentData.ComponentItems.Num())
		return true;
	for (auto& Iter : InComponentData.ComponentItems)
	{
		if (!InForceVisible && FMath::IsNearlyZero(FCString::Atof(*Iter.VisibleParam.Value)))
			continue;
		if (ESingleComponentSource::ECustom == Iter.ComponentSource)
		{
			FPMCSection MeshInfo;
			TArray<TPair<FVector, FVector>> Framework;
			if (false == FGeometryDatas::GenerateMesh(Iter, MeshInfo, Framework))return false;
			OutMeshInfo.Push(MeshInfo);
			OutFramework.Append(Framework);
		}
		else if (ESingleComponentSource::EImportFBX == Iter.ComponentSource)
		{
			for (auto& MeshIter : Iter.ImportMesh)
			{
				OutMeshInfo.Push(MeshIter.SectionMesh);
			}
		}
	}
	return true;
}

bool FGeometryDatas::GenerateMesh(FMultiComponentDataItem& InComponentData, FShowMultiComponentActorProperty& OutMesh, const FString& InDMValue)
{
	UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh --- Id : %s, Name : %s, Type : %d")
		, *InComponentData.ComponentID.Value, *InComponentData.ComponentName, static_cast<int32>(InComponentData.ComponentType));
	//Transform信息错误，需要修改数据结构。
	OutMesh.MeshTransform.SetLocation(InComponentData.ComponentLocation.GetLocation());
	OutMesh.MeshTransform.SetScale3D(InComponentData.ComponentScale.GetScale());
	OutMesh.MeshTransform.SetRotation(FQuat(InComponentData.ComponentRotation.GetRotation()));
	OutMesh.Visibility = !FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value));
	FString DMValue = InDMValue;
	{
		int32 DMIndex = InComponentData.ComponentParameters.IndexOfByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("DM"), ESearchCase::CaseSensitive); });
		if (INDEX_NONE != DMIndex)
			DMValue = InComponentData.ComponentParameters[DMIndex].Data.value;
	}
	if (ECompType::MultiCom == InComponentData.ComponentType)
	{
		UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh -- MultiCom -- Child [Num : %d]")
			, InComponentData.ChildComponent.Num());
		int32 Offset = OutMesh.MultiComponentDatas.AddDefaulted(InComponentData.ChildComponent.Num());
		for (auto& Iter : InComponentData.ChildComponent)
		{
			UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh -- MultiCom -- Child [Index : %d]"), Offset);
			if (!Iter.IsVisiable())
			{
				++Offset;
				continue;
			}
			FGeometryDatas::GenerateMesh(Iter, OutMesh.MultiComponentDatas[Offset++], DMValue);
		}
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh -- Single / Import -- Child [Num : %d]")
			, InComponentData.SingleComponentData.ComponentItems.Num());

		int32 Offset = OutMesh.SingleComponentDatas.AddDefaulted();
		if (InComponentData.SingleComponentData.IsValid() && !FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value)))
		{

			UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh -- Single / Import -- Child [Index : %d]"), Offset);
			for (auto& SingleMeshIter : InComponentData.SingleComponentData.ComponentItems)
			{
				if (FMath::IsNearlyZero(FCString::Atof(*SingleMeshIter.VisibleParam.Value)))
					continue;

				int32 i = OutMesh.SingleComponentDatas[Offset].MeshInfo.AddDefaulted();
				
				UE_LOG(LogTemp, Warning, TEXT("FGeometryDatas::GenerateMesh -- Single / Import -- Child [Mesh Index : %d]"), i);
			
				TArray<TPair<FVector, FVector>> Framework;
				auto FYParam = InComponentData.ComponentParameters.FindByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("FYWLFX"), ESearchCase::CaseSensitive); });
				bool bPathUV = FYParam && !FYParam->Data.value.IsEmpty() && FCString::Atof(*FYParam->Data.value) == 0.0f;
				FGeometryDatas::GenerateMesh(SingleMeshIter, OutMesh.SingleComponentDatas[Offset].MeshInfo[i].SingleMeshInfo, Framework, DMValue,false,nullptr, bPathUV);
				OutMesh.SingleComponentDatas[Offset].MeshInfo[i].CompSource = SingleMeshIter.ComponentSource;
				OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetLocation(SingleMeshIter.SingleComponentLocation.GetLocation());
				OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetRotation(FQuat(SingleMeshIter.SingleComponentRotation.GetRotation()));
				OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetScale3D(SingleMeshIter.SingleComponentScale.GetScale());
				OutMesh.SingleComponentDatas[Offset].OutlinePairs.Append(Framework);
			}
		}
	}
	return true;
}

bool FGeometryDatas::GenerateMeshSections(FSingleComponentItem& InComponentData, FSingleComponent& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible)
{
	if (!InForceVisible && FMath::IsNearlyZero(FCString::Atof(*InComponentData.VisibleParam.Value)))
		return true;
	OutMeshInfo.CompSource = InComponentData.ComponentSource;
	OutMeshInfo.SectionTrans.SetLocation(InComponentData.SingleComponentLocation.GetLocation());
	OutMeshInfo.SectionTrans.SetRotation(FQuat(InComponentData.SingleComponentRotation.GetRotation()));
	OutMeshInfo.SectionTrans.SetScale3D(InComponentData.SingleComponentScale.GetScale());
	if (ESingleComponentSource::ECustom == InComponentData.ComponentSource)
	{
		FPMCSection MeshInfo;
		TArray<TPair<FVector, FVector>> Framework;
		if (false == FGeometryDatas::GenerateMesh(InComponentData, MeshInfo, Framework))
			return false;
		FPMCSection MeshWithUV;
		UGeomtryMathmaticLibrary::GenerateMeshUV(MeshInfo, MeshWithUV);
		FSingleComponentSections SingleComponent;
		SingleComponent.MaterialFolderID = FCString::Atoi(*InComponentData.ComponentMaterial.Value);
		SingleComponent.MeshInfo = MeshWithUV;
		OutMeshInfo.Sections.Push(SingleComponent);
		OutFramework.Append(Framework);
	}
	else if (ESingleComponentSource::EImportFBX == InComponentData.ComponentSource)
	{
		for (auto& MeshIter : InComponentData.ImportMesh)
		{
			FSingleComponentSections SingleComponent;
			SingleComponent.MaterialFolderID = FCString::Atoi(*MeshIter.MaterialId.Value);
			SingleComponent.MeshInfo = MeshIter.SectionMesh;
			OutMeshInfo.Sections.Push(SingleComponent);
		}
	}
	else if (ESingleComponentSource::EImportPAK == InComponentData.ComponentSource)
	{
		FSingleComponentSections ImportMesh;
		ImportMesh.PakRefPath = InComponentData.PakRefPath;
		ImportMesh.PakCode = InDMValue;
		OutMeshInfo.Sections.Push(ImportMesh);
	}
	return true;
}


bool FGeometryDatas::GenerateMeshSections(FMultiComponentDataItem& InComponentData, const FTransform& InParentTrans, TArray<FSingleComponent>& OutSections, const FString& InDMValue)
{
	if (FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value), 0.01f)) return true;
	FTransform NodeTransform;
	NodeTransform.SetLocation(InComponentData.ComponentLocation.GetLocation());
	NodeTransform.SetScale3D(InComponentData.ComponentScale.GetScale());
	NodeTransform.SetRotation(FQuat(InComponentData.ComponentRotation.GetRotation()));
	auto LocalTransform = NodeTransform * InParentTrans;
	FString DMValue = InDMValue;
	{
		int32 DMIndex = InComponentData.ComponentParameters.IndexOfByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("DM"), ESearchCase::CaseSensitive); });
		if (INDEX_NONE != DMIndex)
			DMValue = InComponentData.ComponentParameters[DMIndex].Data.value;
	}
	if (ECompType::MultiCom == InComponentData.ComponentType)
	{
		for (auto& Iter : InComponentData.ChildComponent)
		{
			FGeometryDatas::GenerateMeshSections(Iter, LocalTransform, OutSections, DMValue);
		}
		return true;
	}
	if (ECompType::SingleCom == InComponentData.ComponentType)
	{
		if (InComponentData.SingleComponentData.IsValid() && !FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value), 0.01f))
		{
			for (auto& SingleMeshIter : InComponentData.SingleComponentData.ComponentItems)
			{
				if (FMath::IsNearlyZero(FCString::Atof(*SingleMeshIter.VisibleParam.Value)))
					continue;
				FSingleComponent NewSingleComponent;
				TArray<TPair<FVector, FVector>> Framework;
				FGeometryDatas::GenerateMeshSections(SingleMeshIter, NewSingleComponent, Framework, DMValue);
				NewSingleComponent.SectionTrans = NewSingleComponent.SectionTrans * LocalTransform;
				NewSingleComponent.OutlinePairs.Append(Framework);
				OutSections.Add(NewSingleComponent);
			}
		}
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponent(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentData& InOutMultiComponent)
{
	for (auto& Iter : InOutMultiComponent.ComponentItems)
	{
		if (false == FGeometryDatas::LoadMultiComponentItem(InGlobalParameter, InParameters, Iter))
			return false;
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponent(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentData& InOutMultiComponent, const int32& InIndex)
{
	if (InOutMultiComponent.ComponentItems.IsValidIndex(InIndex))
	{
		return FGeometryDatas::LoadMultiComponentItem(InGlobalParameter, InParameters, InOutMultiComponent.ComponentItems[InIndex]);
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponentItem(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentDataItem& InOutMultiComponent)
{
	if (InOutMultiComponent.ComponentID.Value.IsEmpty() || ECompType::None == InOutMultiComponent.ComponentType)
	{
		InOutMultiComponent.SingleComponentData.ComponentItems.Empty();
		return true;
	}
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  MultiParameter;
	bool Res = UParameterRelativeLibrary::CombineParameters(InParameters, InOutMultiComponent.ComponentParameters, MultiParameter);
	if (ECompType::SingleCom == InOutMultiComponent.ComponentType)
	{
		if (InOutMultiComponent.SingleComponentPath.IsEmpty())return true;
		Res = Res && FGeometryDatas::LoadSingleComponentByFilePath(InOutMultiComponent.SingleComponentPath, InOutMultiComponent.SingleComponentData);
		if (!Res)
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::LoadMultiComponentItem load single component %s failed"), *InOutMultiComponent.SingleComponentPath);
			return false;
		}
		Res = Res && FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, {}, InOutMultiComponent.SingleComponentData);
		return Res;
	}
	else if (ECompType::MultiCom == InOutMultiComponent.ComponentType)
	{
		for (auto& Iter : InOutMultiComponent.ChildComponent)
		{
			bool Res = FGeometryDatas::LoadMultiComponentItem(InGlobalParameter, MultiParameter, Iter);
			if (!Res)return false;
		}
	}
	return true;
}

bool FGeometryDatas::LoadSingleComponentByFilePath(const FString& InFilePath, FSingleComponentProperty& OutSingleComponent)
{
	OutSingleComponent.ComponentItems.Empty();
	FString SingleComponentFilePath = FPaths::ProjectContentDir() + InFilePath;
	SingleComponentFilePath = FPaths::ConvertRelativePathToFull(SingleComponentFilePath);
	if (FPaths::FileExists(SingleComponentFilePath))
	{
		return UProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(SingleComponentFilePath, OutSingleComponent);
	}
	return true;
}
