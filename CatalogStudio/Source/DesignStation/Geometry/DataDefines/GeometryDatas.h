// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "DataCenter/Parameter/ParameterData.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "DataCenter/ComponentData/ComponentPropertyData.h"
#include "DataCenter/ComponentData/CrossSectionOperandData.h"
#include "DataCenter/ComponentData/CrossSectionDataDefine.h"
#include "DataCenter/ComponentData/SingleComponentDataDefine.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "MagicCore/Public/PMCSection.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"


extern struct FShowSingleComponentActorPropertyItem;

extern struct FShowMultiComponentActorProperty;

extern struct FSingleComponentSections;

extern struct FSingleComponent;

/**
*
*/
class DESIGNSTATION_API FGeometryDatas
{

public:

	static void FormatLocation(const EPlanPolygonBelongs& InPlanType, FVector& InOutLocation);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FExpressionValuePair& InOutParameter);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FLocationProperty& InOutLocation);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		,FRotationProperty& InOutRotation);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FScaleProperty& InOutScale);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FGeomtryPointProperty& InOutPoint);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FGeomtryLineProperty& InOutLine);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FGeomtryRectanglePlanProperty& InOutRectangle);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FGeomtryEllipsePlanProperty& InOutEllipse);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FGeomtryCubeProperty& InOutCube);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionDrawOperation& InOutDrawData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionShiftingOperation& InOutShiftData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionZoomOperation& InOutZoomData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionLoftOperation& InOutLoftData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionCutOutOperation& InOutCutoutData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSectionOperation& InOutOperationData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FCrossSectionData& InOutCrossSectionData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSingleComponentItem& InOutSingleComponentData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters
		, FSingleComponentProperty& InOutSingleComponentData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParameters
		, TArray<int64>& ParentNodes, FMultiComponentDataItem& InOutMultiComponentData);

	static bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter
		, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters
		, const int64& ThisFolderID, FMultiComponentData& InOutMultiComponentData);

	//单层计算引用及相关参数
	static bool CalculateCurMultiComponentData(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentDataItem& InOutMultiComponentData);
	
	//
	static bool CalculateCurMultiComponentData(
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, 
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters,
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InHeritParams,
		FMultiComponentDataItem& InOutMultiComponentData);

	
	static bool GenerateMesh(FSingleComponentItem& InComponentData, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, AShowSingleComponentActor* calculateActor = nullptr, bool bPathUV = false);

	//InDMValue为参数DM的值，用于控制生成导入的Pak截面显示的模型
	static bool GenerateMesh(FSingleComponentItem& InComponentData, TArray<FShowSingleComponentActorPropertyItem>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible = false, AShowSingleComponentActor* calculateActor = nullptr,bool bPathUV = false);

	static bool GenerateMesh(FSingleComponentProperty& InComponentData, TArray<FPMCSection>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible = false);

	static bool GenerateMesh(FMultiComponentDataItem& InComponentData, FShowMultiComponentActorProperty& OutMesh, const FString& InDMValue);

	static bool GenerateMeshSections(FSingleComponentItem& InComponentData, FSingleComponent& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue, bool InForceVisible = false);

	static bool GenerateMeshSections(FMultiComponentDataItem& InComponentData, const FTransform& InParentTrans, TArray<FSingleComponent>& OutSections, const FString& InDMValue);

	static bool LoadMultiComponent(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentData& InOutMultiComponent);

	static bool LoadMultiComponent(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentData& InOutMultiComponent, const int32& InIndex);

	static bool LoadMultiComponentItem(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, FMultiComponentDataItem& InOutMultiComponent);

	static bool LoadSingleComponentByFilePath(const FString& InFilePath, FSingleComponentProperty& OutSingleComponent);

};

