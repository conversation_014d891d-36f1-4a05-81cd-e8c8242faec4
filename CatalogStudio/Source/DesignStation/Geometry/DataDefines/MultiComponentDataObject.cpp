// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentDataObject.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"


bool FMultiComponentPropertyItem::CopyData(FMultiComponentPropertyItem& OutNewComponent) const
{
	OutNewComponent.SingleComponentName = this->SingleComponentName;
	OutNewComponent.SingleComponentVisibility = this->SingleComponentVisibility;
	OutNewComponent.SingleComponentID = this->SingleComponentID;
	OutNewComponent.Description = this->Description;
	OutNewComponent.Code = this->Code;
	OutNewComponent.SingleComponentData = this->SingleComponentData;
	OutNewComponent.SingleComponentLocation = this->SingleComponentLocation;
	OutNewComponent.SingleComponentRotation = this->SingleComponentRotation;
	OutNewComponent.SingleComponentScale = this->SingleComponentScale;
	OutNewComponent.SingleComponentParameters = this->SingleComponentParameters;
	return true;
}

int32 FMultiComponentPropertyItem::IsParameterExists(const FString& InParameterName) const
{
	int32 Index = SingleComponentParameters.IndexOfByPredicate([&](const FParameterData& InOther)->bool { return InOther.Data.name.Equals(InParameterName, ESearchCase::CaseSensitive); });
	UE_LOG(LogTemp, Log, TEXT("------- Index is:  %d  parameter name is: %s  --------"), Index, *InParameterName);
	return Index;
}

bool FMultiComponentPropertyItem::AddNewParameter(const FParameterData& InNewParameter)
{
	int32 Index = this->IsParameterExists(InNewParameter.Data.name);
	if (INDEX_NONE == Index)
	{
		this->SingleComponentParameters.Add(InNewParameter);
	}
	return INDEX_NONE == Index;
}

bool FMultiComponentPropertyItem::DeleteParameter(const FParameterData& InParameter)
{
	int32 Index = this->IsParameterExists(InParameter.Data.name);
	if (INDEX_NONE != Index)
	{
		this->SingleComponentParameters.RemoveAt(Index);
	}
	return INDEX_NONE != Index;
}

bool FMultiComponentPropertyItem::UpdateParameterExpressionAndValue(const FParameterData& InParameter)
{
	int32 Index = this->IsParameterExists(InParameter.Data.name);
	if (INDEX_NONE != Index)
	{
		this->SingleComponentParameters[Index].Data.expression = InParameter.Data.expression;
		this->SingleComponentParameters[Index].Data.value = InParameter.Data.value;
	}
	return INDEX_NONE != Index;
}

bool FMultiComponentPropertyItem::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters)
{
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, {}, SingleComponentID))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, {}, SingleComponentVisibility))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, {}, SingleComponentLocation))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, {}, SingleComponentRotation))return false;
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, InParameters, {},SingleComponentScale))return false;
	for (int32 i = 0; i < SingleComponentParameters.Num(); ++i)
	{
		if (false == UParameterRelativeLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, {}, SingleComponentParameters[i]))return false;
	}
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  MultiParameter;
	UParameterRelativeLibrary::CombineParameters(InParameters, SingleComponentParameters, MultiParameter);
	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, {}, SingleComponentData))return false;
	return true;
}

int32 FMultiComponentProperty::CopyComponent(const int32& InSourceIndex)
{
	if (ComponentItems.IsValidIndex(InSourceIndex))
	{
		int32 NewIndex = ComponentItems.AddDefaulted();
		return ComponentItems[InSourceIndex].CopyData(ComponentItems[NewIndex]) ? NewIndex : -1;
	}
	return -1;
}

bool FMultiComponentProperty::SwapItem(const int32& Index1, const int32& Index2)
{
	if (ComponentItems.IsValidIndex(Index1) && ComponentItems.IsValidIndex(Index2))
	{
		ComponentItems.Swap(Index1, Index2);
		return true;
	}
	return false;
}

bool FMultiComponentProperty::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters)
{
	for (int32 i = 0; i < ComponentItems.Num(); ++i)
	{
		if (false == ComponentItems[i].CalculateParameterValue(InGlobalParameter, InParameters))return false;
	}
	return true;
}

bool FMultiComponentProperty::CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, const int32& InSingleComIndex)
{
	if (ComponentItems.IsValidIndex(InSingleComIndex))
	{
		return ComponentItems[InSingleComIndex].CalculateParameterValue(InGlobalParameter, InParameters);
	}
	return true;
}

bool FMultiComponentProperty::LoadComponentData()
{
	bool Res = true;
	for (int32 i = 0; i < ComponentItems.Num(); ++i)
	{
		//Res = ComponentItems[i].LoadComponentData() && Res;
	}
	return Res;
}

bool FMultiComponentProperty::LoadComponentData(const int32& InSingleComIndex)
{
	if (ComponentItems.IsValidIndex(InSingleComIndex))
	{
		//ComponentItems[InSingleComIndex].LoadComponentData();
	}
	return true;
}

void FMultiComponentProperty::RemoveComponent(const int32& InID)
{
	if (ComponentItems.IsValidIndex(InID))
		ComponentItems.RemoveAt(InID);
}

void FMultiComponentProperty::GenerateID()
{
	int32 i = 0;
	for (auto& Iter : ComponentItems)
		Iter.ID = i++;
}

