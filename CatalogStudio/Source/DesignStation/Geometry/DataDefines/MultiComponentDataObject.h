// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GeometryDatas.h"
#include "MultiComponentDataObject.generated.h"

#define LOCTEXT_NAMESPACE "MultiComponentData"

USTRUCT(BlueprintType)
struct FMultiComponentPropertyItem
{
	GENERATED_BODY()
public:

	int32							ID;

	UPROPERTY()
	FString							SingleComponentName;

	UPROPERTY()
	FExpressionValuePair			SingleComponentVisibility;

	UPROPERTY()
	FExpressionValuePair			SingleComponentID;

	UPROPERTY()
	FString							Description;

	UPROPERTY()
	FString							Code;

	UPROPERTY()
	FSingleComponentProperty		SingleComponentData;

	UPROPERTY()
	FLocationProperty				SingleComponentLocation;

	UPROPERTY()
	FRotationProperty				SingleComponentRotation;

	UPROPERTY()
	FScaleProperty					SingleComponentScale;

	UPROPERTY()
	TArray<FParameterData>	SingleComponentParameters;

public:

	FMultiComponentPropertyItem() 
		:SingleComponentName(NSLOCTEXT("LOCTEXT_NAMESPACE", "DefaultCompNameKey", "None").ToString())
		, SingleComponentVisibility(FExpressionValuePair("1"))
		, SingleComponentID(FExpressionValuePair(""))
		, Description(TEXT(""))
		, Code(TEXT(""))
	{}

	bool CopyData(FMultiComponentPropertyItem& OutNewComponent) const;
	
	bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters);

	int32 IsParameterExists(const FString& InParameterName) const;

	bool AddNewParameter(const FParameterData& InNewParameter);

	bool DeleteParameter(const FParameterData& InParameter);

	bool UpdateParameterExpressionAndValue(const FParameterData& InParameter);

	friend FArchive& operator<<(FArchive& Ar, struct FMultiComponentPropertyItem& MultiComponentItemPropertyToSave)
	{
		Ar << MultiComponentItemPropertyToSave.SingleComponentName;
		Ar << MultiComponentItemPropertyToSave.SingleComponentID;
		Ar << MultiComponentItemPropertyToSave.SingleComponentVisibility;
		Ar << MultiComponentItemPropertyToSave.Description;
		Ar << MultiComponentItemPropertyToSave.Code;
		Ar << MultiComponentItemPropertyToSave.SingleComponentLocation;
		Ar << MultiComponentItemPropertyToSave.SingleComponentRotation;
		Ar << MultiComponentItemPropertyToSave.SingleComponentScale;
		Ar << MultiComponentItemPropertyToSave.SingleComponentParameters;
		return Ar;
	}

	bool operator!=(const FMultiComponentPropertyItem& InData)
	{
		if (SingleComponentVisibility != InData.SingleComponentVisibility ||
			SingleComponentID != InData.SingleComponentID ||
			Description != InData.Description ||
			Code != InData.Code ||
			SingleComponentLocation != InData.SingleComponentLocation ||
			SingleComponentRotation != InData.SingleComponentRotation ||
			SingleComponentScale != InData.SingleComponentScale)
			return true;
		if (SingleComponentParameters.Num() != InData.SingleComponentParameters.Num())
			return true;
		int32 i = 0;
		while (i < SingleComponentParameters.Num())
		{
			if (SingleComponentParameters[i] != InData.SingleComponentParameters[i])
				return true;
			++i;
		}
		return false;
	}

	void operator=(const FMultiComponentPropertyItem& InData)
	{
		ID = InData.ID;
		SingleComponentName = InData.SingleComponentName;
		SingleComponentVisibility = InData.SingleComponentVisibility;
		SingleComponentID = InData.SingleComponentID;
		Description = InData.Description;
		Code = InData.Code;
		SingleComponentData = InData.SingleComponentData;
		SingleComponentLocation = InData.SingleComponentLocation;
		SingleComponentRotation = InData.SingleComponentRotation;
		SingleComponentScale = InData.SingleComponentScale;
		SingleComponentParameters = InData.SingleComponentParameters;
	}
};

USTRUCT(BlueprintType)
struct FMultiComponentProperty
{
	GENERATED_BODY()
public:

	UPROPERTY()
	TArray<FMultiComponentPropertyItem>		ComponentItems;

	bool SwapItem(const int32& Index1, const int32& Index2);

	int32 CopyComponent(const int32& InSourceIndex);

	bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters);

	bool CalculateParameterValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParameters, const int32& InSingleComIndex);

	bool LoadComponentData();

	bool LoadComponentData(const int32& InSingleComIndex);

	void RemoveComponent(const int32& InID);

	void GenerateID();

	friend FArchive& operator<<(FArchive& Ar, struct FMultiComponentProperty& MultiComponentPropertyToSave)
	{
		Ar << MultiComponentPropertyToSave.ComponentItems;
		return Ar;
	}

	bool operator!=(const FMultiComponentProperty& InData)
	{
		if (ComponentItems.Num() != InData.ComponentItems.Num())
			return true;
		int32 i = 0;
		while (i < ComponentItems.Num())
		{
			if (ComponentItems[i] != InData.ComponentItems[i])
				return true;
			++i;
		}
		return false;
	}

	void operator=(const FMultiComponentProperty& InData)
	{
		ComponentItems = InData.ComponentItems;
	}
};


/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UMultiComponentDataObject : public UObject
{
	GENERATED_BODY()
	
};

#undef LOCTEXT_NAMESPACE