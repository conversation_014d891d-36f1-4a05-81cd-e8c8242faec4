// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentDisplayerUnit.h"
#include "DesignStation/DesignStation.h"


void FDisplayerUnit::Clear()
{
	if (IS_OBJECT_PTR_VALID(MeshActor)) MeshActor->Destroy();
	MeshActor = nullptr;
	if (IS_OBJECT_PTR_VALID(MeshComp)) MeshComp->DestroyComponent();
	MeshComp = nullptr;
}

FBox FDisplayerUnit::GetBoundBox() const
{
	FBox Box(ForceInit);
	if (ESingleComponentSource::EImportPAK == CompSource)
	{
		if (IS_OBJECT_PTR_VALID(MeshActor))
		{
			FVector Center = FVector::ZeroVector;
			FVector Extention = FVector::ZeroVector;
			MeshActor->GetActorBounds(true, Center, Extention);
			Box += FBox(Center - Extention, Center + Extention);
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(MeshComp))
		{
			Box += MeshComp->Bounds.GetBox();
		}
	}
	return Box;
}

void FSingleComponentDisplayerUnit::Clear()
{
	for (int32 i = 0; i < MeshSections.Num(); ++i)
		MeshSections[i].Clear();
	MeshSections.Empty();
	if (IS_OBJECT_PTR_VALID(Outline)) Outline->DestroyComponent();
	Outline = nullptr;
}

FBox FSingleComponentDisplayerUnit::GetBoundBox() const
{
	FBox Box(ForceInit);
	for (auto& Iter : MeshSections)
	{
		Box += Iter.GetBoundBox();
	}
	return Box;
}

void FMultiComponentDisplayerUnit::Clear()
{
	for (int32 i = 0; i < SingleComponents.Num(); ++i)
		SingleComponents[i].Clear();
	SingleComponents.Empty();
}

FBox FMultiComponentDisplayerUnit::GetBoundBox() const
{
	FBox Box(ForceInit);
	for (auto& Iter : SingleComponents)
	{
		Box += Iter.GetBoundBox();
	}
	return Box;
}
