// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryEdit/Components/VolatileMeshComponent.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "DesignStation/BasicClasses/ImportPakBaseClass.h"
#include "GeometryEdit/Components/OutlineComponent.h"
#include "MultiComponentDisplayerUnit.generated.h"

USTRUCT(BlueprintType)
struct FDisplayerUnit
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadOnly,EditAnyWhere)
		UVolatileMeshComponent* MeshComp = nullptr;

	ESingleComponentSource	CompSource = ESingleComponentSource::ECustom;

	UPROPERTY(BlueprintReadOnly, EditAnyWhere)
		AImportPakBaseClass* MeshActor = nullptr;

public:

	void Clear();

	FBox GetBoundBox() const;
};

USTRUCT(BlueprintType)
struct FSingleComponentDisplayerUnit
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
		TArray<struct FDisplayerUnit> MeshSections;

	UPROPERTY(BlueprintReadOnly, EditAnywhere)
		UOutlineComponent* Outline = nullptr;

public:

	void Clear();

	FBox GetBoundBox() const;

};

USTRUCT(BlueprintType)
struct FMultiComponentDisplayerUnit
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
		TArray<struct FSingleComponentDisplayerUnit> SingleComponents;

public:

	void Clear();

	FBox GetBoundBox() const;
};
