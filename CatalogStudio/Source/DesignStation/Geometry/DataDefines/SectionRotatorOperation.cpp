// Fill out your copyright notice in the Description page of Project Settings.

#include "SectionRotatorOperation.h"

bool FSectionRotatorOperation::operator!=(const FSectionRotatorOperation& InAnother) const
{
	return RotationX != InAnother.RotationX || RotationY != InAnother.RotationY || RotationZ != InAnother.RotationZ || !FMath::IsNearlyEqual(StartAngle, InAnother.StartAngle) || !FMath::IsNearlyEqual(EndAngle, InAnother.EndAngle);
}

bool FSectionRotatorOperation::operator==(const FSectionRotatorOperation& InAnother) const
{
	return RotationX == InAnother.RotationX && RotationY == InAnother.RotationY && RotationZ == InAnother.RotationZ && FMath::IsNearlyEqual(StartAngle, InAnother.StartAngle) && FMath::IsNearlyEqual(EndAngle, InAnother.EndAngle);
}

void FSectionRotatorOperation::operator=(const FSectionRotatorOperation& InAnother)
{
	RotationX = InAnother.RotationX;
	RotationY = InAnother.RotationY;
	RotationZ = InAnother.RotationZ;
	StartAngle = InAnother.StartAngle;
	EndAngle = InAnother.EndAngle;
}

FVector FSectionRotatorOperation::RotatorAxis() const
{
	float X = UParameterPropertyData::ConvertToUeValue(FCString::Atof(*RotationX.Value));
	float Y = UParameterPropertyData::ConvertToUeValue(FCString::Atof(*RotationY.Value));
	float Z = UParameterPropertyData::ConvertToUeValue(FCString::Atof(*RotationZ.Value));
	return FVector(X, Y, Z);
}