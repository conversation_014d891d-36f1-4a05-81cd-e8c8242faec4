// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "SectionRotatorOperation.generated.h"


USTRUCT(BlueprintType)
struct FSectionRotatorOperation
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 ID;
	UPROPERTY()
		FExpressionValuePair RotationX;
	UPROPERTY()
		FExpressionValuePair RotationY;
	UPROPERTY()
		FExpressionValuePair RotationZ;
	UPROPERTY()
		float StartAngle;
	UPROPERTY()
		float EndAngle;

public:

	bool operator!=(const FSectionRotatorOperation& InAnother) const;

	bool operator==(const FSectionRotatorOperation& InAnother) const;

	void operator=(const FSectionRotatorOperation& InAnother);

	FVector RotatorAxis() const;

	FSectionRotatorOperation() :ID(-1), RotationX(0.0f), RotationY(0.0f), RotationZ(0.0f), StartAngle(0.0), EndAngle(360.0f) {};

	void Empty()
	{
		RotationX = FExpressionValuePair(0.0f);
		RotationY = FExpressionValuePair(0.0f);
		RotationZ = FExpressionValuePair(0.0f);
		StartAngle = 0.0f;
		EndAngle = 0.0f;
	}

	friend FArchive& operator<<(FArchive& Ar, struct FSectionRotatorOperation& SectionRotatorOperationToSave)
	{
		Ar << SectionRotatorOperationToSave.RotationX;
		Ar << SectionRotatorOperationToSave.RotationY;
		Ar << SectionRotatorOperationToSave.RotationZ;
		Ar << SectionRotatorOperationToSave.StartAngle;
		Ar << SectionRotatorOperationToSave.EndAngle;
		return Ar;
	}

	void CopyData(const FSectionRotatorOperation& InData)
	{
		ID = InData.ID;
		RotationX = InData.RotationX;
		RotationY = InData.RotationY;
		RotationZ = InData.RotationZ;
		StartAngle = InData.StartAngle;
		EndAngle = InData.EndAngle;
	}
};
