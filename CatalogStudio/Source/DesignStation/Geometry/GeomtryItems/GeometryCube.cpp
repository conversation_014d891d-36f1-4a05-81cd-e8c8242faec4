// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryCube.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"

#define LOCTEXT_NAMESPACE "GeomtryEllipse"

AGeometryCube::AGeometryCube()
{
	PrimaryActorTick.bCanEverTick = false;

	GeomtryItemType = EGeomtryItemType::EGeometryCube;

	NormalMatRef = TEXT("Material'/Game/Materials/WhiteM.WhiteM'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_LineSelected.Mat_LineSelected'");

	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	CubeBodyComp = CreateDefaultSubobject<UVolatileMeshComponent>(TEXT("BodyMesh"));
	CubeBodyComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	CubeBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	this->SetActorEnableCollision(true);
}

void AGeometryCube::SetCubeProperty(const FGeomtryCubeProperty& InCubeProperty)
{
	this->StartLocationX = InCubeProperty.StartLocationX;
	this->StartLocationY = InCubeProperty.StartLocationY;
	this->StartLocationZ = InCubeProperty.StartLocationZ;
	this->EndLocationX = InCubeProperty.EndLocationX;
	this->EndLocationY = InCubeProperty.EndLocationY;
	this->EndLocationZ = InCubeProperty.EndLocationZ;
	this->StartLocation = UParameterPropertyData::ConvertToUeValue(InCubeProperty.StartLocation());
	this->EndLocation = UParameterPropertyData::ConvertToUeValue(InCubeProperty.EndLocation());
}

FText AGeometryCube::GetItemTypeName() const
{
	return NSLOCTEXT(LOCTEXT_NAMESPACE, "CubeKey", "Cube");
}

bool AGeometryCube::ChangeProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryCubeProperty* CubeProperty = static_cast<FGeomtryCubeProperty*>(InOutDataStruct);
	if (CubeProperty)
	{
		if (0 == InType)
		{
			this->StartLocationX = CubeProperty->StartLocationX;
			this->StartLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->StartLocation());
		}
		else if (1 == InType)
		{
			this->StartLocationY = CubeProperty->StartLocationY;
			this->StartLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->StartLocation());
		}
		else if (2 == InType)
		{
			this->StartLocationZ = CubeProperty->StartLocationZ;
			this->StartLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->StartLocation());
		}
		else if (3 == InType)
		{
			this->EndLocationX = CubeProperty->EndLocationX;
			this->EndLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->EndLocation());
		}
		else if (4 == InType)
		{
			this->EndLocationY = CubeProperty->EndLocationY;
			this->EndLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->EndLocation());
		}
		else if (5 == InType)
		{
			this->EndLocationZ = CubeProperty->EndLocationZ;
			this->EndLocation = UParameterPropertyData::ConvertToUeValue(CubeProperty->EndLocation());
		}
		this->Refresh();
		return true;
	}
	return false;
}

void AGeometryCube::Refresh()
{
	FPMCSection MeshInfo;
	if (UGeometryRelativeLibrary::GenerateCubeMesh(this->StartLocation, this->EndLocation, MeshInfo))
	{
		CubeBodyComp->ClearAllMeshSections();
		TArray<FLinearColor> Color;
		CubeBodyComp->CreateMeshSection(0, MeshInfo, true);
		CubeBodyComp->SetMaterial(0, this->GetNormalMaterial());
	}
}

bool AGeometryCube::CollectPoints(TArray<FVector>& OutPoints)
{
	FPMCSection MeshInfo;
	if (UGeometryRelativeLibrary::GenerateCubeMesh(this->StartLocation, this->EndLocation, MeshInfo))
	{
		OutPoints.Append(MeshInfo.Vertexes);
		return true;
	}
	return false;
}

bool AGeometryCube::ConstructSaveData(void* InOutDataStruct)
{
	FGeomtryCubeProperty* CubeProperty = static_cast<FGeomtryCubeProperty*>(InOutDataStruct);
	if (CubeProperty)
	{
		CubeProperty->StartLocationX = this->StartLocationX;
		CubeProperty->StartLocationY = this->StartLocationY;
		CubeProperty->StartLocationZ = this->StartLocationZ;
		CubeProperty->EndLocationX = this->EndLocationX;
		CubeProperty->EndLocationY = this->EndLocationY;
		CubeProperty->EndLocationZ = this->EndLocationZ;
		return true;
	}
	return false;
}

void AGeometryCube::OnMouseOver()
{
	CubeBodyComp->SetMaterial(0, this->GetSelectedMaterial());
}

void AGeometryCube::OnMouseLeave()
{
	CubeBodyComp->SetMaterial(0, this->GetNormalMaterial());
}

void AGeometryCube::OnSlectionChanged(const bool& InNewSelection)
{
	if (InNewSelection != IsSelected)
	{
		IsSelected = InNewSelection;
		CubeBodyComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
	}
}

void AGeometryCube::DeleteGeomtryItem()
{
	AGeometryItemBase::DeleteGeomtryItem();
	CubeBodyComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

void AGeometryCube::BringBack(void* InData)
{
	FGeomtryCubeProperty* PropertyData = static_cast<FGeomtryCubeProperty*>(InData);
	if (nullptr != PropertyData)
	{
		AGeometryItemBase::BringBack(InData);
		CubeBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetCubeProperty(*PropertyData);
		this->Refresh();
	}
}

bool AGeometryCube::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryCubeProperty::GetName());
}

AGeometryCube* AGeometryCube::Create(UWorld*& InWorld, const FGeomtryCubeProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewCube = InWorld->SpawnActor(AGeometryCube::StaticClass(), &tran);
		AGeometryCube* NewGeomtryCube = Cast<AGeometryCube>(NewCube);
		NewGeomtryCube->SetCubeProperty(InProperty);
		NewGeomtryCube->Refresh();
		return NewGeomtryCube;
	}
	return nullptr;
}

#undef LOCTEXT_NAMESPACE
