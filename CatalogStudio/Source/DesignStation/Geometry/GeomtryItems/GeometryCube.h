// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "GeometryEdit/Components/VolatileMeshComponent.h"
#include "GeometryCube.generated.h"

/**
 *
 */
UCLASS()
class DESIGNSTATION_API AGeometryCube : public AGeometryItemBase
{
	GENERATED_BODY()

protected:
	FExpressionValuePair StartLocationX;
	FExpressionValuePair StartLocationY;
	FExpressionValuePair StartLocationZ;
	FExpressionValuePair EndLocationX;
	FExpressionValuePair EndLocationY;
	FExpressionValuePair EndLocationZ;
	FVector StartLocation;
	FVector EndLocation;

	UPROPERTY()
		UVolatileMeshComponent* CubeBodyComp;

public:
	AGeometryCube();

public:

	void SetCubeProperty(const FGeomtryCubeProperty& InEllipseProperty);

	virtual FText GetItemTypeName() const override;

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) override;

	UFUNCTION(BlueprintCallable, Category = "CubeMesh")
		virtual void Refresh() override;

	virtual bool CollectPoints(TArray<FVector>& OutPoints) override;

	virtual bool ConstructSaveData(void* InOutDataStruct) override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	virtual void DeleteGeomtryItem() override;

	virtual void BringBack(void* InData) override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	static AGeometryCube* Create(UWorld*& InWorld, const FGeomtryCubeProperty& InProperty);
};
