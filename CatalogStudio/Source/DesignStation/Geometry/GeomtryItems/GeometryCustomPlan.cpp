// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryCustomPlan.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DataCenter/Libraries/GeomtryMathmaticLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"


FGeomtryCustomPlanProperty::FGeomtryCustomPlanProperty(const FPolygonData& InPolygon)
	:IsPolygon(true)
	, PolygonData(InPolygon)
	, MeshData(FPMCSection())
{
}

FGeomtryCustomPlanProperty::FGeomtryCustomPlanProperty(const FPMCSection& InMesh)
	:IsPolygon(false)
	, PolygonData(FPolygonData())
	, MeshData(InMesh)
{
}

AGeometryCustomPlan::AGeometryCustomPlan()
{
	PrimaryActorTick.bCanEverTick = false;
	GeomtryItemType = EGeomtryItemType::EGeomtryCustomPlan;

	/*NormalMatRef = TEXT("Material'/Game/Materials/Mat_CustomPlanNormal.Mat_CustomPlanNormal'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_CustomPlanSelected.Mat_CustomPlanSelected'");*/
	   
	NormalMatRef = TEXT("Material'/Game/Materials/WhiteM.WhiteM'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_LineSelected.Mat_LineSelected'");

	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	BodyMeshComp = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BodyMesh"));
	BodyMeshComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	this->SetActorEnableCollision(true);
}

void AGeometryCustomPlan::OnMouseOver()
{
	BodyMeshComp->SetMaterial(0, this->GetSelectedMaterial());
}

void AGeometryCustomPlan::OnMouseLeave()
{
	BodyMeshComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
}

void AGeometryCustomPlan::OnSlectionChanged(const bool& InNewSelection)
{
	if (InNewSelection != IsSelected)
	{
		IsSelected = InNewSelection;
		BodyMeshComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
	}
}

void AGeometryCustomPlan::SetCustomPlanProperty(const FGeomtryCustomPlanProperty& InProperty)
{
	PolygonData = InProperty.PolygonData;
	for (auto& Iter : PolygonData.PolygonVertice)
	{
		FGeometryDatas::FormatLocation(PolygonData.PolygonPlan, Iter);
	}
}

void AGeometryCustomPlan::Refresh()
{
	BodyMeshComp->ClearAllMeshSections();
	FPMCSection MeshInfo;
	PolygonData.GenerateMesh(MeshInfo);
	//if (EPlanPolygonBelongs::EYZ_Plan == PolygonData.PolygonPlan) MeshInfo.FlipNormal();
	TArray<FLinearColor> Color;
	BodyMeshComp->CreateMeshSection_LinearColor(0, MeshInfo.Vertexes, MeshInfo.Triangles, MeshInfo.Normals, MeshInfo.UV, Color, MeshInfo.Tangents, true);



	BodyMeshComp->SetMaterial(0, this->GetNormalMaterial());
}

void AGeometryCustomPlan::DeleteGeomtryItem()
{
	Super::DeleteGeomtryItem();
	BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

void AGeometryCustomPlan::BringBack(void* InData)
{
	FGeomtryCustomPlanProperty* PropertyData = static_cast<FGeomtryCustomPlanProperty*>(InData);
	if (nullptr != PropertyData)
	{
		Super::BringBack(InData);
		BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetCustomPlanProperty(*PropertyData);
		this->Refresh();
	}
}

bool AGeometryCustomPlan::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryCustomPlanProperty::GetName());
}

AGeometryCustomPlan* AGeometryCustomPlan::Create(UWorld*& InWorld, const FGeomtryCustomPlanProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewPoint = InWorld->SpawnActor(AGeometryCustomPlan::StaticClass(), &tran);
		AGeometryCustomPlan* NewGeomtryPoint = Cast<AGeometryCustomPlan>(NewPoint);
		NewGeomtryPoint->SetCustomPlanProperty(InProperty);
		NewGeomtryPoint->Refresh();
		return NewGeomtryPoint;
	}
	return nullptr;
}

void AGeometryCustomPlan::ClearPlan()
{
	BodyMeshComp->ClearAllMeshSections();
}
