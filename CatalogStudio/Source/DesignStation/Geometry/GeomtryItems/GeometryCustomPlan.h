// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "ProceduralMeshComponent.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "GeometryCustomPlan.generated.h"

struct FGeomtryCustomPlanProperty
{
private:
	bool IsPolygon;
public:
	bool GetIsPolygon() const { return IsPolygon; }
	FPolygonData PolygonData;
	FPMCSection MeshData;
	FGeomtryCustomPlanProperty() :IsPolygon(true), PolygonData(EPlanPolygonBelongs::EXY_Plan, EPolygonVerticesOrder::EUnknown), MeshData(FPMCSection()) {}
	FGeomtryCustomPlanProperty(const FPolygonData& InPolygon);
	FGeomtryCustomPlanProperty(const FPMCSection& InMesh);

	static FName GetName()
	{
		return FName(TEXT("FGeomtryCustomPlanProperty"));
	}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API AGeometryCustomPlan : public AGeometryItemBase
{
	GENERATED_BODY()

protected:

	FPolygonData PolygonData;

	UPROPERTY()
		UProceduralMeshComponent* BodyMeshComp;

public:

	AGeometryCustomPlan();

	GenerateGetConstRef(FPolygonData, PolygonData)

		void SetCustomPlanProperty(const FGeomtryCustomPlanProperty& InProperty);

	void ClearPlan();

	virtual void Refresh() override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	virtual void DeleteGeomtryItem() override;

	virtual void BringBack(void* InData) override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	static AGeometryCustomPlan* Create(UWorld*& InWorld, const FGeomtryCustomPlanProperty& InProperty);

};
