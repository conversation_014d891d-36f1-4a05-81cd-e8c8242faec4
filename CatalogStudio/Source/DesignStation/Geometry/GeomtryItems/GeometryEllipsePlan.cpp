// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryEllipsePlan.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "GeometryEdit/Public/Geometry3DLibrary.h"

#define LOCTEXT_NAMESPACE "GeomtryEllipse"

// Sets default values
AGeometryEllipsePlan::AGeometryEllipsePlan()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	GeomtryItemType = EGeomtryItemType::EGeometryEllipse;

	NormalMatRef = TEXT("Material'/Game/Materials/WhiteM.WhiteM'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_LineSelected.Mat_LineSelected'");

	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	EllipseleBodyComp = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BodyMesh"));
	EllipseleBodyComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	EllipseleBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	this->SetActorEnableCollision(true);
}

void AGeometryEllipsePlan::setVertex(const TArray<FVector>& inVertex)
{
	vertex.Empty();
	vertex = inVertex;
}

TArray<FVector> AGeometryEllipsePlan::getVertext()
{
	return vertex;
}

void AGeometryEllipsePlan::SetEllipseProperty(const FGeomtryEllipsePlanProperty& InEllipseProperty)
{
	this->PlanBelongs = InEllipseProperty.PlanBelongs;
	this->CenterLocationX = InEllipseProperty.CenterLocationX;
	this->CenterLocationY = InEllipseProperty.CenterLocationY;
	this->CenterLocationZ = InEllipseProperty.CenterLocationZ;
	this->CenterLocation = InEllipseProperty.CenterLocation();
	this->ShortRadiusData = InEllipseProperty.ShortRadiusData;
	this->LongRadiusData = InEllipseProperty.LongRadiusData;
	this->ShortRadius = InEllipseProperty.ShortRadius();
	this->LongRadius = InEllipseProperty.LongRadius();
	this->InterpPointCountData = InEllipseProperty.InterpPointCountData;
}

FText AGeometryEllipsePlan::GetItemTypeName() const
{
	return NSLOCTEXT(LOCTEXT_NAMESPACE, "EllipseKey", "Ellipse");
}

bool AGeometryEllipsePlan::ChangeProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryEllipsePlanProperty* EllipseProperty = static_cast<FGeomtryEllipsePlanProperty*>(InOutDataStruct);
	if (EllipseProperty)
	{
		if (0 == InType/* && EPlanPolygonBelongs::EYZ_Plan != this->PlanBelongs*/)
		{
			this->CenterLocationX = EllipseProperty->CenterLocationX;
			this->CenterLocation = EllipseProperty->CenterLocation();
		}
		else if (1 == InType/* && EPlanPolygonBelongs::EXZ_Plan != this->PlanBelongs*/)
		{
			this->CenterLocationY = EllipseProperty->CenterLocationY;
			this->CenterLocation = EllipseProperty->CenterLocation();
		}
		else if (2 == InType/* && EPlanPolygonBelongs::EXY_Plan != this->PlanBelongs*/)
		{
			this->CenterLocationZ = EllipseProperty->CenterLocationZ;
			this->CenterLocation = EllipseProperty->CenterLocation();
		}
		else if (3 == InType)
		{
			this->ShortRadiusData = EllipseProperty->ShortRadiusData;
			this->ShortRadius = EllipseProperty->ShortRadius();
		}
		else if (4 == InType)
		{
			this->LongRadiusData = EllipseProperty->LongRadiusData;
			this->LongRadius = EllipseProperty->LongRadius();
		}
		else if (5 == InType)
		{
			this->InterpPointCountData = EllipseProperty->InterpPointCountData;
		}
		this->Refresh();
		return true;
	}
	return false;
}

void AGeometryEllipsePlan::Refresh()
{
	EllipseleBodyComp->ClearAllMeshSections();

	FVector PlanNormal = NSPlanType::GetPlanNormal(PlanBelongs);
	
	//if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs) PlanNormal *= -1.0f;
	const auto PlanTangentX = NSPlanType::GetPlanTangentX(PlanBelongs);

	TArray<FVector> EllipsePoints;
	bool Res = FGeometry3DLibrary::GenerateEllipsePlanPoints(CenterLocation, LongRadius, ShortRadius, PlanNormal, PlanTangentX, EllipsePoints, FCString::Atoi(*this->InterpPointCountData.Value));
	if (Res)
	{
		FPMCSection MeshInfo;
		FPolygon3DLibrary::GenerateMeshFromPolygon2D(EllipsePoints, PlanNormal, PlanTangentX, CenterLocation, 100.0f, MeshInfo);
		TArray<FLinearColor> Color;
		EllipseleBodyComp->CreateMeshSection_LinearColor(0, MeshInfo.Vertexes, MeshInfo.Triangles, MeshInfo.Normals, MeshInfo.UV, Color, MeshInfo.Tangents, true);
		EllipseleBodyComp->SetMaterial(0, this->GetNormalMaterial());
	}
}

bool AGeometryEllipsePlan::CollectPoints(TArray<FVector>& OutPoints)
{
	FPMCSection MeshInfo;
	if (UGeometryRelativeLibrary::GenerateEllipseMesh(this->PlanBelongs, this->CenterLocation, this->ShortRadius, this->LongRadius, FCString::Atoi(*this->InterpPointCountData.Value), MeshInfo))
	{
		OutPoints.Append(MeshInfo.Vertexes);
		return true;
	}
	return false;
}

bool AGeometryEllipsePlan::ConstructSaveData(void* InOutDataStruct)
{
	FGeomtryEllipsePlanProperty* DataStruct = static_cast<FGeomtryEllipsePlanProperty*>(InOutDataStruct);
	if (nullptr != DataStruct)
	{
		DataStruct->PlanBelongs = this->PlanBelongs;
		DataStruct->CenterLocationX = this->CenterLocationX;
		DataStruct->CenterLocationY = this->CenterLocationY;
		DataStruct->CenterLocationZ = this->CenterLocationZ;
		DataStruct->ShortRadiusData = this->ShortRadiusData;
		DataStruct->LongRadiusData = this->LongRadiusData;
		DataStruct->InterpPointCountData = this->InterpPointCountData;
		return true;
	}
	return false;
}

void AGeometryEllipsePlan::OnMouseOver()
{
	EllipseleBodyComp->SetMaterial(0, this->GetSelectedMaterial());
}

void AGeometryEllipsePlan::OnMouseLeave()
{
	EllipseleBodyComp->SetMaterial(0, this->GetNormalMaterial());
}

void AGeometryEllipsePlan::OnSlectionChanged(const bool& InNewSelection)
{
	if (InNewSelection != IsSelected)
	{
		IsSelected = InNewSelection;
		EllipseleBodyComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
	}
}

void AGeometryEllipsePlan::DeleteGeomtryItem()
{
	AGeometryItemBase::DeleteGeomtryItem();
	EllipseleBodyComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

void AGeometryEllipsePlan::BringBack(void* InData)
{
	FGeomtryEllipsePlanProperty* PropertyData = static_cast<FGeomtryEllipsePlanProperty*>(InData);
	if (nullptr != PropertyData)
	{
		AGeometryItemBase::BringBack(InData);
		EllipseleBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetEllipseProperty(*PropertyData);
		this->Refresh();
	}
}

bool AGeometryEllipsePlan::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryEllipsePlanProperty::GetName());
}

AGeometryEllipsePlan* AGeometryEllipsePlan::Create(UWorld*& InWorld, const FGeomtryEllipsePlanProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewEllipse = InWorld->SpawnActor(AGeometryEllipsePlan::StaticClass(), &tran);
		AGeometryEllipsePlan* NewGeomtryEllipse = Cast<AGeometryEllipsePlan>(NewEllipse);
		NewGeomtryEllipse->SetEllipseProperty(InProperty);
		NewGeomtryEllipse->Refresh();
		return NewGeomtryEllipse;
	}
	return nullptr;
}

#undef LOCTEXT_NAMESPACE
