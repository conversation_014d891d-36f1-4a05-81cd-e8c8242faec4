// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "ProceduralMeshComponent.h"
#include "GeometryEllipsePlan.generated.h"

UCLASS()
class DESIGNSTATION_API AGeometryEllipsePlan : public AGeometryItemBase
{
	GENERATED_BODY()

protected:
	EPlanPolygonBelongs PlanBelongs;
	FExpressionValuePair CenterLocationX;
	FExpressionValuePair CenterLocationY;
	FExpressionValuePair CenterLocationZ;
	FExpressionValuePair ShortRadiusData;
	FExpressionValuePair LongRadiusData;
	FVector CenterLocation;
	float	ShortRadius;
	float	LongRadius;
	FExpressionValuePair	InterpPointCountData;

	UPROPERTY()
		UProceduralMeshComponent* EllipseleBodyComp;

	TArray<FVector> vertex;

public:
	// Sets default values for this actor's properties
	AGeometryEllipsePlan();

public:

	void setVertex(const TArray<FVector>& inVertex);
	TArray<FVector> getVertext();


	void SetEllipseProperty(const FGeomtryEllipsePlanProperty& InEllipseProperty);

	virtual FText GetItemTypeName() const override;

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) override;

	virtual void Refresh() override;

	virtual bool CollectPoints(TArray<FVector>& OutPoints) override;

	virtual bool ConstructSaveData(void* InOutDataStruct) override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	virtual void DeleteGeomtryItem() override;

	virtual void BringBack(void* InData) override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	static AGeometryEllipsePlan* Create(UWorld*& InWorld, const FGeomtryEllipsePlanProperty& InProperty);
};
