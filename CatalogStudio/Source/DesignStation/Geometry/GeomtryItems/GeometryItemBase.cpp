// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryItemBase.h"


// Sets default values
AGeometryItemBase::AGeometryItemBase()
	:IsDeleted(false)
	, IsSelected(false)
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

}

// Called when the game starts or when spawned
void AGeometryItemBase::BeginPlay()
{
	Super::BeginPlay();

}

void AGeometryItemBase::DeleteGeomtryItem()
{
	IsDeleted = true;
	this->SetActorHiddenInGame(true);
}

void AGeometryItemBase::BringBack(void* InData)
{
	IsDeleted = false;
	this->SetActorHiddenInGame(false);
}

UMaterial* AGeometryItemBase::GetNormalMaterial()
{
	UMaterial* NormalMat = LoadObject<UMaterial>(nullptr, *NormalMatRef);
	return NormalMat;
}

UMaterial* AGeometryItemBase::GetSelectedMaterial()
{
	UMaterial* SelectedMat = LoadObject<UMaterial>(nullptr, *SelectedMatRef);
	return SelectedMat;
}

bool AGeometryItemBase::LineTraceForSingleItem(TArray<AGeometryItemBase*>& InItems, const FVector& Start, const FVector& End, FHitResult& HitInfo)
{
	float ImpactDistance = FVector::Dist(Start, End);
	FCollisionQueryParams CollisionQueryParams(SCENE_QUERY_STAT(ClickableTrace), true);
	bool bHit = false;
	for (auto& Iter : InItems)
	{
		if (!Iter) continue;
		FHitResult Res;
		bool bNewHit = Iter->ActorLineTraceSingle(Res, Start, End, ECollisionChannel::ECC_Visibility, CollisionQueryParams);
		if (bNewHit && (Res.Distance < ImpactDistance))
		{
			//UE_LOG(LogTemp, Log, TEXT("AGeometryItemBase::LineTraceForSingleItem bNewHit=%d"), bNewHit);
			//Res.GetActor() = Iter;
			ImpactDistance = Res.Distance;
			HitInfo = MoveTemp(Res);
		}
		bHit = bHit || bNewHit;
	}
	return bHit;
}