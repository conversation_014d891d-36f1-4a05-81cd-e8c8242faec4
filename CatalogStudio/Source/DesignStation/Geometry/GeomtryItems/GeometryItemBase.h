// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "GeometryItemBase.generated.h"

#define		POINT_ACTOR_HEIGHT	(1.0f)
#define		LINE_ACTOR_HEIGHT	(-1.0f)
#define		PLANE_ACTOR_HEIGHT	(0.0f)

UCLASS()
class DESIGNSTATION_API AGeometryItemBase : public AActor
{
	GENERATED_BODY()

protected:

	bool IsDeleted;

	bool IsSelected;

	EGeomtryItemType GeomtryItemType;

	FString NormalMatRef;

	FString SelectedMatRef;

public:
	// Sets default values for this actor's properties
	AGeometryItemBase();

	GenerateGetConstRef(bool, IsDeleted)
		GenerateGetConstRef(EGeomtryItemType, GeomtryItemType)

		virtual FText GetItemTypeName() const { return FText(); }

	virtual void DeleteGeomtryItem();

	virtual void BringBack(void* InData);

	virtual void Refresh() {}

	virtual bool CollectPoints(TArray<FVector>& OutPoints) { return false; }

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) { return false; }

	virtual bool ConstructSaveData(void* InOutDataStruct) { return false; }

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) { return false; }

	virtual void ScaleGeomtryItem(const float& InScale) {}

	virtual void OnMouseOver() {}
	virtual void OnMouseLeave() {}
	virtual void OnSlectionChanged(const bool& InNewSelection) {}

	static bool LineTraceForSingleItem(TArray<AGeometryItemBase*>& InItems, const FVector& Start, const FVector& End, FHitResult& HitInfo);

	virtual EGeomtryItemType GetType() 
	{
		return GeomtryItemType;
	}
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	UMaterial* GetNormalMaterial();

	UMaterial* GetSelectedMaterial();
};
