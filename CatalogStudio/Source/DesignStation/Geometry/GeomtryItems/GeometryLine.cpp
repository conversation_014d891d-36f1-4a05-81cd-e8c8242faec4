// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryLine.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "Runtime/Engine/Public/DrawDebugHelpers.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "GeometricCalculate/Library/GeometryLibrary.h"

#define GEOMTRY_LINE_THICKNESS	(3.2f)

#define LOCTEXT_NAMESPACE "GeometryLine"

FVector AGeometryLine::OutsideNormal = FVector::UpVector;


// Sets default values
AGeometryLine::AGeometryLine()
	:LineThickness(GEOMTRY_LINE_THICKNESS)
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	GeomtryItemType = EGeomtryItemType::EGeomtryLine;

	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));

	LineBodyComp = CreateDefaultSubobject<ULineSegmentComponent>(TEXT("BodyMesh"));
	LineBodyComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	LineBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	LineBodyComp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Block);
	LineBodyComp->SetLineColor(FColor(0x66, 0x66, 0x66));
	lineData = MakeShared<FGeometryLineData>();
	this->SetActorEnableCollision(true);
}

TSharedPtr<FGeometryLineData> AGeometryLine::getData()
{
	return lineData;
}

void AGeometryLine::SetLineProperty(const FGeomtryLineProperty& InLineProperty)
{
	this->LineType = InLineProperty.LineType;
	this->PlanBelongs = InLineProperty.PlanBelongs;
	this->RadiusOrHeightData = InLineProperty.RadiusOrHeightData;
	this->RadiusOrHeight = InLineProperty.RadiusOrHeight();
	this->InterpPointCountData = InLineProperty.InterpPointCountData;
	this->StartLocation = InLineProperty.StartLocation;
	this->EndLocation = InLineProperty.EndLocation;
	this->BigArc = InLineProperty.BigArc;
	FGeometryDatas::FormatLocation(this->PlanBelongs, this->StartLocation);
	FGeometryDatas::FormatLocation(this->PlanBelongs, this->EndLocation);
}

void AGeometryLine::SetLineData(TSharedPtr<FGeometryLineData> inData)
{
	lineData = inData;
}

FGeomtryLineProperty AGeometryLine::GetLineProperty()
{
	FGeomtryLineProperty lineProperty;
	lineProperty.LineType = this->LineType;
	lineProperty.PlanBelongs = this->PlanBelongs;
	lineProperty.RadiusOrHeightData = this->RadiusOrHeightData;
	lineProperty.InterpPointCountData = this->InterpPointCountData;
	lineProperty.StartLocation = this->StartLocation;
	lineProperty.EndLocation = this->EndLocation;
	lineProperty.BigArc = this->BigArc;
	return lineProperty;
}

void AGeometryLine::Refresh()
{
	FVector Nor = FVector::UpVector;
	FVector RelativeLocation = FVector::ZeroVector;
	if (EPlanPolygonBelongs::EXY_Plan == PlanBelongs)
	{
		RelativeLocation.Z = -0.05f;
		Nor = FVector::ZAxisVector;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs)
	{
		RelativeLocation.X = -0.05f;
		Nor = FVector::XAxisVector;
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == PlanBelongs)
	{
		RelativeLocation.Y = -0.05f;
		Nor = FVector::YAxisVector;
	}
	bool Res = false;
	TArray<FVector> LinePoints;

	if ((ELineType::ELineSegment == this->LineType) || FMath::IsNearlyZero(RadiusOrHeight, THRESH_POINTS_ARE_NEAR))
	{
		LinePoints.AddDefaulted(2);
		LinePoints[0] = StartLocation;
		LinePoints[1] = EndLocation;
	}
	else if (ELineType::ERadiusArc == this->LineType)
	{
		Res = FGeometryLibrary::createArcSegmentByRadius(RadiusOrHeight, StartLocation, EndLocation, 50, Nor, BigArc, FTransform(), LinePoints);
	}
	else if (ELineType::EHeightArc == this->LineType)
	{
		Res = FGeometryLibrary::createArcSegmentByHeight(RadiusOrHeight, StartLocation, EndLocation, 50, Nor, FTransform(), LinePoints);
	}
	LineBodyComp->SetLinePoints(LinePoints);
	LineBodyComp->SetRelativeLocation(RelativeLocation);
	this->SetActorLocation(FVector::ZeroVector);
}

void AGeometryLine::setVertex(const TArray<FVector>& inVertex)
{
	FVector Nor = FVector::UpVector;
	FVector RelativeLocation = FVector::ZeroVector;
	if (EPlanPolygonBelongs::EXY_Plan == PlanBelongs)
	{
		RelativeLocation.Z = -0.05f;
		Nor = FVector::ZAxisVector;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs)
	{
		RelativeLocation.X = -0.05f;
		Nor = FVector::XAxisVector;
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == PlanBelongs)
	{
		RelativeLocation.Y = -0.05f;
		Nor = FVector::YAxisVector;
	}
	LineBodyComp->SetLinePoints(inVertex);
	LineBodyComp->SetRelativeLocation(RelativeLocation);
	this->SetActorLocation(FVector::ZeroVector);
}

bool AGeometryLine::CollectPoints(TArray<FVector>& OutPoints)
{
	OutPoints.Append(LineBodyComp->LinePoints);
	return true;
}

bool AGeometryLine::ConstructSaveData(void* InOutDataStruct)
{
	FGeomtryLineProperty* DataStruct = static_cast<FGeomtryLineProperty*>(InOutDataStruct);
	if (nullptr != DataStruct)
	{
		DataStruct->LineType = this->LineType;
		DataStruct->PlanBelongs = this->PlanBelongs;
		DataStruct->StartLocation = this->StartLocation;
		DataStruct->EndLocation = this->EndLocation;
		DataStruct->RadiusOrHeightData = this->RadiusOrHeightData;
		DataStruct->InterpPointCountData = this->InterpPointCountData;
		DataStruct->BigArc = this->BigArc;
		return true;
	}
	return false;
}

AGeometryLine* AGeometryLine::Create(UWorld*& InWorld, const FGeomtryLineProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewPoint = InWorld->SpawnActor(AGeometryLine::StaticClass(), &tran);
		AGeometryLine* NewGeomtryPoint = Cast<AGeometryLine>(NewPoint);
		NewGeomtryPoint->SetLineProperty(InProperty);
		NewGeomtryPoint->Refresh();
		return NewGeomtryPoint;
	}
	return nullptr;
}

void AGeometryLine::OnMouseOver()
{
	LineBodyComp->SetLineColor(FColor(0x29, 0xb6, 0xf2));
}

void AGeometryLine::OnMouseLeave()
{
	LineBodyComp->SetLineColor(IsSelected ? FColor(0x29, 0xb6, 0xf2) : FColor(0x66, 0x66, 0x66));
}

void AGeometryLine::OnSlectionChanged(const bool& InNewSelection)
{
	IsSelected = InNewSelection;
	LineBodyComp->SetLineColor(IsSelected ? FColor(0x29, 0xb6, 0xf2) : FColor(0x66, 0x66, 0x66));
}


FText AGeometryLine::GetItemTypeName() const
{
	if (ELineType::ELineSegment == LineType)
	{
		return NSLOCTEXT(LOCTEXT_NAMESPACE, "LineSegmentKey", "Line Segment");
	}
	else if (ELineType::EHeightArc == LineType)
	{
		return NSLOCTEXT(LOCTEXT_NAMESPACE, "HeightArcKey", "Height Arc");
	}
	else if (ELineType::ERadiusArc == LineType)
	{
		return NSLOCTEXT(LOCTEXT_NAMESPACE, "RadiusArcKey", "Radius Arc");
	}
	return AGeometryItemBase::GetItemTypeName();
}

bool AGeometryLine::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryLineProperty::GetName());
}

void AGeometryLine::DeleteGeomtryItem()
{
	Super::DeleteGeomtryItem();
	LineBodyComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

void AGeometryLine::BringBack(void* InData)
{
	FGeomtryLineProperty* PropertyData = static_cast<FGeomtryLineProperty*>(InData);
	if (nullptr != PropertyData)
	{
		Super::BringBack(InData);
		LineBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetLineProperty(*PropertyData);
		this->Refresh();
	}
}

bool AGeometryLine::ChangeProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryLineProperty* LineProperty = static_cast<FGeomtryLineProperty*>(InOutDataStruct);
	if (LineProperty)
	{
		if (0 == InType)
		{//line type
			this->LineType = LineProperty->LineType;
		}
		else if (1 == InType)
		{//radius or height
			this->RadiusOrHeightData = LineProperty->RadiusOrHeightData;
			this->RadiusOrHeight = LineProperty->RadiusOrHeight();
		}
		else if (3 == InType)
		{//interp point count
			this->InterpPointCountData = LineProperty->InterpPointCountData;
		}
		else if (5 == InType)
		{//big arc
			this->BigArc = LineProperty->BigArc;
		}
		else
		{
			checkNoEntry();
		}
		this->Refresh();
		return true;
	}
	return false;
}

bool AGeometryLine::setProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryLineProperty* LineProperty = static_cast<FGeomtryLineProperty*>(InOutDataStruct);
	if (LineProperty)
	{
		if (0 == InType)
		{//line type
			this->LineType = LineProperty->LineType;
		}
		else if (1 == InType)
		{//radius or height
			this->RadiusOrHeightData = LineProperty->RadiusOrHeightData;
			this->RadiusOrHeight = LineProperty->RadiusOrHeight();
		}
		else if (3 == InType)
		{//interp point count
			this->InterpPointCountData = LineProperty->InterpPointCountData;
		}
		else if (5 == InType)
		{//big arc
			this->BigArc = LineProperty->BigArc;
		}
		else
		{
			checkNoEntry();
		}
		return true;
	}
	return false;
}

bool AGeometryLine::setProperty(void* InOutDataStruct)
{
	FGeomtryLineProperty* LineProperty = static_cast<FGeomtryLineProperty*>(InOutDataStruct);
	if (LineProperty)
	{
		this->LineType = LineProperty->LineType;
		this->RadiusOrHeightData = LineProperty->RadiusOrHeightData;
		this->RadiusOrHeight = LineProperty->RadiusOrHeight();
		this->InterpPointCountData = LineProperty->InterpPointCountData;
		this->BigArc = LineProperty->BigArc;
	}
	if (lineData.IsValid())
	{
		lineData->lineProperty.CopyData(*LineProperty);
	}
	return true;
}

void AGeometryLine::reverse()
{
	auto temp = StartLocation;
	StartLocation = EndLocation;
	EndLocation = temp;
}

// Called when the game starts or when spawned
void AGeometryLine::BeginPlay()
{
	Super::BeginPlay();

}

#undef GEOMTRY_LINE_THICKNESS
#undef LOCTEXT_NAMESPACE
