// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "GeometryEdit/Components/LineSegmentComponent.h"
#include "GeometryLine.generated.h"




UCLASS()
class DESIGNSTATION_API AGeometryLine : public AGeometryItemBase
{
	GENERATED_BODY()

protected:
	ELineType LineType;
	EPlanPolygonBelongs PlanBelongs;
	FExpressionValuePair RadiusOrHeightData;
	float RadiusOrHeight;
	FExpressionValuePair InterpPointCountData;
	FVector StartLocation;
	FVector EndLocation;
	bool BigArc;

	float LineThickness;

	UPROPERTY()
		ULineSegmentComponent* LineBodyComp;

	TSharedPtr<FGeometryLineData>  lineData;
public:

	static FVector OutsideNormal;


public:
	// Sets default values for this actor's properties
	AGeometryLine();
	TSharedPtr<FGeometryLineData> getData();
	GenerateGetConstRef(ELineType, LineType)
		GenerateGetConstRef(EPlanPolygonBelongs, PlanBelongs)
		GenerateGetConstRef(float, RadiusOrHeight)
		GenerateGetConstRef(FExpressionValuePair, InterpPointCountData)
		GenerateSet(FVector, StartLocation)
		GenerateSet(FVector, EndLocation)
		GenerateSet(FExpressionValuePair, InterpPointCountData)
		GenerateSet(bool, BigArc)

		void SetLineProperty(const FGeomtryLineProperty& InLineProperty);

	void SetLineData(TSharedPtr<FGeometryLineData>  inData);
	TSharedPtr<FGeometryLineData> getLineData() { return lineData; }
	FGeomtryLineProperty GetLineProperty();
	virtual FText GetItemTypeName() const override;

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) override;
	bool setProperty(const int32& InType, void* InOutDataStruct);
	bool setProperty(void* InOutDataStruct);

	virtual void Refresh() override;
	void setVertex(const TArray<FVector>& inVertex);
	virtual bool CollectPoints(TArray<FVector>& OutPoints) override;

	virtual bool ConstructSaveData(void* InOutDataStruct) override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	virtual void DeleteGeomtryItem() override;

	virtual void BringBack(void* InData) override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	static AGeometryLine* Create(UWorld*& InWorld, const FGeomtryLineProperty& InProperty);
	FVector getStartPoint() { return StartLocation; }
	FVector getEndPoint() { return EndLocation; }
	void reverse();

	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
};



