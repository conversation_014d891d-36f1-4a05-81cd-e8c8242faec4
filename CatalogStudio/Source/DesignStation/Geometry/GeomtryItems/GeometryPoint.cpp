// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryPoint.h"
#include "Runtime/Engine/Classes/Engine/StaticMesh.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"

#define LOCTEXT_NAMESPACE "GeometryPoint"

// Sets default values
AGeometryPoint::AGeometryPoint()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;
	GeomtryItemType = EGeomtryItemType::EGeomtryPoint;

	NormalMatRef = TEXT("Material'/Game/Materials/Mat_PointNormal.Mat_PointNormal'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_PointSelected.Mat_PointSelected'");


	USceneComponent* RootScene = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
	RootComponent = RootScene;

	BodyMeshComp = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("BodyMesh"));
	UStaticMesh* BodyMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Script/Engine.StaticMesh'/Game/Mesh/GeoPoint.GeoPoint'"));
	if (BodyMesh)
	{
		BodyMeshComp->SetStaticMesh(BodyMesh);
		BodyMeshComp->SetMaterial(0, this->GetNormalMaterial());
	}
	BodyMeshComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BodyMeshComp->SetWorldScale3D(FVector(1.f));
	BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	this->SetActorEnableCollision(true);
}

// Called when the game starts or when spawned
void AGeometryPoint::BeginPlay()
{
	Super::BeginPlay();
}

void AGeometryPoint::setPointData(TSharedPtr<FGeometryPointData> inData)
{
	pointData = inData;
}

FVector AGeometryPoint::GetPointLocation() const
{
	if (EPositionType::EAbsolute == PositionType)
	{
		return PointLocation;
	}
	else if (EPositionType::ERelative == PositionType)
	{
		return PointLocation + PrePointLocation;
	}
	checkNoEntry();
	return FVector::ZeroVector;
}

void AGeometryPoint::DeleteGeomtryItem()
{
	Super::DeleteGeomtryItem();
	BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

bool AGeometryPoint::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryPointProperty::GetName());
}

void AGeometryPoint::BringBack(void* InData)
{
	FGeomtryPointProperty* PropertyData = static_cast<FGeomtryPointProperty*>(InData);
	if (nullptr != PropertyData)
	{
		Super::BringBack(InData);
		BodyMeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetPointProperty(*PropertyData);
		this->Refresh();
	}
}

void AGeometryPoint::SetPointProperty(const FGeomtryPointProperty& InProperty)
{
	this->PositionType = InProperty.PositionType;
	this->PlanBelongs = InProperty.PlanBelongs;
	this->LocationX = InProperty.LocationX;
	this->LocationY = InProperty.LocationY;
	this->LocationZ = InProperty.LocationZ;
	this->PointLocation = InProperty.PointLocation();
	this->PrePointLocation = InProperty.PrePointLocation;
	//FGeometryDatas::FormatLocation(this->PlanBelongs, this->PointLocation);
	//FGeometryDatas::FormatLocation(this->PlanBelongs, this->PrePointLocation);
}

AGeometryPoint* AGeometryPoint::Create(UWorld*& InWorld, const FGeomtryPointProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewPoint = InWorld->SpawnActor(AGeometryPoint::StaticClass(), &tran);
		AGeometryPoint* NewGeomtryPoint = Cast<AGeometryPoint>(NewPoint);
		NewGeomtryPoint->SetPointProperty(InProperty);
		NewGeomtryPoint->Refresh();
		return NewGeomtryPoint;
	}
	return nullptr;
}

void AGeometryPoint::Refresh()
{
	FVector NewLocation = this->PointLocation;
	if (EPositionType::ERelative == PositionType)
	{
		NewLocation += this->PrePointLocation;
	}
	FRotator NewRotation = FRotator::ZeroRotator;
	if (EPlanPolygonBelongs::EXY_Plan == PlanBelongs)
	{
		NewLocation.Z = POINT_ACTOR_HEIGHT;
	}
	else if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs)
	{
		NewLocation.X = POINT_ACTOR_HEIGHT;
		NewRotation = FRotator(-90.0f, 0.0f, 0.0f);
	}
	else if (EPlanPolygonBelongs::EXZ_Plan == PlanBelongs)
	{
		NewLocation.Y = POINT_ACTOR_HEIGHT;
		NewRotation = FRotator(0.0f, 0.0f, 90.0f);
	}
	this->SetActorLocation(NewLocation);
	this->SetActorRotation(NewRotation);
	UE_LOG(LogTemp, Log, TEXT("------------  Point location is: %s  -----------"), *NewLocation.ToString());
}

FText AGeometryPoint::GetItemTypeName() const
{
	return NSLOCTEXT(LOCTEXT_NAMESPACE, "GeometryPointKey", "Point");
}


bool AGeometryPoint::CollectPoints(TArray<FVector>& OutPoints)
{
	FVector VerticeLocation = PointLocation;
	if (EPositionType::ERelative == PositionType)
	{
		VerticeLocation = PointLocation + PrePointLocation;
	}
	OutPoints.Add(VerticeLocation);
	return true;
}

bool AGeometryPoint::ConstructSaveData(void* InOutDataStruct)
{
	FGeomtryPointProperty* DataStruct = static_cast<FGeomtryPointProperty*>(InOutDataStruct);
	if (nullptr != DataStruct)
	{
		DataStruct->PlanBelongs = this->PlanBelongs;
		DataStruct->LocationX = this->LocationX;
		DataStruct->LocationY = this->LocationY;
		DataStruct->LocationZ = this->LocationZ;
		DataStruct->PositionType = this->PositionType;
		DataStruct->PrePointLocation = this->PrePointLocation;
		return true;
	}
	return false;
}

void AGeometryPoint::OnMouseOver()
{
	BodyMeshComp->SetMaterial(0, this->GetSelectedMaterial());
}

void AGeometryPoint::OnMouseLeave()
{
	if (IsSelected)
	{
		BodyMeshComp->SetMaterial(0, this->GetSelectedMaterial());
	}
	else
	{
		BodyMeshComp->SetMaterial(0, this->GetNormalMaterial());
	}
}

void AGeometryPoint::OnSlectionChanged(const bool& InNewSelection)
{
	if (InNewSelection != IsSelected)
	{
		IsSelected = InNewSelection;
		BodyMeshComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
	}
}

void AGeometryPoint::ScaleGeomtryItem(const float& InScale)
{
	BodyMeshComp->SetRelativeScale3D(FVector(InScale));
}

bool AGeometryPoint::ChangeProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryPointProperty* PointProperty = static_cast<FGeomtryPointProperty*>(InOutDataStruct);
	if (PointProperty)
	{
		if (0 == InType/* && EPlanPolygonBelongs::EYZ_Plan != this->PlanBelongs*/)
		{//Position X
			this->LocationX = PointProperty->LocationX;
			FVector NewLocation = PointProperty->PointLocation();
			//FGeometryDatas::FormatLocation(this->PlanBelongs, NewLocation);
			this->PointLocation = NewLocation;
		}
		else if (1 == InType/* && EPlanPolygonBelongs::EXZ_Plan != this->PlanBelongs*/)
		{//Position Y
			this->LocationY = PointProperty->LocationY;
			FVector NewLocation = PointProperty->PointLocation();
			//FGeometryDatas::FormatLocation(this->PlanBelongs, NewLocation);
			this->PointLocation = NewLocation;
		}
		else if (2 == InType/* && EPlanPolygonBelongs::EXY_Plan != this->PlanBelongs*/)
		{//Position Z
			this->LocationZ = PointProperty->LocationZ;
			FVector NewLocation = PointProperty->PointLocation();
			//FGeometryDatas::FormatLocation(this->PlanBelongs, NewLocation);
			this->PointLocation = NewLocation;
		}
		else if (3 == InType)
		{//Relative
			this->PositionType = PointProperty->PositionType;
		}
		this->Refresh();
		return true;
	}
	return false;
}

#undef LOCTEXT_NAMESPACE 