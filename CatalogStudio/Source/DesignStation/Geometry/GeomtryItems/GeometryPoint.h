// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "GeometryPoint.generated.h"



UCLASS()
class DESIGNSTATION_API AGeometryPoint : public AGeometryItemBase
{
	GENERATED_BODY()

protected:
	EPositionType PositionType;
	EPlanPolygonBelongs PlanBelongs;
	FVector PointLocation;
	FExpressionValuePair LocationX;
	FExpressionValuePair LocationY;
	FExpressionValuePair LocationZ;
	FVector PrePointLocation;

	TSharedPtr<FGeometryPointData> pointData;

	UPROPERTY()
		UStaticMeshComponent* BodyMeshComp;

public:
	// Sets default values for this actor's properties
	AGeometryPoint();

	GenerateSet(EPositionType, PositionType)
		GenerateSet(EPlanPolygonBelongs, PlanBelongs)
		GenerateSet(FVector, PointLocation)
		GenerateSet(FVector, PrePointLocation)

		void SetPointProperty(const FGeomtryPointProperty& InProperty);

	void setPointData(TSharedPtr<FGeometryPointData> inData);
	TSharedPtr<FGeometryPointData>getPointData() { return pointData; }
	FVector GetPointLocation() const;

	virtual FText GetItemTypeName() const override;

	virtual void DeleteGeomtryItem() override;

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) override;

	virtual void BringBack(void* InData) override;

	virtual void Refresh() override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	virtual bool CollectPoints(TArray<FVector>& OutPoints) override;

	virtual bool ConstructSaveData(void* InOutDataStruct) override;

	virtual void ScaleGeomtryItem(const float& InScale) override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	static AGeometryPoint* Create(UWorld*& InWorld, const FGeomtryPointProperty& InProperty);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
};



