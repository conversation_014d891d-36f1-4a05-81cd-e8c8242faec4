// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryRectanglePlan.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "GeometryEdit/Public/Polygon3DLibrary.h"
#include "GeometryEdit/Public/Geometry3DLibrary.h"

#define LOCTEXT_NAMESPACE "GeomtryRectangle"

// Sets default values
AGeometryRectanglePlan::AGeometryRectanglePlan()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;
	GeomtryItemType = EGeomtryItemType::EGeometryRectangle;

	NormalMatRef = TEXT("Material'/Game/Materials/WhiteM.WhiteM'");
	SelectedMatRef = TEXT("Material'/Game/Materials/Mat_LineSelected.Mat_LineSelected'");

	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));

	RectangleBodyComp = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BodyMesh"));
	RectangleBodyComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	RectangleBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	this->SetActorEnableCollision(true);
}

// Called when the game starts or when spawned
void AGeometryRectanglePlan::BeginPlay()
{
	Super::BeginPlay();

}

void AGeometryRectanglePlan::SetRetangleProperty(const FGeomtryRectanglePlanProperty& InRectangleProperty)
{
	this->PlanBelongs = InRectangleProperty.PlanBelongs;
	this->StartLocationX = InRectangleProperty.StartLocationX;
	this->StartLocationY = InRectangleProperty.StartLocationY;
	this->StartLocationZ = InRectangleProperty.StartLocationZ;
	this->StartLocation = InRectangleProperty.StartLocation();
	this->EndLocationX = InRectangleProperty.EndLocationX;
	this->EndLocationY = InRectangleProperty.EndLocationY;
	this->EndLocationZ = InRectangleProperty.EndLocationZ;
	this->EndLocation = InRectangleProperty.EndLocation();
}

FText AGeometryRectanglePlan::GetItemTypeName() const
{
	return NSLOCTEXT(LOCTEXT_NAMESPACE, "RetangleKey", "Retangle");
}

bool AGeometryRectanglePlan::ChangeProperty(const int32& InType, void* InOutDataStruct)
{
	FGeomtryRectanglePlanProperty* RectangleProperty = static_cast<FGeomtryRectanglePlanProperty*>(InOutDataStruct);
	if (RectangleProperty)
	{
		if (0 == InType/* && EPlanPolygonBelongs::EYZ_Plan != this->PlanBelongs*/)
		{
			this->StartLocationX = RectangleProperty->StartLocationX;
			this->StartLocation = RectangleProperty->StartLocation();
		}
		else if (1 == InType/* && EPlanPolygonBelongs::EXZ_Plan != this->PlanBelongs*/)
		{
			this->StartLocationY = RectangleProperty->StartLocationY;
			this->StartLocation = RectangleProperty->StartLocation();
		}
		else if (2 == InType/* && EPlanPolygonBelongs::EXY_Plan != this->PlanBelongs*/)
		{
			this->StartLocationZ = RectangleProperty->StartLocationZ;
			this->StartLocation = RectangleProperty->StartLocation();
		}
		else if (3 == InType/* && EPlanPolygonBelongs::EYZ_Plan != this->PlanBelongs*/)
		{
			this->EndLocationX = RectangleProperty->EndLocationX;
			this->EndLocation = RectangleProperty->EndLocation();
		}
		else if (4 == InType/* && EPlanPolygonBelongs::EXZ_Plan != this->PlanBelongs*/)
		{
			this->EndLocationY = RectangleProperty->EndLocationY;
			this->EndLocation = RectangleProperty->EndLocation();
		}
		else if (5 == InType/* && EPlanPolygonBelongs::EXY_Plan != this->PlanBelongs*/)
		{
			this->EndLocationZ = RectangleProperty->EndLocationZ;
			this->EndLocation = RectangleProperty->EndLocation();
		}
		this->Refresh();
		return true;
	}
	return false;
}

void AGeometryRectanglePlan::Refresh()
{
	RectangleBodyComp->ClearAllMeshSections();
	TArray<FVector> RectPoints;
	FVector PlanNormal = NSPlanType::GetPlanNormal(PlanBelongs);
	//if (EPlanPolygonBelongs::EYZ_Plan == PlanBelongs) PlanNormal *= -1.0f;
	const auto PlanTangentX = NSPlanType::GetPlanTangentX(PlanBelongs);
	bool Res = FGeometry3DLibrary::GenerateRectanglePlanPoints(StartLocation, EndLocation, PlanNormal, PlanTangentX, RectPoints);
	if (Res)
	{
		FPMCSection MeshInfo;
		FPolygon3DLibrary::GenerateMeshFromPolygon2D(RectPoints, PlanNormal, PlanTangentX, StartLocation, 100.0f, MeshInfo);
		TArray<FLinearColor> Color;
		RectangleBodyComp->CreateMeshSection_LinearColor(0, MeshInfo.Vertexes, MeshInfo.Triangles, MeshInfo.Normals, MeshInfo.UV, Color, MeshInfo.Tangents, true);
		RectangleBodyComp->SetMaterial(0, this->GetNormalMaterial());
	}
	
}

bool AGeometryRectanglePlan::CollectPoints(TArray<FVector>& OutPoints)
{
	FPMCSection MeshInfo;
	if (UGeometryRelativeLibrary::GenerateRectangleMesh(this->PlanBelongs, this->StartLocation, this->EndLocation, MeshInfo))
	{
		OutPoints.Append(MeshInfo.Vertexes);
		return true;
	}
	return false;
}

bool AGeometryRectanglePlan::ConstructSaveData(void* InOutDataStruct)
{
	FGeomtryRectanglePlanProperty* DataStruct = static_cast<FGeomtryRectanglePlanProperty*>(InOutDataStruct);
	if (nullptr != DataStruct)
	{
		DataStruct->PlanBelongs = this->PlanBelongs;
		DataStruct->StartLocationX = this->StartLocationX;
		DataStruct->StartLocationY = this->StartLocationY;
		DataStruct->StartLocationZ = this->StartLocationZ;
		DataStruct->EndLocationX = this->EndLocationX;
		DataStruct->EndLocationY = this->EndLocationY;
		DataStruct->EndLocationZ = this->EndLocationZ;
		return true;
	}
	return false;
}

void AGeometryRectanglePlan::OnMouseOver()
{
	RectangleBodyComp->SetMaterial(0, this->GetSelectedMaterial());
}

void AGeometryRectanglePlan::OnMouseLeave()
{
	RectangleBodyComp->SetMaterial(0, this->GetNormalMaterial());
}

void AGeometryRectanglePlan::OnSlectionChanged(const bool& InNewSelection)
{
	if (InNewSelection != IsSelected)
	{
		IsSelected = InNewSelection;
		RectangleBodyComp->SetMaterial(0, IsSelected ? this->GetSelectedMaterial() : this->GetNormalMaterial());
	}
}

void AGeometryRectanglePlan::DeleteGeomtryItem()
{
	AGeometryItemBase::DeleteGeomtryItem();
	RectangleBodyComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	this->SetActorEnableCollision(false);
}

void AGeometryRectanglePlan::BringBack(void* InData)
{
	FGeomtryRectanglePlanProperty* PropertyData = static_cast<FGeomtryRectanglePlanProperty*>(InData);
	if (nullptr != PropertyData)
	{
		AGeometryItemBase::BringBack(InData);
		RectangleBodyComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
		this->SetActorEnableCollision(true);
		this->SetRetangleProperty(*PropertyData);
		this->Refresh();
	}
}

bool AGeometryRectanglePlan::IsCorrectPropertyData(const FName& InPropertyDataName)
{
	return InPropertyDataName.IsEqual(FGeomtryRectanglePlanProperty::GetName());
}

AGeometryRectanglePlan* AGeometryRectanglePlan::Create(UWorld*& InWorld, const FGeomtryRectanglePlanProperty& InProperty)
{
	if (InWorld)
	{
		FTransform tran(FRotator::ZeroRotator, FVector::ZeroVector);
		AActor* NewRectangle = InWorld->SpawnActor(AGeometryRectanglePlan::StaticClass(), &tran);
		AGeometryRectanglePlan* NewGeomtryRectangle = Cast<AGeometryRectanglePlan>(NewRectangle);
		NewGeomtryRectangle->SetRetangleProperty(InProperty);
		NewGeomtryRectangle->Refresh();
		return NewGeomtryRectangle;
	}
	return nullptr;
}

#undef LOCTEXT_NAMESPACE
