// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GeometryItemBase.h"
#include "ProceduralMeshComponent.h"
#include "GeometryRectanglePlan.generated.h"

UCLASS()
class DESIGNSTATION_API AGeometryRectanglePlan : public AGeometryItemBase
{
	GENERATED_BODY()

protected:
	EPlanPolygonBelongs PlanBelongs;
	FVector StartLocation;
	FExpressionValuePair StartLocationX;
	FExpressionValuePair StartLocationY;
	FExpressionValuePair StartLocationZ;

	FExpressionValuePair EndLocationX;
	FExpressionValuePair EndLocationY;
	FExpressionValuePair EndLocationZ;
	FVector EndLocation;

	UPROPERTY()
		UProceduralMeshComponent* RectangleBodyComp;

public:
	// Sets default values for this actor's properties
	AGeometryRectanglePlan();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:

	void SetRetangleProperty(const FGeomtryRectanglePlanProperty& InRectangleProperty);

	virtual FText GetItemTypeName() const override;

	virtual bool ChangeProperty(const int32& InType, void* InOutDataStruct) override;

	virtual void Refresh() override;

	virtual bool CollectPoints(TArray<FVector>& OutPoints) override;

	virtual bool ConstructSaveData(void* InOutDataStruct) override;

	virtual void OnMouseOver() override;
	virtual void OnMouseLeave() override;
	virtual void OnSlectionChanged(const bool& InNewSelection) override;

	virtual void DeleteGeomtryItem() override;

	virtual void BringBack(void* InData) override;

	virtual bool IsCorrectPropertyData(const FName& InPropertyDataName) override;

	static AGeometryRectanglePlan* Create(UWorld*& InWorld, const FGeomtryRectanglePlanProperty& InProperty);

};
