// Fill out your copyright notice in the Description page of Project Settings.

#include "ShowMultiComponentActor.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SubSystem/ResourceSubsystem.h"


// Sets default values
AShowMultiComponentActor::AShowMultiComponentActor()
	:DefaultMaterial(nullptr)
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;
	//RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootScene"));
}

void AShowMultiComponentActor::RefreshComponentMesh(const TArray<struct FShowMultiComponentActorProperty>& InMeshInfos)
{
	UE_LOG(LogTemp, Warning, TEXT("AShowMultiComponentActor::RefreshComponentMesh"));
	auto MeshInfos = InMeshInfos;
	{//清空所有子元素
		for (auto& Iter : DisplayInfo)
			Iter.Clear();
		DisplayInfo.Empty();
		DisplayInfo.Init(FMultiComponentDisplayerUnit(), MeshInfos.Num());
	}
	if (ChildRoot.Num() < MeshInfos.Num())
	{
		const int32 DeltaChild = MeshInfos.Num() - ChildRoot.Num();
		int32 Offset = ChildRoot.AddDefaulted(DeltaChild);
		for (int32 i = 0; i < DeltaChild; ++i, ++Offset)
		{
			FString MeshName = FString::Printf(TEXT("Mesh_%d"), Offset);
			ChildRoot[Offset] = NewObject<USceneComponent>(this, *MeshName, RF_NoFlags, nullptr, false, nullptr);
			ChildRoot[Offset]->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
			ChildRoot[Offset]->RegisterComponent();
		}
	}
	for (int32 i = 0; i < MeshInfos.Num(); ++i)
	{
		ChildRoot[i]->SetRelativeTransform(MeshInfos[i].MeshTransform, true, nullptr, ETeleportType::ResetPhysics);
		MeshInfos[i].MeshTransform = FTransform(FVector::ZeroVector);
		this->RefreshComponentMesh(MeshInfos[i], DisplayInfo[i], FTransform(FVector::ZeroVector), ChildRoot[i]);
	}
	this->ShowOutline();
}

void AShowMultiComponentActor::RefreshComponentMesh(const FShowMultiComponentActorProperty& InMeshInfos, FMultiComponentDisplayerUnit& OutMeshPair, const FTransform& ParentTrans, USceneComponent* InRootScene)
{
	const FTransform ThisNodeLocalTrans = InMeshInfos.MeshTransform * ParentTrans;
	if (InMeshInfos.SingleComponentDatas.Num() > 0)
	{
		const int32 SingleCompCount = InMeshInfos.SingleComponentDatas.Num();
		int32 SingleCompOffset = OutMeshPair.SingleComponents.AddDefaulted(SingleCompCount);
		for (int32 SingleIndex = 0; SingleIndex < SingleCompCount; ++SingleIndex)
		{
			const FTransform LocalTrans = InMeshInfos.SingleComponentDatas[SingleIndex].MeshTransform * ThisNodeLocalTrans;
			const int32 SingleCompIndex = SingleCompOffset + SingleIndex;
			{//Show outline
				const FString OutlineName = FString::Printf(TEXT("%s"), *FGuid::NewGuid().ToString());
				OutMeshPair.SingleComponents[SingleCompIndex].Outline = NewObject<UOutlineComponent>(this, *OutlineName, RF_NoFlags, nullptr, false, nullptr);
				OutMeshPair.SingleComponents[SingleCompIndex].Outline->AttachToComponent(InRootScene, FAttachmentTransformRules::KeepRelativeTransform);
				OutMeshPair.SingleComponents[SingleCompIndex].Outline->RegisterComponent();
				OutMeshPair.SingleComponents[SingleCompIndex].Outline->SetRelativeTransform(LocalTrans, true, nullptr, ETeleportType::ResetPhysics);
				OutMeshPair.SingleComponents[SingleCompIndex].Outline->SetVisibility(InMeshInfos.Visibility);
				for (auto& OutlinePair : InMeshInfos.SingleComponentDatas[SingleIndex].OutlinePairs)
				{
					OutMeshPair.SingleComponents[SingleCompIndex].Outline->AddOutlines(FOutlineLineSegments(OutlinePair.Key, OutlinePair.Value));
				}
				//OutMeshPair.SingleComponents[SingleCompIndex].Outline->SetOutlineSize(1);
			}
			if (InMeshInfos.SingleComponentDatas[SingleIndex].MeshInfo.Num() > 0)
			{ 
				auto& MeshInfo = InMeshInfos.SingleComponentDatas[SingleIndex].MeshInfo;
				auto& MeshSections = OutMeshPair.SingleComponents[SingleCompIndex].MeshSections;

				const int32 SectionCount = MeshInfo.Num();
				MeshSections.AddDefaulted(SectionCount);
				for (int32 SectionIndex = 0; SectionIndex < SectionCount; ++SectionIndex)
				{
					MeshSections[SectionIndex].CompSource = MeshInfo[SectionIndex].CompSource;
					const FTransform SectionLocalTrans = MeshInfo[SectionIndex].MeshTransform * LocalTrans;
					if (ESingleComponentSource::EImportPAK != MeshInfo[SectionIndex].CompSource)
					{
						const FString MeshName = FString::Printf(TEXT("%s"), *FGuid::NewGuid().ToString());
						MeshSections[SectionIndex].MeshComp = NewObject<UVolatileMeshComponent>(this, *MeshName, RF_NoFlags, nullptr, false, nullptr);
						UVolatileMeshComponent* NewMeshComp = MeshSections[SectionIndex].MeshComp;
						NewMeshComp->AttachToComponent(InRootScene, FAttachmentTransformRules::KeepRelativeTransform);
						NewMeshComp->bRenderCustomDepth = true;
						NewMeshComp->CustomDepthStencilValue = 0;
						NewMeshComp->RegisterComponent();
						NewMeshComp->SetRelativeTransform(SectionLocalTrans, true, nullptr, ETeleportType::ResetPhysics);
						NewMeshComp->SetVisibility(InMeshInfos.Visibility);
						NewMeshComp->SetCollisionEnabled(ECollisionEnabled::Type::QueryOnly);
						NewMeshComp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Block);
						for (int32 j = 0; j < MeshInfo[SectionIndex].SingleMeshInfo.Num(); ++j)
						{
							MeshSections[SectionIndex].MeshComp->CreateMeshSection(j, MeshInfo[SectionIndex].SingleMeshInfo[j].MeshInfo, true);
							/*UMaterialInstanceDynamic* MaterialIns = GetMaterial(MeshInfo[SectionIndex].SingleMeshInfo[j].MaterialFolderID);
							if (!IS_OBJECT_PTR_VALID(MaterialIns)) MaterialIns = DefaultMaterial;*/

							UMaterialInstanceDynamic* MaterialIns = UResourceSubsystem::GetInstance()->GetMaterialInsDynamic_Web(
								MeshInfo[SectionIndex].SingleMeshInfo[j].MaterialFolderID
							);

							if (MaterialIns != nullptr)
							{
								MaterialIns->SetScalarParameterValue(FName(TEXT("Normal")), 0.0f);
								MeshSections[SectionIndex].MeshComp->SetMaterialWithName(j, MeshInfo[SectionIndex].SingleMeshInfo[j].MeshInfo.SectionName, MaterialIns);
							}
						}
					}
					else
					{
						for (auto& SingleIter : MeshInfo[SectionIndex].SingleMeshInfo)
						{
							if (!SingleIter.PakRefPath.IsEmpty())
							{
								FString CleanValue = SingleIter.PakCode;
								if (!CleanValue.IsEmpty())
									CleanValue = CleanValue.Mid(1, CleanValue.Len() - 2);
								AImportPakBaseClass* NewActor = FCatalogFunctionLibrary::LoadActorFormPak<AImportPakBaseClass, AImportPakBaseClass>(this->GetWorld(), SingleIter.PakRefPath);
								if (nullptr != NewActor)
								{
									NewActor->AttachToComponent(InRootScene, FAttachmentTransformRules::KeepRelativeTransform);
									NewActor->SetActorRelativeTransform(SectionLocalTrans);
									NewActor->FurnitureCodeChanged(CleanValue);
									NewActor->SetActorHiddenInGame(false == InMeshInfos.Visibility);
									NewActor->SetActorEnableCollision(true);
									NewActor->ForEachComponent<UStaticMeshComponent>(true, [](UStaticMeshComponent* Comp) {
										
										Comp->SetCollisionEnabled(ECollisionEnabled::Type::QueryOnly);
										Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Block);
										});

									//MeshSections[SectionIndex].MeshComp->bRenderCustomDepth = true;
									//MeshSections[SectionIndex].MeshComp->CustomDepthStencilValue = 3;
								}
								MeshSections[SectionIndex].MeshActor = NewActor;
								UE_LOG(LogTemp, Log, TEXT("nullptr == NewActor is %d MeshActorCode is %s "), nullptr == NewActor, *CleanValue);
							}
						}
					}

				}
			}
		}
	}
	for (auto& ChildIter : InMeshInfos.MultiComponentDatas)
	{
		RefreshComponentMesh(ChildIter, OutMeshPair, ThisNodeLocalTrans, InRootScene);
	}
}

void AShowMultiComponentActor::FolderCodeChanged(const FString& NewCode)
{
	for (auto& Iter : DisplayInfo)
	{
		for (auto& SingleIter : Iter.SingleComponents)
		{
			for (auto& SetionIter : SingleIter.MeshSections)
			{
				if (ESingleComponentSource::EImportPAK == SetionIter.CompSource && IS_OBJECT_PTR_VALID(SetionIter.MeshActor) && IsValid(SetionIter.MeshActor))
				{
					SetionIter.MeshActor->FurnitureCodeChanged(NewCode);
				}
			}
		}
	}
}

void AShowMultiComponentActor::DestroyDisplayer()
{
	for (auto& CompIter : DisplayInfo)
	{
		CompIter.Clear();
	}
	DisplayInfo.Empty();
	AShowMeshBaseActor::DestroyDisplayer();
}

TArray<FMultiComponentDisplayerUnit> AShowMultiComponentActor::GetDisplayerUnit()
{
	return DisplayInfo;
}

int32 AShowMultiComponentActor::GetChildRootIndex(USceneComponent* InSceneComponent)
{
	return  ChildRoot.IndexOfByPredicate([InSceneComponent](USceneComponent* Child)
		{
			return Child == InSceneComponent;
		});
}

// Called when the game starts or when spawned
void AShowMultiComponentActor::BeginPlay()
{
	AShowMeshBaseActor::BeginPlay();
	UMaterial* Mat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/Mat_White.Mat_White'"));
	DefaultMaterial = UMaterialInstanceDynamic::Create(Mat, nullptr);
}

void AShowMultiComponentActor::ClearAllMeshes()
{

}

void AShowMultiComponentActor::ShowOutline()
{
	ACatalogPlayerController::Get()->SetPostProcessType(static_cast<int32>(CurrentOultineType));
	for (auto& Iter : DisplayInfo)
	{
		for (auto& SingleIter : Iter.SingleComponents)
		{
			if (EOutlineType::EOutlineAndWhiteMaterial == CurrentOultineType)
			{
				UpdateMeshVisibilityMaterialAndFramwork(SingleIter, true, true, WhiteMaterial);
			}
			else if (EOutlineType::EOnlyOutline == CurrentOultineType)
			{
				UpdateMeshVisibilityMaterialAndFramwork(SingleIter, false, true, WhiteMaterial);
			}
			else if (EOutlineType::EOutlineAndMaterial == CurrentOultineType)
			{
				UpdateMeshVisibilityMaterialAndFramwork(SingleIter, true, true);
			}
			else if (EOutlineType::EOnlyMaterial == CurrentOultineType)
			{
				UpdateMeshVisibilityMaterialAndFramwork(SingleIter, true, false);
			}
		}
	}
}

void AShowMultiComponentActor::UpdateMeshVisibilityMaterialAndFramwork(FSingleComponentDisplayerUnit& InMeshComp, bool MeshVisibale, bool FramworkVisibale, UMaterial* InMaterial)
{
	for (auto& Iter : InMeshComp.MeshSections)
	{
		if (IS_OBJECT_PTR_VALID(Iter.MeshComp))
		{
			Iter.MeshComp->UpdateMaterialForTemporary(InMaterial);
			Iter.MeshComp->SetVisibility(MeshVisibale, false);
		}
		if (IS_OBJECT_PTR_VALID(InMeshComp.Outline))
		{
			InMeshComp.Outline->SetVisibility(FramworkVisibale, true);
		}
	}
}

void AShowMultiComponentActor::SetCompLocation(const int32& InCompIndex, const FVector& InLocation)
{
	if (DisplayInfo.IsValidIndex(InCompIndex) && ChildRoot.IsValidIndex(InCompIndex) && IS_OBJECT_PTR_VALID(ChildRoot[InCompIndex]))
	{
		ChildRoot[InCompIndex]->SetRelativeLocation(InLocation);
	}
}

void AShowMultiComponentActor::SetCompRotation(const int32& InCompIndex, const FRotator& InRotation)
{
	if (DisplayInfo.IsValidIndex(InCompIndex) && ChildRoot.IsValidIndex(InCompIndex) && IS_OBJECT_PTR_VALID(ChildRoot[InCompIndex]))
	{
		ChildRoot[InCompIndex]->SetRelativeRotation(InRotation);
	}
}

void AShowMultiComponentActor::SetCompScale(const int32& InCompIndex, const FVector& InScale)
{
	if (DisplayInfo.IsValidIndex(InCompIndex) && ChildRoot.IsValidIndex(InCompIndex) && IS_OBJECT_PTR_VALID(ChildRoot[InCompIndex]))
	{
		ChildRoot[InCompIndex]->SetRelativeScale3D(InScale);
	}
}

void AShowMultiComponentActor::SetCompVisibility(const int32& InCompIndex, const bool& InVisibility)
{
	if (DisplayInfo.IsValidIndex(InCompIndex) && ChildRoot.IsValidIndex(InCompIndex) && IS_OBJECT_PTR_VALID(ChildRoot[InCompIndex]))
	{
		ChildRoot[InCompIndex]->SetVisibility(InVisibility, true);
	}
}

void AShowMultiComponentActor::UpdateSelection(const int32& InCompIndex, bool InIsSelected)
{
	if (DisplayInfo.IsValidIndex(InCompIndex))
	{
		int32 BoxMaxIndex = -1;
		double MaxV = -1.f;
		for (int32 i = 0; i < DisplayInfo.Num(); i++)
		{
			double TempMax = 0.0f;
			for (auto& MeshIter : DisplayInfo[InCompIndex].SingleComponents)
			{

				auto CurBox = MeshIter.GetBoundBox();
				auto CurV = CurBox.GetVolume();
				TempMax += CurV;
			}

			if (TempMax > MaxV)
			{
				BoxMaxIndex = i;
				MaxV = TempMax;
			}
		}


		for (int32 i = 0; i < DisplayInfo.Num(); ++i)
		{
			for (auto& MeshIter : DisplayInfo[i].SingleComponents)
			{
				if (i == InCompIndex)
				{
					continue;
				}
				for (auto& ChildMesh : MeshIter.MeshSections)
				{
					if (ESingleComponentSource::EImportPAK == ChildMesh.CompSource)
					{
						if (IS_OBJECT_PTR_VALID(ChildMesh.MeshActor))
						{
							ChildMesh.MeshActor->UpdateSelectionState(false, EFurnitureState::ENormal, EFurnitureSelectionState::ENone);
							float StencilValue = BoxMaxIndex == InCompIndex ? 255.0f : 0.0f;
							if (ChildMesh.MeshComp)
							{
								ChildMesh.MeshComp->CustomDepthStencilValue = StencilValue;
							}
						}
					}
					else if (IS_OBJECT_PTR_VALID(ChildMesh.MeshComp))
					{
						bool bCanRender = false;
						float StencilValue = BoxMaxIndex == InCompIndex ? 255.0f : 0.0f;
						ERendererStencilMask BitMask = ERendererStencilMask::ERSM_Default;
						ChildMesh.MeshComp->SetRenderCustomDepth(true);
						//ChildMesh.MeshComp->CustomDepthStencilValue = 0;
						ChildMesh.MeshComp->SetCustomDepthStencilValue(StencilValue);
						ChildMesh.MeshComp->SetCustomDepthStencilWriteMask(BitMask);
						//ChildMesh.MeshComp->GetMaterial(0)->bUseOutline = InIsSelected;
						//ChildMesh.MeshComp->GetMaterial(0)->OutlineColor = FLinearColor::Blue;
						//ChildMesh.MeshComp->GetMaterial(0)->OutlineSize = 0.5f;



					}
				}
			}
		}
		for (auto& MeshIter : DisplayInfo[InCompIndex].SingleComponents)
		{
			
			for (auto& ChildMesh : MeshIter.MeshSections)
			{
				if (ESingleComponentSource::EImportPAK == ChildMesh.CompSource)
				{
					if (IS_OBJECT_PTR_VALID(ChildMesh.MeshActor))
					{
						ChildMesh.MeshActor->UpdateSelectionState(false, EFurnitureState::ENormal, InIsSelected ? EFurnitureSelectionState::ESeclect : EFurnitureSelectionState::ENone);
						//ChildMesh.MeshComp->CustomDepthStencilValue = 3;
						float StencilValue = InIsSelected ? 1 : 0.0f;
						if (ChildMesh.MeshComp)
						{
							ChildMesh.MeshComp->CustomDepthStencilValue = StencilValue;
						}
					}
				}
				else if (IS_OBJECT_PTR_VALID(ChildMesh.MeshComp))
				{
					bool bCanRender = InIsSelected ? true : false;
					float StencilValue = InIsSelected ? 1 :0.0f;
					ERendererStencilMask BitMask = ERendererStencilMask::ERSM_Default;
					ChildMesh.MeshComp->SetRenderCustomDepth(true);
					//ChildMesh.MeshComp->CustomDepthStencilValue = 0;
					ChildMesh.MeshComp->SetCustomDepthStencilValue(StencilValue);
					ChildMesh.MeshComp->SetCustomDepthStencilWriteMask(BitMask);
					//ChildMesh.MeshComp->GetMaterial(0)->bUseOutline = InIsSelected;
					//ChildMesh.MeshComp->GetMaterial(0)->OutlineColor = FLinearColor::Blue;
					//ChildMesh.MeshComp->GetMaterial(0)->OutlineSize = 0.5f;



				}
			}
		}
	}
}

void AShowMultiComponentActor::MoveSelection(const int32& InCompIndex, bool bIsUp)
{
	if (InCompIndex >= 0 && InCompIndex < ChildRoot.Num())
	{
		if (bIsUp && InCompIndex >= 0)
		{
			auto temp = ChildRoot[InCompIndex];
			ChildRoot[InCompIndex] = ChildRoot[InCompIndex + 1];
			ChildRoot[InCompIndex + 1] = temp;


			auto dis = DisplayInfo[InCompIndex];
			DisplayInfo[InCompIndex] = DisplayInfo[InCompIndex + 1];
			DisplayInfo[InCompIndex+1] = dis;


		}
		else if (!bIsUp && InCompIndex != 0)
		{
			auto temp = ChildRoot[InCompIndex];
			ChildRoot[InCompIndex] = ChildRoot[InCompIndex - 1];
			ChildRoot[InCompIndex - 1] = temp;

			auto dis = DisplayInfo[InCompIndex];
			DisplayInfo[InCompIndex] = DisplayInfo[InCompIndex - 1];
			DisplayInfo[InCompIndex-1] = dis;
		}
		for (int32 i = 0; i < ChildRoot.Num(); ++i)
		{
			UpdateSelection(i, i == InCompIndex);
		}
	}

}

void AShowMultiComponentActor::AddDefaultComp()
{
	const int32 Offset = ChildRoot.AddDefaulted();
	const FString MeshName = FString::Printf(TEXT("Mesh_%d"), Offset);
	ChildRoot[Offset] = NewObject<USceneComponent>(this, *MeshName, RF_NoFlags, nullptr, false, nullptr);
	ChildRoot[Offset]->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	ChildRoot[Offset]->RegisterComponent();
}

void AShowMultiComponentActor::SwapComp(const int32& Index1, const int32& Index2)
{
	if (ChildRoot.IsValidIndex(Index1) && ChildRoot.IsValidIndex(Index2))
	{
		ChildRoot.Swap(Index1, Index2);

		for (int32 i = 0; i < ChildRoot.Num(); ++i)
		{
			UpdateSelection(i, i == Index2);
		}
	}

}
