// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/BasicClasses/ShowMeshBaseActor.h"
#include "DesignStation/Geometry/DataDefines/MultiComponentDisplayerUnit.h"

#include "ShowMultiComponentActor.generated.h"


UCLASS()
class DESIGNSTATION_API AShowMultiComponentActor : public AShowMeshBaseActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AShowMultiComponentActor();

	void RefreshComponentMesh(const TArray<FShowMultiComponentActorProperty>& InMeshInfos);

	void SetCompLocation(const int32& InCompIndex, const FVector& InLocation);

	void SetCompRotation(const int32& InCompIndex, const FRotator& InRotation);

	void SetCompScale(const int32& InCompIndex, const FVector& InScale);

	void SetCompVisibility(const int32& InCompIndex, const bool& InVisibility);

	void UpdateSelection(const int32& InCompIndex, bool InIsSelected);

	void MoveSelection(const int32& InCompIndex, bool bIsUp);

	/*
	*  @@ logic compair to add
	*/
	void AddDefaultComp();

	void SwapComp(const int32& Index1, const int32& Index2);


	virtual void DestroyDisplayer() override;

	void FolderCodeChanged(const FString& NewCode);
	
	void GetDisplayerBound(FVector& Center, FVector& Extention) const
	{
		FBox Box(ForceInit);
		for(auto& DI : DisplayInfo)
		{
			Box += DI.GetBoundBox();
		}
		Box.GetCenterAndExtents(Center, Extention);
	}
	
	TArray<struct FMultiComponentDisplayerUnit> GetDisplayerUnit();

	UFUNCTION()
	virtual int32 GetChildRootIndex(USceneComponent* InSceneComponent);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	void ClearAllMeshes();

	void UpdateMeshVisibilityMaterialAndFramwork(FSingleComponentDisplayerUnit& InMeshComp, bool MeshVisibale, bool FramworkVisibale, UMaterial* InMaterial = nullptr);

	virtual void ShowOutline() override;

	void RefreshComponentMesh(const FShowMultiComponentActorProperty& InMeshInfos, FMultiComponentDisplayerUnit& OutMeshPair, const FTransform& ParentTrans, USceneComponent* InRootScene);

protected:

	UPROPERTY()
		UMaterialInstanceDynamic* DefaultMaterial;

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
		TArray<USceneComponent*> ChildRoot;

	UPROPERTY(BlueprintReadOnly,EditAnywhere)
		TArray<struct FMultiComponentDisplayerUnit> DisplayInfo;
};
