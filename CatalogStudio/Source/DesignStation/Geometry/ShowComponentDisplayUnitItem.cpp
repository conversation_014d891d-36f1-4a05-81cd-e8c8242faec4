// Fill out your copyright notice in the Description page of Project Settings.

#include "ShowComponentDisplayUnitItem.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"

void FShowComponentDisplayUnitItem::Clear()
{
	if (nullptr != MeshComponent && MeshComponent->IsValidLowLevel())
	{
		MeshComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		MeshComponent->DestroyComponent();
	}
	if (nullptr != ImportActor && ImportActor->IsValidLowLevel() && IsValid(ImportActor))
	{
		ImportActor->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		ImportActor->Destroy();
	}
	OriginalMaterials.Empty();
	MaterialID = INDEX_NONE;
	MeshSectionIndex = INDEX_NONE;
}

void FShowComponentDisplayUnitItem::Reset()
{
	if (nullptr != MeshComponent && MeshComponent->IsValidLowLevel())
	{
		MeshComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		MeshComponent->DestroyComponent(true);
	}
	if (nullptr != ImportActor && ImportActor->IsValidLowLevel() && IsValid(ImportActor))
	{
		ImportActor->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		ImportActor->Destroy();
	}
	OriginalMaterials.Empty();
	MaterialID = INDEX_NONE;
	MeshSectionIndex = INDEX_NONE;
}

void FShowComponentDisplayUnit::Clear()
{
	for (auto& Iter : ChildComponents)
		Iter.Clear();
	for (int32 i = 0; i < ChildMeshs.Num(); ++i)
		ChildMeshs[i].Clear();
	ChildMeshs.Empty();
	if (nullptr != SceneComponent && SceneComponent->IsValidLowLevel())
	{
		SceneComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		SceneComponent->DestroyComponent();
	}
}

void FShowComponentDisplayUnit::Reset()
{
	for (int32 i = 0; i < ChildMeshs.Num(); ++i)
	{
		ChildMeshs[i].Reset();
	}
	if (nullptr != SceneComponent && SceneComponent->IsValidLowLevel())
	{
		SceneComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		SceneComponent->DestroyComponent(true);
	}
	for (auto& Iter : ChildComponents)
		Iter.Clear();
}

void FShowComponentMeshUnit::Clear()
{
	for (auto& Iter : MeshComponents)
		Iter.Clear();
	MeshComponents.Empty();
	if (nullptr != SceneComponent && SceneComponent->IsValidLowLevel())
	{
		SceneComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		SceneComponent->DestroyComponent();
	}
	if (nullptr != Outline && Outline->IsValidLowLevel())
	{
		Outline->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		Outline->DestroyComponent();
	}
}

void FShowComponentMeshUnit::Reset()
{
	for (auto& Iter : MeshComponents)
		Iter.Reset();
	if (nullptr != SceneComponent && SceneComponent->IsValidLowLevel())
	{
		SceneComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		SceneComponent->DestroyComponent(true);
	}
	if (nullptr != Outline && Outline->IsValidLowLevel())
	{
		Outline->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		Outline->DestroyComponent(true);
	}
}

void FShowMultiComponentDisplayUnit::Clear()
{
	for (auto& Iter : MeshComponents)
	{
		Iter.Clear();
	}
	MeshComponents.Empty();
	for (auto& Iter : ChildComponents)
	{
		Iter.Clear();
	}
	ChildComponents.Empty();
}

void FShowMultiComponentDisplayUnit::Reset()
{
	for (auto& Iter : MeshComponents)
	{
		Iter.Reset();
	}
	for (auto& Iter : ChildComponents)
	{
		Iter.Reset();
	}
}
