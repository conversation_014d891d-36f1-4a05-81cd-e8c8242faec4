// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ProceduralMeshComponent.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "DesignStation/BasicClasses/ImportPakBaseClass.h"
#include "MagicCore/Public/PMCSection.h"
#include "GeometryEdit/Components/OutlineComponent.h"
#include "ShowComponentDisplayUnitItem.generated.h"

#define FRAMWORK_LINE_THICKNESS (0.3f)

struct FShowSingleComponentActorPropertyItem
{
	FPMCSection MeshInfo;
	FString		MaterialFolderID;
	FString		PakRefPath;//截面来源为PAK导入时用于记录资源的引用路径
	FString		PakCode;//截面当前的编码，导入的截面不同的编码会有不同的模型与材质
	FShowSingleComponentActorPropertyItem() :MeshInfo(FPMCSection()), MaterialFolderID(TEXT("")), PakRefPath(TEXT("")), PakCode(TEXT("")) {}
};

struct FShowSingleComponentActorWithTransformProperty
{
	TArray<FShowSingleComponentActorPropertyItem>	SingleMeshInfo;
	FTransform	MeshTransform;
	ESingleComponentSource	CompSource;
	FShowSingleComponentActorWithTransformProperty() :MeshTransform(FTransform()), CompSource(ESingleComponentSource::ECustom) {}
};

struct FShowSingleComponentActorProperty
{
	TArray<FShowSingleComponentActorWithTransformProperty>	MeshInfo;
	TArray<TPair<FVector, FVector>>							OutlinePairs;
	FTransform	MeshTransform;
	bool	Visibility;
	FShowSingleComponentActorProperty() {}
};

struct FShowMultiComponentActorProperty
{
	TArray<FShowSingleComponentActorProperty>	SingleComponentDatas;
	TArray<FShowMultiComponentActorProperty>	MultiComponentDatas;
	FTransform	MeshTransform;
	bool	Visibility;
	FShowMultiComponentActorProperty() {}
};

USTRUCT()
struct FShowComponentDisplayUnitItem
{
	GENERATED_BODY()
public:

	UPROPERTY()
		UProceduralMeshComponent* MeshComponent;

	int32 MaterialID;

	int32 MeshSectionIndex;

	ESingleComponentSource	CompSource;

	UPROPERTY()
		AImportPakBaseClass* ImportActor;

	UPROPERTY()
		TArray<UMaterialInstanceDynamic*> OriginalMaterials;

public:

	FShowComponentDisplayUnitItem() :MeshComponent(nullptr), ImportActor(nullptr) {}

	void Clear();

	void Reset();
};

struct FShowComponentMeshUnit
{
	TArray<struct FShowComponentDisplayUnitItem> MeshComponents;
	USceneComponent* SceneComponent;
	UOutlineComponent* Outline;

public:

	FShowComponentMeshUnit() :SceneComponent(nullptr), Outline(nullptr) {}

	void Clear();

	void Reset();
};

struct FShowComponentDisplayUnit
{

public:

	USceneComponent* SceneComponent;

	TArray<struct FShowComponentMeshUnit> ChildMeshs;

	TArray<struct FShowComponentDisplayUnit> ChildComponents;

public:

	FShowComponentDisplayUnit() :SceneComponent(nullptr) {}

	void Clear();

	void Reset();
};

struct FShowMultiComponentDisplayUnit
{

public:

	TArray<struct FShowComponentDisplayUnit> MeshComponents;

	TArray<struct FShowMultiComponentDisplayUnit> ChildComponents;

public:

	FShowMultiComponentDisplayUnit() {}

	void Clear();

	void Reset();
};

enum class EOutlineType : uint8
{
	EOutlineAndWhiteMaterial = 0,
	EOnlyOutline,
	EOutlineAndMaterial,
	EOnlyMaterial
};


struct FSingleComponentSections
{
	FPMCSection MeshInfo;
	int32 MaterialFolderID;
	FString		PakRefPath;//截面来源为PAK导入时用于记录资源的引用路径
	FString		PakCode;//截面当前的编码，导入的截面不同的编码会有不同的模型与材质
	FSingleComponentSections()
		:MeshInfo(FPMCSection())
		, MaterialFolderID(0)
		, PakRefPath(TEXT(""))
		, PakCode(TEXT(""))
	{}
};

struct FSingleComponent
{
	TArray<FSingleComponentSections>	Sections;
	FTransform							SectionTrans;
	ESingleComponentSource				CompSource;
	TArray<TPair<FVector, FVector>>		OutlinePairs;
	FSingleComponent()
		:Sections(TArray<FSingleComponentSections>())
		, SectionTrans(FTransform(FVector::ZeroVector))
		, CompSource(ESingleComponentSource::ECustom)
		, OutlinePairs(TArray<TPair<FVector, FVector>>())
	{}
};
