// Fill out your copyright notice in the Description page of Project Settings.

#include "ShowSingleComponentActor.h"
#include "DataCenter/Libraries/GeometryRelativeLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SubSystem/ResourceSubsystem.h"


// Sets default values
AShowSingleComponentActor::AShowSingleComponentActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	//MeshComponent = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("Mesh"));
	//MeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);

}

void AShowSingleComponentActor::RefreshComponentMesh(const FShowSingleComponentActorProperty& InMeshInfos)
{
	UMaterialInstanceDynamic* DefaultMaterial = nullptr;
	{
#ifdef USE_REF_LOCAL_FILE

		DefaultMaterial = UResourceSubsystem::GetInstance()->GetDefaultMaterialInsDynamic();

#else
		UMaterial* Mat = LoadObject<UMaterial>(nullptr, TEXT("Material'/Game/Materials/Mat_White.Mat_White'"));
		DefaultMaterial = UMaterialInstanceDynamic::Create(Mat, nullptr);
#endif
	}
	int32 Index = 0;
	if (InMeshInfos.MeshInfo.Num() > MeshComponents.MeshSections.Num())
		MeshComponents.MeshSections.AddZeroed(InMeshInfos.MeshInfo.Num() - MeshComponents.MeshSections.Num());
	TArray<FColor> Color;
	for (auto& Iter : InMeshInfos.MeshInfo)
	{
		{//删除已经生成的actor
			if (IS_OBJECT_PTR_VALID(MeshComponents.MeshSections[Index].MeshActor) && IsValid(MeshComponents.MeshSections[Index].MeshActor))
			{
				MeshComponents.MeshSections[Index].MeshActor->Destroy();
			}
			MeshComponents.MeshSections[Index].MeshActor = nullptr;
		}

		MeshComponents.MeshSections[Index].CompSource = Iter.CompSource;

		if (ESingleComponentSource::EImportPAK == Iter.CompSource)
		{
			for (auto& SingleIter : Iter.SingleMeshInfo)
			{
				if (!SingleIter.PakRefPath.IsEmpty())
				{
					FString CleanValue = SingleIter.PakCode;
					if (!CleanValue.IsEmpty())
						CleanValue = CleanValue.Mid(1, CleanValue.Len() - 2);

					AImportPakBaseClass* NewActor = FCatalogFunctionLibrary::LoadActorFormPak<AImportPakBaseClass, AImportPakBaseClass>(this->GetWorld(), SingleIter.PakRefPath);
					if (nullptr != NewActor)
					{
						NewActor->AttachToActor(this, FAttachmentTransformRules::KeepRelativeTransform);
						NewActor->SetActorRelativeTransform(Iter.MeshTransform);
						NewActor->FurnitureCodeChanged(CleanValue);
					}
					MeshComponents.MeshSections[Index].MeshActor = NewActor;
					UE_LOG(LogTemp, Log, TEXT("nullptr == NewActor is %d MeshActorCode is %s "), nullptr == NewActor, *CleanValue);
				}
			}
		}
		else
		{
			if (!IS_OBJECT_PTR_VALID(MeshComponents.MeshSections[Index].MeshComp))
			{
				FString MeshName = FString::Printf(TEXT("Mesh_%d"), Index);
				MeshComponents.MeshSections[Index].MeshComp = NewObject<UVolatileMeshComponent>(this, *MeshName, RF_NoFlags, nullptr, false, nullptr);
				MeshComponents.MeshSections[Index].MeshComp->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
				MeshComponents.MeshSections[Index].MeshComp->RegisterComponent();
			}
			MeshComponents.MeshSections[Index].MeshComp->ClearAllMeshSections();
			for (int32 j = 0; j < Iter.SingleMeshInfo.Num(); ++j)
			{
				const auto& MeshIter = Iter.SingleMeshInfo[j];
				MeshComponents.MeshSections[Index].MeshComp->CreateMeshSection(j, MeshIter.MeshInfo, false);
				//UMaterialInstanceDynamic* MaterialIns = GetMaterial(MeshIter.MaterialFolderID);
				//if (!IS_OBJECT_PTR_VALID(MaterialIns)) MaterialIns = DefaultMaterial;
				UMaterialInstanceDynamic* MaterialIns = UResourceSubsystem::GetInstance()->GetMaterialInsDynamic_Web(MeshIter.MaterialFolderID);
				MaterialIns->SetScalarParameterValue(FName(TEXT("Normal")), 0.0f);
				MeshComponents.MeshSections[Index].MeshComp->SetMaterialWithName(j, MeshIter.MeshInfo.SectionName, MaterialIns);
			}
			MeshComponents.MeshSections[Index].MeshComp->SetRelativeTransform(Iter.MeshTransform);
			MeshComponents.MeshSections[Index].MeshComp->bRenderCustomDepth = true;
			if (IS_OBJECT_PTR_VALID(MeshComponents.MeshSections[Index].MeshActor))
			{
				MeshComponents.MeshSections[Index].MeshActor->Destroy();
				MeshComponents.MeshSections[Index].MeshActor = nullptr;
			}
		}
		++Index;
	}
	while (MeshComponents.MeshSections.IsValidIndex(Index))
	{
		MeshComponents.MeshSections[Index].Clear();
		++Index;
	}
	if (!IS_OBJECT_PTR_VALID(MeshComponents.Outline))
	{
		MeshComponents.Outline = NewObject<UOutlineComponent>(this, TEXT("Outline"), RF_NoFlags, nullptr, false, nullptr);
		MeshComponents.Outline->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
		MeshComponents.Outline->RegisterComponent();
	}
	MeshComponents.Outline->ClearOutlines();
	for (auto& OutlinePair : InMeshInfos.OutlinePairs)
		MeshComponents.Outline->AddOutlines(FOutlineLineSegments(OutlinePair.Key, OutlinePair.Value));
	ShowOutline();
	tempProperty = InMeshInfos;
}

void AShowSingleComponentActor::DestroyDisplayer()
{
	MeshComponents.Clear();
	AShowMeshBaseActor::DestroyDisplayer();
}

void AShowSingleComponentActor::SetDisplayerHiddenInGame(bool NewHidden)
{
	for (auto Mesh : MeshComponents.MeshSections)
	{
		if (IS_OBJECT_PTR_VALID(Mesh.MeshActor))
		{
			Mesh.MeshActor->SetActorHiddenInGame(NewHidden);
		}
	}
	AShowMeshBaseActor::SetActorHiddenInGame(NewHidden);
}

void AShowSingleComponentActor::SetDisplayerEnableCollision(bool EnableCollision)
{
	for (auto Mesh : MeshComponents.MeshSections)
	{
		if (IS_OBJECT_PTR_VALID(Mesh.MeshActor))
		{
			Mesh.MeshActor->SetActorEnableCollision(EnableCollision);
		}
	}
	AShowMeshBaseActor::SetActorEnableCollision(EnableCollision);
}

FSingleComponentDisplayerUnit AShowSingleComponentActor::GetDisplayerUnit()
{
	return MeshComponents;
}

void AShowSingleComponentActor::RefreshComponentMesh()
{
	MeshComponents.Clear();
}

void AShowSingleComponentActor::BeginPlay()
{
	AShowMeshBaseActor::BeginPlay();
}

void AShowSingleComponentActor::ShowMeshes(EMeshType InMeshType, bool InShow)
{
	for (auto& Iter : MeshComponents.MeshSections)
	{
		if (IS_OBJECT_PTR_VALID(Iter.MeshComp))
		{
			if ((ESingleComponentSource::ECustom == Iter.CompSource && EMeshType::OnlyImport == InMeshType) || (ESingleComponentSource::EImportFBX == Iter.CompSource && EMeshType::OnlyCustom == InMeshType))
				continue;
			Iter.MeshComp->SetVisibility(InShow, true);
		}
	}
}

void AShowSingleComponentActor::ChangeMeshMaterial(EMeshType InMeshType, UMaterial* InCustomMaterial, UMaterial* InImportMaterial)
{
	for (auto& Iter : MeshComponents.MeshSections)
	{
		if (IS_OBJECT_PTR_VALID(Iter.MeshComp))
		{
			if (ESingleComponentSource::ECustom == Iter.CompSource)
			{
				if (EMeshType::OnlyImport == InMeshType) continue;
				Iter.MeshComp->UpdateMaterialForTemporary(InCustomMaterial);
			}
			else if (ESingleComponentSource::EImportFBX == Iter.CompSource)
			{
				if (EMeshType::OnlyCustom == InMeshType) continue;
				Iter.MeshComp->UpdateMaterialForTemporary(InImportMaterial);
			}
		}
	}
}

void AShowSingleComponentActor::ShowOutline()
{
	UE_LOG(LogTemp, Warning, TEXT("outline type : %d"), static_cast<int32>(CurrentOultineType));
	this->ShowMeshes(EMeshType::All, true);
	ACatalogPlayerController::Get()->SetPostProcessType(static_cast<int32>(CurrentOultineType));
	if (EOutlineType::EOutlineAndWhiteMaterial == CurrentOultineType)
	{
		this->ChangeMeshMaterial(EMeshType::All, WhiteMaterial, WhiteMaterial);
		MeshComponents.Outline->SetVisibility(true, true);
	}
	else if (EOutlineType::EOnlyOutline == CurrentOultineType)
	{
		MeshComponents.Outline->SetVisibility(true, true);
		this->ShowMeshes(EMeshType::OnlyCustom, false);
		this->ShowMeshes(EMeshType::OnlyImport, true);
		this->ChangeMeshMaterial(EMeshType::OnlyImport, WhiteMaterial, WhiteMaterial);
	}
	else if (EOutlineType::EOutlineAndMaterial == CurrentOultineType)
	{
		MeshComponents.Outline->SetVisibility(true, true);
		this->ShowMeshes(EMeshType::All, true);
		this->ChangeMeshMaterial(EMeshType::All, nullptr, nullptr);
	}
	else if (EOutlineType::EOnlyMaterial == CurrentOultineType)
	{
		MeshComponents.Outline->SetVisibility(false, true);
		this->ShowMeshes(EMeshType::All, true);
		this->ChangeMeshMaterial(EMeshType::All, nullptr, nullptr);
	}
}
