// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/BasicClasses/ShowMeshBaseActor.h"
#include "DesignStation/Geometry/DataDefines/MultiComponentDisplayerUnit.h"
#include "ShowSingleComponentActor.generated.h"


UCLASS()
class DESIGNSTATION_API AShowSingleComponentActor : public AShowMeshBaseActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AShowSingleComponentActor();

public:

	void RefreshComponentMesh(const FShowSingleComponentActorProperty& InMeshInfos);
	void RefreshComponentMesh();

	virtual void DestroyDisplayer() override;

	void SetDisplayerHiddenInGame(bool NewHidden);

	void SetDisplayerEnableCollision(bool EnableCollision);

	void GetDisplayerBound(FVector& Center, FVector& Extention) const
	{
		FBox Box = MeshComponents.GetBoundBox();
		Box.GetCenterAndExtents(Center, Extention);
	}
	FShowSingleComponentActorProperty getTempProperty() { return tempProperty; }

	FSingleComponentDisplayerUnit GetDisplayerUnit();
protected:

	enum EMeshType
	{
		All,
		OnlyCustom,
		OnlyImport
	};

	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	virtual void ShowOutline() override;

	void ShowMeshes(EMeshType InMeshType, bool InShow);

	void ChangeMeshMaterial(EMeshType InMeshType, UMaterial* InCustomMaterial = nullptr, UMaterial* InImportMaterial = nullptr);

protected:

	UPROPERTY()
		FSingleComponentDisplayerUnit MeshComponents;
	FShowSingleComponentActorProperty tempProperty;
};
