// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterRelativeLibrary.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "DataCenter/Parameter/ParameterEffectionParser.h"
#include "DataCenter/Parameter/ParameterRefrenceParser.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"

#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"
#include "Lexer/ExpressionLexer.h"
#include "Parser/ExpressionParser.h"

#include "Decimal.h"
#include "DecimalMath.h"

#define LOCTEXT_NAMESPACE "ParameterRelativeLibrary"

#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

bool UParameterRelativeLibrary::CombineParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TArray<FParameterData>& InChildParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutParameters)
{
	OutParameters = InParentParameters;
	for (auto& Iter : InChildParameters)
	{
		if (OutParameters.Contains(Iter.Data.name))
		{
			OutParameters[Iter.Data.name] = Iter;
		}
		else
		{
			OutParameters.Add(Iter.Data.name, Iter);
		}
	}
	return true;
}

bool UParameterRelativeLibrary::CombineParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InChildParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutParameters)
{
	OutParameters = InParentParameters;
	for (auto& Iter : InChildParameters)
	{
		if (OutParameters.Contains(Iter.Key))
		{
			OutParameters[Iter.Key] = Iter.Value;
		}
		else
		{
			OutParameters.Add(Iter);
		}
	}
	return true;
}

bool UParameterRelativeLibrary::CombineParameters(const TArray<FParameterData>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InChildParameters, TArray<FParameterData>& OutParameters)
{
	OutParameters.AddDefaulted(InParentParameters.Num());
	int32 i = 0;
	for (auto& Iter : InParentParameters)
	{
		if (InChildParameters.Contains(Iter.Data.name))
			OutParameters[i] = InChildParameters[Iter.Data.name];
		else
			OutParameters[i] = Iter;
		++i;
	}
	return true;
}

bool UParameterRelativeLibrary::CombineParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InOutParameters, const TArray<FParameterData>& InChildParameters)
{
	for (auto& Iter : InChildParameters)
	{
		if (InOutParameters.Contains(Iter.Data.name))
			InOutParameters[Iter.Data.name] = Iter;
		else
			InOutParameters.Add(Iter.Data.name, Iter);
	}
	return true;
}

bool UParameterRelativeLibrary::CombineParameters(TMap<FString, TArray<FString>>& InParentParameters, const TMap<FString, TArray<FString>>& InChildParameters)
{
	for (auto& Iter : InChildParameters)
	{
		if (InParentParameters.Contains(Iter.Key))
			InParentParameters[Iter.Key] = Iter.Value;
		else
			InParentParameters.Add(Iter.Key, Iter.Value);
	}
	return true;
}

bool UParameterRelativeLibrary::CombineFirstParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InOutParameters, const TArray<FParameterData>& InChildParameters)
{
	for (auto& Iter : InChildParameters)
	{
		if (false == InOutParameters.Contains(Iter.Data.name))InOutParameters.Add(Iter.Data.name, Iter);
	}
	return true;
}

bool UParameterRelativeLibrary::CalculateParameterBySymbolTables(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FParameterData& ParameterData)
{
	auto ParseExpressionFunc = [&ParentSymbolTables, &LocalSymbolTables](const FString& Expression, const FString& ParameterName, const TCHAR* ExpressionName, FString& OutResult) -> bool
	{
		FString Error;
		TArray<FAstNodePtr> Nodes;

		FExpressionParser Parser;
		if (!Parser.Parse(Expression, Nodes, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not parse [%s - %s] expression [%s]: %s"), *ParameterName, ExpressionName, *Expression, *Error);
			return false;
		}

		if (Nodes.IsEmpty())
		{
			OutResult = TEXT("");
			return true;
		}
		else if (Nodes.Num() != 1)
		{
			UE_LOG(LogTemp, Error, TEXT("Parse [%s - %s] expression [%s] successfully, but returned invalid result count: %d"), *ParameterName, ExpressionName, *Expression, Nodes.Num());
			return false;
		}

		if (!UCatalogExpressionFunctionLibrary::EvaluateExpressionNode(Nodes[0], ParentSymbolTables, LocalSymbolTables, OutResult, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Evalute [%s - %s] expression [%s] failed: %s"), *ParameterName, ExpressionName, *Expression, *Error);
			return false;
		}

		return true;
	};

	int32 EnumForceIndex = INDEX_NONE;

	// 如果有枚举值，先计算枚举值
	for (int32 EnumIndex = 0; EnumIndex < ParameterData.EnumData.Num(); ++EnumIndex)
	{
		FEnumParameterTableData& TableData = ParameterData.EnumData[EnumIndex];
		if (!ParseExpressionFunc(TableData.visibility_exp, ParameterData.Data.name, TEXT("enum visibility"), TableData.visibility))
		{
			return false;
		}

		if (!ParseExpressionFunc(TableData.expression, ParameterData.Data.name, TEXT("enum value"), TableData.value))
		{
			return false;
		}

		if (EnumForceIndex == INDEX_NONE && FCString::Atof(*TableData.visibility) != 0)
		{
			FString ConditionResult;
			if (!ParseExpressionFunc(TableData.force_select_condition, ParameterData.Data.name, TEXT("enum force select condition"), ConditionResult))
			{
				return false;
			}

			if (FCString::Atof(*ConditionResult) != 0)
			{
				EnumForceIndex = EnumIndex;
			}
		}
	}

	// 计算参数本身的所有限制性表达式
	if (!ParseExpressionFunc(ParameterData.Data.Must_exp, ParameterData.Data.name, TEXT("must"), ParameterData.Data.Must))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.Special_exp, ParameterData.Data.name, TEXT("special"), ParameterData.Data.Special))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.editable_exp, ParameterData.Data.name, TEXT("editable"), ParameterData.Data.editable))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.visibility_exp, ParameterData.Data.name, TEXT("visibility"), ParameterData.Data.visibility))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.min_expression, ParameterData.Data.name, TEXT("min value"), ParameterData.Data.min_value))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.max_expression, ParameterData.Data.name, TEXT("max value"), ParameterData.Data.max_value))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.expression, ParameterData.Data.name, TEXT("value"), ParameterData.Data.value))
	{
		return false;
	}

	if (!ParseExpressionFunc(ParameterData.Data.grid_expression, ParameterData.Data.name, TEXT("grid"), ParameterData.Data.grid_value))
	{
		return false;
	}

	if (!ParameterData.Data.grid_value.IsEmpty() && UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(ParameterData.Data.value))
	{
		if (UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(ParameterData.Data.grid_value))
		{
			FDecimal SnappedValue(ParameterData.Data.value);
			switch (ParameterData.Data.grid_value_mark)
			{
			case EParamGridMarkType::E_Grid_Floor:
				{
					SnappedValue = FDecimalMath::GridSnap(ParameterData.Data.value, ParameterData.Data.grid_value, false);
				}
				break;
			case EParamGridMarkType::E_Grid_Ceil:
				{
					SnappedValue = FDecimalMath::GridSnap(ParameterData.Data.value, ParameterData.Data.grid_value, true);
				}
				break;
			default:
				{
					UE_LOG(LogTemp, Error, TEXT("Parameter [%s] grid value mark is not support: %d."), *ParameterData.Data.name, static_cast<uint8>(ParameterData.Data.grid_value_mark));
					return false;
				}
				break;
			}

			ParameterData.Data.value = SnappedValue.ToString(16);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Parameter [%s] grid value is not numeric."), *ParameterData.Data.name);
			return false;
		}
	}

	bool bValueIsNumeric = UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(ParameterData.Data.value);

	// 校验最大最小值是否有效
	// Key: min value mark, Value: max value mark
	FDecimal MinValue(ParameterData.Data.min_value);
	FDecimal MaxValue(ParameterData.Data.max_value);
	TPair<bool, bool> ValueRangeMark = { false, false };
	if (!ParameterData.Data.min_value.IsEmpty() && !ParameterData.Data.max_value.IsEmpty())
	{
		if (MinValue <= MaxValue)
		{
			ValueRangeMark = { true, true };
		}
	}
	else if (!ParameterData.Data.min_value.IsEmpty())
	{
		ValueRangeMark.Key = true;
	}
	else if (!ParameterData.Data.max_value.IsEmpty())
	{
		ValueRangeMark.Value = true;
	}

	// 如果是枚举，要选枚举值列表里的数据
	if (ParameterData.Data.is_enum != 0 && ParameterData.EnumData.IsValidIndex(0))
	{
		// 如果标记为不强制匹配枚举项列表则跳过匹配
		if (!ParameterData.Data.no_match_enum_data)
		{
			// 如果找到强制匹配项，选中强制匹配项
			if (EnumForceIndex != INDEX_NONE)
			{
				ParameterData.Data.value = ParameterData.EnumData[EnumForceIndex].value;
			}
			else if (!bValueIsNumeric) // 如果参数值不是数值，简单匹配字符串，不存在匹配项则报错
			{
				bool bHasMatched = ParameterData.EnumData.ContainsByPredicate([&](const FEnumParameterTableData& InTableData) { return ParameterData.Data.value.Equals(InTableData.value); });
				if (!bHasMatched)
				{
					UE_LOG(LogTemp, Error, TEXT("Parameter [%s] result [%s] cannot match enumeration list."), *ParameterData.Data.name, *ParameterData.Data.value);
					return false;
				}
			}
			else // 如果参数值是数值
			{
				FDecimal DataValue(ParameterData.Data.value);

				// 匹配枚举列表中相近值
				FEnumParameterTableData* FoundTableData = ParameterData.EnumData.FindByPredicate([&](const FEnumParameterTableData& InTableData)
				{
					if (FCString::Atof(*InTableData.visibility) == 0.0f)
					{
						return false;
					}

					FDecimal TableValue(InTableData.value);
					return FDecimalMath::IsNearlyEqual(TableValue, DataValue, FDecimal(0.01f));
				});

				// 不存在匹配时报错
				if (FoundTableData == nullptr)
				{
					UE_LOG(LogTemp, Error, TEXT("Parameter [%s] result [%s] cannot match enumeration list."), *ParameterData.Data.name, *ParameterData.Data.value);
					return false;
				}

				// 匹配时覆盖值，以防两位小数后不一致，后续计算误差被放大
				ParameterData.Data.value = FoundTableData->value;
			}
		}
	}
	else // 不是枚举则判断是否要做[min, max]区间判断
	{
		// 参数值不是数值或最大最小值不存在时，不做区间校验
		if (bValueIsNumeric && (ValueRangeMark.Key || ValueRangeMark.Value))
		{
			FDecimal DataValue(ParameterData.Data.value);

			if (ValueRangeMark.Key && ValueRangeMark.Value)
			{
				ParameterData.Data.value = FDecimalMath::Clamp(DataValue, MinValue, MaxValue).ToString(16);
			}
			else if (ValueRangeMark.Key)
			{
				if (DataValue < MinValue)
				{
					ParameterData.Data.value = MinValue.ToString(16);
				}
			}
			else if (ValueRangeMark.Value)
			{
				if (DataValue > MaxValue)
				{
					ParameterData.Data.value = MaxValue.ToString(16);
				}
			}
		}
	}

	return true;
}

bool UParameterRelativeLibrary::CalculateParameterExpression(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, FParameterData& InOutParameter)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> ParentSymbolTables;
	for (const TPair<FString, FParameterData>& Pair : InGlobalParameter)
	{
		ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	for (const TPair<FString, FParameterData>& Pair : InParentParameters)
	{
		ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> LocalSymbolTables;
	for (const TPair<FString, FParameterData>& Pair : InLocalParameters)
	{
		LocalSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	return CalculateParameterBySymbolTables(ParentSymbolTables, LocalSymbolTables, InOutParameter);
}

bool UParameterRelativeLibrary::CalculateParameterValue_LevelSort(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  InParentParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ComponentParametersMap)
{
	return CalculateParameterValue_TopoSort(InGlobalParameter, InParentParameters, ComponentParametersMap);
}

bool UParameterRelativeLibrary::GenerateCalculateOrder(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParametersMap, TArray<FString>& OutOrder)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(TArray<FString>)> Graph;	// 参数 -> 引用的参数
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(int32)> InDegree;			// 被引用参数的入度（次数）

	// 初始化图和入度表
	for (const TPair<FString, FParameterData>& Pair : InParametersMap)
	{
		Graph.FindOrAdd(Pair.Key);
		InDegree.FindOrAdd(Pair.Key, 0);
	}

	FString Error;

	for (const TPair<FString, FParameterData>& Pair : InParametersMap)
	{
		TArray<FExpressionLexerToken> Tokens;
		Tokens.Reserve(1024);

		FExpressionLexer Lexer;

		// 如果参数有枚举值，收集枚举值表达式
		for (const FEnumParameterTableData& TableData : Pair.Value.EnumData)
		{
			Lexer.SetContent(TableData.visibility_exp);
			if (!Lexer.Tokenize(Tokens, Error))
			{
				UE_LOG(LogTemp, Error, TEXT("Can not tokenize enum [%s - %s] visibility expression: %s"), *Pair.Key, *TableData.id, *Error);
				return false;
			}

			Lexer.SetContent(TableData.expression);
			if (!Lexer.Tokenize(Tokens, Error))
			{
				UE_LOG(LogTemp, Error, TEXT("Can not tokenize enum [%s - %s] expression: %s"), *Pair.Key, *TableData.id, *Error);
				return false;
			}
		}

		// 收集参数所有表达式
		Lexer.SetContent(Pair.Value.Data.expression);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.max_expression);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] max expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.min_expression);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] min expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.visibility_exp);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] visibility expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.editable_exp);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] editable expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.Special_exp);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] special expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.Must_exp);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] must expression: %s"), *Pair.Key, *Error);
			return false;
		}

		Lexer.SetContent(Pair.Value.Data.grid_expression);
		if (!Lexer.Tokenize(Tokens, Error))
		{
			UE_LOG(LogTemp, Error, TEXT("Can not tokenize parameter [%s] grid expression: %s"), *Pair.Key, *Error);
			return false;
		}

		TArray<FString> ReferencedLocalVariable;
		for (int32 Index = 0; Index < Tokens.Num(); Index++)
		{
			// 找到所有引用的参数
			if (Tokens[Index].Type == EExpressionLexerTokenType::Identifier)
			{
				// 只处理引用的本层参数
				if (Tokens.IsValidIndex(Index - 1) && Tokens[Index - 1].Type == EExpressionLexerTokenType::LeftBrace &&
					Tokens.IsValidIndex(Index + 1) && Tokens[Index + 1].Type == EExpressionLexerTokenType::RightBrace)
				{
					const FString& Variable = Tokens[Index].Value;

					// 参数内的任何表达式不能引用当前参数自身
					if (Variable.Equals(Pair.Key, ESearchCase::CaseSensitive))
					{
						UE_LOG(LogTemp, Error, TEXT("The parameter itself is referenced in the expression."));
						return false;
					}

					// 虽然引用了本层参数，但是本层没有这个参数，也视为引用了上层参数，不加入图里
					if (InParametersMap.Contains(Variable) && !Graph[Variable].Contains(Pair.Key))
					{
						Graph[Variable].Add(Pair.Key);
						InDegree.FindOrAdd(Pair.Key)++;
					}
				}
			}
		}
	}

	// Kahn 算法计算引用排序并检测是否有环引用
	TQueue<FString> Queue;
	for (const TPair<FString, int32>& Pair : InDegree)
	{
		if (Pair.Value == 0)
		{
			Queue.Enqueue(Pair.Key);
		}
	}

	OutOrder.Empty();

	while (!Queue.IsEmpty())
	{
		FString Variable;
		Queue.Dequeue(Variable);
		
		OutOrder.Add(Variable);

		for (const FString& Dependency : Graph[Variable])
		{
			InDegree[Dependency]--;
			if (InDegree[Dependency] == 0)
			{
				Queue.Enqueue(Dependency);
			}
		}
	}

	if (OutOrder.Num() != InParametersMap.Num())
	{
		UE_LOG(LogTemp, Error, TEXT("Detected parameter has a ring reference!"));
		return false;
	}

	return true;
}

bool UParameterRelativeLibrary::CalculateParameterValue_TopoSort(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutComponentParametersMap)
{
	TArray<FString> CalculateOrder;
	if (!GenerateCalculateOrder(OutComponentParametersMap, CalculateOrder))
	{
		return false;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> ParentSymbolTables;
	for (const TPair<FString, FParameterData>& Pair : InGlobalParameter)
	{
		ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	for (const TPair<FString, FParameterData>& Pair : InParentParameters)
	{
		ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> LocalSymbolTables;

	for (int32 Index = 0; Index < CalculateOrder.Num(); Index++)
	{
		FParameterData& ParameterData = OutComponentParametersMap[CalculateOrder[Index]];

		if (!CalculateParameterBySymbolTables(ParentSymbolTables, LocalSymbolTables, ParameterData))
		{
			return false;
		}

		LocalSymbolTables.FindOrAdd(CalculateOrder[Index]) = ParameterData.Data.value;
	}
	
	return true;
}

bool UParameterRelativeLibrary::CalculateParameterExpression(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, const FString& InExpression, FString& OutValue, FString& OutFormatExpress)
{
	OutFormatExpress.Empty();
	OutValue.Empty();
	if (InExpression.IsEmpty())//因为更换同级循环计算，导致原有判断逻辑失效，在此判断为空
	{
		OutFormatExpress = OutValue = TEXT("");
		return true;
	}

	TArray<FAstNodePtr> Nodes;
	FString Error;

	FExpressionParser Parser;
	if (!Parser.Parse(InExpression, Nodes, Error))
	{
		UE_LOG(LogTemp, Error, TEXT("Parse expression [%s] failed: %s"), *InExpression, *Error);
		return false;
	}
	else if (Nodes.Num() != 1)
	{
		UE_LOG(LogTemp, Error, TEXT("Parse expression [%s] successfully, but generated multi results: %d"), *InExpression, Nodes.Num());
		return false;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> ParentSymbolTables;
	for (const TPair<FString, FParameterData>& Pair : InGlobalParameter)
	{
		bool bExist = false;
		for (auto& Iter : ParentSymbolTables)
		{
			if (Iter.Key.Equals(Pair.Key, ESearchCase::CaseSensitive))
			{
				Iter.Value = Pair.Value.Data.value;
				bExist = true;
				break;
			}
		}
		if (!bExist)
		{
			ParentSymbolTables.Add(Pair.Key, Pair.Value.Data.value);
		}
		//ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	for (const TPair<FString, FParameterData>& Pair : InParentParameters)
	{
		bool bExist = false;
		for (auto& Iter : ParentSymbolTables)
		{
			if (Iter.Key.Equals(Pair.Key, ESearchCase::CaseSensitive))
			{
				Iter.Value = Pair.Value.Data.value;
				bExist = true;
				break;
			}
		}
		if (!bExist)
		{
			ParentSymbolTables.Add(Pair.Key, Pair.Value.Data.value);
		}
		//ParentSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)> LocalSymbolTables;
	for (const TPair<FString, FParameterData>& Pair : InLocalParameters)
	{
		bool bExist = false;
		for (auto& Iter : LocalSymbolTables)
		{
			if (Iter.Key.Equals(Pair.Key, ESearchCase::CaseSensitive))
			{
				Iter.Value = Pair.Value.Data.value;
				bExist = true;
				break;
			}
		}
		if (!bExist)
		{
			LocalSymbolTables.Add(Pair.Key, Pair.Value.Data.value);
		}
		//LocalSymbolTables.FindOrAdd(Pair.Key) = Pair.Value.Data.value;
	}

	if (!UCatalogExpressionFunctionLibrary::EvaluateExpressionNode(Nodes[0], ParentSymbolTables, LocalSymbolTables, OutValue, Error))
	{
		UE_LOG(LogTemp, Error, TEXT("Evalute expression [%s] failed: %s"), *InExpression, *Error);
		return false;
	}

	OutFormatExpress = InExpression;

	return true;
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif

#undef LOCTEXT_NAMESPACE