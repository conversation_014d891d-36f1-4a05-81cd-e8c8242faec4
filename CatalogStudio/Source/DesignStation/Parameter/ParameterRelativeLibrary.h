// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CaseSensitiveKeyFuncs.h"
#include "ParameterRelativeLibrary.generated.h"

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UParameterRelativeLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	static bool CombineParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TArray<FParameterData>& InChildParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutParameters);

	static bool CombineParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InChildParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutParameters);

	static bool CombineParameters(const TArray<FParameterData>& InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InChildParameters, TArray<FParameterData>& OutParameters);

	static bool CombineParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InOutParameters, const TArray<FParameterData>& InChildParameters);

	static bool CombineParameters(TMap<FString, TArray<FString>>& InParentParameters, const TMap<FString, TArray<FString>>& InChildParameters);

	static bool CombineFirstParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InOutParameters, const TArray<FParameterData>& InChildParameters);

	// Use symbol tables (not xxxParamsMap) to calculate parameter expressions。
	static bool CalculateParameterBySymbolTables(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& ParentSymbolTables, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FString)>& LocalSymbolTables, FParameterData& ParameterData);

	static bool CalculateParameterExpression(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InLocalParameters, FParameterData& InOutParameter);

	static bool CalculateParameterExpression(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InLocalParameters, const FString& InExpression, FString& OutValue, FString& OutFormatExpress);

	//同级循环引用计算函数
	static bool CalculateParameterValue_LevelSort(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter,TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  InParentParameters,TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ComponentParametersMap);

	static bool GenerateCalculateOrder(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParametersMap, TArray<FString>& OutOrder);
	static bool CalculateParameterValue_TopoSort(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InGlobalParameter, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutComponentParametersMap);
};
