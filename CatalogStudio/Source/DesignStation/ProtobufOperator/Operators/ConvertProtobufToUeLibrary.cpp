// Fill out your copyright notice in the Description page of Project Settings.

#include "ConvertProtobufToUeLibrary.h"


bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const ApplicationConfig& InProtoData, FLoginRemeberInfo& OutUeData)
{
	OutUeData.UserName = FString(UTF8_TO_TCHAR(InProtoData.last_user_name().c_str()));
	OutUeData.Password = FString((InProtoData.password().c_str()));
	OutUeData.RemeberMe = InProtoData.remeber_password();
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::Vector& InProtoData, FVector& OutUeData)
{
	OutUeData.X = InProtoData.x();
	OutUeData.Y = InProtoData.y();
	OutUeData.Z = InProtoData.z();

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::Vector2D& InProtoData, FVector2D& OutUeData)
{
	OutUeData.X = InProtoData.x();
	OutUeData.Y = InProtoData.y();
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ExpressionValuePair& InProtoData, FExpressionValuePair& OutUeData)
{
	OutUeData.Expression = FString(UTF8_TO_TCHAR(InProtoData.expression().c_str()));
	OutUeData.Value = FString(UTF8_TO_TCHAR(InProtoData.value().c_str()));
	return true;
}


bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::GeomtryCubeProperty& InProtoData, FGeomtryCubeProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_x(), OutUeData.StartLocationX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_y(), OutUeData.StartLocationY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_z(), OutUeData.StartLocationZ);
	bool R4 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_x(), OutUeData.EndLocationX);
	bool R5 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_y(), OutUeData.EndLocationY);
	bool R6 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_z(), OutUeData.EndLocationZ);

	return R1 && R2 && R3 && R4 && R5 && R6;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::GeomtryEllipsePlanProperty& InProtoData, FGeomtryEllipsePlanProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.center_location_x(), OutUeData.CenterLocationX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.center_location_y(), OutUeData.CenterLocationY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.center_location_z(), OutUeData.CenterLocationZ);
	bool R4 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.interp_point_count_data(), OutUeData.InterpPointCountData);
	bool R5 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.long_radius_data(), OutUeData.LongRadiusData);
	bool R6 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.short_radius_data(), OutUeData.ShortRadiusData);
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());
	return R1 && R2 && R3 && R4 && R5 && R6;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::GeomtryRectanglePlanProperty& InProtoData, FGeomtryRectanglePlanProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_x(), OutUeData.StartLocationX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_y(), OutUeData.StartLocationY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location_z(), OutUeData.StartLocationZ);
	bool R4 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_x(), OutUeData.EndLocationX);
	bool R5 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_y(), OutUeData.EndLocationY);
	bool R6 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location_z(), OutUeData.EndLocationZ);
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());

	return R1 && R2 && R3 && R4 && R5 && R6;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::GeomtryLineProperty& InProtoData, FGeomtryLineProperty& OutUeData)
{
	OutUeData.BigArc = InProtoData.big_arc();
	OutUeData.ID = InProtoData.id();
	OutUeData.LineType = static_cast<ELineType>(InProtoData.line_type());
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.start_location(), OutUeData.StartLocation);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.end_location(), OutUeData.EndLocation);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.radius_or_height_data(), OutUeData.RadiusOrHeightData);
	bool R4 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.interp_point_count_data(), OutUeData.InterpPointCountData);

	return R1 && R2 && R3 && R4;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::GeomtryPointProperty& InProtoData, FGeomtryPointProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_x(), OutUeData.LocationX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_y(), OutUeData.LocationY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_z(), OutUeData.LocationZ);
	bool R4 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.pre_point_location(), OutUeData.PrePointLocation);
	OutUeData.ID = InProtoData.id();
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());
	OutUeData.PositionType = static_cast<EPositionType>(InProtoData.position_type());

	return R1 && R2 && R3 && R4;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CrossSectionData& InProtoData, FCrossSectionData& OutUeData)
{
	OutUeData = FCrossSectionData();
	OutUeData.SectionType = static_cast<ESectionType>(InProtoData.section_type());
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());
	if (ESectionType::ECustomPlan == OutUeData.SectionType)
	{
		OutUeData.Lines.Empty();
		OutUeData.Lines.AddZeroed(InProtoData.lines_size());
		for (int32 i = 0; i < OutUeData.Lines.Num(); ++i)
		{
			if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.lines(i), OutUeData.Lines[i]))
				return false;
		}

		OutUeData.Points.Empty();
		OutUeData.Points.AddZeroed(InProtoData.points_size());
		for (int32 i = 0; i < OutUeData.Points.Num(); ++i)
		{
			if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.points(i), OutUeData.Points[i]))
				return false;
		}
		return true;
	}
	else if (ESectionType::ECube == OutUeData.SectionType)
	{
		return UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.cube(), OutUeData.Cube);
	}
	else if (ESectionType::EEllipse == OutUeData.SectionType)
	{
		return UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.ellipse(), OutUeData.Ellipse);
	}
	else if (ESectionType::ERectangle == OutUeData.SectionType)
	{
		return UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.rectangle(), OutUeData.Rectangle);
	}
	return false;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CustomMeshInfo& InProtoData, FPMCSection& OutUeData)
{
	OutUeData.Normals.Empty();
	OutUeData.Normals.AddZeroed(InProtoData.normals_size());
	for (int32 i = 0; i < OutUeData.Normals.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.normals(i), OutUeData.Normals[i]))
			return  false;
	}

	OutUeData.Vertexes.Empty();
	OutUeData.Vertexes.AddZeroed(InProtoData.vertex_locations_size());
	for (int32 i = 0; i < OutUeData.Vertexes.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.vertex_locations(i), OutUeData.Vertexes[i]))
			return false;
	}

	OutUeData.UV.Empty();
	OutUeData.UV.AddZeroed(InProtoData.vertex_uv_size());
	for (int32 i = 0; i < OutUeData.UV.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.vertex_uv(i), OutUeData.UV[i]))
			return false;
	}
	OutUeData.Triangles.Empty();
	OutUeData.Triangles.AddZeroed(InProtoData.wedge_indices_size());
	for (int32 i = 0; i < OutUeData.Triangles.Num(); ++i)
	{
		OutUeData.Triangles[i] = InProtoData.wedge_indices(i);
	}

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ImportMeshSection& InProtoData, FImportMeshSection& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.material_id(), OutUeData.MaterialId);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.section_mesh(), OutUeData.SectionMesh);

	return R1 && R2;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::LocationProperty& InProtoData, FLocationProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_x(), OutUeData.LocationX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_y(), OutUeData.LocationY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.location_z(), OutUeData.LocationZ);

	return R1 && R2 && R3;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::RotationProperty& InProtoData, FRotationProperty& OutUeData)
{

	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.pitch(), OutUeData.Pitch);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.roll(), OutUeData.Roll);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.yaw(), OutUeData.Yaw);

	return R1 && R2 && R3;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ScaleProperty& InProtoData, FScaleProperty& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.x(), OutUeData.X);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.y(), OutUeData.Y);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.z(), OutUeData.Z);

	return R1 && R2 && R3;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionCutOutOperation& InProtoData, FSectionCutOutOperation& OutUeData)
{
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.cut_out_value(), OutUeData.CutOutValue);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.ellipse(), OutUeData.Ellipse);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.rectangle(), OutUeData.Rectangle);

	OutUeData.Lines.Empty();
	OutUeData.Lines.AddZeroed(InProtoData.lines_size());
	for (int32 i = 0; i < OutUeData.Lines.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.lines(i), OutUeData.Lines[i]))
			return false;
	}

	OutUeData.Points.Empty();
	OutUeData.Points.AddZeroed(InProtoData.points_size());
	for (int32 i = 0; i < OutUeData.Points.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.points(i), OutUeData.Points[i]))
			return false;
	}
	OutUeData.ID = InProtoData.id();
	OutUeData.SectionType = static_cast<ESectionType>(InProtoData.section_type());
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());

	return R1 && R2 && R3;

}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionDrawOperation& InProtoData, FSectionDrawOperation& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	bool R1 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.draw_offset_x(), OutUeData.DrawOffsetX);
	bool R2 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.draw_offset_y(), OutUeData.DrawOffsetY);
	bool R3 = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.draw_offset_z(), OutUeData.DrawOffsetZ);

	return R1 && R2 && R3;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionLoftOperation& InProtoData, FSectionLoftOperation& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	OutUeData.PlanBelongs = static_cast<EPlanPolygonBelongs>(InProtoData.plan_belongs());

	OutUeData.Lines.Empty();
	OutUeData.Lines.AddZeroed(InProtoData.lines_size());
	for (int32 i = 0; i < OutUeData.Lines.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.lines(i), OutUeData.Lines[i]))
			return false;
	}

	OutUeData.Points.Empty();
	OutUeData.Points.AddZeroed(InProtoData.points_size());
	for (int32 i = 0; i < OutUeData.Points.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.points(i), OutUeData.Points[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionOperationOrder& InProtoData, FSectionOperationOrder& OutUeData)
{
	OutUeData.Index = InProtoData.index();
	OutUeData.OperatorType = static_cast<ESectionOperationType>(InProtoData.operator_type());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionShiftingOperation& InProtoData, FSectionShiftingOperation& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	OutUeData.ShiftValue.Empty();
	OutUeData.ShiftValue.AddZeroed(InProtoData.shift_value_size());
	for (int32 i = 0; i < OutUeData.ShiftValue.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.shift_value(i), OutUeData.ShiftValue[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionZoomOperation& InProtoData, FSectionZoomOperation& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	OutUeData.ZoomValue.Empty();
	OutUeData.ZoomValue.AddZeroed(InProtoData.zoom_value_size());
	for (int32 i = 0; i < OutUeData.ZoomValue.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.zoom_value(i), OutUeData.ZoomValue[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SectionOperation& InProtoData, FSectionOperation& OutUeData)
{
	OutUeData.CutoutOperations.Empty();
	OutUeData.CutoutOperations.AddZeroed(InProtoData.cutout_operations_size());
	for (int32 i = 0; i < OutUeData.CutoutOperations.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.cutout_operations(i), OutUeData.CutoutOperations[i]))
			return false;
	}

	OutUeData.DrawOperations.Empty();
	OutUeData.DrawOperations.AddZeroed(InProtoData.draw_operations_size());
	for (int32 i = 0; i < OutUeData.DrawOperations.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.draw_operations(i), OutUeData.DrawOperations[i]))
			return false;
	}

	OutUeData.ShiftOperations.Empty();
	OutUeData.ShiftOperations.AddZeroed(InProtoData.shift_operations_size());
	for (int32 i = 0; i < OutUeData.ShiftOperations.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.shift_operations(i), OutUeData.ShiftOperations[i]))
			return false;
	}

	OutUeData.ZoomOperations.Empty();
	OutUeData.ZoomOperations.AddZeroed(InProtoData.zoom_operations_size());
	for (int32 i = 0; i < OutUeData.ZoomOperations.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.zoom_operations(i), OutUeData.ZoomOperations[i]))
			return false;
	}

	OutUeData.OperatorOrder.Empty();
	OutUeData.OperatorOrder.AddZeroed(InProtoData.operator_order_size());
	for (int32 i = 0; i < OutUeData.OperatorOrder.Num(); ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.operator_order(i), OutUeData.OperatorOrder[i]))
			return false;
	}

	bool R = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.lofting_operation(), OutUeData.LoftingOperation);

	return R;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SingleComponentItem& InProtoData, FSingleComponentItem& OutUeData)
{
	OutUeData.SectionName = UTF8_TO_TCHAR(InProtoData.section_name().c_str());
	OutUeData.ThumbnailPath = UTF8_TO_TCHAR(InProtoData.thumbnail_path().c_str());
	OutUeData.ComponentSource = static_cast<ESingleComponentSource>(InProtoData.component_source());

	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.visible_param(), OutUeData.VisibleParam))
		return false;

	if (ESingleComponentSource::ECustom == OutUeData.ComponentSource)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.operator_section(), OutUeData.OperatorSection))
			return false;
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_material(), OutUeData.ComponentMaterial))
			return false;
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.section_operation(), OutUeData.SectionOperation))
			return false;
	}
	else if (ESingleComponentSource::EImportFBX == OutUeData.ComponentSource)
	{
		int32 ImportMeshCount = InProtoData.import_mesh_size();
		OutUeData.ImportMesh.Empty();
		OutUeData.ImportMesh.AddZeroed(ImportMeshCount);
		for (int32 i = 0; i < ImportMeshCount; ++i)
		{
			if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.import_mesh(i), OutUeData.ImportMesh[i]))
				return false;
		}
	}
	else if (ESingleComponentSource::EImportPAK == OutUeData.ComponentSource)
	{
		OutUeData.PakRefPath = UTF8_TO_TCHAR(InProtoData.pakrefpath().c_str());
		OutUeData.PakRelativeFilePath = UTF8_TO_TCHAR(InProtoData.pakrelativepath().c_str());
	}
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.single_component_location(), OutUeData.SingleComponentLocation))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.single_component_rotation(), OutUeData.SingleComponentRotation))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.single_component_scale(), OutUeData.SingleComponentScale))
		return false;
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::SingleComponentProperty& InProtoData, FSingleComponentProperty& OutUeData)
{
	int32 ComponentCount = InProtoData.component_items_size();
	OutUeData.ComponentItems.Empty();
	OutUeData.ComponentItems.AddZeroed(ComponentCount);
	for (int32 i = 0; i < ComponentCount; ++i)
	{
		const ::catalog_studio_message::SingleComponentItem& ComponentItem = InProtoData.component_items(i);
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(ComponentItem, OutUeData.ComponentItems[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::MultiComponentData& InProtoData, FMultiComponentData& OutUeData)
{
	int32 ParameterCount = InProtoData.component_items_size();
	OutUeData.ComponentItems.Empty();
	OutUeData.ComponentItems.AddZeroed(ParameterCount);
	for (int32 i = 0; i < ParameterCount; ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_items(i), OutUeData.ComponentItems[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::MultiComponentDataItem& InProtoData, FMultiComponentDataItem& OutUeData)
{
	OutUeData.ID = InProtoData.id();
	OutUeData.ComponentName = UTF8_TO_TCHAR(InProtoData.component_name().c_str());
	OutUeData.Description = UTF8_TO_TCHAR(InProtoData.description().c_str());
	OutUeData.Code = UTF8_TO_TCHAR(InProtoData.code().c_str());
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_visibility(), OutUeData.ComponentVisibility))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_id(), OutUeData.ComponentID))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_location(), OutUeData.ComponentLocation))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_rotation(), OutUeData.ComponentRotation))
		return false;
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_scale(), OutUeData.ComponentScale))
		return false;
	{
		int32 ParameterCount = InProtoData.component_parameters_size();
		OutUeData.ComponentParameters.Empty();
		OutUeData.ComponentParameters.AddZeroed(ParameterCount);
		for (int32 i = 0; i < ParameterCount; ++i)
		{
			if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_parameters(i), OutUeData.ComponentParameters[i]))
				return false;
		}
	}
	return true;
}


bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ParameterData& InProtoData, FParameterData& OutUeData)
{
	if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.data(), OutUeData.Data))
		return false;
	int32 EnumCount = InProtoData.enum_data_size();
	OutUeData.EnumData.Empty();
	OutUeData.EnumData.AddZeroed(EnumCount);
	for (int32 i = 0; i < EnumCount; ++i)
	{
		if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.enum_data(i), OutUeData.EnumData[i]))
			return false;
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ParameterTableData& InProtoData, FParameterTableData& OutUeData)
{
	OutUeData.id = UTF8_TO_TCHAR(InProtoData.id().c_str());
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());
	OutUeData.description = UTF8_TO_TCHAR(InProtoData.description().c_str());
	OutUeData.classific_id = InProtoData.classific_id();
	OutUeData.value = UTF8_TO_TCHAR(InProtoData.value().c_str());
	OutUeData.expression = UTF8_TO_TCHAR(InProtoData.expression().c_str());
	OutUeData.max_value = UTF8_TO_TCHAR(InProtoData.max_value().c_str());
	OutUeData.max_expression = UTF8_TO_TCHAR(InProtoData.max_exp().c_str());
	OutUeData.min_value = UTF8_TO_TCHAR(InProtoData.min_value().c_str());
	OutUeData.min_expression = UTF8_TO_TCHAR(InProtoData.min_exp().c_str());
	OutUeData.visibility = UTF8_TO_TCHAR(InProtoData.visibility().c_str());
	OutUeData.visibility_exp = UTF8_TO_TCHAR(InProtoData.visibility_exp().c_str());
	OutUeData.editable = UTF8_TO_TCHAR(InProtoData.editable().c_str());
	OutUeData.editable_exp = UTF8_TO_TCHAR(InProtoData.editable_exp().c_str());
	OutUeData.is_enum = InProtoData.is_enum();
	OutUeData.param_id = UTF8_TO_TCHAR(InProtoData.param_id().c_str());
	OutUeData.main_id = UTF8_TO_TCHAR(InProtoData.main_id().c_str());
	OutUeData.Special = UTF8_TO_TCHAR(InProtoData.special().c_str());
	OutUeData.Special_exp = UTF8_TO_TCHAR(InProtoData.special_exp().c_str());
	OutUeData.Must = UTF8_TO_TCHAR(InProtoData.must().c_str());
	OutUeData.Must_exp = UTF8_TO_TCHAR(InProtoData.must_exp().c_str());
	OutUeData.DefaultExpress = UTF8_TO_TCHAR(InProtoData.default_express().c_str());
	OutUeData.no_match_enum_data = InProtoData.no_match_enum();

	OutUeData.grid_value_mark = static_cast<EParamGridMarkType>(InProtoData.grid_value_mark());
	OutUeData.grid_expression = UTF8_TO_TCHAR(InProtoData.grid_expression().c_str());
	OutUeData.grid_value = UTF8_TO_TCHAR(InProtoData.grid_value().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::EnumParameterTableData& InProtoData, FEnumParameterTableData& OutUeData)
{
	OutUeData.id = UTF8_TO_TCHAR(InProtoData.id().c_str());
	OutUeData.value = UTF8_TO_TCHAR(InProtoData.value().c_str());
	OutUeData.name_for_display = UTF8_TO_TCHAR(InProtoData.name_for_display().c_str());
	OutUeData.image_for_display = UTF8_TO_TCHAR(InProtoData.image_for_display().c_str());
	OutUeData.visibility = UTF8_TO_TCHAR(InProtoData.visibility().c_str());
	OutUeData.priority = UTF8_TO_TCHAR(InProtoData.priority().c_str());
	OutUeData.main_id = UTF8_TO_TCHAR(InProtoData.main_id().c_str());
	OutUeData.visibility_exp = UTF8_TO_TCHAR(InProtoData.visibility_exp().c_str());
	OutUeData.expression = UTF8_TO_TCHAR(InProtoData.expression().c_str());
	OutUeData.force_select_condition = UTF8_TO_TCHAR(InProtoData.force_select_condition().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::FolderTableDataMsg& InProtoData, FCatalogFolderDataDB& OutUeData)
{
	OutUeData.id = UTF8_TO_TCHAR(InProtoData.id().c_str());
	OutUeData.folder_id = UTF8_TO_TCHAR(InProtoData.folder_id().c_str());
	OutUeData.folder_name = UTF8_TO_TCHAR(InProtoData.folder_name().c_str());
	OutUeData.folder_name_exp = UTF8_TO_TCHAR(InProtoData.folder_name_exp().c_str());
	OutUeData.folder_code = UTF8_TO_TCHAR(InProtoData.folder_code().c_str());
	OutUeData.folder_code_exp = UTF8_TO_TCHAR(InProtoData.folder_code_exp().c_str());
	OutUeData.folder_type = InProtoData.folder_type();
	OutUeData.thumbnail_path = UTF8_TO_TCHAR(InProtoData.thumbnail_path().c_str());
	OutUeData.parent_id = UTF8_TO_TCHAR(InProtoData.parent_id().c_str());
	OutUeData.visibility_exp = UTF8_TO_TCHAR(InProtoData.visibility_exp().c_str());
	OutUeData.visibility = InProtoData.visibility();
	OutUeData.folder_order = InProtoData.folder_order();
	OutUeData.is_new = InProtoData.is_new();
	OutUeData.is_folder = InProtoData.is_folder();
	OutUeData.backend_directory = UTF8_TO_TCHAR(InProtoData.backend_directory().c_str());
	OutUeData.front_directory = UTF8_TO_TCHAR(InProtoData.front_directory().c_str());
	OutUeData.place_rule = InProtoData.place_rule();
	OutUeData.ref_type_code = UTF8_TO_TCHAR(InProtoData.ref_type_code().c_str());
    OutUeData.ref_type = UTF8_TO_TCHAR(InProtoData.ref_type().c_str());
	OutUeData.description = UTF8_TO_TCHAR(InProtoData.description().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::ComponentFileData& InProtoData, FComponentFileData& OutUeData)
{
	OutUeData.file_data_path = UTF8_TO_TCHAR(InProtoData.file_data_path().c_str());
	OutUeData.depend_files = UTF8_TO_TCHAR(InProtoData.depend_files().c_str());
	OutUeData.component_datas.Empty();
	for (int32 i = 0; i < InProtoData.component_datas_size(); ++i)
	{
		FSingleComponentItem ComponentData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_datas(i), ComponentData);
		OutUeData.component_datas.Add(ComponentData);
	}
	OutUeData.file_ref_uuid = UTF8_TO_TCHAR(InProtoData.file_ref_uuid().c_str());
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::RefMultiCompDataItem& InProtoData, FRefToFileComponentData& OutUeData)
{
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_id(), OutUeData.component_id);
	OutUeData.component_name = UTF8_TO_TCHAR(InProtoData.component_name().c_str());
	OutUeData.component_description = UTF8_TO_TCHAR(InProtoData.description().c_str());
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_code(), OutUeData.component_code);
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_visibility(), OutUeData.component_visibility);
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_location(), OutUeData.component_location);
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_rotation(), OutUeData.component_rotation);
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_scale(), OutUeData.component_scale);
	OutUeData.component_params.Empty();
	for (int32 i = 0; i < InProtoData.component_parameters_size(); ++i)
	{
		FParameterData ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.component_parameters(i), ParameterData);
		OutUeData.component_params.Add(ParameterData);
	}
	OutUeData.component_type = InProtoData.component_type();
	OutUeData.component_ref_uuid = UTF8_TO_TCHAR(InProtoData.component_ref_uuid().c_str());
	OutUeData.component_name_exp = UTF8_TO_TCHAR(InProtoData.component_name_exp().c_str());;
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::FolderRefData& InProtoData, FRefToLocalFileData& OutUeData)
{
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.folder_msg(), OutUeData.FolderDBData);
	OutUeData.ParamDatas.Empty();
	for (int32 i = 0; i < InProtoData.param_data_size(); ++i)
	{
		FParameterData ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.param_data(i), ParameterData);
		OutUeData.ParamDatas.Add(ParameterData);
	}
	OutUeData.ComponentDatas.Empty();
	for (int32 i = 0; i < InProtoData.ref_comps_data_size(); ++i)
	{
		FRefToFileComponentData RefCompData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.ref_comps_data(i), RefCompData);
		OutUeData.ComponentDatas.Add(RefCompData);
	}
	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.file_data(), OutUeData.FileData);

	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.mat_model_depend_files(), OutUeData.MatModelDependFiles);

	UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.place_rule_custom_data(), OutUeData.PlaceRuleCustomData);

	OutUeData.AssociateListData.Empty();
	for (int32 i = 0; i < InProtoData.associate_list_data_size(); ++i)
	{
		FAssociateListData Temp;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.associate_list_data(i), Temp);
		OutUeData.AssociateListData.Add(Temp);
	}
	
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleInfoData& InProtoData, FRefToStyleData& OutUeData)
{
	OutUeData.style_id = UTF8_TO_TCHAR(InProtoData.style_id().c_str());
	OutUeData.style_code = UTF8_TO_TCHAR(InProtoData.style_code().c_str());
	OutUeData.style_craft = UTF8_TO_TCHAR(InProtoData.style_craft().c_str());
	OutUeData.style_description = UTF8_TO_TCHAR(InProtoData.style_description().c_str());
	OutUeData.style_thumbnail_path = UTF8_TO_TCHAR(InProtoData.style_thumbnail_path().c_str());
	OutUeData.style_thumbnail_md5 = UTF8_TO_TCHAR(InProtoData.style_thumbnail_md5().c_str());
	OutUeData.style_sort_order = InProtoData.style_sort_order();
	OutUeData.is_checked = InProtoData.is_check();
	OutUeData.style_group = static_cast<ECSStyleGeoup>(InProtoData.style_group());
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleContentData& InProtoData, FRefToContentData& OutUeData)
{
	OutUeData.content_id = UTF8_TO_TCHAR(InProtoData.content_id().c_str());
	OutUeData.content_name = UTF8_TO_TCHAR(InProtoData.content_name().c_str());
	OutUeData.content_sort_order = InProtoData.content_sort_order();
    OutUeData.content_relation_code = UTF8_TO_TCHAR(InProtoData.content_relation_code().c_str());
    OutUeData.content_base_type = InProtoData.content_base_type();

	for(int32 i = 0; i < InProtoData.option_datas_size(); ++i)
	{
		FRefToOptionData OptionData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.option_datas(i), OptionData);
		OutUeData.option_datas.Add(OptionData);
	}

	for(int32 i = 0; i < InProtoData.style_option_checks_size(); ++i)
	{
		FString StyleID;
		FOptionCheckArr OptionCheck;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.style_option_checks(i), StyleID, OptionCheck);
		OutUeData.style_option_checks.Add(StyleID, OptionCheck);
	}

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionData& InProtoData, FRefToOptionData& OutUeData)
{
	OutUeData.option_id = UTF8_TO_TCHAR(InProtoData.option_id().c_str());
	OutUeData.option_code = UTF8_TO_TCHAR(InProtoData.option_code().c_str());
	OutUeData.option_description = UTF8_TO_TCHAR(InProtoData.option_description().c_str());
	OutUeData.option_visibility = UTF8_TO_TCHAR(InProtoData.option_visibility().c_str());
	OutUeData.option_visibility_exp = UTF8_TO_TCHAR(InProtoData.option_visibility_exp().c_str());
	OutUeData.option_sort_order = InProtoData.option_sort_order();
	OutUeData.option_params.Empty();
	for(int32 i = 0; i < InProtoData.option_params_size(); ++i)
	{
		FParameterData ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.option_params(i), ParameterData);
		OutUeData.option_params.Add(ParameterData);
	}

	OutUeData.option_thumbnail_url = UTF8_TO_TCHAR(InProtoData.option_thumbnail_url().c_str());
	OutUeData.option_content_id = UTF8_TO_TCHAR(InProtoData.option_content_id().c_str());
	OutUeData.option_data_source = InProtoData.option_data_source();
	OutUeData.option_custom_id = UTF8_TO_TCHAR(InProtoData.option_custom_id().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionCheckData& InProtoData, FRefToOptionCheck& OutUeData)
{
	OutUeData.option_id = UTF8_TO_TCHAR(InProtoData.option_id().c_str());
	OutUeData.content_id = UTF8_TO_TCHAR(InProtoData.content_id().c_str());
	OutUeData.style_id = UTF8_TO_TCHAR(InProtoData.style_id().c_str());
	OutUeData.is_prime = InProtoData.is_prime();
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionCheckArrData& InProtoData, FString& OutStyleID, FOptionCheckArr& OutUeData)
{
	OutStyleID = UTF8_TO_TCHAR(InProtoData.style_id().c_str());
	for(int32 i = 0; i < InProtoData.option_checks_size(); ++i)
	{
		FRefToOptionCheck OptionCheck;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.option_checks(i), OptionCheck);
		OutUeData.option_checks.Add(OptionCheck);
	}
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::StyleRefData& InProtoData, FRefToStyleFile& OutUeData)
{
	OutUeData.style_datas.Empty();
	for (int32 i = 0; i < InProtoData.style_datas_size(); ++i)
	{
		FRefToStyleData StyleData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.style_datas(i), StyleData);
		OutUeData.style_datas.Add(StyleData);
	}
	OutUeData.content_datas.Empty();
	for (int32 i = 0; i < InProtoData.content_datas_size(); ++i)
	{
		FRefToContentData ContentData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.content_datas(i), ContentData);
		OutUeData.content_datas.Add(ContentData);
	}
	/*OutUeData.option_datas.Empty();
	for (int32 i = 0; i < InProtoData.option_datas_size(); ++i)
	{
		FRefToOptionData OptionData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.option_datas(i), OptionData);
		OutUeData.option_datas.Add(OptionData);
	}
	OutUeData.option_checks.Empty();
	for (int32 i = 0; i < InProtoData.option_check_datas_size(); ++i)
	{
		FRefToOptionCheck OptionCheck;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.option_check_datas(i), OptionCheck);
		OutUeData.option_checks.Add(OptionCheck);
	}*/
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::AssociateData& InProtoData, FRefAssociateData& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());
	OutUeData.meetCondition = UTF8_TO_TCHAR(InProtoData.meetcondition().c_str());
	OutUeData.type = InProtoData.type();
	OutUeData.isMate = InProtoData.ismate();
	OutUeData.dictValue = UTF8_TO_TCHAR(InProtoData.dictvalue().c_str());
	OutUeData.belongId = UTF8_TO_TCHAR(InProtoData.belongid().c_str());
	OutUeData.associationId = InProtoData.associationid();
	OutUeData.bkId = UTF8_TO_TCHAR(InProtoData.bkid().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::AssociateListData& InProtoData, FAssociateListData& OutUeData)
{
	OutUeData.correlationType = UTF8_TO_TCHAR(InProtoData.correlationtype().c_str());
	OutUeData.correlationTypeName = UTF8_TO_TCHAR(InProtoData.correlationtypename().c_str());
	OutUeData.isMate = InProtoData.ismate();

	for (int32 i = 0; i < InProtoData.bkassociationdataillist_size(); ++i)
	{
		FRefAssociateData Data;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.bkassociationdataillist(i), Data);
		OutUeData.bkAssociationDetailList.Add(Data);
	}

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::RefPlaceRuleCustom& InProtoData, FRefPlaceRuleCustomData& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());
	OutUeData.isEnable = InProtoData.isenable();
	OutUeData.isConfig = InProtoData.isconfig();
	//left
	OutUeData.isLeft = InProtoData.isleft();
	OutUeData.leftConfigExpression = UTF8_TO_TCHAR(InProtoData.leftconfigexpression().c_str());
	OutUeData.leftConfig = UTF8_TO_TCHAR(InProtoData.leftconfig().c_str());
	//right
	OutUeData.isRight = InProtoData.isright();
	OutUeData.rightConfigExpression = UTF8_TO_TCHAR(InProtoData.rightconfigexpression().c_str());
	OutUeData.rightConfig = UTF8_TO_TCHAR(InProtoData.rightconfig().c_str());
	//upper
	OutUeData.isUpper = InProtoData.isupper();
	OutUeData.upperConfigExpression = UTF8_TO_TCHAR(InProtoData.upperconfigexpression().c_str());
	OutUeData.upperConfig = UTF8_TO_TCHAR(InProtoData.upperconfig().c_str());
	//down
	OutUeData.isDown = InProtoData.isdown();
	OutUeData.downConfigExpression = UTF8_TO_TCHAR(InProtoData.downconfigexpression().c_str());
	OutUeData.downConfig = UTF8_TO_TCHAR(InProtoData.downconfig().c_str());
	//front
	OutUeData.isFront = InProtoData.isfront();
	OutUeData.frontConfigExpression = UTF8_TO_TCHAR(InProtoData.frontconfigexpression().c_str());
	OutUeData.frontConfig = UTF8_TO_TCHAR(InProtoData.frontconfig().c_str());
	//after
	OutUeData.isAfter = InProtoData.isafter();
	OutUeData.afterConfigExpression = UTF8_TO_TCHAR(InProtoData.afterconfigexpression().c_str());
	OutUeData.afterConfig = UTF8_TO_TCHAR(InProtoData.afterconfig().c_str());

	OutUeData.createdBy = UTF8_TO_TCHAR(InProtoData.createdby().c_str());
	OutUeData.createdTime = UTF8_TO_TCHAR(InProtoData.createdtime().c_str());
	OutUeData.updatedBy = UTF8_TO_TCHAR(InProtoData.updatedby().c_str());
	OutUeData.updatedTime = UTF8_TO_TCHAR(InProtoData.updatedtime().c_str());
	OutUeData.delFlag = InProtoData.delflag();
	OutUeData.IsUserCustom = InProtoData.isusercustom();
	
	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::RefParameterData& InProtoData, FRefParamData& OutUeData)
{
	OutUeData.ParamGroups.Empty();
	for (int32 i = 0; i < InProtoData.ref_param_groups_size(); ++i)
	{
		FParameterGroupTableData ParamGroupData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.ref_param_groups(i), ParamGroupData);
		OutUeData.ParamGroups.Add(ParamGroupData);
	}

	OutUeData.ParamDatas.Empty();
	for (int32 i = 0; i < InProtoData.ref_params_size(); ++i)
	{
		FParameterData ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.ref_params(i), ParameterData);
		OutUeData.ParamDatas.Add(ParameterData);
	}

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::RefParamGroupData& InProtoData, FParameterGroupTableData& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.group_name = UTF8_TO_TCHAR(InProtoData.group_name().c_str());
	OutUeData.description = UTF8_TO_TCHAR(InProtoData.description().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CSMatMapData& InProtoData, FCSMapData& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.materialId = InProtoData.materialid();
	OutUeData.adminId = InProtoData.adminid();
	OutUeData.md5 = UTF8_TO_TCHAR(InProtoData.md5().c_str());
	OutUeData.attrId = InProtoData.attrid();
	OutUeData.attrName = UTF8_TO_TCHAR(InProtoData.attrname().c_str());
	OutUeData.materialValue = UTF8_TO_TCHAR(InProtoData.materialvalue().c_str());
	OutUeData.delFlag = InProtoData.delflag();
	OutUeData.createdBy = UTF8_TO_TCHAR(InProtoData.createdby().c_str());
	OutUeData.createdTime = UTF8_TO_TCHAR(InProtoData.createdtime().c_str());
	OutUeData.updatedBy = UTF8_TO_TCHAR(InProtoData.updatedby().c_str());
	OutUeData.updatedTime = UTF8_TO_TCHAR(InProtoData.updatedtime().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CSMatModelLableData& InProtoData, FCSModelMatLable& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CSModelInfoData& InProtoData, FCSModelInfo& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.type = InProtoData.type();
	OutUeData.code = UTF8_TO_TCHAR(InProtoData.code().c_str());
	OutUeData.md5 = UTF8_TO_TCHAR(InProtoData.md5().c_str());
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());
	OutUeData.fbxFilePath = UTF8_TO_TCHAR(InProtoData.fbxfilepath().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::CSMatModelData& InProtoData, FCSModelMatData& OutUeData)
{
	OutUeData.id = InProtoData.id();
	OutUeData.name = UTF8_TO_TCHAR(InProtoData.name().c_str());
	OutUeData.code = UTF8_TO_TCHAR(InProtoData.code().c_str());
	OutUeData.attrCate = InProtoData.attrcate();
	OutUeData.refPath = UTF8_TO_TCHAR(InProtoData.refpath().c_str());
	OutUeData.pakFilePath = UTF8_TO_TCHAR(InProtoData.pakfilepath().c_str());
	OutUeData.isSelf = InProtoData.isself();
	OutUeData.productImg = UTF8_TO_TCHAR(InProtoData.productimg().c_str());
	OutUeData.brand = UTF8_TO_TCHAR(InProtoData.brand().c_str());
	OutUeData.cateId = InProtoData.cateid();
	OutUeData.cateName = UTF8_TO_TCHAR(InProtoData.catename().c_str());
	OutUeData.materialCate = UTF8_TO_TCHAR(InProtoData.materialcate().c_str());
	OutUeData.mapsImg = UTF8_TO_TCHAR(InProtoData.mapsimg().c_str());
	OutUeData.price = InProtoData.price();
	OutUeData.depth = UTF8_TO_TCHAR(InProtoData.depth().c_str());
	OutUeData.width = UTF8_TO_TCHAR(InProtoData.width().c_str());
	OutUeData.height = UTF8_TO_TCHAR(InProtoData.height().c_str());
	OutUeData.placementRules = UTF8_TO_TCHAR(InProtoData.placementrules().c_str());
	OutUeData.dictGroupValue = UTF8_TO_TCHAR(InProtoData.dictgroupvalue().c_str());
	OutUeData.dictValue = UTF8_TO_TCHAR(InProtoData.dictvalue().c_str());
	OutUeData.shader = UTF8_TO_TCHAR(InProtoData.shader().c_str());
	OutUeData.vrscene = UTF8_TO_TCHAR(InProtoData.vrscene().c_str());
	OutUeData.ue5Param = UTF8_TO_TCHAR(InProtoData.ue5param().c_str());
	OutUeData.templateId = InProtoData.templateid();
	OutUeData.renderingId = InProtoData.renderingid();
	OutUeData.folderId = UTF8_TO_TCHAR(InProtoData.folderid().c_str());
	OutUeData.mark = UTF8_TO_TCHAR(InProtoData.mark().c_str());
	OutUeData.delFlag = InProtoData.delflag();
	OutUeData.md5 = UTF8_TO_TCHAR(InProtoData.md5().c_str());
	OutUeData.createdBy = UTF8_TO_TCHAR(InProtoData.createdby().c_str());
	OutUeData.createdTime = UTF8_TO_TCHAR(InProtoData.createdtime().c_str());
	OutUeData.updatedBy = UTF8_TO_TCHAR(InProtoData.updatedby().c_str());
	OutUeData.updatedTime = UTF8_TO_TCHAR(InProtoData.updatedtime().c_str());

	OutUeData.manageMapsList.Empty();
	for (int32 i = 0; i < InProtoData.managemapslist_size(); ++i)
	{
		FCSMapData ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.managemapslist(i), ParameterData);
		OutUeData.manageMapsList.Add(ParameterData);
	}

	OutUeData.labelList.Empty();
	for (int32 i = 0; i < InProtoData.labellist_size(); ++i)
	{
		FCSModelMatLable ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.labellist(i), ParameterData);
		OutUeData.labelList.Add(ParameterData);
	}

	OutUeData.modelList.Empty();
	for (int32 i = 0; i < InProtoData.modellist_size(); ++i)
	{
		FCSModelInfo ParameterData;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.modellist(i), ParameterData);
		OutUeData.modelList.Add(ParameterData);
	}


	OutUeData.folderCode = UTF8_TO_TCHAR(InProtoData.foldercode().c_str());

	return true;
}

bool UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(const catalog_studio_message::DependFileData& InProtoData, FDependFileData& OutUeData)
{
	OutUeData.ModelDependFiles.Empty();
	for (int32 i = 0; i < InProtoData.modeldependfiles_size(); ++i)
	{
		FCSModelMatData Data;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.modeldependfiles(i), Data);
		OutUeData.ModelDependFiles.Add(Data);
	}

	OutUeData.MatDependFiles.Empty();
	for (int32 i = 0; i < InProtoData.matdependfiles_size(); ++i)
	{
		FCSModelMatData Data;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.matdependfiles(i), Data);
		OutUeData.MatDependFiles.Add(Data);
	}

	OutUeData.ModelDependFolderID.Empty();
	for (int32 i = 0; i < InProtoData.modeldependfolderid_size(); ++i)
	{
		FExpressionValuePair Data;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.modeldependfolderid(i), Data);
		OutUeData.ModelDependFolderID.Add(Data);
	}

	OutUeData.MatDependFolderID.Empty();
	for (int32 i = 0; i < InProtoData.matdependfolderid_size(); ++i)
	{
		FExpressionValuePair Data;
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(InProtoData.matdependfolderid(i), Data);
		OutUeData.MatDependFolderID.Add(Data);
	}

	return true;
}
