// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DataCenter/LoginData/LoginData.h"
#include "ProtobufOperator/ProtoDatas/ApplicationConfig.pb.h"

#include "ProtobufOperator/ProtoDatas/ExpressionValuePair.pb.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"

#include "ProtobufOperator/ProtoDatas/CrossSectionData.pb.h"
#include "DataCenter/ComponentData/CrossSectionDataDefine.h"

#include "ProtobufOperator/ProtoDatas/CustomMeshInfo.pb.h"

#include "ProtobufOperator/ProtoDatas/ImportMeshSection.pb.h"

#include "ProtobufOperator/ProtoDatas/LocationProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/RotationProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/ScaleProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionCutOutOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionDrawOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionLoftOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionOperationOrder.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionShiftingOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionZoomOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SingleComponentItem.pb.h"
#include "ProtobufOperator/ProtoDatas/SingleComponentProperty.pb.h"
#include "ProtobufOperator/ProtoDatas/MultiComponentData.pb.h"
#include "ProtobufOperator/ProtoDatas/EnumParameterTableData.pb.h"
#include "ProtobufOperator/ProtoDatas/ParameterData.pb.h"
#include "ProtobufOperator/ProtoDatas/ParameterTableData.pb.h"

#include "DataCenter/ComponentData/ComponentPropertyData.h"
#include "DataCenter/ComponentData/SingleComponentDataDefine.h"
#include "DataCenter/ComponentData/MultiComponentData.h"

#include "ProtobufOperator/ProtoDatas/CatalogFolderTableData.pb.h"
#include "ProtobufOperator/ProtoDatas/FolderRef.pb.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "ProtobufOperator/ProtoDatas/RefFileMultiComponentItem.pb.h"

#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "ProtobufOperator/ProtoDatas/StyleInfo.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleContent.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleOption.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleOptionCheck.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleRef.pb.h"
#include "ProtobufOperator/ProtoDatas/RefParameterData.pb.h"
#include "ProtobufOperator/ProtoDatas/RefParamGroup.pb.h"

#include "ProtobufOperator/ProtoDatas/AssociateData.pb.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"

#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"

#include "ProtobufOperator/ProtoDatas/CSMatMap.pb.h"
#include "ProtobufOperator/ProtoDatas/CSMatModel.pb.h"
#include "ProtobufOperator/ProtoDatas/CSMatModelLable.pb.h"
#include "ProtobufOperator/ProtoDatas/CSModelInfo.pb.h"
#include "ProtobufOperator/ProtoDatas/RefPlaceRuleCustom.pb.h"
#include "DataCenter/RefFile/Data/RefToPlaceDataLibrary.h"

#include "ConvertProtobufToUeLibrary.generated.h"


/**
 *
 */
UCLASS()
class DESIGNSTATION_API UConvertProtobufToUeLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	static bool ConvertProtoDataToUeData(const ApplicationConfig& InProtoData, FLoginRemeberInfo& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::Vector& InProtoData, FVector& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::Vector2D& InProtoData, FVector2D& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::ExpressionValuePair& InProtoData, FExpressionValuePair& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::GeomtryCubeProperty& InProtoData, FGeomtryCubeProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::GeomtryEllipsePlanProperty& InProtoData, FGeomtryEllipsePlanProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::GeomtryRectanglePlanProperty& InProtoData, FGeomtryRectanglePlanProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::GeomtryLineProperty& InProtoData, FGeomtryLineProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::GeomtryPointProperty& InProtoData, FGeomtryPointProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::CrossSectionData& InProtoData, FCrossSectionData& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::CustomMeshInfo& InProtoData, FPMCSection& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::ImportMeshSection& InProtoData, FImportMeshSection& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::LocationProperty& InProtoData, FLocationProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::RotationProperty& InProtoData, FRotationProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::ScaleProperty& InProtoData, FScaleProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionCutOutOperation& InProtoData, FSectionCutOutOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionDrawOperation& InProtoData, FSectionDrawOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionLoftOperation& InProtoData, FSectionLoftOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionOperationOrder& InProtoData, FSectionOperationOrder& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionShiftingOperation& InProtoData, FSectionShiftingOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionZoomOperation& InProtoData, FSectionZoomOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SectionOperation& InProtoData, FSectionOperation& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SingleComponentItem& InProtoData, FSingleComponentItem& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::SingleComponentProperty& InProtoData, FSingleComponentProperty& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::MultiComponentData& InProtoData, FMultiComponentData& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::MultiComponentDataItem& InProtoData, FMultiComponentDataItem& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::ParameterData& InProtoData, FParameterData& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::ParameterTableData& InProtoData, FParameterTableData& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::EnumParameterTableData& InProtoData, FEnumParameterTableData& OutUeData);

	// RefFile
	static bool ConvertProtoDataToUeData(const catalog_studio_message::FolderTableDataMsg& InProtoData, FCatalogFolderDataDB& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::ComponentFileData& InProtoData, FComponentFileData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::RefMultiCompDataItem& InProtoData, FRefToFileComponentData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::FolderRefData& InProtoData, FRefToLocalFileData& OutUeData);

	static bool ConvertProtoDataToUeData(const catalog_studio_message::RefPlaceRuleCustom& InProtoData, FRefPlaceRuleCustomData& OutUeData);

	//RefStyle
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleInfoData& InProtoData, FRefToStyleData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleContentData& InProtoData, FRefToContentData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionData& InProtoData, FRefToOptionData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionCheckData& InProtoData, FRefToOptionCheck& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleOptionCheckArrData& InProtoData, FString& OutStyleID, FOptionCheckArr& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::StyleRefData& InProtoData, FRefToStyleFile& OutUeData);

	//associate
	static bool ConvertProtoDataToUeData(const catalog_studio_message::AssociateData& InProtoData, FRefAssociateData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::AssociateListData& InProtoData, FAssociateListData& OutUeData);

	
	//params
	static bool ConvertProtoDataToUeData(const catalog_studio_message::RefParameterData& InProtoData, FRefParamData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::RefParamGroupData& InProtoData, FParameterGroupTableData& OutUeData);


	//web pak model / mat data
	static bool ConvertProtoDataToUeData(const catalog_studio_message::CSMatMapData& InProtoData, FCSMapData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::CSMatModelLableData& InProtoData, FCSModelMatLable& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::CSModelInfoData& InProtoData, FCSModelInfo& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::CSMatModelData& InProtoData, FCSModelMatData& OutUeData);
	static bool ConvertProtoDataToUeData(const catalog_studio_message::DependFileData& InProtoData, FDependFileData& OutUeData);

};
