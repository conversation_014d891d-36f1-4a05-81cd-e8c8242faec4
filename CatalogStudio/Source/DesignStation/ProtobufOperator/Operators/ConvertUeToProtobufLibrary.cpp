// Fill out your copyright notice in the Description page of Project Settings.

#include "ConvertUeToProtobufLibrary.h"



bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FLoginRemeberInfo& InUeData, ApplicationConfig& OutProtoData)
{
	OutProtoData.set_last_user_name(TCHAR_TO_UTF8(*InUeData.UserName));
	OutProtoData.set_password(TCHAR_TO_UTF8(*InUeData.Password));
	OutProtoData.set_remeber_password(InUeData.RemeberMe);
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FExpressionValuePair& InUeData, catalog_studio_message::ExpressionValuePair& OutProtoData)
{
	OutProtoData.set_expression(TCHAR_TO_UTF8(*InUeData.Expression));
	OutProtoData.set_value(TCHAR_TO_UTF8(*InUeData.Value));
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FGeomtryCubeProperty& InUeData, catalog_studio_message::GeomtryCubeProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationX, *OutProtoData.mutable_start_location_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationY, *OutProtoData.mutable_start_location_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationZ, *OutProtoData.mutable_start_location_z());
	bool R4 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationX, *OutProtoData.mutable_end_location_x());
	bool R5 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationY, *OutProtoData.mutable_end_location_y());
	bool R6 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationZ, *OutProtoData.mutable_end_location_z());

	return R1 && R2 && R3 && R4 && R5 && R6;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FGeomtryEllipsePlanProperty& InUeData, catalog_studio_message::GeomtryEllipsePlanProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.CenterLocationX, *OutProtoData.mutable_center_location_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.CenterLocationY, *OutProtoData.mutable_center_location_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.CenterLocationZ, *OutProtoData.mutable_center_location_z());
	bool R4 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.InterpPointCountData, *OutProtoData.mutable_interp_point_count_data());
	bool R5 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LongRadiusData, *OutProtoData.mutable_long_radius_data());
	bool R6 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ShortRadiusData, *OutProtoData.mutable_short_radius_data());
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));
	return R1 && R2 && R3 && R4 && R5 && R6;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FGeomtryRectanglePlanProperty& InUeData, catalog_studio_message::GeomtryRectanglePlanProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationX, *OutProtoData.mutable_start_location_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationY, *OutProtoData.mutable_start_location_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocationZ, *OutProtoData.mutable_start_location_z());
	bool R4 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationX, *OutProtoData.mutable_end_location_x());
	bool R5 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationY, *OutProtoData.mutable_end_location_y());
	bool R6 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocationZ, *OutProtoData.mutable_end_location_z());
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));

	return R1 && R2 && R3 && R4 && R5 && R6;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FGeomtryPointProperty& InUeData, catalog_studio_message::GeomtryPointProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationX, *OutProtoData.mutable_location_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationY, *OutProtoData.mutable_location_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationZ, *OutProtoData.mutable_location_z());
	bool R4 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.PrePointLocation, *OutProtoData.mutable_pre_point_location());

	OutProtoData.set_id(InUeData.ID);
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PositionType));

	return R1 && R2 && R3 && R4;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FGeomtryLineProperty& InUeData, catalog_studio_message::GeomtryLineProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.StartLocation, *OutProtoData.mutable_start_location());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.EndLocation, *OutProtoData.mutable_end_location());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.RadiusOrHeightData, *OutProtoData.mutable_radius_or_height_data());
	bool R4 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.InterpPointCountData, *OutProtoData.mutable_interp_point_count_data());

	OutProtoData.set_big_arc(InUeData.BigArc);
	OutProtoData.set_id(InUeData.ID);
	OutProtoData.set_line_type(static_cast<catalog_studio_message::LineType>(InUeData.LineType));
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));

	return R1 && R2 && R3 && R4;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCrossSectionData& InUeData, catalog_studio_message::CrossSectionData& OutProtoData)
{
	OutProtoData.set_section_type(static_cast<catalog_studio_message::SectionType>(InUeData.SectionType));
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));
	if (ESectionType::ECustomPlan == InUeData.SectionType)
	{
		for (auto& iter : InUeData.Lines)
		{
			catalog_studio_message::GeomtryLineProperty* Line = OutProtoData.add_lines();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Line))
				return false;
		}
		for (auto& iter : InUeData.Points)
		{
			catalog_studio_message::GeomtryPointProperty* Point = OutProtoData.add_points();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Point))
				return false;
		}
		return true;
	}
	else if (ESectionType::ECube == InUeData.SectionType)
	{
		return UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Cube, *OutProtoData.mutable_cube());
	}
	else if (ESectionType::EEllipse == InUeData.SectionType)
	{
		return UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Ellipse, *OutProtoData.mutable_ellipse());
	}
	else if (ESectionType::ERectangle == InUeData.SectionType)
	{
		return UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Rectangle, *OutProtoData.mutable_rectangle());
	}
	return false;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FPMCSection& InUeData, catalog_studio_message::CustomMeshInfo& OutProtoData)
{
	for (auto& iter : InUeData.Normals)
	{
		catalog_studio_message::Vector* fvec = OutProtoData.add_normals();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *fvec))
			return false;
	}

	for (auto& iter : InUeData.Vertexes)
	{
		catalog_studio_message::Vector* fvec = OutProtoData.add_vertex_locations();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *fvec))
			return false;
	}

	for (auto& iter : InUeData.UV)
	{
		catalog_studio_message::Vector2D* fvec2 = OutProtoData.add_vertex_uv();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *fvec2))
			return false;
	}

	for (auto& iter : InUeData.Triangles)
	{
		OutProtoData.add_wedge_indices(iter);
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FImportMeshSection& InUeData, catalog_studio_message::ImportMeshSection& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.MaterialId, *OutProtoData.mutable_material_id());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.SectionMesh, *OutProtoData.mutable_section_mesh());

	return R1 && R2;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FLocationProperty& InUeData, catalog_studio_message::LocationProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationX, *OutProtoData.mutable_location_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationY, *OutProtoData.mutable_location_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LocationZ, *OutProtoData.mutable_location_z());

	return R1 && R2 && R3;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRotationProperty& InUeData, catalog_studio_message::RotationProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Pitch, *OutProtoData.mutable_pitch());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Roll, *OutProtoData.mutable_roll());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Yaw, *OutProtoData.mutable_yaw());

	return R1 && R2 && R3;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FScaleProperty& InUeData, catalog_studio_message::ScaleProperty& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.X, *OutProtoData.mutable_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Y, *OutProtoData.mutable_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Z, *OutProtoData.mutable_z());

	return R1 && R2 && R3;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionCutOutOperation& InUeData, catalog_studio_message::SectionCutOutOperation& OutProtoData)
{
	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.CutOutValue, *OutProtoData.mutable_cut_out_value());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Ellipse, *OutProtoData.mutable_ellipse());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Rectangle, *OutProtoData.mutable_rectangle());

	catalog_studio_message::GeomtryPointProperty* Point;
	catalog_studio_message::GeomtryLineProperty* Line;

	for (auto& iter : InUeData.Points)
	{
		Point = OutProtoData.add_points();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Point);
	}

	for (auto& iter : InUeData.Lines)
	{
		Line = OutProtoData.add_lines();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Line);
	}

	OutProtoData.set_id(InUeData.ID);
	OutProtoData.set_section_type(static_cast<catalog_studio_message::SectionType>(InUeData.SectionType));
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));

	return R1 && R2 && R3;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionDrawOperation& InUeData, catalog_studio_message::SectionDrawOperation& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);

	bool R1 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.DrawOffsetX, *OutProtoData.mutable_draw_offset_x());
	bool R2 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.DrawOffsetY, *OutProtoData.mutable_draw_offset_y());
	bool R3 = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.DrawOffsetZ, *OutProtoData.mutable_draw_offset_z());

	return R1 && R2 && R3;

}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionLoftOperation& InUeData, catalog_studio_message::SectionLoftOperation& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);
	OutProtoData.set_plan_belongs(static_cast<catalog_studio_message::PlanPolygonBelongs>(InUeData.PlanBelongs));

	catalog_studio_message::GeomtryLineProperty* Line;
	catalog_studio_message::GeomtryPointProperty* Point;


	for (auto& iter : InUeData.Lines)
	{
		Line = OutProtoData.add_lines();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Line);

	}

	for (auto& iter : InUeData.Points)
	{
		Point = OutProtoData.add_points();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Point);
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionShiftingOperation& InUeData, catalog_studio_message::SectionShiftingOperation& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);

	catalog_studio_message::ExpressionValuePair* EVPair;

	for (auto& iter : InUeData.ShiftValue)
	{
		EVPair = OutProtoData.add_shift_value();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *EVPair);
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionZoomOperation& InUeData, catalog_studio_message::SectionZoomOperation& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);

	catalog_studio_message::ExpressionValuePair* EVPair;

	for (auto& iter : InUeData.ZoomValue)
	{
		EVPair = OutProtoData.add_zoom_value();
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *EVPair);
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionOperationOrder& InUeData, catalog_studio_message::SectionOperationOrder& OutProtoData)
{
	OutProtoData.set_index(InUeData.Index);
	OutProtoData.set_operator_type(static_cast<catalog_studio_message::SectionOperationType>(InUeData.OperatorType));
	UE_LOG(LogTemp, Log, TEXT("InUeData.Index %d InUeData.OperatorType %d"), OutProtoData.index(), OutProtoData.operator_type());
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSectionOperation& InUeData, catalog_studio_message::SectionOperation& OutProtoData)
{
	for (auto& iter : InUeData.OperatorOrder)
	{
		catalog_studio_message::SectionOperationOrder* Order = OutProtoData.add_operator_order();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(iter, *Order))
			return false;
		if (ESectionOperationType::EDrawSection == iter.OperatorType)
		{
			catalog_studio_message::SectionDrawOperation* Draw = OutProtoData.add_draw_operations();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.DrawOperations[iter.Index], *Draw))
				return false;
		}
		else if (ESectionOperationType::EShiftSection == iter.OperatorType)
		{
			catalog_studio_message::SectionShiftingOperation* Shift = OutProtoData.add_shift_operations();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ShiftOperations[iter.Index], *Shift))
				return false;
		}
		else if (ESectionOperationType::EZoomSection == iter.OperatorType)
		{
			catalog_studio_message::SectionZoomOperation* Zoom = OutProtoData.add_zoom_operations();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ZoomOperations[iter.Index], *Zoom))
				return false;
		}
		else if (ESectionOperationType::ECutoutSection == iter.OperatorType)
		{
			catalog_studio_message::SectionCutOutOperation* Cutout = OutProtoData.add_cutout_operations();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.CutoutOperations[iter.Index], *Cutout))
				return false;
		}
		else if (ESectionOperationType::ELoftingSection == iter.OperatorType)
		{
			catalog_studio_message::SectionLoftOperation* Loft = OutProtoData.mutable_lofting_operation();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.LoftingOperation, *Loft))
				return false;
		}
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FVector& InUeData, catalog_studio_message::Vector& OutProtoData)
{
	OutProtoData.set_x(InUeData.X);
	OutProtoData.set_y(InUeData.Y);
	OutProtoData.set_z(InUeData.Z);

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FVector2D& InUeData, catalog_studio_message::Vector2D& OutProtoData)
{
	OutProtoData.set_x(InUeData.X);
	OutProtoData.set_y(InUeData.Y);
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSingleComponentItem& InUeData, catalog_studio_message::SingleComponentItem& OutProtoData)
{
	OutProtoData.set_section_name(TCHAR_TO_UTF8(*InUeData.SectionName));
	OutProtoData.set_thumbnail_path(TCHAR_TO_UTF8(*InUeData.ThumbnailPath));
	OutProtoData.set_component_source(static_cast<catalog_studio_message::SingleComponentSource>(InUeData.ComponentSource));

	catalog_studio_message::ExpressionValuePair* _VisibleParam = OutProtoData.mutable_visible_param();
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.VisibleParam, *_VisibleParam))
		return false;

	if (ESingleComponentSource::ECustom == InUeData.ComponentSource)
	{
		catalog_studio_message::CrossSectionData* _CrossSectionData = OutProtoData.mutable_operator_section();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.OperatorSection, *_CrossSectionData))
			return false;
		catalog_studio_message::SectionOperation* _SectionOperation = OutProtoData.mutable_section_operation();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.SectionOperation, *_SectionOperation))
			return false;
		catalog_studio_message::ExpressionValuePair* _ComponentMaterial = OutProtoData.mutable_component_material();
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentMaterial, *_ComponentMaterial))
			return false;

	}
	else if (ESingleComponentSource::EImportFBX == InUeData.ComponentSource)
	{
		for (auto& Iter : InUeData.ImportMesh)
		{
			catalog_studio_message::ImportMeshSection* _ImportMesh = OutProtoData.add_import_mesh();
			if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *_ImportMesh))
				return false;
		}
	}
	else if (ESingleComponentSource::EImportPAK == InUeData.ComponentSource)
	{
		OutProtoData.set_pakrefpath(TCHAR_TO_UTF8(*InUeData.PakRefPath));
		OutProtoData.set_pakrelativepath(TCHAR_TO_UTF8(*InUeData.PakRelativeFilePath));
	}
	catalog_studio_message::LocationProperty* _Location = OutProtoData.mutable_single_component_location();
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.SingleComponentLocation, *_Location))
		return false;
	catalog_studio_message::RotationProperty* _Rotation = OutProtoData.mutable_single_component_rotation();
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.SingleComponentRotation, *_Rotation))
		return false;
	catalog_studio_message::ScaleProperty* _Scale = OutProtoData.mutable_single_component_scale();
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.SingleComponentScale, *_Scale))
		return false;
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FSingleComponentProperty& InUeData, catalog_studio_message::SingleComponentProperty& OutProtoData)
{
	for (auto& Iter : InUeData.ComponentItems)
	{
		catalog_studio_message::SingleComponentItem* NewComponent = OutProtoData.add_component_items();
		if (nullptr == NewComponent)
			return false;
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *NewComponent))
			return false;
	}
	OutProtoData.set_no_meaning(1);
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FMultiComponentDataItem& InUeData, catalog_studio_message::MultiComponentDataItem& OutProtoData)
{
	OutProtoData.set_id(InUeData.ID);
	OutProtoData.set_component_name(TCHAR_TO_UTF8(*InUeData.ComponentName));
	OutProtoData.set_description(TCHAR_TO_UTF8(*InUeData.Description));
	OutProtoData.set_code(TCHAR_TO_UTF8(*InUeData.Code));
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentVisibility, *OutProtoData.mutable_component_visibility()))
		return false;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentID, *OutProtoData.mutable_component_id()))
		return false;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentLocation, *OutProtoData.mutable_component_location()))
		return false;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentRotation, *OutProtoData.mutable_component_rotation()))
		return false;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.ComponentScale, *OutProtoData.mutable_component_scale()))
		return false;
	for (auto& Iter : InUeData.ComponentParameters)
	{
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_component_parameters()))
			return false;
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FMultiComponentData& InUeData, catalog_studio_message::MultiComponentData& OutProtoData)
{
	for (auto& Iter : InUeData.ComponentItems)
	{
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_component_items()))
			return false;
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FParameterData& InUeData, catalog_studio_message::ParameterData& OutProtoData)
{
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.Data, *OutProtoData.mutable_data()))
		return false;
	for (auto& Iter : InUeData.EnumData)
	{
		if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_enum_data()))
			return false;
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FParameterTableData& InUeData, catalog_studio_message::ParameterTableData& OutProtoData)
{
	OutProtoData.set_id(TCHAR_TO_UTF8(*InUeData.id));
	OutProtoData.set_name(TCHAR_TO_UTF8(*InUeData.name));
	OutProtoData.set_description(TCHAR_TO_UTF8(*InUeData.description));
	OutProtoData.set_classific_id(InUeData.classific_id);
	OutProtoData.set_value(TCHAR_TO_UTF8(*InUeData.value));
	OutProtoData.set_expression(TCHAR_TO_UTF8(*InUeData.expression));
	OutProtoData.set_max_value(TCHAR_TO_UTF8(*InUeData.max_value));
	OutProtoData.set_max_exp(TCHAR_TO_UTF8(*InUeData.max_expression));
	OutProtoData.set_min_value(TCHAR_TO_UTF8(*InUeData.min_value));
	OutProtoData.set_min_exp(TCHAR_TO_UTF8(*InUeData.min_expression));
	OutProtoData.set_visibility(TCHAR_TO_UTF8(*InUeData.visibility));
	OutProtoData.set_visibility_exp(TCHAR_TO_UTF8(*InUeData.visibility_exp));
	OutProtoData.set_editable(TCHAR_TO_UTF8(*InUeData.editable));
	OutProtoData.set_editable_exp(TCHAR_TO_UTF8(*InUeData.editable_exp));
	OutProtoData.set_is_enum(InUeData.is_enum);
	OutProtoData.set_param_id(TCHAR_TO_UTF8(*InUeData.param_id));
	OutProtoData.set_main_id(TCHAR_TO_UTF8(*InUeData.main_id));
	OutProtoData.set_special(TCHAR_TO_UTF8(*InUeData.Special));
	OutProtoData.set_special_exp(TCHAR_TO_UTF8(*InUeData.Special_exp));
	OutProtoData.set_must(TCHAR_TO_UTF8(*InUeData.Must));
	OutProtoData.set_must_exp(TCHAR_TO_UTF8(*InUeData.Must_exp));
    OutProtoData.set_default_express(TCHAR_TO_UTF8(*InUeData.DefaultExpress));
	OutProtoData.set_no_match_enum(InUeData.no_match_enum_data);

	OutProtoData.set_grid_value_mark(static_cast<int32>(InUeData.grid_value_mark));
	OutProtoData.set_grid_expression(TCHAR_TO_UTF8(*InUeData.grid_expression));
	OutProtoData.set_grid_value(TCHAR_TO_UTF8(*InUeData.grid_value));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FEnumParameterTableData& InUeData, catalog_studio_message::EnumParameterTableData& OutProtoData)
{
	OutProtoData.set_id(TCHAR_TO_UTF8(*InUeData.id));
	OutProtoData.set_value(TCHAR_TO_UTF8(*InUeData.value));
	OutProtoData.set_name_for_display(TCHAR_TO_UTF8(*InUeData.name_for_display));
	OutProtoData.set_image_for_display(TCHAR_TO_UTF8(*InUeData.image_for_display));
	OutProtoData.set_visibility(TCHAR_TO_UTF8(*InUeData.visibility));
	OutProtoData.set_priority(TCHAR_TO_UTF8(*InUeData.priority));
	OutProtoData.set_main_id(TCHAR_TO_UTF8(*InUeData.main_id));
	OutProtoData.set_visibility_exp(TCHAR_TO_UTF8(*InUeData.visibility_exp));
	OutProtoData.set_expression(TCHAR_TO_UTF8(*InUeData.expression));
	OutProtoData.set_force_select_condition(TCHAR_TO_UTF8(*InUeData.force_select_condition));
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCatalogFolderDataDB& InUeData, catalog_studio_message::FolderTableDataMsg& OutProtoData)
{
	OutProtoData.set_id(TCHAR_TO_UTF8(*InUeData.id));
	OutProtoData.set_folder_id(TCHAR_TO_UTF8(*InUeData.folder_id));
	OutProtoData.set_folder_name(TCHAR_TO_UTF8(*InUeData.folder_name));
	OutProtoData.set_folder_name_exp(TCHAR_TO_UTF8(*InUeData.folder_name_exp));
	OutProtoData.set_folder_code(TCHAR_TO_UTF8(*InUeData.folder_code));
	OutProtoData.set_folder_code_exp(TCHAR_TO_UTF8(*InUeData.folder_code_exp));
	OutProtoData.set_folder_type(InUeData.folder_type);
	OutProtoData.set_thumbnail_path(TCHAR_TO_UTF8(*InUeData.thumbnail_path));
	OutProtoData.set_parent_id(TCHAR_TO_UTF8(*InUeData.parent_id));
	OutProtoData.set_visibility_exp(TCHAR_TO_UTF8(*InUeData.visibility_exp));
	OutProtoData.set_visibility(InUeData.visibility);
	OutProtoData.set_folder_order(InUeData.folder_order);
	OutProtoData.set_is_new(InUeData.is_new);
	OutProtoData.set_is_folder(InUeData.is_folder);
	OutProtoData.set_backend_directory(TCHAR_TO_UTF8(*InUeData.backend_directory));
	OutProtoData.set_front_directory(TCHAR_TO_UTF8(*InUeData.front_directory));
	OutProtoData.set_place_rule(InUeData.place_rule);
    OutProtoData.set_ref_type_code(TCHAR_TO_UTF8(*InUeData.ref_type_code));
    OutProtoData.set_ref_type(TCHAR_TO_UTF8(*InUeData.ref_type));
	OutProtoData.set_description(TCHAR_TO_UTF8(*InUeData.description));
	
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FComponentFileData& InUeData, catalog_studio_message::ComponentFileData& OutProtoData)
{
	OutProtoData.set_file_data_path(TCHAR_TO_UTF8(*InUeData.file_data_path));
	OutProtoData.set_depend_files(TCHAR_TO_UTF8(*InUeData.depend_files));
	for(auto& Iter : InUeData.component_datas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_component_datas());
	}
	OutProtoData.set_file_ref_uuid(TCHAR_TO_UTF8(*InUeData.file_ref_uuid));
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToFileComponentData& InUeData, catalog_studio_message::RefMultiCompDataItem& OutProtoData)
{
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_id, *OutProtoData.mutable_component_id());
	OutProtoData.set_component_name(TCHAR_TO_UTF8(*InUeData.component_name));
	OutProtoData.set_description(TCHAR_TO_UTF8(*InUeData.component_description));
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_code, *OutProtoData.mutable_component_code());
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_visibility, *OutProtoData.mutable_component_visibility());
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_location, *OutProtoData.mutable_component_location());
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_rotation, *OutProtoData.mutable_component_rotation());
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.component_scale, *OutProtoData.mutable_component_scale());
	for(auto& Iter : InUeData.component_params)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_component_parameters());
	}
	OutProtoData.set_component_type(InUeData.component_type);
	OutProtoData.set_component_ref_uuid(TCHAR_TO_UTF8(*InUeData.component_ref_uuid));
	OutProtoData.set_component_name_exp(TCHAR_TO_UTF8(*InUeData.component_name_exp));
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToLocalFileData& InUeData, catalog_studio_message::FolderRefData& OutProtoData)
{
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.FolderDBData, *OutProtoData.mutable_folder_msg());

	for(auto& Iter : InUeData.ParamDatas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_param_data());
	}

	for(auto& Iter : InUeData.ComponentDatas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_ref_comps_data());
	}

	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.FileData, *OutProtoData.mutable_file_data());

	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.MatModelDependFiles, *OutProtoData.mutable_mat_model_depend_files());

	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InUeData.PlaceRuleCustomData, *OutProtoData.mutable_place_rule_custom_data());


	if (InUeData.FolderDBData.is_folder)
	{
		return true;
	}

	for (auto& Iter : InUeData.AssociateListData)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_associate_list_data());
	}
	
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefPlaceRuleCustomData& InUeData, catalog_studio_message::RefPlaceRuleCustom& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_name(TCHAR_TO_UTF8(*InUeData.name));
	OutProtoData.set_isenable(InUeData.isEnable);
	OutProtoData.set_isconfig(InUeData.isConfig);
	//left
	OutProtoData.set_isleft(InUeData.isLeft);
	OutProtoData.set_leftconfigexpression(TCHAR_TO_UTF8(*InUeData.leftConfigExpression));
	OutProtoData.set_leftconfig(TCHAR_TO_UTF8(*InUeData.leftConfig));
	//right
	OutProtoData.set_isright(InUeData.isRight);
	OutProtoData.set_rightconfigexpression(TCHAR_TO_UTF8(*InUeData.rightConfigExpression));
	OutProtoData.set_rightconfig(TCHAR_TO_UTF8(*InUeData.rightConfig));
	//upper
	OutProtoData.set_isupper(InUeData.isUpper);
	OutProtoData.set_upperconfigexpression(TCHAR_TO_UTF8(*InUeData.upperConfigExpression));
	OutProtoData.set_upperconfig(TCHAR_TO_UTF8(*InUeData.upperConfig));
	//down
	OutProtoData.set_isdown(InUeData.isDown);
	OutProtoData.set_downconfigexpression(TCHAR_TO_UTF8(*InUeData.downConfigExpression));
	OutProtoData.set_downconfig(TCHAR_TO_UTF8(*InUeData.downConfig));
	//front
	OutProtoData.set_isfront(InUeData.isFront);
	OutProtoData.set_frontconfigexpression(TCHAR_TO_UTF8(*InUeData.frontConfigExpression));
	OutProtoData.set_frontconfig(TCHAR_TO_UTF8(*InUeData.frontConfig));
	//back
	OutProtoData.set_isafter(InUeData.isAfter);
	OutProtoData.set_afterconfigexpression(TCHAR_TO_UTF8(*InUeData.afterConfigExpression));
	OutProtoData.set_afterconfig(TCHAR_TO_UTF8(*InUeData.afterConfig));

	OutProtoData.set_createdby(TCHAR_TO_UTF8(*InUeData.createdBy));
	OutProtoData.set_createdtime(TCHAR_TO_UTF8(*InUeData.createdTime));
	OutProtoData.set_updatedby(TCHAR_TO_UTF8(*InUeData.updatedBy));
	OutProtoData.set_updatedtime(TCHAR_TO_UTF8(*InUeData.updatedTime));
	OutProtoData.set_delflag(InUeData.delFlag);
	OutProtoData.set_isusercustom(InUeData.IsUserCustom);

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToStyleData& InUeData, catalog_studio_message::StyleInfoData& OutProtoData)
{
	OutProtoData.set_style_id(TCHAR_TO_UTF8(*InUeData.style_id));
	OutProtoData.set_style_code(TCHAR_TO_UTF8(*InUeData.style_code));
	OutProtoData.set_style_craft(TCHAR_TO_UTF8(*InUeData.style_craft));
	OutProtoData.set_style_description(TCHAR_TO_UTF8(*InUeData.style_description));
	OutProtoData.set_style_thumbnail_path(TCHAR_TO_UTF8(*InUeData.style_thumbnail_path));
	OutProtoData.set_style_thumbnail_md5(TCHAR_TO_UTF8(*InUeData.style_thumbnail_md5));
	OutProtoData.set_style_sort_order(InUeData.style_sort_order);
	OutProtoData.set_is_check(InUeData.is_checked);
	OutProtoData.set_style_group(static_cast<int32>(InUeData.style_group));
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToContentData& InUeData, catalog_studio_message::StyleContentData& OutProtoData)
{
	OutProtoData.set_content_id(TCHAR_TO_UTF8(*InUeData.content_id));
	OutProtoData.set_content_name(TCHAR_TO_UTF8(*InUeData.content_name));
	OutProtoData.set_content_sort_order(InUeData.content_sort_order);
    OutProtoData.set_content_relation_code(TCHAR_TO_UTF8(*InUeData.content_relation_code));
    OutProtoData.set_content_base_type(InUeData.content_base_type);

	for(const auto& Iter : InUeData.option_datas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_option_datas());		
	}

	//UE中的TMap转换成protobuf中的repeated数值
	for(const auto& Iter : InUeData.style_option_checks)
	{
		auto& OptionCheck = *OutProtoData.add_style_option_checks();
		OptionCheck.set_style_id(TCHAR_TO_UTF8(*Iter.Key));
		for(const auto& CheckIter : Iter.Value.option_checks)
		{
			UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(CheckIter, *OptionCheck.add_option_checks());
		}
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToOptionData& InUeData, catalog_studio_message::StyleOptionData& OutProtoData)
{
	OutProtoData.set_option_id(TCHAR_TO_UTF8(*InUeData.option_id));
	OutProtoData.set_option_code(TCHAR_TO_UTF8(*InUeData.option_code));
	OutProtoData.set_option_description(TCHAR_TO_UTF8(*InUeData.option_description));
	OutProtoData.set_option_visibility(TCHAR_TO_UTF8(*InUeData.option_visibility));
	OutProtoData.set_option_visibility_exp(TCHAR_TO_UTF8(*InUeData.option_visibility_exp));
	OutProtoData.set_option_sort_order(InUeData.option_sort_order);
	for(auto& Iter : InUeData.option_params)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_option_params());
	}

	OutProtoData.set_option_thumbnail_url(TCHAR_TO_UTF8(*InUeData.option_thumbnail_url));
	OutProtoData.set_option_content_id(TCHAR_TO_UTF8(*InUeData.option_content_id));
	OutProtoData.set_option_data_source(InUeData.option_data_source);
	OutProtoData.set_option_custom_id(TCHAR_TO_UTF8(*InUeData.option_custom_id));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToOptionCheck& InUeData, catalog_studio_message::StyleOptionCheckData& OutProtoData)
{
	OutProtoData.set_option_id(TCHAR_TO_UTF8(*InUeData.option_id));
	OutProtoData.set_content_id(TCHAR_TO_UTF8(*InUeData.content_id));
	OutProtoData.set_style_id(TCHAR_TO_UTF8(*InUeData.style_id));
	OutProtoData.set_is_prime(InUeData.is_prime);
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FOptionCheckArr& InUeData, catalog_studio_message::StyleOptionCheckArrData& OutProtoData)
{
	for(auto& Iter : InUeData.option_checks)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_option_checks());
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefToStyleFile& InUeData, catalog_studio_message::StyleRefData& OutProtoData)
{
	for(auto& Iter : InUeData.style_datas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_style_datas());
	}
	for(auto& Iter : InUeData.content_datas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_content_datas());
	}
	/*for(auto& Iter : InUeData.option_datas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_option_datas());
	}
	for(auto& Iter : InUeData.option_checks)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_option_check_datas());
	}*/
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefParamData& InUeData, catalog_studio_message::RefParameterData& OutProtoData)
{
	for (auto& Iter : InUeData.ParamGroups)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_ref_param_groups());
	}

	for (auto& Iter : InUeData.ParamDatas)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_ref_params());
	}
	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FParameterGroupTableData& InUeData, catalog_studio_message::RefParamGroupData& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_group_name(TCHAR_TO_UTF8(*InUeData.group_name));
	OutProtoData.set_description(TCHAR_TO_UTF8(*InUeData.description));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FRefAssociateData& InData, catalog_studio_message::AssociateData& OutProtoData)
{
	OutProtoData.set_id(InData.id);
	OutProtoData.set_name(TCHAR_TO_UTF8(*InData.name));
	OutProtoData.set_meetcondition(TCHAR_TO_UTF8(*InData.meetCondition));
	OutProtoData.set_type(InData.type);
	OutProtoData.set_ismate(InData.isMate);
	OutProtoData.set_dictvalue(TCHAR_TO_UTF8(*InData.dictValue));
	OutProtoData.set_belongid(TCHAR_TO_UTF8(*InData.belongId));
	OutProtoData.set_associationid(InData.associationId);
	OutProtoData.set_bkid(TCHAR_TO_UTF8(*InData.bkId));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FAssociateListData& InData, catalog_studio_message::AssociateListData& OutProtoData)
{
	OutProtoData.set_correlationtype(TCHAR_TO_UTF8(*InData.correlationType));
	OutProtoData.set_correlationtypename(TCHAR_TO_UTF8(*InData.correlationTypeName));
	OutProtoData.set_ismate(InData.isMate);

	for (auto& Iter : InData.bkAssociationDetailList)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_bkassociationdataillist());
	}

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCSMapData& InUeData, catalog_studio_message::CSMatMapData& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_materialid(InUeData.materialId);
	OutProtoData.set_adminid(InUeData.adminId);
	OutProtoData.set_md5(TCHAR_TO_UTF8(*InUeData.md5));
	OutProtoData.set_attrid(InUeData.attrId);
	OutProtoData.set_attrname(TCHAR_TO_UTF8(*InUeData.attrName));
	OutProtoData.set_materialvalue(TCHAR_TO_UTF8(*InUeData.materialValue));
	OutProtoData.set_delflag(InUeData.delFlag);
	OutProtoData.set_createdby(TCHAR_TO_UTF8(*InUeData.createdBy));
	OutProtoData.set_createdtime(TCHAR_TO_UTF8(*InUeData.createdTime));
	OutProtoData.set_updatedby(TCHAR_TO_UTF8(*InUeData.updatedBy));
	OutProtoData.set_updatedtime(TCHAR_TO_UTF8(*InUeData.updatedTime));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCSModelMatLable& InUeData, catalog_studio_message::CSMatModelLableData& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_name(TCHAR_TO_UTF8(*InUeData.name));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCSModelInfo& InUeData, catalog_studio_message::CSModelInfoData& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_type(InUeData.type);
	OutProtoData.set_code(TCHAR_TO_UTF8(*InUeData.code));
	OutProtoData.set_md5(TCHAR_TO_UTF8(*InUeData.md5));
	OutProtoData.set_name(TCHAR_TO_UTF8(*InUeData.name));
	OutProtoData.set_fbxfilepath(TCHAR_TO_UTF8(*InUeData.fbxFilePath));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FCSModelMatData& InUeData, catalog_studio_message::CSMatModelData& OutProtoData)
{
	OutProtoData.set_id(InUeData.id);
	OutProtoData.set_name(TCHAR_TO_UTF8(*InUeData.name));
	OutProtoData.set_code(TCHAR_TO_UTF8(*InUeData.code));
	OutProtoData.set_attrcate(InUeData.attrCate);
	OutProtoData.set_refpath(TCHAR_TO_UTF8(*InUeData.refPath));
	OutProtoData.set_pakfilepath(TCHAR_TO_UTF8(*InUeData.pakFilePath));
	OutProtoData.set_isself(InUeData.isSelf);
	OutProtoData.set_productimg(TCHAR_TO_UTF8(*InUeData.productImg));
	OutProtoData.set_brand(TCHAR_TO_UTF8(*InUeData.brand));
	OutProtoData.set_cateid(InUeData.cateId);
	OutProtoData.set_catename(TCHAR_TO_UTF8(*InUeData.cateName));
	OutProtoData.set_materialcate(TCHAR_TO_UTF8(*InUeData.materialCate));
	OutProtoData.set_mapsimg(TCHAR_TO_UTF8(*InUeData.mapsImg));
	OutProtoData.set_price(InUeData.price);
	OutProtoData.set_depth(TCHAR_TO_UTF8(*InUeData.depth));
	OutProtoData.set_width(TCHAR_TO_UTF8(*InUeData.width));
	OutProtoData.set_height(TCHAR_TO_UTF8(*InUeData.height));
	OutProtoData.set_placementrules(TCHAR_TO_UTF8(*InUeData.placementRules));
	OutProtoData.set_dictgroupvalue(TCHAR_TO_UTF8(*InUeData.dictGroupValue));
	OutProtoData.set_dictvalue(TCHAR_TO_UTF8(*InUeData.dictValue));
	OutProtoData.set_shader(TCHAR_TO_UTF8(*InUeData.shader));
	OutProtoData.set_vrscene(TCHAR_TO_UTF8(*InUeData.vrscene));
	OutProtoData.set_ue5param(TCHAR_TO_UTF8(*InUeData.ue5Param));
	OutProtoData.set_templateid(InUeData.templateId);
	OutProtoData.set_renderingid(InUeData.renderingId);
	OutProtoData.set_folderid(TCHAR_TO_UTF8(*InUeData.folderId));
	OutProtoData.set_mark(TCHAR_TO_UTF8(*InUeData.mark));
	OutProtoData.set_delflag(InUeData.delFlag);
	OutProtoData.set_md5(TCHAR_TO_UTF8(*InUeData.md5));
	OutProtoData.set_createdby(TCHAR_TO_UTF8(*InUeData.createdBy));
	OutProtoData.set_createdtime(TCHAR_TO_UTF8(*InUeData.createdTime));
	OutProtoData.set_updatedby(TCHAR_TO_UTF8(*InUeData.updatedBy));
	OutProtoData.set_updatedtime(TCHAR_TO_UTF8(*InUeData.updatedTime));

	for (auto& Iter : InUeData.manageMapsList)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_managemapslist());
	}

	for (auto& Iter : InUeData.labelList)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_labellist());
	}

	for (auto& Iter : InUeData.modelList)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_modellist());
	}

	OutProtoData.set_foldercode(TCHAR_TO_UTF8(*InUeData.folderCode));

	return true;
}

bool UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(const FDependFileData& InUeData, catalog_studio_message::DependFileData& OutProtoData)
{
	for (auto& Iter : InUeData.ModelDependFiles)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_modeldependfiles());
	}

	for (auto& Iter : InUeData.MatDependFiles)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_matdependfiles());
	}

	for (auto& Iter : InUeData.ModelDependFolderID)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_modeldependfolderid());
	}
	for (auto& Iter : InUeData.MatDependFolderID)
	{
		UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(Iter, *OutProtoData.add_matdependfolderid());
	}

	return true;
}
