// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DataCenter/LoginData/LoginData.h"
#include "ProtobufOperator/ProtoDatas/ApplicationConfig.pb.h"

#include "ProtobufOperator/ProtoDatas/CrossSectionData.pb.h"
#include "DataCenter/ComponentData/CrossSectionDataDefine.h"

#include "ProtobufOperator/ProtoDatas/CustomMeshInfo.pb.h"

#include "ProtobufOperator/ProtoDatas/ImportMeshSection.pb.h"

#include "ProtobufOperator/ProtoDatas/LocationProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/RotationProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/ScaleProperty.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionCutOutOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionDrawOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionLoftOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionOperationOrder.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionShiftingOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionZoomOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SectionOperation.pb.h"

#include "ProtobufOperator/ProtoDatas/SingleComponentItem.pb.h"
#include "ProtobufOperator/ProtoDatas/SingleComponentProperty.pb.h"
#include "ProtobufOperator/ProtoDatas/MultiComponentData.pb.h"
#include "ProtobufOperator/ProtoDatas/EnumParameterTableData.pb.h"
#include "ProtobufOperator/ProtoDatas/ParameterData.pb.h"
#include "ProtobufOperator/ProtoDatas/ParameterTableData.pb.h"

#include "DataCenter/ComponentData/ComponentPropertyData.h"
#include "DataCenter/ComponentData/SingleComponentDataDefine.h"

#include "DataCenter/ComponentData/MultiComponentData.h"

#include "ProtobufOperator/ProtoDatas/CatalogFolderTableData.pb.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "ProtobufOperator/ProtoDatas/FolderRef.pb.h"
#include "ProtobufOperator/ProtoDatas/RefFileMultiComponentItem.pb.h"

#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "ProtobufOperator/ProtoDatas/StyleInfo.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleContent.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleOption.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleOptionCheck.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleOptionCheckArr.pb.h"
#include "ProtobufOperator/ProtoDatas/StyleRef.pb.h"
#include "ProtobufOperator/ProtoDatas/RefParameterData.pb.h"
#include "ProtobufOperator/ProtoDatas/RefParamGroup.pb.h"

#include "ProtobufOperator/ProtoDatas/AssociateData.pb.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"

#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"

#include "ProtobufOperator/ProtoDatas/CSMatMap.pb.h"
#include "ProtobufOperator/ProtoDatas/CSMatModel.pb.h"
#include "ProtobufOperator/ProtoDatas/CSMatModelLable.pb.h"
#include "ProtobufOperator/ProtoDatas/CSModelInfo.pb.h"
#include "ProtobufOperator/ProtoDatas/DependFile.pb.h"

#include "DataCenter/RefFile/Data/RefToPlaceDataLibrary.h"
#include "ProtobufOperator/ProtoDatas/RefPlaceRuleCustom.pb.h"

#include "ConvertUeToProtobufLibrary.generated.h"

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UConvertUeToProtobufLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()


public:

	static bool ConvertUeDataToProtobufData(const FLoginRemeberInfo& InUeData, ApplicationConfig& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FVector& InUeData, catalog_studio_message::Vector& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FVector2D& InUeData, catalog_studio_message::Vector2D& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FExpressionValuePair& InUeData, catalog_studio_message::ExpressionValuePair& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FCrossSectionData& InUeData, catalog_studio_message::CrossSectionData& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FGeomtryCubeProperty& InUeData, catalog_studio_message::GeomtryCubeProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FGeomtryEllipsePlanProperty& InUeData, catalog_studio_message::GeomtryEllipsePlanProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FGeomtryRectanglePlanProperty& InUeData, catalog_studio_message::GeomtryRectanglePlanProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FGeomtryPointProperty& InUeData, catalog_studio_message::GeomtryPointProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FGeomtryLineProperty& InUeData, catalog_studio_message::GeomtryLineProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FPMCSection& InUeData, catalog_studio_message::CustomMeshInfo& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FImportMeshSection& InUeData, catalog_studio_message::ImportMeshSection& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FLocationProperty& InUeData, catalog_studio_message::LocationProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FRotationProperty& InUeData, catalog_studio_message::RotationProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FScaleProperty& InUeData, catalog_studio_message::ScaleProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionCutOutOperation& InUeData, catalog_studio_message::SectionCutOutOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionDrawOperation& InUeData, catalog_studio_message::SectionDrawOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionLoftOperation& InUeData, catalog_studio_message::SectionLoftOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionShiftingOperation& InUeData, catalog_studio_message::SectionShiftingOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionZoomOperation& InUeData, catalog_studio_message::SectionZoomOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionOperationOrder& InUeData, catalog_studio_message::SectionOperationOrder& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSectionOperation& InUeData, catalog_studio_message::SectionOperation& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSingleComponentItem& InUeData, catalog_studio_message::SingleComponentItem& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FSingleComponentProperty& InUeData, catalog_studio_message::SingleComponentProperty& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FMultiComponentData& InUeData, catalog_studio_message::MultiComponentData& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FMultiComponentDataItem& InUeData, catalog_studio_message::MultiComponentDataItem& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FParameterData& InUeData, catalog_studio_message::ParameterData& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FParameterTableData& InUeData, catalog_studio_message::ParameterTableData& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FEnumParameterTableData& InUeData, catalog_studio_message::EnumParameterTableData& OutProtoData);

	//RefFile
	static bool ConvertUeDataToProtobufData(const FCatalogFolderDataDB& InUeData, catalog_studio_message::FolderTableDataMsg& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FComponentFileData& InUeData, catalog_studio_message::ComponentFileData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToFileComponentData& InUeData, catalog_studio_message::RefMultiCompDataItem& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToLocalFileData& InUeData, catalog_studio_message::FolderRefData& OutProtoData);

	static bool ConvertUeDataToProtobufData(const FRefPlaceRuleCustomData& InUeData, catalog_studio_message::RefPlaceRuleCustom& OutProtoData);
	
	//RefStyle
	static bool ConvertUeDataToProtobufData(const FRefToStyleData& InUeData, catalog_studio_message::StyleInfoData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToContentData& InUeData, catalog_studio_message::StyleContentData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToOptionData& InUeData, catalog_studio_message::StyleOptionData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToOptionCheck& InUeData, catalog_studio_message::StyleOptionCheckData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FOptionCheckArr& InUeData, catalog_studio_message::StyleOptionCheckArrData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FRefToStyleFile& InUeData, catalog_studio_message::StyleRefData& OutProtoData);

	//params 
	static bool ConvertUeDataToProtobufData(const FRefParamData& InUeData, catalog_studio_message::RefParameterData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FParameterGroupTableData& InUeData, catalog_studio_message::RefParamGroupData& OutProtoData);

	//associate
	static bool ConvertUeDataToProtobufData(const FRefAssociateData& InData, catalog_studio_message::AssociateData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FAssociateListData& InData, catalog_studio_message::AssociateListData& OutProtoData);

	//web pak model / mat data
	static bool ConvertUeDataToProtobufData(const FCSMapData& InUeData, catalog_studio_message::CSMatMapData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FCSModelMatLable& InUeData, catalog_studio_message::CSMatModelLableData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FCSModelInfo& InUeData, catalog_studio_message::CSModelInfoData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FCSModelMatData& InUeData, catalog_studio_message::CSMatModelData& OutProtoData);
	static bool ConvertUeDataToProtobufData(const FDependFileData& InUeData, catalog_studio_message::DependFileData& OutProtoData);

};
