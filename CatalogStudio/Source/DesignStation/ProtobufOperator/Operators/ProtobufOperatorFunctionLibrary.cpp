// Fill out your copyright notice in the Description page of Project Settings.

#include "ProtobufOperatorFunctionLibrary.h"
#include <fstream>

#include "ConvertProtobufToUeLibrary.h"
#include "ConvertUeToProtobufLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "ProtobufOperator/ProtoDatas/ApplicationConfig.pb.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"


bool UProtobufOperatorFunctionLibrary::SaveLoginUserInfoToFile(const FLoginRemeberInfo& InLoginRemberInfo)
{
	ApplicationConfig ConfigRequest;
	UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InLoginRemberInfo, ConfigRequest);
	return UProtobufOperatorFunctionLibrary::SaveStreamToFile<ApplicationConfig>(UProtobufOperatorFunctionLibrary::UserInfoFilePath(), ConfigRequest);
}

bool UProtobufOperatorFunctionLibrary::LoadLoginUserInfoFromFile(FLoginRemeberInfo& OutLoginRemberInfo)
{
	ApplicationConfig ConfigRequest;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<ApplicationConfig>(UProtobufOperatorFunctionLibrary::UserInfoFilePath(), ConfigRequest);
	if (Res)
	{
		UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(ConfigRequest, OutLoginRemberInfo);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::SaveSingleComponentToFile(const FString& InSingleComponentPath, const FSingleComponentProperty& InSingleComponent)
{
	catalog_studio_message::SingleComponentProperty SingleComponent;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InSingleComponent, SingleComponent))
	{
		UE_LOG(LogTemp, Error, TEXT("UProtobufOperatorFunctionLibrary::SaveSingleComponentToFile UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData false"));
		return false;
	}
	return UProtobufOperatorFunctionLibrary::SaveStreamToFile<catalog_studio_message::SingleComponentProperty>(InSingleComponentPath, SingleComponent);
}

bool UProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(const FString& InSingleComponentPath, FSingleComponentProperty& OutSingleComponent)
{
	catalog_studio_message::SingleComponentProperty SingleComponent;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::SingleComponentProperty>(InSingleComponentPath, SingleComponent);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(SingleComponent, OutSingleComponent);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::SaveMultiComponentToFile(const FString& InMultiComponentPath, const FMultiComponentData& InMultiComponent)
{
	catalog_studio_message::MultiComponentData MultiComponent;
	if (false == UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InMultiComponent, MultiComponent))
	{
		UE_LOG(LogTemp, Error, TEXT("UProtobufOperatorFunctionLibrary::SaveSingleComponentToFile UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData false"));
		return false;
	}
	return UProtobufOperatorFunctionLibrary::SaveStreamToFile<catalog_studio_message::MultiComponentData>(InMultiComponentPath, MultiComponent);
}

bool UProtobufOperatorFunctionLibrary::LoadMultiComponentFromFile(const FString& InMultiComponentPath, FMultiComponentData& OutMultiComponent)
{
	catalog_studio_message::MultiComponentData MultiComponent;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::MultiComponentData>(InMultiComponentPath, MultiComponent);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(MultiComponent, OutMultiComponent);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::LoadMultiComponentFromFile(const FString& InMultiComponentPath, TArray<FMultiComponentDataItem>& OutMultiComponent)
{
	catalog_studio_message::MultiComponentData MultiComponent;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::MultiComponentData>(InMultiComponentPath, MultiComponent);
	if (Res)
	{
		const int32 CompCount = MultiComponent.component_items_size();
		OutMultiComponent.Empty();
		OutMultiComponent.AddZeroed(CompCount);
		for (int32 i = 0; i < CompCount; ++i)
		{
			if (false == UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(MultiComponent.component_items(i), OutMultiComponent[i]))
				return false;
		}
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::SaveRelationToFile(FString InRelationFilePath, const FRefToLocalFileData& InRelationDatas)
{
	catalog_studio_message::FolderRefData FileData;
	bool Res = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InRelationDatas, FileData);
	if(Res)
	{
		//auto save backup
		UProtobufOperatorFunctionLibrary::AutoCopyFileBackup(InRelationDatas);
		
		Res = UProtobufOperatorFunctionLibrary::SaveStreamToFile<catalog_studio_message::FolderRefData>(InRelationFilePath, FileData);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FString InRelationFilePath, FRefToLocalFileData& OutRelationDatas)
{
	catalog_studio_message::FolderRefData FileData;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::FolderRefData>(InRelationFilePath, FileData);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(FileData, OutRelationDatas);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::SaveRelationToFile(FString InRelationFilePath, const FRefToStyleFile& InDatas)
{
	catalog_studio_message::StyleRefData StyleRefData;
	bool Res = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InDatas, StyleRefData);
	if (Res)
	{
		//auto save backup
		UProtobufOperatorFunctionLibrary::AutoCopyStyleBackup();
		
		Res = UProtobufOperatorFunctionLibrary::SaveStreamToFile<catalog_studio_message::StyleRefData>(InRelationFilePath, StyleRefData);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FString InRelationFilePath, FRefToStyleFile& OutDatas)
{
	catalog_studio_message::StyleRefData StyleRefData;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::StyleRefData>(InRelationFilePath, StyleRefData);
	if (Res)
	{
		
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(StyleRefData, OutDatas);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::SaveRelationToFile(FString InRelationFilePath, const FRefParamData& InDatas)
{
	catalog_studio_message::RefParameterData Datas;
	bool Res = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InDatas, Datas);
	if (Res)
	{
		//auto save backup
		UProtobufOperatorFunctionLibrary::AutoCopyParamBackup();
		
		Res = UProtobufOperatorFunctionLibrary::SaveStreamToFile<catalog_studio_message::RefParameterData>(InRelationFilePath, Datas);
	}
	return Res;
}

bool UProtobufOperatorFunctionLibrary::LoadRelationFromFile(FString InRelationFilePath, FRefParamData& OutDatas)
{
	catalog_studio_message::RefParameterData Datas;
	bool Res = UProtobufOperatorFunctionLibrary::LoadStreamFromFile<catalog_studio_message::RefParameterData>(InRelationFilePath, Datas);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(Datas, OutDatas);
	}
	return Res;
}

FString UProtobufOperatorFunctionLibrary::GetCurrentPCData()
{
	FString CurTime = UKismetMathLibrary::Now().ToString();
	FString Data, Time;
	CurTime.Split(TEXT("-"), &Data, &Time);
	return Data;
}

FString UProtobufOperatorFunctionLibrary::GetCurrentPCTime()
{
	FString CurTime = UKismetMathLibrary::Now().ToString();
	FString Data, Time;
	CurTime.Split(TEXT("-"), &Data, &Time);
	return Time;
}

FString UProtobufOperatorFunctionLibrary::GetMarkForLocalFile(const FRefToLocalFileData& InData)
{
	const FString AffectID = (InData.FolderDBData.folder_id.IsEmpty() || InData.FolderDBData.is_folder) ? InData.FolderDBData.id : InData.FolderDBData.folder_id;
	const int64 FolderID = FCString::Atoi64(*AffectID);
	return FString::Printf(TEXT("%lld"), FolderID);
}

void UProtobufOperatorFunctionLibrary::AutoCopyFileBackup(const FRefToLocalFileData& InData)
{
	if(InData.IsValid())
	{
		const FString RefFileMark = UProtobufOperatorFunctionLibrary::GetMarkForLocalFile(InData);
		const FString RefFileAbsPath = URefToFileData::GetFileAddress(RefFileMark);
		if(FPaths::FileExists(RefFileAbsPath))
		{
			const FString AutoSaveRelativePath = URefToFileData::GetAutoTempSaveFileRelativeAddress(
			InData.FolderDBData.id,
			InData.FolderDBData.folder_id,
			InData.FolderDBData.folder_name,
			UProtobufOperatorFunctionLibrary::GetCurrentPCData(),
			UProtobufOperatorFunctionLibrary::GetCurrentPCTime()
			);
			const FString BackupAbsPth = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), AutoSaveRelativePath));
			FPlatformFileManager::Get().GetPlatformFile().CopyFile(*BackupAbsPth, *RefFileAbsPath);
		}
	}
}

void UProtobufOperatorFunctionLibrary::AutoCopyParamBackup()
{
	const FString ParamAbsPath = URefToParamDataLibrary::GetRefParamsAddress();
	if(FPaths::FileExists(ParamAbsPath))
	{
		const FString AutoSaveAbsPath = URefToParamDataLibrary::GetAutoTempSaveAddress(
			UProtobufOperatorFunctionLibrary::GetCurrentPCData(),
			UProtobufOperatorFunctionLibrary::GetCurrentPCTime());
		FPlatformFileManager::Get().GetPlatformFile().CopyFile(*AutoSaveAbsPath, *ParamAbsPath);
	}
}

void UProtobufOperatorFunctionLibrary::AutoCopyStyleBackup()
{
	const FString StyleAbsPath = URefToStyleDataLibrary::GetStyleFileAddress();
	if(FPaths::FileExists(StyleAbsPath))
	{
		const FString AutoSaveAbsPath = URefToStyleDataLibrary::GetAutoTempSaveAddress(
			UProtobufOperatorFunctionLibrary::GetCurrentPCData(),
			UProtobufOperatorFunctionLibrary::GetCurrentPCTime());
		FPlatformFileManager::Get().GetPlatformFile().CopyFile(*AutoSaveAbsPath, *StyleAbsPath);
	}
}

template<class T>
bool UProtobufOperatorFunctionLibrary::SaveStreamToFile(const FString& InFilePath, const T& InProtobufStruct)
{
	if (false == UProtobufOperatorFunctionLibrary::CreateDirectoryRecursively(FPaths::GetPath(InFilePath)))
		return false;

	TArray64<uint8> SerializeData;
	SerializeData.SetNumUninitialized(InProtobufStruct.ByteSizeLong());
	if (!InProtobufStruct.SerializeToArray(SerializeData.GetData(), SerializeData.Num()))
	{
        UE_LOG(LogTemp, Log, TEXT("Serialize failed! File path is %s"), *InFilePath);
        return false;
	}
	if (!FFileHelper::SaveArrayToFile(SerializeData, *InFilePath))
	{
        UE_LOG(LogTemp, Log, TEXT("Save file failed! File path is %s"), *InFilePath);
		return false;
	}

	return true;

	//std::wstring FilePath(*InFilePath);
	//std::fstream OutputStream(/*TCHAR_TO_UTF8(*InFilePath)*/FilePath, std::ios::out | std::ios::binary | std::ios::trunc);
	//if (!OutputStream.is_open())
	//{
 //       UE_LOG(LogTemp, Log, TEXT("Open file failed! File path is %s"), *InFilePath);
 //       OutputStream.close();
	//	return false;
	//}
	//if (false == InProtobufStruct.SerializeToOstream(&OutputStream))
	//{
	//	UE_LOG(LogTemp, Log, TEXT("Serialize failed! File path is %s"), *InFilePath);
	//	OutputStream.close();
	//	return false;
	//}
	//OutputStream.close();
	//return true;
}

template<class T>
bool UProtobufOperatorFunctionLibrary::LoadStreamFromFile(const FString& InFilePath, T& OutProtobufStruct)
{
	if (FPaths::FileExists(InFilePath))
	{
		TArray64<uint8> FileContent;
        if (FFileHelper::LoadFileToArray(FileContent, *InFilePath))
        {
            if (!OutProtobufStruct.ParseFromArray(FileContent.GetData(), FileContent.Num()))
            {
                UE_LOG(LogTemp, Log, TEXT("Parse failed! File path is %s"), *InFilePath);
                return false;
            }
        }
		else
		{
            UE_LOG(LogTemp, Log, TEXT("Load file failed! File path is %s"), *InFilePath);
			return false;
		}

		return true;
	}

	return false;

	//std::wstring FilePath(*InFilePath);
	//std::fstream InputStream(/*TCHAR_TO_UTF8(*InFilePath)*/FilePath, std::ios::in | std::ios::binary);
	//if (false == OutProtobufStruct.ParseFromIstream(&InputStream))
	//{
	//	UE_LOG(LogTemp, Log, TEXT("Parse failed! File path is %s"), *InFilePath);
 //       InputStream.close();
	//	return false;
	//}
	//InputStream.close();
	//return true;
}

FString UProtobufOperatorFunctionLibrary::UserInfoFilePath()
{
	return FPaths::ProjectConfigDir() + TEXT("UserInfo.cfg");
}

bool UProtobufOperatorFunctionLibrary::CreateDirectoryRecursively(FString DirectoryToMake)
{
	const int32 MAX_LOOP_ITR = 3000; //limit of 3000 directories in the structure

									 // Normalize all / and \ to TEXT("/") and remove any trailing TEXT("/") 
									 //if the character before that is not a TEXT("/") or a colon
	FPaths::NormalizeDirectoryName(DirectoryToMake);

	//Normalize removes the last "/", but my algorithm wants it
	DirectoryToMake += "/";

	FString Base;
	FString Left;
	FString Remaining;

	//Split off the Root
	DirectoryToMake.Split(TEXT("/"), &Base, &Remaining);
	Base += "/"; //add root text to Base

	int32 LoopItr = 0;
	while (Remaining != "" && LoopItr < MAX_LOOP_ITR)
	{
		Remaining.Split(TEXT("/"), &Left, &Remaining);

		//Add to the Base
		Base += Left + FString("/"); //add left and split text to Base

									 //Create Incremental Directory Structure!
		FPlatformFileManager::Get().GetPlatformFile().CreateDirectory(*Base);

		LoopItr++;
	}
	return true;
}