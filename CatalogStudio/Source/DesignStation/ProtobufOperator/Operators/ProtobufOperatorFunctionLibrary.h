// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DataCenter/LoginData/LoginData.h"
#include "DataCenter/ComponentData/SingleComponentDataDefine.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "ProtobufOperatorFunctionLibrary.generated.h"

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UProtobufOperatorFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:

	static bool SaveLoginUserInfoToFile(const FLoginRemeberInfo& InLoginRemberInfo);

	static bool LoadLoginUserInfoFromFile(FLoginRemeberInfo& OutLoginRemberInfo);

	static bool SaveSingleComponentToFile(const FString& InSingleComponentPath, const FSingleComponentProperty& InSingleComponent);

	static bool LoadSingleComponentFromFile(const FString& InSingleComponentPath, FSingleComponentProperty& OutSingleComponent);

	static bool SaveMultiComponentToFile(const FString& InMultiComponentPath, const FMultiComponentData& InMultiComponent);

	static bool LoadMultiComponentFromFile(const FString& InMultiComponentPath, FMultiComponentData& OutMultiComponent);

	static bool LoadMultiComponentFromFile(const FString& InMultiComponentPath, TArray<FMultiComponentDataItem>& OutMultiComponent);

	//读写文件
public:
	//保存加载关系文件
	static bool SaveRelationToFile(FString InRelationFilePath, const FRefToLocalFileData& InRelationDatas);
	static bool LoadRelationFromFile(FString InRelationFilePath, FRefToLocalFileData& OutRelationDatas);

	//保存加载风格文件
	static bool SaveRelationToFile(FString InRelationFilePath, const FRefToStyleFile& InDatas);
	static bool LoadRelationFromFile(FString InRelationFilePath, FRefToStyleFile& OutDatas);

	//保存加载参数文件
	static bool SaveRelationToFile(FString InRelationFilePath, const FRefParamData& InDatas);
	static bool LoadRelationFromFile(FString InRelationFilePath, FRefParamData& OutDatas);

	//auto save backup file
	static FString GetCurrentPCData();
	static FString GetCurrentPCTime();

	static FString GetMarkForLocalFile(const FRefToLocalFileData& InData);
	
	static void AutoCopyFileBackup(const FRefToLocalFileData& InData);
	static void AutoCopyParamBackup();
	static void AutoCopyStyleBackup();

public:

	static FString UserInfoFilePath();

private:

	template<class T>
	static bool SaveStreamToFile(const FString& InFilePath, const T& InProtobufStruct);

	template<class T>
	static bool LoadStreamFromFile(const FString& InFilePath, T& OutProtobufStruct);

	static bool CreateDirectoryRecursively(FString DirectoryToMake);
};
