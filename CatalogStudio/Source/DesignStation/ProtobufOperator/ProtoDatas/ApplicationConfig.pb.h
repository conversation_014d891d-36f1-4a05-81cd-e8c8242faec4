// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ApplicationConfig.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ApplicationConfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ApplicationConfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ApplicationConfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ApplicationConfig_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ApplicationConfig_2eproto;
class ApplicationConfig;
struct ApplicationConfigDefaultTypeInternal;
extern ApplicationConfigDefaultTypeInternal _ApplicationConfig_default_instance_;
PROTOBUF_NAMESPACE_OPEN
template<> ::ApplicationConfig* Arena::CreateMaybeMessage<::ApplicationConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE

// ===================================================================

class ApplicationConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ApplicationConfig) */ {
 public:
  inline ApplicationConfig() : ApplicationConfig(nullptr) {}
  ~ApplicationConfig() override;
  explicit constexpr ApplicationConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ApplicationConfig(const ApplicationConfig& from);
  ApplicationConfig(ApplicationConfig&& from) noexcept
    : ApplicationConfig() {
    *this = ::std::move(from);
  }

  inline ApplicationConfig& operator=(const ApplicationConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline ApplicationConfig& operator=(ApplicationConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ApplicationConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const ApplicationConfig* internal_default_instance() {
    return reinterpret_cast<const ApplicationConfig*>(
               &_ApplicationConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ApplicationConfig& a, ApplicationConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(ApplicationConfig* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ApplicationConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ApplicationConfig* New() const final {
    return new ApplicationConfig();
  }

  ApplicationConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ApplicationConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ApplicationConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ApplicationConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ApplicationConfig* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ApplicationConfig";
  }
  protected:
  explicit ApplicationConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLastUserNameFieldNumber = 1,
    kPasswordFieldNumber = 2,
    kRemeberPasswordFieldNumber = 3,
  };
  // string last_user_name = 1;
  void clear_last_user_name();
  const std::string& last_user_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_last_user_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_last_user_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_last_user_name();
  void set_allocated_last_user_name(std::string* last_user_name);
  private:
  const std::string& _internal_last_user_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_last_user_name(const std::string& value);
  std::string* _internal_mutable_last_user_name();
  public:

  // string password = 2;
  void clear_password();
  const std::string& password() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_password(ArgT0&& arg0, ArgT... args);
  std::string* mutable_password();
  PROTOBUF_MUST_USE_RESULT std::string* release_password();
  void set_allocated_password(std::string* password);
  private:
  const std::string& _internal_password() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_password(const std::string& value);
  std::string* _internal_mutable_password();
  public:

  // bool remeber_password = 3;
  void clear_remeber_password();
  bool remeber_password() const;
  void set_remeber_password(bool value);
  private:
  bool _internal_remeber_password() const;
  void _internal_set_remeber_password(bool value);
  public:

  // @@protoc_insertion_point(class_scope:ApplicationConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr last_user_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr password_;
  bool remeber_password_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ApplicationConfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ApplicationConfig

// string last_user_name = 1;
inline void ApplicationConfig::clear_last_user_name() {
  last_user_name_.ClearToEmpty();
}
inline const std::string& ApplicationConfig::last_user_name() const {
  // @@protoc_insertion_point(field_get:ApplicationConfig.last_user_name)
  return _internal_last_user_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApplicationConfig::set_last_user_name(ArgT0&& arg0, ArgT... args) {
 
 last_user_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ApplicationConfig.last_user_name)
}
inline std::string* ApplicationConfig::mutable_last_user_name() {
  std::string* _s = _internal_mutable_last_user_name();
  // @@protoc_insertion_point(field_mutable:ApplicationConfig.last_user_name)
  return _s;
}
inline const std::string& ApplicationConfig::_internal_last_user_name() const {
  return last_user_name_.Get();
}
inline void ApplicationConfig::_internal_set_last_user_name(const std::string& value) {
  
  last_user_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ApplicationConfig::_internal_mutable_last_user_name() {
  
  return last_user_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ApplicationConfig::release_last_user_name() {
  // @@protoc_insertion_point(field_release:ApplicationConfig.last_user_name)
  return last_user_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ApplicationConfig::set_allocated_last_user_name(std::string* last_user_name) {
  if (last_user_name != nullptr) {
    
  } else {
    
  }
  last_user_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), last_user_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:ApplicationConfig.last_user_name)
}

// string password = 2;
inline void ApplicationConfig::clear_password() {
  password_.ClearToEmpty();
}
inline const std::string& ApplicationConfig::password() const {
  // @@protoc_insertion_point(field_get:ApplicationConfig.password)
  return _internal_password();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ApplicationConfig::set_password(ArgT0&& arg0, ArgT... args) {
 
 password_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ApplicationConfig.password)
}
inline std::string* ApplicationConfig::mutable_password() {
  std::string* _s = _internal_mutable_password();
  // @@protoc_insertion_point(field_mutable:ApplicationConfig.password)
  return _s;
}
inline const std::string& ApplicationConfig::_internal_password() const {
  return password_.Get();
}
inline void ApplicationConfig::_internal_set_password(const std::string& value) {
  
  password_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ApplicationConfig::_internal_mutable_password() {
  
  return password_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ApplicationConfig::release_password() {
  // @@protoc_insertion_point(field_release:ApplicationConfig.password)
  return password_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ApplicationConfig::set_allocated_password(std::string* password) {
  if (password != nullptr) {
    
  } else {
    
  }
  password_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), password,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:ApplicationConfig.password)
}

// bool remeber_password = 3;
inline void ApplicationConfig::clear_remeber_password() {
  remeber_password_ = false;
}
inline bool ApplicationConfig::_internal_remeber_password() const {
  return remeber_password_;
}
inline bool ApplicationConfig::remeber_password() const {
  // @@protoc_insertion_point(field_get:ApplicationConfig.remeber_password)
  return _internal_remeber_password();
}
inline void ApplicationConfig::_internal_set_remeber_password(bool value) {
  
  remeber_password_ = value;
}
inline void ApplicationConfig::set_remeber_password(bool value) {
  _internal_set_remeber_password(value);
  // @@protoc_insertion_point(field_set:ApplicationConfig.remeber_password)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)


// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ApplicationConfig_2eproto
