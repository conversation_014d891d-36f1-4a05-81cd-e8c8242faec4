// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: AssociateData.proto

#include "AssociateData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr AssociateData::AssociateData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meetcondition_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , dictvalue_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , belongid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bkid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , meetconditionvalue_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(int64_t{0})
  , type_(0)
  , ismate_(0)
  , associationid_(int64_t{0}){}
struct AssociateDataDefaultTypeInternal {
  constexpr AssociateDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AssociateDataDefaultTypeInternal() {}
  union {
    AssociateData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AssociateDataDefaultTypeInternal _AssociateData_default_instance_;
constexpr AssociateListData::AssociateListData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bkassociationdataillist_()
  , correlationtype_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , correlationtypename_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ismate_(0){}
struct AssociateListDataDefaultTypeInternal {
  constexpr AssociateListDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AssociateListDataDefaultTypeInternal() {}
  union {
    AssociateListData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AssociateListDataDefaultTypeInternal _AssociateListData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_AssociateData_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_AssociateData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_AssociateData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_AssociateData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, meetcondition_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, ismate_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, dictvalue_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, belongid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, associationid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, bkid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateData, meetconditionvalue_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateListData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateListData, correlationtype_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateListData, correlationtypename_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateListData, ismate_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::AssociateListData, bkassociationdataillist_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::AssociateData)},
  { 16, -1, -1, sizeof(::catalog_studio_message::AssociateListData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_AssociateData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_AssociateListData_default_instance_),
};

const char descriptor_table_protodef_AssociateData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023AssociateData.proto\022\026catalog_studio_me"
  "ssage\"\304\001\n\rAssociateData\022\n\n\002id\030\001 \001(\003\022\014\n\004n"
  "ame\030\002 \001(\t\022\025\n\rmeetCondition\030\003 \001(\t\022\014\n\004type"
  "\030\004 \001(\005\022\016\n\006isMate\030\005 \001(\005\022\021\n\tdictValue\030\006 \001("
  "\t\022\020\n\010belongId\030\007 \001(\t\022\025\n\rassociationId\030\010 \001"
  "(\003\022\014\n\004bkId\030\t \001(\t\022\032\n\022meetConditionValue\030\n"
  " \001(\t\"\241\001\n\021AssociateListData\022\027\n\017correlatio"
  "nType\030\001 \001(\t\022\033\n\023correlationTypeName\030\002 \001(\t"
  "\022\016\n\006isMate\030\003 \001(\005\022F\n\027bkAssociationDatailL"
  "ist\030\004 \003(\0132%.catalog_studio_message.Assoc"
  "iateDatab\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_AssociateData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_AssociateData_2eproto = {
  false, false, 416, descriptor_table_protodef_AssociateData_2eproto, "AssociateData.proto", 
  &descriptor_table_AssociateData_2eproto_once, nullptr, 0, 2,
  schemas, file_default_instances, TableStruct_AssociateData_2eproto::offsets,
  file_level_metadata_AssociateData_2eproto, file_level_enum_descriptors_AssociateData_2eproto, file_level_service_descriptors_AssociateData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_AssociateData_2eproto_getter() {
  return &descriptor_table_AssociateData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_AssociateData_2eproto(&descriptor_table_AssociateData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class AssociateData::_Internal {
 public:
};

AssociateData::AssociateData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.AssociateData)
}
AssociateData::AssociateData(const AssociateData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  meetcondition_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_meetcondition().empty()) {
    meetcondition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_meetcondition(), 
      GetArenaForAllocation());
  }
  dictvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_dictvalue().empty()) {
    dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dictvalue(), 
      GetArenaForAllocation());
  }
  belongid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_belongid().empty()) {
    belongid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_belongid(), 
      GetArenaForAllocation());
  }
  bkid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_bkid().empty()) {
    bkid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bkid(), 
      GetArenaForAllocation());
  }
  meetconditionvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_meetconditionvalue().empty()) {
    meetconditionvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_meetconditionvalue(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&associationid_) -
    reinterpret_cast<char*>(&id_)) + sizeof(associationid_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.AssociateData)
}

void AssociateData::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
meetcondition_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
dictvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
belongid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
bkid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
meetconditionvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&associationid_) -
    reinterpret_cast<char*>(&id_)) + sizeof(associationid_));
}

AssociateData::~AssociateData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.AssociateData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AssociateData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  meetcondition_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  dictvalue_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  belongid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bkid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  meetconditionvalue_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AssociateData::ArenaDtor(void* object) {
  AssociateData* _this = reinterpret_cast< AssociateData* >(object);
  (void)_this;
}
void AssociateData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AssociateData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AssociateData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.AssociateData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  meetcondition_.ClearToEmpty();
  dictvalue_.ClearToEmpty();
  belongid_.ClearToEmpty();
  bkid_.ClearToEmpty();
  meetconditionvalue_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&associationid_) -
      reinterpret_cast<char*>(&id_)) + sizeof(associationid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AssociateData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string meetCondition = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_meetcondition();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.meetCondition"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isMate = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          ismate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string dictValue = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_dictvalue();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.dictValue"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string belongId = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_belongid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.belongId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 associationId = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          associationid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string bkId = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_bkid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.bkId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string meetConditionValue = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_meetconditionvalue();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateData.meetConditionValue"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AssociateData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.AssociateData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string meetCondition = 3;
  if (!this->_internal_meetcondition().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_meetcondition().data(), static_cast<int>(this->_internal_meetcondition().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.meetCondition");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_meetcondition(), target);
  }

  // int32 type = 4;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_type(), target);
  }

  // int32 isMate = 5;
  if (this->_internal_ismate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_ismate(), target);
  }

  // string dictValue = 6;
  if (!this->_internal_dictvalue().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dictvalue().data(), static_cast<int>(this->_internal_dictvalue().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.dictValue");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_dictvalue(), target);
  }

  // string belongId = 7;
  if (!this->_internal_belongid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_belongid().data(), static_cast<int>(this->_internal_belongid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.belongId");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_belongid(), target);
  }

  // int64 associationId = 8;
  if (this->_internal_associationid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(8, this->_internal_associationid(), target);
  }

  // string bkId = 9;
  if (!this->_internal_bkid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_bkid().data(), static_cast<int>(this->_internal_bkid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.bkId");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_bkid(), target);
  }

  // string meetConditionValue = 10;
  if (!this->_internal_meetconditionvalue().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_meetconditionvalue().data(), static_cast<int>(this->_internal_meetconditionvalue().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateData.meetConditionValue");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_meetconditionvalue(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.AssociateData)
  return target;
}

size_t AssociateData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.AssociateData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string meetCondition = 3;
  if (!this->_internal_meetcondition().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_meetcondition());
  }

  // string dictValue = 6;
  if (!this->_internal_dictvalue().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dictvalue());
  }

  // string belongId = 7;
  if (!this->_internal_belongid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_belongid());
  }

  // string bkId = 9;
  if (!this->_internal_bkid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_bkid());
  }

  // string meetConditionValue = 10;
  if (!this->_internal_meetconditionvalue().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_meetconditionvalue());
  }

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // int32 type = 4;
  if (this->_internal_type() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_type());
  }

  // int32 isMate = 5;
  if (this->_internal_ismate() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_ismate());
  }

  // int64 associationId = 8;
  if (this->_internal_associationid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_associationid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AssociateData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AssociateData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AssociateData::GetClassData() const { return &_class_data_; }

void AssociateData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AssociateData *>(to)->MergeFrom(
      static_cast<const AssociateData &>(from));
}


void AssociateData::MergeFrom(const AssociateData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.AssociateData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_meetcondition().empty()) {
    _internal_set_meetcondition(from._internal_meetcondition());
  }
  if (!from._internal_dictvalue().empty()) {
    _internal_set_dictvalue(from._internal_dictvalue());
  }
  if (!from._internal_belongid().empty()) {
    _internal_set_belongid(from._internal_belongid());
  }
  if (!from._internal_bkid().empty()) {
    _internal_set_bkid(from._internal_bkid());
  }
  if (!from._internal_meetconditionvalue().empty()) {
    _internal_set_meetconditionvalue(from._internal_meetconditionvalue());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_ismate() != 0) {
    _internal_set_ismate(from._internal_ismate());
  }
  if (from._internal_associationid() != 0) {
    _internal_set_associationid(from._internal_associationid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AssociateData::CopyFrom(const AssociateData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.AssociateData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AssociateData::IsInitialized() const {
  return true;
}

void AssociateData::InternalSwap(AssociateData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &meetcondition_, lhs_arena,
      &other->meetcondition_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &dictvalue_, lhs_arena,
      &other->dictvalue_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &belongid_, lhs_arena,
      &other->belongid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bkid_, lhs_arena,
      &other->bkid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &meetconditionvalue_, lhs_arena,
      &other->meetconditionvalue_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AssociateData, associationid_)
      + sizeof(AssociateData::associationid_)
      - PROTOBUF_FIELD_OFFSET(AssociateData, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AssociateData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_AssociateData_2eproto_getter, &descriptor_table_AssociateData_2eproto_once,
      file_level_metadata_AssociateData_2eproto[0]);
}

// ===================================================================

class AssociateListData::_Internal {
 public:
};

AssociateListData::AssociateListData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bkassociationdataillist_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.AssociateListData)
}
AssociateListData::AssociateListData(const AssociateListData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      bkassociationdataillist_(from.bkassociationdataillist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  correlationtype_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_correlationtype().empty()) {
    correlationtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_correlationtype(), 
      GetArenaForAllocation());
  }
  correlationtypename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_correlationtypename().empty()) {
    correlationtypename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_correlationtypename(), 
      GetArenaForAllocation());
  }
  ismate_ = from.ismate_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.AssociateListData)
}

void AssociateListData::SharedCtor() {
correlationtype_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
correlationtypename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
ismate_ = 0;
}

AssociateListData::~AssociateListData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.AssociateListData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AssociateListData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  correlationtype_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  correlationtypename_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AssociateListData::ArenaDtor(void* object) {
  AssociateListData* _this = reinterpret_cast< AssociateListData* >(object);
  (void)_this;
}
void AssociateListData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AssociateListData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AssociateListData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.AssociateListData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bkassociationdataillist_.Clear();
  correlationtype_.ClearToEmpty();
  correlationtypename_.ClearToEmpty();
  ismate_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AssociateListData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string correlationType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_correlationtype();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateListData.correlationType"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string correlationTypeName = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_correlationtypename();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.AssociateListData.correlationTypeName"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isMate = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ismate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.AssociateData bkAssociationDatailList = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_bkassociationdataillist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AssociateListData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.AssociateListData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string correlationType = 1;
  if (!this->_internal_correlationtype().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_correlationtype().data(), static_cast<int>(this->_internal_correlationtype().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateListData.correlationType");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_correlationtype(), target);
  }

  // string correlationTypeName = 2;
  if (!this->_internal_correlationtypename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_correlationtypename().data(), static_cast<int>(this->_internal_correlationtypename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.AssociateListData.correlationTypeName");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_correlationtypename(), target);
  }

  // int32 isMate = 3;
  if (this->_internal_ismate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_ismate(), target);
  }

  // repeated .catalog_studio_message.AssociateData bkAssociationDatailList = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_bkassociationdataillist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_bkassociationdataillist(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.AssociateListData)
  return target;
}

size_t AssociateListData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.AssociateListData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.AssociateData bkAssociationDatailList = 4;
  total_size += 1UL * this->_internal_bkassociationdataillist_size();
  for (const auto& msg : this->bkassociationdataillist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string correlationType = 1;
  if (!this->_internal_correlationtype().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_correlationtype());
  }

  // string correlationTypeName = 2;
  if (!this->_internal_correlationtypename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_correlationtypename());
  }

  // int32 isMate = 3;
  if (this->_internal_ismate() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_ismate());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AssociateListData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AssociateListData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AssociateListData::GetClassData() const { return &_class_data_; }

void AssociateListData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AssociateListData *>(to)->MergeFrom(
      static_cast<const AssociateListData &>(from));
}


void AssociateListData::MergeFrom(const AssociateListData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.AssociateListData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  bkassociationdataillist_.MergeFrom(from.bkassociationdataillist_);
  if (!from._internal_correlationtype().empty()) {
    _internal_set_correlationtype(from._internal_correlationtype());
  }
  if (!from._internal_correlationtypename().empty()) {
    _internal_set_correlationtypename(from._internal_correlationtypename());
  }
  if (from._internal_ismate() != 0) {
    _internal_set_ismate(from._internal_ismate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AssociateListData::CopyFrom(const AssociateListData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.AssociateListData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AssociateListData::IsInitialized() const {
  return true;
}

void AssociateListData::InternalSwap(AssociateListData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bkassociationdataillist_.InternalSwap(&other->bkassociationdataillist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &correlationtype_, lhs_arena,
      &other->correlationtype_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &correlationtypename_, lhs_arena,
      &other->correlationtypename_, rhs_arena
  );
  swap(ismate_, other->ismate_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AssociateListData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_AssociateData_2eproto_getter, &descriptor_table_AssociateData_2eproto_once,
      file_level_metadata_AssociateData_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::AssociateData* Arena::CreateMaybeMessage< ::catalog_studio_message::AssociateData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::AssociateData >(arena);
}
template<> PROTOBUF_NOINLINE ::catalog_studio_message::AssociateListData* Arena::CreateMaybeMessage< ::catalog_studio_message::AssociateListData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::AssociateListData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
