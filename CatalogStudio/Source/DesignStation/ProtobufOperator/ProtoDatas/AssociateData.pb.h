// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: AssociateData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_AssociateData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_AssociateData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_AssociateData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_AssociateData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_AssociateData_2eproto;
namespace catalog_studio_message {
class AssociateData;
struct AssociateDataDefaultTypeInternal;
extern AssociateDataDefaultTypeInternal _AssociateData_default_instance_;
class AssociateListData;
struct AssociateListDataDefaultTypeInternal;
extern AssociateListDataDefaultTypeInternal _AssociateListData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::AssociateData* Arena::CreateMaybeMessage<::catalog_studio_message::AssociateData>(Arena*);
template<> ::catalog_studio_message::AssociateListData* Arena::CreateMaybeMessage<::catalog_studio_message::AssociateListData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class AssociateData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.AssociateData) */ {
 public:
  inline AssociateData() : AssociateData(nullptr) {}
  ~AssociateData() override;
  explicit constexpr AssociateData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AssociateData(const AssociateData& from);
  AssociateData(AssociateData&& from) noexcept
    : AssociateData() {
    *this = ::std::move(from);
  }

  inline AssociateData& operator=(const AssociateData& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssociateData& operator=(AssociateData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AssociateData& default_instance() {
    return *internal_default_instance();
  }
  static inline const AssociateData* internal_default_instance() {
    return reinterpret_cast<const AssociateData*>(
               &_AssociateData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AssociateData& a, AssociateData& b) {
    a.Swap(&b);
  }
  inline void Swap(AssociateData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AssociateData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AssociateData* New() const final {
    return new AssociateData();
  }

  AssociateData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AssociateData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AssociateData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AssociateData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssociateData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.AssociateData";
  }
  protected:
  explicit AssociateData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kMeetConditionFieldNumber = 3,
    kDictValueFieldNumber = 6,
    kBelongIdFieldNumber = 7,
    kBkIdFieldNumber = 9,
    kMeetConditionValueFieldNumber = 10,
    kIdFieldNumber = 1,
    kTypeFieldNumber = 4,
    kIsMateFieldNumber = 5,
    kAssociationIdFieldNumber = 8,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string meetCondition = 3;
  void clear_meetcondition();
  const std::string& meetcondition() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meetcondition(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meetcondition();
  PROTOBUF_MUST_USE_RESULT std::string* release_meetcondition();
  void set_allocated_meetcondition(std::string* meetcondition);
  private:
  const std::string& _internal_meetcondition() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meetcondition(const std::string& value);
  std::string* _internal_mutable_meetcondition();
  public:

  // string dictValue = 6;
  void clear_dictvalue();
  const std::string& dictvalue() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dictvalue(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dictvalue();
  PROTOBUF_MUST_USE_RESULT std::string* release_dictvalue();
  void set_allocated_dictvalue(std::string* dictvalue);
  private:
  const std::string& _internal_dictvalue() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dictvalue(const std::string& value);
  std::string* _internal_mutable_dictvalue();
  public:

  // string belongId = 7;
  void clear_belongid();
  const std::string& belongid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_belongid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_belongid();
  PROTOBUF_MUST_USE_RESULT std::string* release_belongid();
  void set_allocated_belongid(std::string* belongid);
  private:
  const std::string& _internal_belongid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_belongid(const std::string& value);
  std::string* _internal_mutable_belongid();
  public:

  // string bkId = 9;
  void clear_bkid();
  const std::string& bkid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bkid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bkid();
  PROTOBUF_MUST_USE_RESULT std::string* release_bkid();
  void set_allocated_bkid(std::string* bkid);
  private:
  const std::string& _internal_bkid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bkid(const std::string& value);
  std::string* _internal_mutable_bkid();
  public:

  // string meetConditionValue = 10;
  void clear_meetconditionvalue();
  const std::string& meetconditionvalue() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meetconditionvalue(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meetconditionvalue();
  PROTOBUF_MUST_USE_RESULT std::string* release_meetconditionvalue();
  void set_allocated_meetconditionvalue(std::string* meetconditionvalue);
  private:
  const std::string& _internal_meetconditionvalue() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meetconditionvalue(const std::string& value);
  std::string* _internal_mutable_meetconditionvalue();
  public:

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int32 type = 4;
  void clear_type();
  ::PROTOBUF_NAMESPACE_ID::int32 type() const;
  void set_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_type() const;
  void _internal_set_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isMate = 5;
  void clear_ismate();
  ::PROTOBUF_NAMESPACE_ID::int32 ismate() const;
  void set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_ismate() const;
  void _internal_set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int64 associationId = 8;
  void clear_associationid();
  ::PROTOBUF_NAMESPACE_ID::int64 associationid() const;
  void set_associationid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_associationid() const;
  void _internal_set_associationid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.AssociateData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meetcondition_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dictvalue_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr belongid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bkid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meetconditionvalue_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 type_;
  ::PROTOBUF_NAMESPACE_ID::int32 ismate_;
  ::PROTOBUF_NAMESPACE_ID::int64 associationid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_AssociateData_2eproto;
};
// -------------------------------------------------------------------

class AssociateListData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.AssociateListData) */ {
 public:
  inline AssociateListData() : AssociateListData(nullptr) {}
  ~AssociateListData() override;
  explicit constexpr AssociateListData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AssociateListData(const AssociateListData& from);
  AssociateListData(AssociateListData&& from) noexcept
    : AssociateListData() {
    *this = ::std::move(from);
  }

  inline AssociateListData& operator=(const AssociateListData& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssociateListData& operator=(AssociateListData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AssociateListData& default_instance() {
    return *internal_default_instance();
  }
  static inline const AssociateListData* internal_default_instance() {
    return reinterpret_cast<const AssociateListData*>(
               &_AssociateListData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AssociateListData& a, AssociateListData& b) {
    a.Swap(&b);
  }
  inline void Swap(AssociateListData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AssociateListData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AssociateListData* New() const final {
    return new AssociateListData();
  }

  AssociateListData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AssociateListData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AssociateListData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AssociateListData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssociateListData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.AssociateListData";
  }
  protected:
  explicit AssociateListData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBkAssociationDatailListFieldNumber = 4,
    kCorrelationTypeFieldNumber = 1,
    kCorrelationTypeNameFieldNumber = 2,
    kIsMateFieldNumber = 3,
  };
  // repeated .catalog_studio_message.AssociateData bkAssociationDatailList = 4;
  int bkassociationdataillist_size() const;
  private:
  int _internal_bkassociationdataillist_size() const;
  public:
  void clear_bkassociationdataillist();
  ::catalog_studio_message::AssociateData* mutable_bkassociationdataillist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateData >*
      mutable_bkassociationdataillist();
  private:
  const ::catalog_studio_message::AssociateData& _internal_bkassociationdataillist(int index) const;
  ::catalog_studio_message::AssociateData* _internal_add_bkassociationdataillist();
  public:
  const ::catalog_studio_message::AssociateData& bkassociationdataillist(int index) const;
  ::catalog_studio_message::AssociateData* add_bkassociationdataillist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateData >&
      bkassociationdataillist() const;

  // string correlationType = 1;
  void clear_correlationtype();
  const std::string& correlationtype() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_correlationtype(ArgT0&& arg0, ArgT... args);
  std::string* mutable_correlationtype();
  PROTOBUF_MUST_USE_RESULT std::string* release_correlationtype();
  void set_allocated_correlationtype(std::string* correlationtype);
  private:
  const std::string& _internal_correlationtype() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_correlationtype(const std::string& value);
  std::string* _internal_mutable_correlationtype();
  public:

  // string correlationTypeName = 2;
  void clear_correlationtypename();
  const std::string& correlationtypename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_correlationtypename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_correlationtypename();
  PROTOBUF_MUST_USE_RESULT std::string* release_correlationtypename();
  void set_allocated_correlationtypename(std::string* correlationtypename);
  private:
  const std::string& _internal_correlationtypename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_correlationtypename(const std::string& value);
  std::string* _internal_mutable_correlationtypename();
  public:

  // int32 isMate = 3;
  void clear_ismate();
  ::PROTOBUF_NAMESPACE_ID::int32 ismate() const;
  void set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_ismate() const;
  void _internal_set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.AssociateListData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateData > bkassociationdataillist_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr correlationtype_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr correlationtypename_;
  ::PROTOBUF_NAMESPACE_ID::int32 ismate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_AssociateData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AssociateData

// int64 id = 1;
inline void AssociateData::clear_id() {
  id_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AssociateData::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AssociateData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.id)
  return _internal_id();
}
inline void AssociateData::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
}
inline void AssociateData::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.id)
}

// string name = 2;
inline void AssociateData::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& AssociateData::name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.name)
}
inline std::string* AssociateData::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.name)
  return _s;
}
inline const std::string& AssociateData::_internal_name() const {
  return name_.Get();
}
inline void AssociateData::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.name)
}

// string meetCondition = 3;
inline void AssociateData::clear_meetcondition() {
  meetcondition_.ClearToEmpty();
}
inline const std::string& AssociateData::meetcondition() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.meetCondition)
  return _internal_meetcondition();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_meetcondition(ArgT0&& arg0, ArgT... args) {
 
 meetcondition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.meetCondition)
}
inline std::string* AssociateData::mutable_meetcondition() {
  std::string* _s = _internal_mutable_meetcondition();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.meetCondition)
  return _s;
}
inline const std::string& AssociateData::_internal_meetcondition() const {
  return meetcondition_.Get();
}
inline void AssociateData::_internal_set_meetcondition(const std::string& value) {
  
  meetcondition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_meetcondition() {
  
  return meetcondition_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_meetcondition() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.meetCondition)
  return meetcondition_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_meetcondition(std::string* meetcondition) {
  if (meetcondition != nullptr) {
    
  } else {
    
  }
  meetcondition_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), meetcondition,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.meetCondition)
}

// int32 type = 4;
inline void AssociateData::clear_type() {
  type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateData::_internal_type() const {
  return type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateData::type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.type)
  return _internal_type();
}
inline void AssociateData::_internal_set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  type_ = value;
}
inline void AssociateData::set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.type)
}

// int32 isMate = 5;
inline void AssociateData::clear_ismate() {
  ismate_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateData::_internal_ismate() const {
  return ismate_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateData::ismate() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.isMate)
  return _internal_ismate();
}
inline void AssociateData::_internal_set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  ismate_ = value;
}
inline void AssociateData::set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_ismate(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.isMate)
}

// string dictValue = 6;
inline void AssociateData::clear_dictvalue() {
  dictvalue_.ClearToEmpty();
}
inline const std::string& AssociateData::dictvalue() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.dictValue)
  return _internal_dictvalue();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_dictvalue(ArgT0&& arg0, ArgT... args) {
 
 dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.dictValue)
}
inline std::string* AssociateData::mutable_dictvalue() {
  std::string* _s = _internal_mutable_dictvalue();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.dictValue)
  return _s;
}
inline const std::string& AssociateData::_internal_dictvalue() const {
  return dictvalue_.Get();
}
inline void AssociateData::_internal_set_dictvalue(const std::string& value) {
  
  dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_dictvalue() {
  
  return dictvalue_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_dictvalue() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.dictValue)
  return dictvalue_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_dictvalue(std::string* dictvalue) {
  if (dictvalue != nullptr) {
    
  } else {
    
  }
  dictvalue_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dictvalue,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.dictValue)
}

// string belongId = 7;
inline void AssociateData::clear_belongid() {
  belongid_.ClearToEmpty();
}
inline const std::string& AssociateData::belongid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.belongId)
  return _internal_belongid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_belongid(ArgT0&& arg0, ArgT... args) {
 
 belongid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.belongId)
}
inline std::string* AssociateData::mutable_belongid() {
  std::string* _s = _internal_mutable_belongid();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.belongId)
  return _s;
}
inline const std::string& AssociateData::_internal_belongid() const {
  return belongid_.Get();
}
inline void AssociateData::_internal_set_belongid(const std::string& value) {
  
  belongid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_belongid() {
  
  return belongid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_belongid() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.belongId)
  return belongid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_belongid(std::string* belongid) {
  if (belongid != nullptr) {
    
  } else {
    
  }
  belongid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), belongid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.belongId)
}

// int64 associationId = 8;
inline void AssociateData::clear_associationid() {
  associationid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AssociateData::_internal_associationid() const {
  return associationid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 AssociateData::associationid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.associationId)
  return _internal_associationid();
}
inline void AssociateData::_internal_set_associationid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  associationid_ = value;
}
inline void AssociateData::set_associationid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_associationid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.associationId)
}

// string bkId = 9;
inline void AssociateData::clear_bkid() {
  bkid_.ClearToEmpty();
}
inline const std::string& AssociateData::bkid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.bkId)
  return _internal_bkid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_bkid(ArgT0&& arg0, ArgT... args) {
 
 bkid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.bkId)
}
inline std::string* AssociateData::mutable_bkid() {
  std::string* _s = _internal_mutable_bkid();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.bkId)
  return _s;
}
inline const std::string& AssociateData::_internal_bkid() const {
  return bkid_.Get();
}
inline void AssociateData::_internal_set_bkid(const std::string& value) {
  
  bkid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_bkid() {
  
  return bkid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_bkid() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.bkId)
  return bkid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_bkid(std::string* bkid) {
  if (bkid != nullptr) {
    
  } else {
    
  }
  bkid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bkid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.bkId)
}

// string meetConditionValue = 10;
inline void AssociateData::clear_meetconditionvalue() {
  meetconditionvalue_.ClearToEmpty();
}
inline const std::string& AssociateData::meetconditionvalue() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateData.meetConditionValue)
  return _internal_meetconditionvalue();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateData::set_meetconditionvalue(ArgT0&& arg0, ArgT... args) {
 
 meetconditionvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateData.meetConditionValue)
}
inline std::string* AssociateData::mutable_meetconditionvalue() {
  std::string* _s = _internal_mutable_meetconditionvalue();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateData.meetConditionValue)
  return _s;
}
inline const std::string& AssociateData::_internal_meetconditionvalue() const {
  return meetconditionvalue_.Get();
}
inline void AssociateData::_internal_set_meetconditionvalue(const std::string& value) {
  
  meetconditionvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateData::_internal_mutable_meetconditionvalue() {
  
  return meetconditionvalue_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateData::release_meetconditionvalue() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateData.meetConditionValue)
  return meetconditionvalue_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateData::set_allocated_meetconditionvalue(std::string* meetconditionvalue) {
  if (meetconditionvalue != nullptr) {
    
  } else {
    
  }
  meetconditionvalue_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), meetconditionvalue,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateData.meetConditionValue)
}

// -------------------------------------------------------------------

// AssociateListData

// string correlationType = 1;
inline void AssociateListData::clear_correlationtype() {
  correlationtype_.ClearToEmpty();
}
inline const std::string& AssociateListData::correlationtype() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateListData.correlationType)
  return _internal_correlationtype();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateListData::set_correlationtype(ArgT0&& arg0, ArgT... args) {
 
 correlationtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateListData.correlationType)
}
inline std::string* AssociateListData::mutable_correlationtype() {
  std::string* _s = _internal_mutable_correlationtype();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateListData.correlationType)
  return _s;
}
inline const std::string& AssociateListData::_internal_correlationtype() const {
  return correlationtype_.Get();
}
inline void AssociateListData::_internal_set_correlationtype(const std::string& value) {
  
  correlationtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateListData::_internal_mutable_correlationtype() {
  
  return correlationtype_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateListData::release_correlationtype() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateListData.correlationType)
  return correlationtype_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateListData::set_allocated_correlationtype(std::string* correlationtype) {
  if (correlationtype != nullptr) {
    
  } else {
    
  }
  correlationtype_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), correlationtype,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateListData.correlationType)
}

// string correlationTypeName = 2;
inline void AssociateListData::clear_correlationtypename() {
  correlationtypename_.ClearToEmpty();
}
inline const std::string& AssociateListData::correlationtypename() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateListData.correlationTypeName)
  return _internal_correlationtypename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssociateListData::set_correlationtypename(ArgT0&& arg0, ArgT... args) {
 
 correlationtypename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateListData.correlationTypeName)
}
inline std::string* AssociateListData::mutable_correlationtypename() {
  std::string* _s = _internal_mutable_correlationtypename();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateListData.correlationTypeName)
  return _s;
}
inline const std::string& AssociateListData::_internal_correlationtypename() const {
  return correlationtypename_.Get();
}
inline void AssociateListData::_internal_set_correlationtypename(const std::string& value) {
  
  correlationtypename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AssociateListData::_internal_mutable_correlationtypename() {
  
  return correlationtypename_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AssociateListData::release_correlationtypename() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.AssociateListData.correlationTypeName)
  return correlationtypename_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AssociateListData::set_allocated_correlationtypename(std::string* correlationtypename) {
  if (correlationtypename != nullptr) {
    
  } else {
    
  }
  correlationtypename_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), correlationtypename,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.AssociateListData.correlationTypeName)
}

// int32 isMate = 3;
inline void AssociateListData::clear_ismate() {
  ismate_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateListData::_internal_ismate() const {
  return ismate_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AssociateListData::ismate() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateListData.isMate)
  return _internal_ismate();
}
inline void AssociateListData::_internal_set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  ismate_ = value;
}
inline void AssociateListData::set_ismate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_ismate(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.AssociateListData.isMate)
}

// repeated .catalog_studio_message.AssociateData bkAssociationDatailList = 4;
inline int AssociateListData::_internal_bkassociationdataillist_size() const {
  return bkassociationdataillist_.size();
}
inline int AssociateListData::bkassociationdataillist_size() const {
  return _internal_bkassociationdataillist_size();
}
inline void AssociateListData::clear_bkassociationdataillist() {
  bkassociationdataillist_.Clear();
}
inline ::catalog_studio_message::AssociateData* AssociateListData::mutable_bkassociationdataillist(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.AssociateListData.bkAssociationDatailList)
  return bkassociationdataillist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateData >*
AssociateListData::mutable_bkassociationdataillist() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.AssociateListData.bkAssociationDatailList)
  return &bkassociationdataillist_;
}
inline const ::catalog_studio_message::AssociateData& AssociateListData::_internal_bkassociationdataillist(int index) const {
  return bkassociationdataillist_.Get(index);
}
inline const ::catalog_studio_message::AssociateData& AssociateListData::bkassociationdataillist(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.AssociateListData.bkAssociationDatailList)
  return _internal_bkassociationdataillist(index);
}
inline ::catalog_studio_message::AssociateData* AssociateListData::_internal_add_bkassociationdataillist() {
  return bkassociationdataillist_.Add();
}
inline ::catalog_studio_message::AssociateData* AssociateListData::add_bkassociationdataillist() {
  ::catalog_studio_message::AssociateData* _add = _internal_add_bkassociationdataillist();
  // @@protoc_insertion_point(field_add:catalog_studio_message.AssociateListData.bkAssociationDatailList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateData >&
AssociateListData::bkassociationdataillist() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.AssociateListData.bkAssociationDatailList)
  return bkassociationdataillist_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_AssociateData_2eproto
