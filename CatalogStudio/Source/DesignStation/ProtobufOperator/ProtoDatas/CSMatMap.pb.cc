// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CSMatMap.proto

#include "CSMatMap.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr CSMatMapData::CSMatMapData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : md5_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , attrname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , materialvalue_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(int64_t{0})
  , materialid_(int64_t{0})
  , adminid_(int64_t{0})
  , attrid_(int64_t{0})
  , delflag_(0){}
struct CSMatMapDataDefaultTypeInternal {
  constexpr CSMatMapDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CSMatMapDataDefaultTypeInternal() {}
  union {
    CSMatMapData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CSMatMapDataDefaultTypeInternal _CSMatMapData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_CSMatMap_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_CSMatMap_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_CSMatMap_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_CSMatMap_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, materialid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, adminid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, md5_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, attrid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, attrname_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, materialvalue_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, delflag_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, createdby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, createdtime_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, updatedby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatMapData, updatedtime_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::CSMatMapData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_CSMatMapData_default_instance_),
};

const char descriptor_table_protodef_CSMatMap_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\016CSMatMap.proto\022\026catalog_studio_message"
  "\"\346\001\n\014CSMatMapData\022\n\n\002id\030\001 \001(\003\022\022\n\nmateria"
  "lId\030\002 \001(\003\022\017\n\007adminId\030\003 \001(\003\022\013\n\003md5\030\004 \001(\t\022"
  "\016\n\006attrId\030\005 \001(\003\022\020\n\010attrName\030\006 \001(\t\022\025\n\rmat"
  "erialValue\030\007 \001(\t\022\017\n\007delFlag\030\010 \001(\005\022\021\n\tcre"
  "atedBy\030\t \001(\t\022\023\n\013createdTime\030\n \001(\t\022\021\n\tupd"
  "atedBy\030\013 \001(\t\022\023\n\013updatedTime\030\014 \001(\tb\006proto"
  "3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_CSMatMap_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CSMatMap_2eproto = {
  false, false, 281, descriptor_table_protodef_CSMatMap_2eproto, "CSMatMap.proto", 
  &descriptor_table_CSMatMap_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_CSMatMap_2eproto::offsets,
  file_level_metadata_CSMatMap_2eproto, file_level_enum_descriptors_CSMatMap_2eproto, file_level_service_descriptors_CSMatMap_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_CSMatMap_2eproto_getter() {
  return &descriptor_table_CSMatMap_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_CSMatMap_2eproto(&descriptor_table_CSMatMap_2eproto);
namespace catalog_studio_message {

// ===================================================================

class CSMatMapData::_Internal {
 public:
};

CSMatMapData::CSMatMapData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.CSMatMapData)
}
CSMatMapData::CSMatMapData(const CSMatMapData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_md5().empty()) {
    md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_md5(), 
      GetArenaForAllocation());
  }
  attrname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_attrname().empty()) {
    attrname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_attrname(), 
      GetArenaForAllocation());
  }
  materialvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_materialvalue().empty()) {
    materialvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_materialvalue(), 
      GetArenaForAllocation());
  }
  createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdby().empty()) {
    createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdby(), 
      GetArenaForAllocation());
  }
  createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdtime().empty()) {
    createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdtime(), 
      GetArenaForAllocation());
  }
  updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedby().empty()) {
    updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedby(), 
      GetArenaForAllocation());
  }
  updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedtime().empty()) {
    updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedtime(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&delflag_) -
    reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.CSMatMapData)
}

void CSMatMapData::SharedCtor() {
md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
attrname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
materialvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&delflag_) -
    reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
}

CSMatMapData::~CSMatMapData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.CSMatMapData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CSMatMapData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  md5_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  attrname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  materialvalue_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CSMatMapData::ArenaDtor(void* object) {
  CSMatMapData* _this = reinterpret_cast< CSMatMapData* >(object);
  (void)_this;
}
void CSMatMapData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CSMatMapData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CSMatMapData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.CSMatMapData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  md5_.ClearToEmpty();
  attrname_.ClearToEmpty();
  materialvalue_.ClearToEmpty();
  createdby_.ClearToEmpty();
  createdtime_.ClearToEmpty();
  updatedby_.ClearToEmpty();
  updatedtime_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&delflag_) -
      reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CSMatMapData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 materialId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          materialid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 adminId = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          adminid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string md5 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_md5();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.md5"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 attrId = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          attrid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string attrName = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_attrname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.attrName"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string materialValue = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_materialvalue();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.materialValue"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 delFlag = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          delflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdBy = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_createdby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.createdBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdTime = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_createdtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.createdTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedBy = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_updatedby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.updatedBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedTime = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_updatedtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatMapData.updatedTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CSMatMapData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.CSMatMapData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // int64 materialId = 2;
  if (this->_internal_materialid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_materialid(), target);
  }

  // int64 adminId = 3;
  if (this->_internal_adminid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_adminid(), target);
  }

  // string md5 = 4;
  if (!this->_internal_md5().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_md5().data(), static_cast<int>(this->_internal_md5().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.md5");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_md5(), target);
  }

  // int64 attrId = 5;
  if (this->_internal_attrid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_attrid(), target);
  }

  // string attrName = 6;
  if (!this->_internal_attrname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_attrname().data(), static_cast<int>(this->_internal_attrname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.attrName");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_attrname(), target);
  }

  // string materialValue = 7;
  if (!this->_internal_materialvalue().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_materialvalue().data(), static_cast<int>(this->_internal_materialvalue().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.materialValue");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_materialvalue(), target);
  }

  // int32 delFlag = 8;
  if (this->_internal_delflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(8, this->_internal_delflag(), target);
  }

  // string createdBy = 9;
  if (!this->_internal_createdby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdby().data(), static_cast<int>(this->_internal_createdby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.createdBy");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_createdby(), target);
  }

  // string createdTime = 10;
  if (!this->_internal_createdtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdtime().data(), static_cast<int>(this->_internal_createdtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.createdTime");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_createdtime(), target);
  }

  // string updatedBy = 11;
  if (!this->_internal_updatedby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedby().data(), static_cast<int>(this->_internal_updatedby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.updatedBy");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_updatedby(), target);
  }

  // string updatedTime = 12;
  if (!this->_internal_updatedtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedtime().data(), static_cast<int>(this->_internal_updatedtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatMapData.updatedTime");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_updatedtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.CSMatMapData)
  return target;
}

size_t CSMatMapData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.CSMatMapData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string md5 = 4;
  if (!this->_internal_md5().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_md5());
  }

  // string attrName = 6;
  if (!this->_internal_attrname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_attrname());
  }

  // string materialValue = 7;
  if (!this->_internal_materialvalue().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_materialvalue());
  }

  // string createdBy = 9;
  if (!this->_internal_createdby().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdby());
  }

  // string createdTime = 10;
  if (!this->_internal_createdtime().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdtime());
  }

  // string updatedBy = 11;
  if (!this->_internal_updatedby().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedby());
  }

  // string updatedTime = 12;
  if (!this->_internal_updatedtime().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedtime());
  }

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // int64 materialId = 2;
  if (this->_internal_materialid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_materialid());
  }

  // int64 adminId = 3;
  if (this->_internal_adminid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_adminid());
  }

  // int64 attrId = 5;
  if (this->_internal_attrid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_attrid());
  }

  // int32 delFlag = 8;
  if (this->_internal_delflag() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_delflag());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CSMatMapData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CSMatMapData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CSMatMapData::GetClassData() const { return &_class_data_; }

void CSMatMapData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CSMatMapData *>(to)->MergeFrom(
      static_cast<const CSMatMapData &>(from));
}


void CSMatMapData::MergeFrom(const CSMatMapData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.CSMatMapData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_md5().empty()) {
    _internal_set_md5(from._internal_md5());
  }
  if (!from._internal_attrname().empty()) {
    _internal_set_attrname(from._internal_attrname());
  }
  if (!from._internal_materialvalue().empty()) {
    _internal_set_materialvalue(from._internal_materialvalue());
  }
  if (!from._internal_createdby().empty()) {
    _internal_set_createdby(from._internal_createdby());
  }
  if (!from._internal_createdtime().empty()) {
    _internal_set_createdtime(from._internal_createdtime());
  }
  if (!from._internal_updatedby().empty()) {
    _internal_set_updatedby(from._internal_updatedby());
  }
  if (!from._internal_updatedtime().empty()) {
    _internal_set_updatedtime(from._internal_updatedtime());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_materialid() != 0) {
    _internal_set_materialid(from._internal_materialid());
  }
  if (from._internal_adminid() != 0) {
    _internal_set_adminid(from._internal_adminid());
  }
  if (from._internal_attrid() != 0) {
    _internal_set_attrid(from._internal_attrid());
  }
  if (from._internal_delflag() != 0) {
    _internal_set_delflag(from._internal_delflag());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CSMatMapData::CopyFrom(const CSMatMapData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.CSMatMapData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CSMatMapData::IsInitialized() const {
  return true;
}

void CSMatMapData::InternalSwap(CSMatMapData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &md5_, lhs_arena,
      &other->md5_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &attrname_, lhs_arena,
      &other->attrname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &materialvalue_, lhs_arena,
      &other->materialvalue_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdby_, lhs_arena,
      &other->createdby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdtime_, lhs_arena,
      &other->createdtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedby_, lhs_arena,
      &other->updatedby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedtime_, lhs_arena,
      &other->updatedtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CSMatMapData, delflag_)
      + sizeof(CSMatMapData::delflag_)
      - PROTOBUF_FIELD_OFFSET(CSMatMapData, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CSMatMapData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_CSMatMap_2eproto_getter, &descriptor_table_CSMatMap_2eproto_once,
      file_level_metadata_CSMatMap_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::CSMatMapData* Arena::CreateMaybeMessage< ::catalog_studio_message::CSMatMapData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::CSMatMapData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
