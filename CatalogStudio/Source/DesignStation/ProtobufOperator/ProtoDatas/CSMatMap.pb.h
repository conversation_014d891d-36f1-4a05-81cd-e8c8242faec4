// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CSMatMap.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_CSMatMap_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_CSMatMap_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_CSMatMap_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_CSMatMap_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CSMatMap_2eproto;
namespace catalog_studio_message {
class CSMatMapData;
struct CSMatMapDataDefaultTypeInternal;
extern CSMatMapDataDefaultTypeInternal _CSMatMapData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::CSMatMapData* Arena::CreateMaybeMessage<::catalog_studio_message::CSMatMapData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class CSMatMapData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.CSMatMapData) */ {
 public:
  inline CSMatMapData() : CSMatMapData(nullptr) {}
  ~CSMatMapData() override;
  explicit constexpr CSMatMapData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CSMatMapData(const CSMatMapData& from);
  CSMatMapData(CSMatMapData&& from) noexcept
    : CSMatMapData() {
    *this = ::std::move(from);
  }

  inline CSMatMapData& operator=(const CSMatMapData& from) {
    CopyFrom(from);
    return *this;
  }
  inline CSMatMapData& operator=(CSMatMapData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CSMatMapData& default_instance() {
    return *internal_default_instance();
  }
  static inline const CSMatMapData* internal_default_instance() {
    return reinterpret_cast<const CSMatMapData*>(
               &_CSMatMapData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CSMatMapData& a, CSMatMapData& b) {
    a.Swap(&b);
  }
  inline void Swap(CSMatMapData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CSMatMapData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CSMatMapData* New() const final {
    return new CSMatMapData();
  }

  CSMatMapData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CSMatMapData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CSMatMapData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CSMatMapData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CSMatMapData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.CSMatMapData";
  }
  protected:
  explicit CSMatMapData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMd5FieldNumber = 4,
    kAttrNameFieldNumber = 6,
    kMaterialValueFieldNumber = 7,
    kCreatedByFieldNumber = 9,
    kCreatedTimeFieldNumber = 10,
    kUpdatedByFieldNumber = 11,
    kUpdatedTimeFieldNumber = 12,
    kIdFieldNumber = 1,
    kMaterialIdFieldNumber = 2,
    kAdminIdFieldNumber = 3,
    kAttrIdFieldNumber = 5,
    kDelFlagFieldNumber = 8,
  };
  // string md5 = 4;
  void clear_md5();
  const std::string& md5() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_md5(ArgT0&& arg0, ArgT... args);
  std::string* mutable_md5();
  PROTOBUF_MUST_USE_RESULT std::string* release_md5();
  void set_allocated_md5(std::string* md5);
  private:
  const std::string& _internal_md5() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_md5(const std::string& value);
  std::string* _internal_mutable_md5();
  public:

  // string attrName = 6;
  void clear_attrname();
  const std::string& attrname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_attrname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_attrname();
  PROTOBUF_MUST_USE_RESULT std::string* release_attrname();
  void set_allocated_attrname(std::string* attrname);
  private:
  const std::string& _internal_attrname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_attrname(const std::string& value);
  std::string* _internal_mutable_attrname();
  public:

  // string materialValue = 7;
  void clear_materialvalue();
  const std::string& materialvalue() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_materialvalue(ArgT0&& arg0, ArgT... args);
  std::string* mutable_materialvalue();
  PROTOBUF_MUST_USE_RESULT std::string* release_materialvalue();
  void set_allocated_materialvalue(std::string* materialvalue);
  private:
  const std::string& _internal_materialvalue() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_materialvalue(const std::string& value);
  std::string* _internal_mutable_materialvalue();
  public:

  // string createdBy = 9;
  void clear_createdby();
  const std::string& createdby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdby();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdby();
  void set_allocated_createdby(std::string* createdby);
  private:
  const std::string& _internal_createdby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdby(const std::string& value);
  std::string* _internal_mutable_createdby();
  public:

  // string createdTime = 10;
  void clear_createdtime();
  const std::string& createdtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdtime();
  void set_allocated_createdtime(std::string* createdtime);
  private:
  const std::string& _internal_createdtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdtime(const std::string& value);
  std::string* _internal_mutable_createdtime();
  public:

  // string updatedBy = 11;
  void clear_updatedby();
  const std::string& updatedby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedby();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedby();
  void set_allocated_updatedby(std::string* updatedby);
  private:
  const std::string& _internal_updatedby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedby(const std::string& value);
  std::string* _internal_mutable_updatedby();
  public:

  // string updatedTime = 12;
  void clear_updatedtime();
  const std::string& updatedtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedtime();
  void set_allocated_updatedtime(std::string* updatedtime);
  private:
  const std::string& _internal_updatedtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedtime(const std::string& value);
  std::string* _internal_mutable_updatedtime();
  public:

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int64 materialId = 2;
  void clear_materialid();
  ::PROTOBUF_NAMESPACE_ID::int64 materialid() const;
  void set_materialid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_materialid() const;
  void _internal_set_materialid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int64 adminId = 3;
  void clear_adminid();
  ::PROTOBUF_NAMESPACE_ID::int64 adminid() const;
  void set_adminid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_adminid() const;
  void _internal_set_adminid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int64 attrId = 5;
  void clear_attrid();
  ::PROTOBUF_NAMESPACE_ID::int64 attrid() const;
  void set_attrid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_attrid() const;
  void _internal_set_attrid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int32 delFlag = 8;
  void clear_delflag();
  ::PROTOBUF_NAMESPACE_ID::int32 delflag() const;
  void set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_delflag() const;
  void _internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.CSMatMapData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr md5_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr attrname_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr materialvalue_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdtime_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedtime_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int64 materialid_;
  ::PROTOBUF_NAMESPACE_ID::int64 adminid_;
  ::PROTOBUF_NAMESPACE_ID::int64 attrid_;
  ::PROTOBUF_NAMESPACE_ID::int32 delflag_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_CSMatMap_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CSMatMapData

// int64 id = 1;
inline void CSMatMapData::clear_id() {
  id_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.id)
  return _internal_id();
}
inline void CSMatMapData::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
}
inline void CSMatMapData::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.id)
}

// int64 materialId = 2;
inline void CSMatMapData::clear_materialid() {
  materialid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::_internal_materialid() const {
  return materialid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::materialid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.materialId)
  return _internal_materialid();
}
inline void CSMatMapData::_internal_set_materialid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  materialid_ = value;
}
inline void CSMatMapData::set_materialid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_materialid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.materialId)
}

// int64 adminId = 3;
inline void CSMatMapData::clear_adminid() {
  adminid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::_internal_adminid() const {
  return adminid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::adminid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.adminId)
  return _internal_adminid();
}
inline void CSMatMapData::_internal_set_adminid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  adminid_ = value;
}
inline void CSMatMapData::set_adminid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_adminid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.adminId)
}

// string md5 = 4;
inline void CSMatMapData::clear_md5() {
  md5_.ClearToEmpty();
}
inline const std::string& CSMatMapData::md5() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.md5)
  return _internal_md5();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_md5(ArgT0&& arg0, ArgT... args) {
 
 md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.md5)
}
inline std::string* CSMatMapData::mutable_md5() {
  std::string* _s = _internal_mutable_md5();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.md5)
  return _s;
}
inline const std::string& CSMatMapData::_internal_md5() const {
  return md5_.Get();
}
inline void CSMatMapData::_internal_set_md5(const std::string& value) {
  
  md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_md5() {
  
  return md5_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_md5() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.md5)
  return md5_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_md5(std::string* md5) {
  if (md5 != nullptr) {
    
  } else {
    
  }
  md5_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), md5,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.md5)
}

// int64 attrId = 5;
inline void CSMatMapData::clear_attrid() {
  attrid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::_internal_attrid() const {
  return attrid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatMapData::attrid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.attrId)
  return _internal_attrid();
}
inline void CSMatMapData::_internal_set_attrid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  attrid_ = value;
}
inline void CSMatMapData::set_attrid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_attrid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.attrId)
}

// string attrName = 6;
inline void CSMatMapData::clear_attrname() {
  attrname_.ClearToEmpty();
}
inline const std::string& CSMatMapData::attrname() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.attrName)
  return _internal_attrname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_attrname(ArgT0&& arg0, ArgT... args) {
 
 attrname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.attrName)
}
inline std::string* CSMatMapData::mutable_attrname() {
  std::string* _s = _internal_mutable_attrname();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.attrName)
  return _s;
}
inline const std::string& CSMatMapData::_internal_attrname() const {
  return attrname_.Get();
}
inline void CSMatMapData::_internal_set_attrname(const std::string& value) {
  
  attrname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_attrname() {
  
  return attrname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_attrname() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.attrName)
  return attrname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_attrname(std::string* attrname) {
  if (attrname != nullptr) {
    
  } else {
    
  }
  attrname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), attrname,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.attrName)
}

// string materialValue = 7;
inline void CSMatMapData::clear_materialvalue() {
  materialvalue_.ClearToEmpty();
}
inline const std::string& CSMatMapData::materialvalue() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.materialValue)
  return _internal_materialvalue();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_materialvalue(ArgT0&& arg0, ArgT... args) {
 
 materialvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.materialValue)
}
inline std::string* CSMatMapData::mutable_materialvalue() {
  std::string* _s = _internal_mutable_materialvalue();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.materialValue)
  return _s;
}
inline const std::string& CSMatMapData::_internal_materialvalue() const {
  return materialvalue_.Get();
}
inline void CSMatMapData::_internal_set_materialvalue(const std::string& value) {
  
  materialvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_materialvalue() {
  
  return materialvalue_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_materialvalue() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.materialValue)
  return materialvalue_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_materialvalue(std::string* materialvalue) {
  if (materialvalue != nullptr) {
    
  } else {
    
  }
  materialvalue_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), materialvalue,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.materialValue)
}

// int32 delFlag = 8;
inline void CSMatMapData::clear_delflag() {
  delflag_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatMapData::_internal_delflag() const {
  return delflag_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatMapData::delflag() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.delFlag)
  return _internal_delflag();
}
inline void CSMatMapData::_internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  delflag_ = value;
}
inline void CSMatMapData::set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_delflag(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.delFlag)
}

// string createdBy = 9;
inline void CSMatMapData::clear_createdby() {
  createdby_.ClearToEmpty();
}
inline const std::string& CSMatMapData::createdby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.createdBy)
  return _internal_createdby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_createdby(ArgT0&& arg0, ArgT... args) {
 
 createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.createdBy)
}
inline std::string* CSMatMapData::mutable_createdby() {
  std::string* _s = _internal_mutable_createdby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.createdBy)
  return _s;
}
inline const std::string& CSMatMapData::_internal_createdby() const {
  return createdby_.Get();
}
inline void CSMatMapData::_internal_set_createdby(const std::string& value) {
  
  createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_createdby() {
  
  return createdby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_createdby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.createdBy)
  return createdby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_createdby(std::string* createdby) {
  if (createdby != nullptr) {
    
  } else {
    
  }
  createdby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.createdBy)
}

// string createdTime = 10;
inline void CSMatMapData::clear_createdtime() {
  createdtime_.ClearToEmpty();
}
inline const std::string& CSMatMapData::createdtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.createdTime)
  return _internal_createdtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_createdtime(ArgT0&& arg0, ArgT... args) {
 
 createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.createdTime)
}
inline std::string* CSMatMapData::mutable_createdtime() {
  std::string* _s = _internal_mutable_createdtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.createdTime)
  return _s;
}
inline const std::string& CSMatMapData::_internal_createdtime() const {
  return createdtime_.Get();
}
inline void CSMatMapData::_internal_set_createdtime(const std::string& value) {
  
  createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_createdtime() {
  
  return createdtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_createdtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.createdTime)
  return createdtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_createdtime(std::string* createdtime) {
  if (createdtime != nullptr) {
    
  } else {
    
  }
  createdtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.createdTime)
}

// string updatedBy = 11;
inline void CSMatMapData::clear_updatedby() {
  updatedby_.ClearToEmpty();
}
inline const std::string& CSMatMapData::updatedby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.updatedBy)
  return _internal_updatedby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_updatedby(ArgT0&& arg0, ArgT... args) {
 
 updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.updatedBy)
}
inline std::string* CSMatMapData::mutable_updatedby() {
  std::string* _s = _internal_mutable_updatedby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.updatedBy)
  return _s;
}
inline const std::string& CSMatMapData::_internal_updatedby() const {
  return updatedby_.Get();
}
inline void CSMatMapData::_internal_set_updatedby(const std::string& value) {
  
  updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_updatedby() {
  
  return updatedby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_updatedby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.updatedBy)
  return updatedby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_updatedby(std::string* updatedby) {
  if (updatedby != nullptr) {
    
  } else {
    
  }
  updatedby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.updatedBy)
}

// string updatedTime = 12;
inline void CSMatMapData::clear_updatedtime() {
  updatedtime_.ClearToEmpty();
}
inline const std::string& CSMatMapData::updatedtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatMapData.updatedTime)
  return _internal_updatedtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatMapData::set_updatedtime(ArgT0&& arg0, ArgT... args) {
 
 updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatMapData.updatedTime)
}
inline std::string* CSMatMapData::mutable_updatedtime() {
  std::string* _s = _internal_mutable_updatedtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatMapData.updatedTime)
  return _s;
}
inline const std::string& CSMatMapData::_internal_updatedtime() const {
  return updatedtime_.Get();
}
inline void CSMatMapData::_internal_set_updatedtime(const std::string& value) {
  
  updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatMapData::_internal_mutable_updatedtime() {
  
  return updatedtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatMapData::release_updatedtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatMapData.updatedTime)
  return updatedtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatMapData::set_allocated_updatedtime(std::string* updatedtime) {
  if (updatedtime != nullptr) {
    
  } else {
    
  }
  updatedtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatMapData.updatedTime)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_CSMatMap_2eproto
