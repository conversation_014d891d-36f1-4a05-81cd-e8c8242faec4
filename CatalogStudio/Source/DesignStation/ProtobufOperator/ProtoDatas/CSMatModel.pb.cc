// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CSMatModel.proto

#include "CSMatModel.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr CSMatModelData::CSMatModelData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : managemapslist_()
  , labellist_()
  , modellist_()
  , name_(&::PROTOBU<PERSON>_NAMESPACE_ID::internal::fixed_address_empty_string)
  , code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , refpath_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pakfilepath_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , productimg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , brand_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , catename_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , materialcate_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , mapsimg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , depth_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , width_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , height_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , placementrules_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , dictgroupvalue_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , dictvalue_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , shader_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , vrscene_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ue5param_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folderid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , mark_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , md5_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , foldercode_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(int64_t{0})
  , attrcate_(0)
  , isself_(0)
  , cateid_(int64_t{0})
  , price_(0)
  , templateid_(int64_t{0})
  , renderingid_(0)
  , delflag_(0){}
struct CSMatModelDataDefaultTypeInternal {
  constexpr CSMatModelDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CSMatModelDataDefaultTypeInternal() {}
  union {
    CSMatModelData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CSMatModelDataDefaultTypeInternal _CSMatModelData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_CSMatModel_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_CSMatModel_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_CSMatModel_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_CSMatModel_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, attrcate_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, refpath_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, pakfilepath_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, isself_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, productimg_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, brand_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, cateid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, catename_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, materialcate_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, mapsimg_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, price_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, depth_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, width_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, height_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, placementrules_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, dictgroupvalue_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, dictvalue_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, shader_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, vrscene_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, ue5param_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, templateid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, renderingid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, folderid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, mark_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, delflag_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, md5_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, createdby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, createdtime_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, updatedby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, updatedtime_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, managemapslist_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, labellist_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, modellist_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CSMatModelData, foldercode_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::CSMatModelData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_CSMatModelData_default_instance_),
};

const char descriptor_table_protodef_CSMatModel_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\020CSMatModel.proto\022\026catalog_studio_messa"
  "ge\032\016CSMatMap.proto\032\021CSModelInfo.proto\032\025C"
  "SMatModelLable.proto\"\244\006\n\016CSMatModelData\022"
  "\n\n\002id\030\001 \001(\003\022\014\n\004name\030\002 \001(\t\022\014\n\004code\030\003 \001(\t\022"
  "\020\n\010attrCate\030\004 \001(\005\022\017\n\007refPath\030\005 \001(\t\022\023\n\013pa"
  "kFilePath\030\006 \001(\t\022\016\n\006isSelf\030\007 \001(\005\022\022\n\nprodu"
  "ctImg\030\010 \001(\t\022\r\n\005brand\030\t \001(\t\022\016\n\006cateId\030\n \001"
  "(\003\022\020\n\010cateName\030\013 \001(\t\022\024\n\014materialCate\030\014 \001"
  "(\t\022\017\n\007mapsImg\030\r \001(\t\022\r\n\005price\030\016 \001(\001\022\r\n\005de"
  "pth\030\017 \001(\t\022\r\n\005width\030\020 \001(\t\022\016\n\006height\030\021 \001(\t"
  "\022\026\n\016placementRules\030\022 \001(\t\022\026\n\016dictGroupVal"
  "ue\030\023 \001(\t\022\021\n\tdictValue\030\024 \001(\t\022\016\n\006shader\030\025 "
  "\001(\t\022\017\n\007vrscene\030\026 \001(\t\022\020\n\010ue5Param\030\027 \001(\t\022\022"
  "\n\ntemplateId\030\030 \001(\003\022\023\n\013renderingId\030\031 \001(\005\022"
  "\020\n\010folderId\030\032 \001(\t\022\014\n\004mark\030\033 \001(\t\022\017\n\007delFl"
  "ag\030\034 \001(\005\022\013\n\003md5\030\035 \001(\t\022\021\n\tcreatedBy\030\036 \001(\t"
  "\022\023\n\013createdTime\030\037 \001(\t\022\021\n\tupdatedBy\030  \001(\t"
  "\022\023\n\013updatedTime\030! \001(\t\022<\n\016manageMapsList\030"
  "\" \003(\0132$.catalog_studio_message.CSMatMapD"
  "ata\022>\n\tlabelList\030# \003(\0132+.catalog_studio_"
  "message.CSMatModelLableData\022:\n\tmodelList"
  "\030$ \003(\0132\'.catalog_studio_message.CSModelI"
  "nfoData\022\022\n\nfolderCode\030% \001(\tb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_CSMatModel_2eproto_deps[3] = {
  &::descriptor_table_CSMatMap_2eproto,
  &::descriptor_table_CSMatModelLable_2eproto,
  &::descriptor_table_CSModelInfo_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_CSMatModel_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CSMatModel_2eproto = {
  false, false, 915, descriptor_table_protodef_CSMatModel_2eproto, "CSMatModel.proto", 
  &descriptor_table_CSMatModel_2eproto_once, descriptor_table_CSMatModel_2eproto_deps, 3, 1,
  schemas, file_default_instances, TableStruct_CSMatModel_2eproto::offsets,
  file_level_metadata_CSMatModel_2eproto, file_level_enum_descriptors_CSMatModel_2eproto, file_level_service_descriptors_CSMatModel_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_CSMatModel_2eproto_getter() {
  return &descriptor_table_CSMatModel_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_CSMatModel_2eproto(&descriptor_table_CSMatModel_2eproto);
namespace catalog_studio_message {

// ===================================================================

class CSMatModelData::_Internal {
 public:
};

void CSMatModelData::clear_managemapslist() {
  managemapslist_.Clear();
}
void CSMatModelData::clear_labellist() {
  labellist_.Clear();
}
void CSMatModelData::clear_modellist() {
  modellist_.Clear();
}
CSMatModelData::CSMatModelData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  managemapslist_(arena),
  labellist_(arena),
  modellist_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.CSMatModelData)
}
CSMatModelData::CSMatModelData(const CSMatModelData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      managemapslist_(from.managemapslist_),
      labellist_(from.labellist_),
      modellist_(from.modellist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_code().empty()) {
    code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_code(), 
      GetArenaForAllocation());
  }
  refpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_refpath().empty()) {
    refpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_refpath(), 
      GetArenaForAllocation());
  }
  pakfilepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_pakfilepath().empty()) {
    pakfilepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pakfilepath(), 
      GetArenaForAllocation());
  }
  productimg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_productimg().empty()) {
    productimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_productimg(), 
      GetArenaForAllocation());
  }
  brand_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_brand().empty()) {
    brand_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_brand(), 
      GetArenaForAllocation());
  }
  catename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_catename().empty()) {
    catename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_catename(), 
      GetArenaForAllocation());
  }
  materialcate_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_materialcate().empty()) {
    materialcate_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_materialcate(), 
      GetArenaForAllocation());
  }
  mapsimg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_mapsimg().empty()) {
    mapsimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_mapsimg(), 
      GetArenaForAllocation());
  }
  depth_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_depth().empty()) {
    depth_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_depth(), 
      GetArenaForAllocation());
  }
  width_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_width().empty()) {
    width_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_width(), 
      GetArenaForAllocation());
  }
  height_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_height().empty()) {
    height_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_height(), 
      GetArenaForAllocation());
  }
  placementrules_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_placementrules().empty()) {
    placementrules_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_placementrules(), 
      GetArenaForAllocation());
  }
  dictgroupvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_dictgroupvalue().empty()) {
    dictgroupvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dictgroupvalue(), 
      GetArenaForAllocation());
  }
  dictvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_dictvalue().empty()) {
    dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dictvalue(), 
      GetArenaForAllocation());
  }
  shader_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_shader().empty()) {
    shader_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_shader(), 
      GetArenaForAllocation());
  }
  vrscene_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_vrscene().empty()) {
    vrscene_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_vrscene(), 
      GetArenaForAllocation());
  }
  ue5param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_ue5param().empty()) {
    ue5param_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ue5param(), 
      GetArenaForAllocation());
  }
  folderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folderid().empty()) {
    folderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folderid(), 
      GetArenaForAllocation());
  }
  mark_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_mark().empty()) {
    mark_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_mark(), 
      GetArenaForAllocation());
  }
  md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_md5().empty()) {
    md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_md5(), 
      GetArenaForAllocation());
  }
  createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdby().empty()) {
    createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdby(), 
      GetArenaForAllocation());
  }
  createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdtime().empty()) {
    createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdtime(), 
      GetArenaForAllocation());
  }
  updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedby().empty()) {
    updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedby(), 
      GetArenaForAllocation());
  }
  updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedtime().empty()) {
    updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedtime(), 
      GetArenaForAllocation());
  }
  foldercode_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_foldercode().empty()) {
    foldercode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_foldercode(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&delflag_) -
    reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.CSMatModelData)
}

void CSMatModelData::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
refpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
pakfilepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
productimg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
brand_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
catename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
materialcate_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
mapsimg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
depth_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
width_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
height_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
placementrules_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
dictgroupvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
dictvalue_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
shader_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
vrscene_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
ue5param_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folderid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
mark_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
foldercode_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&delflag_) -
    reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
}

CSMatModelData::~CSMatModelData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.CSMatModelData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CSMatModelData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  refpath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pakfilepath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  productimg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  brand_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  catename_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  materialcate_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  mapsimg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  depth_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  width_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  height_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  placementrules_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  dictgroupvalue_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  dictvalue_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  shader_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  vrscene_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ue5param_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folderid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  mark_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  md5_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  foldercode_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CSMatModelData::ArenaDtor(void* object) {
  CSMatModelData* _this = reinterpret_cast< CSMatModelData* >(object);
  (void)_this;
}
void CSMatModelData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CSMatModelData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CSMatModelData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.CSMatModelData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  managemapslist_.Clear();
  labellist_.Clear();
  modellist_.Clear();
  name_.ClearToEmpty();
  code_.ClearToEmpty();
  refpath_.ClearToEmpty();
  pakfilepath_.ClearToEmpty();
  productimg_.ClearToEmpty();
  brand_.ClearToEmpty();
  catename_.ClearToEmpty();
  materialcate_.ClearToEmpty();
  mapsimg_.ClearToEmpty();
  depth_.ClearToEmpty();
  width_.ClearToEmpty();
  height_.ClearToEmpty();
  placementrules_.ClearToEmpty();
  dictgroupvalue_.ClearToEmpty();
  dictvalue_.ClearToEmpty();
  shader_.ClearToEmpty();
  vrscene_.ClearToEmpty();
  ue5param_.ClearToEmpty();
  folderid_.ClearToEmpty();
  mark_.ClearToEmpty();
  md5_.ClearToEmpty();
  createdby_.ClearToEmpty();
  createdtime_.ClearToEmpty();
  updatedby_.ClearToEmpty();
  updatedtime_.ClearToEmpty();
  foldercode_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&delflag_) -
      reinterpret_cast<char*>(&id_)) + sizeof(delflag_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CSMatModelData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string code = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 attrCate = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          attrcate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string refPath = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_refpath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.refPath"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string pakFilePath = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_pakfilepath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.pakFilePath"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isSelf = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          isself_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string productImg = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_productimg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.productImg"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string brand = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_brand();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.brand"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 cateId = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          cateid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string cateName = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_catename();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.cateName"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string materialCate = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_materialcate();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.materialCate"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string mapsImg = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          auto str = _internal_mutable_mapsimg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.mapsImg"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double price = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 113)) {
          price_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string depth = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          auto str = _internal_mutable_depth();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.depth"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string width = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          auto str = _internal_mutable_width();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.width"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string height = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          auto str = _internal_mutable_height();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.height"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string placementRules = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          auto str = _internal_mutable_placementrules();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.placementRules"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string dictGroupValue = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          auto str = _internal_mutable_dictgroupvalue();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.dictGroupValue"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string dictValue = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          auto str = _internal_mutable_dictvalue();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.dictValue"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string shader = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          auto str = _internal_mutable_shader();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.shader"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string vrscene = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          auto str = _internal_mutable_vrscene();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.vrscene"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string ue5Param = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          auto str = _internal_mutable_ue5param();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.ue5Param"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 templateId = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 192)) {
          templateid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 renderingId = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 200)) {
          renderingid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folderId = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 210)) {
          auto str = _internal_mutable_folderid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.folderId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string mark = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 218)) {
          auto str = _internal_mutable_mark();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.mark"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 delFlag = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 224)) {
          delflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string md5 = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 234)) {
          auto str = _internal_mutable_md5();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.md5"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdBy = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 242)) {
          auto str = _internal_mutable_createdby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.createdBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdTime = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 250)) {
          auto str = _internal_mutable_createdtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.createdTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedBy = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 2)) {
          auto str = _internal_mutable_updatedby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.updatedBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedTime = 33;
      case 33:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_updatedtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.updatedTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.CSMatMapData manageMapsList = 34;
      case 34:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_managemapslist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<274>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.CSMatModelLableData labelList = 35;
      case 35:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_labellist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<282>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.CSModelInfoData modelList = 36;
      case 36:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_modellist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<290>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string folderCode = 37;
      case 37:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_foldercode();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.CSMatModelData.folderCode"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CSMatModelData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.CSMatModelData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string code = 3;
  if (!this->_internal_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_code().data(), static_cast<int>(this->_internal_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.code");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_code(), target);
  }

  // int32 attrCate = 4;
  if (this->_internal_attrcate() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_attrcate(), target);
  }

  // string refPath = 5;
  if (!this->_internal_refpath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_refpath().data(), static_cast<int>(this->_internal_refpath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.refPath");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_refpath(), target);
  }

  // string pakFilePath = 6;
  if (!this->_internal_pakfilepath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_pakfilepath().data(), static_cast<int>(this->_internal_pakfilepath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.pakFilePath");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_pakfilepath(), target);
  }

  // int32 isSelf = 7;
  if (this->_internal_isself() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->_internal_isself(), target);
  }

  // string productImg = 8;
  if (!this->_internal_productimg().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_productimg().data(), static_cast<int>(this->_internal_productimg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.productImg");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_productimg(), target);
  }

  // string brand = 9;
  if (!this->_internal_brand().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_brand().data(), static_cast<int>(this->_internal_brand().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.brand");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_brand(), target);
  }

  // int64 cateId = 10;
  if (this->_internal_cateid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(10, this->_internal_cateid(), target);
  }

  // string cateName = 11;
  if (!this->_internal_catename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_catename().data(), static_cast<int>(this->_internal_catename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.cateName");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_catename(), target);
  }

  // string materialCate = 12;
  if (!this->_internal_materialcate().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_materialcate().data(), static_cast<int>(this->_internal_materialcate().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.materialCate");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_materialcate(), target);
  }

  // string mapsImg = 13;
  if (!this->_internal_mapsimg().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mapsimg().data(), static_cast<int>(this->_internal_mapsimg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.mapsImg");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_mapsimg(), target);
  }

  // double price = 14;
  if (!(this->_internal_price() <= 0 && this->_internal_price() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(14, this->_internal_price(), target);
  }

  // string depth = 15;
  if (!this->_internal_depth().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_depth().data(), static_cast<int>(this->_internal_depth().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.depth");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_depth(), target);
  }

  // string width = 16;
  if (!this->_internal_width().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_width().data(), static_cast<int>(this->_internal_width().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.width");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_width(), target);
  }

  // string height = 17;
  if (!this->_internal_height().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_height().data(), static_cast<int>(this->_internal_height().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.height");
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_height(), target);
  }

  // string placementRules = 18;
  if (!this->_internal_placementrules().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_placementrules().data(), static_cast<int>(this->_internal_placementrules().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.placementRules");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_placementrules(), target);
  }

  // string dictGroupValue = 19;
  if (!this->_internal_dictgroupvalue().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dictgroupvalue().data(), static_cast<int>(this->_internal_dictgroupvalue().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.dictGroupValue");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_dictgroupvalue(), target);
  }

  // string dictValue = 20;
  if (!this->_internal_dictvalue().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dictvalue().data(), static_cast<int>(this->_internal_dictvalue().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.dictValue");
    target = stream->WriteStringMaybeAliased(
        20, this->_internal_dictvalue(), target);
  }

  // string shader = 21;
  if (!this->_internal_shader().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_shader().data(), static_cast<int>(this->_internal_shader().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.shader");
    target = stream->WriteStringMaybeAliased(
        21, this->_internal_shader(), target);
  }

  // string vrscene = 22;
  if (!this->_internal_vrscene().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_vrscene().data(), static_cast<int>(this->_internal_vrscene().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.vrscene");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_vrscene(), target);
  }

  // string ue5Param = 23;
  if (!this->_internal_ue5param().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ue5param().data(), static_cast<int>(this->_internal_ue5param().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.ue5Param");
    target = stream->WriteStringMaybeAliased(
        23, this->_internal_ue5param(), target);
  }

  // int64 templateId = 24;
  if (this->_internal_templateid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(24, this->_internal_templateid(), target);
  }

  // int32 renderingId = 25;
  if (this->_internal_renderingid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(25, this->_internal_renderingid(), target);
  }

  // string folderId = 26;
  if (!this->_internal_folderid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folderid().data(), static_cast<int>(this->_internal_folderid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.folderId");
    target = stream->WriteStringMaybeAliased(
        26, this->_internal_folderid(), target);
  }

  // string mark = 27;
  if (!this->_internal_mark().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mark().data(), static_cast<int>(this->_internal_mark().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.mark");
    target = stream->WriteStringMaybeAliased(
        27, this->_internal_mark(), target);
  }

  // int32 delFlag = 28;
  if (this->_internal_delflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(28, this->_internal_delflag(), target);
  }

  // string md5 = 29;
  if (!this->_internal_md5().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_md5().data(), static_cast<int>(this->_internal_md5().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.md5");
    target = stream->WriteStringMaybeAliased(
        29, this->_internal_md5(), target);
  }

  // string createdBy = 30;
  if (!this->_internal_createdby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdby().data(), static_cast<int>(this->_internal_createdby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.createdBy");
    target = stream->WriteStringMaybeAliased(
        30, this->_internal_createdby(), target);
  }

  // string createdTime = 31;
  if (!this->_internal_createdtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdtime().data(), static_cast<int>(this->_internal_createdtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.createdTime");
    target = stream->WriteStringMaybeAliased(
        31, this->_internal_createdtime(), target);
  }

  // string updatedBy = 32;
  if (!this->_internal_updatedby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedby().data(), static_cast<int>(this->_internal_updatedby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.updatedBy");
    target = stream->WriteStringMaybeAliased(
        32, this->_internal_updatedby(), target);
  }

  // string updatedTime = 33;
  if (!this->_internal_updatedtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedtime().data(), static_cast<int>(this->_internal_updatedtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.updatedTime");
    target = stream->WriteStringMaybeAliased(
        33, this->_internal_updatedtime(), target);
  }

  // repeated .catalog_studio_message.CSMatMapData manageMapsList = 34;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_managemapslist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(34, this->_internal_managemapslist(i), target, stream);
  }

  // repeated .catalog_studio_message.CSMatModelLableData labelList = 35;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_labellist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(35, this->_internal_labellist(i), target, stream);
  }

  // repeated .catalog_studio_message.CSModelInfoData modelList = 36;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_modellist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(36, this->_internal_modellist(i), target, stream);
  }

  // string folderCode = 37;
  if (!this->_internal_foldercode().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_foldercode().data(), static_cast<int>(this->_internal_foldercode().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.CSMatModelData.folderCode");
    target = stream->WriteStringMaybeAliased(
        37, this->_internal_foldercode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.CSMatModelData)
  return target;
}

size_t CSMatModelData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.CSMatModelData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.CSMatMapData manageMapsList = 34;
  total_size += 2UL * this->_internal_managemapslist_size();
  for (const auto& msg : this->managemapslist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.CSMatModelLableData labelList = 35;
  total_size += 2UL * this->_internal_labellist_size();
  for (const auto& msg : this->labellist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.CSModelInfoData modelList = 36;
  total_size += 2UL * this->_internal_modellist_size();
  for (const auto& msg : this->modellist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string code = 3;
  if (!this->_internal_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_code());
  }

  // string refPath = 5;
  if (!this->_internal_refpath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_refpath());
  }

  // string pakFilePath = 6;
  if (!this->_internal_pakfilepath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pakfilepath());
  }

  // string productImg = 8;
  if (!this->_internal_productimg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_productimg());
  }

  // string brand = 9;
  if (!this->_internal_brand().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_brand());
  }

  // string cateName = 11;
  if (!this->_internal_catename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_catename());
  }

  // string materialCate = 12;
  if (!this->_internal_materialcate().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_materialcate());
  }

  // string mapsImg = 13;
  if (!this->_internal_mapsimg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mapsimg());
  }

  // string depth = 15;
  if (!this->_internal_depth().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_depth());
  }

  // string width = 16;
  if (!this->_internal_width().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_width());
  }

  // string height = 17;
  if (!this->_internal_height().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_height());
  }

  // string placementRules = 18;
  if (!this->_internal_placementrules().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_placementrules());
  }

  // string dictGroupValue = 19;
  if (!this->_internal_dictgroupvalue().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dictgroupvalue());
  }

  // string dictValue = 20;
  if (!this->_internal_dictvalue().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dictvalue());
  }

  // string shader = 21;
  if (!this->_internal_shader().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_shader());
  }

  // string vrscene = 22;
  if (!this->_internal_vrscene().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_vrscene());
  }

  // string ue5Param = 23;
  if (!this->_internal_ue5param().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ue5param());
  }

  // string folderId = 26;
  if (!this->_internal_folderid().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folderid());
  }

  // string mark = 27;
  if (!this->_internal_mark().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mark());
  }

  // string md5 = 29;
  if (!this->_internal_md5().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_md5());
  }

  // string createdBy = 30;
  if (!this->_internal_createdby().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdby());
  }

  // string createdTime = 31;
  if (!this->_internal_createdtime().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdtime());
  }

  // string updatedBy = 32;
  if (!this->_internal_updatedby().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedby());
  }

  // string updatedTime = 33;
  if (!this->_internal_updatedtime().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedtime());
  }

  // string folderCode = 37;
  if (!this->_internal_foldercode().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_foldercode());
  }

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // int32 attrCate = 4;
  if (this->_internal_attrcate() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_attrcate());
  }

  // int32 isSelf = 7;
  if (this->_internal_isself() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isself());
  }

  // int64 cateId = 10;
  if (this->_internal_cateid() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_cateid());
  }

  // double price = 14;
  if (!(this->_internal_price() <= 0 && this->_internal_price() >= 0)) {
    total_size += 1 + 8;
  }

  // int64 templateId = 24;
  if (this->_internal_templateid() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_templateid());
  }

  // int32 renderingId = 25;
  if (this->_internal_renderingid() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_renderingid());
  }

  // int32 delFlag = 28;
  if (this->_internal_delflag() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_delflag());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CSMatModelData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CSMatModelData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CSMatModelData::GetClassData() const { return &_class_data_; }

void CSMatModelData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CSMatModelData *>(to)->MergeFrom(
      static_cast<const CSMatModelData &>(from));
}


void CSMatModelData::MergeFrom(const CSMatModelData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.CSMatModelData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  managemapslist_.MergeFrom(from.managemapslist_);
  labellist_.MergeFrom(from.labellist_);
  modellist_.MergeFrom(from.modellist_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_code().empty()) {
    _internal_set_code(from._internal_code());
  }
  if (!from._internal_refpath().empty()) {
    _internal_set_refpath(from._internal_refpath());
  }
  if (!from._internal_pakfilepath().empty()) {
    _internal_set_pakfilepath(from._internal_pakfilepath());
  }
  if (!from._internal_productimg().empty()) {
    _internal_set_productimg(from._internal_productimg());
  }
  if (!from._internal_brand().empty()) {
    _internal_set_brand(from._internal_brand());
  }
  if (!from._internal_catename().empty()) {
    _internal_set_catename(from._internal_catename());
  }
  if (!from._internal_materialcate().empty()) {
    _internal_set_materialcate(from._internal_materialcate());
  }
  if (!from._internal_mapsimg().empty()) {
    _internal_set_mapsimg(from._internal_mapsimg());
  }
  if (!from._internal_depth().empty()) {
    _internal_set_depth(from._internal_depth());
  }
  if (!from._internal_width().empty()) {
    _internal_set_width(from._internal_width());
  }
  if (!from._internal_height().empty()) {
    _internal_set_height(from._internal_height());
  }
  if (!from._internal_placementrules().empty()) {
    _internal_set_placementrules(from._internal_placementrules());
  }
  if (!from._internal_dictgroupvalue().empty()) {
    _internal_set_dictgroupvalue(from._internal_dictgroupvalue());
  }
  if (!from._internal_dictvalue().empty()) {
    _internal_set_dictvalue(from._internal_dictvalue());
  }
  if (!from._internal_shader().empty()) {
    _internal_set_shader(from._internal_shader());
  }
  if (!from._internal_vrscene().empty()) {
    _internal_set_vrscene(from._internal_vrscene());
  }
  if (!from._internal_ue5param().empty()) {
    _internal_set_ue5param(from._internal_ue5param());
  }
  if (!from._internal_folderid().empty()) {
    _internal_set_folderid(from._internal_folderid());
  }
  if (!from._internal_mark().empty()) {
    _internal_set_mark(from._internal_mark());
  }
  if (!from._internal_md5().empty()) {
    _internal_set_md5(from._internal_md5());
  }
  if (!from._internal_createdby().empty()) {
    _internal_set_createdby(from._internal_createdby());
  }
  if (!from._internal_createdtime().empty()) {
    _internal_set_createdtime(from._internal_createdtime());
  }
  if (!from._internal_updatedby().empty()) {
    _internal_set_updatedby(from._internal_updatedby());
  }
  if (!from._internal_updatedtime().empty()) {
    _internal_set_updatedtime(from._internal_updatedtime());
  }
  if (!from._internal_foldercode().empty()) {
    _internal_set_foldercode(from._internal_foldercode());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_attrcate() != 0) {
    _internal_set_attrcate(from._internal_attrcate());
  }
  if (from._internal_isself() != 0) {
    _internal_set_isself(from._internal_isself());
  }
  if (from._internal_cateid() != 0) {
    _internal_set_cateid(from._internal_cateid());
  }
  if (!(from._internal_price() <= 0 && from._internal_price() >= 0)) {
    _internal_set_price(from._internal_price());
  }
  if (from._internal_templateid() != 0) {
    _internal_set_templateid(from._internal_templateid());
  }
  if (from._internal_renderingid() != 0) {
    _internal_set_renderingid(from._internal_renderingid());
  }
  if (from._internal_delflag() != 0) {
    _internal_set_delflag(from._internal_delflag());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CSMatModelData::CopyFrom(const CSMatModelData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.CSMatModelData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CSMatModelData::IsInitialized() const {
  return true;
}

void CSMatModelData::InternalSwap(CSMatModelData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  managemapslist_.InternalSwap(&other->managemapslist_);
  labellist_.InternalSwap(&other->labellist_);
  modellist_.InternalSwap(&other->modellist_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &code_, lhs_arena,
      &other->code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &refpath_, lhs_arena,
      &other->refpath_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &pakfilepath_, lhs_arena,
      &other->pakfilepath_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &productimg_, lhs_arena,
      &other->productimg_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &brand_, lhs_arena,
      &other->brand_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &catename_, lhs_arena,
      &other->catename_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &materialcate_, lhs_arena,
      &other->materialcate_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &mapsimg_, lhs_arena,
      &other->mapsimg_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &depth_, lhs_arena,
      &other->depth_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &width_, lhs_arena,
      &other->width_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &height_, lhs_arena,
      &other->height_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &placementrules_, lhs_arena,
      &other->placementrules_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &dictgroupvalue_, lhs_arena,
      &other->dictgroupvalue_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &dictvalue_, lhs_arena,
      &other->dictvalue_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &shader_, lhs_arena,
      &other->shader_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &vrscene_, lhs_arena,
      &other->vrscene_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &ue5param_, lhs_arena,
      &other->ue5param_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folderid_, lhs_arena,
      &other->folderid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &mark_, lhs_arena,
      &other->mark_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &md5_, lhs_arena,
      &other->md5_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdby_, lhs_arena,
      &other->createdby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdtime_, lhs_arena,
      &other->createdtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedby_, lhs_arena,
      &other->updatedby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedtime_, lhs_arena,
      &other->updatedtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &foldercode_, lhs_arena,
      &other->foldercode_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CSMatModelData, delflag_)
      + sizeof(CSMatModelData::delflag_)
      - PROTOBUF_FIELD_OFFSET(CSMatModelData, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CSMatModelData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_CSMatModel_2eproto_getter, &descriptor_table_CSMatModel_2eproto_once,
      file_level_metadata_CSMatModel_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::CSMatModelData* Arena::CreateMaybeMessage< ::catalog_studio_message::CSMatModelData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::CSMatModelData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
