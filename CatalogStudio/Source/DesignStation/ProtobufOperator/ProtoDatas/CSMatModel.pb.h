// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CSMatModel.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_CSMatModel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_CSMatModel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "CSMatMap.pb.h"
#include "CSModelInfo.pb.h"
#include "CSMatModelLable.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_CSMatModel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_CSMatModel_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CSMatModel_2eproto;
namespace catalog_studio_message {
class CSMatModelData;
struct CSMatModelDataDefaultTypeInternal;
extern CSMatModelDataDefaultTypeInternal _CSMatModelData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::CSMatModelData* Arena::CreateMaybeMessage<::catalog_studio_message::CSMatModelData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class CSMatModelData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.CSMatModelData) */ {
 public:
  inline CSMatModelData() : CSMatModelData(nullptr) {}
  ~CSMatModelData() override;
  explicit constexpr CSMatModelData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CSMatModelData(const CSMatModelData& from);
  CSMatModelData(CSMatModelData&& from) noexcept
    : CSMatModelData() {
    *this = ::std::move(from);
  }

  inline CSMatModelData& operator=(const CSMatModelData& from) {
    CopyFrom(from);
    return *this;
  }
  inline CSMatModelData& operator=(CSMatModelData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CSMatModelData& default_instance() {
    return *internal_default_instance();
  }
  static inline const CSMatModelData* internal_default_instance() {
    return reinterpret_cast<const CSMatModelData*>(
               &_CSMatModelData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CSMatModelData& a, CSMatModelData& b) {
    a.Swap(&b);
  }
  inline void Swap(CSMatModelData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CSMatModelData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CSMatModelData* New() const final {
    return new CSMatModelData();
  }

  CSMatModelData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CSMatModelData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CSMatModelData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CSMatModelData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CSMatModelData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.CSMatModelData";
  }
  protected:
  explicit CSMatModelData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kManageMapsListFieldNumber = 34,
    kLabelListFieldNumber = 35,
    kModelListFieldNumber = 36,
    kNameFieldNumber = 2,
    kCodeFieldNumber = 3,
    kRefPathFieldNumber = 5,
    kPakFilePathFieldNumber = 6,
    kProductImgFieldNumber = 8,
    kBrandFieldNumber = 9,
    kCateNameFieldNumber = 11,
    kMaterialCateFieldNumber = 12,
    kMapsImgFieldNumber = 13,
    kDepthFieldNumber = 15,
    kWidthFieldNumber = 16,
    kHeightFieldNumber = 17,
    kPlacementRulesFieldNumber = 18,
    kDictGroupValueFieldNumber = 19,
    kDictValueFieldNumber = 20,
    kShaderFieldNumber = 21,
    kVrsceneFieldNumber = 22,
    kUe5ParamFieldNumber = 23,
    kFolderIdFieldNumber = 26,
    kMarkFieldNumber = 27,
    kMd5FieldNumber = 29,
    kCreatedByFieldNumber = 30,
    kCreatedTimeFieldNumber = 31,
    kUpdatedByFieldNumber = 32,
    kUpdatedTimeFieldNumber = 33,
    kFolderCodeFieldNumber = 37,
    kIdFieldNumber = 1,
    kAttrCateFieldNumber = 4,
    kIsSelfFieldNumber = 7,
    kCateIdFieldNumber = 10,
    kPriceFieldNumber = 14,
    kTemplateIdFieldNumber = 24,
    kRenderingIdFieldNumber = 25,
    kDelFlagFieldNumber = 28,
  };
  // repeated .catalog_studio_message.CSMatMapData manageMapsList = 34;
  int managemapslist_size() const;
  private:
  int _internal_managemapslist_size() const;
  public:
  void clear_managemapslist();
  ::catalog_studio_message::CSMatMapData* mutable_managemapslist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatMapData >*
      mutable_managemapslist();
  private:
  const ::catalog_studio_message::CSMatMapData& _internal_managemapslist(int index) const;
  ::catalog_studio_message::CSMatMapData* _internal_add_managemapslist();
  public:
  const ::catalog_studio_message::CSMatMapData& managemapslist(int index) const;
  ::catalog_studio_message::CSMatMapData* add_managemapslist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatMapData >&
      managemapslist() const;

  // repeated .catalog_studio_message.CSMatModelLableData labelList = 35;
  int labellist_size() const;
  private:
  int _internal_labellist_size() const;
  public:
  void clear_labellist();
  ::catalog_studio_message::CSMatModelLableData* mutable_labellist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelLableData >*
      mutable_labellist();
  private:
  const ::catalog_studio_message::CSMatModelLableData& _internal_labellist(int index) const;
  ::catalog_studio_message::CSMatModelLableData* _internal_add_labellist();
  public:
  const ::catalog_studio_message::CSMatModelLableData& labellist(int index) const;
  ::catalog_studio_message::CSMatModelLableData* add_labellist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelLableData >&
      labellist() const;

  // repeated .catalog_studio_message.CSModelInfoData modelList = 36;
  int modellist_size() const;
  private:
  int _internal_modellist_size() const;
  public:
  void clear_modellist();
  ::catalog_studio_message::CSModelInfoData* mutable_modellist(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSModelInfoData >*
      mutable_modellist();
  private:
  const ::catalog_studio_message::CSModelInfoData& _internal_modellist(int index) const;
  ::catalog_studio_message::CSModelInfoData* _internal_add_modellist();
  public:
  const ::catalog_studio_message::CSModelInfoData& modellist(int index) const;
  ::catalog_studio_message::CSModelInfoData* add_modellist();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSModelInfoData >&
      modellist() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string code = 3;
  void clear_code();
  const std::string& code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_code();
  void set_allocated_code(std::string* code);
  private:
  const std::string& _internal_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code(const std::string& value);
  std::string* _internal_mutable_code();
  public:

  // string refPath = 5;
  void clear_refpath();
  const std::string& refpath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_refpath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_refpath();
  PROTOBUF_MUST_USE_RESULT std::string* release_refpath();
  void set_allocated_refpath(std::string* refpath);
  private:
  const std::string& _internal_refpath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_refpath(const std::string& value);
  std::string* _internal_mutable_refpath();
  public:

  // string pakFilePath = 6;
  void clear_pakfilepath();
  const std::string& pakfilepath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pakfilepath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pakfilepath();
  PROTOBUF_MUST_USE_RESULT std::string* release_pakfilepath();
  void set_allocated_pakfilepath(std::string* pakfilepath);
  private:
  const std::string& _internal_pakfilepath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pakfilepath(const std::string& value);
  std::string* _internal_mutable_pakfilepath();
  public:

  // string productImg = 8;
  void clear_productimg();
  const std::string& productimg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_productimg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_productimg();
  PROTOBUF_MUST_USE_RESULT std::string* release_productimg();
  void set_allocated_productimg(std::string* productimg);
  private:
  const std::string& _internal_productimg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_productimg(const std::string& value);
  std::string* _internal_mutable_productimg();
  public:

  // string brand = 9;
  void clear_brand();
  const std::string& brand() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_brand(ArgT0&& arg0, ArgT... args);
  std::string* mutable_brand();
  PROTOBUF_MUST_USE_RESULT std::string* release_brand();
  void set_allocated_brand(std::string* brand);
  private:
  const std::string& _internal_brand() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_brand(const std::string& value);
  std::string* _internal_mutable_brand();
  public:

  // string cateName = 11;
  void clear_catename();
  const std::string& catename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_catename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_catename();
  PROTOBUF_MUST_USE_RESULT std::string* release_catename();
  void set_allocated_catename(std::string* catename);
  private:
  const std::string& _internal_catename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_catename(const std::string& value);
  std::string* _internal_mutable_catename();
  public:

  // string materialCate = 12;
  void clear_materialcate();
  const std::string& materialcate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_materialcate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_materialcate();
  PROTOBUF_MUST_USE_RESULT std::string* release_materialcate();
  void set_allocated_materialcate(std::string* materialcate);
  private:
  const std::string& _internal_materialcate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_materialcate(const std::string& value);
  std::string* _internal_mutable_materialcate();
  public:

  // string mapsImg = 13;
  void clear_mapsimg();
  const std::string& mapsimg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mapsimg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mapsimg();
  PROTOBUF_MUST_USE_RESULT std::string* release_mapsimg();
  void set_allocated_mapsimg(std::string* mapsimg);
  private:
  const std::string& _internal_mapsimg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mapsimg(const std::string& value);
  std::string* _internal_mutable_mapsimg();
  public:

  // string depth = 15;
  void clear_depth();
  const std::string& depth() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_depth(ArgT0&& arg0, ArgT... args);
  std::string* mutable_depth();
  PROTOBUF_MUST_USE_RESULT std::string* release_depth();
  void set_allocated_depth(std::string* depth);
  private:
  const std::string& _internal_depth() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_depth(const std::string& value);
  std::string* _internal_mutable_depth();
  public:

  // string width = 16;
  void clear_width();
  const std::string& width() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_width(ArgT0&& arg0, ArgT... args);
  std::string* mutable_width();
  PROTOBUF_MUST_USE_RESULT std::string* release_width();
  void set_allocated_width(std::string* width);
  private:
  const std::string& _internal_width() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_width(const std::string& value);
  std::string* _internal_mutable_width();
  public:

  // string height = 17;
  void clear_height();
  const std::string& height() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_height(ArgT0&& arg0, ArgT... args);
  std::string* mutable_height();
  PROTOBUF_MUST_USE_RESULT std::string* release_height();
  void set_allocated_height(std::string* height);
  private:
  const std::string& _internal_height() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_height(const std::string& value);
  std::string* _internal_mutable_height();
  public:

  // string placementRules = 18;
  void clear_placementrules();
  const std::string& placementrules() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_placementrules(ArgT0&& arg0, ArgT... args);
  std::string* mutable_placementrules();
  PROTOBUF_MUST_USE_RESULT std::string* release_placementrules();
  void set_allocated_placementrules(std::string* placementrules);
  private:
  const std::string& _internal_placementrules() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_placementrules(const std::string& value);
  std::string* _internal_mutable_placementrules();
  public:

  // string dictGroupValue = 19;
  void clear_dictgroupvalue();
  const std::string& dictgroupvalue() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dictgroupvalue(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dictgroupvalue();
  PROTOBUF_MUST_USE_RESULT std::string* release_dictgroupvalue();
  void set_allocated_dictgroupvalue(std::string* dictgroupvalue);
  private:
  const std::string& _internal_dictgroupvalue() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dictgroupvalue(const std::string& value);
  std::string* _internal_mutable_dictgroupvalue();
  public:

  // string dictValue = 20;
  void clear_dictvalue();
  const std::string& dictvalue() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dictvalue(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dictvalue();
  PROTOBUF_MUST_USE_RESULT std::string* release_dictvalue();
  void set_allocated_dictvalue(std::string* dictvalue);
  private:
  const std::string& _internal_dictvalue() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dictvalue(const std::string& value);
  std::string* _internal_mutable_dictvalue();
  public:

  // string shader = 21;
  void clear_shader();
  const std::string& shader() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_shader(ArgT0&& arg0, ArgT... args);
  std::string* mutable_shader();
  PROTOBUF_MUST_USE_RESULT std::string* release_shader();
  void set_allocated_shader(std::string* shader);
  private:
  const std::string& _internal_shader() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_shader(const std::string& value);
  std::string* _internal_mutable_shader();
  public:

  // string vrscene = 22;
  void clear_vrscene();
  const std::string& vrscene() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vrscene(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vrscene();
  PROTOBUF_MUST_USE_RESULT std::string* release_vrscene();
  void set_allocated_vrscene(std::string* vrscene);
  private:
  const std::string& _internal_vrscene() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vrscene(const std::string& value);
  std::string* _internal_mutable_vrscene();
  public:

  // string ue5Param = 23;
  void clear_ue5param();
  const std::string& ue5param() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ue5param(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ue5param();
  PROTOBUF_MUST_USE_RESULT std::string* release_ue5param();
  void set_allocated_ue5param(std::string* ue5param);
  private:
  const std::string& _internal_ue5param() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ue5param(const std::string& value);
  std::string* _internal_mutable_ue5param();
  public:

  // string folderId = 26;
  void clear_folderid();
  const std::string& folderid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folderid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folderid();
  PROTOBUF_MUST_USE_RESULT std::string* release_folderid();
  void set_allocated_folderid(std::string* folderid);
  private:
  const std::string& _internal_folderid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folderid(const std::string& value);
  std::string* _internal_mutable_folderid();
  public:

  // string mark = 27;
  void clear_mark();
  const std::string& mark() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mark(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mark();
  PROTOBUF_MUST_USE_RESULT std::string* release_mark();
  void set_allocated_mark(std::string* mark);
  private:
  const std::string& _internal_mark() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mark(const std::string& value);
  std::string* _internal_mutable_mark();
  public:

  // string md5 = 29;
  void clear_md5();
  const std::string& md5() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_md5(ArgT0&& arg0, ArgT... args);
  std::string* mutable_md5();
  PROTOBUF_MUST_USE_RESULT std::string* release_md5();
  void set_allocated_md5(std::string* md5);
  private:
  const std::string& _internal_md5() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_md5(const std::string& value);
  std::string* _internal_mutable_md5();
  public:

  // string createdBy = 30;
  void clear_createdby();
  const std::string& createdby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdby();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdby();
  void set_allocated_createdby(std::string* createdby);
  private:
  const std::string& _internal_createdby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdby(const std::string& value);
  std::string* _internal_mutable_createdby();
  public:

  // string createdTime = 31;
  void clear_createdtime();
  const std::string& createdtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdtime();
  void set_allocated_createdtime(std::string* createdtime);
  private:
  const std::string& _internal_createdtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdtime(const std::string& value);
  std::string* _internal_mutable_createdtime();
  public:

  // string updatedBy = 32;
  void clear_updatedby();
  const std::string& updatedby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedby();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedby();
  void set_allocated_updatedby(std::string* updatedby);
  private:
  const std::string& _internal_updatedby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedby(const std::string& value);
  std::string* _internal_mutable_updatedby();
  public:

  // string updatedTime = 33;
  void clear_updatedtime();
  const std::string& updatedtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedtime();
  void set_allocated_updatedtime(std::string* updatedtime);
  private:
  const std::string& _internal_updatedtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedtime(const std::string& value);
  std::string* _internal_mutable_updatedtime();
  public:

  // string folderCode = 37;
  void clear_foldercode();
  const std::string& foldercode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_foldercode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_foldercode();
  PROTOBUF_MUST_USE_RESULT std::string* release_foldercode();
  void set_allocated_foldercode(std::string* foldercode);
  private:
  const std::string& _internal_foldercode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_foldercode(const std::string& value);
  std::string* _internal_mutable_foldercode();
  public:

  // int64 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int64 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int32 attrCate = 4;
  void clear_attrcate();
  ::PROTOBUF_NAMESPACE_ID::int32 attrcate() const;
  void set_attrcate(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_attrcate() const;
  void _internal_set_attrcate(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isSelf = 7;
  void clear_isself();
  ::PROTOBUF_NAMESPACE_ID::int32 isself() const;
  void set_isself(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isself() const;
  void _internal_set_isself(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int64 cateId = 10;
  void clear_cateid();
  ::PROTOBUF_NAMESPACE_ID::int64 cateid() const;
  void set_cateid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_cateid() const;
  void _internal_set_cateid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // double price = 14;
  void clear_price();
  double price() const;
  void set_price(double value);
  private:
  double _internal_price() const;
  void _internal_set_price(double value);
  public:

  // int64 templateId = 24;
  void clear_templateid();
  ::PROTOBUF_NAMESPACE_ID::int64 templateid() const;
  void set_templateid(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_templateid() const;
  void _internal_set_templateid(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // int32 renderingId = 25;
  void clear_renderingid();
  ::PROTOBUF_NAMESPACE_ID::int32 renderingid() const;
  void set_renderingid(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_renderingid() const;
  void _internal_set_renderingid(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 delFlag = 28;
  void clear_delflag();
  ::PROTOBUF_NAMESPACE_ID::int32 delflag() const;
  void set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_delflag() const;
  void _internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.CSMatModelData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatMapData > managemapslist_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelLableData > labellist_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSModelInfoData > modellist_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr refpath_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pakfilepath_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr productimg_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr brand_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr catename_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr materialcate_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mapsimg_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr depth_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr width_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr height_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr placementrules_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dictgroupvalue_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dictvalue_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr shader_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vrscene_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ue5param_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folderid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mark_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr md5_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdtime_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedtime_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr foldercode_;
  ::PROTOBUF_NAMESPACE_ID::int64 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 attrcate_;
  ::PROTOBUF_NAMESPACE_ID::int32 isself_;
  ::PROTOBUF_NAMESPACE_ID::int64 cateid_;
  double price_;
  ::PROTOBUF_NAMESPACE_ID::int64 templateid_;
  ::PROTOBUF_NAMESPACE_ID::int32 renderingid_;
  ::PROTOBUF_NAMESPACE_ID::int32 delflag_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_CSMatModel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CSMatModelData

// int64 id = 1;
inline void CSMatModelData::clear_id() {
  id_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.id)
  return _internal_id();
}
inline void CSMatModelData::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  id_ = value;
}
inline void CSMatModelData::set_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.id)
}

// string name = 2;
inline void CSMatModelData::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& CSMatModelData::name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.name)
}
inline std::string* CSMatModelData::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.name)
  return _s;
}
inline const std::string& CSMatModelData::_internal_name() const {
  return name_.Get();
}
inline void CSMatModelData::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.name)
}

// string code = 3;
inline void CSMatModelData::clear_code() {
  code_.ClearToEmpty();
}
inline const std::string& CSMatModelData::code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.code)
  return _internal_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_code(ArgT0&& arg0, ArgT... args) {
 
 code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.code)
}
inline std::string* CSMatModelData::mutable_code() {
  std::string* _s = _internal_mutable_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.code)
  return _s;
}
inline const std::string& CSMatModelData::_internal_code() const {
  return code_.Get();
}
inline void CSMatModelData::_internal_set_code(const std::string& value) {
  
  code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_code() {
  
  return code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.code)
  return code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_code(std::string* code) {
  if (code != nullptr) {
    
  } else {
    
  }
  code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.code)
}

// int32 attrCate = 4;
inline void CSMatModelData::clear_attrcate() {
  attrcate_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::_internal_attrcate() const {
  return attrcate_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::attrcate() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.attrCate)
  return _internal_attrcate();
}
inline void CSMatModelData::_internal_set_attrcate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  attrcate_ = value;
}
inline void CSMatModelData::set_attrcate(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_attrcate(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.attrCate)
}

// string refPath = 5;
inline void CSMatModelData::clear_refpath() {
  refpath_.ClearToEmpty();
}
inline const std::string& CSMatModelData::refpath() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.refPath)
  return _internal_refpath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_refpath(ArgT0&& arg0, ArgT... args) {
 
 refpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.refPath)
}
inline std::string* CSMatModelData::mutable_refpath() {
  std::string* _s = _internal_mutable_refpath();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.refPath)
  return _s;
}
inline const std::string& CSMatModelData::_internal_refpath() const {
  return refpath_.Get();
}
inline void CSMatModelData::_internal_set_refpath(const std::string& value) {
  
  refpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_refpath() {
  
  return refpath_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_refpath() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.refPath)
  return refpath_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_refpath(std::string* refpath) {
  if (refpath != nullptr) {
    
  } else {
    
  }
  refpath_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), refpath,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.refPath)
}

// string pakFilePath = 6;
inline void CSMatModelData::clear_pakfilepath() {
  pakfilepath_.ClearToEmpty();
}
inline const std::string& CSMatModelData::pakfilepath() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.pakFilePath)
  return _internal_pakfilepath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_pakfilepath(ArgT0&& arg0, ArgT... args) {
 
 pakfilepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.pakFilePath)
}
inline std::string* CSMatModelData::mutable_pakfilepath() {
  std::string* _s = _internal_mutable_pakfilepath();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.pakFilePath)
  return _s;
}
inline const std::string& CSMatModelData::_internal_pakfilepath() const {
  return pakfilepath_.Get();
}
inline void CSMatModelData::_internal_set_pakfilepath(const std::string& value) {
  
  pakfilepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_pakfilepath() {
  
  return pakfilepath_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_pakfilepath() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.pakFilePath)
  return pakfilepath_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_pakfilepath(std::string* pakfilepath) {
  if (pakfilepath != nullptr) {
    
  } else {
    
  }
  pakfilepath_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pakfilepath,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.pakFilePath)
}

// int32 isSelf = 7;
inline void CSMatModelData::clear_isself() {
  isself_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::_internal_isself() const {
  return isself_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::isself() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.isSelf)
  return _internal_isself();
}
inline void CSMatModelData::_internal_set_isself(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isself_ = value;
}
inline void CSMatModelData::set_isself(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isself(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.isSelf)
}

// string productImg = 8;
inline void CSMatModelData::clear_productimg() {
  productimg_.ClearToEmpty();
}
inline const std::string& CSMatModelData::productimg() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.productImg)
  return _internal_productimg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_productimg(ArgT0&& arg0, ArgT... args) {
 
 productimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.productImg)
}
inline std::string* CSMatModelData::mutable_productimg() {
  std::string* _s = _internal_mutable_productimg();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.productImg)
  return _s;
}
inline const std::string& CSMatModelData::_internal_productimg() const {
  return productimg_.Get();
}
inline void CSMatModelData::_internal_set_productimg(const std::string& value) {
  
  productimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_productimg() {
  
  return productimg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_productimg() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.productImg)
  return productimg_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_productimg(std::string* productimg) {
  if (productimg != nullptr) {
    
  } else {
    
  }
  productimg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), productimg,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.productImg)
}

// string brand = 9;
inline void CSMatModelData::clear_brand() {
  brand_.ClearToEmpty();
}
inline const std::string& CSMatModelData::brand() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.brand)
  return _internal_brand();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_brand(ArgT0&& arg0, ArgT... args) {
 
 brand_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.brand)
}
inline std::string* CSMatModelData::mutable_brand() {
  std::string* _s = _internal_mutable_brand();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.brand)
  return _s;
}
inline const std::string& CSMatModelData::_internal_brand() const {
  return brand_.Get();
}
inline void CSMatModelData::_internal_set_brand(const std::string& value) {
  
  brand_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_brand() {
  
  return brand_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_brand() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.brand)
  return brand_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_brand(std::string* brand) {
  if (brand != nullptr) {
    
  } else {
    
  }
  brand_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), brand,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.brand)
}

// int64 cateId = 10;
inline void CSMatModelData::clear_cateid() {
  cateid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::_internal_cateid() const {
  return cateid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::cateid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.cateId)
  return _internal_cateid();
}
inline void CSMatModelData::_internal_set_cateid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  cateid_ = value;
}
inline void CSMatModelData::set_cateid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_cateid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.cateId)
}

// string cateName = 11;
inline void CSMatModelData::clear_catename() {
  catename_.ClearToEmpty();
}
inline const std::string& CSMatModelData::catename() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.cateName)
  return _internal_catename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_catename(ArgT0&& arg0, ArgT... args) {
 
 catename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.cateName)
}
inline std::string* CSMatModelData::mutable_catename() {
  std::string* _s = _internal_mutable_catename();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.cateName)
  return _s;
}
inline const std::string& CSMatModelData::_internal_catename() const {
  return catename_.Get();
}
inline void CSMatModelData::_internal_set_catename(const std::string& value) {
  
  catename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_catename() {
  
  return catename_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_catename() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.cateName)
  return catename_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_catename(std::string* catename) {
  if (catename != nullptr) {
    
  } else {
    
  }
  catename_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), catename,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.cateName)
}

// string materialCate = 12;
inline void CSMatModelData::clear_materialcate() {
  materialcate_.ClearToEmpty();
}
inline const std::string& CSMatModelData::materialcate() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.materialCate)
  return _internal_materialcate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_materialcate(ArgT0&& arg0, ArgT... args) {
 
 materialcate_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.materialCate)
}
inline std::string* CSMatModelData::mutable_materialcate() {
  std::string* _s = _internal_mutable_materialcate();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.materialCate)
  return _s;
}
inline const std::string& CSMatModelData::_internal_materialcate() const {
  return materialcate_.Get();
}
inline void CSMatModelData::_internal_set_materialcate(const std::string& value) {
  
  materialcate_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_materialcate() {
  
  return materialcate_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_materialcate() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.materialCate)
  return materialcate_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_materialcate(std::string* materialcate) {
  if (materialcate != nullptr) {
    
  } else {
    
  }
  materialcate_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), materialcate,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.materialCate)
}

// string mapsImg = 13;
inline void CSMatModelData::clear_mapsimg() {
  mapsimg_.ClearToEmpty();
}
inline const std::string& CSMatModelData::mapsimg() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.mapsImg)
  return _internal_mapsimg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_mapsimg(ArgT0&& arg0, ArgT... args) {
 
 mapsimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.mapsImg)
}
inline std::string* CSMatModelData::mutable_mapsimg() {
  std::string* _s = _internal_mutable_mapsimg();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.mapsImg)
  return _s;
}
inline const std::string& CSMatModelData::_internal_mapsimg() const {
  return mapsimg_.Get();
}
inline void CSMatModelData::_internal_set_mapsimg(const std::string& value) {
  
  mapsimg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_mapsimg() {
  
  return mapsimg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_mapsimg() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.mapsImg)
  return mapsimg_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_mapsimg(std::string* mapsimg) {
  if (mapsimg != nullptr) {
    
  } else {
    
  }
  mapsimg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mapsimg,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.mapsImg)
}

// double price = 14;
inline void CSMatModelData::clear_price() {
  price_ = 0;
}
inline double CSMatModelData::_internal_price() const {
  return price_;
}
inline double CSMatModelData::price() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.price)
  return _internal_price();
}
inline void CSMatModelData::_internal_set_price(double value) {
  
  price_ = value;
}
inline void CSMatModelData::set_price(double value) {
  _internal_set_price(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.price)
}

// string depth = 15;
inline void CSMatModelData::clear_depth() {
  depth_.ClearToEmpty();
}
inline const std::string& CSMatModelData::depth() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.depth)
  return _internal_depth();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_depth(ArgT0&& arg0, ArgT... args) {
 
 depth_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.depth)
}
inline std::string* CSMatModelData::mutable_depth() {
  std::string* _s = _internal_mutable_depth();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.depth)
  return _s;
}
inline const std::string& CSMatModelData::_internal_depth() const {
  return depth_.Get();
}
inline void CSMatModelData::_internal_set_depth(const std::string& value) {
  
  depth_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_depth() {
  
  return depth_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_depth() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.depth)
  return depth_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_depth(std::string* depth) {
  if (depth != nullptr) {
    
  } else {
    
  }
  depth_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), depth,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.depth)
}

// string width = 16;
inline void CSMatModelData::clear_width() {
  width_.ClearToEmpty();
}
inline const std::string& CSMatModelData::width() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.width)
  return _internal_width();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_width(ArgT0&& arg0, ArgT... args) {
 
 width_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.width)
}
inline std::string* CSMatModelData::mutable_width() {
  std::string* _s = _internal_mutable_width();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.width)
  return _s;
}
inline const std::string& CSMatModelData::_internal_width() const {
  return width_.Get();
}
inline void CSMatModelData::_internal_set_width(const std::string& value) {
  
  width_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_width() {
  
  return width_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_width() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.width)
  return width_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_width(std::string* width) {
  if (width != nullptr) {
    
  } else {
    
  }
  width_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), width,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.width)
}

// string height = 17;
inline void CSMatModelData::clear_height() {
  height_.ClearToEmpty();
}
inline const std::string& CSMatModelData::height() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.height)
  return _internal_height();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_height(ArgT0&& arg0, ArgT... args) {
 
 height_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.height)
}
inline std::string* CSMatModelData::mutable_height() {
  std::string* _s = _internal_mutable_height();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.height)
  return _s;
}
inline const std::string& CSMatModelData::_internal_height() const {
  return height_.Get();
}
inline void CSMatModelData::_internal_set_height(const std::string& value) {
  
  height_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_height() {
  
  return height_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_height() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.height)
  return height_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_height(std::string* height) {
  if (height != nullptr) {
    
  } else {
    
  }
  height_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), height,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.height)
}

// string placementRules = 18;
inline void CSMatModelData::clear_placementrules() {
  placementrules_.ClearToEmpty();
}
inline const std::string& CSMatModelData::placementrules() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.placementRules)
  return _internal_placementrules();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_placementrules(ArgT0&& arg0, ArgT... args) {
 
 placementrules_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.placementRules)
}
inline std::string* CSMatModelData::mutable_placementrules() {
  std::string* _s = _internal_mutable_placementrules();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.placementRules)
  return _s;
}
inline const std::string& CSMatModelData::_internal_placementrules() const {
  return placementrules_.Get();
}
inline void CSMatModelData::_internal_set_placementrules(const std::string& value) {
  
  placementrules_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_placementrules() {
  
  return placementrules_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_placementrules() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.placementRules)
  return placementrules_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_placementrules(std::string* placementrules) {
  if (placementrules != nullptr) {
    
  } else {
    
  }
  placementrules_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), placementrules,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.placementRules)
}

// string dictGroupValue = 19;
inline void CSMatModelData::clear_dictgroupvalue() {
  dictgroupvalue_.ClearToEmpty();
}
inline const std::string& CSMatModelData::dictgroupvalue() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.dictGroupValue)
  return _internal_dictgroupvalue();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_dictgroupvalue(ArgT0&& arg0, ArgT... args) {
 
 dictgroupvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.dictGroupValue)
}
inline std::string* CSMatModelData::mutable_dictgroupvalue() {
  std::string* _s = _internal_mutable_dictgroupvalue();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.dictGroupValue)
  return _s;
}
inline const std::string& CSMatModelData::_internal_dictgroupvalue() const {
  return dictgroupvalue_.Get();
}
inline void CSMatModelData::_internal_set_dictgroupvalue(const std::string& value) {
  
  dictgroupvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_dictgroupvalue() {
  
  return dictgroupvalue_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_dictgroupvalue() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.dictGroupValue)
  return dictgroupvalue_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_dictgroupvalue(std::string* dictgroupvalue) {
  if (dictgroupvalue != nullptr) {
    
  } else {
    
  }
  dictgroupvalue_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dictgroupvalue,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.dictGroupValue)
}

// string dictValue = 20;
inline void CSMatModelData::clear_dictvalue() {
  dictvalue_.ClearToEmpty();
}
inline const std::string& CSMatModelData::dictvalue() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.dictValue)
  return _internal_dictvalue();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_dictvalue(ArgT0&& arg0, ArgT... args) {
 
 dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.dictValue)
}
inline std::string* CSMatModelData::mutable_dictvalue() {
  std::string* _s = _internal_mutable_dictvalue();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.dictValue)
  return _s;
}
inline const std::string& CSMatModelData::_internal_dictvalue() const {
  return dictvalue_.Get();
}
inline void CSMatModelData::_internal_set_dictvalue(const std::string& value) {
  
  dictvalue_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_dictvalue() {
  
  return dictvalue_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_dictvalue() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.dictValue)
  return dictvalue_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_dictvalue(std::string* dictvalue) {
  if (dictvalue != nullptr) {
    
  } else {
    
  }
  dictvalue_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dictvalue,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.dictValue)
}

// string shader = 21;
inline void CSMatModelData::clear_shader() {
  shader_.ClearToEmpty();
}
inline const std::string& CSMatModelData::shader() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.shader)
  return _internal_shader();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_shader(ArgT0&& arg0, ArgT... args) {
 
 shader_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.shader)
}
inline std::string* CSMatModelData::mutable_shader() {
  std::string* _s = _internal_mutable_shader();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.shader)
  return _s;
}
inline const std::string& CSMatModelData::_internal_shader() const {
  return shader_.Get();
}
inline void CSMatModelData::_internal_set_shader(const std::string& value) {
  
  shader_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_shader() {
  
  return shader_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_shader() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.shader)
  return shader_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_shader(std::string* shader) {
  if (shader != nullptr) {
    
  } else {
    
  }
  shader_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), shader,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.shader)
}

// string vrscene = 22;
inline void CSMatModelData::clear_vrscene() {
  vrscene_.ClearToEmpty();
}
inline const std::string& CSMatModelData::vrscene() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.vrscene)
  return _internal_vrscene();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_vrscene(ArgT0&& arg0, ArgT... args) {
 
 vrscene_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.vrscene)
}
inline std::string* CSMatModelData::mutable_vrscene() {
  std::string* _s = _internal_mutable_vrscene();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.vrscene)
  return _s;
}
inline const std::string& CSMatModelData::_internal_vrscene() const {
  return vrscene_.Get();
}
inline void CSMatModelData::_internal_set_vrscene(const std::string& value) {
  
  vrscene_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_vrscene() {
  
  return vrscene_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_vrscene() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.vrscene)
  return vrscene_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_vrscene(std::string* vrscene) {
  if (vrscene != nullptr) {
    
  } else {
    
  }
  vrscene_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), vrscene,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.vrscene)
}

// string ue5Param = 23;
inline void CSMatModelData::clear_ue5param() {
  ue5param_.ClearToEmpty();
}
inline const std::string& CSMatModelData::ue5param() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.ue5Param)
  return _internal_ue5param();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_ue5param(ArgT0&& arg0, ArgT... args) {
 
 ue5param_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.ue5Param)
}
inline std::string* CSMatModelData::mutable_ue5param() {
  std::string* _s = _internal_mutable_ue5param();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.ue5Param)
  return _s;
}
inline const std::string& CSMatModelData::_internal_ue5param() const {
  return ue5param_.Get();
}
inline void CSMatModelData::_internal_set_ue5param(const std::string& value) {
  
  ue5param_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_ue5param() {
  
  return ue5param_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_ue5param() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.ue5Param)
  return ue5param_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_ue5param(std::string* ue5param) {
  if (ue5param != nullptr) {
    
  } else {
    
  }
  ue5param_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ue5param,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.ue5Param)
}

// int64 templateId = 24;
inline void CSMatModelData::clear_templateid() {
  templateid_ = int64_t{0};
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::_internal_templateid() const {
  return templateid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CSMatModelData::templateid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.templateId)
  return _internal_templateid();
}
inline void CSMatModelData::_internal_set_templateid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  templateid_ = value;
}
inline void CSMatModelData::set_templateid(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_templateid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.templateId)
}

// int32 renderingId = 25;
inline void CSMatModelData::clear_renderingid() {
  renderingid_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::_internal_renderingid() const {
  return renderingid_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::renderingid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.renderingId)
  return _internal_renderingid();
}
inline void CSMatModelData::_internal_set_renderingid(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  renderingid_ = value;
}
inline void CSMatModelData::set_renderingid(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_renderingid(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.renderingId)
}

// string folderId = 26;
inline void CSMatModelData::clear_folderid() {
  folderid_.ClearToEmpty();
}
inline const std::string& CSMatModelData::folderid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.folderId)
  return _internal_folderid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_folderid(ArgT0&& arg0, ArgT... args) {
 
 folderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.folderId)
}
inline std::string* CSMatModelData::mutable_folderid() {
  std::string* _s = _internal_mutable_folderid();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.folderId)
  return _s;
}
inline const std::string& CSMatModelData::_internal_folderid() const {
  return folderid_.Get();
}
inline void CSMatModelData::_internal_set_folderid(const std::string& value) {
  
  folderid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_folderid() {
  
  return folderid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_folderid() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.folderId)
  return folderid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_folderid(std::string* folderid) {
  if (folderid != nullptr) {
    
  } else {
    
  }
  folderid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folderid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.folderId)
}

// string mark = 27;
inline void CSMatModelData::clear_mark() {
  mark_.ClearToEmpty();
}
inline const std::string& CSMatModelData::mark() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.mark)
  return _internal_mark();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_mark(ArgT0&& arg0, ArgT... args) {
 
 mark_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.mark)
}
inline std::string* CSMatModelData::mutable_mark() {
  std::string* _s = _internal_mutable_mark();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.mark)
  return _s;
}
inline const std::string& CSMatModelData::_internal_mark() const {
  return mark_.Get();
}
inline void CSMatModelData::_internal_set_mark(const std::string& value) {
  
  mark_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_mark() {
  
  return mark_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_mark() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.mark)
  return mark_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_mark(std::string* mark) {
  if (mark != nullptr) {
    
  } else {
    
  }
  mark_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mark,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.mark)
}

// int32 delFlag = 28;
inline void CSMatModelData::clear_delflag() {
  delflag_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::_internal_delflag() const {
  return delflag_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSMatModelData::delflag() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.delFlag)
  return _internal_delflag();
}
inline void CSMatModelData::_internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  delflag_ = value;
}
inline void CSMatModelData::set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_delflag(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.delFlag)
}

// string md5 = 29;
inline void CSMatModelData::clear_md5() {
  md5_.ClearToEmpty();
}
inline const std::string& CSMatModelData::md5() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.md5)
  return _internal_md5();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_md5(ArgT0&& arg0, ArgT... args) {
 
 md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.md5)
}
inline std::string* CSMatModelData::mutable_md5() {
  std::string* _s = _internal_mutable_md5();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.md5)
  return _s;
}
inline const std::string& CSMatModelData::_internal_md5() const {
  return md5_.Get();
}
inline void CSMatModelData::_internal_set_md5(const std::string& value) {
  
  md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_md5() {
  
  return md5_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_md5() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.md5)
  return md5_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_md5(std::string* md5) {
  if (md5 != nullptr) {
    
  } else {
    
  }
  md5_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), md5,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.md5)
}

// string createdBy = 30;
inline void CSMatModelData::clear_createdby() {
  createdby_.ClearToEmpty();
}
inline const std::string& CSMatModelData::createdby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.createdBy)
  return _internal_createdby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_createdby(ArgT0&& arg0, ArgT... args) {
 
 createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.createdBy)
}
inline std::string* CSMatModelData::mutable_createdby() {
  std::string* _s = _internal_mutable_createdby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.createdBy)
  return _s;
}
inline const std::string& CSMatModelData::_internal_createdby() const {
  return createdby_.Get();
}
inline void CSMatModelData::_internal_set_createdby(const std::string& value) {
  
  createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_createdby() {
  
  return createdby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_createdby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.createdBy)
  return createdby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_createdby(std::string* createdby) {
  if (createdby != nullptr) {
    
  } else {
    
  }
  createdby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.createdBy)
}

// string createdTime = 31;
inline void CSMatModelData::clear_createdtime() {
  createdtime_.ClearToEmpty();
}
inline const std::string& CSMatModelData::createdtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.createdTime)
  return _internal_createdtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_createdtime(ArgT0&& arg0, ArgT... args) {
 
 createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.createdTime)
}
inline std::string* CSMatModelData::mutable_createdtime() {
  std::string* _s = _internal_mutable_createdtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.createdTime)
  return _s;
}
inline const std::string& CSMatModelData::_internal_createdtime() const {
  return createdtime_.Get();
}
inline void CSMatModelData::_internal_set_createdtime(const std::string& value) {
  
  createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_createdtime() {
  
  return createdtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_createdtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.createdTime)
  return createdtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_createdtime(std::string* createdtime) {
  if (createdtime != nullptr) {
    
  } else {
    
  }
  createdtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.createdTime)
}

// string updatedBy = 32;
inline void CSMatModelData::clear_updatedby() {
  updatedby_.ClearToEmpty();
}
inline const std::string& CSMatModelData::updatedby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.updatedBy)
  return _internal_updatedby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_updatedby(ArgT0&& arg0, ArgT... args) {
 
 updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.updatedBy)
}
inline std::string* CSMatModelData::mutable_updatedby() {
  std::string* _s = _internal_mutable_updatedby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.updatedBy)
  return _s;
}
inline const std::string& CSMatModelData::_internal_updatedby() const {
  return updatedby_.Get();
}
inline void CSMatModelData::_internal_set_updatedby(const std::string& value) {
  
  updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_updatedby() {
  
  return updatedby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_updatedby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.updatedBy)
  return updatedby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_updatedby(std::string* updatedby) {
  if (updatedby != nullptr) {
    
  } else {
    
  }
  updatedby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.updatedBy)
}

// string updatedTime = 33;
inline void CSMatModelData::clear_updatedtime() {
  updatedtime_.ClearToEmpty();
}
inline const std::string& CSMatModelData::updatedtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.updatedTime)
  return _internal_updatedtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_updatedtime(ArgT0&& arg0, ArgT... args) {
 
 updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.updatedTime)
}
inline std::string* CSMatModelData::mutable_updatedtime() {
  std::string* _s = _internal_mutable_updatedtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.updatedTime)
  return _s;
}
inline const std::string& CSMatModelData::_internal_updatedtime() const {
  return updatedtime_.Get();
}
inline void CSMatModelData::_internal_set_updatedtime(const std::string& value) {
  
  updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_updatedtime() {
  
  return updatedtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_updatedtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.updatedTime)
  return updatedtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_updatedtime(std::string* updatedtime) {
  if (updatedtime != nullptr) {
    
  } else {
    
  }
  updatedtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.updatedTime)
}

// repeated .catalog_studio_message.CSMatMapData manageMapsList = 34;
inline int CSMatModelData::_internal_managemapslist_size() const {
  return managemapslist_.size();
}
inline int CSMatModelData::managemapslist_size() const {
  return _internal_managemapslist_size();
}
inline ::catalog_studio_message::CSMatMapData* CSMatModelData::mutable_managemapslist(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.manageMapsList)
  return managemapslist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatMapData >*
CSMatModelData::mutable_managemapslist() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CSMatModelData.manageMapsList)
  return &managemapslist_;
}
inline const ::catalog_studio_message::CSMatMapData& CSMatModelData::_internal_managemapslist(int index) const {
  return managemapslist_.Get(index);
}
inline const ::catalog_studio_message::CSMatMapData& CSMatModelData::managemapslist(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.manageMapsList)
  return _internal_managemapslist(index);
}
inline ::catalog_studio_message::CSMatMapData* CSMatModelData::_internal_add_managemapslist() {
  return managemapslist_.Add();
}
inline ::catalog_studio_message::CSMatMapData* CSMatModelData::add_managemapslist() {
  ::catalog_studio_message::CSMatMapData* _add = _internal_add_managemapslist();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CSMatModelData.manageMapsList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatMapData >&
CSMatModelData::managemapslist() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CSMatModelData.manageMapsList)
  return managemapslist_;
}

// repeated .catalog_studio_message.CSMatModelLableData labelList = 35;
inline int CSMatModelData::_internal_labellist_size() const {
  return labellist_.size();
}
inline int CSMatModelData::labellist_size() const {
  return _internal_labellist_size();
}
inline ::catalog_studio_message::CSMatModelLableData* CSMatModelData::mutable_labellist(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.labelList)
  return labellist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelLableData >*
CSMatModelData::mutable_labellist() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CSMatModelData.labelList)
  return &labellist_;
}
inline const ::catalog_studio_message::CSMatModelLableData& CSMatModelData::_internal_labellist(int index) const {
  return labellist_.Get(index);
}
inline const ::catalog_studio_message::CSMatModelLableData& CSMatModelData::labellist(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.labelList)
  return _internal_labellist(index);
}
inline ::catalog_studio_message::CSMatModelLableData* CSMatModelData::_internal_add_labellist() {
  return labellist_.Add();
}
inline ::catalog_studio_message::CSMatModelLableData* CSMatModelData::add_labellist() {
  ::catalog_studio_message::CSMatModelLableData* _add = _internal_add_labellist();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CSMatModelData.labelList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelLableData >&
CSMatModelData::labellist() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CSMatModelData.labelList)
  return labellist_;
}

// repeated .catalog_studio_message.CSModelInfoData modelList = 36;
inline int CSMatModelData::_internal_modellist_size() const {
  return modellist_.size();
}
inline int CSMatModelData::modellist_size() const {
  return _internal_modellist_size();
}
inline ::catalog_studio_message::CSModelInfoData* CSMatModelData::mutable_modellist(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.modelList)
  return modellist_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSModelInfoData >*
CSMatModelData::mutable_modellist() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CSMatModelData.modelList)
  return &modellist_;
}
inline const ::catalog_studio_message::CSModelInfoData& CSMatModelData::_internal_modellist(int index) const {
  return modellist_.Get(index);
}
inline const ::catalog_studio_message::CSModelInfoData& CSMatModelData::modellist(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.modelList)
  return _internal_modellist(index);
}
inline ::catalog_studio_message::CSModelInfoData* CSMatModelData::_internal_add_modellist() {
  return modellist_.Add();
}
inline ::catalog_studio_message::CSModelInfoData* CSMatModelData::add_modellist() {
  ::catalog_studio_message::CSModelInfoData* _add = _internal_add_modellist();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CSMatModelData.modelList)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSModelInfoData >&
CSMatModelData::modellist() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CSMatModelData.modelList)
  return modellist_;
}

// string folderCode = 37;
inline void CSMatModelData::clear_foldercode() {
  foldercode_.ClearToEmpty();
}
inline const std::string& CSMatModelData::foldercode() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSMatModelData.folderCode)
  return _internal_foldercode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSMatModelData::set_foldercode(ArgT0&& arg0, ArgT... args) {
 
 foldercode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSMatModelData.folderCode)
}
inline std::string* CSMatModelData::mutable_foldercode() {
  std::string* _s = _internal_mutable_foldercode();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSMatModelData.folderCode)
  return _s;
}
inline const std::string& CSMatModelData::_internal_foldercode() const {
  return foldercode_.Get();
}
inline void CSMatModelData::_internal_set_foldercode(const std::string& value) {
  
  foldercode_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSMatModelData::_internal_mutable_foldercode() {
  
  return foldercode_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSMatModelData::release_foldercode() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSMatModelData.folderCode)
  return foldercode_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSMatModelData::set_allocated_foldercode(std::string* foldercode) {
  if (foldercode != nullptr) {
    
  } else {
    
  }
  foldercode_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), foldercode,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSMatModelData.folderCode)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_CSMatModel_2eproto
