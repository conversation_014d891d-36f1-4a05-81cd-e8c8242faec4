// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CSModelInfo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_CSModelInfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_CSModelInfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_CSModelInfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_CSModelInfo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CSModelInfo_2eproto;
namespace catalog_studio_message {
class CSModelInfoData;
struct CSModelInfoDataDefaultTypeInternal;
extern CSModelInfoDataDefaultTypeInternal _CSModelInfoData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::CSModelInfoData* Arena::CreateMaybeMessage<::catalog_studio_message::CSModelInfoData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class CSModelInfoData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.CSModelInfoData) */ {
 public:
  inline CSModelInfoData() : CSModelInfoData(nullptr) {}
  ~CSModelInfoData() override;
  explicit constexpr CSModelInfoData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CSModelInfoData(const CSModelInfoData& from);
  CSModelInfoData(CSModelInfoData&& from) noexcept
    : CSModelInfoData() {
    *this = ::std::move(from);
  }

  inline CSModelInfoData& operator=(const CSModelInfoData& from) {
    CopyFrom(from);
    return *this;
  }
  inline CSModelInfoData& operator=(CSModelInfoData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CSModelInfoData& default_instance() {
    return *internal_default_instance();
  }
  static inline const CSModelInfoData* internal_default_instance() {
    return reinterpret_cast<const CSModelInfoData*>(
               &_CSModelInfoData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CSModelInfoData& a, CSModelInfoData& b) {
    a.Swap(&b);
  }
  inline void Swap(CSModelInfoData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CSModelInfoData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CSModelInfoData* New() const final {
    return new CSModelInfoData();
  }

  CSModelInfoData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CSModelInfoData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CSModelInfoData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CSModelInfoData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CSModelInfoData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.CSModelInfoData";
  }
  protected:
  explicit CSModelInfoData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCodeFieldNumber = 3,
    kMd5FieldNumber = 4,
    kNameFieldNumber = 5,
    kFbxFilePathFieldNumber = 6,
    kIdFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // string code = 3;
  void clear_code();
  const std::string& code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_code();
  void set_allocated_code(std::string* code);
  private:
  const std::string& _internal_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code(const std::string& value);
  std::string* _internal_mutable_code();
  public:

  // string md5 = 4;
  void clear_md5();
  const std::string& md5() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_md5(ArgT0&& arg0, ArgT... args);
  std::string* mutable_md5();
  PROTOBUF_MUST_USE_RESULT std::string* release_md5();
  void set_allocated_md5(std::string* md5);
  private:
  const std::string& _internal_md5() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_md5(const std::string& value);
  std::string* _internal_mutable_md5();
  public:

  // string name = 5;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string fbxFilePath = 6;
  void clear_fbxfilepath();
  const std::string& fbxfilepath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_fbxfilepath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_fbxfilepath();
  PROTOBUF_MUST_USE_RESULT std::string* release_fbxfilepath();
  void set_allocated_fbxfilepath(std::string* fbxfilepath);
  private:
  const std::string& _internal_fbxfilepath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_fbxfilepath(const std::string& value);
  std::string* _internal_mutable_fbxfilepath();
  public:

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 type = 2;
  void clear_type();
  ::PROTOBUF_NAMESPACE_ID::int32 type() const;
  void set_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_type() const;
  void _internal_set_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.CSModelInfoData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr md5_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fbxfilepath_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_CSModelInfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CSModelInfoData

// int32 id = 1;
inline void CSModelInfoData::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSModelInfoData::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSModelInfoData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.id)
  return _internal_id();
}
inline void CSModelInfoData::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void CSModelInfoData::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.id)
}

// int32 type = 2;
inline void CSModelInfoData::clear_type() {
  type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSModelInfoData::_internal_type() const {
  return type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CSModelInfoData::type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.type)
  return _internal_type();
}
inline void CSModelInfoData::_internal_set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  type_ = value;
}
inline void CSModelInfoData::set_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.type)
}

// string code = 3;
inline void CSModelInfoData::clear_code() {
  code_.ClearToEmpty();
}
inline const std::string& CSModelInfoData::code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.code)
  return _internal_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSModelInfoData::set_code(ArgT0&& arg0, ArgT... args) {
 
 code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.code)
}
inline std::string* CSModelInfoData::mutable_code() {
  std::string* _s = _internal_mutable_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSModelInfoData.code)
  return _s;
}
inline const std::string& CSModelInfoData::_internal_code() const {
  return code_.Get();
}
inline void CSModelInfoData::_internal_set_code(const std::string& value) {
  
  code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::_internal_mutable_code() {
  
  return code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::release_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSModelInfoData.code)
  return code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSModelInfoData::set_allocated_code(std::string* code) {
  if (code != nullptr) {
    
  } else {
    
  }
  code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSModelInfoData.code)
}

// string md5 = 4;
inline void CSModelInfoData::clear_md5() {
  md5_.ClearToEmpty();
}
inline const std::string& CSModelInfoData::md5() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.md5)
  return _internal_md5();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSModelInfoData::set_md5(ArgT0&& arg0, ArgT... args) {
 
 md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.md5)
}
inline std::string* CSModelInfoData::mutable_md5() {
  std::string* _s = _internal_mutable_md5();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSModelInfoData.md5)
  return _s;
}
inline const std::string& CSModelInfoData::_internal_md5() const {
  return md5_.Get();
}
inline void CSModelInfoData::_internal_set_md5(const std::string& value) {
  
  md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::_internal_mutable_md5() {
  
  return md5_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::release_md5() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSModelInfoData.md5)
  return md5_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSModelInfoData::set_allocated_md5(std::string* md5) {
  if (md5 != nullptr) {
    
  } else {
    
  }
  md5_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), md5,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSModelInfoData.md5)
}

// string name = 5;
inline void CSModelInfoData::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& CSModelInfoData::name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSModelInfoData::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.name)
}
inline std::string* CSModelInfoData::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSModelInfoData.name)
  return _s;
}
inline const std::string& CSModelInfoData::_internal_name() const {
  return name_.Get();
}
inline void CSModelInfoData::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::release_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSModelInfoData.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSModelInfoData::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSModelInfoData.name)
}

// string fbxFilePath = 6;
inline void CSModelInfoData::clear_fbxfilepath() {
  fbxfilepath_.ClearToEmpty();
}
inline const std::string& CSModelInfoData::fbxfilepath() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CSModelInfoData.fbxFilePath)
  return _internal_fbxfilepath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CSModelInfoData::set_fbxfilepath(ArgT0&& arg0, ArgT... args) {
 
 fbxfilepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.CSModelInfoData.fbxFilePath)
}
inline std::string* CSModelInfoData::mutable_fbxfilepath() {
  std::string* _s = _internal_mutable_fbxfilepath();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CSModelInfoData.fbxFilePath)
  return _s;
}
inline const std::string& CSModelInfoData::_internal_fbxfilepath() const {
  return fbxfilepath_.Get();
}
inline void CSModelInfoData::_internal_set_fbxfilepath(const std::string& value) {
  
  fbxfilepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::_internal_mutable_fbxfilepath() {
  
  return fbxfilepath_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CSModelInfoData::release_fbxfilepath() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.CSModelInfoData.fbxFilePath)
  return fbxfilepath_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CSModelInfoData::set_allocated_fbxfilepath(std::string* fbxfilepath) {
  if (fbxfilepath != nullptr) {
    
  } else {
    
  }
  fbxfilepath_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), fbxfilepath,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.CSModelInfoData.fbxFilePath)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_CSModelInfo_2eproto
