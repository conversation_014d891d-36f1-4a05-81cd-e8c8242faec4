// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CatalogFolderTableData.proto

#include "CatalogFolderTableData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr FolderTableDataMsg::FolderTableDataMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_code_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , thumbnail_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , parent_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , visibility_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , backend_directory_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , front_directory_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ref_type_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ref_type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_name_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , folder_type_(0)
  , visibility_(0)
  , folder_order_(0)
  , is_new_(false)
  , is_folder_(false)
  , place_rule_(0){}
struct FolderTableDataMsgDefaultTypeInternal {
  constexpr FolderTableDataMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FolderTableDataMsgDefaultTypeInternal() {}
  union {
    FolderTableDataMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FolderTableDataMsgDefaultTypeInternal _FolderTableDataMsg_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_CatalogFolderTableData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_CatalogFolderTableData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_CatalogFolderTableData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_CatalogFolderTableData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_code_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, thumbnail_path_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, parent_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, visibility_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, visibility_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_order_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, is_new_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, is_folder_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, backend_directory_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, front_directory_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, place_rule_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, ref_type_code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, ref_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, folder_name_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderTableDataMsg, description_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::FolderTableDataMsg)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_FolderTableDataMsg_default_instance_),
};

const char descriptor_table_protodef_CatalogFolderTableData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034CatalogFolderTableData.proto\022\026catalog_"
  "studio_message\"\272\003\n\022FolderTableDataMsg\022\n\n"
  "\002id\030\001 \001(\t\022\021\n\tfolder_id\030\002 \001(\t\022\023\n\013folder_n"
  "ame\030\003 \001(\t\022\023\n\013folder_code\030\004 \001(\t\022\027\n\017folder"
  "_code_exp\030\005 \001(\t\022\023\n\013folder_type\030\006 \001(\005\022\026\n\016"
  "thumbnail_path\030\007 \001(\t\022\021\n\tparent_id\030\010 \001(\t\022"
  "\026\n\016visibility_exp\030\t \001(\t\022\022\n\nvisibility\030\n "
  "\001(\002\022\024\n\014folder_order\030\013 \001(\005\022\016\n\006is_new\030\014 \001("
  "\010\022\021\n\tis_folder\030\r \001(\010\022\031\n\021backend_director"
  "y\030\016 \001(\t\022\027\n\017front_directory\030\017 \001(\t\022\022\n\nplac"
  "e_rule\030\020 \001(\005\022\025\n\rref_type_code\030\021 \001(\t\022\020\n\010r"
  "ef_type\030\022 \001(\t\022\027\n\017folder_name_exp\030\023 \001(\t\022\023"
  "\n\013description\030\024 \001(\tb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_CatalogFolderTableData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CatalogFolderTableData_2eproto = {
  false, false, 507, descriptor_table_protodef_CatalogFolderTableData_2eproto, "CatalogFolderTableData.proto", 
  &descriptor_table_CatalogFolderTableData_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_CatalogFolderTableData_2eproto::offsets,
  file_level_metadata_CatalogFolderTableData_2eproto, file_level_enum_descriptors_CatalogFolderTableData_2eproto, file_level_service_descriptors_CatalogFolderTableData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_CatalogFolderTableData_2eproto_getter() {
  return &descriptor_table_CatalogFolderTableData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_CatalogFolderTableData_2eproto(&descriptor_table_CatalogFolderTableData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class FolderTableDataMsg::_Internal {
 public:
};

FolderTableDataMsg::FolderTableDataMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.FolderTableDataMsg)
}
FolderTableDataMsg::FolderTableDataMsg(const FolderTableDataMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  folder_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folder_id().empty()) {
    folder_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_id(), 
      GetArenaForAllocation());
  }
  folder_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folder_name().empty()) {
    folder_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_name(), 
      GetArenaForAllocation());
  }
  folder_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folder_code().empty()) {
    folder_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_code(), 
      GetArenaForAllocation());
  }
  folder_code_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folder_code_exp().empty()) {
    folder_code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_code_exp(), 
      GetArenaForAllocation());
  }
  thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_thumbnail_path().empty()) {
    thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_thumbnail_path(), 
      GetArenaForAllocation());
  }
  parent_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_parent_id().empty()) {
    parent_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_parent_id(), 
      GetArenaForAllocation());
  }
  visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_visibility_exp().empty()) {
    visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_visibility_exp(), 
      GetArenaForAllocation());
  }
  backend_directory_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_backend_directory().empty()) {
    backend_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_backend_directory(), 
      GetArenaForAllocation());
  }
  front_directory_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_front_directory().empty()) {
    front_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_front_directory(), 
      GetArenaForAllocation());
  }
  ref_type_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_ref_type_code().empty()) {
    ref_type_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ref_type_code(), 
      GetArenaForAllocation());
  }
  ref_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_ref_type().empty()) {
    ref_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ref_type(), 
      GetArenaForAllocation());
  }
  folder_name_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_folder_name_exp().empty()) {
    folder_name_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_name_exp(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  ::memcpy(&folder_type_, &from.folder_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&place_rule_) -
    reinterpret_cast<char*>(&folder_type_)) + sizeof(place_rule_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.FolderTableDataMsg)
}

void FolderTableDataMsg::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folder_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folder_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folder_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folder_code_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
parent_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
backend_directory_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
front_directory_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
ref_type_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
ref_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
folder_name_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&folder_type_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&place_rule_) -
    reinterpret_cast<char*>(&folder_type_)) + sizeof(place_rule_));
}

FolderTableDataMsg::~FolderTableDataMsg() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.FolderTableDataMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FolderTableDataMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folder_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folder_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folder_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folder_code_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  thumbnail_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parent_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  visibility_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  backend_directory_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  front_directory_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ref_type_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ref_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  folder_name_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FolderTableDataMsg::ArenaDtor(void* object) {
  FolderTableDataMsg* _this = reinterpret_cast< FolderTableDataMsg* >(object);
  (void)_this;
}
void FolderTableDataMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FolderTableDataMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FolderTableDataMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.FolderTableDataMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  folder_id_.ClearToEmpty();
  folder_name_.ClearToEmpty();
  folder_code_.ClearToEmpty();
  folder_code_exp_.ClearToEmpty();
  thumbnail_path_.ClearToEmpty();
  parent_id_.ClearToEmpty();
  visibility_exp_.ClearToEmpty();
  backend_directory_.ClearToEmpty();
  front_directory_.ClearToEmpty();
  ref_type_code_.ClearToEmpty();
  ref_type_.ClearToEmpty();
  folder_name_exp_.ClearToEmpty();
  description_.ClearToEmpty();
  ::memset(&folder_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&place_rule_) -
      reinterpret_cast<char*>(&folder_type_)) + sizeof(place_rule_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FolderTableDataMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folder_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_folder_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.folder_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folder_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_folder_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.folder_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folder_code = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_folder_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.folder_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folder_code_exp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_folder_code_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.folder_code_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 folder_type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          folder_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string thumbnail_path = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_thumbnail_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.thumbnail_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string parent_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_parent_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.parent_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string visibility_exp = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_visibility_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.visibility_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float visibility = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          visibility_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 folder_order = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          folder_order_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_new = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          is_new_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_folder = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          is_folder_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string backend_directory = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          auto str = _internal_mutable_backend_directory();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.backend_directory"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string front_directory = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          auto str = _internal_mutable_front_directory();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.front_directory"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 place_rule = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          place_rule_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string ref_type_code = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          auto str = _internal_mutable_ref_type_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.ref_type_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string ref_type = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          auto str = _internal_mutable_ref_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.ref_type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string folder_name_exp = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          auto str = _internal_mutable_folder_name_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.folder_name_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.FolderTableDataMsg.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* FolderTableDataMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.FolderTableDataMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string folder_id = 2;
  if (!this->_internal_folder_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folder_id().data(), static_cast<int>(this->_internal_folder_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.folder_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_folder_id(), target);
  }

  // string folder_name = 3;
  if (!this->_internal_folder_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folder_name().data(), static_cast<int>(this->_internal_folder_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.folder_name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_folder_name(), target);
  }

  // string folder_code = 4;
  if (!this->_internal_folder_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folder_code().data(), static_cast<int>(this->_internal_folder_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.folder_code");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_folder_code(), target);
  }

  // string folder_code_exp = 5;
  if (!this->_internal_folder_code_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folder_code_exp().data(), static_cast<int>(this->_internal_folder_code_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.folder_code_exp");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_folder_code_exp(), target);
  }

  // int32 folder_type = 6;
  if (this->_internal_folder_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_folder_type(), target);
  }

  // string thumbnail_path = 7;
  if (!this->_internal_thumbnail_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_thumbnail_path().data(), static_cast<int>(this->_internal_thumbnail_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.thumbnail_path");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_thumbnail_path(), target);
  }

  // string parent_id = 8;
  if (!this->_internal_parent_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_parent_id().data(), static_cast<int>(this->_internal_parent_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.parent_id");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_parent_id(), target);
  }

  // string visibility_exp = 9;
  if (!this->_internal_visibility_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_visibility_exp().data(), static_cast<int>(this->_internal_visibility_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.visibility_exp");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_visibility_exp(), target);
  }

  // float visibility = 10;
  if (!(this->_internal_visibility() <= 0 && this->_internal_visibility() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_visibility(), target);
  }

  // int32 folder_order = 11;
  if (this->_internal_folder_order() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(11, this->_internal_folder_order(), target);
  }

  // bool is_new = 12;
  if (this->_internal_is_new() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(12, this->_internal_is_new(), target);
  }

  // bool is_folder = 13;
  if (this->_internal_is_folder() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(13, this->_internal_is_folder(), target);
  }

  // string backend_directory = 14;
  if (!this->_internal_backend_directory().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_backend_directory().data(), static_cast<int>(this->_internal_backend_directory().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.backend_directory");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_backend_directory(), target);
  }

  // string front_directory = 15;
  if (!this->_internal_front_directory().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_front_directory().data(), static_cast<int>(this->_internal_front_directory().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.front_directory");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_front_directory(), target);
  }

  // int32 place_rule = 16;
  if (this->_internal_place_rule() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(16, this->_internal_place_rule(), target);
  }

  // string ref_type_code = 17;
  if (!this->_internal_ref_type_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ref_type_code().data(), static_cast<int>(this->_internal_ref_type_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.ref_type_code");
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_ref_type_code(), target);
  }

  // string ref_type = 18;
  if (!this->_internal_ref_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ref_type().data(), static_cast<int>(this->_internal_ref_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.ref_type");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_ref_type(), target);
  }

  // string folder_name_exp = 19;
  if (!this->_internal_folder_name_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_folder_name_exp().data(), static_cast<int>(this->_internal_folder_name_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.folder_name_exp");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_folder_name_exp(), target);
  }

  // string description = 20;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.FolderTableDataMsg.description");
    target = stream->WriteStringMaybeAliased(
        20, this->_internal_description(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.FolderTableDataMsg)
  return target;
}

size_t FolderTableDataMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.FolderTableDataMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string folder_id = 2;
  if (!this->_internal_folder_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_id());
  }

  // string folder_name = 3;
  if (!this->_internal_folder_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_name());
  }

  // string folder_code = 4;
  if (!this->_internal_folder_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_code());
  }

  // string folder_code_exp = 5;
  if (!this->_internal_folder_code_exp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_code_exp());
  }

  // string thumbnail_path = 7;
  if (!this->_internal_thumbnail_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_thumbnail_path());
  }

  // string parent_id = 8;
  if (!this->_internal_parent_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_parent_id());
  }

  // string visibility_exp = 9;
  if (!this->_internal_visibility_exp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_visibility_exp());
  }

  // string backend_directory = 14;
  if (!this->_internal_backend_directory().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_backend_directory());
  }

  // string front_directory = 15;
  if (!this->_internal_front_directory().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_front_directory());
  }

  // string ref_type_code = 17;
  if (!this->_internal_ref_type_code().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ref_type_code());
  }

  // string ref_type = 18;
  if (!this->_internal_ref_type().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ref_type());
  }

  // string folder_name_exp = 19;
  if (!this->_internal_folder_name_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_name_exp());
  }

  // string description = 20;
  if (!this->_internal_description().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // int32 folder_type = 6;
  if (this->_internal_folder_type() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_folder_type());
  }

  // float visibility = 10;
  if (!(this->_internal_visibility() <= 0 && this->_internal_visibility() >= 0)) {
    total_size += 1 + 4;
  }

  // int32 folder_order = 11;
  if (this->_internal_folder_order() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_folder_order());
  }

  // bool is_new = 12;
  if (this->_internal_is_new() != 0) {
    total_size += 1 + 1;
  }

  // bool is_folder = 13;
  if (this->_internal_is_folder() != 0) {
    total_size += 1 + 1;
  }

  // int32 place_rule = 16;
  if (this->_internal_place_rule() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_place_rule());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FolderTableDataMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FolderTableDataMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FolderTableDataMsg::GetClassData() const { return &_class_data_; }

void FolderTableDataMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FolderTableDataMsg *>(to)->MergeFrom(
      static_cast<const FolderTableDataMsg &>(from));
}


void FolderTableDataMsg::MergeFrom(const FolderTableDataMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.FolderTableDataMsg)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_folder_id().empty()) {
    _internal_set_folder_id(from._internal_folder_id());
  }
  if (!from._internal_folder_name().empty()) {
    _internal_set_folder_name(from._internal_folder_name());
  }
  if (!from._internal_folder_code().empty()) {
    _internal_set_folder_code(from._internal_folder_code());
  }
  if (!from._internal_folder_code_exp().empty()) {
    _internal_set_folder_code_exp(from._internal_folder_code_exp());
  }
  if (!from._internal_thumbnail_path().empty()) {
    _internal_set_thumbnail_path(from._internal_thumbnail_path());
  }
  if (!from._internal_parent_id().empty()) {
    _internal_set_parent_id(from._internal_parent_id());
  }
  if (!from._internal_visibility_exp().empty()) {
    _internal_set_visibility_exp(from._internal_visibility_exp());
  }
  if (!from._internal_backend_directory().empty()) {
    _internal_set_backend_directory(from._internal_backend_directory());
  }
  if (!from._internal_front_directory().empty()) {
    _internal_set_front_directory(from._internal_front_directory());
  }
  if (!from._internal_ref_type_code().empty()) {
    _internal_set_ref_type_code(from._internal_ref_type_code());
  }
  if (!from._internal_ref_type().empty()) {
    _internal_set_ref_type(from._internal_ref_type());
  }
  if (!from._internal_folder_name_exp().empty()) {
    _internal_set_folder_name_exp(from._internal_folder_name_exp());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (from._internal_folder_type() != 0) {
    _internal_set_folder_type(from._internal_folder_type());
  }
  if (!(from._internal_visibility() <= 0 && from._internal_visibility() >= 0)) {
    _internal_set_visibility(from._internal_visibility());
  }
  if (from._internal_folder_order() != 0) {
    _internal_set_folder_order(from._internal_folder_order());
  }
  if (from._internal_is_new() != 0) {
    _internal_set_is_new(from._internal_is_new());
  }
  if (from._internal_is_folder() != 0) {
    _internal_set_is_folder(from._internal_is_folder());
  }
  if (from._internal_place_rule() != 0) {
    _internal_set_place_rule(from._internal_place_rule());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FolderTableDataMsg::CopyFrom(const FolderTableDataMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.FolderTableDataMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FolderTableDataMsg::IsInitialized() const {
  return true;
}

void FolderTableDataMsg::InternalSwap(FolderTableDataMsg* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folder_id_, lhs_arena,
      &other->folder_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folder_name_, lhs_arena,
      &other->folder_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folder_code_, lhs_arena,
      &other->folder_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folder_code_exp_, lhs_arena,
      &other->folder_code_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &thumbnail_path_, lhs_arena,
      &other->thumbnail_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &parent_id_, lhs_arena,
      &other->parent_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &visibility_exp_, lhs_arena,
      &other->visibility_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &backend_directory_, lhs_arena,
      &other->backend_directory_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &front_directory_, lhs_arena,
      &other->front_directory_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &ref_type_code_, lhs_arena,
      &other->ref_type_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &ref_type_, lhs_arena,
      &other->ref_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &folder_name_exp_, lhs_arena,
      &other->folder_name_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FolderTableDataMsg, place_rule_)
      + sizeof(FolderTableDataMsg::place_rule_)
      - PROTOBUF_FIELD_OFFSET(FolderTableDataMsg, folder_type_)>(
          reinterpret_cast<char*>(&folder_type_),
          reinterpret_cast<char*>(&other->folder_type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FolderTableDataMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_CatalogFolderTableData_2eproto_getter, &descriptor_table_CatalogFolderTableData_2eproto_once,
      file_level_metadata_CatalogFolderTableData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::FolderTableDataMsg* Arena::CreateMaybeMessage< ::catalog_studio_message::FolderTableDataMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::FolderTableDataMsg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
