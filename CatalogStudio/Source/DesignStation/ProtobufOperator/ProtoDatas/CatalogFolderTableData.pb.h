// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CatalogFolderTableData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_CatalogFolderTableData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_CatalogFolderTableData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_CatalogFolderTableData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_CatalogFolderTableData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CatalogFolderTableData_2eproto;
namespace catalog_studio_message {
class FolderTableDataMsg;
struct FolderTableDataMsgDefaultTypeInternal;
extern FolderTableDataMsgDefaultTypeInternal _FolderTableDataMsg_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::FolderTableDataMsg* Arena::CreateMaybeMessage<::catalog_studio_message::FolderTableDataMsg>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class FolderTableDataMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.FolderTableDataMsg) */ {
 public:
  inline FolderTableDataMsg() : FolderTableDataMsg(nullptr) {}
  ~FolderTableDataMsg() override;
  explicit constexpr FolderTableDataMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FolderTableDataMsg(const FolderTableDataMsg& from);
  FolderTableDataMsg(FolderTableDataMsg&& from) noexcept
    : FolderTableDataMsg() {
    *this = ::std::move(from);
  }

  inline FolderTableDataMsg& operator=(const FolderTableDataMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline FolderTableDataMsg& operator=(FolderTableDataMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FolderTableDataMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const FolderTableDataMsg* internal_default_instance() {
    return reinterpret_cast<const FolderTableDataMsg*>(
               &_FolderTableDataMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FolderTableDataMsg& a, FolderTableDataMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(FolderTableDataMsg* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FolderTableDataMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FolderTableDataMsg* New() const final {
    return new FolderTableDataMsg();
  }

  FolderTableDataMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FolderTableDataMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FolderTableDataMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FolderTableDataMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FolderTableDataMsg* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.FolderTableDataMsg";
  }
  protected:
  explicit FolderTableDataMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kFolderIdFieldNumber = 2,
    kFolderNameFieldNumber = 3,
    kFolderCodeFieldNumber = 4,
    kFolderCodeExpFieldNumber = 5,
    kThumbnailPathFieldNumber = 7,
    kParentIdFieldNumber = 8,
    kVisibilityExpFieldNumber = 9,
    kBackendDirectoryFieldNumber = 14,
    kFrontDirectoryFieldNumber = 15,
    kRefTypeCodeFieldNumber = 17,
    kRefTypeFieldNumber = 18,
    kFolderNameExpFieldNumber = 19,
    kDescriptionFieldNumber = 20,
    kFolderTypeFieldNumber = 6,
    kVisibilityFieldNumber = 10,
    kFolderOrderFieldNumber = 11,
    kIsNewFieldNumber = 12,
    kIsFolderFieldNumber = 13,
    kPlaceRuleFieldNumber = 16,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string folder_id = 2;
  void clear_folder_id();
  const std::string& folder_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folder_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folder_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_folder_id();
  void set_allocated_folder_id(std::string* folder_id);
  private:
  const std::string& _internal_folder_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folder_id(const std::string& value);
  std::string* _internal_mutable_folder_id();
  public:

  // string folder_name = 3;
  void clear_folder_name();
  const std::string& folder_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folder_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folder_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_folder_name();
  void set_allocated_folder_name(std::string* folder_name);
  private:
  const std::string& _internal_folder_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folder_name(const std::string& value);
  std::string* _internal_mutable_folder_name();
  public:

  // string folder_code = 4;
  void clear_folder_code();
  const std::string& folder_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folder_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folder_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_folder_code();
  void set_allocated_folder_code(std::string* folder_code);
  private:
  const std::string& _internal_folder_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folder_code(const std::string& value);
  std::string* _internal_mutable_folder_code();
  public:

  // string folder_code_exp = 5;
  void clear_folder_code_exp();
  const std::string& folder_code_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folder_code_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folder_code_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_folder_code_exp();
  void set_allocated_folder_code_exp(std::string* folder_code_exp);
  private:
  const std::string& _internal_folder_code_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folder_code_exp(const std::string& value);
  std::string* _internal_mutable_folder_code_exp();
  public:

  // string thumbnail_path = 7;
  void clear_thumbnail_path();
  const std::string& thumbnail_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_thumbnail_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_thumbnail_path();
  PROTOBUF_MUST_USE_RESULT std::string* release_thumbnail_path();
  void set_allocated_thumbnail_path(std::string* thumbnail_path);
  private:
  const std::string& _internal_thumbnail_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_thumbnail_path(const std::string& value);
  std::string* _internal_mutable_thumbnail_path();
  public:

  // string parent_id = 8;
  void clear_parent_id();
  const std::string& parent_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_parent_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_parent_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_parent_id();
  void set_allocated_parent_id(std::string* parent_id);
  private:
  const std::string& _internal_parent_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_parent_id(const std::string& value);
  std::string* _internal_mutable_parent_id();
  public:

  // string visibility_exp = 9;
  void clear_visibility_exp();
  const std::string& visibility_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_visibility_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_visibility_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_visibility_exp();
  void set_allocated_visibility_exp(std::string* visibility_exp);
  private:
  const std::string& _internal_visibility_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_visibility_exp(const std::string& value);
  std::string* _internal_mutable_visibility_exp();
  public:

  // string backend_directory = 14;
  void clear_backend_directory();
  const std::string& backend_directory() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_backend_directory(ArgT0&& arg0, ArgT... args);
  std::string* mutable_backend_directory();
  PROTOBUF_MUST_USE_RESULT std::string* release_backend_directory();
  void set_allocated_backend_directory(std::string* backend_directory);
  private:
  const std::string& _internal_backend_directory() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_backend_directory(const std::string& value);
  std::string* _internal_mutable_backend_directory();
  public:

  // string front_directory = 15;
  void clear_front_directory();
  const std::string& front_directory() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_front_directory(ArgT0&& arg0, ArgT... args);
  std::string* mutable_front_directory();
  PROTOBUF_MUST_USE_RESULT std::string* release_front_directory();
  void set_allocated_front_directory(std::string* front_directory);
  private:
  const std::string& _internal_front_directory() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_front_directory(const std::string& value);
  std::string* _internal_mutable_front_directory();
  public:

  // string ref_type_code = 17;
  void clear_ref_type_code();
  const std::string& ref_type_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ref_type_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ref_type_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_ref_type_code();
  void set_allocated_ref_type_code(std::string* ref_type_code);
  private:
  const std::string& _internal_ref_type_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ref_type_code(const std::string& value);
  std::string* _internal_mutable_ref_type_code();
  public:

  // string ref_type = 18;
  void clear_ref_type();
  const std::string& ref_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ref_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ref_type();
  PROTOBUF_MUST_USE_RESULT std::string* release_ref_type();
  void set_allocated_ref_type(std::string* ref_type);
  private:
  const std::string& _internal_ref_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ref_type(const std::string& value);
  std::string* _internal_mutable_ref_type();
  public:

  // string folder_name_exp = 19;
  void clear_folder_name_exp();
  const std::string& folder_name_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_folder_name_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_folder_name_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_folder_name_exp();
  void set_allocated_folder_name_exp(std::string* folder_name_exp);
  private:
  const std::string& _internal_folder_name_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_folder_name_exp(const std::string& value);
  std::string* _internal_mutable_folder_name_exp();
  public:

  // string description = 20;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_MUST_USE_RESULT std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // int32 folder_type = 6;
  void clear_folder_type();
  ::PROTOBUF_NAMESPACE_ID::int32 folder_type() const;
  void set_folder_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_folder_type() const;
  void _internal_set_folder_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // float visibility = 10;
  void clear_visibility();
  float visibility() const;
  void set_visibility(float value);
  private:
  float _internal_visibility() const;
  void _internal_set_visibility(float value);
  public:

  // int32 folder_order = 11;
  void clear_folder_order();
  ::PROTOBUF_NAMESPACE_ID::int32 folder_order() const;
  void set_folder_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_folder_order() const;
  void _internal_set_folder_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // bool is_new = 12;
  void clear_is_new();
  bool is_new() const;
  void set_is_new(bool value);
  private:
  bool _internal_is_new() const;
  void _internal_set_is_new(bool value);
  public:

  // bool is_folder = 13;
  void clear_is_folder();
  bool is_folder() const;
  void set_is_folder(bool value);
  private:
  bool _internal_is_folder() const;
  void _internal_set_is_folder(bool value);
  public:

  // int32 place_rule = 16;
  void clear_place_rule();
  ::PROTOBUF_NAMESPACE_ID::int32 place_rule() const;
  void set_place_rule(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_place_rule() const;
  void _internal_set_place_rule(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.FolderTableDataMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folder_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folder_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folder_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folder_code_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr thumbnail_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr parent_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr visibility_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr backend_directory_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr front_directory_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ref_type_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ref_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr folder_name_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::int32 folder_type_;
  float visibility_;
  ::PROTOBUF_NAMESPACE_ID::int32 folder_order_;
  bool is_new_;
  bool is_folder_;
  ::PROTOBUF_NAMESPACE_ID::int32 place_rule_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_CatalogFolderTableData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FolderTableDataMsg

// string id = 1;
inline void FolderTableDataMsg::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.id)
}
inline std::string* FolderTableDataMsg::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.id)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_id() const {
  return id_.Get();
}
inline void FolderTableDataMsg::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.id)
}

// string folder_id = 2;
inline void FolderTableDataMsg::clear_folder_id() {
  folder_id_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::folder_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_id)
  return _internal_folder_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_folder_id(ArgT0&& arg0, ArgT... args) {
 
 folder_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_id)
}
inline std::string* FolderTableDataMsg::mutable_folder_id() {
  std::string* _s = _internal_mutable_folder_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.folder_id)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_folder_id() const {
  return folder_id_.Get();
}
inline void FolderTableDataMsg::_internal_set_folder_id(const std::string& value) {
  
  folder_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_folder_id() {
  
  return folder_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_folder_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.folder_id)
  return folder_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_folder_id(std::string* folder_id) {
  if (folder_id != nullptr) {
    
  } else {
    
  }
  folder_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folder_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.folder_id)
}

// string folder_name = 3;
inline void FolderTableDataMsg::clear_folder_name() {
  folder_name_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::folder_name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_name)
  return _internal_folder_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_folder_name(ArgT0&& arg0, ArgT... args) {
 
 folder_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_name)
}
inline std::string* FolderTableDataMsg::mutable_folder_name() {
  std::string* _s = _internal_mutable_folder_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.folder_name)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_folder_name() const {
  return folder_name_.Get();
}
inline void FolderTableDataMsg::_internal_set_folder_name(const std::string& value) {
  
  folder_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_folder_name() {
  
  return folder_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_folder_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.folder_name)
  return folder_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_folder_name(std::string* folder_name) {
  if (folder_name != nullptr) {
    
  } else {
    
  }
  folder_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folder_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.folder_name)
}

// string folder_code = 4;
inline void FolderTableDataMsg::clear_folder_code() {
  folder_code_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::folder_code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_code)
  return _internal_folder_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_folder_code(ArgT0&& arg0, ArgT... args) {
 
 folder_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_code)
}
inline std::string* FolderTableDataMsg::mutable_folder_code() {
  std::string* _s = _internal_mutable_folder_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.folder_code)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_folder_code() const {
  return folder_code_.Get();
}
inline void FolderTableDataMsg::_internal_set_folder_code(const std::string& value) {
  
  folder_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_folder_code() {
  
  return folder_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_folder_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.folder_code)
  return folder_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_folder_code(std::string* folder_code) {
  if (folder_code != nullptr) {
    
  } else {
    
  }
  folder_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folder_code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.folder_code)
}

// string folder_code_exp = 5;
inline void FolderTableDataMsg::clear_folder_code_exp() {
  folder_code_exp_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::folder_code_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_code_exp)
  return _internal_folder_code_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_folder_code_exp(ArgT0&& arg0, ArgT... args) {
 
 folder_code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_code_exp)
}
inline std::string* FolderTableDataMsg::mutable_folder_code_exp() {
  std::string* _s = _internal_mutable_folder_code_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.folder_code_exp)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_folder_code_exp() const {
  return folder_code_exp_.Get();
}
inline void FolderTableDataMsg::_internal_set_folder_code_exp(const std::string& value) {
  
  folder_code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_folder_code_exp() {
  
  return folder_code_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_folder_code_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.folder_code_exp)
  return folder_code_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_folder_code_exp(std::string* folder_code_exp) {
  if (folder_code_exp != nullptr) {
    
  } else {
    
  }
  folder_code_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folder_code_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.folder_code_exp)
}

// int32 folder_type = 6;
inline void FolderTableDataMsg::clear_folder_type() {
  folder_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::_internal_folder_type() const {
  return folder_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::folder_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_type)
  return _internal_folder_type();
}
inline void FolderTableDataMsg::_internal_set_folder_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  folder_type_ = value;
}
inline void FolderTableDataMsg::set_folder_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_folder_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_type)
}

// string thumbnail_path = 7;
inline void FolderTableDataMsg::clear_thumbnail_path() {
  thumbnail_path_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::thumbnail_path() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.thumbnail_path)
  return _internal_thumbnail_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_thumbnail_path(ArgT0&& arg0, ArgT... args) {
 
 thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.thumbnail_path)
}
inline std::string* FolderTableDataMsg::mutable_thumbnail_path() {
  std::string* _s = _internal_mutable_thumbnail_path();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.thumbnail_path)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_thumbnail_path() const {
  return thumbnail_path_.Get();
}
inline void FolderTableDataMsg::_internal_set_thumbnail_path(const std::string& value) {
  
  thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_thumbnail_path() {
  
  return thumbnail_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_thumbnail_path() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.thumbnail_path)
  return thumbnail_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_thumbnail_path(std::string* thumbnail_path) {
  if (thumbnail_path != nullptr) {
    
  } else {
    
  }
  thumbnail_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), thumbnail_path,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.thumbnail_path)
}

// string parent_id = 8;
inline void FolderTableDataMsg::clear_parent_id() {
  parent_id_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::parent_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.parent_id)
  return _internal_parent_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_parent_id(ArgT0&& arg0, ArgT... args) {
 
 parent_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.parent_id)
}
inline std::string* FolderTableDataMsg::mutable_parent_id() {
  std::string* _s = _internal_mutable_parent_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.parent_id)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_parent_id() const {
  return parent_id_.Get();
}
inline void FolderTableDataMsg::_internal_set_parent_id(const std::string& value) {
  
  parent_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_parent_id() {
  
  return parent_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_parent_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.parent_id)
  return parent_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_parent_id(std::string* parent_id) {
  if (parent_id != nullptr) {
    
  } else {
    
  }
  parent_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), parent_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.parent_id)
}

// string visibility_exp = 9;
inline void FolderTableDataMsg::clear_visibility_exp() {
  visibility_exp_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::visibility_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.visibility_exp)
  return _internal_visibility_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_visibility_exp(ArgT0&& arg0, ArgT... args) {
 
 visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.visibility_exp)
}
inline std::string* FolderTableDataMsg::mutable_visibility_exp() {
  std::string* _s = _internal_mutable_visibility_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.visibility_exp)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_visibility_exp() const {
  return visibility_exp_.Get();
}
inline void FolderTableDataMsg::_internal_set_visibility_exp(const std::string& value) {
  
  visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_visibility_exp() {
  
  return visibility_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_visibility_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.visibility_exp)
  return visibility_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_visibility_exp(std::string* visibility_exp) {
  if (visibility_exp != nullptr) {
    
  } else {
    
  }
  visibility_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), visibility_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.visibility_exp)
}

// float visibility = 10;
inline void FolderTableDataMsg::clear_visibility() {
  visibility_ = 0;
}
inline float FolderTableDataMsg::_internal_visibility() const {
  return visibility_;
}
inline float FolderTableDataMsg::visibility() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.visibility)
  return _internal_visibility();
}
inline void FolderTableDataMsg::_internal_set_visibility(float value) {
  
  visibility_ = value;
}
inline void FolderTableDataMsg::set_visibility(float value) {
  _internal_set_visibility(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.visibility)
}

// int32 folder_order = 11;
inline void FolderTableDataMsg::clear_folder_order() {
  folder_order_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::_internal_folder_order() const {
  return folder_order_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::folder_order() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_order)
  return _internal_folder_order();
}
inline void FolderTableDataMsg::_internal_set_folder_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  folder_order_ = value;
}
inline void FolderTableDataMsg::set_folder_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_folder_order(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_order)
}

// bool is_new = 12;
inline void FolderTableDataMsg::clear_is_new() {
  is_new_ = false;
}
inline bool FolderTableDataMsg::_internal_is_new() const {
  return is_new_;
}
inline bool FolderTableDataMsg::is_new() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.is_new)
  return _internal_is_new();
}
inline void FolderTableDataMsg::_internal_set_is_new(bool value) {
  
  is_new_ = value;
}
inline void FolderTableDataMsg::set_is_new(bool value) {
  _internal_set_is_new(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.is_new)
}

// bool is_folder = 13;
inline void FolderTableDataMsg::clear_is_folder() {
  is_folder_ = false;
}
inline bool FolderTableDataMsg::_internal_is_folder() const {
  return is_folder_;
}
inline bool FolderTableDataMsg::is_folder() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.is_folder)
  return _internal_is_folder();
}
inline void FolderTableDataMsg::_internal_set_is_folder(bool value) {
  
  is_folder_ = value;
}
inline void FolderTableDataMsg::set_is_folder(bool value) {
  _internal_set_is_folder(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.is_folder)
}

// string backend_directory = 14;
inline void FolderTableDataMsg::clear_backend_directory() {
  backend_directory_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::backend_directory() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.backend_directory)
  return _internal_backend_directory();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_backend_directory(ArgT0&& arg0, ArgT... args) {
 
 backend_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.backend_directory)
}
inline std::string* FolderTableDataMsg::mutable_backend_directory() {
  std::string* _s = _internal_mutable_backend_directory();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.backend_directory)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_backend_directory() const {
  return backend_directory_.Get();
}
inline void FolderTableDataMsg::_internal_set_backend_directory(const std::string& value) {
  
  backend_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_backend_directory() {
  
  return backend_directory_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_backend_directory() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.backend_directory)
  return backend_directory_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_backend_directory(std::string* backend_directory) {
  if (backend_directory != nullptr) {
    
  } else {
    
  }
  backend_directory_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), backend_directory,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.backend_directory)
}

// string front_directory = 15;
inline void FolderTableDataMsg::clear_front_directory() {
  front_directory_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::front_directory() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.front_directory)
  return _internal_front_directory();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_front_directory(ArgT0&& arg0, ArgT... args) {
 
 front_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.front_directory)
}
inline std::string* FolderTableDataMsg::mutable_front_directory() {
  std::string* _s = _internal_mutable_front_directory();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.front_directory)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_front_directory() const {
  return front_directory_.Get();
}
inline void FolderTableDataMsg::_internal_set_front_directory(const std::string& value) {
  
  front_directory_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_front_directory() {
  
  return front_directory_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_front_directory() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.front_directory)
  return front_directory_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_front_directory(std::string* front_directory) {
  if (front_directory != nullptr) {
    
  } else {
    
  }
  front_directory_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), front_directory,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.front_directory)
}

// int32 place_rule = 16;
inline void FolderTableDataMsg::clear_place_rule() {
  place_rule_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::_internal_place_rule() const {
  return place_rule_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 FolderTableDataMsg::place_rule() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.place_rule)
  return _internal_place_rule();
}
inline void FolderTableDataMsg::_internal_set_place_rule(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  place_rule_ = value;
}
inline void FolderTableDataMsg::set_place_rule(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_place_rule(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.place_rule)
}

// string ref_type_code = 17;
inline void FolderTableDataMsg::clear_ref_type_code() {
  ref_type_code_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::ref_type_code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.ref_type_code)
  return _internal_ref_type_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_ref_type_code(ArgT0&& arg0, ArgT... args) {
 
 ref_type_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.ref_type_code)
}
inline std::string* FolderTableDataMsg::mutable_ref_type_code() {
  std::string* _s = _internal_mutable_ref_type_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.ref_type_code)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_ref_type_code() const {
  return ref_type_code_.Get();
}
inline void FolderTableDataMsg::_internal_set_ref_type_code(const std::string& value) {
  
  ref_type_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_ref_type_code() {
  
  return ref_type_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_ref_type_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.ref_type_code)
  return ref_type_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_ref_type_code(std::string* ref_type_code) {
  if (ref_type_code != nullptr) {
    
  } else {
    
  }
  ref_type_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ref_type_code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.ref_type_code)
}

// string ref_type = 18;
inline void FolderTableDataMsg::clear_ref_type() {
  ref_type_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::ref_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.ref_type)
  return _internal_ref_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_ref_type(ArgT0&& arg0, ArgT... args) {
 
 ref_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.ref_type)
}
inline std::string* FolderTableDataMsg::mutable_ref_type() {
  std::string* _s = _internal_mutable_ref_type();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.ref_type)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_ref_type() const {
  return ref_type_.Get();
}
inline void FolderTableDataMsg::_internal_set_ref_type(const std::string& value) {
  
  ref_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_ref_type() {
  
  return ref_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_ref_type() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.ref_type)
  return ref_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_ref_type(std::string* ref_type) {
  if (ref_type != nullptr) {
    
  } else {
    
  }
  ref_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ref_type,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.ref_type)
}

// string folder_name_exp = 19;
inline void FolderTableDataMsg::clear_folder_name_exp() {
  folder_name_exp_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::folder_name_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.folder_name_exp)
  return _internal_folder_name_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_folder_name_exp(ArgT0&& arg0, ArgT... args) {
 
 folder_name_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.folder_name_exp)
}
inline std::string* FolderTableDataMsg::mutable_folder_name_exp() {
  std::string* _s = _internal_mutable_folder_name_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.folder_name_exp)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_folder_name_exp() const {
  return folder_name_exp_.Get();
}
inline void FolderTableDataMsg::_internal_set_folder_name_exp(const std::string& value) {
  
  folder_name_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_folder_name_exp() {
  
  return folder_name_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_folder_name_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.folder_name_exp)
  return folder_name_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_folder_name_exp(std::string* folder_name_exp) {
  if (folder_name_exp != nullptr) {
    
  } else {
    
  }
  folder_name_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), folder_name_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.folder_name_exp)
}

// string description = 20;
inline void FolderTableDataMsg::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& FolderTableDataMsg::description() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderTableDataMsg.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FolderTableDataMsg::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.FolderTableDataMsg.description)
}
inline std::string* FolderTableDataMsg::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderTableDataMsg.description)
  return _s;
}
inline const std::string& FolderTableDataMsg::_internal_description() const {
  return description_.Get();
}
inline void FolderTableDataMsg::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FolderTableDataMsg::release_description() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderTableDataMsg.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FolderTableDataMsg::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderTableDataMsg.description)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_CatalogFolderTableData_2eproto
