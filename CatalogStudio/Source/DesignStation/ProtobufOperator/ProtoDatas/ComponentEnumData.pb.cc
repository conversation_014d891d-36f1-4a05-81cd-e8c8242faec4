// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ComponentEnumData.proto

#include "ComponentEnumData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ComponentEnumData::ComponentEnumData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ComponentEnumDataDefaultTypeInternal {
  constexpr ComponentEnumDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ComponentEnumDataDefaultTypeInternal() {}
  union {
    ComponentEnumData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ComponentEnumDataDefaultTypeInternal _ComponentEnumData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ComponentEnumData_2eproto[1];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_ComponentEnumData_2eproto[11];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ComponentEnumData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ComponentEnumData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentEnumData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ComponentEnumData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ComponentEnumData_default_instance_),
};

const char descriptor_table_protodef_ComponentEnumData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\027ComponentEnumData.proto\022\026catalog_studi"
  "o_message\"\023\n\021ComponentEnumData*P\n\024Polygo"
  "nVerticesOrder\022\021\n\rEUnknownOrder\020\000\022\025\n\021ECo"
  "unterClockWise\020\001\022\016\n\nEClockWise\020\002*P\n\022Plan"
  "PolygonBelongs\022\020\n\014EUnknownPlan\020\000\022\014\n\010EXY_"
  "Plan\020\001\022\014\n\010EYZ_Plan\020\002\022\014\n\010EXZ_Plan\020\003*A\n\013Ve"
  "rticeType\022\027\n\023EUnknownVerticeType\020\000\022\013\n\007EC"
  "onvex\020\001\022\014\n\010EConcave\020\002*,\n\014PositionType\022\r\n"
  "\tEAbsolute\020\000\022\r\n\tERelative\020\001*<\n\010LineType\022"
  "\020\n\014ELineSegment\020\000\022\016\n\nEHeightArc\020\001\022\016\n\nERa"
  "diusArc\020\002*G\n\013SectionType\022\017\n\013ECustomPlan\020"
  "\000\022\t\n\005ECube\020\001\022\014\n\010EEllipse\020\002\022\016\n\nERectangle"
  "\020\003*\217\001\n\017GeomtryItemType\022\021\n\rEGeomtryPoint\020"
  "\000\022\020\n\014EGeomtryLine\020\001\022\026\n\022EGeomtryCustomPla"
  "n\020\002\022\026\n\022EGeometryRectangle\020\003\022\024\n\020EGeometry"
  "Ellipse\020\004\022\021\n\rEGeometryCube\020\005*\213\001\n\024Section"
  "OperationType\022\020\n\014EDrawSection\020\000\022\021\n\rEShif"
  "tSection\020\001\022\020\n\014EZoomSection\020\002\022\022\n\016ECutoutS"
  "ection\020\003\022\023\n\017ELoftingSection\020\004\022\023\n\017ERotato"
  "rSection\020\005*#\n\nNormalType\022\n\n\006Outter\020\000\022\t\n\005"
  "Inner\020\001*A\n\025SingleComponentSource\022\013\n\007ECus"
  "tom\020\000\022\013\n\007EImport\020\001\022\016\n\nEImportPak\020\002*E\n\024Si"
  "ngleComponentState\022\013\n\007ECreate\020\000\022\020\n\014ENeed"
  "Confirm\020\001\022\016\n\nEConfirmed\020\002b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ComponentEnumData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ComponentEnumData_2eproto = {
  false, false, 953, descriptor_table_protodef_ComponentEnumData_2eproto, "ComponentEnumData.proto", 
  &descriptor_table_ComponentEnumData_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_ComponentEnumData_2eproto::offsets,
  file_level_metadata_ComponentEnumData_2eproto, file_level_enum_descriptors_ComponentEnumData_2eproto, file_level_service_descriptors_ComponentEnumData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ComponentEnumData_2eproto_getter() {
  return &descriptor_table_ComponentEnumData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ComponentEnumData_2eproto(&descriptor_table_ComponentEnumData_2eproto);
namespace catalog_studio_message {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PolygonVerticesOrder_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[0];
}
bool PolygonVerticesOrder_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlanPolygonBelongs_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[1];
}
bool PlanPolygonBelongs_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VerticeType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[2];
}
bool VerticeType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PositionType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[3];
}
bool PositionType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LineType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[4];
}
bool LineType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SectionType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[5];
}
bool SectionType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GeomtryItemType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[6];
}
bool GeomtryItemType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SectionOperationType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[7];
}
bool SectionOperationType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* NormalType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[8];
}
bool NormalType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SingleComponentSource_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[9];
}
bool SingleComponentSource_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SingleComponentState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ComponentEnumData_2eproto);
  return file_level_enum_descriptors_ComponentEnumData_2eproto[10];
}
bool SingleComponentState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ComponentEnumData::_Internal {
 public:
};

ComponentEnumData::ComponentEnumData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ComponentEnumData)
}
ComponentEnumData::ComponentEnumData(const ComponentEnumData& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ComponentEnumData)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ComponentEnumData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ComponentEnumData::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata ComponentEnumData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ComponentEnumData_2eproto_getter, &descriptor_table_ComponentEnumData_2eproto_once,
      file_level_metadata_ComponentEnumData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ComponentEnumData* Arena::CreateMaybeMessage< ::catalog_studio_message::ComponentEnumData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ComponentEnumData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
