// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ComponentEnumData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ComponentEnumData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ComponentEnumData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ComponentEnumData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ComponentEnumData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ComponentEnumData_2eproto;
namespace catalog_studio_message {
class ComponentEnumData;
struct ComponentEnumDataDefaultTypeInternal;
extern ComponentEnumDataDefaultTypeInternal _ComponentEnumData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::ComponentEnumData* Arena::CreateMaybeMessage<::catalog_studio_message::ComponentEnumData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

enum PolygonVerticesOrder : int {
  EUnknownOrder = 0,
  ECounterClockWise = 1,
  EClockWise = 2,
  PolygonVerticesOrder_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PolygonVerticesOrder_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PolygonVerticesOrder_IsValid(int value);
constexpr PolygonVerticesOrder PolygonVerticesOrder_MIN = EUnknownOrder;
constexpr PolygonVerticesOrder PolygonVerticesOrder_MAX = EClockWise;
constexpr int PolygonVerticesOrder_ARRAYSIZE = PolygonVerticesOrder_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PolygonVerticesOrder_descriptor();
template<typename T>
inline const std::string& PolygonVerticesOrder_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PolygonVerticesOrder>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PolygonVerticesOrder_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PolygonVerticesOrder_descriptor(), enum_t_value);
}
inline bool PolygonVerticesOrder_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PolygonVerticesOrder* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PolygonVerticesOrder>(
    PolygonVerticesOrder_descriptor(), name, value);
}
enum PlanPolygonBelongs : int {
  EUnknownPlan = 0,
  EXY_Plan = 1,
  EYZ_Plan = 2,
  EXZ_Plan = 3,
  PlanPolygonBelongs_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PlanPolygonBelongs_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PlanPolygonBelongs_IsValid(int value);
constexpr PlanPolygonBelongs PlanPolygonBelongs_MIN = EUnknownPlan;
constexpr PlanPolygonBelongs PlanPolygonBelongs_MAX = EXZ_Plan;
constexpr int PlanPolygonBelongs_ARRAYSIZE = PlanPolygonBelongs_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlanPolygonBelongs_descriptor();
template<typename T>
inline const std::string& PlanPolygonBelongs_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PlanPolygonBelongs>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PlanPolygonBelongs_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PlanPolygonBelongs_descriptor(), enum_t_value);
}
inline bool PlanPolygonBelongs_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PlanPolygonBelongs* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PlanPolygonBelongs>(
    PlanPolygonBelongs_descriptor(), name, value);
}
enum VerticeType : int {
  EUnknownVerticeType = 0,
  EConvex = 1,
  EConcave = 2,
  VerticeType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  VerticeType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool VerticeType_IsValid(int value);
constexpr VerticeType VerticeType_MIN = EUnknownVerticeType;
constexpr VerticeType VerticeType_MAX = EConcave;
constexpr int VerticeType_ARRAYSIZE = VerticeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VerticeType_descriptor();
template<typename T>
inline const std::string& VerticeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VerticeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VerticeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VerticeType_descriptor(), enum_t_value);
}
inline bool VerticeType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VerticeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VerticeType>(
    VerticeType_descriptor(), name, value);
}
enum PositionType : int {
  EAbsolute = 0,
  ERelative = 1,
  PositionType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  PositionType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool PositionType_IsValid(int value);
constexpr PositionType PositionType_MIN = EAbsolute;
constexpr PositionType PositionType_MAX = ERelative;
constexpr int PositionType_ARRAYSIZE = PositionType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PositionType_descriptor();
template<typename T>
inline const std::string& PositionType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PositionType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PositionType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PositionType_descriptor(), enum_t_value);
}
inline bool PositionType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PositionType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PositionType>(
    PositionType_descriptor(), name, value);
}
enum LineType : int {
  ELineSegment = 0,
  EHeightArc = 1,
  ERadiusArc = 2,
  LineType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  LineType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool LineType_IsValid(int value);
constexpr LineType LineType_MIN = ELineSegment;
constexpr LineType LineType_MAX = ERadiusArc;
constexpr int LineType_ARRAYSIZE = LineType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LineType_descriptor();
template<typename T>
inline const std::string& LineType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LineType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LineType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LineType_descriptor(), enum_t_value);
}
inline bool LineType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LineType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LineType>(
    LineType_descriptor(), name, value);
}
enum SectionType : int {
  ECustomPlan = 0,
  ECube = 1,
  EEllipse = 2,
  ERectangle = 3,
  SectionType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SectionType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SectionType_IsValid(int value);
constexpr SectionType SectionType_MIN = ECustomPlan;
constexpr SectionType SectionType_MAX = ERectangle;
constexpr int SectionType_ARRAYSIZE = SectionType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SectionType_descriptor();
template<typename T>
inline const std::string& SectionType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SectionType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SectionType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SectionType_descriptor(), enum_t_value);
}
inline bool SectionType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SectionType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SectionType>(
    SectionType_descriptor(), name, value);
}
enum GeomtryItemType : int {
  EGeomtryPoint = 0,
  EGeomtryLine = 1,
  EGeomtryCustomPlan = 2,
  EGeometryRectangle = 3,
  EGeometryEllipse = 4,
  EGeometryCube = 5,
  GeomtryItemType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  GeomtryItemType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool GeomtryItemType_IsValid(int value);
constexpr GeomtryItemType GeomtryItemType_MIN = EGeomtryPoint;
constexpr GeomtryItemType GeomtryItemType_MAX = EGeometryCube;
constexpr int GeomtryItemType_ARRAYSIZE = GeomtryItemType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GeomtryItemType_descriptor();
template<typename T>
inline const std::string& GeomtryItemType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GeomtryItemType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GeomtryItemType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GeomtryItemType_descriptor(), enum_t_value);
}
inline bool GeomtryItemType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GeomtryItemType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GeomtryItemType>(
    GeomtryItemType_descriptor(), name, value);
}
enum SectionOperationType : int {
  EDrawSection = 0,
  EShiftSection = 1,
  EZoomSection = 2,
  ECutoutSection = 3,
  ELoftingSection = 4,
  ERotatorSection = 5,
  SectionOperationType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SectionOperationType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SectionOperationType_IsValid(int value);
constexpr SectionOperationType SectionOperationType_MIN = EDrawSection;
constexpr SectionOperationType SectionOperationType_MAX = ERotatorSection;
constexpr int SectionOperationType_ARRAYSIZE = SectionOperationType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SectionOperationType_descriptor();
template<typename T>
inline const std::string& SectionOperationType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SectionOperationType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SectionOperationType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SectionOperationType_descriptor(), enum_t_value);
}
inline bool SectionOperationType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SectionOperationType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SectionOperationType>(
    SectionOperationType_descriptor(), name, value);
}
enum NormalType : int {
  Outter = 0,
  Inner = 1,
  NormalType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  NormalType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool NormalType_IsValid(int value);
constexpr NormalType NormalType_MIN = Outter;
constexpr NormalType NormalType_MAX = Inner;
constexpr int NormalType_ARRAYSIZE = NormalType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* NormalType_descriptor();
template<typename T>
inline const std::string& NormalType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, NormalType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function NormalType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    NormalType_descriptor(), enum_t_value);
}
inline bool NormalType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, NormalType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<NormalType>(
    NormalType_descriptor(), name, value);
}
enum SingleComponentSource : int {
  ECustom = 0,
  EImport = 1,
  EImportPak = 2,
  SingleComponentSource_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SingleComponentSource_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SingleComponentSource_IsValid(int value);
constexpr SingleComponentSource SingleComponentSource_MIN = ECustom;
constexpr SingleComponentSource SingleComponentSource_MAX = EImportPak;
constexpr int SingleComponentSource_ARRAYSIZE = SingleComponentSource_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SingleComponentSource_descriptor();
template<typename T>
inline const std::string& SingleComponentSource_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SingleComponentSource>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SingleComponentSource_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SingleComponentSource_descriptor(), enum_t_value);
}
inline bool SingleComponentSource_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SingleComponentSource* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SingleComponentSource>(
    SingleComponentSource_descriptor(), name, value);
}
enum SingleComponentState : int {
  ECreate = 0,
  ENeedConfirm = 1,
  EConfirmed = 2,
  SingleComponentState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  SingleComponentState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool SingleComponentState_IsValid(int value);
constexpr SingleComponentState SingleComponentState_MIN = ECreate;
constexpr SingleComponentState SingleComponentState_MAX = EConfirmed;
constexpr int SingleComponentState_ARRAYSIZE = SingleComponentState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SingleComponentState_descriptor();
template<typename T>
inline const std::string& SingleComponentState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SingleComponentState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SingleComponentState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SingleComponentState_descriptor(), enum_t_value);
}
inline bool SingleComponentState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SingleComponentState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SingleComponentState>(
    SingleComponentState_descriptor(), name, value);
}
// ===================================================================

class ComponentEnumData final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:catalog_studio_message.ComponentEnumData) */ {
 public:
  inline ComponentEnumData() : ComponentEnumData(nullptr) {}
  explicit constexpr ComponentEnumData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ComponentEnumData(const ComponentEnumData& from);
  ComponentEnumData(ComponentEnumData&& from) noexcept
    : ComponentEnumData() {
    *this = ::std::move(from);
  }

  inline ComponentEnumData& operator=(const ComponentEnumData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComponentEnumData& operator=(ComponentEnumData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ComponentEnumData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ComponentEnumData* internal_default_instance() {
    return reinterpret_cast<const ComponentEnumData*>(
               &_ComponentEnumData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ComponentEnumData& a, ComponentEnumData& b) {
    a.Swap(&b);
  }
  inline void Swap(ComponentEnumData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ComponentEnumData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComponentEnumData* New() const final {
    return new ComponentEnumData();
  }

  ComponentEnumData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComponentEnumData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ComponentEnumData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ComponentEnumData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.ComponentEnumData";
  }
  protected:
  explicit ComponentEnumData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:catalog_studio_message.ComponentEnumData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ComponentEnumData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ComponentEnumData

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::catalog_studio_message::PolygonVerticesOrder> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::PolygonVerticesOrder>() {
  return ::catalog_studio_message::PolygonVerticesOrder_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::PlanPolygonBelongs> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::PlanPolygonBelongs>() {
  return ::catalog_studio_message::PlanPolygonBelongs_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::VerticeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::VerticeType>() {
  return ::catalog_studio_message::VerticeType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::PositionType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::PositionType>() {
  return ::catalog_studio_message::PositionType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::LineType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::LineType>() {
  return ::catalog_studio_message::LineType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::SectionType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::SectionType>() {
  return ::catalog_studio_message::SectionType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::GeomtryItemType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::GeomtryItemType>() {
  return ::catalog_studio_message::GeomtryItemType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::SectionOperationType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::SectionOperationType>() {
  return ::catalog_studio_message::SectionOperationType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::NormalType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::NormalType>() {
  return ::catalog_studio_message::NormalType_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::SingleComponentSource> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::SingleComponentSource>() {
  return ::catalog_studio_message::SingleComponentSource_descriptor();
}
template <> struct is_proto_enum< ::catalog_studio_message::SingleComponentState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::catalog_studio_message::SingleComponentState>() {
  return ::catalog_studio_message::SingleComponentState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ComponentEnumData_2eproto
