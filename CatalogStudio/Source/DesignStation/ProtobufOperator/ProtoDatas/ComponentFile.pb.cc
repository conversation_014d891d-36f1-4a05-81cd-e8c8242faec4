// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ComponentFile.proto

#include "ComponentFile.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ComponentFileData::ComponentFileData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : component_datas_()
  , file_data_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , depend_files_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , file_ref_uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ComponentFileDataDefaultTypeInternal {
  constexpr ComponentFileDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ComponentFileDataDefaultTypeInternal() {}
  union {
    ComponentFileData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ComponentFileDataDefaultTypeInternal _ComponentFileData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ComponentFile_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_ComponentFile_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ComponentFile_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ComponentFile_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentFileData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentFileData, file_data_path_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentFileData, depend_files_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentFileData, component_datas_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ComponentFileData, file_ref_uuid_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ComponentFileData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ComponentFileData_default_instance_),
};

const char descriptor_table_protodef_ComponentFile_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023ComponentFile.proto\022\026catalog_studio_me"
  "ssage\032\031SingleComponentItem.proto\"\236\001\n\021Com"
  "ponentFileData\022\026\n\016file_data_path\030\001 \001(\t\022\024"
  "\n\014depend_files\030\002 \001(\t\022D\n\017component_datas\030"
  "\003 \003(\0132+.catalog_studio_message.SingleCom"
  "ponentItem\022\025\n\rfile_ref_uuid\030\004 \001(\tb\006proto"
  "3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_ComponentFile_2eproto_deps[1] = {
  &::descriptor_table_SingleComponentItem_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ComponentFile_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ComponentFile_2eproto = {
  false, false, 241, descriptor_table_protodef_ComponentFile_2eproto, "ComponentFile.proto", 
  &descriptor_table_ComponentFile_2eproto_once, descriptor_table_ComponentFile_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_ComponentFile_2eproto::offsets,
  file_level_metadata_ComponentFile_2eproto, file_level_enum_descriptors_ComponentFile_2eproto, file_level_service_descriptors_ComponentFile_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ComponentFile_2eproto_getter() {
  return &descriptor_table_ComponentFile_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ComponentFile_2eproto(&descriptor_table_ComponentFile_2eproto);
namespace catalog_studio_message {

// ===================================================================

class ComponentFileData::_Internal {
 public:
};

void ComponentFileData::clear_component_datas() {
  component_datas_.Clear();
}
ComponentFileData::ComponentFileData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  component_datas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ComponentFileData)
}
ComponentFileData::ComponentFileData(const ComponentFileData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      component_datas_(from.component_datas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  file_data_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_file_data_path().empty()) {
    file_data_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_file_data_path(), 
      GetArenaForAllocation());
  }
  depend_files_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_depend_files().empty()) {
    depend_files_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_depend_files(), 
      GetArenaForAllocation());
  }
  file_ref_uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_file_ref_uuid().empty()) {
    file_ref_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_file_ref_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ComponentFileData)
}

void ComponentFileData::SharedCtor() {
file_data_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
depend_files_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
file_ref_uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ComponentFileData::~ComponentFileData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.ComponentFileData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ComponentFileData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  file_data_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  depend_files_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  file_ref_uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ComponentFileData::ArenaDtor(void* object) {
  ComponentFileData* _this = reinterpret_cast< ComponentFileData* >(object);
  (void)_this;
}
void ComponentFileData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ComponentFileData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ComponentFileData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.ComponentFileData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  component_datas_.Clear();
  file_data_path_.ClearToEmpty();
  depend_files_.ClearToEmpty();
  file_ref_uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ComponentFileData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string file_data_path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_file_data_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ComponentFileData.file_data_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string depend_files = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_depend_files();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ComponentFileData.depend_files"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.SingleComponentItem component_datas = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_component_datas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string file_ref_uuid = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_file_ref_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ComponentFileData.file_ref_uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ComponentFileData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.ComponentFileData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string file_data_path = 1;
  if (!this->_internal_file_data_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_file_data_path().data(), static_cast<int>(this->_internal_file_data_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ComponentFileData.file_data_path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_file_data_path(), target);
  }

  // string depend_files = 2;
  if (!this->_internal_depend_files().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_depend_files().data(), static_cast<int>(this->_internal_depend_files().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ComponentFileData.depend_files");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_depend_files(), target);
  }

  // repeated .catalog_studio_message.SingleComponentItem component_datas = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_component_datas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_component_datas(i), target, stream);
  }

  // string file_ref_uuid = 4;
  if (!this->_internal_file_ref_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_file_ref_uuid().data(), static_cast<int>(this->_internal_file_ref_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ComponentFileData.file_ref_uuid");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_file_ref_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.ComponentFileData)
  return target;
}

size_t ComponentFileData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.ComponentFileData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.SingleComponentItem component_datas = 3;
  total_size += 1UL * this->_internal_component_datas_size();
  for (const auto& msg : this->component_datas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string file_data_path = 1;
  if (!this->_internal_file_data_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_file_data_path());
  }

  // string depend_files = 2;
  if (!this->_internal_depend_files().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_depend_files());
  }

  // string file_ref_uuid = 4;
  if (!this->_internal_file_ref_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_file_ref_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ComponentFileData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ComponentFileData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ComponentFileData::GetClassData() const { return &_class_data_; }

void ComponentFileData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ComponentFileData *>(to)->MergeFrom(
      static_cast<const ComponentFileData &>(from));
}


void ComponentFileData::MergeFrom(const ComponentFileData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.ComponentFileData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  component_datas_.MergeFrom(from.component_datas_);
  if (!from._internal_file_data_path().empty()) {
    _internal_set_file_data_path(from._internal_file_data_path());
  }
  if (!from._internal_depend_files().empty()) {
    _internal_set_depend_files(from._internal_depend_files());
  }
  if (!from._internal_file_ref_uuid().empty()) {
    _internal_set_file_ref_uuid(from._internal_file_ref_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ComponentFileData::CopyFrom(const ComponentFileData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.ComponentFileData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ComponentFileData::IsInitialized() const {
  return true;
}

void ComponentFileData::InternalSwap(ComponentFileData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  component_datas_.InternalSwap(&other->component_datas_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &file_data_path_, lhs_arena,
      &other->file_data_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &depend_files_, lhs_arena,
      &other->depend_files_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &file_ref_uuid_, lhs_arena,
      &other->file_ref_uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ComponentFileData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ComponentFile_2eproto_getter, &descriptor_table_ComponentFile_2eproto_once,
      file_level_metadata_ComponentFile_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ComponentFileData* Arena::CreateMaybeMessage< ::catalog_studio_message::ComponentFileData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ComponentFileData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
