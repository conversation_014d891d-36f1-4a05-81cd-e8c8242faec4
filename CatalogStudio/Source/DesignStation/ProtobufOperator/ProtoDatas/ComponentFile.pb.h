// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ComponentFile.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ComponentFile_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ComponentFile_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "SingleComponentItem.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ComponentFile_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ComponentFile_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ComponentFile_2eproto;
namespace catalog_studio_message {
class ComponentFileData;
struct ComponentFileDataDefaultTypeInternal;
extern ComponentFileDataDefaultTypeInternal _ComponentFileData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::ComponentFileData* Arena::CreateMaybeMessage<::catalog_studio_message::ComponentFileData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class ComponentFileData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.ComponentFileData) */ {
 public:
  inline ComponentFileData() : ComponentFileData(nullptr) {}
  ~ComponentFileData() override;
  explicit constexpr ComponentFileData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ComponentFileData(const ComponentFileData& from);
  ComponentFileData(ComponentFileData&& from) noexcept
    : ComponentFileData() {
    *this = ::std::move(from);
  }

  inline ComponentFileData& operator=(const ComponentFileData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComponentFileData& operator=(ComponentFileData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ComponentFileData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ComponentFileData* internal_default_instance() {
    return reinterpret_cast<const ComponentFileData*>(
               &_ComponentFileData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ComponentFileData& a, ComponentFileData& b) {
    a.Swap(&b);
  }
  inline void Swap(ComponentFileData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ComponentFileData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComponentFileData* New() const final {
    return new ComponentFileData();
  }

  ComponentFileData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComponentFileData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ComponentFileData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ComponentFileData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComponentFileData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.ComponentFileData";
  }
  protected:
  explicit ComponentFileData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentDatasFieldNumber = 3,
    kFileDataPathFieldNumber = 1,
    kDependFilesFieldNumber = 2,
    kFileRefUuidFieldNumber = 4,
  };
  // repeated .catalog_studio_message.SingleComponentItem component_datas = 3;
  int component_datas_size() const;
  private:
  int _internal_component_datas_size() const;
  public:
  void clear_component_datas();
  ::catalog_studio_message::SingleComponentItem* mutable_component_datas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >*
      mutable_component_datas();
  private:
  const ::catalog_studio_message::SingleComponentItem& _internal_component_datas(int index) const;
  ::catalog_studio_message::SingleComponentItem* _internal_add_component_datas();
  public:
  const ::catalog_studio_message::SingleComponentItem& component_datas(int index) const;
  ::catalog_studio_message::SingleComponentItem* add_component_datas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >&
      component_datas() const;

  // string file_data_path = 1;
  void clear_file_data_path();
  const std::string& file_data_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_data_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_data_path();
  PROTOBUF_MUST_USE_RESULT std::string* release_file_data_path();
  void set_allocated_file_data_path(std::string* file_data_path);
  private:
  const std::string& _internal_file_data_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_data_path(const std::string& value);
  std::string* _internal_mutable_file_data_path();
  public:

  // string depend_files = 2;
  void clear_depend_files();
  const std::string& depend_files() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_depend_files(ArgT0&& arg0, ArgT... args);
  std::string* mutable_depend_files();
  PROTOBUF_MUST_USE_RESULT std::string* release_depend_files();
  void set_allocated_depend_files(std::string* depend_files);
  private:
  const std::string& _internal_depend_files() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_depend_files(const std::string& value);
  std::string* _internal_mutable_depend_files();
  public:

  // string file_ref_uuid = 4;
  void clear_file_ref_uuid();
  const std::string& file_ref_uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_ref_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_ref_uuid();
  PROTOBUF_MUST_USE_RESULT std::string* release_file_ref_uuid();
  void set_allocated_file_ref_uuid(std::string* file_ref_uuid);
  private:
  const std::string& _internal_file_ref_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_ref_uuid(const std::string& value);
  std::string* _internal_mutable_file_ref_uuid();
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.ComponentFileData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem > component_datas_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_data_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr depend_files_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_ref_uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ComponentFile_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ComponentFileData

// string file_data_path = 1;
inline void ComponentFileData::clear_file_data_path() {
  file_data_path_.ClearToEmpty();
}
inline const std::string& ComponentFileData::file_data_path() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ComponentFileData.file_data_path)
  return _internal_file_data_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ComponentFileData::set_file_data_path(ArgT0&& arg0, ArgT... args) {
 
 file_data_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.ComponentFileData.file_data_path)
}
inline std::string* ComponentFileData::mutable_file_data_path() {
  std::string* _s = _internal_mutable_file_data_path();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ComponentFileData.file_data_path)
  return _s;
}
inline const std::string& ComponentFileData::_internal_file_data_path() const {
  return file_data_path_.Get();
}
inline void ComponentFileData::_internal_set_file_data_path(const std::string& value) {
  
  file_data_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ComponentFileData::_internal_mutable_file_data_path() {
  
  return file_data_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ComponentFileData::release_file_data_path() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ComponentFileData.file_data_path)
  return file_data_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ComponentFileData::set_allocated_file_data_path(std::string* file_data_path) {
  if (file_data_path != nullptr) {
    
  } else {
    
  }
  file_data_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_data_path,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ComponentFileData.file_data_path)
}

// string depend_files = 2;
inline void ComponentFileData::clear_depend_files() {
  depend_files_.ClearToEmpty();
}
inline const std::string& ComponentFileData::depend_files() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ComponentFileData.depend_files)
  return _internal_depend_files();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ComponentFileData::set_depend_files(ArgT0&& arg0, ArgT... args) {
 
 depend_files_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.ComponentFileData.depend_files)
}
inline std::string* ComponentFileData::mutable_depend_files() {
  std::string* _s = _internal_mutable_depend_files();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ComponentFileData.depend_files)
  return _s;
}
inline const std::string& ComponentFileData::_internal_depend_files() const {
  return depend_files_.Get();
}
inline void ComponentFileData::_internal_set_depend_files(const std::string& value) {
  
  depend_files_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ComponentFileData::_internal_mutable_depend_files() {
  
  return depend_files_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ComponentFileData::release_depend_files() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ComponentFileData.depend_files)
  return depend_files_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ComponentFileData::set_allocated_depend_files(std::string* depend_files) {
  if (depend_files != nullptr) {
    
  } else {
    
  }
  depend_files_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), depend_files,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ComponentFileData.depend_files)
}

// repeated .catalog_studio_message.SingleComponentItem component_datas = 3;
inline int ComponentFileData::_internal_component_datas_size() const {
  return component_datas_.size();
}
inline int ComponentFileData::component_datas_size() const {
  return _internal_component_datas_size();
}
inline ::catalog_studio_message::SingleComponentItem* ComponentFileData::mutable_component_datas(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ComponentFileData.component_datas)
  return component_datas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >*
ComponentFileData::mutable_component_datas() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.ComponentFileData.component_datas)
  return &component_datas_;
}
inline const ::catalog_studio_message::SingleComponentItem& ComponentFileData::_internal_component_datas(int index) const {
  return component_datas_.Get(index);
}
inline const ::catalog_studio_message::SingleComponentItem& ComponentFileData::component_datas(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ComponentFileData.component_datas)
  return _internal_component_datas(index);
}
inline ::catalog_studio_message::SingleComponentItem* ComponentFileData::_internal_add_component_datas() {
  return component_datas_.Add();
}
inline ::catalog_studio_message::SingleComponentItem* ComponentFileData::add_component_datas() {
  ::catalog_studio_message::SingleComponentItem* _add = _internal_add_component_datas();
  // @@protoc_insertion_point(field_add:catalog_studio_message.ComponentFileData.component_datas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >&
ComponentFileData::component_datas() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.ComponentFileData.component_datas)
  return component_datas_;
}

// string file_ref_uuid = 4;
inline void ComponentFileData::clear_file_ref_uuid() {
  file_ref_uuid_.ClearToEmpty();
}
inline const std::string& ComponentFileData::file_ref_uuid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ComponentFileData.file_ref_uuid)
  return _internal_file_ref_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ComponentFileData::set_file_ref_uuid(ArgT0&& arg0, ArgT... args) {
 
 file_ref_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.ComponentFileData.file_ref_uuid)
}
inline std::string* ComponentFileData::mutable_file_ref_uuid() {
  std::string* _s = _internal_mutable_file_ref_uuid();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ComponentFileData.file_ref_uuid)
  return _s;
}
inline const std::string& ComponentFileData::_internal_file_ref_uuid() const {
  return file_ref_uuid_.Get();
}
inline void ComponentFileData::_internal_set_file_ref_uuid(const std::string& value) {
  
  file_ref_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ComponentFileData::_internal_mutable_file_ref_uuid() {
  
  return file_ref_uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ComponentFileData::release_file_ref_uuid() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ComponentFileData.file_ref_uuid)
  return file_ref_uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ComponentFileData::set_allocated_file_ref_uuid(std::string* file_ref_uuid) {
  if (file_ref_uuid != nullptr) {
    
  } else {
    
  }
  file_ref_uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), file_ref_uuid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ComponentFileData.file_ref_uuid)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ComponentFile_2eproto
