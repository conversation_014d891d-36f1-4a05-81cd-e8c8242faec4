// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomMeshInfo.proto

#include "CustomMeshInfo.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr CustomMeshInfo::CustomMeshInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : vertex_locations_()
  , wedge_indices_()
  , _wedge_indices_cached_byte_size_(0)
  , vertex_uv_()
  , normals_(){}
struct CustomMeshInfoDefaultTypeInternal {
  constexpr CustomMeshInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CustomMeshInfoDefaultTypeInternal() {}
  union {
    CustomMeshInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CustomMeshInfoDefaultTypeInternal _CustomMeshInfo_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_CustomMeshInfo_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_CustomMeshInfo_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_CustomMeshInfo_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_CustomMeshInfo_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CustomMeshInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CustomMeshInfo, vertex_locations_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CustomMeshInfo, wedge_indices_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CustomMeshInfo, vertex_uv_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::CustomMeshInfo, normals_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::CustomMeshInfo)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_CustomMeshInfo_default_instance_),
};

const char descriptor_table_protodef_CustomMeshInfo_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\024CustomMeshInfo.proto\022\026catalog_studio_m"
  "essage\032\014Vector.proto\032\016Vector2D.proto\"\307\001\n"
  "\016CustomMeshInfo\0228\n\020vertex_locations\030\001 \003("
  "\0132\036.catalog_studio_message.Vector\022\025\n\rwed"
  "ge_indices\030\002 \003(\005\0223\n\tvertex_uv\030\003 \003(\0132 .ca"
  "talog_studio_message.Vector2D\022/\n\007normals"
  "\030\004 \003(\0132\036.catalog_studio_message.Vectorb\006"
  "proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_CustomMeshInfo_2eproto_deps[2] = {
  &::descriptor_table_Vector_2eproto,
  &::descriptor_table_Vector2D_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_CustomMeshInfo_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CustomMeshInfo_2eproto = {
  false, false, 286, descriptor_table_protodef_CustomMeshInfo_2eproto, "CustomMeshInfo.proto", 
  &descriptor_table_CustomMeshInfo_2eproto_once, descriptor_table_CustomMeshInfo_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_CustomMeshInfo_2eproto::offsets,
  file_level_metadata_CustomMeshInfo_2eproto, file_level_enum_descriptors_CustomMeshInfo_2eproto, file_level_service_descriptors_CustomMeshInfo_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_CustomMeshInfo_2eproto_getter() {
  return &descriptor_table_CustomMeshInfo_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_CustomMeshInfo_2eproto(&descriptor_table_CustomMeshInfo_2eproto);
namespace catalog_studio_message {

// ===================================================================

class CustomMeshInfo::_Internal {
 public:
};

void CustomMeshInfo::clear_vertex_locations() {
  vertex_locations_.Clear();
}
void CustomMeshInfo::clear_vertex_uv() {
  vertex_uv_.Clear();
}
void CustomMeshInfo::clear_normals() {
  normals_.Clear();
}
CustomMeshInfo::CustomMeshInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  vertex_locations_(arena),
  wedge_indices_(arena),
  vertex_uv_(arena),
  normals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.CustomMeshInfo)
}
CustomMeshInfo::CustomMeshInfo(const CustomMeshInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      vertex_locations_(from.vertex_locations_),
      wedge_indices_(from.wedge_indices_),
      vertex_uv_(from.vertex_uv_),
      normals_(from.normals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.CustomMeshInfo)
}

void CustomMeshInfo::SharedCtor() {
}

CustomMeshInfo::~CustomMeshInfo() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.CustomMeshInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CustomMeshInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CustomMeshInfo::ArenaDtor(void* object) {
  CustomMeshInfo* _this = reinterpret_cast< CustomMeshInfo* >(object);
  (void)_this;
}
void CustomMeshInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CustomMeshInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CustomMeshInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.CustomMeshInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  vertex_locations_.Clear();
  wedge_indices_.Clear();
  vertex_uv_.Clear();
  normals_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CustomMeshInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.Vector vertex_locations = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_vertex_locations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated int32 wedge_indices = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_wedge_indices(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16) {
          _internal_add_wedge_indices(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.Vector2D vertex_uv = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_vertex_uv(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.Vector normals = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_normals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CustomMeshInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.CustomMeshInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.Vector vertex_locations = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_vertex_locations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_vertex_locations(i), target, stream);
  }

  // repeated int32 wedge_indices = 2;
  {
    int byte_size = _wedge_indices_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          2, _internal_wedge_indices(), byte_size, target);
    }
  }

  // repeated .catalog_studio_message.Vector2D vertex_uv = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_vertex_uv_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_vertex_uv(i), target, stream);
  }

  // repeated .catalog_studio_message.Vector normals = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_normals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_normals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.CustomMeshInfo)
  return target;
}

size_t CustomMeshInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.CustomMeshInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.Vector vertex_locations = 1;
  total_size += 1UL * this->_internal_vertex_locations_size();
  for (const auto& msg : this->vertex_locations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 wedge_indices = 2;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->wedge_indices_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _wedge_indices_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .catalog_studio_message.Vector2D vertex_uv = 3;
  total_size += 1UL * this->_internal_vertex_uv_size();
  for (const auto& msg : this->vertex_uv_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.Vector normals = 4;
  total_size += 1UL * this->_internal_normals_size();
  for (const auto& msg : this->normals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CustomMeshInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CustomMeshInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CustomMeshInfo::GetClassData() const { return &_class_data_; }

void CustomMeshInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CustomMeshInfo *>(to)->MergeFrom(
      static_cast<const CustomMeshInfo &>(from));
}


void CustomMeshInfo::MergeFrom(const CustomMeshInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.CustomMeshInfo)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  vertex_locations_.MergeFrom(from.vertex_locations_);
  wedge_indices_.MergeFrom(from.wedge_indices_);
  vertex_uv_.MergeFrom(from.vertex_uv_);
  normals_.MergeFrom(from.normals_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CustomMeshInfo::CopyFrom(const CustomMeshInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.CustomMeshInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CustomMeshInfo::IsInitialized() const {
  return true;
}

void CustomMeshInfo::InternalSwap(CustomMeshInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  vertex_locations_.InternalSwap(&other->vertex_locations_);
  wedge_indices_.InternalSwap(&other->wedge_indices_);
  vertex_uv_.InternalSwap(&other->vertex_uv_);
  normals_.InternalSwap(&other->normals_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CustomMeshInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_CustomMeshInfo_2eproto_getter, &descriptor_table_CustomMeshInfo_2eproto_once,
      file_level_metadata_CustomMeshInfo_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::CustomMeshInfo* Arena::CreateMaybeMessage< ::catalog_studio_message::CustomMeshInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::CustomMeshInfo >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
