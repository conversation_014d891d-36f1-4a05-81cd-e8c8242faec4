// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomMeshInfo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_CustomMeshInfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_CustomMeshInfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "Vector.pb.h"
#include "Vector2D.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_CustomMeshInfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_CustomMeshInfo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_CustomMeshInfo_2eproto;
namespace catalog_studio_message {
class CustomMeshInfo;
struct CustomMeshInfoDefaultTypeInternal;
extern CustomMeshInfoDefaultTypeInternal _CustomMeshInfo_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::CustomMeshInfo* Arena::CreateMaybeMessage<::catalog_studio_message::CustomMeshInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class CustomMeshInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.CustomMeshInfo) */ {
 public:
  inline CustomMeshInfo() : CustomMeshInfo(nullptr) {}
  ~CustomMeshInfo() override;
  explicit constexpr CustomMeshInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CustomMeshInfo(const CustomMeshInfo& from);
  CustomMeshInfo(CustomMeshInfo&& from) noexcept
    : CustomMeshInfo() {
    *this = ::std::move(from);
  }

  inline CustomMeshInfo& operator=(const CustomMeshInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CustomMeshInfo& operator=(CustomMeshInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CustomMeshInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CustomMeshInfo* internal_default_instance() {
    return reinterpret_cast<const CustomMeshInfo*>(
               &_CustomMeshInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CustomMeshInfo& a, CustomMeshInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CustomMeshInfo* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CustomMeshInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CustomMeshInfo* New() const final {
    return new CustomMeshInfo();
  }

  CustomMeshInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CustomMeshInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CustomMeshInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CustomMeshInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CustomMeshInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.CustomMeshInfo";
  }
  protected:
  explicit CustomMeshInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVertexLocationsFieldNumber = 1,
    kWedgeIndicesFieldNumber = 2,
    kVertexUvFieldNumber = 3,
    kNormalsFieldNumber = 4,
  };
  // repeated .catalog_studio_message.Vector vertex_locations = 1;
  int vertex_locations_size() const;
  private:
  int _internal_vertex_locations_size() const;
  public:
  void clear_vertex_locations();
  ::catalog_studio_message::Vector* mutable_vertex_locations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >*
      mutable_vertex_locations();
  private:
  const ::catalog_studio_message::Vector& _internal_vertex_locations(int index) const;
  ::catalog_studio_message::Vector* _internal_add_vertex_locations();
  public:
  const ::catalog_studio_message::Vector& vertex_locations(int index) const;
  ::catalog_studio_message::Vector* add_vertex_locations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >&
      vertex_locations() const;

  // repeated int32 wedge_indices = 2;
  int wedge_indices_size() const;
  private:
  int _internal_wedge_indices_size() const;
  public:
  void clear_wedge_indices();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_wedge_indices(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_wedge_indices() const;
  void _internal_add_wedge_indices(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_wedge_indices();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 wedge_indices(int index) const;
  void set_wedge_indices(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_wedge_indices(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      wedge_indices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_wedge_indices();

  // repeated .catalog_studio_message.Vector2D vertex_uv = 3;
  int vertex_uv_size() const;
  private:
  int _internal_vertex_uv_size() const;
  public:
  void clear_vertex_uv();
  ::catalog_studio_message::Vector2D* mutable_vertex_uv(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector2D >*
      mutable_vertex_uv();
  private:
  const ::catalog_studio_message::Vector2D& _internal_vertex_uv(int index) const;
  ::catalog_studio_message::Vector2D* _internal_add_vertex_uv();
  public:
  const ::catalog_studio_message::Vector2D& vertex_uv(int index) const;
  ::catalog_studio_message::Vector2D* add_vertex_uv();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector2D >&
      vertex_uv() const;

  // repeated .catalog_studio_message.Vector normals = 4;
  int normals_size() const;
  private:
  int _internal_normals_size() const;
  public:
  void clear_normals();
  ::catalog_studio_message::Vector* mutable_normals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >*
      mutable_normals();
  private:
  const ::catalog_studio_message::Vector& _internal_normals(int index) const;
  ::catalog_studio_message::Vector* _internal_add_normals();
  public:
  const ::catalog_studio_message::Vector& normals(int index) const;
  ::catalog_studio_message::Vector* add_normals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >&
      normals() const;

  // @@protoc_insertion_point(class_scope:catalog_studio_message.CustomMeshInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector > vertex_locations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > wedge_indices_;
  mutable std::atomic<int> _wedge_indices_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector2D > vertex_uv_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector > normals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_CustomMeshInfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CustomMeshInfo

// repeated .catalog_studio_message.Vector vertex_locations = 1;
inline int CustomMeshInfo::_internal_vertex_locations_size() const {
  return vertex_locations_.size();
}
inline int CustomMeshInfo::vertex_locations_size() const {
  return _internal_vertex_locations_size();
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::mutable_vertex_locations(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CustomMeshInfo.vertex_locations)
  return vertex_locations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >*
CustomMeshInfo::mutable_vertex_locations() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CustomMeshInfo.vertex_locations)
  return &vertex_locations_;
}
inline const ::catalog_studio_message::Vector& CustomMeshInfo::_internal_vertex_locations(int index) const {
  return vertex_locations_.Get(index);
}
inline const ::catalog_studio_message::Vector& CustomMeshInfo::vertex_locations(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CustomMeshInfo.vertex_locations)
  return _internal_vertex_locations(index);
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::_internal_add_vertex_locations() {
  return vertex_locations_.Add();
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::add_vertex_locations() {
  ::catalog_studio_message::Vector* _add = _internal_add_vertex_locations();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CustomMeshInfo.vertex_locations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >&
CustomMeshInfo::vertex_locations() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CustomMeshInfo.vertex_locations)
  return vertex_locations_;
}

// repeated int32 wedge_indices = 2;
inline int CustomMeshInfo::_internal_wedge_indices_size() const {
  return wedge_indices_.size();
}
inline int CustomMeshInfo::wedge_indices_size() const {
  return _internal_wedge_indices_size();
}
inline void CustomMeshInfo::clear_wedge_indices() {
  wedge_indices_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CustomMeshInfo::_internal_wedge_indices(int index) const {
  return wedge_indices_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CustomMeshInfo::wedge_indices(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CustomMeshInfo.wedge_indices)
  return _internal_wedge_indices(index);
}
inline void CustomMeshInfo::set_wedge_indices(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  wedge_indices_.Set(index, value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.CustomMeshInfo.wedge_indices)
}
inline void CustomMeshInfo::_internal_add_wedge_indices(::PROTOBUF_NAMESPACE_ID::int32 value) {
  wedge_indices_.Add(value);
}
inline void CustomMeshInfo::add_wedge_indices(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_wedge_indices(value);
  // @@protoc_insertion_point(field_add:catalog_studio_message.CustomMeshInfo.wedge_indices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
CustomMeshInfo::_internal_wedge_indices() const {
  return wedge_indices_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
CustomMeshInfo::wedge_indices() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CustomMeshInfo.wedge_indices)
  return _internal_wedge_indices();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
CustomMeshInfo::_internal_mutable_wedge_indices() {
  return &wedge_indices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
CustomMeshInfo::mutable_wedge_indices() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CustomMeshInfo.wedge_indices)
  return _internal_mutable_wedge_indices();
}

// repeated .catalog_studio_message.Vector2D vertex_uv = 3;
inline int CustomMeshInfo::_internal_vertex_uv_size() const {
  return vertex_uv_.size();
}
inline int CustomMeshInfo::vertex_uv_size() const {
  return _internal_vertex_uv_size();
}
inline ::catalog_studio_message::Vector2D* CustomMeshInfo::mutable_vertex_uv(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CustomMeshInfo.vertex_uv)
  return vertex_uv_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector2D >*
CustomMeshInfo::mutable_vertex_uv() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CustomMeshInfo.vertex_uv)
  return &vertex_uv_;
}
inline const ::catalog_studio_message::Vector2D& CustomMeshInfo::_internal_vertex_uv(int index) const {
  return vertex_uv_.Get(index);
}
inline const ::catalog_studio_message::Vector2D& CustomMeshInfo::vertex_uv(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CustomMeshInfo.vertex_uv)
  return _internal_vertex_uv(index);
}
inline ::catalog_studio_message::Vector2D* CustomMeshInfo::_internal_add_vertex_uv() {
  return vertex_uv_.Add();
}
inline ::catalog_studio_message::Vector2D* CustomMeshInfo::add_vertex_uv() {
  ::catalog_studio_message::Vector2D* _add = _internal_add_vertex_uv();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CustomMeshInfo.vertex_uv)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector2D >&
CustomMeshInfo::vertex_uv() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CustomMeshInfo.vertex_uv)
  return vertex_uv_;
}

// repeated .catalog_studio_message.Vector normals = 4;
inline int CustomMeshInfo::_internal_normals_size() const {
  return normals_.size();
}
inline int CustomMeshInfo::normals_size() const {
  return _internal_normals_size();
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::mutable_normals(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.CustomMeshInfo.normals)
  return normals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >*
CustomMeshInfo::mutable_normals() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.CustomMeshInfo.normals)
  return &normals_;
}
inline const ::catalog_studio_message::Vector& CustomMeshInfo::_internal_normals(int index) const {
  return normals_.Get(index);
}
inline const ::catalog_studio_message::Vector& CustomMeshInfo::normals(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.CustomMeshInfo.normals)
  return _internal_normals(index);
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::_internal_add_normals() {
  return normals_.Add();
}
inline ::catalog_studio_message::Vector* CustomMeshInfo::add_normals() {
  ::catalog_studio_message::Vector* _add = _internal_add_normals();
  // @@protoc_insertion_point(field_add:catalog_studio_message.CustomMeshInfo.normals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::Vector >&
CustomMeshInfo::normals() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.CustomMeshInfo.normals)
  return normals_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_CustomMeshInfo_2eproto
