// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DependFile.proto

#include "DependFile.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr DependFileData::DependFileData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : modeldependfiles_()
  , matdependfiles_()
  , modeldependfolderid_()
  , matdependfolderid_(){}
struct DependFileDataDefaultTypeInternal {
  constexpr DependFileDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DependFileDataDefaultTypeInternal() {}
  union {
    DependFileData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DependFileDataDefaultTypeInternal _DependFileData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_DependFile_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_DependFile_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_DependFile_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_DependFile_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::DependFileData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::DependFileData, modeldependfiles_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::DependFileData, matdependfiles_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::DependFileData, modeldependfolderid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::DependFileData, matdependfolderid_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::DependFileData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_DependFileData_default_instance_),
};

const char descriptor_table_protodef_DependFile_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\020DependFile.proto\022\026catalog_studio_messa"
  "ge\032\020CSMatModel.proto\032\031ExpressionValuePai"
  "r.proto\"\244\002\n\016DependFileData\022@\n\020ModelDepen"
  "dFiles\030\001 \003(\0132&.catalog_studio_message.CS"
  "MatModelData\022>\n\016MatDependFiles\030\002 \003(\0132&.c"
  "atalog_studio_message.CSMatModelData\022H\n\023"
  "ModelDependFolderID\030\003 \003(\0132+.catalog_stud"
  "io_message.ExpressionValuePair\022F\n\021MatDep"
  "endFolderID\030\004 \003(\0132+.catalog_studio_messa"
  "ge.ExpressionValuePairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_DependFile_2eproto_deps[2] = {
  &::descriptor_table_CSMatModel_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_DependFile_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_DependFile_2eproto = {
  false, false, 390, descriptor_table_protodef_DependFile_2eproto, "DependFile.proto", 
  &descriptor_table_DependFile_2eproto_once, descriptor_table_DependFile_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_DependFile_2eproto::offsets,
  file_level_metadata_DependFile_2eproto, file_level_enum_descriptors_DependFile_2eproto, file_level_service_descriptors_DependFile_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_DependFile_2eproto_getter() {
  return &descriptor_table_DependFile_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_DependFile_2eproto(&descriptor_table_DependFile_2eproto);
namespace catalog_studio_message {

// ===================================================================

class DependFileData::_Internal {
 public:
};

void DependFileData::clear_modeldependfiles() {
  modeldependfiles_.Clear();
}
void DependFileData::clear_matdependfiles() {
  matdependfiles_.Clear();
}
void DependFileData::clear_modeldependfolderid() {
  modeldependfolderid_.Clear();
}
void DependFileData::clear_matdependfolderid() {
  matdependfolderid_.Clear();
}
DependFileData::DependFileData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  modeldependfiles_(arena),
  matdependfiles_(arena),
  modeldependfolderid_(arena),
  matdependfolderid_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.DependFileData)
}
DependFileData::DependFileData(const DependFileData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      modeldependfiles_(from.modeldependfiles_),
      matdependfiles_(from.matdependfiles_),
      modeldependfolderid_(from.modeldependfolderid_),
      matdependfolderid_(from.matdependfolderid_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.DependFileData)
}

void DependFileData::SharedCtor() {
}

DependFileData::~DependFileData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.DependFileData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DependFileData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DependFileData::ArenaDtor(void* object) {
  DependFileData* _this = reinterpret_cast< DependFileData* >(object);
  (void)_this;
}
void DependFileData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DependFileData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DependFileData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.DependFileData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  modeldependfiles_.Clear();
  matdependfiles_.Clear();
  modeldependfolderid_.Clear();
  matdependfolderid_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DependFileData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.CSMatModelData ModelDependFiles = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_modeldependfiles(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.CSMatModelData MatDependFiles = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_matdependfiles(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ExpressionValuePair ModelDependFolderID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_modeldependfolderid(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ExpressionValuePair MatDependFolderID = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_matdependfolderid(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DependFileData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.DependFileData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.CSMatModelData ModelDependFiles = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_modeldependfiles_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_modeldependfiles(i), target, stream);
  }

  // repeated .catalog_studio_message.CSMatModelData MatDependFiles = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_matdependfiles_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_matdependfiles(i), target, stream);
  }

  // repeated .catalog_studio_message.ExpressionValuePair ModelDependFolderID = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_modeldependfolderid_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_modeldependfolderid(i), target, stream);
  }

  // repeated .catalog_studio_message.ExpressionValuePair MatDependFolderID = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_matdependfolderid_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_matdependfolderid(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.DependFileData)
  return target;
}

size_t DependFileData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.DependFileData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.CSMatModelData ModelDependFiles = 1;
  total_size += 1UL * this->_internal_modeldependfiles_size();
  for (const auto& msg : this->modeldependfiles_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.CSMatModelData MatDependFiles = 2;
  total_size += 1UL * this->_internal_matdependfiles_size();
  for (const auto& msg : this->matdependfiles_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.ExpressionValuePair ModelDependFolderID = 3;
  total_size += 1UL * this->_internal_modeldependfolderid_size();
  for (const auto& msg : this->modeldependfolderid_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.ExpressionValuePair MatDependFolderID = 4;
  total_size += 1UL * this->_internal_matdependfolderid_size();
  for (const auto& msg : this->matdependfolderid_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DependFileData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DependFileData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DependFileData::GetClassData() const { return &_class_data_; }

void DependFileData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DependFileData *>(to)->MergeFrom(
      static_cast<const DependFileData &>(from));
}


void DependFileData::MergeFrom(const DependFileData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.DependFileData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  modeldependfiles_.MergeFrom(from.modeldependfiles_);
  matdependfiles_.MergeFrom(from.matdependfiles_);
  modeldependfolderid_.MergeFrom(from.modeldependfolderid_);
  matdependfolderid_.MergeFrom(from.matdependfolderid_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DependFileData::CopyFrom(const DependFileData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.DependFileData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DependFileData::IsInitialized() const {
  return true;
}

void DependFileData::InternalSwap(DependFileData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  modeldependfiles_.InternalSwap(&other->modeldependfiles_);
  matdependfiles_.InternalSwap(&other->matdependfiles_);
  modeldependfolderid_.InternalSwap(&other->modeldependfolderid_);
  matdependfolderid_.InternalSwap(&other->matdependfolderid_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DependFileData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_DependFile_2eproto_getter, &descriptor_table_DependFile_2eproto_once,
      file_level_metadata_DependFile_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::DependFileData* Arena::CreateMaybeMessage< ::catalog_studio_message::DependFileData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::DependFileData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
