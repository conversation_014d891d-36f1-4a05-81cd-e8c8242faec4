// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DependFile.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_DependFile_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_DependFile_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "CSMatModel.pb.h"
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_DependFile_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_DependFile_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_DependFile_2eproto;
namespace catalog_studio_message {
class DependFileData;
struct DependFileDataDefaultTypeInternal;
extern DependFileDataDefaultTypeInternal _DependFileData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::DependFileData* Arena::CreateMaybeMessage<::catalog_studio_message::DependFileData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class DependFileData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.DependFileData) */ {
 public:
  inline DependFileData() : DependFileData(nullptr) {}
  ~DependFileData() override;
  explicit constexpr DependFileData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DependFileData(const DependFileData& from);
  DependFileData(DependFileData&& from) noexcept
    : DependFileData() {
    *this = ::std::move(from);
  }

  inline DependFileData& operator=(const DependFileData& from) {
    CopyFrom(from);
    return *this;
  }
  inline DependFileData& operator=(DependFileData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DependFileData& default_instance() {
    return *internal_default_instance();
  }
  static inline const DependFileData* internal_default_instance() {
    return reinterpret_cast<const DependFileData*>(
               &_DependFileData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DependFileData& a, DependFileData& b) {
    a.Swap(&b);
  }
  inline void Swap(DependFileData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DependFileData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DependFileData* New() const final {
    return new DependFileData();
  }

  DependFileData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DependFileData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DependFileData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DependFileData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DependFileData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.DependFileData";
  }
  protected:
  explicit DependFileData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelDependFilesFieldNumber = 1,
    kMatDependFilesFieldNumber = 2,
    kModelDependFolderIDFieldNumber = 3,
    kMatDependFolderIDFieldNumber = 4,
  };
  // repeated .catalog_studio_message.CSMatModelData ModelDependFiles = 1;
  int modeldependfiles_size() const;
  private:
  int _internal_modeldependfiles_size() const;
  public:
  void clear_modeldependfiles();
  ::catalog_studio_message::CSMatModelData* mutable_modeldependfiles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >*
      mutable_modeldependfiles();
  private:
  const ::catalog_studio_message::CSMatModelData& _internal_modeldependfiles(int index) const;
  ::catalog_studio_message::CSMatModelData* _internal_add_modeldependfiles();
  public:
  const ::catalog_studio_message::CSMatModelData& modeldependfiles(int index) const;
  ::catalog_studio_message::CSMatModelData* add_modeldependfiles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >&
      modeldependfiles() const;

  // repeated .catalog_studio_message.CSMatModelData MatDependFiles = 2;
  int matdependfiles_size() const;
  private:
  int _internal_matdependfiles_size() const;
  public:
  void clear_matdependfiles();
  ::catalog_studio_message::CSMatModelData* mutable_matdependfiles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >*
      mutable_matdependfiles();
  private:
  const ::catalog_studio_message::CSMatModelData& _internal_matdependfiles(int index) const;
  ::catalog_studio_message::CSMatModelData* _internal_add_matdependfiles();
  public:
  const ::catalog_studio_message::CSMatModelData& matdependfiles(int index) const;
  ::catalog_studio_message::CSMatModelData* add_matdependfiles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >&
      matdependfiles() const;

  // repeated .catalog_studio_message.ExpressionValuePair ModelDependFolderID = 3;
  int modeldependfolderid_size() const;
  private:
  int _internal_modeldependfolderid_size() const;
  public:
  void clear_modeldependfolderid();
  ::catalog_studio_message::ExpressionValuePair* mutable_modeldependfolderid(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
      mutable_modeldependfolderid();
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_modeldependfolderid(int index) const;
  ::catalog_studio_message::ExpressionValuePair* _internal_add_modeldependfolderid();
  public:
  const ::catalog_studio_message::ExpressionValuePair& modeldependfolderid(int index) const;
  ::catalog_studio_message::ExpressionValuePair* add_modeldependfolderid();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
      modeldependfolderid() const;

  // repeated .catalog_studio_message.ExpressionValuePair MatDependFolderID = 4;
  int matdependfolderid_size() const;
  private:
  int _internal_matdependfolderid_size() const;
  public:
  void clear_matdependfolderid();
  ::catalog_studio_message::ExpressionValuePair* mutable_matdependfolderid(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
      mutable_matdependfolderid();
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_matdependfolderid(int index) const;
  ::catalog_studio_message::ExpressionValuePair* _internal_add_matdependfolderid();
  public:
  const ::catalog_studio_message::ExpressionValuePair& matdependfolderid(int index) const;
  ::catalog_studio_message::ExpressionValuePair* add_matdependfolderid();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
      matdependfolderid() const;

  // @@protoc_insertion_point(class_scope:catalog_studio_message.DependFileData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData > modeldependfiles_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData > matdependfiles_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair > modeldependfolderid_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair > matdependfolderid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_DependFile_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DependFileData

// repeated .catalog_studio_message.CSMatModelData ModelDependFiles = 1;
inline int DependFileData::_internal_modeldependfiles_size() const {
  return modeldependfiles_.size();
}
inline int DependFileData::modeldependfiles_size() const {
  return _internal_modeldependfiles_size();
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::mutable_modeldependfiles(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.DependFileData.ModelDependFiles)
  return modeldependfiles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >*
DependFileData::mutable_modeldependfiles() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.DependFileData.ModelDependFiles)
  return &modeldependfiles_;
}
inline const ::catalog_studio_message::CSMatModelData& DependFileData::_internal_modeldependfiles(int index) const {
  return modeldependfiles_.Get(index);
}
inline const ::catalog_studio_message::CSMatModelData& DependFileData::modeldependfiles(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.DependFileData.ModelDependFiles)
  return _internal_modeldependfiles(index);
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::_internal_add_modeldependfiles() {
  return modeldependfiles_.Add();
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::add_modeldependfiles() {
  ::catalog_studio_message::CSMatModelData* _add = _internal_add_modeldependfiles();
  // @@protoc_insertion_point(field_add:catalog_studio_message.DependFileData.ModelDependFiles)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >&
DependFileData::modeldependfiles() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.DependFileData.ModelDependFiles)
  return modeldependfiles_;
}

// repeated .catalog_studio_message.CSMatModelData MatDependFiles = 2;
inline int DependFileData::_internal_matdependfiles_size() const {
  return matdependfiles_.size();
}
inline int DependFileData::matdependfiles_size() const {
  return _internal_matdependfiles_size();
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::mutable_matdependfiles(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.DependFileData.MatDependFiles)
  return matdependfiles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >*
DependFileData::mutable_matdependfiles() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.DependFileData.MatDependFiles)
  return &matdependfiles_;
}
inline const ::catalog_studio_message::CSMatModelData& DependFileData::_internal_matdependfiles(int index) const {
  return matdependfiles_.Get(index);
}
inline const ::catalog_studio_message::CSMatModelData& DependFileData::matdependfiles(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.DependFileData.MatDependFiles)
  return _internal_matdependfiles(index);
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::_internal_add_matdependfiles() {
  return matdependfiles_.Add();
}
inline ::catalog_studio_message::CSMatModelData* DependFileData::add_matdependfiles() {
  ::catalog_studio_message::CSMatModelData* _add = _internal_add_matdependfiles();
  // @@protoc_insertion_point(field_add:catalog_studio_message.DependFileData.MatDependFiles)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::CSMatModelData >&
DependFileData::matdependfiles() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.DependFileData.MatDependFiles)
  return matdependfiles_;
}

// repeated .catalog_studio_message.ExpressionValuePair ModelDependFolderID = 3;
inline int DependFileData::_internal_modeldependfolderid_size() const {
  return modeldependfolderid_.size();
}
inline int DependFileData::modeldependfolderid_size() const {
  return _internal_modeldependfolderid_size();
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::mutable_modeldependfolderid(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.DependFileData.ModelDependFolderID)
  return modeldependfolderid_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
DependFileData::mutable_modeldependfolderid() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.DependFileData.ModelDependFolderID)
  return &modeldependfolderid_;
}
inline const ::catalog_studio_message::ExpressionValuePair& DependFileData::_internal_modeldependfolderid(int index) const {
  return modeldependfolderid_.Get(index);
}
inline const ::catalog_studio_message::ExpressionValuePair& DependFileData::modeldependfolderid(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.DependFileData.ModelDependFolderID)
  return _internal_modeldependfolderid(index);
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::_internal_add_modeldependfolderid() {
  return modeldependfolderid_.Add();
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::add_modeldependfolderid() {
  ::catalog_studio_message::ExpressionValuePair* _add = _internal_add_modeldependfolderid();
  // @@protoc_insertion_point(field_add:catalog_studio_message.DependFileData.ModelDependFolderID)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
DependFileData::modeldependfolderid() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.DependFileData.ModelDependFolderID)
  return modeldependfolderid_;
}

// repeated .catalog_studio_message.ExpressionValuePair MatDependFolderID = 4;
inline int DependFileData::_internal_matdependfolderid_size() const {
  return matdependfolderid_.size();
}
inline int DependFileData::matdependfolderid_size() const {
  return _internal_matdependfolderid_size();
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::mutable_matdependfolderid(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.DependFileData.MatDependFolderID)
  return matdependfolderid_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
DependFileData::mutable_matdependfolderid() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.DependFileData.MatDependFolderID)
  return &matdependfolderid_;
}
inline const ::catalog_studio_message::ExpressionValuePair& DependFileData::_internal_matdependfolderid(int index) const {
  return matdependfolderid_.Get(index);
}
inline const ::catalog_studio_message::ExpressionValuePair& DependFileData::matdependfolderid(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.DependFileData.MatDependFolderID)
  return _internal_matdependfolderid(index);
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::_internal_add_matdependfolderid() {
  return matdependfolderid_.Add();
}
inline ::catalog_studio_message::ExpressionValuePair* DependFileData::add_matdependfolderid() {
  ::catalog_studio_message::ExpressionValuePair* _add = _internal_add_matdependfolderid();
  // @@protoc_insertion_point(field_add:catalog_studio_message.DependFileData.MatDependFolderID)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
DependFileData::matdependfolderid() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.DependFileData.MatDependFolderID)
  return matdependfolderid_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_DependFile_2eproto
