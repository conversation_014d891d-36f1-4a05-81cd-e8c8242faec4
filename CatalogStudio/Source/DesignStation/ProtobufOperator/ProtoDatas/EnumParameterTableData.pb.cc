// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: EnumParameterTableData.proto

#include "EnumParameterTableData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr EnumParameterTableData::EnumParameterTableData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_for_display_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , visibility_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , priority_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , main_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , image_for_display_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , visibility_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , expression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , force_select_condition_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct EnumParameterTableDataDefaultTypeInternal {
  constexpr EnumParameterTableDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EnumParameterTableDataDefaultTypeInternal() {}
  union {
    EnumParameterTableData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EnumParameterTableDataDefaultTypeInternal _EnumParameterTableData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_EnumParameterTableData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_EnumParameterTableData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_EnumParameterTableData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_EnumParameterTableData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, name_for_display_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, image_for_display_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, visibility_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, priority_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, main_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, visibility_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, expression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::EnumParameterTableData, force_select_condition_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::EnumParameterTableData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_EnumParameterTableData_default_instance_),
};

const char descriptor_table_protodef_EnumParameterTableData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034EnumParameterTableData.proto\022\026catalog_"
  "studio_message\"\353\001\n\026EnumParameterTableDat"
  "a\022\n\n\002id\030\001 \001(\t\022\r\n\005value\030\002 \001(\t\022\030\n\020name_for"
  "_display\030\003 \001(\t\022\031\n\021image_for_display\030\007 \001("
  "\t\022\022\n\nvisibility\030\004 \001(\t\022\020\n\010priority\030\005 \001(\t\022"
  "\017\n\007main_id\030\006 \001(\t\022\026\n\016visibility_exp\030\010 \001(\t"
  "\022\022\n\nexpression\030\t \001(\t\022\036\n\026force_select_con"
  "dition\030\n \001(\tb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_EnumParameterTableData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_EnumParameterTableData_2eproto = {
  false, false, 300, descriptor_table_protodef_EnumParameterTableData_2eproto, "EnumParameterTableData.proto", 
  &descriptor_table_EnumParameterTableData_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_EnumParameterTableData_2eproto::offsets,
  file_level_metadata_EnumParameterTableData_2eproto, file_level_enum_descriptors_EnumParameterTableData_2eproto, file_level_service_descriptors_EnumParameterTableData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_EnumParameterTableData_2eproto_getter() {
  return &descriptor_table_EnumParameterTableData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_EnumParameterTableData_2eproto(&descriptor_table_EnumParameterTableData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class EnumParameterTableData::_Internal {
 public:
};

EnumParameterTableData::EnumParameterTableData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.EnumParameterTableData)
}
EnumParameterTableData::EnumParameterTableData(const EnumParameterTableData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_value().empty()) {
    value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_value(), 
      GetArenaForAllocation());
  }
  name_for_display_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_name_for_display().empty()) {
    name_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name_for_display(), 
      GetArenaForAllocation());
  }
  visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_visibility().empty()) {
    visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_visibility(), 
      GetArenaForAllocation());
  }
  priority_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_priority().empty()) {
    priority_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_priority(), 
      GetArenaForAllocation());
  }
  main_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_main_id().empty()) {
    main_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_main_id(), 
      GetArenaForAllocation());
  }
  image_for_display_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_image_for_display().empty()) {
    image_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_image_for_display(), 
      GetArenaForAllocation());
  }
  visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_visibility_exp().empty()) {
    visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_visibility_exp(), 
      GetArenaForAllocation());
  }
  expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_expression().empty()) {
    expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_expression(), 
      GetArenaForAllocation());
  }
  force_select_condition_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_force_select_condition().empty()) {
    force_select_condition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_force_select_condition(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.EnumParameterTableData)
}

void EnumParameterTableData::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
name_for_display_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
priority_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
main_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
image_for_display_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
force_select_condition_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

EnumParameterTableData::~EnumParameterTableData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.EnumParameterTableData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EnumParameterTableData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_for_display_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  visibility_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  priority_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  main_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  image_for_display_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  visibility_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  expression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  force_select_condition_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EnumParameterTableData::ArenaDtor(void* object) {
  EnumParameterTableData* _this = reinterpret_cast< EnumParameterTableData* >(object);
  (void)_this;
}
void EnumParameterTableData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EnumParameterTableData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EnumParameterTableData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.EnumParameterTableData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  value_.ClearToEmpty();
  name_for_display_.ClearToEmpty();
  visibility_.ClearToEmpty();
  priority_.ClearToEmpty();
  main_id_.ClearToEmpty();
  image_for_display_.ClearToEmpty();
  visibility_exp_.ClearToEmpty();
  expression_.ClearToEmpty();
  force_select_condition_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnumParameterTableData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name_for_display = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_name_for_display();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.name_for_display"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string visibility = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_visibility();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.visibility"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string priority = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_priority();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.priority"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string main_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_main_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.main_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string image_for_display = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_image_for_display();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.image_for_display"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string visibility_exp = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_visibility_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.visibility_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string expression = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_expression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.expression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string force_select_condition = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_force_select_condition();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.EnumParameterTableData.force_select_condition"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EnumParameterTableData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.EnumParameterTableData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string value = 2;
  if (!this->_internal_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.value");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_value(), target);
  }

  // string name_for_display = 3;
  if (!this->_internal_name_for_display().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name_for_display().data(), static_cast<int>(this->_internal_name_for_display().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.name_for_display");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_name_for_display(), target);
  }

  // string visibility = 4;
  if (!this->_internal_visibility().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_visibility().data(), static_cast<int>(this->_internal_visibility().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.visibility");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_visibility(), target);
  }

  // string priority = 5;
  if (!this->_internal_priority().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_priority().data(), static_cast<int>(this->_internal_priority().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.priority");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_priority(), target);
  }

  // string main_id = 6;
  if (!this->_internal_main_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_main_id().data(), static_cast<int>(this->_internal_main_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.main_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_main_id(), target);
  }

  // string image_for_display = 7;
  if (!this->_internal_image_for_display().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_image_for_display().data(), static_cast<int>(this->_internal_image_for_display().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.image_for_display");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_image_for_display(), target);
  }

  // string visibility_exp = 8;
  if (!this->_internal_visibility_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_visibility_exp().data(), static_cast<int>(this->_internal_visibility_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.visibility_exp");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_visibility_exp(), target);
  }

  // string expression = 9;
  if (!this->_internal_expression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_expression().data(), static_cast<int>(this->_internal_expression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.expression");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_expression(), target);
  }

  // string force_select_condition = 10;
  if (!this->_internal_force_select_condition().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_force_select_condition().data(), static_cast<int>(this->_internal_force_select_condition().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.EnumParameterTableData.force_select_condition");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_force_select_condition(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.EnumParameterTableData)
  return target;
}

size_t EnumParameterTableData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.EnumParameterTableData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string value = 2;
  if (!this->_internal_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  // string name_for_display = 3;
  if (!this->_internal_name_for_display().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name_for_display());
  }

  // string visibility = 4;
  if (!this->_internal_visibility().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_visibility());
  }

  // string priority = 5;
  if (!this->_internal_priority().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_priority());
  }

  // string main_id = 6;
  if (!this->_internal_main_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_main_id());
  }

  // string image_for_display = 7;
  if (!this->_internal_image_for_display().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_image_for_display());
  }

  // string visibility_exp = 8;
  if (!this->_internal_visibility_exp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_visibility_exp());
  }

  // string expression = 9;
  if (!this->_internal_expression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_expression());
  }

  // string force_select_condition = 10;
  if (!this->_internal_force_select_condition().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_force_select_condition());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EnumParameterTableData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EnumParameterTableData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EnumParameterTableData::GetClassData() const { return &_class_data_; }

void EnumParameterTableData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EnumParameterTableData *>(to)->MergeFrom(
      static_cast<const EnumParameterTableData &>(from));
}


void EnumParameterTableData::MergeFrom(const EnumParameterTableData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.EnumParameterTableData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_value().empty()) {
    _internal_set_value(from._internal_value());
  }
  if (!from._internal_name_for_display().empty()) {
    _internal_set_name_for_display(from._internal_name_for_display());
  }
  if (!from._internal_visibility().empty()) {
    _internal_set_visibility(from._internal_visibility());
  }
  if (!from._internal_priority().empty()) {
    _internal_set_priority(from._internal_priority());
  }
  if (!from._internal_main_id().empty()) {
    _internal_set_main_id(from._internal_main_id());
  }
  if (!from._internal_image_for_display().empty()) {
    _internal_set_image_for_display(from._internal_image_for_display());
  }
  if (!from._internal_visibility_exp().empty()) {
    _internal_set_visibility_exp(from._internal_visibility_exp());
  }
  if (!from._internal_expression().empty()) {
    _internal_set_expression(from._internal_expression());
  }
  if (!from._internal_force_select_condition().empty()) {
    _internal_set_force_select_condition(from._internal_force_select_condition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EnumParameterTableData::CopyFrom(const EnumParameterTableData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.EnumParameterTableData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnumParameterTableData::IsInitialized() const {
  return true;
}

void EnumParameterTableData::InternalSwap(EnumParameterTableData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &value_, lhs_arena,
      &other->value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_for_display_, lhs_arena,
      &other->name_for_display_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &visibility_, lhs_arena,
      &other->visibility_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &priority_, lhs_arena,
      &other->priority_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &main_id_, lhs_arena,
      &other->main_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &image_for_display_, lhs_arena,
      &other->image_for_display_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &visibility_exp_, lhs_arena,
      &other->visibility_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &expression_, lhs_arena,
      &other->expression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &force_select_condition_, lhs_arena,
      &other->force_select_condition_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata EnumParameterTableData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_EnumParameterTableData_2eproto_getter, &descriptor_table_EnumParameterTableData_2eproto_once,
      file_level_metadata_EnumParameterTableData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::EnumParameterTableData* Arena::CreateMaybeMessage< ::catalog_studio_message::EnumParameterTableData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::EnumParameterTableData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
