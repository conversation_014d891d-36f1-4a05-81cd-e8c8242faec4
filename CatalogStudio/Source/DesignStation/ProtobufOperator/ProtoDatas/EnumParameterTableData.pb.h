// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: EnumParameterTableData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_EnumParameterTableData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_EnumParameterTableData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_EnumParameterTableData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_EnumParameterTableData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_EnumParameterTableData_2eproto;
namespace catalog_studio_message {
class EnumParameterTableData;
struct EnumParameterTableDataDefaultTypeInternal;
extern EnumParameterTableDataDefaultTypeInternal _EnumParameterTableData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::EnumParameterTableData* Arena::CreateMaybeMessage<::catalog_studio_message::EnumParameterTableData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class EnumParameterTableData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.EnumParameterTableData) */ {
 public:
  inline EnumParameterTableData() : EnumParameterTableData(nullptr) {}
  ~EnumParameterTableData() override;
  explicit constexpr EnumParameterTableData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnumParameterTableData(const EnumParameterTableData& from);
  EnumParameterTableData(EnumParameterTableData&& from) noexcept
    : EnumParameterTableData() {
    *this = ::std::move(from);
  }

  inline EnumParameterTableData& operator=(const EnumParameterTableData& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnumParameterTableData& operator=(EnumParameterTableData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnumParameterTableData& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnumParameterTableData* internal_default_instance() {
    return reinterpret_cast<const EnumParameterTableData*>(
               &_EnumParameterTableData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EnumParameterTableData& a, EnumParameterTableData& b) {
    a.Swap(&b);
  }
  inline void Swap(EnumParameterTableData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnumParameterTableData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EnumParameterTableData* New() const final {
    return new EnumParameterTableData();
  }

  EnumParameterTableData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EnumParameterTableData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnumParameterTableData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EnumParameterTableData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnumParameterTableData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.EnumParameterTableData";
  }
  protected:
  explicit EnumParameterTableData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kValueFieldNumber = 2,
    kNameForDisplayFieldNumber = 3,
    kVisibilityFieldNumber = 4,
    kPriorityFieldNumber = 5,
    kMainIdFieldNumber = 6,
    kImageForDisplayFieldNumber = 7,
    kVisibilityExpFieldNumber = 8,
    kExpressionFieldNumber = 9,
    kForceSelectConditionFieldNumber = 10,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string value = 2;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_MUST_USE_RESULT std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // string name_for_display = 3;
  void clear_name_for_display();
  const std::string& name_for_display() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name_for_display(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name_for_display();
  PROTOBUF_MUST_USE_RESULT std::string* release_name_for_display();
  void set_allocated_name_for_display(std::string* name_for_display);
  private:
  const std::string& _internal_name_for_display() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name_for_display(const std::string& value);
  std::string* _internal_mutable_name_for_display();
  public:

  // string visibility = 4;
  void clear_visibility();
  const std::string& visibility() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_visibility(ArgT0&& arg0, ArgT... args);
  std::string* mutable_visibility();
  PROTOBUF_MUST_USE_RESULT std::string* release_visibility();
  void set_allocated_visibility(std::string* visibility);
  private:
  const std::string& _internal_visibility() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_visibility(const std::string& value);
  std::string* _internal_mutable_visibility();
  public:

  // string priority = 5;
  void clear_priority();
  const std::string& priority() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_priority(ArgT0&& arg0, ArgT... args);
  std::string* mutable_priority();
  PROTOBUF_MUST_USE_RESULT std::string* release_priority();
  void set_allocated_priority(std::string* priority);
  private:
  const std::string& _internal_priority() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_priority(const std::string& value);
  std::string* _internal_mutable_priority();
  public:

  // string main_id = 6;
  void clear_main_id();
  const std::string& main_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_main_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_main_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_main_id();
  void set_allocated_main_id(std::string* main_id);
  private:
  const std::string& _internal_main_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_main_id(const std::string& value);
  std::string* _internal_mutable_main_id();
  public:

  // string image_for_display = 7;
  void clear_image_for_display();
  const std::string& image_for_display() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_image_for_display(ArgT0&& arg0, ArgT... args);
  std::string* mutable_image_for_display();
  PROTOBUF_MUST_USE_RESULT std::string* release_image_for_display();
  void set_allocated_image_for_display(std::string* image_for_display);
  private:
  const std::string& _internal_image_for_display() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_image_for_display(const std::string& value);
  std::string* _internal_mutable_image_for_display();
  public:

  // string visibility_exp = 8;
  void clear_visibility_exp();
  const std::string& visibility_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_visibility_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_visibility_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_visibility_exp();
  void set_allocated_visibility_exp(std::string* visibility_exp);
  private:
  const std::string& _internal_visibility_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_visibility_exp(const std::string& value);
  std::string* _internal_mutable_visibility_exp();
  public:

  // string expression = 9;
  void clear_expression();
  const std::string& expression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_expression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_expression();
  PROTOBUF_MUST_USE_RESULT std::string* release_expression();
  void set_allocated_expression(std::string* expression);
  private:
  const std::string& _internal_expression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_expression(const std::string& value);
  std::string* _internal_mutable_expression();
  public:

  // string force_select_condition = 10;
  void clear_force_select_condition();
  const std::string& force_select_condition() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_force_select_condition(ArgT0&& arg0, ArgT... args);
  std::string* mutable_force_select_condition();
  PROTOBUF_MUST_USE_RESULT std::string* release_force_select_condition();
  void set_allocated_force_select_condition(std::string* force_select_condition);
  private:
  const std::string& _internal_force_select_condition() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_force_select_condition(const std::string& value);
  std::string* _internal_mutable_force_select_condition();
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.EnumParameterTableData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_for_display_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr visibility_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr priority_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr main_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr image_for_display_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr visibility_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr expression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr force_select_condition_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_EnumParameterTableData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EnumParameterTableData

// string id = 1;
inline void EnumParameterTableData::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.id)
}
inline std::string* EnumParameterTableData::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.id)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_id() const {
  return id_.Get();
}
inline void EnumParameterTableData::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.id)
}

// string value = 2;
inline void EnumParameterTableData::clear_value() {
  value_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::value() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_value(ArgT0&& arg0, ArgT... args) {
 
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.value)
}
inline std::string* EnumParameterTableData::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.value)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_value() const {
  return value_.Get();
}
inline void EnumParameterTableData::_internal_set_value(const std::string& value) {
  
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_value() {
  
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_value() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.value)
  return value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.value)
}

// string name_for_display = 3;
inline void EnumParameterTableData::clear_name_for_display() {
  name_for_display_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::name_for_display() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.name_for_display)
  return _internal_name_for_display();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_name_for_display(ArgT0&& arg0, ArgT... args) {
 
 name_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.name_for_display)
}
inline std::string* EnumParameterTableData::mutable_name_for_display() {
  std::string* _s = _internal_mutable_name_for_display();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.name_for_display)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_name_for_display() const {
  return name_for_display_.Get();
}
inline void EnumParameterTableData::_internal_set_name_for_display(const std::string& value) {
  
  name_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_name_for_display() {
  
  return name_for_display_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_name_for_display() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.name_for_display)
  return name_for_display_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_name_for_display(std::string* name_for_display) {
  if (name_for_display != nullptr) {
    
  } else {
    
  }
  name_for_display_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name_for_display,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.name_for_display)
}

// string image_for_display = 7;
inline void EnumParameterTableData::clear_image_for_display() {
  image_for_display_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::image_for_display() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.image_for_display)
  return _internal_image_for_display();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_image_for_display(ArgT0&& arg0, ArgT... args) {
 
 image_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.image_for_display)
}
inline std::string* EnumParameterTableData::mutable_image_for_display() {
  std::string* _s = _internal_mutable_image_for_display();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.image_for_display)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_image_for_display() const {
  return image_for_display_.Get();
}
inline void EnumParameterTableData::_internal_set_image_for_display(const std::string& value) {
  
  image_for_display_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_image_for_display() {
  
  return image_for_display_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_image_for_display() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.image_for_display)
  return image_for_display_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_image_for_display(std::string* image_for_display) {
  if (image_for_display != nullptr) {
    
  } else {
    
  }
  image_for_display_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), image_for_display,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.image_for_display)
}

// string visibility = 4;
inline void EnumParameterTableData::clear_visibility() {
  visibility_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::visibility() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.visibility)
  return _internal_visibility();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_visibility(ArgT0&& arg0, ArgT... args) {
 
 visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.visibility)
}
inline std::string* EnumParameterTableData::mutable_visibility() {
  std::string* _s = _internal_mutable_visibility();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.visibility)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_visibility() const {
  return visibility_.Get();
}
inline void EnumParameterTableData::_internal_set_visibility(const std::string& value) {
  
  visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_visibility() {
  
  return visibility_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_visibility() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.visibility)
  return visibility_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_visibility(std::string* visibility) {
  if (visibility != nullptr) {
    
  } else {
    
  }
  visibility_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), visibility,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.visibility)
}

// string priority = 5;
inline void EnumParameterTableData::clear_priority() {
  priority_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::priority() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.priority)
  return _internal_priority();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_priority(ArgT0&& arg0, ArgT... args) {
 
 priority_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.priority)
}
inline std::string* EnumParameterTableData::mutable_priority() {
  std::string* _s = _internal_mutable_priority();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.priority)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_priority() const {
  return priority_.Get();
}
inline void EnumParameterTableData::_internal_set_priority(const std::string& value) {
  
  priority_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_priority() {
  
  return priority_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_priority() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.priority)
  return priority_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_priority(std::string* priority) {
  if (priority != nullptr) {
    
  } else {
    
  }
  priority_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), priority,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.priority)
}

// string main_id = 6;
inline void EnumParameterTableData::clear_main_id() {
  main_id_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::main_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.main_id)
  return _internal_main_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_main_id(ArgT0&& arg0, ArgT... args) {
 
 main_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.main_id)
}
inline std::string* EnumParameterTableData::mutable_main_id() {
  std::string* _s = _internal_mutable_main_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.main_id)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_main_id() const {
  return main_id_.Get();
}
inline void EnumParameterTableData::_internal_set_main_id(const std::string& value) {
  
  main_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_main_id() {
  
  return main_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_main_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.main_id)
  return main_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_main_id(std::string* main_id) {
  if (main_id != nullptr) {
    
  } else {
    
  }
  main_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), main_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.main_id)
}

// string visibility_exp = 8;
inline void EnumParameterTableData::clear_visibility_exp() {
  visibility_exp_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::visibility_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.visibility_exp)
  return _internal_visibility_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_visibility_exp(ArgT0&& arg0, ArgT... args) {
 
 visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.visibility_exp)
}
inline std::string* EnumParameterTableData::mutable_visibility_exp() {
  std::string* _s = _internal_mutable_visibility_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.visibility_exp)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_visibility_exp() const {
  return visibility_exp_.Get();
}
inline void EnumParameterTableData::_internal_set_visibility_exp(const std::string& value) {
  
  visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_visibility_exp() {
  
  return visibility_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_visibility_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.visibility_exp)
  return visibility_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_visibility_exp(std::string* visibility_exp) {
  if (visibility_exp != nullptr) {
    
  } else {
    
  }
  visibility_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), visibility_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.visibility_exp)
}

// string expression = 9;
inline void EnumParameterTableData::clear_expression() {
  expression_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::expression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.expression)
  return _internal_expression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_expression(ArgT0&& arg0, ArgT... args) {
 
 expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.expression)
}
inline std::string* EnumParameterTableData::mutable_expression() {
  std::string* _s = _internal_mutable_expression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.expression)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_expression() const {
  return expression_.Get();
}
inline void EnumParameterTableData::_internal_set_expression(const std::string& value) {
  
  expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_expression() {
  
  return expression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_expression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.expression)
  return expression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_expression(std::string* expression) {
  if (expression != nullptr) {
    
  } else {
    
  }
  expression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), expression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.expression)
}

// string force_select_condition = 10;
inline void EnumParameterTableData::clear_force_select_condition() {
  force_select_condition_.ClearToEmpty();
}
inline const std::string& EnumParameterTableData::force_select_condition() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.EnumParameterTableData.force_select_condition)
  return _internal_force_select_condition();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumParameterTableData::set_force_select_condition(ArgT0&& arg0, ArgT... args) {
 
 force_select_condition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.EnumParameterTableData.force_select_condition)
}
inline std::string* EnumParameterTableData::mutable_force_select_condition() {
  std::string* _s = _internal_mutable_force_select_condition();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.EnumParameterTableData.force_select_condition)
  return _s;
}
inline const std::string& EnumParameterTableData::_internal_force_select_condition() const {
  return force_select_condition_.Get();
}
inline void EnumParameterTableData::_internal_set_force_select_condition(const std::string& value) {
  
  force_select_condition_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::_internal_mutable_force_select_condition() {
  
  return force_select_condition_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnumParameterTableData::release_force_select_condition() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.EnumParameterTableData.force_select_condition)
  return force_select_condition_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnumParameterTableData::set_allocated_force_select_condition(std::string* force_select_condition) {
  if (force_select_condition != nullptr) {
    
  } else {
    
  }
  force_select_condition_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), force_select_condition,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.EnumParameterTableData.force_select_condition)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_EnumParameterTableData_2eproto
