// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ExpressionValuePair.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ExpressionValuePair_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ExpressionValuePair_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ExpressionValuePair_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ExpressionValuePair_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ExpressionValuePair_2eproto;
namespace catalog_studio_message {
class ExpressionValuePair;
struct ExpressionValuePairDefaultTypeInternal;
extern ExpressionValuePairDefaultTypeInternal _ExpressionValuePair_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::ExpressionValuePair* Arena::CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class ExpressionValuePair final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.ExpressionValuePair) */ {
 public:
  inline ExpressionValuePair() : ExpressionValuePair(nullptr) {}
  ~ExpressionValuePair() override;
  explicit constexpr ExpressionValuePair(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExpressionValuePair(const ExpressionValuePair& from);
  ExpressionValuePair(ExpressionValuePair&& from) noexcept
    : ExpressionValuePair() {
    *this = ::std::move(from);
  }

  inline ExpressionValuePair& operator=(const ExpressionValuePair& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExpressionValuePair& operator=(ExpressionValuePair&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExpressionValuePair& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExpressionValuePair* internal_default_instance() {
    return reinterpret_cast<const ExpressionValuePair*>(
               &_ExpressionValuePair_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ExpressionValuePair& a, ExpressionValuePair& b) {
    a.Swap(&b);
  }
  inline void Swap(ExpressionValuePair* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExpressionValuePair* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExpressionValuePair* New() const final {
    return new ExpressionValuePair();
  }

  ExpressionValuePair* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExpressionValuePair>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExpressionValuePair& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ExpressionValuePair& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExpressionValuePair* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.ExpressionValuePair";
  }
  protected:
  explicit ExpressionValuePair(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExpressionFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string expression = 1;
  void clear_expression();
  const std::string& expression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_expression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_expression();
  PROTOBUF_MUST_USE_RESULT std::string* release_expression();
  void set_allocated_expression(std::string* expression);
  private:
  const std::string& _internal_expression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_expression(const std::string& value);
  std::string* _internal_mutable_expression();
  public:

  // string value = 2;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_MUST_USE_RESULT std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.ExpressionValuePair)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr expression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ExpressionValuePair_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ExpressionValuePair

// string expression = 1;
inline void ExpressionValuePair::clear_expression() {
  expression_.ClearToEmpty();
}
inline const std::string& ExpressionValuePair::expression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ExpressionValuePair.expression)
  return _internal_expression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExpressionValuePair::set_expression(ArgT0&& arg0, ArgT... args) {
 
 expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.ExpressionValuePair.expression)
}
inline std::string* ExpressionValuePair::mutable_expression() {
  std::string* _s = _internal_mutable_expression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ExpressionValuePair.expression)
  return _s;
}
inline const std::string& ExpressionValuePair::_internal_expression() const {
  return expression_.Get();
}
inline void ExpressionValuePair::_internal_set_expression(const std::string& value) {
  
  expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExpressionValuePair::_internal_mutable_expression() {
  
  return expression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExpressionValuePair::release_expression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ExpressionValuePair.expression)
  return expression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExpressionValuePair::set_allocated_expression(std::string* expression) {
  if (expression != nullptr) {
    
  } else {
    
  }
  expression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), expression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ExpressionValuePair.expression)
}

// string value = 2;
inline void ExpressionValuePair::clear_value() {
  value_.ClearToEmpty();
}
inline const std::string& ExpressionValuePair::value() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ExpressionValuePair.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExpressionValuePair::set_value(ArgT0&& arg0, ArgT... args) {
 
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.ExpressionValuePair.value)
}
inline std::string* ExpressionValuePair::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ExpressionValuePair.value)
  return _s;
}
inline const std::string& ExpressionValuePair::_internal_value() const {
  return value_.Get();
}
inline void ExpressionValuePair::_internal_set_value(const std::string& value) {
  
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExpressionValuePair::_internal_mutable_value() {
  
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExpressionValuePair::release_value() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ExpressionValuePair.value)
  return value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExpressionValuePair::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ExpressionValuePair.value)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ExpressionValuePair_2eproto
