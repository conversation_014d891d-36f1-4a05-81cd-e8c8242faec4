// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FolderRef.proto

#include "FolderRef.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr FolderRefData::FolderRefData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : param_data_()
  , ref_comps_data_()
  , associate_list_data_()
  , folder_msg_(nullptr)
  , file_data_(nullptr)
  , place_rule_custom_data_(nullptr)
  , mat_model_depend_files_(nullptr){}
struct FolderRefDataDefaultTypeInternal {
  constexpr FolderRefDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FolderRefDataDefaultTypeInternal() {}
  union {
    FolderRefData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FolderRefDataDefaultTypeInternal _FolderRefData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FolderRef_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_FolderRef_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FolderRef_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FolderRef_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, folder_msg_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, param_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, ref_comps_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, file_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, place_rule_custom_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, mat_model_depend_files_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::FolderRefData, associate_list_data_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::FolderRefData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_FolderRefData_default_instance_),
};

const char descriptor_table_protodef_FolderRef_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\017FolderRef.proto\022\026catalog_studio_messag"
  "e\032\034CatalogFolderTableData.proto\032\023Paramet"
  "erData.proto\032\037RefFileMultiComponentItem."
  "proto\032\023ComponentFile.proto\032\030RefPlaceRule"
  "Custom.proto\032\020DependFile.proto\032\023Associat"
  "eData.proto\"\352\003\n\rFolderRefData\022>\n\nfolder_"
  "msg\030\001 \001(\0132*.catalog_studio_message.Folde"
  "rTableDataMsg\0229\n\nparam_data\030\002 \003(\0132%.cata"
  "log_studio_message.ParameterData\022D\n\016ref_"
  "comps_data\030\003 \003(\0132,.catalog_studio_messag"
  "e.RefMultiCompDataItem\022<\n\tfile_data\030\004 \001("
  "\0132).catalog_studio_message.ComponentFile"
  "Data\022J\n\026place_rule_custom_data\030\005 \001(\0132*.c"
  "atalog_studio_message.RefPlaceRuleCustom"
  "\022F\n\026mat_model_depend_files\030\006 \001(\0132&.catal"
  "og_studio_message.DependFileData\022F\n\023asso"
  "ciate_list_data\030\007 \003(\0132).catalog_studio_m"
  "essage.AssociateListDatab\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FolderRef_2eproto_deps[7] = {
  &::descriptor_table_AssociateData_2eproto,
  &::descriptor_table_CatalogFolderTableData_2eproto,
  &::descriptor_table_ComponentFile_2eproto,
  &::descriptor_table_DependFile_2eproto,
  &::descriptor_table_ParameterData_2eproto,
  &::descriptor_table_RefFileMultiComponentItem_2eproto,
  &::descriptor_table_RefPlaceRuleCustom_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FolderRef_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FolderRef_2eproto = {
  false, false, 712, descriptor_table_protodef_FolderRef_2eproto, "FolderRef.proto", 
  &descriptor_table_FolderRef_2eproto_once, descriptor_table_FolderRef_2eproto_deps, 7, 1,
  schemas, file_default_instances, TableStruct_FolderRef_2eproto::offsets,
  file_level_metadata_FolderRef_2eproto, file_level_enum_descriptors_FolderRef_2eproto, file_level_service_descriptors_FolderRef_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_FolderRef_2eproto_getter() {
  return &descriptor_table_FolderRef_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_FolderRef_2eproto(&descriptor_table_FolderRef_2eproto);
namespace catalog_studio_message {

// ===================================================================

class FolderRefData::_Internal {
 public:
  static const ::catalog_studio_message::FolderTableDataMsg& folder_msg(const FolderRefData* msg);
  static const ::catalog_studio_message::ComponentFileData& file_data(const FolderRefData* msg);
  static const ::catalog_studio_message::RefPlaceRuleCustom& place_rule_custom_data(const FolderRefData* msg);
  static const ::catalog_studio_message::DependFileData& mat_model_depend_files(const FolderRefData* msg);
};

const ::catalog_studio_message::FolderTableDataMsg&
FolderRefData::_Internal::folder_msg(const FolderRefData* msg) {
  return *msg->folder_msg_;
}
const ::catalog_studio_message::ComponentFileData&
FolderRefData::_Internal::file_data(const FolderRefData* msg) {
  return *msg->file_data_;
}
const ::catalog_studio_message::RefPlaceRuleCustom&
FolderRefData::_Internal::place_rule_custom_data(const FolderRefData* msg) {
  return *msg->place_rule_custom_data_;
}
const ::catalog_studio_message::DependFileData&
FolderRefData::_Internal::mat_model_depend_files(const FolderRefData* msg) {
  return *msg->mat_model_depend_files_;
}
void FolderRefData::clear_folder_msg() {
  if (GetArenaForAllocation() == nullptr && folder_msg_ != nullptr) {
    delete folder_msg_;
  }
  folder_msg_ = nullptr;
}
void FolderRefData::clear_param_data() {
  param_data_.Clear();
}
void FolderRefData::clear_ref_comps_data() {
  ref_comps_data_.Clear();
}
void FolderRefData::clear_file_data() {
  if (GetArenaForAllocation() == nullptr && file_data_ != nullptr) {
    delete file_data_;
  }
  file_data_ = nullptr;
}
void FolderRefData::clear_place_rule_custom_data() {
  if (GetArenaForAllocation() == nullptr && place_rule_custom_data_ != nullptr) {
    delete place_rule_custom_data_;
  }
  place_rule_custom_data_ = nullptr;
}
void FolderRefData::clear_mat_model_depend_files() {
  if (GetArenaForAllocation() == nullptr && mat_model_depend_files_ != nullptr) {
    delete mat_model_depend_files_;
  }
  mat_model_depend_files_ = nullptr;
}
void FolderRefData::clear_associate_list_data() {
  associate_list_data_.Clear();
}
FolderRefData::FolderRefData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  param_data_(arena),
  ref_comps_data_(arena),
  associate_list_data_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.FolderRefData)
}
FolderRefData::FolderRefData(const FolderRefData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      param_data_(from.param_data_),
      ref_comps_data_(from.ref_comps_data_),
      associate_list_data_(from.associate_list_data_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_folder_msg()) {
    folder_msg_ = new ::catalog_studio_message::FolderTableDataMsg(*from.folder_msg_);
  } else {
    folder_msg_ = nullptr;
  }
  if (from._internal_has_file_data()) {
    file_data_ = new ::catalog_studio_message::ComponentFileData(*from.file_data_);
  } else {
    file_data_ = nullptr;
  }
  if (from._internal_has_place_rule_custom_data()) {
    place_rule_custom_data_ = new ::catalog_studio_message::RefPlaceRuleCustom(*from.place_rule_custom_data_);
  } else {
    place_rule_custom_data_ = nullptr;
  }
  if (from._internal_has_mat_model_depend_files()) {
    mat_model_depend_files_ = new ::catalog_studio_message::DependFileData(*from.mat_model_depend_files_);
  } else {
    mat_model_depend_files_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.FolderRefData)
}

void FolderRefData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&folder_msg_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&mat_model_depend_files_) -
    reinterpret_cast<char*>(&folder_msg_)) + sizeof(mat_model_depend_files_));
}

FolderRefData::~FolderRefData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.FolderRefData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FolderRefData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete folder_msg_;
  if (this != internal_default_instance()) delete file_data_;
  if (this != internal_default_instance()) delete place_rule_custom_data_;
  if (this != internal_default_instance()) delete mat_model_depend_files_;
}

void FolderRefData::ArenaDtor(void* object) {
  FolderRefData* _this = reinterpret_cast< FolderRefData* >(object);
  (void)_this;
}
void FolderRefData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FolderRefData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FolderRefData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.FolderRefData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  param_data_.Clear();
  ref_comps_data_.Clear();
  associate_list_data_.Clear();
  if (GetArenaForAllocation() == nullptr && folder_msg_ != nullptr) {
    delete folder_msg_;
  }
  folder_msg_ = nullptr;
  if (GetArenaForAllocation() == nullptr && file_data_ != nullptr) {
    delete file_data_;
  }
  file_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && place_rule_custom_data_ != nullptr) {
    delete place_rule_custom_data_;
  }
  place_rule_custom_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && mat_model_depend_files_ != nullptr) {
    delete mat_model_depend_files_;
  }
  mat_model_depend_files_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FolderRefData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.FolderTableDataMsg folder_msg = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_folder_msg(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ParameterData param_data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_param_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.RefMultiCompDataItem ref_comps_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ref_comps_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ComponentFileData file_data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_file_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.RefPlaceRuleCustom place_rule_custom_data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_place_rule_custom_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.DependFileData mat_model_depend_files = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_mat_model_depend_files(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.AssociateListData associate_list_data = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_associate_list_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* FolderRefData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.FolderRefData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.FolderTableDataMsg folder_msg = 1;
  if (this->_internal_has_folder_msg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::folder_msg(this), target, stream);
  }

  // repeated .catalog_studio_message.ParameterData param_data = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_param_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_param_data(i), target, stream);
  }

  // repeated .catalog_studio_message.RefMultiCompDataItem ref_comps_data = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ref_comps_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_ref_comps_data(i), target, stream);
  }

  // .catalog_studio_message.ComponentFileData file_data = 4;
  if (this->_internal_has_file_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::file_data(this), target, stream);
  }

  // .catalog_studio_message.RefPlaceRuleCustom place_rule_custom_data = 5;
  if (this->_internal_has_place_rule_custom_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::place_rule_custom_data(this), target, stream);
  }

  // .catalog_studio_message.DependFileData mat_model_depend_files = 6;
  if (this->_internal_has_mat_model_depend_files()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::mat_model_depend_files(this), target, stream);
  }

  // repeated .catalog_studio_message.AssociateListData associate_list_data = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_associate_list_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_associate_list_data(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.FolderRefData)
  return target;
}

size_t FolderRefData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.FolderRefData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.ParameterData param_data = 2;
  total_size += 1UL * this->_internal_param_data_size();
  for (const auto& msg : this->param_data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.RefMultiCompDataItem ref_comps_data = 3;
  total_size += 1UL * this->_internal_ref_comps_data_size();
  for (const auto& msg : this->ref_comps_data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.AssociateListData associate_list_data = 7;
  total_size += 1UL * this->_internal_associate_list_data_size();
  for (const auto& msg : this->associate_list_data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .catalog_studio_message.FolderTableDataMsg folder_msg = 1;
  if (this->_internal_has_folder_msg()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *folder_msg_);
  }

  // .catalog_studio_message.ComponentFileData file_data = 4;
  if (this->_internal_has_file_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *file_data_);
  }

  // .catalog_studio_message.RefPlaceRuleCustom place_rule_custom_data = 5;
  if (this->_internal_has_place_rule_custom_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *place_rule_custom_data_);
  }

  // .catalog_studio_message.DependFileData mat_model_depend_files = 6;
  if (this->_internal_has_mat_model_depend_files()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *mat_model_depend_files_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FolderRefData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FolderRefData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FolderRefData::GetClassData() const { return &_class_data_; }

void FolderRefData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FolderRefData *>(to)->MergeFrom(
      static_cast<const FolderRefData &>(from));
}


void FolderRefData::MergeFrom(const FolderRefData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.FolderRefData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  param_data_.MergeFrom(from.param_data_);
  ref_comps_data_.MergeFrom(from.ref_comps_data_);
  associate_list_data_.MergeFrom(from.associate_list_data_);
  if (from._internal_has_folder_msg()) {
    _internal_mutable_folder_msg()->::catalog_studio_message::FolderTableDataMsg::MergeFrom(from._internal_folder_msg());
  }
  if (from._internal_has_file_data()) {
    _internal_mutable_file_data()->::catalog_studio_message::ComponentFileData::MergeFrom(from._internal_file_data());
  }
  if (from._internal_has_place_rule_custom_data()) {
    _internal_mutable_place_rule_custom_data()->::catalog_studio_message::RefPlaceRuleCustom::MergeFrom(from._internal_place_rule_custom_data());
  }
  if (from._internal_has_mat_model_depend_files()) {
    _internal_mutable_mat_model_depend_files()->::catalog_studio_message::DependFileData::MergeFrom(from._internal_mat_model_depend_files());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FolderRefData::CopyFrom(const FolderRefData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.FolderRefData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FolderRefData::IsInitialized() const {
  return true;
}

void FolderRefData::InternalSwap(FolderRefData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  param_data_.InternalSwap(&other->param_data_);
  ref_comps_data_.InternalSwap(&other->ref_comps_data_);
  associate_list_data_.InternalSwap(&other->associate_list_data_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FolderRefData, mat_model_depend_files_)
      + sizeof(FolderRefData::mat_model_depend_files_)
      - PROTOBUF_FIELD_OFFSET(FolderRefData, folder_msg_)>(
          reinterpret_cast<char*>(&folder_msg_),
          reinterpret_cast<char*>(&other->folder_msg_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FolderRefData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_FolderRef_2eproto_getter, &descriptor_table_FolderRef_2eproto_once,
      file_level_metadata_FolderRef_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::FolderRefData* Arena::CreateMaybeMessage< ::catalog_studio_message::FolderRefData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::FolderRefData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
