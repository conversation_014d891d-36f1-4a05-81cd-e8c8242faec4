// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FolderRef.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_FolderRef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_FolderRef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "CatalogFolderTableData.pb.h"
#include "ParameterData.pb.h"
#include "RefFileMultiComponentItem.pb.h"
#include "ComponentFile.pb.h"
#include "RefPlaceRuleCustom.pb.h"
#include "DependFile.pb.h"
#include "AssociateData.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_FolderRef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_FolderRef_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FolderRef_2eproto;
namespace catalog_studio_message {
class FolderRefData;
struct FolderRefDataDefaultTypeInternal;
extern FolderRefDataDefaultTypeInternal _FolderRefData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::FolderRefData* Arena::CreateMaybeMessage<::catalog_studio_message::FolderRefData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class FolderRefData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.FolderRefData) */ {
 public:
  inline FolderRefData() : FolderRefData(nullptr) {}
  ~FolderRefData() override;
  explicit constexpr FolderRefData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FolderRefData(const FolderRefData& from);
  FolderRefData(FolderRefData&& from) noexcept
    : FolderRefData() {
    *this = ::std::move(from);
  }

  inline FolderRefData& operator=(const FolderRefData& from) {
    CopyFrom(from);
    return *this;
  }
  inline FolderRefData& operator=(FolderRefData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FolderRefData& default_instance() {
    return *internal_default_instance();
  }
  static inline const FolderRefData* internal_default_instance() {
    return reinterpret_cast<const FolderRefData*>(
               &_FolderRefData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FolderRefData& a, FolderRefData& b) {
    a.Swap(&b);
  }
  inline void Swap(FolderRefData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FolderRefData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline FolderRefData* New() const final {
    return new FolderRefData();
  }

  FolderRefData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<FolderRefData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FolderRefData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FolderRefData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FolderRefData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.FolderRefData";
  }
  protected:
  explicit FolderRefData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kParamDataFieldNumber = 2,
    kRefCompsDataFieldNumber = 3,
    kAssociateListDataFieldNumber = 7,
    kFolderMsgFieldNumber = 1,
    kFileDataFieldNumber = 4,
    kPlaceRuleCustomDataFieldNumber = 5,
    kMatModelDependFilesFieldNumber = 6,
  };
  // repeated .catalog_studio_message.ParameterData param_data = 2;
  int param_data_size() const;
  private:
  int _internal_param_data_size() const;
  public:
  void clear_param_data();
  ::catalog_studio_message::ParameterData* mutable_param_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
      mutable_param_data();
  private:
  const ::catalog_studio_message::ParameterData& _internal_param_data(int index) const;
  ::catalog_studio_message::ParameterData* _internal_add_param_data();
  public:
  const ::catalog_studio_message::ParameterData& param_data(int index) const;
  ::catalog_studio_message::ParameterData* add_param_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
      param_data() const;

  // repeated .catalog_studio_message.RefMultiCompDataItem ref_comps_data = 3;
  int ref_comps_data_size() const;
  private:
  int _internal_ref_comps_data_size() const;
  public:
  void clear_ref_comps_data();
  ::catalog_studio_message::RefMultiCompDataItem* mutable_ref_comps_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefMultiCompDataItem >*
      mutable_ref_comps_data();
  private:
  const ::catalog_studio_message::RefMultiCompDataItem& _internal_ref_comps_data(int index) const;
  ::catalog_studio_message::RefMultiCompDataItem* _internal_add_ref_comps_data();
  public:
  const ::catalog_studio_message::RefMultiCompDataItem& ref_comps_data(int index) const;
  ::catalog_studio_message::RefMultiCompDataItem* add_ref_comps_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefMultiCompDataItem >&
      ref_comps_data() const;

  // repeated .catalog_studio_message.AssociateListData associate_list_data = 7;
  int associate_list_data_size() const;
  private:
  int _internal_associate_list_data_size() const;
  public:
  void clear_associate_list_data();
  ::catalog_studio_message::AssociateListData* mutable_associate_list_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateListData >*
      mutable_associate_list_data();
  private:
  const ::catalog_studio_message::AssociateListData& _internal_associate_list_data(int index) const;
  ::catalog_studio_message::AssociateListData* _internal_add_associate_list_data();
  public:
  const ::catalog_studio_message::AssociateListData& associate_list_data(int index) const;
  ::catalog_studio_message::AssociateListData* add_associate_list_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateListData >&
      associate_list_data() const;

  // .catalog_studio_message.FolderTableDataMsg folder_msg = 1;
  bool has_folder_msg() const;
  private:
  bool _internal_has_folder_msg() const;
  public:
  void clear_folder_msg();
  const ::catalog_studio_message::FolderTableDataMsg& folder_msg() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::FolderTableDataMsg* release_folder_msg();
  ::catalog_studio_message::FolderTableDataMsg* mutable_folder_msg();
  void set_allocated_folder_msg(::catalog_studio_message::FolderTableDataMsg* folder_msg);
  private:
  const ::catalog_studio_message::FolderTableDataMsg& _internal_folder_msg() const;
  ::catalog_studio_message::FolderTableDataMsg* _internal_mutable_folder_msg();
  public:
  void unsafe_arena_set_allocated_folder_msg(
      ::catalog_studio_message::FolderTableDataMsg* folder_msg);
  ::catalog_studio_message::FolderTableDataMsg* unsafe_arena_release_folder_msg();

  // .catalog_studio_message.ComponentFileData file_data = 4;
  bool has_file_data() const;
  private:
  bool _internal_has_file_data() const;
  public:
  void clear_file_data();
  const ::catalog_studio_message::ComponentFileData& file_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ComponentFileData* release_file_data();
  ::catalog_studio_message::ComponentFileData* mutable_file_data();
  void set_allocated_file_data(::catalog_studio_message::ComponentFileData* file_data);
  private:
  const ::catalog_studio_message::ComponentFileData& _internal_file_data() const;
  ::catalog_studio_message::ComponentFileData* _internal_mutable_file_data();
  public:
  void unsafe_arena_set_allocated_file_data(
      ::catalog_studio_message::ComponentFileData* file_data);
  ::catalog_studio_message::ComponentFileData* unsafe_arena_release_file_data();

  // .catalog_studio_message.RefPlaceRuleCustom place_rule_custom_data = 5;
  bool has_place_rule_custom_data() const;
  private:
  bool _internal_has_place_rule_custom_data() const;
  public:
  void clear_place_rule_custom_data();
  const ::catalog_studio_message::RefPlaceRuleCustom& place_rule_custom_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::RefPlaceRuleCustom* release_place_rule_custom_data();
  ::catalog_studio_message::RefPlaceRuleCustom* mutable_place_rule_custom_data();
  void set_allocated_place_rule_custom_data(::catalog_studio_message::RefPlaceRuleCustom* place_rule_custom_data);
  private:
  const ::catalog_studio_message::RefPlaceRuleCustom& _internal_place_rule_custom_data() const;
  ::catalog_studio_message::RefPlaceRuleCustom* _internal_mutable_place_rule_custom_data();
  public:
  void unsafe_arena_set_allocated_place_rule_custom_data(
      ::catalog_studio_message::RefPlaceRuleCustom* place_rule_custom_data);
  ::catalog_studio_message::RefPlaceRuleCustom* unsafe_arena_release_place_rule_custom_data();

  // .catalog_studio_message.DependFileData mat_model_depend_files = 6;
  bool has_mat_model_depend_files() const;
  private:
  bool _internal_has_mat_model_depend_files() const;
  public:
  void clear_mat_model_depend_files();
  const ::catalog_studio_message::DependFileData& mat_model_depend_files() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::DependFileData* release_mat_model_depend_files();
  ::catalog_studio_message::DependFileData* mutable_mat_model_depend_files();
  void set_allocated_mat_model_depend_files(::catalog_studio_message::DependFileData* mat_model_depend_files);
  private:
  const ::catalog_studio_message::DependFileData& _internal_mat_model_depend_files() const;
  ::catalog_studio_message::DependFileData* _internal_mutable_mat_model_depend_files();
  public:
  void unsafe_arena_set_allocated_mat_model_depend_files(
      ::catalog_studio_message::DependFileData* mat_model_depend_files);
  ::catalog_studio_message::DependFileData* unsafe_arena_release_mat_model_depend_files();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.FolderRefData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData > param_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefMultiCompDataItem > ref_comps_data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateListData > associate_list_data_;
  ::catalog_studio_message::FolderTableDataMsg* folder_msg_;
  ::catalog_studio_message::ComponentFileData* file_data_;
  ::catalog_studio_message::RefPlaceRuleCustom* place_rule_custom_data_;
  ::catalog_studio_message::DependFileData* mat_model_depend_files_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_FolderRef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FolderRefData

// .catalog_studio_message.FolderTableDataMsg folder_msg = 1;
inline bool FolderRefData::_internal_has_folder_msg() const {
  return this != internal_default_instance() && folder_msg_ != nullptr;
}
inline bool FolderRefData::has_folder_msg() const {
  return _internal_has_folder_msg();
}
inline const ::catalog_studio_message::FolderTableDataMsg& FolderRefData::_internal_folder_msg() const {
  const ::catalog_studio_message::FolderTableDataMsg* p = folder_msg_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::FolderTableDataMsg&>(
      ::catalog_studio_message::_FolderTableDataMsg_default_instance_);
}
inline const ::catalog_studio_message::FolderTableDataMsg& FolderRefData::folder_msg() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.folder_msg)
  return _internal_folder_msg();
}
inline void FolderRefData::unsafe_arena_set_allocated_folder_msg(
    ::catalog_studio_message::FolderTableDataMsg* folder_msg) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(folder_msg_);
  }
  folder_msg_ = folder_msg;
  if (folder_msg) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.FolderRefData.folder_msg)
}
inline ::catalog_studio_message::FolderTableDataMsg* FolderRefData::release_folder_msg() {
  
  ::catalog_studio_message::FolderTableDataMsg* temp = folder_msg_;
  folder_msg_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::FolderTableDataMsg* FolderRefData::unsafe_arena_release_folder_msg() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderRefData.folder_msg)
  
  ::catalog_studio_message::FolderTableDataMsg* temp = folder_msg_;
  folder_msg_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::FolderTableDataMsg* FolderRefData::_internal_mutable_folder_msg() {
  
  if (folder_msg_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::FolderTableDataMsg>(GetArenaForAllocation());
    folder_msg_ = p;
  }
  return folder_msg_;
}
inline ::catalog_studio_message::FolderTableDataMsg* FolderRefData::mutable_folder_msg() {
  ::catalog_studio_message::FolderTableDataMsg* _msg = _internal_mutable_folder_msg();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.folder_msg)
  return _msg;
}
inline void FolderRefData::set_allocated_folder_msg(::catalog_studio_message::FolderTableDataMsg* folder_msg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(folder_msg_);
  }
  if (folder_msg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(folder_msg));
    if (message_arena != submessage_arena) {
      folder_msg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, folder_msg, submessage_arena);
    }
    
  } else {
    
  }
  folder_msg_ = folder_msg;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderRefData.folder_msg)
}

// repeated .catalog_studio_message.ParameterData param_data = 2;
inline int FolderRefData::_internal_param_data_size() const {
  return param_data_.size();
}
inline int FolderRefData::param_data_size() const {
  return _internal_param_data_size();
}
inline ::catalog_studio_message::ParameterData* FolderRefData::mutable_param_data(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.param_data)
  return param_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
FolderRefData::mutable_param_data() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.FolderRefData.param_data)
  return &param_data_;
}
inline const ::catalog_studio_message::ParameterData& FolderRefData::_internal_param_data(int index) const {
  return param_data_.Get(index);
}
inline const ::catalog_studio_message::ParameterData& FolderRefData::param_data(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.param_data)
  return _internal_param_data(index);
}
inline ::catalog_studio_message::ParameterData* FolderRefData::_internal_add_param_data() {
  return param_data_.Add();
}
inline ::catalog_studio_message::ParameterData* FolderRefData::add_param_data() {
  ::catalog_studio_message::ParameterData* _add = _internal_add_param_data();
  // @@protoc_insertion_point(field_add:catalog_studio_message.FolderRefData.param_data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
FolderRefData::param_data() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.FolderRefData.param_data)
  return param_data_;
}

// repeated .catalog_studio_message.RefMultiCompDataItem ref_comps_data = 3;
inline int FolderRefData::_internal_ref_comps_data_size() const {
  return ref_comps_data_.size();
}
inline int FolderRefData::ref_comps_data_size() const {
  return _internal_ref_comps_data_size();
}
inline ::catalog_studio_message::RefMultiCompDataItem* FolderRefData::mutable_ref_comps_data(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.ref_comps_data)
  return ref_comps_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefMultiCompDataItem >*
FolderRefData::mutable_ref_comps_data() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.FolderRefData.ref_comps_data)
  return &ref_comps_data_;
}
inline const ::catalog_studio_message::RefMultiCompDataItem& FolderRefData::_internal_ref_comps_data(int index) const {
  return ref_comps_data_.Get(index);
}
inline const ::catalog_studio_message::RefMultiCompDataItem& FolderRefData::ref_comps_data(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.ref_comps_data)
  return _internal_ref_comps_data(index);
}
inline ::catalog_studio_message::RefMultiCompDataItem* FolderRefData::_internal_add_ref_comps_data() {
  return ref_comps_data_.Add();
}
inline ::catalog_studio_message::RefMultiCompDataItem* FolderRefData::add_ref_comps_data() {
  ::catalog_studio_message::RefMultiCompDataItem* _add = _internal_add_ref_comps_data();
  // @@protoc_insertion_point(field_add:catalog_studio_message.FolderRefData.ref_comps_data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefMultiCompDataItem >&
FolderRefData::ref_comps_data() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.FolderRefData.ref_comps_data)
  return ref_comps_data_;
}

// .catalog_studio_message.ComponentFileData file_data = 4;
inline bool FolderRefData::_internal_has_file_data() const {
  return this != internal_default_instance() && file_data_ != nullptr;
}
inline bool FolderRefData::has_file_data() const {
  return _internal_has_file_data();
}
inline const ::catalog_studio_message::ComponentFileData& FolderRefData::_internal_file_data() const {
  const ::catalog_studio_message::ComponentFileData* p = file_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ComponentFileData&>(
      ::catalog_studio_message::_ComponentFileData_default_instance_);
}
inline const ::catalog_studio_message::ComponentFileData& FolderRefData::file_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.file_data)
  return _internal_file_data();
}
inline void FolderRefData::unsafe_arena_set_allocated_file_data(
    ::catalog_studio_message::ComponentFileData* file_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_data_);
  }
  file_data_ = file_data;
  if (file_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.FolderRefData.file_data)
}
inline ::catalog_studio_message::ComponentFileData* FolderRefData::release_file_data() {
  
  ::catalog_studio_message::ComponentFileData* temp = file_data_;
  file_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ComponentFileData* FolderRefData::unsafe_arena_release_file_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderRefData.file_data)
  
  ::catalog_studio_message::ComponentFileData* temp = file_data_;
  file_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ComponentFileData* FolderRefData::_internal_mutable_file_data() {
  
  if (file_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ComponentFileData>(GetArenaForAllocation());
    file_data_ = p;
  }
  return file_data_;
}
inline ::catalog_studio_message::ComponentFileData* FolderRefData::mutable_file_data() {
  ::catalog_studio_message::ComponentFileData* _msg = _internal_mutable_file_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.file_data)
  return _msg;
}
inline void FolderRefData::set_allocated_file_data(::catalog_studio_message::ComponentFileData* file_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_data_);
  }
  if (file_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_data));
    if (message_arena != submessage_arena) {
      file_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, file_data, submessage_arena);
    }
    
  } else {
    
  }
  file_data_ = file_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderRefData.file_data)
}

// .catalog_studio_message.RefPlaceRuleCustom place_rule_custom_data = 5;
inline bool FolderRefData::_internal_has_place_rule_custom_data() const {
  return this != internal_default_instance() && place_rule_custom_data_ != nullptr;
}
inline bool FolderRefData::has_place_rule_custom_data() const {
  return _internal_has_place_rule_custom_data();
}
inline const ::catalog_studio_message::RefPlaceRuleCustom& FolderRefData::_internal_place_rule_custom_data() const {
  const ::catalog_studio_message::RefPlaceRuleCustom* p = place_rule_custom_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::RefPlaceRuleCustom&>(
      ::catalog_studio_message::_RefPlaceRuleCustom_default_instance_);
}
inline const ::catalog_studio_message::RefPlaceRuleCustom& FolderRefData::place_rule_custom_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.place_rule_custom_data)
  return _internal_place_rule_custom_data();
}
inline void FolderRefData::unsafe_arena_set_allocated_place_rule_custom_data(
    ::catalog_studio_message::RefPlaceRuleCustom* place_rule_custom_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(place_rule_custom_data_);
  }
  place_rule_custom_data_ = place_rule_custom_data;
  if (place_rule_custom_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.FolderRefData.place_rule_custom_data)
}
inline ::catalog_studio_message::RefPlaceRuleCustom* FolderRefData::release_place_rule_custom_data() {
  
  ::catalog_studio_message::RefPlaceRuleCustom* temp = place_rule_custom_data_;
  place_rule_custom_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::RefPlaceRuleCustom* FolderRefData::unsafe_arena_release_place_rule_custom_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderRefData.place_rule_custom_data)
  
  ::catalog_studio_message::RefPlaceRuleCustom* temp = place_rule_custom_data_;
  place_rule_custom_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::RefPlaceRuleCustom* FolderRefData::_internal_mutable_place_rule_custom_data() {
  
  if (place_rule_custom_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::RefPlaceRuleCustom>(GetArenaForAllocation());
    place_rule_custom_data_ = p;
  }
  return place_rule_custom_data_;
}
inline ::catalog_studio_message::RefPlaceRuleCustom* FolderRefData::mutable_place_rule_custom_data() {
  ::catalog_studio_message::RefPlaceRuleCustom* _msg = _internal_mutable_place_rule_custom_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.place_rule_custom_data)
  return _msg;
}
inline void FolderRefData::set_allocated_place_rule_custom_data(::catalog_studio_message::RefPlaceRuleCustom* place_rule_custom_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(place_rule_custom_data_);
  }
  if (place_rule_custom_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(place_rule_custom_data));
    if (message_arena != submessage_arena) {
      place_rule_custom_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, place_rule_custom_data, submessage_arena);
    }
    
  } else {
    
  }
  place_rule_custom_data_ = place_rule_custom_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderRefData.place_rule_custom_data)
}

// .catalog_studio_message.DependFileData mat_model_depend_files = 6;
inline bool FolderRefData::_internal_has_mat_model_depend_files() const {
  return this != internal_default_instance() && mat_model_depend_files_ != nullptr;
}
inline bool FolderRefData::has_mat_model_depend_files() const {
  return _internal_has_mat_model_depend_files();
}
inline const ::catalog_studio_message::DependFileData& FolderRefData::_internal_mat_model_depend_files() const {
  const ::catalog_studio_message::DependFileData* p = mat_model_depend_files_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::DependFileData&>(
      ::catalog_studio_message::_DependFileData_default_instance_);
}
inline const ::catalog_studio_message::DependFileData& FolderRefData::mat_model_depend_files() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.mat_model_depend_files)
  return _internal_mat_model_depend_files();
}
inline void FolderRefData::unsafe_arena_set_allocated_mat_model_depend_files(
    ::catalog_studio_message::DependFileData* mat_model_depend_files) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(mat_model_depend_files_);
  }
  mat_model_depend_files_ = mat_model_depend_files;
  if (mat_model_depend_files) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.FolderRefData.mat_model_depend_files)
}
inline ::catalog_studio_message::DependFileData* FolderRefData::release_mat_model_depend_files() {
  
  ::catalog_studio_message::DependFileData* temp = mat_model_depend_files_;
  mat_model_depend_files_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::DependFileData* FolderRefData::unsafe_arena_release_mat_model_depend_files() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.FolderRefData.mat_model_depend_files)
  
  ::catalog_studio_message::DependFileData* temp = mat_model_depend_files_;
  mat_model_depend_files_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::DependFileData* FolderRefData::_internal_mutable_mat_model_depend_files() {
  
  if (mat_model_depend_files_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::DependFileData>(GetArenaForAllocation());
    mat_model_depend_files_ = p;
  }
  return mat_model_depend_files_;
}
inline ::catalog_studio_message::DependFileData* FolderRefData::mutable_mat_model_depend_files() {
  ::catalog_studio_message::DependFileData* _msg = _internal_mutable_mat_model_depend_files();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.mat_model_depend_files)
  return _msg;
}
inline void FolderRefData::set_allocated_mat_model_depend_files(::catalog_studio_message::DependFileData* mat_model_depend_files) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(mat_model_depend_files_);
  }
  if (mat_model_depend_files) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(mat_model_depend_files));
    if (message_arena != submessage_arena) {
      mat_model_depend_files = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, mat_model_depend_files, submessage_arena);
    }
    
  } else {
    
  }
  mat_model_depend_files_ = mat_model_depend_files;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.FolderRefData.mat_model_depend_files)
}

// repeated .catalog_studio_message.AssociateListData associate_list_data = 7;
inline int FolderRefData::_internal_associate_list_data_size() const {
  return associate_list_data_.size();
}
inline int FolderRefData::associate_list_data_size() const {
  return _internal_associate_list_data_size();
}
inline ::catalog_studio_message::AssociateListData* FolderRefData::mutable_associate_list_data(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.FolderRefData.associate_list_data)
  return associate_list_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateListData >*
FolderRefData::mutable_associate_list_data() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.FolderRefData.associate_list_data)
  return &associate_list_data_;
}
inline const ::catalog_studio_message::AssociateListData& FolderRefData::_internal_associate_list_data(int index) const {
  return associate_list_data_.Get(index);
}
inline const ::catalog_studio_message::AssociateListData& FolderRefData::associate_list_data(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.FolderRefData.associate_list_data)
  return _internal_associate_list_data(index);
}
inline ::catalog_studio_message::AssociateListData* FolderRefData::_internal_add_associate_list_data() {
  return associate_list_data_.Add();
}
inline ::catalog_studio_message::AssociateListData* FolderRefData::add_associate_list_data() {
  ::catalog_studio_message::AssociateListData* _add = _internal_add_associate_list_data();
  // @@protoc_insertion_point(field_add:catalog_studio_message.FolderRefData.associate_list_data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::AssociateListData >&
FolderRefData::associate_list_data() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.FolderRefData.associate_list_data)
  return associate_list_data_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_FolderRef_2eproto
