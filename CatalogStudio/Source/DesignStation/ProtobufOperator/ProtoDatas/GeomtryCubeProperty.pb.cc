// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryCubeProperty.proto

#include "GeomtryCubeProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr GeomtryCubeProperty::GeomtryCubeProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : start_location_x_(nullptr)
  , start_location_y_(nullptr)
  , start_location_z_(nullptr)
  , end_location_x_(nullptr)
  , end_location_y_(nullptr)
  , end_location_z_(nullptr){}
struct GeomtryCubePropertyDefaultTypeInternal {
  constexpr GeomtryCubePropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeomtryCubePropertyDefaultTypeInternal() {}
  union {
    GeomtryCubeProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeomtryCubePropertyDefaultTypeInternal _GeomtryCubeProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GeomtryCubeProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GeomtryCubeProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GeomtryCubeProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GeomtryCubeProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, start_location_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, start_location_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, start_location_z_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, end_location_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, end_location_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryCubeProperty, end_location_z_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::GeomtryCubeProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_GeomtryCubeProperty_default_instance_),
};

const char descriptor_table_protodef_GeomtryCubeProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031GeomtryCubeProperty.proto\022\026catalog_stu"
  "dio_message\032\031ExpressionValuePair.proto\"\271"
  "\003\n\023GeomtryCubeProperty\022E\n\020start_location"
  "_x\030\001 \001(\0132+.catalog_studio_message.Expres"
  "sionValuePair\022E\n\020start_location_y\030\002 \001(\0132"
  "+.catalog_studio_message.ExpressionValue"
  "Pair\022E\n\020start_location_z\030\003 \001(\0132+.catalog"
  "_studio_message.ExpressionValuePair\022C\n\016e"
  "nd_location_x\030\004 \001(\0132+.catalog_studio_mes"
  "sage.ExpressionValuePair\022C\n\016end_location"
  "_y\030\005 \001(\0132+.catalog_studio_message.Expres"
  "sionValuePair\022C\n\016end_location_z\030\006 \001(\0132+."
  "catalog_studio_message.ExpressionValuePa"
  "irb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GeomtryCubeProperty_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GeomtryCubeProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryCubeProperty_2eproto = {
  false, false, 530, descriptor_table_protodef_GeomtryCubeProperty_2eproto, "GeomtryCubeProperty.proto", 
  &descriptor_table_GeomtryCubeProperty_2eproto_once, descriptor_table_GeomtryCubeProperty_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_GeomtryCubeProperty_2eproto::offsets,
  file_level_metadata_GeomtryCubeProperty_2eproto, file_level_enum_descriptors_GeomtryCubeProperty_2eproto, file_level_service_descriptors_GeomtryCubeProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GeomtryCubeProperty_2eproto_getter() {
  return &descriptor_table_GeomtryCubeProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GeomtryCubeProperty_2eproto(&descriptor_table_GeomtryCubeProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class GeomtryCubeProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& start_location_x(const GeomtryCubeProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& start_location_y(const GeomtryCubeProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& start_location_z(const GeomtryCubeProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& end_location_x(const GeomtryCubeProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& end_location_y(const GeomtryCubeProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& end_location_z(const GeomtryCubeProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::start_location_x(const GeomtryCubeProperty* msg) {
  return *msg->start_location_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::start_location_y(const GeomtryCubeProperty* msg) {
  return *msg->start_location_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::start_location_z(const GeomtryCubeProperty* msg) {
  return *msg->start_location_z_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::end_location_x(const GeomtryCubeProperty* msg) {
  return *msg->end_location_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::end_location_y(const GeomtryCubeProperty* msg) {
  return *msg->end_location_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryCubeProperty::_Internal::end_location_z(const GeomtryCubeProperty* msg) {
  return *msg->end_location_z_;
}
void GeomtryCubeProperty::clear_start_location_x() {
  if (GetArenaForAllocation() == nullptr && start_location_x_ != nullptr) {
    delete start_location_x_;
  }
  start_location_x_ = nullptr;
}
void GeomtryCubeProperty::clear_start_location_y() {
  if (GetArenaForAllocation() == nullptr && start_location_y_ != nullptr) {
    delete start_location_y_;
  }
  start_location_y_ = nullptr;
}
void GeomtryCubeProperty::clear_start_location_z() {
  if (GetArenaForAllocation() == nullptr && start_location_z_ != nullptr) {
    delete start_location_z_;
  }
  start_location_z_ = nullptr;
}
void GeomtryCubeProperty::clear_end_location_x() {
  if (GetArenaForAllocation() == nullptr && end_location_x_ != nullptr) {
    delete end_location_x_;
  }
  end_location_x_ = nullptr;
}
void GeomtryCubeProperty::clear_end_location_y() {
  if (GetArenaForAllocation() == nullptr && end_location_y_ != nullptr) {
    delete end_location_y_;
  }
  end_location_y_ = nullptr;
}
void GeomtryCubeProperty::clear_end_location_z() {
  if (GetArenaForAllocation() == nullptr && end_location_z_ != nullptr) {
    delete end_location_z_;
  }
  end_location_z_ = nullptr;
}
GeomtryCubeProperty::GeomtryCubeProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.GeomtryCubeProperty)
}
GeomtryCubeProperty::GeomtryCubeProperty(const GeomtryCubeProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_start_location_x()) {
    start_location_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.start_location_x_);
  } else {
    start_location_x_ = nullptr;
  }
  if (from._internal_has_start_location_y()) {
    start_location_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.start_location_y_);
  } else {
    start_location_y_ = nullptr;
  }
  if (from._internal_has_start_location_z()) {
    start_location_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.start_location_z_);
  } else {
    start_location_z_ = nullptr;
  }
  if (from._internal_has_end_location_x()) {
    end_location_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.end_location_x_);
  } else {
    end_location_x_ = nullptr;
  }
  if (from._internal_has_end_location_y()) {
    end_location_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.end_location_y_);
  } else {
    end_location_y_ = nullptr;
  }
  if (from._internal_has_end_location_z()) {
    end_location_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.end_location_z_);
  } else {
    end_location_z_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.GeomtryCubeProperty)
}

void GeomtryCubeProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_location_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&end_location_z_) -
    reinterpret_cast<char*>(&start_location_x_)) + sizeof(end_location_z_));
}

GeomtryCubeProperty::~GeomtryCubeProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.GeomtryCubeProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeomtryCubeProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete start_location_x_;
  if (this != internal_default_instance()) delete start_location_y_;
  if (this != internal_default_instance()) delete start_location_z_;
  if (this != internal_default_instance()) delete end_location_x_;
  if (this != internal_default_instance()) delete end_location_y_;
  if (this != internal_default_instance()) delete end_location_z_;
}

void GeomtryCubeProperty::ArenaDtor(void* object) {
  GeomtryCubeProperty* _this = reinterpret_cast< GeomtryCubeProperty* >(object);
  (void)_this;
}
void GeomtryCubeProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeomtryCubeProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeomtryCubeProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.GeomtryCubeProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && start_location_x_ != nullptr) {
    delete start_location_x_;
  }
  start_location_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_location_y_ != nullptr) {
    delete start_location_y_;
  }
  start_location_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_location_z_ != nullptr) {
    delete start_location_z_;
  }
  start_location_z_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_location_x_ != nullptr) {
    delete end_location_x_;
  }
  end_location_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_location_y_ != nullptr) {
    delete end_location_y_;
  }
  end_location_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_location_z_ != nullptr) {
    delete end_location_z_;
  }
  end_location_z_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeomtryCubeProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.ExpressionValuePair start_location_x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair start_location_y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair start_location_z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair end_location_x = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair end_location_y = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair end_location_z = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GeomtryCubeProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.GeomtryCubeProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair start_location_x = 1;
  if (this->_internal_has_start_location_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::start_location_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair start_location_y = 2;
  if (this->_internal_has_start_location_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::start_location_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair start_location_z = 3;
  if (this->_internal_has_start_location_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::start_location_z(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_x = 4;
  if (this->_internal_has_end_location_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::end_location_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_y = 5;
  if (this->_internal_has_end_location_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::end_location_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_z = 6;
  if (this->_internal_has_end_location_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::end_location_z(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.GeomtryCubeProperty)
  return target;
}

size_t GeomtryCubeProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.GeomtryCubeProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair start_location_x = 1;
  if (this->_internal_has_start_location_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_location_x_);
  }

  // .catalog_studio_message.ExpressionValuePair start_location_y = 2;
  if (this->_internal_has_start_location_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_location_y_);
  }

  // .catalog_studio_message.ExpressionValuePair start_location_z = 3;
  if (this->_internal_has_start_location_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_location_z_);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_x = 4;
  if (this->_internal_has_end_location_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_location_x_);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_y = 5;
  if (this->_internal_has_end_location_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_location_y_);
  }

  // .catalog_studio_message.ExpressionValuePair end_location_z = 6;
  if (this->_internal_has_end_location_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_location_z_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeomtryCubeProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeomtryCubeProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeomtryCubeProperty::GetClassData() const { return &_class_data_; }

void GeomtryCubeProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeomtryCubeProperty *>(to)->MergeFrom(
      static_cast<const GeomtryCubeProperty &>(from));
}


void GeomtryCubeProperty::MergeFrom(const GeomtryCubeProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.GeomtryCubeProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_start_location_x()) {
    _internal_mutable_start_location_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_start_location_x());
  }
  if (from._internal_has_start_location_y()) {
    _internal_mutable_start_location_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_start_location_y());
  }
  if (from._internal_has_start_location_z()) {
    _internal_mutable_start_location_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_start_location_z());
  }
  if (from._internal_has_end_location_x()) {
    _internal_mutable_end_location_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_end_location_x());
  }
  if (from._internal_has_end_location_y()) {
    _internal_mutable_end_location_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_end_location_y());
  }
  if (from._internal_has_end_location_z()) {
    _internal_mutable_end_location_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_end_location_z());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeomtryCubeProperty::CopyFrom(const GeomtryCubeProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.GeomtryCubeProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeomtryCubeProperty::IsInitialized() const {
  return true;
}

void GeomtryCubeProperty::InternalSwap(GeomtryCubeProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeomtryCubeProperty, end_location_z_)
      + sizeof(GeomtryCubeProperty::end_location_z_)
      - PROTOBUF_FIELD_OFFSET(GeomtryCubeProperty, start_location_x_)>(
          reinterpret_cast<char*>(&start_location_x_),
          reinterpret_cast<char*>(&other->start_location_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeomtryCubeProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GeomtryCubeProperty_2eproto_getter, &descriptor_table_GeomtryCubeProperty_2eproto_once,
      file_level_metadata_GeomtryCubeProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::GeomtryCubeProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::GeomtryCubeProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::GeomtryCubeProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
