// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryCubeProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GeomtryCubeProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GeomtryCubeProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GeomtryCubeProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GeomtryCubeProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryCubeProperty_2eproto;
namespace catalog_studio_message {
class GeomtryCubeProperty;
struct GeomtryCubePropertyDefaultTypeInternal;
extern GeomtryCubePropertyDefaultTypeInternal _GeomtryCubeProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::GeomtryCubeProperty* Arena::CreateMaybeMessage<::catalog_studio_message::GeomtryCubeProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class GeomtryCubeProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.GeomtryCubeProperty) */ {
 public:
  inline GeomtryCubeProperty() : GeomtryCubeProperty(nullptr) {}
  ~GeomtryCubeProperty() override;
  explicit constexpr GeomtryCubeProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeomtryCubeProperty(const GeomtryCubeProperty& from);
  GeomtryCubeProperty(GeomtryCubeProperty&& from) noexcept
    : GeomtryCubeProperty() {
    *this = ::std::move(from);
  }

  inline GeomtryCubeProperty& operator=(const GeomtryCubeProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeomtryCubeProperty& operator=(GeomtryCubeProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeomtryCubeProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeomtryCubeProperty* internal_default_instance() {
    return reinterpret_cast<const GeomtryCubeProperty*>(
               &_GeomtryCubeProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeomtryCubeProperty& a, GeomtryCubeProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(GeomtryCubeProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeomtryCubeProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GeomtryCubeProperty* New() const final {
    return new GeomtryCubeProperty();
  }

  GeomtryCubeProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GeomtryCubeProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeomtryCubeProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeomtryCubeProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeomtryCubeProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.GeomtryCubeProperty";
  }
  protected:
  explicit GeomtryCubeProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartLocationXFieldNumber = 1,
    kStartLocationYFieldNumber = 2,
    kStartLocationZFieldNumber = 3,
    kEndLocationXFieldNumber = 4,
    kEndLocationYFieldNumber = 5,
    kEndLocationZFieldNumber = 6,
  };
  // .catalog_studio_message.ExpressionValuePair start_location_x = 1;
  bool has_start_location_x() const;
  private:
  bool _internal_has_start_location_x() const;
  public:
  void clear_start_location_x();
  const ::catalog_studio_message::ExpressionValuePair& start_location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_x();
  void set_allocated_start_location_x(::catalog_studio_message::ExpressionValuePair* start_location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_x();
  public:
  void unsafe_arena_set_allocated_start_location_x(
      ::catalog_studio_message::ExpressionValuePair* start_location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_x();

  // .catalog_studio_message.ExpressionValuePair start_location_y = 2;
  bool has_start_location_y() const;
  private:
  bool _internal_has_start_location_y() const;
  public:
  void clear_start_location_y();
  const ::catalog_studio_message::ExpressionValuePair& start_location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_y();
  void set_allocated_start_location_y(::catalog_studio_message::ExpressionValuePair* start_location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_y();
  public:
  void unsafe_arena_set_allocated_start_location_y(
      ::catalog_studio_message::ExpressionValuePair* start_location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_y();

  // .catalog_studio_message.ExpressionValuePair start_location_z = 3;
  bool has_start_location_z() const;
  private:
  bool _internal_has_start_location_z() const;
  public:
  void clear_start_location_z();
  const ::catalog_studio_message::ExpressionValuePair& start_location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_z();
  void set_allocated_start_location_z(::catalog_studio_message::ExpressionValuePair* start_location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_z();
  public:
  void unsafe_arena_set_allocated_start_location_z(
      ::catalog_studio_message::ExpressionValuePair* start_location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_z();

  // .catalog_studio_message.ExpressionValuePair end_location_x = 4;
  bool has_end_location_x() const;
  private:
  bool _internal_has_end_location_x() const;
  public:
  void clear_end_location_x();
  const ::catalog_studio_message::ExpressionValuePair& end_location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_x();
  void set_allocated_end_location_x(::catalog_studio_message::ExpressionValuePair* end_location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_x();
  public:
  void unsafe_arena_set_allocated_end_location_x(
      ::catalog_studio_message::ExpressionValuePair* end_location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_x();

  // .catalog_studio_message.ExpressionValuePair end_location_y = 5;
  bool has_end_location_y() const;
  private:
  bool _internal_has_end_location_y() const;
  public:
  void clear_end_location_y();
  const ::catalog_studio_message::ExpressionValuePair& end_location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_y();
  void set_allocated_end_location_y(::catalog_studio_message::ExpressionValuePair* end_location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_y();
  public:
  void unsafe_arena_set_allocated_end_location_y(
      ::catalog_studio_message::ExpressionValuePair* end_location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_y();

  // .catalog_studio_message.ExpressionValuePair end_location_z = 6;
  bool has_end_location_z() const;
  private:
  bool _internal_has_end_location_z() const;
  public:
  void clear_end_location_z();
  const ::catalog_studio_message::ExpressionValuePair& end_location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_z();
  void set_allocated_end_location_z(::catalog_studio_message::ExpressionValuePair* end_location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_z();
  public:
  void unsafe_arena_set_allocated_end_location_z(
      ::catalog_studio_message::ExpressionValuePair* end_location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_z();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.GeomtryCubeProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* start_location_x_;
  ::catalog_studio_message::ExpressionValuePair* start_location_y_;
  ::catalog_studio_message::ExpressionValuePair* start_location_z_;
  ::catalog_studio_message::ExpressionValuePair* end_location_x_;
  ::catalog_studio_message::ExpressionValuePair* end_location_y_;
  ::catalog_studio_message::ExpressionValuePair* end_location_z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GeomtryCubeProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeomtryCubeProperty

// .catalog_studio_message.ExpressionValuePair start_location_x = 1;
inline bool GeomtryCubeProperty::_internal_has_start_location_x() const {
  return this != internal_default_instance() && start_location_x_ != nullptr;
}
inline bool GeomtryCubeProperty::has_start_location_x() const {
  return _internal_has_start_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_start_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::start_location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.start_location_x)
  return _internal_start_location_x();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_start_location_x(
    ::catalog_studio_message::ExpressionValuePair* start_location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x_);
  }
  start_location_x_ = start_location_x;
  if (start_location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_start_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_x_;
  start_location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_start_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.start_location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_x_;
  start_location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_start_location_x() {
  
  if (start_location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_x_ = p;
  }
  return start_location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_start_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.start_location_x)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_start_location_x(::catalog_studio_message::ExpressionValuePair* start_location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x_);
  }
  if (start_location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x));
    if (message_arena != submessage_arena) {
      start_location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_x, submessage_arena);
    }
    
  } else {
    
  }
  start_location_x_ = start_location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_x)
}

// .catalog_studio_message.ExpressionValuePair start_location_y = 2;
inline bool GeomtryCubeProperty::_internal_has_start_location_y() const {
  return this != internal_default_instance() && start_location_y_ != nullptr;
}
inline bool GeomtryCubeProperty::has_start_location_y() const {
  return _internal_has_start_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_start_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::start_location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.start_location_y)
  return _internal_start_location_y();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_start_location_y(
    ::catalog_studio_message::ExpressionValuePair* start_location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y_);
  }
  start_location_y_ = start_location_y;
  if (start_location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_start_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_y_;
  start_location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_start_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.start_location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_y_;
  start_location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_start_location_y() {
  
  if (start_location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_y_ = p;
  }
  return start_location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_start_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.start_location_y)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_start_location_y(::catalog_studio_message::ExpressionValuePair* start_location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y_);
  }
  if (start_location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y));
    if (message_arena != submessage_arena) {
      start_location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_y, submessage_arena);
    }
    
  } else {
    
  }
  start_location_y_ = start_location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_y)
}

// .catalog_studio_message.ExpressionValuePair start_location_z = 3;
inline bool GeomtryCubeProperty::_internal_has_start_location_z() const {
  return this != internal_default_instance() && start_location_z_ != nullptr;
}
inline bool GeomtryCubeProperty::has_start_location_z() const {
  return _internal_has_start_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_start_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::start_location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.start_location_z)
  return _internal_start_location_z();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_start_location_z(
    ::catalog_studio_message::ExpressionValuePair* start_location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z_);
  }
  start_location_z_ = start_location_z;
  if (start_location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_start_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_z_;
  start_location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_start_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.start_location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_z_;
  start_location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_start_location_z() {
  
  if (start_location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_z_ = p;
  }
  return start_location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_start_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.start_location_z)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_start_location_z(::catalog_studio_message::ExpressionValuePair* start_location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z_);
  }
  if (start_location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z));
    if (message_arena != submessage_arena) {
      start_location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_z, submessage_arena);
    }
    
  } else {
    
  }
  start_location_z_ = start_location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.start_location_z)
}

// .catalog_studio_message.ExpressionValuePair end_location_x = 4;
inline bool GeomtryCubeProperty::_internal_has_end_location_x() const {
  return this != internal_default_instance() && end_location_x_ != nullptr;
}
inline bool GeomtryCubeProperty::has_end_location_x() const {
  return _internal_has_end_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_end_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::end_location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.end_location_x)
  return _internal_end_location_x();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_end_location_x(
    ::catalog_studio_message::ExpressionValuePair* end_location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x_);
  }
  end_location_x_ = end_location_x;
  if (end_location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_end_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_x_;
  end_location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_end_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.end_location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_x_;
  end_location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_end_location_x() {
  
  if (end_location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_x_ = p;
  }
  return end_location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_end_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.end_location_x)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_end_location_x(::catalog_studio_message::ExpressionValuePair* end_location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x_);
  }
  if (end_location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x));
    if (message_arena != submessage_arena) {
      end_location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_x, submessage_arena);
    }
    
  } else {
    
  }
  end_location_x_ = end_location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_x)
}

// .catalog_studio_message.ExpressionValuePair end_location_y = 5;
inline bool GeomtryCubeProperty::_internal_has_end_location_y() const {
  return this != internal_default_instance() && end_location_y_ != nullptr;
}
inline bool GeomtryCubeProperty::has_end_location_y() const {
  return _internal_has_end_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_end_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::end_location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.end_location_y)
  return _internal_end_location_y();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_end_location_y(
    ::catalog_studio_message::ExpressionValuePair* end_location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y_);
  }
  end_location_y_ = end_location_y;
  if (end_location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_end_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_y_;
  end_location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_end_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.end_location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_y_;
  end_location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_end_location_y() {
  
  if (end_location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_y_ = p;
  }
  return end_location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_end_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.end_location_y)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_end_location_y(::catalog_studio_message::ExpressionValuePair* end_location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y_);
  }
  if (end_location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y));
    if (message_arena != submessage_arena) {
      end_location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_y, submessage_arena);
    }
    
  } else {
    
  }
  end_location_y_ = end_location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_y)
}

// .catalog_studio_message.ExpressionValuePair end_location_z = 6;
inline bool GeomtryCubeProperty::_internal_has_end_location_z() const {
  return this != internal_default_instance() && end_location_z_ != nullptr;
}
inline bool GeomtryCubeProperty::has_end_location_z() const {
  return _internal_has_end_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::_internal_end_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryCubeProperty::end_location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryCubeProperty.end_location_z)
  return _internal_end_location_z();
}
inline void GeomtryCubeProperty::unsafe_arena_set_allocated_end_location_z(
    ::catalog_studio_message::ExpressionValuePair* end_location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z_);
  }
  end_location_z_ = end_location_z;
  if (end_location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::release_end_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_z_;
  end_location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::unsafe_arena_release_end_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryCubeProperty.end_location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_z_;
  end_location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::_internal_mutable_end_location_z() {
  
  if (end_location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_z_ = p;
  }
  return end_location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryCubeProperty::mutable_end_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryCubeProperty.end_location_z)
  return _msg;
}
inline void GeomtryCubeProperty::set_allocated_end_location_z(::catalog_studio_message::ExpressionValuePair* end_location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z_);
  }
  if (end_location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z));
    if (message_arena != submessage_arena) {
      end_location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_z, submessage_arena);
    }
    
  } else {
    
  }
  end_location_z_ = end_location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryCubeProperty.end_location_z)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GeomtryCubeProperty_2eproto
