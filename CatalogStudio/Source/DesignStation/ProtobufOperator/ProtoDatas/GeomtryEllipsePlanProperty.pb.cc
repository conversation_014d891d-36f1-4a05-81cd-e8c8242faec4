// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryEllipsePlanProperty.proto

#include "GeomtryEllipsePlanProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr GeomtryEllipsePlanProperty::GeomtryEllipsePlanProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : center_location_x_(nullptr)
  , center_location_y_(nullptr)
  , center_location_z_(nullptr)
  , short_radius_data_(nullptr)
  , long_radius_data_(nullptr)
  , interp_point_count_data_(nullptr)
  , plan_belongs_(0)
{}
struct GeomtryEllipsePlanPropertyDefaultTypeInternal {
  constexpr GeomtryEllipsePlanPropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeomtryEllipsePlanPropertyDefaultTypeInternal() {}
  union {
    GeomtryEllipsePlanProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeomtryEllipsePlanPropertyDefaultTypeInternal _GeomtryEllipsePlanProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GeomtryEllipsePlanProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GeomtryEllipsePlanProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GeomtryEllipsePlanProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GeomtryEllipsePlanProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, plan_belongs_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, center_location_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, center_location_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, center_location_z_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, short_radius_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, long_radius_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryEllipsePlanProperty, interp_point_count_data_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::GeomtryEllipsePlanProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_GeomtryEllipsePlanProperty_default_instance_),
};

const char descriptor_table_protodef_GeomtryEllipsePlanProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n GeomtryEllipsePlanProperty.proto\022\026cata"
  "log_studio_message\032\027ComponentEnumData.pr"
  "oto\032\031ExpressionValuePair.proto\"\223\004\n\032Geomt"
  "ryEllipsePlanProperty\022@\n\014plan_belongs\030\001 "
  "\001(\0162*.catalog_studio_message.PlanPolygon"
  "Belongs\022F\n\021center_location_x\030\002 \001(\0132+.cat"
  "alog_studio_message.ExpressionValuePair\022"
  "F\n\021center_location_y\030\003 \001(\0132+.catalog_stu"
  "dio_message.ExpressionValuePair\022F\n\021cente"
  "r_location_z\030\004 \001(\0132+.catalog_studio_mess"
  "age.ExpressionValuePair\022F\n\021short_radius_"
  "data\030\005 \001(\0132+.catalog_studio_message.Expr"
  "essionValuePair\022E\n\020long_radius_data\030\006 \001("
  "\0132+.catalog_studio_message.ExpressionVal"
  "uePair\022L\n\027interp_point_count_data\030\007 \001(\0132"
  "+.catalog_studio_message.ExpressionValue"
  "Pairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GeomtryEllipsePlanProperty_2eproto_deps[2] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GeomtryEllipsePlanProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryEllipsePlanProperty_2eproto = {
  false, false, 652, descriptor_table_protodef_GeomtryEllipsePlanProperty_2eproto, "GeomtryEllipsePlanProperty.proto", 
  &descriptor_table_GeomtryEllipsePlanProperty_2eproto_once, descriptor_table_GeomtryEllipsePlanProperty_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_GeomtryEllipsePlanProperty_2eproto::offsets,
  file_level_metadata_GeomtryEllipsePlanProperty_2eproto, file_level_enum_descriptors_GeomtryEllipsePlanProperty_2eproto, file_level_service_descriptors_GeomtryEllipsePlanProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GeomtryEllipsePlanProperty_2eproto_getter() {
  return &descriptor_table_GeomtryEllipsePlanProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GeomtryEllipsePlanProperty_2eproto(&descriptor_table_GeomtryEllipsePlanProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class GeomtryEllipsePlanProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& center_location_x(const GeomtryEllipsePlanProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& center_location_y(const GeomtryEllipsePlanProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& center_location_z(const GeomtryEllipsePlanProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& short_radius_data(const GeomtryEllipsePlanProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& long_radius_data(const GeomtryEllipsePlanProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& interp_point_count_data(const GeomtryEllipsePlanProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::center_location_x(const GeomtryEllipsePlanProperty* msg) {
  return *msg->center_location_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::center_location_y(const GeomtryEllipsePlanProperty* msg) {
  return *msg->center_location_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::center_location_z(const GeomtryEllipsePlanProperty* msg) {
  return *msg->center_location_z_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::short_radius_data(const GeomtryEllipsePlanProperty* msg) {
  return *msg->short_radius_data_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::long_radius_data(const GeomtryEllipsePlanProperty* msg) {
  return *msg->long_radius_data_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryEllipsePlanProperty::_Internal::interp_point_count_data(const GeomtryEllipsePlanProperty* msg) {
  return *msg->interp_point_count_data_;
}
void GeomtryEllipsePlanProperty::clear_center_location_x() {
  if (GetArenaForAllocation() == nullptr && center_location_x_ != nullptr) {
    delete center_location_x_;
  }
  center_location_x_ = nullptr;
}
void GeomtryEllipsePlanProperty::clear_center_location_y() {
  if (GetArenaForAllocation() == nullptr && center_location_y_ != nullptr) {
    delete center_location_y_;
  }
  center_location_y_ = nullptr;
}
void GeomtryEllipsePlanProperty::clear_center_location_z() {
  if (GetArenaForAllocation() == nullptr && center_location_z_ != nullptr) {
    delete center_location_z_;
  }
  center_location_z_ = nullptr;
}
void GeomtryEllipsePlanProperty::clear_short_radius_data() {
  if (GetArenaForAllocation() == nullptr && short_radius_data_ != nullptr) {
    delete short_radius_data_;
  }
  short_radius_data_ = nullptr;
}
void GeomtryEllipsePlanProperty::clear_long_radius_data() {
  if (GetArenaForAllocation() == nullptr && long_radius_data_ != nullptr) {
    delete long_radius_data_;
  }
  long_radius_data_ = nullptr;
}
void GeomtryEllipsePlanProperty::clear_interp_point_count_data() {
  if (GetArenaForAllocation() == nullptr && interp_point_count_data_ != nullptr) {
    delete interp_point_count_data_;
  }
  interp_point_count_data_ = nullptr;
}
GeomtryEllipsePlanProperty::GeomtryEllipsePlanProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.GeomtryEllipsePlanProperty)
}
GeomtryEllipsePlanProperty::GeomtryEllipsePlanProperty(const GeomtryEllipsePlanProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_center_location_x()) {
    center_location_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.center_location_x_);
  } else {
    center_location_x_ = nullptr;
  }
  if (from._internal_has_center_location_y()) {
    center_location_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.center_location_y_);
  } else {
    center_location_y_ = nullptr;
  }
  if (from._internal_has_center_location_z()) {
    center_location_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.center_location_z_);
  } else {
    center_location_z_ = nullptr;
  }
  if (from._internal_has_short_radius_data()) {
    short_radius_data_ = new ::catalog_studio_message::ExpressionValuePair(*from.short_radius_data_);
  } else {
    short_radius_data_ = nullptr;
  }
  if (from._internal_has_long_radius_data()) {
    long_radius_data_ = new ::catalog_studio_message::ExpressionValuePair(*from.long_radius_data_);
  } else {
    long_radius_data_ = nullptr;
  }
  if (from._internal_has_interp_point_count_data()) {
    interp_point_count_data_ = new ::catalog_studio_message::ExpressionValuePair(*from.interp_point_count_data_);
  } else {
    interp_point_count_data_ = nullptr;
  }
  plan_belongs_ = from.plan_belongs_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.GeomtryEllipsePlanProperty)
}

void GeomtryEllipsePlanProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&center_location_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&center_location_x_)) + sizeof(plan_belongs_));
}

GeomtryEllipsePlanProperty::~GeomtryEllipsePlanProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.GeomtryEllipsePlanProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeomtryEllipsePlanProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete center_location_x_;
  if (this != internal_default_instance()) delete center_location_y_;
  if (this != internal_default_instance()) delete center_location_z_;
  if (this != internal_default_instance()) delete short_radius_data_;
  if (this != internal_default_instance()) delete long_radius_data_;
  if (this != internal_default_instance()) delete interp_point_count_data_;
}

void GeomtryEllipsePlanProperty::ArenaDtor(void* object) {
  GeomtryEllipsePlanProperty* _this = reinterpret_cast< GeomtryEllipsePlanProperty* >(object);
  (void)_this;
}
void GeomtryEllipsePlanProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeomtryEllipsePlanProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeomtryEllipsePlanProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.GeomtryEllipsePlanProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && center_location_x_ != nullptr) {
    delete center_location_x_;
  }
  center_location_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && center_location_y_ != nullptr) {
    delete center_location_y_;
  }
  center_location_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && center_location_z_ != nullptr) {
    delete center_location_z_;
  }
  center_location_z_ = nullptr;
  if (GetArenaForAllocation() == nullptr && short_radius_data_ != nullptr) {
    delete short_radius_data_;
  }
  short_radius_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && long_radius_data_ != nullptr) {
    delete long_radius_data_;
  }
  long_radius_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && interp_point_count_data_ != nullptr) {
    delete interp_point_count_data_;
  }
  interp_point_count_data_ = nullptr;
  plan_belongs_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeomtryEllipsePlanProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_plan_belongs(static_cast<::catalog_studio_message::PlanPolygonBelongs>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair center_location_x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_center_location_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair center_location_y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_center_location_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair center_location_z = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_center_location_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair short_radius_data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_short_radius_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair long_radius_data = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_long_radius_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_interp_point_count_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GeomtryEllipsePlanProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.GeomtryEllipsePlanProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
  if (this->_internal_plan_belongs() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_plan_belongs(), target);
  }

  // .catalog_studio_message.ExpressionValuePair center_location_x = 2;
  if (this->_internal_has_center_location_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::center_location_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair center_location_y = 3;
  if (this->_internal_has_center_location_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::center_location_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair center_location_z = 4;
  if (this->_internal_has_center_location_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::center_location_z(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair short_radius_data = 5;
  if (this->_internal_has_short_radius_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::short_radius_data(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair long_radius_data = 6;
  if (this->_internal_has_long_radius_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::long_radius_data(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 7;
  if (this->_internal_has_interp_point_count_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::interp_point_count_data(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.GeomtryEllipsePlanProperty)
  return target;
}

size_t GeomtryEllipsePlanProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.GeomtryEllipsePlanProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair center_location_x = 2;
  if (this->_internal_has_center_location_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_location_x_);
  }

  // .catalog_studio_message.ExpressionValuePair center_location_y = 3;
  if (this->_internal_has_center_location_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_location_y_);
  }

  // .catalog_studio_message.ExpressionValuePair center_location_z = 4;
  if (this->_internal_has_center_location_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_location_z_);
  }

  // .catalog_studio_message.ExpressionValuePair short_radius_data = 5;
  if (this->_internal_has_short_radius_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *short_radius_data_);
  }

  // .catalog_studio_message.ExpressionValuePair long_radius_data = 6;
  if (this->_internal_has_long_radius_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *long_radius_data_);
  }

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 7;
  if (this->_internal_has_interp_point_count_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *interp_point_count_data_);
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
  if (this->_internal_plan_belongs() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_plan_belongs());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeomtryEllipsePlanProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeomtryEllipsePlanProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeomtryEllipsePlanProperty::GetClassData() const { return &_class_data_; }

void GeomtryEllipsePlanProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeomtryEllipsePlanProperty *>(to)->MergeFrom(
      static_cast<const GeomtryEllipsePlanProperty &>(from));
}


void GeomtryEllipsePlanProperty::MergeFrom(const GeomtryEllipsePlanProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.GeomtryEllipsePlanProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_center_location_x()) {
    _internal_mutable_center_location_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_center_location_x());
  }
  if (from._internal_has_center_location_y()) {
    _internal_mutable_center_location_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_center_location_y());
  }
  if (from._internal_has_center_location_z()) {
    _internal_mutable_center_location_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_center_location_z());
  }
  if (from._internal_has_short_radius_data()) {
    _internal_mutable_short_radius_data()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_short_radius_data());
  }
  if (from._internal_has_long_radius_data()) {
    _internal_mutable_long_radius_data()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_long_radius_data());
  }
  if (from._internal_has_interp_point_count_data()) {
    _internal_mutable_interp_point_count_data()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_interp_point_count_data());
  }
  if (from._internal_plan_belongs() != 0) {
    _internal_set_plan_belongs(from._internal_plan_belongs());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeomtryEllipsePlanProperty::CopyFrom(const GeomtryEllipsePlanProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.GeomtryEllipsePlanProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeomtryEllipsePlanProperty::IsInitialized() const {
  return true;
}

void GeomtryEllipsePlanProperty::InternalSwap(GeomtryEllipsePlanProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeomtryEllipsePlanProperty, plan_belongs_)
      + sizeof(GeomtryEllipsePlanProperty::plan_belongs_)
      - PROTOBUF_FIELD_OFFSET(GeomtryEllipsePlanProperty, center_location_x_)>(
          reinterpret_cast<char*>(&center_location_x_),
          reinterpret_cast<char*>(&other->center_location_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeomtryEllipsePlanProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GeomtryEllipsePlanProperty_2eproto_getter, &descriptor_table_GeomtryEllipsePlanProperty_2eproto_once,
      file_level_metadata_GeomtryEllipsePlanProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::GeomtryEllipsePlanProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::GeomtryEllipsePlanProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::GeomtryEllipsePlanProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
