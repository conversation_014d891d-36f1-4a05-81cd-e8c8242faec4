// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: <PERSON>eom<PERSON><PERSON>llipsePlanProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GeomtryEllipsePlanProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GeomtryEllipsePlanProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GeomtryEllipsePlanProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GeomtryEllipsePlanProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryEllipsePlanProperty_2eproto;
namespace catalog_studio_message {
class GeomtryEllipsePlanProperty;
struct GeomtryEllipsePlanPropertyDefaultTypeInternal;
extern GeomtryEllipsePlanPropertyDefaultTypeInternal _GeomtryEllipsePlanProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::GeomtryEllipsePlanProperty* Arena::CreateMaybeMessage<::catalog_studio_message::GeomtryEllipsePlanProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class GeomtryEllipsePlanProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.GeomtryEllipsePlanProperty) */ {
 public:
  inline GeomtryEllipsePlanProperty() : GeomtryEllipsePlanProperty(nullptr) {}
  ~GeomtryEllipsePlanProperty() override;
  explicit constexpr GeomtryEllipsePlanProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeomtryEllipsePlanProperty(const GeomtryEllipsePlanProperty& from);
  GeomtryEllipsePlanProperty(GeomtryEllipsePlanProperty&& from) noexcept
    : GeomtryEllipsePlanProperty() {
    *this = ::std::move(from);
  }

  inline GeomtryEllipsePlanProperty& operator=(const GeomtryEllipsePlanProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeomtryEllipsePlanProperty& operator=(GeomtryEllipsePlanProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeomtryEllipsePlanProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeomtryEllipsePlanProperty* internal_default_instance() {
    return reinterpret_cast<const GeomtryEllipsePlanProperty*>(
               &_GeomtryEllipsePlanProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeomtryEllipsePlanProperty& a, GeomtryEllipsePlanProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(GeomtryEllipsePlanProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeomtryEllipsePlanProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GeomtryEllipsePlanProperty* New() const final {
    return new GeomtryEllipsePlanProperty();
  }

  GeomtryEllipsePlanProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GeomtryEllipsePlanProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeomtryEllipsePlanProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeomtryEllipsePlanProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeomtryEllipsePlanProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.GeomtryEllipsePlanProperty";
  }
  protected:
  explicit GeomtryEllipsePlanProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCenterLocationXFieldNumber = 2,
    kCenterLocationYFieldNumber = 3,
    kCenterLocationZFieldNumber = 4,
    kShortRadiusDataFieldNumber = 5,
    kLongRadiusDataFieldNumber = 6,
    kInterpPointCountDataFieldNumber = 7,
    kPlanBelongsFieldNumber = 1,
  };
  // .catalog_studio_message.ExpressionValuePair center_location_x = 2;
  bool has_center_location_x() const;
  private:
  bool _internal_has_center_location_x() const;
  public:
  void clear_center_location_x();
  const ::catalog_studio_message::ExpressionValuePair& center_location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_center_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_center_location_x();
  void set_allocated_center_location_x(::catalog_studio_message::ExpressionValuePair* center_location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_center_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_center_location_x();
  public:
  void unsafe_arena_set_allocated_center_location_x(
      ::catalog_studio_message::ExpressionValuePair* center_location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_center_location_x();

  // .catalog_studio_message.ExpressionValuePair center_location_y = 3;
  bool has_center_location_y() const;
  private:
  bool _internal_has_center_location_y() const;
  public:
  void clear_center_location_y();
  const ::catalog_studio_message::ExpressionValuePair& center_location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_center_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_center_location_y();
  void set_allocated_center_location_y(::catalog_studio_message::ExpressionValuePair* center_location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_center_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_center_location_y();
  public:
  void unsafe_arena_set_allocated_center_location_y(
      ::catalog_studio_message::ExpressionValuePair* center_location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_center_location_y();

  // .catalog_studio_message.ExpressionValuePair center_location_z = 4;
  bool has_center_location_z() const;
  private:
  bool _internal_has_center_location_z() const;
  public:
  void clear_center_location_z();
  const ::catalog_studio_message::ExpressionValuePair& center_location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_center_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_center_location_z();
  void set_allocated_center_location_z(::catalog_studio_message::ExpressionValuePair* center_location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_center_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_center_location_z();
  public:
  void unsafe_arena_set_allocated_center_location_z(
      ::catalog_studio_message::ExpressionValuePair* center_location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_center_location_z();

  // .catalog_studio_message.ExpressionValuePair short_radius_data = 5;
  bool has_short_radius_data() const;
  private:
  bool _internal_has_short_radius_data() const;
  public:
  void clear_short_radius_data();
  const ::catalog_studio_message::ExpressionValuePair& short_radius_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_short_radius_data();
  ::catalog_studio_message::ExpressionValuePair* mutable_short_radius_data();
  void set_allocated_short_radius_data(::catalog_studio_message::ExpressionValuePair* short_radius_data);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_short_radius_data() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_short_radius_data();
  public:
  void unsafe_arena_set_allocated_short_radius_data(
      ::catalog_studio_message::ExpressionValuePair* short_radius_data);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_short_radius_data();

  // .catalog_studio_message.ExpressionValuePair long_radius_data = 6;
  bool has_long_radius_data() const;
  private:
  bool _internal_has_long_radius_data() const;
  public:
  void clear_long_radius_data();
  const ::catalog_studio_message::ExpressionValuePair& long_radius_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_long_radius_data();
  ::catalog_studio_message::ExpressionValuePair* mutable_long_radius_data();
  void set_allocated_long_radius_data(::catalog_studio_message::ExpressionValuePair* long_radius_data);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_long_radius_data() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_long_radius_data();
  public:
  void unsafe_arena_set_allocated_long_radius_data(
      ::catalog_studio_message::ExpressionValuePair* long_radius_data);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_long_radius_data();

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 7;
  bool has_interp_point_count_data() const;
  private:
  bool _internal_has_interp_point_count_data() const;
  public:
  void clear_interp_point_count_data();
  const ::catalog_studio_message::ExpressionValuePair& interp_point_count_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_interp_point_count_data();
  ::catalog_studio_message::ExpressionValuePair* mutable_interp_point_count_data();
  void set_allocated_interp_point_count_data(::catalog_studio_message::ExpressionValuePair* interp_point_count_data);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_interp_point_count_data() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_interp_point_count_data();
  public:
  void unsafe_arena_set_allocated_interp_point_count_data(
      ::catalog_studio_message::ExpressionValuePair* interp_point_count_data);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_interp_point_count_data();

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
  void clear_plan_belongs();
  ::catalog_studio_message::PlanPolygonBelongs plan_belongs() const;
  void set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  private:
  ::catalog_studio_message::PlanPolygonBelongs _internal_plan_belongs() const;
  void _internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.GeomtryEllipsePlanProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* center_location_x_;
  ::catalog_studio_message::ExpressionValuePair* center_location_y_;
  ::catalog_studio_message::ExpressionValuePair* center_location_z_;
  ::catalog_studio_message::ExpressionValuePair* short_radius_data_;
  ::catalog_studio_message::ExpressionValuePair* long_radius_data_;
  ::catalog_studio_message::ExpressionValuePair* interp_point_count_data_;
  int plan_belongs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GeomtryEllipsePlanProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeomtryEllipsePlanProperty

// .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
inline void GeomtryEllipsePlanProperty::clear_plan_belongs() {
  plan_belongs_ = 0;
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryEllipsePlanProperty::_internal_plan_belongs() const {
  return static_cast< ::catalog_studio_message::PlanPolygonBelongs >(plan_belongs_);
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryEllipsePlanProperty::plan_belongs() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.plan_belongs)
  return _internal_plan_belongs();
}
inline void GeomtryEllipsePlanProperty::_internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  
  plan_belongs_ = value;
}
inline void GeomtryEllipsePlanProperty::set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  _internal_set_plan_belongs(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryEllipsePlanProperty.plan_belongs)
}

// .catalog_studio_message.ExpressionValuePair center_location_x = 2;
inline bool GeomtryEllipsePlanProperty::_internal_has_center_location_x() const {
  return this != internal_default_instance() && center_location_x_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_center_location_x() const {
  return _internal_has_center_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_center_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = center_location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::center_location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_x)
  return _internal_center_location_x();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_center_location_x(
    ::catalog_studio_message::ExpressionValuePair* center_location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_x_);
  }
  center_location_x_ = center_location_x;
  if (center_location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_center_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_x_;
  center_location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_center_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_x_;
  center_location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_center_location_x() {
  
  if (center_location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    center_location_x_ = p;
  }
  return center_location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_center_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_center_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_x)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_center_location_x(::catalog_studio_message::ExpressionValuePair* center_location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_x_);
  }
  if (center_location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_x));
    if (message_arena != submessage_arena) {
      center_location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center_location_x, submessage_arena);
    }
    
  } else {
    
  }
  center_location_x_ = center_location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_x)
}

// .catalog_studio_message.ExpressionValuePair center_location_y = 3;
inline bool GeomtryEllipsePlanProperty::_internal_has_center_location_y() const {
  return this != internal_default_instance() && center_location_y_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_center_location_y() const {
  return _internal_has_center_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_center_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = center_location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::center_location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_y)
  return _internal_center_location_y();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_center_location_y(
    ::catalog_studio_message::ExpressionValuePair* center_location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_y_);
  }
  center_location_y_ = center_location_y;
  if (center_location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_center_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_y_;
  center_location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_center_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_y_;
  center_location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_center_location_y() {
  
  if (center_location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    center_location_y_ = p;
  }
  return center_location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_center_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_center_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_y)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_center_location_y(::catalog_studio_message::ExpressionValuePair* center_location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_y_);
  }
  if (center_location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_y));
    if (message_arena != submessage_arena) {
      center_location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center_location_y, submessage_arena);
    }
    
  } else {
    
  }
  center_location_y_ = center_location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_y)
}

// .catalog_studio_message.ExpressionValuePair center_location_z = 4;
inline bool GeomtryEllipsePlanProperty::_internal_has_center_location_z() const {
  return this != internal_default_instance() && center_location_z_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_center_location_z() const {
  return _internal_has_center_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_center_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = center_location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::center_location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_z)
  return _internal_center_location_z();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_center_location_z(
    ::catalog_studio_message::ExpressionValuePair* center_location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_z_);
  }
  center_location_z_ = center_location_z;
  if (center_location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_center_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_z_;
  center_location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_center_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = center_location_z_;
  center_location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_center_location_z() {
  
  if (center_location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    center_location_z_ = p;
  }
  return center_location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_center_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_center_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_z)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_center_location_z(::catalog_studio_message::ExpressionValuePair* center_location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_z_);
  }
  if (center_location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_location_z));
    if (message_arena != submessage_arena) {
      center_location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center_location_z, submessage_arena);
    }
    
  } else {
    
  }
  center_location_z_ = center_location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.center_location_z)
}

// .catalog_studio_message.ExpressionValuePair short_radius_data = 5;
inline bool GeomtryEllipsePlanProperty::_internal_has_short_radius_data() const {
  return this != internal_default_instance() && short_radius_data_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_short_radius_data() const {
  return _internal_has_short_radius_data();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_short_radius_data() const {
  const ::catalog_studio_message::ExpressionValuePair* p = short_radius_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::short_radius_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.short_radius_data)
  return _internal_short_radius_data();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_short_radius_data(
    ::catalog_studio_message::ExpressionValuePair* short_radius_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(short_radius_data_);
  }
  short_radius_data_ = short_radius_data;
  if (short_radius_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.short_radius_data)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_short_radius_data() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = short_radius_data_;
  short_radius_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_short_radius_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.short_radius_data)
  
  ::catalog_studio_message::ExpressionValuePair* temp = short_radius_data_;
  short_radius_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_short_radius_data() {
  
  if (short_radius_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    short_radius_data_ = p;
  }
  return short_radius_data_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_short_radius_data() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_short_radius_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.short_radius_data)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_short_radius_data(::catalog_studio_message::ExpressionValuePair* short_radius_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(short_radius_data_);
  }
  if (short_radius_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(short_radius_data));
    if (message_arena != submessage_arena) {
      short_radius_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, short_radius_data, submessage_arena);
    }
    
  } else {
    
  }
  short_radius_data_ = short_radius_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.short_radius_data)
}

// .catalog_studio_message.ExpressionValuePair long_radius_data = 6;
inline bool GeomtryEllipsePlanProperty::_internal_has_long_radius_data() const {
  return this != internal_default_instance() && long_radius_data_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_long_radius_data() const {
  return _internal_has_long_radius_data();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_long_radius_data() const {
  const ::catalog_studio_message::ExpressionValuePair* p = long_radius_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::long_radius_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.long_radius_data)
  return _internal_long_radius_data();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_long_radius_data(
    ::catalog_studio_message::ExpressionValuePair* long_radius_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(long_radius_data_);
  }
  long_radius_data_ = long_radius_data;
  if (long_radius_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.long_radius_data)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_long_radius_data() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = long_radius_data_;
  long_radius_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_long_radius_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.long_radius_data)
  
  ::catalog_studio_message::ExpressionValuePair* temp = long_radius_data_;
  long_radius_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_long_radius_data() {
  
  if (long_radius_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    long_radius_data_ = p;
  }
  return long_radius_data_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_long_radius_data() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_long_radius_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.long_radius_data)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_long_radius_data(::catalog_studio_message::ExpressionValuePair* long_radius_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(long_radius_data_);
  }
  if (long_radius_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(long_radius_data));
    if (message_arena != submessage_arena) {
      long_radius_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, long_radius_data, submessage_arena);
    }
    
  } else {
    
  }
  long_radius_data_ = long_radius_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.long_radius_data)
}

// .catalog_studio_message.ExpressionValuePair interp_point_count_data = 7;
inline bool GeomtryEllipsePlanProperty::_internal_has_interp_point_count_data() const {
  return this != internal_default_instance() && interp_point_count_data_ != nullptr;
}
inline bool GeomtryEllipsePlanProperty::has_interp_point_count_data() const {
  return _internal_has_interp_point_count_data();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::_internal_interp_point_count_data() const {
  const ::catalog_studio_message::ExpressionValuePair* p = interp_point_count_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryEllipsePlanProperty::interp_point_count_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryEllipsePlanProperty.interp_point_count_data)
  return _internal_interp_point_count_data();
}
inline void GeomtryEllipsePlanProperty::unsafe_arena_set_allocated_interp_point_count_data(
    ::catalog_studio_message::ExpressionValuePair* interp_point_count_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data_);
  }
  interp_point_count_data_ = interp_point_count_data;
  if (interp_point_count_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.interp_point_count_data)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::release_interp_point_count_data() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = interp_point_count_data_;
  interp_point_count_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::unsafe_arena_release_interp_point_count_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryEllipsePlanProperty.interp_point_count_data)
  
  ::catalog_studio_message::ExpressionValuePair* temp = interp_point_count_data_;
  interp_point_count_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::_internal_mutable_interp_point_count_data() {
  
  if (interp_point_count_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    interp_point_count_data_ = p;
  }
  return interp_point_count_data_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryEllipsePlanProperty::mutable_interp_point_count_data() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_interp_point_count_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryEllipsePlanProperty.interp_point_count_data)
  return _msg;
}
inline void GeomtryEllipsePlanProperty::set_allocated_interp_point_count_data(::catalog_studio_message::ExpressionValuePair* interp_point_count_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data_);
  }
  if (interp_point_count_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data));
    if (message_arena != submessage_arena) {
      interp_point_count_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, interp_point_count_data, submessage_arena);
    }
    
  } else {
    
  }
  interp_point_count_data_ = interp_point_count_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryEllipsePlanProperty.interp_point_count_data)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GeomtryEllipsePlanProperty_2eproto
