// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryLineProperty.proto

#include "GeomtryLineProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr GeomtryLineProperty::GeomtryLineProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : radius_or_height_data_(nullptr)
  , interp_point_count_data_(nullptr)
  , start_location_(nullptr)
  , end_location_(nullptr)
  , id_(0)
  , line_type_(0)

  , plan_belongs_(0)

  , big_arc_(false){}
struct GeomtryLinePropertyDefaultTypeInternal {
  constexpr GeomtryLinePropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeomtryLinePropertyDefaultTypeInternal() {}
  union {
    GeomtryLineProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeomtryLinePropertyDefaultTypeInternal _GeomtryLineProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GeomtryLineProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GeomtryLineProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GeomtryLineProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GeomtryLineProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, line_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, plan_belongs_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, radius_or_height_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, interp_point_count_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, start_location_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, end_location_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryLineProperty, big_arc_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::GeomtryLineProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_GeomtryLineProperty_default_instance_),
};

const char descriptor_table_protodef_GeomtryLineProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031GeomtryLineProperty.proto\022\026catalog_stu"
  "dio_message\032\027ComponentEnumData.proto\032\031Ex"
  "pressionValuePair.proto\032\014Vector.proto\"\261\003"
  "\n\023GeomtryLineProperty\022\n\n\002id\030\001 \001(\005\0223\n\tlin"
  "e_type\030\002 \001(\0162 .catalog_studio_message.Li"
  "neType\022@\n\014plan_belongs\030\003 \001(\0162*.catalog_s"
  "tudio_message.PlanPolygonBelongs\022J\n\025radi"
  "us_or_height_data\030\004 \001(\0132+.catalog_studio"
  "_message.ExpressionValuePair\022L\n\027interp_p"
  "oint_count_data\030\005 \001(\0132+.catalog_studio_m"
  "essage.ExpressionValuePair\0226\n\016start_loca"
  "tion\030\006 \001(\0132\036.catalog_studio_message.Vect"
  "or\0224\n\014end_location\030\007 \001(\0132\036.catalog_studi"
  "o_message.Vector\022\017\n\007big_arc\030\010 \001(\010b\006proto"
  "3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GeomtryLineProperty_2eproto_deps[3] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
  &::descriptor_table_Vector_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GeomtryLineProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryLineProperty_2eproto = {
  false, false, 561, descriptor_table_protodef_GeomtryLineProperty_2eproto, "GeomtryLineProperty.proto", 
  &descriptor_table_GeomtryLineProperty_2eproto_once, descriptor_table_GeomtryLineProperty_2eproto_deps, 3, 1,
  schemas, file_default_instances, TableStruct_GeomtryLineProperty_2eproto::offsets,
  file_level_metadata_GeomtryLineProperty_2eproto, file_level_enum_descriptors_GeomtryLineProperty_2eproto, file_level_service_descriptors_GeomtryLineProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GeomtryLineProperty_2eproto_getter() {
  return &descriptor_table_GeomtryLineProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GeomtryLineProperty_2eproto(&descriptor_table_GeomtryLineProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class GeomtryLineProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& radius_or_height_data(const GeomtryLineProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& interp_point_count_data(const GeomtryLineProperty* msg);
  static const ::catalog_studio_message::Vector& start_location(const GeomtryLineProperty* msg);
  static const ::catalog_studio_message::Vector& end_location(const GeomtryLineProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
GeomtryLineProperty::_Internal::radius_or_height_data(const GeomtryLineProperty* msg) {
  return *msg->radius_or_height_data_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryLineProperty::_Internal::interp_point_count_data(const GeomtryLineProperty* msg) {
  return *msg->interp_point_count_data_;
}
const ::catalog_studio_message::Vector&
GeomtryLineProperty::_Internal::start_location(const GeomtryLineProperty* msg) {
  return *msg->start_location_;
}
const ::catalog_studio_message::Vector&
GeomtryLineProperty::_Internal::end_location(const GeomtryLineProperty* msg) {
  return *msg->end_location_;
}
void GeomtryLineProperty::clear_radius_or_height_data() {
  if (GetArenaForAllocation() == nullptr && radius_or_height_data_ != nullptr) {
    delete radius_or_height_data_;
  }
  radius_or_height_data_ = nullptr;
}
void GeomtryLineProperty::clear_interp_point_count_data() {
  if (GetArenaForAllocation() == nullptr && interp_point_count_data_ != nullptr) {
    delete interp_point_count_data_;
  }
  interp_point_count_data_ = nullptr;
}
void GeomtryLineProperty::clear_start_location() {
  if (GetArenaForAllocation() == nullptr && start_location_ != nullptr) {
    delete start_location_;
  }
  start_location_ = nullptr;
}
void GeomtryLineProperty::clear_end_location() {
  if (GetArenaForAllocation() == nullptr && end_location_ != nullptr) {
    delete end_location_;
  }
  end_location_ = nullptr;
}
GeomtryLineProperty::GeomtryLineProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.GeomtryLineProperty)
}
GeomtryLineProperty::GeomtryLineProperty(const GeomtryLineProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_radius_or_height_data()) {
    radius_or_height_data_ = new ::catalog_studio_message::ExpressionValuePair(*from.radius_or_height_data_);
  } else {
    radius_or_height_data_ = nullptr;
  }
  if (from._internal_has_interp_point_count_data()) {
    interp_point_count_data_ = new ::catalog_studio_message::ExpressionValuePair(*from.interp_point_count_data_);
  } else {
    interp_point_count_data_ = nullptr;
  }
  if (from._internal_has_start_location()) {
    start_location_ = new ::catalog_studio_message::Vector(*from.start_location_);
  } else {
    start_location_ = nullptr;
  }
  if (from._internal_has_end_location()) {
    end_location_ = new ::catalog_studio_message::Vector(*from.end_location_);
  } else {
    end_location_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&big_arc_) -
    reinterpret_cast<char*>(&id_)) + sizeof(big_arc_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.GeomtryLineProperty)
}

void GeomtryLineProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&radius_or_height_data_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&big_arc_) -
    reinterpret_cast<char*>(&radius_or_height_data_)) + sizeof(big_arc_));
}

GeomtryLineProperty::~GeomtryLineProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.GeomtryLineProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeomtryLineProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete radius_or_height_data_;
  if (this != internal_default_instance()) delete interp_point_count_data_;
  if (this != internal_default_instance()) delete start_location_;
  if (this != internal_default_instance()) delete end_location_;
}

void GeomtryLineProperty::ArenaDtor(void* object) {
  GeomtryLineProperty* _this = reinterpret_cast< GeomtryLineProperty* >(object);
  (void)_this;
}
void GeomtryLineProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeomtryLineProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeomtryLineProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.GeomtryLineProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && radius_or_height_data_ != nullptr) {
    delete radius_or_height_data_;
  }
  radius_or_height_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && interp_point_count_data_ != nullptr) {
    delete interp_point_count_data_;
  }
  interp_point_count_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_location_ != nullptr) {
    delete start_location_;
  }
  start_location_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_location_ != nullptr) {
    delete end_location_;
  }
  end_location_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&big_arc_) -
      reinterpret_cast<char*>(&id_)) + sizeof(big_arc_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeomtryLineProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.LineType line_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_line_type(static_cast<::catalog_studio_message::LineType>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_plan_belongs(static_cast<::catalog_studio_message::PlanPolygonBelongs>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair radius_or_height_data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_radius_or_height_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_interp_point_count_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.Vector start_location = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.Vector end_location = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool big_arc = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          big_arc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GeomtryLineProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.GeomtryLineProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.LineType line_type = 2;
  if (this->_internal_line_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_line_type(), target);
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
  if (this->_internal_plan_belongs() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_plan_belongs(), target);
  }

  // .catalog_studio_message.ExpressionValuePair radius_or_height_data = 4;
  if (this->_internal_has_radius_or_height_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::radius_or_height_data(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 5;
  if (this->_internal_has_interp_point_count_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::interp_point_count_data(this), target, stream);
  }

  // .catalog_studio_message.Vector start_location = 6;
  if (this->_internal_has_start_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::start_location(this), target, stream);
  }

  // .catalog_studio_message.Vector end_location = 7;
  if (this->_internal_has_end_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::end_location(this), target, stream);
  }

  // bool big_arc = 8;
  if (this->_internal_big_arc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_big_arc(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.GeomtryLineProperty)
  return target;
}

size_t GeomtryLineProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.GeomtryLineProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair radius_or_height_data = 4;
  if (this->_internal_has_radius_or_height_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *radius_or_height_data_);
  }

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 5;
  if (this->_internal_has_interp_point_count_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *interp_point_count_data_);
  }

  // .catalog_studio_message.Vector start_location = 6;
  if (this->_internal_has_start_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_location_);
  }

  // .catalog_studio_message.Vector end_location = 7;
  if (this->_internal_has_end_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_location_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // .catalog_studio_message.LineType line_type = 2;
  if (this->_internal_line_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_line_type());
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
  if (this->_internal_plan_belongs() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_plan_belongs());
  }

  // bool big_arc = 8;
  if (this->_internal_big_arc() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeomtryLineProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeomtryLineProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeomtryLineProperty::GetClassData() const { return &_class_data_; }

void GeomtryLineProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeomtryLineProperty *>(to)->MergeFrom(
      static_cast<const GeomtryLineProperty &>(from));
}


void GeomtryLineProperty::MergeFrom(const GeomtryLineProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.GeomtryLineProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_radius_or_height_data()) {
    _internal_mutable_radius_or_height_data()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_radius_or_height_data());
  }
  if (from._internal_has_interp_point_count_data()) {
    _internal_mutable_interp_point_count_data()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_interp_point_count_data());
  }
  if (from._internal_has_start_location()) {
    _internal_mutable_start_location()->::catalog_studio_message::Vector::MergeFrom(from._internal_start_location());
  }
  if (from._internal_has_end_location()) {
    _internal_mutable_end_location()->::catalog_studio_message::Vector::MergeFrom(from._internal_end_location());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_line_type() != 0) {
    _internal_set_line_type(from._internal_line_type());
  }
  if (from._internal_plan_belongs() != 0) {
    _internal_set_plan_belongs(from._internal_plan_belongs());
  }
  if (from._internal_big_arc() != 0) {
    _internal_set_big_arc(from._internal_big_arc());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeomtryLineProperty::CopyFrom(const GeomtryLineProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.GeomtryLineProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeomtryLineProperty::IsInitialized() const {
  return true;
}

void GeomtryLineProperty::InternalSwap(GeomtryLineProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeomtryLineProperty, big_arc_)
      + sizeof(GeomtryLineProperty::big_arc_)
      - PROTOBUF_FIELD_OFFSET(GeomtryLineProperty, radius_or_height_data_)>(
          reinterpret_cast<char*>(&radius_or_height_data_),
          reinterpret_cast<char*>(&other->radius_or_height_data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeomtryLineProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GeomtryLineProperty_2eproto_getter, &descriptor_table_GeomtryLineProperty_2eproto_once,
      file_level_metadata_GeomtryLineProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::GeomtryLineProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::GeomtryLineProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::GeomtryLineProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
