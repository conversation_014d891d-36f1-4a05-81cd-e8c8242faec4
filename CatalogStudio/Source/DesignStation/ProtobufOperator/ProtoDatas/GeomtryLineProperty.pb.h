// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryLineProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GeomtryLineProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GeomtryLineProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "ExpressionValuePair.pb.h"
#include "Vector.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GeomtryLineProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GeomtryLineProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryLineProperty_2eproto;
namespace catalog_studio_message {
class GeomtryLineProperty;
struct GeomtryLinePropertyDefaultTypeInternal;
extern GeomtryLinePropertyDefaultTypeInternal _GeomtryLineProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::GeomtryLineProperty* Arena::CreateMaybeMessage<::catalog_studio_message::GeomtryLineProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class GeomtryLineProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.GeomtryLineProperty) */ {
 public:
  inline GeomtryLineProperty() : GeomtryLineProperty(nullptr) {}
  ~GeomtryLineProperty() override;
  explicit constexpr GeomtryLineProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeomtryLineProperty(const GeomtryLineProperty& from);
  GeomtryLineProperty(GeomtryLineProperty&& from) noexcept
    : GeomtryLineProperty() {
    *this = ::std::move(from);
  }

  inline GeomtryLineProperty& operator=(const GeomtryLineProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeomtryLineProperty& operator=(GeomtryLineProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeomtryLineProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeomtryLineProperty* internal_default_instance() {
    return reinterpret_cast<const GeomtryLineProperty*>(
               &_GeomtryLineProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeomtryLineProperty& a, GeomtryLineProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(GeomtryLineProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeomtryLineProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GeomtryLineProperty* New() const final {
    return new GeomtryLineProperty();
  }

  GeomtryLineProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GeomtryLineProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeomtryLineProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeomtryLineProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeomtryLineProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.GeomtryLineProperty";
  }
  protected:
  explicit GeomtryLineProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRadiusOrHeightDataFieldNumber = 4,
    kInterpPointCountDataFieldNumber = 5,
    kStartLocationFieldNumber = 6,
    kEndLocationFieldNumber = 7,
    kIdFieldNumber = 1,
    kLineTypeFieldNumber = 2,
    kPlanBelongsFieldNumber = 3,
    kBigArcFieldNumber = 8,
  };
  // .catalog_studio_message.ExpressionValuePair radius_or_height_data = 4;
  bool has_radius_or_height_data() const;
  private:
  bool _internal_has_radius_or_height_data() const;
  public:
  void clear_radius_or_height_data();
  const ::catalog_studio_message::ExpressionValuePair& radius_or_height_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_radius_or_height_data();
  ::catalog_studio_message::ExpressionValuePair* mutable_radius_or_height_data();
  void set_allocated_radius_or_height_data(::catalog_studio_message::ExpressionValuePair* radius_or_height_data);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_radius_or_height_data() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_radius_or_height_data();
  public:
  void unsafe_arena_set_allocated_radius_or_height_data(
      ::catalog_studio_message::ExpressionValuePair* radius_or_height_data);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_radius_or_height_data();

  // .catalog_studio_message.ExpressionValuePair interp_point_count_data = 5;
  bool has_interp_point_count_data() const;
  private:
  bool _internal_has_interp_point_count_data() const;
  public:
  void clear_interp_point_count_data();
  const ::catalog_studio_message::ExpressionValuePair& interp_point_count_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_interp_point_count_data();
  ::catalog_studio_message::ExpressionValuePair* mutable_interp_point_count_data();
  void set_allocated_interp_point_count_data(::catalog_studio_message::ExpressionValuePair* interp_point_count_data);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_interp_point_count_data() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_interp_point_count_data();
  public:
  void unsafe_arena_set_allocated_interp_point_count_data(
      ::catalog_studio_message::ExpressionValuePair* interp_point_count_data);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_interp_point_count_data();

  // .catalog_studio_message.Vector start_location = 6;
  bool has_start_location() const;
  private:
  bool _internal_has_start_location() const;
  public:
  void clear_start_location();
  const ::catalog_studio_message::Vector& start_location() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::Vector* release_start_location();
  ::catalog_studio_message::Vector* mutable_start_location();
  void set_allocated_start_location(::catalog_studio_message::Vector* start_location);
  private:
  const ::catalog_studio_message::Vector& _internal_start_location() const;
  ::catalog_studio_message::Vector* _internal_mutable_start_location();
  public:
  void unsafe_arena_set_allocated_start_location(
      ::catalog_studio_message::Vector* start_location);
  ::catalog_studio_message::Vector* unsafe_arena_release_start_location();

  // .catalog_studio_message.Vector end_location = 7;
  bool has_end_location() const;
  private:
  bool _internal_has_end_location() const;
  public:
  void clear_end_location();
  const ::catalog_studio_message::Vector& end_location() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::Vector* release_end_location();
  ::catalog_studio_message::Vector* mutable_end_location();
  void set_allocated_end_location(::catalog_studio_message::Vector* end_location);
  private:
  const ::catalog_studio_message::Vector& _internal_end_location() const;
  ::catalog_studio_message::Vector* _internal_mutable_end_location();
  public:
  void unsafe_arena_set_allocated_end_location(
      ::catalog_studio_message::Vector* end_location);
  ::catalog_studio_message::Vector* unsafe_arena_release_end_location();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .catalog_studio_message.LineType line_type = 2;
  void clear_line_type();
  ::catalog_studio_message::LineType line_type() const;
  void set_line_type(::catalog_studio_message::LineType value);
  private:
  ::catalog_studio_message::LineType _internal_line_type() const;
  void _internal_set_line_type(::catalog_studio_message::LineType value);
  public:

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
  void clear_plan_belongs();
  ::catalog_studio_message::PlanPolygonBelongs plan_belongs() const;
  void set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  private:
  ::catalog_studio_message::PlanPolygonBelongs _internal_plan_belongs() const;
  void _internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  public:

  // bool big_arc = 8;
  void clear_big_arc();
  bool big_arc() const;
  void set_big_arc(bool value);
  private:
  bool _internal_big_arc() const;
  void _internal_set_big_arc(bool value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.GeomtryLineProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* radius_or_height_data_;
  ::catalog_studio_message::ExpressionValuePair* interp_point_count_data_;
  ::catalog_studio_message::Vector* start_location_;
  ::catalog_studio_message::Vector* end_location_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  int line_type_;
  int plan_belongs_;
  bool big_arc_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GeomtryLineProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeomtryLineProperty

// int32 id = 1;
inline void GeomtryLineProperty::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GeomtryLineProperty::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GeomtryLineProperty::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.id)
  return _internal_id();
}
inline void GeomtryLineProperty::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void GeomtryLineProperty::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryLineProperty.id)
}

// .catalog_studio_message.LineType line_type = 2;
inline void GeomtryLineProperty::clear_line_type() {
  line_type_ = 0;
}
inline ::catalog_studio_message::LineType GeomtryLineProperty::_internal_line_type() const {
  return static_cast< ::catalog_studio_message::LineType >(line_type_);
}
inline ::catalog_studio_message::LineType GeomtryLineProperty::line_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.line_type)
  return _internal_line_type();
}
inline void GeomtryLineProperty::_internal_set_line_type(::catalog_studio_message::LineType value) {
  
  line_type_ = value;
}
inline void GeomtryLineProperty::set_line_type(::catalog_studio_message::LineType value) {
  _internal_set_line_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryLineProperty.line_type)
}

// .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
inline void GeomtryLineProperty::clear_plan_belongs() {
  plan_belongs_ = 0;
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryLineProperty::_internal_plan_belongs() const {
  return static_cast< ::catalog_studio_message::PlanPolygonBelongs >(plan_belongs_);
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryLineProperty::plan_belongs() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.plan_belongs)
  return _internal_plan_belongs();
}
inline void GeomtryLineProperty::_internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  
  plan_belongs_ = value;
}
inline void GeomtryLineProperty::set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  _internal_set_plan_belongs(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryLineProperty.plan_belongs)
}

// .catalog_studio_message.ExpressionValuePair radius_or_height_data = 4;
inline bool GeomtryLineProperty::_internal_has_radius_or_height_data() const {
  return this != internal_default_instance() && radius_or_height_data_ != nullptr;
}
inline bool GeomtryLineProperty::has_radius_or_height_data() const {
  return _internal_has_radius_or_height_data();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryLineProperty::_internal_radius_or_height_data() const {
  const ::catalog_studio_message::ExpressionValuePair* p = radius_or_height_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryLineProperty::radius_or_height_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.radius_or_height_data)
  return _internal_radius_or_height_data();
}
inline void GeomtryLineProperty::unsafe_arena_set_allocated_radius_or_height_data(
    ::catalog_studio_message::ExpressionValuePair* radius_or_height_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(radius_or_height_data_);
  }
  radius_or_height_data_ = radius_or_height_data;
  if (radius_or_height_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryLineProperty.radius_or_height_data)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::release_radius_or_height_data() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = radius_or_height_data_;
  radius_or_height_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::unsafe_arena_release_radius_or_height_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryLineProperty.radius_or_height_data)
  
  ::catalog_studio_message::ExpressionValuePair* temp = radius_or_height_data_;
  radius_or_height_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::_internal_mutable_radius_or_height_data() {
  
  if (radius_or_height_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    radius_or_height_data_ = p;
  }
  return radius_or_height_data_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::mutable_radius_or_height_data() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_radius_or_height_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryLineProperty.radius_or_height_data)
  return _msg;
}
inline void GeomtryLineProperty::set_allocated_radius_or_height_data(::catalog_studio_message::ExpressionValuePair* radius_or_height_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(radius_or_height_data_);
  }
  if (radius_or_height_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(radius_or_height_data));
    if (message_arena != submessage_arena) {
      radius_or_height_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, radius_or_height_data, submessage_arena);
    }
    
  } else {
    
  }
  radius_or_height_data_ = radius_or_height_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryLineProperty.radius_or_height_data)
}

// .catalog_studio_message.ExpressionValuePair interp_point_count_data = 5;
inline bool GeomtryLineProperty::_internal_has_interp_point_count_data() const {
  return this != internal_default_instance() && interp_point_count_data_ != nullptr;
}
inline bool GeomtryLineProperty::has_interp_point_count_data() const {
  return _internal_has_interp_point_count_data();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryLineProperty::_internal_interp_point_count_data() const {
  const ::catalog_studio_message::ExpressionValuePair* p = interp_point_count_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryLineProperty::interp_point_count_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.interp_point_count_data)
  return _internal_interp_point_count_data();
}
inline void GeomtryLineProperty::unsafe_arena_set_allocated_interp_point_count_data(
    ::catalog_studio_message::ExpressionValuePair* interp_point_count_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data_);
  }
  interp_point_count_data_ = interp_point_count_data;
  if (interp_point_count_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryLineProperty.interp_point_count_data)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::release_interp_point_count_data() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = interp_point_count_data_;
  interp_point_count_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::unsafe_arena_release_interp_point_count_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryLineProperty.interp_point_count_data)
  
  ::catalog_studio_message::ExpressionValuePair* temp = interp_point_count_data_;
  interp_point_count_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::_internal_mutable_interp_point_count_data() {
  
  if (interp_point_count_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    interp_point_count_data_ = p;
  }
  return interp_point_count_data_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryLineProperty::mutable_interp_point_count_data() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_interp_point_count_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryLineProperty.interp_point_count_data)
  return _msg;
}
inline void GeomtryLineProperty::set_allocated_interp_point_count_data(::catalog_studio_message::ExpressionValuePair* interp_point_count_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data_);
  }
  if (interp_point_count_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(interp_point_count_data));
    if (message_arena != submessage_arena) {
      interp_point_count_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, interp_point_count_data, submessage_arena);
    }
    
  } else {
    
  }
  interp_point_count_data_ = interp_point_count_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryLineProperty.interp_point_count_data)
}

// .catalog_studio_message.Vector start_location = 6;
inline bool GeomtryLineProperty::_internal_has_start_location() const {
  return this != internal_default_instance() && start_location_ != nullptr;
}
inline bool GeomtryLineProperty::has_start_location() const {
  return _internal_has_start_location();
}
inline const ::catalog_studio_message::Vector& GeomtryLineProperty::_internal_start_location() const {
  const ::catalog_studio_message::Vector* p = start_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::Vector&>(
      ::catalog_studio_message::_Vector_default_instance_);
}
inline const ::catalog_studio_message::Vector& GeomtryLineProperty::start_location() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.start_location)
  return _internal_start_location();
}
inline void GeomtryLineProperty::unsafe_arena_set_allocated_start_location(
    ::catalog_studio_message::Vector* start_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_);
  }
  start_location_ = start_location;
  if (start_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryLineProperty.start_location)
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::release_start_location() {
  
  ::catalog_studio_message::Vector* temp = start_location_;
  start_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::unsafe_arena_release_start_location() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryLineProperty.start_location)
  
  ::catalog_studio_message::Vector* temp = start_location_;
  start_location_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::_internal_mutable_start_location() {
  
  if (start_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::Vector>(GetArenaForAllocation());
    start_location_ = p;
  }
  return start_location_;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::mutable_start_location() {
  ::catalog_studio_message::Vector* _msg = _internal_mutable_start_location();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryLineProperty.start_location)
  return _msg;
}
inline void GeomtryLineProperty::set_allocated_start_location(::catalog_studio_message::Vector* start_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_);
  }
  if (start_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location));
    if (message_arena != submessage_arena) {
      start_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location, submessage_arena);
    }
    
  } else {
    
  }
  start_location_ = start_location;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryLineProperty.start_location)
}

// .catalog_studio_message.Vector end_location = 7;
inline bool GeomtryLineProperty::_internal_has_end_location() const {
  return this != internal_default_instance() && end_location_ != nullptr;
}
inline bool GeomtryLineProperty::has_end_location() const {
  return _internal_has_end_location();
}
inline const ::catalog_studio_message::Vector& GeomtryLineProperty::_internal_end_location() const {
  const ::catalog_studio_message::Vector* p = end_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::Vector&>(
      ::catalog_studio_message::_Vector_default_instance_);
}
inline const ::catalog_studio_message::Vector& GeomtryLineProperty::end_location() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.end_location)
  return _internal_end_location();
}
inline void GeomtryLineProperty::unsafe_arena_set_allocated_end_location(
    ::catalog_studio_message::Vector* end_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_);
  }
  end_location_ = end_location;
  if (end_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryLineProperty.end_location)
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::release_end_location() {
  
  ::catalog_studio_message::Vector* temp = end_location_;
  end_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::unsafe_arena_release_end_location() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryLineProperty.end_location)
  
  ::catalog_studio_message::Vector* temp = end_location_;
  end_location_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::_internal_mutable_end_location() {
  
  if (end_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::Vector>(GetArenaForAllocation());
    end_location_ = p;
  }
  return end_location_;
}
inline ::catalog_studio_message::Vector* GeomtryLineProperty::mutable_end_location() {
  ::catalog_studio_message::Vector* _msg = _internal_mutable_end_location();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryLineProperty.end_location)
  return _msg;
}
inline void GeomtryLineProperty::set_allocated_end_location(::catalog_studio_message::Vector* end_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_);
  }
  if (end_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location));
    if (message_arena != submessage_arena) {
      end_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location, submessage_arena);
    }
    
  } else {
    
  }
  end_location_ = end_location;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryLineProperty.end_location)
}

// bool big_arc = 8;
inline void GeomtryLineProperty::clear_big_arc() {
  big_arc_ = false;
}
inline bool GeomtryLineProperty::_internal_big_arc() const {
  return big_arc_;
}
inline bool GeomtryLineProperty::big_arc() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryLineProperty.big_arc)
  return _internal_big_arc();
}
inline void GeomtryLineProperty::_internal_set_big_arc(bool value) {
  
  big_arc_ = value;
}
inline void GeomtryLineProperty::set_big_arc(bool value) {
  _internal_set_big_arc(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryLineProperty.big_arc)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GeomtryLineProperty_2eproto
