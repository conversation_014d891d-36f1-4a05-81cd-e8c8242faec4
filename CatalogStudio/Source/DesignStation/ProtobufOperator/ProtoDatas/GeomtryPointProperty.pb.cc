// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: GeomtryPointProperty.proto

#include "GeomtryPointProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr GeomtryPointProperty::GeomtryPointProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : location_x_(nullptr)
  , location_y_(nullptr)
  , location_z_(nullptr)
  , pre_point_location_(nullptr)
  , id_(0)
  , position_type_(0)

  , plan_belongs_(0)
{}
struct GeomtryPointPropertyDefaultTypeInternal {
  constexpr GeomtryPointPropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeomtryPointPropertyDefaultTypeInternal() {}
  union {
    GeomtryPointProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeomtryPointPropertyDefaultTypeInternal _GeomtryPointProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_GeomtryPointProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_GeomtryPointProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_GeomtryPointProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_GeomtryPointProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, position_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, plan_belongs_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, location_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, location_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, location_z_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::GeomtryPointProperty, pre_point_location_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::GeomtryPointProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_GeomtryPointProperty_default_instance_),
};

const char descriptor_table_protodef_GeomtryPointProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032GeomtryPointProperty.proto\022\026catalog_st"
  "udio_message\032\027ComponentEnumData.proto\032\031E"
  "xpressionValuePair.proto\032\014Vector.proto\"\240"
  "\003\n\024GeomtryPointProperty\022\n\n\002id\030\001 \001(\005\022;\n\rp"
  "osition_type\030\002 \001(\0162$.catalog_studio_mess"
  "age.PositionType\022@\n\014plan_belongs\030\003 \001(\0162*"
  ".catalog_studio_message.PlanPolygonBelon"
  "gs\022\?\n\nlocation_x\030\004 \001(\0132+.catalog_studio_"
  "message.ExpressionValuePair\022\?\n\nlocation_"
  "y\030\005 \001(\0132+.catalog_studio_message.Express"
  "ionValuePair\022\?\n\nlocation_z\030\006 \001(\0132+.catal"
  "og_studio_message.ExpressionValuePair\022:\n"
  "\022pre_point_location\030\007 \001(\0132\036.catalog_stud"
  "io_message.Vectorb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_GeomtryPointProperty_2eproto_deps[3] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
  &::descriptor_table_Vector_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_GeomtryPointProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryPointProperty_2eproto = {
  false, false, 545, descriptor_table_protodef_GeomtryPointProperty_2eproto, "GeomtryPointProperty.proto", 
  &descriptor_table_GeomtryPointProperty_2eproto_once, descriptor_table_GeomtryPointProperty_2eproto_deps, 3, 1,
  schemas, file_default_instances, TableStruct_GeomtryPointProperty_2eproto::offsets,
  file_level_metadata_GeomtryPointProperty_2eproto, file_level_enum_descriptors_GeomtryPointProperty_2eproto, file_level_service_descriptors_GeomtryPointProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_GeomtryPointProperty_2eproto_getter() {
  return &descriptor_table_GeomtryPointProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_GeomtryPointProperty_2eproto(&descriptor_table_GeomtryPointProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class GeomtryPointProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& location_x(const GeomtryPointProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& location_y(const GeomtryPointProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& location_z(const GeomtryPointProperty* msg);
  static const ::catalog_studio_message::Vector& pre_point_location(const GeomtryPointProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
GeomtryPointProperty::_Internal::location_x(const GeomtryPointProperty* msg) {
  return *msg->location_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryPointProperty::_Internal::location_y(const GeomtryPointProperty* msg) {
  return *msg->location_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
GeomtryPointProperty::_Internal::location_z(const GeomtryPointProperty* msg) {
  return *msg->location_z_;
}
const ::catalog_studio_message::Vector&
GeomtryPointProperty::_Internal::pre_point_location(const GeomtryPointProperty* msg) {
  return *msg->pre_point_location_;
}
void GeomtryPointProperty::clear_location_x() {
  if (GetArenaForAllocation() == nullptr && location_x_ != nullptr) {
    delete location_x_;
  }
  location_x_ = nullptr;
}
void GeomtryPointProperty::clear_location_y() {
  if (GetArenaForAllocation() == nullptr && location_y_ != nullptr) {
    delete location_y_;
  }
  location_y_ = nullptr;
}
void GeomtryPointProperty::clear_location_z() {
  if (GetArenaForAllocation() == nullptr && location_z_ != nullptr) {
    delete location_z_;
  }
  location_z_ = nullptr;
}
void GeomtryPointProperty::clear_pre_point_location() {
  if (GetArenaForAllocation() == nullptr && pre_point_location_ != nullptr) {
    delete pre_point_location_;
  }
  pre_point_location_ = nullptr;
}
GeomtryPointProperty::GeomtryPointProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.GeomtryPointProperty)
}
GeomtryPointProperty::GeomtryPointProperty(const GeomtryPointProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_location_x()) {
    location_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_x_);
  } else {
    location_x_ = nullptr;
  }
  if (from._internal_has_location_y()) {
    location_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_y_);
  } else {
    location_y_ = nullptr;
  }
  if (from._internal_has_location_z()) {
    location_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_z_);
  } else {
    location_z_ = nullptr;
  }
  if (from._internal_has_pre_point_location()) {
    pre_point_location_ = new ::catalog_studio_message::Vector(*from.pre_point_location_);
  } else {
    pre_point_location_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.GeomtryPointProperty)
}

void GeomtryPointProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&location_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&location_x_)) + sizeof(plan_belongs_));
}

GeomtryPointProperty::~GeomtryPointProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.GeomtryPointProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeomtryPointProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete location_x_;
  if (this != internal_default_instance()) delete location_y_;
  if (this != internal_default_instance()) delete location_z_;
  if (this != internal_default_instance()) delete pre_point_location_;
}

void GeomtryPointProperty::ArenaDtor(void* object) {
  GeomtryPointProperty* _this = reinterpret_cast< GeomtryPointProperty* >(object);
  (void)_this;
}
void GeomtryPointProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeomtryPointProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeomtryPointProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.GeomtryPointProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && location_x_ != nullptr) {
    delete location_x_;
  }
  location_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && location_y_ != nullptr) {
    delete location_y_;
  }
  location_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && location_z_ != nullptr) {
    delete location_z_;
  }
  location_z_ = nullptr;
  if (GetArenaForAllocation() == nullptr && pre_point_location_ != nullptr) {
    delete pre_point_location_;
  }
  pre_point_location_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&plan_belongs_) -
      reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeomtryPointProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.PositionType position_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_position_type(static_cast<::catalog_studio_message::PositionType>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_plan_belongs(static_cast<::catalog_studio_message::PlanPolygonBelongs>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair location_x = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair location_y = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair location_z = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.Vector pre_point_location = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_pre_point_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GeomtryPointProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.GeomtryPointProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.PositionType position_type = 2;
  if (this->_internal_position_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_position_type(), target);
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
  if (this->_internal_plan_belongs() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_plan_belongs(), target);
  }

  // .catalog_studio_message.ExpressionValuePair location_x = 4;
  if (this->_internal_has_location_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::location_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair location_y = 5;
  if (this->_internal_has_location_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::location_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair location_z = 6;
  if (this->_internal_has_location_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::location_z(this), target, stream);
  }

  // .catalog_studio_message.Vector pre_point_location = 7;
  if (this->_internal_has_pre_point_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::pre_point_location(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.GeomtryPointProperty)
  return target;
}

size_t GeomtryPointProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.GeomtryPointProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair location_x = 4;
  if (this->_internal_has_location_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_x_);
  }

  // .catalog_studio_message.ExpressionValuePair location_y = 5;
  if (this->_internal_has_location_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_y_);
  }

  // .catalog_studio_message.ExpressionValuePair location_z = 6;
  if (this->_internal_has_location_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_z_);
  }

  // .catalog_studio_message.Vector pre_point_location = 7;
  if (this->_internal_has_pre_point_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *pre_point_location_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // .catalog_studio_message.PositionType position_type = 2;
  if (this->_internal_position_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_position_type());
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 3;
  if (this->_internal_plan_belongs() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_plan_belongs());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeomtryPointProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeomtryPointProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeomtryPointProperty::GetClassData() const { return &_class_data_; }

void GeomtryPointProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeomtryPointProperty *>(to)->MergeFrom(
      static_cast<const GeomtryPointProperty &>(from));
}


void GeomtryPointProperty::MergeFrom(const GeomtryPointProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.GeomtryPointProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_location_x()) {
    _internal_mutable_location_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_x());
  }
  if (from._internal_has_location_y()) {
    _internal_mutable_location_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_y());
  }
  if (from._internal_has_location_z()) {
    _internal_mutable_location_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_z());
  }
  if (from._internal_has_pre_point_location()) {
    _internal_mutable_pre_point_location()->::catalog_studio_message::Vector::MergeFrom(from._internal_pre_point_location());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_position_type() != 0) {
    _internal_set_position_type(from._internal_position_type());
  }
  if (from._internal_plan_belongs() != 0) {
    _internal_set_plan_belongs(from._internal_plan_belongs());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeomtryPointProperty::CopyFrom(const GeomtryPointProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.GeomtryPointProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeomtryPointProperty::IsInitialized() const {
  return true;
}

void GeomtryPointProperty::InternalSwap(GeomtryPointProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeomtryPointProperty, plan_belongs_)
      + sizeof(GeomtryPointProperty::plan_belongs_)
      - PROTOBUF_FIELD_OFFSET(GeomtryPointProperty, location_x_)>(
          reinterpret_cast<char*>(&location_x_),
          reinterpret_cast<char*>(&other->location_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeomtryPointProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_GeomtryPointProperty_2eproto_getter, &descriptor_table_GeomtryPointProperty_2eproto_once,
      file_level_metadata_GeomtryPointProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::GeomtryPointProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::GeomtryPointProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::GeomtryPointProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
