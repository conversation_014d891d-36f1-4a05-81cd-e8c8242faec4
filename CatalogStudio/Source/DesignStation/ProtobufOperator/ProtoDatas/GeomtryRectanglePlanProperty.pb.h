// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: <PERSON><PERSON>m<PERSON><PERSON><PERSON>tanglePlanProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_GeomtryRectanglePlanProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_GeomtryRectanglePlanProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_GeomtryRectanglePlanProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_GeomtryRectanglePlanProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_GeomtryRectanglePlanProperty_2eproto;
namespace catalog_studio_message {
class GeomtryRectanglePlanProperty;
struct GeomtryRectanglePlanPropertyDefaultTypeInternal;
extern GeomtryRectanglePlanPropertyDefaultTypeInternal _GeomtryRectanglePlanProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::GeomtryRectanglePlanProperty* Arena::CreateMaybeMessage<::catalog_studio_message::GeomtryRectanglePlanProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class GeomtryRectanglePlanProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.GeomtryRectanglePlanProperty) */ {
 public:
  inline GeomtryRectanglePlanProperty() : GeomtryRectanglePlanProperty(nullptr) {}
  ~GeomtryRectanglePlanProperty() override;
  explicit constexpr GeomtryRectanglePlanProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeomtryRectanglePlanProperty(const GeomtryRectanglePlanProperty& from);
  GeomtryRectanglePlanProperty(GeomtryRectanglePlanProperty&& from) noexcept
    : GeomtryRectanglePlanProperty() {
    *this = ::std::move(from);
  }

  inline GeomtryRectanglePlanProperty& operator=(const GeomtryRectanglePlanProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeomtryRectanglePlanProperty& operator=(GeomtryRectanglePlanProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeomtryRectanglePlanProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeomtryRectanglePlanProperty* internal_default_instance() {
    return reinterpret_cast<const GeomtryRectanglePlanProperty*>(
               &_GeomtryRectanglePlanProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeomtryRectanglePlanProperty& a, GeomtryRectanglePlanProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(GeomtryRectanglePlanProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeomtryRectanglePlanProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GeomtryRectanglePlanProperty* New() const final {
    return new GeomtryRectanglePlanProperty();
  }

  GeomtryRectanglePlanProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GeomtryRectanglePlanProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeomtryRectanglePlanProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeomtryRectanglePlanProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeomtryRectanglePlanProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.GeomtryRectanglePlanProperty";
  }
  protected:
  explicit GeomtryRectanglePlanProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartLocationXFieldNumber = 2,
    kStartLocationYFieldNumber = 3,
    kStartLocationZFieldNumber = 4,
    kEndLocationXFieldNumber = 5,
    kEndLocationYFieldNumber = 6,
    kEndLocationZFieldNumber = 7,
    kPlanBelongsFieldNumber = 1,
  };
  // .catalog_studio_message.ExpressionValuePair start_location_x = 2;
  bool has_start_location_x() const;
  private:
  bool _internal_has_start_location_x() const;
  public:
  void clear_start_location_x();
  const ::catalog_studio_message::ExpressionValuePair& start_location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_x();
  void set_allocated_start_location_x(::catalog_studio_message::ExpressionValuePair* start_location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_x();
  public:
  void unsafe_arena_set_allocated_start_location_x(
      ::catalog_studio_message::ExpressionValuePair* start_location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_x();

  // .catalog_studio_message.ExpressionValuePair start_location_y = 3;
  bool has_start_location_y() const;
  private:
  bool _internal_has_start_location_y() const;
  public:
  void clear_start_location_y();
  const ::catalog_studio_message::ExpressionValuePair& start_location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_y();
  void set_allocated_start_location_y(::catalog_studio_message::ExpressionValuePair* start_location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_y();
  public:
  void unsafe_arena_set_allocated_start_location_y(
      ::catalog_studio_message::ExpressionValuePair* start_location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_y();

  // .catalog_studio_message.ExpressionValuePair start_location_z = 4;
  bool has_start_location_z() const;
  private:
  bool _internal_has_start_location_z() const;
  public:
  void clear_start_location_z();
  const ::catalog_studio_message::ExpressionValuePair& start_location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_start_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_start_location_z();
  void set_allocated_start_location_z(::catalog_studio_message::ExpressionValuePair* start_location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_start_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_start_location_z();
  public:
  void unsafe_arena_set_allocated_start_location_z(
      ::catalog_studio_message::ExpressionValuePair* start_location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_start_location_z();

  // .catalog_studio_message.ExpressionValuePair end_location_x = 5;
  bool has_end_location_x() const;
  private:
  bool _internal_has_end_location_x() const;
  public:
  void clear_end_location_x();
  const ::catalog_studio_message::ExpressionValuePair& end_location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_x();
  void set_allocated_end_location_x(::catalog_studio_message::ExpressionValuePair* end_location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_x();
  public:
  void unsafe_arena_set_allocated_end_location_x(
      ::catalog_studio_message::ExpressionValuePair* end_location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_x();

  // .catalog_studio_message.ExpressionValuePair end_location_y = 6;
  bool has_end_location_y() const;
  private:
  bool _internal_has_end_location_y() const;
  public:
  void clear_end_location_y();
  const ::catalog_studio_message::ExpressionValuePair& end_location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_y();
  void set_allocated_end_location_y(::catalog_studio_message::ExpressionValuePair* end_location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_y();
  public:
  void unsafe_arena_set_allocated_end_location_y(
      ::catalog_studio_message::ExpressionValuePair* end_location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_y();

  // .catalog_studio_message.ExpressionValuePair end_location_z = 7;
  bool has_end_location_z() const;
  private:
  bool _internal_has_end_location_z() const;
  public:
  void clear_end_location_z();
  const ::catalog_studio_message::ExpressionValuePair& end_location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_end_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_end_location_z();
  void set_allocated_end_location_z(::catalog_studio_message::ExpressionValuePair* end_location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_end_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_end_location_z();
  public:
  void unsafe_arena_set_allocated_end_location_z(
      ::catalog_studio_message::ExpressionValuePair* end_location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_end_location_z();

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
  void clear_plan_belongs();
  ::catalog_studio_message::PlanPolygonBelongs plan_belongs() const;
  void set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  private:
  ::catalog_studio_message::PlanPolygonBelongs _internal_plan_belongs() const;
  void _internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.GeomtryRectanglePlanProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* start_location_x_;
  ::catalog_studio_message::ExpressionValuePair* start_location_y_;
  ::catalog_studio_message::ExpressionValuePair* start_location_z_;
  ::catalog_studio_message::ExpressionValuePair* end_location_x_;
  ::catalog_studio_message::ExpressionValuePair* end_location_y_;
  ::catalog_studio_message::ExpressionValuePair* end_location_z_;
  int plan_belongs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_GeomtryRectanglePlanProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeomtryRectanglePlanProperty

// .catalog_studio_message.PlanPolygonBelongs plan_belongs = 1;
inline void GeomtryRectanglePlanProperty::clear_plan_belongs() {
  plan_belongs_ = 0;
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryRectanglePlanProperty::_internal_plan_belongs() const {
  return static_cast< ::catalog_studio_message::PlanPolygonBelongs >(plan_belongs_);
}
inline ::catalog_studio_message::PlanPolygonBelongs GeomtryRectanglePlanProperty::plan_belongs() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.plan_belongs)
  return _internal_plan_belongs();
}
inline void GeomtryRectanglePlanProperty::_internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  
  plan_belongs_ = value;
}
inline void GeomtryRectanglePlanProperty::set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  _internal_set_plan_belongs(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.GeomtryRectanglePlanProperty.plan_belongs)
}

// .catalog_studio_message.ExpressionValuePair start_location_x = 2;
inline bool GeomtryRectanglePlanProperty::_internal_has_start_location_x() const {
  return this != internal_default_instance() && start_location_x_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_start_location_x() const {
  return _internal_has_start_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_start_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::start_location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_x)
  return _internal_start_location_x();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_start_location_x(
    ::catalog_studio_message::ExpressionValuePair* start_location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x_);
  }
  start_location_x_ = start_location_x;
  if (start_location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_start_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_x_;
  start_location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_start_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_x_;
  start_location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_start_location_x() {
  
  if (start_location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_x_ = p;
  }
  return start_location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_start_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_x)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_start_location_x(::catalog_studio_message::ExpressionValuePair* start_location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x_);
  }
  if (start_location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_x));
    if (message_arena != submessage_arena) {
      start_location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_x, submessage_arena);
    }
    
  } else {
    
  }
  start_location_x_ = start_location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_x)
}

// .catalog_studio_message.ExpressionValuePair start_location_y = 3;
inline bool GeomtryRectanglePlanProperty::_internal_has_start_location_y() const {
  return this != internal_default_instance() && start_location_y_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_start_location_y() const {
  return _internal_has_start_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_start_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::start_location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_y)
  return _internal_start_location_y();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_start_location_y(
    ::catalog_studio_message::ExpressionValuePair* start_location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y_);
  }
  start_location_y_ = start_location_y;
  if (start_location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_start_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_y_;
  start_location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_start_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_y_;
  start_location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_start_location_y() {
  
  if (start_location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_y_ = p;
  }
  return start_location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_start_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_y)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_start_location_y(::catalog_studio_message::ExpressionValuePair* start_location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y_);
  }
  if (start_location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_y));
    if (message_arena != submessage_arena) {
      start_location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_y, submessage_arena);
    }
    
  } else {
    
  }
  start_location_y_ = start_location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_y)
}

// .catalog_studio_message.ExpressionValuePair start_location_z = 4;
inline bool GeomtryRectanglePlanProperty::_internal_has_start_location_z() const {
  return this != internal_default_instance() && start_location_z_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_start_location_z() const {
  return _internal_has_start_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_start_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = start_location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::start_location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_z)
  return _internal_start_location_z();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_start_location_z(
    ::catalog_studio_message::ExpressionValuePair* start_location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z_);
  }
  start_location_z_ = start_location_z;
  if (start_location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_start_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_z_;
  start_location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_start_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = start_location_z_;
  start_location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_start_location_z() {
  
  if (start_location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    start_location_z_ = p;
  }
  return start_location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_start_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_start_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_z)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_start_location_z(::catalog_studio_message::ExpressionValuePair* start_location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z_);
  }
  if (start_location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_location_z));
    if (message_arena != submessage_arena) {
      start_location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_location_z, submessage_arena);
    }
    
  } else {
    
  }
  start_location_z_ = start_location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.start_location_z)
}

// .catalog_studio_message.ExpressionValuePair end_location_x = 5;
inline bool GeomtryRectanglePlanProperty::_internal_has_end_location_x() const {
  return this != internal_default_instance() && end_location_x_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_end_location_x() const {
  return _internal_has_end_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_end_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::end_location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_x)
  return _internal_end_location_x();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_end_location_x(
    ::catalog_studio_message::ExpressionValuePair* end_location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x_);
  }
  end_location_x_ = end_location_x;
  if (end_location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_end_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_x_;
  end_location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_end_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_x_;
  end_location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_end_location_x() {
  
  if (end_location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_x_ = p;
  }
  return end_location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_end_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_x)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_end_location_x(::catalog_studio_message::ExpressionValuePair* end_location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x_);
  }
  if (end_location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_x));
    if (message_arena != submessage_arena) {
      end_location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_x, submessage_arena);
    }
    
  } else {
    
  }
  end_location_x_ = end_location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_x)
}

// .catalog_studio_message.ExpressionValuePair end_location_y = 6;
inline bool GeomtryRectanglePlanProperty::_internal_has_end_location_y() const {
  return this != internal_default_instance() && end_location_y_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_end_location_y() const {
  return _internal_has_end_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_end_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::end_location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_y)
  return _internal_end_location_y();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_end_location_y(
    ::catalog_studio_message::ExpressionValuePair* end_location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y_);
  }
  end_location_y_ = end_location_y;
  if (end_location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_end_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_y_;
  end_location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_end_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_y_;
  end_location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_end_location_y() {
  
  if (end_location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_y_ = p;
  }
  return end_location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_end_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_y)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_end_location_y(::catalog_studio_message::ExpressionValuePair* end_location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y_);
  }
  if (end_location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_y));
    if (message_arena != submessage_arena) {
      end_location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_y, submessage_arena);
    }
    
  } else {
    
  }
  end_location_y_ = end_location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_y)
}

// .catalog_studio_message.ExpressionValuePair end_location_z = 7;
inline bool GeomtryRectanglePlanProperty::_internal_has_end_location_z() const {
  return this != internal_default_instance() && end_location_z_ != nullptr;
}
inline bool GeomtryRectanglePlanProperty::has_end_location_z() const {
  return _internal_has_end_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::_internal_end_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = end_location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& GeomtryRectanglePlanProperty::end_location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_z)
  return _internal_end_location_z();
}
inline void GeomtryRectanglePlanProperty::unsafe_arena_set_allocated_end_location_z(
    ::catalog_studio_message::ExpressionValuePair* end_location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z_);
  }
  end_location_z_ = end_location_z;
  if (end_location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::release_end_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_z_;
  end_location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::unsafe_arena_release_end_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = end_location_z_;
  end_location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::_internal_mutable_end_location_z() {
  
  if (end_location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    end_location_z_ = p;
  }
  return end_location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* GeomtryRectanglePlanProperty::mutable_end_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_end_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_z)
  return _msg;
}
inline void GeomtryRectanglePlanProperty::set_allocated_end_location_z(::catalog_studio_message::ExpressionValuePair* end_location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z_);
  }
  if (end_location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_location_z));
    if (message_arena != submessage_arena) {
      end_location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_location_z, submessage_arena);
    }
    
  } else {
    
  }
  end_location_z_ = end_location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.GeomtryRectanglePlanProperty.end_location_z)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_GeomtryRectanglePlanProperty_2eproto
