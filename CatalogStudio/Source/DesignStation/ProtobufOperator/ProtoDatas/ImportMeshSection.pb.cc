// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ImportMeshSection.proto

#include "ImportMeshSection.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ImportMeshSection::ImportMeshSection(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : section_mesh_(nullptr)
  , material_id_(nullptr)
  , id_(0){}
struct ImportMeshSectionDefaultTypeInternal {
  constexpr ImportMeshSectionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ImportMeshSectionDefaultTypeInternal() {}
  union {
    ImportMeshSection _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ImportMeshSectionDefaultTypeInternal _ImportMeshSection_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ImportMeshSection_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_ImportMeshSection_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ImportMeshSection_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ImportMeshSection_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ImportMeshSection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ImportMeshSection, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ImportMeshSection, section_mesh_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ImportMeshSection, material_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ImportMeshSection)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ImportMeshSection_default_instance_),
};

const char descriptor_table_protodef_ImportMeshSection_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\027ImportMeshSection.proto\022\026catalog_studi"
  "o_message\032\031ExpressionValuePair.proto\032\024Cu"
  "stomMeshInfo.proto\"\237\001\n\021ImportMeshSection"
  "\022\n\n\002id\030\001 \001(\005\022<\n\014section_mesh\030\002 \001(\0132&.cat"
  "alog_studio_message.CustomMeshInfo\022@\n\013ma"
  "terial_id\030\003 \001(\0132+.catalog_studio_message"
  ".ExpressionValuePairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_ImportMeshSection_2eproto_deps[2] = {
  &::descriptor_table_CustomMeshInfo_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ImportMeshSection_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ImportMeshSection_2eproto = {
  false, false, 268, descriptor_table_protodef_ImportMeshSection_2eproto, "ImportMeshSection.proto", 
  &descriptor_table_ImportMeshSection_2eproto_once, descriptor_table_ImportMeshSection_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_ImportMeshSection_2eproto::offsets,
  file_level_metadata_ImportMeshSection_2eproto, file_level_enum_descriptors_ImportMeshSection_2eproto, file_level_service_descriptors_ImportMeshSection_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ImportMeshSection_2eproto_getter() {
  return &descriptor_table_ImportMeshSection_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ImportMeshSection_2eproto(&descriptor_table_ImportMeshSection_2eproto);
namespace catalog_studio_message {

// ===================================================================

class ImportMeshSection::_Internal {
 public:
  static const ::catalog_studio_message::CustomMeshInfo& section_mesh(const ImportMeshSection* msg);
  static const ::catalog_studio_message::ExpressionValuePair& material_id(const ImportMeshSection* msg);
};

const ::catalog_studio_message::CustomMeshInfo&
ImportMeshSection::_Internal::section_mesh(const ImportMeshSection* msg) {
  return *msg->section_mesh_;
}
const ::catalog_studio_message::ExpressionValuePair&
ImportMeshSection::_Internal::material_id(const ImportMeshSection* msg) {
  return *msg->material_id_;
}
void ImportMeshSection::clear_section_mesh() {
  if (GetArenaForAllocation() == nullptr && section_mesh_ != nullptr) {
    delete section_mesh_;
  }
  section_mesh_ = nullptr;
}
void ImportMeshSection::clear_material_id() {
  if (GetArenaForAllocation() == nullptr && material_id_ != nullptr) {
    delete material_id_;
  }
  material_id_ = nullptr;
}
ImportMeshSection::ImportMeshSection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ImportMeshSection)
}
ImportMeshSection::ImportMeshSection(const ImportMeshSection& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_section_mesh()) {
    section_mesh_ = new ::catalog_studio_message::CustomMeshInfo(*from.section_mesh_);
  } else {
    section_mesh_ = nullptr;
  }
  if (from._internal_has_material_id()) {
    material_id_ = new ::catalog_studio_message::ExpressionValuePair(*from.material_id_);
  } else {
    material_id_ = nullptr;
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ImportMeshSection)
}

void ImportMeshSection::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&section_mesh_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&id_) -
    reinterpret_cast<char*>(&section_mesh_)) + sizeof(id_));
}

ImportMeshSection::~ImportMeshSection() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.ImportMeshSection)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ImportMeshSection::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete section_mesh_;
  if (this != internal_default_instance()) delete material_id_;
}

void ImportMeshSection::ArenaDtor(void* object) {
  ImportMeshSection* _this = reinterpret_cast< ImportMeshSection* >(object);
  (void)_this;
}
void ImportMeshSection::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImportMeshSection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ImportMeshSection::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.ImportMeshSection)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && section_mesh_ != nullptr) {
    delete section_mesh_;
  }
  section_mesh_ = nullptr;
  if (GetArenaForAllocation() == nullptr && material_id_ != nullptr) {
    delete material_id_;
  }
  material_id_ = nullptr;
  id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImportMeshSection::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.CustomMeshInfo section_mesh = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_section_mesh(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair material_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_material_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImportMeshSection::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.ImportMeshSection)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.CustomMeshInfo section_mesh = 2;
  if (this->_internal_has_section_mesh()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::section_mesh(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair material_id = 3;
  if (this->_internal_has_material_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::material_id(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.ImportMeshSection)
  return target;
}

size_t ImportMeshSection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.ImportMeshSection)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.CustomMeshInfo section_mesh = 2;
  if (this->_internal_has_section_mesh()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *section_mesh_);
  }

  // .catalog_studio_message.ExpressionValuePair material_id = 3;
  if (this->_internal_has_material_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *material_id_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ImportMeshSection::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ImportMeshSection::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ImportMeshSection::GetClassData() const { return &_class_data_; }

void ImportMeshSection::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ImportMeshSection *>(to)->MergeFrom(
      static_cast<const ImportMeshSection &>(from));
}


void ImportMeshSection::MergeFrom(const ImportMeshSection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.ImportMeshSection)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_section_mesh()) {
    _internal_mutable_section_mesh()->::catalog_studio_message::CustomMeshInfo::MergeFrom(from._internal_section_mesh());
  }
  if (from._internal_has_material_id()) {
    _internal_mutable_material_id()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_material_id());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ImportMeshSection::CopyFrom(const ImportMeshSection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.ImportMeshSection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImportMeshSection::IsInitialized() const {
  return true;
}

void ImportMeshSection::InternalSwap(ImportMeshSection* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImportMeshSection, id_)
      + sizeof(ImportMeshSection::id_)
      - PROTOBUF_FIELD_OFFSET(ImportMeshSection, section_mesh_)>(
          reinterpret_cast<char*>(&section_mesh_),
          reinterpret_cast<char*>(&other->section_mesh_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImportMeshSection::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ImportMeshSection_2eproto_getter, &descriptor_table_ImportMeshSection_2eproto_once,
      file_level_metadata_ImportMeshSection_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ImportMeshSection* Arena::CreateMaybeMessage< ::catalog_studio_message::ImportMeshSection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ImportMeshSection >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
