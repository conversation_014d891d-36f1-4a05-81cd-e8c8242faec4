// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ImportMeshSection.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ImportMeshSection_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ImportMeshSection_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
#include "CustomMeshInfo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ImportMeshSection_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ImportMeshSection_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ImportMeshSection_2eproto;
namespace catalog_studio_message {
class ImportMeshSection;
struct ImportMeshSectionDefaultTypeInternal;
extern ImportMeshSectionDefaultTypeInternal _ImportMeshSection_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::ImportMeshSection* Arena::CreateMaybeMessage<::catalog_studio_message::ImportMeshSection>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class ImportMeshSection final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.ImportMeshSection) */ {
 public:
  inline ImportMeshSection() : ImportMeshSection(nullptr) {}
  ~ImportMeshSection() override;
  explicit constexpr ImportMeshSection(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ImportMeshSection(const ImportMeshSection& from);
  ImportMeshSection(ImportMeshSection&& from) noexcept
    : ImportMeshSection() {
    *this = ::std::move(from);
  }

  inline ImportMeshSection& operator=(const ImportMeshSection& from) {
    CopyFrom(from);
    return *this;
  }
  inline ImportMeshSection& operator=(ImportMeshSection&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ImportMeshSection& default_instance() {
    return *internal_default_instance();
  }
  static inline const ImportMeshSection* internal_default_instance() {
    return reinterpret_cast<const ImportMeshSection*>(
               &_ImportMeshSection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ImportMeshSection& a, ImportMeshSection& b) {
    a.Swap(&b);
  }
  inline void Swap(ImportMeshSection* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ImportMeshSection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ImportMeshSection* New() const final {
    return new ImportMeshSection();
  }

  ImportMeshSection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ImportMeshSection>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ImportMeshSection& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ImportMeshSection& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ImportMeshSection* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.ImportMeshSection";
  }
  protected:
  explicit ImportMeshSection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSectionMeshFieldNumber = 2,
    kMaterialIdFieldNumber = 3,
    kIdFieldNumber = 1,
  };
  // .catalog_studio_message.CustomMeshInfo section_mesh = 2;
  bool has_section_mesh() const;
  private:
  bool _internal_has_section_mesh() const;
  public:
  void clear_section_mesh();
  const ::catalog_studio_message::CustomMeshInfo& section_mesh() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::CustomMeshInfo* release_section_mesh();
  ::catalog_studio_message::CustomMeshInfo* mutable_section_mesh();
  void set_allocated_section_mesh(::catalog_studio_message::CustomMeshInfo* section_mesh);
  private:
  const ::catalog_studio_message::CustomMeshInfo& _internal_section_mesh() const;
  ::catalog_studio_message::CustomMeshInfo* _internal_mutable_section_mesh();
  public:
  void unsafe_arena_set_allocated_section_mesh(
      ::catalog_studio_message::CustomMeshInfo* section_mesh);
  ::catalog_studio_message::CustomMeshInfo* unsafe_arena_release_section_mesh();

  // .catalog_studio_message.ExpressionValuePair material_id = 3;
  bool has_material_id() const;
  private:
  bool _internal_has_material_id() const;
  public:
  void clear_material_id();
  const ::catalog_studio_message::ExpressionValuePair& material_id() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_material_id();
  ::catalog_studio_message::ExpressionValuePair* mutable_material_id();
  void set_allocated_material_id(::catalog_studio_message::ExpressionValuePair* material_id);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_material_id() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_material_id();
  public:
  void unsafe_arena_set_allocated_material_id(
      ::catalog_studio_message::ExpressionValuePair* material_id);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_material_id();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.ImportMeshSection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::CustomMeshInfo* section_mesh_;
  ::catalog_studio_message::ExpressionValuePair* material_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ImportMeshSection_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ImportMeshSection

// int32 id = 1;
inline void ImportMeshSection::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ImportMeshSection::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ImportMeshSection::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ImportMeshSection.id)
  return _internal_id();
}
inline void ImportMeshSection::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void ImportMeshSection::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.ImportMeshSection.id)
}

// .catalog_studio_message.CustomMeshInfo section_mesh = 2;
inline bool ImportMeshSection::_internal_has_section_mesh() const {
  return this != internal_default_instance() && section_mesh_ != nullptr;
}
inline bool ImportMeshSection::has_section_mesh() const {
  return _internal_has_section_mesh();
}
inline const ::catalog_studio_message::CustomMeshInfo& ImportMeshSection::_internal_section_mesh() const {
  const ::catalog_studio_message::CustomMeshInfo* p = section_mesh_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::CustomMeshInfo&>(
      ::catalog_studio_message::_CustomMeshInfo_default_instance_);
}
inline const ::catalog_studio_message::CustomMeshInfo& ImportMeshSection::section_mesh() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ImportMeshSection.section_mesh)
  return _internal_section_mesh();
}
inline void ImportMeshSection::unsafe_arena_set_allocated_section_mesh(
    ::catalog_studio_message::CustomMeshInfo* section_mesh) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_mesh_);
  }
  section_mesh_ = section_mesh;
  if (section_mesh) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.ImportMeshSection.section_mesh)
}
inline ::catalog_studio_message::CustomMeshInfo* ImportMeshSection::release_section_mesh() {
  
  ::catalog_studio_message::CustomMeshInfo* temp = section_mesh_;
  section_mesh_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::CustomMeshInfo* ImportMeshSection::unsafe_arena_release_section_mesh() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ImportMeshSection.section_mesh)
  
  ::catalog_studio_message::CustomMeshInfo* temp = section_mesh_;
  section_mesh_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::CustomMeshInfo* ImportMeshSection::_internal_mutable_section_mesh() {
  
  if (section_mesh_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::CustomMeshInfo>(GetArenaForAllocation());
    section_mesh_ = p;
  }
  return section_mesh_;
}
inline ::catalog_studio_message::CustomMeshInfo* ImportMeshSection::mutable_section_mesh() {
  ::catalog_studio_message::CustomMeshInfo* _msg = _internal_mutable_section_mesh();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ImportMeshSection.section_mesh)
  return _msg;
}
inline void ImportMeshSection::set_allocated_section_mesh(::catalog_studio_message::CustomMeshInfo* section_mesh) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_mesh_);
  }
  if (section_mesh) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_mesh));
    if (message_arena != submessage_arena) {
      section_mesh = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, section_mesh, submessage_arena);
    }
    
  } else {
    
  }
  section_mesh_ = section_mesh;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ImportMeshSection.section_mesh)
}

// .catalog_studio_message.ExpressionValuePair material_id = 3;
inline bool ImportMeshSection::_internal_has_material_id() const {
  return this != internal_default_instance() && material_id_ != nullptr;
}
inline bool ImportMeshSection::has_material_id() const {
  return _internal_has_material_id();
}
inline const ::catalog_studio_message::ExpressionValuePair& ImportMeshSection::_internal_material_id() const {
  const ::catalog_studio_message::ExpressionValuePair* p = material_id_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& ImportMeshSection::material_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ImportMeshSection.material_id)
  return _internal_material_id();
}
inline void ImportMeshSection::unsafe_arena_set_allocated_material_id(
    ::catalog_studio_message::ExpressionValuePair* material_id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(material_id_);
  }
  material_id_ = material_id;
  if (material_id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.ImportMeshSection.material_id)
}
inline ::catalog_studio_message::ExpressionValuePair* ImportMeshSection::release_material_id() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = material_id_;
  material_id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ImportMeshSection::unsafe_arena_release_material_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ImportMeshSection.material_id)
  
  ::catalog_studio_message::ExpressionValuePair* temp = material_id_;
  material_id_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ImportMeshSection::_internal_mutable_material_id() {
  
  if (material_id_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    material_id_ = p;
  }
  return material_id_;
}
inline ::catalog_studio_message::ExpressionValuePair* ImportMeshSection::mutable_material_id() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_material_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ImportMeshSection.material_id)
  return _msg;
}
inline void ImportMeshSection::set_allocated_material_id(::catalog_studio_message::ExpressionValuePair* material_id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(material_id_);
  }
  if (material_id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(material_id));
    if (message_arena != submessage_arena) {
      material_id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, material_id, submessage_arena);
    }
    
  } else {
    
  }
  material_id_ = material_id;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ImportMeshSection.material_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ImportMeshSection_2eproto
