// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LocationProperty.proto

#include "LocationProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr LocationProperty::LocationProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : location_x_(nullptr)
  , location_y_(nullptr)
  , location_z_(nullptr){}
struct LocationPropertyDefaultTypeInternal {
  constexpr LocationPropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationPropertyDefaultTypeInternal() {}
  union {
    LocationProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationPropertyDefaultTypeInternal _LocationProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_LocationProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_LocationProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_LocationProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_LocationProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::LocationProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::LocationProperty, location_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::LocationProperty, location_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::LocationProperty, location_z_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::LocationProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_LocationProperty_default_instance_),
};

const char descriptor_table_protodef_LocationProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026LocationProperty.proto\022\026catalog_studio"
  "_message\032\031ExpressionValuePair.proto\"\325\001\n\020"
  "LocationProperty\022\?\n\nlocation_x\030\001 \001(\0132+.c"
  "atalog_studio_message.ExpressionValuePai"
  "r\022\?\n\nlocation_y\030\002 \001(\0132+.catalog_studio_m"
  "essage.ExpressionValuePair\022\?\n\nlocation_z"
  "\030\003 \001(\0132+.catalog_studio_message.Expressi"
  "onValuePairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_LocationProperty_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_LocationProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_LocationProperty_2eproto = {
  false, false, 299, descriptor_table_protodef_LocationProperty_2eproto, "LocationProperty.proto", 
  &descriptor_table_LocationProperty_2eproto_once, descriptor_table_LocationProperty_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_LocationProperty_2eproto::offsets,
  file_level_metadata_LocationProperty_2eproto, file_level_enum_descriptors_LocationProperty_2eproto, file_level_service_descriptors_LocationProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_LocationProperty_2eproto_getter() {
  return &descriptor_table_LocationProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_LocationProperty_2eproto(&descriptor_table_LocationProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class LocationProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& location_x(const LocationProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& location_y(const LocationProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& location_z(const LocationProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
LocationProperty::_Internal::location_x(const LocationProperty* msg) {
  return *msg->location_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
LocationProperty::_Internal::location_y(const LocationProperty* msg) {
  return *msg->location_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
LocationProperty::_Internal::location_z(const LocationProperty* msg) {
  return *msg->location_z_;
}
void LocationProperty::clear_location_x() {
  if (GetArenaForAllocation() == nullptr && location_x_ != nullptr) {
    delete location_x_;
  }
  location_x_ = nullptr;
}
void LocationProperty::clear_location_y() {
  if (GetArenaForAllocation() == nullptr && location_y_ != nullptr) {
    delete location_y_;
  }
  location_y_ = nullptr;
}
void LocationProperty::clear_location_z() {
  if (GetArenaForAllocation() == nullptr && location_z_ != nullptr) {
    delete location_z_;
  }
  location_z_ = nullptr;
}
LocationProperty::LocationProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.LocationProperty)
}
LocationProperty::LocationProperty(const LocationProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_location_x()) {
    location_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_x_);
  } else {
    location_x_ = nullptr;
  }
  if (from._internal_has_location_y()) {
    location_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_y_);
  } else {
    location_y_ = nullptr;
  }
  if (from._internal_has_location_z()) {
    location_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.location_z_);
  } else {
    location_z_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.LocationProperty)
}

void LocationProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&location_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&location_z_) -
    reinterpret_cast<char*>(&location_x_)) + sizeof(location_z_));
}

LocationProperty::~LocationProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.LocationProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LocationProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete location_x_;
  if (this != internal_default_instance()) delete location_y_;
  if (this != internal_default_instance()) delete location_z_;
}

void LocationProperty::ArenaDtor(void* object) {
  LocationProperty* _this = reinterpret_cast< LocationProperty* >(object);
  (void)_this;
}
void LocationProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LocationProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LocationProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.LocationProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && location_x_ != nullptr) {
    delete location_x_;
  }
  location_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && location_y_ != nullptr) {
    delete location_y_;
  }
  location_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && location_z_ != nullptr) {
    delete location_z_;
  }
  location_z_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LocationProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.ExpressionValuePair location_x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair location_y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair location_z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_location_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LocationProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.LocationProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair location_x = 1;
  if (this->_internal_has_location_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::location_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair location_y = 2;
  if (this->_internal_has_location_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::location_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair location_z = 3;
  if (this->_internal_has_location_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::location_z(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.LocationProperty)
  return target;
}

size_t LocationProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.LocationProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair location_x = 1;
  if (this->_internal_has_location_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_x_);
  }

  // .catalog_studio_message.ExpressionValuePair location_y = 2;
  if (this->_internal_has_location_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_y_);
  }

  // .catalog_studio_message.ExpressionValuePair location_z = 3;
  if (this->_internal_has_location_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_z_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LocationProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LocationProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LocationProperty::GetClassData() const { return &_class_data_; }

void LocationProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LocationProperty *>(to)->MergeFrom(
      static_cast<const LocationProperty &>(from));
}


void LocationProperty::MergeFrom(const LocationProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.LocationProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_location_x()) {
    _internal_mutable_location_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_x());
  }
  if (from._internal_has_location_y()) {
    _internal_mutable_location_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_y());
  }
  if (from._internal_has_location_z()) {
    _internal_mutable_location_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_location_z());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LocationProperty::CopyFrom(const LocationProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.LocationProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocationProperty::IsInitialized() const {
  return true;
}

void LocationProperty::InternalSwap(LocationProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LocationProperty, location_z_)
      + sizeof(LocationProperty::location_z_)
      - PROTOBUF_FIELD_OFFSET(LocationProperty, location_x_)>(
          reinterpret_cast<char*>(&location_x_),
          reinterpret_cast<char*>(&other->location_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LocationProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_LocationProperty_2eproto_getter, &descriptor_table_LocationProperty_2eproto_once,
      file_level_metadata_LocationProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::LocationProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::LocationProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::LocationProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
