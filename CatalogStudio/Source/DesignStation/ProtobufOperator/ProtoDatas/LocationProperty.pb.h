// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: LocationProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_LocationProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_LocationProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_LocationProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_LocationProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_LocationProperty_2eproto;
namespace catalog_studio_message {
class LocationProperty;
struct LocationPropertyDefaultTypeInternal;
extern LocationPropertyDefaultTypeInternal _LocationProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::LocationProperty* Arena::CreateMaybeMessage<::catalog_studio_message::LocationProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class LocationProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.LocationProperty) */ {
 public:
  inline LocationProperty() : LocationProperty(nullptr) {}
  ~LocationProperty() override;
  explicit constexpr LocationProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocationProperty(const LocationProperty& from);
  LocationProperty(LocationProperty&& from) noexcept
    : LocationProperty() {
    *this = ::std::move(from);
  }

  inline LocationProperty& operator=(const LocationProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocationProperty& operator=(LocationProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocationProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocationProperty* internal_default_instance() {
    return reinterpret_cast<const LocationProperty*>(
               &_LocationProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LocationProperty& a, LocationProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(LocationProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocationProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LocationProperty* New() const final {
    return new LocationProperty();
  }

  LocationProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LocationProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocationProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LocationProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocationProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.LocationProperty";
  }
  protected:
  explicit LocationProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocationXFieldNumber = 1,
    kLocationYFieldNumber = 2,
    kLocationZFieldNumber = 3,
  };
  // .catalog_studio_message.ExpressionValuePair location_x = 1;
  bool has_location_x() const;
  private:
  bool _internal_has_location_x() const;
  public:
  void clear_location_x();
  const ::catalog_studio_message::ExpressionValuePair& location_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_location_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_location_x();
  void set_allocated_location_x(::catalog_studio_message::ExpressionValuePair* location_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_location_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_location_x();
  public:
  void unsafe_arena_set_allocated_location_x(
      ::catalog_studio_message::ExpressionValuePair* location_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_location_x();

  // .catalog_studio_message.ExpressionValuePair location_y = 2;
  bool has_location_y() const;
  private:
  bool _internal_has_location_y() const;
  public:
  void clear_location_y();
  const ::catalog_studio_message::ExpressionValuePair& location_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_location_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_location_y();
  void set_allocated_location_y(::catalog_studio_message::ExpressionValuePair* location_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_location_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_location_y();
  public:
  void unsafe_arena_set_allocated_location_y(
      ::catalog_studio_message::ExpressionValuePair* location_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_location_y();

  // .catalog_studio_message.ExpressionValuePair location_z = 3;
  bool has_location_z() const;
  private:
  bool _internal_has_location_z() const;
  public:
  void clear_location_z();
  const ::catalog_studio_message::ExpressionValuePair& location_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_location_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_location_z();
  void set_allocated_location_z(::catalog_studio_message::ExpressionValuePair* location_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_location_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_location_z();
  public:
  void unsafe_arena_set_allocated_location_z(
      ::catalog_studio_message::ExpressionValuePair* location_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_location_z();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.LocationProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* location_x_;
  ::catalog_studio_message::ExpressionValuePair* location_y_;
  ::catalog_studio_message::ExpressionValuePair* location_z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_LocationProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LocationProperty

// .catalog_studio_message.ExpressionValuePair location_x = 1;
inline bool LocationProperty::_internal_has_location_x() const {
  return this != internal_default_instance() && location_x_ != nullptr;
}
inline bool LocationProperty::has_location_x() const {
  return _internal_has_location_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::_internal_location_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = location_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::location_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.LocationProperty.location_x)
  return _internal_location_x();
}
inline void LocationProperty::unsafe_arena_set_allocated_location_x(
    ::catalog_studio_message::ExpressionValuePair* location_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_x_);
  }
  location_x_ = location_x;
  if (location_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.LocationProperty.location_x)
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::release_location_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_x_;
  location_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::unsafe_arena_release_location_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.LocationProperty.location_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_x_;
  location_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::_internal_mutable_location_x() {
  
  if (location_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    location_x_ = p;
  }
  return location_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::mutable_location_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_location_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.LocationProperty.location_x)
  return _msg;
}
inline void LocationProperty::set_allocated_location_x(::catalog_studio_message::ExpressionValuePair* location_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_x_);
  }
  if (location_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_x));
    if (message_arena != submessage_arena) {
      location_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location_x, submessage_arena);
    }
    
  } else {
    
  }
  location_x_ = location_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.LocationProperty.location_x)
}

// .catalog_studio_message.ExpressionValuePair location_y = 2;
inline bool LocationProperty::_internal_has_location_y() const {
  return this != internal_default_instance() && location_y_ != nullptr;
}
inline bool LocationProperty::has_location_y() const {
  return _internal_has_location_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::_internal_location_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = location_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::location_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.LocationProperty.location_y)
  return _internal_location_y();
}
inline void LocationProperty::unsafe_arena_set_allocated_location_y(
    ::catalog_studio_message::ExpressionValuePair* location_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_y_);
  }
  location_y_ = location_y;
  if (location_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.LocationProperty.location_y)
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::release_location_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_y_;
  location_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::unsafe_arena_release_location_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.LocationProperty.location_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_y_;
  location_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::_internal_mutable_location_y() {
  
  if (location_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    location_y_ = p;
  }
  return location_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::mutable_location_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_location_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.LocationProperty.location_y)
  return _msg;
}
inline void LocationProperty::set_allocated_location_y(::catalog_studio_message::ExpressionValuePair* location_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_y_);
  }
  if (location_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_y));
    if (message_arena != submessage_arena) {
      location_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location_y, submessage_arena);
    }
    
  } else {
    
  }
  location_y_ = location_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.LocationProperty.location_y)
}

// .catalog_studio_message.ExpressionValuePair location_z = 3;
inline bool LocationProperty::_internal_has_location_z() const {
  return this != internal_default_instance() && location_z_ != nullptr;
}
inline bool LocationProperty::has_location_z() const {
  return _internal_has_location_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::_internal_location_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = location_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& LocationProperty::location_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.LocationProperty.location_z)
  return _internal_location_z();
}
inline void LocationProperty::unsafe_arena_set_allocated_location_z(
    ::catalog_studio_message::ExpressionValuePair* location_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_z_);
  }
  location_z_ = location_z;
  if (location_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.LocationProperty.location_z)
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::release_location_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_z_;
  location_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::unsafe_arena_release_location_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.LocationProperty.location_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = location_z_;
  location_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::_internal_mutable_location_z() {
  
  if (location_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    location_z_ = p;
  }
  return location_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* LocationProperty::mutable_location_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_location_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.LocationProperty.location_z)
  return _msg;
}
inline void LocationProperty::set_allocated_location_z(::catalog_studio_message::ExpressionValuePair* location_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_z_);
  }
  if (location_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_z));
    if (message_arena != submessage_arena) {
      location_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location_z, submessage_arena);
    }
    
  } else {
    
  }
  location_z_ = location_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.LocationProperty.location_z)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_LocationProperty_2eproto
