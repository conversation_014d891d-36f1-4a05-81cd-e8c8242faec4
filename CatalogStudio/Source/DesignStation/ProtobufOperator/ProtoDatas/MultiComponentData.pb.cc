// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MultiComponentData.proto

#include "MultiComponentData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr MultiComponentData::MultiComponentData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : component_items_(){}
struct MultiComponentDataDefaultTypeInternal {
  constexpr MultiComponentDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MultiComponentDataDefaultTypeInternal() {}
  union {
    MultiComponentData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MultiComponentDataDefaultTypeInternal _MultiComponentData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_MultiComponentData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_MultiComponentData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_MultiComponentData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_MultiComponentData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentData, component_items_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::MultiComponentData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_MultiComponentData_default_instance_),
};

const char descriptor_table_protodef_MultiComponentData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\030MultiComponentData.proto\022\026catalog_stud"
  "io_message\032\034MultiComponentDataItem.proto"
  "\"]\n\022MultiComponentData\022G\n\017component_item"
  "s\030\013 \003(\0132..catalog_studio_message.MultiCo"
  "mponentDataItemb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_MultiComponentData_2eproto_deps[1] = {
  &::descriptor_table_MultiComponentDataItem_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_MultiComponentData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_MultiComponentData_2eproto = {
  false, false, 183, descriptor_table_protodef_MultiComponentData_2eproto, "MultiComponentData.proto", 
  &descriptor_table_MultiComponentData_2eproto_once, descriptor_table_MultiComponentData_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_MultiComponentData_2eproto::offsets,
  file_level_metadata_MultiComponentData_2eproto, file_level_enum_descriptors_MultiComponentData_2eproto, file_level_service_descriptors_MultiComponentData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_MultiComponentData_2eproto_getter() {
  return &descriptor_table_MultiComponentData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_MultiComponentData_2eproto(&descriptor_table_MultiComponentData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class MultiComponentData::_Internal {
 public:
};

void MultiComponentData::clear_component_items() {
  component_items_.Clear();
}
MultiComponentData::MultiComponentData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  component_items_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.MultiComponentData)
}
MultiComponentData::MultiComponentData(const MultiComponentData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      component_items_(from.component_items_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.MultiComponentData)
}

void MultiComponentData::SharedCtor() {
}

MultiComponentData::~MultiComponentData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.MultiComponentData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MultiComponentData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MultiComponentData::ArenaDtor(void* object) {
  MultiComponentData* _this = reinterpret_cast< MultiComponentData* >(object);
  (void)_this;
}
void MultiComponentData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MultiComponentData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MultiComponentData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.MultiComponentData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  component_items_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MultiComponentData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.MultiComponentDataItem component_items = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_component_items(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MultiComponentData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.MultiComponentData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.MultiComponentDataItem component_items = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_component_items_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, this->_internal_component_items(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.MultiComponentData)
  return target;
}

size_t MultiComponentData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.MultiComponentData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.MultiComponentDataItem component_items = 11;
  total_size += 1UL * this->_internal_component_items_size();
  for (const auto& msg : this->component_items_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MultiComponentData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MultiComponentData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MultiComponentData::GetClassData() const { return &_class_data_; }

void MultiComponentData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MultiComponentData *>(to)->MergeFrom(
      static_cast<const MultiComponentData &>(from));
}


void MultiComponentData::MergeFrom(const MultiComponentData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.MultiComponentData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  component_items_.MergeFrom(from.component_items_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MultiComponentData::CopyFrom(const MultiComponentData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.MultiComponentData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MultiComponentData::IsInitialized() const {
  return true;
}

void MultiComponentData::InternalSwap(MultiComponentData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  component_items_.InternalSwap(&other->component_items_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MultiComponentData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_MultiComponentData_2eproto_getter, &descriptor_table_MultiComponentData_2eproto_once,
      file_level_metadata_MultiComponentData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::MultiComponentData* Arena::CreateMaybeMessage< ::catalog_studio_message::MultiComponentData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::MultiComponentData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
