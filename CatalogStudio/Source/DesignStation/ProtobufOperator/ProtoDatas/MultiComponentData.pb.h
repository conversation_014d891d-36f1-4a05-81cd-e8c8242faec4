// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MultiComponentData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_MultiComponentData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_MultiComponentData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "MultiComponentDataItem.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_MultiComponentData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_MultiComponentData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_MultiComponentData_2eproto;
namespace catalog_studio_message {
class MultiComponentData;
struct MultiComponentDataDefaultTypeInternal;
extern MultiComponentDataDefaultTypeInternal _MultiComponentData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::MultiComponentData* Arena::CreateMaybeMessage<::catalog_studio_message::MultiComponentData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class MultiComponentData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.MultiComponentData) */ {
 public:
  inline MultiComponentData() : MultiComponentData(nullptr) {}
  ~MultiComponentData() override;
  explicit constexpr MultiComponentData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MultiComponentData(const MultiComponentData& from);
  MultiComponentData(MultiComponentData&& from) noexcept
    : MultiComponentData() {
    *this = ::std::move(from);
  }

  inline MultiComponentData& operator=(const MultiComponentData& from) {
    CopyFrom(from);
    return *this;
  }
  inline MultiComponentData& operator=(MultiComponentData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MultiComponentData& default_instance() {
    return *internal_default_instance();
  }
  static inline const MultiComponentData* internal_default_instance() {
    return reinterpret_cast<const MultiComponentData*>(
               &_MultiComponentData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MultiComponentData& a, MultiComponentData& b) {
    a.Swap(&b);
  }
  inline void Swap(MultiComponentData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MultiComponentData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MultiComponentData* New() const final {
    return new MultiComponentData();
  }

  MultiComponentData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MultiComponentData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MultiComponentData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MultiComponentData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiComponentData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.MultiComponentData";
  }
  protected:
  explicit MultiComponentData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentItemsFieldNumber = 11,
  };
  // repeated .catalog_studio_message.MultiComponentDataItem component_items = 11;
  int component_items_size() const;
  private:
  int _internal_component_items_size() const;
  public:
  void clear_component_items();
  ::catalog_studio_message::MultiComponentDataItem* mutable_component_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >*
      mutable_component_items();
  private:
  const ::catalog_studio_message::MultiComponentDataItem& _internal_component_items(int index) const;
  ::catalog_studio_message::MultiComponentDataItem* _internal_add_component_items();
  public:
  const ::catalog_studio_message::MultiComponentDataItem& component_items(int index) const;
  ::catalog_studio_message::MultiComponentDataItem* add_component_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >&
      component_items() const;

  // @@protoc_insertion_point(class_scope:catalog_studio_message.MultiComponentData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem > component_items_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_MultiComponentData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MultiComponentData

// repeated .catalog_studio_message.MultiComponentDataItem component_items = 11;
inline int MultiComponentData::_internal_component_items_size() const {
  return component_items_.size();
}
inline int MultiComponentData::component_items_size() const {
  return _internal_component_items_size();
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentData::mutable_component_items(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentData.component_items)
  return component_items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >*
MultiComponentData::mutable_component_items() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.MultiComponentData.component_items)
  return &component_items_;
}
inline const ::catalog_studio_message::MultiComponentDataItem& MultiComponentData::_internal_component_items(int index) const {
  return component_items_.Get(index);
}
inline const ::catalog_studio_message::MultiComponentDataItem& MultiComponentData::component_items(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentData.component_items)
  return _internal_component_items(index);
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentData::_internal_add_component_items() {
  return component_items_.Add();
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentData::add_component_items() {
  ::catalog_studio_message::MultiComponentDataItem* _add = _internal_add_component_items();
  // @@protoc_insertion_point(field_add:catalog_studio_message.MultiComponentData.component_items)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >&
MultiComponentData::component_items() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.MultiComponentData.component_items)
  return component_items_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_MultiComponentData_2eproto
