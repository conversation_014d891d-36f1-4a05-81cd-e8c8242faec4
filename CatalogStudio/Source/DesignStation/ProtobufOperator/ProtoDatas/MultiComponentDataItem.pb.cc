// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MultiComponentDataItem.proto

#include "MultiComponentDataItem.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr MultiComponentDataItem::MultiComponentDataItem(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : component_parameters_()
  , child_components_()
  , component_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , code_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , single_component_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , component_uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , component_visibility_(nullptr)
  , component_id_(nullptr)
  , component_location_(nullptr)
  , component_rotation_(nullptr)
  , component_scale_(nullptr)
  , single_component_data_(nullptr)
  , id_(0)
  , key_id_(0)
  , component_type_(0)
  , user_hide_(false)
  , model_type_(0){}
struct MultiComponentDataItemDefaultTypeInternal {
  constexpr MultiComponentDataItemDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MultiComponentDataItemDefaultTypeInternal() {}
  union {
    MultiComponentDataItem _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MultiComponentDataItemDefaultTypeInternal _MultiComponentDataItem_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_MultiComponentDataItem_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_MultiComponentDataItem_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_MultiComponentDataItem_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_MultiComponentDataItem_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_visibility_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, description_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, code_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_location_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_rotation_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_scale_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_parameters_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, key_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, single_component_path_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, single_component_data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, component_uuid_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, user_hide_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, child_components_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::MultiComponentDataItem, model_type_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::MultiComponentDataItem)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_MultiComponentDataItem_default_instance_),
};

const char descriptor_table_protodef_MultiComponentDataItem_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034MultiComponentDataItem.proto\022\026catalog_"
  "studio_message\032\023ParameterData.proto\032\031Exp"
  "ressionValuePair.proto\032\026LocationProperty"
  ".proto\032\026RotationProperty.proto\032\023ScalePro"
  "perty.proto\032\035SingleComponentProperty.pro"
  "to\"\260\006\n\026MultiComponentDataItem\022\n\n\002id\030\001 \001("
  "\005\022\026\n\016component_name\030\002 \001(\t\022I\n\024component_v"
  "isibility\030\003 \001(\0132+.catalog_studio_message"
  ".ExpressionValuePair\022A\n\014component_id\030\004 \001"
  "(\0132+.catalog_studio_message.ExpressionVa"
  "luePair\022\023\n\013description\030\005 \001(\t\022\014\n\004code\030\006 \001"
  "(\t\022\020\n\010code_exp\030\007 \001(\t\022D\n\022component_locati"
  "on\030\010 \001(\0132(.catalog_studio_message.Locati"
  "onProperty\022D\n\022component_rotation\030\t \001(\0132("
  ".catalog_studio_message.RotationProperty"
  "\022>\n\017component_scale\030\n \001(\0132%.catalog_stud"
  "io_message.ScaleProperty\022C\n\024component_pa"
  "rameters\030\013 \003(\0132%.catalog_studio_message."
  "ParameterData\022\016\n\006key_id\030\014 \001(\005\022\026\n\016compone"
  "nt_type\030\r \001(\005\022\035\n\025single_component_path\030\016"
  " \001(\t\022N\n\025single_component_data\030\017 \001(\0132/.ca"
  "talog_studio_message.SingleComponentProp"
  "erty\022\026\n\016component_uuid\030\020 \001(\t\022\021\n\tuser_hid"
  "e\030\021 \001(\010\022H\n\020child_components\030\022 \003(\0132..cata"
  "log_studio_message.MultiComponentDataIte"
  "m\022\022\n\nmodel_type\030\023 \001(\005b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_MultiComponentDataItem_2eproto_deps[6] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
  &::descriptor_table_LocationProperty_2eproto,
  &::descriptor_table_ParameterData_2eproto,
  &::descriptor_table_RotationProperty_2eproto,
  &::descriptor_table_ScaleProperty_2eproto,
  &::descriptor_table_SingleComponentProperty_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_MultiComponentDataItem_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_MultiComponentDataItem_2eproto = {
  false, false, 1029, descriptor_table_protodef_MultiComponentDataItem_2eproto, "MultiComponentDataItem.proto", 
  &descriptor_table_MultiComponentDataItem_2eproto_once, descriptor_table_MultiComponentDataItem_2eproto_deps, 6, 1,
  schemas, file_default_instances, TableStruct_MultiComponentDataItem_2eproto::offsets,
  file_level_metadata_MultiComponentDataItem_2eproto, file_level_enum_descriptors_MultiComponentDataItem_2eproto, file_level_service_descriptors_MultiComponentDataItem_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_MultiComponentDataItem_2eproto_getter() {
  return &descriptor_table_MultiComponentDataItem_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_MultiComponentDataItem_2eproto(&descriptor_table_MultiComponentDataItem_2eproto);
namespace catalog_studio_message {

// ===================================================================

class MultiComponentDataItem::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& component_visibility(const MultiComponentDataItem* msg);
  static const ::catalog_studio_message::ExpressionValuePair& component_id(const MultiComponentDataItem* msg);
  static const ::catalog_studio_message::LocationProperty& component_location(const MultiComponentDataItem* msg);
  static const ::catalog_studio_message::RotationProperty& component_rotation(const MultiComponentDataItem* msg);
  static const ::catalog_studio_message::ScaleProperty& component_scale(const MultiComponentDataItem* msg);
  static const ::catalog_studio_message::SingleComponentProperty& single_component_data(const MultiComponentDataItem* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
MultiComponentDataItem::_Internal::component_visibility(const MultiComponentDataItem* msg) {
  return *msg->component_visibility_;
}
const ::catalog_studio_message::ExpressionValuePair&
MultiComponentDataItem::_Internal::component_id(const MultiComponentDataItem* msg) {
  return *msg->component_id_;
}
const ::catalog_studio_message::LocationProperty&
MultiComponentDataItem::_Internal::component_location(const MultiComponentDataItem* msg) {
  return *msg->component_location_;
}
const ::catalog_studio_message::RotationProperty&
MultiComponentDataItem::_Internal::component_rotation(const MultiComponentDataItem* msg) {
  return *msg->component_rotation_;
}
const ::catalog_studio_message::ScaleProperty&
MultiComponentDataItem::_Internal::component_scale(const MultiComponentDataItem* msg) {
  return *msg->component_scale_;
}
const ::catalog_studio_message::SingleComponentProperty&
MultiComponentDataItem::_Internal::single_component_data(const MultiComponentDataItem* msg) {
  return *msg->single_component_data_;
}
void MultiComponentDataItem::clear_component_visibility() {
  if (GetArenaForAllocation() == nullptr && component_visibility_ != nullptr) {
    delete component_visibility_;
  }
  component_visibility_ = nullptr;
}
void MultiComponentDataItem::clear_component_id() {
  if (GetArenaForAllocation() == nullptr && component_id_ != nullptr) {
    delete component_id_;
  }
  component_id_ = nullptr;
}
void MultiComponentDataItem::clear_component_location() {
  if (GetArenaForAllocation() == nullptr && component_location_ != nullptr) {
    delete component_location_;
  }
  component_location_ = nullptr;
}
void MultiComponentDataItem::clear_component_rotation() {
  if (GetArenaForAllocation() == nullptr && component_rotation_ != nullptr) {
    delete component_rotation_;
  }
  component_rotation_ = nullptr;
}
void MultiComponentDataItem::clear_component_scale() {
  if (GetArenaForAllocation() == nullptr && component_scale_ != nullptr) {
    delete component_scale_;
  }
  component_scale_ = nullptr;
}
void MultiComponentDataItem::clear_component_parameters() {
  component_parameters_.Clear();
}
void MultiComponentDataItem::clear_single_component_data() {
  if (GetArenaForAllocation() == nullptr && single_component_data_ != nullptr) {
    delete single_component_data_;
  }
  single_component_data_ = nullptr;
}
MultiComponentDataItem::MultiComponentDataItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  component_parameters_(arena),
  child_components_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.MultiComponentDataItem)
}
MultiComponentDataItem::MultiComponentDataItem(const MultiComponentDataItem& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      component_parameters_(from.component_parameters_),
      child_components_(from.child_components_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  component_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_component_name().empty()) {
    component_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_component_name(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_code().empty()) {
    code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_code(), 
      GetArenaForAllocation());
  }
  code_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_code_exp().empty()) {
    code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_code_exp(), 
      GetArenaForAllocation());
  }
  single_component_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_single_component_path().empty()) {
    single_component_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_single_component_path(), 
      GetArenaForAllocation());
  }
  component_uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_component_uuid().empty()) {
    component_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_component_uuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_component_visibility()) {
    component_visibility_ = new ::catalog_studio_message::ExpressionValuePair(*from.component_visibility_);
  } else {
    component_visibility_ = nullptr;
  }
  if (from._internal_has_component_id()) {
    component_id_ = new ::catalog_studio_message::ExpressionValuePair(*from.component_id_);
  } else {
    component_id_ = nullptr;
  }
  if (from._internal_has_component_location()) {
    component_location_ = new ::catalog_studio_message::LocationProperty(*from.component_location_);
  } else {
    component_location_ = nullptr;
  }
  if (from._internal_has_component_rotation()) {
    component_rotation_ = new ::catalog_studio_message::RotationProperty(*from.component_rotation_);
  } else {
    component_rotation_ = nullptr;
  }
  if (from._internal_has_component_scale()) {
    component_scale_ = new ::catalog_studio_message::ScaleProperty(*from.component_scale_);
  } else {
    component_scale_ = nullptr;
  }
  if (from._internal_has_single_component_data()) {
    single_component_data_ = new ::catalog_studio_message::SingleComponentProperty(*from.single_component_data_);
  } else {
    single_component_data_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&model_type_) -
    reinterpret_cast<char*>(&id_)) + sizeof(model_type_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.MultiComponentDataItem)
}

void MultiComponentDataItem::SharedCtor() {
component_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
code_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
single_component_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
component_uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&component_visibility_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&model_type_) -
    reinterpret_cast<char*>(&component_visibility_)) + sizeof(model_type_));
}

MultiComponentDataItem::~MultiComponentDataItem() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.MultiComponentDataItem)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MultiComponentDataItem::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  component_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  code_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  single_component_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  component_uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete component_visibility_;
  if (this != internal_default_instance()) delete component_id_;
  if (this != internal_default_instance()) delete component_location_;
  if (this != internal_default_instance()) delete component_rotation_;
  if (this != internal_default_instance()) delete component_scale_;
  if (this != internal_default_instance()) delete single_component_data_;
}

void MultiComponentDataItem::ArenaDtor(void* object) {
  MultiComponentDataItem* _this = reinterpret_cast< MultiComponentDataItem* >(object);
  (void)_this;
}
void MultiComponentDataItem::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MultiComponentDataItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MultiComponentDataItem::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.MultiComponentDataItem)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  component_parameters_.Clear();
  child_components_.Clear();
  component_name_.ClearToEmpty();
  description_.ClearToEmpty();
  code_.ClearToEmpty();
  code_exp_.ClearToEmpty();
  single_component_path_.ClearToEmpty();
  component_uuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && component_visibility_ != nullptr) {
    delete component_visibility_;
  }
  component_visibility_ = nullptr;
  if (GetArenaForAllocation() == nullptr && component_id_ != nullptr) {
    delete component_id_;
  }
  component_id_ = nullptr;
  if (GetArenaForAllocation() == nullptr && component_location_ != nullptr) {
    delete component_location_;
  }
  component_location_ = nullptr;
  if (GetArenaForAllocation() == nullptr && component_rotation_ != nullptr) {
    delete component_rotation_;
  }
  component_rotation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && component_scale_ != nullptr) {
    delete component_scale_;
  }
  component_scale_ = nullptr;
  if (GetArenaForAllocation() == nullptr && single_component_data_ != nullptr) {
    delete single_component_data_;
  }
  single_component_data_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&model_type_) -
      reinterpret_cast<char*>(&id_)) + sizeof(model_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MultiComponentDataItem::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string component_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_component_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.component_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair component_visibility = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_visibility(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair component_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string code = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string code_exp = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_code_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.code_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.LocationProperty component_location = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.RotationProperty component_rotation = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_rotation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ScaleProperty component_scale = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_scale(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ParameterData component_parameters = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_component_parameters(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 key_id = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          key_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 component_type = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          component_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string single_component_path = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          auto str = _internal_mutable_single_component_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.single_component_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SingleComponentProperty single_component_data = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr = ctx->ParseMessage(_internal_mutable_single_component_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string component_uuid = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          auto str = _internal_mutable_component_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.MultiComponentDataItem.component_uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool user_hide = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          user_hide_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.MultiComponentDataItem child_components = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_child_components(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 model_type = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          model_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MultiComponentDataItem::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.MultiComponentDataItem)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // string component_name = 2;
  if (!this->_internal_component_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_component_name().data(), static_cast<int>(this->_internal_component_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.component_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_component_name(), target);
  }

  // .catalog_studio_message.ExpressionValuePair component_visibility = 3;
  if (this->_internal_has_component_visibility()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::component_visibility(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair component_id = 4;
  if (this->_internal_has_component_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::component_id(this), target, stream);
  }

  // string description = 5;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.description");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_description(), target);
  }

  // string code = 6;
  if (!this->_internal_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_code().data(), static_cast<int>(this->_internal_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.code");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_code(), target);
  }

  // string code_exp = 7;
  if (!this->_internal_code_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_code_exp().data(), static_cast<int>(this->_internal_code_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.code_exp");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_code_exp(), target);
  }

  // .catalog_studio_message.LocationProperty component_location = 8;
  if (this->_internal_has_component_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::component_location(this), target, stream);
  }

  // .catalog_studio_message.RotationProperty component_rotation = 9;
  if (this->_internal_has_component_rotation()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::component_rotation(this), target, stream);
  }

  // .catalog_studio_message.ScaleProperty component_scale = 10;
  if (this->_internal_has_component_scale()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::component_scale(this), target, stream);
  }

  // repeated .catalog_studio_message.ParameterData component_parameters = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_component_parameters_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, this->_internal_component_parameters(i), target, stream);
  }

  // int32 key_id = 12;
  if (this->_internal_key_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(12, this->_internal_key_id(), target);
  }

  // int32 component_type = 13;
  if (this->_internal_component_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(13, this->_internal_component_type(), target);
  }

  // string single_component_path = 14;
  if (!this->_internal_single_component_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_single_component_path().data(), static_cast<int>(this->_internal_single_component_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.single_component_path");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_single_component_path(), target);
  }

  // .catalog_studio_message.SingleComponentProperty single_component_data = 15;
  if (this->_internal_has_single_component_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        15, _Internal::single_component_data(this), target, stream);
  }

  // string component_uuid = 16;
  if (!this->_internal_component_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_component_uuid().data(), static_cast<int>(this->_internal_component_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.MultiComponentDataItem.component_uuid");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_component_uuid(), target);
  }

  // bool user_hide = 17;
  if (this->_internal_user_hide() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(17, this->_internal_user_hide(), target);
  }

  // repeated .catalog_studio_message.MultiComponentDataItem child_components = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_child_components_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_child_components(i), target, stream);
  }

  // int32 model_type = 19;
  if (this->_internal_model_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(19, this->_internal_model_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.MultiComponentDataItem)
  return target;
}

size_t MultiComponentDataItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.MultiComponentDataItem)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.ParameterData component_parameters = 11;
  total_size += 1UL * this->_internal_component_parameters_size();
  for (const auto& msg : this->component_parameters_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.MultiComponentDataItem child_components = 18;
  total_size += 2UL * this->_internal_child_components_size();
  for (const auto& msg : this->child_components_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string component_name = 2;
  if (!this->_internal_component_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_component_name());
  }

  // string description = 5;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string code = 6;
  if (!this->_internal_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_code());
  }

  // string code_exp = 7;
  if (!this->_internal_code_exp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_code_exp());
  }

  // string single_component_path = 14;
  if (!this->_internal_single_component_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_single_component_path());
  }

  // string component_uuid = 16;
  if (!this->_internal_component_uuid().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_component_uuid());
  }

  // .catalog_studio_message.ExpressionValuePair component_visibility = 3;
  if (this->_internal_has_component_visibility()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_visibility_);
  }

  // .catalog_studio_message.ExpressionValuePair component_id = 4;
  if (this->_internal_has_component_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_id_);
  }

  // .catalog_studio_message.LocationProperty component_location = 8;
  if (this->_internal_has_component_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_location_);
  }

  // .catalog_studio_message.RotationProperty component_rotation = 9;
  if (this->_internal_has_component_rotation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_rotation_);
  }

  // .catalog_studio_message.ScaleProperty component_scale = 10;
  if (this->_internal_has_component_scale()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_scale_);
  }

  // .catalog_studio_message.SingleComponentProperty single_component_data = 15;
  if (this->_internal_has_single_component_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *single_component_data_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // int32 key_id = 12;
  if (this->_internal_key_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_key_id());
  }

  // int32 component_type = 13;
  if (this->_internal_component_type() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_component_type());
  }

  // bool user_hide = 17;
  if (this->_internal_user_hide() != 0) {
    total_size += 2 + 1;
  }

  // int32 model_type = 19;
  if (this->_internal_model_type() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_model_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MultiComponentDataItem::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MultiComponentDataItem::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MultiComponentDataItem::GetClassData() const { return &_class_data_; }

void MultiComponentDataItem::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MultiComponentDataItem *>(to)->MergeFrom(
      static_cast<const MultiComponentDataItem &>(from));
}


void MultiComponentDataItem::MergeFrom(const MultiComponentDataItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.MultiComponentDataItem)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  component_parameters_.MergeFrom(from.component_parameters_);
  child_components_.MergeFrom(from.child_components_);
  if (!from._internal_component_name().empty()) {
    _internal_set_component_name(from._internal_component_name());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_code().empty()) {
    _internal_set_code(from._internal_code());
  }
  if (!from._internal_code_exp().empty()) {
    _internal_set_code_exp(from._internal_code_exp());
  }
  if (!from._internal_single_component_path().empty()) {
    _internal_set_single_component_path(from._internal_single_component_path());
  }
  if (!from._internal_component_uuid().empty()) {
    _internal_set_component_uuid(from._internal_component_uuid());
  }
  if (from._internal_has_component_visibility()) {
    _internal_mutable_component_visibility()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_component_visibility());
  }
  if (from._internal_has_component_id()) {
    _internal_mutable_component_id()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_component_id());
  }
  if (from._internal_has_component_location()) {
    _internal_mutable_component_location()->::catalog_studio_message::LocationProperty::MergeFrom(from._internal_component_location());
  }
  if (from._internal_has_component_rotation()) {
    _internal_mutable_component_rotation()->::catalog_studio_message::RotationProperty::MergeFrom(from._internal_component_rotation());
  }
  if (from._internal_has_component_scale()) {
    _internal_mutable_component_scale()->::catalog_studio_message::ScaleProperty::MergeFrom(from._internal_component_scale());
  }
  if (from._internal_has_single_component_data()) {
    _internal_mutable_single_component_data()->::catalog_studio_message::SingleComponentProperty::MergeFrom(from._internal_single_component_data());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_key_id() != 0) {
    _internal_set_key_id(from._internal_key_id());
  }
  if (from._internal_component_type() != 0) {
    _internal_set_component_type(from._internal_component_type());
  }
  if (from._internal_user_hide() != 0) {
    _internal_set_user_hide(from._internal_user_hide());
  }
  if (from._internal_model_type() != 0) {
    _internal_set_model_type(from._internal_model_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MultiComponentDataItem::CopyFrom(const MultiComponentDataItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.MultiComponentDataItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MultiComponentDataItem::IsInitialized() const {
  return true;
}

void MultiComponentDataItem::InternalSwap(MultiComponentDataItem* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  component_parameters_.InternalSwap(&other->component_parameters_);
  child_components_.InternalSwap(&other->child_components_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &component_name_, lhs_arena,
      &other->component_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &code_, lhs_arena,
      &other->code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &code_exp_, lhs_arena,
      &other->code_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &single_component_path_, lhs_arena,
      &other->single_component_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &component_uuid_, lhs_arena,
      &other->component_uuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MultiComponentDataItem, model_type_)
      + sizeof(MultiComponentDataItem::model_type_)
      - PROTOBUF_FIELD_OFFSET(MultiComponentDataItem, component_visibility_)>(
          reinterpret_cast<char*>(&component_visibility_),
          reinterpret_cast<char*>(&other->component_visibility_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MultiComponentDataItem::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_MultiComponentDataItem_2eproto_getter, &descriptor_table_MultiComponentDataItem_2eproto_once,
      file_level_metadata_MultiComponentDataItem_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::MultiComponentDataItem* Arena::CreateMaybeMessage< ::catalog_studio_message::MultiComponentDataItem >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::MultiComponentDataItem >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
