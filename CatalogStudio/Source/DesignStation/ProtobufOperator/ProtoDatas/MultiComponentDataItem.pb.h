// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MultiComponentDataItem.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_MultiComponentDataItem_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_MultiComponentDataItem_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ParameterData.pb.h"
#include "ExpressionValuePair.pb.h"
#include "LocationProperty.pb.h"
#include "RotationProperty.pb.h"
#include "ScaleProperty.pb.h"
#include "SingleComponentProperty.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_MultiComponentDataItem_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_MultiComponentDataItem_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_MultiComponentDataItem_2eproto;
namespace catalog_studio_message {
class MultiComponentDataItem;
struct MultiComponentDataItemDefaultTypeInternal;
extern MultiComponentDataItemDefaultTypeInternal _MultiComponentDataItem_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::MultiComponentDataItem* Arena::CreateMaybeMessage<::catalog_studio_message::MultiComponentDataItem>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class MultiComponentDataItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.MultiComponentDataItem) */ {
 public:
  inline MultiComponentDataItem() : MultiComponentDataItem(nullptr) {}
  ~MultiComponentDataItem() override;
  explicit constexpr MultiComponentDataItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MultiComponentDataItem(const MultiComponentDataItem& from);
  MultiComponentDataItem(MultiComponentDataItem&& from) noexcept
    : MultiComponentDataItem() {
    *this = ::std::move(from);
  }

  inline MultiComponentDataItem& operator=(const MultiComponentDataItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline MultiComponentDataItem& operator=(MultiComponentDataItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MultiComponentDataItem& default_instance() {
    return *internal_default_instance();
  }
  static inline const MultiComponentDataItem* internal_default_instance() {
    return reinterpret_cast<const MultiComponentDataItem*>(
               &_MultiComponentDataItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MultiComponentDataItem& a, MultiComponentDataItem& b) {
    a.Swap(&b);
  }
  inline void Swap(MultiComponentDataItem* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MultiComponentDataItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MultiComponentDataItem* New() const final {
    return new MultiComponentDataItem();
  }

  MultiComponentDataItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MultiComponentDataItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MultiComponentDataItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MultiComponentDataItem& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiComponentDataItem* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.MultiComponentDataItem";
  }
  protected:
  explicit MultiComponentDataItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentParametersFieldNumber = 11,
    kChildComponentsFieldNumber = 18,
    kComponentNameFieldNumber = 2,
    kDescriptionFieldNumber = 5,
    kCodeFieldNumber = 6,
    kCodeExpFieldNumber = 7,
    kSingleComponentPathFieldNumber = 14,
    kComponentUuidFieldNumber = 16,
    kComponentVisibilityFieldNumber = 3,
    kComponentIdFieldNumber = 4,
    kComponentLocationFieldNumber = 8,
    kComponentRotationFieldNumber = 9,
    kComponentScaleFieldNumber = 10,
    kSingleComponentDataFieldNumber = 15,
    kIdFieldNumber = 1,
    kKeyIdFieldNumber = 12,
    kComponentTypeFieldNumber = 13,
    kUserHideFieldNumber = 17,
    kModelTypeFieldNumber = 19,
  };
  // repeated .catalog_studio_message.ParameterData component_parameters = 11;
  int component_parameters_size() const;
  private:
  int _internal_component_parameters_size() const;
  public:
  void clear_component_parameters();
  ::catalog_studio_message::ParameterData* mutable_component_parameters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
      mutable_component_parameters();
  private:
  const ::catalog_studio_message::ParameterData& _internal_component_parameters(int index) const;
  ::catalog_studio_message::ParameterData* _internal_add_component_parameters();
  public:
  const ::catalog_studio_message::ParameterData& component_parameters(int index) const;
  ::catalog_studio_message::ParameterData* add_component_parameters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
      component_parameters() const;

  // repeated .catalog_studio_message.MultiComponentDataItem child_components = 18;
  int child_components_size() const;
  private:
  int _internal_child_components_size() const;
  public:
  void clear_child_components();
  ::catalog_studio_message::MultiComponentDataItem* mutable_child_components(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >*
      mutable_child_components();
  private:
  const ::catalog_studio_message::MultiComponentDataItem& _internal_child_components(int index) const;
  ::catalog_studio_message::MultiComponentDataItem* _internal_add_child_components();
  public:
  const ::catalog_studio_message::MultiComponentDataItem& child_components(int index) const;
  ::catalog_studio_message::MultiComponentDataItem* add_child_components();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >&
      child_components() const;

  // string component_name = 2;
  void clear_component_name();
  const std::string& component_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_component_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_component_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_component_name();
  void set_allocated_component_name(std::string* component_name);
  private:
  const std::string& _internal_component_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_component_name(const std::string& value);
  std::string* _internal_mutable_component_name();
  public:

  // string description = 5;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_MUST_USE_RESULT std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string code = 6;
  void clear_code();
  const std::string& code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_code();
  void set_allocated_code(std::string* code);
  private:
  const std::string& _internal_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code(const std::string& value);
  std::string* _internal_mutable_code();
  public:

  // string code_exp = 7;
  void clear_code_exp();
  const std::string& code_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_code_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_code_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_code_exp();
  void set_allocated_code_exp(std::string* code_exp);
  private:
  const std::string& _internal_code_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code_exp(const std::string& value);
  std::string* _internal_mutable_code_exp();
  public:

  // string single_component_path = 14;
  void clear_single_component_path();
  const std::string& single_component_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_single_component_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_single_component_path();
  PROTOBUF_MUST_USE_RESULT std::string* release_single_component_path();
  void set_allocated_single_component_path(std::string* single_component_path);
  private:
  const std::string& _internal_single_component_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_single_component_path(const std::string& value);
  std::string* _internal_mutable_single_component_path();
  public:

  // string component_uuid = 16;
  void clear_component_uuid();
  const std::string& component_uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_component_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_component_uuid();
  PROTOBUF_MUST_USE_RESULT std::string* release_component_uuid();
  void set_allocated_component_uuid(std::string* component_uuid);
  private:
  const std::string& _internal_component_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_component_uuid(const std::string& value);
  std::string* _internal_mutable_component_uuid();
  public:

  // .catalog_studio_message.ExpressionValuePair component_visibility = 3;
  bool has_component_visibility() const;
  private:
  bool _internal_has_component_visibility() const;
  public:
  void clear_component_visibility();
  const ::catalog_studio_message::ExpressionValuePair& component_visibility() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_component_visibility();
  ::catalog_studio_message::ExpressionValuePair* mutable_component_visibility();
  void set_allocated_component_visibility(::catalog_studio_message::ExpressionValuePair* component_visibility);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_component_visibility() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_component_visibility();
  public:
  void unsafe_arena_set_allocated_component_visibility(
      ::catalog_studio_message::ExpressionValuePair* component_visibility);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_component_visibility();

  // .catalog_studio_message.ExpressionValuePair component_id = 4;
  bool has_component_id() const;
  private:
  bool _internal_has_component_id() const;
  public:
  void clear_component_id();
  const ::catalog_studio_message::ExpressionValuePair& component_id() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_component_id();
  ::catalog_studio_message::ExpressionValuePair* mutable_component_id();
  void set_allocated_component_id(::catalog_studio_message::ExpressionValuePair* component_id);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_component_id() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_component_id();
  public:
  void unsafe_arena_set_allocated_component_id(
      ::catalog_studio_message::ExpressionValuePair* component_id);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_component_id();

  // .catalog_studio_message.LocationProperty component_location = 8;
  bool has_component_location() const;
  private:
  bool _internal_has_component_location() const;
  public:
  void clear_component_location();
  const ::catalog_studio_message::LocationProperty& component_location() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::LocationProperty* release_component_location();
  ::catalog_studio_message::LocationProperty* mutable_component_location();
  void set_allocated_component_location(::catalog_studio_message::LocationProperty* component_location);
  private:
  const ::catalog_studio_message::LocationProperty& _internal_component_location() const;
  ::catalog_studio_message::LocationProperty* _internal_mutable_component_location();
  public:
  void unsafe_arena_set_allocated_component_location(
      ::catalog_studio_message::LocationProperty* component_location);
  ::catalog_studio_message::LocationProperty* unsafe_arena_release_component_location();

  // .catalog_studio_message.RotationProperty component_rotation = 9;
  bool has_component_rotation() const;
  private:
  bool _internal_has_component_rotation() const;
  public:
  void clear_component_rotation();
  const ::catalog_studio_message::RotationProperty& component_rotation() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::RotationProperty* release_component_rotation();
  ::catalog_studio_message::RotationProperty* mutable_component_rotation();
  void set_allocated_component_rotation(::catalog_studio_message::RotationProperty* component_rotation);
  private:
  const ::catalog_studio_message::RotationProperty& _internal_component_rotation() const;
  ::catalog_studio_message::RotationProperty* _internal_mutable_component_rotation();
  public:
  void unsafe_arena_set_allocated_component_rotation(
      ::catalog_studio_message::RotationProperty* component_rotation);
  ::catalog_studio_message::RotationProperty* unsafe_arena_release_component_rotation();

  // .catalog_studio_message.ScaleProperty component_scale = 10;
  bool has_component_scale() const;
  private:
  bool _internal_has_component_scale() const;
  public:
  void clear_component_scale();
  const ::catalog_studio_message::ScaleProperty& component_scale() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ScaleProperty* release_component_scale();
  ::catalog_studio_message::ScaleProperty* mutable_component_scale();
  void set_allocated_component_scale(::catalog_studio_message::ScaleProperty* component_scale);
  private:
  const ::catalog_studio_message::ScaleProperty& _internal_component_scale() const;
  ::catalog_studio_message::ScaleProperty* _internal_mutable_component_scale();
  public:
  void unsafe_arena_set_allocated_component_scale(
      ::catalog_studio_message::ScaleProperty* component_scale);
  ::catalog_studio_message::ScaleProperty* unsafe_arena_release_component_scale();

  // .catalog_studio_message.SingleComponentProperty single_component_data = 15;
  bool has_single_component_data() const;
  private:
  bool _internal_has_single_component_data() const;
  public:
  void clear_single_component_data();
  const ::catalog_studio_message::SingleComponentProperty& single_component_data() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::SingleComponentProperty* release_single_component_data();
  ::catalog_studio_message::SingleComponentProperty* mutable_single_component_data();
  void set_allocated_single_component_data(::catalog_studio_message::SingleComponentProperty* single_component_data);
  private:
  const ::catalog_studio_message::SingleComponentProperty& _internal_single_component_data() const;
  ::catalog_studio_message::SingleComponentProperty* _internal_mutable_single_component_data();
  public:
  void unsafe_arena_set_allocated_single_component_data(
      ::catalog_studio_message::SingleComponentProperty* single_component_data);
  ::catalog_studio_message::SingleComponentProperty* unsafe_arena_release_single_component_data();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 key_id = 12;
  void clear_key_id();
  ::PROTOBUF_NAMESPACE_ID::int32 key_id() const;
  void set_key_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_key_id() const;
  void _internal_set_key_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 component_type = 13;
  void clear_component_type();
  ::PROTOBUF_NAMESPACE_ID::int32 component_type() const;
  void set_component_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_component_type() const;
  void _internal_set_component_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // bool user_hide = 17;
  void clear_user_hide();
  bool user_hide() const;
  void set_user_hide(bool value);
  private:
  bool _internal_user_hide() const;
  void _internal_set_user_hide(bool value);
  public:

  // int32 model_type = 19;
  void clear_model_type();
  ::PROTOBUF_NAMESPACE_ID::int32 model_type() const;
  void set_model_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_model_type() const;
  void _internal_set_model_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.MultiComponentDataItem)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData > component_parameters_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem > child_components_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr component_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr code_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr single_component_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr component_uuid_;
  ::catalog_studio_message::ExpressionValuePair* component_visibility_;
  ::catalog_studio_message::ExpressionValuePair* component_id_;
  ::catalog_studio_message::LocationProperty* component_location_;
  ::catalog_studio_message::RotationProperty* component_rotation_;
  ::catalog_studio_message::ScaleProperty* component_scale_;
  ::catalog_studio_message::SingleComponentProperty* single_component_data_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 key_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 component_type_;
  bool user_hide_;
  ::PROTOBUF_NAMESPACE_ID::int32 model_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_MultiComponentDataItem_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MultiComponentDataItem

// int32 id = 1;
inline void MultiComponentDataItem::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.id)
  return _internal_id();
}
inline void MultiComponentDataItem::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void MultiComponentDataItem::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.id)
}

// string component_name = 2;
inline void MultiComponentDataItem::clear_component_name() {
  component_name_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::component_name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_name)
  return _internal_component_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_component_name(ArgT0&& arg0, ArgT... args) {
 
 component_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.component_name)
}
inline std::string* MultiComponentDataItem::mutable_component_name() {
  std::string* _s = _internal_mutable_component_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_name)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_component_name() const {
  return component_name_.Get();
}
inline void MultiComponentDataItem::_internal_set_component_name(const std::string& value) {
  
  component_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_component_name() {
  
  return component_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_component_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_name)
  return component_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_component_name(std::string* component_name) {
  if (component_name != nullptr) {
    
  } else {
    
  }
  component_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), component_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_name)
}

// .catalog_studio_message.ExpressionValuePair component_visibility = 3;
inline bool MultiComponentDataItem::_internal_has_component_visibility() const {
  return this != internal_default_instance() && component_visibility_ != nullptr;
}
inline bool MultiComponentDataItem::has_component_visibility() const {
  return _internal_has_component_visibility();
}
inline const ::catalog_studio_message::ExpressionValuePair& MultiComponentDataItem::_internal_component_visibility() const {
  const ::catalog_studio_message::ExpressionValuePair* p = component_visibility_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& MultiComponentDataItem::component_visibility() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_visibility)
  return _internal_component_visibility();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_component_visibility(
    ::catalog_studio_message::ExpressionValuePair* component_visibility) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_visibility_);
  }
  component_visibility_ = component_visibility;
  if (component_visibility) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.component_visibility)
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::release_component_visibility() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_visibility_;
  component_visibility_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::unsafe_arena_release_component_visibility() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_visibility)
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_visibility_;
  component_visibility_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::_internal_mutable_component_visibility() {
  
  if (component_visibility_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    component_visibility_ = p;
  }
  return component_visibility_;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::mutable_component_visibility() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_component_visibility();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_visibility)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_component_visibility(::catalog_studio_message::ExpressionValuePair* component_visibility) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_visibility_);
  }
  if (component_visibility) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_visibility));
    if (message_arena != submessage_arena) {
      component_visibility = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_visibility, submessage_arena);
    }
    
  } else {
    
  }
  component_visibility_ = component_visibility;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_visibility)
}

// .catalog_studio_message.ExpressionValuePair component_id = 4;
inline bool MultiComponentDataItem::_internal_has_component_id() const {
  return this != internal_default_instance() && component_id_ != nullptr;
}
inline bool MultiComponentDataItem::has_component_id() const {
  return _internal_has_component_id();
}
inline const ::catalog_studio_message::ExpressionValuePair& MultiComponentDataItem::_internal_component_id() const {
  const ::catalog_studio_message::ExpressionValuePair* p = component_id_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& MultiComponentDataItem::component_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_id)
  return _internal_component_id();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_component_id(
    ::catalog_studio_message::ExpressionValuePair* component_id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_id_);
  }
  component_id_ = component_id;
  if (component_id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.component_id)
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::release_component_id() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_id_;
  component_id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::unsafe_arena_release_component_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_id)
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_id_;
  component_id_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::_internal_mutable_component_id() {
  
  if (component_id_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    component_id_ = p;
  }
  return component_id_;
}
inline ::catalog_studio_message::ExpressionValuePair* MultiComponentDataItem::mutable_component_id() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_component_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_id)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_component_id(::catalog_studio_message::ExpressionValuePair* component_id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_id_);
  }
  if (component_id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_id));
    if (message_arena != submessage_arena) {
      component_id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_id, submessage_arena);
    }
    
  } else {
    
  }
  component_id_ = component_id;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_id)
}

// string description = 5;
inline void MultiComponentDataItem::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::description() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.description)
}
inline std::string* MultiComponentDataItem::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.description)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_description() const {
  return description_.Get();
}
inline void MultiComponentDataItem::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_description() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.description)
}

// string code = 6;
inline void MultiComponentDataItem::clear_code() {
  code_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.code)
  return _internal_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_code(ArgT0&& arg0, ArgT... args) {
 
 code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.code)
}
inline std::string* MultiComponentDataItem::mutable_code() {
  std::string* _s = _internal_mutable_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.code)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_code() const {
  return code_.Get();
}
inline void MultiComponentDataItem::_internal_set_code(const std::string& value) {
  
  code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_code() {
  
  return code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.code)
  return code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_code(std::string* code) {
  if (code != nullptr) {
    
  } else {
    
  }
  code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.code)
}

// string code_exp = 7;
inline void MultiComponentDataItem::clear_code_exp() {
  code_exp_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::code_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.code_exp)
  return _internal_code_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_code_exp(ArgT0&& arg0, ArgT... args) {
 
 code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.code_exp)
}
inline std::string* MultiComponentDataItem::mutable_code_exp() {
  std::string* _s = _internal_mutable_code_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.code_exp)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_code_exp() const {
  return code_exp_.Get();
}
inline void MultiComponentDataItem::_internal_set_code_exp(const std::string& value) {
  
  code_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_code_exp() {
  
  return code_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_code_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.code_exp)
  return code_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_code_exp(std::string* code_exp) {
  if (code_exp != nullptr) {
    
  } else {
    
  }
  code_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), code_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.code_exp)
}

// .catalog_studio_message.LocationProperty component_location = 8;
inline bool MultiComponentDataItem::_internal_has_component_location() const {
  return this != internal_default_instance() && component_location_ != nullptr;
}
inline bool MultiComponentDataItem::has_component_location() const {
  return _internal_has_component_location();
}
inline const ::catalog_studio_message::LocationProperty& MultiComponentDataItem::_internal_component_location() const {
  const ::catalog_studio_message::LocationProperty* p = component_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::LocationProperty&>(
      ::catalog_studio_message::_LocationProperty_default_instance_);
}
inline const ::catalog_studio_message::LocationProperty& MultiComponentDataItem::component_location() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_location)
  return _internal_component_location();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_component_location(
    ::catalog_studio_message::LocationProperty* component_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_location_);
  }
  component_location_ = component_location;
  if (component_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.component_location)
}
inline ::catalog_studio_message::LocationProperty* MultiComponentDataItem::release_component_location() {
  
  ::catalog_studio_message::LocationProperty* temp = component_location_;
  component_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::LocationProperty* MultiComponentDataItem::unsafe_arena_release_component_location() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_location)
  
  ::catalog_studio_message::LocationProperty* temp = component_location_;
  component_location_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::LocationProperty* MultiComponentDataItem::_internal_mutable_component_location() {
  
  if (component_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::LocationProperty>(GetArenaForAllocation());
    component_location_ = p;
  }
  return component_location_;
}
inline ::catalog_studio_message::LocationProperty* MultiComponentDataItem::mutable_component_location() {
  ::catalog_studio_message::LocationProperty* _msg = _internal_mutable_component_location();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_location)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_component_location(::catalog_studio_message::LocationProperty* component_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_location_);
  }
  if (component_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_location));
    if (message_arena != submessage_arena) {
      component_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_location, submessage_arena);
    }
    
  } else {
    
  }
  component_location_ = component_location;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_location)
}

// .catalog_studio_message.RotationProperty component_rotation = 9;
inline bool MultiComponentDataItem::_internal_has_component_rotation() const {
  return this != internal_default_instance() && component_rotation_ != nullptr;
}
inline bool MultiComponentDataItem::has_component_rotation() const {
  return _internal_has_component_rotation();
}
inline const ::catalog_studio_message::RotationProperty& MultiComponentDataItem::_internal_component_rotation() const {
  const ::catalog_studio_message::RotationProperty* p = component_rotation_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::RotationProperty&>(
      ::catalog_studio_message::_RotationProperty_default_instance_);
}
inline const ::catalog_studio_message::RotationProperty& MultiComponentDataItem::component_rotation() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_rotation)
  return _internal_component_rotation();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_component_rotation(
    ::catalog_studio_message::RotationProperty* component_rotation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_rotation_);
  }
  component_rotation_ = component_rotation;
  if (component_rotation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.component_rotation)
}
inline ::catalog_studio_message::RotationProperty* MultiComponentDataItem::release_component_rotation() {
  
  ::catalog_studio_message::RotationProperty* temp = component_rotation_;
  component_rotation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::RotationProperty* MultiComponentDataItem::unsafe_arena_release_component_rotation() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_rotation)
  
  ::catalog_studio_message::RotationProperty* temp = component_rotation_;
  component_rotation_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::RotationProperty* MultiComponentDataItem::_internal_mutable_component_rotation() {
  
  if (component_rotation_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::RotationProperty>(GetArenaForAllocation());
    component_rotation_ = p;
  }
  return component_rotation_;
}
inline ::catalog_studio_message::RotationProperty* MultiComponentDataItem::mutable_component_rotation() {
  ::catalog_studio_message::RotationProperty* _msg = _internal_mutable_component_rotation();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_rotation)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_component_rotation(::catalog_studio_message::RotationProperty* component_rotation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_rotation_);
  }
  if (component_rotation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_rotation));
    if (message_arena != submessage_arena) {
      component_rotation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_rotation, submessage_arena);
    }
    
  } else {
    
  }
  component_rotation_ = component_rotation;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_rotation)
}

// .catalog_studio_message.ScaleProperty component_scale = 10;
inline bool MultiComponentDataItem::_internal_has_component_scale() const {
  return this != internal_default_instance() && component_scale_ != nullptr;
}
inline bool MultiComponentDataItem::has_component_scale() const {
  return _internal_has_component_scale();
}
inline const ::catalog_studio_message::ScaleProperty& MultiComponentDataItem::_internal_component_scale() const {
  const ::catalog_studio_message::ScaleProperty* p = component_scale_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ScaleProperty&>(
      ::catalog_studio_message::_ScaleProperty_default_instance_);
}
inline const ::catalog_studio_message::ScaleProperty& MultiComponentDataItem::component_scale() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_scale)
  return _internal_component_scale();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_component_scale(
    ::catalog_studio_message::ScaleProperty* component_scale) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_scale_);
  }
  component_scale_ = component_scale;
  if (component_scale) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.component_scale)
}
inline ::catalog_studio_message::ScaleProperty* MultiComponentDataItem::release_component_scale() {
  
  ::catalog_studio_message::ScaleProperty* temp = component_scale_;
  component_scale_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ScaleProperty* MultiComponentDataItem::unsafe_arena_release_component_scale() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_scale)
  
  ::catalog_studio_message::ScaleProperty* temp = component_scale_;
  component_scale_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ScaleProperty* MultiComponentDataItem::_internal_mutable_component_scale() {
  
  if (component_scale_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ScaleProperty>(GetArenaForAllocation());
    component_scale_ = p;
  }
  return component_scale_;
}
inline ::catalog_studio_message::ScaleProperty* MultiComponentDataItem::mutable_component_scale() {
  ::catalog_studio_message::ScaleProperty* _msg = _internal_mutable_component_scale();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_scale)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_component_scale(::catalog_studio_message::ScaleProperty* component_scale) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_scale_);
  }
  if (component_scale) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_scale));
    if (message_arena != submessage_arena) {
      component_scale = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_scale, submessage_arena);
    }
    
  } else {
    
  }
  component_scale_ = component_scale;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_scale)
}

// repeated .catalog_studio_message.ParameterData component_parameters = 11;
inline int MultiComponentDataItem::_internal_component_parameters_size() const {
  return component_parameters_.size();
}
inline int MultiComponentDataItem::component_parameters_size() const {
  return _internal_component_parameters_size();
}
inline ::catalog_studio_message::ParameterData* MultiComponentDataItem::mutable_component_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_parameters)
  return component_parameters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
MultiComponentDataItem::mutable_component_parameters() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.MultiComponentDataItem.component_parameters)
  return &component_parameters_;
}
inline const ::catalog_studio_message::ParameterData& MultiComponentDataItem::_internal_component_parameters(int index) const {
  return component_parameters_.Get(index);
}
inline const ::catalog_studio_message::ParameterData& MultiComponentDataItem::component_parameters(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_parameters)
  return _internal_component_parameters(index);
}
inline ::catalog_studio_message::ParameterData* MultiComponentDataItem::_internal_add_component_parameters() {
  return component_parameters_.Add();
}
inline ::catalog_studio_message::ParameterData* MultiComponentDataItem::add_component_parameters() {
  ::catalog_studio_message::ParameterData* _add = _internal_add_component_parameters();
  // @@protoc_insertion_point(field_add:catalog_studio_message.MultiComponentDataItem.component_parameters)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
MultiComponentDataItem::component_parameters() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.MultiComponentDataItem.component_parameters)
  return component_parameters_;
}

// int32 key_id = 12;
inline void MultiComponentDataItem::clear_key_id() {
  key_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::_internal_key_id() const {
  return key_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::key_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.key_id)
  return _internal_key_id();
}
inline void MultiComponentDataItem::_internal_set_key_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  key_id_ = value;
}
inline void MultiComponentDataItem::set_key_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_key_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.key_id)
}

// int32 component_type = 13;
inline void MultiComponentDataItem::clear_component_type() {
  component_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::_internal_component_type() const {
  return component_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::component_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_type)
  return _internal_component_type();
}
inline void MultiComponentDataItem::_internal_set_component_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  component_type_ = value;
}
inline void MultiComponentDataItem::set_component_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_component_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.component_type)
}

// string single_component_path = 14;
inline void MultiComponentDataItem::clear_single_component_path() {
  single_component_path_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::single_component_path() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.single_component_path)
  return _internal_single_component_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_single_component_path(ArgT0&& arg0, ArgT... args) {
 
 single_component_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.single_component_path)
}
inline std::string* MultiComponentDataItem::mutable_single_component_path() {
  std::string* _s = _internal_mutable_single_component_path();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.single_component_path)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_single_component_path() const {
  return single_component_path_.Get();
}
inline void MultiComponentDataItem::_internal_set_single_component_path(const std::string& value) {
  
  single_component_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_single_component_path() {
  
  return single_component_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_single_component_path() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.single_component_path)
  return single_component_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_single_component_path(std::string* single_component_path) {
  if (single_component_path != nullptr) {
    
  } else {
    
  }
  single_component_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), single_component_path,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.single_component_path)
}

// .catalog_studio_message.SingleComponentProperty single_component_data = 15;
inline bool MultiComponentDataItem::_internal_has_single_component_data() const {
  return this != internal_default_instance() && single_component_data_ != nullptr;
}
inline bool MultiComponentDataItem::has_single_component_data() const {
  return _internal_has_single_component_data();
}
inline const ::catalog_studio_message::SingleComponentProperty& MultiComponentDataItem::_internal_single_component_data() const {
  const ::catalog_studio_message::SingleComponentProperty* p = single_component_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::SingleComponentProperty&>(
      ::catalog_studio_message::_SingleComponentProperty_default_instance_);
}
inline const ::catalog_studio_message::SingleComponentProperty& MultiComponentDataItem::single_component_data() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.single_component_data)
  return _internal_single_component_data();
}
inline void MultiComponentDataItem::unsafe_arena_set_allocated_single_component_data(
    ::catalog_studio_message::SingleComponentProperty* single_component_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_data_);
  }
  single_component_data_ = single_component_data;
  if (single_component_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.MultiComponentDataItem.single_component_data)
}
inline ::catalog_studio_message::SingleComponentProperty* MultiComponentDataItem::release_single_component_data() {
  
  ::catalog_studio_message::SingleComponentProperty* temp = single_component_data_;
  single_component_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::SingleComponentProperty* MultiComponentDataItem::unsafe_arena_release_single_component_data() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.single_component_data)
  
  ::catalog_studio_message::SingleComponentProperty* temp = single_component_data_;
  single_component_data_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::SingleComponentProperty* MultiComponentDataItem::_internal_mutable_single_component_data() {
  
  if (single_component_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::SingleComponentProperty>(GetArenaForAllocation());
    single_component_data_ = p;
  }
  return single_component_data_;
}
inline ::catalog_studio_message::SingleComponentProperty* MultiComponentDataItem::mutable_single_component_data() {
  ::catalog_studio_message::SingleComponentProperty* _msg = _internal_mutable_single_component_data();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.single_component_data)
  return _msg;
}
inline void MultiComponentDataItem::set_allocated_single_component_data(::catalog_studio_message::SingleComponentProperty* single_component_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_data_);
  }
  if (single_component_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_data));
    if (message_arena != submessage_arena) {
      single_component_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, single_component_data, submessage_arena);
    }
    
  } else {
    
  }
  single_component_data_ = single_component_data;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.single_component_data)
}

// string component_uuid = 16;
inline void MultiComponentDataItem::clear_component_uuid() {
  component_uuid_.ClearToEmpty();
}
inline const std::string& MultiComponentDataItem::component_uuid() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.component_uuid)
  return _internal_component_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiComponentDataItem::set_component_uuid(ArgT0&& arg0, ArgT... args) {
 
 component_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.component_uuid)
}
inline std::string* MultiComponentDataItem::mutable_component_uuid() {
  std::string* _s = _internal_mutable_component_uuid();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.component_uuid)
  return _s;
}
inline const std::string& MultiComponentDataItem::_internal_component_uuid() const {
  return component_uuid_.Get();
}
inline void MultiComponentDataItem::_internal_set_component_uuid(const std::string& value) {
  
  component_uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::_internal_mutable_component_uuid() {
  
  return component_uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MultiComponentDataItem::release_component_uuid() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.MultiComponentDataItem.component_uuid)
  return component_uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MultiComponentDataItem::set_allocated_component_uuid(std::string* component_uuid) {
  if (component_uuid != nullptr) {
    
  } else {
    
  }
  component_uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), component_uuid,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.MultiComponentDataItem.component_uuid)
}

// bool user_hide = 17;
inline void MultiComponentDataItem::clear_user_hide() {
  user_hide_ = false;
}
inline bool MultiComponentDataItem::_internal_user_hide() const {
  return user_hide_;
}
inline bool MultiComponentDataItem::user_hide() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.user_hide)
  return _internal_user_hide();
}
inline void MultiComponentDataItem::_internal_set_user_hide(bool value) {
  
  user_hide_ = value;
}
inline void MultiComponentDataItem::set_user_hide(bool value) {
  _internal_set_user_hide(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.user_hide)
}

// repeated .catalog_studio_message.MultiComponentDataItem child_components = 18;
inline int MultiComponentDataItem::_internal_child_components_size() const {
  return child_components_.size();
}
inline int MultiComponentDataItem::child_components_size() const {
  return _internal_child_components_size();
}
inline void MultiComponentDataItem::clear_child_components() {
  child_components_.Clear();
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentDataItem::mutable_child_components(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.MultiComponentDataItem.child_components)
  return child_components_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >*
MultiComponentDataItem::mutable_child_components() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.MultiComponentDataItem.child_components)
  return &child_components_;
}
inline const ::catalog_studio_message::MultiComponentDataItem& MultiComponentDataItem::_internal_child_components(int index) const {
  return child_components_.Get(index);
}
inline const ::catalog_studio_message::MultiComponentDataItem& MultiComponentDataItem::child_components(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.child_components)
  return _internal_child_components(index);
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentDataItem::_internal_add_child_components() {
  return child_components_.Add();
}
inline ::catalog_studio_message::MultiComponentDataItem* MultiComponentDataItem::add_child_components() {
  ::catalog_studio_message::MultiComponentDataItem* _add = _internal_add_child_components();
  // @@protoc_insertion_point(field_add:catalog_studio_message.MultiComponentDataItem.child_components)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::MultiComponentDataItem >&
MultiComponentDataItem::child_components() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.MultiComponentDataItem.child_components)
  return child_components_;
}

// int32 model_type = 19;
inline void MultiComponentDataItem::clear_model_type() {
  model_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::_internal_model_type() const {
  return model_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MultiComponentDataItem::model_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.MultiComponentDataItem.model_type)
  return _internal_model_type();
}
inline void MultiComponentDataItem::_internal_set_model_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  model_type_ = value;
}
inline void MultiComponentDataItem::set_model_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_model_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.MultiComponentDataItem.model_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_MultiComponentDataItem_2eproto
