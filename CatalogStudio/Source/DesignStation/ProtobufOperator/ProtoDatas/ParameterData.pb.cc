// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ParameterData.proto

#include "ParameterData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ParameterData::ParameterData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enum_data_()
  , data_(nullptr){}
struct ParameterDataDefaultTypeInternal {
  constexpr ParameterDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ParameterDataDefaultTypeInternal() {}
  union {
    ParameterData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ParameterDataDefaultTypeInternal _ParameterData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ParameterData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_ParameterData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ParameterData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ParameterData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterData, data_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterData, enum_data_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ParameterData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ParameterData_default_instance_),
};

const char descriptor_table_protodef_ParameterData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023ParameterData.proto\022\026catalog_studio_me"
  "ssage\032\030ParameterTableData.proto\032\034EnumPar"
  "ameterTableData.proto\"\214\001\n\rParameterData\022"
  "8\n\004data\030\001 \001(\0132*.catalog_studio_message.P"
  "arameterTableData\022A\n\tenum_data\030\002 \003(\0132..c"
  "atalog_studio_message.EnumParameterTable"
  "Datab\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_ParameterData_2eproto_deps[2] = {
  &::descriptor_table_EnumParameterTableData_2eproto,
  &::descriptor_table_ParameterTableData_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ParameterData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ParameterData_2eproto = {
  false, false, 252, descriptor_table_protodef_ParameterData_2eproto, "ParameterData.proto", 
  &descriptor_table_ParameterData_2eproto_once, descriptor_table_ParameterData_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_ParameterData_2eproto::offsets,
  file_level_metadata_ParameterData_2eproto, file_level_enum_descriptors_ParameterData_2eproto, file_level_service_descriptors_ParameterData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ParameterData_2eproto_getter() {
  return &descriptor_table_ParameterData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ParameterData_2eproto(&descriptor_table_ParameterData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class ParameterData::_Internal {
 public:
  static const ::catalog_studio_message::ParameterTableData& data(const ParameterData* msg);
};

const ::catalog_studio_message::ParameterTableData&
ParameterData::_Internal::data(const ParameterData* msg) {
  return *msg->data_;
}
void ParameterData::clear_data() {
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
}
void ParameterData::clear_enum_data() {
  enum_data_.Clear();
}
ParameterData::ParameterData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  enum_data_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ParameterData)
}
ParameterData::ParameterData(const ParameterData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      enum_data_(from.enum_data_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_data()) {
    data_ = new ::catalog_studio_message::ParameterTableData(*from.data_);
  } else {
    data_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ParameterData)
}

void ParameterData::SharedCtor() {
data_ = nullptr;
}

ParameterData::~ParameterData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.ParameterData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ParameterData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete data_;
}

void ParameterData::ArenaDtor(void* object) {
  ParameterData* _this = reinterpret_cast< ParameterData* >(object);
  (void)_this;
}
void ParameterData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ParameterData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ParameterData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.ParameterData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enum_data_.Clear();
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ParameterData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.ParameterTableData data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.EnumParameterTableData enum_data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_enum_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ParameterData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.ParameterData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.ParameterTableData data = 1;
  if (this->_internal_has_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::data(this), target, stream);
  }

  // repeated .catalog_studio_message.EnumParameterTableData enum_data = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_enum_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_enum_data(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.ParameterData)
  return target;
}

size_t ParameterData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.ParameterData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.EnumParameterTableData enum_data = 2;
  total_size += 1UL * this->_internal_enum_data_size();
  for (const auto& msg : this->enum_data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .catalog_studio_message.ParameterTableData data = 1;
  if (this->_internal_has_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *data_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ParameterData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ParameterData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ParameterData::GetClassData() const { return &_class_data_; }

void ParameterData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ParameterData *>(to)->MergeFrom(
      static_cast<const ParameterData &>(from));
}


void ParameterData::MergeFrom(const ParameterData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.ParameterData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  enum_data_.MergeFrom(from.enum_data_);
  if (from._internal_has_data()) {
    _internal_mutable_data()->::catalog_studio_message::ParameterTableData::MergeFrom(from._internal_data());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ParameterData::CopyFrom(const ParameterData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.ParameterData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ParameterData::IsInitialized() const {
  return true;
}

void ParameterData::InternalSwap(ParameterData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  enum_data_.InternalSwap(&other->enum_data_);
  swap(data_, other->data_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ParameterData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ParameterData_2eproto_getter, &descriptor_table_ParameterData_2eproto_once,
      file_level_metadata_ParameterData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ParameterData* Arena::CreateMaybeMessage< ::catalog_studio_message::ParameterData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ParameterData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
