// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ParameterTableData.proto

#include "ParameterTableData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ParameterTableData::ParameterTableData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , max_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , min_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , expression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , visibility_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , editable_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , main_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , param_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , max_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , min_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , visibility_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , editable_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , special_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , special_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , must_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , must_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , default_express_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , grid_expression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , grid_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , is_enum_(0)
  , classific_id_(0)
  , no_match_enum_(false)
  , grid_value_mark_(0){}
struct ParameterTableDataDefaultTypeInternal {
  constexpr ParameterTableDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ParameterTableDataDefaultTypeInternal() {}
  union {
    ParameterTableData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ParameterTableDataDefaultTypeInternal _ParameterTableData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ParameterTableData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_ParameterTableData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ParameterTableData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ParameterTableData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, description_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, classific_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, expression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, max_value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, max_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, min_value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, min_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, visibility_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, visibility_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, editable_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, editable_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, is_enum_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, param_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, main_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, special_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, special_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, must_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, must_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, default_express_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, no_match_enum_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, grid_expression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, grid_value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ParameterTableData, grid_value_mark_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ParameterTableData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ParameterTableData_default_instance_),
};

const char descriptor_table_protodef_ParameterTableData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\030ParameterTableData.proto\022\026catalog_stud"
  "io_message\"\210\004\n\022ParameterTableData\022\n\n\002id\030"
  "\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\023\n\013description\030\010 \001(\t"
  "\022\024\n\014classific_id\030\016 \001(\005\022\r\n\005value\030\003 \001(\t\022\022\n"
  "\nexpression\030\006 \001(\t\022\021\n\tmax_value\030\004 \001(\t\022\017\n\007"
  "max_exp\030\020 \001(\t\022\021\n\tmin_value\030\005 \001(\t\022\017\n\007min_"
  "exp\030\021 \001(\t\022\022\n\nvisibility\030\007 \001(\t\022\026\n\016visibil"
  "ity_exp\030\022 \001(\t\022\020\n\010editable\030\t \001(\t\022\024\n\014edita"
  "ble_exp\030\023 \001(\t\022\017\n\007is_enum\030\n \001(\005\022\020\n\010param_"
  "id\030\017 \001(\t\022\017\n\007main_id\030\014 \001(\t\022\017\n\007special\030\024 \001"
  "(\t\022\023\n\013special_exp\030\025 \001(\t\022\014\n\004must\030\026 \001(\t\022\020\n"
  "\010must_exp\030\027 \001(\t\022\027\n\017default_express\030\030 \001(\t"
  "\022\025\n\rno_match_enum\030\031 \001(\010\022\027\n\017grid_expressi"
  "on\030\032 \001(\t\022\022\n\ngrid_value\030\033 \001(\t\022\027\n\017grid_val"
  "ue_mark\030\034 \001(\005b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ParameterTableData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ParameterTableData_2eproto = {
  false, false, 581, descriptor_table_protodef_ParameterTableData_2eproto, "ParameterTableData.proto", 
  &descriptor_table_ParameterTableData_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_ParameterTableData_2eproto::offsets,
  file_level_metadata_ParameterTableData_2eproto, file_level_enum_descriptors_ParameterTableData_2eproto, file_level_service_descriptors_ParameterTableData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ParameterTableData_2eproto_getter() {
  return &descriptor_table_ParameterTableData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ParameterTableData_2eproto(&descriptor_table_ParameterTableData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class ParameterTableData::_Internal {
 public:
};

ParameterTableData::ParameterTableData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ParameterTableData)
}
ParameterTableData::ParameterTableData(const ParameterTableData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_value().empty()) {
    value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_value(), 
      GetArenaForAllocation());
  }
  max_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_max_value().empty()) {
    max_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_max_value(), 
      GetArenaForAllocation());
  }
  min_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_min_value().empty()) {
    min_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_min_value(), 
      GetArenaForAllocation());
  }
  expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_expression().empty()) {
    expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_expression(), 
      GetArenaForAllocation());
  }
  visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_visibility().empty()) {
    visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_visibility(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  editable_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_editable().empty()) {
    editable_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_editable(), 
      GetArenaForAllocation());
  }
  main_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_main_id().empty()) {
    main_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_main_id(), 
      GetArenaForAllocation());
  }
  param_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_param_id().empty()) {
    param_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_param_id(), 
      GetArenaForAllocation());
  }
  max_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_max_exp().empty()) {
    max_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_max_exp(), 
      GetArenaForAllocation());
  }
  min_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_min_exp().empty()) {
    min_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_min_exp(), 
      GetArenaForAllocation());
  }
  visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_visibility_exp().empty()) {
    visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_visibility_exp(), 
      GetArenaForAllocation());
  }
  editable_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_editable_exp().empty()) {
    editable_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_editable_exp(), 
      GetArenaForAllocation());
  }
  special_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_special().empty()) {
    special_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_special(), 
      GetArenaForAllocation());
  }
  special_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_special_exp().empty()) {
    special_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_special_exp(), 
      GetArenaForAllocation());
  }
  must_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_must().empty()) {
    must_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_must(), 
      GetArenaForAllocation());
  }
  must_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_must_exp().empty()) {
    must_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_must_exp(), 
      GetArenaForAllocation());
  }
  default_express_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_default_express().empty()) {
    default_express_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_default_express(), 
      GetArenaForAllocation());
  }
  grid_expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_grid_expression().empty()) {
    grid_expression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_grid_expression(), 
      GetArenaForAllocation());
  }
  grid_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_grid_value().empty()) {
    grid_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_grid_value(), 
      GetArenaForAllocation());
  }
  ::memcpy(&is_enum_, &from.is_enum_,
    static_cast<size_t>(reinterpret_cast<char*>(&grid_value_mark_) -
    reinterpret_cast<char*>(&is_enum_)) + sizeof(grid_value_mark_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ParameterTableData)
}

void ParameterTableData::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
max_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
min_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
editable_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
main_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
param_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
max_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
min_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
editable_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
special_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
special_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
must_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
must_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
default_express_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
grid_expression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
grid_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&is_enum_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&grid_value_mark_) -
    reinterpret_cast<char*>(&is_enum_)) + sizeof(grid_value_mark_));
}

ParameterTableData::~ParameterTableData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.ParameterTableData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ParameterTableData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  max_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  min_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  expression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  visibility_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  editable_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  main_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  param_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  max_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  min_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  visibility_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  editable_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  special_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  special_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  must_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  must_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  default_express_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  grid_expression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  grid_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ParameterTableData::ArenaDtor(void* object) {
  ParameterTableData* _this = reinterpret_cast< ParameterTableData* >(object);
  (void)_this;
}
void ParameterTableData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ParameterTableData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ParameterTableData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.ParameterTableData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  name_.ClearToEmpty();
  value_.ClearToEmpty();
  max_value_.ClearToEmpty();
  min_value_.ClearToEmpty();
  expression_.ClearToEmpty();
  visibility_.ClearToEmpty();
  description_.ClearToEmpty();
  editable_.ClearToEmpty();
  main_id_.ClearToEmpty();
  param_id_.ClearToEmpty();
  max_exp_.ClearToEmpty();
  min_exp_.ClearToEmpty();
  visibility_exp_.ClearToEmpty();
  editable_exp_.ClearToEmpty();
  special_.ClearToEmpty();
  special_exp_.ClearToEmpty();
  must_.ClearToEmpty();
  must_exp_.ClearToEmpty();
  default_express_.ClearToEmpty();
  grid_expression_.ClearToEmpty();
  grid_value_.ClearToEmpty();
  ::memset(&is_enum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&grid_value_mark_) -
      reinterpret_cast<char*>(&is_enum_)) + sizeof(grid_value_mark_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ParameterTableData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string max_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_max_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.max_value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string min_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_min_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.min_value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string expression = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_expression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.expression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string visibility = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_visibility();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.visibility"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string editable = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_editable();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.editable"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 is_enum = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          is_enum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string main_id = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_main_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.main_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 classific_id = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          classific_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string param_id = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          auto str = _internal_mutable_param_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.param_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string max_exp = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          auto str = _internal_mutable_max_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.max_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string min_exp = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          auto str = _internal_mutable_min_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.min_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string visibility_exp = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          auto str = _internal_mutable_visibility_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.visibility_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string editable_exp = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          auto str = _internal_mutable_editable_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.editable_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string special = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          auto str = _internal_mutable_special();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.special"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string special_exp = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          auto str = _internal_mutable_special_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.special_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string must = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          auto str = _internal_mutable_must();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.must"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string must_exp = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          auto str = _internal_mutable_must_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.must_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string default_express = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 194)) {
          auto str = _internal_mutable_default_express();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.default_express"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool no_match_enum = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 200)) {
          no_match_enum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string grid_expression = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 210)) {
          auto str = _internal_mutable_grid_expression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.grid_expression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string grid_value = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 218)) {
          auto str = _internal_mutable_grid_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.ParameterTableData.grid_value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 grid_value_mark = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 224)) {
          grid_value_mark_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ParameterTableData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.ParameterTableData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string value = 3;
  if (!this->_internal_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.value");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_value(), target);
  }

  // string max_value = 4;
  if (!this->_internal_max_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_max_value().data(), static_cast<int>(this->_internal_max_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.max_value");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_max_value(), target);
  }

  // string min_value = 5;
  if (!this->_internal_min_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_min_value().data(), static_cast<int>(this->_internal_min_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.min_value");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_min_value(), target);
  }

  // string expression = 6;
  if (!this->_internal_expression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_expression().data(), static_cast<int>(this->_internal_expression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.expression");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_expression(), target);
  }

  // string visibility = 7;
  if (!this->_internal_visibility().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_visibility().data(), static_cast<int>(this->_internal_visibility().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.visibility");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_visibility(), target);
  }

  // string description = 8;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.description");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_description(), target);
  }

  // string editable = 9;
  if (!this->_internal_editable().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_editable().data(), static_cast<int>(this->_internal_editable().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.editable");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_editable(), target);
  }

  // int32 is_enum = 10;
  if (this->_internal_is_enum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(10, this->_internal_is_enum(), target);
  }

  // string main_id = 12;
  if (!this->_internal_main_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_main_id().data(), static_cast<int>(this->_internal_main_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.main_id");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_main_id(), target);
  }

  // int32 classific_id = 14;
  if (this->_internal_classific_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(14, this->_internal_classific_id(), target);
  }

  // string param_id = 15;
  if (!this->_internal_param_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_param_id().data(), static_cast<int>(this->_internal_param_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.param_id");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_param_id(), target);
  }

  // string max_exp = 16;
  if (!this->_internal_max_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_max_exp().data(), static_cast<int>(this->_internal_max_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.max_exp");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_max_exp(), target);
  }

  // string min_exp = 17;
  if (!this->_internal_min_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_min_exp().data(), static_cast<int>(this->_internal_min_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.min_exp");
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_min_exp(), target);
  }

  // string visibility_exp = 18;
  if (!this->_internal_visibility_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_visibility_exp().data(), static_cast<int>(this->_internal_visibility_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.visibility_exp");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_visibility_exp(), target);
  }

  // string editable_exp = 19;
  if (!this->_internal_editable_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_editable_exp().data(), static_cast<int>(this->_internal_editable_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.editable_exp");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_editable_exp(), target);
  }

  // string special = 20;
  if (!this->_internal_special().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_special().data(), static_cast<int>(this->_internal_special().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.special");
    target = stream->WriteStringMaybeAliased(
        20, this->_internal_special(), target);
  }

  // string special_exp = 21;
  if (!this->_internal_special_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_special_exp().data(), static_cast<int>(this->_internal_special_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.special_exp");
    target = stream->WriteStringMaybeAliased(
        21, this->_internal_special_exp(), target);
  }

  // string must = 22;
  if (!this->_internal_must().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_must().data(), static_cast<int>(this->_internal_must().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.must");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_must(), target);
  }

  // string must_exp = 23;
  if (!this->_internal_must_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_must_exp().data(), static_cast<int>(this->_internal_must_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.must_exp");
    target = stream->WriteStringMaybeAliased(
        23, this->_internal_must_exp(), target);
  }

  // string default_express = 24;
  if (!this->_internal_default_express().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_default_express().data(), static_cast<int>(this->_internal_default_express().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.default_express");
    target = stream->WriteStringMaybeAliased(
        24, this->_internal_default_express(), target);
  }

  // bool no_match_enum = 25;
  if (this->_internal_no_match_enum() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(25, this->_internal_no_match_enum(), target);
  }

  // string grid_expression = 26;
  if (!this->_internal_grid_expression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_grid_expression().data(), static_cast<int>(this->_internal_grid_expression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.grid_expression");
    target = stream->WriteStringMaybeAliased(
        26, this->_internal_grid_expression(), target);
  }

  // string grid_value = 27;
  if (!this->_internal_grid_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_grid_value().data(), static_cast<int>(this->_internal_grid_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.ParameterTableData.grid_value");
    target = stream->WriteStringMaybeAliased(
        27, this->_internal_grid_value(), target);
  }

  // int32 grid_value_mark = 28;
  if (this->_internal_grid_value_mark() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(28, this->_internal_grid_value_mark(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.ParameterTableData)
  return target;
}

size_t ParameterTableData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.ParameterTableData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string value = 3;
  if (!this->_internal_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  // string max_value = 4;
  if (!this->_internal_max_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_max_value());
  }

  // string min_value = 5;
  if (!this->_internal_min_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_min_value());
  }

  // string expression = 6;
  if (!this->_internal_expression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_expression());
  }

  // string visibility = 7;
  if (!this->_internal_visibility().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_visibility());
  }

  // string description = 8;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string editable = 9;
  if (!this->_internal_editable().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_editable());
  }

  // string main_id = 12;
  if (!this->_internal_main_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_main_id());
  }

  // string param_id = 15;
  if (!this->_internal_param_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_param_id());
  }

  // string max_exp = 16;
  if (!this->_internal_max_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_max_exp());
  }

  // string min_exp = 17;
  if (!this->_internal_min_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_min_exp());
  }

  // string visibility_exp = 18;
  if (!this->_internal_visibility_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_visibility_exp());
  }

  // string editable_exp = 19;
  if (!this->_internal_editable_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_editable_exp());
  }

  // string special = 20;
  if (!this->_internal_special().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_special());
  }

  // string special_exp = 21;
  if (!this->_internal_special_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_special_exp());
  }

  // string must = 22;
  if (!this->_internal_must().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_must());
  }

  // string must_exp = 23;
  if (!this->_internal_must_exp().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_must_exp());
  }

  // string default_express = 24;
  if (!this->_internal_default_express().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_default_express());
  }

  // string grid_expression = 26;
  if (!this->_internal_grid_expression().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_grid_expression());
  }

  // string grid_value = 27;
  if (!this->_internal_grid_value().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_grid_value());
  }

  // int32 is_enum = 10;
  if (this->_internal_is_enum() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_is_enum());
  }

  // int32 classific_id = 14;
  if (this->_internal_classific_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_classific_id());
  }

  // bool no_match_enum = 25;
  if (this->_internal_no_match_enum() != 0) {
    total_size += 2 + 1;
  }

  // int32 grid_value_mark = 28;
  if (this->_internal_grid_value_mark() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_grid_value_mark());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ParameterTableData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ParameterTableData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ParameterTableData::GetClassData() const { return &_class_data_; }

void ParameterTableData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ParameterTableData *>(to)->MergeFrom(
      static_cast<const ParameterTableData &>(from));
}


void ParameterTableData::MergeFrom(const ParameterTableData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.ParameterTableData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_value().empty()) {
    _internal_set_value(from._internal_value());
  }
  if (!from._internal_max_value().empty()) {
    _internal_set_max_value(from._internal_max_value());
  }
  if (!from._internal_min_value().empty()) {
    _internal_set_min_value(from._internal_min_value());
  }
  if (!from._internal_expression().empty()) {
    _internal_set_expression(from._internal_expression());
  }
  if (!from._internal_visibility().empty()) {
    _internal_set_visibility(from._internal_visibility());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_editable().empty()) {
    _internal_set_editable(from._internal_editable());
  }
  if (!from._internal_main_id().empty()) {
    _internal_set_main_id(from._internal_main_id());
  }
  if (!from._internal_param_id().empty()) {
    _internal_set_param_id(from._internal_param_id());
  }
  if (!from._internal_max_exp().empty()) {
    _internal_set_max_exp(from._internal_max_exp());
  }
  if (!from._internal_min_exp().empty()) {
    _internal_set_min_exp(from._internal_min_exp());
  }
  if (!from._internal_visibility_exp().empty()) {
    _internal_set_visibility_exp(from._internal_visibility_exp());
  }
  if (!from._internal_editable_exp().empty()) {
    _internal_set_editable_exp(from._internal_editable_exp());
  }
  if (!from._internal_special().empty()) {
    _internal_set_special(from._internal_special());
  }
  if (!from._internal_special_exp().empty()) {
    _internal_set_special_exp(from._internal_special_exp());
  }
  if (!from._internal_must().empty()) {
    _internal_set_must(from._internal_must());
  }
  if (!from._internal_must_exp().empty()) {
    _internal_set_must_exp(from._internal_must_exp());
  }
  if (!from._internal_default_express().empty()) {
    _internal_set_default_express(from._internal_default_express());
  }
  if (!from._internal_grid_expression().empty()) {
    _internal_set_grid_expression(from._internal_grid_expression());
  }
  if (!from._internal_grid_value().empty()) {
    _internal_set_grid_value(from._internal_grid_value());
  }
  if (from._internal_is_enum() != 0) {
    _internal_set_is_enum(from._internal_is_enum());
  }
  if (from._internal_classific_id() != 0) {
    _internal_set_classific_id(from._internal_classific_id());
  }
  if (from._internal_no_match_enum() != 0) {
    _internal_set_no_match_enum(from._internal_no_match_enum());
  }
  if (from._internal_grid_value_mark() != 0) {
    _internal_set_grid_value_mark(from._internal_grid_value_mark());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ParameterTableData::CopyFrom(const ParameterTableData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.ParameterTableData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ParameterTableData::IsInitialized() const {
  return true;
}

void ParameterTableData::InternalSwap(ParameterTableData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &value_, lhs_arena,
      &other->value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &max_value_, lhs_arena,
      &other->max_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &min_value_, lhs_arena,
      &other->min_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &expression_, lhs_arena,
      &other->expression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &visibility_, lhs_arena,
      &other->visibility_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &editable_, lhs_arena,
      &other->editable_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &main_id_, lhs_arena,
      &other->main_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &param_id_, lhs_arena,
      &other->param_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &max_exp_, lhs_arena,
      &other->max_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &min_exp_, lhs_arena,
      &other->min_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &visibility_exp_, lhs_arena,
      &other->visibility_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &editable_exp_, lhs_arena,
      &other->editable_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &special_, lhs_arena,
      &other->special_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &special_exp_, lhs_arena,
      &other->special_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &must_, lhs_arena,
      &other->must_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &must_exp_, lhs_arena,
      &other->must_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &default_express_, lhs_arena,
      &other->default_express_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &grid_expression_, lhs_arena,
      &other->grid_expression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &grid_value_, lhs_arena,
      &other->grid_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ParameterTableData, grid_value_mark_)
      + sizeof(ParameterTableData::grid_value_mark_)
      - PROTOBUF_FIELD_OFFSET(ParameterTableData, is_enum_)>(
          reinterpret_cast<char*>(&is_enum_),
          reinterpret_cast<char*>(&other->is_enum_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ParameterTableData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ParameterTableData_2eproto_getter, &descriptor_table_ParameterTableData_2eproto_once,
      file_level_metadata_ParameterTableData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ParameterTableData* Arena::CreateMaybeMessage< ::catalog_studio_message::ParameterTableData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ParameterTableData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
