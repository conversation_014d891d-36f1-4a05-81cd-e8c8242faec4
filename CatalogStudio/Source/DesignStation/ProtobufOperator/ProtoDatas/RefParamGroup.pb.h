// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RefParamGroup.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_RefParamGroup_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_RefParamGroup_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_RefParamGroup_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_RefParamGroup_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RefParamGroup_2eproto;
namespace catalog_studio_message {
class RefParamGroupData;
struct RefParamGroupDataDefaultTypeInternal;
extern RefParamGroupDataDefaultTypeInternal _RefParamGroupData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::RefParamGroupData* Arena::CreateMaybeMessage<::catalog_studio_message::RefParamGroupData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class RefParamGroupData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.RefParamGroupData) */ {
 public:
  inline RefParamGroupData() : RefParamGroupData(nullptr) {}
  ~RefParamGroupData() override;
  explicit constexpr RefParamGroupData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RefParamGroupData(const RefParamGroupData& from);
  RefParamGroupData(RefParamGroupData&& from) noexcept
    : RefParamGroupData() {
    *this = ::std::move(from);
  }

  inline RefParamGroupData& operator=(const RefParamGroupData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RefParamGroupData& operator=(RefParamGroupData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RefParamGroupData& default_instance() {
    return *internal_default_instance();
  }
  static inline const RefParamGroupData* internal_default_instance() {
    return reinterpret_cast<const RefParamGroupData*>(
               &_RefParamGroupData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RefParamGroupData& a, RefParamGroupData& b) {
    a.Swap(&b);
  }
  inline void Swap(RefParamGroupData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RefParamGroupData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RefParamGroupData* New() const final {
    return new RefParamGroupData();
  }

  RefParamGroupData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RefParamGroupData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RefParamGroupData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RefParamGroupData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RefParamGroupData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.RefParamGroupData";
  }
  protected:
  explicit RefParamGroupData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGroupNameFieldNumber = 2,
    kDescriptionFieldNumber = 3,
    kIdFieldNumber = 1,
  };
  // string group_name = 2;
  void clear_group_name();
  const std::string& group_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_group_name();
  void set_allocated_group_name(std::string* group_name);
  private:
  const std::string& _internal_group_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_name(const std::string& value);
  std::string* _internal_mutable_group_name();
  public:

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_MUST_USE_RESULT std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.RefParamGroupData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_RefParamGroup_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RefParamGroupData

// int32 id = 1;
inline void RefParamGroupData::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefParamGroupData::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefParamGroupData::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefParamGroupData.id)
  return _internal_id();
}
inline void RefParamGroupData::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void RefParamGroupData::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefParamGroupData.id)
}

// string group_name = 2;
inline void RefParamGroupData::clear_group_name() {
  group_name_.ClearToEmpty();
}
inline const std::string& RefParamGroupData::group_name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefParamGroupData.group_name)
  return _internal_group_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefParamGroupData::set_group_name(ArgT0&& arg0, ArgT... args) {
 
 group_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefParamGroupData.group_name)
}
inline std::string* RefParamGroupData::mutable_group_name() {
  std::string* _s = _internal_mutable_group_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefParamGroupData.group_name)
  return _s;
}
inline const std::string& RefParamGroupData::_internal_group_name() const {
  return group_name_.Get();
}
inline void RefParamGroupData::_internal_set_group_name(const std::string& value) {
  
  group_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefParamGroupData::_internal_mutable_group_name() {
  
  return group_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefParamGroupData::release_group_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefParamGroupData.group_name)
  return group_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefParamGroupData::set_allocated_group_name(std::string* group_name) {
  if (group_name != nullptr) {
    
  } else {
    
  }
  group_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), group_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefParamGroupData.group_name)
}

// string description = 3;
inline void RefParamGroupData::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& RefParamGroupData::description() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefParamGroupData.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefParamGroupData::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefParamGroupData.description)
}
inline std::string* RefParamGroupData::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefParamGroupData.description)
  return _s;
}
inline const std::string& RefParamGroupData::_internal_description() const {
  return description_.Get();
}
inline void RefParamGroupData::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefParamGroupData::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefParamGroupData::release_description() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefParamGroupData.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefParamGroupData::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefParamGroupData.description)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_RefParamGroup_2eproto
