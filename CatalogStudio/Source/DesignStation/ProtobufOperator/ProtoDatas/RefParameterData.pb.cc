// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RefParameterData.proto

#include "RefParameterData.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr RefParameterData::RefParameterData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ref_param_groups_()
  , ref_params_(){}
struct RefParameterDataDefaultTypeInternal {
  constexpr RefParameterDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RefParameterDataDefaultTypeInternal() {}
  union {
    RefParameterData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RefParameterDataDefaultTypeInternal _RefParameterData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_RefParameterData_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_RefParameterData_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_RefParameterData_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_RefParameterData_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefParameterData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefParameterData, ref_param_groups_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefParameterData, ref_params_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::RefParameterData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_RefParameterData_default_instance_),
};

const char descriptor_table_protodef_RefParameterData_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026RefParameterData.proto\022\026catalog_studio"
  "_message\032\023ParameterData.proto\032\023RefParamG"
  "roup.proto\"\222\001\n\020RefParameterData\022C\n\020ref_p"
  "aram_groups\030\001 \003(\0132).catalog_studio_messa"
  "ge.RefParamGroupData\0229\n\nref_params\030\002 \003(\013"
  "2%.catalog_studio_message.ParameterDatab"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_RefParameterData_2eproto_deps[2] = {
  &::descriptor_table_ParameterData_2eproto,
  &::descriptor_table_RefParamGroup_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_RefParameterData_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RefParameterData_2eproto = {
  false, false, 247, descriptor_table_protodef_RefParameterData_2eproto, "RefParameterData.proto", 
  &descriptor_table_RefParameterData_2eproto_once, descriptor_table_RefParameterData_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_RefParameterData_2eproto::offsets,
  file_level_metadata_RefParameterData_2eproto, file_level_enum_descriptors_RefParameterData_2eproto, file_level_service_descriptors_RefParameterData_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_RefParameterData_2eproto_getter() {
  return &descriptor_table_RefParameterData_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_RefParameterData_2eproto(&descriptor_table_RefParameterData_2eproto);
namespace catalog_studio_message {

// ===================================================================

class RefParameterData::_Internal {
 public:
};

void RefParameterData::clear_ref_param_groups() {
  ref_param_groups_.Clear();
}
void RefParameterData::clear_ref_params() {
  ref_params_.Clear();
}
RefParameterData::RefParameterData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  ref_param_groups_(arena),
  ref_params_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.RefParameterData)
}
RefParameterData::RefParameterData(const RefParameterData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ref_param_groups_(from.ref_param_groups_),
      ref_params_(from.ref_params_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.RefParameterData)
}

void RefParameterData::SharedCtor() {
}

RefParameterData::~RefParameterData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.RefParameterData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RefParameterData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RefParameterData::ArenaDtor(void* object) {
  RefParameterData* _this = reinterpret_cast< RefParameterData* >(object);
  (void)_this;
}
void RefParameterData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RefParameterData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RefParameterData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.RefParameterData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ref_param_groups_.Clear();
  ref_params_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RefParameterData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.RefParamGroupData ref_param_groups = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ref_param_groups(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ParameterData ref_params = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ref_params(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RefParameterData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.RefParameterData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.RefParamGroupData ref_param_groups = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ref_param_groups_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_ref_param_groups(i), target, stream);
  }

  // repeated .catalog_studio_message.ParameterData ref_params = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ref_params_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_ref_params(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.RefParameterData)
  return target;
}

size_t RefParameterData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.RefParameterData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.RefParamGroupData ref_param_groups = 1;
  total_size += 1UL * this->_internal_ref_param_groups_size();
  for (const auto& msg : this->ref_param_groups_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.ParameterData ref_params = 2;
  total_size += 1UL * this->_internal_ref_params_size();
  for (const auto& msg : this->ref_params_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RefParameterData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RefParameterData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RefParameterData::GetClassData() const { return &_class_data_; }

void RefParameterData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RefParameterData *>(to)->MergeFrom(
      static_cast<const RefParameterData &>(from));
}


void RefParameterData::MergeFrom(const RefParameterData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.RefParameterData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ref_param_groups_.MergeFrom(from.ref_param_groups_);
  ref_params_.MergeFrom(from.ref_params_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RefParameterData::CopyFrom(const RefParameterData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.RefParameterData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefParameterData::IsInitialized() const {
  return true;
}

void RefParameterData::InternalSwap(RefParameterData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ref_param_groups_.InternalSwap(&other->ref_param_groups_);
  ref_params_.InternalSwap(&other->ref_params_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RefParameterData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_RefParameterData_2eproto_getter, &descriptor_table_RefParameterData_2eproto_once,
      file_level_metadata_RefParameterData_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::RefParameterData* Arena::CreateMaybeMessage< ::catalog_studio_message::RefParameterData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::RefParameterData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
