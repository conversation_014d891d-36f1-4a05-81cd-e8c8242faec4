// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RefParameterData.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_RefParameterData_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_RefParameterData_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ParameterData.pb.h"
#include "RefParamGroup.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_RefParameterData_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_RefParameterData_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RefParameterData_2eproto;
namespace catalog_studio_message {
class RefParameterData;
struct RefParameterDataDefaultTypeInternal;
extern RefParameterDataDefaultTypeInternal _RefParameterData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::RefParameterData* Arena::CreateMaybeMessage<::catalog_studio_message::RefParameterData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class RefParameterData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.RefParameterData) */ {
 public:
  inline RefParameterData() : RefParameterData(nullptr) {}
  ~RefParameterData() override;
  explicit constexpr RefParameterData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RefParameterData(const RefParameterData& from);
  RefParameterData(RefParameterData&& from) noexcept
    : RefParameterData() {
    *this = ::std::move(from);
  }

  inline RefParameterData& operator=(const RefParameterData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RefParameterData& operator=(RefParameterData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RefParameterData& default_instance() {
    return *internal_default_instance();
  }
  static inline const RefParameterData* internal_default_instance() {
    return reinterpret_cast<const RefParameterData*>(
               &_RefParameterData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RefParameterData& a, RefParameterData& b) {
    a.Swap(&b);
  }
  inline void Swap(RefParameterData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RefParameterData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RefParameterData* New() const final {
    return new RefParameterData();
  }

  RefParameterData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RefParameterData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RefParameterData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RefParameterData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RefParameterData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.RefParameterData";
  }
  protected:
  explicit RefParameterData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRefParamGroupsFieldNumber = 1,
    kRefParamsFieldNumber = 2,
  };
  // repeated .catalog_studio_message.RefParamGroupData ref_param_groups = 1;
  int ref_param_groups_size() const;
  private:
  int _internal_ref_param_groups_size() const;
  public:
  void clear_ref_param_groups();
  ::catalog_studio_message::RefParamGroupData* mutable_ref_param_groups(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefParamGroupData >*
      mutable_ref_param_groups();
  private:
  const ::catalog_studio_message::RefParamGroupData& _internal_ref_param_groups(int index) const;
  ::catalog_studio_message::RefParamGroupData* _internal_add_ref_param_groups();
  public:
  const ::catalog_studio_message::RefParamGroupData& ref_param_groups(int index) const;
  ::catalog_studio_message::RefParamGroupData* add_ref_param_groups();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefParamGroupData >&
      ref_param_groups() const;

  // repeated .catalog_studio_message.ParameterData ref_params = 2;
  int ref_params_size() const;
  private:
  int _internal_ref_params_size() const;
  public:
  void clear_ref_params();
  ::catalog_studio_message::ParameterData* mutable_ref_params(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
      mutable_ref_params();
  private:
  const ::catalog_studio_message::ParameterData& _internal_ref_params(int index) const;
  ::catalog_studio_message::ParameterData* _internal_add_ref_params();
  public:
  const ::catalog_studio_message::ParameterData& ref_params(int index) const;
  ::catalog_studio_message::ParameterData* add_ref_params();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
      ref_params() const;

  // @@protoc_insertion_point(class_scope:catalog_studio_message.RefParameterData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefParamGroupData > ref_param_groups_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData > ref_params_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_RefParameterData_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RefParameterData

// repeated .catalog_studio_message.RefParamGroupData ref_param_groups = 1;
inline int RefParameterData::_internal_ref_param_groups_size() const {
  return ref_param_groups_.size();
}
inline int RefParameterData::ref_param_groups_size() const {
  return _internal_ref_param_groups_size();
}
inline ::catalog_studio_message::RefParamGroupData* RefParameterData::mutable_ref_param_groups(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefParameterData.ref_param_groups)
  return ref_param_groups_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefParamGroupData >*
RefParameterData::mutable_ref_param_groups() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.RefParameterData.ref_param_groups)
  return &ref_param_groups_;
}
inline const ::catalog_studio_message::RefParamGroupData& RefParameterData::_internal_ref_param_groups(int index) const {
  return ref_param_groups_.Get(index);
}
inline const ::catalog_studio_message::RefParamGroupData& RefParameterData::ref_param_groups(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefParameterData.ref_param_groups)
  return _internal_ref_param_groups(index);
}
inline ::catalog_studio_message::RefParamGroupData* RefParameterData::_internal_add_ref_param_groups() {
  return ref_param_groups_.Add();
}
inline ::catalog_studio_message::RefParamGroupData* RefParameterData::add_ref_param_groups() {
  ::catalog_studio_message::RefParamGroupData* _add = _internal_add_ref_param_groups();
  // @@protoc_insertion_point(field_add:catalog_studio_message.RefParameterData.ref_param_groups)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::RefParamGroupData >&
RefParameterData::ref_param_groups() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.RefParameterData.ref_param_groups)
  return ref_param_groups_;
}

// repeated .catalog_studio_message.ParameterData ref_params = 2;
inline int RefParameterData::_internal_ref_params_size() const {
  return ref_params_.size();
}
inline int RefParameterData::ref_params_size() const {
  return _internal_ref_params_size();
}
inline ::catalog_studio_message::ParameterData* RefParameterData::mutable_ref_params(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefParameterData.ref_params)
  return ref_params_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
RefParameterData::mutable_ref_params() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.RefParameterData.ref_params)
  return &ref_params_;
}
inline const ::catalog_studio_message::ParameterData& RefParameterData::_internal_ref_params(int index) const {
  return ref_params_.Get(index);
}
inline const ::catalog_studio_message::ParameterData& RefParameterData::ref_params(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefParameterData.ref_params)
  return _internal_ref_params(index);
}
inline ::catalog_studio_message::ParameterData* RefParameterData::_internal_add_ref_params() {
  return ref_params_.Add();
}
inline ::catalog_studio_message::ParameterData* RefParameterData::add_ref_params() {
  ::catalog_studio_message::ParameterData* _add = _internal_add_ref_params();
  // @@protoc_insertion_point(field_add:catalog_studio_message.RefParameterData.ref_params)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
RefParameterData::ref_params() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.RefParameterData.ref_params)
  return ref_params_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_RefParameterData_2eproto
