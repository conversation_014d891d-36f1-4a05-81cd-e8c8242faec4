// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RefPlaceRuleCustom.proto

#include "RefPlaceRuleCustom.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr RefPlaceRuleCustom::RefPlaceRuleCustom(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , leftconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , leftconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , rightconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , rightconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , upperconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , upperconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , downconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , downconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , frontconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , frontconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , afterconfigexpression_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , afterconfig_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , createdtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedby_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updatedtime_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(0)
  , isenable_(0)
  , isconfig_(0)
  , isleft_(0)
  , isright_(0)
  , isupper_(0)
  , isdown_(0)
  , isfront_(0)
  , isafter_(0)
  , delflag_(0)
  , isusercustom_(false){}
struct RefPlaceRuleCustomDefaultTypeInternal {
  constexpr RefPlaceRuleCustomDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RefPlaceRuleCustomDefaultTypeInternal() {}
  union {
    RefPlaceRuleCustom _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RefPlaceRuleCustomDefaultTypeInternal _RefPlaceRuleCustom_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_RefPlaceRuleCustom_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_RefPlaceRuleCustom_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_RefPlaceRuleCustom_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_RefPlaceRuleCustom_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isenable_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isleft_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, leftconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, leftconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isright_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, rightconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, rightconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isupper_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, upperconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, upperconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isdown_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, downconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, downconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isfront_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, frontconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, frontconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isafter_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, afterconfigexpression_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, afterconfig_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, createdby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, createdtime_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, updatedby_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, updatedtime_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, delflag_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RefPlaceRuleCustom, isusercustom_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::RefPlaceRuleCustom)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_RefPlaceRuleCustom_default_instance_),
};

const char descriptor_table_protodef_RefPlaceRuleCustom_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\030RefPlaceRuleCustom.proto\022\026catalog_stud"
  "io_message\"\341\004\n\022RefPlaceRuleCustom\022\n\n\002id\030"
  "\001 \001(\005\022\014\n\004name\030\002 \001(\t\022\020\n\010isEnable\030\003 \001(\005\022\020\n"
  "\010isConfig\030\004 \001(\005\022\016\n\006isLeft\030\005 \001(\005\022\034\n\024leftC"
  "onfigExpression\030\006 \001(\t\022\022\n\nleftConfig\030\007 \001("
  "\t\022\017\n\007isRight\030\010 \001(\005\022\035\n\025rightConfigExpress"
  "ion\030\t \001(\t\022\023\n\013rightConfig\030\n \001(\t\022\017\n\007isUppe"
  "r\030\013 \001(\005\022\035\n\025upperConfigExpression\030\014 \001(\t\022\023"
  "\n\013upperConfig\030\r \001(\t\022\016\n\006isDown\030\016 \001(\005\022\034\n\024d"
  "ownConfigExpression\030\017 \001(\t\022\022\n\ndownConfig\030"
  "\020 \001(\t\022\017\n\007isFront\030\021 \001(\005\022\035\n\025frontConfigExp"
  "ression\030\022 \001(\t\022\023\n\013frontConfig\030\023 \001(\t\022\017\n\007is"
  "After\030\024 \001(\005\022\035\n\025afterConfigExpression\030\025 \001"
  "(\t\022\023\n\013afterConfig\030\026 \001(\t\022\021\n\tcreatedBy\030\027 \001"
  "(\t\022\023\n\013createdTime\030\030 \001(\t\022\021\n\tupdatedBy\030\031 \001"
  "(\t\022\023\n\013updatedTime\030\032 \001(\t\022\017\n\007delFlag\030\033 \001(\005"
  "\022\024\n\014IsUserCustom\030\034 \001(\010b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_RefPlaceRuleCustom_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RefPlaceRuleCustom_2eproto = {
  false, false, 670, descriptor_table_protodef_RefPlaceRuleCustom_2eproto, "RefPlaceRuleCustom.proto", 
  &descriptor_table_RefPlaceRuleCustom_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_RefPlaceRuleCustom_2eproto::offsets,
  file_level_metadata_RefPlaceRuleCustom_2eproto, file_level_enum_descriptors_RefPlaceRuleCustom_2eproto, file_level_service_descriptors_RefPlaceRuleCustom_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_RefPlaceRuleCustom_2eproto_getter() {
  return &descriptor_table_RefPlaceRuleCustom_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_RefPlaceRuleCustom_2eproto(&descriptor_table_RefPlaceRuleCustom_2eproto);
namespace catalog_studio_message {

// ===================================================================

class RefPlaceRuleCustom::_Internal {
 public:
};

RefPlaceRuleCustom::RefPlaceRuleCustom(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.RefPlaceRuleCustom)
}
RefPlaceRuleCustom::RefPlaceRuleCustom(const RefPlaceRuleCustom& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  leftconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_leftconfigexpression().empty()) {
    leftconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_leftconfigexpression(), 
      GetArenaForAllocation());
  }
  leftconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_leftconfig().empty()) {
    leftconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_leftconfig(), 
      GetArenaForAllocation());
  }
  rightconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_rightconfigexpression().empty()) {
    rightconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_rightconfigexpression(), 
      GetArenaForAllocation());
  }
  rightconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_rightconfig().empty()) {
    rightconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_rightconfig(), 
      GetArenaForAllocation());
  }
  upperconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_upperconfigexpression().empty()) {
    upperconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_upperconfigexpression(), 
      GetArenaForAllocation());
  }
  upperconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_upperconfig().empty()) {
    upperconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_upperconfig(), 
      GetArenaForAllocation());
  }
  downconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_downconfigexpression().empty()) {
    downconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_downconfigexpression(), 
      GetArenaForAllocation());
  }
  downconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_downconfig().empty()) {
    downconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_downconfig(), 
      GetArenaForAllocation());
  }
  frontconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_frontconfigexpression().empty()) {
    frontconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_frontconfigexpression(), 
      GetArenaForAllocation());
  }
  frontconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_frontconfig().empty()) {
    frontconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_frontconfig(), 
      GetArenaForAllocation());
  }
  afterconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_afterconfigexpression().empty()) {
    afterconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_afterconfigexpression(), 
      GetArenaForAllocation());
  }
  afterconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_afterconfig().empty()) {
    afterconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_afterconfig(), 
      GetArenaForAllocation());
  }
  createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdby().empty()) {
    createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdby(), 
      GetArenaForAllocation());
  }
  createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_createdtime().empty()) {
    createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_createdtime(), 
      GetArenaForAllocation());
  }
  updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedby().empty()) {
    updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedby(), 
      GetArenaForAllocation());
  }
  updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_updatedtime().empty()) {
    updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_updatedtime(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&isusercustom_) -
    reinterpret_cast<char*>(&id_)) + sizeof(isusercustom_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.RefPlaceRuleCustom)
}

void RefPlaceRuleCustom::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
leftconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
leftconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
rightconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
rightconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
upperconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
upperconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
downconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
downconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
frontconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
frontconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
afterconfigexpression_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
afterconfig_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
createdtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedby_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
updatedtime_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&isusercustom_) -
    reinterpret_cast<char*>(&id_)) + sizeof(isusercustom_));
}

RefPlaceRuleCustom::~RefPlaceRuleCustom() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.RefPlaceRuleCustom)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RefPlaceRuleCustom::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  leftconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  leftconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  rightconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  rightconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  upperconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  upperconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  downconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  downconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  frontconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  frontconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  afterconfigexpression_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  afterconfig_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  createdtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedby_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  updatedtime_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RefPlaceRuleCustom::ArenaDtor(void* object) {
  RefPlaceRuleCustom* _this = reinterpret_cast< RefPlaceRuleCustom* >(object);
  (void)_this;
}
void RefPlaceRuleCustom::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RefPlaceRuleCustom::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RefPlaceRuleCustom::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.RefPlaceRuleCustom)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  leftconfigexpression_.ClearToEmpty();
  leftconfig_.ClearToEmpty();
  rightconfigexpression_.ClearToEmpty();
  rightconfig_.ClearToEmpty();
  upperconfigexpression_.ClearToEmpty();
  upperconfig_.ClearToEmpty();
  downconfigexpression_.ClearToEmpty();
  downconfig_.ClearToEmpty();
  frontconfigexpression_.ClearToEmpty();
  frontconfig_.ClearToEmpty();
  afterconfigexpression_.ClearToEmpty();
  afterconfig_.ClearToEmpty();
  createdby_.ClearToEmpty();
  createdtime_.ClearToEmpty();
  updatedby_.ClearToEmpty();
  updatedtime_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&isusercustom_) -
      reinterpret_cast<char*>(&id_)) + sizeof(isusercustom_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RefPlaceRuleCustom::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isEnable = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          isenable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isConfig = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          isconfig_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isLeft = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          isleft_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string leftConfigExpression = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_leftconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string leftConfig = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_leftconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.leftConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isRight = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          isright_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string rightConfigExpression = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_rightconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string rightConfig = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_rightconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.rightConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isUpper = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          isupper_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string upperConfigExpression = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_upperconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string upperConfig = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          auto str = _internal_mutable_upperconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.upperConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isDown = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          isdown_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string downConfigExpression = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          auto str = _internal_mutable_downconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.downConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string downConfig = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          auto str = _internal_mutable_downconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.downConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isFront = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 136)) {
          isfront_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string frontConfigExpression = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          auto str = _internal_mutable_frontconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string frontConfig = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          auto str = _internal_mutable_frontconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.frontConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 isAfter = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 160)) {
          isafter_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string afterConfigExpression = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          auto str = _internal_mutable_afterconfigexpression();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string afterConfig = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          auto str = _internal_mutable_afterconfig();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.afterConfig"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdBy = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          auto str = _internal_mutable_createdby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.createdBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string createdTime = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 194)) {
          auto str = _internal_mutable_createdtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.createdTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedBy = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 202)) {
          auto str = _internal_mutable_updatedby();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.updatedBy"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string updatedTime = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 210)) {
          auto str = _internal_mutable_updatedtime();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.RefPlaceRuleCustom.updatedTime"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 delFlag = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 216)) {
          delflag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool IsUserCustom = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 224)) {
          isusercustom_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RefPlaceRuleCustom::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.RefPlaceRuleCustom)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // int32 isEnable = 3;
  if (this->_internal_isenable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_isenable(), target);
  }

  // int32 isConfig = 4;
  if (this->_internal_isconfig() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_isconfig(), target);
  }

  // int32 isLeft = 5;
  if (this->_internal_isleft() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_isleft(), target);
  }

  // string leftConfigExpression = 6;
  if (!this->_internal_leftconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_leftconfigexpression().data(), static_cast<int>(this->_internal_leftconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_leftconfigexpression(), target);
  }

  // string leftConfig = 7;
  if (!this->_internal_leftconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_leftconfig().data(), static_cast<int>(this->_internal_leftconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.leftConfig");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_leftconfig(), target);
  }

  // int32 isRight = 8;
  if (this->_internal_isright() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(8, this->_internal_isright(), target);
  }

  // string rightConfigExpression = 9;
  if (!this->_internal_rightconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rightconfigexpression().data(), static_cast<int>(this->_internal_rightconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_rightconfigexpression(), target);
  }

  // string rightConfig = 10;
  if (!this->_internal_rightconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_rightconfig().data(), static_cast<int>(this->_internal_rightconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.rightConfig");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_rightconfig(), target);
  }

  // int32 isUpper = 11;
  if (this->_internal_isupper() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(11, this->_internal_isupper(), target);
  }

  // string upperConfigExpression = 12;
  if (!this->_internal_upperconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_upperconfigexpression().data(), static_cast<int>(this->_internal_upperconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_upperconfigexpression(), target);
  }

  // string upperConfig = 13;
  if (!this->_internal_upperconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_upperconfig().data(), static_cast<int>(this->_internal_upperconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.upperConfig");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_upperconfig(), target);
  }

  // int32 isDown = 14;
  if (this->_internal_isdown() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(14, this->_internal_isdown(), target);
  }

  // string downConfigExpression = 15;
  if (!this->_internal_downconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_downconfigexpression().data(), static_cast<int>(this->_internal_downconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.downConfigExpression");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_downconfigexpression(), target);
  }

  // string downConfig = 16;
  if (!this->_internal_downconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_downconfig().data(), static_cast<int>(this->_internal_downconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.downConfig");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_downconfig(), target);
  }

  // int32 isFront = 17;
  if (this->_internal_isfront() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(17, this->_internal_isfront(), target);
  }

  // string frontConfigExpression = 18;
  if (!this->_internal_frontconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_frontconfigexpression().data(), static_cast<int>(this->_internal_frontconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_frontconfigexpression(), target);
  }

  // string frontConfig = 19;
  if (!this->_internal_frontconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_frontconfig().data(), static_cast<int>(this->_internal_frontconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.frontConfig");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_frontconfig(), target);
  }

  // int32 isAfter = 20;
  if (this->_internal_isafter() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(20, this->_internal_isafter(), target);
  }

  // string afterConfigExpression = 21;
  if (!this->_internal_afterconfigexpression().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_afterconfigexpression().data(), static_cast<int>(this->_internal_afterconfigexpression().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression");
    target = stream->WriteStringMaybeAliased(
        21, this->_internal_afterconfigexpression(), target);
  }

  // string afterConfig = 22;
  if (!this->_internal_afterconfig().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_afterconfig().data(), static_cast<int>(this->_internal_afterconfig().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.afterConfig");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_afterconfig(), target);
  }

  // string createdBy = 23;
  if (!this->_internal_createdby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdby().data(), static_cast<int>(this->_internal_createdby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.createdBy");
    target = stream->WriteStringMaybeAliased(
        23, this->_internal_createdby(), target);
  }

  // string createdTime = 24;
  if (!this->_internal_createdtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_createdtime().data(), static_cast<int>(this->_internal_createdtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.createdTime");
    target = stream->WriteStringMaybeAliased(
        24, this->_internal_createdtime(), target);
  }

  // string updatedBy = 25;
  if (!this->_internal_updatedby().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedby().data(), static_cast<int>(this->_internal_updatedby().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.updatedBy");
    target = stream->WriteStringMaybeAliased(
        25, this->_internal_updatedby(), target);
  }

  // string updatedTime = 26;
  if (!this->_internal_updatedtime().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_updatedtime().data(), static_cast<int>(this->_internal_updatedtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.RefPlaceRuleCustom.updatedTime");
    target = stream->WriteStringMaybeAliased(
        26, this->_internal_updatedtime(), target);
  }

  // int32 delFlag = 27;
  if (this->_internal_delflag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(27, this->_internal_delflag(), target);
  }

  // bool IsUserCustom = 28;
  if (this->_internal_isusercustom() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(28, this->_internal_isusercustom(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.RefPlaceRuleCustom)
  return target;
}

size_t RefPlaceRuleCustom::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.RefPlaceRuleCustom)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string leftConfigExpression = 6;
  if (!this->_internal_leftconfigexpression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_leftconfigexpression());
  }

  // string leftConfig = 7;
  if (!this->_internal_leftconfig().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_leftconfig());
  }

  // string rightConfigExpression = 9;
  if (!this->_internal_rightconfigexpression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rightconfigexpression());
  }

  // string rightConfig = 10;
  if (!this->_internal_rightconfig().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_rightconfig());
  }

  // string upperConfigExpression = 12;
  if (!this->_internal_upperconfigexpression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_upperconfigexpression());
  }

  // string upperConfig = 13;
  if (!this->_internal_upperconfig().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_upperconfig());
  }

  // string downConfigExpression = 15;
  if (!this->_internal_downconfigexpression().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_downconfigexpression());
  }

  // string downConfig = 16;
  if (!this->_internal_downconfig().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_downconfig());
  }

  // string frontConfigExpression = 18;
  if (!this->_internal_frontconfigexpression().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_frontconfigexpression());
  }

  // string frontConfig = 19;
  if (!this->_internal_frontconfig().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_frontconfig());
  }

  // string afterConfigExpression = 21;
  if (!this->_internal_afterconfigexpression().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_afterconfigexpression());
  }

  // string afterConfig = 22;
  if (!this->_internal_afterconfig().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_afterconfig());
  }

  // string createdBy = 23;
  if (!this->_internal_createdby().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdby());
  }

  // string createdTime = 24;
  if (!this->_internal_createdtime().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_createdtime());
  }

  // string updatedBy = 25;
  if (!this->_internal_updatedby().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedby());
  }

  // string updatedTime = 26;
  if (!this->_internal_updatedtime().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_updatedtime());
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // int32 isEnable = 3;
  if (this->_internal_isenable() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isenable());
  }

  // int32 isConfig = 4;
  if (this->_internal_isconfig() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isconfig());
  }

  // int32 isLeft = 5;
  if (this->_internal_isleft() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isleft());
  }

  // int32 isRight = 8;
  if (this->_internal_isright() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isright());
  }

  // int32 isUpper = 11;
  if (this->_internal_isupper() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isupper());
  }

  // int32 isDown = 14;
  if (this->_internal_isdown() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_isdown());
  }

  // int32 isFront = 17;
  if (this->_internal_isfront() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_isfront());
  }

  // int32 isAfter = 20;
  if (this->_internal_isafter() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_isafter());
  }

  // int32 delFlag = 27;
  if (this->_internal_delflag() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_delflag());
  }

  // bool IsUserCustom = 28;
  if (this->_internal_isusercustom() != 0) {
    total_size += 2 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RefPlaceRuleCustom::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RefPlaceRuleCustom::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RefPlaceRuleCustom::GetClassData() const { return &_class_data_; }

void RefPlaceRuleCustom::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RefPlaceRuleCustom *>(to)->MergeFrom(
      static_cast<const RefPlaceRuleCustom &>(from));
}


void RefPlaceRuleCustom::MergeFrom(const RefPlaceRuleCustom& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.RefPlaceRuleCustom)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_leftconfigexpression().empty()) {
    _internal_set_leftconfigexpression(from._internal_leftconfigexpression());
  }
  if (!from._internal_leftconfig().empty()) {
    _internal_set_leftconfig(from._internal_leftconfig());
  }
  if (!from._internal_rightconfigexpression().empty()) {
    _internal_set_rightconfigexpression(from._internal_rightconfigexpression());
  }
  if (!from._internal_rightconfig().empty()) {
    _internal_set_rightconfig(from._internal_rightconfig());
  }
  if (!from._internal_upperconfigexpression().empty()) {
    _internal_set_upperconfigexpression(from._internal_upperconfigexpression());
  }
  if (!from._internal_upperconfig().empty()) {
    _internal_set_upperconfig(from._internal_upperconfig());
  }
  if (!from._internal_downconfigexpression().empty()) {
    _internal_set_downconfigexpression(from._internal_downconfigexpression());
  }
  if (!from._internal_downconfig().empty()) {
    _internal_set_downconfig(from._internal_downconfig());
  }
  if (!from._internal_frontconfigexpression().empty()) {
    _internal_set_frontconfigexpression(from._internal_frontconfigexpression());
  }
  if (!from._internal_frontconfig().empty()) {
    _internal_set_frontconfig(from._internal_frontconfig());
  }
  if (!from._internal_afterconfigexpression().empty()) {
    _internal_set_afterconfigexpression(from._internal_afterconfigexpression());
  }
  if (!from._internal_afterconfig().empty()) {
    _internal_set_afterconfig(from._internal_afterconfig());
  }
  if (!from._internal_createdby().empty()) {
    _internal_set_createdby(from._internal_createdby());
  }
  if (!from._internal_createdtime().empty()) {
    _internal_set_createdtime(from._internal_createdtime());
  }
  if (!from._internal_updatedby().empty()) {
    _internal_set_updatedby(from._internal_updatedby());
  }
  if (!from._internal_updatedtime().empty()) {
    _internal_set_updatedtime(from._internal_updatedtime());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_isenable() != 0) {
    _internal_set_isenable(from._internal_isenable());
  }
  if (from._internal_isconfig() != 0) {
    _internal_set_isconfig(from._internal_isconfig());
  }
  if (from._internal_isleft() != 0) {
    _internal_set_isleft(from._internal_isleft());
  }
  if (from._internal_isright() != 0) {
    _internal_set_isright(from._internal_isright());
  }
  if (from._internal_isupper() != 0) {
    _internal_set_isupper(from._internal_isupper());
  }
  if (from._internal_isdown() != 0) {
    _internal_set_isdown(from._internal_isdown());
  }
  if (from._internal_isfront() != 0) {
    _internal_set_isfront(from._internal_isfront());
  }
  if (from._internal_isafter() != 0) {
    _internal_set_isafter(from._internal_isafter());
  }
  if (from._internal_delflag() != 0) {
    _internal_set_delflag(from._internal_delflag());
  }
  if (from._internal_isusercustom() != 0) {
    _internal_set_isusercustom(from._internal_isusercustom());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RefPlaceRuleCustom::CopyFrom(const RefPlaceRuleCustom& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.RefPlaceRuleCustom)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefPlaceRuleCustom::IsInitialized() const {
  return true;
}

void RefPlaceRuleCustom::InternalSwap(RefPlaceRuleCustom* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &leftconfigexpression_, lhs_arena,
      &other->leftconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &leftconfig_, lhs_arena,
      &other->leftconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &rightconfigexpression_, lhs_arena,
      &other->rightconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &rightconfig_, lhs_arena,
      &other->rightconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &upperconfigexpression_, lhs_arena,
      &other->upperconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &upperconfig_, lhs_arena,
      &other->upperconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &downconfigexpression_, lhs_arena,
      &other->downconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &downconfig_, lhs_arena,
      &other->downconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &frontconfigexpression_, lhs_arena,
      &other->frontconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &frontconfig_, lhs_arena,
      &other->frontconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &afterconfigexpression_, lhs_arena,
      &other->afterconfigexpression_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &afterconfig_, lhs_arena,
      &other->afterconfig_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdby_, lhs_arena,
      &other->createdby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &createdtime_, lhs_arena,
      &other->createdtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedby_, lhs_arena,
      &other->updatedby_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &updatedtime_, lhs_arena,
      &other->updatedtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RefPlaceRuleCustom, isusercustom_)
      + sizeof(RefPlaceRuleCustom::isusercustom_)
      - PROTOBUF_FIELD_OFFSET(RefPlaceRuleCustom, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RefPlaceRuleCustom::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_RefPlaceRuleCustom_2eproto_getter, &descriptor_table_RefPlaceRuleCustom_2eproto_once,
      file_level_metadata_RefPlaceRuleCustom_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::RefPlaceRuleCustom* Arena::CreateMaybeMessage< ::catalog_studio_message::RefPlaceRuleCustom >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::RefPlaceRuleCustom >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
