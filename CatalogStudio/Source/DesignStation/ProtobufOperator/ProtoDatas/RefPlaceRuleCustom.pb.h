// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RefPlaceRuleCustom.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_RefPlaceRuleCustom_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_RefPlaceRuleCustom_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_RefPlaceRuleCustom_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_RefPlaceRuleCustom_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RefPlaceRuleCustom_2eproto;
namespace catalog_studio_message {
class RefPlaceRuleCustom;
struct RefPlaceRuleCustomDefaultTypeInternal;
extern RefPlaceRuleCustomDefaultTypeInternal _RefPlaceRuleCustom_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::RefPlaceRuleCustom* Arena::CreateMaybeMessage<::catalog_studio_message::RefPlaceRuleCustom>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class RefPlaceRuleCustom final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.RefPlaceRuleCustom) */ {
 public:
  inline RefPlaceRuleCustom() : RefPlaceRuleCustom(nullptr) {}
  ~RefPlaceRuleCustom() override;
  explicit constexpr RefPlaceRuleCustom(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RefPlaceRuleCustom(const RefPlaceRuleCustom& from);
  RefPlaceRuleCustom(RefPlaceRuleCustom&& from) noexcept
    : RefPlaceRuleCustom() {
    *this = ::std::move(from);
  }

  inline RefPlaceRuleCustom& operator=(const RefPlaceRuleCustom& from) {
    CopyFrom(from);
    return *this;
  }
  inline RefPlaceRuleCustom& operator=(RefPlaceRuleCustom&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RefPlaceRuleCustom& default_instance() {
    return *internal_default_instance();
  }
  static inline const RefPlaceRuleCustom* internal_default_instance() {
    return reinterpret_cast<const RefPlaceRuleCustom*>(
               &_RefPlaceRuleCustom_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RefPlaceRuleCustom& a, RefPlaceRuleCustom& b) {
    a.Swap(&b);
  }
  inline void Swap(RefPlaceRuleCustom* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RefPlaceRuleCustom* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RefPlaceRuleCustom* New() const final {
    return new RefPlaceRuleCustom();
  }

  RefPlaceRuleCustom* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RefPlaceRuleCustom>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RefPlaceRuleCustom& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RefPlaceRuleCustom& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RefPlaceRuleCustom* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.RefPlaceRuleCustom";
  }
  protected:
  explicit RefPlaceRuleCustom(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kLeftConfigExpressionFieldNumber = 6,
    kLeftConfigFieldNumber = 7,
    kRightConfigExpressionFieldNumber = 9,
    kRightConfigFieldNumber = 10,
    kUpperConfigExpressionFieldNumber = 12,
    kUpperConfigFieldNumber = 13,
    kDownConfigExpressionFieldNumber = 15,
    kDownConfigFieldNumber = 16,
    kFrontConfigExpressionFieldNumber = 18,
    kFrontConfigFieldNumber = 19,
    kAfterConfigExpressionFieldNumber = 21,
    kAfterConfigFieldNumber = 22,
    kCreatedByFieldNumber = 23,
    kCreatedTimeFieldNumber = 24,
    kUpdatedByFieldNumber = 25,
    kUpdatedTimeFieldNumber = 26,
    kIdFieldNumber = 1,
    kIsEnableFieldNumber = 3,
    kIsConfigFieldNumber = 4,
    kIsLeftFieldNumber = 5,
    kIsRightFieldNumber = 8,
    kIsUpperFieldNumber = 11,
    kIsDownFieldNumber = 14,
    kIsFrontFieldNumber = 17,
    kIsAfterFieldNumber = 20,
    kDelFlagFieldNumber = 27,
    kIsUserCustomFieldNumber = 28,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string leftConfigExpression = 6;
  void clear_leftconfigexpression();
  const std::string& leftconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_leftconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_leftconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_leftconfigexpression();
  void set_allocated_leftconfigexpression(std::string* leftconfigexpression);
  private:
  const std::string& _internal_leftconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_leftconfigexpression(const std::string& value);
  std::string* _internal_mutable_leftconfigexpression();
  public:

  // string leftConfig = 7;
  void clear_leftconfig();
  const std::string& leftconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_leftconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_leftconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_leftconfig();
  void set_allocated_leftconfig(std::string* leftconfig);
  private:
  const std::string& _internal_leftconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_leftconfig(const std::string& value);
  std::string* _internal_mutable_leftconfig();
  public:

  // string rightConfigExpression = 9;
  void clear_rightconfigexpression();
  const std::string& rightconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rightconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rightconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_rightconfigexpression();
  void set_allocated_rightconfigexpression(std::string* rightconfigexpression);
  private:
  const std::string& _internal_rightconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rightconfigexpression(const std::string& value);
  std::string* _internal_mutable_rightconfigexpression();
  public:

  // string rightConfig = 10;
  void clear_rightconfig();
  const std::string& rightconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rightconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rightconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_rightconfig();
  void set_allocated_rightconfig(std::string* rightconfig);
  private:
  const std::string& _internal_rightconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rightconfig(const std::string& value);
  std::string* _internal_mutable_rightconfig();
  public:

  // string upperConfigExpression = 12;
  void clear_upperconfigexpression();
  const std::string& upperconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_upperconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_upperconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_upperconfigexpression();
  void set_allocated_upperconfigexpression(std::string* upperconfigexpression);
  private:
  const std::string& _internal_upperconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_upperconfigexpression(const std::string& value);
  std::string* _internal_mutable_upperconfigexpression();
  public:

  // string upperConfig = 13;
  void clear_upperconfig();
  const std::string& upperconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_upperconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_upperconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_upperconfig();
  void set_allocated_upperconfig(std::string* upperconfig);
  private:
  const std::string& _internal_upperconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_upperconfig(const std::string& value);
  std::string* _internal_mutable_upperconfig();
  public:

  // string downConfigExpression = 15;
  void clear_downconfigexpression();
  const std::string& downconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_downconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_downconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_downconfigexpression();
  void set_allocated_downconfigexpression(std::string* downconfigexpression);
  private:
  const std::string& _internal_downconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_downconfigexpression(const std::string& value);
  std::string* _internal_mutable_downconfigexpression();
  public:

  // string downConfig = 16;
  void clear_downconfig();
  const std::string& downconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_downconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_downconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_downconfig();
  void set_allocated_downconfig(std::string* downconfig);
  private:
  const std::string& _internal_downconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_downconfig(const std::string& value);
  std::string* _internal_mutable_downconfig();
  public:

  // string frontConfigExpression = 18;
  void clear_frontconfigexpression();
  const std::string& frontconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_frontconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_frontconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_frontconfigexpression();
  void set_allocated_frontconfigexpression(std::string* frontconfigexpression);
  private:
  const std::string& _internal_frontconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_frontconfigexpression(const std::string& value);
  std::string* _internal_mutable_frontconfigexpression();
  public:

  // string frontConfig = 19;
  void clear_frontconfig();
  const std::string& frontconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_frontconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_frontconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_frontconfig();
  void set_allocated_frontconfig(std::string* frontconfig);
  private:
  const std::string& _internal_frontconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_frontconfig(const std::string& value);
  std::string* _internal_mutable_frontconfig();
  public:

  // string afterConfigExpression = 21;
  void clear_afterconfigexpression();
  const std::string& afterconfigexpression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_afterconfigexpression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_afterconfigexpression();
  PROTOBUF_MUST_USE_RESULT std::string* release_afterconfigexpression();
  void set_allocated_afterconfigexpression(std::string* afterconfigexpression);
  private:
  const std::string& _internal_afterconfigexpression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_afterconfigexpression(const std::string& value);
  std::string* _internal_mutable_afterconfigexpression();
  public:

  // string afterConfig = 22;
  void clear_afterconfig();
  const std::string& afterconfig() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_afterconfig(ArgT0&& arg0, ArgT... args);
  std::string* mutable_afterconfig();
  PROTOBUF_MUST_USE_RESULT std::string* release_afterconfig();
  void set_allocated_afterconfig(std::string* afterconfig);
  private:
  const std::string& _internal_afterconfig() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_afterconfig(const std::string& value);
  std::string* _internal_mutable_afterconfig();
  public:

  // string createdBy = 23;
  void clear_createdby();
  const std::string& createdby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdby();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdby();
  void set_allocated_createdby(std::string* createdby);
  private:
  const std::string& _internal_createdby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdby(const std::string& value);
  std::string* _internal_mutable_createdby();
  public:

  // string createdTime = 24;
  void clear_createdtime();
  const std::string& createdtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_createdtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_createdtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_createdtime();
  void set_allocated_createdtime(std::string* createdtime);
  private:
  const std::string& _internal_createdtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_createdtime(const std::string& value);
  std::string* _internal_mutable_createdtime();
  public:

  // string updatedBy = 25;
  void clear_updatedby();
  const std::string& updatedby() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedby(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedby();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedby();
  void set_allocated_updatedby(std::string* updatedby);
  private:
  const std::string& _internal_updatedby() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedby(const std::string& value);
  std::string* _internal_mutable_updatedby();
  public:

  // string updatedTime = 26;
  void clear_updatedtime();
  const std::string& updatedtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_updatedtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_updatedtime();
  PROTOBUF_MUST_USE_RESULT std::string* release_updatedtime();
  void set_allocated_updatedtime(std::string* updatedtime);
  private:
  const std::string& _internal_updatedtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_updatedtime(const std::string& value);
  std::string* _internal_mutable_updatedtime();
  public:

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isEnable = 3;
  void clear_isenable();
  ::PROTOBUF_NAMESPACE_ID::int32 isenable() const;
  void set_isenable(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isenable() const;
  void _internal_set_isenable(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isConfig = 4;
  void clear_isconfig();
  ::PROTOBUF_NAMESPACE_ID::int32 isconfig() const;
  void set_isconfig(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isconfig() const;
  void _internal_set_isconfig(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isLeft = 5;
  void clear_isleft();
  ::PROTOBUF_NAMESPACE_ID::int32 isleft() const;
  void set_isleft(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isleft() const;
  void _internal_set_isleft(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isRight = 8;
  void clear_isright();
  ::PROTOBUF_NAMESPACE_ID::int32 isright() const;
  void set_isright(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isright() const;
  void _internal_set_isright(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isUpper = 11;
  void clear_isupper();
  ::PROTOBUF_NAMESPACE_ID::int32 isupper() const;
  void set_isupper(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isupper() const;
  void _internal_set_isupper(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isDown = 14;
  void clear_isdown();
  ::PROTOBUF_NAMESPACE_ID::int32 isdown() const;
  void set_isdown(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isdown() const;
  void _internal_set_isdown(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isFront = 17;
  void clear_isfront();
  ::PROTOBUF_NAMESPACE_ID::int32 isfront() const;
  void set_isfront(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isfront() const;
  void _internal_set_isfront(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 isAfter = 20;
  void clear_isafter();
  ::PROTOBUF_NAMESPACE_ID::int32 isafter() const;
  void set_isafter(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_isafter() const;
  void _internal_set_isafter(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 delFlag = 27;
  void clear_delflag();
  ::PROTOBUF_NAMESPACE_ID::int32 delflag() const;
  void set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_delflag() const;
  void _internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // bool IsUserCustom = 28;
  void clear_isusercustom();
  bool isusercustom() const;
  void set_isusercustom(bool value);
  private:
  bool _internal_isusercustom() const;
  void _internal_set_isusercustom(bool value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.RefPlaceRuleCustom)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr leftconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr leftconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rightconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rightconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr upperconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr upperconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr downconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr downconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr frontconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr frontconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr afterconfigexpression_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr afterconfig_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr createdtime_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedby_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr updatedtime_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 isenable_;
  ::PROTOBUF_NAMESPACE_ID::int32 isconfig_;
  ::PROTOBUF_NAMESPACE_ID::int32 isleft_;
  ::PROTOBUF_NAMESPACE_ID::int32 isright_;
  ::PROTOBUF_NAMESPACE_ID::int32 isupper_;
  ::PROTOBUF_NAMESPACE_ID::int32 isdown_;
  ::PROTOBUF_NAMESPACE_ID::int32 isfront_;
  ::PROTOBUF_NAMESPACE_ID::int32 isafter_;
  ::PROTOBUF_NAMESPACE_ID::int32 delflag_;
  bool isusercustom_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_RefPlaceRuleCustom_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RefPlaceRuleCustom

// int32 id = 1;
inline void RefPlaceRuleCustom::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.id)
  return _internal_id();
}
inline void RefPlaceRuleCustom::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void RefPlaceRuleCustom::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.id)
}

// string name = 2;
inline void RefPlaceRuleCustom::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.name)
}
inline std::string* RefPlaceRuleCustom::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.name)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_name() const {
  return name_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.name)
}

// int32 isEnable = 3;
inline void RefPlaceRuleCustom::clear_isenable() {
  isenable_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isenable() const {
  return isenable_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isenable() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isEnable)
  return _internal_isenable();
}
inline void RefPlaceRuleCustom::_internal_set_isenable(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isenable_ = value;
}
inline void RefPlaceRuleCustom::set_isenable(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isenable(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isEnable)
}

// int32 isConfig = 4;
inline void RefPlaceRuleCustom::clear_isconfig() {
  isconfig_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isconfig() const {
  return isconfig_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isConfig)
  return _internal_isconfig();
}
inline void RefPlaceRuleCustom::_internal_set_isconfig(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isconfig_ = value;
}
inline void RefPlaceRuleCustom::set_isconfig(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isconfig(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isConfig)
}

// int32 isLeft = 5;
inline void RefPlaceRuleCustom::clear_isleft() {
  isleft_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isleft() const {
  return isleft_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isleft() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isLeft)
  return _internal_isleft();
}
inline void RefPlaceRuleCustom::_internal_set_isleft(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isleft_ = value;
}
inline void RefPlaceRuleCustom::set_isleft(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isleft(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isLeft)
}

// string leftConfigExpression = 6;
inline void RefPlaceRuleCustom::clear_leftconfigexpression() {
  leftconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::leftconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression)
  return _internal_leftconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_leftconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 leftconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_leftconfigexpression() {
  std::string* _s = _internal_mutable_leftconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_leftconfigexpression() const {
  return leftconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_leftconfigexpression(const std::string& value) {
  
  leftconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_leftconfigexpression() {
  
  return leftconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_leftconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression)
  return leftconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_leftconfigexpression(std::string* leftconfigexpression) {
  if (leftconfigexpression != nullptr) {
    
  } else {
    
  }
  leftconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), leftconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.leftConfigExpression)
}

// string leftConfig = 7;
inline void RefPlaceRuleCustom::clear_leftconfig() {
  leftconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::leftconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.leftConfig)
  return _internal_leftconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_leftconfig(ArgT0&& arg0, ArgT... args) {
 
 leftconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.leftConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_leftconfig() {
  std::string* _s = _internal_mutable_leftconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.leftConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_leftconfig() const {
  return leftconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_leftconfig(const std::string& value) {
  
  leftconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_leftconfig() {
  
  return leftconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_leftconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.leftConfig)
  return leftconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_leftconfig(std::string* leftconfig) {
  if (leftconfig != nullptr) {
    
  } else {
    
  }
  leftconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), leftconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.leftConfig)
}

// int32 isRight = 8;
inline void RefPlaceRuleCustom::clear_isright() {
  isright_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isright() const {
  return isright_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isright() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isRight)
  return _internal_isright();
}
inline void RefPlaceRuleCustom::_internal_set_isright(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isright_ = value;
}
inline void RefPlaceRuleCustom::set_isright(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isright(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isRight)
}

// string rightConfigExpression = 9;
inline void RefPlaceRuleCustom::clear_rightconfigexpression() {
  rightconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::rightconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression)
  return _internal_rightconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_rightconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 rightconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_rightconfigexpression() {
  std::string* _s = _internal_mutable_rightconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_rightconfigexpression() const {
  return rightconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_rightconfigexpression(const std::string& value) {
  
  rightconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_rightconfigexpression() {
  
  return rightconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_rightconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression)
  return rightconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_rightconfigexpression(std::string* rightconfigexpression) {
  if (rightconfigexpression != nullptr) {
    
  } else {
    
  }
  rightconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), rightconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.rightConfigExpression)
}

// string rightConfig = 10;
inline void RefPlaceRuleCustom::clear_rightconfig() {
  rightconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::rightconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.rightConfig)
  return _internal_rightconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_rightconfig(ArgT0&& arg0, ArgT... args) {
 
 rightconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.rightConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_rightconfig() {
  std::string* _s = _internal_mutable_rightconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.rightConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_rightconfig() const {
  return rightconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_rightconfig(const std::string& value) {
  
  rightconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_rightconfig() {
  
  return rightconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_rightconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.rightConfig)
  return rightconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_rightconfig(std::string* rightconfig) {
  if (rightconfig != nullptr) {
    
  } else {
    
  }
  rightconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), rightconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.rightConfig)
}

// int32 isUpper = 11;
inline void RefPlaceRuleCustom::clear_isupper() {
  isupper_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isupper() const {
  return isupper_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isupper() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isUpper)
  return _internal_isupper();
}
inline void RefPlaceRuleCustom::_internal_set_isupper(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isupper_ = value;
}
inline void RefPlaceRuleCustom::set_isupper(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isupper(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isUpper)
}

// string upperConfigExpression = 12;
inline void RefPlaceRuleCustom::clear_upperconfigexpression() {
  upperconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::upperconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression)
  return _internal_upperconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_upperconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 upperconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_upperconfigexpression() {
  std::string* _s = _internal_mutable_upperconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_upperconfigexpression() const {
  return upperconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_upperconfigexpression(const std::string& value) {
  
  upperconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_upperconfigexpression() {
  
  return upperconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_upperconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression)
  return upperconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_upperconfigexpression(std::string* upperconfigexpression) {
  if (upperconfigexpression != nullptr) {
    
  } else {
    
  }
  upperconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), upperconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.upperConfigExpression)
}

// string upperConfig = 13;
inline void RefPlaceRuleCustom::clear_upperconfig() {
  upperconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::upperconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.upperConfig)
  return _internal_upperconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_upperconfig(ArgT0&& arg0, ArgT... args) {
 
 upperconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.upperConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_upperconfig() {
  std::string* _s = _internal_mutable_upperconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.upperConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_upperconfig() const {
  return upperconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_upperconfig(const std::string& value) {
  
  upperconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_upperconfig() {
  
  return upperconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_upperconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.upperConfig)
  return upperconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_upperconfig(std::string* upperconfig) {
  if (upperconfig != nullptr) {
    
  } else {
    
  }
  upperconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), upperconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.upperConfig)
}

// int32 isDown = 14;
inline void RefPlaceRuleCustom::clear_isdown() {
  isdown_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isdown() const {
  return isdown_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isdown() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isDown)
  return _internal_isdown();
}
inline void RefPlaceRuleCustom::_internal_set_isdown(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isdown_ = value;
}
inline void RefPlaceRuleCustom::set_isdown(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isdown(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isDown)
}

// string downConfigExpression = 15;
inline void RefPlaceRuleCustom::clear_downconfigexpression() {
  downconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::downconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.downConfigExpression)
  return _internal_downconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_downconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 downconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.downConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_downconfigexpression() {
  std::string* _s = _internal_mutable_downconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.downConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_downconfigexpression() const {
  return downconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_downconfigexpression(const std::string& value) {
  
  downconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_downconfigexpression() {
  
  return downconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_downconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.downConfigExpression)
  return downconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_downconfigexpression(std::string* downconfigexpression) {
  if (downconfigexpression != nullptr) {
    
  } else {
    
  }
  downconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), downconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.downConfigExpression)
}

// string downConfig = 16;
inline void RefPlaceRuleCustom::clear_downconfig() {
  downconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::downconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.downConfig)
  return _internal_downconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_downconfig(ArgT0&& arg0, ArgT... args) {
 
 downconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.downConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_downconfig() {
  std::string* _s = _internal_mutable_downconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.downConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_downconfig() const {
  return downconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_downconfig(const std::string& value) {
  
  downconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_downconfig() {
  
  return downconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_downconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.downConfig)
  return downconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_downconfig(std::string* downconfig) {
  if (downconfig != nullptr) {
    
  } else {
    
  }
  downconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), downconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.downConfig)
}

// int32 isFront = 17;
inline void RefPlaceRuleCustom::clear_isfront() {
  isfront_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isfront() const {
  return isfront_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isfront() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isFront)
  return _internal_isfront();
}
inline void RefPlaceRuleCustom::_internal_set_isfront(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isfront_ = value;
}
inline void RefPlaceRuleCustom::set_isfront(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isfront(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isFront)
}

// string frontConfigExpression = 18;
inline void RefPlaceRuleCustom::clear_frontconfigexpression() {
  frontconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::frontconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression)
  return _internal_frontconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_frontconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 frontconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_frontconfigexpression() {
  std::string* _s = _internal_mutable_frontconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_frontconfigexpression() const {
  return frontconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_frontconfigexpression(const std::string& value) {
  
  frontconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_frontconfigexpression() {
  
  return frontconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_frontconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression)
  return frontconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_frontconfigexpression(std::string* frontconfigexpression) {
  if (frontconfigexpression != nullptr) {
    
  } else {
    
  }
  frontconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), frontconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.frontConfigExpression)
}

// string frontConfig = 19;
inline void RefPlaceRuleCustom::clear_frontconfig() {
  frontconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::frontconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.frontConfig)
  return _internal_frontconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_frontconfig(ArgT0&& arg0, ArgT... args) {
 
 frontconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.frontConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_frontconfig() {
  std::string* _s = _internal_mutable_frontconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.frontConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_frontconfig() const {
  return frontconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_frontconfig(const std::string& value) {
  
  frontconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_frontconfig() {
  
  return frontconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_frontconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.frontConfig)
  return frontconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_frontconfig(std::string* frontconfig) {
  if (frontconfig != nullptr) {
    
  } else {
    
  }
  frontconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), frontconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.frontConfig)
}

// int32 isAfter = 20;
inline void RefPlaceRuleCustom::clear_isafter() {
  isafter_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_isafter() const {
  return isafter_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::isafter() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.isAfter)
  return _internal_isafter();
}
inline void RefPlaceRuleCustom::_internal_set_isafter(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  isafter_ = value;
}
inline void RefPlaceRuleCustom::set_isafter(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_isafter(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.isAfter)
}

// string afterConfigExpression = 21;
inline void RefPlaceRuleCustom::clear_afterconfigexpression() {
  afterconfigexpression_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::afterconfigexpression() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression)
  return _internal_afterconfigexpression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_afterconfigexpression(ArgT0&& arg0, ArgT... args) {
 
 afterconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression)
}
inline std::string* RefPlaceRuleCustom::mutable_afterconfigexpression() {
  std::string* _s = _internal_mutable_afterconfigexpression();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_afterconfigexpression() const {
  return afterconfigexpression_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_afterconfigexpression(const std::string& value) {
  
  afterconfigexpression_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_afterconfigexpression() {
  
  return afterconfigexpression_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_afterconfigexpression() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression)
  return afterconfigexpression_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_afterconfigexpression(std::string* afterconfigexpression) {
  if (afterconfigexpression != nullptr) {
    
  } else {
    
  }
  afterconfigexpression_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), afterconfigexpression,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.afterConfigExpression)
}

// string afterConfig = 22;
inline void RefPlaceRuleCustom::clear_afterconfig() {
  afterconfig_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::afterconfig() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.afterConfig)
  return _internal_afterconfig();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_afterconfig(ArgT0&& arg0, ArgT... args) {
 
 afterconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.afterConfig)
}
inline std::string* RefPlaceRuleCustom::mutable_afterconfig() {
  std::string* _s = _internal_mutable_afterconfig();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.afterConfig)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_afterconfig() const {
  return afterconfig_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_afterconfig(const std::string& value) {
  
  afterconfig_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_afterconfig() {
  
  return afterconfig_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_afterconfig() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.afterConfig)
  return afterconfig_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_afterconfig(std::string* afterconfig) {
  if (afterconfig != nullptr) {
    
  } else {
    
  }
  afterconfig_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), afterconfig,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.afterConfig)
}

// string createdBy = 23;
inline void RefPlaceRuleCustom::clear_createdby() {
  createdby_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::createdby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.createdBy)
  return _internal_createdby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_createdby(ArgT0&& arg0, ArgT... args) {
 
 createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.createdBy)
}
inline std::string* RefPlaceRuleCustom::mutable_createdby() {
  std::string* _s = _internal_mutable_createdby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.createdBy)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_createdby() const {
  return createdby_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_createdby(const std::string& value) {
  
  createdby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_createdby() {
  
  return createdby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_createdby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.createdBy)
  return createdby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_createdby(std::string* createdby) {
  if (createdby != nullptr) {
    
  } else {
    
  }
  createdby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.createdBy)
}

// string createdTime = 24;
inline void RefPlaceRuleCustom::clear_createdtime() {
  createdtime_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::createdtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.createdTime)
  return _internal_createdtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_createdtime(ArgT0&& arg0, ArgT... args) {
 
 createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.createdTime)
}
inline std::string* RefPlaceRuleCustom::mutable_createdtime() {
  std::string* _s = _internal_mutable_createdtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.createdTime)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_createdtime() const {
  return createdtime_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_createdtime(const std::string& value) {
  
  createdtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_createdtime() {
  
  return createdtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_createdtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.createdTime)
  return createdtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_createdtime(std::string* createdtime) {
  if (createdtime != nullptr) {
    
  } else {
    
  }
  createdtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), createdtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.createdTime)
}

// string updatedBy = 25;
inline void RefPlaceRuleCustom::clear_updatedby() {
  updatedby_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::updatedby() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.updatedBy)
  return _internal_updatedby();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_updatedby(ArgT0&& arg0, ArgT... args) {
 
 updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.updatedBy)
}
inline std::string* RefPlaceRuleCustom::mutable_updatedby() {
  std::string* _s = _internal_mutable_updatedby();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.updatedBy)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_updatedby() const {
  return updatedby_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_updatedby(const std::string& value) {
  
  updatedby_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_updatedby() {
  
  return updatedby_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_updatedby() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.updatedBy)
  return updatedby_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_updatedby(std::string* updatedby) {
  if (updatedby != nullptr) {
    
  } else {
    
  }
  updatedby_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedby,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.updatedBy)
}

// string updatedTime = 26;
inline void RefPlaceRuleCustom::clear_updatedtime() {
  updatedtime_.ClearToEmpty();
}
inline const std::string& RefPlaceRuleCustom::updatedtime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.updatedTime)
  return _internal_updatedtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RefPlaceRuleCustom::set_updatedtime(ArgT0&& arg0, ArgT... args) {
 
 updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.updatedTime)
}
inline std::string* RefPlaceRuleCustom::mutable_updatedtime() {
  std::string* _s = _internal_mutable_updatedtime();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RefPlaceRuleCustom.updatedTime)
  return _s;
}
inline const std::string& RefPlaceRuleCustom::_internal_updatedtime() const {
  return updatedtime_.Get();
}
inline void RefPlaceRuleCustom::_internal_set_updatedtime(const std::string& value) {
  
  updatedtime_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::_internal_mutable_updatedtime() {
  
  return updatedtime_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RefPlaceRuleCustom::release_updatedtime() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RefPlaceRuleCustom.updatedTime)
  return updatedtime_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RefPlaceRuleCustom::set_allocated_updatedtime(std::string* updatedtime) {
  if (updatedtime != nullptr) {
    
  } else {
    
  }
  updatedtime_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), updatedtime,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RefPlaceRuleCustom.updatedTime)
}

// int32 delFlag = 27;
inline void RefPlaceRuleCustom::clear_delflag() {
  delflag_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::_internal_delflag() const {
  return delflag_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 RefPlaceRuleCustom::delflag() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.delFlag)
  return _internal_delflag();
}
inline void RefPlaceRuleCustom::_internal_set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  delflag_ = value;
}
inline void RefPlaceRuleCustom::set_delflag(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_delflag(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.delFlag)
}

// bool IsUserCustom = 28;
inline void RefPlaceRuleCustom::clear_isusercustom() {
  isusercustom_ = false;
}
inline bool RefPlaceRuleCustom::_internal_isusercustom() const {
  return isusercustom_;
}
inline bool RefPlaceRuleCustom::isusercustom() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RefPlaceRuleCustom.IsUserCustom)
  return _internal_isusercustom();
}
inline void RefPlaceRuleCustom::_internal_set_isusercustom(bool value) {
  
  isusercustom_ = value;
}
inline void RefPlaceRuleCustom::set_isusercustom(bool value) {
  _internal_set_isusercustom(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.RefPlaceRuleCustom.IsUserCustom)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_RefPlaceRuleCustom_2eproto
