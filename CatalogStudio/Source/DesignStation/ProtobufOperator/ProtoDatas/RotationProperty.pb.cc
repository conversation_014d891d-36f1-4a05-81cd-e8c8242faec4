// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RotationProperty.proto

#include "RotationProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr RotationProperty::RotationProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : roll_(nullptr)
  , pitch_(nullptr)
  , yaw_(nullptr){}
struct RotationPropertyDefaultTypeInternal {
  constexpr RotationPropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RotationPropertyDefaultTypeInternal() {}
  union {
    RotationProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RotationPropertyDefaultTypeInternal _RotationProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_RotationProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_RotationProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_RotationProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_RotationProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RotationProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RotationProperty, roll_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RotationProperty, pitch_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::RotationProperty, yaw_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::RotationProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_RotationProperty_default_instance_),
};

const char descriptor_table_protodef_RotationProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026RotationProperty.proto\022\026catalog_studio"
  "_message\032\031ExpressionValuePair.proto\"\303\001\n\020"
  "RotationProperty\0229\n\004roll\030\001 \001(\0132+.catalog"
  "_studio_message.ExpressionValuePair\022:\n\005p"
  "itch\030\002 \001(\0132+.catalog_studio_message.Expr"
  "essionValuePair\0228\n\003yaw\030\003 \001(\0132+.catalog_s"
  "tudio_message.ExpressionValuePairb\006proto"
  "3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_RotationProperty_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_RotationProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RotationProperty_2eproto = {
  false, false, 281, descriptor_table_protodef_RotationProperty_2eproto, "RotationProperty.proto", 
  &descriptor_table_RotationProperty_2eproto_once, descriptor_table_RotationProperty_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_RotationProperty_2eproto::offsets,
  file_level_metadata_RotationProperty_2eproto, file_level_enum_descriptors_RotationProperty_2eproto, file_level_service_descriptors_RotationProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_RotationProperty_2eproto_getter() {
  return &descriptor_table_RotationProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_RotationProperty_2eproto(&descriptor_table_RotationProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class RotationProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& roll(const RotationProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& pitch(const RotationProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& yaw(const RotationProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
RotationProperty::_Internal::roll(const RotationProperty* msg) {
  return *msg->roll_;
}
const ::catalog_studio_message::ExpressionValuePair&
RotationProperty::_Internal::pitch(const RotationProperty* msg) {
  return *msg->pitch_;
}
const ::catalog_studio_message::ExpressionValuePair&
RotationProperty::_Internal::yaw(const RotationProperty* msg) {
  return *msg->yaw_;
}
void RotationProperty::clear_roll() {
  if (GetArenaForAllocation() == nullptr && roll_ != nullptr) {
    delete roll_;
  }
  roll_ = nullptr;
}
void RotationProperty::clear_pitch() {
  if (GetArenaForAllocation() == nullptr && pitch_ != nullptr) {
    delete pitch_;
  }
  pitch_ = nullptr;
}
void RotationProperty::clear_yaw() {
  if (GetArenaForAllocation() == nullptr && yaw_ != nullptr) {
    delete yaw_;
  }
  yaw_ = nullptr;
}
RotationProperty::RotationProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.RotationProperty)
}
RotationProperty::RotationProperty(const RotationProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_roll()) {
    roll_ = new ::catalog_studio_message::ExpressionValuePair(*from.roll_);
  } else {
    roll_ = nullptr;
  }
  if (from._internal_has_pitch()) {
    pitch_ = new ::catalog_studio_message::ExpressionValuePair(*from.pitch_);
  } else {
    pitch_ = nullptr;
  }
  if (from._internal_has_yaw()) {
    yaw_ = new ::catalog_studio_message::ExpressionValuePair(*from.yaw_);
  } else {
    yaw_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.RotationProperty)
}

void RotationProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&roll_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&yaw_) -
    reinterpret_cast<char*>(&roll_)) + sizeof(yaw_));
}

RotationProperty::~RotationProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.RotationProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RotationProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete roll_;
  if (this != internal_default_instance()) delete pitch_;
  if (this != internal_default_instance()) delete yaw_;
}

void RotationProperty::ArenaDtor(void* object) {
  RotationProperty* _this = reinterpret_cast< RotationProperty* >(object);
  (void)_this;
}
void RotationProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotationProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RotationProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.RotationProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && roll_ != nullptr) {
    delete roll_;
  }
  roll_ = nullptr;
  if (GetArenaForAllocation() == nullptr && pitch_ != nullptr) {
    delete pitch_;
  }
  pitch_ = nullptr;
  if (GetArenaForAllocation() == nullptr && yaw_ != nullptr) {
    delete yaw_;
  }
  yaw_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotationProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.ExpressionValuePair roll = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_roll(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair pitch = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_pitch(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair yaw = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_yaw(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RotationProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.RotationProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair roll = 1;
  if (this->_internal_has_roll()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::roll(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair pitch = 2;
  if (this->_internal_has_pitch()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::pitch(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair yaw = 3;
  if (this->_internal_has_yaw()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::yaw(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.RotationProperty)
  return target;
}

size_t RotationProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.RotationProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair roll = 1;
  if (this->_internal_has_roll()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *roll_);
  }

  // .catalog_studio_message.ExpressionValuePair pitch = 2;
  if (this->_internal_has_pitch()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *pitch_);
  }

  // .catalog_studio_message.ExpressionValuePair yaw = 3;
  if (this->_internal_has_yaw()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *yaw_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RotationProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RotationProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RotationProperty::GetClassData() const { return &_class_data_; }

void RotationProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RotationProperty *>(to)->MergeFrom(
      static_cast<const RotationProperty &>(from));
}


void RotationProperty::MergeFrom(const RotationProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.RotationProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_roll()) {
    _internal_mutable_roll()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_roll());
  }
  if (from._internal_has_pitch()) {
    _internal_mutable_pitch()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_pitch());
  }
  if (from._internal_has_yaw()) {
    _internal_mutable_yaw()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_yaw());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RotationProperty::CopyFrom(const RotationProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.RotationProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotationProperty::IsInitialized() const {
  return true;
}

void RotationProperty::InternalSwap(RotationProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotationProperty, yaw_)
      + sizeof(RotationProperty::yaw_)
      - PROTOBUF_FIELD_OFFSET(RotationProperty, roll_)>(
          reinterpret_cast<char*>(&roll_),
          reinterpret_cast<char*>(&other->roll_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotationProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_RotationProperty_2eproto_getter, &descriptor_table_RotationProperty_2eproto_once,
      file_level_metadata_RotationProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::RotationProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::RotationProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::RotationProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
