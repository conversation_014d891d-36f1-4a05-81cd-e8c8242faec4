// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RotationProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_RotationProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_RotationProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_RotationProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_RotationProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_RotationProperty_2eproto;
namespace catalog_studio_message {
class RotationProperty;
struct RotationPropertyDefaultTypeInternal;
extern RotationPropertyDefaultTypeInternal _RotationProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::RotationProperty* Arena::CreateMaybeMessage<::catalog_studio_message::RotationProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class RotationProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.RotationProperty) */ {
 public:
  inline RotationProperty() : RotationProperty(nullptr) {}
  ~RotationProperty() override;
  explicit constexpr RotationProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RotationProperty(const RotationProperty& from);
  RotationProperty(RotationProperty&& from) noexcept
    : RotationProperty() {
    *this = ::std::move(from);
  }

  inline RotationProperty& operator=(const RotationProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline RotationProperty& operator=(RotationProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RotationProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const RotationProperty* internal_default_instance() {
    return reinterpret_cast<const RotationProperty*>(
               &_RotationProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RotationProperty& a, RotationProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(RotationProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RotationProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RotationProperty* New() const final {
    return new RotationProperty();
  }

  RotationProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RotationProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RotationProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RotationProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RotationProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.RotationProperty";
  }
  protected:
  explicit RotationProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRollFieldNumber = 1,
    kPitchFieldNumber = 2,
    kYawFieldNumber = 3,
  };
  // .catalog_studio_message.ExpressionValuePair roll = 1;
  bool has_roll() const;
  private:
  bool _internal_has_roll() const;
  public:
  void clear_roll();
  const ::catalog_studio_message::ExpressionValuePair& roll() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_roll();
  ::catalog_studio_message::ExpressionValuePair* mutable_roll();
  void set_allocated_roll(::catalog_studio_message::ExpressionValuePair* roll);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_roll() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_roll();
  public:
  void unsafe_arena_set_allocated_roll(
      ::catalog_studio_message::ExpressionValuePair* roll);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_roll();

  // .catalog_studio_message.ExpressionValuePair pitch = 2;
  bool has_pitch() const;
  private:
  bool _internal_has_pitch() const;
  public:
  void clear_pitch();
  const ::catalog_studio_message::ExpressionValuePair& pitch() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_pitch();
  ::catalog_studio_message::ExpressionValuePair* mutable_pitch();
  void set_allocated_pitch(::catalog_studio_message::ExpressionValuePair* pitch);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_pitch() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_pitch();
  public:
  void unsafe_arena_set_allocated_pitch(
      ::catalog_studio_message::ExpressionValuePair* pitch);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_pitch();

  // .catalog_studio_message.ExpressionValuePair yaw = 3;
  bool has_yaw() const;
  private:
  bool _internal_has_yaw() const;
  public:
  void clear_yaw();
  const ::catalog_studio_message::ExpressionValuePair& yaw() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_yaw();
  ::catalog_studio_message::ExpressionValuePair* mutable_yaw();
  void set_allocated_yaw(::catalog_studio_message::ExpressionValuePair* yaw);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_yaw() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_yaw();
  public:
  void unsafe_arena_set_allocated_yaw(
      ::catalog_studio_message::ExpressionValuePair* yaw);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_yaw();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.RotationProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* roll_;
  ::catalog_studio_message::ExpressionValuePair* pitch_;
  ::catalog_studio_message::ExpressionValuePair* yaw_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_RotationProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RotationProperty

// .catalog_studio_message.ExpressionValuePair roll = 1;
inline bool RotationProperty::_internal_has_roll() const {
  return this != internal_default_instance() && roll_ != nullptr;
}
inline bool RotationProperty::has_roll() const {
  return _internal_has_roll();
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::_internal_roll() const {
  const ::catalog_studio_message::ExpressionValuePair* p = roll_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::roll() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RotationProperty.roll)
  return _internal_roll();
}
inline void RotationProperty::unsafe_arena_set_allocated_roll(
    ::catalog_studio_message::ExpressionValuePair* roll) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(roll_);
  }
  roll_ = roll;
  if (roll) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.RotationProperty.roll)
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::release_roll() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = roll_;
  roll_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::unsafe_arena_release_roll() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RotationProperty.roll)
  
  ::catalog_studio_message::ExpressionValuePair* temp = roll_;
  roll_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::_internal_mutable_roll() {
  
  if (roll_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    roll_ = p;
  }
  return roll_;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::mutable_roll() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_roll();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RotationProperty.roll)
  return _msg;
}
inline void RotationProperty::set_allocated_roll(::catalog_studio_message::ExpressionValuePair* roll) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(roll_);
  }
  if (roll) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(roll));
    if (message_arena != submessage_arena) {
      roll = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, roll, submessage_arena);
    }
    
  } else {
    
  }
  roll_ = roll;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RotationProperty.roll)
}

// .catalog_studio_message.ExpressionValuePair pitch = 2;
inline bool RotationProperty::_internal_has_pitch() const {
  return this != internal_default_instance() && pitch_ != nullptr;
}
inline bool RotationProperty::has_pitch() const {
  return _internal_has_pitch();
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::_internal_pitch() const {
  const ::catalog_studio_message::ExpressionValuePair* p = pitch_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::pitch() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RotationProperty.pitch)
  return _internal_pitch();
}
inline void RotationProperty::unsafe_arena_set_allocated_pitch(
    ::catalog_studio_message::ExpressionValuePair* pitch) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pitch_);
  }
  pitch_ = pitch;
  if (pitch) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.RotationProperty.pitch)
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::release_pitch() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = pitch_;
  pitch_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::unsafe_arena_release_pitch() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RotationProperty.pitch)
  
  ::catalog_studio_message::ExpressionValuePair* temp = pitch_;
  pitch_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::_internal_mutable_pitch() {
  
  if (pitch_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    pitch_ = p;
  }
  return pitch_;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::mutable_pitch() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_pitch();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RotationProperty.pitch)
  return _msg;
}
inline void RotationProperty::set_allocated_pitch(::catalog_studio_message::ExpressionValuePair* pitch) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(pitch_);
  }
  if (pitch) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pitch));
    if (message_arena != submessage_arena) {
      pitch = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pitch, submessage_arena);
    }
    
  } else {
    
  }
  pitch_ = pitch;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RotationProperty.pitch)
}

// .catalog_studio_message.ExpressionValuePair yaw = 3;
inline bool RotationProperty::_internal_has_yaw() const {
  return this != internal_default_instance() && yaw_ != nullptr;
}
inline bool RotationProperty::has_yaw() const {
  return _internal_has_yaw();
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::_internal_yaw() const {
  const ::catalog_studio_message::ExpressionValuePair* p = yaw_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& RotationProperty::yaw() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.RotationProperty.yaw)
  return _internal_yaw();
}
inline void RotationProperty::unsafe_arena_set_allocated_yaw(
    ::catalog_studio_message::ExpressionValuePair* yaw) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(yaw_);
  }
  yaw_ = yaw;
  if (yaw) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.RotationProperty.yaw)
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::release_yaw() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = yaw_;
  yaw_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::unsafe_arena_release_yaw() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.RotationProperty.yaw)
  
  ::catalog_studio_message::ExpressionValuePair* temp = yaw_;
  yaw_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::_internal_mutable_yaw() {
  
  if (yaw_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    yaw_ = p;
  }
  return yaw_;
}
inline ::catalog_studio_message::ExpressionValuePair* RotationProperty::mutable_yaw() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_yaw();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.RotationProperty.yaw)
  return _msg;
}
inline void RotationProperty::set_allocated_yaw(::catalog_studio_message::ExpressionValuePair* yaw) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(yaw_);
  }
  if (yaw) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(yaw));
    if (message_arena != submessage_arena) {
      yaw = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, yaw, submessage_arena);
    }
    
  } else {
    
  }
  yaw_ = yaw;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.RotationProperty.yaw)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_RotationProperty_2eproto
