// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ScaleProperty.proto

#include "ScaleProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr ScaleProperty::ScaleProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(nullptr)
  , y_(nullptr)
  , z_(nullptr){}
struct ScalePropertyDefaultTypeInternal {
  constexpr ScalePropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ScalePropertyDefaultTypeInternal() {}
  union {
    ScaleProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ScalePropertyDefaultTypeInternal _ScaleProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ScaleProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_ScaleProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ScaleProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_ScaleProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ScaleProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ScaleProperty, x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ScaleProperty, y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::ScaleProperty, z_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::ScaleProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_ScaleProperty_default_instance_),
};

const char descriptor_table_protodef_ScaleProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023ScaleProperty.proto\022\026catalog_studio_me"
  "ssage\032\031ExpressionValuePair.proto\"\267\001\n\rSca"
  "leProperty\0226\n\001x\030\001 \001(\0132+.catalog_studio_m"
  "essage.ExpressionValuePair\0226\n\001y\030\002 \001(\0132+."
  "catalog_studio_message.ExpressionValuePa"
  "ir\0226\n\001z\030\003 \001(\0132+.catalog_studio_message.E"
  "xpressionValuePairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_ScaleProperty_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ScaleProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ScaleProperty_2eproto = {
  false, false, 266, descriptor_table_protodef_ScaleProperty_2eproto, "ScaleProperty.proto", 
  &descriptor_table_ScaleProperty_2eproto_once, descriptor_table_ScaleProperty_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_ScaleProperty_2eproto::offsets,
  file_level_metadata_ScaleProperty_2eproto, file_level_enum_descriptors_ScaleProperty_2eproto, file_level_service_descriptors_ScaleProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ScaleProperty_2eproto_getter() {
  return &descriptor_table_ScaleProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ScaleProperty_2eproto(&descriptor_table_ScaleProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class ScaleProperty::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& x(const ScaleProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& y(const ScaleProperty* msg);
  static const ::catalog_studio_message::ExpressionValuePair& z(const ScaleProperty* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
ScaleProperty::_Internal::x(const ScaleProperty* msg) {
  return *msg->x_;
}
const ::catalog_studio_message::ExpressionValuePair&
ScaleProperty::_Internal::y(const ScaleProperty* msg) {
  return *msg->y_;
}
const ::catalog_studio_message::ExpressionValuePair&
ScaleProperty::_Internal::z(const ScaleProperty* msg) {
  return *msg->z_;
}
void ScaleProperty::clear_x() {
  if (GetArenaForAllocation() == nullptr && x_ != nullptr) {
    delete x_;
  }
  x_ = nullptr;
}
void ScaleProperty::clear_y() {
  if (GetArenaForAllocation() == nullptr && y_ != nullptr) {
    delete y_;
  }
  y_ = nullptr;
}
void ScaleProperty::clear_z() {
  if (GetArenaForAllocation() == nullptr && z_ != nullptr) {
    delete z_;
  }
  z_ = nullptr;
}
ScaleProperty::ScaleProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.ScaleProperty)
}
ScaleProperty::ScaleProperty(const ScaleProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_x()) {
    x_ = new ::catalog_studio_message::ExpressionValuePair(*from.x_);
  } else {
    x_ = nullptr;
  }
  if (from._internal_has_y()) {
    y_ = new ::catalog_studio_message::ExpressionValuePair(*from.y_);
  } else {
    y_ = nullptr;
  }
  if (from._internal_has_z()) {
    z_ = new ::catalog_studio_message::ExpressionValuePair(*from.z_);
  } else {
    z_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.ScaleProperty)
}

void ScaleProperty::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

ScaleProperty::~ScaleProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.ScaleProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ScaleProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete x_;
  if (this != internal_default_instance()) delete y_;
  if (this != internal_default_instance()) delete z_;
}

void ScaleProperty::ArenaDtor(void* object) {
  ScaleProperty* _this = reinterpret_cast< ScaleProperty* >(object);
  (void)_this;
}
void ScaleProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ScaleProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ScaleProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.ScaleProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && x_ != nullptr) {
    delete x_;
  }
  x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && y_ != nullptr) {
    delete y_;
  }
  y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && z_ != nullptr) {
    delete z_;
  }
  z_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ScaleProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .catalog_studio_message.ExpressionValuePair x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ScaleProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.ScaleProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair x = 1;
  if (this->_internal_has_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair y = 2;
  if (this->_internal_has_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair z = 3;
  if (this->_internal_has_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::z(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.ScaleProperty)
  return target;
}

size_t ScaleProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.ScaleProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair x = 1;
  if (this->_internal_has_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *x_);
  }

  // .catalog_studio_message.ExpressionValuePair y = 2;
  if (this->_internal_has_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *y_);
  }

  // .catalog_studio_message.ExpressionValuePair z = 3;
  if (this->_internal_has_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *z_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ScaleProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ScaleProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ScaleProperty::GetClassData() const { return &_class_data_; }

void ScaleProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ScaleProperty *>(to)->MergeFrom(
      static_cast<const ScaleProperty &>(from));
}


void ScaleProperty::MergeFrom(const ScaleProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.ScaleProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_x()) {
    _internal_mutable_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_x());
  }
  if (from._internal_has_y()) {
    _internal_mutable_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_y());
  }
  if (from._internal_has_z()) {
    _internal_mutable_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_z());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ScaleProperty::CopyFrom(const ScaleProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.ScaleProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScaleProperty::IsInitialized() const {
  return true;
}

void ScaleProperty::InternalSwap(ScaleProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ScaleProperty, z_)
      + sizeof(ScaleProperty::z_)
      - PROTOBUF_FIELD_OFFSET(ScaleProperty, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ScaleProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ScaleProperty_2eproto_getter, &descriptor_table_ScaleProperty_2eproto_once,
      file_level_metadata_ScaleProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::ScaleProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::ScaleProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::ScaleProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
