// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ScaleProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ScaleProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ScaleProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ScaleProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ScaleProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ScaleProperty_2eproto;
namespace catalog_studio_message {
class ScaleProperty;
struct ScalePropertyDefaultTypeInternal;
extern ScalePropertyDefaultTypeInternal _ScaleProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::ScaleProperty* Arena::CreateMaybeMessage<::catalog_studio_message::ScaleProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class ScaleProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.ScaleProperty) */ {
 public:
  inline ScaleProperty() : ScaleProperty(nullptr) {}
  ~ScaleProperty() override;
  explicit constexpr ScaleProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ScaleProperty(const ScaleProperty& from);
  ScaleProperty(ScaleProperty&& from) noexcept
    : ScaleProperty() {
    *this = ::std::move(from);
  }

  inline ScaleProperty& operator=(const ScaleProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline ScaleProperty& operator=(ScaleProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ScaleProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const ScaleProperty* internal_default_instance() {
    return reinterpret_cast<const ScaleProperty*>(
               &_ScaleProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ScaleProperty& a, ScaleProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(ScaleProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ScaleProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ScaleProperty* New() const final {
    return new ScaleProperty();
  }

  ScaleProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ScaleProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ScaleProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ScaleProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScaleProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.ScaleProperty";
  }
  protected:
  explicit ScaleProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // .catalog_studio_message.ExpressionValuePair x = 1;
  bool has_x() const;
  private:
  bool _internal_has_x() const;
  public:
  void clear_x();
  const ::catalog_studio_message::ExpressionValuePair& x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_x();
  void set_allocated_x(::catalog_studio_message::ExpressionValuePair* x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_x();
  public:
  void unsafe_arena_set_allocated_x(
      ::catalog_studio_message::ExpressionValuePair* x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_x();

  // .catalog_studio_message.ExpressionValuePair y = 2;
  bool has_y() const;
  private:
  bool _internal_has_y() const;
  public:
  void clear_y();
  const ::catalog_studio_message::ExpressionValuePair& y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_y();
  void set_allocated_y(::catalog_studio_message::ExpressionValuePair* y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_y();
  public:
  void unsafe_arena_set_allocated_y(
      ::catalog_studio_message::ExpressionValuePair* y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_y();

  // .catalog_studio_message.ExpressionValuePair z = 3;
  bool has_z() const;
  private:
  bool _internal_has_z() const;
  public:
  void clear_z();
  const ::catalog_studio_message::ExpressionValuePair& z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_z();
  void set_allocated_z(::catalog_studio_message::ExpressionValuePair* z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_z();
  public:
  void unsafe_arena_set_allocated_z(
      ::catalog_studio_message::ExpressionValuePair* z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_z();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.ScaleProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* x_;
  ::catalog_studio_message::ExpressionValuePair* y_;
  ::catalog_studio_message::ExpressionValuePair* z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ScaleProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ScaleProperty

// .catalog_studio_message.ExpressionValuePair x = 1;
inline bool ScaleProperty::_internal_has_x() const {
  return this != internal_default_instance() && x_ != nullptr;
}
inline bool ScaleProperty::has_x() const {
  return _internal_has_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::_internal_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ScaleProperty.x)
  return _internal_x();
}
inline void ScaleProperty::unsafe_arena_set_allocated_x(
    ::catalog_studio_message::ExpressionValuePair* x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(x_);
  }
  x_ = x;
  if (x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.ScaleProperty.x)
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::release_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = x_;
  x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::unsafe_arena_release_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ScaleProperty.x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = x_;
  x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::_internal_mutable_x() {
  
  if (x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    x_ = p;
  }
  return x_;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::mutable_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ScaleProperty.x)
  return _msg;
}
inline void ScaleProperty::set_allocated_x(::catalog_studio_message::ExpressionValuePair* x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(x_);
  }
  if (x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(x));
    if (message_arena != submessage_arena) {
      x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, x, submessage_arena);
    }
    
  } else {
    
  }
  x_ = x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ScaleProperty.x)
}

// .catalog_studio_message.ExpressionValuePair y = 2;
inline bool ScaleProperty::_internal_has_y() const {
  return this != internal_default_instance() && y_ != nullptr;
}
inline bool ScaleProperty::has_y() const {
  return _internal_has_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::_internal_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ScaleProperty.y)
  return _internal_y();
}
inline void ScaleProperty::unsafe_arena_set_allocated_y(
    ::catalog_studio_message::ExpressionValuePair* y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(y_);
  }
  y_ = y;
  if (y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.ScaleProperty.y)
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::release_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = y_;
  y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::unsafe_arena_release_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ScaleProperty.y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = y_;
  y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::_internal_mutable_y() {
  
  if (y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    y_ = p;
  }
  return y_;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::mutable_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ScaleProperty.y)
  return _msg;
}
inline void ScaleProperty::set_allocated_y(::catalog_studio_message::ExpressionValuePair* y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(y_);
  }
  if (y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(y));
    if (message_arena != submessage_arena) {
      y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, y, submessage_arena);
    }
    
  } else {
    
  }
  y_ = y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ScaleProperty.y)
}

// .catalog_studio_message.ExpressionValuePair z = 3;
inline bool ScaleProperty::_internal_has_z() const {
  return this != internal_default_instance() && z_ != nullptr;
}
inline bool ScaleProperty::has_z() const {
  return _internal_has_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::_internal_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& ScaleProperty::z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.ScaleProperty.z)
  return _internal_z();
}
inline void ScaleProperty::unsafe_arena_set_allocated_z(
    ::catalog_studio_message::ExpressionValuePair* z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(z_);
  }
  z_ = z;
  if (z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.ScaleProperty.z)
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::release_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = z_;
  z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::unsafe_arena_release_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.ScaleProperty.z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = z_;
  z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::_internal_mutable_z() {
  
  if (z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    z_ = p;
  }
  return z_;
}
inline ::catalog_studio_message::ExpressionValuePair* ScaleProperty::mutable_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.ScaleProperty.z)
  return _msg;
}
inline void ScaleProperty::set_allocated_z(::catalog_studio_message::ExpressionValuePair* z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(z_);
  }
  if (z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(z));
    if (message_arena != submessage_arena) {
      z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, z, submessage_arena);
    }
    
  } else {
    
  }
  z_ = z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.ScaleProperty.z)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ScaleProperty_2eproto
