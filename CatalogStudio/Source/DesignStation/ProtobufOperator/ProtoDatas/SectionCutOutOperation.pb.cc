// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionCutOutOperation.proto

#include "SectionCutOutOperation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionCutOutOperation::SectionCutOutOperation(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : points_()
  , lines_()
  , cut_out_value_(nullptr)
  , rectangle_(nullptr)
  , ellipse_(nullptr)
  , id_(0)
  , section_type_(0)

  , plan_belongs_(0)
{}
struct SectionCutOutOperationDefaultTypeInternal {
  constexpr SectionCutOutOperationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionCutOutOperationDefaultTypeInternal() {}
  union {
    SectionCutOutOperation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionCutOutOperationDefaultTypeInternal _SectionCutOutOperation_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionCutOutOperation_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionCutOutOperation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionCutOutOperation_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionCutOutOperation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, cut_out_value_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, section_type_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, plan_belongs_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, points_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, lines_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, rectangle_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionCutOutOperation, ellipse_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionCutOutOperation)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionCutOutOperation_default_instance_),
};

const char descriptor_table_protodef_SectionCutOutOperation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034SectionCutOutOperation.proto\022\026catalog_"
  "studio_message\032\027ComponentEnumData.proto\032"
  "\031ExpressionValuePair.proto\032\032GeomtryPoint"
  "Property.proto\032\031GeomtryLineProperty.prot"
  "o\032\"GeomtryRectanglePlanProperty.proto\032 G"
  "eomtryEllipsePlanProperty.proto\"\355\003\n\026Sect"
  "ionCutOutOperation\022\n\n\002id\030\001 \001(\005\022B\n\rcut_ou"
  "t_value\030\002 \001(\0132+.catalog_studio_message.E"
  "xpressionValuePair\0229\n\014section_type\030\003 \001(\016"
  "2#.catalog_studio_message.SectionType\022@\n"
  "\014plan_belongs\030\004 \001(\0162*.catalog_studio_mes"
  "sage.PlanPolygonBelongs\022<\n\006points\030\005 \003(\0132"
  ",.catalog_studio_message.GeomtryPointPro"
  "perty\022:\n\005lines\030\006 \003(\0132+.catalog_studio_me"
  "ssage.GeomtryLineProperty\022G\n\trectangle\030\007"
  " \001(\01324.catalog_studio_message.GeomtryRec"
  "tanglePlanProperty\022C\n\007ellipse\030\010 \001(\01322.ca"
  "talog_studio_message.GeomtryEllipsePlanP"
  "ropertyb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionCutOutOperation_2eproto_deps[6] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
  &::descriptor_table_GeomtryEllipsePlanProperty_2eproto,
  &::descriptor_table_GeomtryLineProperty_2eproto,
  &::descriptor_table_GeomtryPointProperty_2eproto,
  &::descriptor_table_GeomtryRectanglePlanProperty_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionCutOutOperation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionCutOutOperation_2eproto = {
  false, false, 735, descriptor_table_protodef_SectionCutOutOperation_2eproto, "SectionCutOutOperation.proto", 
  &descriptor_table_SectionCutOutOperation_2eproto_once, descriptor_table_SectionCutOutOperation_2eproto_deps, 6, 1,
  schemas, file_default_instances, TableStruct_SectionCutOutOperation_2eproto::offsets,
  file_level_metadata_SectionCutOutOperation_2eproto, file_level_enum_descriptors_SectionCutOutOperation_2eproto, file_level_service_descriptors_SectionCutOutOperation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionCutOutOperation_2eproto_getter() {
  return &descriptor_table_SectionCutOutOperation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionCutOutOperation_2eproto(&descriptor_table_SectionCutOutOperation_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionCutOutOperation::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& cut_out_value(const SectionCutOutOperation* msg);
  static const ::catalog_studio_message::GeomtryRectanglePlanProperty& rectangle(const SectionCutOutOperation* msg);
  static const ::catalog_studio_message::GeomtryEllipsePlanProperty& ellipse(const SectionCutOutOperation* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
SectionCutOutOperation::_Internal::cut_out_value(const SectionCutOutOperation* msg) {
  return *msg->cut_out_value_;
}
const ::catalog_studio_message::GeomtryRectanglePlanProperty&
SectionCutOutOperation::_Internal::rectangle(const SectionCutOutOperation* msg) {
  return *msg->rectangle_;
}
const ::catalog_studio_message::GeomtryEllipsePlanProperty&
SectionCutOutOperation::_Internal::ellipse(const SectionCutOutOperation* msg) {
  return *msg->ellipse_;
}
void SectionCutOutOperation::clear_cut_out_value() {
  if (GetArenaForAllocation() == nullptr && cut_out_value_ != nullptr) {
    delete cut_out_value_;
  }
  cut_out_value_ = nullptr;
}
void SectionCutOutOperation::clear_points() {
  points_.Clear();
}
void SectionCutOutOperation::clear_lines() {
  lines_.Clear();
}
void SectionCutOutOperation::clear_rectangle() {
  if (GetArenaForAllocation() == nullptr && rectangle_ != nullptr) {
    delete rectangle_;
  }
  rectangle_ = nullptr;
}
void SectionCutOutOperation::clear_ellipse() {
  if (GetArenaForAllocation() == nullptr && ellipse_ != nullptr) {
    delete ellipse_;
  }
  ellipse_ = nullptr;
}
SectionCutOutOperation::SectionCutOutOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  points_(arena),
  lines_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionCutOutOperation)
}
SectionCutOutOperation::SectionCutOutOperation(const SectionCutOutOperation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_),
      lines_(from.lines_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_cut_out_value()) {
    cut_out_value_ = new ::catalog_studio_message::ExpressionValuePair(*from.cut_out_value_);
  } else {
    cut_out_value_ = nullptr;
  }
  if (from._internal_has_rectangle()) {
    rectangle_ = new ::catalog_studio_message::GeomtryRectanglePlanProperty(*from.rectangle_);
  } else {
    rectangle_ = nullptr;
  }
  if (from._internal_has_ellipse()) {
    ellipse_ = new ::catalog_studio_message::GeomtryEllipsePlanProperty(*from.ellipse_);
  } else {
    ellipse_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionCutOutOperation)
}

void SectionCutOutOperation::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&cut_out_value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&cut_out_value_)) + sizeof(plan_belongs_));
}

SectionCutOutOperation::~SectionCutOutOperation() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionCutOutOperation)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionCutOutOperation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete cut_out_value_;
  if (this != internal_default_instance()) delete rectangle_;
  if (this != internal_default_instance()) delete ellipse_;
}

void SectionCutOutOperation::ArenaDtor(void* object) {
  SectionCutOutOperation* _this = reinterpret_cast< SectionCutOutOperation* >(object);
  (void)_this;
}
void SectionCutOutOperation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionCutOutOperation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionCutOutOperation::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionCutOutOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  lines_.Clear();
  if (GetArenaForAllocation() == nullptr && cut_out_value_ != nullptr) {
    delete cut_out_value_;
  }
  cut_out_value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && rectangle_ != nullptr) {
    delete rectangle_;
  }
  rectangle_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ellipse_ != nullptr) {
    delete ellipse_;
  }
  ellipse_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&plan_belongs_) -
      reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionCutOutOperation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair cut_out_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_cut_out_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SectionType section_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_section_type(static_cast<::catalog_studio_message::SectionType>(val));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_plan_belongs(static_cast<::catalog_studio_message::PlanPolygonBelongs>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.GeomtryPointProperty points = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.GeomtryLineProperty lines = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lines(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.GeomtryRectanglePlanProperty rectangle = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_rectangle(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.GeomtryEllipsePlanProperty ellipse = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_ellipse(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionCutOutOperation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionCutOutOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.ExpressionValuePair cut_out_value = 2;
  if (this->_internal_has_cut_out_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::cut_out_value(this), target, stream);
  }

  // .catalog_studio_message.SectionType section_type = 3;
  if (this->_internal_section_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_section_type(), target);
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 4;
  if (this->_internal_plan_belongs() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_plan_belongs(), target);
  }

  // repeated .catalog_studio_message.GeomtryPointProperty points = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_points(i), target, stream);
  }

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lines_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_lines(i), target, stream);
  }

  // .catalog_studio_message.GeomtryRectanglePlanProperty rectangle = 7;
  if (this->_internal_has_rectangle()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::rectangle(this), target, stream);
  }

  // .catalog_studio_message.GeomtryEllipsePlanProperty ellipse = 8;
  if (this->_internal_has_ellipse()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::ellipse(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionCutOutOperation)
  return target;
}

size_t SectionCutOutOperation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionCutOutOperation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.GeomtryPointProperty points = 5;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 6;
  total_size += 1UL * this->_internal_lines_size();
  for (const auto& msg : this->lines_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .catalog_studio_message.ExpressionValuePair cut_out_value = 2;
  if (this->_internal_has_cut_out_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *cut_out_value_);
  }

  // .catalog_studio_message.GeomtryRectanglePlanProperty rectangle = 7;
  if (this->_internal_has_rectangle()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rectangle_);
  }

  // .catalog_studio_message.GeomtryEllipsePlanProperty ellipse = 8;
  if (this->_internal_has_ellipse()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ellipse_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // .catalog_studio_message.SectionType section_type = 3;
  if (this->_internal_section_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_section_type());
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 4;
  if (this->_internal_plan_belongs() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_plan_belongs());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionCutOutOperation::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionCutOutOperation::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionCutOutOperation::GetClassData() const { return &_class_data_; }

void SectionCutOutOperation::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionCutOutOperation *>(to)->MergeFrom(
      static_cast<const SectionCutOutOperation &>(from));
}


void SectionCutOutOperation::MergeFrom(const SectionCutOutOperation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionCutOutOperation)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
  lines_.MergeFrom(from.lines_);
  if (from._internal_has_cut_out_value()) {
    _internal_mutable_cut_out_value()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_cut_out_value());
  }
  if (from._internal_has_rectangle()) {
    _internal_mutable_rectangle()->::catalog_studio_message::GeomtryRectanglePlanProperty::MergeFrom(from._internal_rectangle());
  }
  if (from._internal_has_ellipse()) {
    _internal_mutable_ellipse()->::catalog_studio_message::GeomtryEllipsePlanProperty::MergeFrom(from._internal_ellipse());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_section_type() != 0) {
    _internal_set_section_type(from._internal_section_type());
  }
  if (from._internal_plan_belongs() != 0) {
    _internal_set_plan_belongs(from._internal_plan_belongs());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionCutOutOperation::CopyFrom(const SectionCutOutOperation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionCutOutOperation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionCutOutOperation::IsInitialized() const {
  return true;
}

void SectionCutOutOperation::InternalSwap(SectionCutOutOperation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
  lines_.InternalSwap(&other->lines_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SectionCutOutOperation, plan_belongs_)
      + sizeof(SectionCutOutOperation::plan_belongs_)
      - PROTOBUF_FIELD_OFFSET(SectionCutOutOperation, cut_out_value_)>(
          reinterpret_cast<char*>(&cut_out_value_),
          reinterpret_cast<char*>(&other->cut_out_value_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionCutOutOperation::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionCutOutOperation_2eproto_getter, &descriptor_table_SectionCutOutOperation_2eproto_once,
      file_level_metadata_SectionCutOutOperation_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionCutOutOperation* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionCutOutOperation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionCutOutOperation >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
