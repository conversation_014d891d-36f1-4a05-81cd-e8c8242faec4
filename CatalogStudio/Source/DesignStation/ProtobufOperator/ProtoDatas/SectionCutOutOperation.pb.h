// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionCutOutOperation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionCutOutOperation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionCutOutOperation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "ExpressionValuePair.pb.h"
#include "GeomtryPointProperty.pb.h"
#include "GeomtryLineProperty.pb.h"
#include "GeomtryRectanglePlanProperty.pb.h"
#include "GeomtryEllipsePlanProperty.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionCutOutOperation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionCutOutOperation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionCutOutOperation_2eproto;
namespace catalog_studio_message {
class SectionCutOutOperation;
struct SectionCutOutOperationDefaultTypeInternal;
extern SectionCutOutOperationDefaultTypeInternal _SectionCutOutOperation_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionCutOutOperation* Arena::CreateMaybeMessage<::catalog_studio_message::SectionCutOutOperation>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionCutOutOperation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionCutOutOperation) */ {
 public:
  inline SectionCutOutOperation() : SectionCutOutOperation(nullptr) {}
  ~SectionCutOutOperation() override;
  explicit constexpr SectionCutOutOperation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionCutOutOperation(const SectionCutOutOperation& from);
  SectionCutOutOperation(SectionCutOutOperation&& from) noexcept
    : SectionCutOutOperation() {
    *this = ::std::move(from);
  }

  inline SectionCutOutOperation& operator=(const SectionCutOutOperation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionCutOutOperation& operator=(SectionCutOutOperation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionCutOutOperation& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionCutOutOperation* internal_default_instance() {
    return reinterpret_cast<const SectionCutOutOperation*>(
               &_SectionCutOutOperation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionCutOutOperation& a, SectionCutOutOperation& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionCutOutOperation* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionCutOutOperation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionCutOutOperation* New() const final {
    return new SectionCutOutOperation();
  }

  SectionCutOutOperation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionCutOutOperation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionCutOutOperation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionCutOutOperation& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionCutOutOperation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionCutOutOperation";
  }
  protected:
  explicit SectionCutOutOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 5,
    kLinesFieldNumber = 6,
    kCutOutValueFieldNumber = 2,
    kRectangleFieldNumber = 7,
    kEllipseFieldNumber = 8,
    kIdFieldNumber = 1,
    kSectionTypeFieldNumber = 3,
    kPlanBelongsFieldNumber = 4,
  };
  // repeated .catalog_studio_message.GeomtryPointProperty points = 5;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::catalog_studio_message::GeomtryPointProperty* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >*
      mutable_points();
  private:
  const ::catalog_studio_message::GeomtryPointProperty& _internal_points(int index) const;
  ::catalog_studio_message::GeomtryPointProperty* _internal_add_points();
  public:
  const ::catalog_studio_message::GeomtryPointProperty& points(int index) const;
  ::catalog_studio_message::GeomtryPointProperty* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >&
      points() const;

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 6;
  int lines_size() const;
  private:
  int _internal_lines_size() const;
  public:
  void clear_lines();
  ::catalog_studio_message::GeomtryLineProperty* mutable_lines(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >*
      mutable_lines();
  private:
  const ::catalog_studio_message::GeomtryLineProperty& _internal_lines(int index) const;
  ::catalog_studio_message::GeomtryLineProperty* _internal_add_lines();
  public:
  const ::catalog_studio_message::GeomtryLineProperty& lines(int index) const;
  ::catalog_studio_message::GeomtryLineProperty* add_lines();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >&
      lines() const;

  // .catalog_studio_message.ExpressionValuePair cut_out_value = 2;
  bool has_cut_out_value() const;
  private:
  bool _internal_has_cut_out_value() const;
  public:
  void clear_cut_out_value();
  const ::catalog_studio_message::ExpressionValuePair& cut_out_value() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_cut_out_value();
  ::catalog_studio_message::ExpressionValuePair* mutable_cut_out_value();
  void set_allocated_cut_out_value(::catalog_studio_message::ExpressionValuePair* cut_out_value);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_cut_out_value() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_cut_out_value();
  public:
  void unsafe_arena_set_allocated_cut_out_value(
      ::catalog_studio_message::ExpressionValuePair* cut_out_value);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_cut_out_value();

  // .catalog_studio_message.GeomtryRectanglePlanProperty rectangle = 7;
  bool has_rectangle() const;
  private:
  bool _internal_has_rectangle() const;
  public:
  void clear_rectangle();
  const ::catalog_studio_message::GeomtryRectanglePlanProperty& rectangle() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::GeomtryRectanglePlanProperty* release_rectangle();
  ::catalog_studio_message::GeomtryRectanglePlanProperty* mutable_rectangle();
  void set_allocated_rectangle(::catalog_studio_message::GeomtryRectanglePlanProperty* rectangle);
  private:
  const ::catalog_studio_message::GeomtryRectanglePlanProperty& _internal_rectangle() const;
  ::catalog_studio_message::GeomtryRectanglePlanProperty* _internal_mutable_rectangle();
  public:
  void unsafe_arena_set_allocated_rectangle(
      ::catalog_studio_message::GeomtryRectanglePlanProperty* rectangle);
  ::catalog_studio_message::GeomtryRectanglePlanProperty* unsafe_arena_release_rectangle();

  // .catalog_studio_message.GeomtryEllipsePlanProperty ellipse = 8;
  bool has_ellipse() const;
  private:
  bool _internal_has_ellipse() const;
  public:
  void clear_ellipse();
  const ::catalog_studio_message::GeomtryEllipsePlanProperty& ellipse() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::GeomtryEllipsePlanProperty* release_ellipse();
  ::catalog_studio_message::GeomtryEllipsePlanProperty* mutable_ellipse();
  void set_allocated_ellipse(::catalog_studio_message::GeomtryEllipsePlanProperty* ellipse);
  private:
  const ::catalog_studio_message::GeomtryEllipsePlanProperty& _internal_ellipse() const;
  ::catalog_studio_message::GeomtryEllipsePlanProperty* _internal_mutable_ellipse();
  public:
  void unsafe_arena_set_allocated_ellipse(
      ::catalog_studio_message::GeomtryEllipsePlanProperty* ellipse);
  ::catalog_studio_message::GeomtryEllipsePlanProperty* unsafe_arena_release_ellipse();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .catalog_studio_message.SectionType section_type = 3;
  void clear_section_type();
  ::catalog_studio_message::SectionType section_type() const;
  void set_section_type(::catalog_studio_message::SectionType value);
  private:
  ::catalog_studio_message::SectionType _internal_section_type() const;
  void _internal_set_section_type(::catalog_studio_message::SectionType value);
  public:

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 4;
  void clear_plan_belongs();
  ::catalog_studio_message::PlanPolygonBelongs plan_belongs() const;
  void set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  private:
  ::catalog_studio_message::PlanPolygonBelongs _internal_plan_belongs() const;
  void _internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionCutOutOperation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty > points_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty > lines_;
  ::catalog_studio_message::ExpressionValuePair* cut_out_value_;
  ::catalog_studio_message::GeomtryRectanglePlanProperty* rectangle_;
  ::catalog_studio_message::GeomtryEllipsePlanProperty* ellipse_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  int section_type_;
  int plan_belongs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionCutOutOperation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionCutOutOperation

// int32 id = 1;
inline void SectionCutOutOperation::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionCutOutOperation::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionCutOutOperation::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.id)
  return _internal_id();
}
inline void SectionCutOutOperation::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void SectionCutOutOperation::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionCutOutOperation.id)
}

// .catalog_studio_message.ExpressionValuePair cut_out_value = 2;
inline bool SectionCutOutOperation::_internal_has_cut_out_value() const {
  return this != internal_default_instance() && cut_out_value_ != nullptr;
}
inline bool SectionCutOutOperation::has_cut_out_value() const {
  return _internal_has_cut_out_value();
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionCutOutOperation::_internal_cut_out_value() const {
  const ::catalog_studio_message::ExpressionValuePair* p = cut_out_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionCutOutOperation::cut_out_value() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.cut_out_value)
  return _internal_cut_out_value();
}
inline void SectionCutOutOperation::unsafe_arena_set_allocated_cut_out_value(
    ::catalog_studio_message::ExpressionValuePair* cut_out_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cut_out_value_);
  }
  cut_out_value_ = cut_out_value;
  if (cut_out_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionCutOutOperation.cut_out_value)
}
inline ::catalog_studio_message::ExpressionValuePair* SectionCutOutOperation::release_cut_out_value() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = cut_out_value_;
  cut_out_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionCutOutOperation::unsafe_arena_release_cut_out_value() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionCutOutOperation.cut_out_value)
  
  ::catalog_studio_message::ExpressionValuePair* temp = cut_out_value_;
  cut_out_value_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionCutOutOperation::_internal_mutable_cut_out_value() {
  
  if (cut_out_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    cut_out_value_ = p;
  }
  return cut_out_value_;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionCutOutOperation::mutable_cut_out_value() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_cut_out_value();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionCutOutOperation.cut_out_value)
  return _msg;
}
inline void SectionCutOutOperation::set_allocated_cut_out_value(::catalog_studio_message::ExpressionValuePair* cut_out_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(cut_out_value_);
  }
  if (cut_out_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cut_out_value));
    if (message_arena != submessage_arena) {
      cut_out_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cut_out_value, submessage_arena);
    }
    
  } else {
    
  }
  cut_out_value_ = cut_out_value;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionCutOutOperation.cut_out_value)
}

// .catalog_studio_message.SectionType section_type = 3;
inline void SectionCutOutOperation::clear_section_type() {
  section_type_ = 0;
}
inline ::catalog_studio_message::SectionType SectionCutOutOperation::_internal_section_type() const {
  return static_cast< ::catalog_studio_message::SectionType >(section_type_);
}
inline ::catalog_studio_message::SectionType SectionCutOutOperation::section_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.section_type)
  return _internal_section_type();
}
inline void SectionCutOutOperation::_internal_set_section_type(::catalog_studio_message::SectionType value) {
  
  section_type_ = value;
}
inline void SectionCutOutOperation::set_section_type(::catalog_studio_message::SectionType value) {
  _internal_set_section_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionCutOutOperation.section_type)
}

// .catalog_studio_message.PlanPolygonBelongs plan_belongs = 4;
inline void SectionCutOutOperation::clear_plan_belongs() {
  plan_belongs_ = 0;
}
inline ::catalog_studio_message::PlanPolygonBelongs SectionCutOutOperation::_internal_plan_belongs() const {
  return static_cast< ::catalog_studio_message::PlanPolygonBelongs >(plan_belongs_);
}
inline ::catalog_studio_message::PlanPolygonBelongs SectionCutOutOperation::plan_belongs() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.plan_belongs)
  return _internal_plan_belongs();
}
inline void SectionCutOutOperation::_internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  
  plan_belongs_ = value;
}
inline void SectionCutOutOperation::set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  _internal_set_plan_belongs(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionCutOutOperation.plan_belongs)
}

// repeated .catalog_studio_message.GeomtryPointProperty points = 5;
inline int SectionCutOutOperation::_internal_points_size() const {
  return points_.size();
}
inline int SectionCutOutOperation::points_size() const {
  return _internal_points_size();
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionCutOutOperation::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionCutOutOperation.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >*
SectionCutOutOperation::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionCutOutOperation.points)
  return &points_;
}
inline const ::catalog_studio_message::GeomtryPointProperty& SectionCutOutOperation::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::catalog_studio_message::GeomtryPointProperty& SectionCutOutOperation::points(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.points)
  return _internal_points(index);
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionCutOutOperation::_internal_add_points() {
  return points_.Add();
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionCutOutOperation::add_points() {
  ::catalog_studio_message::GeomtryPointProperty* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionCutOutOperation.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >&
SectionCutOutOperation::points() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionCutOutOperation.points)
  return points_;
}

// repeated .catalog_studio_message.GeomtryLineProperty lines = 6;
inline int SectionCutOutOperation::_internal_lines_size() const {
  return lines_.size();
}
inline int SectionCutOutOperation::lines_size() const {
  return _internal_lines_size();
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionCutOutOperation::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionCutOutOperation.lines)
  return lines_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >*
SectionCutOutOperation::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionCutOutOperation.lines)
  return &lines_;
}
inline const ::catalog_studio_message::GeomtryLineProperty& SectionCutOutOperation::_internal_lines(int index) const {
  return lines_.Get(index);
}
inline const ::catalog_studio_message::GeomtryLineProperty& SectionCutOutOperation::lines(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.lines)
  return _internal_lines(index);
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionCutOutOperation::_internal_add_lines() {
  return lines_.Add();
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionCutOutOperation::add_lines() {
  ::catalog_studio_message::GeomtryLineProperty* _add = _internal_add_lines();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionCutOutOperation.lines)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >&
SectionCutOutOperation::lines() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionCutOutOperation.lines)
  return lines_;
}

// .catalog_studio_message.GeomtryRectanglePlanProperty rectangle = 7;
inline bool SectionCutOutOperation::_internal_has_rectangle() const {
  return this != internal_default_instance() && rectangle_ != nullptr;
}
inline bool SectionCutOutOperation::has_rectangle() const {
  return _internal_has_rectangle();
}
inline const ::catalog_studio_message::GeomtryRectanglePlanProperty& SectionCutOutOperation::_internal_rectangle() const {
  const ::catalog_studio_message::GeomtryRectanglePlanProperty* p = rectangle_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::GeomtryRectanglePlanProperty&>(
      ::catalog_studio_message::_GeomtryRectanglePlanProperty_default_instance_);
}
inline const ::catalog_studio_message::GeomtryRectanglePlanProperty& SectionCutOutOperation::rectangle() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.rectangle)
  return _internal_rectangle();
}
inline void SectionCutOutOperation::unsafe_arena_set_allocated_rectangle(
    ::catalog_studio_message::GeomtryRectanglePlanProperty* rectangle) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rectangle_);
  }
  rectangle_ = rectangle;
  if (rectangle) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionCutOutOperation.rectangle)
}
inline ::catalog_studio_message::GeomtryRectanglePlanProperty* SectionCutOutOperation::release_rectangle() {
  
  ::catalog_studio_message::GeomtryRectanglePlanProperty* temp = rectangle_;
  rectangle_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::GeomtryRectanglePlanProperty* SectionCutOutOperation::unsafe_arena_release_rectangle() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionCutOutOperation.rectangle)
  
  ::catalog_studio_message::GeomtryRectanglePlanProperty* temp = rectangle_;
  rectangle_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::GeomtryRectanglePlanProperty* SectionCutOutOperation::_internal_mutable_rectangle() {
  
  if (rectangle_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::GeomtryRectanglePlanProperty>(GetArenaForAllocation());
    rectangle_ = p;
  }
  return rectangle_;
}
inline ::catalog_studio_message::GeomtryRectanglePlanProperty* SectionCutOutOperation::mutable_rectangle() {
  ::catalog_studio_message::GeomtryRectanglePlanProperty* _msg = _internal_mutable_rectangle();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionCutOutOperation.rectangle)
  return _msg;
}
inline void SectionCutOutOperation::set_allocated_rectangle(::catalog_studio_message::GeomtryRectanglePlanProperty* rectangle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(rectangle_);
  }
  if (rectangle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rectangle));
    if (message_arena != submessage_arena) {
      rectangle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rectangle, submessage_arena);
    }
    
  } else {
    
  }
  rectangle_ = rectangle;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionCutOutOperation.rectangle)
}

// .catalog_studio_message.GeomtryEllipsePlanProperty ellipse = 8;
inline bool SectionCutOutOperation::_internal_has_ellipse() const {
  return this != internal_default_instance() && ellipse_ != nullptr;
}
inline bool SectionCutOutOperation::has_ellipse() const {
  return _internal_has_ellipse();
}
inline const ::catalog_studio_message::GeomtryEllipsePlanProperty& SectionCutOutOperation::_internal_ellipse() const {
  const ::catalog_studio_message::GeomtryEllipsePlanProperty* p = ellipse_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::GeomtryEllipsePlanProperty&>(
      ::catalog_studio_message::_GeomtryEllipsePlanProperty_default_instance_);
}
inline const ::catalog_studio_message::GeomtryEllipsePlanProperty& SectionCutOutOperation::ellipse() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionCutOutOperation.ellipse)
  return _internal_ellipse();
}
inline void SectionCutOutOperation::unsafe_arena_set_allocated_ellipse(
    ::catalog_studio_message::GeomtryEllipsePlanProperty* ellipse) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ellipse_);
  }
  ellipse_ = ellipse;
  if (ellipse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionCutOutOperation.ellipse)
}
inline ::catalog_studio_message::GeomtryEllipsePlanProperty* SectionCutOutOperation::release_ellipse() {
  
  ::catalog_studio_message::GeomtryEllipsePlanProperty* temp = ellipse_;
  ellipse_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::GeomtryEllipsePlanProperty* SectionCutOutOperation::unsafe_arena_release_ellipse() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionCutOutOperation.ellipse)
  
  ::catalog_studio_message::GeomtryEllipsePlanProperty* temp = ellipse_;
  ellipse_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::GeomtryEllipsePlanProperty* SectionCutOutOperation::_internal_mutable_ellipse() {
  
  if (ellipse_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::GeomtryEllipsePlanProperty>(GetArenaForAllocation());
    ellipse_ = p;
  }
  return ellipse_;
}
inline ::catalog_studio_message::GeomtryEllipsePlanProperty* SectionCutOutOperation::mutable_ellipse() {
  ::catalog_studio_message::GeomtryEllipsePlanProperty* _msg = _internal_mutable_ellipse();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionCutOutOperation.ellipse)
  return _msg;
}
inline void SectionCutOutOperation::set_allocated_ellipse(::catalog_studio_message::GeomtryEllipsePlanProperty* ellipse) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ellipse_);
  }
  if (ellipse) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ellipse));
    if (message_arena != submessage_arena) {
      ellipse = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ellipse, submessage_arena);
    }
    
  } else {
    
  }
  ellipse_ = ellipse;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionCutOutOperation.ellipse)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionCutOutOperation_2eproto
