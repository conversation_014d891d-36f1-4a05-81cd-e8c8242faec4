// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionDrawOperation.proto

#include "SectionDrawOperation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionDrawOperation::SectionDrawOperation(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : draw_offset_x_(nullptr)
  , draw_offset_y_(nullptr)
  , draw_offset_z_(nullptr)
  , id_(0){}
struct SectionDrawOperationDefaultTypeInternal {
  constexpr SectionDrawOperationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionDrawOperationDefaultTypeInternal() {}
  union {
    SectionDrawOperation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionDrawOperationDefaultTypeInternal _SectionDrawOperation_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionDrawOperation_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionDrawOperation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionDrawOperation_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionDrawOperation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionDrawOperation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionDrawOperation, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionDrawOperation, draw_offset_x_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionDrawOperation, draw_offset_y_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionDrawOperation, draw_offset_z_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionDrawOperation)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionDrawOperation_default_instance_),
};

const char descriptor_table_protodef_SectionDrawOperation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032SectionDrawOperation.proto\022\026catalog_st"
  "udio_message\032\031ExpressionValuePair.proto\""
  "\356\001\n\024SectionDrawOperation\022\n\n\002id\030\001 \001(\005\022B\n\r"
  "draw_offset_x\030\002 \001(\0132+.catalog_studio_mes"
  "sage.ExpressionValuePair\022B\n\rdraw_offset_"
  "y\030\003 \001(\0132+.catalog_studio_message.Express"
  "ionValuePair\022B\n\rdraw_offset_z\030\004 \001(\0132+.ca"
  "talog_studio_message.ExpressionValuePair"
  "b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionDrawOperation_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionDrawOperation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionDrawOperation_2eproto = {
  false, false, 328, descriptor_table_protodef_SectionDrawOperation_2eproto, "SectionDrawOperation.proto", 
  &descriptor_table_SectionDrawOperation_2eproto_once, descriptor_table_SectionDrawOperation_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_SectionDrawOperation_2eproto::offsets,
  file_level_metadata_SectionDrawOperation_2eproto, file_level_enum_descriptors_SectionDrawOperation_2eproto, file_level_service_descriptors_SectionDrawOperation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionDrawOperation_2eproto_getter() {
  return &descriptor_table_SectionDrawOperation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionDrawOperation_2eproto(&descriptor_table_SectionDrawOperation_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionDrawOperation::_Internal {
 public:
  static const ::catalog_studio_message::ExpressionValuePair& draw_offset_x(const SectionDrawOperation* msg);
  static const ::catalog_studio_message::ExpressionValuePair& draw_offset_y(const SectionDrawOperation* msg);
  static const ::catalog_studio_message::ExpressionValuePair& draw_offset_z(const SectionDrawOperation* msg);
};

const ::catalog_studio_message::ExpressionValuePair&
SectionDrawOperation::_Internal::draw_offset_x(const SectionDrawOperation* msg) {
  return *msg->draw_offset_x_;
}
const ::catalog_studio_message::ExpressionValuePair&
SectionDrawOperation::_Internal::draw_offset_y(const SectionDrawOperation* msg) {
  return *msg->draw_offset_y_;
}
const ::catalog_studio_message::ExpressionValuePair&
SectionDrawOperation::_Internal::draw_offset_z(const SectionDrawOperation* msg) {
  return *msg->draw_offset_z_;
}
void SectionDrawOperation::clear_draw_offset_x() {
  if (GetArenaForAllocation() == nullptr && draw_offset_x_ != nullptr) {
    delete draw_offset_x_;
  }
  draw_offset_x_ = nullptr;
}
void SectionDrawOperation::clear_draw_offset_y() {
  if (GetArenaForAllocation() == nullptr && draw_offset_y_ != nullptr) {
    delete draw_offset_y_;
  }
  draw_offset_y_ = nullptr;
}
void SectionDrawOperation::clear_draw_offset_z() {
  if (GetArenaForAllocation() == nullptr && draw_offset_z_ != nullptr) {
    delete draw_offset_z_;
  }
  draw_offset_z_ = nullptr;
}
SectionDrawOperation::SectionDrawOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionDrawOperation)
}
SectionDrawOperation::SectionDrawOperation(const SectionDrawOperation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_draw_offset_x()) {
    draw_offset_x_ = new ::catalog_studio_message::ExpressionValuePair(*from.draw_offset_x_);
  } else {
    draw_offset_x_ = nullptr;
  }
  if (from._internal_has_draw_offset_y()) {
    draw_offset_y_ = new ::catalog_studio_message::ExpressionValuePair(*from.draw_offset_y_);
  } else {
    draw_offset_y_ = nullptr;
  }
  if (from._internal_has_draw_offset_z()) {
    draw_offset_z_ = new ::catalog_studio_message::ExpressionValuePair(*from.draw_offset_z_);
  } else {
    draw_offset_z_ = nullptr;
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionDrawOperation)
}

void SectionDrawOperation::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&draw_offset_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&id_) -
    reinterpret_cast<char*>(&draw_offset_x_)) + sizeof(id_));
}

SectionDrawOperation::~SectionDrawOperation() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionDrawOperation)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionDrawOperation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete draw_offset_x_;
  if (this != internal_default_instance()) delete draw_offset_y_;
  if (this != internal_default_instance()) delete draw_offset_z_;
}

void SectionDrawOperation::ArenaDtor(void* object) {
  SectionDrawOperation* _this = reinterpret_cast< SectionDrawOperation* >(object);
  (void)_this;
}
void SectionDrawOperation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionDrawOperation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionDrawOperation::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionDrawOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && draw_offset_x_ != nullptr) {
    delete draw_offset_x_;
  }
  draw_offset_x_ = nullptr;
  if (GetArenaForAllocation() == nullptr && draw_offset_y_ != nullptr) {
    delete draw_offset_y_;
  }
  draw_offset_y_ = nullptr;
  if (GetArenaForAllocation() == nullptr && draw_offset_z_ != nullptr) {
    delete draw_offset_z_;
  }
  draw_offset_z_ = nullptr;
  id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionDrawOperation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair draw_offset_x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_draw_offset_x(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair draw_offset_y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_draw_offset_y(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair draw_offset_z = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_draw_offset_z(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionDrawOperation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionDrawOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.ExpressionValuePair draw_offset_x = 2;
  if (this->_internal_has_draw_offset_x()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::draw_offset_x(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair draw_offset_y = 3;
  if (this->_internal_has_draw_offset_y()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::draw_offset_y(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair draw_offset_z = 4;
  if (this->_internal_has_draw_offset_z()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::draw_offset_z(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionDrawOperation)
  return target;
}

size_t SectionDrawOperation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionDrawOperation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .catalog_studio_message.ExpressionValuePair draw_offset_x = 2;
  if (this->_internal_has_draw_offset_x()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *draw_offset_x_);
  }

  // .catalog_studio_message.ExpressionValuePair draw_offset_y = 3;
  if (this->_internal_has_draw_offset_y()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *draw_offset_y_);
  }

  // .catalog_studio_message.ExpressionValuePair draw_offset_z = 4;
  if (this->_internal_has_draw_offset_z()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *draw_offset_z_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionDrawOperation::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionDrawOperation::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionDrawOperation::GetClassData() const { return &_class_data_; }

void SectionDrawOperation::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionDrawOperation *>(to)->MergeFrom(
      static_cast<const SectionDrawOperation &>(from));
}


void SectionDrawOperation::MergeFrom(const SectionDrawOperation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionDrawOperation)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_draw_offset_x()) {
    _internal_mutable_draw_offset_x()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_draw_offset_x());
  }
  if (from._internal_has_draw_offset_y()) {
    _internal_mutable_draw_offset_y()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_draw_offset_y());
  }
  if (from._internal_has_draw_offset_z()) {
    _internal_mutable_draw_offset_z()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_draw_offset_z());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionDrawOperation::CopyFrom(const SectionDrawOperation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionDrawOperation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionDrawOperation::IsInitialized() const {
  return true;
}

void SectionDrawOperation::InternalSwap(SectionDrawOperation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SectionDrawOperation, id_)
      + sizeof(SectionDrawOperation::id_)
      - PROTOBUF_FIELD_OFFSET(SectionDrawOperation, draw_offset_x_)>(
          reinterpret_cast<char*>(&draw_offset_x_),
          reinterpret_cast<char*>(&other->draw_offset_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionDrawOperation::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionDrawOperation_2eproto_getter, &descriptor_table_SectionDrawOperation_2eproto_once,
      file_level_metadata_SectionDrawOperation_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionDrawOperation* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionDrawOperation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionDrawOperation >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
