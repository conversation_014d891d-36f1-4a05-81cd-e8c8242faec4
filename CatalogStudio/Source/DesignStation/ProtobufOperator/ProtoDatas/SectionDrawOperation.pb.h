// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionDrawOperation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionDrawOperation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionDrawOperation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionDrawOperation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionDrawOperation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionDrawOperation_2eproto;
namespace catalog_studio_message {
class SectionDrawOperation;
struct SectionDrawOperationDefaultTypeInternal;
extern SectionDrawOperationDefaultTypeInternal _SectionDrawOperation_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionDrawOperation* Arena::CreateMaybeMessage<::catalog_studio_message::SectionDrawOperation>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionDrawOperation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionDrawOperation) */ {
 public:
  inline SectionDrawOperation() : SectionDrawOperation(nullptr) {}
  ~SectionDrawOperation() override;
  explicit constexpr SectionDrawOperation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionDrawOperation(const SectionDrawOperation& from);
  SectionDrawOperation(SectionDrawOperation&& from) noexcept
    : SectionDrawOperation() {
    *this = ::std::move(from);
  }

  inline SectionDrawOperation& operator=(const SectionDrawOperation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionDrawOperation& operator=(SectionDrawOperation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionDrawOperation& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionDrawOperation* internal_default_instance() {
    return reinterpret_cast<const SectionDrawOperation*>(
               &_SectionDrawOperation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionDrawOperation& a, SectionDrawOperation& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionDrawOperation* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionDrawOperation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionDrawOperation* New() const final {
    return new SectionDrawOperation();
  }

  SectionDrawOperation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionDrawOperation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionDrawOperation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionDrawOperation& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionDrawOperation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionDrawOperation";
  }
  protected:
  explicit SectionDrawOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDrawOffsetXFieldNumber = 2,
    kDrawOffsetYFieldNumber = 3,
    kDrawOffsetZFieldNumber = 4,
    kIdFieldNumber = 1,
  };
  // .catalog_studio_message.ExpressionValuePair draw_offset_x = 2;
  bool has_draw_offset_x() const;
  private:
  bool _internal_has_draw_offset_x() const;
  public:
  void clear_draw_offset_x();
  const ::catalog_studio_message::ExpressionValuePair& draw_offset_x() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_draw_offset_x();
  ::catalog_studio_message::ExpressionValuePair* mutable_draw_offset_x();
  void set_allocated_draw_offset_x(::catalog_studio_message::ExpressionValuePair* draw_offset_x);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_draw_offset_x() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_draw_offset_x();
  public:
  void unsafe_arena_set_allocated_draw_offset_x(
      ::catalog_studio_message::ExpressionValuePair* draw_offset_x);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_draw_offset_x();

  // .catalog_studio_message.ExpressionValuePair draw_offset_y = 3;
  bool has_draw_offset_y() const;
  private:
  bool _internal_has_draw_offset_y() const;
  public:
  void clear_draw_offset_y();
  const ::catalog_studio_message::ExpressionValuePair& draw_offset_y() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_draw_offset_y();
  ::catalog_studio_message::ExpressionValuePair* mutable_draw_offset_y();
  void set_allocated_draw_offset_y(::catalog_studio_message::ExpressionValuePair* draw_offset_y);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_draw_offset_y() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_draw_offset_y();
  public:
  void unsafe_arena_set_allocated_draw_offset_y(
      ::catalog_studio_message::ExpressionValuePair* draw_offset_y);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_draw_offset_y();

  // .catalog_studio_message.ExpressionValuePair draw_offset_z = 4;
  bool has_draw_offset_z() const;
  private:
  bool _internal_has_draw_offset_z() const;
  public:
  void clear_draw_offset_z();
  const ::catalog_studio_message::ExpressionValuePair& draw_offset_z() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_draw_offset_z();
  ::catalog_studio_message::ExpressionValuePair* mutable_draw_offset_z();
  void set_allocated_draw_offset_z(::catalog_studio_message::ExpressionValuePair* draw_offset_z);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_draw_offset_z() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_draw_offset_z();
  public:
  void unsafe_arena_set_allocated_draw_offset_z(
      ::catalog_studio_message::ExpressionValuePair* draw_offset_z);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_draw_offset_z();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionDrawOperation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::catalog_studio_message::ExpressionValuePair* draw_offset_x_;
  ::catalog_studio_message::ExpressionValuePair* draw_offset_y_;
  ::catalog_studio_message::ExpressionValuePair* draw_offset_z_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionDrawOperation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionDrawOperation

// int32 id = 1;
inline void SectionDrawOperation::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionDrawOperation::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionDrawOperation::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionDrawOperation.id)
  return _internal_id();
}
inline void SectionDrawOperation::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void SectionDrawOperation::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionDrawOperation.id)
}

// .catalog_studio_message.ExpressionValuePair draw_offset_x = 2;
inline bool SectionDrawOperation::_internal_has_draw_offset_x() const {
  return this != internal_default_instance() && draw_offset_x_ != nullptr;
}
inline bool SectionDrawOperation::has_draw_offset_x() const {
  return _internal_has_draw_offset_x();
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::_internal_draw_offset_x() const {
  const ::catalog_studio_message::ExpressionValuePair* p = draw_offset_x_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::draw_offset_x() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionDrawOperation.draw_offset_x)
  return _internal_draw_offset_x();
}
inline void SectionDrawOperation::unsafe_arena_set_allocated_draw_offset_x(
    ::catalog_studio_message::ExpressionValuePair* draw_offset_x) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_x_);
  }
  draw_offset_x_ = draw_offset_x;
  if (draw_offset_x) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_x)
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::release_draw_offset_x() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_x_;
  draw_offset_x_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::unsafe_arena_release_draw_offset_x() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionDrawOperation.draw_offset_x)
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_x_;
  draw_offset_x_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::_internal_mutable_draw_offset_x() {
  
  if (draw_offset_x_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    draw_offset_x_ = p;
  }
  return draw_offset_x_;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::mutable_draw_offset_x() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_draw_offset_x();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionDrawOperation.draw_offset_x)
  return _msg;
}
inline void SectionDrawOperation::set_allocated_draw_offset_x(::catalog_studio_message::ExpressionValuePair* draw_offset_x) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_x_);
  }
  if (draw_offset_x) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_x));
    if (message_arena != submessage_arena) {
      draw_offset_x = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, draw_offset_x, submessage_arena);
    }
    
  } else {
    
  }
  draw_offset_x_ = draw_offset_x;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_x)
}

// .catalog_studio_message.ExpressionValuePair draw_offset_y = 3;
inline bool SectionDrawOperation::_internal_has_draw_offset_y() const {
  return this != internal_default_instance() && draw_offset_y_ != nullptr;
}
inline bool SectionDrawOperation::has_draw_offset_y() const {
  return _internal_has_draw_offset_y();
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::_internal_draw_offset_y() const {
  const ::catalog_studio_message::ExpressionValuePair* p = draw_offset_y_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::draw_offset_y() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionDrawOperation.draw_offset_y)
  return _internal_draw_offset_y();
}
inline void SectionDrawOperation::unsafe_arena_set_allocated_draw_offset_y(
    ::catalog_studio_message::ExpressionValuePair* draw_offset_y) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_y_);
  }
  draw_offset_y_ = draw_offset_y;
  if (draw_offset_y) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_y)
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::release_draw_offset_y() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_y_;
  draw_offset_y_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::unsafe_arena_release_draw_offset_y() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionDrawOperation.draw_offset_y)
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_y_;
  draw_offset_y_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::_internal_mutable_draw_offset_y() {
  
  if (draw_offset_y_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    draw_offset_y_ = p;
  }
  return draw_offset_y_;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::mutable_draw_offset_y() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_draw_offset_y();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionDrawOperation.draw_offset_y)
  return _msg;
}
inline void SectionDrawOperation::set_allocated_draw_offset_y(::catalog_studio_message::ExpressionValuePair* draw_offset_y) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_y_);
  }
  if (draw_offset_y) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_y));
    if (message_arena != submessage_arena) {
      draw_offset_y = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, draw_offset_y, submessage_arena);
    }
    
  } else {
    
  }
  draw_offset_y_ = draw_offset_y;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_y)
}

// .catalog_studio_message.ExpressionValuePair draw_offset_z = 4;
inline bool SectionDrawOperation::_internal_has_draw_offset_z() const {
  return this != internal_default_instance() && draw_offset_z_ != nullptr;
}
inline bool SectionDrawOperation::has_draw_offset_z() const {
  return _internal_has_draw_offset_z();
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::_internal_draw_offset_z() const {
  const ::catalog_studio_message::ExpressionValuePair* p = draw_offset_z_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionDrawOperation::draw_offset_z() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionDrawOperation.draw_offset_z)
  return _internal_draw_offset_z();
}
inline void SectionDrawOperation::unsafe_arena_set_allocated_draw_offset_z(
    ::catalog_studio_message::ExpressionValuePair* draw_offset_z) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_z_);
  }
  draw_offset_z_ = draw_offset_z;
  if (draw_offset_z) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_z)
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::release_draw_offset_z() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_z_;
  draw_offset_z_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::unsafe_arena_release_draw_offset_z() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionDrawOperation.draw_offset_z)
  
  ::catalog_studio_message::ExpressionValuePair* temp = draw_offset_z_;
  draw_offset_z_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::_internal_mutable_draw_offset_z() {
  
  if (draw_offset_z_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    draw_offset_z_ = p;
  }
  return draw_offset_z_;
}
inline ::catalog_studio_message::ExpressionValuePair* SectionDrawOperation::mutable_draw_offset_z() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_draw_offset_z();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionDrawOperation.draw_offset_z)
  return _msg;
}
inline void SectionDrawOperation::set_allocated_draw_offset_z(::catalog_studio_message::ExpressionValuePair* draw_offset_z) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_z_);
  }
  if (draw_offset_z) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(draw_offset_z));
    if (message_arena != submessage_arena) {
      draw_offset_z = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, draw_offset_z, submessage_arena);
    }
    
  } else {
    
  }
  draw_offset_z_ = draw_offset_z;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionDrawOperation.draw_offset_z)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionDrawOperation_2eproto
