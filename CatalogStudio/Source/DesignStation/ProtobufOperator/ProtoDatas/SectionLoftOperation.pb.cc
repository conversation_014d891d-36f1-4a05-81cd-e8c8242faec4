// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionLoftOperation.proto

#include "SectionLoftOperation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionLoftOperation::SectionLoftOperation(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : points_()
  , lines_()
  , id_(0)
  , plan_belongs_(0)
{}
struct SectionLoftOperationDefaultTypeInternal {
  constexpr SectionLoftOperationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionLoftOperationDefaultTypeInternal() {}
  union {
    SectionLoftOperation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionLoftOperationDefaultTypeInternal _SectionLoftOperation_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionLoftOperation_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionLoftOperation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionLoftOperation_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionLoftOperation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionLoftOperation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionLoftOperation, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionLoftOperation, plan_belongs_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionLoftOperation, points_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionLoftOperation, lines_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionLoftOperation)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionLoftOperation_default_instance_),
};

const char descriptor_table_protodef_SectionLoftOperation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032SectionLoftOperation.proto\022\026catalog_st"
  "udio_message\032\027ComponentEnumData.proto\032\032G"
  "eomtryPointProperty.proto\032\031GeomtryLinePr"
  "operty.proto\"\336\001\n\024SectionLoftOperation\022\n\n"
  "\002id\030\001 \001(\005\022@\n\014plan_belongs\030\002 \001(\0162*.catalo"
  "g_studio_message.PlanPolygonBelongs\022<\n\006p"
  "oints\030\003 \003(\0132,.catalog_studio_message.Geo"
  "mtryPointProperty\022:\n\005lines\030\004 \003(\0132+.catal"
  "og_studio_message.GeomtryLinePropertyb\006p"
  "roto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionLoftOperation_2eproto_deps[3] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_GeomtryLineProperty_2eproto,
  &::descriptor_table_GeomtryPointProperty_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionLoftOperation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionLoftOperation_2eproto = {
  false, false, 365, descriptor_table_protodef_SectionLoftOperation_2eproto, "SectionLoftOperation.proto", 
  &descriptor_table_SectionLoftOperation_2eproto_once, descriptor_table_SectionLoftOperation_2eproto_deps, 3, 1,
  schemas, file_default_instances, TableStruct_SectionLoftOperation_2eproto::offsets,
  file_level_metadata_SectionLoftOperation_2eproto, file_level_enum_descriptors_SectionLoftOperation_2eproto, file_level_service_descriptors_SectionLoftOperation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionLoftOperation_2eproto_getter() {
  return &descriptor_table_SectionLoftOperation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionLoftOperation_2eproto(&descriptor_table_SectionLoftOperation_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionLoftOperation::_Internal {
 public:
};

void SectionLoftOperation::clear_points() {
  points_.Clear();
}
void SectionLoftOperation::clear_lines() {
  lines_.Clear();
}
SectionLoftOperation::SectionLoftOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  points_(arena),
  lines_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionLoftOperation)
}
SectionLoftOperation::SectionLoftOperation(const SectionLoftOperation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_),
      lines_(from.lines_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionLoftOperation)
}

void SectionLoftOperation::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plan_belongs_) -
    reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
}

SectionLoftOperation::~SectionLoftOperation() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionLoftOperation)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionLoftOperation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SectionLoftOperation::ArenaDtor(void* object) {
  SectionLoftOperation* _this = reinterpret_cast< SectionLoftOperation* >(object);
  (void)_this;
}
void SectionLoftOperation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionLoftOperation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionLoftOperation::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionLoftOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  lines_.Clear();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&plan_belongs_) -
      reinterpret_cast<char*>(&id_)) + sizeof(plan_belongs_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionLoftOperation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_plan_belongs(static_cast<::catalog_studio_message::PlanPolygonBelongs>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.GeomtryPointProperty points = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.GeomtryLineProperty lines = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lines(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionLoftOperation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionLoftOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 2;
  if (this->_internal_plan_belongs() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_plan_belongs(), target);
  }

  // repeated .catalog_studio_message.GeomtryPointProperty points = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_points(i), target, stream);
  }

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lines_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_lines(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionLoftOperation)
  return target;
}

size_t SectionLoftOperation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionLoftOperation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.GeomtryPointProperty points = 3;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 4;
  total_size += 1UL * this->_internal_lines_size();
  for (const auto& msg : this->lines_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 2;
  if (this->_internal_plan_belongs() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_plan_belongs());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionLoftOperation::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionLoftOperation::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionLoftOperation::GetClassData() const { return &_class_data_; }

void SectionLoftOperation::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionLoftOperation *>(to)->MergeFrom(
      static_cast<const SectionLoftOperation &>(from));
}


void SectionLoftOperation::MergeFrom(const SectionLoftOperation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionLoftOperation)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
  lines_.MergeFrom(from.lines_);
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_plan_belongs() != 0) {
    _internal_set_plan_belongs(from._internal_plan_belongs());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionLoftOperation::CopyFrom(const SectionLoftOperation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionLoftOperation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionLoftOperation::IsInitialized() const {
  return true;
}

void SectionLoftOperation::InternalSwap(SectionLoftOperation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
  lines_.InternalSwap(&other->lines_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SectionLoftOperation, plan_belongs_)
      + sizeof(SectionLoftOperation::plan_belongs_)
      - PROTOBUF_FIELD_OFFSET(SectionLoftOperation, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionLoftOperation::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionLoftOperation_2eproto_getter, &descriptor_table_SectionLoftOperation_2eproto_once,
      file_level_metadata_SectionLoftOperation_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionLoftOperation* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionLoftOperation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionLoftOperation >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
