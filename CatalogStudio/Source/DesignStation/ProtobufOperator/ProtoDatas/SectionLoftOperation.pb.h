// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionLoftOperation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionLoftOperation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionLoftOperation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "GeomtryPointProperty.pb.h"
#include "GeomtryLineProperty.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionLoftOperation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionLoftOperation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionLoftOperation_2eproto;
namespace catalog_studio_message {
class SectionLoftOperation;
struct SectionLoftOperationDefaultTypeInternal;
extern SectionLoftOperationDefaultTypeInternal _SectionLoftOperation_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionLoftOperation* Arena::CreateMaybeMessage<::catalog_studio_message::SectionLoftOperation>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionLoftOperation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionLoftOperation) */ {
 public:
  inline SectionLoftOperation() : SectionLoftOperation(nullptr) {}
  ~SectionLoftOperation() override;
  explicit constexpr SectionLoftOperation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionLoftOperation(const SectionLoftOperation& from);
  SectionLoftOperation(SectionLoftOperation&& from) noexcept
    : SectionLoftOperation() {
    *this = ::std::move(from);
  }

  inline SectionLoftOperation& operator=(const SectionLoftOperation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionLoftOperation& operator=(SectionLoftOperation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionLoftOperation& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionLoftOperation* internal_default_instance() {
    return reinterpret_cast<const SectionLoftOperation*>(
               &_SectionLoftOperation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionLoftOperation& a, SectionLoftOperation& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionLoftOperation* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionLoftOperation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionLoftOperation* New() const final {
    return new SectionLoftOperation();
  }

  SectionLoftOperation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionLoftOperation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionLoftOperation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionLoftOperation& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionLoftOperation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionLoftOperation";
  }
  protected:
  explicit SectionLoftOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 3,
    kLinesFieldNumber = 4,
    kIdFieldNumber = 1,
    kPlanBelongsFieldNumber = 2,
  };
  // repeated .catalog_studio_message.GeomtryPointProperty points = 3;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::catalog_studio_message::GeomtryPointProperty* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >*
      mutable_points();
  private:
  const ::catalog_studio_message::GeomtryPointProperty& _internal_points(int index) const;
  ::catalog_studio_message::GeomtryPointProperty* _internal_add_points();
  public:
  const ::catalog_studio_message::GeomtryPointProperty& points(int index) const;
  ::catalog_studio_message::GeomtryPointProperty* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >&
      points() const;

  // repeated .catalog_studio_message.GeomtryLineProperty lines = 4;
  int lines_size() const;
  private:
  int _internal_lines_size() const;
  public:
  void clear_lines();
  ::catalog_studio_message::GeomtryLineProperty* mutable_lines(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >*
      mutable_lines();
  private:
  const ::catalog_studio_message::GeomtryLineProperty& _internal_lines(int index) const;
  ::catalog_studio_message::GeomtryLineProperty* _internal_add_lines();
  public:
  const ::catalog_studio_message::GeomtryLineProperty& lines(int index) const;
  ::catalog_studio_message::GeomtryLineProperty* add_lines();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >&
      lines() const;

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .catalog_studio_message.PlanPolygonBelongs plan_belongs = 2;
  void clear_plan_belongs();
  ::catalog_studio_message::PlanPolygonBelongs plan_belongs() const;
  void set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  private:
  ::catalog_studio_message::PlanPolygonBelongs _internal_plan_belongs() const;
  void _internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionLoftOperation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty > points_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty > lines_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  int plan_belongs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionLoftOperation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionLoftOperation

// int32 id = 1;
inline void SectionLoftOperation::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionLoftOperation::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionLoftOperation::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionLoftOperation.id)
  return _internal_id();
}
inline void SectionLoftOperation::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void SectionLoftOperation::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionLoftOperation.id)
}

// .catalog_studio_message.PlanPolygonBelongs plan_belongs = 2;
inline void SectionLoftOperation::clear_plan_belongs() {
  plan_belongs_ = 0;
}
inline ::catalog_studio_message::PlanPolygonBelongs SectionLoftOperation::_internal_plan_belongs() const {
  return static_cast< ::catalog_studio_message::PlanPolygonBelongs >(plan_belongs_);
}
inline ::catalog_studio_message::PlanPolygonBelongs SectionLoftOperation::plan_belongs() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionLoftOperation.plan_belongs)
  return _internal_plan_belongs();
}
inline void SectionLoftOperation::_internal_set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  
  plan_belongs_ = value;
}
inline void SectionLoftOperation::set_plan_belongs(::catalog_studio_message::PlanPolygonBelongs value) {
  _internal_set_plan_belongs(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionLoftOperation.plan_belongs)
}

// repeated .catalog_studio_message.GeomtryPointProperty points = 3;
inline int SectionLoftOperation::_internal_points_size() const {
  return points_.size();
}
inline int SectionLoftOperation::points_size() const {
  return _internal_points_size();
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionLoftOperation::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionLoftOperation.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >*
SectionLoftOperation::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionLoftOperation.points)
  return &points_;
}
inline const ::catalog_studio_message::GeomtryPointProperty& SectionLoftOperation::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::catalog_studio_message::GeomtryPointProperty& SectionLoftOperation::points(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionLoftOperation.points)
  return _internal_points(index);
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionLoftOperation::_internal_add_points() {
  return points_.Add();
}
inline ::catalog_studio_message::GeomtryPointProperty* SectionLoftOperation::add_points() {
  ::catalog_studio_message::GeomtryPointProperty* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionLoftOperation.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryPointProperty >&
SectionLoftOperation::points() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionLoftOperation.points)
  return points_;
}

// repeated .catalog_studio_message.GeomtryLineProperty lines = 4;
inline int SectionLoftOperation::_internal_lines_size() const {
  return lines_.size();
}
inline int SectionLoftOperation::lines_size() const {
  return _internal_lines_size();
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionLoftOperation::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionLoftOperation.lines)
  return lines_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >*
SectionLoftOperation::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionLoftOperation.lines)
  return &lines_;
}
inline const ::catalog_studio_message::GeomtryLineProperty& SectionLoftOperation::_internal_lines(int index) const {
  return lines_.Get(index);
}
inline const ::catalog_studio_message::GeomtryLineProperty& SectionLoftOperation::lines(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionLoftOperation.lines)
  return _internal_lines(index);
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionLoftOperation::_internal_add_lines() {
  return lines_.Add();
}
inline ::catalog_studio_message::GeomtryLineProperty* SectionLoftOperation::add_lines() {
  ::catalog_studio_message::GeomtryLineProperty* _add = _internal_add_lines();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionLoftOperation.lines)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::GeomtryLineProperty >&
SectionLoftOperation::lines() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionLoftOperation.lines)
  return lines_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionLoftOperation_2eproto
