// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionOperation.proto

#include "SectionOperation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionOperation::SectionOperation(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : operator_order_()
  , draw_operations_()
  , shift_operations_()
  , zoom_operations_()
  , cutout_operations_()
  , lofting_operation_(nullptr){}
struct SectionOperationDefaultTypeInternal {
  constexpr SectionOperationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionOperationDefaultTypeInternal() {}
  union {
    SectionOperation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionOperationDefaultTypeInternal _SectionOperation_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionOperation_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionOperation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionOperation_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionOperation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, operator_order_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, draw_operations_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, shift_operations_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, zoom_operations_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, cutout_operations_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperation, lofting_operation_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionOperation)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionOperation_default_instance_),
};

const char descriptor_table_protodef_SectionOperation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026SectionOperation.proto\022\026catalog_studio"
  "_message\032\033SectionOperationOrder.proto\032\032S"
  "ectionDrawOperation.proto\032\036SectionShifti"
  "ngOperation.proto\032\032SectionZoomOperation."
  "proto\032\034SectionCutOutOperation.proto\032\032Sec"
  "tionLoftOperation.proto\"\307\003\n\020SectionOpera"
  "tion\022E\n\016operator_order\030\001 \003(\0132-.catalog_s"
  "tudio_message.SectionOperationOrder\022E\n\017d"
  "raw_operations\030\002 \003(\0132,.catalog_studio_me"
  "ssage.SectionDrawOperation\022J\n\020shift_oper"
  "ations\030\003 \003(\01320.catalog_studio_message.Se"
  "ctionShiftingOperation\022E\n\017zoom_operation"
  "s\030\004 \003(\0132,.catalog_studio_message.Section"
  "ZoomOperation\022I\n\021cutout_operations\030\005 \003(\013"
  "2..catalog_studio_message.SectionCutOutO"
  "peration\022G\n\021lofting_operation\030\006 \001(\0132,.ca"
  "talog_studio_message.SectionLoftOperatio"
  "nb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionOperation_2eproto_deps[6] = {
  &::descriptor_table_SectionCutOutOperation_2eproto,
  &::descriptor_table_SectionDrawOperation_2eproto,
  &::descriptor_table_SectionLoftOperation_2eproto,
  &::descriptor_table_SectionOperationOrder_2eproto,
  &::descriptor_table_SectionShiftingOperation_2eproto,
  &::descriptor_table_SectionZoomOperation_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionOperation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionOperation_2eproto = {
  false, false, 689, descriptor_table_protodef_SectionOperation_2eproto, "SectionOperation.proto", 
  &descriptor_table_SectionOperation_2eproto_once, descriptor_table_SectionOperation_2eproto_deps, 6, 1,
  schemas, file_default_instances, TableStruct_SectionOperation_2eproto::offsets,
  file_level_metadata_SectionOperation_2eproto, file_level_enum_descriptors_SectionOperation_2eproto, file_level_service_descriptors_SectionOperation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionOperation_2eproto_getter() {
  return &descriptor_table_SectionOperation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionOperation_2eproto(&descriptor_table_SectionOperation_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionOperation::_Internal {
 public:
  static const ::catalog_studio_message::SectionLoftOperation& lofting_operation(const SectionOperation* msg);
};

const ::catalog_studio_message::SectionLoftOperation&
SectionOperation::_Internal::lofting_operation(const SectionOperation* msg) {
  return *msg->lofting_operation_;
}
void SectionOperation::clear_operator_order() {
  operator_order_.Clear();
}
void SectionOperation::clear_draw_operations() {
  draw_operations_.Clear();
}
void SectionOperation::clear_shift_operations() {
  shift_operations_.Clear();
}
void SectionOperation::clear_zoom_operations() {
  zoom_operations_.Clear();
}
void SectionOperation::clear_cutout_operations() {
  cutout_operations_.Clear();
}
void SectionOperation::clear_lofting_operation() {
  if (GetArenaForAllocation() == nullptr && lofting_operation_ != nullptr) {
    delete lofting_operation_;
  }
  lofting_operation_ = nullptr;
}
SectionOperation::SectionOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  operator_order_(arena),
  draw_operations_(arena),
  shift_operations_(arena),
  zoom_operations_(arena),
  cutout_operations_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionOperation)
}
SectionOperation::SectionOperation(const SectionOperation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      operator_order_(from.operator_order_),
      draw_operations_(from.draw_operations_),
      shift_operations_(from.shift_operations_),
      zoom_operations_(from.zoom_operations_),
      cutout_operations_(from.cutout_operations_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_lofting_operation()) {
    lofting_operation_ = new ::catalog_studio_message::SectionLoftOperation(*from.lofting_operation_);
  } else {
    lofting_operation_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionOperation)
}

void SectionOperation::SharedCtor() {
lofting_operation_ = nullptr;
}

SectionOperation::~SectionOperation() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionOperation)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionOperation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete lofting_operation_;
}

void SectionOperation::ArenaDtor(void* object) {
  SectionOperation* _this = reinterpret_cast< SectionOperation* >(object);
  (void)_this;
}
void SectionOperation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionOperation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionOperation::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  operator_order_.Clear();
  draw_operations_.Clear();
  shift_operations_.Clear();
  zoom_operations_.Clear();
  cutout_operations_.Clear();
  if (GetArenaForAllocation() == nullptr && lofting_operation_ != nullptr) {
    delete lofting_operation_;
  }
  lofting_operation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionOperation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.SectionOperationOrder operator_order = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_operator_order(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.SectionDrawOperation draw_operations = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_draw_operations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.SectionShiftingOperation shift_operations = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_shift_operations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.SectionZoomOperation zoom_operations = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_zoom_operations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.SectionCutOutOperation cutout_operations = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_cutout_operations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SectionLoftOperation lofting_operation = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_lofting_operation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionOperation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.SectionOperationOrder operator_order = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_operator_order_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_operator_order(i), target, stream);
  }

  // repeated .catalog_studio_message.SectionDrawOperation draw_operations = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_draw_operations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_draw_operations(i), target, stream);
  }

  // repeated .catalog_studio_message.SectionShiftingOperation shift_operations = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_shift_operations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_shift_operations(i), target, stream);
  }

  // repeated .catalog_studio_message.SectionZoomOperation zoom_operations = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_zoom_operations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_zoom_operations(i), target, stream);
  }

  // repeated .catalog_studio_message.SectionCutOutOperation cutout_operations = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_cutout_operations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_cutout_operations(i), target, stream);
  }

  // .catalog_studio_message.SectionLoftOperation lofting_operation = 6;
  if (this->_internal_has_lofting_operation()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::lofting_operation(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionOperation)
  return target;
}

size_t SectionOperation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionOperation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.SectionOperationOrder operator_order = 1;
  total_size += 1UL * this->_internal_operator_order_size();
  for (const auto& msg : this->operator_order_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.SectionDrawOperation draw_operations = 2;
  total_size += 1UL * this->_internal_draw_operations_size();
  for (const auto& msg : this->draw_operations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.SectionShiftingOperation shift_operations = 3;
  total_size += 1UL * this->_internal_shift_operations_size();
  for (const auto& msg : this->shift_operations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.SectionZoomOperation zoom_operations = 4;
  total_size += 1UL * this->_internal_zoom_operations_size();
  for (const auto& msg : this->zoom_operations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.SectionCutOutOperation cutout_operations = 5;
  total_size += 1UL * this->_internal_cutout_operations_size();
  for (const auto& msg : this->cutout_operations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .catalog_studio_message.SectionLoftOperation lofting_operation = 6;
  if (this->_internal_has_lofting_operation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *lofting_operation_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionOperation::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionOperation::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionOperation::GetClassData() const { return &_class_data_; }

void SectionOperation::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionOperation *>(to)->MergeFrom(
      static_cast<const SectionOperation &>(from));
}


void SectionOperation::MergeFrom(const SectionOperation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionOperation)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  operator_order_.MergeFrom(from.operator_order_);
  draw_operations_.MergeFrom(from.draw_operations_);
  shift_operations_.MergeFrom(from.shift_operations_);
  zoom_operations_.MergeFrom(from.zoom_operations_);
  cutout_operations_.MergeFrom(from.cutout_operations_);
  if (from._internal_has_lofting_operation()) {
    _internal_mutable_lofting_operation()->::catalog_studio_message::SectionLoftOperation::MergeFrom(from._internal_lofting_operation());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionOperation::CopyFrom(const SectionOperation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionOperation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionOperation::IsInitialized() const {
  return true;
}

void SectionOperation::InternalSwap(SectionOperation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  operator_order_.InternalSwap(&other->operator_order_);
  draw_operations_.InternalSwap(&other->draw_operations_);
  shift_operations_.InternalSwap(&other->shift_operations_);
  zoom_operations_.InternalSwap(&other->zoom_operations_);
  cutout_operations_.InternalSwap(&other->cutout_operations_);
  swap(lofting_operation_, other->lofting_operation_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionOperation::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionOperation_2eproto_getter, &descriptor_table_SectionOperation_2eproto_once,
      file_level_metadata_SectionOperation_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionOperation* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionOperation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionOperation >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
