// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionOperation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionOperation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionOperation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "SectionOperationOrder.pb.h"
#include "SectionDrawOperation.pb.h"
#include "SectionShiftingOperation.pb.h"
#include "SectionZoomOperation.pb.h"
#include "SectionCutOutOperation.pb.h"
#include "SectionLoftOperation.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionOperation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionOperation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionOperation_2eproto;
namespace catalog_studio_message {
class SectionOperation;
struct SectionOperationDefaultTypeInternal;
extern SectionOperationDefaultTypeInternal _SectionOperation_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionOperation* Arena::CreateMaybeMessage<::catalog_studio_message::SectionOperation>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionOperation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionOperation) */ {
 public:
  inline SectionOperation() : SectionOperation(nullptr) {}
  ~SectionOperation() override;
  explicit constexpr SectionOperation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionOperation(const SectionOperation& from);
  SectionOperation(SectionOperation&& from) noexcept
    : SectionOperation() {
    *this = ::std::move(from);
  }

  inline SectionOperation& operator=(const SectionOperation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionOperation& operator=(SectionOperation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionOperation& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionOperation* internal_default_instance() {
    return reinterpret_cast<const SectionOperation*>(
               &_SectionOperation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionOperation& a, SectionOperation& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionOperation* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionOperation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionOperation* New() const final {
    return new SectionOperation();
  }

  SectionOperation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionOperation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionOperation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionOperation& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionOperation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionOperation";
  }
  protected:
  explicit SectionOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperatorOrderFieldNumber = 1,
    kDrawOperationsFieldNumber = 2,
    kShiftOperationsFieldNumber = 3,
    kZoomOperationsFieldNumber = 4,
    kCutoutOperationsFieldNumber = 5,
    kLoftingOperationFieldNumber = 6,
  };
  // repeated .catalog_studio_message.SectionOperationOrder operator_order = 1;
  int operator_order_size() const;
  private:
  int _internal_operator_order_size() const;
  public:
  void clear_operator_order();
  ::catalog_studio_message::SectionOperationOrder* mutable_operator_order(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionOperationOrder >*
      mutable_operator_order();
  private:
  const ::catalog_studio_message::SectionOperationOrder& _internal_operator_order(int index) const;
  ::catalog_studio_message::SectionOperationOrder* _internal_add_operator_order();
  public:
  const ::catalog_studio_message::SectionOperationOrder& operator_order(int index) const;
  ::catalog_studio_message::SectionOperationOrder* add_operator_order();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionOperationOrder >&
      operator_order() const;

  // repeated .catalog_studio_message.SectionDrawOperation draw_operations = 2;
  int draw_operations_size() const;
  private:
  int _internal_draw_operations_size() const;
  public:
  void clear_draw_operations();
  ::catalog_studio_message::SectionDrawOperation* mutable_draw_operations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionDrawOperation >*
      mutable_draw_operations();
  private:
  const ::catalog_studio_message::SectionDrawOperation& _internal_draw_operations(int index) const;
  ::catalog_studio_message::SectionDrawOperation* _internal_add_draw_operations();
  public:
  const ::catalog_studio_message::SectionDrawOperation& draw_operations(int index) const;
  ::catalog_studio_message::SectionDrawOperation* add_draw_operations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionDrawOperation >&
      draw_operations() const;

  // repeated .catalog_studio_message.SectionShiftingOperation shift_operations = 3;
  int shift_operations_size() const;
  private:
  int _internal_shift_operations_size() const;
  public:
  void clear_shift_operations();
  ::catalog_studio_message::SectionShiftingOperation* mutable_shift_operations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionShiftingOperation >*
      mutable_shift_operations();
  private:
  const ::catalog_studio_message::SectionShiftingOperation& _internal_shift_operations(int index) const;
  ::catalog_studio_message::SectionShiftingOperation* _internal_add_shift_operations();
  public:
  const ::catalog_studio_message::SectionShiftingOperation& shift_operations(int index) const;
  ::catalog_studio_message::SectionShiftingOperation* add_shift_operations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionShiftingOperation >&
      shift_operations() const;

  // repeated .catalog_studio_message.SectionZoomOperation zoom_operations = 4;
  int zoom_operations_size() const;
  private:
  int _internal_zoom_operations_size() const;
  public:
  void clear_zoom_operations();
  ::catalog_studio_message::SectionZoomOperation* mutable_zoom_operations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionZoomOperation >*
      mutable_zoom_operations();
  private:
  const ::catalog_studio_message::SectionZoomOperation& _internal_zoom_operations(int index) const;
  ::catalog_studio_message::SectionZoomOperation* _internal_add_zoom_operations();
  public:
  const ::catalog_studio_message::SectionZoomOperation& zoom_operations(int index) const;
  ::catalog_studio_message::SectionZoomOperation* add_zoom_operations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionZoomOperation >&
      zoom_operations() const;

  // repeated .catalog_studio_message.SectionCutOutOperation cutout_operations = 5;
  int cutout_operations_size() const;
  private:
  int _internal_cutout_operations_size() const;
  public:
  void clear_cutout_operations();
  ::catalog_studio_message::SectionCutOutOperation* mutable_cutout_operations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionCutOutOperation >*
      mutable_cutout_operations();
  private:
  const ::catalog_studio_message::SectionCutOutOperation& _internal_cutout_operations(int index) const;
  ::catalog_studio_message::SectionCutOutOperation* _internal_add_cutout_operations();
  public:
  const ::catalog_studio_message::SectionCutOutOperation& cutout_operations(int index) const;
  ::catalog_studio_message::SectionCutOutOperation* add_cutout_operations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionCutOutOperation >&
      cutout_operations() const;

  // .catalog_studio_message.SectionLoftOperation lofting_operation = 6;
  bool has_lofting_operation() const;
  private:
  bool _internal_has_lofting_operation() const;
  public:
  void clear_lofting_operation();
  const ::catalog_studio_message::SectionLoftOperation& lofting_operation() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::SectionLoftOperation* release_lofting_operation();
  ::catalog_studio_message::SectionLoftOperation* mutable_lofting_operation();
  void set_allocated_lofting_operation(::catalog_studio_message::SectionLoftOperation* lofting_operation);
  private:
  const ::catalog_studio_message::SectionLoftOperation& _internal_lofting_operation() const;
  ::catalog_studio_message::SectionLoftOperation* _internal_mutable_lofting_operation();
  public:
  void unsafe_arena_set_allocated_lofting_operation(
      ::catalog_studio_message::SectionLoftOperation* lofting_operation);
  ::catalog_studio_message::SectionLoftOperation* unsafe_arena_release_lofting_operation();

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionOperation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionOperationOrder > operator_order_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionDrawOperation > draw_operations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionShiftingOperation > shift_operations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionZoomOperation > zoom_operations_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionCutOutOperation > cutout_operations_;
  ::catalog_studio_message::SectionLoftOperation* lofting_operation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionOperation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionOperation

// repeated .catalog_studio_message.SectionOperationOrder operator_order = 1;
inline int SectionOperation::_internal_operator_order_size() const {
  return operator_order_.size();
}
inline int SectionOperation::operator_order_size() const {
  return _internal_operator_order_size();
}
inline ::catalog_studio_message::SectionOperationOrder* SectionOperation::mutable_operator_order(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.operator_order)
  return operator_order_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionOperationOrder >*
SectionOperation::mutable_operator_order() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionOperation.operator_order)
  return &operator_order_;
}
inline const ::catalog_studio_message::SectionOperationOrder& SectionOperation::_internal_operator_order(int index) const {
  return operator_order_.Get(index);
}
inline const ::catalog_studio_message::SectionOperationOrder& SectionOperation::operator_order(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.operator_order)
  return _internal_operator_order(index);
}
inline ::catalog_studio_message::SectionOperationOrder* SectionOperation::_internal_add_operator_order() {
  return operator_order_.Add();
}
inline ::catalog_studio_message::SectionOperationOrder* SectionOperation::add_operator_order() {
  ::catalog_studio_message::SectionOperationOrder* _add = _internal_add_operator_order();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionOperation.operator_order)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionOperationOrder >&
SectionOperation::operator_order() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionOperation.operator_order)
  return operator_order_;
}

// repeated .catalog_studio_message.SectionDrawOperation draw_operations = 2;
inline int SectionOperation::_internal_draw_operations_size() const {
  return draw_operations_.size();
}
inline int SectionOperation::draw_operations_size() const {
  return _internal_draw_operations_size();
}
inline ::catalog_studio_message::SectionDrawOperation* SectionOperation::mutable_draw_operations(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.draw_operations)
  return draw_operations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionDrawOperation >*
SectionOperation::mutable_draw_operations() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionOperation.draw_operations)
  return &draw_operations_;
}
inline const ::catalog_studio_message::SectionDrawOperation& SectionOperation::_internal_draw_operations(int index) const {
  return draw_operations_.Get(index);
}
inline const ::catalog_studio_message::SectionDrawOperation& SectionOperation::draw_operations(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.draw_operations)
  return _internal_draw_operations(index);
}
inline ::catalog_studio_message::SectionDrawOperation* SectionOperation::_internal_add_draw_operations() {
  return draw_operations_.Add();
}
inline ::catalog_studio_message::SectionDrawOperation* SectionOperation::add_draw_operations() {
  ::catalog_studio_message::SectionDrawOperation* _add = _internal_add_draw_operations();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionOperation.draw_operations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionDrawOperation >&
SectionOperation::draw_operations() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionOperation.draw_operations)
  return draw_operations_;
}

// repeated .catalog_studio_message.SectionShiftingOperation shift_operations = 3;
inline int SectionOperation::_internal_shift_operations_size() const {
  return shift_operations_.size();
}
inline int SectionOperation::shift_operations_size() const {
  return _internal_shift_operations_size();
}
inline ::catalog_studio_message::SectionShiftingOperation* SectionOperation::mutable_shift_operations(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.shift_operations)
  return shift_operations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionShiftingOperation >*
SectionOperation::mutable_shift_operations() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionOperation.shift_operations)
  return &shift_operations_;
}
inline const ::catalog_studio_message::SectionShiftingOperation& SectionOperation::_internal_shift_operations(int index) const {
  return shift_operations_.Get(index);
}
inline const ::catalog_studio_message::SectionShiftingOperation& SectionOperation::shift_operations(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.shift_operations)
  return _internal_shift_operations(index);
}
inline ::catalog_studio_message::SectionShiftingOperation* SectionOperation::_internal_add_shift_operations() {
  return shift_operations_.Add();
}
inline ::catalog_studio_message::SectionShiftingOperation* SectionOperation::add_shift_operations() {
  ::catalog_studio_message::SectionShiftingOperation* _add = _internal_add_shift_operations();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionOperation.shift_operations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionShiftingOperation >&
SectionOperation::shift_operations() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionOperation.shift_operations)
  return shift_operations_;
}

// repeated .catalog_studio_message.SectionZoomOperation zoom_operations = 4;
inline int SectionOperation::_internal_zoom_operations_size() const {
  return zoom_operations_.size();
}
inline int SectionOperation::zoom_operations_size() const {
  return _internal_zoom_operations_size();
}
inline ::catalog_studio_message::SectionZoomOperation* SectionOperation::mutable_zoom_operations(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.zoom_operations)
  return zoom_operations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionZoomOperation >*
SectionOperation::mutable_zoom_operations() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionOperation.zoom_operations)
  return &zoom_operations_;
}
inline const ::catalog_studio_message::SectionZoomOperation& SectionOperation::_internal_zoom_operations(int index) const {
  return zoom_operations_.Get(index);
}
inline const ::catalog_studio_message::SectionZoomOperation& SectionOperation::zoom_operations(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.zoom_operations)
  return _internal_zoom_operations(index);
}
inline ::catalog_studio_message::SectionZoomOperation* SectionOperation::_internal_add_zoom_operations() {
  return zoom_operations_.Add();
}
inline ::catalog_studio_message::SectionZoomOperation* SectionOperation::add_zoom_operations() {
  ::catalog_studio_message::SectionZoomOperation* _add = _internal_add_zoom_operations();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionOperation.zoom_operations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionZoomOperation >&
SectionOperation::zoom_operations() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionOperation.zoom_operations)
  return zoom_operations_;
}

// repeated .catalog_studio_message.SectionCutOutOperation cutout_operations = 5;
inline int SectionOperation::_internal_cutout_operations_size() const {
  return cutout_operations_.size();
}
inline int SectionOperation::cutout_operations_size() const {
  return _internal_cutout_operations_size();
}
inline ::catalog_studio_message::SectionCutOutOperation* SectionOperation::mutable_cutout_operations(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.cutout_operations)
  return cutout_operations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionCutOutOperation >*
SectionOperation::mutable_cutout_operations() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionOperation.cutout_operations)
  return &cutout_operations_;
}
inline const ::catalog_studio_message::SectionCutOutOperation& SectionOperation::_internal_cutout_operations(int index) const {
  return cutout_operations_.Get(index);
}
inline const ::catalog_studio_message::SectionCutOutOperation& SectionOperation::cutout_operations(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.cutout_operations)
  return _internal_cutout_operations(index);
}
inline ::catalog_studio_message::SectionCutOutOperation* SectionOperation::_internal_add_cutout_operations() {
  return cutout_operations_.Add();
}
inline ::catalog_studio_message::SectionCutOutOperation* SectionOperation::add_cutout_operations() {
  ::catalog_studio_message::SectionCutOutOperation* _add = _internal_add_cutout_operations();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionOperation.cutout_operations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SectionCutOutOperation >&
SectionOperation::cutout_operations() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionOperation.cutout_operations)
  return cutout_operations_;
}

// .catalog_studio_message.SectionLoftOperation lofting_operation = 6;
inline bool SectionOperation::_internal_has_lofting_operation() const {
  return this != internal_default_instance() && lofting_operation_ != nullptr;
}
inline bool SectionOperation::has_lofting_operation() const {
  return _internal_has_lofting_operation();
}
inline const ::catalog_studio_message::SectionLoftOperation& SectionOperation::_internal_lofting_operation() const {
  const ::catalog_studio_message::SectionLoftOperation* p = lofting_operation_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::SectionLoftOperation&>(
      ::catalog_studio_message::_SectionLoftOperation_default_instance_);
}
inline const ::catalog_studio_message::SectionLoftOperation& SectionOperation::lofting_operation() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperation.lofting_operation)
  return _internal_lofting_operation();
}
inline void SectionOperation::unsafe_arena_set_allocated_lofting_operation(
    ::catalog_studio_message::SectionLoftOperation* lofting_operation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lofting_operation_);
  }
  lofting_operation_ = lofting_operation;
  if (lofting_operation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SectionOperation.lofting_operation)
}
inline ::catalog_studio_message::SectionLoftOperation* SectionOperation::release_lofting_operation() {
  
  ::catalog_studio_message::SectionLoftOperation* temp = lofting_operation_;
  lofting_operation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::SectionLoftOperation* SectionOperation::unsafe_arena_release_lofting_operation() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SectionOperation.lofting_operation)
  
  ::catalog_studio_message::SectionLoftOperation* temp = lofting_operation_;
  lofting_operation_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::SectionLoftOperation* SectionOperation::_internal_mutable_lofting_operation() {
  
  if (lofting_operation_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::SectionLoftOperation>(GetArenaForAllocation());
    lofting_operation_ = p;
  }
  return lofting_operation_;
}
inline ::catalog_studio_message::SectionLoftOperation* SectionOperation::mutable_lofting_operation() {
  ::catalog_studio_message::SectionLoftOperation* _msg = _internal_mutable_lofting_operation();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionOperation.lofting_operation)
  return _msg;
}
inline void SectionOperation::set_allocated_lofting_operation(::catalog_studio_message::SectionLoftOperation* lofting_operation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(lofting_operation_);
  }
  if (lofting_operation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lofting_operation));
    if (message_arena != submessage_arena) {
      lofting_operation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lofting_operation, submessage_arena);
    }
    
  } else {
    
  }
  lofting_operation_ = lofting_operation;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SectionOperation.lofting_operation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionOperation_2eproto
