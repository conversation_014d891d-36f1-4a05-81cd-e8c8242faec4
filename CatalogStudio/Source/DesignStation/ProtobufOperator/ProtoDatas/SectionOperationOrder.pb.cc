// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionOperationOrder.proto

#include "SectionOperationOrder.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionOperationOrder::SectionOperationOrder(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : index_(0)
  , operator_type_(0)
{}
struct SectionOperationOrderDefaultTypeInternal {
  constexpr SectionOperationOrderDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionOperationOrderDefaultTypeInternal() {}
  union {
    SectionOperationOrder _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionOperationOrderDefaultTypeInternal _SectionOperationOrder_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionOperationOrder_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionOperationOrder_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionOperationOrder_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionOperationOrder_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperationOrder, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperationOrder, index_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionOperationOrder, operator_type_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionOperationOrder)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionOperationOrder_default_instance_),
};

const char descriptor_table_protodef_SectionOperationOrder_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033SectionOperationOrder.proto\022\026catalog_s"
  "tudio_message\032\027ComponentEnumData.proto\"k"
  "\n\025SectionOperationOrder\022\r\n\005index\030\001 \001(\005\022C"
  "\n\roperator_type\030\002 \001(\0162,.catalog_studio_m"
  "essage.SectionOperationTypeb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionOperationOrder_2eproto_deps[1] = {
  &::descriptor_table_ComponentEnumData_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionOperationOrder_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionOperationOrder_2eproto = {
  false, false, 195, descriptor_table_protodef_SectionOperationOrder_2eproto, "SectionOperationOrder.proto", 
  &descriptor_table_SectionOperationOrder_2eproto_once, descriptor_table_SectionOperationOrder_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_SectionOperationOrder_2eproto::offsets,
  file_level_metadata_SectionOperationOrder_2eproto, file_level_enum_descriptors_SectionOperationOrder_2eproto, file_level_service_descriptors_SectionOperationOrder_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionOperationOrder_2eproto_getter() {
  return &descriptor_table_SectionOperationOrder_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionOperationOrder_2eproto(&descriptor_table_SectionOperationOrder_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionOperationOrder::_Internal {
 public:
};

SectionOperationOrder::SectionOperationOrder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionOperationOrder)
}
SectionOperationOrder::SectionOperationOrder(const SectionOperationOrder& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&index_, &from.index_,
    static_cast<size_t>(reinterpret_cast<char*>(&operator_type_) -
    reinterpret_cast<char*>(&index_)) + sizeof(operator_type_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionOperationOrder)
}

void SectionOperationOrder::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&index_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&operator_type_) -
    reinterpret_cast<char*>(&index_)) + sizeof(operator_type_));
}

SectionOperationOrder::~SectionOperationOrder() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionOperationOrder)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionOperationOrder::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SectionOperationOrder::ArenaDtor(void* object) {
  SectionOperationOrder* _this = reinterpret_cast< SectionOperationOrder* >(object);
  (void)_this;
}
void SectionOperationOrder::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionOperationOrder::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionOperationOrder::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionOperationOrder)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&operator_type_) -
      reinterpret_cast<char*>(&index_)) + sizeof(operator_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionOperationOrder::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 index = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SectionOperationType operator_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_operator_type(static_cast<::catalog_studio_message::SectionOperationType>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionOperationOrder::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionOperationOrder)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 index = 1;
  if (this->_internal_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_index(), target);
  }

  // .catalog_studio_message.SectionOperationType operator_type = 2;
  if (this->_internal_operator_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_operator_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionOperationOrder)
  return target;
}

size_t SectionOperationOrder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionOperationOrder)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 index = 1;
  if (this->_internal_index() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_index());
  }

  // .catalog_studio_message.SectionOperationType operator_type = 2;
  if (this->_internal_operator_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_operator_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionOperationOrder::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionOperationOrder::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionOperationOrder::GetClassData() const { return &_class_data_; }

void SectionOperationOrder::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionOperationOrder *>(to)->MergeFrom(
      static_cast<const SectionOperationOrder &>(from));
}


void SectionOperationOrder::MergeFrom(const SectionOperationOrder& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionOperationOrder)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_index() != 0) {
    _internal_set_index(from._internal_index());
  }
  if (from._internal_operator_type() != 0) {
    _internal_set_operator_type(from._internal_operator_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionOperationOrder::CopyFrom(const SectionOperationOrder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionOperationOrder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionOperationOrder::IsInitialized() const {
  return true;
}

void SectionOperationOrder::InternalSwap(SectionOperationOrder* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SectionOperationOrder, operator_type_)
      + sizeof(SectionOperationOrder::operator_type_)
      - PROTOBUF_FIELD_OFFSET(SectionOperationOrder, index_)>(
          reinterpret_cast<char*>(&index_),
          reinterpret_cast<char*>(&other->index_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionOperationOrder::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionOperationOrder_2eproto_getter, &descriptor_table_SectionOperationOrder_2eproto_once,
      file_level_metadata_SectionOperationOrder_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionOperationOrder* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionOperationOrder >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionOperationOrder >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
