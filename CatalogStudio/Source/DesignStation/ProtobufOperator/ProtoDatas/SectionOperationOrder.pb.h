// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionOperationOrder.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionOperationOrder_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionOperationOrder_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionOperationOrder_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionOperationOrder_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionOperationOrder_2eproto;
namespace catalog_studio_message {
class SectionOperationOrder;
struct SectionOperationOrderDefaultTypeInternal;
extern SectionOperationOrderDefaultTypeInternal _SectionOperationOrder_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionOperationOrder* Arena::CreateMaybeMessage<::catalog_studio_message::SectionOperationOrder>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionOperationOrder final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionOperationOrder) */ {
 public:
  inline SectionOperationOrder() : SectionOperationOrder(nullptr) {}
  ~SectionOperationOrder() override;
  explicit constexpr SectionOperationOrder(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionOperationOrder(const SectionOperationOrder& from);
  SectionOperationOrder(SectionOperationOrder&& from) noexcept
    : SectionOperationOrder() {
    *this = ::std::move(from);
  }

  inline SectionOperationOrder& operator=(const SectionOperationOrder& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionOperationOrder& operator=(SectionOperationOrder&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionOperationOrder& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionOperationOrder* internal_default_instance() {
    return reinterpret_cast<const SectionOperationOrder*>(
               &_SectionOperationOrder_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionOperationOrder& a, SectionOperationOrder& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionOperationOrder* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionOperationOrder* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionOperationOrder* New() const final {
    return new SectionOperationOrder();
  }

  SectionOperationOrder* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionOperationOrder>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionOperationOrder& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionOperationOrder& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionOperationOrder* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionOperationOrder";
  }
  protected:
  explicit SectionOperationOrder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIndexFieldNumber = 1,
    kOperatorTypeFieldNumber = 2,
  };
  // int32 index = 1;
  void clear_index();
  ::PROTOBUF_NAMESPACE_ID::int32 index() const;
  void set_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_index() const;
  void _internal_set_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .catalog_studio_message.SectionOperationType operator_type = 2;
  void clear_operator_type();
  ::catalog_studio_message::SectionOperationType operator_type() const;
  void set_operator_type(::catalog_studio_message::SectionOperationType value);
  private:
  ::catalog_studio_message::SectionOperationType _internal_operator_type() const;
  void _internal_set_operator_type(::catalog_studio_message::SectionOperationType value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionOperationOrder)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 index_;
  int operator_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionOperationOrder_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionOperationOrder

// int32 index = 1;
inline void SectionOperationOrder::clear_index() {
  index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionOperationOrder::_internal_index() const {
  return index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionOperationOrder::index() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperationOrder.index)
  return _internal_index();
}
inline void SectionOperationOrder::_internal_set_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  index_ = value;
}
inline void SectionOperationOrder::set_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_index(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionOperationOrder.index)
}

// .catalog_studio_message.SectionOperationType operator_type = 2;
inline void SectionOperationOrder::clear_operator_type() {
  operator_type_ = 0;
}
inline ::catalog_studio_message::SectionOperationType SectionOperationOrder::_internal_operator_type() const {
  return static_cast< ::catalog_studio_message::SectionOperationType >(operator_type_);
}
inline ::catalog_studio_message::SectionOperationType SectionOperationOrder::operator_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionOperationOrder.operator_type)
  return _internal_operator_type();
}
inline void SectionOperationOrder::_internal_set_operator_type(::catalog_studio_message::SectionOperationType value) {
  
  operator_type_ = value;
}
inline void SectionOperationOrder::set_operator_type(::catalog_studio_message::SectionOperationType value) {
  _internal_set_operator_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionOperationOrder.operator_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionOperationOrder_2eproto
