// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SectionShiftingOperation.proto

#include "SectionShiftingOperation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SectionShiftingOperation::SectionShiftingOperation(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : shift_value_()
  , id_(0){}
struct SectionShiftingOperationDefaultTypeInternal {
  constexpr SectionShiftingOperationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SectionShiftingOperationDefaultTypeInternal() {}
  union {
    SectionShiftingOperation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SectionShiftingOperationDefaultTypeInternal _SectionShiftingOperation_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SectionShiftingOperation_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SectionShiftingOperation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SectionShiftingOperation_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SectionShiftingOperation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionShiftingOperation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionShiftingOperation, id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SectionShiftingOperation, shift_value_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SectionShiftingOperation)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SectionShiftingOperation_default_instance_),
};

const char descriptor_table_protodef_SectionShiftingOperation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036SectionShiftingOperation.proto\022\026catalo"
  "g_studio_message\032\031ExpressionValuePair.pr"
  "oto\"h\n\030SectionShiftingOperation\022\n\n\002id\030\001 "
  "\001(\005\022@\n\013shift_value\030\002 \003(\0132+.catalog_studi"
  "o_message.ExpressionValuePairb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SectionShiftingOperation_2eproto_deps[1] = {
  &::descriptor_table_ExpressionValuePair_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SectionShiftingOperation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionShiftingOperation_2eproto = {
  false, false, 197, descriptor_table_protodef_SectionShiftingOperation_2eproto, "SectionShiftingOperation.proto", 
  &descriptor_table_SectionShiftingOperation_2eproto_once, descriptor_table_SectionShiftingOperation_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_SectionShiftingOperation_2eproto::offsets,
  file_level_metadata_SectionShiftingOperation_2eproto, file_level_enum_descriptors_SectionShiftingOperation_2eproto, file_level_service_descriptors_SectionShiftingOperation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SectionShiftingOperation_2eproto_getter() {
  return &descriptor_table_SectionShiftingOperation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SectionShiftingOperation_2eproto(&descriptor_table_SectionShiftingOperation_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SectionShiftingOperation::_Internal {
 public:
};

void SectionShiftingOperation::clear_shift_value() {
  shift_value_.Clear();
}
SectionShiftingOperation::SectionShiftingOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  shift_value_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SectionShiftingOperation)
}
SectionShiftingOperation::SectionShiftingOperation(const SectionShiftingOperation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      shift_value_(from.shift_value_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SectionShiftingOperation)
}

void SectionShiftingOperation::SharedCtor() {
id_ = 0;
}

SectionShiftingOperation::~SectionShiftingOperation() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SectionShiftingOperation)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SectionShiftingOperation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SectionShiftingOperation::ArenaDtor(void* object) {
  SectionShiftingOperation* _this = reinterpret_cast< SectionShiftingOperation* >(object);
  (void)_this;
}
void SectionShiftingOperation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SectionShiftingOperation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SectionShiftingOperation::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SectionShiftingOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  shift_value_.Clear();
  id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SectionShiftingOperation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ExpressionValuePair shift_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_shift_value(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SectionShiftingOperation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SectionShiftingOperation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // repeated .catalog_studio_message.ExpressionValuePair shift_value = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_shift_value_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_shift_value(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SectionShiftingOperation)
  return target;
}

size_t SectionShiftingOperation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SectionShiftingOperation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.ExpressionValuePair shift_value = 2;
  total_size += 1UL * this->_internal_shift_value_size();
  for (const auto& msg : this->shift_value_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SectionShiftingOperation::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SectionShiftingOperation::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SectionShiftingOperation::GetClassData() const { return &_class_data_; }

void SectionShiftingOperation::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SectionShiftingOperation *>(to)->MergeFrom(
      static_cast<const SectionShiftingOperation &>(from));
}


void SectionShiftingOperation::MergeFrom(const SectionShiftingOperation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SectionShiftingOperation)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  shift_value_.MergeFrom(from.shift_value_);
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SectionShiftingOperation::CopyFrom(const SectionShiftingOperation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SectionShiftingOperation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SectionShiftingOperation::IsInitialized() const {
  return true;
}

void SectionShiftingOperation::InternalSwap(SectionShiftingOperation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  shift_value_.InternalSwap(&other->shift_value_);
  swap(id_, other->id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SectionShiftingOperation::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SectionShiftingOperation_2eproto_getter, &descriptor_table_SectionShiftingOperation_2eproto_once,
      file_level_metadata_SectionShiftingOperation_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SectionShiftingOperation* Arena::CreateMaybeMessage< ::catalog_studio_message::SectionShiftingOperation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SectionShiftingOperation >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
