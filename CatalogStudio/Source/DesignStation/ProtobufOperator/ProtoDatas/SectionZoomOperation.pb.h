// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Section<PERSON>oomOperation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SectionZoomOperation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SectionZoomOperation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ExpressionValuePair.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SectionZoomOperation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SectionZoomOperation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SectionZoomOperation_2eproto;
namespace catalog_studio_message {
class SectionZoomOperation;
struct SectionZoomOperationDefaultTypeInternal;
extern SectionZoomOperationDefaultTypeInternal _SectionZoomOperation_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SectionZoomOperation* Arena::CreateMaybeMessage<::catalog_studio_message::SectionZoomOperation>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SectionZoomOperation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SectionZoomOperation) */ {
 public:
  inline SectionZoomOperation() : SectionZoomOperation(nullptr) {}
  ~SectionZoomOperation() override;
  explicit constexpr SectionZoomOperation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SectionZoomOperation(const SectionZoomOperation& from);
  SectionZoomOperation(SectionZoomOperation&& from) noexcept
    : SectionZoomOperation() {
    *this = ::std::move(from);
  }

  inline SectionZoomOperation& operator=(const SectionZoomOperation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SectionZoomOperation& operator=(SectionZoomOperation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SectionZoomOperation& default_instance() {
    return *internal_default_instance();
  }
  static inline const SectionZoomOperation* internal_default_instance() {
    return reinterpret_cast<const SectionZoomOperation*>(
               &_SectionZoomOperation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SectionZoomOperation& a, SectionZoomOperation& b) {
    a.Swap(&b);
  }
  inline void Swap(SectionZoomOperation* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SectionZoomOperation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SectionZoomOperation* New() const final {
    return new SectionZoomOperation();
  }

  SectionZoomOperation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SectionZoomOperation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SectionZoomOperation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SectionZoomOperation& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SectionZoomOperation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SectionZoomOperation";
  }
  protected:
  explicit SectionZoomOperation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kZoomValueFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // repeated .catalog_studio_message.ExpressionValuePair zoom_value = 2;
  int zoom_value_size() const;
  private:
  int _internal_zoom_value_size() const;
  public:
  void clear_zoom_value();
  ::catalog_studio_message::ExpressionValuePair* mutable_zoom_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
      mutable_zoom_value();
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_zoom_value(int index) const;
  ::catalog_studio_message::ExpressionValuePair* _internal_add_zoom_value();
  public:
  const ::catalog_studio_message::ExpressionValuePair& zoom_value(int index) const;
  ::catalog_studio_message::ExpressionValuePair* add_zoom_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
      zoom_value() const;

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SectionZoomOperation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair > zoom_value_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SectionZoomOperation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SectionZoomOperation

// int32 id = 1;
inline void SectionZoomOperation::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionZoomOperation::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SectionZoomOperation::id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionZoomOperation.id)
  return _internal_id();
}
inline void SectionZoomOperation::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void SectionZoomOperation::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SectionZoomOperation.id)
}

// repeated .catalog_studio_message.ExpressionValuePair zoom_value = 2;
inline int SectionZoomOperation::_internal_zoom_value_size() const {
  return zoom_value_.size();
}
inline int SectionZoomOperation::zoom_value_size() const {
  return _internal_zoom_value_size();
}
inline ::catalog_studio_message::ExpressionValuePair* SectionZoomOperation::mutable_zoom_value(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SectionZoomOperation.zoom_value)
  return zoom_value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >*
SectionZoomOperation::mutable_zoom_value() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SectionZoomOperation.zoom_value)
  return &zoom_value_;
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionZoomOperation::_internal_zoom_value(int index) const {
  return zoom_value_.Get(index);
}
inline const ::catalog_studio_message::ExpressionValuePair& SectionZoomOperation::zoom_value(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SectionZoomOperation.zoom_value)
  return _internal_zoom_value(index);
}
inline ::catalog_studio_message::ExpressionValuePair* SectionZoomOperation::_internal_add_zoom_value() {
  return zoom_value_.Add();
}
inline ::catalog_studio_message::ExpressionValuePair* SectionZoomOperation::add_zoom_value() {
  ::catalog_studio_message::ExpressionValuePair* _add = _internal_add_zoom_value();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SectionZoomOperation.zoom_value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ExpressionValuePair >&
SectionZoomOperation::zoom_value() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SectionZoomOperation.zoom_value)
  return zoom_value_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SectionZoomOperation_2eproto
