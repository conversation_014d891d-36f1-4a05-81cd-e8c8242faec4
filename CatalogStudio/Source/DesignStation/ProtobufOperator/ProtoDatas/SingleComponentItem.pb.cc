// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SingleComponentItem.proto

#include "SingleComponentItem.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SingleComponentItem::SingleComponentItem(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : import_mesh_()
  , section_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , thumbnail_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pakrefpath_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pakrelativepath_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , operator_section_(nullptr)
  , component_material_(nullptr)
  , section_operation_(nullptr)
  , visible_param_(nullptr)
  , single_component_location_(nullptr)
  , single_component_rotation_(nullptr)
  , single_component_scale_(nullptr)
  , component_source_(0)
{}
struct SingleComponentItemDefaultTypeInternal {
  constexpr SingleComponentItemDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SingleComponentItemDefaultTypeInternal() {}
  union {
    SingleComponentItem _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SingleComponentItemDefaultTypeInternal _SingleComponentItem_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SingleComponentItem_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SingleComponentItem_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SingleComponentItem_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SingleComponentItem_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, section_name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, thumbnail_path_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, operator_section_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, component_material_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, section_operation_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, visible_param_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, component_source_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, import_mesh_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, single_component_location_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, single_component_rotation_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, single_component_scale_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, pakrefpath_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentItem, pakrelativepath_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SingleComponentItem)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SingleComponentItem_default_instance_),
};

const char descriptor_table_protodef_SingleComponentItem_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031SingleComponentItem.proto\022\026catalog_stu"
  "dio_message\032\027ComponentEnumData.proto\032\031Ex"
  "pressionValuePair.proto\032\026CrossSectionDat"
  "a.proto\032\026SectionOperation.proto\032\027ImportM"
  "eshSection.proto\032\026LocationProperty.proto"
  "\032\026RotationProperty.proto\032\023ScaleProperty."
  "proto\"\360\005\n\023SingleComponentItem\022\024\n\014section"
  "_name\030\001 \001(\t\022\026\n\016thumbnail_path\030\002 \001(\t\022B\n\020o"
  "perator_section\030\003 \001(\0132(.catalog_studio_m"
  "essage.CrossSectionData\022G\n\022component_mat"
  "erial\030\004 \001(\0132+.catalog_studio_message.Exp"
  "ressionValuePair\022C\n\021section_operation\030\005 "
  "\001(\0132(.catalog_studio_message.SectionOper"
  "ation\022B\n\rvisible_param\030\006 \001(\0132+.catalog_s"
  "tudio_message.ExpressionValuePair\022G\n\020com"
  "ponent_source\030\007 \001(\0162-.catalog_studio_mes"
  "sage.SingleComponentSource\022>\n\013import_mes"
  "h\030\010 \003(\0132).catalog_studio_message.ImportM"
  "eshSection\022K\n\031single_component_location\030"
  "\t \001(\0132(.catalog_studio_message.LocationP"
  "roperty\022K\n\031single_component_rotation\030\n \001"
  "(\0132(.catalog_studio_message.RotationProp"
  "erty\022E\n\026single_component_scale\030\013 \001(\0132%.c"
  "atalog_studio_message.ScaleProperty\022\022\n\nP"
  "akRefPath\030\014 \001(\t\022\027\n\017PakRelativePath\030\r \001(\t"
  "b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SingleComponentItem_2eproto_deps[8] = {
  &::descriptor_table_ComponentEnumData_2eproto,
  &::descriptor_table_CrossSectionData_2eproto,
  &::descriptor_table_ExpressionValuePair_2eproto,
  &::descriptor_table_ImportMeshSection_2eproto,
  &::descriptor_table_LocationProperty_2eproto,
  &::descriptor_table_RotationProperty_2eproto,
  &::descriptor_table_ScaleProperty_2eproto,
  &::descriptor_table_SectionOperation_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SingleComponentItem_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SingleComponentItem_2eproto = {
  false, false, 1008, descriptor_table_protodef_SingleComponentItem_2eproto, "SingleComponentItem.proto", 
  &descriptor_table_SingleComponentItem_2eproto_once, descriptor_table_SingleComponentItem_2eproto_deps, 8, 1,
  schemas, file_default_instances, TableStruct_SingleComponentItem_2eproto::offsets,
  file_level_metadata_SingleComponentItem_2eproto, file_level_enum_descriptors_SingleComponentItem_2eproto, file_level_service_descriptors_SingleComponentItem_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SingleComponentItem_2eproto_getter() {
  return &descriptor_table_SingleComponentItem_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SingleComponentItem_2eproto(&descriptor_table_SingleComponentItem_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SingleComponentItem::_Internal {
 public:
  static const ::catalog_studio_message::CrossSectionData& operator_section(const SingleComponentItem* msg);
  static const ::catalog_studio_message::ExpressionValuePair& component_material(const SingleComponentItem* msg);
  static const ::catalog_studio_message::SectionOperation& section_operation(const SingleComponentItem* msg);
  static const ::catalog_studio_message::ExpressionValuePair& visible_param(const SingleComponentItem* msg);
  static const ::catalog_studio_message::LocationProperty& single_component_location(const SingleComponentItem* msg);
  static const ::catalog_studio_message::RotationProperty& single_component_rotation(const SingleComponentItem* msg);
  static const ::catalog_studio_message::ScaleProperty& single_component_scale(const SingleComponentItem* msg);
};

const ::catalog_studio_message::CrossSectionData&
SingleComponentItem::_Internal::operator_section(const SingleComponentItem* msg) {
  return *msg->operator_section_;
}
const ::catalog_studio_message::ExpressionValuePair&
SingleComponentItem::_Internal::component_material(const SingleComponentItem* msg) {
  return *msg->component_material_;
}
const ::catalog_studio_message::SectionOperation&
SingleComponentItem::_Internal::section_operation(const SingleComponentItem* msg) {
  return *msg->section_operation_;
}
const ::catalog_studio_message::ExpressionValuePair&
SingleComponentItem::_Internal::visible_param(const SingleComponentItem* msg) {
  return *msg->visible_param_;
}
const ::catalog_studio_message::LocationProperty&
SingleComponentItem::_Internal::single_component_location(const SingleComponentItem* msg) {
  return *msg->single_component_location_;
}
const ::catalog_studio_message::RotationProperty&
SingleComponentItem::_Internal::single_component_rotation(const SingleComponentItem* msg) {
  return *msg->single_component_rotation_;
}
const ::catalog_studio_message::ScaleProperty&
SingleComponentItem::_Internal::single_component_scale(const SingleComponentItem* msg) {
  return *msg->single_component_scale_;
}
void SingleComponentItem::clear_operator_section() {
  if (GetArenaForAllocation() == nullptr && operator_section_ != nullptr) {
    delete operator_section_;
  }
  operator_section_ = nullptr;
}
void SingleComponentItem::clear_component_material() {
  if (GetArenaForAllocation() == nullptr && component_material_ != nullptr) {
    delete component_material_;
  }
  component_material_ = nullptr;
}
void SingleComponentItem::clear_section_operation() {
  if (GetArenaForAllocation() == nullptr && section_operation_ != nullptr) {
    delete section_operation_;
  }
  section_operation_ = nullptr;
}
void SingleComponentItem::clear_visible_param() {
  if (GetArenaForAllocation() == nullptr && visible_param_ != nullptr) {
    delete visible_param_;
  }
  visible_param_ = nullptr;
}
void SingleComponentItem::clear_import_mesh() {
  import_mesh_.Clear();
}
void SingleComponentItem::clear_single_component_location() {
  if (GetArenaForAllocation() == nullptr && single_component_location_ != nullptr) {
    delete single_component_location_;
  }
  single_component_location_ = nullptr;
}
void SingleComponentItem::clear_single_component_rotation() {
  if (GetArenaForAllocation() == nullptr && single_component_rotation_ != nullptr) {
    delete single_component_rotation_;
  }
  single_component_rotation_ = nullptr;
}
void SingleComponentItem::clear_single_component_scale() {
  if (GetArenaForAllocation() == nullptr && single_component_scale_ != nullptr) {
    delete single_component_scale_;
  }
  single_component_scale_ = nullptr;
}
SingleComponentItem::SingleComponentItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  import_mesh_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SingleComponentItem)
}
SingleComponentItem::SingleComponentItem(const SingleComponentItem& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      import_mesh_(from.import_mesh_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  section_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_section_name().empty()) {
    section_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_section_name(), 
      GetArenaForAllocation());
  }
  thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_thumbnail_path().empty()) {
    thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_thumbnail_path(), 
      GetArenaForAllocation());
  }
  pakrefpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_pakrefpath().empty()) {
    pakrefpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pakrefpath(), 
      GetArenaForAllocation());
  }
  pakrelativepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_pakrelativepath().empty()) {
    pakrelativepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pakrelativepath(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_operator_section()) {
    operator_section_ = new ::catalog_studio_message::CrossSectionData(*from.operator_section_);
  } else {
    operator_section_ = nullptr;
  }
  if (from._internal_has_component_material()) {
    component_material_ = new ::catalog_studio_message::ExpressionValuePair(*from.component_material_);
  } else {
    component_material_ = nullptr;
  }
  if (from._internal_has_section_operation()) {
    section_operation_ = new ::catalog_studio_message::SectionOperation(*from.section_operation_);
  } else {
    section_operation_ = nullptr;
  }
  if (from._internal_has_visible_param()) {
    visible_param_ = new ::catalog_studio_message::ExpressionValuePair(*from.visible_param_);
  } else {
    visible_param_ = nullptr;
  }
  if (from._internal_has_single_component_location()) {
    single_component_location_ = new ::catalog_studio_message::LocationProperty(*from.single_component_location_);
  } else {
    single_component_location_ = nullptr;
  }
  if (from._internal_has_single_component_rotation()) {
    single_component_rotation_ = new ::catalog_studio_message::RotationProperty(*from.single_component_rotation_);
  } else {
    single_component_rotation_ = nullptr;
  }
  if (from._internal_has_single_component_scale()) {
    single_component_scale_ = new ::catalog_studio_message::ScaleProperty(*from.single_component_scale_);
  } else {
    single_component_scale_ = nullptr;
  }
  component_source_ = from.component_source_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SingleComponentItem)
}

void SingleComponentItem::SharedCtor() {
section_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
pakrefpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
pakrelativepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&operator_section_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&component_source_) -
    reinterpret_cast<char*>(&operator_section_)) + sizeof(component_source_));
}

SingleComponentItem::~SingleComponentItem() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SingleComponentItem)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SingleComponentItem::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  section_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  thumbnail_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pakrefpath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pakrelativepath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete operator_section_;
  if (this != internal_default_instance()) delete component_material_;
  if (this != internal_default_instance()) delete section_operation_;
  if (this != internal_default_instance()) delete visible_param_;
  if (this != internal_default_instance()) delete single_component_location_;
  if (this != internal_default_instance()) delete single_component_rotation_;
  if (this != internal_default_instance()) delete single_component_scale_;
}

void SingleComponentItem::ArenaDtor(void* object) {
  SingleComponentItem* _this = reinterpret_cast< SingleComponentItem* >(object);
  (void)_this;
}
void SingleComponentItem::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SingleComponentItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SingleComponentItem::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SingleComponentItem)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  import_mesh_.Clear();
  section_name_.ClearToEmpty();
  thumbnail_path_.ClearToEmpty();
  pakrefpath_.ClearToEmpty();
  pakrelativepath_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && operator_section_ != nullptr) {
    delete operator_section_;
  }
  operator_section_ = nullptr;
  if (GetArenaForAllocation() == nullptr && component_material_ != nullptr) {
    delete component_material_;
  }
  component_material_ = nullptr;
  if (GetArenaForAllocation() == nullptr && section_operation_ != nullptr) {
    delete section_operation_;
  }
  section_operation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && visible_param_ != nullptr) {
    delete visible_param_;
  }
  visible_param_ = nullptr;
  if (GetArenaForAllocation() == nullptr && single_component_location_ != nullptr) {
    delete single_component_location_;
  }
  single_component_location_ = nullptr;
  if (GetArenaForAllocation() == nullptr && single_component_rotation_ != nullptr) {
    delete single_component_rotation_;
  }
  single_component_rotation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && single_component_scale_ != nullptr) {
    delete single_component_scale_;
  }
  single_component_scale_ = nullptr;
  component_source_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SingleComponentItem::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string section_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_section_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.SingleComponentItem.section_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string thumbnail_path = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_thumbnail_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.SingleComponentItem.thumbnail_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.CrossSectionData operator_section = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_operator_section(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair component_material = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_component_material(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SectionOperation section_operation = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_section_operation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ExpressionValuePair visible_param = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_visible_param(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.SingleComponentSource component_source = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_component_source(static_cast<::catalog_studio_message::SingleComponentSource>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ImportMeshSection import_mesh = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_import_mesh(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.LocationProperty single_component_location = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_single_component_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.RotationProperty single_component_rotation = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_single_component_rotation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .catalog_studio_message.ScaleProperty single_component_scale = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_single_component_scale(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string PakRefPath = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_pakrefpath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.SingleComponentItem.PakRefPath"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string PakRelativePath = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          auto str = _internal_mutable_pakrelativepath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.SingleComponentItem.PakRelativePath"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SingleComponentItem::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SingleComponentItem)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string section_name = 1;
  if (!this->_internal_section_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_section_name().data(), static_cast<int>(this->_internal_section_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.SingleComponentItem.section_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_section_name(), target);
  }

  // string thumbnail_path = 2;
  if (!this->_internal_thumbnail_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_thumbnail_path().data(), static_cast<int>(this->_internal_thumbnail_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.SingleComponentItem.thumbnail_path");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_thumbnail_path(), target);
  }

  // .catalog_studio_message.CrossSectionData operator_section = 3;
  if (this->_internal_has_operator_section()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::operator_section(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair component_material = 4;
  if (this->_internal_has_component_material()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::component_material(this), target, stream);
  }

  // .catalog_studio_message.SectionOperation section_operation = 5;
  if (this->_internal_has_section_operation()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::section_operation(this), target, stream);
  }

  // .catalog_studio_message.ExpressionValuePair visible_param = 6;
  if (this->_internal_has_visible_param()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::visible_param(this), target, stream);
  }

  // .catalog_studio_message.SingleComponentSource component_source = 7;
  if (this->_internal_component_source() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_component_source(), target);
  }

  // repeated .catalog_studio_message.ImportMeshSection import_mesh = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_import_mesh_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_import_mesh(i), target, stream);
  }

  // .catalog_studio_message.LocationProperty single_component_location = 9;
  if (this->_internal_has_single_component_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::single_component_location(this), target, stream);
  }

  // .catalog_studio_message.RotationProperty single_component_rotation = 10;
  if (this->_internal_has_single_component_rotation()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::single_component_rotation(this), target, stream);
  }

  // .catalog_studio_message.ScaleProperty single_component_scale = 11;
  if (this->_internal_has_single_component_scale()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::single_component_scale(this), target, stream);
  }

  // string PakRefPath = 12;
  if (!this->_internal_pakrefpath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_pakrefpath().data(), static_cast<int>(this->_internal_pakrefpath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.SingleComponentItem.PakRefPath");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_pakrefpath(), target);
  }

  // string PakRelativePath = 13;
  if (!this->_internal_pakrelativepath().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_pakrelativepath().data(), static_cast<int>(this->_internal_pakrelativepath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.SingleComponentItem.PakRelativePath");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_pakrelativepath(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SingleComponentItem)
  return target;
}

size_t SingleComponentItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SingleComponentItem)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.ImportMeshSection import_mesh = 8;
  total_size += 1UL * this->_internal_import_mesh_size();
  for (const auto& msg : this->import_mesh_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string section_name = 1;
  if (!this->_internal_section_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_section_name());
  }

  // string thumbnail_path = 2;
  if (!this->_internal_thumbnail_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_thumbnail_path());
  }

  // string PakRefPath = 12;
  if (!this->_internal_pakrefpath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pakrefpath());
  }

  // string PakRelativePath = 13;
  if (!this->_internal_pakrelativepath().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pakrelativepath());
  }

  // .catalog_studio_message.CrossSectionData operator_section = 3;
  if (this->_internal_has_operator_section()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *operator_section_);
  }

  // .catalog_studio_message.ExpressionValuePair component_material = 4;
  if (this->_internal_has_component_material()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *component_material_);
  }

  // .catalog_studio_message.SectionOperation section_operation = 5;
  if (this->_internal_has_section_operation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *section_operation_);
  }

  // .catalog_studio_message.ExpressionValuePair visible_param = 6;
  if (this->_internal_has_visible_param()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *visible_param_);
  }

  // .catalog_studio_message.LocationProperty single_component_location = 9;
  if (this->_internal_has_single_component_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *single_component_location_);
  }

  // .catalog_studio_message.RotationProperty single_component_rotation = 10;
  if (this->_internal_has_single_component_rotation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *single_component_rotation_);
  }

  // .catalog_studio_message.ScaleProperty single_component_scale = 11;
  if (this->_internal_has_single_component_scale()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *single_component_scale_);
  }

  // .catalog_studio_message.SingleComponentSource component_source = 7;
  if (this->_internal_component_source() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_component_source());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SingleComponentItem::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SingleComponentItem::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SingleComponentItem::GetClassData() const { return &_class_data_; }

void SingleComponentItem::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SingleComponentItem *>(to)->MergeFrom(
      static_cast<const SingleComponentItem &>(from));
}


void SingleComponentItem::MergeFrom(const SingleComponentItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SingleComponentItem)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  import_mesh_.MergeFrom(from.import_mesh_);
  if (!from._internal_section_name().empty()) {
    _internal_set_section_name(from._internal_section_name());
  }
  if (!from._internal_thumbnail_path().empty()) {
    _internal_set_thumbnail_path(from._internal_thumbnail_path());
  }
  if (!from._internal_pakrefpath().empty()) {
    _internal_set_pakrefpath(from._internal_pakrefpath());
  }
  if (!from._internal_pakrelativepath().empty()) {
    _internal_set_pakrelativepath(from._internal_pakrelativepath());
  }
  if (from._internal_has_operator_section()) {
    _internal_mutable_operator_section()->::catalog_studio_message::CrossSectionData::MergeFrom(from._internal_operator_section());
  }
  if (from._internal_has_component_material()) {
    _internal_mutable_component_material()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_component_material());
  }
  if (from._internal_has_section_operation()) {
    _internal_mutable_section_operation()->::catalog_studio_message::SectionOperation::MergeFrom(from._internal_section_operation());
  }
  if (from._internal_has_visible_param()) {
    _internal_mutable_visible_param()->::catalog_studio_message::ExpressionValuePair::MergeFrom(from._internal_visible_param());
  }
  if (from._internal_has_single_component_location()) {
    _internal_mutable_single_component_location()->::catalog_studio_message::LocationProperty::MergeFrom(from._internal_single_component_location());
  }
  if (from._internal_has_single_component_rotation()) {
    _internal_mutable_single_component_rotation()->::catalog_studio_message::RotationProperty::MergeFrom(from._internal_single_component_rotation());
  }
  if (from._internal_has_single_component_scale()) {
    _internal_mutable_single_component_scale()->::catalog_studio_message::ScaleProperty::MergeFrom(from._internal_single_component_scale());
  }
  if (from._internal_component_source() != 0) {
    _internal_set_component_source(from._internal_component_source());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SingleComponentItem::CopyFrom(const SingleComponentItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SingleComponentItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SingleComponentItem::IsInitialized() const {
  return true;
}

void SingleComponentItem::InternalSwap(SingleComponentItem* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  import_mesh_.InternalSwap(&other->import_mesh_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &section_name_, lhs_arena,
      &other->section_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &thumbnail_path_, lhs_arena,
      &other->thumbnail_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &pakrefpath_, lhs_arena,
      &other->pakrefpath_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &pakrelativepath_, lhs_arena,
      &other->pakrelativepath_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SingleComponentItem, component_source_)
      + sizeof(SingleComponentItem::component_source_)
      - PROTOBUF_FIELD_OFFSET(SingleComponentItem, operator_section_)>(
          reinterpret_cast<char*>(&operator_section_),
          reinterpret_cast<char*>(&other->operator_section_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SingleComponentItem::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SingleComponentItem_2eproto_getter, &descriptor_table_SingleComponentItem_2eproto_once,
      file_level_metadata_SingleComponentItem_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SingleComponentItem* Arena::CreateMaybeMessage< ::catalog_studio_message::SingleComponentItem >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SingleComponentItem >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
