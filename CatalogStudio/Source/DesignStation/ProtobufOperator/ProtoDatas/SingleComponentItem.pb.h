// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SingleComponentItem.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SingleComponentItem_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SingleComponentItem_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ComponentEnumData.pb.h"
#include "ExpressionValuePair.pb.h"
#include "CrossSectionData.pb.h"
#include "SectionOperation.pb.h"
#include "ImportMeshSection.pb.h"
#include "LocationProperty.pb.h"
#include "RotationProperty.pb.h"
#include "ScaleProperty.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SingleComponentItem_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SingleComponentItem_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SingleComponentItem_2eproto;
namespace catalog_studio_message {
class SingleComponentItem;
struct SingleComponentItemDefaultTypeInternal;
extern SingleComponentItemDefaultTypeInternal _SingleComponentItem_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SingleComponentItem* Arena::CreateMaybeMessage<::catalog_studio_message::SingleComponentItem>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SingleComponentItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SingleComponentItem) */ {
 public:
  inline SingleComponentItem() : SingleComponentItem(nullptr) {}
  ~SingleComponentItem() override;
  explicit constexpr SingleComponentItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SingleComponentItem(const SingleComponentItem& from);
  SingleComponentItem(SingleComponentItem&& from) noexcept
    : SingleComponentItem() {
    *this = ::std::move(from);
  }

  inline SingleComponentItem& operator=(const SingleComponentItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline SingleComponentItem& operator=(SingleComponentItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SingleComponentItem& default_instance() {
    return *internal_default_instance();
  }
  static inline const SingleComponentItem* internal_default_instance() {
    return reinterpret_cast<const SingleComponentItem*>(
               &_SingleComponentItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SingleComponentItem& a, SingleComponentItem& b) {
    a.Swap(&b);
  }
  inline void Swap(SingleComponentItem* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SingleComponentItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SingleComponentItem* New() const final {
    return new SingleComponentItem();
  }

  SingleComponentItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SingleComponentItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SingleComponentItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SingleComponentItem& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SingleComponentItem* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SingleComponentItem";
  }
  protected:
  explicit SingleComponentItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kImportMeshFieldNumber = 8,
    kSectionNameFieldNumber = 1,
    kThumbnailPathFieldNumber = 2,
    kPakRefPathFieldNumber = 12,
    kPakRelativePathFieldNumber = 13,
    kOperatorSectionFieldNumber = 3,
    kComponentMaterialFieldNumber = 4,
    kSectionOperationFieldNumber = 5,
    kVisibleParamFieldNumber = 6,
    kSingleComponentLocationFieldNumber = 9,
    kSingleComponentRotationFieldNumber = 10,
    kSingleComponentScaleFieldNumber = 11,
    kComponentSourceFieldNumber = 7,
  };
  // repeated .catalog_studio_message.ImportMeshSection import_mesh = 8;
  int import_mesh_size() const;
  private:
  int _internal_import_mesh_size() const;
  public:
  void clear_import_mesh();
  ::catalog_studio_message::ImportMeshSection* mutable_import_mesh(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ImportMeshSection >*
      mutable_import_mesh();
  private:
  const ::catalog_studio_message::ImportMeshSection& _internal_import_mesh(int index) const;
  ::catalog_studio_message::ImportMeshSection* _internal_add_import_mesh();
  public:
  const ::catalog_studio_message::ImportMeshSection& import_mesh(int index) const;
  ::catalog_studio_message::ImportMeshSection* add_import_mesh();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ImportMeshSection >&
      import_mesh() const;

  // string section_name = 1;
  void clear_section_name();
  const std::string& section_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_section_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_section_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_section_name();
  void set_allocated_section_name(std::string* section_name);
  private:
  const std::string& _internal_section_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_section_name(const std::string& value);
  std::string* _internal_mutable_section_name();
  public:

  // string thumbnail_path = 2;
  void clear_thumbnail_path();
  const std::string& thumbnail_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_thumbnail_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_thumbnail_path();
  PROTOBUF_MUST_USE_RESULT std::string* release_thumbnail_path();
  void set_allocated_thumbnail_path(std::string* thumbnail_path);
  private:
  const std::string& _internal_thumbnail_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_thumbnail_path(const std::string& value);
  std::string* _internal_mutable_thumbnail_path();
  public:

  // string PakRefPath = 12;
  void clear_pakrefpath();
  const std::string& pakrefpath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pakrefpath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pakrefpath();
  PROTOBUF_MUST_USE_RESULT std::string* release_pakrefpath();
  void set_allocated_pakrefpath(std::string* pakrefpath);
  private:
  const std::string& _internal_pakrefpath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pakrefpath(const std::string& value);
  std::string* _internal_mutable_pakrefpath();
  public:

  // string PakRelativePath = 13;
  void clear_pakrelativepath();
  const std::string& pakrelativepath() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pakrelativepath(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pakrelativepath();
  PROTOBUF_MUST_USE_RESULT std::string* release_pakrelativepath();
  void set_allocated_pakrelativepath(std::string* pakrelativepath);
  private:
  const std::string& _internal_pakrelativepath() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pakrelativepath(const std::string& value);
  std::string* _internal_mutable_pakrelativepath();
  public:

  // .catalog_studio_message.CrossSectionData operator_section = 3;
  bool has_operator_section() const;
  private:
  bool _internal_has_operator_section() const;
  public:
  void clear_operator_section();
  const ::catalog_studio_message::CrossSectionData& operator_section() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::CrossSectionData* release_operator_section();
  ::catalog_studio_message::CrossSectionData* mutable_operator_section();
  void set_allocated_operator_section(::catalog_studio_message::CrossSectionData* operator_section);
  private:
  const ::catalog_studio_message::CrossSectionData& _internal_operator_section() const;
  ::catalog_studio_message::CrossSectionData* _internal_mutable_operator_section();
  public:
  void unsafe_arena_set_allocated_operator_section(
      ::catalog_studio_message::CrossSectionData* operator_section);
  ::catalog_studio_message::CrossSectionData* unsafe_arena_release_operator_section();

  // .catalog_studio_message.ExpressionValuePair component_material = 4;
  bool has_component_material() const;
  private:
  bool _internal_has_component_material() const;
  public:
  void clear_component_material();
  const ::catalog_studio_message::ExpressionValuePair& component_material() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_component_material();
  ::catalog_studio_message::ExpressionValuePair* mutable_component_material();
  void set_allocated_component_material(::catalog_studio_message::ExpressionValuePair* component_material);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_component_material() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_component_material();
  public:
  void unsafe_arena_set_allocated_component_material(
      ::catalog_studio_message::ExpressionValuePair* component_material);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_component_material();

  // .catalog_studio_message.SectionOperation section_operation = 5;
  bool has_section_operation() const;
  private:
  bool _internal_has_section_operation() const;
  public:
  void clear_section_operation();
  const ::catalog_studio_message::SectionOperation& section_operation() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::SectionOperation* release_section_operation();
  ::catalog_studio_message::SectionOperation* mutable_section_operation();
  void set_allocated_section_operation(::catalog_studio_message::SectionOperation* section_operation);
  private:
  const ::catalog_studio_message::SectionOperation& _internal_section_operation() const;
  ::catalog_studio_message::SectionOperation* _internal_mutable_section_operation();
  public:
  void unsafe_arena_set_allocated_section_operation(
      ::catalog_studio_message::SectionOperation* section_operation);
  ::catalog_studio_message::SectionOperation* unsafe_arena_release_section_operation();

  // .catalog_studio_message.ExpressionValuePair visible_param = 6;
  bool has_visible_param() const;
  private:
  bool _internal_has_visible_param() const;
  public:
  void clear_visible_param();
  const ::catalog_studio_message::ExpressionValuePair& visible_param() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ExpressionValuePair* release_visible_param();
  ::catalog_studio_message::ExpressionValuePair* mutable_visible_param();
  void set_allocated_visible_param(::catalog_studio_message::ExpressionValuePair* visible_param);
  private:
  const ::catalog_studio_message::ExpressionValuePair& _internal_visible_param() const;
  ::catalog_studio_message::ExpressionValuePair* _internal_mutable_visible_param();
  public:
  void unsafe_arena_set_allocated_visible_param(
      ::catalog_studio_message::ExpressionValuePair* visible_param);
  ::catalog_studio_message::ExpressionValuePair* unsafe_arena_release_visible_param();

  // .catalog_studio_message.LocationProperty single_component_location = 9;
  bool has_single_component_location() const;
  private:
  bool _internal_has_single_component_location() const;
  public:
  void clear_single_component_location();
  const ::catalog_studio_message::LocationProperty& single_component_location() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::LocationProperty* release_single_component_location();
  ::catalog_studio_message::LocationProperty* mutable_single_component_location();
  void set_allocated_single_component_location(::catalog_studio_message::LocationProperty* single_component_location);
  private:
  const ::catalog_studio_message::LocationProperty& _internal_single_component_location() const;
  ::catalog_studio_message::LocationProperty* _internal_mutable_single_component_location();
  public:
  void unsafe_arena_set_allocated_single_component_location(
      ::catalog_studio_message::LocationProperty* single_component_location);
  ::catalog_studio_message::LocationProperty* unsafe_arena_release_single_component_location();

  // .catalog_studio_message.RotationProperty single_component_rotation = 10;
  bool has_single_component_rotation() const;
  private:
  bool _internal_has_single_component_rotation() const;
  public:
  void clear_single_component_rotation();
  const ::catalog_studio_message::RotationProperty& single_component_rotation() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::RotationProperty* release_single_component_rotation();
  ::catalog_studio_message::RotationProperty* mutable_single_component_rotation();
  void set_allocated_single_component_rotation(::catalog_studio_message::RotationProperty* single_component_rotation);
  private:
  const ::catalog_studio_message::RotationProperty& _internal_single_component_rotation() const;
  ::catalog_studio_message::RotationProperty* _internal_mutable_single_component_rotation();
  public:
  void unsafe_arena_set_allocated_single_component_rotation(
      ::catalog_studio_message::RotationProperty* single_component_rotation);
  ::catalog_studio_message::RotationProperty* unsafe_arena_release_single_component_rotation();

  // .catalog_studio_message.ScaleProperty single_component_scale = 11;
  bool has_single_component_scale() const;
  private:
  bool _internal_has_single_component_scale() const;
  public:
  void clear_single_component_scale();
  const ::catalog_studio_message::ScaleProperty& single_component_scale() const;
  PROTOBUF_MUST_USE_RESULT ::catalog_studio_message::ScaleProperty* release_single_component_scale();
  ::catalog_studio_message::ScaleProperty* mutable_single_component_scale();
  void set_allocated_single_component_scale(::catalog_studio_message::ScaleProperty* single_component_scale);
  private:
  const ::catalog_studio_message::ScaleProperty& _internal_single_component_scale() const;
  ::catalog_studio_message::ScaleProperty* _internal_mutable_single_component_scale();
  public:
  void unsafe_arena_set_allocated_single_component_scale(
      ::catalog_studio_message::ScaleProperty* single_component_scale);
  ::catalog_studio_message::ScaleProperty* unsafe_arena_release_single_component_scale();

  // .catalog_studio_message.SingleComponentSource component_source = 7;
  void clear_component_source();
  ::catalog_studio_message::SingleComponentSource component_source() const;
  void set_component_source(::catalog_studio_message::SingleComponentSource value);
  private:
  ::catalog_studio_message::SingleComponentSource _internal_component_source() const;
  void _internal_set_component_source(::catalog_studio_message::SingleComponentSource value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SingleComponentItem)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ImportMeshSection > import_mesh_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr section_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr thumbnail_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pakrefpath_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pakrelativepath_;
  ::catalog_studio_message::CrossSectionData* operator_section_;
  ::catalog_studio_message::ExpressionValuePair* component_material_;
  ::catalog_studio_message::SectionOperation* section_operation_;
  ::catalog_studio_message::ExpressionValuePair* visible_param_;
  ::catalog_studio_message::LocationProperty* single_component_location_;
  ::catalog_studio_message::RotationProperty* single_component_rotation_;
  ::catalog_studio_message::ScaleProperty* single_component_scale_;
  int component_source_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SingleComponentItem_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SingleComponentItem

// string section_name = 1;
inline void SingleComponentItem::clear_section_name() {
  section_name_.ClearToEmpty();
}
inline const std::string& SingleComponentItem::section_name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.section_name)
  return _internal_section_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SingleComponentItem::set_section_name(ArgT0&& arg0, ArgT... args) {
 
 section_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentItem.section_name)
}
inline std::string* SingleComponentItem::mutable_section_name() {
  std::string* _s = _internal_mutable_section_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.section_name)
  return _s;
}
inline const std::string& SingleComponentItem::_internal_section_name() const {
  return section_name_.Get();
}
inline void SingleComponentItem::_internal_set_section_name(const std::string& value) {
  
  section_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::_internal_mutable_section_name() {
  
  return section_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::release_section_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.section_name)
  return section_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SingleComponentItem::set_allocated_section_name(std::string* section_name) {
  if (section_name != nullptr) {
    
  } else {
    
  }
  section_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), section_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.section_name)
}

// string thumbnail_path = 2;
inline void SingleComponentItem::clear_thumbnail_path() {
  thumbnail_path_.ClearToEmpty();
}
inline const std::string& SingleComponentItem::thumbnail_path() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.thumbnail_path)
  return _internal_thumbnail_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SingleComponentItem::set_thumbnail_path(ArgT0&& arg0, ArgT... args) {
 
 thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentItem.thumbnail_path)
}
inline std::string* SingleComponentItem::mutable_thumbnail_path() {
  std::string* _s = _internal_mutable_thumbnail_path();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.thumbnail_path)
  return _s;
}
inline const std::string& SingleComponentItem::_internal_thumbnail_path() const {
  return thumbnail_path_.Get();
}
inline void SingleComponentItem::_internal_set_thumbnail_path(const std::string& value) {
  
  thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::_internal_mutable_thumbnail_path() {
  
  return thumbnail_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::release_thumbnail_path() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.thumbnail_path)
  return thumbnail_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SingleComponentItem::set_allocated_thumbnail_path(std::string* thumbnail_path) {
  if (thumbnail_path != nullptr) {
    
  } else {
    
  }
  thumbnail_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), thumbnail_path,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.thumbnail_path)
}

// .catalog_studio_message.CrossSectionData operator_section = 3;
inline bool SingleComponentItem::_internal_has_operator_section() const {
  return this != internal_default_instance() && operator_section_ != nullptr;
}
inline bool SingleComponentItem::has_operator_section() const {
  return _internal_has_operator_section();
}
inline const ::catalog_studio_message::CrossSectionData& SingleComponentItem::_internal_operator_section() const {
  const ::catalog_studio_message::CrossSectionData* p = operator_section_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::CrossSectionData&>(
      ::catalog_studio_message::_CrossSectionData_default_instance_);
}
inline const ::catalog_studio_message::CrossSectionData& SingleComponentItem::operator_section() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.operator_section)
  return _internal_operator_section();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_operator_section(
    ::catalog_studio_message::CrossSectionData* operator_section) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(operator_section_);
  }
  operator_section_ = operator_section;
  if (operator_section) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.operator_section)
}
inline ::catalog_studio_message::CrossSectionData* SingleComponentItem::release_operator_section() {
  
  ::catalog_studio_message::CrossSectionData* temp = operator_section_;
  operator_section_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::CrossSectionData* SingleComponentItem::unsafe_arena_release_operator_section() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.operator_section)
  
  ::catalog_studio_message::CrossSectionData* temp = operator_section_;
  operator_section_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::CrossSectionData* SingleComponentItem::_internal_mutable_operator_section() {
  
  if (operator_section_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::CrossSectionData>(GetArenaForAllocation());
    operator_section_ = p;
  }
  return operator_section_;
}
inline ::catalog_studio_message::CrossSectionData* SingleComponentItem::mutable_operator_section() {
  ::catalog_studio_message::CrossSectionData* _msg = _internal_mutable_operator_section();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.operator_section)
  return _msg;
}
inline void SingleComponentItem::set_allocated_operator_section(::catalog_studio_message::CrossSectionData* operator_section) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(operator_section_);
  }
  if (operator_section) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(operator_section));
    if (message_arena != submessage_arena) {
      operator_section = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, operator_section, submessage_arena);
    }
    
  } else {
    
  }
  operator_section_ = operator_section;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.operator_section)
}

// .catalog_studio_message.ExpressionValuePair component_material = 4;
inline bool SingleComponentItem::_internal_has_component_material() const {
  return this != internal_default_instance() && component_material_ != nullptr;
}
inline bool SingleComponentItem::has_component_material() const {
  return _internal_has_component_material();
}
inline const ::catalog_studio_message::ExpressionValuePair& SingleComponentItem::_internal_component_material() const {
  const ::catalog_studio_message::ExpressionValuePair* p = component_material_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SingleComponentItem::component_material() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.component_material)
  return _internal_component_material();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_component_material(
    ::catalog_studio_message::ExpressionValuePair* component_material) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_material_);
  }
  component_material_ = component_material;
  if (component_material) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.component_material)
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::release_component_material() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_material_;
  component_material_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::unsafe_arena_release_component_material() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.component_material)
  
  ::catalog_studio_message::ExpressionValuePair* temp = component_material_;
  component_material_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::_internal_mutable_component_material() {
  
  if (component_material_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    component_material_ = p;
  }
  return component_material_;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::mutable_component_material() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_component_material();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.component_material)
  return _msg;
}
inline void SingleComponentItem::set_allocated_component_material(::catalog_studio_message::ExpressionValuePair* component_material) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_material_);
  }
  if (component_material) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(component_material));
    if (message_arena != submessage_arena) {
      component_material = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, component_material, submessage_arena);
    }
    
  } else {
    
  }
  component_material_ = component_material;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.component_material)
}

// .catalog_studio_message.SectionOperation section_operation = 5;
inline bool SingleComponentItem::_internal_has_section_operation() const {
  return this != internal_default_instance() && section_operation_ != nullptr;
}
inline bool SingleComponentItem::has_section_operation() const {
  return _internal_has_section_operation();
}
inline const ::catalog_studio_message::SectionOperation& SingleComponentItem::_internal_section_operation() const {
  const ::catalog_studio_message::SectionOperation* p = section_operation_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::SectionOperation&>(
      ::catalog_studio_message::_SectionOperation_default_instance_);
}
inline const ::catalog_studio_message::SectionOperation& SingleComponentItem::section_operation() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.section_operation)
  return _internal_section_operation();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_section_operation(
    ::catalog_studio_message::SectionOperation* section_operation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_operation_);
  }
  section_operation_ = section_operation;
  if (section_operation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.section_operation)
}
inline ::catalog_studio_message::SectionOperation* SingleComponentItem::release_section_operation() {
  
  ::catalog_studio_message::SectionOperation* temp = section_operation_;
  section_operation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::SectionOperation* SingleComponentItem::unsafe_arena_release_section_operation() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.section_operation)
  
  ::catalog_studio_message::SectionOperation* temp = section_operation_;
  section_operation_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::SectionOperation* SingleComponentItem::_internal_mutable_section_operation() {
  
  if (section_operation_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::SectionOperation>(GetArenaForAllocation());
    section_operation_ = p;
  }
  return section_operation_;
}
inline ::catalog_studio_message::SectionOperation* SingleComponentItem::mutable_section_operation() {
  ::catalog_studio_message::SectionOperation* _msg = _internal_mutable_section_operation();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.section_operation)
  return _msg;
}
inline void SingleComponentItem::set_allocated_section_operation(::catalog_studio_message::SectionOperation* section_operation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_operation_);
  }
  if (section_operation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(section_operation));
    if (message_arena != submessage_arena) {
      section_operation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, section_operation, submessage_arena);
    }
    
  } else {
    
  }
  section_operation_ = section_operation;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.section_operation)
}

// .catalog_studio_message.ExpressionValuePair visible_param = 6;
inline bool SingleComponentItem::_internal_has_visible_param() const {
  return this != internal_default_instance() && visible_param_ != nullptr;
}
inline bool SingleComponentItem::has_visible_param() const {
  return _internal_has_visible_param();
}
inline const ::catalog_studio_message::ExpressionValuePair& SingleComponentItem::_internal_visible_param() const {
  const ::catalog_studio_message::ExpressionValuePair* p = visible_param_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ExpressionValuePair&>(
      ::catalog_studio_message::_ExpressionValuePair_default_instance_);
}
inline const ::catalog_studio_message::ExpressionValuePair& SingleComponentItem::visible_param() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.visible_param)
  return _internal_visible_param();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_visible_param(
    ::catalog_studio_message::ExpressionValuePair* visible_param) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(visible_param_);
  }
  visible_param_ = visible_param;
  if (visible_param) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.visible_param)
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::release_visible_param() {
  
  ::catalog_studio_message::ExpressionValuePair* temp = visible_param_;
  visible_param_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::unsafe_arena_release_visible_param() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.visible_param)
  
  ::catalog_studio_message::ExpressionValuePair* temp = visible_param_;
  visible_param_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::_internal_mutable_visible_param() {
  
  if (visible_param_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ExpressionValuePair>(GetArenaForAllocation());
    visible_param_ = p;
  }
  return visible_param_;
}
inline ::catalog_studio_message::ExpressionValuePair* SingleComponentItem::mutable_visible_param() {
  ::catalog_studio_message::ExpressionValuePair* _msg = _internal_mutable_visible_param();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.visible_param)
  return _msg;
}
inline void SingleComponentItem::set_allocated_visible_param(::catalog_studio_message::ExpressionValuePair* visible_param) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(visible_param_);
  }
  if (visible_param) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(visible_param));
    if (message_arena != submessage_arena) {
      visible_param = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, visible_param, submessage_arena);
    }
    
  } else {
    
  }
  visible_param_ = visible_param;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.visible_param)
}

// .catalog_studio_message.SingleComponentSource component_source = 7;
inline void SingleComponentItem::clear_component_source() {
  component_source_ = 0;
}
inline ::catalog_studio_message::SingleComponentSource SingleComponentItem::_internal_component_source() const {
  return static_cast< ::catalog_studio_message::SingleComponentSource >(component_source_);
}
inline ::catalog_studio_message::SingleComponentSource SingleComponentItem::component_source() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.component_source)
  return _internal_component_source();
}
inline void SingleComponentItem::_internal_set_component_source(::catalog_studio_message::SingleComponentSource value) {
  
  component_source_ = value;
}
inline void SingleComponentItem::set_component_source(::catalog_studio_message::SingleComponentSource value) {
  _internal_set_component_source(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentItem.component_source)
}

// repeated .catalog_studio_message.ImportMeshSection import_mesh = 8;
inline int SingleComponentItem::_internal_import_mesh_size() const {
  return import_mesh_.size();
}
inline int SingleComponentItem::import_mesh_size() const {
  return _internal_import_mesh_size();
}
inline ::catalog_studio_message::ImportMeshSection* SingleComponentItem::mutable_import_mesh(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.import_mesh)
  return import_mesh_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ImportMeshSection >*
SingleComponentItem::mutable_import_mesh() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SingleComponentItem.import_mesh)
  return &import_mesh_;
}
inline const ::catalog_studio_message::ImportMeshSection& SingleComponentItem::_internal_import_mesh(int index) const {
  return import_mesh_.Get(index);
}
inline const ::catalog_studio_message::ImportMeshSection& SingleComponentItem::import_mesh(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.import_mesh)
  return _internal_import_mesh(index);
}
inline ::catalog_studio_message::ImportMeshSection* SingleComponentItem::_internal_add_import_mesh() {
  return import_mesh_.Add();
}
inline ::catalog_studio_message::ImportMeshSection* SingleComponentItem::add_import_mesh() {
  ::catalog_studio_message::ImportMeshSection* _add = _internal_add_import_mesh();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SingleComponentItem.import_mesh)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ImportMeshSection >&
SingleComponentItem::import_mesh() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SingleComponentItem.import_mesh)
  return import_mesh_;
}

// .catalog_studio_message.LocationProperty single_component_location = 9;
inline bool SingleComponentItem::_internal_has_single_component_location() const {
  return this != internal_default_instance() && single_component_location_ != nullptr;
}
inline bool SingleComponentItem::has_single_component_location() const {
  return _internal_has_single_component_location();
}
inline const ::catalog_studio_message::LocationProperty& SingleComponentItem::_internal_single_component_location() const {
  const ::catalog_studio_message::LocationProperty* p = single_component_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::LocationProperty&>(
      ::catalog_studio_message::_LocationProperty_default_instance_);
}
inline const ::catalog_studio_message::LocationProperty& SingleComponentItem::single_component_location() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.single_component_location)
  return _internal_single_component_location();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_single_component_location(
    ::catalog_studio_message::LocationProperty* single_component_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_location_);
  }
  single_component_location_ = single_component_location;
  if (single_component_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.single_component_location)
}
inline ::catalog_studio_message::LocationProperty* SingleComponentItem::release_single_component_location() {
  
  ::catalog_studio_message::LocationProperty* temp = single_component_location_;
  single_component_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::LocationProperty* SingleComponentItem::unsafe_arena_release_single_component_location() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.single_component_location)
  
  ::catalog_studio_message::LocationProperty* temp = single_component_location_;
  single_component_location_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::LocationProperty* SingleComponentItem::_internal_mutable_single_component_location() {
  
  if (single_component_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::LocationProperty>(GetArenaForAllocation());
    single_component_location_ = p;
  }
  return single_component_location_;
}
inline ::catalog_studio_message::LocationProperty* SingleComponentItem::mutable_single_component_location() {
  ::catalog_studio_message::LocationProperty* _msg = _internal_mutable_single_component_location();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.single_component_location)
  return _msg;
}
inline void SingleComponentItem::set_allocated_single_component_location(::catalog_studio_message::LocationProperty* single_component_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_location_);
  }
  if (single_component_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_location));
    if (message_arena != submessage_arena) {
      single_component_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, single_component_location, submessage_arena);
    }
    
  } else {
    
  }
  single_component_location_ = single_component_location;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.single_component_location)
}

// .catalog_studio_message.RotationProperty single_component_rotation = 10;
inline bool SingleComponentItem::_internal_has_single_component_rotation() const {
  return this != internal_default_instance() && single_component_rotation_ != nullptr;
}
inline bool SingleComponentItem::has_single_component_rotation() const {
  return _internal_has_single_component_rotation();
}
inline const ::catalog_studio_message::RotationProperty& SingleComponentItem::_internal_single_component_rotation() const {
  const ::catalog_studio_message::RotationProperty* p = single_component_rotation_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::RotationProperty&>(
      ::catalog_studio_message::_RotationProperty_default_instance_);
}
inline const ::catalog_studio_message::RotationProperty& SingleComponentItem::single_component_rotation() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.single_component_rotation)
  return _internal_single_component_rotation();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_single_component_rotation(
    ::catalog_studio_message::RotationProperty* single_component_rotation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_rotation_);
  }
  single_component_rotation_ = single_component_rotation;
  if (single_component_rotation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.single_component_rotation)
}
inline ::catalog_studio_message::RotationProperty* SingleComponentItem::release_single_component_rotation() {
  
  ::catalog_studio_message::RotationProperty* temp = single_component_rotation_;
  single_component_rotation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::RotationProperty* SingleComponentItem::unsafe_arena_release_single_component_rotation() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.single_component_rotation)
  
  ::catalog_studio_message::RotationProperty* temp = single_component_rotation_;
  single_component_rotation_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::RotationProperty* SingleComponentItem::_internal_mutable_single_component_rotation() {
  
  if (single_component_rotation_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::RotationProperty>(GetArenaForAllocation());
    single_component_rotation_ = p;
  }
  return single_component_rotation_;
}
inline ::catalog_studio_message::RotationProperty* SingleComponentItem::mutable_single_component_rotation() {
  ::catalog_studio_message::RotationProperty* _msg = _internal_mutable_single_component_rotation();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.single_component_rotation)
  return _msg;
}
inline void SingleComponentItem::set_allocated_single_component_rotation(::catalog_studio_message::RotationProperty* single_component_rotation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_rotation_);
  }
  if (single_component_rotation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_rotation));
    if (message_arena != submessage_arena) {
      single_component_rotation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, single_component_rotation, submessage_arena);
    }
    
  } else {
    
  }
  single_component_rotation_ = single_component_rotation;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.single_component_rotation)
}

// .catalog_studio_message.ScaleProperty single_component_scale = 11;
inline bool SingleComponentItem::_internal_has_single_component_scale() const {
  return this != internal_default_instance() && single_component_scale_ != nullptr;
}
inline bool SingleComponentItem::has_single_component_scale() const {
  return _internal_has_single_component_scale();
}
inline const ::catalog_studio_message::ScaleProperty& SingleComponentItem::_internal_single_component_scale() const {
  const ::catalog_studio_message::ScaleProperty* p = single_component_scale_;
  return p != nullptr ? *p : reinterpret_cast<const ::catalog_studio_message::ScaleProperty&>(
      ::catalog_studio_message::_ScaleProperty_default_instance_);
}
inline const ::catalog_studio_message::ScaleProperty& SingleComponentItem::single_component_scale() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.single_component_scale)
  return _internal_single_component_scale();
}
inline void SingleComponentItem::unsafe_arena_set_allocated_single_component_scale(
    ::catalog_studio_message::ScaleProperty* single_component_scale) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_scale_);
  }
  single_component_scale_ = single_component_scale;
  if (single_component_scale) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:catalog_studio_message.SingleComponentItem.single_component_scale)
}
inline ::catalog_studio_message::ScaleProperty* SingleComponentItem::release_single_component_scale() {
  
  ::catalog_studio_message::ScaleProperty* temp = single_component_scale_;
  single_component_scale_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::catalog_studio_message::ScaleProperty* SingleComponentItem::unsafe_arena_release_single_component_scale() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.single_component_scale)
  
  ::catalog_studio_message::ScaleProperty* temp = single_component_scale_;
  single_component_scale_ = nullptr;
  return temp;
}
inline ::catalog_studio_message::ScaleProperty* SingleComponentItem::_internal_mutable_single_component_scale() {
  
  if (single_component_scale_ == nullptr) {
    auto* p = CreateMaybeMessage<::catalog_studio_message::ScaleProperty>(GetArenaForAllocation());
    single_component_scale_ = p;
  }
  return single_component_scale_;
}
inline ::catalog_studio_message::ScaleProperty* SingleComponentItem::mutable_single_component_scale() {
  ::catalog_studio_message::ScaleProperty* _msg = _internal_mutable_single_component_scale();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.single_component_scale)
  return _msg;
}
inline void SingleComponentItem::set_allocated_single_component_scale(::catalog_studio_message::ScaleProperty* single_component_scale) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_scale_);
  }
  if (single_component_scale) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(single_component_scale));
    if (message_arena != submessage_arena) {
      single_component_scale = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, single_component_scale, submessage_arena);
    }
    
  } else {
    
  }
  single_component_scale_ = single_component_scale;
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.single_component_scale)
}

// string PakRefPath = 12;
inline void SingleComponentItem::clear_pakrefpath() {
  pakrefpath_.ClearToEmpty();
}
inline const std::string& SingleComponentItem::pakrefpath() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.PakRefPath)
  return _internal_pakrefpath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SingleComponentItem::set_pakrefpath(ArgT0&& arg0, ArgT... args) {
 
 pakrefpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentItem.PakRefPath)
}
inline std::string* SingleComponentItem::mutable_pakrefpath() {
  std::string* _s = _internal_mutable_pakrefpath();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.PakRefPath)
  return _s;
}
inline const std::string& SingleComponentItem::_internal_pakrefpath() const {
  return pakrefpath_.Get();
}
inline void SingleComponentItem::_internal_set_pakrefpath(const std::string& value) {
  
  pakrefpath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::_internal_mutable_pakrefpath() {
  
  return pakrefpath_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::release_pakrefpath() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.PakRefPath)
  return pakrefpath_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SingleComponentItem::set_allocated_pakrefpath(std::string* pakrefpath) {
  if (pakrefpath != nullptr) {
    
  } else {
    
  }
  pakrefpath_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pakrefpath,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.PakRefPath)
}

// string PakRelativePath = 13;
inline void SingleComponentItem::clear_pakrelativepath() {
  pakrelativepath_.ClearToEmpty();
}
inline const std::string& SingleComponentItem::pakrelativepath() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentItem.PakRelativePath)
  return _internal_pakrelativepath();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SingleComponentItem::set_pakrelativepath(ArgT0&& arg0, ArgT... args) {
 
 pakrelativepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentItem.PakRelativePath)
}
inline std::string* SingleComponentItem::mutable_pakrelativepath() {
  std::string* _s = _internal_mutable_pakrelativepath();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentItem.PakRelativePath)
  return _s;
}
inline const std::string& SingleComponentItem::_internal_pakrelativepath() const {
  return pakrelativepath_.Get();
}
inline void SingleComponentItem::_internal_set_pakrelativepath(const std::string& value) {
  
  pakrelativepath_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::_internal_mutable_pakrelativepath() {
  
  return pakrelativepath_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SingleComponentItem::release_pakrelativepath() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.SingleComponentItem.PakRelativePath)
  return pakrelativepath_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SingleComponentItem::set_allocated_pakrelativepath(std::string* pakrelativepath) {
  if (pakrelativepath != nullptr) {
    
  } else {
    
  }
  pakrelativepath_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pakrelativepath,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.SingleComponentItem.PakRelativePath)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SingleComponentItem_2eproto
