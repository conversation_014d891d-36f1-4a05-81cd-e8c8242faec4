// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SingleComponentProperty.proto

#include "SingleComponentProperty.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr SingleComponentProperty::SingleComponentProperty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : component_items_()
  , no_meaning_(0){}
struct SingleComponentPropertyDefaultTypeInternal {
  constexpr SingleComponentPropertyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SingleComponentPropertyDefaultTypeInternal() {}
  union {
    SingleComponentProperty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SingleComponentPropertyDefaultTypeInternal _SingleComponentProperty_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_SingleComponentProperty_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_SingleComponentProperty_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_SingleComponentProperty_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_SingleComponentProperty_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentProperty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentProperty, component_items_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::SingleComponentProperty, no_meaning_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::SingleComponentProperty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_SingleComponentProperty_default_instance_),
};

const char descriptor_table_protodef_SingleComponentProperty_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035SingleComponentProperty.proto\022\026catalog"
  "_studio_message\032\031SingleComponentItem.pro"
  "to\"s\n\027SingleComponentProperty\022D\n\017compone"
  "nt_items\030\001 \003(\0132+.catalog_studio_message."
  "SingleComponentItem\022\022\n\nno_meaning\030\002 \001(\005b"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_SingleComponentProperty_2eproto_deps[1] = {
  &::descriptor_table_SingleComponentItem_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_SingleComponentProperty_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SingleComponentProperty_2eproto = {
  false, false, 207, descriptor_table_protodef_SingleComponentProperty_2eproto, "SingleComponentProperty.proto", 
  &descriptor_table_SingleComponentProperty_2eproto_once, descriptor_table_SingleComponentProperty_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_SingleComponentProperty_2eproto::offsets,
  file_level_metadata_SingleComponentProperty_2eproto, file_level_enum_descriptors_SingleComponentProperty_2eproto, file_level_service_descriptors_SingleComponentProperty_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_SingleComponentProperty_2eproto_getter() {
  return &descriptor_table_SingleComponentProperty_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_SingleComponentProperty_2eproto(&descriptor_table_SingleComponentProperty_2eproto);
namespace catalog_studio_message {

// ===================================================================

class SingleComponentProperty::_Internal {
 public:
};

void SingleComponentProperty::clear_component_items() {
  component_items_.Clear();
}
SingleComponentProperty::SingleComponentProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  component_items_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.SingleComponentProperty)
}
SingleComponentProperty::SingleComponentProperty(const SingleComponentProperty& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      component_items_(from.component_items_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  no_meaning_ = from.no_meaning_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.SingleComponentProperty)
}

void SingleComponentProperty::SharedCtor() {
no_meaning_ = 0;
}

SingleComponentProperty::~SingleComponentProperty() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.SingleComponentProperty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SingleComponentProperty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SingleComponentProperty::ArenaDtor(void* object) {
  SingleComponentProperty* _this = reinterpret_cast< SingleComponentProperty* >(object);
  (void)_this;
}
void SingleComponentProperty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SingleComponentProperty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SingleComponentProperty::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.SingleComponentProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  component_items_.Clear();
  no_meaning_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SingleComponentProperty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.SingleComponentItem component_items = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_component_items(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 no_meaning = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          no_meaning_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SingleComponentProperty::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.SingleComponentProperty)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.SingleComponentItem component_items = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_component_items_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_component_items(i), target, stream);
  }

  // int32 no_meaning = 2;
  if (this->_internal_no_meaning() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_no_meaning(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.SingleComponentProperty)
  return target;
}

size_t SingleComponentProperty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.SingleComponentProperty)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.SingleComponentItem component_items = 1;
  total_size += 1UL * this->_internal_component_items_size();
  for (const auto& msg : this->component_items_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 no_meaning = 2;
  if (this->_internal_no_meaning() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_no_meaning());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SingleComponentProperty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SingleComponentProperty::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SingleComponentProperty::GetClassData() const { return &_class_data_; }

void SingleComponentProperty::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SingleComponentProperty *>(to)->MergeFrom(
      static_cast<const SingleComponentProperty &>(from));
}


void SingleComponentProperty::MergeFrom(const SingleComponentProperty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.SingleComponentProperty)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  component_items_.MergeFrom(from.component_items_);
  if (from._internal_no_meaning() != 0) {
    _internal_set_no_meaning(from._internal_no_meaning());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SingleComponentProperty::CopyFrom(const SingleComponentProperty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.SingleComponentProperty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SingleComponentProperty::IsInitialized() const {
  return true;
}

void SingleComponentProperty::InternalSwap(SingleComponentProperty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  component_items_.InternalSwap(&other->component_items_);
  swap(no_meaning_, other->no_meaning_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SingleComponentProperty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_SingleComponentProperty_2eproto_getter, &descriptor_table_SingleComponentProperty_2eproto_once,
      file_level_metadata_SingleComponentProperty_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::SingleComponentProperty* Arena::CreateMaybeMessage< ::catalog_studio_message::SingleComponentProperty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::SingleComponentProperty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
