// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SingleComponentProperty.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_SingleComponentProperty_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_SingleComponentProperty_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "SingleComponentItem.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_SingleComponentProperty_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_SingleComponentProperty_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_SingleComponentProperty_2eproto;
namespace catalog_studio_message {
class SingleComponentProperty;
struct SingleComponentPropertyDefaultTypeInternal;
extern SingleComponentPropertyDefaultTypeInternal _SingleComponentProperty_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::SingleComponentProperty* Arena::CreateMaybeMessage<::catalog_studio_message::SingleComponentProperty>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class SingleComponentProperty final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.SingleComponentProperty) */ {
 public:
  inline SingleComponentProperty() : SingleComponentProperty(nullptr) {}
  ~SingleComponentProperty() override;
  explicit constexpr SingleComponentProperty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SingleComponentProperty(const SingleComponentProperty& from);
  SingleComponentProperty(SingleComponentProperty&& from) noexcept
    : SingleComponentProperty() {
    *this = ::std::move(from);
  }

  inline SingleComponentProperty& operator=(const SingleComponentProperty& from) {
    CopyFrom(from);
    return *this;
  }
  inline SingleComponentProperty& operator=(SingleComponentProperty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SingleComponentProperty& default_instance() {
    return *internal_default_instance();
  }
  static inline const SingleComponentProperty* internal_default_instance() {
    return reinterpret_cast<const SingleComponentProperty*>(
               &_SingleComponentProperty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SingleComponentProperty& a, SingleComponentProperty& b) {
    a.Swap(&b);
  }
  inline void Swap(SingleComponentProperty* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SingleComponentProperty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SingleComponentProperty* New() const final {
    return new SingleComponentProperty();
  }

  SingleComponentProperty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SingleComponentProperty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SingleComponentProperty& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SingleComponentProperty& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SingleComponentProperty* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.SingleComponentProperty";
  }
  protected:
  explicit SingleComponentProperty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentItemsFieldNumber = 1,
    kNoMeaningFieldNumber = 2,
  };
  // repeated .catalog_studio_message.SingleComponentItem component_items = 1;
  int component_items_size() const;
  private:
  int _internal_component_items_size() const;
  public:
  void clear_component_items();
  ::catalog_studio_message::SingleComponentItem* mutable_component_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >*
      mutable_component_items();
  private:
  const ::catalog_studio_message::SingleComponentItem& _internal_component_items(int index) const;
  ::catalog_studio_message::SingleComponentItem* _internal_add_component_items();
  public:
  const ::catalog_studio_message::SingleComponentItem& component_items(int index) const;
  ::catalog_studio_message::SingleComponentItem* add_component_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >&
      component_items() const;

  // int32 no_meaning = 2;
  void clear_no_meaning();
  ::PROTOBUF_NAMESPACE_ID::int32 no_meaning() const;
  void set_no_meaning(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_no_meaning() const;
  void _internal_set_no_meaning(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.SingleComponentProperty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem > component_items_;
  ::PROTOBUF_NAMESPACE_ID::int32 no_meaning_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_SingleComponentProperty_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SingleComponentProperty

// repeated .catalog_studio_message.SingleComponentItem component_items = 1;
inline int SingleComponentProperty::_internal_component_items_size() const {
  return component_items_.size();
}
inline int SingleComponentProperty::component_items_size() const {
  return _internal_component_items_size();
}
inline ::catalog_studio_message::SingleComponentItem* SingleComponentProperty::mutable_component_items(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.SingleComponentProperty.component_items)
  return component_items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >*
SingleComponentProperty::mutable_component_items() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.SingleComponentProperty.component_items)
  return &component_items_;
}
inline const ::catalog_studio_message::SingleComponentItem& SingleComponentProperty::_internal_component_items(int index) const {
  return component_items_.Get(index);
}
inline const ::catalog_studio_message::SingleComponentItem& SingleComponentProperty::component_items(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentProperty.component_items)
  return _internal_component_items(index);
}
inline ::catalog_studio_message::SingleComponentItem* SingleComponentProperty::_internal_add_component_items() {
  return component_items_.Add();
}
inline ::catalog_studio_message::SingleComponentItem* SingleComponentProperty::add_component_items() {
  ::catalog_studio_message::SingleComponentItem* _add = _internal_add_component_items();
  // @@protoc_insertion_point(field_add:catalog_studio_message.SingleComponentProperty.component_items)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::SingleComponentItem >&
SingleComponentProperty::component_items() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.SingleComponentProperty.component_items)
  return component_items_;
}

// int32 no_meaning = 2;
inline void SingleComponentProperty::clear_no_meaning() {
  no_meaning_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SingleComponentProperty::_internal_no_meaning() const {
  return no_meaning_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 SingleComponentProperty::no_meaning() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.SingleComponentProperty.no_meaning)
  return _internal_no_meaning();
}
inline void SingleComponentProperty::_internal_set_no_meaning(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  no_meaning_ = value;
}
inline void SingleComponentProperty::set_no_meaning(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_no_meaning(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.SingleComponentProperty.no_meaning)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_SingleComponentProperty_2eproto
