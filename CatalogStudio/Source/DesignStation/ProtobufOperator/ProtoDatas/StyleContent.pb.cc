// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleContent.proto

#include "StyleContent.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleContentData::StyleContentData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : option_datas_()
  , style_option_checks_()
  , content_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , content_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , content_relation_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , content_sort_order_(0)
  , content_base_type_(0){}
struct StyleContentDataDefaultTypeInternal {
  constexpr StyleContentDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleContentDataDefaultTypeInternal() {}
  union {
    StyleContentData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleContentDataDefaultTypeInternal _StyleContentData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleContent_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleContent_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleContent_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleContent_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, content_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, content_name_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, content_sort_order_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, option_datas_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, style_option_checks_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, content_relation_code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleContentData, content_base_type_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleContentData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleContentData_default_instance_),
};

const char descriptor_table_protodef_StyleContent_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022StyleContent.proto\022\026catalog_studio_mes"
  "sage\032\021StyleOption.proto\032\031StyleOptionChec"
  "kArr.proto\"\237\002\n\020StyleContentData\022\022\n\nconte"
  "nt_id\030\001 \001(\t\022\024\n\014content_name\030\002 \001(\t\022\032\n\022con"
  "tent_sort_order\030\003 \001(\005\022=\n\014option_datas\030\004 "
  "\003(\0132\'.catalog_studio_message.StyleOption"
  "Data\022L\n\023style_option_checks\030\005 \003(\0132/.cata"
  "log_studio_message.StyleOptionCheckArrDa"
  "ta\022\035\n\025content_relation_code\030\006 \001(\t\022\031\n\021con"
  "tent_base_type\030\007 \001(\005b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_StyleContent_2eproto_deps[2] = {
  &::descriptor_table_StyleOption_2eproto,
  &::descriptor_table_StyleOptionCheckArr_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleContent_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleContent_2eproto = {
  false, false, 388, descriptor_table_protodef_StyleContent_2eproto, "StyleContent.proto", 
  &descriptor_table_StyleContent_2eproto_once, descriptor_table_StyleContent_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_StyleContent_2eproto::offsets,
  file_level_metadata_StyleContent_2eproto, file_level_enum_descriptors_StyleContent_2eproto, file_level_service_descriptors_StyleContent_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleContent_2eproto_getter() {
  return &descriptor_table_StyleContent_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleContent_2eproto(&descriptor_table_StyleContent_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleContentData::_Internal {
 public:
};

void StyleContentData::clear_option_datas() {
  option_datas_.Clear();
}
void StyleContentData::clear_style_option_checks() {
  style_option_checks_.Clear();
}
StyleContentData::StyleContentData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  option_datas_(arena),
  style_option_checks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleContentData)
}
StyleContentData::StyleContentData(const StyleContentData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      option_datas_(from.option_datas_),
      style_option_checks_(from.style_option_checks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content_id().empty()) {
    content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content_id(), 
      GetArenaForAllocation());
  }
  content_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content_name().empty()) {
    content_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content_name(), 
      GetArenaForAllocation());
  }
  content_relation_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content_relation_code().empty()) {
    content_relation_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content_relation_code(), 
      GetArenaForAllocation());
  }
  ::memcpy(&content_sort_order_, &from.content_sort_order_,
    static_cast<size_t>(reinterpret_cast<char*>(&content_base_type_) -
    reinterpret_cast<char*>(&content_sort_order_)) + sizeof(content_base_type_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleContentData)
}

void StyleContentData::SharedCtor() {
content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
content_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
content_relation_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&content_sort_order_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&content_base_type_) -
    reinterpret_cast<char*>(&content_sort_order_)) + sizeof(content_base_type_));
}

StyleContentData::~StyleContentData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleContentData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleContentData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  content_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  content_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  content_relation_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StyleContentData::ArenaDtor(void* object) {
  StyleContentData* _this = reinterpret_cast< StyleContentData* >(object);
  (void)_this;
}
void StyleContentData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleContentData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleContentData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleContentData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  option_datas_.Clear();
  style_option_checks_.Clear();
  content_id_.ClearToEmpty();
  content_name_.ClearToEmpty();
  content_relation_code_.ClearToEmpty();
  ::memset(&content_sort_order_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&content_base_type_) -
      reinterpret_cast<char*>(&content_sort_order_)) + sizeof(content_base_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleContentData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string content_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_content_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleContentData.content_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string content_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_content_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleContentData.content_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 content_sort_order = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          content_sort_order_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.StyleOptionData option_datas = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_option_datas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.StyleOptionCheckArrData style_option_checks = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_style_option_checks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string content_relation_code = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_content_relation_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleContentData.content_relation_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 content_base_type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          content_base_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleContentData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleContentData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string content_id = 1;
  if (!this->_internal_content_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_content_id().data(), static_cast<int>(this->_internal_content_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleContentData.content_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_content_id(), target);
  }

  // string content_name = 2;
  if (!this->_internal_content_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_content_name().data(), static_cast<int>(this->_internal_content_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleContentData.content_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_content_name(), target);
  }

  // int32 content_sort_order = 3;
  if (this->_internal_content_sort_order() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_content_sort_order(), target);
  }

  // repeated .catalog_studio_message.StyleOptionData option_datas = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_option_datas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_option_datas(i), target, stream);
  }

  // repeated .catalog_studio_message.StyleOptionCheckArrData style_option_checks = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_style_option_checks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_style_option_checks(i), target, stream);
  }

  // string content_relation_code = 6;
  if (!this->_internal_content_relation_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_content_relation_code().data(), static_cast<int>(this->_internal_content_relation_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleContentData.content_relation_code");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_content_relation_code(), target);
  }

  // int32 content_base_type = 7;
  if (this->_internal_content_base_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->_internal_content_base_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleContentData)
  return target;
}

size_t StyleContentData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleContentData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.StyleOptionData option_datas = 4;
  total_size += 1UL * this->_internal_option_datas_size();
  for (const auto& msg : this->option_datas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.StyleOptionCheckArrData style_option_checks = 5;
  total_size += 1UL * this->_internal_style_option_checks_size();
  for (const auto& msg : this->style_option_checks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string content_id = 1;
  if (!this->_internal_content_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_content_id());
  }

  // string content_name = 2;
  if (!this->_internal_content_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_content_name());
  }

  // string content_relation_code = 6;
  if (!this->_internal_content_relation_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_content_relation_code());
  }

  // int32 content_sort_order = 3;
  if (this->_internal_content_sort_order() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_content_sort_order());
  }

  // int32 content_base_type = 7;
  if (this->_internal_content_base_type() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_content_base_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleContentData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleContentData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleContentData::GetClassData() const { return &_class_data_; }

void StyleContentData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleContentData *>(to)->MergeFrom(
      static_cast<const StyleContentData &>(from));
}


void StyleContentData::MergeFrom(const StyleContentData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleContentData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  option_datas_.MergeFrom(from.option_datas_);
  style_option_checks_.MergeFrom(from.style_option_checks_);
  if (!from._internal_content_id().empty()) {
    _internal_set_content_id(from._internal_content_id());
  }
  if (!from._internal_content_name().empty()) {
    _internal_set_content_name(from._internal_content_name());
  }
  if (!from._internal_content_relation_code().empty()) {
    _internal_set_content_relation_code(from._internal_content_relation_code());
  }
  if (from._internal_content_sort_order() != 0) {
    _internal_set_content_sort_order(from._internal_content_sort_order());
  }
  if (from._internal_content_base_type() != 0) {
    _internal_set_content_base_type(from._internal_content_base_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleContentData::CopyFrom(const StyleContentData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleContentData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleContentData::IsInitialized() const {
  return true;
}

void StyleContentData::InternalSwap(StyleContentData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  option_datas_.InternalSwap(&other->option_datas_);
  style_option_checks_.InternalSwap(&other->style_option_checks_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &content_id_, lhs_arena,
      &other->content_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &content_name_, lhs_arena,
      &other->content_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &content_relation_code_, lhs_arena,
      &other->content_relation_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StyleContentData, content_base_type_)
      + sizeof(StyleContentData::content_base_type_)
      - PROTOBUF_FIELD_OFFSET(StyleContentData, content_sort_order_)>(
          reinterpret_cast<char*>(&content_sort_order_),
          reinterpret_cast<char*>(&other->content_sort_order_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleContentData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleContent_2eproto_getter, &descriptor_table_StyleContent_2eproto_once,
      file_level_metadata_StyleContent_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleContentData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleContentData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleContentData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
