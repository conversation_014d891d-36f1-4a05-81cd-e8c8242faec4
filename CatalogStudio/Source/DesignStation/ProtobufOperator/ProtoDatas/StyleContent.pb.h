// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleContent.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleContent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleContent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "StyleOption.pb.h"
#include "StyleOptionCheckArr.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleContent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleContent_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleContent_2eproto;
namespace catalog_studio_message {
class StyleContentData;
struct StyleContentDataDefaultTypeInternal;
extern StyleContentDataDefaultTypeInternal _StyleContentData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleContentData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleContentData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleContentData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleContentData) */ {
 public:
  inline StyleContentData() : StyleContentData(nullptr) {}
  ~StyleContentData() override;
  explicit constexpr StyleContentData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleContentData(const StyleContentData& from);
  StyleContentData(StyleContentData&& from) noexcept
    : StyleContentData() {
    *this = ::std::move(from);
  }

  inline StyleContentData& operator=(const StyleContentData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleContentData& operator=(StyleContentData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleContentData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleContentData* internal_default_instance() {
    return reinterpret_cast<const StyleContentData*>(
               &_StyleContentData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleContentData& a, StyleContentData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleContentData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleContentData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleContentData* New() const final {
    return new StyleContentData();
  }

  StyleContentData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleContentData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleContentData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleContentData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleContentData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleContentData";
  }
  protected:
  explicit StyleContentData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionDatasFieldNumber = 4,
    kStyleOptionChecksFieldNumber = 5,
    kContentIdFieldNumber = 1,
    kContentNameFieldNumber = 2,
    kContentRelationCodeFieldNumber = 6,
    kContentSortOrderFieldNumber = 3,
    kContentBaseTypeFieldNumber = 7,
  };
  // repeated .catalog_studio_message.StyleOptionData option_datas = 4;
  int option_datas_size() const;
  private:
  int _internal_option_datas_size() const;
  public:
  void clear_option_datas();
  ::catalog_studio_message::StyleOptionData* mutable_option_datas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionData >*
      mutable_option_datas();
  private:
  const ::catalog_studio_message::StyleOptionData& _internal_option_datas(int index) const;
  ::catalog_studio_message::StyleOptionData* _internal_add_option_datas();
  public:
  const ::catalog_studio_message::StyleOptionData& option_datas(int index) const;
  ::catalog_studio_message::StyleOptionData* add_option_datas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionData >&
      option_datas() const;

  // repeated .catalog_studio_message.StyleOptionCheckArrData style_option_checks = 5;
  int style_option_checks_size() const;
  private:
  int _internal_style_option_checks_size() const;
  public:
  void clear_style_option_checks();
  ::catalog_studio_message::StyleOptionCheckArrData* mutable_style_option_checks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckArrData >*
      mutable_style_option_checks();
  private:
  const ::catalog_studio_message::StyleOptionCheckArrData& _internal_style_option_checks(int index) const;
  ::catalog_studio_message::StyleOptionCheckArrData* _internal_add_style_option_checks();
  public:
  const ::catalog_studio_message::StyleOptionCheckArrData& style_option_checks(int index) const;
  ::catalog_studio_message::StyleOptionCheckArrData* add_style_option_checks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckArrData >&
      style_option_checks() const;

  // string content_id = 1;
  void clear_content_id();
  const std::string& content_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_content_id();
  void set_allocated_content_id(std::string* content_id);
  private:
  const std::string& _internal_content_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content_id(const std::string& value);
  std::string* _internal_mutable_content_id();
  public:

  // string content_name = 2;
  void clear_content_name();
  const std::string& content_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content_name();
  PROTOBUF_MUST_USE_RESULT std::string* release_content_name();
  void set_allocated_content_name(std::string* content_name);
  private:
  const std::string& _internal_content_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content_name(const std::string& value);
  std::string* _internal_mutable_content_name();
  public:

  // string content_relation_code = 6;
  void clear_content_relation_code();
  const std::string& content_relation_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content_relation_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content_relation_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_content_relation_code();
  void set_allocated_content_relation_code(std::string* content_relation_code);
  private:
  const std::string& _internal_content_relation_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content_relation_code(const std::string& value);
  std::string* _internal_mutable_content_relation_code();
  public:

  // int32 content_sort_order = 3;
  void clear_content_sort_order();
  ::PROTOBUF_NAMESPACE_ID::int32 content_sort_order() const;
  void set_content_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_content_sort_order() const;
  void _internal_set_content_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 content_base_type = 7;
  void clear_content_base_type();
  ::PROTOBUF_NAMESPACE_ID::int32 content_base_type() const;
  void set_content_base_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_content_base_type() const;
  void _internal_set_content_base_type(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleContentData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionData > option_datas_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckArrData > style_option_checks_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_relation_code_;
  ::PROTOBUF_NAMESPACE_ID::int32 content_sort_order_;
  ::PROTOBUF_NAMESPACE_ID::int32 content_base_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleContent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleContentData

// string content_id = 1;
inline void StyleContentData::clear_content_id() {
  content_id_.ClearToEmpty();
}
inline const std::string& StyleContentData::content_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.content_id)
  return _internal_content_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleContentData::set_content_id(ArgT0&& arg0, ArgT... args) {
 
 content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleContentData.content_id)
}
inline std::string* StyleContentData::mutable_content_id() {
  std::string* _s = _internal_mutable_content_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleContentData.content_id)
  return _s;
}
inline const std::string& StyleContentData::_internal_content_id() const {
  return content_id_.Get();
}
inline void StyleContentData::_internal_set_content_id(const std::string& value) {
  
  content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleContentData::_internal_mutable_content_id() {
  
  return content_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleContentData::release_content_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleContentData.content_id)
  return content_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleContentData::set_allocated_content_id(std::string* content_id) {
  if (content_id != nullptr) {
    
  } else {
    
  }
  content_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), content_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleContentData.content_id)
}

// string content_name = 2;
inline void StyleContentData::clear_content_name() {
  content_name_.ClearToEmpty();
}
inline const std::string& StyleContentData::content_name() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.content_name)
  return _internal_content_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleContentData::set_content_name(ArgT0&& arg0, ArgT... args) {
 
 content_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleContentData.content_name)
}
inline std::string* StyleContentData::mutable_content_name() {
  std::string* _s = _internal_mutable_content_name();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleContentData.content_name)
  return _s;
}
inline const std::string& StyleContentData::_internal_content_name() const {
  return content_name_.Get();
}
inline void StyleContentData::_internal_set_content_name(const std::string& value) {
  
  content_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleContentData::_internal_mutable_content_name() {
  
  return content_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleContentData::release_content_name() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleContentData.content_name)
  return content_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleContentData::set_allocated_content_name(std::string* content_name) {
  if (content_name != nullptr) {
    
  } else {
    
  }
  content_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), content_name,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleContentData.content_name)
}

// int32 content_sort_order = 3;
inline void StyleContentData::clear_content_sort_order() {
  content_sort_order_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleContentData::_internal_content_sort_order() const {
  return content_sort_order_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleContentData::content_sort_order() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.content_sort_order)
  return _internal_content_sort_order();
}
inline void StyleContentData::_internal_set_content_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  content_sort_order_ = value;
}
inline void StyleContentData::set_content_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_content_sort_order(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleContentData.content_sort_order)
}

// repeated .catalog_studio_message.StyleOptionData option_datas = 4;
inline int StyleContentData::_internal_option_datas_size() const {
  return option_datas_.size();
}
inline int StyleContentData::option_datas_size() const {
  return _internal_option_datas_size();
}
inline ::catalog_studio_message::StyleOptionData* StyleContentData::mutable_option_datas(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleContentData.option_datas)
  return option_datas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionData >*
StyleContentData::mutable_option_datas() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleContentData.option_datas)
  return &option_datas_;
}
inline const ::catalog_studio_message::StyleOptionData& StyleContentData::_internal_option_datas(int index) const {
  return option_datas_.Get(index);
}
inline const ::catalog_studio_message::StyleOptionData& StyleContentData::option_datas(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.option_datas)
  return _internal_option_datas(index);
}
inline ::catalog_studio_message::StyleOptionData* StyleContentData::_internal_add_option_datas() {
  return option_datas_.Add();
}
inline ::catalog_studio_message::StyleOptionData* StyleContentData::add_option_datas() {
  ::catalog_studio_message::StyleOptionData* _add = _internal_add_option_datas();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleContentData.option_datas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionData >&
StyleContentData::option_datas() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleContentData.option_datas)
  return option_datas_;
}

// repeated .catalog_studio_message.StyleOptionCheckArrData style_option_checks = 5;
inline int StyleContentData::_internal_style_option_checks_size() const {
  return style_option_checks_.size();
}
inline int StyleContentData::style_option_checks_size() const {
  return _internal_style_option_checks_size();
}
inline ::catalog_studio_message::StyleOptionCheckArrData* StyleContentData::mutable_style_option_checks(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleContentData.style_option_checks)
  return style_option_checks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckArrData >*
StyleContentData::mutable_style_option_checks() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleContentData.style_option_checks)
  return &style_option_checks_;
}
inline const ::catalog_studio_message::StyleOptionCheckArrData& StyleContentData::_internal_style_option_checks(int index) const {
  return style_option_checks_.Get(index);
}
inline const ::catalog_studio_message::StyleOptionCheckArrData& StyleContentData::style_option_checks(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.style_option_checks)
  return _internal_style_option_checks(index);
}
inline ::catalog_studio_message::StyleOptionCheckArrData* StyleContentData::_internal_add_style_option_checks() {
  return style_option_checks_.Add();
}
inline ::catalog_studio_message::StyleOptionCheckArrData* StyleContentData::add_style_option_checks() {
  ::catalog_studio_message::StyleOptionCheckArrData* _add = _internal_add_style_option_checks();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleContentData.style_option_checks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckArrData >&
StyleContentData::style_option_checks() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleContentData.style_option_checks)
  return style_option_checks_;
}

// string content_relation_code = 6;
inline void StyleContentData::clear_content_relation_code() {
  content_relation_code_.ClearToEmpty();
}
inline const std::string& StyleContentData::content_relation_code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.content_relation_code)
  return _internal_content_relation_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleContentData::set_content_relation_code(ArgT0&& arg0, ArgT... args) {
 
 content_relation_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleContentData.content_relation_code)
}
inline std::string* StyleContentData::mutable_content_relation_code() {
  std::string* _s = _internal_mutable_content_relation_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleContentData.content_relation_code)
  return _s;
}
inline const std::string& StyleContentData::_internal_content_relation_code() const {
  return content_relation_code_.Get();
}
inline void StyleContentData::_internal_set_content_relation_code(const std::string& value) {
  
  content_relation_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleContentData::_internal_mutable_content_relation_code() {
  
  return content_relation_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleContentData::release_content_relation_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleContentData.content_relation_code)
  return content_relation_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleContentData::set_allocated_content_relation_code(std::string* content_relation_code) {
  if (content_relation_code != nullptr) {
    
  } else {
    
  }
  content_relation_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), content_relation_code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleContentData.content_relation_code)
}

// int32 content_base_type = 7;
inline void StyleContentData::clear_content_base_type() {
  content_base_type_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleContentData::_internal_content_base_type() const {
  return content_base_type_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleContentData::content_base_type() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleContentData.content_base_type)
  return _internal_content_base_type();
}
inline void StyleContentData::_internal_set_content_base_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  content_base_type_ = value;
}
inline void StyleContentData::set_content_base_type(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_content_base_type(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleContentData.content_base_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleContent_2eproto
