// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleInfo.proto

#include "StyleInfo.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleInfoData::StyleInfoData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : style_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_craft_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_thumbnail_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_thumbnail_md5_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_sort_order_(0)
  , is_check_(false)
  , style_group_(0){}
struct StyleInfoDataDefaultTypeInternal {
  constexpr StyleInfoDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleInfoDataDefaultTypeInternal() {}
  union {
    StyleInfoData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleInfoDataDefaultTypeInternal _StyleInfoData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleInfo_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleInfo_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleInfo_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleInfo_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_craft_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_description_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_thumbnail_path_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_thumbnail_md5_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_sort_order_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, is_check_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleInfoData, style_group_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleInfoData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleInfoData_default_instance_),
};

const char descriptor_table_protodef_StyleInfo_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\017StyleInfo.proto\022\026catalog_studio_messag"
  "e\"\341\001\n\rStyleInfoData\022\020\n\010style_id\030\001 \001(\t\022\022\n"
  "\nstyle_code\030\002 \001(\t\022\023\n\013style_craft\030\003 \001(\t\022\031"
  "\n\021style_description\030\004 \001(\t\022\034\n\024style_thumb"
  "nail_path\030\005 \001(\t\022\033\n\023style_thumbnail_md5\030\006"
  " \001(\t\022\030\n\020style_sort_order\030\007 \001(\005\022\020\n\010is_che"
  "ck\030\010 \001(\010\022\023\n\013style_group\030\t \001(\005b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleInfo_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleInfo_2eproto = {
  false, false, 277, descriptor_table_protodef_StyleInfo_2eproto, "StyleInfo.proto", 
  &descriptor_table_StyleInfo_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_StyleInfo_2eproto::offsets,
  file_level_metadata_StyleInfo_2eproto, file_level_enum_descriptors_StyleInfo_2eproto, file_level_service_descriptors_StyleInfo_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleInfo_2eproto_getter() {
  return &descriptor_table_StyleInfo_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleInfo_2eproto(&descriptor_table_StyleInfo_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleInfoData::_Internal {
 public:
};

StyleInfoData::StyleInfoData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleInfoData)
}
StyleInfoData::StyleInfoData(const StyleInfoData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_id().empty()) {
    style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_id(), 
      GetArenaForAllocation());
  }
  style_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_code().empty()) {
    style_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_code(), 
      GetArenaForAllocation());
  }
  style_craft_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_craft().empty()) {
    style_craft_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_craft(), 
      GetArenaForAllocation());
  }
  style_description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_description().empty()) {
    style_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_description(), 
      GetArenaForAllocation());
  }
  style_thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_thumbnail_path().empty()) {
    style_thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_thumbnail_path(), 
      GetArenaForAllocation());
  }
  style_thumbnail_md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_thumbnail_md5().empty()) {
    style_thumbnail_md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_thumbnail_md5(), 
      GetArenaForAllocation());
  }
  ::memcpy(&style_sort_order_, &from.style_sort_order_,
    static_cast<size_t>(reinterpret_cast<char*>(&style_group_) -
    reinterpret_cast<char*>(&style_sort_order_)) + sizeof(style_group_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleInfoData)
}

void StyleInfoData::SharedCtor() {
style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_craft_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_thumbnail_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_thumbnail_md5_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&style_sort_order_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&style_group_) -
    reinterpret_cast<char*>(&style_sort_order_)) + sizeof(style_group_));
}

StyleInfoData::~StyleInfoData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleInfoData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleInfoData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  style_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_craft_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_thumbnail_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_thumbnail_md5_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StyleInfoData::ArenaDtor(void* object) {
  StyleInfoData* _this = reinterpret_cast< StyleInfoData* >(object);
  (void)_this;
}
void StyleInfoData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleInfoData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleInfoData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleInfoData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  style_id_.ClearToEmpty();
  style_code_.ClearToEmpty();
  style_craft_.ClearToEmpty();
  style_description_.ClearToEmpty();
  style_thumbnail_path_.ClearToEmpty();
  style_thumbnail_md5_.ClearToEmpty();
  ::memset(&style_sort_order_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&style_group_) -
      reinterpret_cast<char*>(&style_sort_order_)) + sizeof(style_group_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleInfoData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string style_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_style_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_code = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_style_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_craft = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_style_craft();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_craft"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_description = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_style_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_thumbnail_path = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_style_thumbnail_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_thumbnail_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_thumbnail_md5 = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_style_thumbnail_md5();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleInfoData.style_thumbnail_md5"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 style_sort_order = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          style_sort_order_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_check = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          is_check_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 style_group = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          style_group_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleInfoData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleInfoData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string style_id = 1;
  if (!this->_internal_style_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_id().data(), static_cast<int>(this->_internal_style_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_style_id(), target);
  }

  // string style_code = 2;
  if (!this->_internal_style_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_code().data(), static_cast<int>(this->_internal_style_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_code");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_style_code(), target);
  }

  // string style_craft = 3;
  if (!this->_internal_style_craft().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_craft().data(), static_cast<int>(this->_internal_style_craft().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_craft");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_style_craft(), target);
  }

  // string style_description = 4;
  if (!this->_internal_style_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_description().data(), static_cast<int>(this->_internal_style_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_description");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_style_description(), target);
  }

  // string style_thumbnail_path = 5;
  if (!this->_internal_style_thumbnail_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_thumbnail_path().data(), static_cast<int>(this->_internal_style_thumbnail_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_thumbnail_path");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_style_thumbnail_path(), target);
  }

  // string style_thumbnail_md5 = 6;
  if (!this->_internal_style_thumbnail_md5().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_thumbnail_md5().data(), static_cast<int>(this->_internal_style_thumbnail_md5().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleInfoData.style_thumbnail_md5");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_style_thumbnail_md5(), target);
  }

  // int32 style_sort_order = 7;
  if (this->_internal_style_sort_order() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->_internal_style_sort_order(), target);
  }

  // bool is_check = 8;
  if (this->_internal_is_check() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_is_check(), target);
  }

  // int32 style_group = 9;
  if (this->_internal_style_group() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(9, this->_internal_style_group(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleInfoData)
  return target;
}

size_t StyleInfoData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleInfoData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string style_id = 1;
  if (!this->_internal_style_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_id());
  }

  // string style_code = 2;
  if (!this->_internal_style_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_code());
  }

  // string style_craft = 3;
  if (!this->_internal_style_craft().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_craft());
  }

  // string style_description = 4;
  if (!this->_internal_style_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_description());
  }

  // string style_thumbnail_path = 5;
  if (!this->_internal_style_thumbnail_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_thumbnail_path());
  }

  // string style_thumbnail_md5 = 6;
  if (!this->_internal_style_thumbnail_md5().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_thumbnail_md5());
  }

  // int32 style_sort_order = 7;
  if (this->_internal_style_sort_order() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_style_sort_order());
  }

  // bool is_check = 8;
  if (this->_internal_is_check() != 0) {
    total_size += 1 + 1;
  }

  // int32 style_group = 9;
  if (this->_internal_style_group() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_style_group());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleInfoData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleInfoData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleInfoData::GetClassData() const { return &_class_data_; }

void StyleInfoData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleInfoData *>(to)->MergeFrom(
      static_cast<const StyleInfoData &>(from));
}


void StyleInfoData::MergeFrom(const StyleInfoData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleInfoData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_style_id().empty()) {
    _internal_set_style_id(from._internal_style_id());
  }
  if (!from._internal_style_code().empty()) {
    _internal_set_style_code(from._internal_style_code());
  }
  if (!from._internal_style_craft().empty()) {
    _internal_set_style_craft(from._internal_style_craft());
  }
  if (!from._internal_style_description().empty()) {
    _internal_set_style_description(from._internal_style_description());
  }
  if (!from._internal_style_thumbnail_path().empty()) {
    _internal_set_style_thumbnail_path(from._internal_style_thumbnail_path());
  }
  if (!from._internal_style_thumbnail_md5().empty()) {
    _internal_set_style_thumbnail_md5(from._internal_style_thumbnail_md5());
  }
  if (from._internal_style_sort_order() != 0) {
    _internal_set_style_sort_order(from._internal_style_sort_order());
  }
  if (from._internal_is_check() != 0) {
    _internal_set_is_check(from._internal_is_check());
  }
  if (from._internal_style_group() != 0) {
    _internal_set_style_group(from._internal_style_group());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleInfoData::CopyFrom(const StyleInfoData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleInfoData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleInfoData::IsInitialized() const {
  return true;
}

void StyleInfoData::InternalSwap(StyleInfoData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_id_, lhs_arena,
      &other->style_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_code_, lhs_arena,
      &other->style_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_craft_, lhs_arena,
      &other->style_craft_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_description_, lhs_arena,
      &other->style_description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_thumbnail_path_, lhs_arena,
      &other->style_thumbnail_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_thumbnail_md5_, lhs_arena,
      &other->style_thumbnail_md5_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StyleInfoData, style_group_)
      + sizeof(StyleInfoData::style_group_)
      - PROTOBUF_FIELD_OFFSET(StyleInfoData, style_sort_order_)>(
          reinterpret_cast<char*>(&style_sort_order_),
          reinterpret_cast<char*>(&other->style_sort_order_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleInfoData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleInfo_2eproto_getter, &descriptor_table_StyleInfo_2eproto_once,
      file_level_metadata_StyleInfo_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleInfoData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleInfoData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleInfoData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
