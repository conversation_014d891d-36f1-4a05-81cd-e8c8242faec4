// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleInfo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleInfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleInfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleInfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleInfo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleInfo_2eproto;
namespace catalog_studio_message {
class StyleInfoData;
struct StyleInfoDataDefaultTypeInternal;
extern StyleInfoDataDefaultTypeInternal _StyleInfoData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleInfoData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleInfoData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleInfoData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleInfoData) */ {
 public:
  inline StyleInfoData() : StyleInfoData(nullptr) {}
  ~StyleInfoData() override;
  explicit constexpr StyleInfoData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleInfoData(const StyleInfoData& from);
  StyleInfoData(StyleInfoData&& from) noexcept
    : StyleInfoData() {
    *this = ::std::move(from);
  }

  inline StyleInfoData& operator=(const StyleInfoData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleInfoData& operator=(StyleInfoData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleInfoData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleInfoData* internal_default_instance() {
    return reinterpret_cast<const StyleInfoData*>(
               &_StyleInfoData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleInfoData& a, StyleInfoData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleInfoData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleInfoData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleInfoData* New() const final {
    return new StyleInfoData();
  }

  StyleInfoData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleInfoData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleInfoData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleInfoData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleInfoData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleInfoData";
  }
  protected:
  explicit StyleInfoData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStyleIdFieldNumber = 1,
    kStyleCodeFieldNumber = 2,
    kStyleCraftFieldNumber = 3,
    kStyleDescriptionFieldNumber = 4,
    kStyleThumbnailPathFieldNumber = 5,
    kStyleThumbnailMd5FieldNumber = 6,
    kStyleSortOrderFieldNumber = 7,
    kIsCheckFieldNumber = 8,
    kStyleGroupFieldNumber = 9,
  };
  // string style_id = 1;
  void clear_style_id();
  const std::string& style_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_id();
  void set_allocated_style_id(std::string* style_id);
  private:
  const std::string& _internal_style_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_id(const std::string& value);
  std::string* _internal_mutable_style_id();
  public:

  // string style_code = 2;
  void clear_style_code();
  const std::string& style_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_code();
  void set_allocated_style_code(std::string* style_code);
  private:
  const std::string& _internal_style_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_code(const std::string& value);
  std::string* _internal_mutable_style_code();
  public:

  // string style_craft = 3;
  void clear_style_craft();
  const std::string& style_craft() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_craft(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_craft();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_craft();
  void set_allocated_style_craft(std::string* style_craft);
  private:
  const std::string& _internal_style_craft() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_craft(const std::string& value);
  std::string* _internal_mutable_style_craft();
  public:

  // string style_description = 4;
  void clear_style_description();
  const std::string& style_description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_description();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_description();
  void set_allocated_style_description(std::string* style_description);
  private:
  const std::string& _internal_style_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_description(const std::string& value);
  std::string* _internal_mutable_style_description();
  public:

  // string style_thumbnail_path = 5;
  void clear_style_thumbnail_path();
  const std::string& style_thumbnail_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_thumbnail_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_thumbnail_path();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_thumbnail_path();
  void set_allocated_style_thumbnail_path(std::string* style_thumbnail_path);
  private:
  const std::string& _internal_style_thumbnail_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_thumbnail_path(const std::string& value);
  std::string* _internal_mutable_style_thumbnail_path();
  public:

  // string style_thumbnail_md5 = 6;
  void clear_style_thumbnail_md5();
  const std::string& style_thumbnail_md5() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_thumbnail_md5(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_thumbnail_md5();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_thumbnail_md5();
  void set_allocated_style_thumbnail_md5(std::string* style_thumbnail_md5);
  private:
  const std::string& _internal_style_thumbnail_md5() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_thumbnail_md5(const std::string& value);
  std::string* _internal_mutable_style_thumbnail_md5();
  public:

  // int32 style_sort_order = 7;
  void clear_style_sort_order();
  ::PROTOBUF_NAMESPACE_ID::int32 style_sort_order() const;
  void set_style_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_style_sort_order() const;
  void _internal_set_style_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // bool is_check = 8;
  void clear_is_check();
  bool is_check() const;
  void set_is_check(bool value);
  private:
  bool _internal_is_check() const;
  void _internal_set_is_check(bool value);
  public:

  // int32 style_group = 9;
  void clear_style_group();
  ::PROTOBUF_NAMESPACE_ID::int32 style_group() const;
  void set_style_group(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_style_group() const;
  void _internal_set_style_group(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleInfoData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_craft_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_thumbnail_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_thumbnail_md5_;
  ::PROTOBUF_NAMESPACE_ID::int32 style_sort_order_;
  bool is_check_;
  ::PROTOBUF_NAMESPACE_ID::int32 style_group_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleInfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleInfoData

// string style_id = 1;
inline void StyleInfoData::clear_style_id() {
  style_id_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_id)
  return _internal_style_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_id(ArgT0&& arg0, ArgT... args) {
 
 style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_id)
}
inline std::string* StyleInfoData::mutable_style_id() {
  std::string* _s = _internal_mutable_style_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_id)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_id() const {
  return style_id_.Get();
}
inline void StyleInfoData::_internal_set_style_id(const std::string& value) {
  
  style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_id() {
  
  return style_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_id)
  return style_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_id(std::string* style_id) {
  if (style_id != nullptr) {
    
  } else {
    
  }
  style_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_id)
}

// string style_code = 2;
inline void StyleInfoData::clear_style_code() {
  style_code_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_code)
  return _internal_style_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_code(ArgT0&& arg0, ArgT... args) {
 
 style_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_code)
}
inline std::string* StyleInfoData::mutable_style_code() {
  std::string* _s = _internal_mutable_style_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_code)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_code() const {
  return style_code_.Get();
}
inline void StyleInfoData::_internal_set_style_code(const std::string& value) {
  
  style_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_code() {
  
  return style_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_code)
  return style_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_code(std::string* style_code) {
  if (style_code != nullptr) {
    
  } else {
    
  }
  style_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_code)
}

// string style_craft = 3;
inline void StyleInfoData::clear_style_craft() {
  style_craft_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_craft() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_craft)
  return _internal_style_craft();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_craft(ArgT0&& arg0, ArgT... args) {
 
 style_craft_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_craft)
}
inline std::string* StyleInfoData::mutable_style_craft() {
  std::string* _s = _internal_mutable_style_craft();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_craft)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_craft() const {
  return style_craft_.Get();
}
inline void StyleInfoData::_internal_set_style_craft(const std::string& value) {
  
  style_craft_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_craft() {
  
  return style_craft_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_craft() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_craft)
  return style_craft_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_craft(std::string* style_craft) {
  if (style_craft != nullptr) {
    
  } else {
    
  }
  style_craft_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_craft,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_craft)
}

// string style_description = 4;
inline void StyleInfoData::clear_style_description() {
  style_description_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_description() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_description)
  return _internal_style_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_description(ArgT0&& arg0, ArgT... args) {
 
 style_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_description)
}
inline std::string* StyleInfoData::mutable_style_description() {
  std::string* _s = _internal_mutable_style_description();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_description)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_description() const {
  return style_description_.Get();
}
inline void StyleInfoData::_internal_set_style_description(const std::string& value) {
  
  style_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_description() {
  
  return style_description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_description() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_description)
  return style_description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_description(std::string* style_description) {
  if (style_description != nullptr) {
    
  } else {
    
  }
  style_description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_description,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_description)
}

// string style_thumbnail_path = 5;
inline void StyleInfoData::clear_style_thumbnail_path() {
  style_thumbnail_path_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_thumbnail_path() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_thumbnail_path)
  return _internal_style_thumbnail_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_thumbnail_path(ArgT0&& arg0, ArgT... args) {
 
 style_thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_thumbnail_path)
}
inline std::string* StyleInfoData::mutable_style_thumbnail_path() {
  std::string* _s = _internal_mutable_style_thumbnail_path();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_thumbnail_path)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_thumbnail_path() const {
  return style_thumbnail_path_.Get();
}
inline void StyleInfoData::_internal_set_style_thumbnail_path(const std::string& value) {
  
  style_thumbnail_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_thumbnail_path() {
  
  return style_thumbnail_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_thumbnail_path() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_thumbnail_path)
  return style_thumbnail_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_thumbnail_path(std::string* style_thumbnail_path) {
  if (style_thumbnail_path != nullptr) {
    
  } else {
    
  }
  style_thumbnail_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_thumbnail_path,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_thumbnail_path)
}

// string style_thumbnail_md5 = 6;
inline void StyleInfoData::clear_style_thumbnail_md5() {
  style_thumbnail_md5_.ClearToEmpty();
}
inline const std::string& StyleInfoData::style_thumbnail_md5() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_thumbnail_md5)
  return _internal_style_thumbnail_md5();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleInfoData::set_style_thumbnail_md5(ArgT0&& arg0, ArgT... args) {
 
 style_thumbnail_md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_thumbnail_md5)
}
inline std::string* StyleInfoData::mutable_style_thumbnail_md5() {
  std::string* _s = _internal_mutable_style_thumbnail_md5();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleInfoData.style_thumbnail_md5)
  return _s;
}
inline const std::string& StyleInfoData::_internal_style_thumbnail_md5() const {
  return style_thumbnail_md5_.Get();
}
inline void StyleInfoData::_internal_set_style_thumbnail_md5(const std::string& value) {
  
  style_thumbnail_md5_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleInfoData::_internal_mutable_style_thumbnail_md5() {
  
  return style_thumbnail_md5_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleInfoData::release_style_thumbnail_md5() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleInfoData.style_thumbnail_md5)
  return style_thumbnail_md5_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleInfoData::set_allocated_style_thumbnail_md5(std::string* style_thumbnail_md5) {
  if (style_thumbnail_md5 != nullptr) {
    
  } else {
    
  }
  style_thumbnail_md5_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_thumbnail_md5,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleInfoData.style_thumbnail_md5)
}

// int32 style_sort_order = 7;
inline void StyleInfoData::clear_style_sort_order() {
  style_sort_order_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleInfoData::_internal_style_sort_order() const {
  return style_sort_order_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleInfoData::style_sort_order() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_sort_order)
  return _internal_style_sort_order();
}
inline void StyleInfoData::_internal_set_style_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  style_sort_order_ = value;
}
inline void StyleInfoData::set_style_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_style_sort_order(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_sort_order)
}

// bool is_check = 8;
inline void StyleInfoData::clear_is_check() {
  is_check_ = false;
}
inline bool StyleInfoData::_internal_is_check() const {
  return is_check_;
}
inline bool StyleInfoData::is_check() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.is_check)
  return _internal_is_check();
}
inline void StyleInfoData::_internal_set_is_check(bool value) {
  
  is_check_ = value;
}
inline void StyleInfoData::set_is_check(bool value) {
  _internal_set_is_check(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.is_check)
}

// int32 style_group = 9;
inline void StyleInfoData::clear_style_group() {
  style_group_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleInfoData::_internal_style_group() const {
  return style_group_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleInfoData::style_group() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleInfoData.style_group)
  return _internal_style_group();
}
inline void StyleInfoData::_internal_set_style_group(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  style_group_ = value;
}
inline void StyleInfoData::set_style_group(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_style_group(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleInfoData.style_group)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleInfo_2eproto
