// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOption.proto

#include "StyleOption.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleOptionData::StyleOptionData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : option_params_()
  , option_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_visibility_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_visibility_exp_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_thumbnail_url_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_content_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_custom_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , option_sort_order_(0)
  , option_data_source_(0){}
struct StyleOptionDataDefaultTypeInternal {
  constexpr StyleOptionDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleOptionDataDefaultTypeInternal() {}
  union {
    StyleOptionData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleOptionDataDefaultTypeInternal _StyleOptionData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleOption_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleOption_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleOption_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleOption_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_code_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_description_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_visibility_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_visibility_exp_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_sort_order_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_params_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_thumbnail_url_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_content_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_data_source_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionData, option_custom_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleOptionData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleOptionData_default_instance_),
};

const char descriptor_table_protodef_StyleOption_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\021StyleOption.proto\022\026catalog_studio_mess"
  "age\032\023ParameterData.proto\"\327\002\n\017StyleOption"
  "Data\022\021\n\toption_id\030\001 \001(\t\022\023\n\013option_code\030\002"
  " \001(\t\022\032\n\022option_description\030\003 \001(\t\022\031\n\021opti"
  "on_visibility\030\004 \001(\t\022\035\n\025option_visibility"
  "_exp\030\005 \001(\t\022\031\n\021option_sort_order\030\006 \001(\005\022<\n"
  "\roption_params\030\007 \003(\0132%.catalog_studio_me"
  "ssage.ParameterData\022\034\n\024option_thumbnail_"
  "url\030\010 \001(\t\022\031\n\021option_content_id\030\t \001(\t\022\032\n\022"
  "option_data_source\030\n \001(\005\022\030\n\020option_custo"
  "m_id\030\013 \001(\tb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_StyleOption_2eproto_deps[1] = {
  &::descriptor_table_ParameterData_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleOption_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOption_2eproto = {
  false, false, 418, descriptor_table_protodef_StyleOption_2eproto, "StyleOption.proto", 
  &descriptor_table_StyleOption_2eproto_once, descriptor_table_StyleOption_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_StyleOption_2eproto::offsets,
  file_level_metadata_StyleOption_2eproto, file_level_enum_descriptors_StyleOption_2eproto, file_level_service_descriptors_StyleOption_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleOption_2eproto_getter() {
  return &descriptor_table_StyleOption_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleOption_2eproto(&descriptor_table_StyleOption_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleOptionData::_Internal {
 public:
};

void StyleOptionData::clear_option_params() {
  option_params_.Clear();
}
StyleOptionData::StyleOptionData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  option_params_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleOptionData)
}
StyleOptionData::StyleOptionData(const StyleOptionData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      option_params_(from.option_params_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  option_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_id().empty()) {
    option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_id(), 
      GetArenaForAllocation());
  }
  option_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_code().empty()) {
    option_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_code(), 
      GetArenaForAllocation());
  }
  option_description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_description().empty()) {
    option_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_description(), 
      GetArenaForAllocation());
  }
  option_visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_visibility().empty()) {
    option_visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_visibility(), 
      GetArenaForAllocation());
  }
  option_visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_visibility_exp().empty()) {
    option_visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_visibility_exp(), 
      GetArenaForAllocation());
  }
  option_thumbnail_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_thumbnail_url().empty()) {
    option_thumbnail_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_thumbnail_url(), 
      GetArenaForAllocation());
  }
  option_content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_content_id().empty()) {
    option_content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_content_id(), 
      GetArenaForAllocation());
  }
  option_custom_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_custom_id().empty()) {
    option_custom_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_custom_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&option_sort_order_, &from.option_sort_order_,
    static_cast<size_t>(reinterpret_cast<char*>(&option_data_source_) -
    reinterpret_cast<char*>(&option_sort_order_)) + sizeof(option_data_source_));
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleOptionData)
}

void StyleOptionData::SharedCtor() {
option_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_visibility_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_visibility_exp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_thumbnail_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
option_custom_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&option_sort_order_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&option_data_source_) -
    reinterpret_cast<char*>(&option_sort_order_)) + sizeof(option_data_source_));
}

StyleOptionData::~StyleOptionData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleOptionData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleOptionData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  option_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_visibility_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_visibility_exp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_thumbnail_url_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_content_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  option_custom_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StyleOptionData::ArenaDtor(void* object) {
  StyleOptionData* _this = reinterpret_cast< StyleOptionData* >(object);
  (void)_this;
}
void StyleOptionData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleOptionData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleOptionData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleOptionData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  option_params_.Clear();
  option_id_.ClearToEmpty();
  option_code_.ClearToEmpty();
  option_description_.ClearToEmpty();
  option_visibility_.ClearToEmpty();
  option_visibility_exp_.ClearToEmpty();
  option_thumbnail_url_.ClearToEmpty();
  option_content_id_.ClearToEmpty();
  option_custom_id_.ClearToEmpty();
  ::memset(&option_sort_order_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&option_data_source_) -
      reinterpret_cast<char*>(&option_sort_order_)) + sizeof(option_data_source_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleOptionData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string option_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_option_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_code = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_option_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_description = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_option_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_visibility = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_option_visibility();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_visibility"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_visibility_exp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          auto str = _internal_mutable_option_visibility_exp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_visibility_exp"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 option_sort_order = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          option_sort_order_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.ParameterData option_params = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_option_params(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string option_thumbnail_url = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_option_thumbnail_url();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_thumbnail_url"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_content_id = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_option_content_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_content_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 option_data_source = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          option_data_source_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string option_custom_id = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_option_custom_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionData.option_custom_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleOptionData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleOptionData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string option_id = 1;
  if (!this->_internal_option_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_id().data(), static_cast<int>(this->_internal_option_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_option_id(), target);
  }

  // string option_code = 2;
  if (!this->_internal_option_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_code().data(), static_cast<int>(this->_internal_option_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_code");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_option_code(), target);
  }

  // string option_description = 3;
  if (!this->_internal_option_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_description().data(), static_cast<int>(this->_internal_option_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_description");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_option_description(), target);
  }

  // string option_visibility = 4;
  if (!this->_internal_option_visibility().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_visibility().data(), static_cast<int>(this->_internal_option_visibility().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_visibility");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_option_visibility(), target);
  }

  // string option_visibility_exp = 5;
  if (!this->_internal_option_visibility_exp().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_visibility_exp().data(), static_cast<int>(this->_internal_option_visibility_exp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_visibility_exp");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_option_visibility_exp(), target);
  }

  // int32 option_sort_order = 6;
  if (this->_internal_option_sort_order() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_option_sort_order(), target);
  }

  // repeated .catalog_studio_message.ParameterData option_params = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_option_params_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_option_params(i), target, stream);
  }

  // string option_thumbnail_url = 8;
  if (!this->_internal_option_thumbnail_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_thumbnail_url().data(), static_cast<int>(this->_internal_option_thumbnail_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_thumbnail_url");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_option_thumbnail_url(), target);
  }

  // string option_content_id = 9;
  if (!this->_internal_option_content_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_content_id().data(), static_cast<int>(this->_internal_option_content_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_content_id");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_option_content_id(), target);
  }

  // int32 option_data_source = 10;
  if (this->_internal_option_data_source() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(10, this->_internal_option_data_source(), target);
  }

  // string option_custom_id = 11;
  if (!this->_internal_option_custom_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_custom_id().data(), static_cast<int>(this->_internal_option_custom_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionData.option_custom_id");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_option_custom_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleOptionData)
  return target;
}

size_t StyleOptionData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleOptionData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.ParameterData option_params = 7;
  total_size += 1UL * this->_internal_option_params_size();
  for (const auto& msg : this->option_params_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string option_id = 1;
  if (!this->_internal_option_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_id());
  }

  // string option_code = 2;
  if (!this->_internal_option_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_code());
  }

  // string option_description = 3;
  if (!this->_internal_option_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_description());
  }

  // string option_visibility = 4;
  if (!this->_internal_option_visibility().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_visibility());
  }

  // string option_visibility_exp = 5;
  if (!this->_internal_option_visibility_exp().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_visibility_exp());
  }

  // string option_thumbnail_url = 8;
  if (!this->_internal_option_thumbnail_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_thumbnail_url());
  }

  // string option_content_id = 9;
  if (!this->_internal_option_content_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_content_id());
  }

  // string option_custom_id = 11;
  if (!this->_internal_option_custom_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_custom_id());
  }

  // int32 option_sort_order = 6;
  if (this->_internal_option_sort_order() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_option_sort_order());
  }

  // int32 option_data_source = 10;
  if (this->_internal_option_data_source() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_option_data_source());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleOptionData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleOptionData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleOptionData::GetClassData() const { return &_class_data_; }

void StyleOptionData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleOptionData *>(to)->MergeFrom(
      static_cast<const StyleOptionData &>(from));
}


void StyleOptionData::MergeFrom(const StyleOptionData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleOptionData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  option_params_.MergeFrom(from.option_params_);
  if (!from._internal_option_id().empty()) {
    _internal_set_option_id(from._internal_option_id());
  }
  if (!from._internal_option_code().empty()) {
    _internal_set_option_code(from._internal_option_code());
  }
  if (!from._internal_option_description().empty()) {
    _internal_set_option_description(from._internal_option_description());
  }
  if (!from._internal_option_visibility().empty()) {
    _internal_set_option_visibility(from._internal_option_visibility());
  }
  if (!from._internal_option_visibility_exp().empty()) {
    _internal_set_option_visibility_exp(from._internal_option_visibility_exp());
  }
  if (!from._internal_option_thumbnail_url().empty()) {
    _internal_set_option_thumbnail_url(from._internal_option_thumbnail_url());
  }
  if (!from._internal_option_content_id().empty()) {
    _internal_set_option_content_id(from._internal_option_content_id());
  }
  if (!from._internal_option_custom_id().empty()) {
    _internal_set_option_custom_id(from._internal_option_custom_id());
  }
  if (from._internal_option_sort_order() != 0) {
    _internal_set_option_sort_order(from._internal_option_sort_order());
  }
  if (from._internal_option_data_source() != 0) {
    _internal_set_option_data_source(from._internal_option_data_source());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleOptionData::CopyFrom(const StyleOptionData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleOptionData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleOptionData::IsInitialized() const {
  return true;
}

void StyleOptionData::InternalSwap(StyleOptionData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  option_params_.InternalSwap(&other->option_params_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_id_, lhs_arena,
      &other->option_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_code_, lhs_arena,
      &other->option_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_description_, lhs_arena,
      &other->option_description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_visibility_, lhs_arena,
      &other->option_visibility_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_visibility_exp_, lhs_arena,
      &other->option_visibility_exp_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_thumbnail_url_, lhs_arena,
      &other->option_thumbnail_url_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_content_id_, lhs_arena,
      &other->option_content_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_custom_id_, lhs_arena,
      &other->option_custom_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StyleOptionData, option_data_source_)
      + sizeof(StyleOptionData::option_data_source_)
      - PROTOBUF_FIELD_OFFSET(StyleOptionData, option_sort_order_)>(
          reinterpret_cast<char*>(&option_sort_order_),
          reinterpret_cast<char*>(&other->option_sort_order_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleOptionData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleOption_2eproto_getter, &descriptor_table_StyleOption_2eproto_once,
      file_level_metadata_StyleOption_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleOptionData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleOptionData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleOptionData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
