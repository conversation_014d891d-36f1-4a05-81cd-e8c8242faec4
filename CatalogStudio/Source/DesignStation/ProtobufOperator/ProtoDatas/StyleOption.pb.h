// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOption.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleOption_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleOption_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "ParameterData.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleOption_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleOption_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOption_2eproto;
namespace catalog_studio_message {
class StyleOptionData;
struct StyleOptionDataDefaultTypeInternal;
extern StyleOptionDataDefaultTypeInternal _StyleOptionData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleOptionData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleOptionData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleOptionData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleOptionData) */ {
 public:
  inline StyleOptionData() : StyleOptionData(nullptr) {}
  ~StyleOptionData() override;
  explicit constexpr StyleOptionData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleOptionData(const StyleOptionData& from);
  StyleOptionData(StyleOptionData&& from) noexcept
    : StyleOptionData() {
    *this = ::std::move(from);
  }

  inline StyleOptionData& operator=(const StyleOptionData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleOptionData& operator=(StyleOptionData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleOptionData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleOptionData* internal_default_instance() {
    return reinterpret_cast<const StyleOptionData*>(
               &_StyleOptionData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleOptionData& a, StyleOptionData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleOptionData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleOptionData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleOptionData* New() const final {
    return new StyleOptionData();
  }

  StyleOptionData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleOptionData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleOptionData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleOptionData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleOptionData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleOptionData";
  }
  protected:
  explicit StyleOptionData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionParamsFieldNumber = 7,
    kOptionIdFieldNumber = 1,
    kOptionCodeFieldNumber = 2,
    kOptionDescriptionFieldNumber = 3,
    kOptionVisibilityFieldNumber = 4,
    kOptionVisibilityExpFieldNumber = 5,
    kOptionThumbnailUrlFieldNumber = 8,
    kOptionContentIdFieldNumber = 9,
    kOptionCustomIdFieldNumber = 11,
    kOptionSortOrderFieldNumber = 6,
    kOptionDataSourceFieldNumber = 10,
  };
  // repeated .catalog_studio_message.ParameterData option_params = 7;
  int option_params_size() const;
  private:
  int _internal_option_params_size() const;
  public:
  void clear_option_params();
  ::catalog_studio_message::ParameterData* mutable_option_params(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
      mutable_option_params();
  private:
  const ::catalog_studio_message::ParameterData& _internal_option_params(int index) const;
  ::catalog_studio_message::ParameterData* _internal_add_option_params();
  public:
  const ::catalog_studio_message::ParameterData& option_params(int index) const;
  ::catalog_studio_message::ParameterData* add_option_params();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
      option_params() const;

  // string option_id = 1;
  void clear_option_id();
  const std::string& option_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_id();
  void set_allocated_option_id(std::string* option_id);
  private:
  const std::string& _internal_option_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_id(const std::string& value);
  std::string* _internal_mutable_option_id();
  public:

  // string option_code = 2;
  void clear_option_code();
  const std::string& option_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_code();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_code();
  void set_allocated_option_code(std::string* option_code);
  private:
  const std::string& _internal_option_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_code(const std::string& value);
  std::string* _internal_mutable_option_code();
  public:

  // string option_description = 3;
  void clear_option_description();
  const std::string& option_description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_description();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_description();
  void set_allocated_option_description(std::string* option_description);
  private:
  const std::string& _internal_option_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_description(const std::string& value);
  std::string* _internal_mutable_option_description();
  public:

  // string option_visibility = 4;
  void clear_option_visibility();
  const std::string& option_visibility() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_visibility(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_visibility();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_visibility();
  void set_allocated_option_visibility(std::string* option_visibility);
  private:
  const std::string& _internal_option_visibility() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_visibility(const std::string& value);
  std::string* _internal_mutable_option_visibility();
  public:

  // string option_visibility_exp = 5;
  void clear_option_visibility_exp();
  const std::string& option_visibility_exp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_visibility_exp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_visibility_exp();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_visibility_exp();
  void set_allocated_option_visibility_exp(std::string* option_visibility_exp);
  private:
  const std::string& _internal_option_visibility_exp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_visibility_exp(const std::string& value);
  std::string* _internal_mutable_option_visibility_exp();
  public:

  // string option_thumbnail_url = 8;
  void clear_option_thumbnail_url();
  const std::string& option_thumbnail_url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_thumbnail_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_thumbnail_url();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_thumbnail_url();
  void set_allocated_option_thumbnail_url(std::string* option_thumbnail_url);
  private:
  const std::string& _internal_option_thumbnail_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_thumbnail_url(const std::string& value);
  std::string* _internal_mutable_option_thumbnail_url();
  public:

  // string option_content_id = 9;
  void clear_option_content_id();
  const std::string& option_content_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_content_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_content_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_content_id();
  void set_allocated_option_content_id(std::string* option_content_id);
  private:
  const std::string& _internal_option_content_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_content_id(const std::string& value);
  std::string* _internal_mutable_option_content_id();
  public:

  // string option_custom_id = 11;
  void clear_option_custom_id();
  const std::string& option_custom_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_custom_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_custom_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_custom_id();
  void set_allocated_option_custom_id(std::string* option_custom_id);
  private:
  const std::string& _internal_option_custom_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_custom_id(const std::string& value);
  std::string* _internal_mutable_option_custom_id();
  public:

  // int32 option_sort_order = 6;
  void clear_option_sort_order();
  ::PROTOBUF_NAMESPACE_ID::int32 option_sort_order() const;
  void set_option_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_option_sort_order() const;
  void _internal_set_option_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 option_data_source = 10;
  void clear_option_data_source();
  ::PROTOBUF_NAMESPACE_ID::int32 option_data_source() const;
  void set_option_data_source(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_option_data_source() const;
  void _internal_set_option_data_source(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleOptionData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData > option_params_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_visibility_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_visibility_exp_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_thumbnail_url_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_content_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_custom_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 option_sort_order_;
  ::PROTOBUF_NAMESPACE_ID::int32 option_data_source_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleOption_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleOptionData

// string option_id = 1;
inline void StyleOptionData::clear_option_id() {
  option_id_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_id)
  return _internal_option_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_id(ArgT0&& arg0, ArgT... args) {
 
 option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_id)
}
inline std::string* StyleOptionData::mutable_option_id() {
  std::string* _s = _internal_mutable_option_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_id)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_id() const {
  return option_id_.Get();
}
inline void StyleOptionData::_internal_set_option_id(const std::string& value) {
  
  option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_id() {
  
  return option_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_id)
  return option_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_id(std::string* option_id) {
  if (option_id != nullptr) {
    
  } else {
    
  }
  option_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_id)
}

// string option_code = 2;
inline void StyleOptionData::clear_option_code() {
  option_code_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_code() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_code)
  return _internal_option_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_code(ArgT0&& arg0, ArgT... args) {
 
 option_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_code)
}
inline std::string* StyleOptionData::mutable_option_code() {
  std::string* _s = _internal_mutable_option_code();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_code)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_code() const {
  return option_code_.Get();
}
inline void StyleOptionData::_internal_set_option_code(const std::string& value) {
  
  option_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_code() {
  
  return option_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_code() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_code)
  return option_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_code(std::string* option_code) {
  if (option_code != nullptr) {
    
  } else {
    
  }
  option_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_code,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_code)
}

// string option_description = 3;
inline void StyleOptionData::clear_option_description() {
  option_description_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_description() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_description)
  return _internal_option_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_description(ArgT0&& arg0, ArgT... args) {
 
 option_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_description)
}
inline std::string* StyleOptionData::mutable_option_description() {
  std::string* _s = _internal_mutable_option_description();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_description)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_description() const {
  return option_description_.Get();
}
inline void StyleOptionData::_internal_set_option_description(const std::string& value) {
  
  option_description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_description() {
  
  return option_description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_description() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_description)
  return option_description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_description(std::string* option_description) {
  if (option_description != nullptr) {
    
  } else {
    
  }
  option_description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_description,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_description)
}

// string option_visibility = 4;
inline void StyleOptionData::clear_option_visibility() {
  option_visibility_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_visibility() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_visibility)
  return _internal_option_visibility();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_visibility(ArgT0&& arg0, ArgT... args) {
 
 option_visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_visibility)
}
inline std::string* StyleOptionData::mutable_option_visibility() {
  std::string* _s = _internal_mutable_option_visibility();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_visibility)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_visibility() const {
  return option_visibility_.Get();
}
inline void StyleOptionData::_internal_set_option_visibility(const std::string& value) {
  
  option_visibility_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_visibility() {
  
  return option_visibility_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_visibility() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_visibility)
  return option_visibility_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_visibility(std::string* option_visibility) {
  if (option_visibility != nullptr) {
    
  } else {
    
  }
  option_visibility_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_visibility,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_visibility)
}

// string option_visibility_exp = 5;
inline void StyleOptionData::clear_option_visibility_exp() {
  option_visibility_exp_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_visibility_exp() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_visibility_exp)
  return _internal_option_visibility_exp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_visibility_exp(ArgT0&& arg0, ArgT... args) {
 
 option_visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_visibility_exp)
}
inline std::string* StyleOptionData::mutable_option_visibility_exp() {
  std::string* _s = _internal_mutable_option_visibility_exp();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_visibility_exp)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_visibility_exp() const {
  return option_visibility_exp_.Get();
}
inline void StyleOptionData::_internal_set_option_visibility_exp(const std::string& value) {
  
  option_visibility_exp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_visibility_exp() {
  
  return option_visibility_exp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_visibility_exp() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_visibility_exp)
  return option_visibility_exp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_visibility_exp(std::string* option_visibility_exp) {
  if (option_visibility_exp != nullptr) {
    
  } else {
    
  }
  option_visibility_exp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_visibility_exp,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_visibility_exp)
}

// int32 option_sort_order = 6;
inline void StyleOptionData::clear_option_sort_order() {
  option_sort_order_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleOptionData::_internal_option_sort_order() const {
  return option_sort_order_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleOptionData::option_sort_order() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_sort_order)
  return _internal_option_sort_order();
}
inline void StyleOptionData::_internal_set_option_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  option_sort_order_ = value;
}
inline void StyleOptionData::set_option_sort_order(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_option_sort_order(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_sort_order)
}

// repeated .catalog_studio_message.ParameterData option_params = 7;
inline int StyleOptionData::_internal_option_params_size() const {
  return option_params_.size();
}
inline int StyleOptionData::option_params_size() const {
  return _internal_option_params_size();
}
inline ::catalog_studio_message::ParameterData* StyleOptionData::mutable_option_params(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_params)
  return option_params_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >*
StyleOptionData::mutable_option_params() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleOptionData.option_params)
  return &option_params_;
}
inline const ::catalog_studio_message::ParameterData& StyleOptionData::_internal_option_params(int index) const {
  return option_params_.Get(index);
}
inline const ::catalog_studio_message::ParameterData& StyleOptionData::option_params(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_params)
  return _internal_option_params(index);
}
inline ::catalog_studio_message::ParameterData* StyleOptionData::_internal_add_option_params() {
  return option_params_.Add();
}
inline ::catalog_studio_message::ParameterData* StyleOptionData::add_option_params() {
  ::catalog_studio_message::ParameterData* _add = _internal_add_option_params();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleOptionData.option_params)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::ParameterData >&
StyleOptionData::option_params() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleOptionData.option_params)
  return option_params_;
}

// string option_thumbnail_url = 8;
inline void StyleOptionData::clear_option_thumbnail_url() {
  option_thumbnail_url_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_thumbnail_url() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_thumbnail_url)
  return _internal_option_thumbnail_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_thumbnail_url(ArgT0&& arg0, ArgT... args) {
 
 option_thumbnail_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_thumbnail_url)
}
inline std::string* StyleOptionData::mutable_option_thumbnail_url() {
  std::string* _s = _internal_mutable_option_thumbnail_url();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_thumbnail_url)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_thumbnail_url() const {
  return option_thumbnail_url_.Get();
}
inline void StyleOptionData::_internal_set_option_thumbnail_url(const std::string& value) {
  
  option_thumbnail_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_thumbnail_url() {
  
  return option_thumbnail_url_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_thumbnail_url() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_thumbnail_url)
  return option_thumbnail_url_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_thumbnail_url(std::string* option_thumbnail_url) {
  if (option_thumbnail_url != nullptr) {
    
  } else {
    
  }
  option_thumbnail_url_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_thumbnail_url,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_thumbnail_url)
}

// string option_content_id = 9;
inline void StyleOptionData::clear_option_content_id() {
  option_content_id_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_content_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_content_id)
  return _internal_option_content_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_content_id(ArgT0&& arg0, ArgT... args) {
 
 option_content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_content_id)
}
inline std::string* StyleOptionData::mutable_option_content_id() {
  std::string* _s = _internal_mutable_option_content_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_content_id)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_content_id() const {
  return option_content_id_.Get();
}
inline void StyleOptionData::_internal_set_option_content_id(const std::string& value) {
  
  option_content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_content_id() {
  
  return option_content_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_content_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_content_id)
  return option_content_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_content_id(std::string* option_content_id) {
  if (option_content_id != nullptr) {
    
  } else {
    
  }
  option_content_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_content_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_content_id)
}

// int32 option_data_source = 10;
inline void StyleOptionData::clear_option_data_source() {
  option_data_source_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleOptionData::_internal_option_data_source() const {
  return option_data_source_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 StyleOptionData::option_data_source() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_data_source)
  return _internal_option_data_source();
}
inline void StyleOptionData::_internal_set_option_data_source(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  option_data_source_ = value;
}
inline void StyleOptionData::set_option_data_source(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_option_data_source(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_data_source)
}

// string option_custom_id = 11;
inline void StyleOptionData::clear_option_custom_id() {
  option_custom_id_.ClearToEmpty();
}
inline const std::string& StyleOptionData::option_custom_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionData.option_custom_id)
  return _internal_option_custom_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionData::set_option_custom_id(ArgT0&& arg0, ArgT... args) {
 
 option_custom_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionData.option_custom_id)
}
inline std::string* StyleOptionData::mutable_option_custom_id() {
  std::string* _s = _internal_mutable_option_custom_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionData.option_custom_id)
  return _s;
}
inline const std::string& StyleOptionData::_internal_option_custom_id() const {
  return option_custom_id_.Get();
}
inline void StyleOptionData::_internal_set_option_custom_id(const std::string& value) {
  
  option_custom_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionData::_internal_mutable_option_custom_id() {
  
  return option_custom_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionData::release_option_custom_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionData.option_custom_id)
  return option_custom_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionData::set_allocated_option_custom_id(std::string* option_custom_id) {
  if (option_custom_id != nullptr) {
    
  } else {
    
  }
  option_custom_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_custom_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionData.option_custom_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleOption_2eproto
