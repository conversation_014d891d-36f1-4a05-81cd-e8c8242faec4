// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOptionCheck.proto

#include "StyleOptionCheck.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleOptionCheckData::StyleOptionCheckData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : option_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , content_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , style_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , is_prime_(false){}
struct StyleOptionCheckDataDefaultTypeInternal {
  constexpr StyleOptionCheckDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleOptionCheckDataDefaultTypeInternal() {}
  union {
    StyleOptionCheckData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleOptionCheckDataDefaultTypeInternal _StyleOptionCheckData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleOptionCheck_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleOptionCheck_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleOptionCheck_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleOptionCheck_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckData, option_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckData, content_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckData, style_id_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckData, is_prime_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleOptionCheckData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleOptionCheckData_default_instance_),
};

const char descriptor_table_protodef_StyleOptionCheck_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026StyleOptionCheck.proto\022\026catalog_studio"
  "_message\"a\n\024StyleOptionCheckData\022\021\n\topti"
  "on_id\030\001 \001(\t\022\022\n\ncontent_id\030\002 \001(\t\022\020\n\010style"
  "_id\030\003 \001(\t\022\020\n\010is_prime\030\004 \001(\010b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleOptionCheck_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOptionCheck_2eproto = {
  false, false, 155, descriptor_table_protodef_StyleOptionCheck_2eproto, "StyleOptionCheck.proto", 
  &descriptor_table_StyleOptionCheck_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_StyleOptionCheck_2eproto::offsets,
  file_level_metadata_StyleOptionCheck_2eproto, file_level_enum_descriptors_StyleOptionCheck_2eproto, file_level_service_descriptors_StyleOptionCheck_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleOptionCheck_2eproto_getter() {
  return &descriptor_table_StyleOptionCheck_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleOptionCheck_2eproto(&descriptor_table_StyleOptionCheck_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleOptionCheckData::_Internal {
 public:
};

StyleOptionCheckData::StyleOptionCheckData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleOptionCheckData)
}
StyleOptionCheckData::StyleOptionCheckData(const StyleOptionCheckData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  option_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_option_id().empty()) {
    option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_option_id(), 
      GetArenaForAllocation());
  }
  content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content_id().empty()) {
    content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content_id(), 
      GetArenaForAllocation());
  }
  style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_id().empty()) {
    style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_id(), 
      GetArenaForAllocation());
  }
  is_prime_ = from.is_prime_;
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleOptionCheckData)
}

void StyleOptionCheckData::SharedCtor() {
option_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
content_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
is_prime_ = false;
}

StyleOptionCheckData::~StyleOptionCheckData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleOptionCheckData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleOptionCheckData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  option_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  content_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  style_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StyleOptionCheckData::ArenaDtor(void* object) {
  StyleOptionCheckData* _this = reinterpret_cast< StyleOptionCheckData* >(object);
  (void)_this;
}
void StyleOptionCheckData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleOptionCheckData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleOptionCheckData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleOptionCheckData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  option_id_.ClearToEmpty();
  content_id_.ClearToEmpty();
  style_id_.ClearToEmpty();
  is_prime_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleOptionCheckData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string option_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_option_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionCheckData.option_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string content_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_content_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionCheckData.content_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string style_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_style_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionCheckData.style_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_prime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          is_prime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleOptionCheckData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleOptionCheckData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string option_id = 1;
  if (!this->_internal_option_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_option_id().data(), static_cast<int>(this->_internal_option_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionCheckData.option_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_option_id(), target);
  }

  // string content_id = 2;
  if (!this->_internal_content_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_content_id().data(), static_cast<int>(this->_internal_content_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionCheckData.content_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_content_id(), target);
  }

  // string style_id = 3;
  if (!this->_internal_style_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_id().data(), static_cast<int>(this->_internal_style_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionCheckData.style_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_style_id(), target);
  }

  // bool is_prime = 4;
  if (this->_internal_is_prime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_is_prime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleOptionCheckData)
  return target;
}

size_t StyleOptionCheckData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleOptionCheckData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string option_id = 1;
  if (!this->_internal_option_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_option_id());
  }

  // string content_id = 2;
  if (!this->_internal_content_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_content_id());
  }

  // string style_id = 3;
  if (!this->_internal_style_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_id());
  }

  // bool is_prime = 4;
  if (this->_internal_is_prime() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleOptionCheckData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleOptionCheckData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleOptionCheckData::GetClassData() const { return &_class_data_; }

void StyleOptionCheckData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleOptionCheckData *>(to)->MergeFrom(
      static_cast<const StyleOptionCheckData &>(from));
}


void StyleOptionCheckData::MergeFrom(const StyleOptionCheckData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleOptionCheckData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_option_id().empty()) {
    _internal_set_option_id(from._internal_option_id());
  }
  if (!from._internal_content_id().empty()) {
    _internal_set_content_id(from._internal_content_id());
  }
  if (!from._internal_style_id().empty()) {
    _internal_set_style_id(from._internal_style_id());
  }
  if (from._internal_is_prime() != 0) {
    _internal_set_is_prime(from._internal_is_prime());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleOptionCheckData::CopyFrom(const StyleOptionCheckData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleOptionCheckData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleOptionCheckData::IsInitialized() const {
  return true;
}

void StyleOptionCheckData::InternalSwap(StyleOptionCheckData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &option_id_, lhs_arena,
      &other->option_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &content_id_, lhs_arena,
      &other->content_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_id_, lhs_arena,
      &other->style_id_, rhs_arena
  );
  swap(is_prime_, other->is_prime_);
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleOptionCheckData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleOptionCheck_2eproto_getter, &descriptor_table_StyleOptionCheck_2eproto_once,
      file_level_metadata_StyleOptionCheck_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleOptionCheckData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleOptionCheckData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleOptionCheckData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
