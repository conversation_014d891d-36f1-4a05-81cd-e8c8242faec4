// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOptionCheck.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheck_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheck_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleOptionCheck_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleOptionCheck_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOptionCheck_2eproto;
namespace catalog_studio_message {
class StyleOptionCheckData;
struct StyleOptionCheckDataDefaultTypeInternal;
extern StyleOptionCheckDataDefaultTypeInternal _StyleOptionCheckData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleOptionCheckData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleOptionCheckData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleOptionCheckData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleOptionCheckData) */ {
 public:
  inline StyleOptionCheckData() : StyleOptionCheckData(nullptr) {}
  ~StyleOptionCheckData() override;
  explicit constexpr StyleOptionCheckData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleOptionCheckData(const StyleOptionCheckData& from);
  StyleOptionCheckData(StyleOptionCheckData&& from) noexcept
    : StyleOptionCheckData() {
    *this = ::std::move(from);
  }

  inline StyleOptionCheckData& operator=(const StyleOptionCheckData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleOptionCheckData& operator=(StyleOptionCheckData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleOptionCheckData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleOptionCheckData* internal_default_instance() {
    return reinterpret_cast<const StyleOptionCheckData*>(
               &_StyleOptionCheckData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleOptionCheckData& a, StyleOptionCheckData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleOptionCheckData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleOptionCheckData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleOptionCheckData* New() const final {
    return new StyleOptionCheckData();
  }

  StyleOptionCheckData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleOptionCheckData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleOptionCheckData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleOptionCheckData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleOptionCheckData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleOptionCheckData";
  }
  protected:
  explicit StyleOptionCheckData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionIdFieldNumber = 1,
    kContentIdFieldNumber = 2,
    kStyleIdFieldNumber = 3,
    kIsPrimeFieldNumber = 4,
  };
  // string option_id = 1;
  void clear_option_id();
  const std::string& option_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_option_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_option_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_option_id();
  void set_allocated_option_id(std::string* option_id);
  private:
  const std::string& _internal_option_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_option_id(const std::string& value);
  std::string* _internal_mutable_option_id();
  public:

  // string content_id = 2;
  void clear_content_id();
  const std::string& content_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_content_id();
  void set_allocated_content_id(std::string* content_id);
  private:
  const std::string& _internal_content_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content_id(const std::string& value);
  std::string* _internal_mutable_content_id();
  public:

  // string style_id = 3;
  void clear_style_id();
  const std::string& style_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_id();
  void set_allocated_style_id(std::string* style_id);
  private:
  const std::string& _internal_style_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_id(const std::string& value);
  std::string* _internal_mutable_style_id();
  public:

  // bool is_prime = 4;
  void clear_is_prime();
  bool is_prime() const;
  void set_is_prime(bool value);
  private:
  bool _internal_is_prime() const;
  void _internal_set_is_prime(bool value);
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleOptionCheckData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr option_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_id_;
  bool is_prime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleOptionCheck_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleOptionCheckData

// string option_id = 1;
inline void StyleOptionCheckData::clear_option_id() {
  option_id_.ClearToEmpty();
}
inline const std::string& StyleOptionCheckData::option_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckData.option_id)
  return _internal_option_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionCheckData::set_option_id(ArgT0&& arg0, ArgT... args) {
 
 option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionCheckData.option_id)
}
inline std::string* StyleOptionCheckData::mutable_option_id() {
  std::string* _s = _internal_mutable_option_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionCheckData.option_id)
  return _s;
}
inline const std::string& StyleOptionCheckData::_internal_option_id() const {
  return option_id_.Get();
}
inline void StyleOptionCheckData::_internal_set_option_id(const std::string& value) {
  
  option_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::_internal_mutable_option_id() {
  
  return option_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::release_option_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionCheckData.option_id)
  return option_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionCheckData::set_allocated_option_id(std::string* option_id) {
  if (option_id != nullptr) {
    
  } else {
    
  }
  option_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), option_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionCheckData.option_id)
}

// string content_id = 2;
inline void StyleOptionCheckData::clear_content_id() {
  content_id_.ClearToEmpty();
}
inline const std::string& StyleOptionCheckData::content_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckData.content_id)
  return _internal_content_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionCheckData::set_content_id(ArgT0&& arg0, ArgT... args) {
 
 content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionCheckData.content_id)
}
inline std::string* StyleOptionCheckData::mutable_content_id() {
  std::string* _s = _internal_mutable_content_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionCheckData.content_id)
  return _s;
}
inline const std::string& StyleOptionCheckData::_internal_content_id() const {
  return content_id_.Get();
}
inline void StyleOptionCheckData::_internal_set_content_id(const std::string& value) {
  
  content_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::_internal_mutable_content_id() {
  
  return content_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::release_content_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionCheckData.content_id)
  return content_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionCheckData::set_allocated_content_id(std::string* content_id) {
  if (content_id != nullptr) {
    
  } else {
    
  }
  content_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), content_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionCheckData.content_id)
}

// string style_id = 3;
inline void StyleOptionCheckData::clear_style_id() {
  style_id_.ClearToEmpty();
}
inline const std::string& StyleOptionCheckData::style_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckData.style_id)
  return _internal_style_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionCheckData::set_style_id(ArgT0&& arg0, ArgT... args) {
 
 style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionCheckData.style_id)
}
inline std::string* StyleOptionCheckData::mutable_style_id() {
  std::string* _s = _internal_mutable_style_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionCheckData.style_id)
  return _s;
}
inline const std::string& StyleOptionCheckData::_internal_style_id() const {
  return style_id_.Get();
}
inline void StyleOptionCheckData::_internal_set_style_id(const std::string& value) {
  
  style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::_internal_mutable_style_id() {
  
  return style_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckData::release_style_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionCheckData.style_id)
  return style_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionCheckData::set_allocated_style_id(std::string* style_id) {
  if (style_id != nullptr) {
    
  } else {
    
  }
  style_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionCheckData.style_id)
}

// bool is_prime = 4;
inline void StyleOptionCheckData::clear_is_prime() {
  is_prime_ = false;
}
inline bool StyleOptionCheckData::_internal_is_prime() const {
  return is_prime_;
}
inline bool StyleOptionCheckData::is_prime() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckData.is_prime)
  return _internal_is_prime();
}
inline void StyleOptionCheckData::_internal_set_is_prime(bool value) {
  
  is_prime_ = value;
}
inline void StyleOptionCheckData::set_is_prime(bool value) {
  _internal_set_is_prime(value);
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionCheckData.is_prime)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheck_2eproto
