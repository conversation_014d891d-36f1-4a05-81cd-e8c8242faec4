// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOptionCheckArr.proto

#include "StyleOptionCheckArr.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleOptionCheckArrData::StyleOptionCheckArrData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : option_checks_()
  , style_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct StyleOptionCheckArrDataDefaultTypeInternal {
  constexpr StyleOptionCheckArrDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleOptionCheckArrDataDefaultTypeInternal() {}
  union {
    StyleOptionCheckArrData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleOptionCheckArrDataDefaultTypeInternal _StyleOptionCheckArrData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleOptionCheckArr_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleOptionCheckArr_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleOptionCheckArr_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleOptionCheckArr_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckArrData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckArrData, option_checks_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleOptionCheckArrData, style_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleOptionCheckArrData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleOptionCheckArrData_default_instance_),
};

const char descriptor_table_protodef_StyleOptionCheckArr_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031StyleOptionCheckArr.proto\022\026catalog_stu"
  "dio_message\032\026StyleOptionCheck.proto\"p\n\027S"
  "tyleOptionCheckArrData\022C\n\roption_checks\030"
  "\001 \003(\0132,.catalog_studio_message.StyleOpti"
  "onCheckData\022\020\n\010style_id\030\002 \001(\tb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_StyleOptionCheckArr_2eproto_deps[1] = {
  &::descriptor_table_StyleOptionCheck_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleOptionCheckArr_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOptionCheckArr_2eproto = {
  false, false, 197, descriptor_table_protodef_StyleOptionCheckArr_2eproto, "StyleOptionCheckArr.proto", 
  &descriptor_table_StyleOptionCheckArr_2eproto_once, descriptor_table_StyleOptionCheckArr_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_StyleOptionCheckArr_2eproto::offsets,
  file_level_metadata_StyleOptionCheckArr_2eproto, file_level_enum_descriptors_StyleOptionCheckArr_2eproto, file_level_service_descriptors_StyleOptionCheckArr_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleOptionCheckArr_2eproto_getter() {
  return &descriptor_table_StyleOptionCheckArr_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleOptionCheckArr_2eproto(&descriptor_table_StyleOptionCheckArr_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleOptionCheckArrData::_Internal {
 public:
};

void StyleOptionCheckArrData::clear_option_checks() {
  option_checks_.Clear();
}
StyleOptionCheckArrData::StyleOptionCheckArrData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  option_checks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleOptionCheckArrData)
}
StyleOptionCheckArrData::StyleOptionCheckArrData(const StyleOptionCheckArrData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      option_checks_(from.option_checks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_style_id().empty()) {
    style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_style_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleOptionCheckArrData)
}

void StyleOptionCheckArrData::SharedCtor() {
style_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

StyleOptionCheckArrData::~StyleOptionCheckArrData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleOptionCheckArrData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleOptionCheckArrData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  style_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StyleOptionCheckArrData::ArenaDtor(void* object) {
  StyleOptionCheckArrData* _this = reinterpret_cast< StyleOptionCheckArrData* >(object);
  (void)_this;
}
void StyleOptionCheckArrData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleOptionCheckArrData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleOptionCheckArrData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleOptionCheckArrData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  option_checks_.Clear();
  style_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleOptionCheckArrData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.StyleOptionCheckData option_checks = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_option_checks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string style_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_style_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "catalog_studio_message.StyleOptionCheckArrData.style_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleOptionCheckArrData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleOptionCheckArrData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.StyleOptionCheckData option_checks = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_option_checks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_option_checks(i), target, stream);
  }

  // string style_id = 2;
  if (!this->_internal_style_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_style_id().data(), static_cast<int>(this->_internal_style_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "catalog_studio_message.StyleOptionCheckArrData.style_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_style_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleOptionCheckArrData)
  return target;
}

size_t StyleOptionCheckArrData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleOptionCheckArrData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.StyleOptionCheckData option_checks = 1;
  total_size += 1UL * this->_internal_option_checks_size();
  for (const auto& msg : this->option_checks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string style_id = 2;
  if (!this->_internal_style_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_style_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleOptionCheckArrData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleOptionCheckArrData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleOptionCheckArrData::GetClassData() const { return &_class_data_; }

void StyleOptionCheckArrData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleOptionCheckArrData *>(to)->MergeFrom(
      static_cast<const StyleOptionCheckArrData &>(from));
}


void StyleOptionCheckArrData::MergeFrom(const StyleOptionCheckArrData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleOptionCheckArrData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  option_checks_.MergeFrom(from.option_checks_);
  if (!from._internal_style_id().empty()) {
    _internal_set_style_id(from._internal_style_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleOptionCheckArrData::CopyFrom(const StyleOptionCheckArrData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleOptionCheckArrData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleOptionCheckArrData::IsInitialized() const {
  return true;
}

void StyleOptionCheckArrData::InternalSwap(StyleOptionCheckArrData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  option_checks_.InternalSwap(&other->option_checks_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &style_id_, lhs_arena,
      &other->style_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleOptionCheckArrData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleOptionCheckArr_2eproto_getter, &descriptor_table_StyleOptionCheckArr_2eproto_once,
      file_level_metadata_StyleOptionCheckArr_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleOptionCheckArrData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleOptionCheckArrData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleOptionCheckArrData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
