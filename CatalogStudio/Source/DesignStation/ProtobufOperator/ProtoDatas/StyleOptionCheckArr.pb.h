// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleOptionCheckArr.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheckArr_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheckArr_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "StyleOptionCheck.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleOptionCheckArr_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleOptionCheckArr_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleOptionCheckArr_2eproto;
namespace catalog_studio_message {
class StyleOptionCheckArrData;
struct StyleOptionCheckArrDataDefaultTypeInternal;
extern StyleOptionCheckArrDataDefaultTypeInternal _StyleOptionCheckArrData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleOptionCheckArrData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleOptionCheckArrData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleOptionCheckArrData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleOptionCheckArrData) */ {
 public:
  inline StyleOptionCheckArrData() : StyleOptionCheckArrData(nullptr) {}
  ~StyleOptionCheckArrData() override;
  explicit constexpr StyleOptionCheckArrData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleOptionCheckArrData(const StyleOptionCheckArrData& from);
  StyleOptionCheckArrData(StyleOptionCheckArrData&& from) noexcept
    : StyleOptionCheckArrData() {
    *this = ::std::move(from);
  }

  inline StyleOptionCheckArrData& operator=(const StyleOptionCheckArrData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleOptionCheckArrData& operator=(StyleOptionCheckArrData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleOptionCheckArrData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleOptionCheckArrData* internal_default_instance() {
    return reinterpret_cast<const StyleOptionCheckArrData*>(
               &_StyleOptionCheckArrData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleOptionCheckArrData& a, StyleOptionCheckArrData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleOptionCheckArrData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleOptionCheckArrData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleOptionCheckArrData* New() const final {
    return new StyleOptionCheckArrData();
  }

  StyleOptionCheckArrData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleOptionCheckArrData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleOptionCheckArrData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleOptionCheckArrData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleOptionCheckArrData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleOptionCheckArrData";
  }
  protected:
  explicit StyleOptionCheckArrData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionChecksFieldNumber = 1,
    kStyleIdFieldNumber = 2,
  };
  // repeated .catalog_studio_message.StyleOptionCheckData option_checks = 1;
  int option_checks_size() const;
  private:
  int _internal_option_checks_size() const;
  public:
  void clear_option_checks();
  ::catalog_studio_message::StyleOptionCheckData* mutable_option_checks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckData >*
      mutable_option_checks();
  private:
  const ::catalog_studio_message::StyleOptionCheckData& _internal_option_checks(int index) const;
  ::catalog_studio_message::StyleOptionCheckData* _internal_add_option_checks();
  public:
  const ::catalog_studio_message::StyleOptionCheckData& option_checks(int index) const;
  ::catalog_studio_message::StyleOptionCheckData* add_option_checks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckData >&
      option_checks() const;

  // string style_id = 2;
  void clear_style_id();
  const std::string& style_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_style_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_style_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_style_id();
  void set_allocated_style_id(std::string* style_id);
  private:
  const std::string& _internal_style_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_style_id(const std::string& value);
  std::string* _internal_mutable_style_id();
  public:

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleOptionCheckArrData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckData > option_checks_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr style_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleOptionCheckArr_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleOptionCheckArrData

// repeated .catalog_studio_message.StyleOptionCheckData option_checks = 1;
inline int StyleOptionCheckArrData::_internal_option_checks_size() const {
  return option_checks_.size();
}
inline int StyleOptionCheckArrData::option_checks_size() const {
  return _internal_option_checks_size();
}
inline ::catalog_studio_message::StyleOptionCheckData* StyleOptionCheckArrData::mutable_option_checks(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionCheckArrData.option_checks)
  return option_checks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckData >*
StyleOptionCheckArrData::mutable_option_checks() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleOptionCheckArrData.option_checks)
  return &option_checks_;
}
inline const ::catalog_studio_message::StyleOptionCheckData& StyleOptionCheckArrData::_internal_option_checks(int index) const {
  return option_checks_.Get(index);
}
inline const ::catalog_studio_message::StyleOptionCheckData& StyleOptionCheckArrData::option_checks(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckArrData.option_checks)
  return _internal_option_checks(index);
}
inline ::catalog_studio_message::StyleOptionCheckData* StyleOptionCheckArrData::_internal_add_option_checks() {
  return option_checks_.Add();
}
inline ::catalog_studio_message::StyleOptionCheckData* StyleOptionCheckArrData::add_option_checks() {
  ::catalog_studio_message::StyleOptionCheckData* _add = _internal_add_option_checks();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleOptionCheckArrData.option_checks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleOptionCheckData >&
StyleOptionCheckArrData::option_checks() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleOptionCheckArrData.option_checks)
  return option_checks_;
}

// string style_id = 2;
inline void StyleOptionCheckArrData::clear_style_id() {
  style_id_.ClearToEmpty();
}
inline const std::string& StyleOptionCheckArrData::style_id() const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleOptionCheckArrData.style_id)
  return _internal_style_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StyleOptionCheckArrData::set_style_id(ArgT0&& arg0, ArgT... args) {
 
 style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:catalog_studio_message.StyleOptionCheckArrData.style_id)
}
inline std::string* StyleOptionCheckArrData::mutable_style_id() {
  std::string* _s = _internal_mutable_style_id();
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleOptionCheckArrData.style_id)
  return _s;
}
inline const std::string& StyleOptionCheckArrData::_internal_style_id() const {
  return style_id_.Get();
}
inline void StyleOptionCheckArrData::_internal_set_style_id(const std::string& value) {
  
  style_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckArrData::_internal_mutable_style_id() {
  
  return style_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StyleOptionCheckArrData::release_style_id() {
  // @@protoc_insertion_point(field_release:catalog_studio_message.StyleOptionCheckArrData.style_id)
  return style_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StyleOptionCheckArrData::set_allocated_style_id(std::string* style_id) {
  if (style_id != nullptr) {
    
  } else {
    
  }
  style_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), style_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:catalog_studio_message.StyleOptionCheckArrData.style_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleOptionCheckArr_2eproto
