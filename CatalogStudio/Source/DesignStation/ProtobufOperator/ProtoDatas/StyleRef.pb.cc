// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleRef.proto

#include "StyleRef.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace catalog_studio_message {
constexpr StyleRefData::StyleRefData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : style_datas_()
  , content_datas_(){}
struct StyleRefDataDefaultTypeInternal {
  constexpr StyleRefDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StyleRefDataDefaultTypeInternal() {}
  union {
    StyleRefData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StyleRefDataDefaultTypeInternal _StyleRefData_default_instance_;
}  // namespace catalog_studio_message
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_StyleRef_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_StyleRef_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_StyleRef_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_StyleRef_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleRefData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleRefData, style_datas_),
  PROTOBUF_FIELD_OFFSET(::catalog_studio_message::StyleRefData, content_datas_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::catalog_studio_message::StyleRefData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::catalog_studio_message::_StyleRefData_default_instance_),
};

const char descriptor_table_protodef_StyleRef_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\016StyleRef.proto\022\026catalog_studio_message"
  "\032\017StyleInfo.proto\032\022StyleContent.proto\"\213\001"
  "\n\014StyleRefData\022:\n\013style_datas\030\001 \003(\0132%.ca"
  "talog_studio_message.StyleInfoData\022\?\n\rco"
  "ntent_datas\030\002 \003(\0132(.catalog_studio_messa"
  "ge.StyleContentDatab\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_StyleRef_2eproto_deps[2] = {
  &::descriptor_table_StyleContent_2eproto,
  &::descriptor_table_StyleInfo_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_StyleRef_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleRef_2eproto = {
  false, false, 227, descriptor_table_protodef_StyleRef_2eproto, "StyleRef.proto", 
  &descriptor_table_StyleRef_2eproto_once, descriptor_table_StyleRef_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_StyleRef_2eproto::offsets,
  file_level_metadata_StyleRef_2eproto, file_level_enum_descriptors_StyleRef_2eproto, file_level_service_descriptors_StyleRef_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_StyleRef_2eproto_getter() {
  return &descriptor_table_StyleRef_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_StyleRef_2eproto(&descriptor_table_StyleRef_2eproto);
namespace catalog_studio_message {

// ===================================================================

class StyleRefData::_Internal {
 public:
};

void StyleRefData::clear_style_datas() {
  style_datas_.Clear();
}
void StyleRefData::clear_content_datas() {
  content_datas_.Clear();
}
StyleRefData::StyleRefData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  style_datas_(arena),
  content_datas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:catalog_studio_message.StyleRefData)
}
StyleRefData::StyleRefData(const StyleRefData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      style_datas_(from.style_datas_),
      content_datas_(from.content_datas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:catalog_studio_message.StyleRefData)
}

void StyleRefData::SharedCtor() {
}

StyleRefData::~StyleRefData() {
  // @@protoc_insertion_point(destructor:catalog_studio_message.StyleRefData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StyleRefData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void StyleRefData::ArenaDtor(void* object) {
  StyleRefData* _this = reinterpret_cast< StyleRefData* >(object);
  (void)_this;
}
void StyleRefData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StyleRefData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StyleRefData::Clear() {
// @@protoc_insertion_point(message_clear_start:catalog_studio_message.StyleRefData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  style_datas_.Clear();
  content_datas_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StyleRefData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .catalog_studio_message.StyleInfoData style_datas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_style_datas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .catalog_studio_message.StyleContentData content_datas = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_content_datas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StyleRefData::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:catalog_studio_message.StyleRefData)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .catalog_studio_message.StyleInfoData style_datas = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_style_datas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_style_datas(i), target, stream);
  }

  // repeated .catalog_studio_message.StyleContentData content_datas = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_content_datas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_content_datas(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:catalog_studio_message.StyleRefData)
  return target;
}

size_t StyleRefData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:catalog_studio_message.StyleRefData)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .catalog_studio_message.StyleInfoData style_datas = 1;
  total_size += 1UL * this->_internal_style_datas_size();
  for (const auto& msg : this->style_datas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .catalog_studio_message.StyleContentData content_datas = 2;
  total_size += 1UL * this->_internal_content_datas_size();
  for (const auto& msg : this->content_datas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StyleRefData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StyleRefData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StyleRefData::GetClassData() const { return &_class_data_; }

void StyleRefData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StyleRefData *>(to)->MergeFrom(
      static_cast<const StyleRefData &>(from));
}


void StyleRefData::MergeFrom(const StyleRefData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:catalog_studio_message.StyleRefData)
  GOOGLE_DCHECK_NE(&from, this);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  style_datas_.MergeFrom(from.style_datas_);
  content_datas_.MergeFrom(from.content_datas_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StyleRefData::CopyFrom(const StyleRefData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:catalog_studio_message.StyleRefData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StyleRefData::IsInitialized() const {
  return true;
}

void StyleRefData::InternalSwap(StyleRefData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  style_datas_.InternalSwap(&other->style_datas_);
  content_datas_.InternalSwap(&other->content_datas_);
}

::PROTOBUF_NAMESPACE_ID::Metadata StyleRefData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_StyleRef_2eproto_getter, &descriptor_table_StyleRef_2eproto_once,
      file_level_metadata_StyleRef_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::catalog_studio_message::StyleRefData* Arena::CreateMaybeMessage< ::catalog_studio_message::StyleRefData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::catalog_studio_message::StyleRefData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
