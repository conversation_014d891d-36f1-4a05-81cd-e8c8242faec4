// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: StyleRef.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_StyleRef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_StyleRef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "StyleInfo.pb.h"
#include "StyleContent.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_StyleRef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_StyleRef_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_StyleRef_2eproto;
namespace catalog_studio_message {
class StyleRefData;
struct StyleRefDataDefaultTypeInternal;
extern StyleRefDataDefaultTypeInternal _StyleRefData_default_instance_;
}  // namespace catalog_studio_message
PROTOBUF_NAMESPACE_OPEN
template<> ::catalog_studio_message::StyleRefData* Arena::CreateMaybeMessage<::catalog_studio_message::StyleRefData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace catalog_studio_message {

// ===================================================================

class StyleRefData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:catalog_studio_message.StyleRefData) */ {
 public:
  inline StyleRefData() : StyleRefData(nullptr) {}
  ~StyleRefData() override;
  explicit constexpr StyleRefData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StyleRefData(const StyleRefData& from);
  StyleRefData(StyleRefData&& from) noexcept
    : StyleRefData() {
    *this = ::std::move(from);
  }

  inline StyleRefData& operator=(const StyleRefData& from) {
    CopyFrom(from);
    return *this;
  }
  inline StyleRefData& operator=(StyleRefData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StyleRefData& default_instance() {
    return *internal_default_instance();
  }
  static inline const StyleRefData* internal_default_instance() {
    return reinterpret_cast<const StyleRefData*>(
               &_StyleRefData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StyleRefData& a, StyleRefData& b) {
    a.Swap(&b);
  }
  inline void Swap(StyleRefData* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StyleRefData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline StyleRefData* New() const final {
    return new StyleRefData();
  }

  StyleRefData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<StyleRefData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StyleRefData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StyleRefData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StyleRefData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "catalog_studio_message.StyleRefData";
  }
  protected:
  explicit StyleRefData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStyleDatasFieldNumber = 1,
    kContentDatasFieldNumber = 2,
  };
  // repeated .catalog_studio_message.StyleInfoData style_datas = 1;
  int style_datas_size() const;
  private:
  int _internal_style_datas_size() const;
  public:
  void clear_style_datas();
  ::catalog_studio_message::StyleInfoData* mutable_style_datas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleInfoData >*
      mutable_style_datas();
  private:
  const ::catalog_studio_message::StyleInfoData& _internal_style_datas(int index) const;
  ::catalog_studio_message::StyleInfoData* _internal_add_style_datas();
  public:
  const ::catalog_studio_message::StyleInfoData& style_datas(int index) const;
  ::catalog_studio_message::StyleInfoData* add_style_datas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleInfoData >&
      style_datas() const;

  // repeated .catalog_studio_message.StyleContentData content_datas = 2;
  int content_datas_size() const;
  private:
  int _internal_content_datas_size() const;
  public:
  void clear_content_datas();
  ::catalog_studio_message::StyleContentData* mutable_content_datas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleContentData >*
      mutable_content_datas();
  private:
  const ::catalog_studio_message::StyleContentData& _internal_content_datas(int index) const;
  ::catalog_studio_message::StyleContentData* _internal_add_content_datas();
  public:
  const ::catalog_studio_message::StyleContentData& content_datas(int index) const;
  ::catalog_studio_message::StyleContentData* add_content_datas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleContentData >&
      content_datas() const;

  // @@protoc_insertion_point(class_scope:catalog_studio_message.StyleRefData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleInfoData > style_datas_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleContentData > content_datas_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_StyleRef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StyleRefData

// repeated .catalog_studio_message.StyleInfoData style_datas = 1;
inline int StyleRefData::_internal_style_datas_size() const {
  return style_datas_.size();
}
inline int StyleRefData::style_datas_size() const {
  return _internal_style_datas_size();
}
inline ::catalog_studio_message::StyleInfoData* StyleRefData::mutable_style_datas(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleRefData.style_datas)
  return style_datas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleInfoData >*
StyleRefData::mutable_style_datas() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleRefData.style_datas)
  return &style_datas_;
}
inline const ::catalog_studio_message::StyleInfoData& StyleRefData::_internal_style_datas(int index) const {
  return style_datas_.Get(index);
}
inline const ::catalog_studio_message::StyleInfoData& StyleRefData::style_datas(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleRefData.style_datas)
  return _internal_style_datas(index);
}
inline ::catalog_studio_message::StyleInfoData* StyleRefData::_internal_add_style_datas() {
  return style_datas_.Add();
}
inline ::catalog_studio_message::StyleInfoData* StyleRefData::add_style_datas() {
  ::catalog_studio_message::StyleInfoData* _add = _internal_add_style_datas();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleRefData.style_datas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleInfoData >&
StyleRefData::style_datas() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleRefData.style_datas)
  return style_datas_;
}

// repeated .catalog_studio_message.StyleContentData content_datas = 2;
inline int StyleRefData::_internal_content_datas_size() const {
  return content_datas_.size();
}
inline int StyleRefData::content_datas_size() const {
  return _internal_content_datas_size();
}
inline ::catalog_studio_message::StyleContentData* StyleRefData::mutable_content_datas(int index) {
  // @@protoc_insertion_point(field_mutable:catalog_studio_message.StyleRefData.content_datas)
  return content_datas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleContentData >*
StyleRefData::mutable_content_datas() {
  // @@protoc_insertion_point(field_mutable_list:catalog_studio_message.StyleRefData.content_datas)
  return &content_datas_;
}
inline const ::catalog_studio_message::StyleContentData& StyleRefData::_internal_content_datas(int index) const {
  return content_datas_.Get(index);
}
inline const ::catalog_studio_message::StyleContentData& StyleRefData::content_datas(int index) const {
  // @@protoc_insertion_point(field_get:catalog_studio_message.StyleRefData.content_datas)
  return _internal_content_datas(index);
}
inline ::catalog_studio_message::StyleContentData* StyleRefData::_internal_add_content_datas() {
  return content_datas_.Add();
}
inline ::catalog_studio_message::StyleContentData* StyleRefData::add_content_datas() {
  ::catalog_studio_message::StyleContentData* _add = _internal_add_content_datas();
  // @@protoc_insertion_point(field_add:catalog_studio_message.StyleRefData.content_datas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::catalog_studio_message::StyleContentData >&
StyleRefData::content_datas() const {
  // @@protoc_insertion_point(field_list:catalog_studio_message.StyleRefData.content_datas)
  return content_datas_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace catalog_studio_message

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_StyleRef_2eproto
