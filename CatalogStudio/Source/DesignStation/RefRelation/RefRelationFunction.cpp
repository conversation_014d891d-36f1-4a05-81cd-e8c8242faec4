// Fill out your copyright notice in the Description page of Project Settings.


#include "RefRelationFunction.h"
#include "ProtobufOperator/ProtoDatas/FolderRef.pb.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include <string>
#include "zlib.h"
#include "unzip.h"
#include "zip.h"
#include <codecvt>
#include <locale>

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Internationalization/Culture.h"
#include "Kismet/KismetMathLibrary.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"

#define DIRECTORY_SLASH_MARK TEXT("/")



/*
bool URefRelationFunction::SaveRelationToFile(const FRefToLocalFileData& InRelationDatas)
{
	FString RefFilePathIdentify = URefToFileData::GetFileAddress(InRelationDatas.FolderDBData.folder_id);
	return URefRelationFunction::SaveRelationToFile(RefFilePathIdentify, InRelationDatas);
}

bool URefRelationFunction::LoadRelationFromFile(FRefToLocalFileData& OutRelationDatas)
{
	FString RefFilePathIdentify = URefToFileData::GetFileAddress(OutRelationDatas.FolderDBData.folder_id);
	return URefRelationFunction::LoadRelationFromFile(RefFilePathIdentify, OutRelationDatas);
}

bool URefRelationFunction::SaveRelationToFile(const FRefToStyleFile& InDatas)
{
	FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	return URefRelationFunction::SaveRelationToFile(RefFilePathIdentify, InDatas);
}

bool URefRelationFunction::LoadRelationFromFile(FRefToStyleFile& OutDatas)
{
	FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	return URefRelationFunction::LoadRelationFromFile(RefFilePathIdentify, OutDatas);
}

bool URefRelationFunction::SaveRelationToFile(FString InRelationFilePath, const FRefToLocalFileData& InRelationDatas)
{
	catalog_studio_message::FolderRefData FileData;
	bool Res = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InRelationDatas, FileData);
	if (Res)
	{
		Res = URefRelationFunction::SaveStreamToFile<catalog_studio_message::FolderRefData>(InRelationFilePath, FileData);
	}
	return Res;
}

bool URefRelationFunction::LoadRelationFromFile(FString InRelationFilePath, FRefToLocalFileData& OutRelationDatas)
{
	catalog_studio_message::FolderRefData FileData;
	bool Res = URefRelationFunction::LoadStreamFromFile<catalog_studio_message::FolderRefData>(InRelationFilePath, FileData);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(FileData, OutRelationDatas);
	}
	return Res;
}

bool URefRelationFunction::SaveRelationToFile(FString InRelationFilePath, const FRefToStyleFile& InDatas)
{
	catalog_studio_message::StyleRefData StyleRefData;
	bool Res = UConvertUeToProtobufLibrary::ConvertUeDataToProtobufData(InDatas, StyleRefData);
	if (Res)
	{
		Res = URefRelationFunction::SaveStreamToFile<catalog_studio_message::StyleRefData>(InRelationFilePath, StyleRefData);
	}
	return Res;
}

bool URefRelationFunction::LoadRelationFromFile(FString InRelationFilePath, FRefToStyleFile& OutDatas)
{
	catalog_studio_message::StyleRefData StyleRefData;
	bool Res = URefRelationFunction::LoadStreamFromFile<catalog_studio_message::StyleRefData>(InRelationFilePath, StyleRefData);
	if (Res)
	{
		Res = UConvertProtobufToUeLibrary::ConvertProtoDataToUeData(StyleRefData, OutDatas);
	}
	return Res;
}
*/

void URefRelationFunction::AutoCopyFileBackup(const FRefToLocalFileData& InData)
{
	if(InData.IsValid())
	{
		const FString RefFileMark = URefRelationFunction::GetMarkToBackendDirectory(InData);
		const FString RefFileAbsPath = URefToFileData::GetFileAddress(RefFileMark);
		if(FPaths::FileExists(RefFileAbsPath))
		{
			const FString AutoSaveRelativePath = URefToFileData::GetAutoTempSaveFileRelativeAddress(
			InData.FolderDBData.id,
			InData.FolderDBData.folder_id,
			InData.FolderDBData.folder_name,
			URefRelationFunction::GetCurrentPCData(),
			URefRelationFunction::GetCurrentPCTime()
			);
			const FString BackupAbsPth = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectSavedDir(), AutoSaveRelativePath));
			FPlatformFileManager::Get().GetPlatformFile().CopyFile(*BackupAbsPth, *RefFileAbsPath);
		}
	}
}

bool URefRelationFunction::SaveFile(const FRefToLocalFileData& InDatas)
{
	const FString AffectID = (InDatas.FolderDBData.folder_id.IsEmpty() || InDatas.FolderDBData.is_folder) ? InDatas.FolderDBData.id : InDatas.FolderDBData.folder_id;
	FString RefFilePathIdentify = URefToFileData::GetFileAddress(URefRelationFunction::FormatFolderID(AffectID));
	return UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, InDatas);
}

bool URefRelationFunction::SaveFile(const FString& RelativePath, const FRefToLocalFileData& InDatas)
{
	const FString RefFilePathIdentify = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelativePath));
	return UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, InDatas);
}

void URefRelationFunction::SyncFileNetwork(const FRefToLocalFileData& InDatas)
{
	//TODO : 接口信息
}

void URefRelationFunction::SyncFileNetwork(const FRefToStyleFile& InDatas)
{
	//TODO : 接口信息
}

bool URefRelationFunction::SaveTempLocalFile(const FRefToLocalFileData& InData)
{
	const FString AffectID = InData.FolderDBData.id;
	FString RefFilePathRelIdentify = URefToFileData::GetTempSaveFolderRelativeAddress(
		AffectID,
		URefRelationFunction::GetCurrentPCData(),
		URefRelationFunction::GetCurrentPCTime()
	);
	const FString RefFilePathIdentify = FPaths::ConvertRelativePathToFull(
		FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathRelIdentify)
	);
	return UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, InData);
}

FString URefRelationFunction::SaveNewLocalFile(const FRefDirectoryData& Data, const int32& FolderType)
{
	/*const FString FileName = GetMarkToBackendDirectory(Data);
	const FString RefFilePathIdentify = URefToFileData::GetFileRelativeAddress(URefRelationFunction::FormatFolderID(FileName));*/
	const FString RefFilePathIdentify = URefRelationFunction::GetRefFileRelativePath(Data);
	FRefToLocalFileData FileData;
	FileData.FolderDBData.CopyData(Data.id, Data.folderId, Data.folderName, Data.folderNameExp,Data.folderCode, Data.folderCodeExp
		, FolderType, Data.thumbnailPath, TEXT(""), Data.visibilityExp, Data.visibility, Data.dirOrder, Data.isNew, Data.isFolder
		, Data.backendFolderPath, Data.fontFolderPath);
	const FString FileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathIdentify));
	const bool Res = UProtobufOperatorFunctionLibrary::SaveRelationToFile(FileAbsPath, FileData);;
	return Res ? RefFilePathIdentify : TEXT("");
}

bool URefRelationFunction::RenameLocalFile(const FString& OldFilePath, const FString& NewFilePath)
{
	const FString OldFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), OldFilePath));
	const FString NewFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), NewFilePath));
	if (FPaths::FileExists(OldFileAbsPath) && !OldFileAbsPath.Equals(NewFileAbsPath))
	{
		FPlatformFileManager::Get().GetPlatformFile().MoveFile(*NewFileAbsPath, *OldFileAbsPath);
		FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*OldFileAbsPath);
	}
	return false;
}

bool URefRelationFunction::DeleteLocalFile(const FFolderTableData& Data)
{
	FString RefRelativePath = URefRelationFunction::GetRefFileRelativePath(Data);
	const FString RefFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RefRelativePath));
	if (FPaths::FileExists(RefFilePath))
	{
		return FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*RefFilePath);
	}

	return false;
}

void URefRelationFunction::ConvertDBDataToDirctoryData(const FFolderTableData& InData, FRefDirectoryData& OutDirData)
{
	OutDirData.id = InData.id;
	OutDirData.folderId = InData.folder_id;
	OutDirData.folderName = InData.folder_name;
	OutDirData.folderCodeExp = InData.folder_name_exp;

	OutDirData.folderCode = InData.folder_code;
	OutDirData.folderCodeExp = InData.folder_code_exp;
	OutDirData.thumbnailPath = InData.thumbnail_path;

	OutDirData.backendFolderPath = InData.backend_folder_path;
	OutDirData.visibility = InData.visibility;
	OutDirData.visibilityExp = InData.visibility_exp;
	OutDirData.dirOrder = InData.folder_order;
	OutDirData.isFolder = static_cast<int32>(InData.can_add_subfolder);
	OutDirData.isNew = static_cast<int32>(InData.is_new);
	OutDirData.md5 = InData.md5;

	OutDirData.width = InData.width;
	OutDirData.height = InData.height;
	OutDirData.depth = InData.depth;
	OutDirData.boxOffset = InData.boxOffset;

	OutDirData.placeRule = InData.placeRule;
	OutDirData.description = InData.description;
}

void URefRelationFunction::ConvertDirctoryDataToDBData(const FRefDirectoryData& InData, FFolderTableData& OutData)
{
	OutData.id = InData.id;
	OutData.folder_id = InData.folderId;
	OutData.folder_name = InData.folderName;
	OutData.folder_name_exp = InData.folderNameExp;

	OutData.folder_code = InData.folderCode;
	OutData.folder_code_exp = InData.folderCodeExp;
	OutData.thumbnail_path = InData.thumbnailPath;
	OutData.visibility = InData.visibility;
	OutData.visibility_exp = InData.visibilityExp;
	OutData.folder_order = InData.dirOrder;
	OutData.can_add_subfolder = InData.isFolder != 0;
	OutData.is_new = InData.isNew != 0;
	OutData.backend_folder_path = InData.backendFolderPath;
	OutData.folder_order = InData.dirOrder;
	OutData.md5 = InData.md5;

	OutData.width = InData.width;
	OutData.height = InData.height;
	OutData.depth = InData.depth;
	OutData.boxOffset = InData.boxOffset;

	OutData.placeRule = InData.placeRule;
	OutData.description = InData.description;
}

void URefRelationFunction::ConvertDirctoryDataToDBData(const TArray<FRefDirectoryData>& InData, TArray<FFolderTableData>& OutData)
{
	OutData.Empty();
	for (auto& Iter : InData)
	{
		FFolderTableData Temp;
		URefRelationFunction::ConvertDirctoryDataToDBData(Iter, Temp);
		OutData.Add(Temp);
	}
}

void URefRelationFunction::GetRootDirectory(TArray<FRefDirectoryData>& OutDatas)
{
	//TODO : 接口获取信息
#ifdef USE_INTERFACE_GET_DIRECTORY 

#else

	//const FString SQL = FString::Printf(TEXT("select * from directory where BACKEND_FOLDER_PATH NOT LIKE '%%/%%' ORDER BY DIR_ORDER ASC"));
	//bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FRefDirectoryData>(SQL, OutDatas);

#endif

}

void URefRelationFunction::GetRootDirectory(TArray<FFolderTableData>& OutDatas)
{
	OutDatas.Empty();
	TArray<FRefDirectoryData> DirectoryDatas;
	URefRelationFunction::GetRootDirectory(DirectoryDatas);
	URefRelationFunction::ConvertDirctoryDataToDBData(DirectoryDatas, OutDatas);
}

void URefRelationFunction::GetSubDirectory(const FString& DirPathStr, TArray<FRefDirectoryData>& OutDatas)
{
	//TODO : 接口获取信息
#ifdef USE_INTERFACE_GET_DIRECTORY 

#else

	//const FString SQL = FString::Printf(TEXT("select * from directory where BACKEND_FOLDER_PATH LIKE '%s/%%' ORDER BY DIR_ORDER ASC"), *DirPathStr);
	//bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FRefDirectoryData>(SQL, OutDatas);

#endif
}

void URefRelationFunction::GetSubDirectory(const FString& DirPathStr, TArray<FFolderTableData>& OutDatas)
{
	OutDatas.Empty();
	TArray<FRefDirectoryData> DirectoryDatas;
	URefRelationFunction::GetSubDirectory(DirPathStr, DirectoryDatas);
	DirectoryDatas = URefRelationFunction::GetSubLevelDirectory(DirectoryDatas, DirPathStr);
	URefRelationFunction::ConvertDirctoryDataToDBData(DirectoryDatas, OutDatas);
}

bool URefRelationFunction::IsRootDirectory(const FString& InDirectory)
{
	return !InDirectory.Contains(DIRECTORY_SLASH_MARK);
}

TArray<FRefDirectoryData> URefRelationFunction::FliteRootDirectory(const TArray<FRefDirectoryData>& InDatas)
{
	TArray<FRefDirectoryData> OutDatas;
	for (const auto Iter : InDatas)
	{
		if (IsRootDirectory(Iter.backendFolderPath))
		{
			OutDatas.Add(Iter);
		}
	}
	return OutDatas;
}

void URefRelationFunction::GetSizeInfo(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParamsMap, FString& OutW, FString& OutH, FString& OutD)
{
#define MARK_TO_WIDTH TEXT("W")
#define MARK_TO_HEIGHT TEXT("H")
#define MARK_TO_DEPTH TEXT("D")

	OutW = ParamsMap.Contains(MARK_TO_WIDTH) ? ParamsMap[MARK_TO_WIDTH].Data.value : TEXT("0");
	OutH = ParamsMap.Contains(MARK_TO_HEIGHT) ? ParamsMap[MARK_TO_HEIGHT].Data.value : TEXT("0");
	OutD = ParamsMap.Contains(MARK_TO_DEPTH) ? ParamsMap[MARK_TO_DEPTH].Data.value : TEXT("0");

#undef MARK_TO_WIDTH 
#undef MARK_TO_HEIGHT 
#undef MARK_TO_DEPTH
}

void URefRelationFunction::GetSizeInfo(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GP, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OP, FString& OutW, FString& OutH, FString& OutD)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(GP, OP, PeerParameters);
	URefRelationFunction::GetSizeInfo(PeerParameters, OutW, OutH, OutD);
}

TArray<FRefDirectoryData> URefRelationFunction::GetSubLevelDirectory(const TArray<FRefDirectoryData>& AllDatas, const FString& CurDirectory)
{
	TArray<FRefDirectoryData> OutDatas;
	for (const auto Iter : AllDatas)
	{
		FString JudgeStr = Iter.backendFolderPath;
		bool Res = JudgeStr.RemoveFromStart(CurDirectory + DIRECTORY_SLASH_MARK);
		if (Res && !JudgeStr.Contains(DIRECTORY_SLASH_MARK))
		{
			OutDatas.Add(Iter);
		}
	}
	return OutDatas;
}

TArray<FRefDirectoryDataContent> URefRelationFunction::GetDirectoryContentData(
	const TArray<FRefDirectoryData>& AllDatas,
	const TArray<FString>& CurDirectoryArr
)
{
	TArray<FRefDirectoryDataContent> Res;

	FString CurDirectory = TEXT("");
	for (const auto& CDA : CurDirectoryArr)
	{
		FString TempDirectory = CDA;
		if (!CurDirectory.IsEmpty())
		{
			TempDirectory = FPaths::Combine(CurDirectory, TempDirectory);
		}
		TArray<FRefDirectoryData> SubDatas = URefRelationFunction::GetSubLevelDirectory(AllDatas, TempDirectory);
		auto& CurContent = Res.AddDefaulted_GetRef();
		CurContent.PID = CDA;
		CurContent.CDataArr = SubDatas;

		CurDirectory = TempDirectory;
	}

	return Res;
}

FString URefRelationFunction::GetFolderDirectory(FString InDirectory, bool IsFolder)
{
	if (!IsFolder)
	{
		int32 Index = INDEX_NONE;
		InDirectory.FindLastChar('/', Index);
		InDirectory = InDirectory.Left(Index);
	}
	return InDirectory;
}

bool URefRelationFunction::IsSameRootDirectory(const FString& InPath1, const FString& InPath2)
{
	TArray<FString> PathArr1 = URefRelationFunction::GetUpperFolderDirectory(InPath1, true);
	TArray<FString> PathArr2 = URefRelationFunction::GetUpperFolderDirectory(InPath2, true);
	if (!PathArr1.IsValidIndex(0) || !PathArr2.IsValidIndex(0))
	{
		return false;
	}
	else
	{
		return PathArr1[0].Equals(PathArr2[0], ESearchCase::IgnoreCase);
	}
}

bool URefRelationFunction::IsSameFolderDirectory(const FString& InPath1, const FString& InPath2)
{
	const TArray<FString> PathArr1 = URefRelationFunction::GetUpperFolderDirectory(InPath1, false);
	const TArray<FString> PathArr2 = URefRelationFunction::GetUpperFolderDirectory(InPath2, false);
	if (PathArr1.Num() != PathArr2.Num())
	{
		return false;
	}
	else
	{
		for (int32 i = 0; i < PathArr1.Num(); ++i)
		{
			if (!PathArr1[i].Equals(PathArr2[i], ESearchCase::IgnoreCase))
			{
				return false;
			}
		}

	}
	return true;
}

TArray<FString> URefRelationFunction::GetUpperFolderDirectory(const FString& InDirectory, bool HasLast)
{
	TArray<FString> OutRes;
	FString UpperDirectory = InDirectory;
	if (!HasLast)
		UpperDirectory = URefRelationFunction::GetFolderDirectory(InDirectory, false);
	UpperDirectory.ParseIntoArray(OutRes, DIRECTORY_SLASH_MARK);
	return OutRes;
}

bool URefRelationFunction::SpliteUpperFolderDirectory(const FString& InDirectory, const FString& InFolderID, bool HasLast, const TArray<FString>& AleadyDownloadFiles, TArray<FString>& UpperIds, TArray<FString>& NeedDownloadFiles)
{
	NeedDownloadFiles.Empty();
	TArray<FString> UpperFolderIds = URefRelationFunction::GetUpperFolderDirectory(InDirectory, HasLast);
	if (HasLast && !InFolderID.IsEmpty() && !UpperFolderIds.Last().Equals(InFolderID, ESearchCase::CaseSensitive))
	{
		FString ToRemove = UpperFolderIds.Last();
		UpperFolderIds.Remove(ToRemove);
		UpperFolderIds.AddUnique(InFolderID);
	}
	for (auto& UFI : UpperFolderIds)
	{
		UpperIds.AddUnique(UFI);

		FString DatFileAbsPath = URefToFileData::GetFileAddress(UFI);
		//文件夹默认下载
		//if (!FPaths::FileExists(DatFileAbsPath))
		{
			FString DownloadPath = URefToFileData::GetFileRelativeAddress(UFI);
			if (!AleadyDownloadFiles.Contains(DownloadPath))
			{
				NeedDownloadFiles.AddUnique(DownloadPath);
			}
		}
	}
	return NeedDownloadFiles.IsEmpty();
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  URefRelationFunction::GetUpperFolderInheritParams(const TArray<FString>& UpperIds)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  Res;

	TArray<FRefToLocalFileData> UpperFolderData;
	for (const auto& UI : UpperIds)
	{
		FRefToLocalFileData CurFileData;
		if (URefRelationFunction::GetCurrentRefRelationFromFile(UI, CurFileData))
		{
			UpperFolderData.Add(CurFileData);
		}
		else
		{
			//checkf(false, TEXT("open [%s].dat file error"), *UI);
			UE_LOG(LogTemp, Error, TEXT("open %s.dat file error"), *UI);
		}
	}

	//URefRelationFunction::GetTopLevelFolderParameterDataHasCalculate(UpperFolderData, Res);
	URefRelationFunction::GetTopLevelFolderParameterData(UpperFolderData, Res);
	URefRelationFunction::ConfirmComponentParams(Res);

	return Res;
}

FString URefRelationFunction::GetThisContainFolderID(const FString& InBackDirectory)
{
	TArray<FString> SplitePaths = URefRelationFunction::GetUpperFolderDirectory(InBackDirectory, false);
	if (SplitePaths.Num() > 0)
	{
		return SplitePaths.Last();
	}
	return FString();
}

int32 URefRelationFunction::GetFolderLevel(const FString& InDirectiry)
{
	TArray<FString> SplitePaths;
	InDirectiry.ParseIntoArray(SplitePaths, DIRECTORY_SLASH_MARK);
	return SplitePaths.Num();
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  URefRelationFunction::GetGlobalParameters()
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OutData;
#ifdef USE_REF_LOCAL_FILE
	//TODO : 修改，获取全局参数
	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(OutData);
#else
	FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
#endif
	return OutData;
}

bool URefRelationFunction::EnsureDefaultExpressionValid(TArray<FParameterData>& InParams)
{
	bool HasChange = false;
	for (auto& Param : InParams)
	{
		bool TempValid = Param.Data.EnsureDefaultExpress();
		if (!TempValid)
		{
			HasChange = true;
		}
	}

	return HasChange;
}

bool URefRelationFunction::EnsureMultiComponentDefaultExpress(TArray<FMultiComponentDataItem>& MultiConstruct)
{
	bool HasChange = false;
	for (auto& Item : MultiConstruct)
	{
		bool TempChange = URefRelationFunction::EnsureMultiComponentDefaultExpress(Item);
        HasChange = TempChange || HasChange;
	}
	return HasChange;
}

bool URefRelationFunction::EnsureMultiComponentDefaultExpress(FMultiComponentDataItem& MultiConstruct)
{
	bool HasChange = URefRelationFunction::EnsureDefaultExpressionValid(MultiConstruct.ComponentParameters);

	for (auto& CItem : MultiConstruct.ChildComponent)
	{
		bool CChange = URefRelationFunction::EnsureMultiComponentDefaultExpress(CItem);
        HasChange = CChange || HasChange;
	}
	return HasChange;
}

FString URefRelationFunction::GetMarkToBackendDirectory(const FRefDirectoryData& Data)
{
	return URefRelationFunction::FormatFolderID(Data.folderId.IsEmpty() ? Data.id : (Data.isFolder != 0 ? Data.id : Data.folderId));
}

FString URefRelationFunction::GetMarkToBackendDirectory(const FFolderTableData& Data)
{
	return URefRelationFunction::FormatFolderID(Data.folder_id.IsEmpty() ? Data.id : (Data.can_add_subfolder ? Data.id : Data.folder_id));
}

FString URefRelationFunction::GetMarkToBackendDirectory(const FRefToLocalFileData& Data)
{
	const FString AffectID = (Data.FolderDBData.folder_id.IsEmpty() || Data.FolderDBData.is_folder) ? Data.FolderDBData.id : Data.FolderDBData.folder_id;
	return URefRelationFunction::FormatFolderID(AffectID);
}

FString URefRelationFunction::GetRefFileRelativePath(const FRefDirectoryData& Data)
{
	const FString FileName = GetMarkToBackendDirectory(Data);
	return URefToFileData::GetFileRelativeAddress(URefRelationFunction::FormatFolderID(FileName));
}

FString URefRelationFunction::GetRefFileRelativePath(const FFolderTableData& Data)
{
	FString MarkID = Data.folder_id.IsEmpty() ? Data.id : (Data.can_add_subfolder ? Data.id : Data.folder_id);
	return URefToFileData::GetFileRelativeAddress(URefRelationFunction::FormatFolderID(MarkID));
}

FString URefRelationFunction::GetRefFileRelativePath(const FRefToLocalFileData& Data)
{
	FString MarkID = Data.FolderDBData.folder_id.IsEmpty() ? Data.FolderDBData.id : (Data.FolderDBData.is_folder ? Data.FolderDBData.id : Data.FolderDBData.folder_id);
	return URefToFileData::GetFileRelativeAddress(URefRelationFunction::FormatFolderID(MarkID));
}

bool URefRelationFunction::NeedDownloadFile(const FRefDirectoryData& Data)
{
	if (!Data.md5.IsEmpty())
	{
		const FString FileName = URefRelationFunction::GetMarkToBackendDirectory(Data);
		const FString FilePath = URefToFileData::GetFileAddress(FileName);
		if (FPaths::FileExists(FilePath))
		{
			FString FileMD5 = TEXT("");
			int64 FileSize = 0;
			ACatalogPlayerController::Get()->GetFileMD5AndSize(FilePath, FileMD5, FileSize);
			if (!FileMD5.Equals(Data.md5))
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		else
		{
			return true;
		}
	}
	return true;
}

FString URefRelationFunction::GetZipRelativePath(const FString& PakPath, bool IsAbs)
{
	const FString FileName = FPaths::GetBaseFilename(PakPath);
	/*
	*  @@ upper upper folder ( zip has own folder )
	*  @@ ../../
	*/
	FString FolderPath = FPaths::GetPath(PakPath);
	FString ZipPath = FPaths::Combine(FolderPath, FileName + TEXT(".zip"));
	if (IsAbs)
	{
		ZipPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), ZipPath));
	}
	return ZipPath;
}

TArray<FString> URefRelationFunction::GetAllFileInFolders(FString InFolderPath)
{
	TArray<FString> Res;
	FPaths::NormalizeDirectoryName(InFolderPath);
	const FString FindFile = FPaths::Combine(InFolderPath, TEXT("*.dat"));
	IFileManager::Get().FindFiles(Res, *FindFile, true, true);
	return Res;
}

FString URefRelationFunction::GetCurrentPCData()
{
	FString CurTime = UKismetMathLibrary::Now().ToString();
	FString Data, Time;
	CurTime.Split(TEXT("-"), &Data, &Time);
	return Data;
}

FString URefRelationFunction::GetCurrentPCTime()
{
	FString CurTime = UKismetMathLibrary::Now().ToString();
	FString Data, Time;
	CurTime.Split(TEXT("-"), &Data, &Time);
	return Time;
}


void URefRelationFunction::GetTopLevelFolderParameterData(const FString& InDirectory, TArray<FParameterTableData>& OutParameterDatas)
{
	TArray<FString> DirectoryPaths;
	InDirectory.ParseIntoArray(DirectoryPaths, DIRECTORY_SLASH_MARK);
	for (const auto Iter : DirectoryPaths)
	{

	}
}

void URefRelationFunction::GetTopLevelFolderParameterData(const TArray<FRefToLocalFileData>& DirectoryRefDatas,
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutheritParameters)
{
	if (DirectoryRefDatas.Num() > 0)
	{
		OutheritParameters = URefRelationFunction::ConvertParamsArrayToMap(DirectoryRefDatas[0].ParamDatas);
		for (int32 i = 1; i < DirectoryRefDatas.Num(); ++i)
		{
			for (const auto ParamIter : DirectoryRefDatas[i].ParamDatas)
			{
				if (OutheritParameters.Contains(ParamIter.Data.name))
				{
					OutheritParameters[ParamIter.Data.name] = ParamIter;
				}
				else
				{
					OutheritParameters.Add(ParamIter.Data.name, ParamIter);
				}
			}
		}
	}
}

void URefRelationFunction::GetTopLevelFolderParameterDataHasCalculate(const TArray<FRefToLocalFileData>& DirectoryRefDatas, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& OutheritParameters)
{
	if (DirectoryRefDatas.Num() > 0)
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GlobalParams = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		OutheritParameters = URefRelationFunction::ConvertParamsArrayToMap(DirectoryRefDatas[0].ParamDatas);
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, {}, OutheritParameters);

		for (int32 i = 1; i < DirectoryRefDatas.Num(); ++i)
		{
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CurParams = URefRelationFunction::ConvertParamsArrayToMap(DirectoryRefDatas[i].ParamDatas);
			UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, OutheritParameters, CurParams);

			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> OverrideParams;
			UParameterRelativeLibrary::CombineParameters(OutheritParameters, CurParams, OverrideParams);
			OutheritParameters = OverrideParams;
		}
	}
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  URefRelationFunction::ConvertParamsArrayToMap(const TArray<FParameterData>& Params)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OutData;
	for (const auto Iter : Params)
	{
		OutData.Add(Iter.Data.name, Iter);
	}
	return OutData;
}

TArray<FParameterData> URefRelationFunction::ConvertParamsMapToArray(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Params)
{
	TArray<FParameterData> OutData;
	for (const auto Iter : Params)
	{
		OutData.Add(Iter.Value);
	}
	return OutData;
}

bool URefRelationFunction::GetMultiComponentInfo(
	const FString& InID,
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & UpperLevelParams,
	FMultiComponentDataItem& OutComponentDatas,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& InnerMatAdditionIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	TArray<FExpressionValuePair>& DependMatIDS,
	const FDependFileData& InDependDatas,
	const TArray<FString>& InSkipDependFiles,
	const TArray<FString>& InAleadyDownloadFiles,
	const TArray<FMatAdditionInfo>& MatAdditionInfos,
	bool IsInit /*= true*/
)
{
	UE_LOG(LogTemp, Log, TEXT("URefRelationFunction::GetMultiComponentInfo -- WITH ID"));

	FRefToLocalFileData CurFileData;
	if (URefRelationFunction::GetCurrentRefRelationFromFile(InID, CurFileData))
	{
		TArray<FString> UpperIDs;
		if (!URefRelationFunction::SpliteUpperFolderDirectory(CurFileData.FolderDBData.backend_directory, CurFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, UpperIDs, NeedDownloadIDS))
		{
			return true;
		}

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> InheritParams = URefRelationFunction::GetUpperFolderInheritParams(UpperIDs);
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, {}, InheritParams);
		InheritParams.GenerateValueArray(OutComponentDatas.InheritParams);

		URefRelationFunction::ConfirmComponentParams(OutComponentDatas);

		TArray<FString> UpperRef;
		UpperRef.Add(URefRelationFunction::FormatFolderID(CurFileData.FolderDBData.folder_id));
		URefRelationFunction::GetRefRelationRecursiveLevelOrder(
			CurFileData,
			GlobalParams,
			UpperLevelParams,
			InheritParams,
			OutComponentDatas,
			NeedDownloadIDS,
			InnerMatAdditionIDS,
			DependModelIDS,
			DependMatIDS,
			UpperRef,
			InDependDatas,
			InSkipDependFiles,
			InAleadyDownloadFiles,
			MatAdditionInfos
		);
	}
	else
	{
		checkf(false, TEXT("URefRelationFunction::GetMultiComponentInfo [%s] false"), *InID);
	}
	return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0 || InnerMatAdditionIDS.Num() > 0;
}

bool URefRelationFunction::GetMultiComponentInfo(
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UpperLevelParams,
	FMultiComponentDataItem& OutComponentDatas,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& InnerMatAdditionIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	TArray<FExpressionValuePair>& DependMatIDS,
	FDependFileData InDependDatas,
	TArray<FString> InSkipDependFiles,
	const TArray<FString>& InAleadyDownloadFiles,
	const TArray<FMatAdditionInfo>& MatAdditionInfos
)
{

	UE_LOG(LogTemp, Log, TEXT("URefRelationFunction::GetMultiComponentInfo"));

	URefRelationFunction::ConfirmComponentParams(OutComponentDatas.ComponentParameters);

	/*
	 *  @@ 计算、组合本层参数
	 *	@@ 当ComponentID为空时标识为最外层, 此时需使用外层文件上的参数
	 */
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  CurComponentParameters;
	const bool IsInnerLevel = OutComponentDatas.ComponentID.IsValid();
	if (IsInnerLevel)
	{
		UParameterRelativeLibrary::CombineParameters(CurComponentParameters, OutComponentDatas.ComponentParameters);
	}
	else
	{
		CurComponentParameters = URefRelationFunction::ConvertParamsArrayToMap(OutComponentDatas.ComponentParameters);

	}

	//upper inherit params
	FRefToLocalFileData IterFileData;
	FString FormatID = URefRelationFunction::FormatFolderID(OutComponentDatas.ComponentID.Value);
	if (URefRelationFunction::GetCurrentRefRelationFromFile(FormatID, IterFileData))
	{
		TArray<FString> IterUpperIDs;
		TArray<FString> TempNeedDownload;
		if (!URefRelationFunction::SpliteUpperFolderDirectory(IterFileData.FolderDBData.backend_directory, IterFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, IterUpperIDs, TempNeedDownload))
		{
			URefRelationFunction::StrArrayAddUnique(NeedDownloadIDS, TempNeedDownload);
			return true;
		}
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> IterInheritParams = URefRelationFunction::GetUpperFolderInheritParams(IterUpperIDs);
		IterInheritParams.GenerateValueArray(OutComponentDatas.InheritParams);

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  TempComponentParameters = CurComponentParameters;
		UParameterRelativeLibrary::CombineParameters(IterInheritParams, TempComponentParameters, CurComponentParameters);
	}

	URefRelationFunction::ConfirmComponentParams(CurComponentParameters);

	/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  CurComponentParameters;
	UParameterRelativeLibrary::CombineParameters(CurComponentParameters, OutComponentDatas.ComponentParameters);*/
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, UpperLevelParams, CurComponentParameters);
	auto& ParamsArray = OutComponentDatas.ComponentParameters;
	for (const auto& iter : CurComponentParameters)
	{
		auto& EditParameter = iter.Value;
		const int32 Index = ParamsArray.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive); });
		if (INDEX_NONE != Index) ParamsArray[Index] = EditParameter;
	}

	//计算当前层数据
	for (auto& Iter : OutComponentDatas.ChildComponent)
	{
		FGeometryDatas::CalculateParameterValue(GlobalParams, UpperLevelParams, CurComponentParameters, Iter.ComponentID);

		//FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, Iter);
	}

	//UParameterRelativeLibrary::CombineParameters(UpperLevelParams, ParamsArray, UpperLevelParams);

	//need download files
	for (auto& Iter : OutComponentDatas.ChildComponent)
	{
		//if (Iter.ComponentType == ECompType::None) continue;

		Iter.ComponentID.FormatValue();

		if (Iter.ComponentID.Value.IsEmpty()) continue;

		if (InSkipDependFiles.Contains(Iter.ComponentID.Value))
		{//skip parse, clear old data, only save component id

			URefRelationFunction::EmptyNodeData(Iter);

			continue;
		}

		if(URefRelationFunction::AnalysisFileDownlload(
			Iter,
			NeedDownloadIDS, 
			DependModelIDS,
			InSkipDependFiles, 
			InDependDatas, 
			InAleadyDownloadFiles
		))
		{
			continue;
		}
	}

	//递归获取下一层数据
	if (NeedDownloadIDS.Num() <= 0 && InnerMatAdditionIDS.Num() <= 0)
	{
		for (auto& Iter : OutComponentDatas.ChildComponent)
		{
			URefRelationFunction::ConfirmComponentParams(Iter.ComponentParameters);

			Iter.ComponentID.FormatValue();
			TArray<FString> UpperRef;
			if (Iter.ComponentType == ECompType::None)
			{
				FRefToLocalFileData IterFileData;
				if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter.ComponentID.Value, IterFileData))
				{
					UpperRef.Add(URefRelationFunction::FormatFolderID(IterFileData.FolderDBData.folder_id));
					URefRelationFunction::ParseNodeData_Custom(Iter, IterFileData);

					TArray<FString> IterUpperIDs;
					TArray<FString> TempNeedDownload;
					if (!URefRelationFunction::SpliteUpperFolderDirectory(IterFileData.FolderDBData.backend_directory, IterFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, IterUpperIDs, TempNeedDownload))
					{
						URefRelationFunction::StrArrayAddUnique(NeedDownloadIDS, TempNeedDownload);
						return true;
					}
					TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> IterInheritParams = URefRelationFunction::GetUpperFolderInheritParams(IterUpperIDs);
					IterInheritParams.GenerateValueArray(Iter.InheritParams);

					FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, IterInheritParams, Iter);

					URefRelationFunction::GetRefRelationRecursiveLevelOrder(
						IterFileData,
						GlobalParams,
						UpperLevelParams,
						IterInheritParams,
						Iter,
						NeedDownloadIDS,
						InnerMatAdditionIDS,
						DependModelIDS,
						DependMatIDS,
						UpperRef,
						InDependDatas,
						InSkipDependFiles,
						InAleadyDownloadFiles,
						MatAdditionInfos
					);
				}
				else
				{
					if (InDependDatas.ModelAlreadyHas(Iter.ComponentID.Value))
					{
						auto& ImportModelComp = Iter.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
						ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

						FCSModelMatData ModelData;
						if (InDependDatas.GetDependFile(Iter.ComponentID.Value, ModelData))
						{
							ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

							if (Iter.ComponentType == ECompType::None)
							{//new write

								Iter.ComponentType = ECompType::ImportModel;
							}
							URefRelationFunction::ParseNodeData_Model(Iter, ModelData);
						}

					}
					continue;
				}
			}
			else if (Iter.ComponentType == ECompType::ImportModel /*|| Iter.ComponentType == ECompType::None*/)
			{
				Iter.ComponentID.FormatValue();
				if (InDependDatas.ModelAlreadyHas(Iter.ComponentID.Value))
				{
					auto& ImportModelComp = Iter.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
					ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

					FCSModelMatData ModelData;
					if (InDependDatas.GetDependFile(Iter.ComponentID.Value, ModelData))
					{
						ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

						if (Iter.ComponentType == ECompType::None)
						{//new write

							Iter.ComponentType = ECompType::ImportModel;
						}
						URefRelationFunction::ParseNodeData_Model(Iter, ModelData);
					}
					else
					{
						//Iter.SingleComponentData.ComponentItems.RemoveAt();
					}
				}
				continue;
			}
			else
			{
				//TArray<FString> UpperRef;
				FRefToLocalFileData IterFileData;
				if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter.ComponentID.Value, IterFileData))
				{
					UpperRef.Add(URefRelationFunction::FormatFolderID(IterFileData.FolderDBData.folder_id));
					URefRelationFunction::ParseNodeData_Custom(Iter, IterFileData);

					TArray<FString> IterUpperIDs;
					TArray<FString> TempNeedDownload;
					if (!URefRelationFunction::SpliteUpperFolderDirectory(IterFileData.FolderDBData.backend_directory, IterFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, IterUpperIDs, TempNeedDownload))
					{
						URefRelationFunction::StrArrayAddUnique(NeedDownloadIDS, TempNeedDownload);
						return true;
					}
					TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> IterInheritParams = URefRelationFunction::GetUpperFolderInheritParams(IterUpperIDs);
					IterInheritParams.GenerateValueArray(Iter.InheritParams);

					FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, IterInheritParams, Iter);

					URefRelationFunction::GetRefRelationRecursiveLevelOrder(
						IterFileData,
						GlobalParams,
						UpperLevelParams,
						IterInheritParams,
						Iter,
						NeedDownloadIDS,
						InnerMatAdditionIDS,
						DependModelIDS,
						DependMatIDS,
						UpperRef,
						InDependDatas,
						InSkipDependFiles,
						InAleadyDownloadFiles,
						MatAdditionInfos
					);
				}
			}
			
		}
	}

	return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0 || InnerMatAdditionIDS.Num() > 0;
}

bool URefRelationFunction::GetSingleComponentInfo(const FString& InID, TArray<FSingleComponentItem>& OutComponentDatas)
{
	return false;
}

void URefRelationFunction::ConfirmComponentParams(FMultiComponentDataItem& ModifyComponent)
{
	for (auto& CP : ModifyComponent.ComponentParameters)
	{
		for (auto& NCP : CP.EnumData)
		{
			if (NCP.expression.IsEmpty() && !NCP.value.IsEmpty())
			{
				NCP.expression = NCP.value;
			}
		}
	}
}

void URefRelationFunction::ConfirmComponentParams(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ModifyParams)
{
	for (auto& MP : ModifyParams)
	{
		for (auto& NCP : MP.Value.EnumData)
		{
			if (!NCP.expression.IsEmpty() && !NCP.value.IsEmpty() && !NCP.expression.Equals(NCP.value, ESearchCase::CaseSensitive))
			{
				UE_LOG(LogTemp, Error, TEXT("param [%s][%s]"), *NCP.expression, *NCP.value);
			}

			if (NCP.expression.IsEmpty() && !NCP.value.IsEmpty())
			{
				NCP.expression = NCP.value;
			}
		}

	}
}

void URefRelationFunction::ConfirmComponentParams(TArray<FParameterData>& ModifyParams)
{
	for (auto& MP : ModifyParams)
	{
		for (auto& NCP : MP.EnumData)
		{
			if (!NCP.expression.IsEmpty() && !NCP.value.IsEmpty() && !NCP.expression.Equals(NCP.value, ESearchCase::CaseSensitive))
			{
				UE_LOG(LogTemp, Log, TEXT("URefRelationFunction::ConfirmComponentParams: param express -- value [%s][%s]"), *NCP.expression, *NCP.value);
			}


			if (NCP.expression.IsEmpty() && !NCP.value.IsEmpty())
			{
				NCP.expression = NCP.value;
			}
		}

	}
}

bool URefRelationFunction::GetCurrentRefRelationFromFile(const FString& InID, FRefToLocalFileData& FileData)
{
	const FString FormatID = URefRelationFunction::FormatFolderID(InID);
	const FString RefLocalFilePath = URefToFileData::GetFileAddress(FormatID);
	if (FPaths::FileExists(RefLocalFilePath) && UProtobufOperatorFunctionLibrary::LoadRelationFromFile(RefLocalFilePath, FileData))
	{
		return true;
	}
	return false;
}

bool URefRelationFunction::GetRefRelationByPath(const FString& InPath, FRefToLocalFileData& FileData)
{
	if (FPaths::FileExists(InPath) && UProtobufOperatorFunctionLibrary::LoadRelationFromFile(InPath, FileData))
	{
		return true;
	}
	return false;
}

bool URefRelationFunction::GetRefRelationRecursiveLevelOrder(
	FRefToLocalFileData& FileData,
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UpperLevelParams,
	const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & UpperInheritParams,
	FMultiComponentDataItem& CurComponentData,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& InnerMatAdditionIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	TArray<FExpressionValuePair>& DependMatIDS,
	TArray<FString>& UpperRefID,
	FDependFileData InDependDatas,
	TArray<FString> InSkipDependFiles,
	const TArray<FString>& InAleadyDownloadFiles,
	const TArray<FMatAdditionInfo>& MatAdditionInfos
)
{
	UE_LOG(LogTemp, Log, TEXT("URefRelationFunction::GetRefRelationRecursiveLevelOrder"));

	CurComponentData.ChildComponent.Empty();

	/*
	 *  @@ calculate , combine current layer parameters
	 *  @@ when ComponentID is empty, it is marked as the outermost layer, and the parameters on the outer layer file need to be used at this time
	 */
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  CurComponentParameters;
	const bool IsInnerLevel = CurComponentData.ComponentID.IsValid();
	if (IsInnerLevel)
	{
		UParameterRelativeLibrary::CombineParameters(CurComponentParameters, CurComponentData.ComponentParameters);
	}
	else
	{
		CurComponentParameters = URefRelationFunction::ConvertParamsArrayToMap(FileData.ParamDatas);

	}

	//对于顶层，需有虚拟父级参数
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> NoInnerLevelParams;
	//材质的关联信息
	const FString CustomMatMark = TEXT("DZCZ");
	if (CurComponentParameters.Contains(CustomMatMark) || UpperInheritParams.Contains(CustomMatMark))
	{
		const FParameterData DZCZData = CurComponentParameters.Contains(CustomMatMark) ? CurComponentParameters[CustomMatMark] : UpperInheritParams[CustomMatMark];
		const int32& ReadyIndex = MatAdditionInfos.IndexOfByPredicate(
			[&DZCZData](const FMatAdditionInfo& InInfo)->bool
			{
				return DZCZData.Data.value.Equals(InInfo.FolderID, ESearchCase::CaseSensitive);
			}
		);


		if (ReadyIndex == INDEX_NONE)
		{//need parse mat data
			InnerMatAdditionIDS.Add(FExpressionValuePair(DZCZData.Data.expression, DZCZData.Data.value));
		}
		else
		{//add to params
			const FMatAdditionInfo& CurMatInfo = MatAdditionInfos[ReadyIndex];
			TArray<FString> AdditionMatStr = CurMatInfo.GetAdditionStrArr();
			for (const auto& AMS : AdditionMatStr)
			{
				FString Exp, Value;
				AMS.Split(TEXT("="), &Exp, &Value);
				if (!Exp.IsEmpty() && !Value.IsEmpty())
				{
					if (IsInnerLevel)
					{
						if (UpperLevelParams.Contains(Exp))
						{
							UpperLevelParams[Exp].Data.expression = Value;
							UpperLevelParams[Exp].Data.value = Value;
						}
						else if (GlobalParams.Contains(Exp))
						{//no param, new param to add
							FParameterData ToAddParam = GlobalParams[Exp];
							ToAddParam.Data.expression = Value;
							ToAddParam.Data.value = Value;
							UpperLevelParams.Add(Exp, ToAddParam);
						}
					}
					else
					{
						if (NoInnerLevelParams.Contains(Exp))
						{
							NoInnerLevelParams[Exp].Data.expression = Value;
							NoInnerLevelParams[Exp].Data.value = Value;
						}
						else if (GlobalParams.Contains(Exp))
						{//no param, new param to add
							FParameterData ToAddParam = GlobalParams[Exp];
							ToAddParam.Data.expression = Value;
							ToAddParam.Data.value = Value;
							NoInnerLevelParams.Add(Exp, ToAddParam);
						}

					}
					
				}
			}
		}
	}

	//
	if (InnerMatAdditionIDS.Num() > 0)
	{
		return true;
	}

	//计算文件参数
	if (!IsInnerLevel)
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> NoInnerOverride;
		UParameterRelativeLibrary::CombineParameters(UpperInheritParams, NoInnerLevelParams, NoInnerOverride);
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, NoInnerOverride, CurComponentParameters);
	}
	else
	{
		//原始文件夹参数和本层参数合并
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  TempUpperLevel = CurComponentParameters;
		UParameterRelativeLibrary::CombineParameters(UpperInheritParams, TempUpperLevel, CurComponentParameters);

		URefRelationFunction::ConfirmComponentParams(CurComponentParameters);
		//计算本层参数
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParams, UpperLevelParams, CurComponentParameters);
	}

	//回填计算的参数值  回填计算的文件夹参数值
	auto& ParamsArray = IsInnerLevel ? CurComponentData.ComponentParameters : FileData.ParamDatas;
	for (const auto& iter : CurComponentParameters)
	{
		auto& EditParameter = iter.Value;

		//本层参数
		const int32 Index = ParamsArray.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive); });
		if (INDEX_NONE != Index) ParamsArray[Index] = EditParameter;

		if (IsInnerLevel)
		{//内部组件文件夹参数
			const int32 InheritIndex = CurComponentData.InheritParams.IndexOfByPredicate(
				[EditParameter](const FParameterData& Param)-> bool
				{
					return Param.Data.name.Equals(EditParameter.Data.name, ESearchCase::CaseSensitive);
				}
			);
			if (Index == INDEX_NONE && InheritIndex != INDEX_NONE)
			{
				CurComponentData.InheritParams[InheritIndex] = EditParameter;
			}
		}
	}


	if (IsInnerLevel)
	{
		UParameterRelativeLibrary::CombineParameters(UpperLevelParams, CurComponentParameters, UpperLevelParams);
	}
	else
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  TempCurParams;
		UParameterRelativeLibrary::CombineParameters(NoInnerLevelParams, CurComponentParameters, TempCurParams);
		UParameterRelativeLibrary::CombineParameters(UpperInheritParams, TempCurParams, UpperLevelParams);

	}


	//UE_LOG(LogTemp, Warning, TEXT("%s -- %s"), *FileData.FolderDBData.folder_id, *FileData.FolderDBData.folder_name);

	if (FileData.FolderDBData.folder_type == 4)  //folder_type == EFolderType
	{
		CurComponentData.ComponentType = ECompType::MultiCom;
		/*
		*  @@ get and transform current layer datas
		*/
		for (auto& Iter : FileData.ComponentDatas)
		{
			FMultiComponentDataItem& NewData = CurComponentData.ChildComponent.AddDefaulted_GetRef();
			NewData = Iter.ConvertToMultiComponentDataItem();
			NewData.ComponentID.FormatValue();
		}

		/*
		*  @@ calculate current layer data
		*/
		for (auto& Iter : CurComponentData.ChildComponent)
		{
			//FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, Iter);
			// 
			//计算引用ID
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(Iter.ComponentParameters);
			FGeometryDatas::CalculateParameterValue(GlobalParams, UpperLevelParams, LocalParamMap, Iter.ComponentID);
		}

		//need download files
		for (auto& Iter : CurComponentData.ChildComponent)
		{
			/*
			*  @@ old file data may be conponentType == ECompType::None, so it could be single or multi component
			*  @@ need to judge data in parse file
			*/
			//if (Iter.ComponentType == ECompType::None) continue;

			Iter.ComponentID.FormatValue();

			if (Iter.ComponentID.Value.IsEmpty()) continue;

			if(URefRelationFunction::AnalysisFileDownlload(
				Iter, 
				NeedDownloadIDS, 
				DependModelIDS, 
				InSkipDependFiles, 
				InDependDatas, 
				InAleadyDownloadFiles
			))
			{
				continue;
			}
		}

		//depend model
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Model())
		{
			DependModelIDS.AddUnique(Iter);
		}

		//depend mat
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Mat())
		{
			DependMatIDS.AddUnique(Iter);
		}

		//递归获取下一层数据
		if (NeedDownloadIDS.Num() <= 0 && InnerMatAdditionIDS.Num() <= 0)
		{
			for (auto& Iter : CurComponentData.ChildComponent)
			{
				FString UpperStr = TEXT("");
				for (auto& ISDF : UpperRefID)
				{
					UpperStr = FPaths::Combine(UpperStr, ISDF);
				}

				UE_LOG(LogTemp, Warning, TEXT("circle ref -- %s + %s"), *UpperStr, *Iter.ComponentID.Value);

				if (UpperRefID.Contains(Iter.ComponentID.FormatValueRet()))
				{
					return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0;
				}

				int32 NewIndex = UpperRefID.Add(Iter.ComponentID.FormatValueRet());

				URefRelationFunction::ConfirmComponentParams(Iter.ComponentParameters);

				Iter.ComponentID.FormatValue();
				if(Iter.ComponentType == ECompType::None)
				{
					FRefToLocalFileData IterFileData;
					if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter.ComponentID.Value, IterFileData))
					{
						TArray<FString> IterUpperIDs;
						TArray<FString> TempNeedDownload;
						if (!URefRelationFunction::SpliteUpperFolderDirectory(IterFileData.FolderDBData.backend_directory, IterFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, IterUpperIDs, TempNeedDownload))
						{
							URefRelationFunction::StrArrayAddUnique(NeedDownloadIDS, TempNeedDownload);
							return true;
						}
						TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> IterInheritParams = URefRelationFunction::GetUpperFolderInheritParams(IterUpperIDs);
						IterInheritParams.GenerateValueArray(Iter.InheritParams);

						URefRelationFunction::ParseNodeData_Custom(Iter, IterFileData);
						//
						FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, IterInheritParams, Iter);

						if (Iter.ComponentType == ECompType::None)
						{//rewrite type from data
							Iter.ComponentType = static_cast<ECompType>(IterFileData.FolderDBData.folder_type);
						}
						URefRelationFunction::GetRefRelationRecursiveLevelOrder(
							IterFileData,
							GlobalParams,
							UpperLevelParams,
							IterInheritParams,
							Iter,
							NeedDownloadIDS,
							InnerMatAdditionIDS,
							DependModelIDS,
							DependMatIDS,
							UpperRefID,
							InDependDatas,
							InSkipDependFiles,
							InAleadyDownloadFiles,
							MatAdditionInfos
						);
					}
					else
					{
						if (InDependDatas.ModelAlreadyHas(Iter.ComponentID.Value))
						{
							auto& ImportModelComp = Iter.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
							ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

							FCSModelMatData ModelData;
							if (InDependDatas.GetDependFile(Iter.ComponentID.Value, ModelData))
							{
								ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

								if (Iter.ComponentType == ECompType::None)
								{//new write

									Iter.ComponentType = ECompType::ImportModel;
								}
								URefRelationFunction::ParseNodeData_Model(Iter, ModelData);
							}


						}
						UpperRefID.RemoveAt(NewIndex);
						continue;
					}
				}
				else if (Iter.ComponentType == ECompType::ImportModel)
				{
					//Iter.ComponentID.FormatValue();
					if (InDependDatas.ModelAlreadyHas(Iter.ComponentID.Value))
					{
						auto& ImportModelComp = Iter.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
						ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

						FCSModelMatData ModelData;
						if (InDependDatas.GetDependFile(Iter.ComponentID.Value, ModelData))
						{
							ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);

							if (Iter.ComponentType == ECompType::None)
							{//new write

								Iter.ComponentType = ECompType::ImportModel;
							}
							URefRelationFunction::ParseNodeData_Model(Iter, ModelData);
						}


					}
					UpperRefID.RemoveAt(NewIndex);
					continue;
				}
				else
				{
					FRefToLocalFileData IterFileData;
					if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter.ComponentID.Value, IterFileData))
					{
						TArray<FString> IterUpperIDs;
						TArray<FString> TempNeedDownload;
						if (!URefRelationFunction::SpliteUpperFolderDirectory(IterFileData.FolderDBData.backend_directory, IterFileData.FolderDBData.folder_id, true, InAleadyDownloadFiles, IterUpperIDs, TempNeedDownload))
						{
							URefRelationFunction::StrArrayAddUnique(NeedDownloadIDS, TempNeedDownload);
							return true;
						}
						TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> IterInheritParams = URefRelationFunction::GetUpperFolderInheritParams(IterUpperIDs);
						IterInheritParams.GenerateValueArray(Iter.InheritParams);

						URefRelationFunction::ParseNodeData_Custom(Iter, IterFileData);
						FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, IterInheritParams, Iter);

						if (Iter.ComponentType == ECompType::None)
						{//rewrite type from data
							Iter.ComponentType = static_cast<ECompType>(IterFileData.FolderDBData.folder_type);
						}
						URefRelationFunction::GetRefRelationRecursiveLevelOrder(
							IterFileData,
							GlobalParams,
							UpperLevelParams,
							IterInheritParams,
							Iter,
							NeedDownloadIDS,
							InnerMatAdditionIDS,
							DependModelIDS,
							DependMatIDS,
							UpperRefID,
							InDependDatas,
							InSkipDependFiles,
							InAleadyDownloadFiles,
							MatAdditionInfos
						);
					}
				}

				UpperRefID.RemoveAt(NewIndex);
			}
		}
	}
	else if (FileData.FolderDBData.folder_type == 5)
	{
		CurComponentData.ComponentType = ECompType::SingleCom;
		CurComponentData.SingleComponentData.ComponentItems = FileData.FileData.component_datas;
		FGeometryDatas::CalculateParameterValue(GlobalParams, UpperLevelParams, {}, CurComponentData.SingleComponentData);
		CurComponentData.SingleComponentPath = FileData.FileData.file_data_path;

		//calculate load material add to depend mat
		for (auto& SCD : CurComponentData.SingleComponentData.ComponentItems)
		{
			if (!SCD.ComponentMaterial.Value.IsEmpty() && SCD.ComponentMaterial.Value.IsNumeric())
			{
				SCD.ComponentMaterial.FormatValue();

				if(InSkipDependFiles.Contains(SCD.ComponentMaterial.Value)) continue;
				if(InAleadyDownloadFiles.Contains(SCD.ComponentMaterial.Value)) continue;
				if (InDependDatas.MatFileAlreadyHas(SCD.ComponentMaterial.Value)) continue;

				DependMatIDS.AddUnique(SCD.ComponentMaterial);
			}
		}

		//depend model
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Model())
		{
			Iter.FormatValue();
			if (InSkipDependFiles.Contains(Iter.Value)) continue;
			if (InAleadyDownloadFiles.Contains(Iter.Value)) continue;
			if (InDependDatas.ModelAlreadyHas(Iter.Value)) continue;
			DependModelIDS.AddUnique(Iter);
		}

		//depend mat
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Mat())
		{
			Iter.FormatValue();
			if (InSkipDependFiles.Contains(Iter.Value)) continue;
			if (InAleadyDownloadFiles.Contains(Iter.Value)) continue;
			if (InDependDatas.MatFileAlreadyHas(Iter.Value)) continue;
			DependMatIDS.AddUnique(Iter);
		}

	}
	return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0 || InnerMatAdditionIDS.Num() > 0;
}

bool URefRelationFunction::GetRefComponentData(const FRefToFileComponentData& InRefLocalFileData, FMultiComponentDataItem& OutComponentDatas)
{

	return true;
}

bool URefRelationFunction::AnalysisFileDownlload(FMultiComponentDataItem& ItemData,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	const TArray<FString>& SkipDependFiles,
	FDependFileData InDependDatas,
	const TArray<FString>& InAleadyDownloadFiles
)
{
	ItemData.ComponentID.FormatValue();

	FString FileName = URefRelationFunction::FormatFolderID(ItemData.ComponentID.Value);
	FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FileName);

	if (ItemData.ComponentType == ECompType::None || ItemData.ComponentType == ECompType::MultiCom)
	{
		/*
		*  @@ custom file -> model file -> none
		*  @@ step 1 : judge has compare as custom file, if not input file id to download custom file array [NeedDownloadIDS]
		*  @@ if step 1 is true, then step 2
		*  @@ step 2 : judge has skip as import file, if not input file id to import file array [DependModelIDS]
		*  @@ if step 2 is true, then all skip , this file has no ref, only save component id
		*/
		FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath));
		if (!FPaths::FileExists(AbsPath))
		{
			//model file depend
			if (SkipDependFiles.Contains(FileRelativePath))
			{
				if (!SkipDependFiles.Contains(ItemData.ComponentID.Value) && !InDependDatas.ModelAlreadyHas(ItemData.ComponentID.Value))
				{
					DependModelIDS.AddUnique(ItemData.ComponentID);
				}
				return true; // return true to skip this step in outside for loop  
			}
			else
			{
				//custom file depend
				NeedDownloadIDS.AddUnique(FileRelativePath);
			}
			
		}
		else
		{//has custom file [.data], judge uuid info [if already download, skip download]

			URefRelationFunction::CompareLocalAndRef_SyncUUID(
				ItemData, 
				NeedDownloadIDS, 
				InAleadyDownloadFiles, 
				FileRelativePath
			);
		}

		//consider origin folder files


	}
	else if(ItemData.ComponentType == ECompType::ImportModel)
	{
		if (!InDependDatas.ModelAlreadyHas(ItemData.ComponentID.Value) && !SkipDependFiles.Contains(ItemData.ComponentID.Value))
		{
			DependModelIDS.AddUnique(ItemData.ComponentID);
		}
	}
	else if(ItemData.ComponentType == ECompType::SingleCom)
	{
		if (!FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath))))
		{
			if (!SkipDependFiles.Contains(ItemData.ComponentID.Value))
			{
				NeedDownloadIDS.AddUnique(FileRelativePath);
			}
		}
		else
		{
			URefRelationFunction::CompareLocalAndRef_SyncUUID(
				ItemData,
				NeedDownloadIDS,
				InAleadyDownloadFiles,
				FileRelativePath
			);
		}
	}
	else
	{
		checkNoEntry();
	}

	return false;
}

void URefRelationFunction::EmptyNodeData(FMultiComponentDataItem& ItemData)
{
	ItemData.ChildComponent.Empty();
	ItemData.ComponentName.Empty();
	ItemData.ComponentNameExp.Empty();
	ItemData.Code.Empty();
	ItemData.CodeExp.Empty();
	ItemData.SingleComponentPath.Empty();
	ItemData.SingleComponentData.ComponentItems.Empty();
}

void URefRelationFunction::ParseNodeData_Custom(FMultiComponentDataItem& ItemData, const FRefToLocalFileData& FileData)
{
	//ItemData.ComponentName = FileData.FolderDBData.folder_name;
	ItemData.ComponentNameExp = FileData.FolderDBData.folder_name_exp;
	ItemData.CodeExp = FileData.FolderDBData.folder_code_exp;
	ItemData.Description = FileData.FolderDBData.description;
	//ItemData.Code = FileData.FolderDBData.folder_code;
}

void URefRelationFunction::ParseNodeData_Model(FMultiComponentDataItem& ItemData, const FCSModelMatData& FileData)
{
	ItemData.ComponentName = FileData.name;
	ItemData.CodeExp = FileData.folderCode;
	ItemData.Code = FileData.folderCode;
}

void URefRelationFunction::CompareLocalAndRef_SyncUUID(
	FMultiComponentDataItem& ItemData,
	TArray<FString>& NeedDownloadIDS, 
	const TArray<FString>& InAleadyDownloadFiles, 
	const FString& InRelativePath
)
{
	FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), InRelativePath));
	int64 FileSize = 0;
	FString LocalUUID = TEXT("");
	ACatalogPlayerController::GetFileMD5AndSize(AbsPath, LocalUUID, FileSize);

	if (!InAleadyDownloadFiles.Contains(InRelativePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("Tree Node[%s - %s] compare uuid : UUID In Tree[%s] -- UUID InFile [%s]")
			, *ItemData.ComponentID.Value, *ItemData.ComponentName, *ItemData.RefFileUUID, *LocalUUID);
		//if (!LocalUUID.Equals(ItemData.RefFileUUID, ESearchCase::IgnoreCase))
		{
			NeedDownloadIDS.AddUnique(InRelativePath);
		}
	}
	else
	{// update file info
		UE_LOG(LogTemp, Warning, TEXT("Tree Node[%s - %s] update tree UUID : UUID In Tree[%s] -- UUID InFile [%s]")
			, *ItemData.ComponentID.Value, *ItemData.ComponentName, *ItemData.RefFileUUID, *LocalUUID);
		ItemData.RefFileUUID = LocalUUID;
	}
}

FString URefRelationFunction::FormatFolderID(const FString& InFolderID)
{
	if (!InFolderID.IsNumeric())
		return InFolderID;

	const int64 FolderID = FCString::Atoi64(*InFolderID);
	return FString::Printf(TEXT("%lld"), FolderID);
}

bool URefRelationFunction::IsExpressionCorrect(const FString& InExpression)
{
	TArray<FString> Words;
	FString ErrorMessage;
	UGrammerAnalysisSubsystem* GrammerAnalysis = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>();
	bool Res = GrammerAnalysis->LexicalAnalysis(InExpression, Words, ErrorMessage);
	if (Res)
	{
		TArray<FString> WordsFormatted;
		Res = GrammerAnalysis->FormatWords(Words, WordsFormatted, ErrorMessage);
		if (Res)
		{
			FString OutValue;
			Res = GrammerAnalysis->CalculateExpressionValue(WordsFormatted, OutValue, ErrorMessage);
		}
	}
	return Res;
}

TArray<FParameterData> URefRelationFunction::FormatCleanParams(const TArray<FParameterData>& InParams)
{
	TArray<FParameterData> Res;
	TArray<FString> HasParam;
	for (const auto& Iter : InParams)
	{
		bool CompileRes = HasParam.ContainsByPredicate(
			[Iter](const FString& HP)->bool 
			{
				return HP.Equals(Iter.Data.name, ESearchCase::CaseSensitive);
			}
		);
		if(CompileRes) continue;

		Res.Add(Iter);
		HasParam.AddUnique(Iter.Data.name);
	}
	return Res;
}

void URefRelationFunction::StrArrayAddUnique(TArray<FString>& Array1, const TArray<FString>& AddArray)
{
	for (const auto& AA : AddArray)
	{
		Array1.AddUnique(AA);
	}
}

void URefRelationFunction::ParseMatAdditionData(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& GlobalParams, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& ModifyParams, const TArray<FMatAdditionInfo>& MatAdditionInfos)
{
	const FString DZCZStr = TEXT("DZCZ");
	if (ModifyParams.Contains(DZCZStr))
	{
		const FString MatFolderID = ModifyParams[DZCZStr].Data.value;
		const int32 MatIndex = MatAdditionInfos.IndexOfByPredicate(
			[&MatFolderID](const FMatAdditionInfo& Info)->bool
			{
				return MatFolderID.Equals(Info.FolderID, ESearchCase::CaseSensitive);
			}
		);
		if (MatIndex != INDEX_NONE)
		{
			const FMatAdditionInfo& CurMatInfo = MatAdditionInfos[MatIndex];
			TArray<FString> AdditionMatStr = CurMatInfo.GetAdditionStrArr();
			for (const auto& AMS : AdditionMatStr)
			{
				FString Exp, Value;
				AMS.Split(TEXT("="), &Exp, &Value);
				if (!Exp.IsEmpty() && !Value.IsEmpty())
				{
					if (ModifyParams.Contains(Exp))
					{//has param
						ModifyParams[Exp].Data.expression = Value;
						ModifyParams[Exp].Data.value = Value;
					}
					else if (GlobalParams.Contains(Exp))
					{//no param, new param to add
						FParameterData ToAddParam = GlobalParams[Exp];
						ToAddParam.Data.expression = Value;
						ToAddParam.Data.value = Value;
						ModifyParams.Add(Exp, ToAddParam);
					}
				}
			}
		}
	}
}

bool URefRelationFunction::SaveStyleRefRelationToFile(const FRefToStyleFile& RefStyleFile)
{
	FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	return UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, RefStyleFile);
}

bool URefRelationFunction::GetStyleRefRelationFromFile(FRefToStyleFile& OutStyleRef)
{
	FString RefFilePathIdentify = URefToStyleDataLibrary::GetStyleFileAddress();
	if (!FPaths::FileExists(RefFilePathIdentify)) return false;
	return UProtobufOperatorFunctionLibrary::LoadRelationFromFile(RefFilePathIdentify, OutStyleRef);
}

bool URefRelationFunction::IsConstructChange(TArray<FSingleComponentItem> OldConstruct, const TArray<FSingleComponentItem>& NewConstruct)
{
	if (OldConstruct.Num() != NewConstruct.Num())
		return true;

	for (int32 i = 0; i < OldConstruct.Num(); ++i)
	{
		if (!OldConstruct[i].Equal_Precise(NewConstruct[i]))
			return true;
	}
	return false;
}

bool URefRelationFunction::IsConstructChange(TArray<FMultiComponentDataItem> OldConstruct, TArray<FMultiComponentDataItem>& NewConstruct)
{
	if (OldConstruct.Num() != NewConstruct.Num())
		return true;

	for (int32 i = 0; i < OldConstruct.Num(); ++i)
	{
		if (OldConstruct[i].Equal_Precise(NewConstruct[i]))
			return true;

		if (URefRelationFunction::EnsureDefaultExpressionValid(NewConstruct[i].ComponentParameters))
			return true;
	}
	return false;
}

bool URefRelationFunction::IsConstructChange(const TArray<FRefToFileComponentData>& OldConstruct, TArray<FMultiComponentDataItem>& NewConstruct)
{
	if (OldConstruct.Num() != NewConstruct.Num())
		return true;

	for (int32 i = 0; i < OldConstruct.Num(); ++i)
	{
		if (URefRelationFunction::IsConstructChange(OldConstruct[i], NewConstruct[i]))
			return true;

		if(URefRelationFunction::EnsureDefaultExpressionValid(NewConstruct[i].ComponentParameters))
            return true;
	}
	return false;
}

bool URefRelationFunction::IsConstructChange(const FRefToFileComponentData& OldConstruct, FMultiComponentDataItem& NewConstruct)
{
	FMultiComponentDataItem ConvertData = OldConstruct.ConvertToMultiComponentDataItem();
	/*if (!ConvertData.ComponentVisibility.Equals_Precise(NewConstruct.ComponentVisibility) ||
		!ConvertData.ComponentID.Equals_Precise(NewConstruct.ComponentID) ||
		!ConvertData.Description.Equals(NewConstruct.Description) ||
		!ConvertData.Code.Equals(NewConstruct.Code) ||
		!ConvertData.CodeExp.Equals(NewConstruct.CodeExp) ||
		!ConvertData.ComponentLocation.Equals_Precise(NewConstruct.ComponentLocation) ||
		!ConvertData.ComponentRotation.Equals_Precise(NewConstruct.ComponentRotation) ||
		!ConvertData.ComponentScale.Equals_Precise(NewConstruct.ComponentScale)
		)
	{
		return true;
	}*/

	if(!ConvertData.Equal_Precise_NoChild(NewConstruct))
	{
		return true;
	}

	if (ConvertData.ComponentParameters.Num() != NewConstruct.ComponentParameters.Num())
		return true;
	for (int32 i = 0; i < ConvertData.ComponentParameters.Num(); ++i)
	{
		if (!ConvertData.ComponentParameters[i].Equal_Precise(NewConstruct.ComponentParameters[i]))
		{
			return true;
		}
	}

	if (URefRelationFunction::EnsureDefaultExpressionValid(NewConstruct.ComponentParameters))
		return true;

	return false;
}


template <class Elem, class Byte = char, class Statype = std::mbstate_t>
class TCodeConvertByName : public std::codecvt<Elem, Byte, Statype> {
public:
	explicit TCodeConvertByName(const char* Locname, size_t Refs = 0)
		: std::codecvt<Elem, Byte, Statype>(Locname, Refs) {}					// depend name to convert
	explicit TCodeConvertByName(const std::string& Str, size_t Refs = 0)
		: std::codecvt<Elem, Byte, Statype>(Str.c_str(), Refs) {}

	virtual ~TCodeConvertByName() {}
};
typedef TCodeConvertByName<TCHAR> FCodeConvertByName;

std::string URefRelationFunction::WideCharToMultiByte(const TCHAR* WChar)
{
#if PLATFORM_WINDOWS // Windows ANSI Coding
	const std::string& OSLang = TCHAR_TO_UTF8(*FInternationalization::Get().GetDefaultLanguage()->GetName());
	static std::wstring_convert<FCodeConvertByName, TCHAR> StrCnv(new FCodeConvertByName(OSLang));

	try
	{
		return std::string(StrCnv.to_bytes(WChar));
	}
	catch (std::exception)
	{
		return TCHAR_TO_UTF8(WChar);
	}
#endif

	return TCHAR_TO_UTF8(WChar);
}

FString URefRelationFunction::MultiByteToWideChar(const char* MultiByteChar)
{
	const std::string& OSLang = TCHAR_TO_UTF8(*FInternationalization::Get().GetDefaultLanguage()->GetName());
	static std::wstring_convert<FCodeConvertByName, TCHAR> StrCnv(new FCodeConvertByName(OSLang));

	try
	{
		return StrCnv.from_bytes(MultiByteChar).c_str();
	}
	catch (std::exception)
	{
		return UTF8_TO_TCHAR(MultiByteChar);
	}
}




void URefRelationFunction::UnPackFile(FString PackFilePath, FString UnpackPath)
{

	//open zlib file
	const unzFile& ZFile = unzOpen64(URefRelationFunction::WideCharToMultiByte(*PackFilePath).c_str());

	//get global info
	unz_global_info64 ZGlobalInfo;
	unzGetGlobalInfo64(ZFile, &ZGlobalInfo);

	//get file size for process
	const int64 TotalCompressSize = IFileManager::Get().FileSize(*PackFilePath);
	int64 CurrentCompressSize = 0;

	//list file in pack
	unz_file_info64 ZFileInfo;

	const int NAME_BUFF_SIZE = 512;
	const int FILE_DATA_INIT_BUFF_SIZE = 512 * 1024;
	char* FileName = new char[NAME_BUFF_SIZE];
	char* FileData = new char[FILE_DATA_INIT_BUFF_SIZE];

	int ReadLength = 0;

	//error num
	uint32 ErrorCount = 0;
	//folder or file num
	uint32 TotalCount = ZGlobalInfo.number_entry;
	for (int i = 0; i < ZGlobalInfo.number_entry; ++i)
	{
		if (i > 0)
		{
			//close current file
			unzCloseCurrentFile(ZFile);
			//point to next file
			unzGoToNextFile(ZFile);
		}

		//get current file info
		if (unzGetCurrentFileInfo64(ZFile, &ZFileInfo, FileName, NAME_BUFF_SIZE
			, nullptr, 0, nullptr, 0) != UNZ_OK)
		{
			ErrorCount++;
			continue;
		}

		//open current file
		if (unzOpenCurrentFile(ZFile) != UNZ_OK)
		{
			ErrorCount++;
			continue;
		}

		FString AbsFullFilePath = FPaths::ConvertRelativePathToFull(FPaths::Combine(UnpackPath, FileName));
		if (ZFileInfo.uncompressed_size == 0)
		{
			if (AbsFullFilePath.EndsWith(TEXT("/")) || AbsFullFilePath.EndsWith(TEXT("\\")))
			{
				if (!IFileManager::Get().MakeDirectory(*AbsFullFilePath, true))
				{
					ErrorCount++;
					continue;
				}
			}
			else
			{
				if (!FFileHelper::SaveStringToFile(TEXT(""), *AbsFullFilePath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
				{
					ErrorCount++;
					continue;
				}
			}
		}
		else
		{
			TSharedPtr<FArchive> ArchivePtr = TSharedPtr<FArchive>(IFileManager::Get().CreateFileWriter(*AbsFullFilePath));
			if (ArchivePtr != nullptr)
			{
				const double UnCompressRadio = ((double)ZFileInfo.compressed_size) / ZFileInfo.uncompressed_size;
				const int BlockCount = (ZFileInfo.uncompressed_size + FILE_DATA_INIT_BUFF_SIZE - 1) / FILE_DATA_INIT_BUFF_SIZE;
				for (int Block = 0; Block < BlockCount; ++Block)
				{
					// read stream
					ReadLength = unzReadCurrentFile(ZFile, FileData, FILE_DATA_INIT_BUFF_SIZE);
					ArchivePtr->Serialize(FileData, ReadLength);

					// 
					CurrentCompressSize += ReadLength * UnCompressRadio;
				}

				if (!ArchivePtr->Close())
				{
					ErrorCount++;
					continue;
				}
			}
			else
			{
				ErrorCount++;
				continue;
			}
		}
	}

	unzClose(ZFile);
	delete[] FileName;
	delete[] FileData;

}

void URefRelationFunction::PackFile(const FString& UnPackFilePath, const FString& PackPath)
{
	uint32 ErrorCount = 0;
	uint32 TotalCount = 0;

	const zipFile& ZFile = zipOpen64(URefRelationFunction::WideCharToMultiByte(*UnPackFilePath).c_str(), APPEND_STATUS_CREATE);

	int64 TotalUnCompressedSize = 0;
	int64 CurrentUnCompressedSize = 0;
	IFileManager::Get().IterateDirectoryRecursively(*PackPath, [&TotalUnCompressedSize](const TCHAR* FilenameOrDirectory, bool bIsDirectory)
		{
			if (!bIsDirectory)
			{
				TotalUnCompressedSize += IFileManager::Get().FileSize(FilenameOrDirectory);
			}
			return true;
		});


	const int FILE_DATA_INIT_BUFF_SIZE = 512 * 1024;
	char* FileData = new char[FILE_DATA_INIT_BUFF_SIZE];

	TArray<FString> AllFilenames; // file or directories	
	IFileManager::Get().FindFilesRecursive(AllFilenames, *PackPath, TEXT("*.*"), true, true);

	TotalCount = AllFilenames.Num();
	for (uint32 i = 0; i < TotalCount; ++i)
	{
		const FString& Filename = AllFilenames[i];
		FDateTime DataTime = IFileManager::Get().GetTimeStamp(*Filename);
		DataTime = DataTime + (FDateTime::Now() - FDateTime::UtcNow());
		const int64 LocalFileSize = IFileManager::Get().FileSize(*Filename);

		zip_fileinfo ZipFileInfo;
		int32 Year, Month, Day;
		DataTime.GetDate(Year, Month, Day);
		ZipFileInfo.tmz_date.tm_year = Year;
		ZipFileInfo.tmz_date.tm_mon = Month - 1;
		ZipFileInfo.tmz_date.tm_mday = Day;
		ZipFileInfo.tmz_date.tm_hour = DataTime.GetHour();
		ZipFileInfo.tmz_date.tm_min = DataTime.GetMinute();
		ZipFileInfo.tmz_date.tm_sec = DataTime.GetSecond();

		ZipFileInfo.dosDate = 0;
		ZipFileInfo.internal_fa = 0;
		ZipFileInfo.external_fa = 0;

		FString LocalFilename = *Filename.Replace(*PackPath, TEXT("")); //
		if (LocalFilename.StartsWith(TEXT("/")))
		{
			LocalFilename = LocalFilename.RightChop(1);
		}
		else if (LocalFilename.StartsWith(TEXT("\\")))
		{
			LocalFilename = LocalFilename.RightChop(2);
		}

		const bool bDirectory = FPaths::DirectoryExists(AllFilenames[i]);
		if (bDirectory)
		{
			LocalFilename += TEXT("/");
		}

		if (i > 0)
		{
			zipCloseFileInZip(ZFile);
		}

		// 
		if (zipOpenNewFileInZip64(ZFile, URefRelationFunction::WideCharToMultiByte(*LocalFilename).c_str(), &ZipFileInfo,
			nullptr, 0, nullptr, 0,
			nullptr, Z_DEFLATED, Z_DEFAULT_COMPRESSION, LocalFileSize >= UINT32_MAX) != ZIP_OK)
		{
			ErrorCount++;
			continue;
		}

		if (!bDirectory)
		{
			TSharedPtr<FArchive> ArchivePtr = TSharedPtr<FArchive>(IFileManager::Get().CreateFileReader(*Filename));
			if (ArchivePtr != nullptr)
			{
				const int64 TotalReadSize = ArchivePtr->TotalSize();
				const int BlockCount = (TotalReadSize + FILE_DATA_INIT_BUFF_SIZE - 1) / FILE_DATA_INIT_BUFF_SIZE;
				for (int Block = 0; Block < BlockCount; ++Block)
				{
					int CurrReadSize = 0;
					if (Block == BlockCount - 1)
					{
						CurrReadSize = TotalReadSize - Block * FILE_DATA_INIT_BUFF_SIZE;
					}
					else
					{
						CurrReadSize = FILE_DATA_INIT_BUFF_SIZE;
					}

					ArchivePtr->Serialize(FileData, CurrReadSize);
					if (zipWriteInFileInZip(ZFile, FileData, CurrReadSize) != ZIP_OK)
					{
						ErrorCount++;
						continue;
					}
					else
					{

						CurrentUnCompressedSize += CurrReadSize;
					}
				}

				if (!ArchivePtr->Close())
				{
					ErrorCount++;
					continue;
				}
			}
			else
			{
				ErrorCount++;
				continue;
			}
		}
	}

	delete[] FileData;
	zipClose(ZFile, nullptr);

}

