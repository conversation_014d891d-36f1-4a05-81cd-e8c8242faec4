// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#pragma warning(disable:4996)

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include <string>

#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "RefRelationFunction.generated.h"

struct FFrontDirectoryConbine;
struct FFolderTableData;
struct FRefDirectoryData;
struct FSingleComponentItem;
struct FRefToFileComponentData;
struct FRefToLocalFileData;
struct FParameterTableData;
struct FMultiComponentDataItem;
struct FRefToStyleFile;
/**
 *   @@ 文件引用关系解析方法库
 */
UCLASS(Blueprintable)
class DESIGNSTATION_API URefRelationFunction : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

	/*
	 *  @@ 文件读取、保存操作
	 *	@@ 模块循环引用（ 方法暂不使用 ）
	 */
#pragma region FileSaveLoad
public:
	/*
	//保存、加载关系文件
	static bool SaveRelationToFile(const FRefToLocalFileData& InRelationDatas);
	static bool LoadRelationFromFile(FRefToLocalFileData& OutRelationDatas);
	static bool SaveRelationToFile(FString InRelationFilePath, const FRefToLocalFileData& InRelationDatas);
	static bool LoadRelationFromFile(FString InRelationFilePath, FRefToLocalFileData& OutRelationDatas);

	//保存、加载风格文件
	static bool SaveRelationToFile(const FRefToStyleFile& InDatas);
	static bool LoadRelationFromFile(FRefToStyleFile& OutDatas);
	static bool SaveRelationToFile(FString InRelationFilePath, const FRefToStyleFile& InDatas);
	static bool LoadRelationFromFile(FString InRelationFilePath, FRefToStyleFile& OutDatas);

private:
	template<class T>
	static bool SaveStreamToFile(FString InRelationFilePath, const T& InRelationDatas)
	{
		FString ContentFolder = FPaths::GetPath(InRelationFilePath);
		CHECK_FOLDER_VALID(ContentFolder);
		std::wstring wstr(*InRelationFilePath);
		std::fstream saveString(wstr, std::ios::out | std::ios::binary | std::ios::trunc);
		if (false == InRelationDatas.SerializeToOstream(&saveString))
		{
			UE_LOG(LogTemp, Error, TEXT("Serialize failed! File path is %s"), *InRelationFilePath);
			return false;
		}
		return true;

	}

	template<class T>
	static bool LoadStreamFromFile(FString InRelationFilePath, T& OutRelationDatas)
	{
		std::wstring wstr(*InRelationFilePath);
		std::fstream loadString(wstr, std::ios::in | std::ios::binary);
		if (false == OutRelationDatas.ParseFromIstream(&loadString))
		{
			UE_LOG(LogTemp, Error, TEXT("Deserialize failed! File path is %s"), *InRelationFilePath);
			return false;
		}
		return true;
	}
	*/


	/*
	 *  @@ auto save a backup file when new data rewrite old file
	 *  @@ save to path :  ../../Saved/CatalogAutoSave/..
	 *  @@ TODO : undo / redo
	 */
	static void AutoCopyFileBackup(const FRefToLocalFileData& InData);

	static bool SaveFile(const FRefToLocalFileData& InDatas);
	static bool SaveFile(const FString& RelativePath, const FRefToLocalFileData& InDatas);
	static void SyncFileNetwork(const FRefToLocalFileData& InDatas);
	static void SyncFileNetwork(const FRefToStyleFile& InDatas);

	static bool SaveTempLocalFile(const FRefToLocalFileData& InData);

	/*
	 *  @@ generate local ref file
	 *  @@ return file relative path
	 *
	 */
	static FString SaveNewLocalFile(const FRefDirectoryData& Data, const int32& FolderType);

	/*
	 *  @@ rename local ref file
	 *  @@ OldFilePath : old file path
	 *  @@ NewFilePath : new file path
	 *  @@ ret val : rename success ?
	 */
	static bool RenameLocalFile(const FString& OldFilePath, const FString& NewFilePath);

	/*
	*  @@ delete local ref file when delete
	*/
	static bool DeleteLocalFile(const FFolderTableData& Data);

#pragma endregion

	/*
	 *	@@ 新的文件夹文件
	 *	@@ 字符串拼接形成地址
	 */
#pragma region DIRECTORY



	 /*
	  *  @@ 将原数据库信息转换为新的地址信息
	  *	@@ 转换方法，后续逻辑不用
	  */
	static void ConvertDBDataToDirctoryData(const FFolderTableData& InData, FRefDirectoryData& OutDirData);
	static void ConvertDirctoryDataToDBData(const FRefDirectoryData& InData, FFolderTableData& OutData);
	static void ConvertDirctoryDataToDBData(const TArray<FRefDirectoryData>& InData, TArray<FFolderTableData>& OutData);

	static void GetRootDirectory(TArray<FRefDirectoryData>& OutDatas);
	static void GetRootDirectory(TArray<FFolderTableData>& OutDatas);

	static void GetSubDirectory(const FString& DirPathStr, TArray<FRefDirectoryData>& OutDatas);
	static void GetSubDirectory(const FString& DirPathStr, TArray<FFolderTableData>& OutDatas);

	static bool IsRootDirectory(const FString& InDirectory);
	static TArray<FRefDirectoryData> FliteRootDirectory(const TArray<FRefDirectoryData>& InDatas);

	/**
	 * @Retrieves the size information (width, height, depth) from the provided parameter map.
	 *
	 * @param ParamsMap A map containing parameter data with keys as parameter names and values as FParameterData.
	 * @param OutW Output parameter to store the width information as a string.
	 * @param OutH Output parameter to store the height information as a string.
	 * @param OutD Output parameter to store the depth information as a string.
	 */
	static void GetSizeInfo(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParamsMap, FString& OutW, FString& OutH, FString& OutD);
	static void GetSizeInfo(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GP, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OP, FString& OutW, FString& OutH, FString& OutD);

	/*
	 *  @@ 获取文件夹下的对应层级的数据
	 *	@@ AllDatas ： 文件夹下所有数据
	 *	@@ Level --> 1 ： 标识子文件文件夹； 2 ： 标识子子文件文件夹； ...
	 */
	static TArray<FRefDirectoryData> GetSubLevelDirectory(const TArray<FRefDirectoryData>& AllDatas, const FString& CurDirectory);

	static TArray<FRefDirectoryDataContent> GetDirectoryContentData(const TArray<FRefDirectoryData>& AllDatas, const TArray<FString>& CurDirectoryArr);

	/*
	 *	@@ 获取文件夹目录
	 *	@@ 文件地址去除最后路径
	 */
	static FString GetFolderDirectory(FString InDirectory, bool IsFolder);

	/*
	*  @@ is same root directory
	*  @@ for folder / file copy / shear action
	*/
	static bool IsSameRootDirectory(const FString& InPath1, const FString& InPath2);

	/*
	*  @@ is file / folder in same folder directory
	*/
	static bool IsSameFolderDirectory(const FString& InPath1, const FString& InPath2);

	/*
	 *  @@ 获取上层文件夹地址数组
	 */
	static TArray<FString> GetUpperFolderDirectory(const FString& InDirectory, bool HasLast);

	/*
	*  @@ 切割上层文件层级
	*  @@ RetVal = true : 不需要下载上层依赖dat文件
	*/
	static bool SpliteUpperFolderDirectory(
		const FString& InDirectory, 
		const FString& InFolderID,
		bool HasLast,
		const TArray<FString>& AleadyDownloadFiles, 
		TArray<FString>& UpperIds, 
		TArray<FString>& NeedDownloadFiles
	);

	/**
	 *  @@ 获取上层继承参数
	 */
	static TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetUpperFolderInheritParams(const TArray<FString>& UpperIds);

	/*
	*  @@ get this file or folder contain folder
	*  @@ InDirectory : file or folder backendDirectory
	*  @@ return value : contain folder id
	*/
	static FString GetThisContainFolderID(const FString& InBackDirectory);

	/*
	*  @@ get folder level
	*/
	static int32 GetFolderLevel(const FString& InDirectiry);

	/*
	 *  @@ 获取全局参数
	 */
	static TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetGlobalParameters();

	/**
	 *  @@ 确保默认表达式与值表达式一致
	 *  @@ RetValue : true 标识比对后有修改
	 */
    static bool EnsureDefaultExpressionValid(TArray<FParameterData>& InParams);

	static bool EnsureMultiComponentDefaultExpress(TArray<FMultiComponentDataItem>& MultiConstruct);
	static bool EnsureMultiComponentDefaultExpress(FMultiComponentDataItem& MultiConstruct);

	/*
	*	@@ Get ref file name for file
	 *  @@ get [id] or [folder_id] for backend directory
	 *	@@ if [folder_id] empty, use [id]
	 *	@@ else if is_folder use [id] or use [folder_id]
	 */
	static FString GetMarkToBackendDirectory(const FRefDirectoryData& Data);
	static FString GetMarkToBackendDirectory(const FFolderTableData& Data);
	static FString GetMarkToBackendDirectory(const FRefToLocalFileData& Data);

	/*
	 *	 @@ get ref file relative path
	 *   @@ return value :  relative path
	*/
	static FString GetRefFileRelativePath(const FRefDirectoryData& Data);
	static FString GetRefFileRelativePath(const FFolderTableData& Data);
	static FString GetRefFileRelativePath(const FRefToLocalFileData& Data);

	static bool NeedDownloadFile(const FRefDirectoryData& Data);

	/*
	*  @@ combine zip path
	*/
	static FString GetZipRelativePath(const FString& PakPath, bool IsAbs);


	static TArray<FString> GetAllFileInFolders(FString InFolderPath);

	static FString GetCurrentPCData();
	static FString GetCurrentPCTime();

#pragma endregion

	/*
	 *  @@ 解析文件引用关系
	 */
#pragma region FileRefRelation

public:
	//获取上层文件夹数据
	static void GetTopLevelFolderParameterData(const FString& InDirectory, TArray<FParameterTableData>& OutParameterDatas);
	static void GetTopLevelFolderParameterData(const TArray<FRefToLocalFileData>& DirectoryRefDatas, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutheritParameters);
	static void GetTopLevelFolderParameterDataHasCalculate(const TArray<FRefToLocalFileData>& DirectoryRefDatas, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & OutheritParameters);

	static TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ConvertParamsArrayToMap(const TArray<FParameterData>& Params);
	static TArray<FParameterData> ConvertParamsMapToArray(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Params);

	//获取结构信息
	static bool GetMultiComponentInfo(
		const FString& InID,
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & UpperLevelParams,
		FMultiComponentDataItem& OutComponentDatas,
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& InnerMatAdditionIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		TArray<FExpressionValuePair>& DependMatIDS,
		const FDependFileData& InDependDatas,
		const TArray<FString>& InSkipDependFiles,
		const TArray<FString>& InAleadyDownloadFiles,
		const TArray<FMatAdditionInfo>& MatAdditionInfos,
		bool IsInit = true
	);
	static bool GetMultiComponentInfo(
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UpperLevelParams,
		FMultiComponentDataItem& OutComponentDatas,
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& InnerMatAdditionIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		TArray<FExpressionValuePair>& DependMatIDS,
		FDependFileData InDependDatas,
		TArray<FString> InSkipDependFiles,
		const TArray<FString>& InAleadyDownloadFiles,
		const TArray<FMatAdditionInfo>& MatAdditionInfos
	);


	static bool GetSingleComponentInfo(const FString& InID, TArray<FSingleComponentItem>& OutComponentDatas);

	static void ConfirmComponentParams(FMultiComponentDataItem& ModifyComponent);
	static void ConfirmComponentParams(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ModifyParams);
	static void ConfirmComponentParams(TArray<FParameterData>& ModifyParams);


	//获取引用关系，分层递归
	static bool GetCurrentRefRelationFromFile(const FString& InID, FRefToLocalFileData& FileData);
	static bool GetRefRelationByPath(const FString& InPath, FRefToLocalFileData& FileData);

	//规范化folder_id格式
	static FString FormatFolderID(const FString& InFolderID);

	//
	static bool IsExpressionCorrect(const FString& InExpression);

	//format clean params
	static TArray<FParameterData> FormatCleanParams(const TArray<FParameterData>& InParams);

	//
	static void StrArrayAddUnique(TArray<FString>& Array1, const TArray<FString>& AddArray);

	static void ParseMatAdditionData(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& GlobalParams
		, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& ModifyParams, const TArray<FMatAdditionInfo>& MatAdditionInfos);

private:
	//递归获取引用关系
	static bool GetRefRelationRecursiveLevelOrder(
		FRefToLocalFileData& FileData,
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & GlobalParams,
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UpperLevelParams,
		const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & UpperInheritParams,
		FMultiComponentDataItem& CurComponentData,
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& InnerMatAdditionIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		TArray<FExpressionValuePair>& DependMatIDS,
		TArray<FString>& UpperRefID,
		FDependFileData InDependDatas,
		TArray<FString> InSkipDependFiles,
		const TArray<FString>& InAleadyDownloadFiles,
		const TArray<FMatAdditionInfo>& MatAdditionInfos
	);
	static bool GetRefComponentData(const FRefToFileComponentData& InRefLocalFileData, FMultiComponentDataItem& OutComponentDatas);

	static bool AnalysisFileDownlload(FMultiComponentDataItem& ItemData, 
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		const TArray<FString>& SkipDependFiles,
		FDependFileData InDependDatas,
		const TArray<FString>& InAleadyDownloadFiles
	);

	static void EmptyNodeData(FMultiComponentDataItem& ItemData);
	static void ParseNodeData_Custom(FMultiComponentDataItem& ItemData, const FRefToLocalFileData& FileData);
	static void ParseNodeData_Model(FMultiComponentDataItem& ItemData, const FCSModelMatData& FileData);

	static void CompareLocalAndRef_SyncUUID(
		FMultiComponentDataItem& ItemData, 
		TArray<FString>& NeedDownloadIDS, 
		const TArray<FString>& InAleadyDownloadFiles,
		const FString& InRelativePath
	);

#pragma endregion

	/*
	 *  @@ 解析风格引用关系
	 */
#pragma region StyleRefRelation

public:
	static bool SaveStyleRefRelationToFile(const FRefToStyleFile& RefStyleFile);
	static bool GetStyleRefRelationFromFile(FRefToStyleFile& OutStyleRef);

#pragma endregion


#pragma region FrontDirectory


#pragma endregion

	/*
	 *  @@ 部件
	 */
#pragma region Component

	static bool IsConstructChange(TArray<FSingleComponentItem> OldConstruct, const TArray<FSingleComponentItem>& NewConstruct);
	static bool IsConstructChange(TArray<FMultiComponentDataItem> OldConstruct, TArray<FMultiComponentDataItem>& NewConstruct);
	static bool IsConstructChange(const TArray<FRefToFileComponentData>& OldConstruct, TArray<FMultiComponentDataItem>& NewConstruct);
	static bool IsConstructChange(const FRefToFileComponentData& OldConstruct, FMultiComponentDataItem& NewConstruct);

#pragma endregion


#pragma region ZLIB

	static std::string WideCharToMultiByte(const TCHAR* WChar);
	static FString MultiByteToWideChar(const char* MultiByteChar);

	UFUNCTION(BlueprintCallable, Category = "Minizip")
	static void UnPackFile(FString PackFilePath, FString UnpackPath);

	static void PackFile(const FString& UnPackFilePath, const FString& PackPath);

#pragma endregion




};
