// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DecoParamFunctionLibrary.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FDecorateParam
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 id;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString name;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString description;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString expression;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString value;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 parent_selection_id;

public:
	FDecorateParam() : id(-1), name(TEXT("")), description(TEXT("")), expression(TEXT("")), value(TEXT("")), parent_selection_id(-1) {}
	FDecorateParam(const int32& InSelectionID, const int32& InStyleID) : id(-1), name(TEXT("")), description(TEXT("")), expression(TEXT("")), value(TEXT("")), parent_selection_id(InSelectionID) {}

	bool operator==(const FDecorateParam& InData)
	{
		return id == InData.id;
	}
	bool operator==(const FDecorateParam& InData) const 
	{
		return id == InData.id;
	}
};


UCLASS()
class DESIGNSTATION_API UDecoParamFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

};
