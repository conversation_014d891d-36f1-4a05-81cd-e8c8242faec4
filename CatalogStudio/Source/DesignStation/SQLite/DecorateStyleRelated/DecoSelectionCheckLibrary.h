// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DecoSelectionCheckLibrary.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FDecoSelectionCheckData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 id;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString selection_id;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString content_id;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString style_id;
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 is_prime;

public:
	FDecoSelectionCheckData() : id(0), selection_id(TEXT("-1")), content_id(TEXT("-1")), style_id(TEXT("-1")), is_prime(0) {}
	FDecoSelectionCheckData(const FDecoSelectionCheckData& InData)
	{
		id = InData.id;
		selection_id = InData.selection_id;
		content_id = InData.content_id;
		style_id = InData.style_id;
		is_prime = InData.is_prime;
	}

	void operator=(const FDecoSelectionCheckData& InData)
	{
		id = InData.id;
		selection_id = InData.selection_id;
		content_id = InData.content_id;
		style_id = InData.style_id;
		is_prime = InData.is_prime;
	}

	bool operator==(const FDecoSelectionCheckData& InData)
	{
		return id == InData.id;
	}
	bool operator==(const FDecoSelectionCheckData& InData) const
	{
		return id == InData.id;
	}
};

UCLASS()
class DESIGNSTATION_API UDecoSelectionCheckLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
};
