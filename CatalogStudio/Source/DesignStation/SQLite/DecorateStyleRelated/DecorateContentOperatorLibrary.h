// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DecorateContentOperatorLibrary.generated.h"

#define LOCTEXT_NAMESPACE "decorate style"

enum class EDecorateContentType : uint8
{
	EDoorMaterial = 0,//门板材质
	ESideMaterial,//柜体材质
	EGlassMaterial,//玻璃材质
	EMesaMaterial//台面材质
};

const TArray<FString>  DecorateContentName = 
{
	NSLOCTEXT(LOCTEXT_NAMESPACE, "DoorKey", "DoorMaterial").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "SideKey", "SideMaterial").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "GlassKey", "GlassMaterial").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "Mesa<PERSON><PERSON>", "MesaMaterial").ToString()
};

UENUM(BlueprintType)
enum class EDecorateContentBaseType : uint8
{
    E_Material					UMETA(DisplayName = "材质"),
	E_Model						UMETA(DisplayName = "样式")
};

const TArray<FText>  ContentBaseTypeLocText = 
{
	NSLOCTEXT(LOCTEXT_NAMESPACE, "E_Material", "材质"),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "E_Model", "样式")
};

USTRUCT(BlueprintType)
struct FDecorateContent
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString id;

	//UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//	int32 type;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString name;

	//UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//	int32 parent_id;

	//UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//	int32 selected;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 sort_order;


	//内容对应关联相关代码
	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString RelationCode;

	//内容类型
    UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		EDecorateContentBaseType BaseType;

public:
	FDecorateContent() 
		:id(TEXT(""))
		/*, type(0)*/
		, name(TEXT(""))
		//, parent_id(-1)
		//, selected(0)
		, sort_order(0) 
		, RelationCode(TEXT(""))
		, BaseType(EDecorateContentBaseType::E_Model)
	{}

	void operator=(const FDecorateContent& InData)
	{
		id = InData.id;
		//type = InData.type;
		name = InData.name;
		//parent_id = InData.parent_id;
		//selected = InData.selected;
		sort_order = InData.sort_order;
        RelationCode = InData.RelationCode;
        BaseType = InData.BaseType;
	}

	bool operator==(const FDecorateContent& InData)
	{
		return id == InData.id;
	}

	bool operator==(const FDecorateContent& InData) const 
	{
		return id == InData.id;
	}
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UDecorateContentOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
	
};

#undef LOCTEXT_NAMESPACE
