// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DecorateSelectionOperatorLibrary.generated.h"

USTRUCT(BlueprintType)
struct FDecorateSelection
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString code;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString description;

	/*UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 folder_id;*/

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString thumbnail_path;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 sort_order;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString visibility;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString visibility_exp;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString parent_id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int64 data_source;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString custom_id;

public:
	FDecorateSelection() 
		: id(TEXT(""))
		, code(TEXT(""))
		, description(TEXT(""))
		//, folder_id(-1)
		, thumbnail_path(TEXT(""))
		, sort_order(0)
		, visibility(TEXT(""))
		, visibility_exp(TEXT(""))
		, parent_id(TEXT(""))
		, data_source(INDEX_NONE)
		, custom_id(TEXT(""))
	{}

	void SetInValid() { id = TEXT(""); }

	bool IsValid() { return !id.IsEmpty(); }

	void operator=(const FDecorateSelection& InData)
	{
		id = InData.id;
		code = InData.code;
		description = InData.description;
		//folder_id = InData.folder_id;
		thumbnail_path = InData.thumbnail_path;
		sort_order = InData.sort_order;
		visibility = InData.visibility;
		visibility_exp = InData.visibility_exp;
		parent_id = InData.parent_id;
		data_source = InData.data_source;
		custom_id = InData.custom_id;
	}

	bool operator==(const FDecorateSelection& InData)
	{
		return id.Equals(InData.id, ESearchCase::IgnoreCase);
	}

	bool operator==(const FDecorateSelection& InData) const
	{
		return id.Equals(InData.id, ESearchCase::IgnoreCase);
	}

};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UDecorateSelectionOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	

};
