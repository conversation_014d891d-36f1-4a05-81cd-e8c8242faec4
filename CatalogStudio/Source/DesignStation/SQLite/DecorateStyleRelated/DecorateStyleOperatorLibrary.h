// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DecorateStyleOperatorLibrary.generated.h"

USTRUCT(BlueprintType)
struct FDecorateStyle
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 checked;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString code;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString craft;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString description;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString thumbnail_path;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		FString thumbnail_md5;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 sort_order;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
		int32 style_group;

public:
	FDecorateStyle() 
		: id(TEXT(""))
		, checked(1)
		, code(TEXT(""))
		, craft(TEXT(""))
		, description(TEXT(""))
		, thumbnail_path(TEXT(""))
		, thumbnail_md5(TEXT(""))
		, sort_order(0)
		, style_group(0)
	{}

	void operator=(const FDecorateStyle& InData)
	{
		id = InData.id;
		checked = InData.checked;
		code = InData.code;
		craft = InData.craft;
		description = InData.description;
		thumbnail_path = InData.thumbnail_path;
		thumbnail_md5 = InData.thumbnail_md5;
		sort_order = InData.sort_order;
		style_group = InData.style_group;
	}

	bool operator==(const FDecorateStyle& InData)
	{
		return id == InData.id;
	}

	bool operator==(const FDecorateStyle& InData) const
	{
		return id == InData.id;
	}
};

/**
 *
 */
UCLASS()
class DESIGNSTATION_API UDecorateStyleOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

};
