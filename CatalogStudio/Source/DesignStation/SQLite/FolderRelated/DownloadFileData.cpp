// Fill out your copyright notice in the Description page of Project Settings.

#include "DownloadFileData.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"

bool FDownloadFileData::operator==(const FDownloadFileData& InOther) const
{
	return path == InOther.path && md5 == InOther.md5 && size == InOther.size;
}

FString FDownloadFileDataLibrary::RetriveFileMD5(const FString& TableName, const FString& InPath)
{
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *InPath);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
	return (1 == Files.Num()) ? Files[0].md5 : TEXT("");
}

bool FDownloadFileDataLibrary::RetriveFileMD5(const FString& TableName, const TArray<FString>& InPath, TArray<FDownloadFileData>& MD5s)
{
	if (0 == InPath.Num()) return true;
	for (auto& Path : InPath)
	{
		TArray<FDownloadFileData> Files;
		const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *Path);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
		MD5s.Append(Files);
	}
	return true;
}

bool FDownloadFileDataLibrary::RetriveFileMD5(const FString& TableName, const FString& InPath, FDownloadFileData& MD5s)
{
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *InPath);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
	if (1 != Files.Num()) return false;
	MD5s = Files[0];
	return true;
}

bool FDownloadFileDataLibrary::RetriveFileMD5(const FString& TableName, const TArray<FString>& InPath, TArray<FString>& MD5s)
{
	if (0 == InPath.Num()) return true;
	MD5s.SetNum(InPath.Num());
	int32 Offset = 0;
	for (auto& Path : InPath)
	{
		TArray<FDownloadFileData> Files;
		const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *Path);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
		MD5s[Offset] = (1 == Files.Num()) ? Files[0].md5 : TEXT("");
		++Offset;
	}
	return true;
}

bool FDownloadFileDataLibrary::RetriveUneuqalFilesMD5(const FString& TableName, const TArray<TPair<FString, FString>>& InPath, TArray<FString>& MD5s)
{
	if (0 == InPath.Num()) return true;
	for (auto& Path : InPath)
	{
		TArray<FDownloadFileData> Files;
		const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *Path.Key);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
		const FString FileMD5 = (1 == Files.Num()) ? Files[0].md5 : TEXT("");
		if (FileMD5.Equals(Path.Value, ESearchCase::IgnoreCase))
		{
			MD5s.AddUnique(Path.Key);
		}
	}
	return true;
}

bool FDownloadFileDataLibrary::UpdateFileMD5(const FString& TableName, const FDownloadFileData& File)
{
	if (File.path.IsEmpty()) return false;
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *File.path);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SQL, Files);
	if (Files.Num() > 0)
	{
		const FString UpdateSQL = FString::Printf(TEXT("update %s set md5='%s', size='%d' where path='%s'"), *TableName, *File.md5, File.size, *File.path);
		FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	}
	else
	{
		const FString InsertSQL = FString::Printf(TEXT("insert into %s (path,md5,size) values('%s','%s','%d')"), *TableName, *File.path, *File.md5, File.size);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
	}
	return true;
}

bool FDownloadFileDataLibrary::UpdateFileMD5(const FString& TableName, const TArray<FDownloadFileData>& Files)
{
	for (auto& File : Files)
	{
		UpdateFileMD5(TableName, File);
	}
	return true;
}

FString FDownloadFileDataLibrary::RetriveServerFileMD5(const FString& TableName, const FString& InPath)
{
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *InPath);
	FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SQL, Files);
	return (1 == Files.Num()) ? Files[0].md5 : TEXT("");
}

bool FDownloadFileDataLibrary::RetriveServerFileMD5(const FString& TableName, const FString& InPath, FDownloadFileData& FileInfo)
{
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *InPath);
	FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SQL, Files);
	if (1 == Files.Num())
	{
		FileInfo = Files[0];
		return true;
	}
	return false;
}

bool FDownloadFileDataLibrary::UpdateServerFileMD5(const FString& TableName, const FDownloadFileData& File)
{
	if (File.path.IsEmpty()) return false;
	TArray<FDownloadFileData> Files;
	const FString SQL = FString::Printf(TEXT("select * from %s where path='%s'"), *TableName, *File.path);
	FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SQL, Files);
	if (Files.Num() > 0)
	{
		const FString UpdateSQL = FString::Printf(TEXT("update %s set md5='%s', size='%d' where path='%s'"), *TableName, *File.md5, File.size, *File.path);
		FLocalDatabaseOperatorLibrary::UpdateDataFromServerDataBaseBySQL(UpdateSQL);
	}
	else
	{
		const FString InsertSQL = FString::Printf(TEXT("insert into %s (path,md5,size) values('%s','%s','%d')"), *TableName, *File.path, *File.md5, File.size);
		FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertSQL);
	}
	return true;
}

bool FDownloadFileDataLibrary::UpdateServerFileMD5(const FString& TableName, const TArray<FDownloadFileData>& Files)
{
	for (auto& File : Files)
	{
		UpdateServerFileMD5(TableName, File);
	}
	return true;
}