// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DownloadFileData.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FDownloadFileData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
		FString path;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
		FString md5;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
		int64 size;
	bool operator == (const FDownloadFileData& InOther) const;
public:
	FDownloadFileData() :path(TEXT("")), md5(TEXT("")), size(0) {}

	FDownloadFileData(const FString& Path) :path(Path), md5(TEXT("")), size(0) {}

	FDownloadFileData(const FString& Path, const FString& MD5, const int64& Size) :path(Path), md5(MD5), size(Size) {}
};

class DESIGNSTATION_API FDownloadFileDataLibrary
{
public:

	static FString RetriveFileMD5(const FString& TableName, const FString& InPath);

	static bool RetriveFileMD5(const FString& TableName, const TArray<FString>& InPath, TArray<FString>& MD5s);

	static bool RetriveFileMD5(const FString& TableName, const FString& InPath, FDownloadFileData& MD5s);

	static bool RetriveFileMD5(const FString& TableName, const TArray<FString>& InPath, TArray<FDownloadFileData>& MD5s);

	static bool RetriveUneuqalFilesMD5(const FString& TableName, const TArray<TPair<FString, FString>>& InPath, TArray<FString>& MD5s);

	static bool UpdateFileMD5(const FString& TableName, const FDownloadFileData& File);

	static bool UpdateFileMD5(const FString& TableName, const TArray<FDownloadFileData>& Files);

	static FString RetriveServerFileMD5(const FString& TableName, const FString& InPath);

	static bool RetriveServerFileMD5(const FString& TableName, const FString& InPath, FDownloadFileData& FileInfo);

	static bool UpdateServerFileMD5(const FString& TableName, const FDownloadFileData& File);

	static bool UpdateServerFileMD5(const FString& TableName, const TArray<FDownloadFileData>& Files);
};
