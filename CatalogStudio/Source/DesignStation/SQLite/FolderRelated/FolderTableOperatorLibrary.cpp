// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderTableOperatorLibrary.h"
#include "SQLiteCore/Public/SQLiteDatabase.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include <time.h>

#include "DownloadFileData.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SQLite/MaterialRelated/CustomMaterialTableOperatorLibrary.h"
#include "DesignStation/SQLite/MultiComponentRelated/MultiComTableOperatorLibrary.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "DesignStation/SubSystem/LocalDatabaseSubsystem.h"
#include "DesignStation/UI/FolderLayoutUI/FolderAndFileBaseWidget.h"
#include "DesignStation/UI/MainUI/FolderAndFileListWidget.h"

//#ifdef WITH_EDITOR
//#pragma optimize("", off)
//#endif

DEFINE_LOG_CATEGORY(FolderFileLocalLibraryLog);

#define LOCTEXT_NAMESPACE "Folders"


//time macro
#define SHOW_TIME_LOG

clock_t s_Time, e_Time;
double duration;

#define GET_TIME(TimeArg) TimeArg = clock()
#define GET_TIME_WITH_DEFINE(TimeArg) clock_t TimeArg = clock()

#define START_TIME()	GET_TIME(s_Time)
#define END_TIME()		GET_TIME(e_Time)

#define PRINT_FUNCTION_TIME() \
duration = (double)(e_Time - s_Time) / CLOCKS_PER_SEC; \
UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("func time : %f \n"), duration);

#define PRINT_FUNCTION_TIME_WITH_NAME(FunName) \
duration = (double)(e_Time - s_Time) / CLOCKS_PER_SEC; \
UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("func[ %s ] time : %f \n"), *(FString(#FunName)), duration);

#define FUNCTION_TIME_COST(FunName, STime, ETime) \
double Curduration = (double)(ETime - STime) / CLOCKS_PER_SEC; \
UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("func[ %s ] time : %f \n"), *(FString(#FunName)), Curduration);

//multi thread
#define USER_MULTI_THREAD_COPY
#define USER_MULTI_THREAD_COPY_INNER

FCriticalSection Mutex;
#define MUTEX_SCOPE_LOCK FScopeLock ScopeLock(&Mutex)

#define CALL_THREAD(EventRef, InTaskDelegate, OtherTask, CallThreadName) \
EventRef = FSimpleDelegateGraphTask::CreateAndDispatchWhenReady(InTaskDelegate, TStatId(), OtherTask, CallThreadName)

#define CALL_THREAD_ARRAY(EventRef, InTaskDelegate, OtherTaskArray, CallThreadName) \
EventRef = FSimpleDelegateGraphTask::CreateAndDispatchWhenReady(InTaskDelegate, TStatId(), &OtherTaskArray, CallThreadName)

#define CALL_THREAD_LAMBDA(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD(EventRef, FSimpleDelegate::CreateLambda(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_STATIC(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD(EventRef, FSimpleDelegate::CreateStatic(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UFUNCTION(EventRef, OtherTask, CallThreadName, Object, Method, ...) \
CALL_THREAD(EventRef, FSimpleDelegate::CreateUFunction(Object, Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UFUNCTION_ARRAY(EventRef, OtherTaskArray, CallThreadName, Object, Method, ...) \
CALL_THREAD_ARRAY(EventRef, FSimpleDelegate::CreateUFunction(Object, Method,##__VA_ARGS__), OtherTaskArray, CallThreadName)

//#ifdef USER_MULTI_THREAD_COPY_INNER
TMap<FString, FGraphEventArray> GraphEventMap;
FGraphEventArray GraphEventArray;
//#else
FGraphEventRef GraphEventRef;
//#endif
FGraphEventRef GraphEventAfterRef;

//FFolderTableData::FFolderTableData(const FFolderTableDataForDB& InTableDataForDB)
//{
//	this->id = InTableDataForDB.id;
//	this->folder_id = InTableDataForDB.folder_id;
//	this->folder_name = InTableDataForDB.folder_name;
//	this->folder_code = InTableDataForDB.folder_code;
//	this->folder_code_exp = InTableDataForDB.folder_code_exp;
//	this->folder_type = static_cast<EFolderType>(InTableDataForDB.folder_type);
//	this->thumbnail_path = InTableDataForDB.thumbnail_path;
//	this->thumbnailMd5 = InTableDataForDB.thumbnailMd5;
//	this->parent_id = InTableDataForDB.parent_id;
//	this->visibility_exp = InTableDataForDB.visibility_exp;
//	this->visibility = InTableDataForDB.visibility;
//	this->deletable = 0 != InTableDataForDB.is_delete;
//	this->can_add_subfolder = 0 != InTableDataForDB.can_add_subfolder;
//	this->folder_order = InTableDataForDB.folder_order;
//	this->is_new = 0 != InTableDataForDB.is_new;
//	this->update_time = InTableDataForDB.update_time;
//}

FFolderTableData::FFolderTableData(const FFolderDataDB& InData)
{
	id = InData.id;
	folder_id = TEXT("");
	folder_name = InData.folder_name;
	folder_code_exp = TEXT("");
	folder_code = TEXT("");
	folder_type = static_cast<EFolderType>(InData.folder_type);
	thumbnail_path = TEXT("");
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	deletable = false;
	can_add_subfolder = true;
	folder_order = InData.folder_order;
	is_new = false;
	update_time = TEXT("");
}

FFolderTableData::FFolderTableData(const FFileDBData& InData)
{
	id = InData.id;
	folder_id = InData.folder_id;
	folder_name = InData.folder_name;
	folder_code_exp = InData.folder_code_exp;
	folder_code = InData.folder_code;
	folder_type = static_cast<EFolderType>(InData.folder_type);
	thumbnail_path = InData.thumbnail_path;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	deletable = true;
	can_add_subfolder = false;
	folder_order = InData.folder_order;
	is_new = InData.is_new > 0;
	update_time = TEXT("");
}

//bool FFolderTableData::ConvertToFolderTableDataForDBArray(const TArray<FFolderTableData>& InArray, TArray<FFolderTableDataForDB>& OutArray)
//{
//	OutArray.AddDefaulted(InArray.Num());
//	int32 i = 0;
//	for (auto& Iter : InArray)
//	{
//		OutArray[i] = Iter;
//		++i;
//	}
//	return true;
//}

bool FFolderTableData::GenerateThumbnailPath(FString& OutPath) const
{
	if (this->IsValid() && !can_add_subfolder)
	{
		OutPath = FString::Printf(TEXT("Thumbnails/%s.png"), *id);
		return true;
	}
	return false;
}

bool FFolderTableData::GenerateThumbnailPath(const FString& InFilePath, FString& OutPath) const
{
	if (this->IsValid() && !can_add_subfolder)
	{
		OutPath = FString::Printf(TEXT("Thumbnails/%s.%s"), *id, *FPaths::GetExtension(InFilePath));
		return true;
	}
	return false;
}

FString FFolderTableData::EnumToString(const EFolderType& InFolderType)
{
	switch (InFolderType)
	{
	case EFolderType::EMaterial: return TEXT("Material");
	case EFolderType::EMultiMeshs: return TEXT("MultiMeshs");
	case EFolderType::ESingleMesh: return TEXT("SingleMesh");
	case EFolderType::EMultiComponents: return TEXT("MultiComponents");
	case EFolderType::ESingleComponent: return TEXT("SingleComponent");
	default:return TEXT("");
	}
}

void FFolderTableData::CopyData(const FFolderDataDB& InData)
{
	id = InData.id;
	folder_id = TEXT("");
	folder_name = InData.folder_name;
	folder_code_exp = TEXT("");
	folder_code = TEXT("");
	folder_type = static_cast<EFolderType>(InData.folder_type);
	thumbnail_path = TEXT("");
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	deletable = false;
	can_add_subfolder = true;
	folder_order = InData.folder_order;
	is_new = false;
	update_time = TEXT(""); // temp -- TODO
}

void FFolderTableData::CopyData(const FFileDBData& InData)
{
	id = InData.id;
	folder_id = InData.folder_id;
	folder_name = InData.folder_name;
	folder_code_exp = InData.folder_code_exp;
	folder_code = InData.folder_code;
	folder_type = static_cast<EFolderType>(InData.folder_type);
	thumbnail_path = InData.thumbnail_path;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	deletable = false;
	can_add_subfolder = false;
	folder_order = InData.folder_order;
	is_new = InData.is_new > 0;
	update_time = TEXT(""); // temp -- TODO
}

void FFolderTableData::operator=(const FFolderTableData& InData)
{
	id = InData.id;
	folder_id = InData.folder_id;
	folder_name = InData.folder_name;
	folder_name_exp = InData.folder_name_exp;
	folder_code = InData.folder_code;
	folder_code_exp = InData.folder_code_exp;
	folder_type = InData.folder_type;
	thumbnail_path = InData.thumbnail_path;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	deletable = InData.deletable;
	can_add_subfolder = InData.can_add_subfolder;
	folder_order = InData.folder_order;
	is_new = InData.is_new;
	update_time = InData.update_time;
	backend_folder_path = InData.backend_folder_path;
	md5 = InData.md5;
	width = InData.width;
	height = InData.height;
	depth = InData.depth;
	boxOffset = InData.boxOffset;

	placeRule = InData.placeRule;
	description = InData.description;
}

//FFolderTableDataForDB::FFolderTableDataForDB(const FFolderTableData& InFolderData)
//{
//	this->id = InFolderData.id;
//	this->folder_id = InFolderData.folder_id;
//	this->folder_name = InFolderData.folder_name;
//	this->folder_code = InFolderData.folder_code;
//	this->folder_code_exp = InFolderData.folder_code_exp;
//	this->folder_type = static_cast<int32>(InFolderData.folder_type);
//	this->thumbnail_path = InFolderData.thumbnail_path;
//	this->parent_id = InFolderData.parent_id;
//	this->visibility_exp = InFolderData.visibility_exp;
//	this->visibility = InFolderData.visibility;
//	this->is_delete = InFolderData.deletable;
//	this->can_add_subfolder = InFolderData.can_add_subfolder;
//	this->folder_order = InFolderData.folder_order;
//	this->is_new = InFolderData.is_new;
//	this->update_time = InFolderData.update_time;
//}
//
//bool FFolderTableDataForDB::ConvertToFFolderTableDataArray(const TArray<FFolderTableDataForDB>& InArray, TArray<FFolderTableData>& OutArray)
//{
//	OutArray.AddDefaulted(InArray.Num());
//	int32 i = 0;
//	for (auto& Iter : InArray)
//	{
//		OutArray[i] = Iter;
//		++i;
//	}
//	return true;
//}

#undef LOCTEXT_NAMESPACE

/**
 *
 */
FFolderDataDB::FFolderDataDB(const FFolderDataDB& InData)
{
	id = InData.id;
	folder_name = InData.folder_name;
	folder_type = InData.folder_type;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	folder_order = InData.folder_order;
}

void FFolderDataDB::CopyData(const FFolderTableData& InData)
{
	id = InData.id;
	folder_name = InData.folder_name;
	folder_type = static_cast<int32>(InData.folder_type);
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	folder_order = InData.folder_order;
}

void FFolderDataDB::ConvertDBDataToProgramData(const TArray<FFolderDataDB>& InData, TArray<FFolderTableData>& OutData)
{
	OutData.Empty();
	for (auto& Iter : InData)
	{
		FFolderTableData& NewData = OutData.AddDefaulted_GetRef();
		NewData.CopyData(Iter);
	}
}

void FFolderDataDB::ConvertProgramDataToDBData(const TArray<FFolderTableData>& InData, TArray<FFolderDataDB>& OutData)
{
	OutData.Empty();
	for (auto& Iter : InData)
	{
		FFolderDataDB& NewData = OutData.AddDefaulted_GetRef();
		NewData.CopyData(Iter);
	}
}

FFileDBData::FFileDBData(const FFileDBData& InData)
{
	id = InData.id;
	folder_id = InData.folder_id;
	folder_name = InData.folder_name;
	folder_code = InData.folder_code;
	folder_code_exp = InData.folder_code_exp;
	folder_type = InData.folder_type;
	thumbnail_path = InData.thumbnail_path;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	//deletable = InData.deletable;
	//can_add_subfolder = InData.can_add_subfolder;
	folder_order = InData.folder_order;
	is_new = InData.is_new;
	//update_time = InData.update_time;
}

void FFileDBData::CopyData(const FFolderTableData& InData)
{
	id = InData.id;
	folder_id = InData.folder_id;
	folder_name = InData.folder_name;
	folder_code = InData.folder_code;
	folder_code_exp = InData.folder_code_exp;
	folder_type = static_cast<int32>(InData.folder_type);
	thumbnail_path = InData.thumbnail_path;
	parent_id = InData.parent_id;
	visibility_exp = InData.visibility_exp;
	visibility = InData.visibility;
	folder_order = InData.folder_order;
	is_new = InData.is_new;
}

void FFileDBData::ConvertDBDataToProgramData(const TArray<FFileDBData>& InData, TArray<FFolderTableData>& OutData)
{
	OutData.Empty();
	for (auto& Iter : InData)
	{
		FFolderTableData& NewData = OutData.AddDefaulted_GetRef();
		NewData.CopyData(Iter);
	}
}

void FFileDBData::ConvertProgramDataToDBData(const TArray<FFolderTableData>& InData, TArray<FFileDBData>& OutData)
{
	OutData.Empty();
	for (auto& Iter : InData)
	{
		FFileDBData& NewData = OutData.AddDefaulted_GetRef();
		NewData.CopyData(Iter);
	}
}

TArray<FString> FRecursePathData::GetFolderPathFromID(const TArray<FRecursePathData>& InPaths)
{
	TArray<FString> OutPath;
	for (int32 i = InPaths.Num() - 1; i >= 0; --i)
	{
		OutPath.Add(InPaths[i].id);
	}
	return OutPath;
}

#define SEARCH_FROM_FOLDER	FString(TEXT("select * from folder"))
#define SEARCH_FROM_FILE	FString(TEXT("select * from file"))

#define LINK_STR_ONE		FString(TEXT(" where "))
#define LINK_STR_TWO		FString(TEXT(" and "))

#define SEARCH_RULE_ASC		FString(TEXT(" order by folder_order ASC"))
#define SEARCH_RULE_DESC	FString(TEXT(" order by folder_order DESC"))

#define SINGLE_COMPONENT_DIR			FString(TEXT("SingleComponents"))
#define MULTI_COMPONENT_DIR				FString(TEXT("MultiComponents"))
#define FOLDER_SLASH					FString(TEXT("/"))
#define SINGLE_COMPONENT_FILE_NAME		FString(TEXT("comp.data"))
#define THUMBNAIL_FOLDER				FString(TEXT("Thumbnails"))

bool UFolderTableOperatorLibrary::FolderFileSearchByID(const FString& ID, const EFolderFileSearchType& SearchType, TArray<FFolderTableData>& OutProgramData, const ESearchRuleType& SearchRule /*= ESearchRuleType::E_ASC */)
{
	START_TIME();

	bool Res = FolderFileSearch(ID, TEXT(""), TEXT(""), SearchType, TEXT(""), TEXT(""), SearchRule, OutProgramData);

	END_TIME();
	PRINT_FUNCTION_TIME_WITH_NAME(FolderFileSearchByID);

	return Res;
}

bool UFolderTableOperatorLibrary::FolderFileSearchByParentID(const FString& ParentID, const EFolderFileSearchType& SearchType, TArray<FFolderTableData>& OutProgramData, const ESearchRuleType& SearchRule /*= ESearchRuleType::E_ASC */)
{
	START_TIME();

	bool Res = FolderFileSearch(TEXT(""), TEXT(""), ParentID, SearchType, TEXT(""), TEXT(""), SearchRule, OutProgramData);

	END_TIME();
	PRINT_FUNCTION_TIME_WITH_NAME(FolderFileSearchByParentID);

	return Res;
}

void UFolderTableOperatorLibrary::SpliteFolderFile(const TArray<FFolderTableData>& AllDatas, TArray<FFolderTableData>& OutFolderData, TArray<FFolderTableData>& OutFileData)
{
	OutFolderData.Empty();
	OutFileData.Empty();
	for (auto& Iter : AllDatas)
	{
		if (Iter.can_add_subfolder)
		{
			OutFolderData.Add(Iter);
		}
		else
		{
			OutFileData.Add(Iter);
		}
	}
}

bool UFolderTableOperatorLibrary::SelectFilsByIDOrNameOrCode(const FString& InStr, const FString& ParentID, TArray<FFolderTableData>& OutProgramData)
{
	FString SQL = SEARCH_FROM_FILE + LINK_STR_ONE;
	FString SearchStr = TEXT("%") + InStr + TEXT("%");
	FString ActionSQL = FString::Printf(TEXT("( folder_id like \'%s\' or folder_name like \'%s\' or folder_code like \'%s\' )"), *SearchStr, *SearchStr, *SearchStr);

	if (!ParentID.IsEmpty())
	{
		SQL += FString::Printf(TEXT("parent_id = \'%s\'"), *ParentID);
		SQL += LINK_STR_TWO;
		SQL += ActionSQL;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("search action[under folder : %s] --- SQL : %s"), *ParentID, *SQL);
		TArray<FFileDBData> CurFolderMatchInfo;
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL, CurFolderMatchInfo);
		if (CurFolderMatchInfo.Num() > 0)
		{
			TArray<FFolderTableData> CurMatchData;
			FFileDBData::ConvertDBDataToProgramData(CurFolderMatchInfo, CurMatchData);
			OutProgramData.Append(CurMatchData);
		}

		TArray<FFolderTableData> SubFolders;
		UFolderTableOperatorLibrary::FolderFileSearchByParentID(ParentID, EFolderFileSearchType::EFolderOnly, SubFolders);
		if (SubFolders.Num() > 0)
		{
			for (auto& SubIter : SubFolders)
			{
				TArray<FFolderTableData> SuBMatchFile;
				UFolderTableOperatorLibrary::SelectFilsByIDOrNameOrCode(InStr, SubIter.id, SuBMatchFile);
				if (SuBMatchFile.Num() > 0)
				{
					OutProgramData.Append(SuBMatchFile);
				}
			}
		}
		return true;
	}
	else
	{//没有选中文件或文件夹，全局搜索
		SQL += ActionSQL;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("search action[all DB] --- SQL : %s"), *SQL);
		TArray<FFileDBData> DBInfo;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL, DBInfo);
		if (DBInfo.Num() > 0)
		{
			FFileDBData::ConvertDBDataToProgramData(DBInfo, OutProgramData);
		}
		return true;
	}

	return false;
}

bool UFolderTableOperatorLibrary::SelectFolderFilePath(const FString& ID, bool IsFolder, TArray<FString>& OutParams)
{
	FString Table = IsFolder ? FString(TEXT("folder")) : FString(TEXT("file"));
	FString SQL = FString::Printf(TEXT("WITH RECURSIVE FP(id, parent_id, upid, uppid) AS (select s.id, s.parent_id, s.id upid, s.parent_id uppid from %s s where s.id = \'%s\' UNION ALL select f.id, f.parent_id, FP.upid, FP.uppid from folder f, FP where FP.parent_id = f.id) select * from FP"), *Table, *ID);
	//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
	TArray<FRecursePathData> FolderPaths;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FRecursePathData>(SQL, FolderPaths);
	OutParams = FRecursePathData::GetFolderPathFromID(FolderPaths);
	return Res;
}

bool UFolderTableOperatorLibrary::GetFolderLevel(const FString& ID, int32& OutLevel)
{
	TArray<FString> FolderPaths;
	if (UFolderTableOperatorLibrary::SelectFolderFilePath(ID, true, FolderPaths))
	{
		OutLevel = FolderPaths.Num() - 1;
		return true;
	}
	return false;
}

bool UFolderTableOperatorLibrary::DeleteFolderFile(const FString& ID, bool IsFolder)
{
	FString SQL = FString(TEXT(""));
	TArray<FParameterData> Parameters;
	if (IsFolder)
	{
		FLocalDatabaseParameterLibrary::RetriveFolderParametersByID(ID, Parameters);
		SQL = FString::Printf(TEXT("delete from folder where id = \'%s\'"), *ID);
		FString	 SelectFolderSql = FString::Printf(TEXT("select * from folder where parent_id = \'%s\'"), *ID);
		FString	 SelectFileSql = FString::Printf(TEXT("select * from file where parent_id = \'%s\'"), *ID);

		TArray<FFileDBData> Files;
		TArray<FFolderDataDB> Folders;

		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SelectFileSql, Files);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SelectFolderSql, Folders);

		if (Files.Num() > 0)
		{
			for (auto& iter : Files)
			{
				DeleteFolderFile(iter.id, false);
			}
		}
		if (Folders.Num() > 0)
		{
			for (auto& iter : Folders)
			{
				DeleteFolderFile(iter.id, true);
			}
		}

	}
	else
	{
		FLocalDatabaseParameterLibrary::RetriveFileParametersByID(ID, Parameters);
		SQL = FString::Printf(TEXT("delete from file where id = \'%s\'"), *ID);
	}
	if (Parameters.Num() > 0)
	{
		for (auto Parametersiter : Parameters)
		{
			FLocalDatabaseParameterLibrary::DeleteFolderFileParameter(Parametersiter.Data.id, IsFolder);
		}
	}
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(SQL);
}

bool UFolderTableOperatorLibrary::DeleteServerFolderFile(const FString& ID, bool IsFolder)
{
	FString SQL = FString(TEXT(""));
	if (IsFolder)
	{
		SQL = FString::Printf(TEXT("delete from folder where id = \'%s\'"), *ID);
	}
	else
	{
		SQL = FString::Printf(TEXT("delete from file where id = \'%s\'"), *ID);
	}
	if (!SQL.IsEmpty())
	{
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("PRAGMA foreign_keys = ON;"));
		return FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(SQL);
	}
	return false;
}

bool UFolderTableOperatorLibrary::CreateFolderFileData(FFolderTableData& NewData, bool NeedGenerateUUID /*= true*/)
{
	int32 MaxOrder = INDEX_NONE;
	if (UFolderTableOperatorLibrary::GetFolderFileMaxOrder(NewData.parent_id, EFolderFileSearchType::EFolderAndFile, MaxOrder))
	{
		if (NeedGenerateUUID)
			NewData.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
		NewData.folder_order = (++MaxOrder);
		FString SQL = TEXT("");
		if (NewData.can_add_subfolder)
		{
			FFolderDataDB DBFolder;
			DBFolder.CopyData(NewData);
			SQL = FString::Printf(TEXT("insert into folder values (\'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d)"),
				*DBFolder.id, *DBFolder.folder_name, DBFolder.folder_type, *DBFolder.parent_id,
				*DBFolder.visibility_exp, DBFolder.visibility, DBFolder.folder_order);
		}
		else
		{
			FFileDBData DBFile;
			DBFile.CopyData(NewData);
			SQL = FString::Printf(TEXT("insert into file values (\'%s\', \'%s\', \'%s\', \'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d, %d, \'%s\')"),
				*DBFile.id, *DBFile.folder_id, *DBFile.folder_name, *DBFile.folder_code_exp, *NewData.folder_code,
				DBFile.folder_type, *DBFile.thumbnail_path, *DBFile.visibility_exp, DBFile.visibility,
				DBFile.is_new, DBFile.folder_order, *DBFile.parent_id);

			if (!DBFile.thumbnail_path.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + DBFile.thumbnail_path);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into file_image values('%s','%s',%d)")
					, *DBFile.thumbnail_path, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
		if (SQL.IsEmpty())
			return false;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
		return FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
	}

	return false;
}

bool UFolderTableOperatorLibrary::GetFolderFileMaxOrder(const FString& ParentID, const EFolderFileSearchType& SearchType, int32& OutMaxOrder)
{
	if (SearchType == EFolderFileSearchType::EFolderOnly)
	{// no use
		FString SQL = TEXT("");
		SQL = TEXT("select * from folder where ( folder_order = (select max(folder_order) from folder))");
		TArray<FFolderDataDB> OutDB;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SQL, OutDB);
		if (Res && OutDB.Num() > 0)
		{
			OutMaxOrder = OutDB[0].folder_order;
		}
		else
		{
			OutMaxOrder = 0;
		}
		return true;
	}
	else if (SearchType == EFolderFileSearchType::EFileOnly)
	{// no use
		FString SQL = TEXT("");
		SQL = TEXT("select * from file where ( folder_order = (select max(folder_order) from file))");
		TArray<FFileDBData> OutDB;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL, OutDB);
		if (Res && OutDB.Num() > 0)
		{
			OutMaxOrder = OutDB[0].folder_order;
		}
		else
		{
			OutMaxOrder = 0;
		}
		return true;
	}
	else if (SearchType == EFolderFileSearchType::EFolderAndFile)
	{
		FString SQL1 = FString::Printf(TEXT("select * from folder where (folder_order = (select max(folder_order) from folder where parent_id = \'%s\') and parent_id = \'%s\')")
			, *ParentID, *ParentID);
		FString SQL2 = FString::Printf(TEXT("select * from file where (folder_order = (select max(folder_order) from file where parent_id = \'%s\') and parent_id = \'%s\')")
			, *ParentID, *ParentID);
		TArray<FFolderDataDB> OutDB1;
		bool Res1 = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SQL1, OutDB1);
		TArray<FFileDBData> OutDB2;
		bool Res2 = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL2, OutDB2);
		if (OutDB1.Num() > 0 && OutDB2.Num() > 0)
		{
			OutMaxOrder = OutDB1[0].folder_order >= OutDB2[0].folder_order ? OutDB1[0].folder_order : OutDB2[0].folder_order;
		}
		else if (OutDB1.Num() > 0)
		{
			OutMaxOrder = OutDB1[0].folder_order;
		}
		else if (OutDB2.Num() > 0)
		{
			OutMaxOrder = OutDB2[0].folder_order;
		}
		else
		{
			OutMaxOrder = 0;
		}
		return true;
	}
	else
	{
		checkNoEntry();
	}

	return false;
}

bool UFolderTableOperatorLibrary::GetFolderFileParameters(const FString& ID, bool IsFolder, TArray<FParameterData>& OutParameters)
{
	if (IsFolder)
		return FLocalDatabaseParameterLibrary::RetriveFolderParametersByID(ID, OutParameters);
	else
		return FLocalDatabaseParameterLibrary::RetriveFileParametersByID(ID, OutParameters);
}

bool UFolderTableOperatorLibrary::SwapFolderFileOrder(const FString& ID1, int32 NewOrder1, const FString& ID2, int32 NewOrder2, bool IsFolder)
{
	FString SQL1 = TEXT("");
	FString SQL2 = TEXT("");
	if (IsFolder)
	{
		SQL1 = FString::Printf(TEXT("update folder set folder_order = %d where id = \'%s\'"), NewOrder1, *ID1);
		SQL2 = FString::Printf(TEXT("update folder set folder_order = %d where id = \'%s\'"), NewOrder2, *ID2);
	}
	else
	{
		SQL1 = FString::Printf(TEXT("update file set folder_order = %d where id = \'%s\'"), NewOrder1, *ID1);
		SQL2 = FString::Printf(TEXT("update file set folder_order = %d where id = \'%s\'"), NewOrder2, *ID2);
	}
	//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL1 : %s"), *SQL1);
	//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL2 : %s"), *SQL2);
	return (FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL1) && FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL2));

}

bool UFolderTableOperatorLibrary::UpdateFolderFileOrder(const FString& ID, int32 NewOrder, bool IsFolder)
{
	FString SQL = TEXT("");
	if (IsFolder)
	{
		SQL = FString::Printf(TEXT("update folder set folder_order = %d where id = \'%s\'"), NewOrder, *ID);
	}
	else
	{
		SQL = FString::Printf(TEXT("update file set folder_order = %d where id = \'%s\'"), NewOrder, *ID);
	}
	//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL1 : %s"), *SQL1);
	//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL2 : %s"), *SQL2);
	return (FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL));
}


bool UFolderTableOperatorLibrary::FolderFileCopyLogic(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData)
{
#ifdef USER_MULTI_THREAD_COPY

#ifdef USER_MULTI_THREAD_COPY_INNER
	FString NewDataID = UFolderTableOperatorLibrary::GenerateUUIDLower();
	UFolderTableOperatorLibrary::FolderFileCopyMultiThreadOperator(ActionWidget, SourceID, TargetFolder, NewDataID);
	return false;
#else
	FString NewDataID = UFolderTableOperatorLibrary::GenerateUUIDLower();
	UFolderTableOperatorLibrary::FolderFileCopyMultiThread(ActionWidget, SourceID, TargetFolder, NewDataID);
	return false;
#endif

#else

	return UFolderTableOperatorLibrary::FolderFileCopy(SourceID, TargetFolder, NewData);

#endif
}

bool UFolderTableOperatorLibrary::FolderFileCopyLogic(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FString& TargetID, FFolderTableData& NewData)
{
	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(TargetID, EFolderFileSearchType::EFolderAndFile, DBDatas);
	if (DBDatas.Num() > 0)
	{
		return UFolderTableOperatorLibrary::FolderFileCopyLogic(ActionWidget, SourceID, DBDatas[0], NewData);
	}

	return false;
}

bool UFolderTableOperatorLibrary::FolderFileCopyLogic(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData)
{
#ifdef USER_MULTI_THREAD_COPY

#ifdef USER_MULTI_THREAD_COPY_INNER
	FString NewDataID = UFolderTableOperatorLibrary::GenerateUUIDLower();
	UFolderTableOperatorLibrary::FolderFileCopyMultiThreadOperator(ActionWidget, SourceID, TargetFolder, NewDataID);
	return false;

#else
	FString NewDataID = UFolderTableOperatorLibrary::GenerateUUIDLower();
	UFolderTableOperatorLibrary::FolderFileCopyMultiThread(ActionWidget, SourceID, TargetFolder, NewDataID);
	return false;
#endif

#else

	return UFolderTableOperatorLibrary::FolderFileCopy(SourceID, TargetFolder, NewData);

#endif
}

bool UFolderTableOperatorLibrary::FolderFileCopy(const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData)
{
	GET_TIME_WITH_DEFINE(CopyS);

	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, DBDatas);
	if (DBDatas.Num() > 0)
	{
		FFolderTableData SourceData = DBDatas[0];
		UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderFileCopy ---  [source id : %s, name : %s] [target id : %s, name : %s]")
			, *SourceData.id, *SourceData.folder_name, *TargetFolder.id, *TargetFolder.folder_name);
		if (SourceData.can_add_subfolder)
		{
			TPair<FString, TArray<FFolderTableData>> OriginFolderContent;
			UFolderTableOperatorLibrary::IsTargetSubFolder(SourceID, TargetFolder.id, OriginFolderContent);
			bool Res = FolderCopyRecurseInnerLogic(SourceData, TargetFolder, OriginFolderContent, NewData);

			GET_TIME_WITH_DEFINE(CopyE);
			FUNCTION_TIME_COST(FolderFileCopy, CopyS, CopyE);

			return Res;
		}
		else
		{
			bool Res = FileCopyInnerLogic(SourceData, TargetFolder, NewData);

			GET_TIME_WITH_DEFINE(CopyE);
			FUNCTION_TIME_COST(FolderFileCopy, CopyS, CopyE);

			return Res;
		}
	}

	return false;
}

bool UFolderTableOperatorLibrary::FolderFileCopy(const FString& SourceID, const FString& TargetFolderID, FFolderTableData& NewData)
{
	GET_TIME_WITH_DEFINE(CopyS);

	TArray<FFolderTableData> SourceDBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, SourceDBDatas);
	TArray<FFolderTableData> TargetDBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(TargetFolderID, EFolderFileSearchType::EFolderAndFile, TargetDBDatas);
	if (SourceDBDatas.Num() > 0 && TargetDBDatas.Num() > 0)
	{
		FFolderTableData SourceData = SourceDBDatas[0];
		FFolderTableData TargetData = TargetDBDatas[0];
		UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderFileCopy ---  [source id : %s, name : %s] [target id : %s, name : %s]")
			, *SourceData.id, *SourceData.folder_name, *TargetData.id, *TargetData.folder_name);
		if (SourceData.can_add_subfolder)
		{
			TPair<FString, TArray<FFolderTableData>> OriginFolderContent;
			UFolderTableOperatorLibrary::IsTargetSubFolder(SourceID, TargetFolderID, OriginFolderContent);
			bool Res = FolderCopyRecurseInnerLogic(SourceData, TargetData, OriginFolderContent, NewData);

			GET_TIME_WITH_DEFINE(CopyE);
			FUNCTION_TIME_COST(FolderFileCopy, CopyS, CopyE);

			return Res;
		}
		else
		{
			bool Res = FileCopyInnerLogic(SourceData, TargetData, NewData);

			GET_TIME_WITH_DEFINE(CopyE);
			FUNCTION_TIME_COST(FolderFileCopy, CopyS, CopyE);

			return Res;
		}
	}

	return false;
}

bool UFolderTableOperatorLibrary::FolderFileCut(const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData)
{
	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, DBDatas);
	if (DBDatas.Num() > 0)
	{
		FFolderTableData SourceData = DBDatas[0];
		NewData = SourceData;
		NewData.parent_id = TargetFolder.id;
		TArray<FFolderTableData> Mates;

		FString SQL = TEXT("");
		if (SourceData.can_add_subfolder)
		{
			UFolderTableOperatorLibrary::FolderFileSearchByParentID(TargetFolder.id, EFolderFileSearchType::EFolderOnly, Mates);
			int32 MaxOrder = -1;
			for (const auto& iter : Mates)
			{
				if (iter.folder_order > MaxOrder)
				{
					MaxOrder = iter.folder_order;
				}
			}
			NewData.folder_order = MaxOrder + 1;
			SQL = FString::Printf(TEXT("update folder set parent_id = \'%s\' , folder_order =  \'%d\' where id = \'%s\'"), *NewData.parent_id, NewData.folder_order, *NewData.id);
		}
		else
		{
			UFolderTableOperatorLibrary::FolderFileSearchByParentID(TargetFolder.id, EFolderFileSearchType::EFileOnly, Mates);
			int32 MaxOrder = -1;
			for (const auto& iter : Mates)
			{
				if (iter.folder_order > MaxOrder)
				{
					MaxOrder = iter.folder_order;
				}
			}
			NewData.folder_order = MaxOrder + 1;
			SQL = FString::Printf(TEXT("update file set parent_id = \'%s\', folder_order =  \'%d\' where id = \'%s\'"), *NewData.parent_id, NewData.folder_order, *NewData.id);
		}
		if (SQL.IsEmpty())
			return false;
		UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
		return FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL);
	}
	return false;
}

bool UFolderTableOperatorLibrary::FolderFileCut(const FString& SourceID, const FString& TargetFolderID, FFolderTableData& NewData)
{
	TArray<FFolderTableData> SourceDBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, SourceDBDatas);
	TArray<FFolderTableData> TargetDBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(TargetFolderID, EFolderFileSearchType::EFolderAndFile, TargetDBDatas);
	if (SourceDBDatas.Num() > 0 && TargetDBDatas.Num() > 0)
	{
		FFolderTableData SourceData = SourceDBDatas[0];
		FFolderTableData TargetFolder = TargetDBDatas[0];
		NewData = SourceData;
		NewData.parent_id = TargetFolder.id;
		FString SQL = TEXT("");
		if (SourceData.can_add_subfolder)
		{
			SQL = FString::Printf(TEXT("update folder set parent_id = \'%s\' where id = \'%s\'"), *NewData.parent_id, *NewData.id);
		}
		else
		{
			SQL = FString::Printf(TEXT("update file set parent_id = \'%s\' where id = \'%s\'"), *NewData.parent_id, *NewData.id);
		}
		if (SQL.IsEmpty())
			return false;
		UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
		return FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL);
	}
	return false;
}

void UFolderTableOperatorLibrary::FolderFileCopyMultiThread(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID)
{
	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("User Multi Thread To Copy[Folder File Widget], New Data Id : %s"), *NewDataID);
	if (!IS_OBJECT_PTR_VALID(ActionWidget))
		return;

	MUTEX_SCOPE_LOCK;

	CALL_THREAD_STATIC(
		GraphEventRef,
		NULL,
		ENamedThreads::AnyThread,
		&UFolderTableOperatorLibrary::FolderFileCopyMultiThreadBind,
		SourceID, TargetFolder, NewDataID, false);

	CALL_THREAD_UFUNCTION(
		GraphEventAfterRef,
		GraphEventRef,
		ENamedThreads::AnyThread,
		ActionWidget,
		FName(TEXT("FolderFileCopyFinishAction")),
		NewDataID
	);

}

void UFolderTableOperatorLibrary::FolderFileCopyMultiThread(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID)
{
	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("User Multi Thread To Copy[List Widget], New Data Id : %s"), *NewDataID);
	if (!IS_OBJECT_PTR_VALID(ActionWidget))
		return;

	CALL_THREAD_STATIC(
		GraphEventRef,
		NULL,
		ENamedThreads::AnyThread,
		&UFolderTableOperatorLibrary::FolderFileCopyMultiThreadBind,
		SourceID, TargetFolder, NewDataID, false
	);

	CALL_THREAD_UFUNCTION(
		GraphEventAfterRef,
		GraphEventRef,
		ENamedThreads::AnyThread,
		ActionWidget,
		FName(TEXT("FolderFileCopyFinishAction")),
		NewDataID
	);
}

void UFolderTableOperatorLibrary::FolderFileCopyMultiThreadOperator(UUserWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID)
{
	if (!IS_OBJECT_PTR_VALID(ActionWidget))
		return;
	bool IsFolderFile = IS_OBJECT_PTR_VALID(Cast<UFolderAndFileBaseWidget>(ActionWidget));
	bool IsList = IS_OBJECT_PTR_VALID(Cast<UFolderAndFileListWidget>(ActionWidget));
	if (!IsFolderFile && !IsList)
		return;

	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("User Multi Thread Operator To Copy, New Data Id : %s"), *NewDataID);
	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, DBDatas);
	if (DBDatas.Num() > 0)
	{
		FFolderTableData SourceData = DBDatas[0];

		TArray<FFolderTableData> FolderDatas;
		TArray<FParameterData> FolderParams;
		TArray<FFolderTableData> FileDatas;
		TArray<FParameterData> FileParams;
		TMap<FString, FString> FileThumbnailMap;
		TArray<FSingleComponentTableData> SingleComponentDatas;
		TMap<FString, FString> SingleComponentFileMap;
		TMap<TPair<FString, int32>, FMultiComponentDataItem> MultiComponentDatas;
		TArray<FParameterData> MultiComponentParams;
		TArray<FCustomMaterialTableData> MatDatas;
		UFolderTableOperatorLibrary::RecurseDataInFolder(SourceData, TargetFolder, NewDataID,
			FolderDatas, FolderParams,
			FileDatas, FileParams,
			FileThumbnailMap,
			SingleComponentDatas, SingleComponentFileMap,
			MultiComponentDatas, MultiComponentParams,
			MatDatas,
			true
		);

		GraphEventArray.Empty();

		FGraphEventRef CopyFolderEvent;
		if (FolderDatas.Num() > 0)
		{
			CALL_THREAD_STATIC(
				CopyFolderEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchFolderThread,
				FolderDatas
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyFolderEvent);
			}
		}
		if (FolderParams.Num() > 0)
		{
			FGraphEventRef CopyFolderParamsEvent;
			CALL_THREAD_STATIC(
				CopyFolderParamsEvent,
				CopyFolderEvent,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchFolderParamsThread,
				FolderParams
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyFolderParamsEvent);
			}
		}

		FGraphEventRef CopyFileEvent;
		if (FileDatas.Num() > 0)
		{
			CALL_THREAD_STATIC(
				CopyFileEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchFileThread,
				FileDatas
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyFileEvent);
			}
		}
		if (FileParams.Num() > 0)
		{
			FGraphEventRef CopyFileParamsEvent;
			CALL_THREAD_STATIC(
				CopyFileParamsEvent,
				CopyFileEvent,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchFileParamsThread,
				FileParams
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyFileParamsEvent);
			}
		}

		if (FileThumbnailMap.Num() > 0)
		{
			FGraphEventRef CopyThumbnailEvent;
			CALL_THREAD_STATIC(
				CopyThumbnailEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchThumbnailCopyThread,
				FileThumbnailMap
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyThumbnailEvent);
			}
		}
		if (SingleComponentDatas.Num() > 0)
		{
			FGraphEventRef CopySingleCompsEvent;
			CALL_THREAD_STATIC(
				CopySingleCompsEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchSingleComponentInfoThread,
				SingleComponentDatas
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopySingleCompsEvent);
			}
		}
		if (SingleComponentFileMap.Num() > 0)
		{
			FGraphEventRef CopySingleCompsFileEvent;
			CALL_THREAD_STATIC(
				CopySingleCompsFileEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchSingleComponentCopyThread,
				SingleComponentFileMap
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopySingleCompsFileEvent);
			}
		}
		if (MatDatas.Num() > 0)
		{
			FGraphEventRef CopyMaterialEvent;
			CALL_THREAD_STATIC(
				CopyMaterialEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchMaterialDataThread,
				MatDatas
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyMaterialEvent);
			}
		}
		FGraphEventRef CopyMultiCompsEvent;
		if (MultiComponentDatas.Num() > 0)
		{
			CALL_THREAD_STATIC(
				CopyMultiCompsEvent,
				NULL,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchMultiComponentInfoThread,
				MultiComponentDatas
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyMultiCompsEvent);
			}
		}
		if (MultiComponentParams.Num() > 0)
		{
			FGraphEventRef CopyMultiCompsParamsEvent;
			CALL_THREAD_STATIC(
				CopyMultiCompsParamsEvent,
				CopyMultiCompsEvent,
				ENamedThreads::AnyThread,
				&UFolderTableOperatorLibrary::BatchMultiComponentParamsThread,
				MultiComponentParams
			);
			{
				MUTEX_SCOPE_LOCK;
				GraphEventArray.Add(CopyMultiCompsParamsEvent);
			}
		}

		if (Cast<UFolderAndFileBaseWidget>(ActionWidget))
		{
			CALL_THREAD_UFUNCTION(
				GraphEventAfterRef,
				&GraphEventArray,
				ENamedThreads::GameThread,
				Cast<UFolderAndFileBaseWidget>(ActionWidget),
				FName(TEXT("FolderFileCopyFinishAction")),
				NewDataID
			);
		}
		else if (Cast<UFolderAndFileListWidget>(ActionWidget))
		{
			CALL_THREAD_UFUNCTION(
				GraphEventAfterRef,
				&GraphEventArray,
				ENamedThreads::GameThread,
				Cast<UFolderAndFileListWidget>(ActionWidget),
				FName(TEXT("FolderFileCopyFinishAction")),
				NewDataID
			);
		}
		else
		{
			UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("User Multi Thread Operator To Copy, No Correct Widget"));
		}
	}
}

//void UFolderTableOperatorLibrary::FolderFileCopyMultiThreadOperator(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID)
//{
//
//}

void UFolderTableOperatorLibrary::FolderCopyMultiThreadOperator(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const FString& NewDataID /*= TEXT("")*/, bool NeedGenerateID /*= true*/)
{
	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByParentID(SourceData.id, EFolderFileSearchType::EFolderAndFile, DBDatas);
	TArray<FFolderTableData> DBFolderDatas, DBFileDatas;
	UFolderTableOperatorLibrary::SpliteFolderFile(DBDatas, DBFolderDatas, DBFileDatas);

	FGraphEventArray CurEventArray;
	{
		MUTEX_SCOPE_LOCK;
		GraphEventMap.Add(SourceData.id, CurEventArray);
	}

	FFolderTableData NewData = SourceData;
	NewData.parent_id = TargetFolder.id;
	if (NeedGenerateID)
		NewData.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
	else
		NewDataID.IsEmpty() ? UFolderTableOperatorLibrary::GenerateUUIDLower() : NewData.id = NewDataID;

	for (auto& FolderIter : DBFolderDatas)
	{//folder

		UFolderTableOperatorLibrary::FolderCopyMultiThreadOperator(FolderIter, NewData);

		auto LambdaFunc = [](const FString& SourceID, const FString& SourceName)->void
		{
			UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderMultiInnerCopyComplete---[source id : %s, name : %s]"), *SourceID, *SourceName);
		};

		FGraphEventRef FolderCopyEventRef;
		CALL_THREAD_LAMBDA(
			FolderCopyEventRef,
			GraphEventMap.Contains(FolderIter.id) ? &(GraphEventMap[FolderIter.id]) : NULL,
			ENamedThreads::AnyThread,
			LambdaFunc,
			FolderIter.id, FolderIter.folder_name
		);
		{
			MUTEX_SCOPE_LOCK;
			GraphEventMap[SourceData.id].Add(FolderCopyEventRef);
		}
	}

	TMap<FFolderTableData, FFolderTableData> OldNewMap;
	TMap<FString, FString> ThumbnailMap;
	for (auto& FileIter : DBFileDatas)
	{//file
		FFolderTableData NewFileData;
		NewFileData.CopyDataWithoutID(FileIter, true);
		NewFileData.parent_id = NewData.id;
		NewFileData.folder_id = FString(TEXT(""));

		if (!FileIter.thumbnail_path.IsEmpty())
		{
			FString Extension = FPaths::GetExtension(SourceData.thumbnail_path, true);
			NewFileData.thumbnail_path = SINGLE_COMPONENT_DIR + FOLDER_SLASH + NewFileData.id + FOLDER_SLASH + THUMBNAIL_FOLDER + FOLDER_SLASH + NewFileData.id + Extension;
			ThumbnailMap.Add(FileIter.thumbnail_path, NewFileData.thumbnail_path);
		}
		else
			NewFileData.thumbnail_path = TEXT("");

		OldNewMap.Add(FileIter, NewFileData);
	}

	FGraphEventRef FolderSubFileEventRef;
	CALL_THREAD_STATIC(
		FolderSubFileEventRef,
		NULL,
		ENamedThreads::AnyThread,
		&UFolderTableOperatorLibrary::MultiCreateFileDBData,
		OldNewMap
	);
	{
		MUTEX_SCOPE_LOCK;
		GraphEventMap[SourceData.id].Add(FolderSubFileEventRef);
	}

	if (ThumbnailMap.Num() > 0)
	{
		FGraphEventRef CopyThumbnailEventRef;
		CALL_THREAD_STATIC(
			CopyThumbnailEventRef,
			NULL,
			ENamedThreads::AnyThread,
			&UFolderTableOperatorLibrary::CopyFileAction,
			ThumbnailMap
		);
		{
			MUTEX_SCOPE_LOCK;
			GraphEventMap[SourceData.id].Add(CopyThumbnailEventRef);
		}
	}

	if (SourceData.folder_type == EFolderType::ESingleComponent)
	{
		FGraphEventRef CopySingleComponentEventRef;
		CALL_THREAD_STATIC(
			CopySingleComponentEventRef,
			NULL,
			ENamedThreads::AnyThread,
			&UFolderTableOperatorLibrary::CopySingleComponentsAction,
			OldNewMap
		);
		{
			MUTEX_SCOPE_LOCK;
			GraphEventMap[SourceData.id].Add(CopySingleComponentEventRef);
		}
	}
	else if (SourceData.folder_type == EFolderType::EMultiComponents)
	{

	}

	FGraphEventRef FolderSelfEventRef;
	CALL_THREAD_STATIC(
		FolderSelfEventRef,
		NULL,
		ENamedThreads::AnyThread,
		&UFolderTableOperatorLibrary::CreateFolderSelfDBData,
		SourceData, NewData
	);
	{
		MUTEX_SCOPE_LOCK;
		GraphEventMap[SourceData.id].Add(FolderSelfEventRef);
	}
}

void UFolderTableOperatorLibrary::MultiCreateFileDBData(TMap<FFolderTableData, FFolderTableData> OldNewFileMap)
{
	FString SQL = FString(TEXT("insert into file values "));
	FString ParamSQL = FString(TEXT("insert into file_param (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values "));
	FString EnumSQL = FString(TEXT("insert into file_param_enum (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "));
	bool NeedParamSQLModefy = false;
	bool NeedEnumSQLModefy = false;
	for (auto& Iter : OldNewFileMap)
	{
		FFolderTableData DBFile = Iter.Value;
		FString ValueSQL = FString::Printf(TEXT("(\'%s\', \'%s\', \'%s\', \'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d, %d, \'%s\'),"),
			*DBFile.id, *DBFile.folder_id, *DBFile.folder_name, *DBFile.folder_code_exp, *DBFile.folder_code,
			DBFile.folder_type, *DBFile.thumbnail_path, *DBFile.visibility_exp, DBFile.visibility,
			DBFile.is_new, DBFile.folder_order, *DBFile.parent_id);
		SQL += ValueSQL;

		FFolderTableData DBSourceFile = Iter.Key;
		TArray<FParameterData> SourceParameters;
		UFolderTableOperatorLibrary::GetFolderFileParameters(DBSourceFile.id, DBSourceFile.can_add_subfolder, SourceParameters);
		for (auto& ParamData : SourceParameters)
		{
			NeedParamSQLModefy = true;
			ParamData.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
			ParamData.Data.main_id = DBFile.id;
			const FString ParamValueSQL = FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
				, *ParamData.Data.id, *ParamData.Data.name, *ParamData.Data.description, ParamData.Data.classific_id, *ParamData.Data.value
				, *ParamData.Data.expression, *ParamData.Data.max_value, *ParamData.Data.max_expression, *ParamData.Data.min_value
				, *ParamData.Data.min_expression, *ParamData.Data.visibility, *ParamData.Data.visibility_exp, *ParamData.Data.editable
				, *ParamData.Data.editable_exp, ParamData.Data.is_enum, *ParamData.Data.param_id, *ParamData.Data.main_id);
			ParamSQL += ParamValueSQL;
			for (auto& EnumDataiter : ParamData.EnumData)
			{
				NeedEnumSQLModefy = true;
				EnumDataiter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
				EnumDataiter.main_id = ParamData.Data.id;
				EnumSQL = EnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),")
					, *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display
					, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);

				if (!EnumDataiter.image_for_display.IsEmpty())
				{
					const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
					FString LocalMD5 = TEXT("");
					int64 FileSize = 0;
					ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

					FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
						, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
					FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
				}
			}
		}
	}
	if (OldNewFileMap.Num() > 0)
	{
		SQL = SQL.Mid(0, SQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
	}
	if (NeedParamSQLModefy)
	{
		ParamSQL = ParamSQL.Mid(0, ParamSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(ParamSQL);
	}
	if (NeedEnumSQLModefy)
	{
		EnumSQL = EnumSQL.Mid(0, EnumSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(EnumSQL);
	}
}

void UFolderTableOperatorLibrary::MultiCreateFolderFileParamDBData(TArray<FParameterData> NewParams, const FString& NewFolderFileID, bool IsFolder)
{
	const FString Table = IsFolder ? FString(TEXT("folder_param")) : FString(TEXT("file_param"));
	const FString EnumTable = IsFolder ? FString(TEXT("folder_param_enum")) : FString(TEXT("file_param_enum"));
	FString ParamSQL = FString::Printf(TEXT("insert into %s (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values "), *Table);
	FString EnumSQL = FString::Printf(TEXT("insert into %s (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "), *EnumTable);
	bool NeedParamSQLModefy = false;
	bool NeedEnumSQLModefy = false;
	for (auto& ParamData : NewParams)
	{
		NeedParamSQLModefy = true;
		ParamData.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
		ParamData.Data.main_id = NewFolderFileID;
		const FString ParamValueSQL = FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
			, *ParamData.Data.id, *ParamData.Data.name, *ParamData.Data.description, ParamData.Data.classific_id, *ParamData.Data.value
			, *ParamData.Data.expression, *ParamData.Data.max_value, *ParamData.Data.max_expression, *ParamData.Data.min_value
			, *ParamData.Data.min_expression, *ParamData.Data.visibility, *ParamData.Data.visibility_exp, *ParamData.Data.editable
			, *ParamData.Data.editable_exp, ParamData.Data.is_enum, *ParamData.Data.param_id, *ParamData.Data.main_id);
		ParamSQL += ParamValueSQL;
		for (auto& EnumDataiter : ParamData.EnumData)
		{
			NeedEnumSQLModefy = true;
			EnumDataiter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
			EnumDataiter.main_id = ParamData.Data.id;
			EnumSQL = EnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),")
				, *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display
				, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);

			if (!EnumDataiter.image_for_display.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
					, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
	}

	if (NeedParamSQLModefy)
	{
		ParamSQL = ParamSQL.Mid(0, ParamSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(ParamSQL);
	}
	if (NeedEnumSQLModefy)
	{
		EnumSQL = EnumSQL.Mid(0, EnumSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(EnumSQL);
	}
}

void UFolderTableOperatorLibrary::CreateFolderSelfDBData(FFolderTableData SourceData, FFolderTableData NewData)
{
	int32 MaxOrder = INDEX_NONE;
	if (UFolderTableOperatorLibrary::GetFolderFileMaxOrder(NewData.parent_id, EFolderFileSearchType::EFolderAndFile, MaxOrder))
	{
		NewData.folder_order = (++MaxOrder);
		FString SQL = TEXT("");
		if (NewData.can_add_subfolder)
		{
			FFolderDataDB DBFolder;
			DBFolder.CopyData(NewData);
			SQL = FString::Printf(TEXT("insert into folder values (\'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d)"),
				*DBFolder.id, *DBFolder.folder_name, DBFolder.folder_type, *DBFolder.parent_id,
				*DBFolder.visibility_exp, DBFolder.visibility, DBFolder.folder_order);
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);

			TArray<FParameterData> SourceParameters;
			UFolderTableOperatorLibrary::GetFolderFileParameters(SourceData.id, SourceData.can_add_subfolder, SourceParameters);
			if (SourceParameters.Num() > 0)
			{
				UFolderTableOperatorLibrary::MultiCreateFolderFileParamDBData(SourceParameters, NewData.id, true);
			}
		}
	}
}

void UFolderTableOperatorLibrary::CopyFileAction(TMap<FString, FString> OldNewMap)
{
	for (auto& PathIter : OldNewMap)
	{
		FString SourcePath = FPaths::ProjectContentDir() + PathIter.Key;
		FString TargetPath = FPaths::ProjectContentDir() + PathIter.Value;
		ECopyFileErrorCode CopyRes = FCatalogFunctionLibrary::CopyFileTo(FPaths::ConvertRelativePathToFull(SourcePath), FPaths::ConvertRelativePathToFull(TargetPath));
		if (CopyRes == ECopyFileErrorCode::ECanNotDeleteDestinationFile || CopyRes == ECopyFileErrorCode::EUnkonwnError /*|| CopyRes == ECopyFileErrorCode::ESourceFileNotExits*/)
		{
			UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy file thumbnail error[copy file]--- Res : %d"), static_cast<int32>(CopyRes));
		}
		else
		{
			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + PathIter.Value);
			FDownloadFileData FileInfo(PathIter.Value);
			ACatalogPlayerController::GetFileMD5AndSize(FilePath, FileInfo.md5, FileInfo.size);
			FString SQL = TEXT("");
			if (PathIter.Value.Contains(TEXT(".png")))
			{
				SQL = FString::Printf(TEXT("insert into file_image values (\'%s\', \'%s\', %d)"), *PathIter.Value, *FileInfo.md5, FileInfo.size);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
			}
			else if (PathIter.Value.Contains(TEXT(".data")))
			{
				SQL = FString::Printf(TEXT("insert into other_file values (\'%s\', \'%s\', %d)"),
					*PathIter.Value, *FileInfo.md5, FileInfo.size);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
			}
		}
	}
}

void UFolderTableOperatorLibrary::CopySingleComponentsAction(TMap<FFolderTableData, FFolderTableData> OldNewFileMap)
{
	TArray<FSingleComponentTableData> NewSingleDatas;
	TMap<FString, FString> SinglePathMap;
	for (auto& Data : OldNewFileMap)
	{
		FFolderTableData SourceData = Data.Key;
		FFolderTableData NewData = Data.Value;
		FSingleComponentTableData SingleData;
		FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(SourceData.id, SingleData);
		FSingleComponentTableData NewSingleData = SingleData;
		NewSingleData.folder_id = NewData.id;
		NewSingleData.data_path = SINGLE_COMPONENT_DIR + FOLDER_SLASH + NewData.id + FOLDER_SLASH + SINGLE_COMPONENT_FILE_NAME;

		NewSingleDatas.Add(NewSingleData);
		if (!SingleData.data_path.IsEmpty())
		{
			SinglePathMap.Add(SingleData.data_path, NewSingleData.data_path);
		}
	}

	if (NewSingleDatas.Num() > 0)
	{
		UFolderTableOperatorLibrary::MultiCreateSingleComponentsDBData(NewSingleDatas);
	}
	if (SinglePathMap.Num() > 0)
	{
		UFolderTableOperatorLibrary::CopyFileAction(SinglePathMap);
	}
}

void UFolderTableOperatorLibrary::GetSingleComponentsData(TMap<FFolderTableData, FFolderTableData> OldNewFileMap, TArray<FSingleComponentTableData>& OutNewSingleDatas, TMap<FString, FString>& OutSinglePathMap)
{
	OutNewSingleDatas.Empty();
	OutSinglePathMap.Empty();
	for (auto& Data : OldNewFileMap)
	{
		FFolderTableData SourceData = Data.Key;
		FFolderTableData NewData = Data.Value;
		FSingleComponentTableData SingleData;
		FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(SourceData.id, SingleData);
		FSingleComponentTableData NewSingleData = SingleData;
		NewSingleData.folder_id = NewData.id;
		NewSingleData.data_path = SINGLE_COMPONENT_DIR + FOLDER_SLASH + NewData.id + FOLDER_SLASH + SINGLE_COMPONENT_FILE_NAME;

		OutNewSingleDatas.Add(NewSingleData);
		if (!SingleData.data_path.IsEmpty())
		{
			OutSinglePathMap.Add(SingleData.data_path, NewSingleData.data_path);
		}
	}
}

void UFolderTableOperatorLibrary::MultiCreateSingleComponentsDBData(const TArray<FSingleComponentTableData>& NewDatas)
{
	if (NewDatas.Num() <= 0) return;
	FString SQL = FString(TEXT("insert into single_comp (DATA_PATH,DEPEND_FILES,FOLDER_ID) values "));
	for (auto& SingleIter : NewDatas)
	{
		FString ValueSQL = FString::Printf(TEXT("('%s','%s', '%s'),")
			, *SingleIter.data_path, *SingleIter.depend_files, *SingleIter.folder_id);
		SQL += ValueSQL;
	}
	SQL = SQL.Mid(0, SQL.Len() - 1);
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
}

void UFolderTableOperatorLibrary::FolderFileCopyMultiThreadBind(FString SourceID, FFolderTableData TargetFolder, FString NewDataID, bool NeedGenerateID)
{
	TArray<FFolderTableData> DBDatas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(SourceID, EFolderFileSearchType::EFolderAndFile, DBDatas);
	if (DBDatas.Num() > 0)
	{
		FFolderTableData SourceData = DBDatas[0];
		FFolderTableData NewData = SourceData;
		NewData.id = NewDataID;
		UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderFileCopyMulti ---  [source id : %s, name : %s] [target id : %s, name : %s]")
			, *SourceData.id, *SourceData.folder_name, *TargetFolder.id, *TargetFolder.folder_name);
		if (SourceData.can_add_subfolder)
		{
			TPair<FString, TArray<FFolderTableData>> OriginFolderContent;
			UFolderTableOperatorLibrary::IsTargetSubFolder(SourceID, TargetFolder.id, OriginFolderContent);
			FolderCopyRecurseInnerLogic(SourceData, TargetFolder, OriginFolderContent, NewData, NeedGenerateID);

		}
		else
		{
			FileCopyInnerLogic(SourceData, TargetFolder, NewData, NeedGenerateID);
		}
	}
}

void UFolderTableOperatorLibrary::FolderFileCopyMultiThreadBindWithSourceData(FFolderTableData SourceData, FFolderTableData TargetFolder, FString NewDataID, bool NeedGenerateID)
{
	FFolderTableData NewData = SourceData;
	NewData.id = NewDataID;
	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderFileCopyMulti ---  [source id : %s, name : %s] [target id : %s, name : %s]")
		, *SourceData.id, *SourceData.folder_name, *TargetFolder.id, *TargetFolder.folder_name);
	if (SourceData.can_add_subfolder)
	{
		TPair<FString, TArray<FFolderTableData>> OriginFolderContent;
		UFolderTableOperatorLibrary::IsTargetSubFolder(SourceData.id, TargetFolder.id, OriginFolderContent);
		FolderCopyRecurseInnerLogic(SourceData, TargetFolder, OriginFolderContent, NewData, NeedGenerateID);

	}
	else
	{
		FileCopyInnerLogic(SourceData, TargetFolder, NewData, NeedGenerateID);
	}
}

bool UFolderTableOperatorLibrary::UpdateFolderFile(const FFolderTableData& NewData)
{
	FString SQL = TEXT("");
	if (NewData.can_add_subfolder)
	{
		SQL = FString::Printf(TEXT("update folder set FOLDER_NAME = \'%s\', VISIBILITY_EXP = \'%s\', VISIBILITY = %f where id = \'%s\'")
			, *NewData.folder_name, *NewData.visibility_exp, NewData.visibility, *NewData.id);
	}
	else
	{
		SQL = FString::Printf(TEXT("update file set FOLDER_ID = \'%s\', FOLDER_NAME = \'%s\', FOLDER_CODE_EXP = \'%s\', FOLDER_CODE = \'%s\', VISIBILITY_EXP = \'%s\', VISIBILITY = %f, IS_NEW = %d where id = \'%s\'")
			, *NewData.folder_id, *NewData.folder_name, *NewData.folder_code_exp, *NewData.folder_code, *NewData.visibility_exp, NewData.visibility, NewData.is_new ? 1 : 0, *NewData.id);
	}
	if (SQL.IsEmpty())
		return false;
	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
	return FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(SQL);
}

bool UFolderTableOperatorLibrary::DeleteFolderFileParameter(const FString& ParamID, bool IsFolder)
{
	return FLocalDatabaseParameterLibrary::DeleteFolderFileParameter(ParamID, IsFolder);
}

bool UFolderTableOperatorLibrary::UpdateFolderFileParameter(const FParameterData& ParamData, bool IsFolder)
{
	return FLocalDatabaseParameterLibrary::UpdateFolderFileParameter(ParamData, IsFolder);
}

bool UFolderTableOperatorLibrary::UpdateFolderFileParameter(const TArray<FParameterData>& ParamData, bool IsFolder)
{
	bool Res = true;
	for (auto& ParamIter : ParamData)
	{
		Res = UFolderTableOperatorLibrary::UpdateFolderFileParameter(ParamIter, IsFolder) && Res;
		if (!Res)
			break;
	}
	return Res;
}

bool UFolderTableOperatorLibrary::CreateFolderFileParameter(FParameterData& ParamData, bool IsFolder)
{
	ParamData.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
	for (auto& EnumIter : ParamData.EnumData)
	{
		EnumIter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
		EnumIter.main_id = ParamData.Data.id;
	}
	return FLocalDatabaseParameterLibrary::InsertFolderFileParameters(ParamData, IsFolder);
}

bool UFolderTableOperatorLibrary::RetriveFileByFolderId(const FString& FolderID, FFolderTableData& File)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from file where FOLDER_ID='%s'"), *FolderID);
	TArray<FFileDBData> DBInfo;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(RetriveSQL, DBInfo);
	if (false == Res || 1 != DBInfo.Num()) return false;
	File.CopyData(DBInfo[0]);
	return true;
}

bool UFolderTableOperatorLibrary::IsFolderIdUnique(const FString& FolderID)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from file where FOLDER_ID='%s'"), *FolderID);
	TArray<FFolderTableData> Files;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(RetriveSQL, Files);
	return Files.Num() <= 0;
}

bool UFolderTableOperatorLibrary::UpdateFile(const FFolderTableData& File)
{
	FString UpdateSQL = FString::Printf(TEXT("update file set FOLDER_ID='%s',FOLDER_NAME='%s',FOLDER_CODE_EXP='%s',FOLDER_CODE='%s',FOLDER_TYPE=%d,THUMBNAIL_PATH='%s',VISIBILITY_EXP='%s',VISIBILITY=%f,IS_NEW=%d,FOLDER_ORDER=%d where ID='%s'"), *File.folder_id, *File.folder_name, *File.folder_code_exp, *File.folder_code, File.folder_type, *File.thumbnail_path, *File.visibility_exp, File.visibility, File.is_new, File.folder_order, *File.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);

	if (!File.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + File.thumbnail_path);
		FDownloadFileData FileInfo(File.thumbnail_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, FileInfo.md5, FileInfo.size);
		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("file_image"), FileInfo);
	}

	return Res;
}

FString UFolderTableOperatorLibrary::GenerateUUIDLower()
{
	return (FGuid::NewGuid().ToString()).ToLower();
}

void UFolderTableOperatorLibrary::GenerateUUIDForParameter(FParameterData& EditParam)
{
	EditParam.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
	for (auto& EnumIter : EditParam.EnumData)
	{
		EnumIter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
		EnumIter.main_id = EditParam.Data.id;
	}
}

bool UFolderTableOperatorLibrary::FolderFileSearch(const FString& ID, const FString& FolderID, const FString& ParentID, const EFolderFileSearchType& SearchType, const FString& FolderName, const FString& FolderCode, const ESearchRuleType& SearchRule, TArray<FFolderTableData>& OutProgramData)
{
	FString ActionSQL = TEXT("");
	if (!ID.IsEmpty())
	{
		ActionSQL = LINK_STR_ONE + FString::Printf(TEXT("id = \'%s\'"), *ID);
	}
	if (!FolderID.IsEmpty())
	{
		if (SearchType == EFolderFileSearchType::EFileOnly || SearchType == EFolderFileSearchType::EFolderAndFile)
		{
			FString LinkStr = ActionSQL.IsEmpty() ? (LINK_STR_ONE) : (ActionSQL + LINK_STR_TWO);
			ActionSQL = LinkStr + FString::Printf(TEXT("folder_id = \'%s\'"), *FolderID);
		}
	}
	if (!ParentID.IsEmpty())
	{
		FString LinkStr = ActionSQL.IsEmpty() ? (LINK_STR_ONE) : (ActionSQL + LINK_STR_TWO);
		ActionSQL = LinkStr + FString::Printf(TEXT("parent_id = \'%s\'"), *ParentID);
	}
	if (!FolderName.IsEmpty())
	{
		FString LinkStr = ActionSQL.IsEmpty() ? (LINK_STR_ONE) : (ActionSQL + LINK_STR_TWO);
		ActionSQL = LinkStr + FString::Printf(TEXT("folder_name = \'%s\'"), *FolderName);
	}
	if (!FolderCode.IsEmpty())
	{
		if (SearchType == EFolderFileSearchType::EFileOnly || SearchType == EFolderFileSearchType::EFolderAndFile)
		{
			FString LinkStr = ActionSQL.IsEmpty() ? (LINK_STR_ONE) : (ActionSQL + LINK_STR_TWO);
			ActionSQL = LinkStr + FString::Printf(TEXT("folder_code = \'%s\'"), *FolderCode);
		}
	}

	if (ActionSQL.IsEmpty())
		return false;

	FString OrderStr = SearchRule == ESearchRuleType::E_None ? TEXT("") :
		SearchRule == ESearchRuleType::E_ASC ? SEARCH_RULE_ASC :
		SearchRule == ESearchRuleType::E_DESC ? SEARCH_RULE_DESC : TEXT("");

	if (SearchType == EFolderFileSearchType::EFolderOnly)
	{
		FString SQL = SEARCH_FROM_FOLDER + ActionSQL + OrderStr;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
		TArray<FFolderDataDB> DBInfo;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SQL, DBInfo);
		if (DBInfo.Num() > 0)
		{
			FFolderDataDB::ConvertDBDataToProgramData(DBInfo, OutProgramData);
			return true;
		}
		return false;
	}
	else if (SearchType == EFolderFileSearchType::EFileOnly)
	{
		FString SQL = SEARCH_FROM_FILE + ActionSQL + OrderStr;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL : %s"), *SQL);
		TArray<FFileDBData> DBInfo;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL, DBInfo);
		if (DBInfo.Num() > 0)
		{
			FFileDBData::ConvertDBDataToProgramData(DBInfo, OutProgramData);
			return true;
		}
		return false;
	}
	else if (SearchType == EFolderFileSearchType::EFolderAndFile)
	{
		FString SQL1 = SEARCH_FROM_FOLDER + ActionSQL + OrderStr;
		FString SQL2 = SEARCH_FROM_FILE + ActionSQL + OrderStr;
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL1 : %s"), *SQL1);
		//UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("SQL2 : %s"), *SQL2);
		TArray<FFolderDataDB> DBInfo1;
		TArray<FFileDBData> DBInfo2;
		bool Res1 = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SQL1, DBInfo1);
		bool Res2 = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SQL2, DBInfo2);
		if (DBInfo1.Num() > 0 || DBInfo2.Num() > 0)
		{
			OutProgramData.Empty();
			if (DBInfo1.Num() > 0)
			{
				TArray<FFolderTableData> Temp;
				FFolderDataDB::ConvertDBDataToProgramData(DBInfo1, Temp);
				OutProgramData.Append(Temp);
			}
			if (DBInfo2.Num() > 0)
			{
				TArray<FFolderTableData> Temp;
				FFileDBData::ConvertDBDataToProgramData(DBInfo2, Temp);
				OutProgramData.Append(Temp);
			}
			return true;
		}
		return false;
	}
	else
	{
		checkNoEntry();
		return false;
	}

	return false;
}

bool UFolderTableOperatorLibrary::FolderCopyRecurseInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const TPair<FString, TArray<FFolderTableData>>& OriginTargetContent, FFolderTableData& NewData, bool NeedGenerateID/* = true*/)
{
#ifndef SHOW_TIME_LOG
	GET_TIME_WITH_DEFINE(CopyStart);
#endif

	//将获取文件和文件夹提至前面，防止一个文件夹的复制拷贝形成环，从而死循环
	//用原始文件夹数据代替新查询的,防止父类向子类文件夹拷贝导致递归环死循环
	TArray<FFolderTableData> DBContentDatas;
	if (!SourceData.id.Equals(OriginTargetContent.Key, ESearchCase::IgnoreCase))
		UFolderTableOperatorLibrary::FolderFileSearchByParentID(SourceData.id, EFolderFileSearchType::EFolderAndFile, DBContentDatas);
	else
		DBContentDatas = OriginTargetContent.Value;

	UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("FolderCopyRecurseInnerLogic ---  [source id : %s, name : %s] [target id : %s, name : %s]")
		, *SourceData.id, *SourceData.folder_name, *TargetFolder.id, *TargetFolder.folder_name);

	/*UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("copy folder, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
		*(SourceData.id), *(SourceData.folder_name), *(TargetFolder.id), *(TargetFolder.folder_name));*/

	if (!UFolderTableOperatorLibrary::FolderCopyInnerLogic(SourceData, TargetFolder, NewData, NeedGenerateID))
	{
		UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy folder error, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
			*(SourceData.id), *(SourceData.folder_name), *(TargetFolder.id), *(TargetFolder.folder_name));
		return false;
	}
	/*UE_LOG(FolderFileLocalLibraryLog, Warning, TEXT("copy create new, [NewData id : %s, NewData name : %s, NewData parent_id : %s]"),
		*(NewData.id), *(NewData.folder_name), *(NewData.parent_id));*/

	if (DBContentDatas.Num() > 0)
	{
		for (auto& SourceIter : DBContentDatas)
		{
			if (SourceIter.can_add_subfolder)
			{
				/*UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy sub folder, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
					*(SourceIter.id), *(SourceIter.folder_name), *(NewData.id), *(NewData.folder_name));*/
				FFolderTableData NewSubData;
				bool Res = UFolderTableOperatorLibrary::FolderCopyRecurseInnerLogic(SourceIter, NewData, OriginTargetContent, NewSubData, true);
				if (!Res)
				{
					UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy sub folder error, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
						*(SourceIter.id), *(SourceIter.folder_name), *(NewData.id), *(NewData.folder_name));
					return false;
				}
			}
			else
			{
				/*UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy sub file, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
					*(SourceIter.id), *(SourceIter.folder_name), *(NewData.id), *(NewData.folder_name));*/
				FFolderTableData NewSubData;
				bool Res = UFolderTableOperatorLibrary::FileCopyInnerLogic(SourceIter, NewData, NewSubData, true);
				if (!Res)
				{
					UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy sub file error, [source id : %s, name : %s] --- [target id : %s, name : %s]"),
						*(SourceIter.id), *(SourceIter.folder_name), *(NewData.id), *(NewData.folder_name));
					return false;
				}
			}
		}
	}

#ifndef SHOW_TIME_LOG
	GET_TIME_WITH_DEFINE(CopyEnd);
	FUNCTION_TIME_COST(FolderCopyRecurseInnerLogic, CopyStart, CopyEnd);
#endif

	return true;
}

bool UFolderTableOperatorLibrary::FolderCopyInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, FFolderTableData& NewData, bool NeedGenerateID /*= true*/)
{
#ifndef SHOW_TIME_LOG
	GET_TIME_WITH_DEFINE(CopyStart);
#endif

	/*NewData = SourceData;
	if(NeedGenerateID)
		NewData.id = UFolderTableOperatorLibrary::GenerateUUIDLower();*/
	NewData.CopyDataWithoutID(SourceData, NeedGenerateID);
	NewData.parent_id = TargetFolder.id;

	if (UFolderTableOperatorLibrary::CreateFolderFileData(NewData, false))
	{
		TArray<FParameterData> FileParameters;
		UFolderTableOperatorLibrary::GetFolderFileParameters(SourceData.id, true, FileParameters);
		if (FileParameters.Num() > 0)
		{
			for (auto& Iter : FileParameters)
			{
				Iter.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
				Iter.Data.main_id = NewData.id;
				if (Iter.Data.is_enum > 0)
				{
					for (auto& EnumIter : Iter.EnumData)
					{
						EnumIter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
						EnumIter.main_id = Iter.Data.id;
					}
				}
				if (!FLocalDatabaseParameterLibrary::InsertFolderFileParameters(Iter, true))
				{
					UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("insert file parameter error[copy file] : name : %s, description : %s"), *Iter.Data.name, *Iter.Data.description);
				}
			}
		}

#ifndef SHOW_TIME_LOG
		GET_TIME_WITH_DEFINE(CopyEnd);
		FUNCTION_TIME_COST(FolderCopyInnerLogic, CopyStart, CopyEnd);
#endif

		return true;
	}

	return false;
}

bool UFolderTableOperatorLibrary::FileCopyInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, FFolderTableData& NewData, bool NeedGenerateID /*= true*/)
{
#ifndef SHOW_TIME_LOG
	GET_TIME_WITH_DEFINE(CopyStart);
#endif

	/*NewData = SourceData;
	if(NeedGenerateID)
		NewData.id = UFolderTableOperatorLibrary::GenerateUUIDLower();*/
	NewData.CopyDataWithoutID(SourceData, NeedGenerateID);
	NewData.parent_id = TargetFolder.id;

	NewData.folder_id = FString(TEXT(""));

	FString SourcePath = FPaths::ProjectContentDir() + SourceData.thumbnail_path;
	FString TargetPath = TEXT("");
	bool NeedCopyThumbnail = false;
	if (!SourceData.thumbnail_path.IsEmpty())
	{
		NeedCopyThumbnail = true;
		FString Extension = FPaths::GetExtension(SourceData.thumbnail_path, true);
		NewData.thumbnail_path = SINGLE_COMPONENT_DIR + FOLDER_SLASH + NewData.id + FOLDER_SLASH + THUMBNAIL_FOLDER + FOLDER_SLASH + NewData.id + Extension;
		TargetPath = FPaths::ProjectContentDir() + NewData.thumbnail_path;
	}


	if (UFolderTableOperatorLibrary::CreateFolderFileData(NewData, false))
	{
		if (NeedCopyThumbnail)
		{
			ECopyFileErrorCode CopyRes = FCatalogFunctionLibrary::CopyFileTo(FPaths::ConvertRelativePathToFull(SourcePath), FPaths::ConvertRelativePathToFull(TargetPath));
			if (CopyRes == ECopyFileErrorCode::ECanNotDeleteDestinationFile || CopyRes == ECopyFileErrorCode::EUnkonwnError /*|| CopyRes == ECopyFileErrorCode::ESourceFileNotExits*/)
			{
				UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy file thumbnail error[copy file]--- Res : %d"), static_cast<int32>(CopyRes));
			}
		}

		if (NewData.folder_type == EFolderType::ESingleComponent)
		{//需插入新的到单部件表和拷贝原始文件
			FSingleComponentTableData SingleData;
			FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(SourceData.id, SingleData);
			FSingleComponentTableData NewSingleData = SingleData;
			NewSingleData.folder_id = NewData.id;
			NewSingleData.data_path = SINGLE_COMPONENT_DIR + FOLDER_SLASH + NewData.id + FOLDER_SLASH + SINGLE_COMPONENT_FILE_NAME;
			if (!FSingleComponentOperatorLibrary::CreateSingleComponent(NewSingleData))
			{
				UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("create single_comp error[copy file]"));
				return false;
			}

			ECopyFileErrorCode CopyRes = FCatalogFunctionLibrary::CopyFileTo(
				FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleData.data_path),
				FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + NewSingleData.data_path)
			);
			if (CopyRes == ECopyFileErrorCode::ECanNotDeleteDestinationFile || CopyRes == ECopyFileErrorCode::EUnkonwnError /*|| CopyRes == ECopyFileErrorCode::ESourceFileNotExits*/)
			{
				UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("copy file single_comp file error[copy file] --- Res : %d"), static_cast<int32>(CopyRes));
			}
		}
		else if (NewData.folder_type == EFolderType::EMultiComponents)
		{//多部件需复制引用逻辑和引用参数
			TArray<FMultiComponentDataItem> OriginMultiComponents;
			FMultiComTableOperatorLibrary::RetriveFileMultiComponents(SourceData.id, OriginMultiComponents);
			for (int32 i = 0; i < OriginMultiComponents.Num(); i++)
			{
				FString OldID = OriginMultiComponents[i].KeyID;
				OriginMultiComponents[i].KeyID = UFolderTableOperatorLibrary::GenerateUUIDLower();
				if (FMultiComTableOperatorLibrary::InsertMultiComponentItem(OriginMultiComponents[i], NewData.id, i))
				{
					TArray<FParameterData> OldParameters;
					FLocalDatabaseParameterLibrary::RetriveMultiParametersByID(OldID, OldParameters);
					for (auto& Param : OldParameters)
					{
						UFolderTableOperatorLibrary::GenerateUUIDForParameter(Param);
						FLocalDatabaseParameterLibrary::InsertMultiParameters(Param, OriginMultiComponents[i].KeyID);
					}
				}
			}
		}

		TArray<FParameterData> FileParameters;
		UFolderTableOperatorLibrary::GetFolderFileParameters(SourceData.id, false, FileParameters);
		if (FileParameters.Num() > 0)
		{
			for (auto& Iter : FileParameters)
			{
				Iter.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
				Iter.Data.main_id = NewData.id;
				if (Iter.Data.is_enum > 0)
				{
					for (auto& EnumIter : Iter.EnumData)
					{
						EnumIter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
						EnumIter.main_id = Iter.Data.id;
					}
				}
				if (!FLocalDatabaseParameterLibrary::InsertFolderFileParameters(Iter, false))
				{
					UE_LOG(FolderFileLocalLibraryLog, Error, TEXT("insert file parameter error[copy file] : name : %s, description : %s"), *Iter.Data.name, *Iter.Data.description);
					return false;
				}
			}
		}

#ifndef SHOW_TIME_LOG
		GET_TIME_WITH_DEFINE(CopyEnd);
		FUNCTION_TIME_COST(FileCopyInnerLogic, CopyStart, CopyEnd);
#endif

		return true;
	}

	return false;
}

bool UFolderTableOperatorLibrary::IsTargetSubFolder(const FString& SourceID, const FString& TargetID, TPair<FString, TArray<FFolderTableData>>& OriginContent)
{
	bool Res = false;
	if (SourceID.Equals(TargetID, ESearchCase::IgnoreCase))
		return Res;
	TArray<FString> TargetPath;
	UFolderTableOperatorLibrary::SelectFolderFilePath(TargetID, true, TargetPath);
	int32 Index = TargetPath.IndexOfByPredicate(
		[&SourceID](const FString& PathIter) ->bool { return SourceID.Equals(PathIter, ESearchCase::IgnoreCase); }
	);
	if (Index != INDEX_NONE)
	{
		Res = true;
		TArray<FFolderTableData> TargetOriginContent;
		UFolderTableOperatorLibrary::FolderFileSearchByParentID(TargetID, EFolderFileSearchType::EFolderAndFile, TargetOriginContent);
		OriginContent.Key = TargetID;
		OriginContent.Value = TargetOriginContent;
	}
	return Res;
}

bool UFolderTableOperatorLibrary::RecurseDataInFolder(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const FString& NewDataID, TArray<FFolderTableData>& FolderDatas, TArray<FParameterData>& FolderParams, TArray<FFolderTableData>& FileDatas, TArray<FParameterData>& FileParams, TMap<FString, FString>& FileThumbnailMap, TArray<FSingleComponentTableData>& SingleComponentDatas, TMap<FString, FString>& SingleComponentFileMap, TMap<TPair<FString, int32>, FMultiComponentDataItem>& MultiComponentDatas, TArray<FParameterData>& MultiComponentParams, TArray<FCustomMaterialTableData>& MaterialDatas, bool NeedFindOrder/* = false*/)
{
	FFolderTableData NewData = SourceData;
	NewData.id = NewDataID;
	NewData.folder_id = TEXT("");
	NewData.parent_id = TargetFolder.id;

	if (NeedFindOrder)
	{
		int32 MaxOrder = INDEX_NONE;
		if (UFolderTableOperatorLibrary::GetFolderFileMaxOrder(TargetFolder.id, EFolderFileSearchType::EFolderAndFile, MaxOrder))
		{
			NewData.folder_order = (++MaxOrder);
		}
	}

	if (SourceData.can_add_subfolder)
	{
		TArray<FParameterData> SourceParams;
		UFolderTableOperatorLibrary::GetFolderFileParameters(SourceData.id, true, SourceParams);
		for (auto& ParamIter : SourceParams)
		{
			UFolderTableOperatorLibrary::GenerateUUIDForParameter(ParamIter);
			ParamIter.Data.main_id = NewData.id;
			FolderParams.Add(ParamIter);
		}
		FolderDatas.Add(NewData);

		TArray<FFolderTableData> SubDBData;
		UFolderTableOperatorLibrary::FolderFileSearchByParentID(SourceData.id, EFolderFileSearchType::EFolderAndFile, SubDBData);
		for (auto& DataIter : SubDBData)
		{
			UFolderTableOperatorLibrary::RecurseDataInFolder(DataIter, NewData, UFolderTableOperatorLibrary::GenerateUUIDLower(),
				FolderDatas,
				FolderParams,
				FileDatas,
				FileParams,
				FileThumbnailMap,
				SingleComponentDatas,
				SingleComponentFileMap,
				MultiComponentDatas,
				MultiComponentParams,
				MaterialDatas
			);
		}
	}
	else
	{
		TArray<FParameterData> CurFileParams;
		FSingleComponentTableData CurSingleInfo;
		TArray<FMultiComponentDataItem> CurMultiInfo;
		TMap<FString, TArray<FParameterData>> CurMultiParams;
		FCustomMaterialTableData OutMaterial;
		UFolderTableOperatorLibrary::GetFileInfo(SourceData, CurFileParams, CurSingleInfo, CurMultiInfo, CurMultiParams, OutMaterial);

		TMap<TPair<FString, int32>, FMultiComponentDataItem> MultiCompInfo;
		TArray<FParameterData> MultiInfoParams;
		TPair<FString, FString> ThumbnailPair;
		TPair<FString, FString> SingleCompFilePair;
		TPair<FString, FString> MatFilePair;
		UFolderTableOperatorLibrary::ModifyNewFileInfo(SourceData, NewData, CurMultiInfo, CurMultiParams
			, CurFileParams, CurSingleInfo, MultiCompInfo, MultiInfoParams, ThumbnailPair, SingleCompFilePair, OutMaterial, MatFilePair);

		FileDatas.Add(NewData);
		FileParams.Append(CurFileParams);
		FileThumbnailMap.Add(ThumbnailPair);
		if (NewData.folder_type == EFolderType::ESingleComponent)
		{
			SingleComponentDatas.Add(CurSingleInfo);
			SingleComponentFileMap.Add(SingleCompFilePair);
		}
		else if (NewData.folder_type == EFolderType::EMultiComponents)
		{
			MultiComponentDatas.Append(MultiCompInfo);
			MultiComponentParams.Append(MultiInfoParams);
		}
		else if (NewData.folder_type == EFolderType::EMaterial)
		{
			MaterialDatas.Add(OutMaterial);
		}
	}
	return true;
}

void UFolderTableOperatorLibrary::GetFileInfo(const FFolderTableData& TableData, TArray<FParameterData>& OutFileParams, FSingleComponentTableData& OutSingleInfo, TArray<FMultiComponentDataItem>& OutMultiInfo, TMap<FString, TArray<FParameterData>>& OutMultiParams, FCustomMaterialTableData& OutMaterial)
{
	UFolderTableOperatorLibrary::GetFolderFileParameters(TableData.id, false, OutFileParams);
	if (TableData.folder_type == EFolderType::ESingleComponent)
	{
		FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(TableData.id, OutSingleInfo);
	}
	else if (TableData.folder_type == EFolderType::EMultiComponents)
	{
		FMultiComTableOperatorLibrary::RetriveFileMultiComponents(TableData.id, OutMultiInfo);
		for (auto& MultiIter : OutMultiInfo)
		{
			TArray<FParameterData> MultiParams;
			FLocalDatabaseParameterLibrary::RetriveMultiParametersByID(MultiIter.KeyID, MultiParams);
			OutMultiParams.Add(MultiIter.KeyID, MultiParams);
		}
	}
	else if (TableData.folder_type == EFolderType::EMaterial)
	{
		FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(TableData.id, OutMaterial);
	}
}

void UFolderTableOperatorLibrary::ModifyNewFileInfo(const FFolderTableData& TableData, FFolderTableData& NewTableData, TArray<FMultiComponentDataItem> MultiCompInfo, TMap<FString, TArray<FParameterData>> MultiCompParams, TArray<FParameterData>& OutFileParams, FSingleComponentTableData& OutSingleInfo, TMap<TPair<FString, int32>, FMultiComponentDataItem>& OutMultiInfo, TArray<FParameterData>& OutMultiParams, TPair<FString, FString>& ThumbnailPair, TPair<FString, FString>& SingleCompFilePair, FCustomMaterialTableData& OutMatData, TPair<FString, FString> MatFilePair)
{
	for (auto& ParamIter : OutFileParams)
	{
		ParamIter.Data.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
		ParamIter.Data.main_id = NewTableData.id;
		for (auto& EnumIter : ParamIter.EnumData)
		{
			EnumIter.id = UFolderTableOperatorLibrary::GenerateUUIDLower();
			EnumIter.main_id = ParamIter.Data.id;
		}
	}

	if (!TableData.thumbnail_path.IsEmpty())
	{
		FString Extension = FPaths::GetExtension(TableData.thumbnail_path, true);
		NewTableData.thumbnail_path = UFolderTableOperatorLibrary::GenerateThumbnailRelativePath(NewTableData.id, Extension, static_cast<int32>(TableData.folder_type));
		ThumbnailPair.Key = TableData.thumbnail_path;
		ThumbnailPair.Value = NewTableData.thumbnail_path;
	}

	if (NewTableData.folder_type == EFolderType::ESingleComponent)
	{
		SingleCompFilePair.Key = OutSingleInfo.data_path;
		OutSingleInfo.folder_id = NewTableData.id;
		OutSingleInfo.data_path = UFolderTableOperatorLibrary::GenerateSingleComponentFileRelativePath(NewTableData.id);
		SingleCompFilePair.Value = OutSingleInfo.data_path;

	}
	else if (NewTableData.folder_type == EFolderType::EMultiComponents)
	{
		for (int32 i = 0; i < MultiCompInfo.Num(); ++i)
		{
			FString OldID = MultiCompInfo[i].KeyID;
			MultiCompInfo[i].KeyID = UFolderTableOperatorLibrary::GenerateUUIDLower();
			OutMultiInfo.Add(TPair<FString, int32>(NewTableData.id, i), MultiCompInfo[i]);

			if (MultiCompParams.Contains(OldID))
			{
				for (int32 j = 0; j < MultiCompParams[OldID].Num(); ++j)
				{
					UFolderTableOperatorLibrary::GenerateUUIDForParameter(MultiCompParams[OldID][j]);
					MultiCompParams[OldID][j].Data.main_id = MultiCompInfo[i].KeyID;
					OutMultiParams.Add(MultiCompParams[OldID][j]);
				}
			}
		}
	}
	else if (NewTableData.folder_type == EFolderType::EMaterial)
	{
		OutMatData.folder_id = NewTableData.id;
	}
}

FString UFolderTableOperatorLibrary::GenerateSingleComponentFileRelativePath(const FString& ID)
{
	return SINGLE_COMPONENT_DIR + FOLDER_SLASH + ID + FOLDER_SLASH + SINGLE_COMPONENT_FILE_NAME;
}

FString UFolderTableOperatorLibrary::GenerateThumbnailRelativePath(const FString& ID, const FString& FileExtension, const int32& FileType)
{
	const FString& FolderDir = (FileType == static_cast<int32>(EFolderType::EMultiComponents)) ? MULTI_COMPONENT_DIR : SINGLE_COMPONENT_DIR;
	return FolderDir + FOLDER_SLASH + ID + FOLDER_SLASH + THUMBNAIL_FOLDER + FOLDER_SLASH + ID + FileExtension;
}

FString UFolderTableOperatorLibrary::GenerateMaterialFileRelativePath(const FString& ID)
{
	return FString();
}

void UFolderTableOperatorLibrary::BatchFolderThread(TArray<FFolderTableData> FolderDatas)
{
	if (FolderDatas.Num() <= 0)
		return;

	FString SQL = FString(TEXT("insert into folder values "));
	for (auto& FolderIter : FolderDatas)
	{
		FString ValueSQL = FString::Printf(TEXT("(\'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d),"),
			*FolderIter.id, *FolderIter.folder_name, FolderIter.folder_type, *FolderIter.parent_id,
			*FolderIter.visibility_exp, FolderIter.visibility, FolderIter.folder_order);
		SQL += ValueSQL;
	}

	SQL = SQL.Mid(0, SQL.Len() - 1);
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
}

void UFolderTableOperatorLibrary::BatchFolderParamsThread(TArray<FParameterData> FolderParams)
{
	if (FolderParams.Num() <= 0)
		return;

	FString ParamSQL = FString::Printf(TEXT("insert into folder_param (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values "));
	FString EnumSQL = FString::Printf(TEXT("insert into folder_param_enum (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "));
	bool NeedEnumSQLModefy = false;
	for (auto& ParamData : FolderParams)
	{
		ParamSQL += FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
			, *ParamData.Data.id, *ParamData.Data.name, *ParamData.Data.description, ParamData.Data.classific_id, *ParamData.Data.value
			, *ParamData.Data.expression, *ParamData.Data.max_value, *ParamData.Data.max_expression, *ParamData.Data.min_value
			, *ParamData.Data.min_expression, *ParamData.Data.visibility, *ParamData.Data.visibility_exp, *ParamData.Data.editable
			, *ParamData.Data.editable_exp, ParamData.Data.is_enum, *ParamData.Data.param_id, *ParamData.Data.main_id);
		for (auto& EnumDataiter : ParamData.EnumData)
		{
			NeedEnumSQLModefy = true;
			EnumSQL = EnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),")
				, *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display
				, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
		}
	}
	ParamSQL = ParamSQL.Mid(0, ParamSQL.Len() - 1);
	if (NeedEnumSQLModefy)
	{
		EnumSQL = EnumSQL.Mid(0, EnumSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(EnumSQL);
	}
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(ParamSQL);
}

void UFolderTableOperatorLibrary::BatchFileThread(TArray<FFolderTableData> FileDatas)
{
	if (FileDatas.Num() <= 0)
		return;

	FString SQL = FString(TEXT("insert into file values "));
	for (auto& DBFile : FileDatas)
	{
		SQL += FString::Printf(TEXT("(\'%s\', \'%s\', \'%s\', \'%s\', \'%s\', %d, \'%s\', \'%s\', %f, %d, %d, \'%s\'),"),
			*DBFile.id, *DBFile.folder_id, *DBFile.folder_name, *DBFile.folder_code_exp, *DBFile.folder_code,
			DBFile.folder_type, *DBFile.thumbnail_path, *DBFile.visibility_exp, DBFile.visibility,
			DBFile.is_new, DBFile.folder_order, *DBFile.parent_id);
	}
	SQL = SQL.Mid(0, SQL.Len() - 1);
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
}

void UFolderTableOperatorLibrary::BatchFileParamsThread(TArray<FParameterData> FileParams)
{
	if (FileParams.Num() <= 0)
		return;

	FString ParamSQL = FString::Printf(TEXT("insert into file_param (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values "));
	FString EnumSQL = FString::Printf(TEXT("insert into file_param_enum (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "));
	bool NeedEnumSQLModefy = false;
	for (auto& ParamData : FileParams)
	{
		ParamSQL += FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
			, *ParamData.Data.id, *ParamData.Data.name, *ParamData.Data.description, ParamData.Data.classific_id, *ParamData.Data.value
			, *ParamData.Data.expression, *ParamData.Data.max_value, *ParamData.Data.max_expression, *ParamData.Data.min_value
			, *ParamData.Data.min_expression, *ParamData.Data.visibility, *ParamData.Data.visibility_exp, *ParamData.Data.editable
			, *ParamData.Data.editable_exp, ParamData.Data.is_enum, *ParamData.Data.param_id, *ParamData.Data.main_id);
		for (auto& EnumDataiter : ParamData.EnumData)
		{
			NeedEnumSQLModefy = true;
			EnumSQL = EnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),")
				, *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display
				, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
		}
	}
	ParamSQL = ParamSQL.Mid(0, ParamSQL.Len() - 1);
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(ParamSQL);
	if (NeedEnumSQLModefy)
	{
		EnumSQL = EnumSQL.Mid(0, EnumSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(EnumSQL);
	}
}

void UFolderTableOperatorLibrary::BatchThumbnailCopyThread(TMap<FString, FString> FileThumbnailMap)
{
	UFolderTableOperatorLibrary::CopyFileAction(FileThumbnailMap);
}

void UFolderTableOperatorLibrary::BatchSingleComponentInfoThread(TArray<FSingleComponentTableData> SingleComponentDatas)
{
	if (SingleComponentDatas.Num() <= 0)
		return;

	FString SQL = FString(TEXT("insert into single_comp (DATA_PATH,DEPEND_FILES,FOLDER_ID) values "));
	for (auto& SingleIter : SingleComponentDatas)
	{
		FString ValueSQL = FString::Printf(TEXT("('%s','%s', '%s'),")
			, *SingleIter.data_path, *SingleIter.depend_files, *SingleIter.folder_id);
		SQL += ValueSQL;
	}
	SQL = SQL.Mid(0, SQL.Len() - 1);
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
}

void UFolderTableOperatorLibrary::BatchSingleComponentCopyThread(TMap<FString, FString> SingleComponentFileMap)
{
	UFolderTableOperatorLibrary::CopyFileAction(SingleComponentFileMap);
}

void UFolderTableOperatorLibrary::BatchMultiComponentInfoThread(TMap<TPair<FString, int32>, FMultiComponentDataItem> MultiComponentDatas)
{
	for (auto& MultiData : MultiComponentDatas)
		FMultiComTableOperatorLibrary::InsertMultiComponentItem(MultiData.Value, MultiData.Key.Key, MultiData.Key.Value);
}

void UFolderTableOperatorLibrary::BatchMultiComponentParamsThread(TArray<FParameterData> MultiComponentParams)
{
	FString ParamSQL = FString::Printf(TEXT("insert into multi_param (ID,NAME, DESCRIPTION, CLASSIFIC_ID, VALUE, EXPRESSION, MAX_VALUE, MAX_EXPRESSION, MIN_VALUE, MIN_EXPRESSION, VISIBILITY, VISIBILITY_EXP, EDITABLE, EDITABLE_EXP, IS_ENUM, PARAM_ID,MAIN_ID) values "));
	FString EnumSQL = FString::Printf(TEXT("insert into multi_param_enum (ID, VALUE, EXPRESSION, NAME_FOR_DISPLAY, IMAGE_FOR_DISPLAY, VISIBILITY, VISIBILITY_EXP, PRIORITY, MAIN_ID) values "));
	bool NeedEnumSQLModefy = false;
	for (auto& ParamData : MultiComponentParams)
	{
		ParamSQL += FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
			, *ParamData.Data.id, *ParamData.Data.name, *ParamData.Data.description, ParamData.Data.classific_id, *ParamData.Data.value
			, *ParamData.Data.expression, *ParamData.Data.max_value, *ParamData.Data.max_expression, *ParamData.Data.min_value
			, *ParamData.Data.min_expression, *ParamData.Data.visibility, *ParamData.Data.visibility_exp, *ParamData.Data.editable
			, *ParamData.Data.editable_exp, ParamData.Data.is_enum, *ParamData.Data.param_id, *ParamData.Data.main_id);
		for (auto& EnumDataiter : ParamData.EnumData)
		{
			NeedEnumSQLModefy = true;
			EnumSQL = EnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),")
				, *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display
				, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
			if (!EnumDataiter.image_for_display.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
					, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
	}
	ParamSQL = ParamSQL.Mid(0, ParamSQL.Len() - 1);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(ParamSQL);

	if (NeedEnumSQLModefy)
	{
		EnumSQL = EnumSQL.Mid(0, EnumSQL.Len() - 1);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(EnumSQL);
	}
}

void UFolderTableOperatorLibrary::BatchMaterialDataThread(TArray<FCustomMaterialTableData> MatDatas)
{
	if (MatDatas.Num() <= 0)
		return;

	for (auto& Iter : MatDatas)
	{
		FString SQL = FString::Printf(TEXT("insert into material (REF_PATH,IMPORT_PATH,FOLDER_ID) values(\"%s\",'%s','%s')")
			, *Iter.ref_path, *Iter.import_path, *Iter.folder_id);
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(SQL);
	}
}

TSharedPtr<FSQLiteDatabase> UFolderTableOperatorLibrary::GetLocalDataBase()
{
	ULocalDatabaseSubsystem* DBSystem = GWorld->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	if (DBSystem)
	{
		return DBSystem->GetDatabase();
	}
	return NULL;

}

TSharedPtr<FSQLiteDatabase> UFolderTableOperatorLibrary::GetServerDataBase()
{
	ULocalDatabaseSubsystem* DBSystem = GWorld->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	if (DBSystem)
	{
		return DBSystem->GetServerDatabase();
	}
	return NULL;
}

#undef SEARCH_FROM_FOLDER
#undef SEARCH_FROM_FILE

#undef LINK_STR_ONE
#undef LINK_STR_TWO

#undef SEARCH_RULE_ASC	
#undef SEARCH_RULE_DESC

//#ifdef WITH_EDITOR
//#pragma optimize("", on)
//#endif

