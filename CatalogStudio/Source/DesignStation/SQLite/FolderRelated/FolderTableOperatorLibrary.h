// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "FolderTableOperatorLibrary.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(FolderFileLocalLibraryLog, Log, All);


UENUM(BlueprintType)
enum class EFolderType : uint8
{
	EUnkown = 0 UMETA(DisplayName = "Unkown"),
	EMaterial = 1 UMETA(DisplayName = "Material"),
	EMultiMeshs = 2 UMETA(DisplayName = "MultiMeshs"),
	ESingleMesh = 3 UMETA(DisplayName = "SingleMesh"),
	EMultiComponents = 4 UMETA(DisplayName = "MultiComponents"),
	ESingleComponent = 5 UMETA(DisplayName = "SingleComponent")
};

struct FFolderTableDataForDB;
class UFolderAndFileBaseWidget;
class UFolderTableOperatorLibrary;
class UFolderAndFileListWidget;
struct FSingleComponentTableData;
struct FMultiComponentDataItem;
struct FSingleComponentTableData;
struct FCustomMaterialTableData;
struct FParameterData;

USTRUCT(BlueprintType)
struct FFolderTableData
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString folder_id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString folder_name;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString folder_name_exp;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString folder_code_exp;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString folder_code;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	EFolderType folder_type;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString thumbnail_path;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString parent_id;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString visibility_exp;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	float visibility;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	bool deletable;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	bool can_add_subfolder;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	int32 folder_order;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	bool is_new;//

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString update_time;//

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString backend_folder_path;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString md5;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString width;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString height;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString depth;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString boxOffset;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	int32 placeRule;

	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	FString description;   //工艺描述

public:
	FFolderTableData()
		:id(TEXT(""))
		, folder_id(TEXT(""))
		, folder_name(TEXT(""))
		, folder_code_exp(TEXT(""))
		, folder_code(TEXT(""))
		, folder_type(EFolderType::EUnkown)
		, thumbnail_path(TEXT(""))
		, parent_id(TEXT(""))
		, visibility_exp(TEXT("0.0"))
		, visibility(0.0)
		, deletable(false)
		, can_add_subfolder(true)
		, folder_order(-1)
		, is_new(false)
		, update_time(TEXT(""))
		, backend_folder_path(TEXT(""))
		, md5(TEXT(""))
		, width(TEXT("0"))
		, height(TEXT("0"))
		, depth(TEXT("0"))
		, boxOffset(FVector::ZeroVector.ToString())
		, placeRule(0)
		, description(TEXT(""))
	{}

	//FFolderTableData(const FFolderTableDataForDB& InTableDataForDB);
	FFolderTableData(const FFolderDataDB& InData);
	FFolderTableData(const FFileDBData& InData);

	bool IsValid() const { return !id.IsEmpty(); }
	void SetNoValid() { id.Empty(); }

	bool IsRootFolder() const { return !backend_folder_path.Contains(TEXT("/")); }

	bool GenerateThumbnailPath(FString& OutPath) const;

	bool GenerateThumbnailPath(const FString& InFilePath, FString& OutPath) const;

	//static bool ConvertToFolderTableDataForDBArray(const TArray<FFolderTableData>& InArray, TArray<FFolderTableDataForDB>& OutArray);

	static FString EnumToString(const EFolderType& InFolderType);

	void operator=(const FFolderTableData& InData);

	bool operator==(const FFolderTableData& InData) const { return id == InData.id; }

	/*friend inline bool operator==(const FFolderTableData& DataA, const FFolderTableData& DataB)
	{
		return DataA.id.Equals(DataB.id, ESearchCase::IgnoreCase);
	}*/
	friend inline uint32 GetTypeHash(const FFolderTableData& Data)
	{
		return HashCombine(0, GetTypeHash(Data.id));
	}

	void CopyData(const FFolderTableData& InData)
	{
		id = InData.id;
		folder_id = InData.folder_id;
		folder_name = InData.folder_name;
		folder_code = InData.folder_code;
		folder_code_exp = InData.folder_code_exp;
		folder_type = InData.folder_type;
		thumbnail_path = InData.thumbnail_path;
		parent_id = InData.parent_id;
		visibility_exp = InData.visibility_exp;
		visibility = InData.visibility;
		deletable = InData.deletable;
		can_add_subfolder = InData.can_add_subfolder;
		folder_order = InData.folder_order;
		is_new = InData.is_new;
		update_time = InData.update_time;
		backend_folder_path = InData.backend_folder_path;
		md5 = InData.md5;
		width = InData.width;
		height = InData.height;
		depth = InData.depth;
		boxOffset = InData.boxOffset;
		placeRule = InData.placeRule;
		description = InData.description;
	}

	void CopyData(
		const FString& InID,
		const FString& InFolderID,
		const FString& InFolderName,
		const FString& InFolderCode,
		const FString& InFolderCodeExp,
		const int32& InFolderType,
		const FString& InThumbnailPath,
		const FString& InParentID,
		const FString& InVisibilityExp,
		const float& InVisibility,
		const bool& InCanAddSubFolder,
		const int32& InFolderOrder,
		const bool& InIsNew,
		const FString& InUpdateTime,
		const FString& InBackendFolderPath,
		const FString& InMD5,
		const FString& InWidth,
		const FString& InHeight,
		const FString& InDepth,
		const FString& InBoxOffset,
		const int32& InPlaceRule,
		const FString& InDescription
	)
	{
		id = InID;
		folder_id = InFolderID;
		folder_name = InFolderName;
		folder_code = InFolderCode;
		folder_code_exp = InFolderCodeExp;
		folder_type = static_cast<EFolderType>(InFolderType);
		thumbnail_path = InThumbnailPath;
		parent_id = InParentID;
		visibility_exp = InVisibilityExp;
		visibility = InVisibility;
		can_add_subfolder = InCanAddSubFolder;
		folder_order = InFolderOrder;
		is_new = InIsNew;
		update_time = InUpdateTime;
		backend_folder_path = InBackendFolderPath;
		md5 = InMD5;
		width = InWidth;
		height = InHeight;
		depth = InDepth;
		boxOffset = InBoxOffset;
		placeRule = InPlaceRule;
		description = InDescription;
	}

	void CopyDataWithoutID(const FFolderTableData& InData, bool NeedGenerateID)
	{
		if (NeedGenerateID)
			id = (FGuid::NewGuid().ToString()).ToLower();
		folder_id = InData.folder_id;
		folder_name = InData.folder_name;
		folder_code = InData.folder_code;
		folder_code_exp = InData.folder_code_exp;
		folder_type = InData.folder_type;
		thumbnail_path = InData.thumbnail_path;
		parent_id = InData.parent_id;
		visibility_exp = InData.visibility_exp;
		visibility = InData.visibility;
		deletable = InData.deletable;
		can_add_subfolder = InData.can_add_subfolder;
		folder_order = InData.folder_order;
		is_new = InData.is_new;
		update_time = InData.update_time;
		backend_folder_path = InData.backend_folder_path;
		md5 = InData.md5;
		width = InData.width;
		height = InData.height;
		depth = InData.depth;
		boxOffset = InData.boxOffset;
		placeRule = InData.placeRule;
		description = InData.description;
	}

	void CopyData(const FFolderDataDB& InData);
	void CopyData(const FFileDBData& InData);
};

USTRUCT(BlueprintType)
struct FFolderTableDataForDB
{
	GENERATED_USTRUCT_BODY()
public:
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 id;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString folder_id;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString folder_name;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString folder_code;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString folder_code_exp;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 folder_type;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString thumbnail_path;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString thumbnailMd5;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 parent_id;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString visibility_exp;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		float visibility;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 is_delete;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 can_add_subfolder;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 folder_order;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		int32 is_new;
	//
	//	UPROPERTY(BlueprintReadWrite, Category = UserInfo)
	//		FString update_time;//is_new
	//
	//public:
	//	FFolderTableDataForDB()
	//		:id(0)
	//		, folder_id(TEXT(""))
	//		, folder_name(TEXT(""))
	//		, folder_code(TEXT(""))
	//		, folder_code_exp(TEXT(""))
	//		, folder_type(0)
	//		, thumbnail_path(TEXT(""))
	//		, thumbnailMd5(TEXT(""))
	//		, parent_id(-1)
	//		, visibility_exp(TEXT("1"))
	//		, visibility(1)
	//		, is_delete(0)
	//		, can_add_subfolder(1)
	//		, folder_order(-1)
	//		, is_new(0)
	//		, update_time(TEXT(""))
	//	{}
	//	FFolderTableDataForDB(const FFolderTableData& InFolderData);
	//
	//	static bool ConvertToFFolderTableDataArray(const TArray<FFolderTableDataForDB>& InArray, TArray<FFolderTableData>& OutArray);

};

/**
 *
 */

USTRUCT(BlueprintType)
struct FFolderDataDB
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString id;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString folder_name;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	int32 folder_type;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString parent_id;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	float visibility;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	int32 folder_order;

public:
	FFolderDataDB()
		: id(TEXT("")), folder_name(TEXT("")), folder_type(INDEX_NONE), parent_id(TEXT("")), visibility_exp(TEXT("")), visibility(0.0f), folder_order(INDEX_NONE)
	{}

	FFolderDataDB(const FFolderDataDB& InData);

	bool IsValid() { return !id.IsEmpty(); }

	void CopyData(const FFolderTableData& InData);

	static void ConvertDBDataToProgramData(const TArray<FFolderDataDB>& InData, TArray<FFolderTableData>& OutData);
	static void ConvertProgramDataToDBData(const TArray<FFolderTableData>& InData, TArray<FFolderDataDB>& OutData);
};

USTRUCT(BlueprintType)
struct FFileDBData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString id;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString folder_id;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString folder_name;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString folder_code;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString folder_code_exp;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	int32 folder_type;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString thumbnail_path;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString parent_id;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	FString visibility_exp;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	float visibility;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	int32 folder_order;
	UPROPERTY(BlueprintReadWrite, Category = "DB Struct Data")
	int32 is_new;

public:
	FFileDBData()
		: id(TEXT("")), folder_id(TEXT("")), folder_name(TEXT("")), folder_code(TEXT("")), folder_code_exp(TEXT("")), folder_type(INDEX_NONE), thumbnail_path(TEXT(""))
		, parent_id(TEXT("")), visibility_exp(TEXT("")), visibility(0.0f), folder_order(INDEX_NONE), is_new(INDEX_NONE)
	{}

	FFileDBData(const FFileDBData& InData);

	bool IsValid() { return !id.IsEmpty(); }

	void CopyData(const FFolderTableData& InData);

	static void ConvertDBDataToProgramData(const TArray<FFileDBData>& InData, TArray<FFolderTableData>& OutData);
	static void ConvertProgramDataToDBData(const TArray<FFolderTableData>& InData, TArray<FFileDBData>& OutData);
};

USTRUCT(BlueprintType)
struct FRecursePathData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Folder Recurse Data")
	FString id;
	UPROPERTY(BlueprintReadWrite, Category = "Folder Recurse Data")
	FString parent_id;
	UPROPERTY(BlueprintReadWrite, Category = "Folder Recurse Data")
	FString upid;
	UPROPERTY(BlueprintReadWrite, Category = "Folder Recurse Data")
	FString uppid;

public:
	FRecursePathData()
		: id(TEXT("")), parent_id(TEXT("")), upid(TEXT("")), uppid(TEXT(""))
	{}

	static TArray<FString> GetFolderPathFromID(const TArray<FRecursePathData>& InPaths);
};

UENUM()
enum class EFolderFileSearchType
{
	EFolderOnly = 0,
	EFileOnly,
	EFolderAndFile
};

UENUM()
enum class ESearchRuleType
{
	E_None = 0,
	E_ASC,
	E_DESC
};

class FSQLiteDatabase;
struct FParameterData;

UCLASS()
class DESIGNSTATION_API UFolderTableOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static bool FolderFileSearchByID(
		const FString& ID,
		const EFolderFileSearchType& SearchType,
		TArray<FFolderTableData>& OutProgramData,
		const ESearchRuleType& SearchRule = ESearchRuleType::E_ASC
	);
	static bool FolderFileSearchByParentID(
		const FString& ParentID,
		const EFolderFileSearchType& SearchType,
		TArray<FFolderTableData>& OutProgramData,
		const ESearchRuleType& SearchRule = ESearchRuleType::E_ASC
	);

	static void SpliteFolderFile(const TArray<FFolderTableData>& AllDatas, TArray<FFolderTableData>& OutFolderData, TArray<FFolderTableData>& OutFileData);

	static bool SelectFilsByIDOrNameOrCode(const FString& InStr, const FString& ParentID, TArray<FFolderTableData>& OutProgramData);

	static bool SelectFolderFilePath(const FString& ID, bool IsFolder, TArray<FString>& OutParams);

	static bool GetFolderLevel(const FString& ID, int32& OutLevel);

	static bool DeleteFolderFile(const FString& ID, bool IsFolder);
	static bool DeleteServerFolderFile(const FString& ID, bool IsFolder);


	//
	static bool CreateFolderFileData(FFolderTableData& NewData, bool NeedGenerateUUID = true);

	//
	static bool GetFolderFileMaxOrder(const FString& ParentID, const EFolderFileSearchType& SearchType, int32& OutMaxOrder);

	//
	static bool SwapFolderFileOrder(const FString& ID1, int32 NewOrder1, const FString& ID2, int32 NewOrder2, bool IsFolder);
	static bool UpdateFolderFileOrder(const FString& ID, int32 NewOrder, bool IsFolder);


	//
	static bool FolderFileCopyLogic(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData);
	static bool FolderFileCopyLogic(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FString& TargetID, FFolderTableData& NewData);
	static bool FolderFileCopyLogic(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData);

	static bool FolderFileCopy(const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData);
	static bool FolderFileCopy(const FString& SourceID, const FString& TargetFolderID, FFolderTableData& NewData);
	static bool FolderFileCut(const FString& SourceID, const FFolderTableData& TargetFolder, FFolderTableData& NewData);
	static bool FolderFileCut(const FString& SourceID, const FString& TargetFolderID, FFolderTableData& NewData);

	static void FolderFileCopyMultiThread(UFolderAndFileBaseWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID);
	static void FolderFileCopyMultiThreadBind(FString SourceID, FFolderTableData TargetFolder, FString NewDataID, bool NeedGenerateID);
	static void FolderFileCopyMultiThreadBindWithSourceData(FFolderTableData SourceData, FFolderTableData TargetFolder, FString NewDataID, bool NeedGenerateID);
	static void FolderFileCopyMultiThread(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID);

	//multi thread copy to insert sql
	static void FolderFileCopyMultiThreadOperator(UUserWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID);
	//static void FolderFileCopyMultiThreadOperator(UFolderAndFileListWidget* ActionWidget, const FString& SourceID, const FFolderTableData& TargetFolder, const FString& NewDataID);
	static void FolderCopyMultiThreadOperator(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const FString& NewDataID = TEXT(""), bool NeedGenerateID = true);
	static void FolderCopyMultiThreadOperator(const FFolderTableData& SourceData, const FFolderTableData& NewData, const FString& TargetFolderID);

	static void MultiCreateFileDBData(TMap<FFolderTableData, FFolderTableData> OldNewFileMap);
	static void MultiCreateFolderFileParamDBData(TArray<FParameterData> NewParams, const FString& NewFolderFileID, bool IsFolder);
	static void CreateFolderSelfDBData(FFolderTableData SourceData, FFolderTableData NewData);
	static void CopyFileAction(TMap<FString, FString> OldNewMap);
	static void CopySingleComponentsAction(TMap<FFolderTableData, FFolderTableData> OldNewFileMap);
	static void GetSingleComponentsData(TMap<FFolderTableData, FFolderTableData> OldNewFileMap,
		TArray<FSingleComponentTableData>& OutNewSingleDatas, TMap<FString, FString>& OutSinglePathMap);
	static void MultiCreateSingleComponentsDBData(const TArray<FSingleComponentTableData>& NewDatas);



	//
	static bool UpdateFolderFile(const FFolderTableData& NewData);

	//
	static bool GetFolderFileParameters(const FString& ID, bool IsFolder, TArray<FParameterData>& OutParameters);
	static bool DeleteFolderFileParameter(const FString& ParamID, bool IsFolder);
	static bool UpdateFolderFileParameter(const TArray<FParameterData>& ParamData, bool IsFolder);
	static bool UpdateFolderFileParameter(const FParameterData& ParamData, bool IsFolder);
	static bool CreateFolderFileParameter(FParameterData& ParamData, bool IsFolder);

	//通过FOLDER_ID查询file表
	static bool RetriveFileByFolderId(const FString& FolderID, FFolderTableData& File);

	//查询FolderID的值在file表中的FOLDER_ID是否已经存在，不存在返回true，否则返回false
	static bool IsFolderIdUnique(const FString& FolderID);

	//保存文件信息
	static bool UpdateFile(const FFolderTableData& File);


	//normal function
	static FString GenerateUUIDLower();

	static void GenerateUUIDForParameter(FParameterData& EditParam);

	template<typename T>
	static int32 GetArrayIndex(const TArray<T>& ContentArray, const T& SearchT)
	{
		return ContentArray.IndexOfByPredicate(
			[&SearchT](const T& Iter)->bool {return SearchT.Equals(Iter, ESearchCase::IgnoreCase); }
		);
	}

private:
	static bool FolderFileSearch(
		const FString& ID,
		const FString& FolderID,
		const FString& ParentID,
		const EFolderFileSearchType& SearchType,
		const FString& FolderName,
		const FString& FolderCode,
		const ESearchRuleType& SearchRule,
		TArray<FFolderTableData>& OutProgramData
	);

	static bool FolderCopyRecurseInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const TPair<FString, TArray<FFolderTableData>>& OriginTargetContent,
		FFolderTableData& NewData, bool NeedGenerateID = true);
	static bool FolderCopyInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, FFolderTableData& NewData, bool NeedGenerateID = true);
	static bool FileCopyInnerLogic(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, FFolderTableData& NewData, bool NeedGenerateID = true);
	static bool IsTargetSubFolder(const FString& SourceID, const FString& TargetID, TPair<FString, TArray<FFolderTableData>>& OriginContent);

	/********** multi thread ************/
	static bool RecurseDataInFolder(const FFolderTableData& SourceData, const FFolderTableData& TargetFolder, const FString& NewDataID,
		TArray<FFolderTableData>& FolderDatas,
		TArray<FParameterData>& FolderParams,
		TArray<FFolderTableData>& FileDatas,
		TArray<FParameterData>& FileParams,
		TMap<FString, FString>& FileThumbnailMap,
		TArray<FSingleComponentTableData>& SingleComponentDatas,
		TMap<FString, FString>& SingleComponentFileMap,
		TMap<TPair<FString, int32>, FMultiComponentDataItem>& MultiComponentDatas,
		TArray<FParameterData>& MultiComponentParams,
		TArray<FCustomMaterialTableData>& MaterialDatas,
		bool NeedFindOrder = false
	);
	static void GetFileInfo(const FFolderTableData& TableData,
		TArray<FParameterData>& OutFileParams,
		FSingleComponentTableData& OutSingleInfo,
		TArray<FMultiComponentDataItem>& OutMultiInfo,
		TMap<FString, TArray<FParameterData>>& OutMultiParams,
		FCustomMaterialTableData& OutMaterial
	);
	static void ModifyNewFileInfo(const FFolderTableData& TableData, FFolderTableData& NewTableData,
		TArray<FMultiComponentDataItem> MultiCompInfo, TMap<FString, TArray<FParameterData>> MultiCompParams,
		TArray<FParameterData>& OutFileParams,
		FSingleComponentTableData& OutSingleInfo,
		TMap<TPair<FString, int32>, FMultiComponentDataItem>& OutMultiInfo,
		TArray<FParameterData>& OutMultiParams,
		TPair<FString, FString>& ThumbnailPair,
		TPair<FString, FString>& SingleCompFilePair,
		FCustomMaterialTableData& OutMatData,
		TPair<FString, FString> MatFilePair
	);
	static FString GenerateSingleComponentFileRelativePath(const FString& ID);
	static FString GenerateThumbnailRelativePath(const FString& ID, const FString& FileExtension, const int32& FileType);
	static FString GenerateMaterialFileRelativePath(const FString& ID);

	static void BatchFolderThread(TArray<FFolderTableData> FolderDatas);
	static void BatchFolderParamsThread(TArray<FParameterData> FolderParams);
	static void BatchFileThread(TArray<FFolderTableData> FileDatas);
	static void BatchFileParamsThread(TArray<FParameterData> FileParams);
	static void BatchThumbnailCopyThread(TMap<FString, FString> FileThumbnailMap);
	static void BatchSingleComponentInfoThread(TArray<FSingleComponentTableData> SingleComponentDatas);
	static void BatchSingleComponentCopyThread(TMap<FString, FString> SingleComponentFileMap);
	static void BatchMultiComponentInfoThread(TMap<TPair<FString, int32>, FMultiComponentDataItem> MultiComponentDatas);
	static void BatchMultiComponentParamsThread(TArray<FParameterData> MultiComponentParams);
	static void BatchMaterialDataThread(TArray<FCustomMaterialTableData> MatDatas);


	/************************************/

	static TSharedPtr<FSQLiteDatabase> GetLocalDataBase();

	static TSharedPtr<FSQLiteDatabase> GetServerDataBase();

};
