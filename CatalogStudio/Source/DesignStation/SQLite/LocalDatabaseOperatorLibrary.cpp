// Copyright Epic Games, Inc. All Rights Reserved.

#include "LocalDatabaseOperatorLibrary.h"


bool FLocalDatabaseOperatorLibrary::DeleteDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL)
{
	if (Database.IsValid())
	{
		bool bExecute = Database.Execute(*InSQL);
		return bExecute;
	}
	return false;
}

bool FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL)
{
	return DeleteDataFromDataBase(Database, InSQL);
}

bool FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL)
{
	return DeleteDataFromDataBase(Database, InSQL);
}

FString FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()
{
	return  FString("Cache/local_cache.db");
}

bool FLocalDatabaseOperatorLibrary::ExecuteSQL(FSQLiteDatabase& Database, const FString& InSQL)
{
	return DeleteDataFromDataBase(Database, InSQL);
}

bool FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(const FString& InSQL)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;

	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBase(*LocalDB, InSQL);
	return Res;
}

bool FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(const FString& InSQL)
{
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(InSQL);
}

bool FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(const FString& InSQL)
{
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(InSQL);
}

bool FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(const FString& InSQL)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetServerDatabase();
	if (false == LocalDB.IsValid()) return false;

	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBase(*LocalDB, InSQL);
	return Res;
}

bool FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(const FString& InSQL)
{
	return FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(InSQL);
}

bool FLocalDatabaseOperatorLibrary::UpdateDataFromServerDataBaseBySQL(const FString& InSQL)
{
	return FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(InSQL);
}

bool FLocalDatabaseOperatorLibrary::MakeImageTable()
{

	return false;
}
