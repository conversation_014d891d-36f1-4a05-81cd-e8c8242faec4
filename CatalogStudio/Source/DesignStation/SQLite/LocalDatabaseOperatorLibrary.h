// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SubSystem/LocalDatabaseSubsystem.h"
#include "SQLiteCore/Public/SQLiteDatabase.h"
#include "SQLiteSupport/Public/SQLiteResultSet.h"



class DESIGNSTATION_API FLocalDatabaseOperatorLibrary
{
public:

	template<typename T>
	static bool SelectDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL, TArray<T>& OutResult)
	{
		if (Database.IsValid())
		{
			auto SQLitePrepareStatementHandler = [&](const FSQLitePreparedStatement& InResult)
			{
				T NewDataItem;
				for (auto& ColumnName : InResult.GetColumnNames())
				{
					//UE_LOG(LogTemp, Warning, TEXT("ColumnName %s"), *ColumnName);
					FProperty* Property = NewDataItem.StaticStruct()->FindPropertyByName(FName(*ColumnName));
					if (!Property)  continue;//return ESQLitePreparedStatementExecuteRowResult::Continue;
					ESQLiteColumnType ColumnType = ESQLiteColumnType::Null;
					bool Res = InResult.GetColumnTypeByName(*ColumnName, ColumnType);
					if (!Res) return ESQLitePreparedStatementExecuteRowResult::Error;
					if (ESQLiteColumnType::Integer == ColumnType)
					{
						bool bSetted = SetPropertyValue<FByteProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FInt8Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FInt16Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FUInt16Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FIntProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FUInt32Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FInt64Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FUInt64Property>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
					}
					else if (ESQLiteColumnType::Blob == ColumnType)
					{
						bool bSetted = SetPropertyValue<FBoolProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
					}
					else if (ESQLiteColumnType::Float == ColumnType)
					{
						bool bSetted = SetPropertyValue<FFloatProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FDoubleProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
					}
					else if (ESQLiteColumnType::String == ColumnType)
					{
						bool bSetted = SetPropertyValue<FStrProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FNameProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
						bSetted = SetPropertyValue<FTextProperty>(&NewDataItem, Property, ColumnName, InResult);
						if (bSetted) continue;
					}
					else
					{
						//return ESQLitePreparedStatementExecuteRowResult::Error;
					}
				}
				//UE_LOG(LogTemp, Log, TEXT("%s"), *NewDataItem.ToString());
				OutResult.Add(NewDataItem);
				return ESQLitePreparedStatementExecuteRowResult::Continue; // Execute everything
			};
			int32 bExecute = Database.Execute(*InSQL, SQLitePrepareStatementHandler);
			return bExecute > 0;
		}
		return false;
	}

	static bool DeleteDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL);

	static bool InsertDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL);

	static bool UpdateDataFromDataBase(FSQLiteDatabase& Database, const FString& InSQL);

	static FString GetLocalDataBasePath();

	static bool ExecuteSQL(FSQLiteDatabase& Database, const FString& InSQL);

	//
	template<typename T>
	static bool SelectDataFromDataBaseBySQL(const FString& InSQL, TArray<T>& OutResult)
	{
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
		//UE_LOG(LogTemp, Error, TEXT("LocalDB is vaild: %d"), LocalDB.IsValid());
		if (false == LocalDB.IsValid()) return false;

		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<T>(*LocalDB, InSQL, OutResult);
		//UE_LOG(LogTemp, Error, TEXT("selectdata res: %d || OutResult num: %d"), Res, OutResult.Num());
		return Res;
	}

	static bool DeleteDataFromDataBaseBySQL(const FString& InSQL);

	static bool InsertDataFromDataBaseBySQL(const FString& InSQL);

	static bool UpdateDataFromDataBaseBySQL(const FString& InSQL);


	template<typename T>
	static bool SelectDataFromServerDataBaseBySQL(const FString& InSQL, TArray<T>& OutResult)
	{
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetServerDatabase();
		if (false == LocalDB.IsValid()) return false;

		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<T>(*LocalDB, InSQL, OutResult);
		return Res;
	}

	static bool DeleteDataFromServerDataBaseBySQL(const FString& InSQL);

	static bool InsertDataFromServerDataBaseBySQL(const FString& InSQL);

	static bool UpdateDataFromServerDataBaseBySQL(const FString& InSQL);

	static bool MakeImageTable();
private:

	template<typename PropertyType>
	static bool SetPropertyValue(void* Containner, FProperty* InProperty, const FString& ColumnName, const FSQLitePreparedStatement& InResult)
	{
		if (Containner && InProperty && CastField<PropertyType>(InProperty))
		{
			typename PropertyType::TCppType* IntValue = InProperty->ContainerPtrToValuePtr<typename PropertyType::TCppType>(Containner);
			if (IntValue)
			{
				InResult.GetColumnValueByName(*ColumnName, *IntValue);
				return true;
			}
		}
		return false;
	}

	template<>
	bool SetPropertyValue<FBoolProperty>(void* Containner, FProperty* InProperty, const FString& ColumnName, const FSQLitePreparedStatement& InResult)
	{
		if (Containner && InProperty && CastField<FBoolProperty>(InProperty))
		{
			bool* BoolValue = InProperty->ContainerPtrToValuePtr<bool>(Containner);
			if (BoolValue)
			{
				int8 IntValue = 0;
				InResult.GetColumnValueByName(*ColumnName, IntValue);
				*BoolValue = (0 != IntValue);
				return true;
			}
		}
		return false;
	}

};
