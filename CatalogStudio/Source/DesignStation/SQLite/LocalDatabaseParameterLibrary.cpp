// Fill out your copyright notice in the Description page of Project Settings.

#include "LocalDatabaseParameterLibrary.h"

#include "LocalDatabaseOperatorLibrary.h"
#include "FolderRelated/FolderTableOperatorLibrary.h"
#include "Misc/Paths.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"


bool FLocalDatabaseParameterLibrary::RetriveGlobalParameters(TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from global_param"));
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from global_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);
				TArray<FEnumParameterTableData> ParamsEnums;
				for (int32 k = 0; k < Parameters[i].EnumData.Num(); k++)
				{
					FString num = Parameters[i].EnumData[k].priority;

					bool IsHas1 = false;
					for (const auto& ParamsEnumsiter : ParamsEnums)
					{
						if (ParamsEnumsiter.priority.Equals(num))
						{
							IsHas1 = true;
							break;
						}
					}
					if (!IsHas1)
					{
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							bool IsHas = false;
							for (const auto& ParamsEnumsiter : ParamsEnums)
							{
								if (ParamsEnumsiter.priority.Equals(ParamsEnumiter.priority))
								{
									IsHas = true;
									break;
								}
							}
							if (!IsHas && FCString::Atoi(*ParamsEnumiter.priority) < FCString::Atoi(*num))
								num = ParamsEnumiter.priority;
						}
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							if (ParamsEnumiter.priority.Equals(num))
							{
								ParamsEnums.Add(ParamsEnumiter);
							}
						}
					}
				}
				Parameters[i].EnumData = ParamsEnums;
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::RetriveGlobalParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters)
{
	TArray<FParameterData> GPs;
	bool Res = FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GPs);
	Parameters.Empty();
	for (const auto& Iter : GPs)
		Parameters.Add(Iter.Data.name, Iter);
	return Res;
}

bool FLocalDatabaseParameterLibrary::SearchNameGlobalParameters(const FString& InName, TArray<FParameterData>& Parameters)
{
	const FString SearchSQL = FString::Printf(TEXT("select * from global_param where name like '%%%s%%' or description like '%%%s%%' or param_id like '%%%s%%'"), *InName, *InName, *InName);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(SearchSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from global_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);

				TArray<FEnumParameterTableData> ParamsEnums;
				for (int32 k = 0; k < Parameters[i].EnumData.Num(); k++)
				{
					FString num = Parameters[i].EnumData[k].priority;

					bool IsHas1 = false;
					for (const auto& ParamsEnumsiter : ParamsEnums)
					{
						if (ParamsEnumsiter.priority.Equals(num))
						{
							IsHas1 = true;
							break;
						}
					}
					if (!IsHas1)
					{
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							bool IsHas = false;
							for (const auto& ParamsEnumsiter : ParamsEnums)
							{
								if (ParamsEnumsiter.priority.Equals(ParamsEnumiter.priority))
								{
									IsHas = true;
									break;
								}
							}
							if (!IsHas && FCString::Atoi(*ParamsEnumiter.priority) < FCString::Atoi(*num))
								num = ParamsEnumiter.priority;
						}
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							if (ParamsEnumiter.priority.Equals(num))
							{
								ParamsEnums.Add(ParamsEnumiter);
							}
						}
					}
				}
				Parameters[i].EnumData = ParamsEnums;
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::SearchNameGlobalParameter(const FString& InName)
{
	const FString SearchSQL = FString::Printf(TEXT("select * from global_param where description = '%s'"), *InName);
	TArray<FParameterData> Parameters;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterData>(SearchSQL, Parameters);
	return Parameters.Num() < 1;
}

bool FLocalDatabaseParameterLibrary::SearchDMGlobalParameter(const FString& InDM)
{
	const FString SearchSQL = FString::Printf(TEXT("select * from global_param where name = '%s'"), *InDM);
	TArray<FParameterData> Parameters;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterData>(SearchSQL, Parameters);
	return Parameters.Num() < 1;
}

bool FLocalDatabaseParameterLibrary::SearchIDGlobalParameter(const FString& InParamId)
{
	const FString SearchSQL = FString::Printf(TEXT("select * from global_param where param_id = '%s'"), *InParamId);
	TArray<FParameterData> Parameters;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterData>(SearchSQL, Parameters);
	return Parameters.Num() < 1;
}

bool FLocalDatabaseParameterLibrary::RetriveGlobalParameters(const int32& InClassificId, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from global_param where classific_id = %d"), InClassificId);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from global_param_enum where main_id = '%s'"), *Params[i].id);
				if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData))
				{
					TArray<FEnumParameterTableData> ParamsEnums;
					for (int32 k = 0; k < Parameters[i].EnumData.Num(); k++)
					{
						FString num = Parameters[i].EnumData[k].priority;

						bool IsHas1 = false;
						for (const auto& ParamsEnumsiter : ParamsEnums)
						{
							if (ParamsEnumsiter.priority.Equals(num))
							{
								IsHas1 = true;
								break;
							}
						}
						if (!IsHas1)
						{
							for (const auto& ParamsEnumiter : Parameters[i].EnumData)
							{
								bool IsHas = false;
								for (const auto& ParamsEnumsiter : ParamsEnums)
								{
									if (ParamsEnumsiter.priority.Equals(ParamsEnumiter.priority))
									{
										IsHas = true;
										break;
									}
								}
								if (!IsHas && FCString::Atoi(*ParamsEnumiter.priority) < FCString::Atoi(*num))
									num = ParamsEnumiter.priority;
							}
							for (const auto& ParamsEnumiter : Parameters[i].EnumData)
							{
								if (ParamsEnumiter.priority.Equals(num))
								{
									ParamsEnums.Add(ParamsEnumiter);
								}
							}
						}
					}
					Parameters[i].EnumData = ParamsEnums;
					bool NeedUpdata = false;
					for (int32 k2 = 0; k2 < Parameters[i].EnumData.Num() - 1; k2++)
					{
						if (FCString::Atoi(*Parameters[i].EnumData[k2].priority) + 1 != FCString::Atoi(*Parameters[i].EnumData[k2 + 1].priority))
							NeedUpdata = true;
					}
					if (NeedUpdata)
					{
						for (int32 j = 0; j < Parameters[i].EnumData.Num(); j++)
						{
							Parameters[i].EnumData[j].priority = FString::FromInt(j);
						}
					}
					for (auto EnumDataiter : Parameters[i].EnumData)
					{
						for (auto& EnumDataiter2 : Parameters[i].EnumData)
						{
							if (EnumDataiter.id != EnumDataiter2.id && !EnumDataiter.value.IsEmpty() && !EnumDataiter2.value.IsEmpty() && FCString::Atoi(*EnumDataiter.value) == FCString::Atoi(*EnumDataiter2.value))
							{
								EnumDataiter2.value.Empty();
								NeedUpdata = true;
							}
						}
					}
					if (NeedUpdata)
					{
						FLocalDatabaseParameterLibrary::UpdateGlobalParameters(Parameters[i]);
					}
				}
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::UpdateGlobalParameters(const FParameterData& Parameters)
{
	const FString UpdateSQL = FString::Printf(TEXT("update global_param set name = '%s' , description = '%s' , classific_id = %d, value = '%s', expression = '%s', max_value = '%s', max_expression = '%s', min_value = '%s', min_expression = '%s', visibility = '%s', visibility_exp = '%s', editable = '%s', editable_exp = '%s', is_enum = %d, param_id = '%s' where id = '%s'"), *Parameters.Data.name, *Parameters.Data.description, Parameters.Data.classific_id, *Parameters.Data.value, *Parameters.Data.expression, *Parameters.Data.max_value, *Parameters.Data.max_expression, *Parameters.Data.min_value, *Parameters.Data.min_expression, *Parameters.Data.visibility, *Parameters.Data.visibility_exp, *Parameters.Data.editable, *Parameters.Data.editable_exp, Parameters.EnumData.Num() > 0 ? 1 : 0, *Parameters.Data.param_id, *Parameters.Data.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	const FString DeleteSQL = FString::Printf(TEXT("delete from global_param_enum where main_id = '%s'"), *Parameters.Data.id);
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	if (Parameters.EnumData.Num() > 0)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into global_param_enum(id, value, expression, name_for_display, image_for_display, visibility, visibility_exp, priority, main_id) values "));
		for (auto& EnumDataiter : Parameters.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);

			if (!EnumDataiter.image_for_display.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
					, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::InsertGlobalParameters(const FParameterData& Parameters)
{
	const FString InsertSQL = FString::Printf(TEXT("insert into global_param(id ,name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id) values('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s')"), *Parameters.Data.id, *Parameters.Data.name, *Parameters.Data.description, Parameters.Data.classific_id, *Parameters.Data.value, *Parameters.Data.expression, *Parameters.Data.max_value, *Parameters.Data.max_expression, *Parameters.Data.min_value, *Parameters.Data.min_expression, *Parameters.Data.visibility, *Parameters.Data.visibility_exp, *Parameters.Data.editable, *Parameters.Data.editable_exp, Parameters.EnumData.Num() > 0 ? 1 : 0, *Parameters.Data.param_id);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
	if (Parameters.Data.is_enum)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into global_param_enum(id, value, expression, name_for_display, image_for_display, visibility, visibility_exp, priority, main_id) values "));
		for (auto& EnumDataiter : Parameters.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
			if (!EnumDataiter.image_for_display.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
					, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::DeleteGlobalParameterEnum(const FEnumParameterTableData& Parameters)
{
	const FString DeleteSQL = FString::Printf(TEXT("delete from global_param_enum where id = '%s'"), *Parameters.id);
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
}

bool FLocalDatabaseParameterLibrary::RetriveParameterGroups(TArray<FParameterGroupTableData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from parameter_group"));
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterGroupTableData>(RetriveSQL, Parameters);
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::InsertParameterGroup(const FString& GroupName, const FString& Description)
{
	TArray<FParameterGroupTableData> Parameters;
	FLocalDatabaseParameterLibrary::RetriveParameterGroups(Parameters);
	const FString InsertSQL = FString::Printf(TEXT("insert into parameter_group(id,group_name,description) values(%d,'%s','%s')"), Parameters.Num() + 1, *GroupName, *Description);
	return FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
}

bool FLocalDatabaseParameterLibrary::SearchNameParameterGroup(const FString& InName)
{
	const FString SearchSQL = FString::Printf(TEXT("select * from parameter_group where group_name = '%s'"), *InName);
	TArray<FParameterGroupTableData> Parameters;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterGroupTableData>(SearchSQL, Parameters);
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::UpdateParameterGroup(const FParameterGroupTableData& GroupData)
{
	const FString UpdateSQL = FString::Printf(TEXT("update parameter_group set group_name = '%s' , description = '%s' where id = %d"), *GroupData.group_name, *GroupData.description, GroupData.id);
	return FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
}

bool FLocalDatabaseParameterLibrary::DeleteParameterGroup(const FString& ID)
{
	const FString RetriveSQL = FString::Printf(TEXT("delete from parameter_group where id = '%s'"), *ID);
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(RetriveSQL);
}

int32 FLocalDatabaseParameterLibrary::PairParameterGroupNameCount(const FString& GroupName)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from parameter_group where group_name like %s%%"), *GroupName);
	TArray<FParameterGroupTableData> GroupTableData;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterGroupTableData>(RetriveSQL, GroupTableData);
	return GroupTableData.Num();
}

bool FLocalDatabaseParameterLibrary::GetCurrentLevelParameters(FString ID, bool IsFolder, TArray<FParameterData>& Parameters)
{
	FString TableParamName = IsFolder? TEXT("folder_param") : TEXT("file_param");
	FString EnumTableParamName = IsFolder? TEXT("folder_param_enum") : TEXT("file_param_enum");
	const FString SQL = FString::Printf(TEXT("select * from %s where main_id = '%s'"), *TableParamName, *ID);
	TArray<FParameterTableData> Params;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(SQL, Params);
	if(!Res)
		return false;
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString EnumSQL = FString::Printf(TEXT("select * from %s where main_id = '%s'"), *EnumTableParamName, *Params[i].id);
				Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(EnumSQL, Parameters[i].EnumData) && Res;
				if (!Res)
					return false;
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::RetriveFolderParametersByID(const FString& FolderID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from folder_param where main_id = '%s'"), *FolderID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from folder_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::RetriveFileParametersByID(const FString& FileID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from file_param where main_id = '%s'"), *FileID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from file_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParameters(bool IsFile, const FString& ID, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters)
{
	bool Res = true;
	TArray<FParameterData> InheritParameters;
	if (IsFile)
		Res = FLocalDatabaseParameterLibrary::RetriveFileParametersByID(ID, InheritParameters) && Res;
	else
		Res = FLocalDatabaseParameterLibrary::RetriveFolderParametersByID(ID, InheritParameters) && Res;
	for (auto& ParameterIter : InheritParameters)
	{
		if (Parameters.Contains(ParameterIter.Data.name)) continue;
		Parameters.Add(ParameterIter.Data.name, ParameterIter);
	}
	FString ParentID = TEXT("");
	if (IsFile)
	{
		const FString RetriveParentSQL = FString::Printf(TEXT("select * from file where id='%s'"), *ID);
		TArray<FFolderTableData> Self;
		Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(RetriveParentSQL, Self) && Res;
		if (1 != Self.Num()) return false;
		ParentID = Self[0].parent_id;
	}
	else
	{
		const FString RetriveParentSQL = FString::Printf(TEXT("select * from folder where id='%s'"), *ID);
		TArray<FFolderTableData> Self;
		Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(RetriveParentSQL, Self) && Res;
		if (1 != Self.Num()) return false;
		ParentID = Self[0].parent_id;
	}
	Res = FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParameters(false, ParentID, Parameters) && Res;
	return Res;
}

bool FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParametersNoSelf(bool IsFile, const FString& ID, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters)
{
	bool Res = true;
	const FString Table = IsFile ? FString(TEXT("file")) : FString(TEXT("folder"));
	const FString RetriveParentSQL = FString::Printf(TEXT("select * from %s where id='%s'"), *Table, *ID);

	TArray<FFolderTableData> SelfData;
	Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(RetriveParentSQL, SelfData) && Res;
	if (1 != SelfData.Num()) return false;
	TArray<FParameterData> InheritParameters;
	if (!SelfData[0].can_add_subfolder)
		Res = FLocalDatabaseParameterLibrary::RetriveFileParametersByID(SelfData[0].id, InheritParameters) && Res;
	else
		Res = FLocalDatabaseParameterLibrary::RetriveFolderParametersByID(SelfData[0].id, InheritParameters) && Res;
	for (auto& ParameterIter : InheritParameters)
	{
		if (Parameters.Contains(ParameterIter.Data.name)) continue;
		Parameters.Add(ParameterIter.Data.name, ParameterIter);
	}
	Res = FLocalDatabaseParameterLibrary::RetriveFolderFileInheritParametersNoSelf(false, SelfData[0].parent_id, Parameters) && Res;
	return Res;
}

bool FLocalDatabaseParameterLibrary::InsertFolderFileParameters(const FParameterData& Parameters, bool IsFolder)
{
	const FString Table = IsFolder ? FString(TEXT("folder_param")) : FString(TEXT("file_param"));
	const FString EnumTable = IsFolder ? FString(TEXT("folder_param_enum")) : FString(TEXT("file_param_enum"));
	const FString InsertSQL = FString::Printf(TEXT("insert into %s (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s')")
		, *Table, *Parameters.Data.id, *Parameters.Data.name, *Parameters.Data.description, Parameters.Data.classific_id, *Parameters.Data.value, *Parameters.Data.expression, *Parameters.Data.max_value, *Parameters.Data.max_expression, *Parameters.Data.min_value, *Parameters.Data.min_expression, *Parameters.Data.visibility, *Parameters.Data.visibility_exp, *Parameters.Data.editable, *Parameters.Data.editable_exp, Parameters.Data.is_enum, *Parameters.Data.param_id, *Parameters.Data.main_id);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
	if (Parameters.EnumData.Num() > 0)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into %s (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "), *EnumTable);
		for (auto& EnumDataiter : Parameters.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
		}
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::DeleteFolderFileParameter(const FString& ParamID, bool IsFolder)
{
	FString EnumTable = IsFolder ? FString(TEXT("folder_param_enum")) : FString(TEXT("file_param_enum"));
	const FString EnumSQL = FString::Printf(TEXT("delete from %s where MAIN_ID = \'%s\'"), *EnumTable, *ParamID);
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(EnumSQL);

	FString Table = IsFolder ? FString(TEXT("folder_param")) : FString(TEXT("file_param"));
	const FString SQL = FString::Printf(TEXT("delete from %s where ID = \'%s\'"), *Table, *ParamID);

	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(SQL);
}

bool FLocalDatabaseParameterLibrary::DeleteFolderFileParameterEnum(const FEnumParameterTableData& EnumParameterTable, bool IsFolder)
{
	FString Table = IsFolder ? FString(TEXT("folder_param_enum")) : FString(TEXT("file_param_enum"));
	const FString SQL = FString::Printf(TEXT("delete from %s where ID = \'%s\'"), *Table, *EnumParameterTable.id);
	return FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(SQL);
}

bool FLocalDatabaseParameterLibrary::UpdateFolderFileParameter(const FParameterData& ParameterData, bool IsFolder)
{
	FString Table = IsFolder ? FString(TEXT("folder_param")) : FString(TEXT("file_param"));
	FString EnumTable = IsFolder ? FString(TEXT("folder_param_enum")) : FString(TEXT("file_param_enum"));
	FString DataSQL = FString::Printf(TEXT("update %s set NAME='%s', DESCRIPTION='%s', CLASSIFIC_ID=%d, VALUE='%s', EXPRESSION='%s', MAX_VALUE='%s', MAX_EXPRESSION='%s', MIN_VALUE='%s', MIN_EXPRESSION='%s', VISIBILITY='%s', VISIBILITY_EXP='%s', EDITABLE='%s', EDITABLE_EXP='%s', IS_ENUM=%d, PARAM_ID='%s' where ID='%s'")
		, *Table, *ParameterData.Data.name, *ParameterData.Data.description, ParameterData.Data.classific_id, *ParameterData.Data.value, *ParameterData.Data.expression, *ParameterData.Data.max_value, *ParameterData.Data.max_expression, *ParameterData.Data.min_value, *ParameterData.Data.min_expression, *ParameterData.Data.visibility, *ParameterData.Data.visibility_exp, *ParameterData.Data.editable, *ParameterData.Data.editable_exp, ParameterData.Data.is_enum, *ParameterData.Data.param_id, *ParameterData.Data.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(DataSQL);

	const FString DeleteSQL = FString::Printf(TEXT("delete from %s where main_id = '%s'"), *EnumTable, *ParameterData.Data.id);
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	if (ParameterData.EnumData.Num() > 0)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into %s (id ,value,expression,name_for_display,image_for_display,visibility,visibility_exp,priority,main_id) values "), *EnumTable);
		for (auto& EnumDataiter : ParameterData.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *EnumDataiter.main_id);
		}
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::RetriveMultiParametersByID(const FString& MultiID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from multi_param where main_id = '%s'"), *MultiID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from multi_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);
				TArray<FEnumParameterTableData> ParamsEnums;
				for (int32 k = 0; k < Parameters[i].EnumData.Num(); k++)
				{
					FString num = Parameters[i].EnumData[k].priority;

					bool IsHas1 = false;
					for (const auto& ParamsEnumsiter : ParamsEnums)
					{
						if (ParamsEnumsiter.priority.Equals(num))
						{
							IsHas1 = true;
							break;
						}
					}
					if (!IsHas1)
					{
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							bool IsHas = false;
							for (const auto& ParamsEnumsiter : ParamsEnums)
							{
								if (ParamsEnumsiter.priority.Equals(ParamsEnumiter.priority))
								{
									IsHas = true;
									break;
								}
							}
							if (!IsHas && FCString::Atoi(*ParamsEnumiter.priority) < FCString::Atoi(*num))
								num = ParamsEnumiter.priority;
						}
						for (const auto& ParamsEnumiter : Parameters[i].EnumData)
						{
							if (ParamsEnumiter.priority.Equals(num))
							{
								ParamsEnums.Add(ParamsEnumiter);
							}
						}
					}
				}
				Parameters[i].EnumData = ParamsEnums;
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return true;
}

bool FLocalDatabaseParameterLibrary::UpdateMultiParameters(const FParameterData& Parameter)
{
	auto& ParameterData = Parameter.Data;
	const FString UpdateSQL = FString::Printf(TEXT("update multi_param set NAME='%s', DESCRIPTION='%s', CLASSIFIC_ID=%d, VALUE='%s', EXPRESSION='%s', MAX_VALUE='%s', MAX_EXPRESSION='%s', MIN_VALUE='%s', MIN_EXPRESSION='%s', VISIBILITY='%s', VISIBILITY_EXP='%s', EDITABLE='%s', EDITABLE_EXP='%s', IS_ENUM=%d, PARAM_ID='%s' where ID='%s'"), *ParameterData.name, *ParameterData.description, ParameterData.classific_id, *ParameterData.value, *ParameterData.expression, *ParameterData.max_value, *ParameterData.max_expression, *ParameterData.min_value, *ParameterData.min_expression, *ParameterData.visibility, *ParameterData.visibility_exp, *ParameterData.editable, *ParameterData.editable_exp, ParameterData.is_enum, *ParameterData.param_id, *ParameterData.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	const FString DeleteSQL = FString::Printf(TEXT("delete from multi_param_enum where main_id = '%s'"), *Parameter.Data.id);
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	if (Res && (Parameter.EnumData.Num() > 0))
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into multi_param_enum (ID, VALUE, EXPRESSION, NAME_FOR_DISPLAY, IMAGE_FOR_DISPLAY, VISIBILITY, VISIBILITY_EXP, PRIORITY, MAIN_ID) values "));
		for (auto& EnumDataiter : Parameter.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *ParameterData.id);
		}
		Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1)) && Res;
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::UpdateMultiParameters(const TArray<FParameterData>& Parameters)
{
	bool Res = true;
	for (auto& Iter : Parameters)
		Res = FLocalDatabaseParameterLibrary::UpdateMultiParameters(Iter) && Res;
	return Res;
}

bool FLocalDatabaseParameterLibrary::DeleteMultiParameters(const FString& ParameterID)
{
	const FString DeleteSQL = FString::Printf(TEXT("delete from multi_param where ID='%s'"), *ParameterID);
	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	const FString DeleteEnumSQL = FString::Printf(TEXT("delete from multi_param_enum where MAIN_ID='%s'"), *ParameterID);
	Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteEnumSQL) && Res;
	return Res;
}

bool FLocalDatabaseParameterLibrary::InsertMultiParameters(const FParameterData& Parameter, const FString& MultiItemID)
{
	auto& ParameterData = Parameter.Data;
	const FString InsertSQL = FString::Printf(TEXT("insert into multi_param (ID,NAME, DESCRIPTION, CLASSIFIC_ID, VALUE, EXPRESSION, MAX_VALUE, MAX_EXPRESSION, MIN_VALUE, MIN_EXPRESSION, VISIBILITY, VISIBILITY_EXP, EDITABLE, EDITABLE_EXP, IS_ENUM, PARAM_ID,MAIN_ID) values('%s','%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s','%s')"), *ParameterData.id, *ParameterData.name, *ParameterData.description, ParameterData.classific_id, *ParameterData.value, *ParameterData.expression, *ParameterData.max_value, *ParameterData.max_expression, *ParameterData.min_value, *ParameterData.min_expression, *ParameterData.visibility, *ParameterData.visibility_exp, *ParameterData.editable, *ParameterData.editable_exp, ParameterData.is_enum, *ParameterData.param_id, *MultiItemID);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
	if (Res && 0 != ParameterData.is_enum)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into multi_param_enum (ID, VALUE, EXPRESSION, NAME_FOR_DISPLAY, IMAGE_FOR_DISPLAY, VISIBILITY, VISIBILITY_EXP, PRIORITY, MAIN_ID) values "));
		for (auto& EnumDataiter : Parameter.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *ParameterData.id);
		}
		Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1)) && Res;
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::RetriveStyleParametersByID(const FString& StyleID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from decorate_param where main_id = '%s'"), *StyleID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			if (Params[i].is_enum)
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from decorate_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(RetriveEnumSQL, Parameters[i].EnumData);
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FLocalDatabaseParameterLibrary::DeleteStyleParameter(const FString& ParameterID)
{
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *ParameterID));
	const FString DeleteSQL = FString::Printf(TEXT("delete from decorate_param where ID='%s'"), *ParameterID);
	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	return Res;
}

bool FLocalDatabaseParameterLibrary::InsertStyleParameter(const FParameterData& Parameter)
{
	auto& ParameterData = Parameter.Data;
	const FString InsertSQL = FString::Printf(TEXT("insert into decorate_param (ID,NAME, DESCRIPTION, CLASSIFIC_ID, VALUE, EXPRESSION, MAX_VALUE, MAX_EXPRESSION, MIN_VALUE, MIN_EXPRESSION, VISIBILITY, VISIBILITY_EXP, EDITABLE, EDITABLE_EXP, IS_ENUM, PARAM_ID,MAIN_ID) values('%s','%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s','%s')"), *ParameterData.id, *ParameterData.name, *ParameterData.description, ParameterData.classific_id, *ParameterData.value, *ParameterData.expression, *ParameterData.max_value, *ParameterData.max_expression, *ParameterData.min_value, *ParameterData.min_expression, *ParameterData.visibility, *ParameterData.visibility_exp, *ParameterData.editable, *ParameterData.editable_exp, ParameterData.is_enum, *ParameterData.param_id, *ParameterData.main_id);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);
	if (Res && 0 != ParameterData.is_enum)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param_enum (ID, VALUE, EXPRESSION, NAME_FOR_DISPLAY, IMAGE_FOR_DISPLAY, VISIBILITY, VISIBILITY_EXP, PRIORITY, MAIN_ID) values "));
		for (auto& EnumDataiter : Parameter.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *ParameterData.id);
		}
		Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1)) && Res;
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::InsertStyleParameters(const TArray<FParameterData>& Parameters)
{
	bool Res = true;
	for (auto& Parameter : Parameters)
	{
		Res = FLocalDatabaseParameterLibrary::InsertStyleParameter(Parameter) && Res;
	}
	return Res;
}

bool FLocalDatabaseParameterLibrary::UpdateStyleParameter(const FParameterData& Parameter)
{
	auto& ParameterData = Parameter.Data;
	const FString UpdateSQL = FString::Printf(TEXT("update decorate_param set NAME='%s', DESCRIPTION='%s', CLASSIFIC_ID=%d, VALUE='%s', EXPRESSION='%s', MAX_VALUE='%s', MAX_EXPRESSION='%s', MIN_VALUE='%s', MIN_EXPRESSION='%s', VISIBILITY='%s', VISIBILITY_EXP='%s', EDITABLE='%s', EDITABLE_EXP='%s', IS_ENUM=%d, PARAM_ID='%s' where ID='%s'"), *ParameterData.name, *ParameterData.description, ParameterData.classific_id, *ParameterData.value, *ParameterData.expression, *ParameterData.max_value, *ParameterData.max_expression, *ParameterData.min_value, *ParameterData.min_expression, *ParameterData.visibility, *ParameterData.visibility_exp, *ParameterData.editable, *ParameterData.editable_exp, ParameterData.is_enum, *ParameterData.param_id, *ParameterData.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	const FString DeleteSQL = FString::Printf(TEXT("delete from decorate_param_enum where main_id = '%s'"), *Parameter.Data.id);
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteSQL);
	if (Res && (Parameter.EnumData.Num() > 0))
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param_enum (ID, VALUE, EXPRESSION, NAME_FOR_DISPLAY, IMAGE_FOR_DISPLAY, VISIBILITY, VISIBILITY_EXP, PRIORITY, MAIN_ID) values "));
		for (auto& EnumDataiter : Parameter.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumDataiter.id, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *ParameterData.id);
		}
		Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1)) && Res;
	}
	return Res;
}