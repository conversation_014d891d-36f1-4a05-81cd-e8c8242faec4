// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
/**
 *
 */
class DESIGNSTATION_API FLocalDatabaseParameterLibrary
{

public:

	static bool RetriveGlobalParameters(TArray<FParameterData>& Parameters);

	static bool RetriveGlobalParameters(TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters);

	static bool SearchNameGlobalParameters(const FString& InName, TArray<FParameterData>& Parameters);

	static bool SearchNameGlobalParameter(const FString& InName);

	static bool SearchDMGlobalParameter(const FString& InDM);

	static bool SearchIDGlobalParameter(const FString& InParamId);

	static bool RetriveGlobalParameters(const int32& InClassificId, TArray<FParameterData>& Parameters);

	static bool UpdateGlobalParameters(const FParameterData& Parameters);

	static bool InsertGlobalParameters(const FParameterData& Parameters);

	static bool DeleteGlobalParameterEnum(const FEnumParameterTableData& Parameters);

	static bool RetriveParameterGroups(TArray<FParameterGroupTableData>& SingleComp);

	static bool InsertParameterGroup(const FString& GroupName, const FString& Description);

	static bool UpdateParameterGroup(const FParameterGroupTableData& GroupData);

	static bool DeleteParameterGroup(const FString& ID);

	static bool SearchNameParameterGroup(const FString& InName);

	static int32 PairParameterGroupNameCount(const FString& GroupName);

	//获取当前文件夹和文件参数
	static bool GetCurrentLevelParameters(FString ID, bool IsFolder, TArray<FParameterData>& Parameters);

	//folder file
	static bool RetriveFolderParametersByID(const FString& FolderID, TArray<FParameterData>& Parameters);

	static bool RetriveFileParametersByID(const FString& FileID, TArray<FParameterData>& Parameters);

	static bool RetriveFolderFileInheritParameters(bool IsFile, const FString& ID, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters);

	static bool RetriveFolderFileInheritParametersNoSelf(bool IsFile, const FString& ID, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & Parameters);

	static bool InsertFolderFileParameters(const FParameterData& Parameters, bool IsFolder);

	static bool DeleteFolderFileParameter(const FString& ParamID, bool IsFolder);

	static bool DeleteFolderFileParameterEnum(const FEnumParameterTableData& EnumParameterTable, bool IsFolder);

	static bool UpdateFolderFileParameter(const FParameterData& ParameterData, bool IsFolder);

	//
	static bool RetriveMultiParametersByID(const FString& MultiID, TArray<FParameterData>& Parameters);

	static bool UpdateMultiParameters(const FParameterData& Parameter);

	static bool UpdateMultiParameters(const TArray<FParameterData>& Parameters);

	static bool DeleteMultiParameters(const FString& ParameterID);

	static bool InsertMultiParameters(const FParameterData& Parameter, const FString& MultiItemID);

	//风格
	static bool RetriveStyleParametersByID(const FString& StyleID, TArray<FParameterData>& Parameters);

	static bool DeleteStyleParameter(const FString& ParameterID);

	static bool InsertStyleParameter(const FParameterData& Parameter);

	static bool InsertStyleParameters(const TArray<FParameterData>& Parameters);

	static bool UpdateStyleParameter(const FParameterData& Parameter);
};
