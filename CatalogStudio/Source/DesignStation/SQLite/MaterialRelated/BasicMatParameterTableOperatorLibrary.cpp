// Fill out your copyright notice in the Description page of Project Settings.

#include "BasicMatParameterTableOperatorLibrary.h"


void FBasicMatParameterTableOperatorLibrary::ConstructMaterialParameters(const TArray<FBasicMatParameterTableData>& InMaterialParameterGroups, const TArray<FBasicMatParameterTableData>& InMaterialParameters, TArray<UMatParameterGroup*>& OutParameterGroup)
{
	for (auto& Iter : InMaterialParameterGroups)
	{
		if (EMatParameterType::EGroup == static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			OutParameterGroup.Add(Cast<UMatParameterGroup>(NewItem));
		}
	}
	for (auto& Iter : InMaterialParameters)
	{
		if (EMatParameterType::EGroup != static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			UMatParameterGroup** GroupFound = OutParameterGroup.FindByPredicate([&](UMaterialParameterBasicData* InItem)->bool {return InItem->ParameterName.Equals(Iter.group_name); });
			if (nullptr != GroupFound)
			{
				(*GroupFound)->GroupItems.Push(NewItem);
			}
		}
	}
}