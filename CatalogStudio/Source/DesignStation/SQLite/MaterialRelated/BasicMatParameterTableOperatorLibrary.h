// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"

/**
 *
 */
class DESIGNSTATION_API FBasicMatParameterTableOperatorLibrary
{

public:

	static void ConstructMaterialParameters(const TArray<FBasicMatParameterTableData>& InMaterialParameterGroups, const TArray<FBasicMatParameterTableData>& InMaterialParameters, TArray<UMatParameterGroup*>& OutParameterGroup);
};
