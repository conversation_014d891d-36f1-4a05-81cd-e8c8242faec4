// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "BasicMaterialOperatorLibrary.generated.h"

USTRUCT(BlueprintType)
struct FBasicMaterials
{
	GENERATED_USTRUCT_BODY()
public:

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		int32 id;

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		FString mat_name;

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		int32 material_type;

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		FString ref_path; 

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		FString thumbnail_path;

	UPROPERTY(BlueprintReadWrite, Category = BasicMaterials)
		int32 material_level_id;

public:
	FBasicMaterials() :id(0), mat_name(TEXT("")), material_type(0), ref_path(TEXT("")), thumbnail_path(TEXT("")), material_level_id(0) {}

	void operator=(const FBasicMaterials & InData)
	{
		id = InData.id;
		mat_name = InData.mat_name;
		material_type = InData.material_type;
		ref_path = InData.ref_path;
		thumbnail_path = InData.thumbnail_path;
		material_level_id = InData.material_level_id;
	}
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UBasicMaterialOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:

	static bool SelectAllBasicMaterials(TArray<FBasicMaterials>& OutBasicMaterial);

};
