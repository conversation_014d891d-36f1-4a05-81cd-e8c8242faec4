// Fill out your copyright notice in the Description page of Project Settings.

#include "CustomMatParameterTableOperatorLibrary.h"

void FCustomMatParameterTableOperatorLibrary::ConstructMaterialParameters(const TArray<FBasicMatParameterTableData>& InMaterialParameterGroups, const TArray<FCustomMatParameterTableData>& InMaterialParameters, TArray<UMatParameterGroup*>& OutParameterGroup)
{
	for (auto& Iter : InMaterialParameterGroups)
	{
		if (EMatParameterType::EGroup == static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			OutParameterGroup.Add(Cast<UMatParameterGroup>(NewItem));
		}
	}
	for (auto& Iter : InMaterialParameters)
	{
		if (EMatParameterType::EGroup != static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			UMatParameterGroup** GroupFound = OutParameterGroup.FindByPredicate([&](UMaterialParameterBasicData* InItem)->bool {return InItem->ParameterName.Equals(Iter.group_name); });
			if (nullptr != GroupFound)
			{
				(*GroupFound)->GroupItems.Push(NewItem);
			}
		}
	}
}

void FCustomMatParameterTableOperatorLibrary::ConstructCustomMaterialGroups(const TArray<FCustomMatParameterTableData>& CustomMatParameterData, TArray<UMatParameterGroup*>& CustomParameterGroup)
{
	for (auto& Iter : CustomMatParameterData)
	{
		if (EMatParameterType::EGroup == static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			CustomParameterGroup.Add(Cast<UMatParameterGroup>(NewItem));
		}
	}
	for (auto& Iter : CustomMatParameterData)
	{
		if (EMatParameterType::EGroup != static_cast<EMatParameterType>(Iter.parameter_type))
		{
			UMaterialParameterBasicData* NewItem = UMatParameterFactory::CreateMaterialParameterData(Iter);
			UMatParameterGroup** GroupFound = CustomParameterGroup.FindByPredicate([&](UMaterialParameterBasicData* InItem)->bool {return InItem->ParameterName.Equals(Iter.group_name); });
			if (nullptr != GroupFound)
			{
				(*GroupFound)->GroupItems.Push(NewItem);
			}
		}
	}
}
