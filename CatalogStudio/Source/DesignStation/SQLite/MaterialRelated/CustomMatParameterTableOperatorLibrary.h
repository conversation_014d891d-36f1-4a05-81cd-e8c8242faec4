// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"

/**
 *
 */
class DESIGNSTATION_API FCustomMatParameterTableOperatorLibrary
{

public:
	static void ConstructMaterialParameters(const TArray<FBasicMatParameterTableData>& InMaterialParameterGroups, const TArray<FCustomMatParameterTableData>& InMaterialParameters, TArray<UMatParameterGroup*>& OutParameterGroup);

	static void ConstructCustomMaterialGroups(const TArray<FCustomMatParameterTableData>& CustomMatParameterData, TArray<UMatParameterGroup*>& CustomParameterGroup);
};
