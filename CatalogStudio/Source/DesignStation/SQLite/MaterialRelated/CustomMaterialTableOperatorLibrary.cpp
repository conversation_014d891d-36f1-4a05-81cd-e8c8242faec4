// Fill out your copyright notice in the Description page of Project Settings.

#include "CustomMaterialTableOperatorLibrary.h"

#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"


bool FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(const FString& FolderID, FCustomMaterialTableData& CustomMaterial)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from material where FOLDER_ID='%s'"), *FolderID);
	TArray<FCustomMaterialTableData> CMs;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FCustomMaterialTableData>(RetriveSQL, CMs);
	if (false == Res) return false;
	CustomMaterial = (1 == CMs.Num() ? CMs[0] : FCustomMaterialTableData());
	return true;
}

bool FCustomMaterialTableOperatorLibrary::UpdateCustomMaterial(FCustomMaterialTableData& CustomMaterial)
{
	const FString UpdateSQL = FString::Printf(TEXT("update material set REF_PATH=\"%s\",IMPORT_PATH='%s' where ID='%d'"), *CustomMaterial.ref_path, *CustomMaterial.import_path, CustomMaterial.id);

	const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + CustomMaterial.import_path);
	FDownloadFileData File(CustomMaterial.import_path);
	ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);
	FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);

	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	return Res;
}

bool FCustomMaterialTableOperatorLibrary::InsertCustomMaterial(FCustomMaterialTableData& CustomMaterial)
{
	const FString InsertSQL = FString::Printf(TEXT("insert into material (REF_PATH,IMPORT_PATH,FOLDER_ID) values(\"%s\",'%s','%s')"), *CustomMaterial.ref_path, *CustomMaterial.import_path, *CustomMaterial.folder_id);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSQL);

	if (Res)
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + CustomMaterial.import_path);
		FDownloadFileData File(CustomMaterial.import_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);
		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
		Res = FCustomMaterialTableOperatorLibrary::RetriveCustomMaterial(CustomMaterial.folder_id, CustomMaterial);
	}
	return Res;
}