// Fill out your copyright notice in the Description page of Project Settings.

#include "MaterialOperationLibrary.h"
#include "CustomMaterialTableOperatorLibrary.h"



UMaterialInstanceDynamic* FMaterialOperationLibrary::LoadMaterialByMaterialRef(const FString& InMaterialRef)
{
	UMaterialInstanceDynamic* MatInstance = nullptr;
	if (InMaterialRef.Contains(TEXT("MaterialInstanceConstant")))
	{
		UMaterialInstance* ParentMat = LoadObject<UMaterialInstance>(nullptr, *InMaterialRef);
		MatInstance = UMaterialInstanceDynamic::Create(ParentMat, nullptr);
	}
	else
	{
		UMaterial* ParentMat = LoadObject<UMaterial>(nullptr, *InMaterialRef);
		MatInstance = UMaterialInstanceDynamic::Create(ParentMat, nullptr);
	}
	return MatInstance;
}
