// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComTableOperatorLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"


bool FMultiComTableOperatorLibrary::RetriveFileMultiComponents(const FString& FileID, TArray<FMultiComponentDataItem>& MultiComponents)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from multi_comp where FOLDER_ID='%s' order by COMPONENT_ORDER ASC"), *FileID);
	TArray<FMultiComponentDataDB> MultiComps;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FMultiComponentDataDB>(RetriveSQL, MultiComps);
	if (false == Res) return false;
	if (MultiComps.Num() > 0)
	{
		MultiComponents.Init(FMultiComponentDataItem(), MultiComps.Num());
		for (int32 i = 0; i < MultiComps.Num(); ++i)
		{
			MultiComponents[i] = MultiComps[i];
			Res = FLocalDatabaseParameterLibrary::RetriveMultiParametersByID(MultiComps[i].id, MultiComponents[i].ComponentParameters) && Res;
		}
	}
	if (false == Res) MultiComponents.Empty();
	return Res;
}

bool FMultiComTableOperatorLibrary::UpdateMultiComponentItem(const FMultiComponentDataItem& InComponentItem)
{
	FString UpdateSQL = FString::Printf(TEXT("update multi_comp set "));
	UpdateSQL += FString::Printf(TEXT("VISIBILITY_EXP='%s',VISIBILITY_VALUE='%s',"), *InComponentItem.ComponentVisibility.Expression, *InComponentItem.ComponentVisibility.Value);
	UpdateSQL += FString::Printf(TEXT("COMPONENT_ID_EXP='%s',COMPONENT_ID_VALUE='%s',"), *InComponentItem.ComponentID.Expression, *InComponentItem.ComponentID.Value);
	UpdateSQL += FString::Printf(TEXT("DESCRIPTION='%s',"), *InComponentItem.Description);

	UpdateSQL += FString::Printf(TEXT("LOCATION_X_EXP='%s',LOCATION_X_VALUE='%s',"), *InComponentItem.ComponentLocation.LocationX.Expression, *InComponentItem.ComponentLocation.LocationX.Value);
	UpdateSQL += FString::Printf(TEXT("LOCATION_Y_EXP='%s',LOCATION_Y_VALUE='%s',"), *InComponentItem.ComponentLocation.LocationY.Expression, *InComponentItem.ComponentLocation.LocationY.Value);
	UpdateSQL += FString::Printf(TEXT("LOCATION_Z_EXP='%s',LOCATION_Z_VALUE='%s',"), *InComponentItem.ComponentLocation.LocationZ.Expression, *InComponentItem.ComponentLocation.LocationZ.Value);

	UpdateSQL += FString::Printf(TEXT("ROTATION_ROLL_EXP='%s',ROTATION_ROLL_VALUE='%s',"), *InComponentItem.ComponentRotation.Roll.Expression, *InComponentItem.ComponentRotation.Roll.Value);
	UpdateSQL += FString::Printf(TEXT("ROTATION_PITCH_EXP='%s',ROTATION_PITCH_VALUE='%s',"), *InComponentItem.ComponentRotation.Pitch.Expression, *InComponentItem.ComponentRotation.Pitch.Value);
	UpdateSQL += FString::Printf(TEXT("ROTATION_YAW_EXP='%s',ROTATION_YAW_VALUE='%s',"), *InComponentItem.ComponentRotation.Yaw.Expression, *InComponentItem.ComponentRotation.Yaw.Value);

	UpdateSQL += FString::Printf(TEXT("SCALE_X_EXP='%s',SCALE_X_VALUE='%s',"), *InComponentItem.ComponentScale.X.Expression, *InComponentItem.ComponentScale.X.Value);
	UpdateSQL += FString::Printf(TEXT("SCALE_Y_EXP='%s',SCALE_Y_VALUE='%s',"), *InComponentItem.ComponentScale.Y.Expression, *InComponentItem.ComponentScale.Y.Value);
	UpdateSQL += FString::Printf(TEXT("SCALE_Z_EXP='%s',SCALE_Z_VALUE='%s' where ID='%s'"), *InComponentItem.ComponentScale.Z.Expression, *InComponentItem.ComponentScale.Z.Value, *InComponentItem.KeyID);

	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	return Res;
}

bool FMultiComTableOperatorLibrary::InsertMultiComponentItem(const FMultiComponentDataItem& InComponentItem, const FString& FileID, const int32& Order)
{
	FString InsertMultiSQL = TEXT("insert into multi_comp ");
	InsertMultiSQL += TEXT("(ID, FOLDER_ID, VISIBILITY_EXP, VISIBILITY_VALUE, COMPONENT_ID_EXP, COMPONENT_ID_VALUE, DESCRIPTION, LOCATION_X_EXP, LOCATION_X_VALUE, LOCATION_Y_EXP, LOCATION_Y_VALUE, LOCATION_Z_EXP, LOCATION_Z_VALUE, ROTATION_ROLL_EXP, ROTATION_ROLL_VALUE, ROTATION_PITCH_EXP, ROTATION_PITCH_VALUE, ROTATION_YAW_EXP, ROTATION_YAW_VALUE, SCALE_X_EXP, SCALE_X_VALUE, SCALE_Y_EXP, SCALE_Y_VALUE, SCALE_Z_EXP, SCALE_Z_VALUE, COMPONENT_ORDER) ");
	const FString LocationSQL = FString::Printf(TEXT("'%s', '%s', '%s', '%s', '%s', '%s'"), *InComponentItem.ComponentLocation.LocationX.Expression, *InComponentItem.ComponentLocation.LocationX.Value, *InComponentItem.ComponentLocation.LocationY.Expression, *InComponentItem.ComponentLocation.LocationY.Value, *InComponentItem.ComponentLocation.LocationZ.Expression, *InComponentItem.ComponentLocation.LocationZ.Value);
	const FString RotationSQL = FString::Printf(TEXT("'%s', '%s', '%s', '%s', '%s', '%s'"), *InComponentItem.ComponentRotation.Roll.Expression, *InComponentItem.ComponentRotation.Roll.Value, *InComponentItem.ComponentRotation.Pitch.Expression, *InComponentItem.ComponentRotation.Pitch.Value, *InComponentItem.ComponentRotation.Yaw.Expression, *InComponentItem.ComponentRotation.Yaw.Value);
	const FString ScaleSQL = FString::Printf(TEXT("'%s', '%s', '%s', '%s', '%s', '%s'"), *InComponentItem.ComponentScale.X.Expression, *InComponentItem.ComponentScale.X.Value, *InComponentItem.ComponentScale.Y.Expression, *InComponentItem.ComponentScale.Y.Value, *InComponentItem.ComponentScale.Z.Expression, *InComponentItem.ComponentScale.Z.Value);
	InsertMultiSQL += FString::Printf(TEXT("values('%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, %s, %s, %d)"), *InComponentItem.KeyID, *FileID, *InComponentItem.ComponentVisibility.Expression, *InComponentItem.ComponentVisibility.Value, *InComponentItem.ComponentID.Expression, *InComponentItem.ComponentID.Value, *InComponentItem.Description, *LocationSQL, *RotationSQL, *ScaleSQL, Order);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertMultiSQL);
	return Res;
}

bool FMultiComTableOperatorLibrary::DeleteMultiComponentItem(const FString& InID)
{
	const FString DeleteMultiSQL = FString::Printf(TEXT("delete from multi_comp where ID='%s'"), *InID);
	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(DeleteMultiSQL);
	return Res;
}

bool FMultiComTableOperatorLibrary::SwapTwoMultiComponentItemOrder(const FString& InID1, const FString& InID2)
{
	int32 ID1Order = 0;
	int32 ID2Order = 0;
	{
		const FString RetriveSQL1 = FString::Printf(TEXT("select * from multi_comp where ID='%s'"), *InID1);
		TArray<FMultiComponentDataDB> MultiComp1;
		bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FMultiComponentDataDB>(RetriveSQL1, MultiComp1);
		if (false == Res || 1 != MultiComp1.Num()) return false;

		const FString RetriveSQL2 = FString::Printf(TEXT("select * from multi_comp where ID='%s'"), *InID2);
		TArray<FMultiComponentDataDB> MultiComp2;
		Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FMultiComponentDataDB>(RetriveSQL2, MultiComp2);
		if (false == Res || 1 != MultiComp2.Num()) return false;

		ID1Order = MultiComp2[0].component_order;
		ID2Order = MultiComp1[0].component_order;
	}
	FString UpdateSQL = FString::Printf(TEXT("update multi_comp set COMPONENT_ORDER=%d where ID='%s'"), ID1Order, *InID1);
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	UpdateSQL = FString::Printf(TEXT("update multi_comp set COMPONENT_ORDER=%d where ID='%s'"), ID2Order, *InID2);
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(UpdateSQL);
	return true;
}

int32 FMultiComTableOperatorLibrary::RetriveMultiComponentMaxOrder(const FString& FileID)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from multi_comp where FOLDER_ID='%s' order by COMPONENT_ORDER ASC"), *FileID);
	TArray<FMultiComponentDataDB> MultiComps;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FMultiComponentDataDB>(RetriveSQL, MultiComps);
	int32 MaxOrder = 0;
	for (int32 i = 0; i < MultiComps.Num(); ++i)
	{
		MaxOrder = FMath::Max(MultiComps[i].component_order, MaxOrder);
	}
	return MaxOrder;
}