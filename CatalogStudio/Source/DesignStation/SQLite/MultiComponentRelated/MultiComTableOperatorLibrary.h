// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "DataCenter/ComponentData/MultiComponentData.h"


/**
 *
 */
class DESIGNSTATION_API FMultiComTableOperatorLibrary
{
public:

	static bool RetriveFileMultiComponents(const FString& FileID, TArray<FMultiComponentDataItem>& MultiComponents);

	static bool UpdateMultiComponentItem(const FMultiComponentDataItem& InComponentItem);

	static bool InsertMultiComponentItem(const FMultiComponentDataItem& InComponentItem, const FString& FileID, const int32& Order);

	static bool DeleteMultiComponentItem(const FString& InID);

	static bool SwapTwoMultiComponentItemOrder(const FString& InID1, const FString& InID2);

	static int32 RetriveMultiComponentMaxOrder(const FString& FileID);
};
