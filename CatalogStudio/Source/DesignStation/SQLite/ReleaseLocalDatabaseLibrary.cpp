// Fill out your copyright notice in the Description page of Project Settings.

#include "ReleaseLocalDatabaseLibrary.h"

#include "LocalDatabaseOperatorLibrary.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "FolderRelated/DownloadFileData.h"
#include "SingleComponentRelated/SingleComponentTableData.h"


bool FReleaseLocalDatabaseLibrary::ReleaseLocalDatabase(const FString& SourcePath, const FString& TargetPath)
{
	//const FString SourcePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("Cache/local_cache.db"));
	//const FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("Cache/target.dat"));
	bool Res = true;
	{//生成数据库文件
		TArray<uint8> EmptyContent;
		EmptyContent.Empty();
		Res = FCatalogFunctionLibrary::WriteDataToFile(TargetPath, EmptyContent);
		if (false == Res) return false;
	}
	TargetDatabase = MakeShared<FSQLiteDatabase>();
	TargetDatabase->Open(*TargetPath, ESQLiteDatabaseOpenMode::ReadWriteCreate);
	const FString AttachSQL = FString::Printf(TEXT("ATTACH DATABASE '%s' as 's'"), *SourcePath);
	TargetDatabase->Execute(*AttachSQL);
	//FString ErrorStr = TargetDatabase->GetLastError();
	//复制风格
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_style AS SELECT * FROM s.decorate_style;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_content AS SELECT * FROM s.decorate_content;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_sel AS SELECT * FROM s.decorate_sel;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_selch AS SELECT * FROM s.decorate_selch;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_param AS SELECT * FROM s.decorate_param;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE decorate_param_enum AS SELECT * FROM s.decorate_param_enum;"));
	//复制物理文件信息
	TargetDatabase->Execute(TEXT("CREATE TABLE file_image AS SELECT * FROM s.file_image;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE param_image AS SELECT * FROM s.param_image;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE style_image AS SELECT * FROM s.style_image;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE other_file AS SELECT * FROM s.other_file;"));
	//复制变量组
	TargetDatabase->Execute(TEXT("CREATE TABLE parameter_group AS SELECT * FROM s.global_param;"));
	//复制全局变量
	TargetDatabase->Execute(TEXT("CREATE TABLE global_param AS SELECT * FROM s.global_param;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE global_param_enum AS SELECT * FROM s.global_param_enum;"));
	//复制单部件
	TargetDatabase->Execute(TEXT("CREATE TABLE single_comp AS SELECT * FROM s.single_comp;"));
	//复制材质部件
	TargetDatabase->Execute(TEXT("CREATE TABLE material AS SELECT * FROM s.material;"));
	//复制多部件
	TargetDatabase->Execute(TEXT("CREATE TABLE multi_comp AS SELECT * FROM s.multi_comp;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE multi_param AS SELECT * FROM s.multi_param;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE multi_param_enum AS SELECT * FROM s.multi_param_enum;"));
	//复制文件夹表结构
	TargetDatabase->Execute(TEXT("CREATE TABLE folder AS SELECT * FROM s.folder;"));
	//复制文件表结构
	TargetDatabase->Execute(TEXT("CREATE TABLE file AS SELECT * FROM s.file;"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'SXFG' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'SXYS' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'SXCC' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'PP' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'MC' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'NAME' TEXT(100) DEFAULT '';"));
	TargetDatabase->Execute(TEXT("ALTER TABLE file ADD 'MODEL_TYPE' INTEGER(2) DEFAULT '-1';"));
	TargetDatabase->Execute(TEXT("CREATE TABLE folder_sxcc(id INTEGER PRIMARY KEY ASC AUTOINCREMENT NOT NULL,MAINID TEXT(32),VALUE FLOAT(10),NAME TEXT(100));"));
	//复制文件变量表结构
	TargetDatabase->Execute(TEXT("CREATE TABLE file_param AS SELECT * FROM s.file_param where 1=0;"));
	TargetDatabase->Execute(TEXT("CREATE TABLE file_param_enum AS SELECT * FROM s.file_param_enum where 1=0;"));
	//创建定制文件信息表
	TargetDatabase->Execute(TEXT("CREATE TABLE custom_files(FILE_PATH TEXT(200) PRIMARY KEY NOT NULL,FILE_MD5 TEXT(32),FILE_SIZE INTEGER(64));"));
	{//文件转换

		FFolderDataDB SingleCompRoot = FFolderDataDB();
		FFolderDataDB MultiCompRoot = FFolderDataDB();
		FFolderDataDB MaterialRoot = FFolderDataDB();
		{//转存文件
			TArray<FFolderDataDB> AllRootFolders = TArray<FFolderDataDB>();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, TEXT("select * from s.folder where parent_id=-1"), AllRootFolders);
			for (auto& RootIter : AllRootFolders)
			{
				if (2 == RootIter.folder_type || 3 == RootIter.folder_type) continue;
				TArray<FParameterData> FolderParameters;
				FReleaseLocalDatabaseLibrary::RetriveFolderParametersByID(RootIter.id, FolderParameters);
				TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ComponentParametersMap;
				UParameterRelativeLibrary::CombineParameters(ComponentParametersMap, FolderParameters);
				UpdateFolderChildFiles(RootIter.id, ComponentParametersMap);
				switch (RootIter.folder_type)
				{
				case 5:SingleCompRoot = RootIter; break;
				case 4:MultiCompRoot = RootIter; break;
				case 1:MaterialRoot = RootIter; break;
				}
			}
		}
		FFolderDataDB SingleCustom = FFolderDataDB();
		{//跨类型合并目录,单部件与多部件中的定制文件夹合并，单部件文件在前。材质与多部件中的硬装合并，材质文件在前。
			FFolderDataDB MultiCustom = FFolderDataDB();
			FFolderDataDB MultiHardware = FFolderDataDB();

			TArray<FFolderDataDB> Folders = TArray<FFolderDataDB>();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' and FOLDER_NAME='定制'"), *MultiCompRoot.id), Folders);
			MultiCustom = Folders[0];

			Folders.Empty();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' and FOLDER_NAME='硬装'"), *MultiCompRoot.id), Folders);
			MultiHardware = Folders[0];

			/*Folders.Empty();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' and FOLDER_NAME='定制'"), *SingleCompRoot.id), Folders);
			SingleCustom = Folders[0];
			FString UpdateSQL = FString::Printf(TEXT("update folder set PARENT_ID='%s' where PARENT_ID='%s'"), *MultiCustom.id, *SingleCustom.id);
			FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*TargetDatabase, UpdateSQL);*/
			FString UpdateSQL = TEXT("");
			Folders.Empty();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' order by FOLDER_TYPE DESC,FOLDER_ORDER ASC"), *MultiCustom.id), Folders);
			for (int32 i = 0; i < Folders.Num(); ++i)
			{
				UpdateSQL = FString::Printf(TEXT("update folder set FOLDER_ORDER=%d where ID='%s'"), i + 1, *Folders[i].id);
				FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*TargetDatabase, UpdateSQL);
			}

			Folders.Empty();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' and FOLDER_NAME='硬装'"), *MaterialRoot.id), Folders);
			UpdateSQL = FString::Printf(TEXT("update folder set PARENT_ID='%s' where PARENT_ID='%s'"), *MultiHardware.id, *Folders[0].id);
			FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*TargetDatabase, UpdateSQL);
			Folders.Empty();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' order by FOLDER_TYPE ASC,FOLDER_ORDER ASC"), *MultiHardware.id), Folders);
			for (int32 i = 0; i < Folders.Num(); ++i)
			{
				UpdateSQL = FString::Printf(TEXT("update folder set FOLDER_ORDER=%d where ID='%s'"), i + 1, *Folders[i].id);
				FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*TargetDatabase, UpdateSQL);
			}
		}
		{//写入定制的依赖文件
			ParseLogicFilesDependFiles(CustomFiles);
			//ParseDependFilesUnderFolder(SingleCustom.id);
			//TArray<FFolderDataDB> CustomMaterialFolders = TArray<FFolderDataDB>();
			//FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, FString::Printf(TEXT("select * from folder where parent_id='%s' and FOLDER_NAME='定制材质'"), *MaterialRoot.id), CustomMaterialFolders);
			//for (auto& Folder : CustomMaterialFolders)
			//{
			//	ParseDependFilesUnderFolder(Folder.id);
			//}
		}
	}
	RetriveDefaultHardwareFiles();
	TargetDatabase->Close();
	TargetDatabase.Reset();
	return Res;
}

bool FReleaseLocalDatabaseLibrary::UpdateFolderChildFiles(const FString& Parent, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParentParameters)
{
	{//处理子文件
		TArray<FFileDBData> ChildFiles = TArray<FFileDBData>();
		FString RetriveSQL = FString::Printf(TEXT("select * from s.file where parent_id='%s'"), *Parent);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFileDBData>(*TargetDatabase, *RetriveSQL, ChildFiles);
		for (auto& FileIter : ChildFiles)
		{
			TArray<FParameterData> FileParameters;
			FReleaseLocalDatabaseLibrary::RetriveFileParametersByID(FileIter.id, FileParameters);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ComponentParametersMap;
			UParameterRelativeLibrary::CombineParameters(ParentParameters, FileParameters, ComponentParametersMap);
			FString SXFG(TEXT(""));
			FString SXYS(TEXT(""));
			FString SXCC(TEXT(""));
			FString PP(TEXT(""));
			FString MC(TEXT(""));
			int32 ZLLX = -1;
			if (ComponentParametersMap.Contains(TEXT("SXFG")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("SXFG")].Data;
				const auto& EnumData = ComponentParametersMap[TEXT("SXFG")].EnumData;
				for (int32 i = 0; i < EnumData.Num(); ++i)
				{
					bool Res = FMath::IsNearlyEqual(FCString::Atof(*EnumData[i].value), FCString::Atof(*ParameterData.value), 0.01f);
					if (Res)
					{
						SXFG = EnumData[i].name_for_display.IsEmpty() ? ParameterData.value : EnumData[i].name_for_display;
						break;
					}
				}
				ComponentParametersMap.Remove(TEXT("SXFG"));
			}
			if (ComponentParametersMap.Contains(TEXT("SXYS")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("SXYS")].Data;
				const auto& EnumData = ComponentParametersMap[TEXT("SXYS")].EnumData;
				for (int32 i = 0; i < EnumData.Num(); ++i)
				{
					bool Res = FMath::IsNearlyEqual(FCString::Atof(*EnumData[i].value), FCString::Atof(*ParameterData.value), 0.01f);
					if (Res)
					{
						SXYS = EnumData[i].name_for_display.IsEmpty() ? ParameterData.value : EnumData[i].name_for_display;
						break;
					}
				}
				ComponentParametersMap.Remove(TEXT("SXYS"));
			}
			if (ComponentParametersMap.Contains(TEXT("SXCC")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("SXCC")].Data;
				const auto& EnumData = ComponentParametersMap[TEXT("SXCC")].EnumData;
				for (int32 i = 0; i < EnumData.Num(); ++i)
				{
					bool Res = FMath::IsNearlyEqual(FCString::Atof(*EnumData[i].value), FCString::Atof(*ParameterData.value), 0.01f);
					if (Res)
					{
						SXCC = EnumData[i].name_for_display.IsEmpty() ? ParameterData.value : EnumData[i].name_for_display;
						break;
					}
				}
				ComponentParametersMap.Remove(TEXT("SXCC"));
			}
			if (ComponentParametersMap.Contains(TEXT("PP")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("PP")].Data;
				PP = ParameterData.value.Contains("\"") ? ParameterData.value.Mid(1, ParameterData.value.Len() - 2) : ParameterData.value;
				ComponentParametersMap.Remove(TEXT("PP"));
			}
			if (ComponentParametersMap.Contains(TEXT("MC")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("MC")].Data;
				MC = ParameterData.value.Contains("\"") ? ParameterData.value.Mid(1, ParameterData.value.Len() - 2) : ParameterData.value;
				ComponentParametersMap.Remove(TEXT("MC"));
			}
			if (ComponentParametersMap.Contains(TEXT("ZLLX")))
			{
				const auto& ParameterData = ComponentParametersMap[TEXT("ZLLX")].Data;
				ZLLX = FCString::Atoi(*ParameterData.value);
				ComponentParametersMap.Remove(TEXT("ZLLX"));
			}
			if (ComponentParametersMap.Contains(TEXT("ZLXZ")))
			{//有ZLXZ变量则下载，不再判断值是否为0
				//const auto& ParameterData = ComponentParametersMap[TEXT("ZLXZ")].Data;
				//const int32 ZLXZ = FCString::Atoi(*ParameterData.value);
				//if (0 != ZLXZ) CustomFiles.Add(FileIter);
				CustomFiles.Add(FileIter);
				ComponentParametersMap.Remove(TEXT("ZLXZ"));
			}
			const FString CleanCode = FileIter.folder_code.Contains("\"") ? FileIter.folder_code.Mid(1, FileIter.folder_code.Len() - 2) : FileIter.folder_code;
			const FString CleanName = FParameterTableData::GetCleanDataWithoutAdditionMsg(FileIter.folder_name);
			const FString UpdateSQL = FString::Printf(TEXT("update file set SXFG='%s',SXYS='%s',SXCC='%s',PP='%s',MC='%s',NAME='%s',FOLDER_CODE='%s',MODEL_TYPE=%d where ID='%s'"), *SXFG, *SXYS, *SXCC, *PP, *MC, *CleanName, *CleanCode, ZLLX, *FileIter.id);
			FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*TargetDatabase, UpdateSQL);
			FString InsertParameterSQL = FString(TEXT("insert into file_param (id, name, description, classific_id, value, expression, max_value, max_expression, min_value, min_expression, visibility, visibility_exp, editable, editable_exp, is_enum, param_id, main_id) values"));
			FString InsertEnumSQL = FString::Printf(TEXT("insert into file_param_enum (id, value,expression,name_for_display, image_for_display,visibility,visibility_exp,priority,main_id) values "));
			for (auto& PairIter : ComponentParametersMap)
			{
				const FString ID = FGuid::NewGuid().ToString().ToLower();
				auto& Iter = PairIter.Value;
				InsertParameterSQL = InsertParameterSQL + FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),")
					, *ID, *Iter.Data.name, *Iter.Data.description, Iter.Data.classific_id, *Iter.Data.value, *Iter.Data.expression, *Iter.Data.max_value, *Iter.Data.max_expression, *Iter.Data.min_value, *Iter.Data.min_expression, *Iter.Data.visibility, *Iter.Data.visibility_exp, *Iter.Data.editable, *Iter.Data.editable_exp, Iter.Data.is_enum, *Iter.Data.param_id, *FileIter.id);

				if (Iter.EnumData.Num() > 0)
				{
					for (auto& EnumDataiter : Iter.EnumData)
					{
						const FString EnumID = FGuid::NewGuid().ToString().ToLower();
						InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumID, *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *ID);
					}
				}
			}
			bool Res = true;
			if (InsertParameterSQL.Len() > 230)
			{
				InsertParameterSQL.RemoveAt(InsertParameterSQL.Len() - 1);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(*TargetDatabase, InsertParameterSQL);
			}
			if (InsertEnumSQL.Len() > 150)
			{
				InsertEnumSQL.RemoveAt(InsertEnumSQL.Len() - 1);
				Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(*TargetDatabase, InsertEnumSQL);
			}
		}
	}
	{//处理子文件夹
		TArray<FFolderDataDB> ChildFolders = TArray<FFolderDataDB>();
		FString RetriveSQL = FString::Printf(TEXT("select * from s.folder where parent_id='%s'"), *Parent);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, *RetriveSQL, ChildFolders);
		FString InsertSXCCSQL = FString(TEXT("insert into folder_sxcc (MAINID,VALUE,NAME) values "));
		for (auto& FolderIter : ChildFolders)
		{
			UE_LOG(LogTemp, Warning, TEXT("current folder name : [%s], id : [%s], parent_id : [%s]"), *FolderIter.folder_name, *FolderIter.id, *FolderIter.parent_id);
			TArray<FParameterData> FolderParameters;
			FReleaseLocalDatabaseLibrary::RetriveFolderParametersByID(FolderIter.id, FolderParameters);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ComponentParametersMap;
			UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParameters, ComponentParametersMap);
			if (ComponentParametersMap.Contains(TEXT("SXCC")))
			{
				const auto& SXCC = ComponentParametersMap[TEXT("SXCC")];
				for (auto& Iter : SXCC.EnumData)
				{
					InsertSXCCSQL = InsertSXCCSQL + FString::Printf(TEXT("('%s',%f,'%s'),"), *FolderIter.id, FCString::Atof(*Iter.value), *Iter.name_for_display);
				}
			}
			UpdateFolderChildFiles(FolderIter.id, ComponentParametersMap);
		}
		if (InsertSXCCSQL.Len() > 60)
		{
			InsertSXCCSQL.RemoveAt(InsertSXCCSQL.Len() - 1);
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(*TargetDatabase, InsertSXCCSQL);
		}
	}
	return true;
}

bool FReleaseLocalDatabaseLibrary::RetriveFolderParametersByID(const FString& FolderID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from s.folder_param where main_id = '%s'"), *FolderID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FParameterTableData>(*TargetDatabase, RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from s.folder_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FEnumParameterTableData>(*TargetDatabase, RetriveEnumSQL, Parameters[i].EnumData);
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FReleaseLocalDatabaseLibrary::RetriveFileParametersByID(const FString& FileID, TArray<FParameterData>& Parameters)
{
	const FString RetriveSQL = FString::Printf(TEXT("select * from s.file_param where main_id = '%s'"), *FileID);
	TArray<FParameterTableData> Params;
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FParameterTableData>(*TargetDatabase, RetriveSQL, Params);
	if (Params.Num() > 0)
	{
		Parameters.Init(FParameterData(), Params.Num());
		for (int32 i = 0; i < Params.Num(); ++i)
		{
			Parameters[i].Data = Params[i];
			{
				const FString RetriveEnumSQL = FString::Printf(TEXT("select * from s.file_param_enum where main_id = '%s'"), *Params[i].id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FEnumParameterTableData>(*TargetDatabase, RetriveEnumSQL, Parameters[i].EnumData);
			}
			Params[i].is_enum = Parameters[i].EnumData.Num() > 0;
		}
	}
	return Parameters.Num() > 0;
}

bool FReleaseLocalDatabaseLibrary::ParseDependFilesUnderFolder(const FString& FolderID)
{
	{//查找子文件
		const FString RetriveFileSQL = FString::Printf(TEXT("select * from s.file where PARENT_ID='%s'"), *FolderID);
		TArray<FFileDBData> ChildFiles = TArray<FFileDBData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFileDBData>(*TargetDatabase, RetriveFileSQL, ChildFiles);
		ParseLogicFilesDependFiles(ChildFiles);
	}
	{//查找子文件夹
		const FString RetriveFileSQL = FString::Printf(TEXT("select * from s.folder where PARENT_ID='%s'"), *FolderID);
		TArray<FFolderDataDB> ChildFolders = TArray<FFolderDataDB>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFolderDataDB>(*TargetDatabase, RetriveFileSQL, ChildFolders);
		for (auto& Folder : ChildFolders)
		{
			FReleaseLocalDatabaseLibrary::ParseDependFilesUnderFolder(Folder.id);
		}
	}
	return true;
}

void FReleaseLocalDatabaseLibrary::ParseLogicFilesDependFiles(const TArray<FFileDBData>& Files)
{
	TArray<FString> AllDependFiles;
	for (auto& File : Files)
	{
		if (5 == File.folder_type)
		{//单部件
			TArray<FSingleComponentTableData> SCs;
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FSingleComponentTableData>(*TargetDatabase, FString::Printf(TEXT("select * from s.single_comp where FOLDER_ID='%s'"), *File.id), SCs);
			for (auto& Iter : SCs)
			{
				AllDependFiles.AddUnique(Iter.data_path);
				TArray<FString> DependFiles;
				Iter.FilePathsAndMD5Pairs(DependFiles);
				AllDependFiles.Append(DependFiles);
			}
		}
		else if (1 == File.folder_type)
		{//材质
			TArray<FCustomMaterialTableData> Mats;
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FCustomMaterialTableData>(*TargetDatabase, FString::Printf(TEXT("select * from s.material where FOLDER_ID='%s'"), *File.id), Mats);
			for (auto& Iter : Mats)
			{
				AllDependFiles.AddUnique(Iter.import_path);
			}
		}
	}
	for (auto& FileIter : AllDependFiles)
	{
		FString InsertFileSQL = FString::Printf(TEXT("insert into custom_files (FILE_PATH, FILE_MD5,FILE_SIZE) values "));
		const FString RetriveFileSQL = FString::Printf(TEXT("select * from s.other_file where path='%s'"), *FileIter);
		TArray<FDownloadFileData> FileDatas = TArray<FDownloadFileData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FDownloadFileData>(*TargetDatabase, RetriveFileSQL, FileDatas);
		for (auto& Folder : FileDatas)
		{
			InsertFileSQL = InsertFileSQL + FString::Printf(TEXT("('%s','%s',%d),"), *Folder.path, *Folder.md5, Folder.size);
		}
		if (InsertFileSQL.Len() > 70)
		{
			InsertFileSQL.RemoveAt(InsertFileSQL.Len() - 1);
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(*TargetDatabase, InsertFileSQL);
		}
	}
}

bool FReleaseLocalDatabaseLibrary::RetriveDefaultHardwareFiles()
{
	TArray<int64> UniqueFiles;
	{//查询硬装的风格选项上使用的文件的folder_id
		TArray<FParameterTableData> StyleParameters;
		const FString RetriveSQL = TEXT("select * from decorate_param where (MAIN_ID in (select id from decorate_sel where PARENT_ID in (select id from decorate_content where NAME like '*%'))) and (NAME='FV')");
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FParameterTableData>(*TargetDatabase, RetriveSQL, StyleParameters);
		for (auto& Parameter : StyleParameters)
		{
			UniqueFiles.AddUnique(FCString::Atoi64(*Parameter.value));
		}
	}
	TArray<FFileDBData> AllHardwareFiles = TArray<FFileDBData>();
	for (auto& File : UniqueFiles)
	{
		const FString RetriveFileSQL = FString::Printf(TEXT("select * from s.file where FOLDER_ID='%d'"), File);
		TArray<FFileDBData> ChildFiles = TArray<FFileDBData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FFileDBData>(*TargetDatabase, RetriveFileSQL, ChildFiles);
		AllHardwareFiles.Append(ChildFiles);
	}
	ParseLogicFilesDependFiles(AllHardwareFiles);
	return true;
}