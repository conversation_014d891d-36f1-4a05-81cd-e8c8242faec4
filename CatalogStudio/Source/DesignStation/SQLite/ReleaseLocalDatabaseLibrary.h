// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "DataCenter/Parameter/ParameterData.h"
#include "FolderRelated/FolderTableOperatorLibrary.h"
#include "SQLiteCore/Public/SQLiteDatabase.h"

/**
 *
 */
class DESIGNSTATION_API FReleaseLocalDatabaseLibrary
{

public:

	//将LocalDB中的数据转存到ReleaseDBPath指定的数据库中，ReleaseDBPath为绝对路径
	bool ReleaseLocalDatabase(const FString& SourcePath, const FString& TargetPath);


private:

	bool RetriveFolderParametersByID(const FString& FolderID, TArray<FParameterData>& Parameters);

	bool RetriveFileParametersByID(const FString& FileID, TArray<FParameterData>& Parameters);

	bool UpdateFolderChildFiles(const FString& Parent, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParentParameters);

	bool ParseDependFilesUnderFolder(const FString& FolderID);

	//风格中使用的默认硬装文件也需要更新时下载
	bool RetriveDefaultHardwareFiles();

	void ParseLogicFilesDependFiles(const TArray<FFileDBData>& Files);

private:

	TSharedPtr<FSQLiteDatabase> TargetDatabase = nullptr;

	TArray<FFileDBData> CustomFiles;
};
