// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentTableData.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/SubSystem/LocalDatabaseSubsystem.h"
#include "Misc/Paths.h"


#define LOCTEXT_NAMESPACE "SingleComponent"

FSingleComponentTableData::FSingleComponentTableData(const FString& InFileMd5, const FString& InDataPath, const FString& InDependFiles, const FString& InFolderID)
	:data_path(InDataPath)
	, depend_files(InDependFiles)
	, folder_id(InFolderID)
{
}

FString FSingleComponentTableData::ConvertFilePathArrayToString(const TArray<FString>& InFilePaths)
{
	FString RefPath(TEXT(""));
	for (auto& PathIter : InFilePaths)
	{
		FString FullPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + PathIter);
		FString FileMd5(TEXT(""));
		int64 FileSize(0);
		bool Res = ACatalogPlayerController::GetFileMD5AndSize(FullPath, FileMd5, FileSize);
		if (Res)
		{
			FString SubString = FString::Printf(TEXT("%s+%s;"), *PathIter, *FileMd5);
			RefPath.Append(SubString);
		}
	}
	return RefPath;
}

void FSingleComponentTableData::FilePathsAndMD5Pairs(TArray<FString>& FileMD5Pairs)
{
	FString StringLeft(depend_files);
	FString Left(TEXT(""));
	FString Right(TEXT(""));
	bool bHasSplitChar = StringLeft.Split(TEXT(";"), &Left, &Right);
	while (bHasSplitChar && !Left.IsEmpty())
	{
		{
			FString FilePath(TEXT(""));
			FString FileMD5(TEXT(""));
			Left.Split(TEXT("+"), &FilePath, &FileMD5);
			if (!FilePath.IsEmpty())
			{
				FileMD5Pairs.AddUnique(FilePath);
			}
		}

		StringLeft = Right;
		Left = Right = TEXT("");
		bHasSplitChar = StringLeft.Split(TEXT(";"), &Left, &Right);
	}
	if (false == data_path.IsEmpty()) FileMD5Pairs.AddUnique(data_path);
}

bool FSingleComponentTableData::GenerateDataFilePath(FString& OutPath) const
{
	OutPath = FString::Printf(TEXT("SingleComponents/%s/comp.data"), *this->folder_id);
	return true;
}

bool FSingleComponentTableData::GenerateThumbnailPath(FString& OutThumbnialPath) const
{
	OutThumbnialPath = FString::Printf(TEXT("SingleComponents/%d/"), this->id);
	return true;
}

bool FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(const FString& FileID, FSingleComponentTableData& SingleComp)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;

	const FString RetriveSQL = FString::Printf(TEXT("select * from single_comp where FOLDER_ID='%s'"), *FileID);
	TArray<FSingleComponentTableData> Results;
	bool Res = FLocalDatabaseOperatorLibrary::SelectDataFromDataBase<FSingleComponentTableData>(*LocalDB, RetriveSQL, Results);
	if (false == Res || 1 != Results.Num()) return false;
	SingleComp = Results[0];
	return true;
}

bool FSingleComponentOperatorLibrary::CreateSingleComponent(FSingleComponentTableData& SingleComp)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;
	const FString CreateSQL = FString::Printf(TEXT("insert into single_comp (DATA_PATH,DEPEND_FILES,FOLDER_ID) values ('%s','%s', '%s')"), *SingleComp.data_path, *SingleComp.depend_files, *SingleComp.folder_id);
	bool Res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBase(*LocalDB, CreateSQL);
	FSingleComponentTableData NewSingleComp;

	Res = Res && FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(SingleComp.folder_id, NewSingleComp);
	if (!SingleComp.data_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComp.data_path);
		FDownloadFileData File(SingleComp.data_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);

		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
	}
	TArray<FString> PathAndMd5;
	SingleComp.FilePathsAndMD5Pairs(PathAndMd5);
	for (auto& depIter : PathAndMd5)
	{
		if (!depIter.IsEmpty())
		{
			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + depIter);
			FDownloadFileData File(depIter);
			ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);

			FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
		}
	}

	if (Res) SingleComp = NewSingleComp;
	return Res;
}

bool FSingleComponentOperatorLibrary::UpdateSingleComponent(FSingleComponentTableData& SingleComp)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;

	const FString UpdateSQL = FString::Printf(TEXT("update single_comp set DATA_PATH='%s', DEPEND_FILES='%s' where id=%d"), *SingleComp.data_path, *SingleComp.depend_files, SingleComp.id);
	bool Res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBase(*LocalDB, UpdateSQL);
	if (!SingleComp.data_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComp.data_path);
		FDownloadFileData File(SingleComp.data_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);

		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
	}
	TArray<FString> PathAndMd5;
	SingleComp.FilePathsAndMD5Pairs(PathAndMd5);
	for (auto& depIter : PathAndMd5)
	{
		if (!depIter.IsEmpty())
		{
			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + depIter);
			FDownloadFileData File(depIter);
			ACatalogPlayerController::GetFileMD5AndSize(FilePath, File.md5, File.size);

			FDownloadFileDataLibrary::UpdateFileMD5(TEXT("other_file"), File);
		}
	}
	return Res;
}

bool FSingleComponentOperatorLibrary::DeleteSingleComponent(const int32& ID)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;

	const FString DeleteSQL = FString::Printf(TEXT("delete from single_comp where id=%d"), ID);
	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBase(*LocalDB, DeleteSQL);
	return Res;
}

bool FSingleComponentOperatorLibrary::DeleteSingleComponent(const FString& FileID)
{
	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	TSharedPtr<FSQLiteDatabase> LocalDB = LocalDBSubsystem->GetDatabase();
	if (false == LocalDB.IsValid()) return false;

	const FString DeleteSQL = FString::Printf(TEXT("delete from single_comp where FOLDER_ID='%s'"), *FileID);
	bool Res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBase(*LocalDB, DeleteSQL);
	return Res;
}

#undef LOCTEXT_NAMESPACE