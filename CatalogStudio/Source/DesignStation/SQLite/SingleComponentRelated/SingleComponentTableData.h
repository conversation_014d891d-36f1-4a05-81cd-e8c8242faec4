// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/ComponentData/ComponentEnumData.h"
#include "SingleComponentTableData.generated.h"


USTRUCT(BlueprintType)
struct FSingleComponentTableData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY(BlueprintReadWrite, Category = SingleComponent)
		int32 id;
	UPROPERTY(BlueprintReadWrite, Category = SingleComponent)
		FString data_path;
	UPROPERTY(BlueprintReadWrite, Category = SingleComponent)
		FString depend_files;
	UPROPERTY(BlueprintReadWrite, Category = SingleComponent)
		FString folder_id;

	FSingleComponentTableData() :id(-1), data_path(TEXT("")), depend_files(TEXT("")), folder_id(TEXT("")) {}
	FSingleComponentTableData(const FString& InFileMd5, const FString& InDataPath, const FString& InDependFiles, const FString& InFolderID);

	bool IsValid() const { return id > 0; }

	bool GenerateDataFilePath(FString& OutPath) const;

	bool GenerateThumbnailPath(FString& OutThumbnialPath) const;

	static FString ConvertFilePathArrayToString(const TArray<FString>& InFilePaths);

	void FilePathsAndMD5Pairs(TArray<FString>& FileMD5Pairs);
};

/**
 *
 */
class DESIGNSTATION_API FSingleComponentOperatorLibrary
{

public:

	static bool RetriveSingleComponentByFileID(const FString& FileID, FSingleComponentTableData& SingleComp);

	static bool CreateSingleComponent(FSingleComponentTableData& SingleComp);

	static bool UpdateSingleComponent(FSingleComponentTableData& SingleComp);

	static bool DeleteSingleComponent(const int32& ID);

	static bool DeleteSingleComponent(const FString& FileID);
};
