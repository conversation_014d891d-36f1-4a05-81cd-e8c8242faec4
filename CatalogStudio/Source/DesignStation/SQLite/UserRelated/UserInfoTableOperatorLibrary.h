
// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "UserInfoTableOperatorLibrary.generated.h"



USTRUCT(BlueprintType)
struct DESIGNSTATION_API FUserInfoTableData
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		int32 id;
	UPROPERTY()
		FString userName;
	UPROPERTY()
		FString name;
	UPROPERTY()
		FString phone;
	UPROPERTY()
		FString profilePhoto;
	UPROPERTY()
		FString token;
public:
	FUserInfoTableData()
	: id(INDEX_NONE)
	, userName(TEXT(""))
	, name(TEXT(""))
	, phone(TEXT(""))
	, profilePhoto(TEXT(""))
	, token(TEXT(""))
	{}

	FUserInfoTableData(
		const int32& InID,
		const FString& InUserName, 
		const FString& InName, 
		const FString& InUserPhone,
		const FString& InUserProfilePhoto
	);
};

USTRUCT(BlueprintType)
struct  DESIGNSTATION_API FUserInfoTableDataMsg
{
	GENERATED_USTRUCT_BODY()
public:
	UPROPERTY()
		FString code;
	UPROPERTY()
		FUserInfoTableData resp;
	UPROPERTY()
		bool success;

public:
	FUserInfoTableDataMsg() : code(TEXT("")), resp(FUserInfoTableData()), success(false) {}
};
/**
 *
 */
UCLASS()
class DESIGNSTATION_API UUserInfoTableOperatorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:


};
