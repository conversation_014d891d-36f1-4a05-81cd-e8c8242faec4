// Fill out your copyright notice in the Description page of Project Settings.

#include "RemeberUserName.h"
#include "Kismet/GameplayStatics.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"
#include "Runtime/Core/Public/Serialization/MemoryReader.h"
#include "Runtime/Core/Public/Serialization/ArchiveSaveCompressedProxy.h"
#include "Runtime/Core/Public/Serialization/ArchiveLoadCompressedProxy.h"
#include "Serialization/BufferArchive.h"


URemeberUserName* URemeberUserName::Get()
{
	URemeberUserName* SaveLoadInstance = Cast<URemeberUserName>(UGameplayStatics::CreateSaveGameObject(URemeberUserName::StaticClass()));
	return SaveLoadInstance;
}

bool URemeberUserName::SaveRemeberDataToFile(const FString& InFileFullPath)
{
	FBufferArchive ToBinary;
	this->SaveLoadData(ToBinary);
	if (ToBinary.Num() <= 0) return false;
	TArray<uint8> CompressedData;
	FArchiveSaveCompressedProxy Compressor = FArchiveSaveCompressedProxy(CompressedData, NAME_Zlib);
	Compressor << ToBinary;
	Compressor.Flush();
	if (FFileHelper::SaveArrayToFile(CompressedData, *InFileFullPath))
	{
		Compressor.FlushCache();
		CompressedData.Empty();
		ToBinary.FlushCache();
		ToBinary.Empty();
		ToBinary.Close();
		return true;
	}
	Compressor.FlushCache();
	CompressedData.Empty();
	ToBinary.FlushCache();
	ToBinary.Empty();
	ToBinary.Close();
	return false;
}

bool URemeberUserName::LoadRemeberDataFromFile(const FString& InFillFullPath)
{
	TArray<uint8> CompressedData;
	bool Res = true;
	Res = FFileHelper::LoadFileToArray(CompressedData, *InFillFullPath);
	if (!Res)
	{
		UE_LOG(LogTemp, Error, TEXT("FFileHelper::LoadFileToArray [%s] failed!"), *InFillFullPath);
		return false;
	}
		
	FArchiveLoadCompressedProxy Decompressor = FArchiveLoadCompressedProxy(CompressedData, NAME_Zlib);
	if (Decompressor.GetError())
		return false;

	FBufferArchive DecompressedBinaryArray;
	Decompressor << DecompressedBinaryArray;

	FMemoryReader FromBinary = FMemoryReader(DecompressedBinaryArray, true);
	FromBinary.Seek(0);
	this->SaveLoadData(FromBinary);

	CompressedData.Empty();
	Decompressor.FlushCache();

	FromBinary.FlushCache();
	FromBinary.Close();

	DecompressedBinaryArray.Empty();
	DecompressedBinaryArray.Close();
	return true;
}

void URemeberUserName::SaveLoadData(FArchive& Ar)
{
	Ar << RemeberData;
}


