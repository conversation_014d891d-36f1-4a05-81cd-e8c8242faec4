// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/DesignStation.h"
#include "GameFramework/SaveGame.h"
#include "RemeberUserName.generated.h"

USTRUCT(BlueprintType)
struct FUserInfoRemebered
{
	GENERATED_USTRUCT_BODY()

	bool	RemeberMe;
	FString UserName;
	FString Password;

	FUserInfoRemebered() :RemeberMe(false), UserName(TEXT("")), Password(TEXT("")) {  }
	FUserInfoRemebered(bool InRemeberMe, const FString& InUserName, const FString& InPassword) :UserName(InUserName), Password(InPassword) {}

	friend FArchive& operator<<(FArchive& Ar, struct FUserInfoRemebered& InData)
	{
		Ar << InData.RemeberMe;
		Ar << InData.UserName;
		Ar << InData.Password;
		return Ar;
	}
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API URemeberUserName : public USaveGame
{
	GENERATED_BODY()
	
private:

	FUserInfoRemebered RemeberData;
	
	
public:

	GenerateGetRef(FUserInfoRemebered, RemeberData)
	GenerateSet(FUserInfoRemebered, RemeberData)

	static URemeberUserName* Get();

	bool SaveRemeberDataToFile(const FString& InFileFullPath);

	bool LoadRemeberDataFromFile(const FString& InFillFullPath);

private:

	void SaveLoadData(FArchive& Ar);
};
