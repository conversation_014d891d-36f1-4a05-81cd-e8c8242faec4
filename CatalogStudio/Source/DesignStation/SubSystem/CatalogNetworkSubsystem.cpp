// Fill out your copyright notice in the Description page of Project Settings.


#include "CatalogNetworkSubsystem.h"
#include "PakFileManagerSubsystem.h"
#include "WebCommunicationBPLibrary.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "DataCenter/RefFile/Data/Marco.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToPlaceDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/BasicClasses/JsonUtilitiesLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/UserRelated/UserInfoTableOperatorLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

DEFINE_LOG_CATEGORY(CatalogNetworkLog);

/*
 *  @@ 定义网络请求字段
 */
#define NET_MARK_STR_BACK_LOGIN							(TEXT("Login"))
#define	NET_MARK_STR_BACK_UPLOAD_FILE					(TEXT("UploadFile"))
#define	NET_MARK_STR_BACK_DOWNLOAD_FILE					(TEXT("DownloadFile"))
#define NET_MARK_STR_BACK_QUERY_RELEASE_LOG				(TEXT("QueryReleaseLog"))
#define NET_MARK_STR_BACK_MERGE_INSERT_LOG				(TEXT("MergeInsertLog"))
#define NET_MARK_STR_BACK_SEARCH_MERGE_LOG				(TEXT("SearchMergeLog"))
#define NET_MARK_STR_BACK_RELEASE						(TEXT("Release"))
#define NET_MARK_STR_BACK_RELEASE_INFOS					(TEXT("ReleaseInfos"))
#define NET_MARK_STR_BACK_RELEASE_SELECT				(TEXT("ReleaseSelect"))
#define NET_MARK_STR_BACK_RELEASE_STYLE_PARAMS			(TEXT("ReleaseStyleOrParams"))
#define NET_MARK_STR_BACK_RELEASE_DETAIL				(TEXT("ReleaseDetail"))
#define NET_MARK_STR_BACK_STYLE_CRAFT					(TEXT("StyleCraft"))

#define NET_MARK_STR_BACK_DIRECTORY_ADD					(TEXT("BackDirectoryAdd"))
#define NET_MARK_STR_BACK_DIRECTORY_DEL					(TEXT("BackDirectoryDel"))
#define NET_MARK_STR_BACK_DIRECTORY_UPDATE				(TEXT("BackDirectoryUpdate"))
#define NET_MARK_STR_BACK_DIRECTORY_SEARCH				(TEXT("BackDirectorySearch"))
#define NET_MARK_STR_BACK_DIRECTORY_SORT				(TEXT("BackDirectorySort"))
#define NET_MARK_STR_BACK_DIRECTORY_COPY				(TEXT("BackDirectoryCopy"))
#define NET_MARK_STR_BACK_DIRECTORY_CUT					(TEXT("BackDirectoryCut"))

#define NET_MARK_STR_BACK_DIRECTORY_OBSURCE_SEARCH		(TEXT("BackDirectoryObsurceSearch"))
#define NET_MARK_STR_BACK_DIRECTORY_QUERY_UPPERLEVEL	(TEXT("BackDirectoryQueryUpperLevel"))

#define NET_MARK_STR_FRONT_DIRECTORY_SEARCH				(TEXT("FrontDirectorySearch"))
#define NET_MARK_STR_FRONT_DIRECTORY_UPDATE				(TEXT("FrontDirectoryUpdate"))

#define NET_MARK_STR_CATEGORY_DETAIL_DATA				(TEXT("CategoryDetailData"))
#define NET_MARK_STR_CATEGORY_DETAIL_DATA_PARSE			(TEXT("CategoryDetailDataToParse"))
#define NET_MARK_STR_CATEGORY_DETAIL_DATA_ARR_PARSE		(TEXT("CategoryDetailDataArrayToParse"))
#define NET_MARK_STR_CATEGORY_UPDATE_FOLDER_ID			(TEXT("CategoryDetailDataUpdateFolderID"))

#define NET_MARK_STR_DOWNLOAD_URL						(TEXT("DownloadURLFile"))

#define NET_MARK_STR_ASSOCIATE_CREATE					(TEXT("AssociateCreate"))
#define NET_MARK_STR_ASSOCIATE_DELETE					(TEXT("AssociateDelete"))
#define NET_MARK_STR_ASSOCIATE_UPDATE					(TEXT("AssociateUpdate"))
#define NET_MARK_STR_ASSOCIATE_QUERY					(TEXT("AssociateQuery"))
#define NET_MARK_STR_ASSOCIATE_CATEGORY					(TEXT("AssociateCategory"))
#define NET_MARK_STR_ASSOCIATE_CATEGORY_SHOW			(TEXT("AssociateCategoryShow"))

#define NET_MARK_STR_PLACE_RULE_QUERY 					(TEXT("PlaceRuleQuery"))

 /*
  *  @@ 网络状态
  */
#define CATALOG_NET_STATE_OK 200

FString UCatalogNetworkSubsystem::SendLoginRequest(const FString& InUserName, const FString& InPassword)
{
	FString URL = FString::Printf(TEXT("%slogin/userLogin"), SERVER_URL);
	FString Content = FString::Printf(TEXT("{\"password\" : \"%s\" , \"userName\" : \"%s\"}"), *InPassword, *InUserName);
	FString UUID = FGuid::NewGuid().ToString();
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Content);
	Connet_PC_SendJsonStringNetworkRequest(URL, Content, NET_MARK_STR_BACK_LOGIN, UUID);
	return UUID;
}

void UCatalogNetworkSubsystem::LoginCheckResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(LogTemp, Warning, TEXT("%s"), *InJson);

	FUserInfoTableDataMsg UserInfo;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FUserInfoTableDataMsg>(InJson, UserInfo);
	AccessToken = UserInfo.resp.token;
	bool LoginSuccess = (NetworkStatus == CATALOG_NET_STATE_OK) && UserInfo.success;
	LoginResponseDelegate.Broadcast(InUUID, LoginSuccess, UserInfo.resp);
}

FString UCatalogNetworkSubsystem::NetLogoutRequest()
{
	AccessToken = TEXT("");
	return FString();
}

FString UCatalogNetworkSubsystem::SendUploadFileRequest(const FString& FilePath)
{
	return UploadFileInner(FilePath);

}

FString UCatalogNetworkSubsystem::SendDownloadFileRequest(const FString& FilePath)
{
	TArray<FString> FilesToDownload;
	FilesToDownload.Add(FilePath);
	return SendDownloadMultiFilesRequest(FilesToDownload);
}

FString UCatalogNetworkSubsystem::SendDownloadMultiFilesRequest(const TArray<FString>& FilePaths)
{
	FString UUID = FGuid::NewGuid().ToString().ToLower();
	bool Downloaded = false;
	for (auto& FilePath : FilePaths)
	{
		if (FilePath.Len() > 4)
		{
			Downloaded = true;
			DownloadFilesQueue.AddFile(UUID, FilePath);
			UE_LOG(LogTemp, Warning, TEXT("ACatalogPlayerController::DownloadMultiFilesRequest StartDownload @ %s"), *FilePath);
			DownloadFileInner(UUID, FilePath);
		}
	}
	return Downloaded ? UUID : TEXT("");
}

FString UCatalogNetworkSubsystem::SendQueryReleaseLogRequest(const int32& InPageNum, const int32& InPageSize, const int32& InType)
{
	FString uuid = FGuid::NewGuid().ToString().ToLower();
	const FString URL = FString::Printf(TEXT("%sapi/releaseLog/releaseLogQueryByPage"), SERVER_URL);
	//const FString Content = FString::Printf(TEXT("{\"page\":\"%d\",\"size\":\"%d\", \"releasetype\":\"%d\"}"), InPageNum, InPageSize, InType);
	const FString Content = FString::Printf(TEXT("{\"page\":\"%d\",\"size\":\"%d\"}"), InPageNum, InPageSize);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendQueryReleaseLogRequest --- URL [ %s ], Content [ %s ]"), *URL, *Content);
	Connet_PC_SendJsonStringNetworkRequest(
		URL,
		Content,
		NET_MARK_STR_BACK_QUERY_RELEASE_LOG,
		uuid
	);
	return uuid;
}

void UCatalogNetworkSubsystem::OnQueryReleaseLogResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	PARSE_NET_RESPONSE_RESP_NO_SUCCESS(FReleaseLogDataMsg, ReleaseResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::ReleaseRequest(const FString& InMd5, const int32& InType)
{
	return TEXT("");
}

void UCatalogNetworkSubsystem::OnReleaseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
}

FString UCatalogNetworkSubsystem::SendUserReleaseInfosRequest(const int32& PageIndex, const int32& OnePageNum)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("{\"pageNumber\" : %d, \"pageSize\" : %d, \"userId\" : \"%s\"}"), PageIndex, OnePageNum, *GlobalData.GetUserIDStr());
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/getListByUserId"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_RELEASE_INFOS,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::OnUserReleaseInfosResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("OnUserReleaseInfosResponseHandler"));

	//PARSE_NET_RESPONSE_RESP_MSG(FReleaseNetMsg, ReleaseInfosResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendBackDirectoryAddRequest(const FRefDirectoryData& InData)
{
	//FString RequestType = FString::Printf(TEXT("%s-%s"), NET_MARK_STR_BACK_DIRECTORY_ADD, *InData.id);
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"createdTime\" : \"%s\", \"createdBy\" : \"%s\", \"dirOrder\" : %d, \"folderCode\" : \"%s\", \"folderCodeExp\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"fontFolderPath\" : \"%s\", \"id\" : \"%s\", \"isFolder\" : %d, \"isNew\" : %d, \"md5\" : \"%s\", \"thumbnailPath\" : \"%s\", \"updatedBy\" : \"%s\", \"visibility\" : %d, \"visibilityExp\" : \"%s\", \"width\" : \"%s\", \"height\" : \"%s\", \"depth\" : \"%s\", \"boxOffset\" : \"%s\", \"refTypeCode\" : \"%s\", \"refType\" : \"%s\", \"description\" : \"%s\"}")
		, *InData.backendFolderPath, *InData.createdTime, *InData.createdBy, InData.dirOrder, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCode), *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCodeExp), *InData.folderId, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderName)
		, *InData.fontFolderPath, *InData.id, InData.isFolder, InData.isNew, *InData.md5, *InData.thumbnailPath, *InData.updatedBy, static_cast<int32>(InData.visibility), *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.visibilityExp), *InData.width, *InData.height, *InData.depth, *InData.boxOffset, *InData.RefTypeCode, *InData.RefType,*InData.description);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectoryAddRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/createdDirectory"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_ADD,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectoryAddResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectoryAddResponseDelegate"));

	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectoryAddResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendBackDirectoryDeleteRequest(const TArray<FString>& InIDs)
{
	FString RequestType = FGuid::NewGuid().ToString();
	FString JsonString = FString::Printf(TEXT("["));
	for (int i = 0; i < InIDs.Num(); ++i)
	{
		JsonString.Append(FString::Printf(TEXT("\"%s\""), *InIDs[i]));
		if (i < InIDs.Num() - 1)
		{
			JsonString.Append(TEXT(","));
		}
	}
	JsonString.Append(TEXT("]"));
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectoryDeleteRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/delDirectory"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_DEL,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectoryDeleteResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectoryDeleteResponseDelegate"));

	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectoryDeleteResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendBackDirectoryUpdateRequest(FRefDirectoryData InData)
{
	/*
	*  @@ update user info
	*/
	InData.updatedBy = GetUpdataUserInfo();

	//FString RequestType = FString::Printf(TEXT("%s-%s"), NET_MARK_STR_BACK_DIRECTORY_UPDATE, *InData.id);
	FString RequestType = FGuid::NewGuid().ToString();
	/*const FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"createdTime\" : \"%s\", \"createdBy\" : \"%s\", \"dirOrder\" : %d, \"folderCode\" : \"%s\", \"folderCodeExp\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"fontFolderPath\" : \"%s\", \"id\" : \"%s\", \"isFolder\" : %d, \"isNew\" : %d, \"md5\" : \"%s\", \"thumbnailPath\" : \"%s\", \"updatedBy\" : \"%s\", \"visibility\" : %d, \"visibilityExp\" : \"%s\"}")
		, *InData.backendFolderPath, *InData.createdTime, *InData.createdBy, InData.dirOrder, *InData.folderCode, *InData.folderCodeExp, *InData.folderId, *InData.folderName
		, *InData.fontFolderPath, *InData.id, InData.isFolder, InData.isNew, *InData.md5, *InData.thumbnailPath, *InData.updatedBy, static_cast<int32>(InData.visibility), *InData.visibilityExp);
	*/
	const FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"createdTime\" : \"%s\", \"createdBy\" : \"%s\", \"dirOrder\" : %d, \"folderCode\" : \"%s\", \"folderCodeExp\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"folderNameExp\" : \"%s\", \"fontFolderPath\" : \"%s\", \"id\" : \"%s\", \"isFolder\" : %d, \"isNew\" : %d, \"md5\" : \"%s\", \"thumbnailPath\" : \"%s\", \"updatedBy\" : \"%s\", \"visibility\" : %f, \"visibilityExp\" : \"%s\", \"isCut\" : %d, \"width\" : \"%s\", \"height\" : \"%s\", \"depth\" : \"%s\", \"boxOffset\" : \"%s\", \"placeRule\" : %d, \"refTypeCode\" : \"%s\", \"refType\" : \"%s\",\"description\" : \"%s\"}")
		, *InData.backendFolderPath, *InData.createdTime, *InData.createdBy, InData.dirOrder, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCode), *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCodeExp), *InData.folderId, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderName), *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderNameExp)
		, *InData.fontFolderPath, *InData.id, InData.isFolder, InData.isNew, *InData.md5, *InData.thumbnailPath, *InData.updatedBy, InData.visibility, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.visibilityExp), 1, *InData.width, *InData.height, *InData.depth, *InData.boxOffset, InData.placeRule, *InData.RefTypeCode, *InData.RefType, *InData.description);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectoryUpdateRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/editDirectory"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_UPDATE,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectoryUpdateResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectoryUpdateResponseDelegate -- Json : %s"), *InJson);
	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectoryUpdateResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendBackDirectorySearchRequest_FolderID(const FString& InStr)
{
	return SendBackDirectorySearchRequest(TEXT(""), TEXT(""), InStr, TEXT(""), TArray<FString>());
}

FString UCatalogNetworkSubsystem::SendBackDirectorySearchRequest_FolderCode(const FString& InStr)
{
	return SendBackDirectorySearchRequest(TEXT(""), InStr, TEXT(""), TEXT(""), TArray<FString>());
}

FString UCatalogNetworkSubsystem::SendBackDirectorySearchRequest_ID(const TArray<FString>& InID)
{
	return SendBackDirectorySearchRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), InID);
}

FString UCatalogNetworkSubsystem::SendBackDirectorySearchRequest(
	const FString& InBackFolderPath,
	const FString& InFodlerCode,
	const FString& InFolderID,
	const FString& InFodlerName,
	const TArray<FString>& IDs
)
{
	FString RequestType = FGuid::NewGuid().ToString();
	FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"folderCode\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"id\" : [")
		, *InBackFolderPath, *InFodlerCode, *InFolderID, *InFodlerName);
	if (IDs.Num() > 0)
	{
		for (int i = 0; i < IDs.Num(); ++i)
		{
			JsonString.Append(FString::Printf(TEXT("\"%s\""), *IDs[i]));
			if (i < IDs.Num() - 1)
			{
				JsonString.Append(TEXT(","));
			}
		}

	}
	JsonString.Append(TEXT("]}"));

	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectorySearchRequest --- %s"), *JsonString);

	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/getDirectoryList"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_SEARCH,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectorySearchResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectorySearchResponseDelegate"));

	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectorySearchResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendBackDirectorySortRequest(const FString& ID1, const FString& ID2)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("[\"%s\", \"%s\"]"), *ID1, *ID2);

	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectorySortRequest --- %s"), *JsonString);

	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/editDirectorySort"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_SORT,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectorySortResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectorySortResponseDelegate"));
	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectorySortResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendBackDirectoryCutRequest(const FRefDirectoryData& InData)
{
	FString RequestType = FGuid::NewGuid().ToString();
	/*const FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"createdTime\" : \"%s\", \"createdBy\" : \"%s\", \"dirOrder\" : %d, \"folderCode\" : \"%s\", \"folderCodeExp\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"fontFolderPath\" : \"%s\", \"id\" : \"%s\", \"isFolder\" : %d, \"isNew\" : %d, \"md5\" : \"%s\", \"thumbnailPath\" : \"%s\", \"updatedBy\" : \"%s\", \"visibility\" : %d, \"visibilityExp\" : \"%s\"}")
		, *InData.backendFolderPath, *InData.createdTime, *InData.createdBy, InData.dirOrder, *InData.folderCode, *InData.folderCodeExp, *InData.folderId, *InData.folderName
		, *InData.fontFolderPath, *InData.id, InData.isFolder, InData.isNew, *InData.md5, *InData.thumbnailPath, *InData.updatedBy, static_cast<int32>(InData.visibility), *InData.visibilityExp);
	*/
	const FString JsonString = FString::Printf(TEXT("{\"backendFolderPath\" : \"%s\" , \"createdTime\" : \"%s\", \"createdBy\" : \"%s\", \"dirOrder\" : %d, \"folderCode\" : \"%s\", \"folderCodeExp\" : \"%s\", \"folderId\" : \"%s\", \"folderName\" : \"%s\", \"fontFolderPath\" : \"%s\", \"id\" : \"%s\", \"isFolder\" : %d, \"isNew\" : %d, \"md5\" : \"%s\", \"thumbnailPath\" : \"%s\", \"updatedBy\" : \"%s\", \"visibility\" : %f, \"visibilityExp\" : \"%s\", \"isCut\" : %d, \"placeRule\" : %d, \"refTypeCode\" : \"%s\", \"refType\" : \"%s\", \"description\" : \"%s\"}")
		, *InData.backendFolderPath, *InData.createdTime, *InData.createdBy, InData.dirOrder, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCode), *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderCodeExp), *InData.folderId, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.folderName)
		, *InData.fontFolderPath, *InData.id, InData.isFolder, InData.isNew, *InData.md5, *InData.thumbnailPath, *InData.updatedBy, InData.visibility, *UCatalogNetworkSubsystem::FormatNetJsonStr(InData.visibilityExp), 0, InData.placeRule, *InData.RefTypeCode, *InData.RefType,*InData.description);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectoryCutRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/editDirectory"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_CUT,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectoryCutResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectoryCutResponseDelegate"));

	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectoryCutResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendBackDirectoryCopyRequest(const FString& OriginID, const FString& TargetID)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("{\"oriId\" : \"%s\" , \"targetId\" : \"%s\"}"), *OriginID, *TargetID);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendBackDirectoryCopyRequest --- %s"), *JsonString);

	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/copyDirectory"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_DIRECTORY_COPY,
		RequestType);
	return RequestType;
}

void UCatalogNetworkSubsystem::BackDirectoryCopyResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("BackDirectoryCopyResponseDelegate"));
	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackDirectoryCopyResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendFrontDirectorySearchRequest(const FString& DictGroupValue, const FString& DictValue, const int64& PID, const int32& Level)
{
	FString RequestType = FGuid::NewGuid().ToString();
	FString JsonString = FString::Printf(TEXT("{\"dictGroupValue\" : \"%s\" , \"dictValue\" : \"%s\""), *DictGroupValue, *DictValue);
	if (PID != INDEX_NONE)
	{
		FString PIDStr = FString::Printf(TEXT(",\"pid\" : %lld"), PID);
		JsonString.Append(PIDStr);
	}
	if (Level != INDEX_NONE)
	{
		FString LevelStr = FString::Printf(TEXT(",\"level\" : %d"), Level);
		JsonString.Append(LevelStr);
	}
	JsonString.Append(TEXT("}"));
	UE_LOG(CatalogNetworkLog, Warning, TEXT("SendFrontDirectorySearchRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/getCateList"), SERVER_URL),
		JsonString,
		NET_MARK_STR_FRONT_DIRECTORY_SEARCH,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::FrontDirectorySearchResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	/*UE_LOG(CatalogNetworkLog, Log, TEXT("FrontDirectorySearchResponseDelegate --- json str : %s"), *InJson);
	FFrontDirectoryNetMsg NetMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FFrontDirectoryNetMsg>(InJson, NetMsg);
	const bool NetSuccess = (NetworkStatus == CATALOG_NET_STATE_OK) && NetMsg.success;
	UE_LOG(CatalogNetworkLog, Log, TEXT("FrontDirectorySearchResponseDelegate --- success : %d"), NetSuccess);
	FrontDirectorySearchResponseDelegate.Broadcast(InUUID, NetSuccess, NetMsg.resp);*/

	PARSE_FRONT_DIRECTORY_NET_RESPONSE(FrontDirectorySearchResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendFurnitureOrMaterialSearchRequest(const int32& CategoryId, const int32& PageNum, const int32& PageSize, const int32& InType,const FString& QueryParams)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(
		TEXT("{\"cateId\" : %d , \"pageNumber\" : %d, \"pageSize\" : %d, \"type\" : %d,\"qryParam\":\"%s\"}")
		, CategoryId, PageNum, PageSize, InType,*QueryParams);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendFurnitureOrMaterialSearchRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/getdateByCate"), SERVER_URL),
		JsonString,
		NET_MARK_STR_CATEGORY_DETAIL_DATA,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::FurnitureOrMaterialSearchResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("FurnitureOrMaterialSearchResponseHandler --- json str : %s"), *InJson);
	PARSE_NET_RESPONSE_RESP_MSG(FModelMatItemNetMsg, CategoryDataResponseDelegate, InUUID, NetworkStatus, InJson);

	/*FModelMatItemNetMsg NetMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FModelMatItemNetMsg>(InJson, NetMsg);
	const bool NetSuccess = (NetworkStatus == CATALOG_NET_STATE_OK) && NetMsg.success;
	UE_LOG(CatalogNetworkLog, Log, TEXT("FurnitureOrMaterialSearchResponseHandler --- success : %d"), NetSuccess);
	CategoryDataResponseDelegate.Broadcast(InUUID, NetSuccess, NetMsg.resp);*/
}

FString UCatalogNetworkSubsystem::SendFurnitureOrMaterialDataForParseSearchRequest(const int32& Id)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sapi/bkDirectory/infoManage"), SERVER_URL);
	const FString GetURL = GetInterface + TEXT("?") + FString::Printf(TEXT("id=%d"), Id);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendFurnitureOrMaterialDataForParseSearchRequest --- GetURL : %s"), *GetURL);
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_CATEGORY_DETAIL_DATA_PARSE,
		RequestType
	);
	return RequestType;
}

FString UCatalogNetworkSubsystem::SendFurnitureOrMaterialDataForParseSearchRequest_FolderID(const FString& InStr)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sapi/bkDirectory/infoManage"), SERVER_URL);
	const FString GetURL = GetInterface + TEXT("?") + FString::Printf(TEXT("folderId=%s"), *InStr);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendFurnitureOrMaterialDataForParseSearchRequest_FolderID --- GetURL : %s"), *GetURL);
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_CATEGORY_DETAIL_DATA_PARSE,
		RequestType
	);
	return RequestType;
}

FString UCatalogNetworkSubsystem::SendFurnitureOrMaterialDataForParseSearchRequest_FolderCode(const FString& InStr)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sapi/bkDirectory/infoManage"), SERVER_URL);
	const FString GetURL = GetInterface + TEXT("?") + FString::Printf(TEXT("folderCode=%s"), *InStr);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendFurnitureOrMaterialDataForParseSearchRequest_FolderCode --- GetURL : %s"), *GetURL);
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_CATEGORY_DETAIL_DATA_PARSE,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::FurnitureOrMaterialDataForParseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("FurnitureOrMaterialDataForParseResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FCSModelMatDataNetMsg, CategoryDataForParseResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendGetFurnitureOrMaterialDataForParseRequest(const TArray<FString>& StrArr, const int32& InType)
{
	FString UUID = FGuid::NewGuid().ToString();
	FString JsonString = TEXT("{\"codeOrIdList\": [");
	for (int i = 0; i < StrArr.Num(); ++i)
	{
		JsonString.Append(FString::Printf(TEXT("\"%s\""), *StrArr[i]));
		if (i < StrArr.Num() - 1)
		{
			JsonString.Append(TEXT(","));
		}
	}
	JsonString.Append(FString::Printf(TEXT("], \"type\":%d}"), InType));
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendGetFurnitureOrMaterialDataForParseRequest --- %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(FString::Printf(TEXT("%sapi/bkDirectory/infoManageList"), SERVER_URL),
		JsonString,
		NET_MARK_STR_CATEGORY_DETAIL_DATA_ARR_PARSE,
		UUID
	);
	return UUID;
}

void UCatalogNetworkSubsystem::GetFurnitureOrMaterialDataForParseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("GetFurnitureOrMaterialDataForParseResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FCSModelMatArrDataNetMsg, CategoryDataArrForParseResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendQueryReleaseListRequest(const FString& UseId, const int32& PageIndex, const int32& PageSize)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("{\"pageNumber\" : %d, \"pageSize\" : %d, \"userId\" : \"%s\"}"), PageIndex, PageSize, *UseId);
	UE_LOG(CatalogNetworkLog, Log, TEXT("QueryReleaseList --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/getListByUserId"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_RELEASE_INFOS,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::QueryReleaseListResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	PARSE_NET_RESPONSE_CONTENT_RESP_MSG(FReleaseNetMsg, ReleaseInfosResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendReleaseRequest(const TArray<FString>& IDs)
{
	FString RequestType = FGuid::NewGuid().ToString();
	int32 CurrentVersion = 0;
	FString JsonString = TEXT("[");
	for (const auto ID : IDs)
	{
		/*
		*  @@ remove version
		*  @@ TODO : add version logic
		*/
		//FString Temp = FString::Printf(TEXT("{\"id\": \"%s\", \"version\": %d}"), *ID, CurrentVersion);
		FString Temp = FString::Printf(TEXT("{\"id\": \"%s\"}"), *ID);
		JsonString.Append(Temp);

		if (!ID.Equals(IDs.Last()))
		{
			JsonString.Append(TEXT(","));
		}

	}
	JsonString.Append(TEXT("]"));

	UE_LOG(CatalogNetworkLog, Log, TEXT("SendReleaseRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/releaseFile"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_RELEASE_SELECT,
		RequestType
	);
	return RequestType;
}

FString UCatalogNetworkSubsystem::SendReleaseStyleOrParameterRequest(const int32& InType)
{
	const FString RelativePath = InType == 1 ? URefToStyleDataLibrary::GetStyleRelativeAddress()
		: InType == 2 ? URefToParamDataLibrary::GetRelativePath()
		: TEXT("");
	if (!RelativePath.IsEmpty())
	{
		const FString UUID = FGuid::NewGuid().ToString();
		const FString PercentURLParam = FString::Printf(TEXT("?type=%d&url=%s"), InType, *RelativePath);
		const FString URLRequest = FString::Printf(TEXT("%sapi/bkDirectory/releaseStyleVarFile%s"), SERVER_URL, *PercentURLParam);
		UE_LOG(CatalogNetworkLog, Log, TEXT("SendReleaseRequest --- json str : %s"), *URLRequest);
		Connet_PC_SendJsonStringNetworkRequest(
			URLRequest,
			RelativePath,
			NET_MARK_STR_BACK_RELEASE_STYLE_PARAMS,
			UUID
		);

		return UUID;
	}
	return TEXT("");
}

void UCatalogNetworkSubsystem::ReleaseStyleParamsResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	PARSE_NET_RESPONSE_MSG(FCatalogNormalNetMsg, ReleaseStyleParamsResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendGetReleaseDetailInfoRequest(const int32& MainID)
{
	const FString UUID = FGuid::NewGuid().ToString();
	const FString PercentURLParam = FString::Printf(TEXT("?mainId=%d"), MainID);
	const FString URLRequest = FString::Printf(TEXT("%sapi/releaseLog/getReleaseLogDetail%s"), SERVER_URL, *PercentURLParam);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendGetReleaseDetailInfoRequest --- json str : %s"), *URLRequest);
	Connet_PC_SendJsonNetworkRequest(
		URLRequest,
		TArray<uint8>(),
		NET_MARK_STR_BACK_RELEASE_DETAIL,
		UUID
	);
	return UUID;
}

void UCatalogNetworkSubsystem::GetReleaseDetailInfoResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("GetReleaseDetailInfoResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FReleaseDetailNetMsg, ReleaseDetailResponseDelegate, InUUID, NetworkStatus, InJson);
}

void UCatalogNetworkSubsystem::ReleaseResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	PARSE_NET_RESPONSE_CONTENT_RESP_MSG(FReleaseNetMsg, ReleaseSelectResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendUpdateFolderIdCodeForWebRequest(const FString& InFolderId, const FString& InFolderCode, const int32& Id)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString JsonString = FString::Printf(TEXT("{\"folderCode\" : \"%s\" , \"folderId\" : \"%s\" , \"id\" : %d}"), *InFolderCode, *InFolderId, Id);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendUpdateFolderIdCodeForWebRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/bkDirectory/updateBk"), SERVER_URL),
		JsonString,
		NET_MARK_STR_CATEGORY_UPDATE_FOLDER_ID,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::UpdateFolderIdForWebResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("UpdateFolderIdForWebResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FModelMatSingleNetMsg, UpdateFolderIdForWebResponseDelegate, InUUID, NetworkStatus, InJson);
}

void UCatalogNetworkSubsystem::SendDownloadURLInnerRequest(const FString& UUID, const FString& InURL, const int32& InType, const FString& FilePath)
{
	const float CurrentTime = UKismetSystemLibrary::GetGameTimeInSeconds(this);
	for (int i = 0; i < MAX_DOWNLOAD_UPLOAD_THREAD; ++i)
	{
		if (DownloadThreadInfo[i].IsValid())
		{
			const FString FileExtention = FPaths::GetExtension(DownloadThreadInfo[i]->FilePath);
			const float Timeout = FileExtention.Equals(TEXT("pak"), ESearchCase::IgnoreCase) ? 40.0f : 10.0f;//PAK文件较大下载用时较久
			if (CurrentTime - DownloadThreadInfo[i]->StartTime > Timeout)
			{
				TArray<FString> Headers;
				Headers.Add(FString::Printf(TEXT("uuid:%s"), *DownloadThreadInfo[i]->FilePath));
				Connet_PC_NetworkRequestCompleted(TArray<FString>(), Headers, 0, TArray<uint8>(), FString::Printf(TEXT("%s-%s"), NET_MARK_STR_DOWNLOAD_URL, *DownloadThreadInfo[i]->FilePath));
			}
		}
		if (false == DownloadThreadInfo[i].IsValid())
		{
			if (FilePath.IsEmpty()) return;
			FString RequestType = FString::Printf(TEXT("%s-%s"), NET_MARK_STR_DOWNLOAD_URL, *FilePath);
			FString PercentURLParam = FString::Printf(TEXT("?fileUrl=%s&type=%d"), *InURL, InType);
			FString FileABPath = FPaths::ProjectContentDir() + FilePath;
			FileABPath = FPaths::ConvertRelativePathToFull(FileABPath);
			FString OSSURLRequest = FString::Printf(TEXT("%sapi/bkDirectory/downloadFile%s"), SERVER_URL, *PercentURLParam);
			UE_LOG(LogTemp, Log, TEXT("OSSURLRequest [%s]"), *OSSURLRequest);
			Connet_PC_DownloadFileFromServer_Get(OSSURLRequest, FileABPath, RequestType, UUID, FilePath);
			DownloadThreadInfo[i] = MakeShared<FDownloadUploadThreadInfo>(UUID, FilePath);
			DownloadThreadInfo[i]->StartTime = CurrentTime;
			DownloadFilesQueue.Update(UUID, DownloadThreadInfo[i]->FilePath, NFileNetworkOpertator::EDownloadState::Downloading);
			return;
		}
	}
}

FString UCatalogNetworkSubsystem::SendDownloadURLRequest(const FString& InURL, const int32& InType, const FString& DownloadPath)
{
	return SendMultiDownloadURLRequest({ InURL }, InType, { DownloadPath });
}

FString UCatalogNetworkSubsystem::SendMultiDownloadURLRequest(const TArray<FString>& InURL, const int32& InType, const TArray<FString>& DownloadPath)
{
	FString UUID = FGuid::NewGuid().ToString().ToLower();
	bool Downloaded = false;
	for (int32 i = 0; i < InURL.Num(); ++i)
	{
		Downloaded = true;
		if (!DownloadPath.IsValidIndex(i))
			return Downloaded ? UUID : TEXT("");
		DownloadFilesQueue.AddFile(UUID, DownloadPath[i]);
		UE_LOG(LogTemp, Warning, TEXT("ACatalogPlayerController::SendMultiDownloadURLRequest StartDownload [URL : %s][Type : %d][Path : %s]"), *InURL[i], InType, *DownloadPath[i]);
		SendDownloadURLInnerRequest(UUID, InURL[i], InType, DownloadPath[i]);
	}
	return Downloaded ? UUID : TEXT("");
}

void UCatalogNetworkSubsystem::DownloadURLResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
}

void UCatalogNetworkSubsystem::InitStyleCraft()
{
	SendQueryStyleCraftRequest();
}

FString UCatalogNetworkSubsystem::SendQueryStyleCraftRequest()
{
#define STYLE_CRAFT_DICT_GEROUP_MARK TEXT("FGGY")

	const FString UUID = FGuid::NewGuid().ToString();

	const FString JsonString = FString::Printf(TEXT("{\"dictGroupValue\" : \"%s\" , \"qryParam\" : \"%s\"}"), STYLE_CRAFT_DICT_GEROUP_MARK, TEXT(""));
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendQueryStyleCraftRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sapi/dict/dictDtlList"), SERVER_URL),
		JsonString,
		NET_MARK_STR_BACK_STYLE_CRAFT,
		UUID
	);

	return UUID;

#undef STYLE_CRAFT_DICT_GEROUP_MARK
}

void UCatalogNetworkSubsystem::QueryStyleCraftResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("QueryStyleCraftResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_JSON_DATA(FStyleCraftNetMsg, InJson);
	StyleCraft.Empty();
	for (const auto NM : NetMsg.resp)
	{
		if (!NM.dictName.IsEmpty())
		{
			StyleCraft.AddUnique(NM.dictName);
		}
	}

	//PARSE_NET_RESPONSE_RESP_MSG(FStyleCraftNetMsg, QueryStyleCraftResponseDelegate, InUUID, NetworkStatus, InJson);
}

//std::string url_encode(const std::string& value) {
//	std::ostringstream escaped;
//	escaped.fill('0');
//	escaped << std::hex;
//
//	for (std::string::const_iterator i = value.begin(), n = value.end(); i != n; ++i) {
//		std::string::value_type c = (*i);
//
//		// Keep alphanumeric and other accepted characters intact
//		if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
//			escaped << c;
//			continue;
//		}
//
//		// Any other characters are percent-encoded
//		escaped << std::uppercase;
//		escaped << '%' << std::setw(2) << int((unsigned char)c);
//		escaped << std::nouppercase;
//	}
//
//	return escaped.str();
//}

FString UCatalogNetworkSubsystem::SendObscureSearchRequest(const FString& InObscureStr, const FString& SelectFolderPath)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sapi/bkDirectory/getVagueList"), SERVER_URL);
	//encode param
	FString URLEncode = UWebCommunicationBPLibrary::urlEncode(InObscureStr);
	FString GetURL = GetInterface + TEXT("?") + FString::Printf(TEXT("qryParam=%s"), *URLEncode);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendObscureSearchRequest --- GetURL Encode : %s"), *GetURL);

	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_BACK_DIRECTORY_OBSURCE_SEARCH,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::ObscureSearchResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("ObscureSearchResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FBackDirectoryNetMsg, BackendDirectoryObscureSearchResponseDelegate, InUUID, NetworkStatus, InJson);
}

FString UCatalogNetworkSubsystem::SendQueryUpperLevelContainsRequest(const FString& SelectBackendPath)
{
	return TEXT("");
}

void UCatalogNetworkSubsystem::QueryUpperLevelContainsResponseHandler(const FString& InUUID, int32 NetworkStatus,
	void* pData, const FString& InJson)
{
}

FString UCatalogNetworkSubsystem::SendAssociateCreateRequest(const TArray<FRefAssociateData>& Datas)
{
	const FString UUID = FGuid::NewGuid().ToString();
	const FString JsonString = CSStructArrToJson<FRefAssociateData>(Datas);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateCreateRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sbkAssociation/createdAssociation"), SERVER_URL),
		JsonString,
		NET_MARK_STR_ASSOCIATE_CREATE,
		UUID
	);

	return UUID;
}

void UCatalogNetworkSubsystem::AssociateCreateResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateCreateResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FRefAssociateData_NetMsg, AssociateCreateResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendAssociateDeleteRequest(const TArray<int64>& IDS)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sbkAssociation/delAssociation"), SERVER_URL);
	FString GetURL = GetInterface + TEXT("?");
	GetURL.Append(FString::Printf(TEXT("ids=%lld"), IDS[0]));
	for(int32 i = 1; i < IDS.Num(); ++i)
	{
		GetURL.Append(FString::Printf(TEXT(",%lld"), IDS[i]));
	}
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateDeleteRequest --- GetURL : %s"), *GetURL);
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_ASSOCIATE_DELETE,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::AssociateDeleteResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateDeleteResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_MSG(FCatalogNormalNetMsg, AssociateDelResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendAssociateUpdateRequest(const FRefAssociateData& Data)
{
	const FString UUID = FGuid::NewGuid().ToString();
	/*FString JsonString;
	FJsonObjectConverter::UStructToJsonObjectString<FRefAssociateData>(Data, JsonString);*/
	FString JsonString = FormatJsonStrForAssociateData<FRefAssociateData>(Data);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateUpdateRequest --- json str : %s"), *JsonString);
	/*FormatJsonStrID(JsonString);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateUpdateRequest --- json str2: %s"), *JsonString);*/
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sbkAssociation/updatedAssociation"), SERVER_URL),
		JsonString,
		NET_MARK_STR_ASSOCIATE_UPDATE,
		UUID
	);

	return UUID;
}

void UCatalogNetworkSubsystem::AssociateUpdateResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateUpdateResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FRefAssociateData_NetMsg, AssociateUpdateResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendAssociateQueryRequest(const FString& InQueryID)
{
	FString RequestType = FGuid::NewGuid().ToString();
	const FString GetInterface = FString::Printf(TEXT("%sbkAssociation/qryList"), SERVER_URL);
	FString GetURL = GetInterface + TEXT("?") + FString::Printf(TEXT("id=%s"), *InQueryID);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateQueryRequest --- GetURL : %s"), *GetURL);
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_ASSOCIATE_QUERY,
		RequestType
	);
	return RequestType;
}

void UCatalogNetworkSubsystem::AssociateQueryResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateQueryResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FAssociateListData_NetMsg, AssociateQueryResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendAssociateCategoryRequest(const FString& InStr, const FString& InBkID)
{
	const FString UUID = FGuid::NewGuid().ToString();
	FString JsonString = FString::Printf(TEXT("{\"correlationType\" : \"%s\",\"bkId\" : \"%s\"}"), *InStr, *InBkID);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateCategoryRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sbkAssociation/qryCateList"), SERVER_URL),
		JsonString,
		NET_MARK_STR_ASSOCIATE_CATEGORY,
		UUID
	);

	return UUID;
}

void UCatalogNetworkSubsystem::AssociateCategoryResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateCategoryResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FAssociateCategoryListData_NetMsg, AssociateCategoryResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendAssociateCategoryShowDataRequest(const int64& InCategoryID, const int32& InType,
	const int32& InPageNumber, const int32& InPageSize, const FString& InBkID)
{
	const FString UUID = FGuid::NewGuid().ToString();
	FString JsonString = FString::Printf(TEXT("{\"cateId\" : \"%lld\",\"pageNumber\" : \"%d\",\"pageSize\" : \"%d\",\"type\" : \"%d\",\"bkId\" : \"%s\"}"), InCategoryID, InPageNumber, InPageSize, InType, *InBkID);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendAssociateCategoryShowDataRequest --- json str : %s"), *JsonString);
	Connet_PC_SendJsonStringNetworkRequest(
		FString::Printf(TEXT("%sbkAssociation/getByCate"), SERVER_URL),
		JsonString,
		NET_MARK_STR_ASSOCIATE_CATEGORY_SHOW,
		UUID
	);

	return UUID;
}

void UCatalogNetworkSubsystem::AssociateCategoryShowDataResponseHandler(const FString& InUUID, int32 NetworkStatus,
	void* pData, const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("AssociateCategoryShowDataResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FAssociateShowData_NetMsg, AssociateCategoryShowDataResponseDelegate, InUUID, NetworkStatus, InJson);

}

FString UCatalogNetworkSubsystem::SendQueryRefPlaceRuleRequest()
{
	const FString UUID = FGuid::NewGuid().ToString();
	const FString GetURL = FString::Printf(TEXT("%sapi/bkAdsorption/qryList"), SERVER_URL);
	UE_LOG(CatalogNetworkLog, Log, TEXT("SendQueryRefPlaceRuleRequest"));
	Connet_PC_SendJsonNetworkRequest(
		GetURL,
		TArray<uint8>(),
		NET_MARK_STR_PLACE_RULE_QUERY,
		UUID
	);
	return UUID;
}

void UCatalogNetworkSubsystem::QueryRefPlaceRuleResponseHandler(const FString& InUUID, int32 NetworkStatus, void* pData,
	const FString& InJson)
{
	UE_LOG(CatalogNetworkLog, Log, TEXT("QueryRefPlaceRuleResponseHandler --- json str : %s"), *InJson);

	PARSE_NET_RESPONSE_RESP_MSG(FRefPlaceDataNetMsg, RefPlaceRuleResponseDelegate, InUUID, NetworkStatus, InJson);
}

void UCatalogNetworkSubsystem::InitSystem()
{
	AccessToken = TEXT("");

	// 初始化网络请求类型
	RequestTypeMap.Add(NET_MARK_STR_BACK_LOGIN, ECatalogNetRequestType::E_Back_Login);
	RequestTypeMap.Add(NET_MARK_STR_BACK_UPLOAD_FILE, ECatalogNetRequestType::E_Back_UploadFile);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DOWNLOAD_FILE, ECatalogNetRequestType::E_Back_DownloadFile);
	RequestTypeMap.Add(NET_MARK_STR_BACK_QUERY_RELEASE_LOG, ECatalogNetRequestType::E_Back_QueryReleaseLog);
	RequestTypeMap.Add(NET_MARK_STR_BACK_MERGE_INSERT_LOG, ECatalogNetRequestType::E_Back_MergeInsertLog);
	RequestTypeMap.Add(NET_MARK_STR_BACK_SEARCH_MERGE_LOG, ECatalogNetRequestType::E_Back_SearchMergeLog);
	RequestTypeMap.Add(NET_MARK_STR_BACK_RELEASE, ECatalogNetRequestType::E_Back_Release);
	RequestTypeMap.Add(NET_MARK_STR_BACK_RELEASE_INFOS, ECatalogNetRequestType::E_Back_Release_Info);
	RequestTypeMap.Add(NET_MARK_STR_BACK_RELEASE_SELECT, ECatalogNetRequestType::E_Back_UserRelease);
	RequestTypeMap.Add(NET_MARK_STR_BACK_RELEASE_STYLE_PARAMS, ECatalogNetRequestType::E_Back_ReleaseStyleOrParams);
	RequestTypeMap.Add(NET_MARK_STR_BACK_RELEASE_DETAIL, ECatalogNetRequestType::E_Back_ReleaseDetail);
	RequestTypeMap.Add(NET_MARK_STR_BACK_STYLE_CRAFT, ECatalogNetRequestType::E_Back_StyleCraft);

	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_ADD, ECatalogNetRequestType::E_Back_Add);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_DEL, ECatalogNetRequestType::E_Back_Del);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_UPDATE, ECatalogNetRequestType::E_Back_Update);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_SEARCH, ECatalogNetRequestType::E_Back_Search);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_SORT, ECatalogNetRequestType::E_Back_Sort);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_COPY, ECatalogNetRequestType::E_Back_Copy);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_CUT, ECatalogNetRequestType::E_Back_Cut);

	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_OBSURCE_SEARCH, ECatalogNetRequestType::E_Back_Obsurce_Search);
	RequestTypeMap.Add(NET_MARK_STR_BACK_DIRECTORY_QUERY_UPPERLEVEL, ECatalogNetRequestType::E_Back_Query_UpperLevel);

	RequestTypeMap.Add(NET_MARK_STR_FRONT_DIRECTORY_SEARCH, ECatalogNetRequestType::E_Front_Search);
	RequestTypeMap.Add(NET_MARK_STR_FRONT_DIRECTORY_UPDATE, ECatalogNetRequestType::E_Front_Update);

	RequestTypeMap.Add(NET_MARK_STR_CATEGORY_DETAIL_DATA, ECatalogNetRequestType::E_Front_Category_Data);
	RequestTypeMap.Add(NET_MARK_STR_CATEGORY_DETAIL_DATA_PARSE, ECatalogNetRequestType::E_Front_Category_Data_For_Parse);
	RequestTypeMap.Add(NET_MARK_STR_CATEGORY_DETAIL_DATA_ARR_PARSE, ECatalogNetRequestType::E_Front_Category_Data_Arr_For_Parse);
	RequestTypeMap.Add(NET_MARK_STR_CATEGORY_UPDATE_FOLDER_ID, ECatalogNetRequestType::E_Front_Update_FolderId);

	RequestTypeMap.Add(NET_MARK_STR_DOWNLOAD_URL, ECatalogNetRequestType::E_Download_URL);

	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_CREATE, ECatalogNetRequestType::E_Associate_Create);
	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_DELETE, ECatalogNetRequestType::E_Associate_Delete);
	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_UPDATE, ECatalogNetRequestType::E_Associate_Update);
	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_QUERY, ECatalogNetRequestType::E_Associate_Query);
	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_CATEGORY, ECatalogNetRequestType::E_Associate_Category);
	RequestTypeMap.Add(NET_MARK_STR_ASSOCIATE_CATEGORY_SHOW, ECatalogNetRequestType::E_Associate_Category_ShowData);

	RequestTypeMap.Add(NET_MARK_STR_PLACE_RULE_QUERY, ECatalogNetRequestType::E_Place_Rule_Query);
}

void UCatalogNetworkSubsystem::DeinitSystem()
{
	RequestTypeMap.Empty();
}

FString UCatalogNetworkSubsystem::UploadFileInner(const FString& FilePath)
{
	FString FileMD5 = TEXT("");
	int64 FileSize = 0;
	const FString FileABPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FilePath));
	{
		const bool bValid = ACatalogPlayerController::GetFileMD5AndSize(FileABPath, FileMD5, FileSize);
		if (!bValid) return TEXT("");
	}
	const FString RequestType = FString::Printf(TEXT("%s-%s"), NET_MARK_STR_BACK_UPLOAD_FILE, *FilePath);
	Connet_PC_UploadFileToServer(FString::Printf(TEXT("%sapi/file/upload?md5=%s&path=%s&size=%lld"), SERVER_URL, *FileMD5, *FPaths::GetPath(FilePath), FileSize), FileABPath, RequestType, FilePath);
	return FilePath;
}

bool UCatalogNetworkSubsystem::DownloadFileInner(const FString& UUID, const FString& FilePath)
{
	const float CurrentTime = UKismetSystemLibrary::GetGameTimeInSeconds(this);
	for (int i = 0; i < MAX_DOWNLOAD_UPLOAD_THREAD; ++i)
	{
		if (DownloadThreadInfo[i].IsValid())
		{
			const FString FileExtention = FPaths::GetExtension(DownloadThreadInfo[i]->FilePath);
			const float Timeout = FileExtention.Equals(TEXT("pak"), ESearchCase::IgnoreCase) ? 40.0f : 10.0f;//PAK文件较大下载用时较久
			if (CurrentTime - DownloadThreadInfo[i]->StartTime > Timeout)
			{
				TArray<FString> Headers;
				Headers.Add(FString::Printf(TEXT("uuid:%s"), *DownloadThreadInfo[i]->FilePath));
				Connet_PC_NetworkRequestCompleted(TArray<FString>(), Headers, 0, TArray<uint8>(), FString::Printf(TEXT("%s-%s"), NET_MARK_STR_BACK_DOWNLOAD_FILE, *DownloadThreadInfo[i]->FilePath));
			}
		}
		if (false == DownloadThreadInfo[i].IsValid())
		{
			if (FilePath.IsEmpty()) return TEXT("");
			FString RequestType = FString::Printf(TEXT("%s-%s"), NET_MARK_STR_BACK_DOWNLOAD_FILE, *FilePath);
			FString PercentURL = FString::Printf(TEXT("{\"filePath\": \"%s\"}"), *FilePath);
			//FString PercentURL = UJsonUtilitiesLibrary::ConvertToPercentURL(FilePath);
			FString FileABPath = FPaths::ProjectContentDir() + FilePath;
			FileABPath = FPaths::ConvertRelativePathToFull(FileABPath);
			Connet_PC_DownloadFileFromServer(FString::Printf(TEXT("%sapi/file/download"), SERVER_URL), FileABPath, RequestType, UUID, PercentURL);
			DownloadThreadInfo[i] = MakeShared<FDownloadUploadThreadInfo>(UUID, FilePath);
			DownloadThreadInfo[i]->StartTime = CurrentTime;
			DownloadFilesQueue.Update(UUID, DownloadThreadInfo[i]->FilePath, NFileNetworkOpertator::EDownloadState::Downloading);
			return true;
		}
	}
	return false;
}

FString UCatalogNetworkSubsystem::FormatNetJsonStr(const FString& Str)
{
	FString Result = Str;
	Result = Result.Replace(TEXT("\""), TEXT("\\\""));
	return Result;
}

FString UCatalogNetworkSubsystem::FormatShowJsonStr(const FString& Str)
{
	FString Result = Str;
	Result = Result.Replace(TEXT("\\\""), TEXT("\""));
	return Result;
}

void UCatalogNetworkSubsystem::FormatJsonStrID(FString& JsonStr)
{
	JsonStr.ReplaceInline(TEXT("iD"), TEXT("id"), ESearchCase::CaseSensitive);
	JsonStr.ReplaceInline(TEXT("ID"), TEXT("id"), ESearchCase::CaseSensitive);
	JsonStr.ReplaceInline(TEXT("Id"), TEXT("id"), ESearchCase::CaseSensitive);
}

//FString UCatalogNetworkSubsystem::FormatJsonStrForAssociateData(const FRefAssociateData& InData)
//{
//	FString JsonString = FString::Printf(TEXT("{\"id\" : %lld,\"name\" : \"%s\",\"meetCondition\" : \"%s\",\"type\" : %d,\"isMate\" : %d,\"dictValue\" : \"%s\",\"belongId\" : \"%s\",\"associationId\" : %d,\"bkId\" : \"%s\"}")
//		, InData.id, *InData.name, *InData.meetCondition, InData.type, InData.isMate, *InData.dictValue, *InData.belongId, InData.associationId, *InData.bkId);
//
//	return JsonString;
//}

void UCatalogNetworkSubsystem::Connet_PC_SendJsonNetworkRequest(const FString& InURL, const TArray<uint8>& InContent, const FString& InRequestType, const FString& InUUID)
{
	ACatalogPlayerController::Get()->BP_SendJsonNetworkRequest(InURL, InContent, InRequestType, InUUID);
}

void UCatalogNetworkSubsystem::Connet_PC_SendJsonStringNetworkRequest(const FString& InURL, const FString& InContent, const FString& InRequestType, const FString& InUUID)
{
	ACatalogPlayerController::Get()->BP_SendJsonStringNetworkRequest(InURL, InContent, InRequestType, InUUID);
}

bool UCatalogNetworkSubsystem::Connet_PC_NetworkRequestCompleted(const TArray<FString>& InJsonDataString, const TArray<FString>& InHeaders, const int32& InStatusCode, const TArray<uint8>& InDataBytes, const FString& InRequestType)
{
	UE_LOG(LogTemp, Log, TEXT("RequestType %s InDataBytes count is %d "), *InRequestType, InDataBytes.Num());
	ECatalogNetRequestType RequestType = ECatalogNetRequestType::E_Back_None;
	FString UUID = TEXT("");
	{
		if (InRequestType.Contains(NET_MARK_STR_BACK_UPLOAD_FILE))
		{
			RequestType = ECatalogNetRequestType::E_Back_UploadFile;
		}
		else if (InRequestType.Contains(NET_MARK_STR_BACK_DOWNLOAD_FILE))
		{
			RequestType = ECatalogNetRequestType::E_Back_DownloadFile;
		}
		else if (InRequestType.Contains(NET_MARK_STR_DOWNLOAD_URL))
		{
			RequestType = ECatalogNetRequestType::E_Download_URL;
		}
		else
		{
			RequestType = RequestTypeMap[InRequestType];
		}

	}
	{
		UE_LOG(LogTemp, Log, TEXT("RequestType is %d "), static_cast<int32>(RequestType));
		TMap<FString, FString> Headers;
		{//解决报头
			for (auto& HeaderIter : InHeaders)
			{
				FString Key;
				FString Value;
				bool bSplited = HeaderIter.Split(TEXT(":"), &Key, &Value);
				if (bSplited && !Headers.Contains(Key))
				{
					Headers.Add(Key, Value);
				}
			}
		}
		UUID = Headers.Contains(TEXT("uuid")) ? Headers[TEXT("uuid")] : TEXT("");
		if (UUID.IsEmpty())
		{
			if ((InJsonDataString.Num() > 0) && (InJsonDataString[0] == TEXT("正在发布中，请稍后重试")))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT(" "), FText::FromStringTable(FName("PosSt"), TEXT("release is in process try again later")).ToString());
				UE_LOG(LogTemp, Log, TEXT("Quit function"));
				return false;
			}
			UE_LOG(LogTemp, Log, TEXT("UUID is empty!"));
			//return false;
		}
		UUID = UUID.TrimStartAndEnd();
	}

	switch (RequestType)
	{
	case ECatalogNetRequestType::E_Back_Login:
	{
		if (InJsonDataString.Num() > 0)
		{
			UE_LOG(LogTemp, Log, TEXT("%s "), *InJsonDataString[0]);
			LoginCheckResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		}
		else
		{
			LoginCheckResponseHandler(UUID, InStatusCode, (void*)this, TEXT(""));
		}
		break;
	}
	case ECatalogNetRequestType::E_Back_QueryReleaseLog:
	{
		if (InJsonDataString.Num() > 0)
		{
			OnQueryReleaseLogResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		}
		break;
	}
	case ECatalogNetRequestType::E_Back_UploadFile:
	{
		FString FilePath = InRequestType.Right(InRequestType.Len() - FString(NET_MARK_STR_BACK_UPLOAD_FILE).Len() - 1);
		UploadFileResponseDelegate.Broadcast(200 == InStatusCode, FilePath);
		break;
	}
	case ECatalogNetRequestType::E_Back_DownloadFile:
	case ECatalogNetRequestType::E_Download_URL:
	{
		UE_LOG(LogTemp, Log, TEXT("ERequestType::EDownloadFile"));
		FString FlagStr = RequestType == ECatalogNetRequestType::E_Download_URL ? NET_MARK_STR_DOWNLOAD_URL : NET_MARK_STR_BACK_DOWNLOAD_FILE;
		FString FilePath = InRequestType.Right(InRequestType.Len() - FlagStr.Len() - 1);
		if (0 == FilePath.Len()) break;
		UE_LOG(LogTemp, Warning, TEXT("ERequestType::EDownloadFile FilePath [ %s ]"), *FilePath);
		bool bSuccess = false;
		int32 StatusCode = InStatusCode;
		if (200 == InStatusCode && RequestType != ECatalogNetRequestType::E_Download_URL)
		{
			for (int32 i = 0; (i < InJsonDataString.Num()) && (InJsonDataString[i].Contains(TEXT("{"))); ++i)
			{
				//UE_LOG(LogTemp, Warning, TEXT("ERequestType::EDownloadFile Json [ %s ]"), *InJsonDataString[i]);

				auto& Json = InJsonDataString[i];
				FUserInfoTableDataMsg DownloadMsg;
				bool bSuccess2 = UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FUserInfoTableDataMsg>(Json, DownloadMsg);//正常的JSON数据一定会大于20
				if (bSuccess2)
				{
					StatusCode = FCString::Atoi(*DownloadMsg.code);
					break;
				}
			}
		}
		UUID = DownloadFilesQueue.WhichPackageFileIn(FilePath);
		if (200 == StatusCode)
		{
			if (RequestType == ECatalogNetRequestType::E_Download_URL)
			{
				FString FileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FilePath));

				if (FilePath.EndsWith(TEXT(".pak")))
				{//pak
					//unmount origin
					{
						//FString PakAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir, FilePath));
						GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->UnmountPakFile(FileAbsPath);
						FCatalogFunctionLibrary::DeleteFile(FileAbsPath);
					}

					//FString FileName = FPaths::GetBaseFilename(FilePath);
					///*
					//*  @@ upper upper folder ( zip has own folder )
					//*  @@ ../../
					//*/
					//FString FolderPath = FPaths::GetPath(FPaths::GetPath(FilePath));
					//FString ZipPath = FPaths::Combine(FolderPath, FileName + TEXT(".zip"));
					FString ZipAbsPath = URefRelationFunction::GetZipRelativePath(FilePath, true);
					bool Res = FCatalogFunctionLibrary::WriteDataToFile(ZipAbsPath, InDataBytes);

					//mount after unzip
				}
				else
				{
					bool Res = FCatalogFunctionLibrary::WriteDataToFile(FileAbsPath, InDataBytes);
				}
			}
			else
			{
				//如果下载的文件是pak文件，则Mount此文件
				//在写文件之前需要判断是否存在旧文件，如果存在需要先将旧文件Unmount，然后删除旧文件
				FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + FilePath);
				bool Res = false;
				const FString FileExtension = FPaths::GetExtension(FilePath);
				if (FileExtension.Equals(TEXT("pak"), ESearchCase::IgnoreCase))
				{//如果文件后缀为Pak则Mount
					GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->UnmountPakFile(FilePath);
					FCatalogFunctionLibrary::DeleteFile(FilePath);
					Res = FCatalogFunctionLibrary::WriteDataToFile(FilePath, InDataBytes);
					GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>()->MountPakFile(FilePath, true);
				}
				else
				{
					Res = FCatalogFunctionLibrary::WriteDataToFile(FilePath, InDataBytes);
				}
			}
		}
		else if (1044 == StatusCode)
		{
			UE_LOG(LogTemp, Error, TEXT("ERequestType::EDownloadFile StatusCode @ 1044"));
			//check(false);
		}
		{
			int32 Index = IndexOfUUID(UUID);
			if (INDEX_NONE != Index)
			{
				DownloadFilesQueue.Update(DownloadThreadInfo[Index]->UUID, DownloadThreadInfo[Index]->FilePath, NFileNetworkOpertator::EDownloadState::Downloaded);
				if (DownloadFilesQueue.IsDownloaded(DownloadThreadInfo[Index]->UUID))
				{
					TArray<FString> FilePaths;
					DownloadFilesQueue.GetFilePaths(DownloadThreadInfo[Index]->UUID, FilePaths);
					DownloadFileResponseDelegate.Broadcast(UUID, 200 == StatusCode || 500 == StatusCode, FilePaths);
				}
				DownloadFilesQueue.TryRemoveDownloadedPackage();
				DownloadThreadInfo[Index].Reset();
				FString NewUUID, NewFilePath;
				if (DownloadFilesQueue.First(NewUUID, NewFilePath))
				{
					this->DownloadFileInner(NewUUID, NewFilePath);
				}
			}
		}
		break;
	}
	/*case ECatalogNetRequestType::EMergeInsertLog:
	{
		OnMergeInsertResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ERequestType::ESearchMergeLog:
	{
		OnSearchMergeLogResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}*/
	case ECatalogNetRequestType::E_Back_Release:
	{
		OnReleaseResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Release_Info:
	{
		QueryReleaseListResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_UserRelease:
	{
		ReleaseResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_ReleaseStyleOrParams:
	{
		ReleaseStyleParamsResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_ReleaseDetail:
	{
		GetReleaseDetailInfoResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_StyleCraft:
	{
		QueryStyleCraftResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Add:
	{
		BackDirectoryAddResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Del:
	{
		BackDirectoryDeleteResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Update:
	{
		BackDirectoryUpdateResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Search:
	{
		BackDirectorySearchResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Sort:
	{
		BackDirectorySortResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Copy:
	{
		BackDirectoryCopyResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Cut:
	{
		BackDirectoryCutResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Obsurce_Search:
	{
		ObscureSearchResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Back_Query_UpperLevel:
	{
		QueryUpperLevelContainsResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Front_Search:
	{
		FrontDirectorySearchResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Front_Category_Data:
	{
		FurnitureOrMaterialSearchResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Front_Category_Data_For_Parse:
	{
		FurnitureOrMaterialDataForParseResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Front_Category_Data_Arr_For_Parse:
	{
		GetFurnitureOrMaterialDataForParseResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Front_Update_FolderId:
	{
		UpdateFolderIdForWebResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Create:
	{
		AssociateCreateResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Delete:
	{
		AssociateDeleteResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Update:
	{
		AssociateUpdateResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Query:
	{
		AssociateQueryResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Category:
	{
		AssociateCategoryResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Associate_Category_ShowData:
	{
		AssociateCategoryShowDataResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	case ECatalogNetRequestType::E_Place_Rule_Query:
	{
		QueryRefPlaceRuleResponseHandler(UUID, InStatusCode, (void*)this, InJsonDataString[0]);
		break;
	}
	default:
		break;
	}

	return false;
}

void UCatalogNetworkSubsystem::Connet_PC_UploadFileToServer(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID)
{
	ACatalogPlayerController::Get()->BP_UploadFileToServer(InURL, InFilePath, RequestType, UUID);
}

void UCatalogNetworkSubsystem::Connet_PC_DownloadFileFromServer(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID, const FString& InRelativeFilePath)
{
	ACatalogPlayerController::Get()->BP_DownloadFileFromServer(InURL, InFilePath, RequestType, UUID, InRelativeFilePath);
}

void UCatalogNetworkSubsystem::Connet_PC_DownloadFileFromServer_Get(const FString& InURL, const FString& InFilePath, const FString& RequestType, const FString& UUID, const FString& InRelativeFilePath)
{
	ACatalogPlayerController::Get()->BP_DownloadFileFromServer_Get(InURL, InFilePath, RequestType, UUID, InRelativeFilePath);

}

void UCatalogNetworkSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	InitSystem();
}

void UCatalogNetworkSubsystem::Deinitialize()
{
	DeinitSystem();

	Super::Deinitialize();
}

bool UCatalogNetworkSubsystem::IsAdminLogin() const
{
	bool bIsLoginAdmin = GlobalData.IsAdminUser();

	// 临时权限逻辑
	FString TempFilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("Access.txt"));
	if (!bIsLoginAdmin && FPaths::FileExists(TempFilePath))
	{//当登录不是管理员时，则看临时管理员权限文件
		TArray<FString> TempFileContent;
        FFileHelper::LoadFileToStringArray(TempFileContent, *TempFilePath);
		bIsLoginAdmin = TempFileContent.Contains(GlobalData.GetUserName());
	}
	return bIsLoginAdmin;
	//return GlobalData.IsAdminUser();
}

FString UCatalogNetworkSubsystem::GetUpdataUserInfo()
{
	//return FPaths::Combine(GlobalData.GetUserID(), GlobalData.GetUserName());
	return GlobalData.GetUserIDStr();
}

FString UCatalogNetworkSubsystem::GetObsurceUserInfo()
{
	return GlobalData.GetUserIDStr();
}

UCatalogNetworkSubsystem* UCatalogNetworkSubsystem::GetInstance()
{
	return ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UCatalogNetworkSubsystem>();
 }



#undef NET_MARK_STR_BACK_LOGIN					
#undef	NET_MARK_STR_BACK_UPLOAD_FILE			
#undef	NET_MARK_STR_BACK_DOWNLOAD_FILE			
#undef NET_MARK_STR_BACK_QUERY_RELEASE_LOG		
#undef NET_MARK_STR_BACK_MERGE_INSERT_LOG
#undef NET_MARK_STR_BACK_SEARCH_MERGE_LOG		
#undef NET_MARK_STR_BACK_RELEASE				

#undef NET_MARK_STR_BACK_DIRECTORY_ADD			
#undef NET_MARK_STR_BACK_DIRECTORY_DEL			
#undef NET_MARK_STR_BACK_DIRECTORY_UPDATE		
#undef NET_MARK_STR_BACK_DIRECTORY_SEARCH		
#undef NET_MARK_STR_BACK_DIRECTORY_SORT		
#undef NET_MARK_STR_BACK_DIRECTORY_COPY		

#undef NET_MARK_STR_FRONT_DIRECTORY_SEARCH		


#undef CATALOG_NET_STATE_OK 
