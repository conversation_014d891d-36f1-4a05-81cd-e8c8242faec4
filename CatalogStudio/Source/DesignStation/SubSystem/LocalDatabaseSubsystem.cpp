// Fill out your copyright notice in the Description page of Project Settings.


#include "LocalDatabaseSubsystem.h"
#include "Runtime/Core/Public/HAL/IPlatformFileModule.h"

ULocalDatabaseSubsystem::~ULocalDatabaseSubsystem()
{
	CloseDatabase();
	CloseServerDatabase();
}

bool ULocalDatabaseSubsystem::OpenDatabase(const FString& DBPath)
{
	DatabasePtr = MakeShared<FSQLiteDatabase>();
	bool bOpen = DatabasePtr->Open(*DBPath, ESQLiteDatabaseOpenMode::ReadOnly);
	return bOpen;
}

void ULocalDatabaseSubsystem::CloseDatabase()
{
	if (DatabasePtr.IsValid() && DatabasePtr->IsValid())
	{
		DatabasePtr->Close();
	}
	DatabasePtr.Reset();
}

bool ULocalDatabaseSubsystem::OpenServerDatabase(const FString& DBPath)
{
	ServerDatabasePtr = MakeShared<FSQLiteDatabase>();
	bool bOpen = ServerDatabasePtr->Open(*DBPath, ESQLiteDatabaseOpenMode::ReadWrite);
	return bOpen;
}

void ULocalDatabaseSubsystem::CloseServerDatabase()
{
	if (ServerDatabasePtr.IsValid() && ServerDatabasePtr->IsValid())
	{
		ServerDatabasePtr->Close();
	}
	ServerDatabasePtr.Reset();
}
