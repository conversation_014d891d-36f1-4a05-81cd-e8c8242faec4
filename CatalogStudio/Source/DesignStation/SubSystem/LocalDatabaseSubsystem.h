// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "SQLiteCore/Public/SQLiteDatabase.h"
#include "LocalDatabaseSubsystem.generated.h"


UCLASS()
class DESIGNSTATION_API ULocalDatabaseSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:

	virtual ~ULocalDatabaseSubsystem();

	bool OpenDatabase(const FString& DBPath);

	TSharedPtr<FSQLiteDatabase> GetDatabase() { return DatabasePtr; }

	void CloseDatabase();

	bool OpenServerDatabase(const FString& DBPath);

	TSharedPtr<FSQLiteDatabase> GetServerDatabase() { return ServerDatabasePtr; }

	void CloseServerDatabase();

private:

	TSharedPtr<FSQLiteDatabase> DatabasePtr;

	TSharedPtr<FSQLiteDatabase> ServerDatabasePtr;
};
