// Fill out your copyright notice in the Description page of Project Settings.


#include "PakFileManagerSubsystem.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "Runtime/Core/Public/HAL/IPlatformFileModule.h"

//启动时Mount所有的pak文件(NO USE)
void UPakFileManagerSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	//从配置文件中获取所有pak文件的根目录
	//TArray<FString> FolderNames;
	//GetPakRootFolders(FolderNames);
	//FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
	//if (PakPlatformFile)
	//{
	//	for (int32 i = 0; i < FolderNames.Num(); ++i)
	//	{
	//		FString FolderPath = FPaths::ProjectContentDir() + FolderNames[i] + TEXT("/");
	//		FolderPath = FPaths::ConvertRelativePathToFull(FolderPath);
	//		TArray<FString> PakFilesInFolder;
	//		//获取pak文件的根目录下所有的pak文件
	//		this->FindPakFilesInDirectory(PakPlatformFile->GetLowerLevel(), *FolderPath, TEXT("*.pak"), PakFilesInFolder);
	//		for (auto& PakFilePath : PakFilesInFolder)
	//		{
	//			//Mount每个pak文件并保存其mount状态
	//			bool bMounted = this->MountPakFile(PakFilePath);
	//			UE_LOG(LogTemp, Log, TEXT("%s mount %s"), *PakFilePath, bMounted ? TEXT("success") : TEXT("failed"));
	//			PakFileMounted.Add(PakFilePath, bMounted);
	//		}
	//	}
	//}
}

//关闭时Unmount所有的pak文件以解除文件占用
void UPakFileManagerSubsystem::Deinitialize()
{
	FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
	if (PakPlatformFile)
	{
		for (auto PakFilePair : PakFileMounted)
		{
			if (false == PakFilePair.Value) continue;
			PakPlatformFile->Unmount(*PakFilePair.Key);
		}
	}

	PakReMountOrder.Empty();

	Super::Deinitialize();
}

UPakFileManagerSubsystem* UPakFileManagerSubsystem::GetInstance()
{
	return ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UPakFileManagerSubsystem>();
}

bool UPakFileManagerSubsystem::GetAllFilesInPakFile(const FString& InPakFilePath, TArray<FString>& OutFiles)
{
	FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
	if (PakPlatformFile)
	{
		PakPlatformFile->GetPrunedFilenamesInPakFile(InPakFilePath, OutFiles);
		return true;
	}
	return false;
}

bool UPakFileManagerSubsystem::MountPakFile(const FString& InPakFilePath, bool bForceMount)
{
	if (FPaths::FileExists(InPakFilePath))
	{
		bool bMounted = PakFileMounted.Contains(InPakFilePath) && PakFileMounted[InPakFilePath];
		//此文件已经被Mount且不需要重新Mount则返回true
		if (bMounted && !bForceMount) return true;
		//此文件已经被Mount但需要重新Mount则先unmount
		if (bMounted)
		{
			bool bUnmounted = this->UnmountPakFile(InPakFilePath);
			//Unmount失败返回false，因为此时无法继续mount
			if (false == bUnmounted) return false;
		}
		bMounted = MountPakFile(InPakFilePath);
		return bMounted;
	}
	return false;
}

bool UPakFileManagerSubsystem::UnmountPakFile(const FString& InPakFilePath)
{
	if (FPaths::FileExists(InPakFilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("unmount pak : %s"), *InPakFilePath);
		FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
		if (PakPlatformFile)
		{
			bool Unmounted = PakPlatformFile->Unmount(*InPakFilePath);
			//Unmount成功且此文件在Pak文件列表中则修改文件的mount状态
			if (Unmounted && PakFileMounted.Contains(InPakFilePath))
			{
				PakFileMounted[InPakFilePath] = false;
			}
			if (Unmounted)
			{
				if (PakReMountOrder.Contains(InPakFilePath))
				{
					PakReMountOrder[InPakFilePath] = PakReMountOrder[InPakFilePath] + 1;
				}
				else
				{
					PakReMountOrder.Add(InPakFilePath, 0);
				}
				UE_LOG(LogTemp, Warning, TEXT("unmount pak : %s ----- new order : %d"), *InPakFilePath, PakReMountOrder[InPakFilePath]);
			}
			return Unmounted;
		}
	}
	return false;
}

bool UPakFileManagerSubsystem::GetPakRootFolders(TArray<FString>& RootFolders)
{
	RootFolders.Empty();
	FString ConfigFilePath = FPaths::ProjectConfigDir() + TEXT("PakConfig.ini");
	ConfigFilePath = FPaths::ConvertRelativePathToFull(ConfigFilePath);
	bool Res = FFileHelper::LoadFileToStringArray(RootFolders, *ConfigFilePath);
	return Res && (RootFolders.Num() > 0);
}

bool UPakFileManagerSubsystem::MountPakFile(const FString& InPakFilePath)
{
	FString OriginMountPoint(TEXT(""));
	FString LeftStr(TEXT(""));
	InPakFilePath.Split(TEXT("Content/"), &LeftStr, &OriginMountPoint);
	OriginMountPoint = FPaths::GetPath(OriginMountPoint) + TEXT("/");

	FString MountPoint = FPaths::ProjectContentDir();
#if WITH_EDITOR
	MountPoint = FPaths::ConvertRelativePathToFull(MountPoint);
#endif
	/*
	*  test 
	*/
	//OriginMountPoint = FPaths::Combine(OriginMountPoint, TEXT("D"));

	MountPoint = FPaths::Combine(MountPoint, OriginMountPoint);
	UE_LOG(LogTemp, Log, TEXT("OriginMountPoint =%s"), *MountPoint);
	FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
	uint32 PakMountOrder = PakReMountOrder.Contains(InPakFilePath) ? ++PakReMountOrder[InPakFilePath] : 0;
	UE_LOG(LogTemp, Warning, TEXT("mount pak ------ order: %d"), PakMountOrder);
	bool bMounted = PakPlatformFile && PakPlatformFile->Mount(*InPakFilePath, PakMountOrder, *MountPoint);
	return bMounted;
}

FPakPlatformFile* UPakFileManagerSubsystem::GetPakPlatformFile() const
{
	FPakPlatformFile* PakPlatformFile = static_cast<FPakPlatformFile*>(FPlatformFileManager::Get().FindPlatformFile(FPakPlatformFile::GetTypeName()));
	if (!PakPlatformFile)
	{
		IPlatformFileModule& PakModule = FModuleManager::LoadModuleChecked<IPlatformFileModule>("PakFile");
		PakPlatformFile = static_cast<FPakPlatformFile*>(PakModule.GetPlatformFile());
		if (PakPlatformFile && !PakPlatformFile->GetLowerLevel())
		{
			IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
			PakPlatformFile->Initialize(&PlatformFile, TEXT(""));
			PakPlatformFile->InitializeNewAsyncIO();
		}
	}
	return PakPlatformFile;
}

void UPakFileManagerSubsystem::FindPakFilesInDirectory(IPlatformFile* LowLevelFile, const TCHAR* Directory, const FString& WildCard, TArray<FString>& OutPakFiles)
{
	// Helper class to find all pak files.
	class FPakSearchVisitor : public IPlatformFile::FDirectoryVisitor
	{
		TArray<FString>& FoundPakFiles;
		IPlatformChunkInstall* ChunkInstall = nullptr;
		FString WildCard;

	public:
		FPakSearchVisitor(TArray<FString>& InFoundPakFiles, const FString& InWildCard, IPlatformChunkInstall* InChunkInstall)
			: FoundPakFiles(InFoundPakFiles)
			, ChunkInstall(InChunkInstall)
			, WildCard(InWildCard)
		{}
		virtual bool Visit(const TCHAR* FilenameOrDirectory, bool bIsDirectory)
		{
			if (bIsDirectory == false)
			{
				FString Filename(FilenameOrDirectory);
				if (Filename.MatchesWildcard(WildCard))
				{
					// if a platform supports chunk style installs, make sure that the chunk a pak file resides in is actually fully installed before accepting pak files from it
					if (ChunkInstall)
					{
						int32 PakchunkIndex = UPakFileManagerSubsystem::GetPakchunkIndexFromPakFile(Filename);
						if (PakchunkIndex != INDEX_NONE)
						{
							if (ChunkInstall->GetPakchunkLocation(PakchunkIndex) == EChunkLocation::NotAvailable)
							{
								return true;
							}
						}
					}
					FoundPakFiles.Add(Filename);
				}
			}
			return true;
		}
	};

	// Find all pak files.
	FPakSearchVisitor Visitor(OutPakFiles, WildCard, FPlatformMisc::GetPlatformChunkInstall());
	LowLevelFile->IterateDirectoryRecursively(Directory, Visitor);
}

int32 UPakFileManagerSubsystem::GetPakchunkIndexFromPakFile(const FString& InFilename)
{
	FString ChunkIdentifier(TEXT("pakchunk"));
	FString BaseFilename = FPaths::GetBaseFilename(InFilename);
	int32 ChunkNumber = INDEX_NONE;

	if (BaseFilename.StartsWith(ChunkIdentifier))
	{
		int32 StartOfNumber = ChunkIdentifier.Len();
		int32 DigitCount = 0;
		if (FChar::IsDigit(BaseFilename[StartOfNumber]))
		{
			while ((DigitCount + StartOfNumber) < BaseFilename.Len() && FChar::IsDigit(BaseFilename[StartOfNumber + DigitCount]))
			{
				DigitCount++;
			}

			if ((StartOfNumber + DigitCount) < BaseFilename.Len())
			{
				FString ChunkNumberString = BaseFilename.Mid(StartOfNumber, DigitCount);
				check(ChunkNumberString.IsNumeric());
				TTypeFromString<int32>::FromString(ChunkNumber, *ChunkNumberString);
			}
		}
	}

	return ChunkNumber;
}
