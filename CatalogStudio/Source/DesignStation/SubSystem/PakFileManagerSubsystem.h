// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Runtime/PakFile/Public/IPlatformFilePak.h"
#include "PakFileManagerSubsystem.generated.h"

/**
 *此子系统在管理pak文件时以pak文件所在的文件目录来解析其mountpoint
 * 因此每个pak必须保存在Content目录下对应的目录中，否则会因mountpoint错误
 * 无法加载其中的数据
 */
UCLASS()
class DESIGNSTATION_API UPakFileManagerSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	static UPakFileManagerSubsystem* GetInstance();

	//获取pak文件中所有的文件，如果pak文件不存在、Mount失败、pak内部文件为空返回false，返回false时OutFiles为空
	//InPakFilePath为pak文件的绝对路径
	//OutFiles为查询到的文件信息,信息格式参见FPakPlatformFile中的GetPrunedFilenamesInPakFile函数
	bool GetAllFilesInPakFile(const FString& InPakFilePath, TArray<FString>& OutFiles);

	//Mount pak文件，mount成功返回true，否则返回false
	//InPakFilePath为pak文件的绝对路径
	//bForceMount为true表示InPakFilePath已经Mount则先执行unmount然后再mount，为flase时InPakFilePath已经mount则不再重新mount
	bool MountPakFile(const FString& InPakFilePath, bool bForceMount);

	//Unmount pak文件，unmount成功返回true，否则返回false
	//InPakFilePath为pak文件的绝对路径
	bool UnmountPakFile(const FString& InPakFilePath);

private:

	bool GetPakRootFolders(TArray<FString>& RootFolders);

	FPakPlatformFile* GetPakPlatformFile() const;

	void FindPakFilesInDirectory(IPlatformFile* LowLevelFile, const TCHAR* Directory, const FString& WildCard, TArray<FString>& OutPakFiles);

	static int32 GetPakchunkIndexFromPakFile(const FString& InFilename);

	bool MountPakFile(const FString& InPakFilePath);

private:

	UPROPERTY()
		TMap<FString, bool> PakFileMounted;//主键为pak绝对路径，值为pak文件是否已经mount成功

	UPROPERTY()
		TMap<FString, uint32> PakReMountOrder;

};
