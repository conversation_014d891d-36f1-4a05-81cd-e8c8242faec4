// Fill out your copyright notice in the Description page of Project Settings.


#include "ResourceSubsystem.h"
#include "IPlatformFilePak.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/BasicClasses/JsonUtilitiesLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "HAL/IPlatformFileModule.h"


class IPlatformFileModule;
DEFINE_LOG_CATEGORY(ResourceSubsystemLog);

#define DEFAULT_MAT_PATH TEXT("Material'/Game/Materials/Mat_White.Mat_White'")

void UResourceSubsystem::Init_Local()
{
	{//load default material
		UMaterialInterface* Mat = LoadObject<UMaterialInterface>(nullptr, DEFAULT_MAT_PATH);
		UMaterialInstanceDynamic* MatIns = UMaterialInstanceDynamic::Create(Mat, nullptr);
		if (Mat)
		{
			MaterialInsDynamic.Add(DEFAULT_MAT_PATH, MatIns);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to load default material"));
		}

	}
}

void UResourceSubsystem::Deinit_Local()
{
	MaterialInsDynamic.Empty();
}

FPakPlatformFile* UResourceSubsystem::GetPakPlatformFile() const
{
	FPakPlatformFile* PakPlatformFile = static_cast<FPakPlatformFile*>(FPlatformFileManager::Get().FindPlatformFile(FPakPlatformFile::GetTypeName()));
	if (!PakPlatformFile)
	{
		IPlatformFileModule& PakModule = FModuleManager::LoadModuleChecked<IPlatformFileModule>("PakFile");
		PakPlatformFile = static_cast<FPakPlatformFile*>(PakModule.GetPlatformFile());
		if (PakPlatformFile && !PakPlatformFile->GetLowerLevel())
		{
			IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
			PakPlatformFile->Initialize(&PlatformFile, TEXT(""));
			PakPlatformFile->InitializeNewAsyncIO();
		}
	}
	return PakPlatformFile;
}

void UResourceSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	Init_Local();
}

void UResourceSubsystem::Deinitialize()
{
	Deinit_Local();

	Super::Deinitialize();
}

UResourceSubsystem* UResourceSubsystem::GetInstance()
{
	return ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<UResourceSubsystem>();
}

UMaterialInstanceDynamic* UResourceSubsystem::GetDefaultMaterialInsDynamic()
{
	return GetMaterialInsDynamic(DEFAULT_MAT_PATH);
}

UMaterialInstanceDynamic* UResourceSubsystem::GetMaterialInsDynamic(const FString& MaterialPath)
{
	UMaterialInstanceDynamic** MatIns = MaterialInsDynamic.Find(MaterialPath);
	if (MatIns)
	{
		return *MatIns;
	}
	else
	{
		UMaterialInterface* Mat = LoadObject<UMaterialInterface>(nullptr, *MaterialPath);
		if (Mat)
		{
			UMaterialInstanceDynamic* InstanceDynamic = UMaterialInstanceDynamic::Create(Mat, nullptr);
			MaterialInsDynamic.Add(MaterialPath, InstanceDynamic);
			return InstanceDynamic;
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to load material %s"), *MaterialPath);
			return nullptr;
		}
	}
}

#define IMAGE_EXTENSION1 TEXT("jpeg")
#define IMAGE_EXTENSION2 TEXT("jpg")
#define IMAGE_EXTENSION3 TEXT("png")
UTexture2D* UResourceSubsystem::GetImageTexture2D(const FString& TexturePath)
{
	//check extension1
	if(UTexture2D* Texture = GetImageTexture2D_NewExtension(TexturePath, IMAGE_EXTENSION1))
	{
		return Texture;
	}
	//check extension2
	if(UTexture2D* Texture = GetImageTexture2D_NewExtension(TexturePath, IMAGE_EXTENSION2))
	{
		return Texture;
	}
	//check extension3
	if(UTexture2D* Texture = GetImageTexture2D_NewExtension(TexturePath, IMAGE_EXTENSION3))
	{
		return Texture;
	}
	
	return nullptr;
}

bool UResourceSubsystem::IsMatFile(const FString& Path)
{
	FString Extension = FPaths::GetExtension(Path);
	return Extension.Equals(IMAGE_EXTENSION1) || Extension.Equals(IMAGE_EXTENSION2) || Extension.Equals(IMAGE_EXTENSION3);
}
#undef IMAGE_EXTENSION1
#undef IMAGE_EXTENSION2
#undef IMAGE_EXTENSION3

UTexture2D* UResourceSubsystem::GetImageTexture2D_NewExtension(const FString& TexturePath, const FString& NewExtension)
{
	const FString ImagePath = FPaths::ChangeExtension(TexturePath, NewExtension);
	if(FPaths::FileExists(ImagePath))
	{
		return FCatalogFunctionLibrary::LoadTextureFromJPG(ImagePath);
	}
	return nullptr;
}

void UResourceSubsystem::AddMaterialWebData(const TArray<FCSModelMatData>& InMatData, const TArray<FCSModelMatData>& NeedRedreshMat)
{
	for (auto IMD : InMatData)
	{
		const int32 Index = MaterialWebData.IndexOfByPredicate(
			[IMD](const FCSModelMatData& InData)->bool
			{
				return IMD.id == InData.id;
			}
		);
		if (Index != INDEX_NONE)
		{
			MaterialWebData[Index] = IMD;
		}
		else
		{
			MaterialWebData.Add(IMD);
		}

		const int32 RefreshIndex = NeedRedreshMat.IndexOfByPredicate(
			[IMD](const FCSModelMatData& InData)->bool
			{
				return IMD.id == InData.id;
			}
		);
		if (RefreshIndex != INDEX_NONE && !IMD.folderId.IsEmpty())
		{
			MaterialInsDynamic.Remove(IMD.folderId);
		}
	}
}

UMaterialInstanceDynamic* UResourceSubsystem::GetMaterialInsDynamic_Web(const FString& FolderId)
{
#define WEB_BASE_MATERIAL_PATH TEXT("Material'/Game/Materials/OLOMaterial/M_BaseMatLib.M_BaseMatLib'")
#define DIFFUSE_MARK TEXT("diffuse")
#define DS_IMPORT_JSON_MARK TEXT("json")
	
	if (MaterialInsDynamic.Contains(FolderId))
	{
		return MaterialInsDynamic[FolderId];
	}
	else
	{
		const int32 MaterialIndex = MaterialWebData.IndexOfByPredicate(
			[FolderId](const FCSModelMatData& InData)->bool
			{
				bool EqualOfFolderID = InData.folderId.Equals(FolderId);
				bool EqualOfID = (InData.id == FCString::Atoi64(*FolderId));
				return EqualOfFolderID || EqualOfID;
			}
		);
		if (MaterialIndex != INDEX_NONE)
		{
			TArray<FString> FilePaths, NeedDownloadPaths;
			MaterialWebData[MaterialIndex].AnalysisData(FilePaths, NeedDownloadPaths);

			if (FilePaths.Num() >= 1)
			{
				const FString JsonFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FilePaths.Last()));
				if(!FPaths::FileExists(JsonFileAbsPath) || !JsonFileAbsPath.EndsWith(DS_IMPORT_JSON_MARK))
				{
					UE_LOG(ResourceSubsystemLog, Error, TEXT("Mat Json File[%s] No Exist!"), *JsonFileAbsPath);
					return GetDefaultMaterialInsDynamic();
				}
				
				FString JsonStr = TEXT("");
				if (FFileHelper::LoadFileToString(JsonStr, *JsonFileAbsPath))
				{
					FString MatAbsFolder = FPaths::GetPath(JsonFileAbsPath);

					TSharedPtr<FJsonObject> ParamsObj;
					TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonStr);
					if (!FJsonSerializer::Deserialize(Reader, ParamsObj))
					{
						return nullptr;
					}

					if(ParamsObj->HasTypedField<EJson::String>(TEXT("Parent")))
					{
						FString ParentMatPath = ParamsObj->GetStringField(TEXT("Parent"));
						if(ParentMatPath.IsEmpty())
						{
							ParentMatPath = WEB_BASE_MATERIAL_PATH;
						}

						if(UMaterial* BaseMat = LoadObject<UMaterial>(nullptr, *ParentMatPath))
						{
							if(UMaterialInstanceDynamic* MatInstance = UMaterialInstanceDynamic::Create(BaseMat, nullptr))
							{
#define GET_COLOR_TO_MAT(JsonObj, Field, MatInstance) \
	if (JsonObj->HasTypedField<EJson::Object>(Field)) \
	{ \
		TSharedPtr<FJsonObject> ColorObj = JsonObj->GetObjectField(Field); \
		FLinearColor Color = FLinearColor::White; \
		if (ColorObj->HasTypedField<EJson::Number>(TEXT("R"))) \
			Color.R = ColorObj->GetNumberField(TEXT("R")); \
		if (ColorObj->HasTypedField<EJson::Number>(TEXT("G"))) \
			Color.G = ColorObj->GetNumberField(TEXT("G")); \
		if (ColorObj->HasTypedField<EJson::Number>(TEXT("B"))) \
			Color.B = ColorObj->GetNumberField(TEXT("B")); \
		if (ColorObj->HasTypedField<EJson::Number>(TEXT("A"))) \
			Color.A = ColorObj->GetNumberField(TEXT("A")); \
		MatInstance->SetVectorParameterValue(Field, Color); \
	}

#define GET_STRING_TO_MAT(JsonObj, Field, MatInstance) \
	if(JsonObj->HasTypedField<EJson::String>(Field)) \
	{ \
		FString TexturePath = JsonObj->GetStringField(Field); \
		UTexture2D* InnerTex = nullptr; \
		if(!TexturePath.IsEmpty()) \
		{ \
			FString InnerMatAbsPath = FPaths::Combine(MatAbsFolder, TexturePath); \
			InnerTex = UResourceSubsystem::GetImageTexture2D(InnerMatAbsPath); \
		} \
		if(InnerTex != nullptr) \
		MatInstance->SetTextureParameterValue(Field, InnerTex); \
	}

#define GET_BOOL_TO_MAT(JsonObj, Field, MatInstance) \
	if(JsonObj->HasTypedField<EJson::Boolean>(Field)) \
	{ \
		bool bEnable = JsonObj->GetBoolField(Field); \
		MatInstance->SetScalarParameterValue(Field, bEnable ? 1.0f : 0.0f); \
	}

#define GET_FLOAT_TO_MAT(JsonObj, Field, MatInstance) \
	if(JsonObj->HasTypedField<EJson::Number>(Field)) \
	{ \
		float Value = JsonObj->GetNumberField(Field); \
		MatInstance->SetScalarParameterValue(Field, Value); \
	}
	
								//base color colorize
								GET_COLOR_TO_MAT(ParamsObj, TEXT("BaseColor_Colorize"), MatInstance);
								
								//basic texture color, consider different image extension [ jpg, jpeg, png ]
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_BaseColor"), MatInstance);
								
								//metallic
								GET_BOOL_TO_MAT(ParamsObj, TEXT("Enable_Metallic"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("Metallic"), MatInstance);
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_Metallic"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("MetallicMultiply"), MatInstance);

								//roughness
								GET_BOOL_TO_MAT(ParamsObj, TEXT("Enable_Roughness"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("Roughness"), MatInstance);
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_RoughPack"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("RoughnessMultiply"), MatInstance);

								//emissive mask
								GET_BOOL_TO_MAT(ParamsObj, TEXT("Enable_EmissiveColor"), MatInstance);
								GET_COLOR_TO_MAT(ParamsObj, TEXT("EmissiveColor"), MatInstance);
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_EmissiveMask"), MatInstance);

								//opacity
								GET_BOOL_TO_MAT(ParamsObj, TEXT("Enable_Opacity"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("Opacity"), MatInstance);
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_Opacity"), MatInstance);

								//normal
								GET_STRING_TO_MAT(ParamsObj, TEXT("Tex_Normal"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("NormalMultiply"), MatInstance);

								//uv offset
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("UVOffsetU"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("UVOffsetV"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("UVTileU"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("UVTileV"), MatInstance);
								GET_FLOAT_TO_MAT(ParamsObj, TEXT("UVRotation"), MatInstance);

								//add to cache
								MaterialInsDynamic.Add(FolderId, MatInstance);
								return MatInstance;
								
							}
						}
					}
				}
			}
			else
			{
				UE_LOG(ResourceSubsystemLog, Error, TEXT("Analysis Mat [%s] Error!"), *FolderId);
			}
		}
	}
#if 0

			/*
			*  @@ analysis material data
			*  @@ load basic material
			*  @@ load diffuse image
			*  @@ set diffuse image and params to material
			*/

			UMaterial* BaseMat = LoadObject<UMaterial>(nullptr, WEB_BASE_MATERIAL_PATH);
			UMaterialInstanceDynamic* MatInstance = UMaterialInstanceDynamic::Create(BaseMat, nullptr);


			/*
			*  @@ set diffuse image and params to material
			*  @@ index 1 : params file  path
			*  @@ index 0 : diffuse image path
			*/

			for (int32 i = 0; i < FilePaths.Num(); ++i)
			{
				FString TextPath = (FilePaths[i].EndsWith(TEXT(".jpg")) || FilePaths[i].EndsWith(TEXT(".png")))
					? FilePaths[i] : TEXT("");
				if (TextPath.IsEmpty() || !TextPath.Contains(DIFFUSE_MARK))
					continue;

				const FString AbsPath = FPaths::Combine(FPaths::ProjectContentDir(), TextPath);
				UTexture2D* DiffuseTex = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);

				if (DiffuseTex)
				{
					MatInstance->SetTextureParameterValue(TEXT("Tex_BaseColor"), DiffuseTex);
				}

				if (FilePaths.Num() > 1)
				{
					const FString JsonFileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FilePaths.Last()));
					FString JsonStr = TEXT("");
					if (FFileHelper::LoadFileToString(JsonStr, *JsonFileAbsPath))
					{
						TSharedPtr<FJsonObject> JsonObject;
						TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonStr);
						if (FJsonSerializer::Deserialize(Reader, JsonObject))
						{
							const TSharedPtr<FJsonValue> RoughnessJSON = JsonObject->GetField<EJson::Number>(FString(TEXT("roughness")));
							if (RoughnessJSON.IsValid())
							{
								const double CurRoughness = RoughnessJSON->AsNumber();
								UE_LOG(LogTemp, Warning, TEXT("[%s] RoughnessJSON [%f]"), *JsonFileAbsPath, CurRoughness);
								MatInstance->SetScalarParameterValue(TEXT("Roughness"), CurRoughness);
							}
						}
						else
						{
							const FString JsonFileError = FString::Printf(TEXT("Json File [%s] "), *FilePaths.Last()).Append(TEXT("Format Error!"));
							UI_POP_WINDOW_ERROR(JsonFileError);
						}
					}
				}
				else
				{
					MatInstance->SetScalarParameterValue(TEXT("Roughness"), 0.5f);
				}

				break;
			}
			if (MatInstance != nullptr)
			{
				MaterialInsDynamic.Add(FolderId, MatInstance);
				return MatInstance;
			}


		}
	}
#endif

#undef WEB_BASE_MATERIAL_PATH
#undef DIFFUSE_MARK
#undef DS_IMPORT_JSON_MARK

	return GetDefaultMaterialInsDynamic();
}

void UResourceSubsystem::AddModelWebData(const TArray<FCSModelMatData>& InModelData)
{
	for (auto IMD : InModelData)
	{
		const int32 Index = ModelWebData.IndexOfByPredicate(
			[IMD](const FCSModelMatData& InData)->bool
			{
				return IMD.id == InData.id;
			}
		);
		if (Index != INDEX_NONE)
		{
			ModelWebData[Index] = IMD;
		}
		else
		{
			ModelWebData.Add(IMD);
		}

		/*FString RefPath, RelativePath;
		IMD.GetPakFileMountInfo(RefPath, RelativePath);
		MountPakFile(FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), RelativePath)
		));*/
	}
}

void UResourceSubsystem::SyncModelWebData(const TArray<FExpressionValuePair>& FolderIDPair, const TArray<FCSModelMatData>& NetData)
{
	if (FolderIDPair.Num() != NetData.Num())
	{//if some mat data missing
		for (auto MI : FolderIDPair)
		{
			MI.FormatValue();
			const int32 FolderIDIndex = NetData.IndexOfByPredicate(
				[MI](const FCSModelMatData& InData)
				{
					return InData.folderId.Equals(MI.Value);
				}
			);
			if (FolderIDIndex == INDEX_NONE)
			{
				MaterialInsDynamic.Remove(MI.Value);

				const int32 MaterialIndex = MaterialWebData.IndexOfByPredicate(
					[MI](const FCSModelMatData& InData)->bool
					{
						return InData.folderId.Equals(MI.Value, ESearchCase::IgnoreCase);
					}
				);
				if (MaterialIndex != INDEX_NONE)
				{
					MaterialWebData.RemoveAt(MaterialIndex);
				}
			}
		}
	}
}

void UResourceSubsystem::MountDependentPakFiles(const TArray<FCSModelMatData>& InModelData)
{
	for (auto IMD : InModelData)
	{
		FString RefPath, RelativePath;
		IMD.GetPakFileMountInfo(RefPath, RelativePath);
		const FString FullPath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), RelativePath)
		);
		if (!FPaths::FileExists(FullPath))
		{
			UE_LOG(LogTemp, Error, TEXT("Dependent Pak File [%s] Not Exist! --- Need To Unzip"), *FullPath);
			FString PakPath = FullPath;
			FString ZipPath = PakPath.Replace(TEXT(".pak"), TEXT(".zip"));
			FString UnZipToPath = FPaths::GetPath(ZipPath);
			URefRelationFunction::UnPackFile(ZipPath, UnZipToPath);
		}
		UnmountPakFile(FullPath);
		MountPakFile(FullPath);
	}
}

bool UResourceSubsystem::MountPakFile(const FString& InPakFilePath)
{
	if (FPaths::FileExists(InPakFilePath))
	{
		FString OriginMountPoint(TEXT(""));
		FString LeftStr(TEXT(""));
		InPakFilePath.Split(TEXT("Content/"), &LeftStr, &OriginMountPoint);
		OriginMountPoint = FPaths::GetPath(OriginMountPoint) + TEXT("/");

		FString MountPoint = FPaths::ProjectContentDir();
#if WITH_EDITOR
		MountPoint = FPaths::ConvertRelativePathToFull(MountPoint);
#endif

		MountPoint = FPaths::Combine(MountPoint, OriginMountPoint);
		UE_LOG(LogTemp, Log, TEXT("OriginMountPoint =%s"), *MountPoint);

		FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();

		const bool bMounted = PakPlatformFile && PakPlatformFile->Mount(*InPakFilePath, 0, *MountPoint);
		return bMounted;
	}
	return false;
}

bool UResourceSubsystem::UnmountPakFile(const FString& InPakFilePath)
{
	if (FPaths::FileExists(InPakFilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("unmount pak : %s"), *InPakFilePath);
		FPakPlatformFile* PakPlatformFile = GetPakPlatformFile();
		if (PakPlatformFile)
		{
			const bool Unmounted = PakPlatformFile->Unmount(*InPakFilePath);
			
			return Unmounted;
		}
	}
	return false;
}
