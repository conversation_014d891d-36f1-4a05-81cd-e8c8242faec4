// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "IPlatformFilePak.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "ResourceSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(ResourceSubsystemLog, Log, All);

// @@ ImportMaterial Json Data
USTRUCT(BlueprintType)
struct FDSImportMaterialJsonData
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FColor BaseColor_Colorize;

	UPROPERTY()
	FString Tex_BaseColor;

	UPROPERTY()
	bool Enable_Metallic;

	UPROPERTY()
	float Metallic;

	UPROPERTY()
	FString Tex_Metallic;

	UPROPERTY()
	float MetallicMultiply;

	UPROPERTY()
	bool Enable_Roughness;

	UPROPERTY()
	float Roughness;

	UPROPERTY()
	FString Tex_RoughPack;

	UPROPERTY()
	float RoughnessMultiply;

	UPROPERTY()
	bool Enable_EmissiveColor;

	UPROPERTY()
	FColor EmissiveColor;

	UPROPERTY()
	FString Tex_EmissiveMask;

	UPROPERTY()
	bool Enable_Opacity;

	UPROPERTY()
	float Opacity;

	UPROPERTY()
	FString Tex_Opacity;

	UPROPERTY()
	FString Tex_Normal;

	UPROPERTY()
	float NormalMultiply;

	UPROPERTY()
	float UVOffsetU;

	UPROPERTY()
	float UVOffsetV;

	UPROPERTY()
	float UVTileU;

	UPROPERTY()
	float UVTileV;

	UPROPERTY()
	float UVRotation;

	UPROPERTY()
	FString Parent;

public:
	FDSImportMaterialJsonData()
		: BaseColor_Colorize(FColor::White)
		, Enable_Metallic(false)
		, Metallic(0.0f)
		, MetallicMultiply(1.0f)
		, Enable_Roughness(false)
		, Roughness(0.0f)
		, RoughnessMultiply(1.0f)
		, Enable_EmissiveColor(false)
		, EmissiveColor(FColor::White)
		, Enable_Opacity(false)
		, Opacity(1.0f)
		, NormalMultiply(1.0f)
		, UVOffsetU(0.0f)
		, UVOffsetV(0.0f)
		, UVTileU(1.0f)
		, UVTileV(1.0f)
		, UVRotation(0.0f)
		, Parent(TEXT(""))
	{
	}
};



UCLASS()
class DESIGNSTATION_API UResourceSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

private:
	/*
	*  @@ cache material instance dynamic
	*  @@ key: defalut material path / folder_id
	*  @@ value: material instance dynamic
	*/
	UPROPERTY()
	TMap<FString, UMaterialInstanceDynamic*> MaterialInsDynamic;

	UPROPERTY()
	TArray<FCSModelMatData> MaterialWebData;


	/*
	*  @@ cache model data
	* 
	*/

	UPROPERTY()
	TArray<FCSModelMatData> ModelWebData;

private:
	void Init_Local();
	void Deinit_Local();

	FPakPlatformFile* GetPakPlatformFile() const;

public:
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	static UResourceSubsystem* GetInstance();

	UMaterialInstanceDynamic* GetDefaultMaterialInsDynamic();
	UMaterialInstanceDynamic* GetMaterialInsDynamic(const FString& MaterialPath);

	/*
	 *  @@ Get Texture2D by TexturePath[absolute path]
	 *  @@ special logic : consider extension [jpeg, jpg, png]
	 */
	UTexture2D* GetImageTexture2D(const FString& TexturePath);

	bool IsMatFile(const FString& Path);

	UTexture2D* GetImageTexture2D_NewExtension(const FString& TexturePath, const FString& NewExtension);

	void AddMaterialWebData(const TArray<FCSModelMatData>& InMatData, const TArray<FCSModelMatData>& NeedRedreshMat);
	UMaterialInstanceDynamic* GetMaterialInsDynamic_Web(const FString& FolderId);

	void AddModelWebData(const TArray<FCSModelMatData>& InModelData);

	/*
	*  @@ sync model web data 
	*  @@ sometimes, folderID changed, so we need to sync cache data
	*/
	void SyncModelWebData(const TArray<FExpressionValuePair>& FolderIDPair, const TArray<FCSModelMatData>& NetData);

	void MountDependentPakFiles(const TArray<FCSModelMatData>& InModelData);
	/*
	*  @@ InPakFilePath : pak file absolute path
	*  @@ RetVal : true if mount / unmount success
	*/
	bool MountPakFile(const FString& InPakFilePath);
	bool UnmountPakFile(const FString& InPakFilePath);
};
