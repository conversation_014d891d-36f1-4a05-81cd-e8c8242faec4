// Fill out your copyright notice in the Description page of Project Settings.


#include "UIManagerSubsystem.h"


void UUIManagerSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	ClearAllRef();
}

void UUIManagerSubsystem::Deinitialize()
{
	ClearAllRef();

	Super::Deinitialize();
}

void UUIManagerSubsystem::ClearAllRef()
{
	LoginUIWidget = nullptr;
	MainUILayout = nullptr;
	ToolBarUI = nullptr;
	BreadNavigationUI = nullptr;
	FolderLayoutUI = nullptr;
	FolderAndFileListUI = nullptr;
	FolderAndFilePropertyUI = nullptr;
	StyleLayoutUI = nullptr;
	PersonalCenterWidget = nullptr;
}
