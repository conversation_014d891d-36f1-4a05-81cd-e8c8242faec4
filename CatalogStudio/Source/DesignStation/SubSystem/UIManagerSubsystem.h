// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/UI/Widget_Login.h"
#include "DesignStation/UI/GeneralWidgets/MainLayoutWidget.h"
#include "DesignStation/UI/GeneralWidgets/StyleLayoutWidget.h"
#include "DesignStation/UI/MainUI/BreadNavigationWidget.h"
#include "DesignStation/UI/MainUI/FolderAndFilePropertyWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "DesignStation/UI/MainUI/MainToolBarWidget.h"
#include "DesignStation/UI/Personal/PersonalCenterWidget.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "UIManagerSubsystem.generated.h"



/**
 *此子系统用于缓存UI ptr信息，需要手动清空缓存
 */
UCLASS()
class DESIGNSTATION_API UUIManagerSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	void ClearAllRef();

	void SetLoginWidget(UWidget_Login* InWidget) { LoginUIWidget = InWidget; }

	UWidget_Login* GetLoginWidget() { return LoginUIWidget; }

	void SetMainUILayoutWidget(UMainLayoutWidget* InWidget) { MainUILayout = InWidget; }

	UMainLayoutWidget* GetMainUILayoutWidget() { return MainUILayout; }

	void SetMainToolBarWidget(UMainToolBarWidget* InWidget) { ToolBarUI = InWidget; }

	UMainToolBarWidget* GetMainToolBarWidget() { return ToolBarUI; }

	void SetNavigationWidget(UBreadNavigationWidget* InWidget) { BreadNavigationUI = InWidget; }

	UBreadNavigationWidget* GetNavigationWidget() { return BreadNavigationUI; }

	void SetFolderWidget(UFolderWidget* InWidget) { FolderLayoutUI = InWidget; }

	UFolderWidget* GetFolderWidget() { return FolderLayoutUI; }

	void SetFolderAndFileListWidget(UFolderAndFileListWidget* InWidget) { FolderAndFileListUI = InWidget; }

	UFolderAndFileListWidget* GetFolderAndFileListWidget() { return FolderAndFileListUI; }

	void SetFolderAndFilePropertyWidget(UFolderAndFilePropertyWidget* InWidget) { FolderAndFilePropertyUI = InWidget; }

	UFolderAndFilePropertyWidget* GetFolderAndFilePropertyWidget() { return FolderAndFilePropertyUI; }

	void SetStyleLayoutWidget(UStyleLayoutWidget* InWidget) { StyleLayoutUI = InWidget; }

	UStyleLayoutWidget* GetStyleLayoutWidget() { return StyleLayoutUI; }

	void SetPersonalCenterWidget(UPersonalCenterWidget* InWidget) { PersonalCenterWidget = InWidget; }

	UPersonalCenterWidget* GetPersonalCenterWidget() { return PersonalCenterWidget; }

private:
	UPROPERTY()
		UWidget_Login* LoginUIWidget;
	UPROPERTY()
		UMainLayoutWidget* MainUILayout;
	UPROPERTY()
		UMainToolBarWidget* ToolBarUI;
	UPROPERTY()
		UBreadNavigationWidget* BreadNavigationUI;
	UPROPERTY()
		UFolderWidget* FolderLayoutUI;
	UPROPERTY()
		UFolderAndFileListWidget* FolderAndFileListUI;
	UPROPERTY()
		UFolderAndFilePropertyWidget* FolderAndFilePropertyUI;
	UPROPERTY()
		UStyleLayoutWidget* StyleLayoutUI;
	UPROPERTY()
		UPersonalCenterWidget* PersonalCenterWidget;

};
