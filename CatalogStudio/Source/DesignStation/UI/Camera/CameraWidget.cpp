// Fill out your copyright notice in the Description page of Project Settings.

#include "CameraWidget.h"

#include "Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Border.h"

FString UCameraWidget::CameraWidgetPath = TEXT("WidgetBlueprint'/Game/UI/CameraUI/CameraWidget.CameraWidget_C'");

bool UCameraWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnFrontView, Btn_FrontView);
	BIND_PARAM_CPP_TO_UMG(BtnRearView, Btn_RearView);
	BIND_PARAM_CPP_TO_UMG(BtnLeftView, Btn_LeftView);
	BIND_PARAM_CPP_TO_UMG(BtnRightView, Btn_RightView);
	BIND_PARAM_CPP_TO_UMG(BtnTopView, Btn_TopView);
	BIND_PARAM_CPP_TO_UMG(BtnTLFView, Btn_TLFView);
	BIND_PARAM_CPP_TO_UMG(BtnTLRView, Btn_TLRView);
	BIND_PARAM_CPP_TO_UMG(BtnTRFView, Btn_TRFView);
	BIND_PARAM_CPP_TO_UMG(BtnTRRView, Btn_TRRView);
	BIND_PARAM_CPP_TO_UMG(BtnImport, Btn_Import);
	BIND_PARAM_CPP_TO_UMG(BtnSure, Btn_Sure);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);

	BIND_PARAM_CPP_TO_UMG(TxtFrontView, Txt_FrontView);
	BIND_PARAM_CPP_TO_UMG(TxtRearView, Txt_RearView);
	BIND_PARAM_CPP_TO_UMG(TxtLeftView, Txt_LeftView);
	BIND_PARAM_CPP_TO_UMG(TxtRightView, Txt_RightView);
	BIND_PARAM_CPP_TO_UMG(TxtTopView, Txt_TopView);
	BIND_PARAM_CPP_TO_UMG(TxtTLFView, Txt_TLFView);
	BIND_PARAM_CPP_TO_UMG(TxtTLRView, Txt_TLRView);
	BIND_PARAM_CPP_TO_UMG(TxtTRFView, Txt_TRFView);
	BIND_PARAM_CPP_TO_UMG(TxtTRRView, Txt_TRRView);
	BIND_PARAM_CPP_TO_UMG(TxtImport, Txt_Import);
	BIND_PARAM_CPP_TO_UMG(TxtCancel, Txt_Cancel);

	BIND_PARAM_CPP_TO_UMG(ImgImport, Img_Import);

	BIND_WIDGET_FUNCTION(BtnFrontView, OnClicked, UCameraWidget::OnClickedFrontView);
	BIND_WIDGET_FUNCTION(BtnFrontView, OnReleased, UCameraWidget::OnReleasedFrontView);
	BIND_WIDGET_FUNCTION(BtnFrontView, OnPressed, UCameraWidget::OnPressedFrontView);


	BIND_WIDGET_FUNCTION(BtnRearView, OnClicked, UCameraWidget::OnClickedRearView);
	BIND_WIDGET_FUNCTION(BtnRearView, OnReleased, UCameraWidget::OnReleasedBtnRearView);
	BIND_WIDGET_FUNCTION(BtnRearView, OnPressed, UCameraWidget::OnPressedBtnRearView);

	BIND_WIDGET_FUNCTION(BtnLeftView, OnClicked, UCameraWidget::OnClickedLeftView);
	BIND_WIDGET_FUNCTION(BtnLeftView, OnReleased, UCameraWidget::OnReleasedBtnLeftView);
	BIND_WIDGET_FUNCTION(BtnLeftView, OnPressed, UCameraWidget::OnPressedBtnLeftView);

	BIND_WIDGET_FUNCTION(BtnRightView, OnClicked, UCameraWidget::OnClickedRightView);
	BIND_WIDGET_FUNCTION(BtnRightView, OnReleased, UCameraWidget::OnReleasedBtnRightView);
	BIND_WIDGET_FUNCTION(BtnRightView, OnPressed, UCameraWidget::OnPressedBtnRightView);

	BIND_WIDGET_FUNCTION(BtnTopView, OnClicked, UCameraWidget::OnClickedTopView);
	BIND_WIDGET_FUNCTION(BtnTopView, OnReleased, UCameraWidget::OnReleasedBtnTopView);
	BIND_WIDGET_FUNCTION(BtnTopView, OnPressed, UCameraWidget::OnPressedBtnTopView);

	BIND_WIDGET_FUNCTION(BtnTLFView, OnClicked, UCameraWidget::OnClickedTLFView);
	BIND_WIDGET_FUNCTION(BtnTLFView, OnReleased, UCameraWidget::OnReleasedBtnTLFView);
	BIND_WIDGET_FUNCTION(BtnTLFView, OnPressed, UCameraWidget::OnPressedBtnTLFView);

	BIND_WIDGET_FUNCTION(BtnTLRView, OnClicked, UCameraWidget::OnClickedTLRView);
	BIND_WIDGET_FUNCTION(BtnTLRView, OnReleased, UCameraWidget::OnReleasedBtnTLRView);
	BIND_WIDGET_FUNCTION(BtnTLRView, OnPressed, UCameraWidget::OnPressedBtnTLRView);

	BIND_WIDGET_FUNCTION(BtnTRFView, OnClicked, UCameraWidget::OnClickedTRFView);
	BIND_WIDGET_FUNCTION(BtnTRFView, OnReleased, UCameraWidget::OnReleasedBtnTRFView);
	BIND_WIDGET_FUNCTION(BtnTRFView, OnPressed, UCameraWidget::OnPressedBtnTRFView);

	BIND_WIDGET_FUNCTION(BtnTRRView, OnClicked, UCameraWidget::OnClickedTRRView);
	BIND_WIDGET_FUNCTION(BtnTRRView, OnReleased, UCameraWidget::OnReleasedBtnTRRView);
	BIND_WIDGET_FUNCTION(BtnTRRView, OnPressed, UCameraWidget::OnPressedBtnTRRView);

	BIND_WIDGET_FUNCTION(BtnImport, OnClicked, UCameraWidget::OnClickedImport);
	BIND_WIDGET_FUNCTION(BtnImport, OnReleased, UCameraWidget::OnReleasedBtnImport);
	BIND_WIDGET_FUNCTION(BtnImport, OnPressed, UCameraWidget::OnPressedBtnImport);

	BIND_WIDGET_FUNCTION(BtnSure, OnClicked, UCameraWidget::OnClickedSure);

	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UCameraWidget::OnClickedCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnReleased, UCameraWidget::OnReleasedBtnCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnPressed, UCameraWidget::OnPressedBtnCancel);

	return true;
}

UCameraWidget* UCameraWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UCameraWidget>(UCameraWidget::CameraWidgetPath);
}

void UCameraWidget::OnClickedFrontView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::FrontView);
}

void UCameraWidget::OnClickedRearView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::RearView);
}

void UCameraWidget::OnClickedLeftView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::LeftView);
}

void UCameraWidget::OnClickedRightView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::RightView);
}

void UCameraWidget::OnClickedTopView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::TopView);
}

void UCameraWidget::OnClickedTLFView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::TLFView);
}

void UCameraWidget::OnClickedTLRView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::TLRView);
}

void UCameraWidget::OnClickedTRFView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::TRFView);
}

void UCameraWidget::OnClickedTRRView()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::TRRView);
}

void UCameraWidget::OnClickedImport()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::Import);
}

void UCameraWidget::OnClickedSure()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::Sure);
}

void UCameraWidget::OnClickedCancel()
{
	CameraBtnTypeDelegate.ExecuteIfBound((int32)ECameraBtnType::Cancel);
}

void UCameraWidget::OnReleasedFrontView()
{
	if (IS_OBJECT_PTR_VALID(TxtFrontView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtFrontView);
	}
}

void UCameraWidget::OnReleasedBtnRearView()
{
	if (IS_OBJECT_PTR_VALID(TxtRearView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtRearView);
	}
}

void UCameraWidget::OnReleasedBtnLeftView()
{
	if (IS_OBJECT_PTR_VALID(TxtLeftView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtLeftView);
	}
}

void UCameraWidget::OnReleasedBtnRightView()
{
	if (IS_OBJECT_PTR_VALID(TxtRightView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtRightView);
	}
}

void UCameraWidget::OnReleasedBtnTopView()
{
	if (IS_OBJECT_PTR_VALID(TxtTopView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtTopView);
	}
}

void UCameraWidget::OnReleasedBtnTLFView()
{
	if (IS_OBJECT_PTR_VALID(TxtTLFView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtTLFView);
	}
}

void UCameraWidget::OnReleasedBtnTLRView()
{
	if (IS_OBJECT_PTR_VALID(TxtTLRView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtTLRView);
	}
}

void UCameraWidget::OnReleasedBtnTRFView()
{
	if (IS_OBJECT_PTR_VALID(TxtTRFView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtTRFView);
	}
}

void UCameraWidget::OnReleasedBtnTRRView()
{
	if (IS_OBJECT_PTR_VALID(TxtTRRView))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtTRRView);
	}
}

void UCameraWidget::OnReleasedBtnImport()
{
	if (IS_OBJECT_PTR_VALID(TxtImport))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtImport);
	}
}

void UCameraWidget::OnReleasedBtnCancel()
{
	if (IS_OBJECT_PTR_VALID(TxtCancel))
	{
		UUIFunctionLibrary::SetTextColor(TextNormal, TxtCancel);
	}
}

void UCameraWidget::OnPressedFrontView()
{
	if (IS_OBJECT_PTR_VALID(TxtFrontView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtFrontView);
	}
}

void UCameraWidget::OnPressedBtnRearView()
{
	if (IS_OBJECT_PTR_VALID(TxtRearView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtRearView);
	}
}

void UCameraWidget::OnPressedBtnLeftView()
{
	if (IS_OBJECT_PTR_VALID(TxtLeftView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtLeftView);
	}
}

void UCameraWidget::OnPressedBtnRightView()
{
	if (IS_OBJECT_PTR_VALID(TxtRightView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtRightView);
	}
}

void UCameraWidget::OnPressedBtnTopView()
{
	if (IS_OBJECT_PTR_VALID(TxtTopView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtTopView);
	}
}

void UCameraWidget::OnPressedBtnTLFView()
{
	if (IS_OBJECT_PTR_VALID(TxtTLFView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtTLFView);
	}
}

void UCameraWidget::OnPressedBtnTLRView()
{
	if (IS_OBJECT_PTR_VALID(TxtTLRView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtTLRView);
	}
}

void UCameraWidget::OnPressedBtnTRFView()
{
	if (IS_OBJECT_PTR_VALID(TxtTRFView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtTRFView);
	}
}

void UCameraWidget::OnPressedBtnTRRView()
{
	if (IS_OBJECT_PTR_VALID(TxtTRRView))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtTRRView);
	}
}

void UCameraWidget::OnPressedBtnImport()
{
	if (IS_OBJECT_PTR_VALID(TxtImport))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtImport);
	}
}

void UCameraWidget::OnPressedBtnCancel()
{
	if (IS_OBJECT_PTR_VALID(TxtCancel))
	{
		UUIFunctionLibrary::SetTextColor(FLinearColor::White, TxtCancel);
	}
}

void UCameraWidget::SetBtnState(const int32& Type, bool IsEnable)
{
	switch (Type)
	{
	case (int32)ECameraBtnType::FrontView:
		BtnFrontView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::RearView:
		BtnRearView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::LeftView:
		BtnLeftView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::RightView:
		BtnRightView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::TopView:
		BtnTopView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::TLFView:
		BtnTLFView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::TLRView:
		BtnTLRView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::TRFView:
		BtnTRFView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::TRRView:
		BtnTRRView->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::Import:
		BtnImport->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::Sure:
		BtnSure->SetIsEnabled(IsEnable);
		break;
	case (int32)ECameraBtnType::Cancel:
		BtnCancel->SetIsEnabled(IsEnable);
		break;
	default:
		break;
	}
}

void UCameraWidget::SetImage(const FString& ImagePath)
{
	UTexture2D* NewTex = FCatalogFunctionLibrary::LoadTextureFromJPG(ImagePath);
	UE_LOG(LogTemp, Error, TEXT("UCameraWidget::SetImage %s %s"), *ImagePath, nullptr == NewTex ? TEXT("failed") : TEXT("success"));
	ImgImport->SetBrushFromTexture(NewTex);
	BD_ImportBackground->SetBrushColor(FLinearColor::White);
}

void UCameraWidget::ShowImage(bool IsShow)
{
	if (IsShow)
	{
		ImgImport->SetVisibility(ESlateVisibility::Visible);
		BD_ImportBackground->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ImgImport->SetVisibility(ESlateVisibility::Collapsed);
		BD_ImportBackground->SetVisibility(ESlateVisibility::Hidden);
	}
}
