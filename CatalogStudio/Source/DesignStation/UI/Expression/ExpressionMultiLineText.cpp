// Fill out your copyright notice in the Description page of Project Settings.


#include "ExpressionMultiLineText.h"
#include "Widgets/Text/SMultiLineEditableText.h"
FTextLocation UExpressionMultiLineText::GetCursorLocation()
{
	return MyMultiLineEditableText->GetCursorLocation();
}

void UExpressionMultiLineText::SetCursorLocation(const FTextLocation& InTextLocation)
{
	MyMultiLineEditableText->GoTo(InTextLocation);
}

void UExpressionMultiLineText::InsertByCursorLocation(const FText& Content, const FTextLocation& InTextLocation)
{
	//TODO: If has SelectedText ,Replace SelectedText With InContent
	FString TextStr =  MyMultiLineEditableText->GetText().ToString();
	TArray<FString> Lines;
	TextStr.ParseIntoArrayLines(Lines);
	int32 LineIndex = InTextLocation.GetLineIndex();
	if (Lines.IsValidIndex(LineIndex))
	{
		FString& TargetLine = Lines[LineIndex];
		TargetLine.InsertAt(InTextLocation.GetOffset(), Content.ToString());
		FString NewTextString = FString::Join(Lines, TEXT("\n"));
		MyMultiLineEditableText->SetText(FText::FromString(NewTextString));
		FTextLocation NewTextLocation(LineIndex, InTextLocation.GetOffset() + Content.ToString().Len());
		MyMultiLineEditableText->GoTo(NewTextLocation);
	}
}
