// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/MultiLineEditableText.h"
#include "ExpressionMultiLineText.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UExpressionMultiLineText : public UMultiLineEditableText
{
	GENERATED_BODY()
	
	
public:
	FTextLocation GetCursorLocation();

	void SetCursorLocation(const FTextLocation& InTextLocation);

	void InsertByCursorLocation(const FText& Content, const FTextLocation& InTextLocation);
};
