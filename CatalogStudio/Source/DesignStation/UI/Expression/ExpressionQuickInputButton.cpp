// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Expression/ExpressionQuickInputButton.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"

void UExpressionQuickInputButton::SetExpressionQuickInputData(const FCatalogExpressionDescriptor& InData)
{
	ExpressionQuickInputData = InData;
	ToolTipText = InData.ToolTip;
	SetShowName(InData.DisplayText);
}

void UExpressionQuickInputButton::SetShowName(const FText& InShowName)
{
	Name->SetText(InShowName);
}

void UExpressionQuickInputButton::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	Button->OnClicked.AddDynamic(this, &ThisClass::OnButtonClick);
}

void UExpressionQuickInputButton::OnButtonClick()
{
	OnExpressionQuickInputButtonClickDel.ExecuteIfBound(ExpressionQuickInputData.Operator);
}
