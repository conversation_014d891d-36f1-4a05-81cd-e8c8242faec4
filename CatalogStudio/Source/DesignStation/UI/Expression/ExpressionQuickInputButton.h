// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "CatalogExpressionDescriptor.h"
#include "ExpressionQuickInputButton.generated.h"


class UButton;
class UTextBlock;
DECLARE_DYNAMIC_DELEGATE_OneParam(FOnExpressionQuickInputButtonClick ,const FText&, Code);
/**
 * 
 */
UCLASS(Blueprintable)
class DESIGNSTATION_API UExpressionQuickInputButton : public UUserWidget
{
	GENERATED_BODY()

public:
	void SetExpressionQuickInputData(const FCatalogExpressionDescriptor& InData);

	void SetShowName(const FText& InShowNameName);
protected:
	virtual void NativeOnInitialized() override;

	UFUNCTION()
	void OnButtonClick();
public:
	FOnExpressionQuickInputButtonClick OnExpressionQuickInputButtonClickDel;
protected:
	UPROPERTY()
	FCatalogExpressionDescriptor ExpressionQuickInputData;


	UPROPERTY(meta = (BindWidget))
	UButton* Button;

	UPROPERTY(meta = (BindWidget))
	UTextBlock* Name;
};
