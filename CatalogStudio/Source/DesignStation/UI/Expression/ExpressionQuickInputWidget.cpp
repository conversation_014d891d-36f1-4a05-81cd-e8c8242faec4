// Fill out your copyright notice in the Description page of Project Settings.


#include "ExpressionQuickInputWidget.h"
#include "Components/WrapBox.h"
#include "DesignStation/UI/Expression/ExpressionQuickInputButton.h"
#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"
#include "CatalogExpressionDescriptor.h"

void UExpressionQuickInputWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	TArray<FCatalogExpressionDescriptor> ExpressionDescriptors =  UCatalogExpressionFunctionLibrary::CollectAllDescriptors();

	DefaultExpressionButton = { 
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Sin)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Cos)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Tan)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::ArcTan2)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Sqrt)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Power)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Round)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Add)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Minus)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Multiply)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Divide)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Or)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::And)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::ToDeg)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::ToRad)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Greater)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Less)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::Equal)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::LessOrEqual)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::GreaterOrEqual)),
		FExpressionType(false,static_cast<uint8>(EExpressionMathOperatorType::NotEqual)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::If)),
		FExpressionType(true,static_cast<uint8>(EExpressionFunctionType::Condition)),
	};

	for (auto& ExpressionType: DefaultExpressionButton)
	{

		 int32 Index = ExpressionDescriptors.IndexOfByPredicate([ExpressionType](const FCatalogExpressionDescriptor& InExpressionDescriptor)
			{
				if (InExpressionDescriptor.bIsFunctionCall == ExpressionType.bIsFunctionalCall
					&& InExpressionDescriptor.Type == ExpressionType.Value)
				{
					return true;
				}
				return false;
			});
		if (Index != INDEX_NONE)
		{
			UExpressionQuickInputButton* ButtonWidget = CreateWidget<UExpressionQuickInputButton>(this, ButtonClass);
			ButtonWidget->SetExpressionQuickInputData(ExpressionDescriptors[Index]);
			ButtonWidget->OnExpressionQuickInputButtonClickDel.BindDynamic(this, &ThisClass::OnExpressionQuickInputButtonClick);
			WrapBox->AddChild(ButtonWidget);
			DefaultExpressionQuickInputButton.Add(ButtonWidget);
			ExpressionDescriptors.RemoveAt(Index);
		}
	}
	for (const auto& OtherDescriptor : ExpressionDescriptors)
	{
		UExpressionQuickInputButton* ButtonWidget = CreateWidget<UExpressionQuickInputButton>(this, ButtonClass);
		ButtonWidget->SetExpressionQuickInputData(OtherDescriptor);
		ButtonWidget->OnExpressionQuickInputButtonClickDel.BindDynamic(this, &ThisClass::OnExpressionQuickInputButtonClick);
		WrapBox->AddChild(ButtonWidget);
		MoreExpressionQuickInputButton.Add(ButtonWidget);
	}
	
	MoreButtonWidget = CreateWidget<UExpressionQuickInputButton>(this, ButtonClass, FName(TEXT("更多")));
	MoreButtonWidget->OnExpressionQuickInputButtonClickDel.BindDynamic(this, &ThisClass::OnMoreButtonClick);
	MoreButtonWidget->SetShowName(NSLOCTEXT("CatalogExpression", "CatalogExpressionMore", "More"));
	WrapBox->AddChild(MoreButtonWidget);

	OnSwitchAllExpressionButtonShow(bShowAllExpressionQuickInputButton);
}

void UExpressionQuickInputWidget::OnExpressionQuickInputButtonClick(const FText& InQuickInputStr)
{
	GetExpressionQuickInputStr.ExecuteIfBound(InQuickInputStr);
}

void UExpressionQuickInputWidget::OnMoreButtonClick(const FText& InQuickInputStr)
{
	bShowAllExpressionQuickInputButton = !bShowAllExpressionQuickInputButton;
	OnSwitchAllExpressionButtonShow(bShowAllExpressionQuickInputButton);
}

void UExpressionQuickInputWidget::OnSwitchAllExpressionButtonShow(bool bInShowAllExpressionButton)
{
	if (bShowAllExpressionQuickInputButton)
	{
		for (auto& MoreButton : MoreExpressionQuickInputButton)
		{
			MoreButton->SetVisibility(ESlateVisibility::Visible);
		}
		MoreButtonWidget->SetShowName(NSLOCTEXT("CatalogExpression", "CatalogExpressionCollapse", "Collapse"));
	}
	else
	{
		for (auto& MoreButton : MoreExpressionQuickInputButton)
		{
			MoreButton->SetVisibility(ESlateVisibility::Collapsed);
		}
		MoreButtonWidget->SetShowName(NSLOCTEXT("CatalogExpression", "CatalogExpressionMore", "More"));
	}
}
