// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ExpressionQuickInputWidget.generated.h"


DECLARE_DYNAMIC_DELEGATE_OneParam(FGetExpressionQuickInput, const FText&, QuickInputStr);
class UWrapBox;
class  UExpressionQuickInputButton;
/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UExpressionQuickInputWidget : public UUserWidget
{
	GENERATED_BODY()

protected:
	virtual void NativeOnInitialized() override;

	UFUNCTION()
	void OnExpressionQuickInputButtonClick(const FText& InQuickInputStr);
	UFUNCTION()
	void OnMoreButtonClick(const FText& InQuickInputStr);

	UFUNCTION()
	void OnSwitchAllExpressionButtonShow(bool bInShowAllExpressionButton);
public:
	FGetExpressionQuickInput GetExpressionQuickInputStr;
protected:
	UPROPERTY( BlueprintReadWrite, EditAnywhere, Category = "Appearance")
	TSubclassOf<UExpressionQuickInputButton> ButtonClass;

	UPROPERTY(BlueprintReadOnly, Category = "Appearance")
	bool bShowAllExpressionQuickInputButton;

	UPROPERTY(BlueprintReadOnly, EditAnywhere,meta = (BindWidget))
	UWrapBox* WrapBox;

	UPROPERTY()
	UExpressionQuickInputButton* MoreButtonWidget;
	struct FExpressionType
	{
		bool bIsFunctionalCall;
		uint8 Value;
	};
	TArray<FExpressionType> DefaultExpressionButton;

	TArray<UExpressionQuickInputButton*> DefaultExpressionQuickInputButton;

	TArray<UExpressionQuickInputButton*> MoreExpressionQuickInputButton;
};
