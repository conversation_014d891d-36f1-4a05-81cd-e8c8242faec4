// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderAndFileBaseWidget.h"

#include "Components/HorizontalBoxSlot.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"

class UOperationOnHoldWidget;
extern const int PopUIZOrder;

//bool UFolderAndFileBaseWidget::Initialize()
//{
//	if (!Super::Initialize())
//	{
//		return false;
//	}
//
//	BindDelegate();
//
//	IsSuperiorVisible = true;
//	return true;
//}

void UFolderAndFileBaseWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BindDelegate();

	IsSuperiorVisible = true;
}

void UFolderAndFileBaseWidget::SetIsSelected(bool _IsSelected)
{
	IsSelected = _IsSelected;
	SetFolderOrFileBackBorderColor(IsSelected ? EBackBorderState::Select : EBackBorderState::Normal);
}

bool UFolderAndFileBaseWidget::IsParamAType()
{
	return false;
}

void UFolderAndFileBaseWidget::SelectSelf()
{
}

void UFolderAndFileBaseWidget::UpdateFolderState(bool _IsVisible)
{
	IsVisible = _IsVisible;
	UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? NormalTextColor : InVisibleTextColor, TxtName);
}

FString UFolderAndFileBaseWidget::GetParamId()
{
	return TEXT("-1");
}

int32 UFolderAndFileBaseWidget::GetParamClassificID()
{
	return -1;
}

FString UFolderAndFileBaseWidget::GetFolderItemId()
{
	return TEXT("");
}

void UFolderAndFileBaseWidget::SetFolderOrFileBackBorderColor(const EBackBorderState& BorType, bool IsFolderTree)
{
	if (IS_OBJECT_PTR_VALID(BorFolderOrFile) && IS_OBJECT_PTR_VALID(TxtName))
	{
		if (BorType == EBackBorderState::Normal)
		{
			UUIFunctionLibrary::SetBorderBrushColor(FolderOrFileNormal, BorFolderOrFile);
			TxtName->SetColorAndOpacity(FSlateColor((IsVisible && IsSuperiorVisible) ? NameNormalOrHover : NameInVisible));
		}
		else if (BorType == EBackBorderState::Hover)
		{
			UUIFunctionLibrary::SetBorderBrushColor(((IsVisible && IsSuperiorVisible) ? FolderOrFileHover : InVisFolderOrFileHover), BorFolderOrFile);
			TxtName->SetColorAndOpacity(FSlateColor(FSlateColor((IsVisible && IsSuperiorVisible) ? NameNormalOrHover : NameInVisible)));
		}
		else if (BorType == EBackBorderState::Select)
		{
			UUIFunctionLibrary::SetBorderBrushColor(((IsVisible && IsSuperiorVisible) ? FolderOrFileSelect : InVisFolderOrFileSelect), BorFolderOrFile);
			TxtName->SetColorAndOpacity(FSlateColor(NameSelect));
		}
		else if (BorType == EBackBorderState::UnFold)
		{
			BorFolderOrFile->SetBrushColor(FLinearColor(0.204f, 0.204f, 0.204f, 0.6f));
			TxtName->SetColorAndOpacity(FSlateColor((IsVisible && IsSuperiorVisible) ? NameNormalOrHover : NameInVisible));
		}
	}
}

void UFolderAndFileBaseWidget::SetViewFolderOrFileBorderColor(const EBackBorderState& BorType)
{
	if (IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		SelectType = BorType;
		if (BorType == EBackBorderState::Normal)
		{
			UUIFunctionLibrary::SetBorderBrushColor(ViewFolderOrFileNormal, BorFolderOrFile);
			BorFolderOrFile->SetVisibility(ESlateVisibility::Collapsed);
		}
		else if (BorType == EBackBorderState::Hover)
		{
			if (IsGrey) return;
			UUIFunctionLibrary::SetBorderBrushColor(ViewFolderOrFileHover, BorFolderOrFile);
			BorFolderOrFile->SetVisibility(ESlateVisibility::Visible);
		}
		else if (BorType == EBackBorderState::Select)
		{
			UUIFunctionLibrary::SetBorderBrushColor(ViewFolderOrFileSelect, BorFolderOrFile);
			BorFolderOrFile->SetVisibility(ESlateVisibility::Visible);
		}
	}
}

//void UFolderAndFileBaseWidget::RightClickMenuEdit(const EFolderAndFileRightMenu::Type& ActionType)
//{
//}

//void UFolderAndFileBaseWidget::OnRightMenuClickHandler(const ERightMenuType & ActionType)
//{
//}


void UFolderAndFileBaseWidget::RefreshVisibleState()
{
	GetFodlerImageBrush();
	UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? NormalTextColor : InVisibleTextColor, TxtName);
	SetIsSelected(IsSelected);
}

void UFolderAndFileBaseWidget::GetFodlerImageBrush()
{

	ImageOne = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/close_fnelei.close_fnelei'")));
	ImageTwo = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/zk3.zk3'")));
	/*ImageOne = ((IsVisible && IsSuperiorVisible) ?
		Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/jt_mr.jt_mr'"))) :
		Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/zk2.zk2'"))));
	ImageThree = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/zk3.zk3'")));
	ImageTwo = ((IsVisible && IsSuperiorVisible) ?
		Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/jt_mr2.jt_mr2'"))) :
		Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/zk4.zk4'"))));

	ImageFour = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/close_fnelei.close_fnelei'")));*/

}

void UFolderAndFileBaseWidget::SetIndent(const int32& LevelIndent)
{
	if (IS_OBJECT_PTR_VALID(SZIndent))
	{
		UHorizontalBoxSlot* SZIndentSlot = UWidgetLayoutLibrary::SlotAsHorizontalBoxSlot(SZIndent);
		SZIndentSlot->SetPadding(FMargin(LevelIndent * 10.0f + 8.0f, 0.0f, 0.0f, 0.0f));
	}
}


void UFolderAndFileBaseWidget::SetFolderOrder(const int32& InOrder)
{
	
}

void UFolderAndFileBaseWidget::ErrorProcess(const FString& ErrorMsg)
{
	UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
	OnHold->SetVisibility(ESlateVisibility::Collapsed);
	UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
		FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
		ErrorMsg
	);
}

FReply UFolderAndFileBaseWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	//if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	//{
	//	if (!IS_OBJECT_PTR_VALID(RightMenuWidget))
	//	{
	//		RightMenuWidget = URightMouseMenuWidget::Create();
	//		RightMenuWidget->AddToViewport(PopUIZOrder);
	//	}
	//	RightMenuWidget->UpdateContent(InMouseEvent.GetScreenSpacePosition());
	//	RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
	//	RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
	//	/*RightMenu = SNew(SRightMouseMenuWidget)
	//		.OwnerWidget(this);
	//	RightMenu->ConstructMenuContent();*/
	//	return FReply::Handled();
	//}
	return FReply::Unhandled();
}

void UFolderAndFileBaseWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryDeleteResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileBaseWidget::OnDeleteActionResponseHandle);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryCopyResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileBaseWidget::OnCopyActionResponseHandle);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryCutResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileBaseWidget::OnCutActionResponseHandle);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileBaseWidget::OnSearchActionResponseHandle);
}

FString UFolderAndFileBaseWidget::DeleteAction(const FString& ID)
{
	return UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryDeleteRequest({ID});
}

FString UFolderAndFileBaseWidget::CopyAction(const FString& OriginID, const FString& TargetID)
{
	if (UFolderWidget::Get()->CanExecuteCopyOperator(OriginID, TargetID))
	{
		return UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryCopyRequest(OriginID, TargetID);
	}
	else
	{
		UI_POP_WINDOW_ERROR_ST(TEXT("can not copy upper folder to child"));
	}

	return TEXT("");
}

bool UFolderAndFileBaseWidget::CanCopy(const FString& OriginID, const FString& TargetID)
{

	return true;
}

FString UFolderAndFileBaseWidget::CutAction(const FString& OriginID, const FString& TargetID)
{
	FRefDirectoryData CutModifyData;
	bool Res = UFolderWidget::Get()->ConstructCutActionData(OriginID, TargetID, CutModifyData);
	if(Res)
	{
		return UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryCutRequest(CutModifyData);
	}

	return TEXT("");
}
