// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "FolderFunctionLibrary.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DesignStation/UI/RightMouseMenu/RightMouseMenuWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/Engine/Classes/Engine/Texture2D.h"
#include "Components/CheckBox.h"
#include "FolderAndFileBaseWidget.generated.h"

struct FFolderTableData;

/**
 *
 */

UENUM(BlueprintType)
enum class EBackBorderState : uint8
{
	Normal = 0,
	Hover,
	Select,
	UnFold
};

const FLinearColor FolderOrFileNormal = FLinearColor(0.913099f, 0.913099f, 0.913099f, 1.0f);
const FLinearColor FolderOrFileHover = FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.3f);
const FLinearColor FolderOrFileSelect = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
const FLinearColor InVisFolderOrFileHover = FLinearColor(0.665387f, 0.048172f, 0.016807f, 0.3f);
const FLinearColor InVisFolderOrFileSelect = FLinearColor(0.665387f, 0.048172f, 0.016807f, 1.0f);

const FLinearColor NameNormalOrHover = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);
const FLinearColor NameSelect = FLinearColor::White;
const FLinearColor NameInVisible = FLinearColor(0.651406f, 0.03434f, 0.009134f, 1.0f);

const FLinearColor ViewFolderOrFileNormal = FLinearColor::White;
const FLinearColor ViewFolderOrFileHover = FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.3f);
const FLinearColor ViewFolderOrFileSelect = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);

DECLARE_DYNAMIC_DELEGATE_OneParam(FViewFolderOrFileOpenedDelegate, const FFolderTableData&, ViewItemData);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FViewItemRightActionDelegate, const FFolderTableData&, ViewItemData, const int32&, ActionType);

UCLASS()
class DESIGNSTATION_API UFolderAndFileBaseWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	//virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	virtual void SetIsSelected(bool _IsSelected);
	virtual bool IsParamAType();
	virtual void SelectSelf();
	virtual void UpdateFolderState(bool _IsVisible);
	virtual void RefreshVisibleState();
	virtual FString GetParamId();
	virtual int32 GetParamClassificID();

	virtual FString GetFolderItemId();

	virtual void SetFolderOrFileBackBorderColor(const EBackBorderState& BorType, bool IsFolderTree = true);
	virtual void SetViewFolderOrFileBorderColor(const EBackBorderState& BorType);

	UFUNCTION()
		virtual void FolderFileCopyFinishAction(const FString& ActionReturnID) {}
	virtual void CopyDirectActionByGameThread(const FString& ActionID) {}

	//virtual void RightClickMenuEdit(const EFolderAndFileRightMenu::Type& ActionType);

	/*virtual void OnRightMenuClickHandler(const ERightMenuType& ActionType);*/
	void GetFodlerImageBrush();
	void SetIndent(const int32& LevelIndent);
	bool GetIsSelected() const { return IsSelected; }
	bool GetSuperiorVisible() const { return IsSuperiorVisible; }
	void SetSuperiorVisible(bool _InVisible) { IsSuperiorVisible = _InVisible; }
	bool GetCurrentVisibleState() const { return IsVisible && IsSuperiorVisible; }
	virtual void SetFolderOrder(const int32& InOrder);

	void ErrorProcess(const FString& ErrorMsg);

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

#pragma region Net_File_Tree

protected:
	void BindDelegate();

	FString DeleteAction(const FString& ID);
	FString CopyAction(const FString& OriginID, const FString& TargetID);
	bool CanCopy(const FString& OriginID, const FString& TargetID);
	FString CutAction(const FString& OriginID, const FString& TargetID);

	UFUNCTION()
	virtual void OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData){}

	UFUNCTION()
	virtual void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData){}

	UFUNCTION()
	virtual void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData){}

	UFUNCTION()
	virtual void OnSearchActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) {}

protected:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

#pragma endregion

public:
	FViewItemRightActionDelegate ViewItemRightActionDelegate;
	FNoParamDelegate ParamItemRightClickDelegate;

public:
	bool IsSelected;
	//bool IsDelete;
	bool IsSubOpen;
	bool IsGrey;
	bool IsVisible;
	bool IsSuperiorVisible;

	UPROPERTY()
		UTexture2D* ImageOne;
	UPROPERTY()
		UTexture2D* ImageTwo;
	UPROPERTY()
		UTexture2D* ImageThree;
	UPROPERTY()
		UTexture2D* ImageFour;

public:
	UPROPERTY()
		USizeBox* SZIndent;
	UPROPERTY()
		UButton* BtnSelect;
	UPROPERTY()
		UBorder* BorFolderOrFile;
	UPROPERTY()
		UBorder* BorGrey;
	UPROPERTY()
		UTextBlock* TxtName;

public:
	UPROPERTY()
		URightMouseMenuWidget* RightMenuWidget;
	UPROPERTY()
	EBackBorderState SelectType;
	//	TSharedPtr<SRightMouseMenuWidget> RightMenu;
};
