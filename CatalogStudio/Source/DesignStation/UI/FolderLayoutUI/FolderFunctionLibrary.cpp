// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderFunctionLibrary.h"
#include "Runtime/UMG/Public/Blueprint/UserWidget.h"
#include "Widget_FolderItem.h"
#include "Widget_FileItem.h"
#include "Widget_ContentFolderItem.h"
#include "Widget_ContentFileItem.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"

FString UFolderFunctionLibrary::FolderItemPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/FolderItemUI.FolderItemUI_C'");
FString UFolderFunctionLibrary::FileItemPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/FileItemUI.FileItemUI_C'");

FString UFolderFunctionLibrary::ContentFolderItemPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/ContentFolderItem.ContentFolderItem_C'");
FString UFolderFunctionLibrary::ContentFileItemPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/ContentFileItem.ContentFileItem_C'");

FString UFolderFunctionLibrary::DetailFileInfoPath = TEXT("WidgetBlueprint'/Game/UI/DetailUI/FileInfoUI.FileInfoUI_C'");
FString UFolderFunctionLibrary::DetailFolderInfoPath = TEXT("WidgetBlueprint'/Game/UI/DetailUI/FolderInfoUI.FolderInfoUI_C'");

FString UFolderFunctionLibrary::PopAddFolderPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/Widget_PopAddFolder.Widget_PopAddFolder_C'");
FString UFolderFunctionLibrary::PopUserInfoPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/Widget_PopUserInfo.Widget_PopUserInfo_C'");
FString UFolderFunctionLibrary::PopErrorPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/Widget_PopError.Widget_PopError_C'");

UUserWidget* UFolderFunctionLibrary::CreateFolderLayoutItem(int FolderItemType)
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderFunctionLibrary::CreateFolderLayoutItem"));
	UUserWidget* ItemWidget = nullptr;
	switch ((ELayoutItemType)FolderItemType)
	{
	case ELayoutItemType::FolderItem :
	{
		if (UClass* FolderItemBp = LoadClass<UUserWidget>(NULL, *FolderItemPath))
		{
			ItemWidget = CreateWidget<UWidget_FolderItem>(GWorld.GetReference(), FolderItemBp);
		}
		break;
	}
	case ELayoutItemType::FileItem :
	{
		if (UClass* FileItemBp = LoadClass<UUserWidget>(NULL, *FileItemPath))
		{
			ItemWidget = CreateWidget<UWidget_FileItem>(GWorld.GetReference(), FileItemBp);
		}
		break;
	}
	case ELayoutItemType::ViewFolderItem :
	{
		if (UClass* ContentFolderItemBp = LoadClass<UUserWidget>(NULL, *ContentFolderItemPath))
		{
			ItemWidget = CreateWidget<UWidget_ContentFolderItem>(GWorld.GetReference(), ContentFolderItemBp);
		}
		break;
	}
	case ELayoutItemType::ViewFileItem :
	{
		if (UClass* ContentFileItemBp = LoadClass<UUserWidget>(NULL, *ContentFileItemPath))
		{
			ItemWidget = CreateWidget<UWidget_ContentFileItem>(GWorld.GetReference(), ContentFileItemBp);
		}
		break;
	}
	default:
	{
		break;
	}
	}

	return ItemWidget;
}

//UUserWidget* UFolderFunctionLibrary::CreateDetailFileInfo(int DetailInfoType)
//{
//	UUserWidget* ItemWidget = nullptr;
//	switch ((EDetailInfoType)DetailInfoType)
//	{
//	case EDetailInfoType::FileInfo :
//	{
//		if (UClass* DetailFileInfoBp = LoadClass<UUserWidget>(NULL, *DetailFileInfoPath))
//		{
//			ItemWidget = CreateWidget<UWidget_FileInfo>(GWorld, DetailFileInfoBp);
//		}
//		break;
//	}
//	case EDetailInfoType::FolderInfo :
//	{
//		if (UClass* DetailFolderInfoBp = LoadClass<UUserWidget>(NULL, *DetailFolderInfoPath))
//		{
//			ItemWidget = CreateWidget<UWidget_FolderInfo>(GWorld, DetailFolderInfoBp);
//		}
//		break;
//	}
//	default:
//		break;
//	}
//
//	return ItemWidget;
//}

UUserWidget * UFolderFunctionLibrary::CreatePopWidget(int PopWidgetType)
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderFunctionLibrary::CreatePopWidget"));
	UUserWidget* ItemWidget = nullptr;
	switch ((EPopWidgetType)PopWidgetType)
	{
	case EPopWidgetType::PopAddFolder :
	{
		break;
	}
	case EPopWidgetType::PopAddFile :
	{
		break;
	}
	case EPopWidgetType::PopUserInfo :
	{
		break;
	}
	case EPopWidgetType::PopError : 
	{
		break;
	}
	default:
		break;
	}

	return ItemWidget;
}

void UFolderFunctionLibrary::SetBorderColor(const FLinearColor& InColor, UBorder* InBorder)
{
	FSlateBrush CurrentBrush;
	CurrentBrush.Tiling = ESlateBrushTileType::NoTile;
	CurrentBrush.DrawAs = ESlateBrushDrawType::Image;
	CurrentBrush.Mirroring = ESlateBrushMirrorType::NoMirror;
	CurrentBrush.ImageType = ESlateBrushImageType::NoImage;
	CurrentBrush.TintColor = InColor;
	if (InBorder)
	{
		InBorder->SetBrush(CurrentBrush);
	}
}

void UFolderFunctionLibrary::SetTextColor(const FLinearColor & InColr, UTextBlock * InText)
{
	if (InText)
	{
		InText->SetColorAndOpacity(InColr);
	}
}

