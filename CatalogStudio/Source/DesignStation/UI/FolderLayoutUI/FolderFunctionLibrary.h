// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "FolderFunctionLibrary.generated.h"

/**
 * 
 */

const FLinearColor NormalColor = FLinearColor(0.955974f, 0.83077f, 0.679543f, 1.0);
const FLinearColor HoverColor = FLinearColor(1.0f, 0.0f, 1.0f, 1.0);
const FLinearColor SelectColor = FLinearColor(1.0f, 0.029557f, 0.029557f, 1.0);
const FLinearColor InVisibleColor = FLinearColor(0.83077f, 0.83077f, 0.83077f, 0.6f);
const FLinearColor InVisibleTextColor = FLinearColor::Red;
const FLinearColor NormalTextColor = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);

UENUM(BlueprintType)
enum class ELayoutItemType : uint8
{
	FolderItem = 0,
	FileItem,
	ViewFolderItem,
	ViewFileItem,
	None
};

UENUM(BlueprintType)
enum class EDetailInfoType : uint8
{
	FileInfo = 0,
	FolderInfo
};

UENUM(BlueprintType)
enum class EPopWidgetType : uint8
{
	PopAddFolder = 0,
	PopAddFile = 1,
	PopUserInfo = 2,
	PopError
};

UENUM(BlueprintType)
enum class EMouseClickType : uint8
{
	ClickNone = 0,
	ClickOnce
};

class UUserWidget;
class UBorder;
class UTextBlock;

UCLASS()
class DESIGNSTATION_API UFolderFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	static UUserWidget* CreateFolderLayoutItem(int FolderItemType);

	//static UUserWidget* CreateDetailFileInfo(int DetailInfoType);

	static UUserWidget* CreatePopWidget(int PopWidgetType);

	static void SetBorderColor(const FLinearColor& InColr, UBorder* InBorder);
	static void SetTextColor(const FLinearColor& InColr, UTextBlock* InText);
private:
	static FString FolderItemPath;
	static FString FileItemPath;

	static FString ContentFolderItemPath;
	static FString ContentFileItemPath;

	static FString DetailFileInfoPath;
	static FString DetailFolderInfoPath;

	static FString PopAddFolderPath;
	static FString PopUserInfoPath;
	static FString PopErrorPath;
};
