// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_ContentFileItem.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "FolderFunctionLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "ImageProcess/Public/ImageProcess.h"
#include <Operators/ProtobufOperatorFunctionLibrary.h>

int UWidget_ContentFileItem::ViewItemType = static_cast<int>(ELayoutItemType::ViewFileItem);

extern const int PopUIZOrder;
extern const FString RootParentID;

FString UWidget_ContentFileItem::WidgetContentFilePath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/ContentFileItem.ContentFileItem_C'");

bool UWidget_ContentFileItem::Initialize()
{
	if (!UFolderAndFileBaseWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UWidget_ContentFileItem::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UWidget_ContentFileItem::OnHoverBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UWidget_ContentFileItem::OnUnHoverBtnSelect);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FIleName);

	IsSelected = false;
	IsVisible = true;

	ResetSelectState();

	BindDelegate_Inner();
	//CurrentMouseClickType = (int)EMouseClickType::ClickNone;

	return true;
}

void UWidget_ContentFileItem::UpdateContent(const FFolderTableData& FolderData)
{
	ViewFileData = FolderData;
	UpdateFileName(FolderData.folder_name);
	UpdateFileThumbnail(FolderData.thumbnail_path);
}

void UWidget_ContentFileItem::UpdateFileName(const FString& InName, const FString& InThumbnail)
{
	if (TxtName)
	{
		FString FormatName = ViewFileData.folder_id.IsEmpty() ? InName : FString::Printf(TEXT("%s/%s"), *InName, *ViewFileData.folder_id);
		TxtName->SetText(FText::FromString(FormatName));
	}
	if (!InThumbnail.IsEmpty())
	{
		FRefDirectoryData RefData;
		URefRelationFunction::ConvertDBDataToDirctoryData(ViewFileData, RefData);
		if (URefRelationFunction::NeedDownloadFile(RefData))
		{
			SendDowmloadImageRequest(InThumbnail);
		}
		else
		{
			UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ConvertRelativePathToFull(
				FPaths::Combine(FPaths::ProjectContentDir(), InThumbnail)
			));
			if (FileImage)
			{
				Img_File->SetBrushFromTexture(FileImage);
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("load file thumbnail Error"));
			}
		}
	}
	else
	{
		SendDowmloadImageRequest(InThumbnail);
	}

	if (IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		BorFolderOrFile->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UWidget_ContentFileItem::UpdateFileName(const FString& InName)
{
	if (IS_OBJECT_PTR_VALID(TxtName))
	{
		FString FormatName = ViewFileData.folder_id.IsEmpty() ? InName : FString::Printf(TEXT("%s/%s"), *InName, *ViewFileData.folder_id);
		TxtName->SetText(FText::FromString(FormatName));
	}
}

void UWidget_ContentFileItem::UpdateFileThumbnail(const FString& InThumbnail)
{
	FRefDirectoryData CurData;
	if (!UFolderWidget::Get()->GetCacheDataForRefDirectory(ViewFileData.id, CurData))
	{
		URefRelationFunction::ConvertDBDataToDirctoryData(ViewFileData, CurData);
	}

	if (CurData.thumbnailPath.IsEmpty()) return;
	if (URefRelationFunction::NeedDownloadFile(CurData))
	{
		SendDowmloadImageRequest(InThumbnail);
	}
	else
	{
		LoadImage(InThumbnail);
	}	
}

void UWidget_ContentFileItem::ResetSelectState()
{
	SetIsSelected(false);
	/*IsSelected = false;
	CurrentMouseClickType = (int)EMouseClickType::ClickNone;
	if (BorBackground)
	{
		UFolderFunctionLibrary::SetBorderColor(NormalColor, BorBackground);
	}*/
}

void UWidget_ContentFileItem::UpdateFolderState(bool _IsVisible)
{
	IsVisible = _IsVisible;
	//UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? FLinearColor::White : InVisibleTextColor, TxtName);
	CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickNone);
	//if (BorFolderOrFile)
	//{
	//	UFolderFunctionLibrary::SetBorderColor((IsVisible && IsSuperiorVisible) ? NormalColor : InVisibleColor, BorFolderOrFile);
	//}
}

void UWidget_ContentFileItem::RefreshVisibleState()
{
	//UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? FLinearColor::White : InVisibleTextColor, TxtName);
	//if (BorFolderOrFile)
	//{
	//	UFolderFunctionLibrary::SetBorderColor((IsVisible && IsSuperiorVisible) ? NormalColor : InVisibleColor, BorFolderOrFile);
	//}
	SetIsSelected(IsSelected);
}

void UWidget_ContentFileItem::SetIsSelected(bool _IsSelect)
{
	IsSelected = _IsSelect;
	if (!_IsSelect)
	{
		CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickNone);
	}
	UFolderAndFileBaseWidget::SetViewFolderOrFileBorderColor(_IsSelect ? EBackBorderState::Select : EBackBorderState::Normal);
	Img_Choose->SetVisibility(_IsSelect ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
}

FString UWidget_ContentFileItem::GetFolderItemId()
{
	return FolderId;
}

void UWidget_ContentFileItem::FolderFileCopyFinishAction(const FString& ActionReturnID)
{
	UE_LOG(LogTemp, Warning, TEXT("[UWidget_ContentFileItem] --- [finish copy]"));

	if (IsInGameThread())
	{
		CopyDirectActionByGameThread(ActionReturnID);
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread,
			[this]()->void 
		{
			this->ViewItemRightActionDelegate.ExecuteIfBound(this->ViewFileData, static_cast<int32>(ERightMenuType::Paste));
		}
		);
	}

}

void UWidget_ContentFileItem::CopyDirectActionByGameThread(const FString& ActionID)
{
	ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Paste));
}

void UWidget_ContentFileItem::LoadImage(const FString& ImgPath)
{
	if (!ImgPath.IsEmpty())
	{
		UTexture2D* FileImage = FImageProcessModule::Get()->LoadTexture2DFromImage(FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), ImgPath)),230, 230);
		//UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), ImgPath)));
		if (FileImage)
		{
			Img_File->SetBrushFromTexture(FileImage);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("load file thumbnail Error"));
		}
	}
}

UWidget_ContentFileItem* UWidget_ContentFileItem::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UWidget_ContentFileItem::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UWidget_ContentFileItem>(UWidget_ContentFileItem::WidgetContentFilePath);
}

void UWidget_ContentFileItem::BindDelegate_Inner()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UWidget_ContentFileItem::OnDownloadImageHandler);
}

void UWidget_ContentFileItem::OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.DeleteUUID.Equals(UUID))
	{
		NetUUID.ResetDeleteAction();
		if(bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			UE_LOG(LogTemp, Log, TEXT("delete view file item, Item name : %s"), *ViewFileData.folder_name);
			this->SetVisibility(ESlateVisibility::Collapsed);
			this->RemoveFromParent();
			ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Delete));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
			//ErrorProcess(Msg);
		}
	}
}

void UWidget_ContentFileItem::OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CopyUUID.Equals(UUID))
	{
		NetUUID.ResetCopyAction();
		if(bSuccess/* && DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
			//ErrorProcess(Msg);
		}
	}
}

void UWidget_ContentFileItem::OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (NetUUID.CutUUID.Equals(UUID))
	{
		NetUUID.ResetCutAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//update cache 
			UFolderWidget::Get()->AddCacheDataForRefDirectory(DirData[0]);

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
			//ErrorProcess(Msg);	
		}
	}
}

void UWidget_ContentFileItem::SendDowmloadImageRequest(const FString& ImgPath)
{
	NetUUID.DownloadImage = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(ImgPath);
}

void UWidget_ContentFileItem::OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath)
{
	if (NetUUID.DownloadImage.Equals(UUID))
	{
		if (bSuccess && FilePath.IsValidIndex(0))
		{
			LoadImage(FilePath[0]);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file image error [%s]"), *ViewFileData.thumbnail_path);
		}
	}
}

FReply UWidget_ContentFileItem::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (!IS_OBJECT_PTR_VALID(RightMenuWidget))
		{
			RightMenuWidget = URightMouseMenuWidget::Create();
			RightMenuWidget->AddToViewport(PopUIZOrder);
		}
		RightMenuWidget->UpdateContent(InMouseEvent.GetScreenSpacePosition());
		//OnClickedBtnSelect();
		IsSelected = true;
		FileSelectDelegate.ExecuteIfBound(this, ViewItemType);
		RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
		RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UWidget_ContentFileItem::OnRightMenuClickHandler(const ERightMenuType& ActionType)
{
	if (ActionType == ERightMenuType::Copy)
	{
		UE_LOG(LogTemp, Log, TEXT("copy view file item, Item name : %s"), *ViewFileData.folder_name);
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Copy));
	}
	else if (ActionType == ERightMenuType::Shear)
	{
		UE_LOG(LogTemp, Log, TEXT("shear view file item, Item name : %s"), *ViewFileData.folder_name);
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Shear));
	}
	else if (ActionType == ERightMenuType::Paste)
	{
		if (UFolderWidget::Get()->GetActionID().Equals(RootParentID))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Shear First!!")).ToString());
			return;
		}
		if (!URefRelationFunction::IsSameRootDirectory(UFolderWidget::Get()->GetActionPath(), ViewFileData.backend_folder_path))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Can not paste to different type folder!!")).ToString());
			return;
		}

		UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(1);
		OnHold->SetOperationText(TEXT("Paste"));
		OnHold->SetVisibility(ESlateVisibility::Visible);

		bool IsSuccess = true;
		FFolderTableData NewActionData;
		if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Copy))
		{
#ifdef USE_REF_LOCAL_FILE
			NetUUID.CopyUUID = CopyAction(UFolderWidget::Get()->GetActionID(), URefRelationFunction::GetThisContainFolderID(ViewFileData.backend_folder_path));

			//no need copy action, reset state
			if(NetUUID.CopyUUID.IsEmpty())
			{
				OPERATOR_SUCCESS();
			}
#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCopyLogic(this, UFolderWidget::Get()->GetActionID(), ViewFileData.parent_id, NewActionData);
#endif
		}
		else if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
		{
#ifdef USE_REF_LOCAL_FILE
			NetUUID.CutUUID = CutAction(UFolderWidget::Get()->GetActionID(), URefRelationFunction::GetThisContainFolderID(ViewFileData.backend_folder_path));
#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCut(UFolderWidget::Get()->GetActionID(), ViewFileData.parent_id, NewActionData);
#endif
		}

#ifdef USE_REF_LOCAL_FILE
#else
		if (IsSuccess)
		{
			UE_LOG(LogTemp, Log, TEXT("copy or cut view file id : %s, name : %s"), *NewActionData.id, *NewActionData.folder_name);
			ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Paste));
		}
#endif
	}
	else if (ActionType == ERightMenuType::Delete)
	{
		FString Tips = FText::FromStringTable(FName("PosSt"), TEXT("Do you want to Delete the ")).ToString() + TEXT("\"") + ViewFileData.folder_name + TEXT("\"") + FText::FromStringTable(FName("PosSt"), TEXT(" file?")).ToString();
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), Tips))
		{
			UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(2);
			OnHold->SetOperationText(TEXT("Delete"));
			OnHold->SetVisibility(ESlateVisibility::Visible);
			OnTipPopConfirmHandler();
		}
	}
}

void UWidget_ContentFileItem::OnTipPopConfirmHandler()
{
	UE_LOG(LogTemp, Log, TEXT("confirm to delete view file item, Item name : %s"), *ViewFileData.folder_name);

#ifdef USE_REF_LOCAL_FILE

	NetUUID.DeleteUUID = DeleteAction(ViewFileData.id);

#else
	if (UFolderTableOperatorLibrary::DeleteFolderFile(ViewFileData.id, false))
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UE_LOG(LogTemp, Log, TEXT("delete view file item, Item name : %s"), *ViewFileData.folder_name);
		this->SetVisibility(ESlateVisibility::Collapsed);
		this->RemoveFromParent();
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFileData, static_cast<int32>(ERightMenuType::Delete));
	}
	else
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Delete view list file error")).ToString());
	}
#endif
}

void UWidget_ContentFileItem::OnClickedBtnSelect()
{
	/*if (BorBackground)
	{
		UFolderFunctionLibrary::SetBorderColor(SelectColor, BorBackground);
	}*/
	IsSelected = true;
	switch (static_cast<EMouseClickType>(CurrentMouseClickType))
	{
	case EMouseClickType::ClickNone:
	{
		CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickOnce);
		FileSelectDelegate.ExecuteIfBound(this, ViewItemType);
		break;
	}
	case EMouseClickType::ClickOnce:
	{
		//ResetSelectState();
		FileOpenDelegate.ExecuteIfBound(ViewFileData);
		break;
	}
	default:
		break;
	}
}

void UWidget_ContentFileItem::OnHoverBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		//UFolderFunctionLibrary::SetBorderColor(HoverColor, BorBackground);
		Super::SetViewFolderOrFileBorderColor(EBackBorderState::Hover);
	}
}

void UWidget_ContentFileItem::OnUnHoverBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		//UFolderFunctionLibrary::SetBorderColor(NormalColor, BorBackground);
		Super::SetViewFolderOrFileBorderColor(EBackBorderState::Normal);
	}
}
