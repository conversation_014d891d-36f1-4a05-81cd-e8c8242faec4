// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FolderAndFileBaseWidget.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Widget_ContentFileItem.generated.h"

/**
 * 
 */

class UBorder;
class UButton;
class UImage;
class UTextBlock;
class UWidget_ContentFileItem;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FViewFileSelectedDelegate, UUserWidget*, SelectWidget, const int32&, ItemType);

UCLASS()
class DESIGNSTATION_API UWidget_ContentFileItem : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FFolderTableData & FolderData);
	void UpdateFileName(const FString& InName, const FString& InThumbnail);
	void UpdateFileName(const FString& InName);
	void UpdateFileThumbnail(const FString& InThumbnail);
	void ResetSelectState();
	void UpdateFolderState(bool _IsVisible) override;
	virtual void RefreshVisibleState() override;

	//virtual void RightClickMenuEdit(const EFolderAndFileRightMenu::Type& ActionType) override;

	//FORCEINLINE bool GetIsSelected() const { return IsSelected; }
	virtual void SetIsSelected(bool _IsSelect) override;

	FORCEINLINE int32 GetFolderType() const { return FolderType; }
	FORCEINLINE void SetFolderType(const int32& _Type) { FolderType = _Type; }
	virtual FString GetFolderItemId() override;/* { return FolderId; }*/
	FORCEINLINE void SetFolderItemId(const FString& _Id) { FolderId = _Id; }
	FORCEINLINE FFolderTableData& GetViewItemData() { return ViewFileData; }

	virtual void FolderFileCopyFinishAction(const FString& ActionReturnID) override;
	virtual void CopyDirectActionByGameThread(const FString& ActionID) override;

	void LoadImage(const FString& ImgPath);

	static UWidget_ContentFileItem* Create();

protected:
	void BindDelegate_Inner();

	virtual void OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;

	void SendDowmloadImageRequest(const FString& ImgPath);
	UFUNCTION()
	void OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath);

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

	UFUNCTION()
		void OnRightMenuClickHandler(const ERightMenuType& ActionType);
	UFUNCTION()
		void OnTipPopConfirmHandler();

private:
	static FString WidgetContentFilePath;

protected:
	UFUNCTION()
		void OnClickedBtnSelect();
	UFUNCTION()
		void OnHoverBtnSelect();
	UFUNCTION()
		void OnUnHoverBtnSelect();

private:
	UPROPERTY()
		FFolderTableData ViewFileData;

	int32 FolderType;
	FString FolderId;

	int CurrentMouseClickType;
	static int ViewItemType;

private:
	/*UPROPERTY()
		UBorder*    BorBackground;*/
	/*UPROPERTY()
		UButton*    BtnSelect;*/
	/*UPROPERTY()
		UTextBlock* TxtFileName;*/
	UPROPERTY(BlueprintReadWrite, Category = "ContentFileItem", meta = (AllowPrivateAccess = true, BindWidget = true))
		UImage*     Img_File;
	UPROPERTY(BlueprintReadWrite, Category = "ContentFileItem", meta = (AllowPrivateAccess = true, BindWidget = true))
		UImage*     Img_Choose;
	
public:
	FViewFileSelectedDelegate FileSelectDelegate;
	FViewFolderOrFileOpenedDelegate FileOpenDelegate;
};
