// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_ContentFolderItem.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "FolderFunctionLibrary.h"
#include "Components/Border.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include <Operators/ProtobufOperatorFunctionLibrary.h>

int32 UWidget_ContentFolderItem::ViewItemType = static_cast<int32>(ELayoutItemType::ViewFolderItem);

extern const int PopUIZOrder;
extern const FString RootParentID;

FString UWidget_ContentFolderItem::WidgetContentFolderPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/ContentFolderItem.ContentFolderItem_C'");

bool UWidget_ContentFolderItem::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UWidget_ContentFolderItem::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UWidget_ContentFolderItem::OnHoverBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UWidget_ContentFolderItem::OnUnHoverBtnSelect);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FolderName);
	BIND_PARAM_CPP_TO_UMG(ImgFolder, Img_Folder);
	BIND_PARAM_CPP_TO_UMG(ImgChoose, Img_Choose);

	IsSelected = false;
	IsVisible = true;
	ResetSelectState();

	return true;
}

void UWidget_ContentFolderItem::UpdateContent(const FFolderTableData& FolderData)
{
	ViewFolderData = FolderData;
	UpdateFolderName(FolderData.folder_name);
}

void UWidget_ContentFolderItem::UpdateFolderName(const FString& InName)
{
	if (TxtName)
	{
		TxtName->SetText(FText::FromString(InName));
	}
}

void UWidget_ContentFolderItem::ResetSelectState()
{
	SetIsSelected(false);
	/*IsSelected = false;
	CurrentMouseClickType = (int)EMouseClickType::ClickNone;
	if (BorBackground)
	{
		UFolderFunctionLibrary::SetBorderColor(NormalColor, BorBackground);
	}*/
}

void UWidget_ContentFolderItem::UpdateFolderState(bool _IsVisible)
{
	IsVisible = _IsVisible;
	//UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? FLinearColor::White : InVisibleTextColor, TxtName);
	CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickNone);
	/*if (BorFolderOrFile)
	{
		UFolderFunctionLibrary::SetBorderColor((IsVisible && IsSuperiorVisible) ? NormalColor : InVisibleColor, BorFolderOrFile);
	}*/
}

void UWidget_ContentFolderItem::RefreshVisibleState()
{
	//UFolderFunctionLibrary::SetTextColor((IsVisible && IsSuperiorVisible) ? FLinearColor::White : InVisibleTextColor, TxtName);
	//if (BorFolderOrFile)
	//{
	//	UFolderFunctionLibrary::SetBorderColor((IsVisible && IsSuperiorVisible) ? NormalColor : InVisibleColor, BorFolderOrFile);
	//}
	SetIsSelected(IsSelected);
}

FString UWidget_ContentFolderItem::GetWidgetName()
{
	if (TxtName)
	{
		return TxtName->GetText().ToString();
	}
	return FString();
}

void UWidget_ContentFolderItem::SetIsSelected(bool _IsSelect)
{
	IsSelected = _IsSelect;
	if (!_IsSelect)
	{
		CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickNone);
	}
	Super::SetViewFolderOrFileBorderColor(_IsSelect ? EBackBorderState::Select : EBackBorderState::Normal);
	if (ImgChoose)
	{
		ImgChoose->SetVisibility(_IsSelect ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

FString UWidget_ContentFolderItem::GetFolderItemId()
{
	return FolderId;
}

void UWidget_ContentFolderItem::FolderFileCopyFinishAction(const FString& ActionReturnID)
{
	UE_LOG(LogTemp, Warning, TEXT("[UWidget_ContentFolderItem] --- [Finish Copy]"));

	if (IsInGameThread())
	{
		CopyDirectActionByGameThread(ActionReturnID);
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread,
			[this]()->void
			{
				this->ViewItemRightActionDelegate.ExecuteIfBound(this->ViewFolderData, static_cast<int32>(ERightMenuType::Paste));
			}
		);
	}

}

void UWidget_ContentFolderItem::CopyDirectActionByGameThread(const FString& ActionID)
{
	ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Paste));
}

UWidget_ContentFolderItem* UWidget_ContentFolderItem::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UWidget_ContentFolderItem::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UWidget_ContentFolderItem>(UWidget_ContentFolderItem::WidgetContentFolderPath);
}

void UWidget_ContentFolderItem::OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.DeleteUUID.Equals(UUID))
	{
		NetUUID.ResetDeleteAction();
		if(bSuccess/* && DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			UE_LOG(LogTemp, Log, TEXT("delete view folder item, Item name : %s"), *ViewFolderData.folder_name);
			this->SetVisibility(ESlateVisibility::Collapsed);
			this->RemoveFromParent();
			ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Delete));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UWidget_ContentFolderItem::OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (NetUUID.CopyUUID.Equals(UUID))
	{
		NetUUID.ResetCopyAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UWidget_ContentFolderItem::OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (NetUUID.CutUUID.Equals(UUID))
	{
		NetUUID.ResetCutAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//update cache 
			UFolderWidget::Get()->AddCacheDataForRefDirectory(DirData[0]);

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

FReply UWidget_ContentFolderItem::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (!IS_OBJECT_PTR_VALID(RightMenuWidget))
		{
			RightMenuWidget = URightMouseMenuWidget::Create();
			RightMenuWidget->AddToViewport(PopUIZOrder);
		}
		RightMenuWidget->UpdateContent(InMouseEvent.GetScreenSpacePosition());
		//OnClickedBtnSelect();
		IsSelected = true;
		FolderSelectDelegate.ExecuteIfBound(this, ViewItemType);
		RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
		RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UWidget_ContentFolderItem::OnRightMenuClickHandler(const ERightMenuType& ActionType)
{
	if (ActionType == ERightMenuType::Copy)
	{
		UE_LOG(LogTemp, Log, TEXT("copy view folder item, Item name : %s"), *ViewFolderData.folder_name);
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Copy));
	}
	else if (ActionType == ERightMenuType::Shear)
	{
		UE_LOG(LogTemp, Log, TEXT("shear view folder item, Item name : %s"), *ViewFolderData.folder_name);
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Shear));
	}
	else if (ActionType == ERightMenuType::Paste)
	{
		if (UFolderWidget::Get()->GetActionID().Equals(RootParentID))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Shear First!!")).ToString());
			return;
		}
		if (!URefRelationFunction::IsSameRootDirectory(UFolderWidget::Get()->GetActionPath(), ViewFolderData.backend_folder_path))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Can not paste to different type folder!!")).ToString());
			return;
		}

		UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(1);
		OnHold->SetOperationText(TEXT("Paste"));
		OnHold->SetVisibility(ESlateVisibility::Visible);

		bool IsSuccess = true;
		FFolderTableData NewActionData;
		if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Copy))
		{
#ifdef USE_REF_LOCAL_FILE
			NetUUID.CopyUUID = CopyAction(UFolderWidget::Get()->GetActionID(), ViewFolderData.id);
			//no need copy action, reset state
			if (NetUUID.CopyUUID.IsEmpty())
			{
				OPERATOR_SUCCESS();
			}
#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCopyLogic(this, UFolderWidget::Get()->GetActionID(), ViewFolderData, NewActionData);
#endif
		}
		else if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
		{
#ifdef USE_REF_LOCAL_FILE
			NetUUID.CutUUID = CutAction(UFolderWidget::Get()->GetActionID(), ViewFolderData.id);
#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCut(UFolderWidget::Get()->GetActionID(), ViewFolderData, NewActionData);
#endif
		}

#ifdef USE_REF_LOCAL_FILE
#else
		if (IsSuccess)
		{
			UE_LOG(LogTemp, Log, TEXT("copy or cut view folder id : %s, name : %s"), *NewActionData.id, *NewActionData.folder_name);
			ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Paste));
		}
#endif
	}
	else if (ActionType == ERightMenuType::Delete)
	{
		UE_LOG(LogTemp, Log, TEXT("delete view folder item, Item name : %s"), *ViewFolderData.folder_name);
		FString Tips = FText::FromStringTable(FName("PosSt"), TEXT("Do you want to Delete the ")).ToString() + TEXT("\"") + ViewFolderData.folder_name + TEXT("\"") + FText::FromStringTable(FName("PosSt"), TEXT(" folder?")).ToString();
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), Tips))
		{
			UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(2);
			OnHold->SetOperationText(TEXT("Delete"));
			OnHold->SetVisibility(ESlateVisibility::Visible);
			OnTipPopConfirmHandler();
		}
	}
}

void UWidget_ContentFolderItem::OnTipPopConfirmHandler()
{
	UE_LOG(LogTemp, Log, TEXT("confirm to delete view folder item, Item name : %s"), *ViewFolderData.folder_name);

#ifdef USE_REF_LOCAL_FILE

	NetUUID.DeleteUUID = DeleteAction(ViewFolderData.id);

#else
	if (UFolderTableOperatorLibrary::DeleteFolderFile(ViewFolderData.id, true))
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UE_LOG(LogTemp, Log, TEXT("delete view folder item, Item name : %s"), *ViewFolderData.folder_name);
		this->SetVisibility(ESlateVisibility::Collapsed);
		this->RemoveFromParent();
		ViewItemRightActionDelegate.ExecuteIfBound(ViewFolderData, static_cast<int32>(ERightMenuType::Delete));
	}
	else
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Delete view list folder error")).ToString());
	}
#endif
}

void UWidget_ContentFolderItem::OnClickedBtnSelect()
{
	/*if (BorBackground)
	{
		UFolderFunctionLibrary::SetBorderColor(IsDelete ? SelectColor : InVisibleColor, BorBackground);
	}*/
	IsSelected = true;
	switch (static_cast<EMouseClickType>(CurrentMouseClickType))
	{
	case EMouseClickType::ClickNone:
	{
		CurrentMouseClickType = static_cast<int>(EMouseClickType::ClickOnce);
		FolderSelectDelegate.ExecuteIfBound(this, ViewItemType);
		break;
	}
	case EMouseClickType::ClickOnce:
	{
		ResetSelectState();
		FolderOpenDelegate.ExecuteIfBound(ViewFolderData);
	}
	default:
		break;
	}
}

void UWidget_ContentFolderItem::OnHoverBtnSelect()
{
	UE_LOG(LogTemp, Log, TEXT("UWidget_ContentFolderItem hover"));
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		//UFolderFunctionLibrary::SetBorderColor(HoverColor, BorBackground);
		Super::SetViewFolderOrFileBorderColor(EBackBorderState::Hover);
	}
}

void UWidget_ContentFolderItem::OnUnHoverBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		//UFolderFunctionLibrary::SetBorderColor(NormalColor, BorBackground);
		Super::SetViewFolderOrFileBorderColor(EBackBorderState::Normal);
	}
}
