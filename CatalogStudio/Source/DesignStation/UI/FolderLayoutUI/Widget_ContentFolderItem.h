// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FolderAndFileBaseWidget.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Widget_ContentFolderItem.generated.h"

/**
 * 
 */

class UBorder;
class UButton;
class UTextBlock;
class UImage;
class UWidget_ContentFolderItem;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FViewFolderSelectedDelegate, UUserWidget*, SelectWidget, const int32&, ItemType);

UCLASS()
class DESIGNSTATION_API UWidget_ContentFolderItem : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void UpdateContent(const FFolderTableData & FolderData);
	void UpdateFolderName(const FString& InName);
	void ResetSelectState();
	void UpdateFolderState(bool _IsVisible) override;
	virtual void RefreshVisibleState() override; 

	FString GetWidgetName();

	//bool GetIsSelected() const { return IsSelected; }
	virtual void SetIsSelected(bool _IsSelect) override;
	virtual FString GetFolderItemId() override;

	FORCEINLINE void SetFolderItemId(const FString& _Id) { FolderId = _Id; }
	FORCEINLINE FFolderTableData& GetViewItemData() { return ViewFolderData; }

	virtual void FolderFileCopyFinishAction(const FString& ActionReturnID) override;
	virtual void CopyDirectActionByGameThread(const FString& ActionID) override;

	static UWidget_ContentFolderItem* Create();

protected:
	virtual void OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

	UFUNCTION()
		void OnRightMenuClickHandler(const ERightMenuType& ActionType);
	UFUNCTION()
		void OnTipPopConfirmHandler();

private:
	static FString WidgetContentFolderPath;

protected:
	UFUNCTION()
		void OnClickedBtnSelect();
	UFUNCTION()
		void OnHoverBtnSelect();
	UFUNCTION()
		void OnUnHoverBtnSelect();

private:
	//int32 FolderType;
	UPROPERTY()
		FFolderTableData ViewFolderData;

	FString FolderId;

	int CurrentMouseClickType;
	static int ViewItemType;

private:
	UPROPERTY()
		UImage* ImgFolder;
	UPROPERTY()
		UImage* ImgChoose;
	/*UPROPERTY()
		UBorder*    BorBackground;
	UPROPERTY()
		UButton*    BtnSelect;
	UPROPERTY()
		UTextBlock* TxtFolderName;*/
	
public:
	FViewFolderSelectedDelegate FolderSelectDelegate;
	FViewFolderOrFileOpenedDelegate FolderOpenDelegate;
};
