// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_FileItem.h"

#include "Widget_FolderItem.h"
#include "Components/Border.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include <Operators/ProtobufOperatorFunctionLibrary.h>

#define LOCTEXT_NAMESPACE "FileItem"

extern const int PopUIZOrder;
extern const FString RootParentID;

int UWidget_FileItem::LayoutItemType = (int)ELayoutItemType::FileItem;

FString UWidget_FileItem::WidgetFileItemPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/FileItemUI.FileItemUI_C'");

void UWidget_FileItem::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UWidget_FileItem::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UWidget_FileItem::OnHoveredBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UWidget_FileItem::OnUnHoverBtnSelect);
	BIND_PARAM_CPP_TO_UMG(SZIndent, SZ_Indent);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FileName);
	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_File);

	BIND_WIDGET_FUNCTION(Ckb_Select, OnCheckStateChanged, UWidget_FileItem::OnStateChangedSelectCheck);

	BindDelegate_Local();

	IsSelected = false;

}

//void UWidget_FileItem::RightClickMenuEdit(const EFolderAndFileRightMenu::Type & ActionType)
//{
//	if (ActionType == EFolderAndFileRightMenu::Type::Delete)
//	{
//
//		UE_LOG(LogTemp, Log, TEXT("delete left tree file item"));
//	}
//	else if (ActionType == EFolderAndFileRightMenu::Type::Copy)
//	{
//		UE_LOG(LogTemp, Log, TEXT("copy left tree file item"));
//	}
//	else if (ActionType == EFolderAndFileRightMenu::Type::Cut)
//	{
//		UE_LOG(LogTemp, Log, TEXT("cut left tree file item"));
//	}
//	else if (ActionType == EFolderAndFileRightMenu::Type::Paste)
//	{
//		UE_LOG(LogTemp, Log, TEXT("paste left tree file item"));
//	}
//}

void UWidget_FileItem::OnRightMenuClickHandler(const ERightMenuType& ActionType)
{
	if (ActionType == ERightMenuType::Copy)
	{
		UE_LOG(LogTemp, Log, TEXT("copy left tree file item, Item name : %s"), *ItemData.folder_name);
		FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Copy));
	}
	else if (ActionType == ERightMenuType::Shear)
	{
		UE_LOG(LogTemp, Log, TEXT("shear left tree file item, Item name : %s"), *ItemData.folder_name);
		FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Shear));
	}
	else if (ActionType == ERightMenuType::Paste)
	{
		UE_LOG(LogTemp, Log, TEXT("paste left tree file item, Item name : %s"), *ItemData.folder_name);
		if (UFolderWidget::Get())
		{
			if (UFolderWidget::Get()->GetActionID().Equals(RootParentID))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Shear First!!")).ToString());
				return;
			}
			if (!URefRelationFunction::IsSameRootDirectory(UFolderWidget::Get()->GetActionPath(), ItemData.backend_folder_path))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Can not paste to different type folder!!")).ToString());
				return;
			}

			UE_LOG(LogTemp, Log, TEXT("Removing On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(1);
			OnHold->SetOperationText(TEXT("Pasted"));
			OnHold->SetVisibility(ESlateVisibility::Visible);

			bool IsSuccess = true;
			FFolderTableData NewActionData;
			if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Copy))
			{
#ifdef USE_REF_LOCAL_FILE

				NetUUID.CopyUUID = CopyAction(UFolderWidget::Get()->GetActionID(), ParentFolderWidget.Get()->GetItemData().id);
				//no need copy action, reset state
				if (NetUUID.CopyUUID.IsEmpty())
				{
					OPERATOR_SUCCESS();
				}
#else
				IsSuccess = UFolderTableOperatorLibrary::FolderFileCopyLogic(this, UFolderWidget::Get()->GetActionID(), ParentFolderWidget.Get()->GetItemData(), NewActionData);
				//IsSuccess = UFolderTableOperatorLibrary::FolderFileCopy(UFolderWidget::Get()->GetActionID(), ParentFolderWidget.Get()->GetItemData(), NewActionData);
#endif
			}
			else if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
			{
#ifdef USE_REF_LOCAL_FILE

				NetUUID.CutUUID = CutAction(UFolderWidget::Get()->GetActionID(), ParentFolderWidget.Get()->GetItemData().id);

#else
				IsSuccess = UFolderTableOperatorLibrary::FolderFileCut(UFolderWidget::Get()->GetActionID(), ParentFolderWidget.Get()->GetItemData(), NewActionData);
#endif
			}

#ifdef USE_REF_LOCAL_FILE
#else 
			if (IsSuccess)
			{
				if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
				{
					if (NewActionData.can_add_subfolder)
					{
						if (UFolderWidget::Get()->GetCutFolderItem())
						{
							UFolderWidget::Get()->GetCutFolderItem()->RemoveFromParentFolder();
						}
					}
					else
					{
						if (UFolderWidget::Get()->GetCutFileItem())
						{
							UFolderWidget::Get()->GetCutFileItem()->RemoveFromParentFolder();
						}
					}
				}
				if (ParentFolderWidget.Get())
				{
					ParentFolderWidget.Get()->AddDataToFolder(NewActionData);
					if (NewActionData.can_add_subfolder)
					{
						ParentFolderWidget.Get()->GetChildFolders()[NewActionData.id]->SelectSelf();
					}
					else
					{
						ParentFolderWidget.Get()->GetChildFilesMap()[NewActionData.id]->SelectSelf();
					}
				}
				FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
			}
#endif
		}
	}
	else if (ActionType == ERightMenuType::Delete)
	{
		UE_LOG(LogTemp, Log, TEXT("delete left tree file item, Item name : %s"), *ItemData.folder_name);
		FString Tips = FText::FromStringTable(FName("PosSt"), TEXT("Do you want to Delete the ")).ToString() + TEXT("\"") + ItemData.folder_name + TEXT("\"") + FText::FromStringTable(FName("PosSt"), TEXT(" file?")).ToString();
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), Tips))
		{
			UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(2);
			OnHold->SetOperationText(TEXT("Delete"));
			OnHold->SetVisibility(ESlateVisibility::Visible);
			OnTipPopConfirmHandler();
		}

	}
}

void UWidget_FileItem::OnTipPopConfirmHandler()
{
	UE_LOG(LogTemp, Log, TEXT("confirm to delete left tree folder item, Item name : %s"), *ItemData.folder_name);
#ifdef USE_REF_LOCAL_FILE

	NetUUID.DeleteUUID = DeleteAction(ItemData.id);

#else
	if (UFolderTableOperatorLibrary::DeleteFolderFile(ItemData.id, false))
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		FileRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Delete);
		if (ParentFolderWidget.IsValid())
		{
			ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
			ParentFolderWidget.Get()->SelectSelf();
		}
	}
	else
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Delete File Error!!")).ToString());
	}
#endif
}

void UWidget_FileItem::SetItemData(const FFolderTableData & InData)
{
	if (!FMath::IsNearlyEqual(ItemData.visibility, InData.visibility, 0.001f))
	{
		UpdateFolderState(!FMath::IsNearlyZero(InData.visibility));
	}
	ItemData = InData;
	if (InData.folder_id.IsEmpty())
	{
		UpdateFileName(InData.folder_name);
	}
	else
	{
		UpdateFileName(InData.folder_id + TEXT(" - ") + InData.folder_name);
	}
	
}

void UWidget_FileItem::SyncItemData(const FRefDirectoryData& InData)
{
	FFolderTableData SyncData;
	URefRelationFunction::ConvertDirctoryDataToDBData(InData, SyncData);
	SetItemData(SyncData);
}

void UWidget_FileItem::UpdateFileName(const FString& FileName)
{
	if (TxtName)
	{
		TxtName->SetText(FText::FromString(FileName));
	}
}

void UWidget_FileItem::UpdateFolderState(bool _IsVisible)
{
	Super::UpdateFolderState(_IsVisible);
	SetIsSelected(IsSelected);
	/*IsVisible = _IsVisible;
	UFolderFunctionLibrary::SetTextColor(IsVisible ? NormalTextColor : InVisibleTextColor, TxtName);*/
}

void UWidget_FileItem::RemoveFromParentFolder()
{
	if (ParentFolderWidget.IsValid())
	{
		ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
	}
}

void UWidget_FileItem::SetIsSelected(bool _IsSelected)
{
	Super::SetIsSelected(_IsSelected);
}

void UWidget_FileItem::SelectSelf()
{
	OnClickedBtnSelect();
}

void UWidget_FileItem::FolderFileCopyFinishAction(const FString& ActionReturnID)
{
	UE_LOG(LogTemp, Warning, TEXT("[UWidget_FileItem] --- [Finish Copy]"));

	if (IsInGameThread())
	{
		CopyDirectActionByGameThread(ActionReturnID);
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread, 
			[ActionReturnID, this]()->void 
		{
			TArray<FFolderTableData> Datas;
			UFolderTableOperatorLibrary::FolderFileSearchByID(ActionReturnID, EFolderFileSearchType::EFolderAndFile, Datas);
			if (Datas.Num() <= 0)
				return;
			FFolderTableData NewActionData = Datas[0];
			if (this->ParentFolderWidget.Get())
			{
				this->ParentFolderWidget.Get()->AddDataToFolder(NewActionData);
				if (NewActionData.can_add_subfolder)
				{
					this->ParentFolderWidget.Get()->GetChildFolders()[NewActionData.id]->SelectSelf();
				}
				else
				{
					this->ParentFolderWidget.Get()->GetChildFilesMap()[NewActionData.id]->SelectSelf();
				}
			}
			this->FileRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Paste);
		}
		);
	}
}

void UWidget_FileItem::CopyDirectActionByGameThread(const FString& ActionID)
{
	TArray<FFolderTableData> Datas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(ActionID, EFolderFileSearchType::EFolderAndFile, Datas);
	if (Datas.Num() <= 0)
		return;
	FFolderTableData NewActionData = Datas[0];
	if (ParentFolderWidget.Get())
	{
		ParentFolderWidget.Get()->AddDataToFolder(NewActionData);
		if (NewActionData.can_add_subfolder)
		{
			ParentFolderWidget.Get()->GetChildFolders()[NewActionData.id]->SelectSelf();
		}
		else
		{
			ParentFolderWidget.Get()->GetChildFilesMap()[NewActionData.id]->SelectSelf();
		}
	}
	FileRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Paste);
}

void UWidget_FileItem::SetFolderOrder(const int32& InOrder)
{
	ItemData.folder_order = InOrder;
	UFolderTableOperatorLibrary::UpdateFolderFileOrder(ItemData.id, InOrder, false);
}

UWidget_FileItem * UWidget_FileItem::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UWidget_FileItem::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UWidget_FileItem>(UWidget_FileItem::WidgetFileItemPath);
}

void UWidget_FileItem::OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.DeleteUUID.Equals(UUID))
	{
		UE_LOG(LogTemp, Log, TEXT("UWidget_FileItem::OnDeleteActionResponseHandle --- uuid [%s], bSuccess [%d]"), *UUID, bSuccess);
		NetUUID.ResetDeleteAction();
		if (bSuccess)
		{
			OPERATOR_SUCCESS();

			FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Delete));
			if (ParentFolderWidget.IsValid())
			{
				ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
				ParentFolderWidget.Get()->SelectSelf();
			}


		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UWidget_FileItem::OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CopyUUID.Equals(UUID))
	{
		NetUUID.ResetCopyAction();
		if(bSuccess)
		{
			OPERATOR_SUCCESS();

			if (!DirData.IsValidIndex(0))
				return;

			UE_LOG(LogTemp, Log, TEXT("copy action success!"));
			FFolderTableData NewActionData;
			URefRelationFunction::ConvertDirctoryDataToDBData(DirData[0], NewActionData);

			UFolderWidget::Get()->AddCacheDataForDirectory_Single(
				URefRelationFunction::GetFolderDirectory(DirData[0].backendFolderPath, false),
				DirData[0]
			);

			//修改文件里面的路径
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(NewActionData);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = NewActionData.backend_folder_path;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			if (ParentFolderWidget.Get())
			{
				ParentFolderWidget.Get()->AddDataToFolder(NewActionData);
				if (NewActionData.can_add_subfolder)
				{
					ParentFolderWidget.Get()->GetChildFolders()[NewActionData.id]->SelectSelf();
				}
				else
				{
					ParentFolderWidget.Get()->GetChildFilesMap()[NewActionData.id]->SelectSelf();
				}
			}
			FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UWidget_FileItem::OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CutUUID.Equals(UUID))
	{
		NetUUID.ResetCutAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			if (!DirData.IsValidIndex(0))
				return;
			FFolderTableData NewActionData;
			URefRelationFunction::ConvertDirctoryDataToDBData(DirData[0], NewActionData);

			//update cache 
			UFolderWidget::Get()->AddCacheDataForRefDirectory(DirData[0]);

			//修改文件里面的路径
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(NewActionData);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = NewActionData.backend_folder_path;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			if (NewActionData.can_add_subfolder)
			{
				if (UFolderWidget::Get()->GetCutFolderItem())
				{
					UFolderWidget::Get()->GetCutFolderItem()->RemoveFromParentFolder();
				}
			}
			else
			{
				if (UFolderWidget::Get()->GetCutFileItem())
				{
					UFolderWidget::Get()->GetCutFileItem()->RemoveFromParentFolder();
				}
			}
			if (ParentFolderWidget.Get())
			{
				ParentFolderWidget.Get()->AddDataToFolder(NewActionData);
				if (NewActionData.can_add_subfolder)
				{
					ParentFolderWidget.Get()->GetChildFolders()[NewActionData.id]->SelectSelf();
				}
				else
				{
					ParentFolderWidget.Get()->GetChildFilesMap()[NewActionData.id]->SelectSelf();
				}
			}
			FileRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UWidget_FileItem::BindDelegate_Local()
{
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UWidget_FileItem::OnQueryCurrentFileDataResponseHandle);
}

void UWidget_FileItem::QueryFileData()
{
	if(ItemData.folder_id.IsEmpty())
	{
		NetUUID_Local.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest_ID({ItemData.id});
	}
	else
	{
		NetUUID_Local.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest_FolderID(ItemData.folder_id);
	}
}

void UWidget_FileItem::OnQueryCurrentFileDataResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if(NetUUID_Local.SearchUUID.Equals(UUID))
	{
		NetUUID_Local.ResetSearchAction();
		if(bSuccess)
		{
			if(Datas.IsValidIndex(0))
			{
				FRefDirectoryData LatestData = Datas[0];
				if(LatestData.id.Equals(ItemData.id))
				{
					URefRelationFunction::ConvertDirctoryDataToDBData(LatestData, ItemData);
					UFolderWidget::Get()->SyncSelectData(LatestData);
				}
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}

		OnClickedBtnSelect_Execute();
	}
}

FReply UWidget_FileItem::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (ItemData.parent_id.Equals(RootParentID))
	{
		return FReply::Unhandled();
	}
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if(UFolderWidget::Get()->IsMultiSelect())
			return FReply::Handled();

		OnClickedBtnSelect();
		if (!IS_OBJECT_PTR_VALID(RightMenuWidget))
		{
			RightMenuWidget = URightMouseMenuWidget::Create();
			RightMenuWidget->AddToViewport(PopUIZOrder);
		}
		RightMenuWidget->UpdateContent(InMouseEvent.GetScreenSpacePosition());
		SelectSelf();
		RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
		RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UWidget_FileItem::OnClickedBtnSelect()
{
	//QueryFileData();
	
	 UE_LOG(LogTemp, Log, TEXT("select file"));
	 IsSelected = true;
	 /*if (BorBackground)
	 {
	 	UFolderFunctionLibrary::SetBorderColor(SelectColor, BorBackground);
	 }*/
	 FileSelectDelegate.ExecuteIfBound(this);
}

void UWidget_FileItem::OnClickedBtnSelect_Execute()
{
	UE_LOG(LogTemp, Log, TEXT("select file"));
	IsSelected = true;
	FileSelectDelegate.ExecuteIfBound(this);
}

void UWidget_FileItem::OnHoveredBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile) )
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Hover);
	}
}

void UWidget_FileItem::OnUnHoverBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Normal);
	}
}

void UWidget_FileItem::OnStateChangedSelectCheck(bool IsCheck)
{

}

#undef LOCTEXT_NAMESPACE
