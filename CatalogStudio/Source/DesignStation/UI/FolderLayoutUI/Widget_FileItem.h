// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FolderAndFileBaseWidget.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Widget_FileItem.generated.h"

/**
 * 
 */

class UButton;
class UTextBlock;
class UBorder;
class UWidget_FolderItem;

DECLARE_DYNAMIC_DELEGATE_OneParam(FFileItemSelectDelegate, UWidget_FileItem*, SelectedFile);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFileRightMenuActionDelegate, UWidget_FileItem*, ActionFile, const int32&, ActionType);

UCLASS()
class DESIGNSTATION_API UWidget_FileItem : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()
	
public:
	//virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void SetItemData(const FFolderTableData& InData);
	void SyncItemData(const FRefDirectoryData& InData);
	FFolderTableData& GetItemData() { return ItemData; }
	FFolderTableData GetData() { return ItemData; }
	FString& GetFileName() { return ItemData.folder_name; }

	//virtual void RightClickMenuEdit(const EFolderAndFileRightMenu::Type& ActionType) override;

	void UpdateFileName(const FString& FileName);
	void UpdateFolderState(bool _IsVisible) override;
	void RemoveFromParentFolder();

	//bool GetIsSelected() const { return IsSelected; }
	virtual void SetIsSelected(bool _IsSelected) override;
	virtual void SelectSelf() override;

	virtual void FolderFileCopyFinishAction(const FString& ActionReturnID) override;
	virtual void CopyDirectActionByGameThread(const FString& ActionID) override;
	virtual void SetFolderOrder(const int32& InOrder) override;

	static UWidget_FileItem* Create();

protected:
	virtual void OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	
private:
	void BindDelegate_Local();
	void QueryFileData();

	UFUNCTION()
	void OnQueryCurrentFileDataResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);


	UFUNCTION()
		void OnRightMenuClickHandler(const ERightMenuType& ActionType);
	UFUNCTION()
		void OnTipPopConfirmHandler();

private:
	static FString WidgetFileItemPath;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	
protected:
	UFUNCTION()
		void OnClickedBtnSelect();

	void OnClickedBtnSelect_Execute();
	
	UFUNCTION()
		void OnHoveredBtnSelect();
	UFUNCTION()
		void OnUnHoverBtnSelect();

	UFUNCTION()
		void OnStateChangedSelectCheck(bool IsCheck);
private:
	UPROPERTY()
		FFolderTableData ItemData;

	UPROPERTY()
		FBackendDirectoryNetUUID NetUUID_Local;

	static int LayoutItemType;
//
//private:
//	bool IsSelected;
	//bool IsVisible;
//
public:
	FFileItemSelectDelegate FileSelectDelegate;
	FFileRightMenuActionDelegate FileRightMenuActionDelegate;
	TWeakObjectPtr<UWidget_FolderItem> ParentFolderWidget;

protected:
	UPROPERTY(meta = (BindWidget))
	UCheckBox* Ckb_Select;
};
