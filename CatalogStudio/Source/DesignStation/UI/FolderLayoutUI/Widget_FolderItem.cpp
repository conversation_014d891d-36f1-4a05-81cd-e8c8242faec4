// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_FolderItem.h"

#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include <BasicClasses/CatalogPlayerController.h>
#include <Operators/ProtobufOperatorFunctionLibrary.h>

#define LOCTEXT_NAMESPACE "FolderItemWidget"

extern const int PopUIZOrder;
extern const FString RootParentID;

int32 UWidget_FolderItem::LayoutItemType = static_cast<int32>(ELayoutItemType::FolderItem);

FString UWidget_FolderItem::WidgetFolderPath = TEXT("WidgetBlueprint'/Game/UI/FolderLayoutUI/FolderItemUI.FolderItemUI_C'");

void UWidget_FolderItem::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UWidget_FolderItem::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UWidget_FolderItem::OnHoveredBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UWidget_FolderItem::OnUnHoveredBtnSelect);
	BIND_PARAM_CPP_TO_UMG(SZIndent, SZ_Indent);
	BIND_PARAM_CPP_TO_UMG(CkbExpandChild, Ckb_ExpandChild);
	BIND_WIDGET_FUNCTION(CkbExpandChild, OnCheckStateChanged, UWidget_FolderItem::OnStateChangedCkbExpand);
	BIND_PARAM_CPP_TO_UMG(ScbChild, Scb_Child);
	ScbChild->SetVisibility(ESlateVisibility::Collapsed);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FolderName);
	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_Folder);
	BIND_PARAM_CPP_TO_UMG(ImgFolder, Img_Folder);
	BIND_PARAM_CPP_TO_UMG(BorGrey, Bor_Grey);

	BIND_WIDGET_FUNCTION(Ckb_Select, OnCheckStateChanged, UWidget_FolderItem::OnStateChangedSelectCheck);

	IsSelected = false;
	CopyOrCutData = FFolderTableData();

	FolderLevel = -1;
	IsGrey = false;
}


void UWidget_FolderItem::OnSearchActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if(NetUUID.SearchUUID.Equals(UUID))
	{
		NetUUID.ResetSearchAction();
		if(bSuccess)
		{
			GenerateSubFoldersInner(Datas);
			RefreshContentWidget();
			FolderSelectDelegate.ExecuteIfBound(this, true);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UWidget_FolderItem::SetFolderStateBrush(const EFolderState & InType)
{
	if (!IS_OBJECT_PTR_VALID(ImgFolder))
	{
		return;
	}
	if (!IS_OBJECT_PTR_VALID(ImageOne) || !IS_OBJECT_PTR_VALID(ImageTwo) || !IS_OBJECT_PTR_VALID(ImageThree))
	{
		GetFodlerImageBrush();
	}
	if (InType == EFolderState::UnCheckUnSelected || InType == EFolderState::UnCheckSelected)
	{
		ImgFolder->SetBrushFromTexture(ImageOne);
	}
	//else if (InType == EFolderState::UnCheckSelected)
	//{
	//	ImgFolder->SetBrushFromTexture(IsGrey ? ImageFour : ImageThree);
	//}
	else if (InType == EFolderState::CheckSelected || InType == EFolderState::CheckUnSelected)
	{
		ImgFolder->SetBrushFromTexture(ImageTwo);
	}
	//else if (InType == EFolderState::CheckUnSelected)
	//{
	//	ImgFolder->SetBrushFromTexture(IsGrey ? ImageTwo : ImageThree);
	//}
}

void UWidget_FolderItem::OnRightMenuClickHandler(const ERightMenuType & ActionType)
{
	if (ActionType == ERightMenuType::Copy)
	{
		if (ItemData.IsRootFolder())
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Root Folder Cannot Be Copy!!")).ToString());
			return;
		}
		UE_LOG(LogTemp, Log, TEXT("copy left tree folder item, Item name : %s"), *ItemData.folder_name);
		FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Copy));
	}
	else if (ActionType == ERightMenuType::Shear)
	{
		if (ItemData.IsRootFolder())
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Root Folder Cannot Be Shear!!")).ToString());
			return;
		}
		UE_LOG(LogTemp, Log, TEXT("shear left tree folder item, Item name : %s"), *ItemData.folder_name);
		FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Shear));
	}
	else if (ActionType == ERightMenuType::Paste)
	{
		UE_LOG(LogTemp, Log, TEXT("paste left tree folder item, Item name : %s"), *ItemData.folder_name);
		if (UFolderWidget::Get())
		{
			if (UFolderWidget::Get()->GetActionID().Equals(RootParentID))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Shear First!!")).ToString());
				return;
			}
			if (!URefRelationFunction::IsSameRootDirectory(UFolderWidget::Get()->GetActionPath(), ItemData.backend_folder_path))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Can not paste to different type folder!!")).ToString());
				return;
			}

			UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(1);
			OnHold->SetOperationText(TEXT("Paste"));
			OnHold->SetVisibility(ESlateVisibility::Visible);

			bool IsSuccess = true;
			FFolderTableData NewActionData;
			if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Copy))
			{
#ifdef USE_REF_LOCAL_FILE
				NetUUID.CopyUUID = CopyAction(UFolderWidget::Get()->GetActionID(), ItemData.id);
				//no need copy action, reset state
				if (NetUUID.CopyUUID.IsEmpty())
				{
					OPERATOR_SUCCESS();
				}
#else
				IsSuccess = UFolderTableOperatorLibrary::FolderFileCopyLogic(this, UFolderWidget::Get()->GetActionID(), ItemData, NewActionData);
#endif
			}
			else if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
			{
#ifdef USE_REF_LOCAL_FILE
				NetUUID.CutUUID = CutAction(UFolderWidget::Get()->GetActionID(), ItemData.id);
#else
				IsSuccess = UFolderTableOperatorLibrary::FolderFileCut(UFolderWidget::Get()->GetActionID(), ItemData, NewActionData);
#endif
			}

#ifdef USE_REF_LOCAL_FILE
#else
			if (IsSuccess)
			{
				if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
				{
					if (NewActionData.can_add_subfolder)
					{
						if (UFolderWidget::Get()->GetCutFolderItem())
						{
							UFolderWidget::Get()->GetCutFolderItem()->RemoveFromParentFolder();
						}
					}
					else
					{
						if (UFolderWidget::Get()->GetCutFileItem())
						{
							UFolderWidget::Get()->GetCutFileItem()->RemoveFromParentFolder();
						}
					}
				}
				if (CkbExpandChild->IsChecked())
				{
					AddDataToFolder(NewActionData);
					if (ChildFolders.Find(NewActionData.id))
					{
						ChildFolders[NewActionData.id]->SelectSelf();
					}
					else
					{
						ChildFilesMap[NewActionData.id]->SelectSelf();
					}
				}
				else
				{
					CopyOrCutData = NewActionData;
					SetChildFolderExpand(true);
				}
				FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
			}
#endif

			/*TArray<FFolderTableData> FolderData;
			FString ErrorMessage;
			if (UFolderTableOperatorLibrary::SelectFolderData(UFolderWidget::Get()->GetActionID(), FolderData, ErrorMessage))
			{
				if (FolderData[0].folder_type != ItemData.folder_type)
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Error"), TEXT("Can not paste to different type folder!!"));
					return;
				}
			}
			else
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Error"), TEXT("Get Folder Data Error!!"));
				return;
			}*/

			/*if (UFolderWidget::Get()->GetActionType() == (int32)ERightMenuType::Copy)
			{
				UUIFunctionLibrary::CopyDBFolderData(FolderData[0], ItemData.id);
				RefreshScbContentWidget();
			}
			else if (UFolderWidget::Get()->GetActionType() == (int32)ERightMenuType::Shear)
			{
				if (FolderData[0].can_add_subfolder)
				{
					if (UFolderWidget::Get()->GetCutFolderItem())
					{
						UFolderWidget::Get()->GetCutFolderItem()->RemoveFromParentFolder();
					}
				}
				else
				{
					if (UFolderWidget::Get()->GetCutFileItem())
					{
						UFolderWidget::Get()->GetCutFileItem()->RemoveFromParentFolder();
					}
				}
				
				UUIFunctionLibrary::CutDBFolderData(FolderData[0], ItemData.id);
				RefreshScbContentWidget();
				if (ChildFolders.Find(FolderData[0].id))
				{
					ChildFolders[FolderData[0].id]->SelectSelf();
				}
				else
				{
					ChildFilesMap[FolderData[0].id]->SelectSelf();
				}
			}*/
		}
		/*FolderRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Paste);
		UFolderWidget::Get()->ReSelectCurrentItem();*/
	}
	else if (ActionType == ERightMenuType::Delete)
	{
		if (ItemData.IsRootFolder())
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Root Folder Cannot Be Delete!!")).ToString());
			return;
		}
		FString Tips = FText::FromStringTable(FName("PosSt"), TEXT("Do you want to Delete the ")).ToString() + TEXT("\"") + ItemData.folder_name + TEXT("\"") + FText::FromStringTable(FName("PosSt"), TEXT(" folder?")).ToString();
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), Tips))
		{
			UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
			UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SwitchState(2);
			OnHold->SetOperationText(TEXT("Delete"));
			OnHold->SetVisibility(ESlateVisibility::Visible);
			OnTipPopConfirmHandler();
		}
	}
}

void UWidget_FolderItem::FolderWidgetEdit(const FString& FolderId, const ERightMenuType & ActionType, bool IsFolder)
{
	if (ActionType == ERightMenuType::Delete)
	{
		if (ScbChild)
		{
			if (IsFolder && IS_OBJECT_PTR_VALID(ChildFolders[FolderId]))
			{
				ScbChild->RemoveChild(ChildFolders[FolderId]);
				ChildFolders.Remove(FolderId);
			}
			else
			{
				if (IS_OBJECT_PTR_VALID(ChildFilesMap[FolderId]))
				{
					ScbChild->RemoveChild(ChildFilesMap[FolderId]);
					ChildFilesMap.Remove(FolderId);
				}
			}
			for (int32 i = 0; i < SubFolderDatas.Num(); ++i)
			{
				if (SubFolderDatas[i].id.Equals(FolderId))
				{
					SubFolderDatas.RemoveAt(i);
					break;
				}
			}
		}
		RefreshContentWidget();
		
	}
	else if (ActionType == ERightMenuType::Copy)
	{
	}
	else if (ActionType == ERightMenuType::Shear)
	{
	}
	else if (ActionType == ERightMenuType::Paste)
	{
	}
}

void UWidget_FolderItem::ClearAllSelectState()
{
	this->SetIsSelected(false);
	for (TMap<FString, UWidget_FolderItem*>::TConstIterator Iter = ChildFolders.CreateConstIterator(); Iter; ++Iter)
	{
		Iter->Value->SetIsSelected(false);
		Iter->Value->SetFolderToGrey(false);
	}
	for (TMap<FString, UWidget_FileItem*>::TConstIterator Iter = ChildFilesMap.CreateConstIterator(); Iter; ++Iter)
	{
		Iter->Value->SetIsSelected(false);
	}
}

void UWidget_FolderItem::SetItemData(const FFolderTableData & InData)
{
	if (!FMath::IsNearlyEqual(ItemData.visibility, InData.visibility, 0.0001f))
	{
		UpdateFolderState(!FMath::IsNearlyZero(InData.visibility));
	}
	ItemData = InData;
	if (InData.folder_id.IsEmpty())
	{
		UpdateFolderName(ItemData.folder_name);
	}
	else
	{
		UpdateFolderName(ItemData.folder_id + TEXT(" - ") + ItemData.folder_name);
	}
	
	if (ItemData.can_add_subfolder)
	{
#ifdef USE_REF_LOCAL_FILE

		int32 CurLevel = URefRelationFunction::GetFolderLevel(ItemData.backend_folder_path);
		UE_LOG(LogTemp, Log, TEXT("current data name : %s, level : %d"), *ItemData.folder_name, CurLevel);
		if (FolderLevel == -1) FolderLevel = CurLevel;

#else
		int32 CurLevel = 0;
		if (UFolderTableOperatorLibrary::GetFolderLevel(ItemData.id, CurLevel))
		{
			UE_LOG(LogTemp, Log, TEXT("current data name : %s, level : %d"), *ItemData.folder_name, CurLevel);
			if (FolderLevel == -1) FolderLevel = CurLevel;
		}
#endif
	}
}

void UWidget_FolderItem::SyncSubItemData(const FFolderTableData & InData)
{
	for (auto& Iter : SubFolderDatas)
	{
		if (Iter.id.Equals(InData.id))
		{
			Iter.CopyData(InData);
			return;
		}		
	}
	ConstructSubFolder(SubFolderDatas);
}

TArray<FFolderTableData>& UWidget_FolderItem::GetSubFolderDatas()
{
#ifdef USE_REF_LOCAL_FILE

	FRefDirectoryData FolderData;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(ItemData.id, FolderData))
	{
		TArray<FRefDirectoryData> SubDatas;
		UFolderWidget::Get()->GetCacheDataForDirectory(FolderData.backendFolderPath, SubDatas);

		URefRelationFunction::ConvertDirctoryDataToDBData(SubDatas, SubFolderDatas);
	}

#else
	TArray<FFolderTableData> OutParams;
	bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(ItemData.id, EFolderFileSearchType::EFolderAndFile, OutParams);
	SubFolderDatas = OutParams;
#endif

	SortAndSpliteFolderAndFile(SubFolderDatas);
	return SubFolderDatas;
}

TArray<FFolderTableData> UWidget_FolderItem::GetSubDatas()
{
	TArray<FFolderTableData> CurrentSubDatas;
	FRefDirectoryData FolderData;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(ItemData.id, FolderData))
	{
		TArray<FRefDirectoryData> SubDatas;
		UFolderWidget::Get()->GetCacheDataForDirectory(FolderData.backendFolderPath, SubDatas);

		URefRelationFunction::ConvertDirctoryDataToDBData(SubDatas, CurrentSubDatas);
	}

	SortAndSpliteFolderAndFile(CurrentSubDatas);
	return CurrentSubDatas;
}

int32 UWidget_FolderItem::GetSubLastOrder()
{
	auto SubDatas = GetSubDatas();
	if (SubDatas.Num() > 0)
	{
		SubDatas.Sort([](const FFolderTableData& A, const FFolderTableData& B) {
			return A.folder_order <= B.folder_order;
			});
		return SubDatas.Last().folder_order;
	}
	return INDEX_NONE;
}

void UWidget_FolderItem::UpdateFolderName(const FString& FolderName)
{
	if (TxtName)
	{
		if (ParentFolderWidget.Get())
		{
			for (auto& iter : ParentFolderWidget.Get()->SubFolderDatas)
			{
				if (iter.id.Equals(ItemData.id))
				{
					iter.folder_name = FolderName;
				}
			}
		}

		TxtName->SetText(FText::FromString(FolderName));
	}
}

void UWidget_FolderItem::AddFileToFolder(UWidget_FolderItem* InWidget)
{
	if (ScbChild)
	{	
		if (SubFolderDatas.Num() <= 0 || SubFolderDatas.Last().can_add_subfolder)
		{
			SubFolderDatas.Add(InWidget->GetItemData());
			ScbChild->AddChild(InWidget);
		}
		else
		{
			int32 LastFolderIndex = GetLastFolderIndexInSCB();
			UE_LOG(LogTemp, Log, TEXT("add folder to array by insert --- insert index : %d"), LastFolderIndex);
			SubFolderDatas.Insert(InWidget->GetItemData(), LastFolderIndex);
			TArray<UWidget*> ExistChild = ScbChild->GetAllChildren();
			ScbChild->ClearChildren();
			for (int32 i = 0; i < ExistChild.Num(); i++)
			{
				if (i == LastFolderIndex)
				{
					ScbChild->AddChild(InWidget);
				}
				ScbChild->AddChild(ExistChild[i]);
			}
		}
		//ScbChild->InsertChildAt(LastFolderIndex, InWidget);
		ScbChild->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UWidget_FolderItem::AddFileToFolder(UWidget_FileItem * InWidget)
{
	if (ScbChild)
	{
		SubFolderDatas.Add(InWidget->GetItemData());
		ScbChild->AddChild(InWidget);
		ScbChild->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UWidget_FolderItem::AddFileToFolder(UFolderAndFileBaseWidget * InWidget)
{
	if (ScbChild)
	{
		ScbChild->AddChild(InWidget);
	}
}

void UWidget_FolderItem::AddDataToFolder(const FFolderTableData & InData)
{
	if (InData.can_add_subfolder)
	{
		UWidget_FolderItem* NewFolder = UWidget_FolderItem::Create();
		NewFolder->FolderSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectItemEdit")));
		NewFolder->FolderItemUnFoldDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("UnFoldItem")));
		NewFolder->FolderRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FolderRightMenuActionEdit")));
		NewFolder->UpdateFolderName(InData.folder_name);
		NewFolder->SetSuperiorVisible(IsVisible && IsSuperiorVisible);
		NewFolder->UpdateFolderState(!FMath::IsNearlyZero(InData.visibility));
		NewFolder->SetVisibility(ESlateVisibility::Visible);
		NewFolder->ParentFolderWidget = this;
		NewFolder->SetItemData(InData);
		NewFolder->SetIndent(FolderLevel + 1);

		ChildFolders.Add(InData.id, NewFolder);
		//ScbChild->AddChild(NewFolder);
		AddFileToFolder(NewFolder);
	}
	else
	{
		UWidget_FileItem* NewFile = UWidget_FileItem::Create();
		NewFile->FileSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectFileItemEdit")));
		NewFile->FileRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FileRightMenuActionEdit")));
		NewFile->UpdateFileName(InData.folder_name);
		NewFile->SetSuperiorVisible(IsVisible && IsSuperiorVisible);
		NewFile->UpdateFolderState(!FMath::IsNearlyZero(InData.visibility));
		NewFile->SetVisibility(ESlateVisibility::Visible);
		NewFile->ParentFolderWidget = this;
		NewFile->SetItemData(InData);
		NewFile->SetIndent(FolderLevel + 1);

		ChildFilesMap.Add(InData.id, NewFile);
		//ScbChild->AddChild(NewFile);
		AddFileToFolder(NewFile);
	}
	//SubFolderDatas.Add(InData);
	ScbChild->SetVisibility(ESlateVisibility::Visible);
}

UFolderAndFileBaseWidget * UWidget_FolderItem::ConstructNewSubWidget(const FFolderTableData & InData)
{
	if (InData.can_add_subfolder)
	{
		UWidget_FolderItem* NewFolder = UWidget_FolderItem::Create();
		NewFolder->SetFolderLevel(FolderLevel + 1);
		NewFolder->FolderSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectItemEdit")));
		NewFolder->FolderItemUnFoldDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("UnFoldItem")));
		NewFolder->FolderRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FolderRightMenuActionEdit")));
		NewFolder->UpdateFolderName(InData.folder_name);
		NewFolder->IsVisible = !FMath::IsNearlyZero(InData.visibility);
		NewFolder->SetSuperiorVisible(IsVisible && IsSuperiorVisible);
		NewFolder->RefreshVisibleState();
		//NewFolder->SetIndent(FolderLevel + 1);
		NewFolder->SetVisibility(ESlateVisibility::Visible);
		NewFolder->ParentFolderWidget = this;
		NewFolder->SetItemData(InData);

		ChildFolders.Add(InData.id, NewFolder);
		return NewFolder;
	}
	else
	{
		UWidget_FileItem* NewFile = UWidget_FileItem::Create();
		NewFile->SetIndent(FolderLevel + 1);
		NewFile->FileSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectFileItemEdit")));
		NewFile->FileRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FileRightMenuActionEdit")));
		NewFile->UpdateFileName(InData.folder_name);
		NewFile->IsVisible = !FMath::IsNearlyZero(InData.visibility);
		NewFile->SetSuperiorVisible(IsVisible && IsSuperiorVisible);
		NewFile->RefreshVisibleState();
		//NewFile->SetIndent(FolderLevel + 1);
		NewFile->SetVisibility(ESlateVisibility::Visible);
		NewFile->ParentFolderWidget = this;
		NewFile->SetItemData(InData);

		ChildFilesMap.Add(InData.id, NewFile);
		return NewFile;
	}
}

void UWidget_FolderItem::RefreshContentWidget()
{
	if (ScbChild)
	{
		ScbChild->SetVisibility(ESlateVisibility::Collapsed);
		ScbChild->SetVisibility(ESlateVisibility::Visible);
	}
}

void UWidget_FolderItem::UpdateFolderState(bool _IsVisible)
{
	Super::UpdateFolderState(_IsVisible);
	SetFolderStateBrush(static_cast<EFolderState>(SelectType));
	//SetFolderStateBrush(GetIsSelected() ? (CkbExpandChild->IsChecked() ? EFolderState::CheckSelected : EFolderState::UnCheckSelected) : (CkbExpandChild->IsChecked() ? EFolderState::CheckUnSelected : EFolderState::UnCheckUnSelected));
	/*IsVisible = _IsVisible;
	UFolderFunctionLibrary::SetTextColor(IsVisible ? NormalTextColor : InVisibleTextColor, TxtName);*/
}

void UWidget_FolderItem::SyncFolderState(bool _IsVisible)
{
	IsVisible = _IsVisible;
	RefreshVisibleState();
	//UpdateFolderState(_IsVisible && IsSuperiorVisible);
	SyncSubState(_IsVisible && IsSuperiorVisible);
}

void UWidget_FolderItem::SyncSubState(bool _InSubSuperiorVisible)
{
	for (TMap<FString, UWidget_FolderItem*>::TConstIterator Iter = ChildFolders.CreateConstIterator(); Iter; ++Iter)
	{
		Iter->Value->SetSuperiorVisible(_InSubSuperiorVisible);
		Iter->Value->RefreshVisibleState();
		Iter->Value->SyncSubState(Iter->Value->GetCurrentVisibleState());
	}
	for (TMap<FString, UWidget_FileItem*>::TConstIterator Iter = ChildFilesMap.CreateConstIterator(); Iter; ++Iter)
	{
		Iter->Value->SetSuperiorVisible(_InSubSuperiorVisible);
		Iter->Value->RefreshVisibleState();
	}
}

void UWidget_FolderItem::SetIsSelected(bool _IsSelected)
{
	Super::SetIsSelected(_IsSelected);
	SetFolderStateBrush(_IsSelected ? (CkbExpandChild->IsChecked() ? EFolderState::CheckSelected : EFolderState::UnCheckSelected) : (CkbExpandChild->IsChecked() ? EFolderState::CheckUnSelected : EFolderState::UnCheckUnSelected));
}

void UWidget_FolderItem::SelectSelf()
{
	OnClickedBtnSelect();
}

void UWidget_FolderItem::SetChildFolderExpand(bool IsExpand)
{
	if (CkbExpandChild)
	{
		CkbExpandChild->SetIsChecked(IsExpand);
	}
	SetFolderStateBrush(IsSelected ? (IsExpand ? EFolderState::CheckSelected : EFolderState::CheckUnSelected) : (IsExpand ? EFolderState::CheckUnSelected : EFolderState::UnCheckUnSelected));
	//OnStateChangedCkbExpand(IsExpand);
	//ChildExpandInner(IsExpand);

	if (IsExpand)
	{
		RefreshContentWidget();
	}
}

void UWidget_FolderItem::SetSearchFolderExpand(const TArray<FString>& InSubArray)
{
	UE_LOG(LogTemp, Log, TEXT("Currnet folder is check : %d, name : %s, SubArray size : %d"), IsCkbExpandCheck(), *ItemData.folder_name, InSubArray.Num());
	if (IsCkbExpandCheck())
	{
		if (InSubArray.Num() == 1) // 1 is file
		{
			/*if (IS_OBJECT_PTR_VALID(*(ChildFolders.Find(InSubArray[0]))))
			{
				ChildFolders[InSubArray[0]]->SelectSelf();
			}
			else*/ if (IS_OBJECT_PTR_VALID(*(ChildFilesMap.Find(InSubArray[0]))))
			{
				ChildFilesMap[InSubArray[0]]->SelectSelf();
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("Widget_FolderItem---path error, Expand Fail"));
			}
		}
		else
		{
			TArray<FString> SubPaths;
			for (int32 i = 1; i < InSubArray.Num(); ++i)
			{
				SubPaths.Add(InSubArray[i]);
			}
			ChildFolders[InSubArray[0]]->SetSearchFolderExpand(SubPaths);
		}
	}
	else
	{
		SubPaths = InSubArray;
		SetChildFolderExpand(true);
	}
}

FString UWidget_FolderItem::GetSubFolderSelectID()
{
	for(auto& FolderWidget : ChildFolders)
	{
		if(IS_OBJECT_PTR_VALID(FolderWidget.Value))
		{
			if(FolderWidget.Value->GetIsSelected())
			{
				return FolderWidget.Key;
			}
		}
	}
	return TEXT("-1");
}

FFolderTableData UWidget_FolderItem::GetSubFolderSelectData()
{
	for (auto& FolderWidget : ChildFolders)
	{
		if (IS_OBJECT_PTR_VALID(FolderWidget.Value))
		{
			if (FolderWidget.Value->GetIsSelected())
			{
				return FolderWidget.Value->GetItemData();
			}
		}
	}
	return FFolderTableData();
}

void UWidget_FolderItem::SetObscureSearchExpand(
	const TArray<FRefDirectoryData>& CurSubDatas,
	TArray<FRefDirectoryDataContent>& ContentDatas, 
	TArray<FString>& Paths
)
{
	if (Paths.IsEmpty())
		return;

	if(!IsCkbExpandCheck())
	{
		SetChildFolderExpand(true);

		GenerateSubFoldersInner(CurSubDatas);

	}

	SetObscureSearchExpand_Inner(ContentDatas, Paths);
}

void UWidget_FolderItem::SetObscureSearchExpand_Inner(
	TArray<FRefDirectoryDataContent>& ContentDatas,
	TArray<FString>& Paths
)
{
	const FString Mark = Paths[0];
	if (Paths.Num() == 1)
	{
		if (ChildFilesMap.Contains(Mark))   //ID
		{
			ChildFilesMap[Mark]->SelectSelf();
		}
		else
		{
			for (auto CFM : ChildFilesMap) //folderID
			{
				if (IS_OBJECT_PTR_VALID(CFM.Value))
				{
					auto CurData = CFM.Value->GetItemData();
					if (Mark.Equals(CurData.folder_id, ESearchCase::IgnoreCase))
					{
						CFM.Value->SelectSelf();
						break;
					}
				}
			}
		}
	}
	else
	{
		if (ChildFolders.Contains(Mark))
		{
			auto CurArrContent = ContentDatas[0];
			ContentDatas.RemoveAt(0);
			Paths.RemoveAt(0);
			ChildFolders[Mark]->SetObscureSearchExpand(CurArrContent.CDataArr, ContentDatas, Paths);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Widget_FolderItem --- Folder Path [%s] Error, Expand Fail"), *Mark);
		}
	}
}

void UWidget_FolderItem::OpenFolderExpand(const TArray<FString>& InSubArray)
{
	if (IsCkbExpandCheck())
	{
		if (InSubArray.Num() == 1)
		{
			if (IS_OBJECT_PTR_VALID(*(ChildFolders.Find(InSubArray[0]))))
			{
				ChildFolders[InSubArray[0]]->SelectSelf();
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("Widget_FolderItem---path error, Expand Fail"));
			}
		}
		else
		{
			TArray<FString> SubPaths;
			for (int32 i = 1; i < InSubArray.Num(); ++i)
			{
				SubPaths.Add(InSubArray[i]);
			}
			ChildFolders[InSubArray[0]]->OpenFolderExpand(SubPaths);
		}
	}
	else
	{
		FolderPaths = InSubArray;
		SetChildFolderExpand(true);
	}
}

void UWidget_FolderItem::GrayFolderByAlreadyExpand(TArray<FString> InSubArray, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair)
{
	if (InSubArray.IsEmpty())
		return;

	if (InSubArray.Num() == 1)
	{
		GrayFolderInnerAction(InSubArray[0], IsCheck, SelectIDFolderPair);
	}
	else
	{
		if (IsCkbExpandCheck())
		{
			if (ChildFolders.Contains(InSubArray[0]))
			{
				FString CurPath = InSubArray[0];
				InSubArray.RemoveAt(0);
				ChildFolders[CurPath]->SetFolderToGrey(false);
				ChildFolders[CurPath]->GrayFolderByAlreadyExpand(InSubArray, IsCheck, SelectIDFolderPair);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error, sub folder no contain"), *ItemData.id, *ItemData.folder_name);
			}
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error, folder no expand"), *ItemData.id, *ItemData.folder_name);
		}
	}
}

void UWidget_FolderItem::GrayFolderInnerAction(const FString& ActionID, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair)
{
	if (!IsCheck.Key)
	{
		if (IsCkbExpandCheck())
		{
			if (ChildFolders.Contains(ActionID))
			{
				ChildFolders[ActionID]->SetFolderToGrey(true);
				UFolderWidget::Get()->SetGrayWidget(ChildFolders[ActionID]);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error last, sub folder no contain"), *ItemData.id, *ItemData.folder_name);
			}
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error when last, folder no expand"), *ItemData.id, *ItemData.folder_name);
		}
	}
	else
	{
		if (IsCheck.Value)
		{
			if (ActionID.Equals(SelectIDFolderPair.Key, ESearchCase::IgnoreCase))
			{
				if (SelectIDFolderPair.Value)
				{
					if (ChildFolders.Contains(ActionID))
					{
						ChildFolders[ActionID]->SelectSelf();
					}
				}
				else
				{
					if (ChildFilesMap.Contains(ActionID))
					{
						ChildFilesMap[ActionID]->SelectSelf();
					}
				}
			}
			else
			{
				if (ChildFolders.Contains(ActionID))
				{
					ChildFolders[ActionID]->SetFolderToGrey(true);
					UFolderWidget::Get()->SetGrayWidget(ChildFolders[ActionID]);
				}
			}
		}
		else
		{
			if (SelectIDFolderPair.Value)
			{
				if (ChildFolders[ActionID]->GetChildFolders().Contains(SelectIDFolderPair.Key))
					ChildFolders[ActionID]->GetChildFolders()[SelectIDFolderPair.Key]->SelectSelf();
			}
			else
			{
				if (ChildFolders[ActionID]->GetChildFilesMap().Contains(SelectIDFolderPair.Key))
					ChildFolders[ActionID]->GetChildFilesMap()[SelectIDFolderPair.Key]->SelectSelf();
			}
		}
	}
}

void UWidget_FolderItem::GrayFolderInnerAction(const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair)
{
	if (!IsCheck.Key)
	{
		if (IsCkbExpandCheck())
		{
			if (SelectIDFolderPair.Value)
			{
				if (ChildFolders.Contains(SelectIDFolderPair.Key))
				{
					ChildFolders[SelectIDFolderPair.Key]->SetFolderToGrey(true);
					UFolderWidget::Get()->SetGrayWidget(ChildFolders[SelectIDFolderPair.Key]);
				}
				else
				{
					UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error last, sub folder no contain"), *ItemData.id, *ItemData.folder_name);
				}
			}
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("gray folder [id : %s, name : %s] error when last, folder no expand"), *ItemData.id, *ItemData.folder_name);
		}
	}
	else
	{
		if (IsCheck.Value)
		{
			if (ItemData.id.Equals(SelectIDFolderPair.Key, ESearchCase::IgnoreCase))
			{
				this->SelectSelf();
			}
			else
			{
				this->SetFolderToGrey(true);
				UFolderWidget::Get()->SetGrayWidget(this);
			}
			
		}
		else
		{
			if (SelectIDFolderPair.Value)
			{
				if (ChildFolders.Contains(SelectIDFolderPair.Key))
					ChildFolders[SelectIDFolderPair.Key]->SelectSelf();
			}
			else
			{
				if (ChildFolders.Contains(SelectIDFolderPair.Key))
					ChildFolders[SelectIDFolderPair.Key]->SelectSelf();
			}
		}
	}
}

void UWidget_FolderItem::RefreshScbContentWidget()
{
	checkf(ScbChild, TEXT("scb content is null"));
	ScbChild->ClearChildren();
	ChildFolders.Empty();
	SearchSubFoldersRequest();
	/*TArray<FFolderTableData> CurrentSubFolder;
	FString ErrorMessage;
	bool Result = UFolderTableOperatorLibrary::SelectSubFolder(ItemData.id, CurrentSubFolder, ErrorMessage);
	checkf(Result, TEXT("get sub folder data error"));*/
	/*TArray<FFolderTableData> SubFolders;
	TArray<FFolderTableData> SubFiles;
	for (auto& Data : CurrentSubFolder)
	{
		if (Data.can_add_subfolder)
		{
			SubFolders.Add(Data);
		}
		else
		{
			SubFiles.Add(Data);
		}
	}
	ConstructSubFolder(SubFolders, SubFiles);*/
	//ConstructSubFolder(CurrentSubFolder);
	RefreshContentWidget();
}

void UWidget_FolderItem::OnTipPopConfirmHandler()
{
	UE_LOG(LogTemp, Log, TEXT("confirm to delete left tree folder item, Item name : %s"), *ItemData.folder_name);
#ifdef USE_REF_LOCAL_FILE

	NetUUID.DeleteUUID = DeleteAction(ItemData.id);

#else
	if (UFolderTableOperatorLibrary::DeleteFolderFile(ItemData.id, true))
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		FolderRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Delete);
		if (ParentFolderWidget.IsValid())
		{
			ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
			ParentFolderWidget.Get()->SelectSelf();
		}
		
	}
	else
	{
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Delete Folder Error!!")).ToString());
	}
#endif
}

void UWidget_FolderItem::ChildExpandInner(bool IsCheck)
{
	CheckBoxLogicInner(IsCheck, false);
	/*IsSubOpen = IsCheck;
	if (IsSubOpen)
	{
		SetFolderStateBrush(IsSelected ? EFolderState::CheckSelected : EFolderState::CheckUnSelected);
		ConstructSubFolder(false);
	}
	else
	{
		SetFolderStateBrush(IsSelected ? EFolderState::UnCheckSelected : EFolderState::UnCheckUnSelected);
		checkf(ScbChild, TEXT("scb content is null"));
		ScbChild->ClearChildren();
		ChildFolders.Empty();
		FolderItemUnFoldDelegate.ExecuteIfBound(this, IsSubOpen);
	}
	if (ScbChild)
	{
		ScbChild->SetVisibility(IsCheck ? (ScbChild->GetChildrenCount() == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible) : ESlateVisibility::Collapsed);
	}*/
}

void UWidget_FolderItem::CheckBoxLogicInner(bool IsCheck, bool IsSync)
{
	IsSubOpen = IsCheck;
	if (IsSubOpen)
	{
		SetFolderStateBrush(IsSelected ? EFolderState::CheckSelected : EFolderState::CheckUnSelected);
		ConstructSubFolder(IsSync);
	}
	else
	{
		SetFolderStateBrush(IsSelected ? EFolderState::UnCheckSelected : EFolderState::UnCheckUnSelected);
		checkf(ScbChild, TEXT("scb content is null"));
		ScbChild->ClearChildren();
		ChildFolders.Empty();
		
	}
	if (ScbChild)
	{
		ScbChild->SetVisibility(IsCheck ? (ScbChild->GetChildrenCount() == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible) : ESlateVisibility::Collapsed);
	}
}

int32 UWidget_FolderItem::GetLastFolderIndexInSCB()
{
	int Index = 0;
	for (int32 i = 0; i < SubFolderDatas.Num(); i++)
	{
		if (!SubFolderDatas[i].can_add_subfolder)
			return i;
		Index = i;
	}

	return Index;
}

void UWidget_FolderItem::OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (NetUUID.DeleteUUID.Equals(UUID))
	{
		NetUUID.ResetDeleteAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Delete));
			if (ParentFolderWidget.IsValid())
			{
				ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
				ParentFolderWidget.Get()->SelectSelf();
			}
			else
			{
				UFolderWidget::Get()->ClearSelectWidget();
				this->SetVisibility(ESlateVisibility::Collapsed);
				this->RemoveFromParent();
			}
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);

			/*UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SetVisibility(ESlateVisibility::Collapsed);
			UI_POP_WINDOW_ERROR(Msg);*/
		}
	}
}

void UWidget_FolderItem::OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CopyUUID.Equals(UUID))
	{
		NetUUID.ResetCopyAction();
		if(bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			if (!DirData.IsValidIndex(0))
				return;
			FFolderTableData NewActionData;
			URefRelationFunction::ConvertDirctoryDataToDBData(DirData[0], NewActionData);

			UFolderWidget::Get()->AddCacheDataForDirectory_Single(
				URefRelationFunction::GetFolderDirectory(DirData[0].backendFolderPath, false),
				DirData[0]
			);
			UFolderWidget::Get()->AddCacheDataForRefDirectory(DirData[0]);

			//修改文件里面的路径
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(NewActionData);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = NewActionData.backend_folder_path;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}
			
			if (CkbExpandChild->IsChecked())
			{
				AddDataToFolder(NewActionData);
				if (ChildFolders.Find(NewActionData.id))
				{
					ChildFolders[NewActionData.id]->SelectSelf();
				}
				else
				{
					ChildFilesMap[NewActionData.id]->SelectSelf();
				}
			}
			else
			{
				CopyOrCutData = NewActionData;
				SetChildFolderExpand(true);
			}
			FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);

			/*UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
			OnHold->SetVisibility(ESlateVisibility::Collapsed);
			UI_POP_WINDOW_ERROR(Msg);*/

		}
	}
}

void UWidget_FolderItem::OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CutUUID.Equals(UUID))
	{
		NetUUID.ResetCutAction();
		if (bSuccess && DirData.IsValidIndex(0))
		{

			OPERATOR_SUCCESS();

			if (!DirData.IsValidIndex(0))
				return;

			FFolderTableData NewActionData;
			URefRelationFunction::ConvertDirctoryDataToDBData(DirData[0], NewActionData);

			//修改文件里面的路径
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(NewActionData);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = NewActionData.backend_folder_path;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			//update cache 
			UFolderWidget::Get()->AddCacheDataForRefDirectory(DirData[0]);

			if (NewActionData.can_add_subfolder)
			{
				if (UFolderWidget::Get()->GetCutFolderItem())
				{
					UFolderWidget::Get()->GetCutFolderItem()->RemoveFromParentFolder();
				}
			}
			else
			{
				if (UFolderWidget::Get()->GetCutFileItem())
				{
					UFolderWidget::Get()->GetCutFileItem()->RemoveFromParentFolder();
				}
			}
			if (CkbExpandChild->IsChecked())
			{
				AddDataToFolder(NewActionData);
				if (ChildFolders.Find(NewActionData.id))
				{
					ChildFolders[NewActionData.id]->SelectSelf();
				}
				else
				{
					ChildFilesMap[NewActionData.id]->SelectSelf();
				}
			}
			else
			{
				CopyOrCutData = NewActionData;
				SetChildFolderExpand(true);
			}
			FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		
		}
	}
}
     
//no use
void UWidget_FolderItem::ConstructSubFolder(const TArray<FFolderTableData>& SubFolderDatas, const TArray<FFolderTableData>& SubFileDatas)
{
	checkf(ScbChild, TEXT("scb content is null"));
	ScbChild->ClearChildren();
	ChildFolders.Empty();
	ChildFilesMap.Empty();
	for (auto& FolderData : SubFolderDatas)
	{
		UWidget_FolderItem* NewFolder = UWidget_FolderItem::Create();
		NewFolder->FolderSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectItemEdit")));
		NewFolder->FolderItemUnFoldDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("UnFoldItem")));
		NewFolder->FolderRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FolderRightMenuActionEdit")));
		NewFolder->UpdateFolderName(FolderData.folder_name);
		NewFolder->UpdateFolderState(true);
		NewFolder->SetVisibility(ESlateVisibility::Visible);
		NewFolder->ParentFolderWidget = this;
		NewFolder->SetItemData(FolderData);

		ChildFolders.Add(FolderData.id, NewFolder);
		ScbChild->AddChild(NewFolder);
	}
	for (auto& FileData : SubFileDatas)
	{
		UWidget_FileItem* NewFile = UWidget_FileItem::Create();
		NewFile->FileSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectFileItemEdit")));
		NewFile->FileRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FileRightMenuActionEdit")));
		NewFile->UpdateFileName(FileData.folder_name);
		NewFile->UpdateFolderState(true);
		NewFile->SetVisibility(ESlateVisibility::Visible);
		NewFile->ParentFolderWidget = this;
		NewFile->SetItemData(FileData);

		ChildFilesMap.Add(FileData.id, NewFile);
		ScbChild->AddChild(NewFile);
	}
}

void UWidget_FolderItem::ConstructSubFolder(const TArray<FFolderTableData>& SubDatas)
{
	//int32 FolderLevel;
	/*bool Res = UFolderTableOperatorLibrary::GetLevelOfFolder(ItemData.id, FolderLevel);
	if (!Res)
	{
		UE_LOG(LogTemp, Log, TEXT("get folder level error"));
	}*/
	checkf(ScbChild, TEXT("scb content is null"));
	ScbChild->ClearChildren();
	ChildFolders.Empty();
	ChildFilesMap.Empty();

	SubFolderDatas = SubDatas;
	bool SubSuperiorVisible = (IsVisible && IsSuperiorVisible);
	int32 Order = 0;
	for (auto& Data : SubDatas)
	{
		if (Data.deletable)
		{
			continue;
		}
		if (Data.can_add_subfolder)
		{
			UWidget_FolderItem* NewFolder = UWidget_FolderItem::Create();
			if (FolderLevel == -1) FolderLevel = 1;
			NewFolder->SetFolderLevel(FolderLevel + 1);
			NewFolder->FolderSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectItemEdit")));
			NewFolder->FolderItemUnFoldDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("UnFoldItem")));
			NewFolder->FolderRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FolderRightMenuActionEdit")));
			NewFolder->UpdateFolderName(Data.folder_name);
			NewFolder->IsSuperiorVisible = SubSuperiorVisible;
			NewFolder->UpdateFolderState(!FMath::IsNearlyZero(Data.visibility));
			NewFolder->SetVisibility(ESlateVisibility::Visible);
			NewFolder->ParentFolderWidget = this;
			NewFolder->SetItemData(Data);
			//NewFolder->SetFolderOrder(++Order);
			//NewFolder->SetIndent(FolderLevel + 1);

			if (UFolderWidget::Get()->IsInMultiSelect(NewFolder))
			{
				NewFolder->SetIsSelected(true);
				UFolderWidget::Get()->UpdateMultiSelectItem(NewFolder);
			}

			ChildFolders.Add(Data.id, NewFolder);
			ScbChild->AddChild(NewFolder);
		}
		else
		{
			UWidget_FileItem* NewFile = UWidget_FileItem::Create();
			NewFile->SetIndent(FolderLevel + 1);
			NewFile->FileSelectDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("SelectFileItemEdit")));
			NewFile->FileRightMenuActionDelegate.BindUFunction(UFolderWidget::Get(), FName(TEXT("FileRightMenuActionEdit")));
			NewFile->UpdateFileName(Data.folder_name);
			NewFile->IsSuperiorVisible = SubSuperiorVisible;
			NewFile->UpdateFolderState(!FMath::IsNearlyZero(Data.visibility));	
			NewFile->SetVisibility(ESlateVisibility::Visible);
			NewFile->ParentFolderWidget = this;
			NewFile->SetItemData(Data);
			//NewFile->SetFolderOrder(++Order);
			//NewFile->SetIndent(FolderLevel + 1);
			if (UFolderWidget::Get()->IsInMultiSelect(NewFile))
			{
				NewFile->SetIsSelected(true);
				UFolderWidget::Get()->UpdateMultiSelectItem(NewFile);
			}

			ChildFilesMap.Add(Data.id, NewFile);
			ScbChild->AddChild(NewFile);
		}
	}
	ScbChild->SetVisibility(CkbExpandChild->IsChecked() ? (ScbChild->GetChildrenCount() == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible) : ESlateVisibility::Collapsed);
}

void UWidget_FolderItem::ConstructSubFolder(bool NeedSyncSelect /*= false*/)
{
	SearchSubFoldersRequest();
	if (NeedSyncSelect)
	{
		UWidget_FolderItem* CurSelectFolder = UFolderWidget::Get()->GetCurrentSelectItem();
		if (IS_OBJECT_PTR_VALID(CurSelectFolder))
		{
			const FFolderTableData& SelectFolderData = CurSelectFolder->GetItemData();
			if (UWidget_FileItem* CurSelectFile = UFolderWidget::Get()->GetCurrentSelectFile())
			{
				if (SelectFolderData.id.Equals(ItemData.id))
				{//展开同一文件夹
					ReSelectSubFolderOrFile(CurSelectFile->GetItemData().id, false);
				}
			}
			else
			{
				if (SelectFolderData.parent_id.Equals(ItemData.id))
				{//展开选中文件夹的上层文件夹
					ReSelectSubFolderOrFile(SelectFolderData.id, true);
				}
			}
		}
	}

	/*TArray<FFolderTableData> SubDatas;
	TArray<FFolderTableData> SubFolders;
	TArray<FFolderTableData> SubFiles;
	FString RootErrorMessage;
	bool Result = UFolderTableOperatorLibrary::SelectSubFolder(ItemData.id, SubDatas, RootErrorMessage);
	if (!Result)
	{
		UE_LOG(LogTemp, Log, TEXT("FolderItem---get sub folder error/parent id : %s, %d"), *RootErrorMessage, ItemData.id);
		return;
	}
	for (auto& Data : SubDatas)
	{
		if (Data.can_add_subfolder)
		{
			SubFolders.Add(Data);
		}
		else
		{
			SubFiles.Add(Data);
		}
	}
	ConstructSubFolder(SubDatas);*/
	//ConstructSubFolder(SubFolders, SubFiles);
}

void UWidget_FolderItem::ReSelectSubFolderOrFile(const FString& FolderOrFileId, bool IsFolder)
{
	ConstructSubFolder();
	if (IsFolder)
	{
		ChildFolders[FolderOrFileId]->SelectSelf();
	}
	else
	{
		ChildFilesMap[FolderOrFileId]->SelectSelf();
	}

}

void UWidget_FolderItem::ReSelectAfterSwap(const TArray<FFolderTableData>& SwapDatas)
{
	//index 0 is current select data ; index 1 is exchange data
	if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		int32 CurrentSelectIndex;
		SubFolderDatas.Find(SwapDatas[0], CurrentSelectIndex);
		SubFolderDatas[CurrentSelectIndex].CopyData(SwapDatas[0]);
		if (SwapDatas[0].folder_order < SwapDatas[1].folder_order)
		{
			SubFolderDatas.RemoveAt(CurrentSelectIndex - 1);
			//ScbChild->RemoveChildAt(CurrentSelectIndex - 1);
		}
		else if (SwapDatas[0].folder_order == SwapDatas[1].folder_order)
		{
			return;
		}
		else
		{
			SubFolderDatas.RemoveAt(CurrentSelectIndex + 1);
			//ScbChild->RemoveChildAt(CurrentSelectIndex + 1);
		}
		SubFolderDatas.Insert(SwapDatas[1], CurrentSelectIndex);
		UFolderWidget::Get()->SyncSortData(ItemData.backend_folder_path, SubFolderDatas);
		ConstructSubFolder(SubFolderDatas);
		if (SwapDatas[0].can_add_subfolder)
		{
			ChildFolders[SwapDatas[0].id]->SelectSelf();
		}
		else
		{
			ChildFilesMap[SwapDatas[0].id]->SelectSelf();
		}
		//ScbChild->InsertChildAt(CurrentSelectIndex, ConstructNewSubWidget(SwapDatas[1]));
	}
}

void UWidget_FolderItem::RemoveFromParentFolder()
{
	if (ParentFolderWidget.IsValid())
	{
		ParentFolderWidget.Get()->FolderWidgetEdit(ItemData.id, ERightMenuType::Delete, ItemData.can_add_subfolder);
	}
}

void UWidget_FolderItem::RemoveSubFolder(const FFolderTableData& InData)
{
	if (InData.can_add_subfolder)
	{
		ChildFolders[InData.id]->SetVisibility(ESlateVisibility::Collapsed);
		ChildFolders.Remove(InData.id);
		//ScbChild->RemoveChild(ChildFolders[InData.id]);
	}
	else
	{
		ChildFilesMap[InData.id]->SetVisibility(ESlateVisibility::Collapsed);
		ChildFilesMap.Remove(InData.id);
		//ScbChild->RemoveChild(ChildFilesMap[InData.id]);
	}
	SubFolderDatas.Remove(InData);
}

bool UWidget_FolderItem::IsCkbExpandCheck()
{
	return CkbExpandChild->IsChecked();
}

UWidget_FolderItem * UWidget_FolderItem::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UWidget_FolderItem::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UWidget_FolderItem>(UWidget_FolderItem::WidgetFolderPath);
}

void UWidget_FolderItem::SetFolderToGrey(bool InState)
{
	if (InState)
	{
		BorFolderOrFile->SetBrushColor(FLinearColor(0.204f, 0.204f, 0.204f, 0.6f));
		TxtName->SetColorAndOpacity(FSlateColor((IsVisible && IsSuperiorVisible) ? NameSelect : NameInVisible));
		IsGrey = true;
	}
	else 
	{
		BorFolderOrFile->SetBrushColor(FolderOrFileNormal);
		TxtName->SetColorAndOpacity(FSlateColor((IsVisible && IsSuperiorVisible) ? NameNormalOrHover : NameInVisible));
		IsGrey = false;
	}
}


void UWidget_FolderItem::FolderFileCopyFinishAction(const FString& ActionReturnID)
{
	UE_LOG(LogTemp, Warning, TEXT("[UWidget_FolderItem] --- [Finish Copy]  Func Return Id : %s"), *ActionReturnID);

	if (IsInGameThread())
	{
		CopyDirectActionByGameThread(ActionReturnID);
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread,
			[ActionReturnID, this]()->void
		{
			TArray<FFolderTableData> Datas;
			UFolderTableOperatorLibrary::FolderFileSearchByID(ActionReturnID, EFolderFileSearchType::EFolderAndFile, Datas);
			if (Datas.Num() <= 0)
				return;
			FFolderTableData NewActionData = Datas[0];
			if (this->CkbExpandChild->IsChecked())
			{
				this->AddDataToFolder(NewActionData);
				if (this->ChildFolders.Find(NewActionData.id))
				{
					this->ChildFolders[NewActionData.id]->SelectSelf();
				}
				else
				{
					this->ChildFilesMap[NewActionData.id]->SelectSelf();
				}
			}
			else
			{
				this->CopyOrCutData = NewActionData;
				this->SetChildFolderExpand(true);
			}
			this->FolderRightMenuActionDelegate.ExecuteIfBound(this, static_cast<int32>(ERightMenuType::Paste));
		}
		);
	}
}

void UWidget_FolderItem::SearchSubFoldersRequest()
{
	
#ifdef USE_REF_LOCAL_FILE

	/*FRefToLocalFileData RefData;
	UFolderWidget::Get()->GetCacheDataForFile(
		ItemData.can_add_subfolder ? URefRelationFunction::FormatFolderID(ItemData.id) : URefRelationFunction::FormatFolderID(ItemData.folder_id),
		RefData);
	TArray<FRefDirectoryData> SubDatas;
	if(!UFolderWidget::Get()->GetCacheDataForDirectory(RefData.FolderDBData.backend_directory, SubDatas))
	{
		URefRelationFunction::GetSubDirectory(RefData.FolderDBData.backend_directory, SubDatas);
		SubDatas = URefRelationFunction::GetSubLevelDirectory(SubDatas, RefData.FolderDBData.backend_directory);
		UFolderWidget::Get()->AddCacheDataForDirectory(RefData.FolderDBData.backend_directory, SubDatas);
	}
	URefRelationFunction::ConvertDirctoryDataToDBData(SubDatas, OutDatas);*/

	NetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest(ItemData.backend_folder_path);

#else

	TArray<FFolderTableData> OutDatas;
	bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(ItemData.id, EFolderFileSearchType::EFolderAndFile, OutDatas);


	ConstructSubFolder(OutDatas);
	if (OutDatas.Num() > 0)
	{
		
		if (CopyOrCutData.IsValid())
		{
			if (CopyOrCutData.can_add_subfolder)
			{
				ChildFolders[CopyOrCutData.id]->SelectSelf();
			}
			else
			{
				ChildFilesMap[CopyOrCutData.id]->SelectSelf();
			}
			CopyOrCutData.id = RootParentID;
		}
		if (SubPaths.Num() > 0)
		{
			SetSearchFolderExpand(SubPaths);
			SubPaths.Empty();
		}
		if (FolderPaths.Num() > 0)
		{
			OpenFolderExpand(FolderPaths);
			FolderPaths.Empty();
		}
	}
#endif

}

void UWidget_FolderItem::SortAndSpliteFolderAndFile(TArray<FFolderTableData>& Datas)
{
	TArray<FFolderTableData> FoldersDatas;
	TArray<FFolderTableData> FilesDatas;
	for(auto& Iter : Datas)
	{
		if(Iter.can_add_subfolder)
		{
			FoldersDatas.Add(Iter);
		}
		else
		{
			FilesDatas.Add(Iter);
		}
	}
	FoldersDatas.Sort(
		[](const FFolderTableData& Data1, const FFolderTableData& Data2)->bool
		{
			return Data1.folder_order <= Data2.folder_order;
		}
	);
	FilesDatas.Sort(
		[](const FFolderTableData& Data1, const FFolderTableData& Data2)->bool
		{
			return Data1.folder_order <= Data2.folder_order;
		}
	);

	Datas.Empty();
	Datas.Append(FoldersDatas);
	Datas.Append(FilesDatas);
}

void UWidget_FolderItem::GenerateSubFoldersInner(TArray<FRefDirectoryData> SubDatas)
{
	TArray<FFolderTableData> OutDatas;

	SubDatas = URefRelationFunction::GetSubLevelDirectory(SubDatas, ItemData.backend_folder_path);
	UFolderWidget::Get()->AddCacheDataForRefDirectory(SubDatas);
	UFolderWidget::Get()->AddCacheDataForDirectory(ItemData.backend_folder_path, SubDatas);
	URefRelationFunction::ConvertDirctoryDataToDBData(SubDatas, OutDatas);

	SortAndSpliteFolderAndFile(OutDatas);
	ConstructSubFolder(OutDatas);
	
	if (OutDatas.Num() > 0)
	{

		if (CopyOrCutData.IsValid())
		{
			if (CopyOrCutData.can_add_subfolder)
			{
				ChildFolders[CopyOrCutData.id]->SelectSelf();
			}
			else
			{
				ChildFilesMap[CopyOrCutData.id]->SelectSelf();
			}
			CopyOrCutData.id = RootParentID;
		}
		if (SubPaths.Num() > 0)
		{
			SetSearchFolderExpand(SubPaths);
			SubPaths.Empty();
		}
		if (FolderPaths.Num() > 0)
		{
			OpenFolderExpand(FolderPaths);
			FolderPaths.Empty();
		}
	}
}

void UWidget_FolderItem::CopyDirectActionByGameThread(const FString& ActionID)
{
	TArray<FFolderTableData> Datas;
	UFolderTableOperatorLibrary::FolderFileSearchByID(ActionID, EFolderFileSearchType::EFolderAndFile, Datas);
	if (Datas.Num() <= 0)
		return;
	FFolderTableData NewActionData = Datas[0];
	if (CkbExpandChild->IsChecked())
	{
		AddDataToFolder(NewActionData);
		if (ChildFolders.Find(NewActionData.id))
		{
			ChildFolders[NewActionData.id]->SelectSelf();
		}
		else
		{
			ChildFilesMap[NewActionData.id]->SelectSelf();
		}
	}
	else
	{
		CopyOrCutData = NewActionData;
		SetChildFolderExpand(true);
	}
	FolderRightMenuActionDelegate.ExecuteIfBound(this, (int32)ERightMenuType::Paste);
}

void UWidget_FolderItem::SetFolderOrder(const int32& InOrder)
{
	ItemData.folder_order = InOrder;
	UFolderTableOperatorLibrary::UpdateFolderFileOrder(ItemData.id, InOrder, true);
}

FReply UWidget_FolderItem::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	/*if (ItemData.parent_id == -1)
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Warning"), TEXT("Root Folder Cannot Be Operator!!"));
		return FReply::Unhandled();
	}*/
	
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (UFolderWidget::Get()->IsMultiSelect())
			return FReply::Handled();

		if (!IS_OBJECT_PTR_VALID(RightMenuWidget))
		{
			RightMenuWidget = URightMouseMenuWidget::Create();
			RightMenuWidget->AddToViewport(10);
		}
		RightMenuWidget->UpdateContent(InMouseEvent.GetScreenSpacePosition());
		SelectSelf();
		RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
		RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UWidget_FolderItem::OnStateChangedCkbExpand(bool IsCheck)
{
	CheckBoxLogicInner(IsCheck, true);
	FolderItemUnFoldDelegate.ExecuteIfBound(this, IsCheck);
	//IsSubOpen = IsCheck;
	//if (IsSubOpen)
	//{
	//	SetFolderStateBrush(IsSelected ? EFolderState::CheckSelected : EFolderState::CheckUnSelected);
	//	ConstructSubFolder(true);
	//}
	//else
	//{
	//	SetFolderStateBrush(IsSelected ? EFolderState::UnCheckSelected : EFolderState::UnCheckUnSelected);
	//	checkf(ScbChild, TEXT("scb content is null"));
	//	ScbChild->ClearChildren();
	//	ChildFolders.Empty();
	//	//BorGrey->SetBrushColor(FLinearColor(0.204f, 0.204f, 0.204f, 0.6f));
	//	FolderItemUnFoldDelegate.ExecuteIfBound(this, IsSubOpen);
	//}
	//if (ScbChild)
	//{
	//	ScbChild->SetVisibility(IsCheck ? (ScbChild->GetChildrenCount() == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible) : ESlateVisibility::Collapsed);
	//}
}

void UWidget_FolderItem::OnClickedBtnSelect()
{
	UE_LOG(LogTemp, Log, TEXT("select folder"));
	IsSelected = true;
	SearchSubFoldersRequest();
	//FolderSelectDelegate.ExecuteIfBound(this, true);
}

void UWidget_FolderItem::OnHoveredBtnSelect()
{
	if (IsGrey) return;
	if (!IsSelected && BorFolderOrFile)
	{
		//UFolderFunctionLibrary::SetBorderColor(HoverColor, BorBackground);
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Hover);
	}
}

void UWidget_FolderItem::OnUnHoveredBtnSelect()
{
	if (IsGrey) return;
	if (!IsSelected && BorFolderOrFile)
	{
		//UFolderFunctionLibrary::SetBorderColor(NormalColor, BorBackground);
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Normal);
	}
}

void UWidget_FolderItem::OnStateChangedSelectCheck(bool IsCheck)
{

}

#undef LOCTEXT_NAMESPACE
