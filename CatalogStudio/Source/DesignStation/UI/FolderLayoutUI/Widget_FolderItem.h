// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FolderAndFileBaseWidget.h"
#include "Widget_FileItem.h"
#include "Widget_FolderItem.generated.h"

/**
 * 
 */

class UCheckBox;
class UScrollBox;
class UImage;
class USizeBox;
class UBorder;

UENUM(BlueprintType)
enum class EFolderState : uint8
{
	UnCheckUnSelected = 0,
	UnCheckSelected,
	CheckSelected,
	CheckUnSelected
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FFolderItemSelectDelegate, UWidget_FolderItem* , FolderItem, bool, IsFolderExpand);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFolderItemUnFoldDelegate, UWidget_FolderItem*, FolderItem, bool, IsSubOpen);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFolderRightMenuActionDelegate, UWidget_FolderItem*, ActionFolder, const int32&, ActionType);

UCLASS()
class DESIGNSTATION_API UWidget_FolderItem : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()
	
public:
	//virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;
	//virtual void RightClickMenuEdit(const EFolderAndFileRightMenu::Type& ActionType) override;

	void FolderWidgetEdit(const FString& FolderId, const ERightMenuType& ActionType, bool IsFolder);

	void ClearAllSelectState();
	
	void SetItemData(const FFolderTableData& InData);
	void SyncSubItemData(const FFolderTableData& InData);

	FORCEINLINE FFolderTableData& GetItemData() { return ItemData; }
	FORCEINLINE TMap<FString, UWidget_FolderItem*>& GetChildFolders() { return ChildFolders; }
	FORCEINLINE TMap<FString, UWidget_FileItem*>& GetChildFilesMap() { return ChildFilesMap; }
	FORCEINLINE TArray<UWidget_FileItem*>& GetChildFiles() { return ChildFiles; }
	FORCEINLINE int32 GetCurrentFolderLevel() const { return FolderLevel; }
	FORCEINLINE void SetFolderLevel(int32 InLevel) { FolderLevel = InLevel; SetIndent(FolderLevel); }

	TArray<FFolderTableData>& GetSubFolderDatas(); //{ return SubFolderDatas; }
	TArray<FFolderTableData> GetSubDatas(); //{ return SubFolderDatas; }
	//TArray<FFolderTableData> GetSubDatasArr() { return SubFolderDatas; }

	/*
	*  @@ get max order
	*  @@ if empty return INDEX_NONE
	*/
	int32 GetSubLastOrder();

	FORCEINLINE FString& GetFolderName() { return ItemData.folder_name; }

	void UpdateFolderName(const FString& FolderName);
	void AddFileToFolder(UWidget_FolderItem* InWidget);
	void AddFileToFolder(UWidget_FileItem* InWidget);
	void AddFileToFolder(UFolderAndFileBaseWidget* InWidget);
	void AddDataToFolder(const FFolderTableData& InData);
	UFolderAndFileBaseWidget* ConstructNewSubWidget(const FFolderTableData& InData);
	void RefreshScbContentWidget();
	void RefreshContentWidget();

	void UpdateFolderState(bool _IsVisible) override;
	void SyncFolderState(bool _IsVisible);
	void SyncSubState(bool _InSubSuperiorVisible);

	//bool GetIsSelected() const { return IsSelected; }
	virtual void SetIsSelected(bool _IsSelected) override;
	virtual void SelectSelf() override;
	void SetChildFolderExpand(bool IsExpand);
	void SetSearchFolderExpand(const TArray<FString>& InSubArray);

	FString GetSubFolderSelectID();
	FFolderTableData GetSubFolderSelectData();

	void SetObscureSearchExpand(
		const TArray<FRefDirectoryData>& CurSubDatas, 
		TArray<FRefDirectoryDataContent>& ContentDatas,
		TArray<FString>& Paths);
	void SetObscureSearchExpand_Inner(
		TArray<FRefDirectoryDataContent>& ContentDatas,
		TArray<FString>& Paths);

	void OpenFolderExpand(const TArray<FString>& InSubArray);
	void GrayFolderByAlreadyExpand(TArray<FString> InSubArray, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair);
	void GrayFolderInnerAction(const FString& ActionID, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair);
	void GrayFolderInnerAction(const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair);

	void ConstructSubFolder(const TArray<FFolderTableData>& SubFolderDatas, const TArray<FFolderTableData>& SubFileDatas);
	void ConstructSubFolder(const TArray<FFolderTableData>& SubDatas);
	void ConstructSubFolder(bool NeedSyncSelect = false);     
	void ReSelectSubFolderOrFile(const FString& FolderOrFileId, bool IsFolder);
	void ReSelectAfterSwap(const TArray<FFolderTableData>& SwapDatas);
	void RemoveFromParentFolder();
	void RemoveSubFolder(const FFolderTableData& InData);
	bool IsCkbExpandCheck();

	static UWidget_FolderItem* Create();

	void SetFolderToGrey(bool InState);

	virtual void FolderFileCopyFinishAction(const FString& ActionReturnID) override;
	virtual void CopyDirectActionByGameThread(const FString& ActionID) override;
	virtual void SetFolderOrder(const int32& InOrder) override;
	
private:
	void SearchSubFoldersRequest();
	void SortAndSpliteFolderAndFile(TArray<FFolderTableData>& Datas);
	void GenerateSubFoldersInner(TArray<FRefDirectoryData> SubDatas);

private:
	int32 FolderLevel;

	UPROPERTY()
		TArray<FString> SubPaths;
	UPROPERTY()
		TArray<FString> FolderPaths;

protected:
	virtual void OnSearchActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas) override;

private:
	//void GetFolderImageBrush();
	void SetFolderStateBrush(const EFolderState& InType);
	UFUNCTION()
		void OnRightMenuClickHandler(const ERightMenuType& ActionType);
	UFUNCTION()
		void OnTipPopConfirmHandler();

	void ChildExpandInner(bool IsCheck);
	void CheckBoxLogicInner(bool IsCheck, bool IsSync);

	int32 GetLastFolderIndexInSCB();

protected:
	virtual void OnDeleteActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;
	virtual void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData) override;

public:
	FFolderItemSelectDelegate FolderSelectDelegate;
	FFolderItemUnFoldDelegate FolderItemUnFoldDelegate;
	FFolderRightMenuActionDelegate FolderRightMenuActionDelegate;
	TWeakObjectPtr<UWidget_FolderItem> ParentFolderWidget;

private:
	static FString WidgetFolderPath;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnStateChangedCkbExpand(bool IsCheck);
	UFUNCTION()
		void OnClickedBtnSelect();
	UFUNCTION()
		void OnHoveredBtnSelect();
	UFUNCTION()
		void OnUnHoveredBtnSelect();
	UFUNCTION()
	void OnStateChangedSelectCheck(bool IsCheck);

private:
	UPROPERTY()
		FFolderTableData ItemData;
	UPROPERTY()
		FFolderTableData CopyOrCutData;
	/*UPROPERTY()
		TArray<UWidget_FolderItem*> ChildFolders;*/
	/*UPROPERTY()
		TMap<FString, UWidget_FolderItem*> ChildFolders;*/
	UPROPERTY()
		TMap<FString, UWidget_FolderItem*> ChildFolders;
	/*UPROPERTY()
		TMap<FString, UWidget_FileItem*> ChildFilesMap;*/
	UPROPERTY()
		TMap<FString, UWidget_FileItem*> ChildFilesMap;
	UPROPERTY()
		TArray<UWidget_FileItem*> ChildFiles;
	UPROPERTY()
		TArray<FFolderTableData> SubFolderDatas;

	static int LayoutItemType;
private:
	UPROPERTY()
		UCheckBox*     CkbExpandChild;
	UPROPERTY()
		UScrollBox*    ScbChild;
	UPROPERTY()
		UImage*        ImgFolder;

		UPROPERTY(meta = (BindWidget))
		UCheckBox* Ckb_Select;
};
