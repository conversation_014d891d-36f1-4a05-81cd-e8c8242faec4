// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontBaseWidget.h"

#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

bool UFrontBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	//BindDelegate();

	return true;
}

void UFrontBaseWidget::SendSearchFrontDirectoryRequest(const int32& PID, const FString& DictGroup, const FString& DictValue, const int32& Level /*= INDEX_NONE*/)
{
	NetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendFrontDirectorySearchRequest(DictGroup, DictValue, PID, Level);
}

void UFrontBaseWidget::NativeOnInitialized()
{
	BindDelegate();
}

void UFrontBaseWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->FrontDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UFrontBaseWidget::OnSearchFrontDirectoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->FrontDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &UFrontBaseWidget::OnUpdateFrontDirectoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataResponseDelegate.AddUniqueDynamic(this, &UFrontBaseWidget::OnSearchCategoryDetailDataResponseHandler);
}

void UFrontBaseWidget::OnSearchFrontDirectoryResponseHandler(const FString& UUID, bool bSuccess, const TArray<FFrontDirectoryData>& DirData)
{
	if(NetUUID.SearchUUID.Equals(UUID))
	{
		NetUUID.ResetSearchAction();
		if(bSuccess)
		{
			AddToCache(DirData);
			GenerateDirectory(DirData);
		}
	}
}

void UFrontBaseWidget::OnUpdateFrontDirectoryResponseHandler(const FString& UUID, bool bSuccess, const TArray<FFrontDirectoryData>& DirData)
{
	if(NetUUID.UpdateUUID.Equals(UUID))
	{
		NetUUID.ResetUpdateAction();
		if(bSuccess)
		{
			UpdateDirectory(DirData);
		}
	}
}

void UFrontBaseWidget::OnSearchCategoryDetailDataResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FModelMatItemResp& DirData)
{
	if(NetUUID.CategoryDetailUUID.Equals(UUID))
	{
		NetUUID.ResetCategoryDetailAction();
		if(bSuccess)
		{
			//GenerateCategoryDetailData(DirData.content);
			GenerateCategoryDetailData(DirData.content);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}
