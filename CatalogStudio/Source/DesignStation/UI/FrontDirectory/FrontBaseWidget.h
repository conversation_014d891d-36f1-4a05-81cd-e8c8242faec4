// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FrontBaseWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UFrontBaseWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void SendSearchFrontDirectoryRequest(const int32& PID, const FString& DictGroup, const FString& DictValue, const int32& Level = INDEX_NONE);

	/*
	 * @@ virtual function
	 * @@ inherit class should override those function to handle the response data to generate the ui to show 
	 */
	virtual void GenerateDirectory(const TArray<FFrontDirectoryData>& DirData){}
	virtual void UpdateDirectory(const TArray<FFrontDirectoryData>& DirData){}
	virtual void GenerateCategoryDetailData(const TArray<FModelMatItemData>& DirData){}
	virtual void GenerateCategoryDetailData(const FModelMatItemContent& Datas){}

	virtual void AddToCache(const TArray<FFrontDirectoryData>& Datas) {}

protected:
	virtual void NativeOnInitialized() override;

public:
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	void ClearSubWidget();

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	void AddSubWidget(UUserWidget* SubWidget);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	bool IsSubWidgetHasItem();

protected:
	UPROPERTY()
	FFrontDirectoryNetUUID NetUUID;

	UPROPERTY()
	FBackendDirectoryNetUUID BackNetUUID;

protected:
	void BindDelegate();

	UFUNCTION()
	void OnSearchFrontDirectoryResponseHandler(const FString& UUID, bool bSuccess, const TArray<FFrontDirectoryData>& DirData);

	UFUNCTION()
	void OnUpdateFrontDirectoryResponseHandler(const FString& UUID, bool bSuccess, const TArray<FFrontDirectoryData>& DirData);

	UFUNCTION()
	void OnSearchCategoryDetailDataResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FModelMatItemResp& DirData);

};
