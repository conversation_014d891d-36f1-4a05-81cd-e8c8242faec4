// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontDirectoryContainWidget.h"
#include "FrontDirectoryWidget.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

FString UFrontDirectoryContainWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/FrontDiretory/BP_FrontDirectoryContain.BP_FrontDirectoryContain_C'");


bool UFrontDirectoryContainWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	DataType = EFrontDirectoryDataType::E_None;

	return true;
}

void UFrontDirectoryContainWidget::Init(const EFrontDirectoryDataType& InDataType)
{
	DataType = InDataType;
}

#define CATEGORY_CATALOG_DICT_GROUP					TEXT("QWDZ")
#define CATEGORY_CATALOG_DICT_VALUE					TEXT("QWDZMX")
#define CATEGORY_FURNITURE_DICT_GROUP				TEXT("CPL")
#define CATEGORY_FURNITURE_DICT_VALUE_MODEL			TEXT("SCFLMX")
#define CATEGORY_FURNITURE_DICT_VALUE_MATERIAL		TEXT("SCFLCZ")

void UFrontDirectoryContainWidget::InitRootDirectory(const EFrontDirectoryDataType& InDataType)
{
	DataType = InDataType;

	FString DictGroup;
	FString DictValue;

	switch (InDataType)
	{
	case EFrontDirectoryDataType::E_Catalog:
	{
		DictGroup = CATEGORY_CATALOG_DICT_GROUP;
		DictValue = CATEGORY_CATALOG_DICT_VALUE;
		break;
	}
	case EFrontDirectoryDataType::E_Furniture:
	{
		DictGroup = CATEGORY_FURNITURE_DICT_GROUP;
		DictValue = CATEGORY_FURNITURE_DICT_VALUE_MODEL;
		break;
	}
	case EFrontDirectoryDataType::E_Material:
	{
		DictGroup = CATEGORY_FURNITURE_DICT_GROUP;
		DictValue = CATEGORY_FURNITURE_DICT_VALUE_MATERIAL;
		break;
	}
	case EFrontDirectoryDataType::E_Mapping:
	{
		DictGroup = CATEGORY_CATALOG_DICT_GROUP;
		DictValue = CATEGORY_CATALOG_DICT_VALUE;
		break;
	}
	default: checkNoEntry(); break;
	}

	SendSearchFrontDirectoryRequest(0, DictGroup, DictValue);

}

#undef CATEGORY_CATALOG_DICT_GROUP					
#undef CATEGORY_CATALOG_DICT_VALUE					
#undef CATEGORY_FURNITURE_DICT_GROUP				
#undef CATEGORY_FURNITURE_DICT_VALUE_MODEL			
#undef CATEGORY_FURNITURE_DICT_VALUE_MATERIAL		

void UFrontDirectoryContainWidget::GenerateDirectory(const TArray<FFrontDirectoryData>& DirData)
{
	ClearSubWidget();
	for (const auto DD : DirData)
	{
		UFrontDirectoryInnerWidget* NewWidget = UFrontDirectoryInnerWidget::Create();
		NewWidget->UpdateProperty(DD, DataType);
		AddSubWidget(NewWidget);
	}
}

void UFrontDirectoryContainWidget::UpdateDirectory(const TArray<FFrontDirectoryData>& DirData)
{

}

void UFrontDirectoryContainWidget::AddToCache(const TArray<FFrontDirectoryData>& Datas)
{
	UFrontDirectoryWidget::GetInstance()->AddDirectoryData(Datas);
}

void UFrontDirectoryContainWidget::MappingAction()
{
	UFrontDirectoryWidget::GetInstance()->MappingActionConfirm();
}

UFrontDirectoryContainWidget* UFrontDirectoryContainWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UFrontDirectoryContainWidget>(UFrontDirectoryContainWidget::FilePath);

}
