// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontBaseWidget.h"
#include "FrontDirectoryInnerWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FrontDirectoryContainWidget.generated.h"

/**
 * 
 */



UCLASS(Blueprintable)
class DESIGNSTATION_API UFrontDirectoryContainWidget : public UFrontBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void Init(const EFrontDirectoryDataType& InDataType);

	/*
	*  @@ Init Root Directory
	*/
	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryContain")
	void InitRootDirectory(const EFrontDirectoryDataType& InDataType);

	virtual void GenerateDirectory(const TArray<FFrontDirectoryData>& DirData) override;
	virtual void UpdateDirectory(const TArray<FFrontDirectoryData>& DirData) override;

	virtual void AddToCache(const TArray<FFrontDirectoryData>& Datas) override;

	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryContain")
	void MappingAction();

	static UFrontDirectoryContainWidget* Create();

public:
	UPROPERTY(BlueprintReadOnly, Category = "FrontDirectoryContain")
	EFrontDirectoryDataType DataType;

private:
	UPROPERTY()
	TArray<UFrontDirectoryInnerWidget*> InnerWidgets;

private:
	static FString FilePath;
};
