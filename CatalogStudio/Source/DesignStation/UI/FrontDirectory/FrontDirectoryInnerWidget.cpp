// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontDirectoryInnerWidget.h"
#include "FrontDirectoryWidget.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

FString UFrontDirectoryInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/FrontDiretory/BP_FrontDirectoryInner.BP_FrontDirectoryInner_C'");


bool UFrontDirectoryInnerWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}
	return true;
}

void UFrontDirectoryInnerWidget::UpdateProperty(const FFrontDirectoryData& InData, const EFrontDirectoryDataType& InType)
{
	DirectoryData.FrontDirectoryData = InData;
	DirectoryData.DataType = InType;
	UpdateShow(InData);
}

void UFrontDirectoryInnerWidget::SelectAction()
{
	bSelect = true;
	SelectStyle();

	UFrontDirectoryWidget::GetInstance()->NewSelectWidget(this);

	if ((DirectoryData.DataType == EFrontDirectoryDataType::E_Furniture || DirectoryData.DataType == EFrontDirectoryDataType::E_Material)
		&& DirectoryData.FrontDirectoryData.IsLastDirectory())
	{
		return;
	}

	SendSearchFrontDirectoryRequest(
		DirectoryData.FrontDirectoryData.id,
		DirectoryData.FrontDirectoryData.dictGroupValue, 
		DirectoryData.FrontDirectoryData.dictValue
	);
}

void UFrontDirectoryInnerWidget::UnSelectAction()
{
	bSelect = false;
	UnSelectStyle();
}

void UFrontDirectoryInnerWidget::HoverAction()
{
	if(!bSelect)
	{
		HoverStyle();
	}
}

void UFrontDirectoryInnerWidget::UnHoverAction()
{
	if(!bSelect)
	{
		UnHoverStyle();
	}
}

void UFrontDirectoryInnerWidget::GenerateDirectory(const TArray<FFrontDirectoryData>& DirData)
{
	ClearSubWidget();
	for(const auto DD : DirData)
	{
		UFrontDirectoryInnerWidget* InnerWidget = UFrontDirectoryInnerWidget::Create();
		InnerWidget->UpdateProperty(DD, DirectoryData.DataType);
		InnerWidget->SetExtendWidth(this->GetExtendWidth());
		AddSubWidget(InnerWidget);
	}
}

void UFrontDirectoryInnerWidget::AddToCache(const TArray<FFrontDirectoryData>& Datas)
{
	UFrontDirectoryWidget::GetInstance()->AddDirectoryData(Datas);
}

UFrontDirectoryInnerWidget* UFrontDirectoryInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UFrontDirectoryInnerWidget>(UFrontDirectoryInnerWidget::FilePath);
}
