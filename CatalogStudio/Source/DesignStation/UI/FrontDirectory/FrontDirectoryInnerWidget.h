// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontBaseWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FrontDirectoryInnerWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UFrontDirectoryInnerWidget : public UFrontBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void UpdateProperty(const FFrontDirectoryData& InData, const EFrontDirectoryDataType& InType);

	FFrontDirectoryConbine GetData() const { return DirectoryData; }
	EFrontDirectoryDataType GetDataType() const { return DirectoryData.DataType; }

	virtual void GenerateDirectory(const TArray<FFrontDirectoryData>& DirData) override;

	virtual void AddToCache(const TArray<FFrontDirectoryData>& Datas) override;

	static UFrontDirectoryInnerWidget* Create();

	//BP Logic
public:
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Style")
	void UpdateShow(const FFrontDirectoryData& InData);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Style")
	void SelectStyle();
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Style")
	void UnSelectStyle();
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Style")
	void HoverStyle();
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Style")
	void UnHoverStyle();


	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryInner | Action")
	void SelectAction();
	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryInner | Action")
	void UnSelectAction();
	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryInner | Action")
	void HoverAction();
	UFUNCTION(BlueprintCallable, Category = "FrontDirectoryInner | Action")
	void UnHoverAction();

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Action")
	float GetExtendWidth();
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectoryInner | Action")
	void SetExtendWidth(float InParentWidth);

public:
	UPROPERTY(BlueprintReadWrite, Category = "FrontDirectoryInner")
	bool bSelect;

public:


private:
	UPROPERTY()
	FFrontDirectoryConbine  DirectoryData;

	static FString FilePath;
};
