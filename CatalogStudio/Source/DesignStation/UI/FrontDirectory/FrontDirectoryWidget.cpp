// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontDirectoryWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UFrontDirectoryWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/FrontDiretory/BP_FrontDirectory.BP_FrontDirectory_C'");
UFrontDirectoryWidget* UFrontDirectoryWidget::Instance = nullptr;


bool UFrontDirectoryWidget::Initialize()
{
	return Super::Initialize();
}

void UFrontDirectoryWidget::Init()
{
	InitDirectoryWidget();
	InitUI();
}

void UFrontDirectoryWidget::GenerateDirectory(const TArray<FFrontDirectoryData>& DirData)
{
	
}

void UFrontDirectoryWidget::UpdateDirectory(const TArray<FFrontDirectoryData>& DirData)
{
	
}

void UFrontDirectoryWidget::AddToCache(const TArray<FFrontDirectoryData>& Datas)
{
	AddDirectoryData(Datas);
}

void UFrontDirectoryWidget::NewSelectWidget(UFrontDirectoryInnerWidget* NewWidget)
{
	if(IS_OBJECT_PTR_VALID(SelectWidget) && SelectWidget != NewWidget)
	{
		SelectWidget->UnSelectAction();
	}
	SelectWidget = NewWidget;

	if(NewWidget->GetDataType() == EFrontDirectoryDataType::E_Furniture 
		|| NewWidget->GetDataType() == EFrontDirectoryDataType::E_Material)
	{
		{
			FrontShowDelegate.ExecuteIfBound(NewWidget->GetData().FrontDirectoryData.id, 0);
		}
	}
	else if(NewWidget->GetDataType() == EFrontDirectoryDataType::E_Catalog)
	{
		FrontShowDelegate.ExecuteIfBound(NewWidget->GetData().FrontDirectoryData.id, 1);
	}
	else if(NewWidget->GetDataType() == EFrontDirectoryDataType::E_Mapping)
	{
		
	}
	else
	{
		checkNoEntry();
	}
}


void UFrontDirectoryWidget::SwitchShowType(const EFrontDirectoryDataType& Type)
{
	UUserWidget* ToActive = nullptr;
	if(Type == EFrontDirectoryDataType::E_Catalog)
	{
		ToActive = CatalogWidget;
		CatalogWidget->InitRootDirectory(EFrontDirectoryDataType::E_Catalog);
	}
	else if (Type == EFrontDirectoryDataType::E_Furniture)
	{
		ToActive = FurnitureWidget;
		FurnitureWidget->InitRootDirectory(EFrontDirectoryDataType::E_Furniture);
	}
	else if (Type == EFrontDirectoryDataType::E_Material)
	{
		ToActive = MaterialWidget;
		MaterialWidget->InitRootDirectory(EFrontDirectoryDataType::E_Material);
	}
	else if (Type == EFrontDirectoryDataType::E_Mapping)
	{
		ToActive = MappingWidget;
		MappingWidget->InitRootDirectory(EFrontDirectoryDataType::E_Mapping);
	}

	if(ToActive)
	{
		SwitchShowTypeUI(ToActive);
	}
	else
	{
		checkf(false, TEXT(" switch directory show type error! "));
	}
}

void UFrontDirectoryWidget::MappingActionConfirm()
{
	
	FString MappingPath = TEXT("");
	if(IS_OBJECT_PTR_VALID(SelectWidget))
	{
		if (SelectWidget->IsSubWidgetHasItem())
		{
			UI_POP_WINDOW_ERROR_ST(TEXT("need ref to leaf folder"));
		}
		else
		{
			MappingPath = FString::FromInt(SelectWidget->GetData().FrontDirectoryData.id);
		}
	}

	if (!MappingPath.IsEmpty())
	{
		FrontMappingDelegate.ExecuteIfBound(MappingPath);
		RemappingDelegate.ExecuteIfBound(MappingPath);
	}
}

UFrontDirectoryWidget* UFrontDirectoryWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UFrontDirectoryWidget>(UFrontDirectoryWidget::FilePath);
}

UFrontDirectoryWidget* UFrontDirectoryWidget::GetInstance()
{
	if (UFrontDirectoryWidget::Instance == nullptr)
	{
		UFrontDirectoryWidget::Instance = UFrontDirectoryWidget::Create();
		UFrontDirectoryWidget::Instance->Init();
		//TODO : should RemoveFromRoot when delete destructor
		UFrontDirectoryWidget::Instance->AddToRoot();
	}
	return UFrontDirectoryWidget::Instance;
}

void UFrontDirectoryWidget::AddDirectoryData(const FFrontDirectoryData& InData)
{
	if (CacheDirectoryData.Contains(InData.id))
	{
		CacheDirectoryData[InData.id] = InData;
	}
	else
	{
		CacheDirectoryData.Add(InData.id, InData);
	}
}

void UFrontDirectoryWidget::AddDirectoryData(const TArray<FFrontDirectoryData>& InData)
{
	for (const auto ID : InData)
	{
		AddDirectoryData(ID);
	}
}

bool UFrontDirectoryWidget::GetDirectoryData(const int64& InID, FFrontDirectoryData& OutData)
{
	if (CacheDirectoryData.Contains(InID))
	{
		OutData = CacheDirectoryData[InID];
		return true;
	}
	return false;
}

FString UFrontDirectoryWidget::GetMappingFullDirectoryPath(const int64& InID)
{
	FString OutPath = TEXT("");
	FFrontDirectoryData Data;
	if (GetDirectoryData(InID, Data))
	{
		OutPath = Data.name;
		int64 PID = Data.pid;
		while (PID > 0)
		{
			if (GetDirectoryData(PID, Data))
			{
				PID = Data.pid;
				OutPath = FPaths::Combine(Data.name, OutPath);
				UE_LOG(LogTemp, Warning, TEXT("GetMappingFullDirectoryPath -- OutPath : %s"), *OutPath);
			}
			else
			{
				break;
			}
		}
	}
	return OutPath;
}

void UFrontDirectoryWidget::InitDirectoryWidget()
{
	if (!IS_OBJECT_PTR_VALID(CatalogWidget))
	{
		CatalogWidget = UFrontDirectoryContainWidget::Create();
		CatalogWidget->Init(EFrontDirectoryDataType::E_Catalog);
		//CatalogWidget->InitRootDirectory(EFrontDirectoryDataType::E_Catalog);
	}

	if(!IS_OBJECT_PTR_VALID(FurnitureWidget))
	{
		FurnitureWidget = UFrontDirectoryContainWidget::Create();
		FurnitureWidget->Init(EFrontDirectoryDataType::E_Furniture);
		//FurnitureWidget->InitRootDirectory(EFrontDirectoryDataType::E_Furniture);
	}

	if (!IS_OBJECT_PTR_VALID(MaterialWidget))
	{
		MaterialWidget = UFrontDirectoryContainWidget::Create();
		MaterialWidget->Init(EFrontDirectoryDataType::E_Material);
		//MaterialWidget->InitRootDirectory(EFrontDirectoryDataType::E_Material);
	}

	if (!IS_OBJECT_PTR_VALID(MappingWidget))
	{
		MappingWidget = UFrontDirectoryContainWidget::Create();
		MappingWidget->Init(EFrontDirectoryDataType::E_Mapping);
		//MappingWidget->InitRootDirectory(EFrontDirectoryDataType::E_Mapping);
	}
}
