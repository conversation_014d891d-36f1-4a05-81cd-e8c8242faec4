// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontBaseWidget.h"
#include "FrontDirectoryContainWidget.h"
#include "FrontDirectoryInnerWidget.h"
#include "FrontDirectoryWidget.generated.h"

struct FFrontDirectoryConbine;

UENUM(BlueprintType)
enum class FFrontDirectoryState : uint8
{
	E_Mapping = 0, //mapping action show
	E_Shown
};


DECLARE_DELEGATE_OneParam(FFrontMappingDelegate, FString);

/*
*  @@ param1, the id of the directory id
*  @@ param2, the type of the directory ( Catalog , Furniture ... )
 */
DECLARE_DELEGATE_TwoParams(FFrontShowDelegate, int32, int32);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UFrontDirectoryWidget : public UFrontBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	/*
	 *  @@ ��ʼ��UI����
	 */
	void Init();
	/*
	 *  @@ ����BPʹ�ã���ʼ��UI����
	 */
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	void InitUI();

	virtual void GenerateDirectory(const TArray<FFrontDirectoryData>& DirData) override;
	virtual void UpdateDirectory(const TArray<FFrontDirectoryData>& DirData) override;

	virtual void AddToCache(const TArray<FFrontDirectoryData>& Datas) override;

	void NewSelectWidget(UFrontDirectoryInnerWidget* NewWidget);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	void SwitchShowTypeUI(UUserWidget* ActiveWidget);
	void SwitchShowType(const EFrontDirectoryDataType& Type);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontDirectory")
	void MappingUIShow(bool ToShow);
	void MappingActionConfirm();

	/*
	 *  @@ singleton 
	 */
	static UFrontDirectoryWidget* Create();
	static UFrontDirectoryWidget* GetInstance();

public:
	TMap<int64, FFrontDirectoryData> GetCacheDirectoryData() { return CacheDirectoryData; }
	TMap<int64, FFrontDirectoryData>& GetCacheDirectoryDataRef() { return CacheDirectoryData; }
	void AddDirectoryData(const FFrontDirectoryData& InData);
	void AddDirectoryData(const TArray<FFrontDirectoryData>& InData);
	bool GetDirectoryData(const int64& InID, FFrontDirectoryData& OutData);
	FString GetMappingFullDirectoryPath(const int64& InID);

	TMap<int64, FFrontDirectoryDataArr> GetCacheSubDirectoryData() { return CacheSubDirectoryData; }
	TMap<int64, FFrontDirectoryDataArr>& GetCacheSubDirectoryDataRef() { return CacheSubDirectoryData; }

private:
	void InitDirectoryWidget();

private:
	//@@ catalog directory widget
	UPROPERTY(BlueprintReadWrite, meta=(AllowPrivateAccess =true))
	UFrontDirectoryContainWidget* CatalogWidget;

	//@@ furniture directory widget
	UPROPERTY(BlueprintReadWrite, meta =(AllowPrivateAccess=true))
	UFrontDirectoryContainWidget* FurnitureWidget;

	//@@ material directory widget
	UPROPERTY(BlueprintReadWrite, meta = (AllowPrivateAccess = true))
	UFrontDirectoryContainWidget* MaterialWidget;

	//@@ mapping action directory widget
	UPROPERTY(BlueprintReadWrite, meta = (AllowPrivateAccess = true))
	UFrontDirectoryContainWidget* MappingWidget;

public:
	FFrontMappingDelegate FrontMappingDelegate;
	FFrontShowDelegate FrontShowDelegate;
	FFrontMappingDelegate RemappingDelegate;

private:
	UPROPERTY()
	UFrontDirectoryInnerWidget* SelectWidget;

	/*
	 *  @@ cache front directory data
	 *	@@ pair : key - id, value - data
	 */
	UPROPERTY()
	TMap<int64, FFrontDirectoryData> CacheDirectoryData;

	/*
	 *  @@ cache front subdirectory data
	 *	@@ pair : key - parent id, value - data array
	 */
	TMap<int64, FFrontDirectoryDataArr> CacheSubDirectoryData;

private:
	static FString FilePath;
	static UFrontDirectoryWidget* Instance;
};
