// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontShowInnerWidget.h"
#include "Components/MultiLineEditableTextBox.h"
#include "FrontDirectoryWidget.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UFrontShowInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/FrontDiretory/BP_FrontShowInner.BP_FrontShowInner_C'");


bool UFrontShowInnerWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	DataType = EFrontDirectoryDataType::E_None;

	//BindDelegate_Other();

	return true;
}

void UFrontShowInnerWidget::Init(const FModelMatItemData& InData, const EFrontDirectoryDataType& InType)
{
	Data = InData;
	DataType = InType;
	MTB_ID->SetIsReadOnly(false);
	MTB_Code->SetIsReadOnly(false);
	UpdateUIShow(InData);
}

void UFrontShowInnerWidget::Init(const FRefDirectoryData& InData, const EFrontDirectoryDataType& InType)
{
	RefData = InData;
	DataType = InType;
	MTB_ID->SetIsReadOnly(true);
	MTB_Code->SetIsReadOnly(true);
	UpdateUIShow_Cabinet(InData);
}

UFrontShowInnerWidget* UFrontShowInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UFrontShowInnerWidget>(UFrontShowInnerWidget::FilePath);
}

void UFrontShowInnerWidget::NativeOnInitialized()
{
	BindDelegate_Other();
}

void UFrontShowInnerWidget::LoadImage(const FString& InImagePath)
{
	if (InImagePath.IsEmpty())
		return;

	const FString AbsImagePath = FPaths::ConvertRelativePathToFull(
		FPaths::Combine(FPaths::ProjectContentDir(), InImagePath)
	);
	if (FPaths::FileExists(AbsImagePath))
	{
		UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsImagePath);
		UpdateImage_Cabinet(FileImage);
	}
	else
	{
		SendDowmloadImageRequest(InImagePath);
	}
}

void UFrontShowInnerWidget::OnDeleteAction()
{
	MappingDelegate.ExecuteIfBound(RefData, TEXT(""), 0);

	this->SetVisibility(ESlateVisibility::Collapsed);
	this->RemoveFromParent();
}

void UFrontShowInnerWidget::BindReMappingDelegate()
{
	UFrontDirectoryWidget::GetInstance()->RemappingDelegate.BindUFunction(this, FName(TEXT("OnReMappingAction")));
}

void UFrontShowInnerWidget::UnBindReMappingDelegate()
{
	UFrontDirectoryWidget::GetInstance()->RemappingDelegate.Unbind();
}

void UFrontShowInnerWidget::OnReMappingAction(const FString& NewMapping)
{
	OnReMappingUIShow(false);

	if (!NewMapping.Equals(RefData.fontFolderPath))
	{
		MappingDelegate.ExecuteIfBound(RefData, NewMapping, 1);

		this->SetVisibility(ESlateVisibility::Collapsed);
		this->RemoveFromParent();
	}
	UnBindReMappingDelegate();
}

void UFrontShowInnerWidget::OnOpenToViewAction()
{
	SendParseDetailInfoRequest();
}

void UFrontShowInnerWidget::UpdateFolderID_BP(const FString& InFolderID)
{

	switch (DataType)
	{
	case EFrontDirectoryDataType::E_Catalog:
	{
		if (!InFolderID.Equals(RefData.folderId))
		{
			SendUpdateFolderIDRequest(InFolderID);
		}
	}
		break;
	case EFrontDirectoryDataType::E_Furniture:
	case EFrontDirectoryDataType::E_Material:
	{
		if (!InFolderID.Equals(Data.folderId))
		{
			SendUpdateFolderIDRequest(InFolderID);
		}
	}
		break;
	case EFrontDirectoryDataType::E_None:
	case EFrontDirectoryDataType::E_Mapping:
	default:
		break;
	}


	/*if (!InFolderID.IsEmpty())
	{
		if (!InFolderID.Equals(Data.folderId))
		{
			SendUpdateFolderIDRequest(InFolderID);
		}
	}
	else
	{
		UpdateFolderIDOnly(Data.folderId);
	}*/
}

void UFrontShowInnerWidget::UpdateFolderCode_BP(const FString& InFolderCode)
{
	switch (DataType)
	{
	case EFrontDirectoryDataType::E_Catalog:
	{
		if (!InFolderCode.Equals(RefData.folderCode))
		{
			SendUpdateFolderCodeRequest(InFolderCode);
		}
	}
		break;
	case EFrontDirectoryDataType::E_Furniture:
	case EFrontDirectoryDataType::E_Material:
	{
		if (!InFolderCode.Equals(Data.folderCode))
		{
			SendUpdateFolderCodeRequest(InFolderCode);
		}
	}
		break;
	case EFrontDirectoryDataType::E_None:
	case EFrontDirectoryDataType::E_Mapping:
	default:
		break;
	}
	
}

void UFrontShowInnerWidget::BindDelegate_Other()
{
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.AddUniqueDynamic(this, &UFrontShowInnerWidget::OnGetFurnitureOrMatDataForParseResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->UpdateFolderIdForWebResponseDelegate.AddUniqueDynamic(this, &UFrontShowInnerWidget::OnUpdateFolderIDResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UFrontShowInnerWidget::OnDownloadImageHandler);
}

void UFrontShowInnerWidget::SendParseDetailInfoRequest()
{
	NetUUID.CatagoryDetailFileToParseUUID = UCatalogNetworkSubsystem::GetInstance()->SendFurnitureOrMaterialDataForParseSearchRequest(Data.id);
}

void UFrontShowInnerWidget::OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData)
{
	if(NetUUID.CatagoryDetailFileToParseUUID.Equals(UUID))
	{
		NetUUID.ResetCatagoryDetailFileToParseAction();
		if(bSuccess)
		{
			//ACatalogPlayerController::Get()->OpenViewModule(DirData);
			OpenToViewDelegate.ExecuteIfBound(DirData);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFrontShowInnerWidget::SendUpdateFolderIDRequest(const FString& InFolderID)
{
	NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendUpdateFolderIdCodeForWebRequest(InFolderID, TEXT(""), Data.id);
}

void UFrontShowInnerWidget::SendUpdateFolderCodeRequest(const FString& InFolderCode)
{
	NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendUpdateFolderIdCodeForWebRequest(TEXT(""), InFolderCode, Data.id);
}

void UFrontShowInnerWidget::OnUpdateFolderIDResponseHandler(const FString& UUID, bool bSuccess, const FString& TipMsg, const FModelMatItemData& NewData)
{
	if (NetUUID.UpdateUUID.Equals(UUID))
	{
		NetUUID.ResetUpdateAction();
		if (!bSuccess)
		{//reset old folder id to show
			UI_POP_WINDOW_ERROR(TipMsg);
		}
		else
		{
			Data = NewData;
		}
		UpdateFolderIDOnly(Data.folderId);
		UpdateFolderCodeOnly(Data.folderCode);
	}
}

void UFrontShowInnerWidget::SendDowmloadImageRequest(const FString& ImgPath)
{
	NetUUID.DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(ImgPath);
}

void UFrontShowInnerWidget::OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath)
{
	if (NetUUID.DownloadUUID.Equals(UUID, ESearchCase::IgnoreCase))
	{
		if (bSuccess && FilePath.IsValidIndex(0))
		{
			LoadImage(FilePath[0]);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file image error [%s]"), *RefData.thumbnailPath);
		}
	}
}
