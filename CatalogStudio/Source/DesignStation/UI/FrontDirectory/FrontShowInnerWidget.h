// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontBaseWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FrontShowInnerWidget.generated.h"

/**
 * 
 */
class UMultiLineEditableTextBox;

DECLARE_DELEGATE_OneParam(FOpenToViewDelegate, const FCSModelMatData&);
DECLARE_DELEGATE_ThreeParams(FMappingDelegate, FRefDirectoryData, FString, int32);

UCLASS()
class DESIGNSTATION_API UFrontShowInnerWidget : public UFrontBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void Init(const FModelMatItemData& InData, const EFrontDirectoryDataType& InType);
	void Init(const FRefDirectoryData& InData, const EFrontDirectoryDataType& InType);

	static UFrontShowInnerWidget* Create();

protected:
	virtual void NativeOnInitialized() override;

	//BP Function
public:

	/*
	*  @@ BP Logic to update ui data show 
	*/
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateUIShow(const FModelMatItemData& InData);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateUIShow_Cabinet(const FRefDirectoryData& InData);

	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void LoadImage(const FString& InImagePath);
	/*
	*  @@ BP Logic to update image show
	*  @@ for custom file
	*/
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateImage_Cabinet(UTexture2D* FileImage);

	/*
	*  @@ BP Logic to update input widget state ( folder_id input widget)
	*/
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateInputState();

	/*
	*  @@ BP Logic to update folder id only
	* 
	*/
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateFolderIDOnly(const FString& InFolderID);

	/*
	 *  @@ Only for catalog data
	 *	@@ delete the mapping data	
	 */
	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void OnDeleteAction();


	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void BindReMappingDelegate();
	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void UnBindReMappingDelegate();
	/*
	 *  @@ remapping the mapping data to move file to another folder
	 *  @@ Only for catalog data
	 */
	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void OnReMappingAction(const FString& NewMapping);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void OnReMappingUIShow(bool IsShow);

	/*
	 *  @@ open the file to view
	 *	@@ for furniture and material data
	 */
	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void OnOpenToViewAction();

	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void UpdateFolderID_BP(const FString& InFolderID);

	/*
	*  @@ logic to update folder code
	*  @@ UpdateFolderCode_BP : BP Logic to update folder code
	*  @@ UpdateFolderCodeOnly : Only update text folder code
	* 	
	*/
	UFUNCTION(BlueprintCallable, Category = "FrontShowInner")
	void UpdateFolderCode_BP(const FString& InFolderCode);
	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShowInner")
	void UpdateFolderCodeOnly(const FString& InFolderCode);


public:
	void BindDelegate_Other();

	void SendParseDetailInfoRequest();
	UFUNCTION()
	void OnGetFurnitureOrMatDataForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& DirData);

	void SendUpdateFolderIDRequest(const FString& InFolderID);
	void SendUpdateFolderCodeRequest(const FString& InFolderCode);
	UFUNCTION()
	void OnUpdateFolderIDResponseHandler(const FString& UUID, bool bSuccess, const FString& TipMsg, const FModelMatItemData& NewData);
	
	void SendDowmloadImageRequest(const FString& ImgPath);
	UFUNCTION()
	void OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath);

public:
	UPROPERTY(BlueprintReadOnly, Category = "FrontShowInnerWidget")
	EFrontDirectoryDataType DataType;

	UPROPERTY()
	FModelMatItemData Data;

	UPROPERTY()
	FRefDirectoryData RefData;

	//delegate
	FOpenToViewDelegate OpenToViewDelegate;
	FMappingDelegate MappingDelegate;


protected:
	UPROPERTY(BlueprintReadWrite,EditAnywhere, meta = (BindWidget))
	UMultiLineEditableTextBox* MTB_ID;

	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta = (BindWidget))
	UMultiLineEditableTextBox* MTB_Code;
private:
	static FString FilePath;
};
