// Fill out your copyright notice in the Description page of Project Settings.


#include "FrontShowWidget.h"
#include "FrontDirectoryWidget.h"
#include "FrontShowInnerWidget.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/MainUI/FolderWidget.h"
#include "Components/EditableText.h"
#include "Components/Button.h"

FString UFrontShowWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/FrontDiretory/BP_FrontShow.BP_FrontShow_C'");
UFrontShowWidget* UFrontShowWidget::Instance = nullptr;

bool UFrontShowWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void UFrontShowWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	DataType = EFrontDirectoryDataType::E_None;
	bHasDirectorySelected = false;
	QueryConfirmButton->OnClicked.AddDynamic(this, &ThisClass::OnQueryConfirmButtonClick);
	QueryParamEditableText->OnTextCommitted.AddDynamic(this, &ThisClass::OnQueryEditableTextCommited);


	BindNetDelegate();
}

void UFrontShowWidget::SwitchShowType(const EFrontDirectoryDataType& Type)
{
	ClearSubWidget();

	DataType = Type;
	UFrontDirectoryWidget::GetInstance()->SwitchShowType(Type);
}

void UFrontShowWidget::Init()
{
	InitDirectoryUI(UFrontDirectoryWidget::GetInstance());
}

void UFrontShowWidget::CloseAction()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
	QueryParamEditableText->SetText(FText::GetEmpty());
	bHasDirectorySelected = false;
}

void UFrontShowWidget::BindInnerDelegate()
{
	if(!UFrontDirectoryWidget::GetInstance()->FrontShowDelegate.IsBound())
	{
		UFrontDirectoryWidget::GetInstance()->FrontShowDelegate.BindUFunction(this, FName(TEXT("OnDirectoryInnerDetailShow")));
	}

	
}

void UFrontShowWidget::OnDirectoryInnerDetailShow(int32 CataID, int32 CataType)
{
	bHasDirectorySelected = true;
	CurrentCataID = CataID;
	SendDirectoryDetailDataRequest(CataID, 1, 100, CataType, QueryParamEditableText->GetText().ToString());
}

void UFrontShowWidget::SendDirectoryDetailDataRequest(const int32& CategoryID, const int32& PageNumber, const int32& PageSize, const int32& CateType, const FString& InQueryParam)
{
	NetUUID.CategoryDetailUUID = UCatalogNetworkSubsystem::GetInstance()->SendFurnitureOrMaterialSearchRequest(CategoryID, PageNumber, PageSize, CateType,InQueryParam);
}

void UFrontShowWidget::GenerateCategoryDetailData(const TArray<FModelMatItemData>& DirData)
{
	ClearSubWidget();
	for(const auto DD : DirData)
	{
		UFrontShowInnerWidget* InnerWidget = UFrontShowInnerWidget::Create();
		InnerWidget->Init(DD, DataType);
		InnerWidget->OpenToViewDelegate.BindUFunction(this, FName(TEXT("OnOpenToView")));
		
		AddSubWidget(InnerWidget);
	}
}

void UFrontShowWidget::GenerateCategoryDetailData(const TArray<FRefDirectoryData>& DirData)
{
	ClearSubWidget();
	for (const auto DD : DirData)
	{
		UFrontShowInnerWidget* InnerWidget = UFrontShowInnerWidget::Create();
		InnerWidget->Init(DD, DataType);
		InnerWidget->OpenToViewDelegate.BindUFunction(this, FName(TEXT("OnOpenToView")));
		InnerWidget->MappingDelegate.BindUFunction(this, FName(TEXT("OnMappingAction")));
		AddSubWidget(InnerWidget);
	}
}

void UFrontShowWidget::GenerateCategoryDetailData(const FModelMatItemContent& Datas)
{
	if (DataType == EFrontDirectoryDataType::E_Furniture || DataType == EFrontDirectoryDataType::E_Material)
	{
		GenerateCategoryDetailData(Datas.resourceAdminListResp);
	}
	else if (DataType == EFrontDirectoryDataType::E_Catalog)
	{
		GenerateCategoryDetailData(Datas.bkDirectoryList);
	}
}

void UFrontShowWidget::BindNetDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UFrontShowWidget::OnUploadFileResponseHandler);

	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &UFrontShowWidget::OnUpdateResponseHandler);

}

void UFrontShowWidget::UploadFileRequest(const FString& FileRelativePath)
{
	BackNetUUID.UploadFile = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void UFrontShowWidget::UpdateDataRequest(const FRefDirectoryData& InData)
{
	BackNetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(InData);
}

void UFrontShowWidget::OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (BackNetUUID.UpdateUUID.Equals(UUID))
	{
		if (bSuccess && Datas.IsValidIndex(0))
		{
			//sync
			UFolderWidget::Get()->SyncSelectData(Datas[0]);
			UFolderWidget::Get()->SyncRefLocalData(Datas[0]);

			//UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(Datas[0]));
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFrontShowWidget::OnUploadFileResponseHandler(bool OutRes, const FString& UploadFilePath)
{
	if (BackNetUUID.UploadFile.Equals(UploadFilePath))
	{
		BackNetUUID.ResetUploadFileAction();
		if (OutRes)
		{
			UE_LOG(LogTemp, Log, TEXT("UFrontShowWidget -- Upload [%s] SUCCESS"), *UploadFilePath);
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("UFrontShowWidget -- Upload [%s] FAILED"), *UploadFilePath);
		}
	}
}

void UFrontShowWidget::OnOpenToView(const FCSModelMatData& InData)
{
	CloseAction();
	ACatalogPlayerController::Get()->OpenViewModule(InData);
}

void UFrontShowWidget::OnMappingAction(FRefDirectoryData RefData, FString NewMapping, int32 MappingType)
{
	RefData.fontFolderPath = NewMapping;
	UpdateDataRequest(RefData);
}

UFrontShowWidget* UFrontShowWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UFrontShowWidget>(UFrontShowWidget::FilePath);

}

UFrontShowWidget* UFrontShowWidget::GetInstance()
{
	if(UFrontShowWidget::Instance == nullptr)
	{
		UFrontShowWidget::Instance = UFrontShowWidget::Create();
		UFrontShowWidget::Instance->Init();
		UFrontShowWidget::Instance->AddToViewport(100);
		UFrontShowWidget::Instance->SetVisibility(ESlateVisibility::Collapsed);
	}
	return UFrontShowWidget::Instance;
}

void UFrontShowWidget::OnQueryConfirmButtonClick()
{
	if (DataType == EFrontDirectoryDataType::E_None || DataType == EFrontDirectoryDataType::E_Mapping)
	{
		return;
	}
	uint32 CataType = DataType == EFrontDirectoryDataType::E_Catalog ? 1 : 0;
	SendDirectoryDetailDataRequest(CurrentCataID , 1, 100, CataType, QueryParamEditableText->GetText().ToString());
}

void UFrontShowWidget::OnQueryEditableTextCommited(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnEnter)
	{
		return;
	}
	if (DataType == EFrontDirectoryDataType::E_None||DataType == EFrontDirectoryDataType::E_Mapping)
	{
		return;
	}
	uint32 CataType = DataType == EFrontDirectoryDataType::E_Catalog ? 1 : 0;
	SendDirectoryDetailDataRequest(CurrentCataID, 1, 100, CataType, Text.ToString());
}
