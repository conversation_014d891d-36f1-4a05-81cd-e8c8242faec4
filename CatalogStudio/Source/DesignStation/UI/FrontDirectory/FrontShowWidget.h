// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FrontBaseWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FrontShowWidget.generated.h"



class UEditableText;
class UButton;
/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UFrontShowWidget : public UFrontBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	UFUNCTION(BlueprintCallable, Category = "FrontShow")
	void SwitchShowType(const EFrontDirectoryDataType& Type);

	UFUNCTION(BlueprintImplementableEvent, Category = "FrontShow")
	void InitDirectoryUI(UUserWidget* DirectoryUI);

	void Init();

	UFUNCTION(BlueprintCallable, Category = "FrontShow")
	void CloseAction();

	/*
	 *  @@ bind delegate with FrontDirectoryWidget
	 */
	void BindInnerDelegate();

	/*
	 *  @@ delegate handler to show the detail content file in directory
	 *	@@ CataID, the id of the directory id
	 *	@@ cataType, the type of the directory ( Catalog , Furniture ... )
	 */
	UFUNCTION()
	void OnDirectoryInnerDetailShow(int32 CataID, int32 CataType);

	/*
	 *  @@ function to get the detail content file in directory
	 *	@@ CategoryID, the id of the directory id
	 *	@@ PageNumber : current page number index
	 *	@@ pagesize : item number one page
	 *  @@ CateType : search category, 0 -- furniture / material ; 1 -- cabinet
	 *  @@ QueryParam: search params for furniture / material
	 */
	void SendDirectoryDetailDataRequest(const int32& CategoryID, const int32& PageNumber, const int32& PageSize, const int32& CateType,const FString& InQueryParam);

	/*
	 *  @@ override the function to show the detail content file in directory
	 */
	virtual void GenerateCategoryDetailData(const TArray<FModelMatItemData>& DirData) override;
	void GenerateCategoryDetailData(const TArray<FRefDirectoryData>& DirData);
	virtual void GenerateCategoryDetailData(const FModelMatItemContent& Datas) override;

	void BindNetDelegate();
	void UploadFileRequest(const FString& FileRelativePath);
	void UpdateDataRequest(const FRefDirectoryData& InData);
	UFUNCTION()
	void OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);
	UFUNCTION()
	void OnUploadFileResponseHandler(bool OutRes, const FString& UploadFilePath);

	/*
	*  @@ function to bind delegate
	*/
	UFUNCTION()
	void OnOpenToView(const FCSModelMatData& InData);
	UFUNCTION()
	void OnMappingAction(FRefDirectoryData RefData, FString NewMapping, int32 MappingType);

	static UFrontShowWidget* Create();
	static UFrontShowWidget* GetInstance();

protected:
	UFUNCTION()
	void OnQueryConfirmButtonClick();

	UFUNCTION()
	void OnQueryEditableTextCommited(const FText& Text, ETextCommit::Type CommitMethod);
public:
	UPROPERTY(BlueprintReadWrite, Category = "FrontShow")
	EFrontDirectoryDataType DataType;

protected:
	UPROPERTY(meta = (BindWidget))
	UEditableText* QueryParamEditableText;

	UPROPERTY(meta = (BindWidget))
	UButton* QueryConfirmButton;
private:
	static FString FilePath;
	static UFrontShowWidget* Instance;

	uint64 CurrentCataID;
	bool bHasDirectorySelected;
};
