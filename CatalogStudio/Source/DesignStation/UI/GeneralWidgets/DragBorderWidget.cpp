// Fill out your copyright notice in the Description page of Project Settings.

#include "DragBorderWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"

FString UDragBorderWidget::DragBorderPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/DragBorderUI.DragBorderUI_C'");

bool UDragBorderWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorDrag, Bor_Drag);
	BIND_SLATE_WIDGET_FUNCTION(BorDrag, OnMouseButtonDownEvent, FName(TEXT("LeftMouseDownToDrag")));
	BIND_SLATE_WIDGET_FUNCTION(BorDrag, OnMouseButtonUpEvent, FName(TEXT("LeftMouseUpToRelease")));

	return true;
}

void UDragBorderWidget::SetBorderBrushColor(const FLinearColor & InColor)
{
	if (IS_OBJECT_PTR_VALID(BorDrag))
	{
		BorDrag->SetBrushColor(InColor);
	}
}

UDragBorderWidget * UDragBorderWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UDragBorderWidget>(UDragBorderWidget::DragBorderPath);
}

void UDragBorderWidget::ClickToDragChangeMouseCursor(bool IsDrag)
{
	UE_LOG(LogTemp, Log, TEXT("change mouse cursor"));
	auto PC = UGameplayStatics::GetPlayerController(GWorld, 0);
	if (IS_OBJECT_PTR_VALID(PC))
	{
		PC->CurrentMouseCursor = IsDrag ? EMouseCursor::ResizeLeftRight : EMouseCursor::Default;
		//PC->SetMouseCursorWidget(IsDrag ? EMouseCursor::Type::ResizeLeftRight : EMouseCursor::Type::Default, nullptr);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("get pc error"));
	}
}

FEventReply UDragBorderWidget::LeftMouseDownToDrag(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ClickToDragChangeMouseCursor(true);
		DragBorderDelegate.ExecuteIfBound(true, MouseEvent);
		return FEventReply(true);
	}

	return FEventReply();
}

FEventReply UDragBorderWidget::LeftMouseUpToRelease(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ClickToDragChangeMouseCursor(false);
		DragBorderDelegate.ExecuteIfBound(false, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

void UDragBorderWidget::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("mouse enter drag widget"));
	UWidgetBlueprintLibrary::SetFocusToGameViewport();
	ClickToDragChangeMouseCursor(true);
}

void UDragBorderWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	UWidgetBlueprintLibrary::SetFocusToGameViewport();
	ClickToDragChangeMouseCursor(false);
}
