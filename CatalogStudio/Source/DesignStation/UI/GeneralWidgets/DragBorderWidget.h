// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DragBorderWidget.generated.h"

/**
 * 
 */

class UBorder;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FDragBorderDelegate, bool, IsDrag, const FPointerEvent&, MouseEvent);

UCLASS()
class DESIGNSTATION_API UDragBorderWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void SetBorderBrushColor(const FLinearColor& InColor);

	static UDragBorderWidget* Create();

private:
	void ClickToDragChangeMouseCursor(bool IsDrag);

public:
	FDragBorderDelegate DragBorderDelegate;

private:
	static FString DragBorderPath;

protected:
	UFUNCTION()
		FEventReply LeftMouseDownToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseUpToRelease(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent);
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent);
	//virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent);
	//virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent);
	
private:
	UPROPERTY()
		UBorder* BorDrag;
};
