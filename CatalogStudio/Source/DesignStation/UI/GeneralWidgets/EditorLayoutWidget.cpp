// Fill out your copyright notice in the Description page of Project Settings.

#include "EditorLayoutWidget.h"

#include "DragBorderWidget.h"
#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/BasicClasses/CatalogPawn.h"

FString UEditorLayoutWidget::EditorLayoutPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/EditLayout.EditLayout_C'");

bool UEditorLayoutWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(CPToolBar, CP_ToolBar);
	BIND_PARAM_CPP_TO_UMG(BorToolBar, Bor_ToolBar);
	BIND_PARAM_CPP_TO_UMG(CPDetail, CP_Detail);
	BIND_PARAM_CPP_TO_UMG(BorProperty, Bor_Property);
	BIND_PARAM_CPP_TO_UMG(CPProperty, CP_Property);
	BIND_PARAM_CPP_TO_UMG(SZRightResize, SZ_ResizeContent);
	BIND_PARAM_CPP_TO_UMG(SBCamera, SB_Camera);
	BIND_PARAM_CPP_TO_UMG(BtnCamera, Btn_Camera);
	BIND_PARAM_CPP_TO_UMG(BtnReset, Btn_Reset);
	BIND_PARAM_CPP_TO_UMG(BtnFront, Btn_Front);
	BIND_PARAM_CPP_TO_UMG(BtnSide, Btn_Side);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_PARAM_CPP_TO_UMG(SBTop, SB_Top);
	BIND_PARAM_CPP_TO_UMG(SBRight, SB_Right);
	BIND_PARAM_CPP_TO_UMG(PbLoad, Pb_Load);
	BIND_PARAM_CPP_TO_UMG(TxtLoad, Txt_Load);
	BIND_PARAM_CPP_TO_UMG(CPTips, CP_Tips);
	BIND_PARAM_CPP_TO_UMG(SBProcess, SB_Process);
	BIND_PARAM_CPP_TO_UMG(SBSuccess, SB_Success);
	BIND_PARAM_CPP_TO_UMG(SBFailure, SB_Failure);



	BIND_WIDGET_FUNCTION(BtnCamera, OnClicked, UEditorLayoutWidget::OnClickedCamera);
	BIND_WIDGET_FUNCTION(BtnReset, OnClicked, UEditorLayoutWidget::OnClickedReset);
	BIND_WIDGET_FUNCTION(BtnFront, OnClicked, UEditorLayoutWidget::OnClickedFront);
	BIND_WIDGET_FUNCTION(BtnSide, OnClicked, UEditorLayoutWidget::OnClickedSide);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UEditorLayoutWidget::OnClickedDown);

	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
	InitDragWidget();

	CameraWidget = UCameraWidget::Create();
	SBCamera->AddChild(CameraWidget);
	CameraWidget->CameraBtnTypeDelegate.BindUFunction(this, FName(TEXT("TakePicture")));
	OpenCameraWidget(false);
	//SetCameraEnable(true);

	return true;
}

void UEditorLayoutWidget::AddToolBar(UUserWidget* InToolBar)
{
	if (BorToolBar && IS_OBJECT_PTR_VALID(InToolBar))
	{
		BorToolBar->ClearChildren();
		BorToolBar->AddChild(InToolBar);
		ToolBarSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(BorToolBar);
		ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, FVector2D::ZeroVector, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

void UEditorLayoutWidget::AddFolderWidget(UUserWidget* InFolderWidget)
{
}

void UEditorLayoutWidget::AddFileListWidget(UUserWidget* InFileListWidget)
{
}

void UEditorLayoutWidget::AddPropertyWidget(UUserWidget* InPropertyWidget)
{
	if (IS_OBJECT_PTR_VALID(CPProperty) && IS_OBJECT_PTR_VALID(InPropertyWidget))
	{
		if (InPropertyWidget == CPProperty->GetChildAt(0))
		{
			return;
		}
		CPProperty->ClearChildren();
		CPProperty->AddChild(InPropertyWidget);
		PropertySlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InPropertyWidget);
		SetSlotToSuitContent(PropertySlot, false);
		ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, FVector2D::ZeroVector, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

FVector2D UEditorLayoutWidget::CalculateSlotNewSize(const FVector2D& OriginSize, const FVector2D& MoveVector)
{
	return FVector2D();
}

void UEditorLayoutWidget::SetDetailPanelIsShow(bool IsShow)
{
	if (CPDetail)
	{
		CPDetail->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

UEditorLayoutWidget* UEditorLayoutWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UEditorLayoutWidget>(UEditorLayoutWidget::EditorLayoutPath);
}

void UEditorLayoutWidget::ShowViewButton(bool bShow)
{
	BtnFront->SetVisibility(bShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	BtnSide->SetVisibility(bShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	BtnDown->SetVisibility(bShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
}

void UEditorLayoutWidget::AddSectionToolBar(UUserWidget* InSectionToolBarWidget)
{
}

void UEditorLayoutWidget::SetSectionToolBarPanelShow(bool IsShow)
{
}

void UEditorLayoutWidget::InitDragWidget()
{
	DragWidget = UDragBorderWidget::Create();
	DragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnDragWidgetHandler")));
	DragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZRightResize))
	{
		SZRightResize->AddChild(DragWidget);
	}
}

void UEditorLayoutWidget::OnDragWidgetHandler(bool IsDrag, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsRightResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UEditorLayoutWidget::OnClickedCamera()
{
	PictureTypeDelegate.ExecuteIfBound(-1);
	OpenCameraWidget(true);
}

void UEditorLayoutWidget::OnClickedReset()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->ResetCameraTransform();
	}
}

void UEditorLayoutWidget::OnClickedFront()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::EFrontView);
	}
}

void UEditorLayoutWidget::OnClickedSide()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::ERightView);
	}
}

void UEditorLayoutWidget::OnClickedDown()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::ETopView);
	}
}

void UEditorLayoutWidget::OpenCameraWidget(bool IsOpen)
{
	if (IsOpen && CameraWidget)
	{
		SBCamera->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBTop->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBRight->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
	else
	{
		SBCamera->SetVisibility(ESlateVisibility::Collapsed);
		SBTop->SetVisibility(ESlateVisibility::Collapsed);
		SBRight->SetVisibility(ESlateVisibility::Collapsed);

	}
}

void UEditorLayoutWidget::TakePicture(const int32& Type)
{
	switch (Type)
	{
	case 0:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 1:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 2:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 3:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 4:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 5:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 6:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 7:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 8:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 9:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 10:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	case 11:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	default:
		break;
	}
}

FReply UEditorLayoutWidget::NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		LeftMouseUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}
	return FReply::Unhandled();
}

FReply UEditorLayoutWidget::NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (IsLeftMouseDown)
	{
		FVector2D MoveVector = UUIFunctionLibrary::GetMouseMoveVector(InMouseEvent, LastMousePosition);
		UE_LOG(LogTemp, Log, TEXT("Move vector : %s, last pos : %s"), *MoveVector.ToString(), *LastMousePosition.ToString());
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		if (IsRightResize)
		{
			int ResolutionX = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
			float NewX = UKismetMathLibrary::FMax(MinRightSize, ResolutionX - LastMousePosition.X);
			UE_LOG(LogTemp, Log, TEXT("Mouse : %f, new right x size : %f"), LastMousePosition.X, NewX);
			PropertySlot->SetSize(FVector2D(NewX, PropertySlot->GetSize().Y));
			ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
				, FVector2D::ZeroVector, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
	}
	return FReply::Unhandled();
}

void UEditorLayoutWidget::SetCameraBtnState(const int32& Type, bool IsEnable)
{
	if (CameraWidget)
	{
		CameraWidget->SetBtnState(Type, IsEnable);
	}
}

void UEditorLayoutWidget::SetCameraImage(const FString& ImagePath, bool IsShow)
{
	if (CameraWidget)
	{
		CameraWidget->SetImage(ImagePath);
		CameraWidget->ShowImage(IsShow);
	}
}

void UEditorLayoutWidget::SetCameraEnable(bool IsEnable)
{
	if (IsEnable)
	{
		BtnCamera->SetVisibility(ESlateVisibility::Visible);

	}
	else
	{
		BtnCamera->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UEditorLayoutWidget::SetLoadingPercent(const float& perc)
{
	if (perc >= 0 && perc <= 1)
	{
		PbLoad->SetPercent(perc);
		TxtLoad->SetText(FText::FromString(FString::FromInt(perc * 100)));
	}
}

void UEditorLayoutWidget::ShowToast(bool bShow)
{
	if (IS_OBJECT_PTR_VALID(CPTips))
	{
		if (bShow)
		{
			CPTips->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			SBProcess->SetVisibility(ESlateVisibility::Collapsed);
			SBSuccess->SetVisibility(ESlateVisibility::Collapsed);
			SBFailure->SetVisibility(ESlateVisibility::Collapsed);
			CPTips->SetRenderOpacity(1.0f);
		}
		else
		{
			//CPTips->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}


void UEditorLayoutWidget::SetToast(bool bSuccess)
{
	//SBProcess->SetVisibility(ESlateVisibility::Collapsed);
	if (bSuccess)
	{
		SBSuccess->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBFailure->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		SBSuccess->SetVisibility(ESlateVisibility::Collapsed);
		SBFailure->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
	UpdateSaveTipTimeActor();
}

void UEditorLayoutWidget::UpdateSaveTipTimeActor()
{
	if (!IS_OBJECT_PTR_VALID(SaveTipTimeActor))
	{
		SaveTipTimeActor = NewObject<ATimeActor>();
	}
	if (IS_OBJECT_PTR_VALID(SaveTipTimeActor))
	{
		SaveTipTimeActor->ResetTipShowTime();
		SaveTipTimeActor->BindTimeHandler();
		SaveTipTimeActor->TimeActorDelegate.BindUFunction(this, FName(TEXT("OnTimeActorSaveHandler")));
	}
}

void UEditorLayoutWidget::OnTimeActorSaveHandler(float ShowRate)
{
	UE_LOG(LogTemp, Log, TEXT("save tip show rate : %f"), ShowRate);
	if (IS_OBJECT_PTR_VALID(CPTips))
	{
		CPTips->SetRenderOpacity(ShowRate);
		if (ShowRate <= 0.0f)
		{
			CPTips->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}