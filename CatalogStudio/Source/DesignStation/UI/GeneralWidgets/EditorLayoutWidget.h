// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LayoutBaseWidget.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "EditorLayoutWidget.generated.h"

/**
 * 
 */

class UCanvasPanel;
class UBorder;
class UCanvasPanelSlot;
class UDragBorderWidget;
class USizeBox;
class UCameraWidget;
class UProgressBar;
class UTextBlock;

UCLASS()
class DESIGNSTATION_API UEditorLayoutWidget : public ULayoutBaseWidget /*UUserWidget, public ILayoutWidgetInterface*/
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	virtual void AddToolBar(UUserWidget* InToolBar) override;

	virtual void AddFolderWidget(UUserWidget* InFolderWidget) override;

	virtual void AddFileListWidget(UUserWidget* InFileListWidget) override;

	virtual void AddPropertyWidget(UUserWidget* InPropertyWidget) override;

	virtual UUserWidget* GetThis() override { return this; }

	virtual FVector2D CalculateSlotNewSize(const FVector2D& OriginSize, const FVector2D& MoveVector) override;

	virtual void SetDetailPanelIsShow(bool IsShow) override;

	static UEditorLayoutWidget* Create();

	void ShowViewButton(bool bShow);

public:
	void AddSectionToolBar(UUserWidget* InSectionToolBarWidget);
	void SetSectionToolBarPanelShow(bool IsShow);

private:
	void InitDragWidget();
	UFUNCTION()
		void OnDragWidgetHandler(bool IsDrag, const FPointerEvent& MouseEvent);

private:
	UPROPERTY()
		UDragBorderWidget* DragWidget;

	static FString EditorLayoutPath;

	UFUNCTION()
		void OnClickedCamera();
	UFUNCTION()
		void OnClickedReset();
	UFUNCTION()
		void OnClickedFront();
	UFUNCTION()
		void OnClickedSide();
	UFUNCTION()
		void OnClickedDown();

	UFUNCTION()
		void OpenCameraWidget(bool IsOpen);
	UFUNCTION()
		void TakePicture(const int32& Type);

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	
private:
	UPROPERTY()
		UCanvasPanel* CPToolBar;
	UPROPERTY()
		UBorder* BorToolBar;
	UPROPERTY()
		UCanvasPanel* CPFolder;
	UPROPERTY()
		UBorder* BorFolder;
	UPROPERTY()
		UCanvasPanel* CPSectionToolBar;
	UPROPERTY()
		UBorder* BorSectionToolBar;

	UPROPERTY()
		UCanvasPanel* CPDetail;
	UPROPERTY()
		UBorder* BorProperty;

	UPROPERTY()
		UCanvasPanel* CPProperty;

	UPROPERTY()
		USizeBox*	SBCamera;
	UPROPERTY()
		UButton*	BtnCamera;
	UPROPERTY()
		UButton*	BtnReset;
	UPROPERTY()
		UButton* BtnFront;
	UPROPERTY()
		UButton* BtnSide;
	UPROPERTY()
		UButton* BtnDown;
	UPROPERTY()
		USizeBox*	SBTop;

	UPROPERTY()
		USizeBox*	SBRight;

	UPROPERTY()
		UProgressBar* PbLoad;
	UPROPERTY()
		UTextBlock* TxtLoad;
	UPROPERTY()
		UCanvasPanel* CPTips;
	UPROPERTY()
		USizeBox* SBProcess;
	UPROPERTY()
		USizeBox* SBSuccess;
	UPROPERTY()
		USizeBox* SBFailure;
	UCameraWidget*	CameraWidget;
	UPROPERTY()
		ATimeActor* SaveTipTimeActor;
public:
	void SetCameraBtnState(const int32& Type, bool IsEnable);
	void SetCameraImage(const FString & ImagePath, bool IsShow);
	void SetCameraEnable(bool IsEnable);
	void SetLoadingPercent(const float& process);
	void ShowToast(bool bShow = true);
	void SetToast(bool bSuccess);
	void UpdateSaveTipTimeActor();
	UFUNCTION()
	void OnTimeActorSaveHandler(float ShowRate);
public:
	FCameraBtnTypeDelegate	PictureTypeDelegate;
};
