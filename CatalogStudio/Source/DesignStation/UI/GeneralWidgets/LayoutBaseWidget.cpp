// Fill out your copyright notice in the Description page of Project Settings.

#include "LayoutBaseWidget.h"

#include "DesignStation/DesignStation.h"

bool ULayoutBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void ULayoutBaseWidget::AddToolBar(UUserWidget * InToolBar)
{
}

void ULayoutBaseWidget::AddFolderWidget(UUserWidget * InFolderWidget)
{
}

void ULayoutBaseWidget::AddFileListWidget(UUserWidget * InFileListWidget)
{
}

void ULayoutBaseWidget::AddPropertyWidget(UUserWidget * InPropertyWidget)
{
}

UUserWidget * ULayoutBaseWidget::GetThis()
{
	return this;
}

FVector2D ULayoutBaseWidget::CalculateSlotNewSize(const FVector2D & OriginSize, const FVector2D & MoveVector)
{
	return FVector2D::ZeroVector;
}

void ULayoutBaseWidget::SetDetailPanelIsShow(bool IsShow)
{
}

void ULayoutBaseWidget::RefreshSceneViewportSize()
{
	ResizeSceneViewport(ToolBarSlot ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
		, FolderTreeSlot ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
		, PropertySlot ? PropertySlot->GetSize() : FVector2D::ZeroVector);
}

void ULayoutBaseWidget::ResizeSceneViewport(const FVector2D & ToolBarSize, const FVector2D & FolderSize, const FVector2D & PropertySize)
{
	if (GWorld && GWorld->WorldType == EWorldType::Type::Game && GEngine && GEngine->GameViewport)
	{
		FVector2D ViewportOringinSize;
		GEngine->GameViewport->GetViewportSize(ViewportOringinSize);
		FVector2D ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
		UE_LOG(LogTemp, Log, TEXT("----------------------brgin----------------------------"));
		UE_LOG(LogTemp, Log, TEXT("viewport origin size:%f,%f"), ViewportOringinSize.X, ViewportOringinSize.Y);
		UE_LOG(LogTemp, Log, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);
		if (ViewportOringinSize.X > 0 && ViewportOringinSize.Y > 0 && ViewportResolution.X > 0 && ViewportResolution.Y > 0)
		{
			//FVector2D ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
			FVector2D ToolSizeRelativeToViewport = ToolBarSize / ViewportOringinSize;
			FVector2D FolderSizeRelativeToViewport = FolderSize / ViewportOringinSize;
			FVector2D PropertySizeRelativeToViewport = PropertySize / ViewportOringinSize;
			UE_LOG(LogTemp, Log, TEXT("ToolBar Relative size:%f,%f"), ToolSizeRelativeToViewport.X, ToolSizeRelativeToViewport.Y);
			UE_LOG(LogTemp, Log, TEXT("folder Relative size:%f,%f"), FolderSizeRelativeToViewport.X, FolderSizeRelativeToViewport.Y);
			UE_LOG(LogTemp, Log, TEXT("property Relative size:%f,%f"), PropertySizeRelativeToViewport.X, PropertySizeRelativeToViewport.Y);
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderSizeRelativeToViewport.X;
			//GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X - PropertySizeRelativeToViewport.X;
			//GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X;
			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
			FVector2D ViewportResizeSize;
			GEngine->GameViewport->GetViewportSize(ViewportResizeSize);
			UE_LOG(LogTemp, Log, TEXT("viewport resize size:%f,%f"), ViewportResizeSize.X, ViewportResizeSize.Y);
			ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
			UE_LOG(LogTemp, Log, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);
			UE_LOG(LogTemp, Log, TEXT("----------------------------end-----------------------"));
		}
	}
}

void ULayoutBaseWidget::SetSlotToSuitContent(UCanvasPanelSlot * InSlot, bool IsFolderTree)
{
	if (IS_OBJECT_PTR_VALID(InSlot))
	{
		InSlot->SetAlignment(FVector2D::ZeroVector);
		if (IsFolderTree)
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(290.0f, 0.0f));
		}
		else
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(310.0f, 0.0f));
		}
		//InSlot->bAutoSize = true;
	}
}

void ULayoutBaseWidget::LeftMouseUpToDrop()
{
	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
}

FEventReply ULayoutBaseWidget::UpdateLastMousePos(const FPointerEvent & MouseEvent)
{
	FVector2D PixelPosTemp; // no use
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), PixelPosTemp, LastMousePosition);
	UE_LOG(LogTemp, Log, TEXT("left mouse to resize , last pos : %s"), *LastMousePosition.ToString());
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
	return UWidgetBlueprintLibrary::CaptureMouse(DetectReply, nullptr);
}

FReply ULayoutBaseWidget::NativeOnMouseButtonUp(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}

FReply ULayoutBaseWidget::NativeOnMouseMove(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}

void ULayoutBaseWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	LeftMouseUpToDrop();
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
	UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply);
}
