// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"
#include "DesignStation/UI/Parameters/TimeActor.h"
#include "LayoutBaseWidget.generated.h"

/**
 * 
 */

const float MinLeftSize = 290.0f;
const float MinRightSize = 290.0f;
const float SingleComponentMinSize = 310.0f;

UCLASS()
class DESIGNSTATION_API ULayoutBaseWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	virtual void AddToolBar(UUserWidget* InToolBar);

	virtual void AddFolderWidget(UUserWidget* InFolderWidget);

	virtual void AddFileListWidget(UUserWidget* InFileListWidget);

	virtual void AddPropertyWidget(UUserWidget* InPropertyWidget);

	virtual UUserWidget* GetThis();

	virtual FVector2D CalculateSlotNewSize(const FVector2D& OriginSize, const FVector2D& MoveVector);

	virtual void SetDetailPanelIsShow(bool IsShow);

	void RefreshSceneViewportSize();

	static void ResizeSceneViewport(const FVector2D& ToolbarSize, const FVector2D& FolderSize, const FVector2D& PropertySize);

public:
	void SetSlotToSuitContent(UCanvasPanelSlot* InSlot, bool IsFolderTree = true);
	void LeftMouseUpToDrop();
	FEventReply UpdateLastMousePos(const FPointerEvent & MouseEvent);
	
protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	
public:
	FVector2D LastMousePosition;
	bool IsLeftMouseDown;
	bool IsLeftResize;
	bool IsRightResize;

public:
	UPROPERTY()
		UCanvasPanelSlot* ToolBarSlot;
	UPROPERTY()
		UCanvasPanelSlot* FolderTreeSlot;
	UPROPERTY()
		UCanvasPanelSlot* PropertySlot;

	UPROPERTY()
		USizeBox* SZLeftResize;
	UPROPERTY()
		USizeBox* SZRightResize;
};
