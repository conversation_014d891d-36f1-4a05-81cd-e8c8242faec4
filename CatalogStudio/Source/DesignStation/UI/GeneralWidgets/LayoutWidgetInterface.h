// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Blueprint/UserWidget.h"
#include "LayoutWidgetInterface.generated.h"

// This class does not need to be modified.
UINTERFACE(MinimalAPI)
class ULayoutWidgetInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * 
 */
class DESIGNSTATION_API ILayoutWidgetInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	
	virtual void AddToolBar(UUserWidget* InToolBar) = 0;

	virtual void AddFolderWidget(UUserWidget* InFolderWidget) = 0;

	virtual void AddFileListWidget(UUserWidget* InFileListWidget) = 0;

	virtual void AddPropertyWidget(UUserWidget* InPropertyWidget) = 0;

	virtual UUserWidget* GetThis() = 0;

	virtual void SetDetailPanelIsShow(bool IsShow) = 0;
};
