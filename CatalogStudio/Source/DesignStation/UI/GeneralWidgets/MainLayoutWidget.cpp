// Fill out your copyright notice in the Description page of Project Settings.

#include "MainLayoutWidget.h"

#include "DragBorderWidget.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Components/ScaleBox.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"

FString UMainLayoutWidget::MainLayoutWidgetPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/MainLayout.MainLayout_C'");

bool UMainLayoutWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(CPToolBar, CP_ToolBar);
	BIND_PARAM_CPP_TO_UMG(<PERSON>r<PERSON>oolBar, Bor_ToolBar);
	BIND_PARAM_CPP_TO_UMG(ScaNavigation, Sca_Navigation);
	BIND_PARAM_CPP_TO_UMG(BorBreadNavigation, Bor_Navigation);
	BIND_PARAM_CPP_TO_UMG(CPFolder, CP_Folder);
	BIND_PARAM_CPP_TO_UMG(BorFolder, Bor_Folder);
	BIND_PARAM_CPP_TO_UMG(CPFolderTree, CP_FolderTree);
	BIND_PARAM_CPP_TO_UMG(SZLeftResize, SZ_LeftResize);

	BIND_PARAM_CPP_TO_UMG(BorFileList, Bor_FileList);
	BIND_PARAM_CPP_TO_UMG(CPDetail, CP_Detail);
	CPDetail->SetVisibility(ESlateVisibility::Collapsed);

	BIND_PARAM_CPP_TO_UMG(BorProperty, Bor_Property);
	BIND_PARAM_CPP_TO_UMG(CPProperty, CP_Property);
	BIND_PARAM_CPP_TO_UMG(SZRightResize, SZ_RightResize);

	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
	InitResizeWidget();

	return true;
}

void UMainLayoutWidget::AddToolBar(UUserWidget* InToolBar)
{
	if (BorToolBar && IS_OBJECT_PTR_VALID(InToolBar))
	{
		BorToolBar->AddChild(InToolBar);
	}
}

void UMainLayoutWidget::AddBreadNavigation(UUserWidget * InWidget)
{
	if (IS_OBJECT_PTR_VALID(BorBreadNavigation) && IS_OBJECT_PTR_VALID(InWidget))
	{
		BorBreadNavigation->AddChild(InWidget);
		SetNavigationBarShow(false);
	}
}

void UMainLayoutWidget::AddFolderWidget(UUserWidget* InFolderWidget)
{
	if (CPFolderTree && IS_OBJECT_PTR_VALID(InFolderWidget))
	{
		if (CPFolderTree->GetChildrenCount() != 0)
		{
			return;
		}
		CPFolderTree->AddChild(InFolderWidget);
		FolderTreeSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InFolderWidget);
		SetSlotToSuitContent(FolderTreeSlot);
		//BorFolder->AddChild(InFolderWidget);
	}
}

void UMainLayoutWidget::AddFileListWidget(UUserWidget* InFileListWidget)
{
	if (BorFileList && IS_OBJECT_PTR_VALID(InFileListWidget))
	{
		if (BorFileList->GetChildAt(0) == InFileListWidget)
		{
			return;
		}
		BorFileList->ClearChildren();
		BorFileList->AddChild(InFileListWidget);
	}
}

void UMainLayoutWidget::AddPropertyWidget(UUserWidget* InPropertyWidget)
{
	if (CPProperty && IS_OBJECT_PTR_VALID(InPropertyWidget))
	{
		//BorProperty->AddChild(InPropertyWidget);
		CPProperty->AddChild(InPropertyWidget);
		PropertySlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InPropertyWidget);
		SetSlotToSuitContent(PropertySlot, false);
	}
}

void UMainLayoutWidget::SetDetailPanelIsShow(bool IsShow)
{
	if (CPDetail)
	{
		CPDetail->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

ESlateVisibility UMainLayoutWidget::GetDetailPanelState()
{
	if (CPDetail)
	{
		return CPDetail->GetVisibility();
	}
	return ESlateVisibility::Collapsed;
}

void UMainLayoutWidget::SetNavigationBarShow(bool IsShow)
{
	if (IS_OBJECT_PTR_VALID(ScaNavigation))
	{
		ScaNavigation->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

int32 UMainLayoutWidget::GetPropertyBorderChildrenCount()
{
	if (BorProperty)
	{
		return BorProperty->GetChildrenCount();
	}
	return -1;
}

void UMainLayoutWidget::ClearPropertyWidget()
{
	if (CPProperty != nullptr)
	{
		CPProperty->ClearChildren();
		PropertySlot = nullptr;
	}
}

void UMainLayoutWidget::ClearFileListWidget()
{
	if (BorFileList)
	{
		BorFileList->ClearChildren();
	}
}

UMainLayoutWidget * UMainLayoutWidget::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UThreeTextWidget::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UMainLayoutWidget>(UMainLayoutWidget::MainLayoutWidgetPath);
}

void UMainLayoutWidget::InitResizeWidget()
{
	LeftDragWidget = UDragBorderWidget::Create();
	LeftDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnLeftResizeHandler")));
	LeftDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZLeftResize))
	{
		SZLeftResize->AddChild(LeftDragWidget);
	}

	RightDragWidget = UDragBorderWidget::Create();
	RightDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnRightResizeHandler")));
	RightDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZRightResize))
	{
		SZRightResize->AddChild(RightDragWidget);
	}
}

void UMainLayoutWidget::OnLeftResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsLeftResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UMainLayoutWidget::OnRightResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsRightResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

FVector2D UMainLayoutWidget::CalculateSlotNewSize(const FVector2D & OriginSize, const FVector2D & MoveVector)
{
	FVector2D NewSize;
	int X = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
	if (IsLeftResize)
	{
		if (CPDetail->GetVisibility() == ESlateVisibility::Visible)
		{
			NewSize.X = UKismetMathLibrary::FMin(IS_OBJECT_PTR_VALID(PropertySlot) ? (X - PropertySlot->GetSize().X - 100.0f) : (X - 200.0f), UKismetMathLibrary::FMax(MinLeftSize, OriginSize.X + MoveVector.X));
		}
		else
		{
			NewSize.X = UKismetMathLibrary::FMin(X - 200.0f, UKismetMathLibrary::FMax(MinLeftSize, OriginSize.X + MoveVector.X));
		}
	}
	/*else if (IsRightResize)
	{
		float NewX = UKismetMathLibrary::FMax(MinRightSize, X - UKismetMathLibrary::FMax(FolderTreeSlot->GetSize().X, LastMousePosition.X));
	}*/
	//NewSize.Y = UKismetMathLibrary::FMin(MaxLeftSize, UKismetMathLibrary::FMax(MinLeftSize, OriginSize.X + MoveVector.X));
	NewSize.Y = OriginSize.Y;
	return NewSize;
}

FReply UMainLayoutWidget::NativeOnMouseButtonUp(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		LeftMouseUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}
	return FReply::Unhandled();
}

FReply UMainLayoutWidget::NativeOnMouseMove(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (IsLeftMouseDown)
	{
		FVector2D MoveVector = UUIFunctionLibrary::GetMouseMoveVector(InMouseEvent, LastMousePosition);
		UE_LOG(LogTemp, Log, TEXT("Move vector : %s, last pos : %s"), *MoveVector.ToString(), *LastMousePosition.ToString());
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		if (IsLeftResize)
		{
			FolderTreeSlot->SetSize(CalculateSlotNewSize(FolderTreeSlot->GetSize(), MoveVector));
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
		else if (IsRightResize)
		{
			int ResolutionX = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
			float NewX = UKismetMathLibrary::FMax(MinRightSize, ResolutionX - UKismetMathLibrary::FMax(FolderTreeSlot->GetSize().X + 100.0f, LastMousePosition.X));
			UE_LOG(LogTemp, Log, TEXT("Left : %f, Mouse : %f, new right x size : %f"), FolderTreeSlot->GetSize().X, LastMousePosition.X,NewX);
			PropertySlot->SetSize(FVector2D(NewX, PropertySlot->GetSize().Y));
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
	}
	return FReply::Unhandled();
}
