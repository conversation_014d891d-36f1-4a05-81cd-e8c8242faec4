// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "LayoutWidgetInterface.h"
#include "LayoutBaseWidget.h"
#include "MainLayoutWidget.generated.h"

/**
 * 
 */

class UCanvasPanel;
class UBorder;
class UCanvasPanelSlot;
class UScaleBox;
class UDragBorderWidget;

UCLASS()
class DESIGNSTATION_API UMainLayoutWidget : public ULayoutBaseWidget/*UUserWidget, public ILayoutWidgetInterface*/
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	virtual void AddToolBar(UUserWidget* InToolBar) override;

	void AddBreadNavigation(UUserWidget* InWidget);

	virtual void AddFolderWidget(UUserWidget* InFolderWidget) override;

	virtual void AddFileListWidget(UUserWidget* InFileListWidget) override;

	virtual void AddPropertyWidget(UUserWidget* InPropertyWidget) override;

	virtual UUserWidget* GetThis() override { return this; }

	virtual FVector2D CalculateSlotNewSize(const FVector2D& OriginSize, const FVector2D& MoveVector) override;

	virtual void SetDetailPanelIsShow(bool IsShow) override;
	ESlateVisibility GetDetailPanelState();
	void SetNavigationBarShow(bool IsShow);

	int32 GetPropertyBorderChildrenCount();

	void ClearPropertyWidget();
	void ClearFileListWidget();

	static UMainLayoutWidget* Create();

private:
	void InitResizeWidget();
	UFUNCTION()
		void OnLeftResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnRightResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);

private:
	UPROPERTY()
		UDragBorderWidget* LeftDragWidget;
	UPROPERTY()
		UDragBorderWidget* RightDragWidget;

	static FString MainLayoutWidgetPath;

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	UPROPERTY()
		UCanvasPanel* CPToolBar;
	UPROPERTY()
		UBorder* BorToolBar;

	UPROPERTY()
		UScaleBox* ScaNavigation;
	UPROPERTY()
		UBorder* BorBreadNavigation;

	//left panel
	UPROPERTY()
		UCanvasPanel* CPFolder;
	UPROPERTY()
		UBorder* BorFolder;
	UPROPERTY()
		UCanvasPanel* CPFolderTree;
	/*UPROPERTY()
		UBorder* BorLeftResize;*/

	UPROPERTY()
		UBorder* BorFileList;

	UPROPERTY()
		UCanvasPanel* CPDetail;
	UPROPERTY()
		UBorder* BorProperty;
	UPROPERTY()
		UCanvasPanel* CPProperty;
	/*UPROPERTY()
		UBorder* BorRightResize;*/

};
