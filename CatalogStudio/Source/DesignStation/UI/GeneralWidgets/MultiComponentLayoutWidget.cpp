// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentLayoutWidget.h"

#include "DragBorderWidget.h"
#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/BasicClasses/CatalogPawn.h"

FString UMultiComponentLayoutWidget::MultiCompLayoutPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/MultiComponentLayout.MultiComponentLayout_C'");

bool UMultiComponentLayoutWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorToolBar, Bor_ToolBar);

	BIND_PARAM_CPP_TO_UMG(CPComponentTree, CP_ComponentTree);
	BIND_PARAM_CPP_TO_UMG(SZLeftResize, SZ_LeftResize);

	BIND_PARAM_CPP_TO_UMG(CPDetail, CP_Detail);
	BIND_PARAM_CPP_TO_UMG(CPComponentProperty, CP_ComponentProperty);
	BIND_PARAM_CPP_TO_UMG(SZRightResize, SZ_RightResize);
	BIND_PARAM_CPP_TO_UMG(SBCamera, SB_Camera);
	BIND_PARAM_CPP_TO_UMG(BtnCamera, Btn_Camera);
	BIND_PARAM_CPP_TO_UMG(SBTop,SB_Top);
	BIND_PARAM_CPP_TO_UMG(SBLeft, SB_Left);
	BIND_PARAM_CPP_TO_UMG(SBRight, SB_Right);
	BIND_PARAM_CPP_TO_UMG(BtnReset, Btn_Reset);
	BIND_PARAM_CPP_TO_UMG(BtnFront, Btn_Front);
	BIND_PARAM_CPP_TO_UMG(BtnSide, Btn_Side);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnCamera, OnClicked, UMultiComponentLayoutWidget::OnClickedCamera);
	BIND_WIDGET_FUNCTION(BtnReset, OnClicked, UMultiComponentLayoutWidget::OnClickedReset);
	BIND_WIDGET_FUNCTION(BtnFront, OnClicked, UMultiComponentLayoutWidget::OnClickedFront);
	BIND_WIDGET_FUNCTION(BtnSide, OnClicked, UMultiComponentLayoutWidget::OnClickedSide);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UMultiComponentLayoutWidget::OnClickedDown);

	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
	InitResizeWidget();

	
	CameraWidget = UCameraWidget::Create();
	SBCamera->AddChild(CameraWidget);
	CameraWidget->CameraBtnTypeDelegate.BindUFunction(this, FName(TEXT("TakePicture")));
	OpenCameraWidget(false);
	


	return true;
}

void UMultiComponentLayoutWidget::AddToolBar(UUserWidget* InToolBar)
{
	if (BorToolBar && IS_OBJECT_PTR_VALID(InToolBar))
	{
		BorToolBar->AddChild(InToolBar);
		ToolBarSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(BorToolBar);
		ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
		    , IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

void UMultiComponentLayoutWidget::AddFolderWidget(UUserWidget* InFolderWidget)
{
	/*if (BorFolder && IS_OBJECT_PTR_VALID(InFolderWidget))
	{
		BorFolder->AddChild(InFolderWidget);
	}*/
	if (IS_OBJECT_PTR_VALID(CPComponentTree) && IS_OBJECT_PTR_VALID(InFolderWidget))
	{
		if (CPComponentTree->GetChildrenCount() != 0)
		{
			return;
		}
		CPComponentTree->AddChild(InFolderWidget);
		FolderTreeSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InFolderWidget);
		SetSlotToSuitContent(FolderTreeSlot);
		ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
			, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

void UMultiComponentLayoutWidget::AddFileListWidget(UUserWidget* InFileListWidget)
{

}

void UMultiComponentLayoutWidget::AddPropertyWidget(UUserWidget* InPropertyWidget)
{
	/*if (BorSingleCompList && IS_OBJECT_PTR_VALID(InListWidget))
	{
		BorSingleCompList->ClearChildren();
		BorSingleCompList->AddChild(InListWidget);
	}*/
	if (IS_OBJECT_PTR_VALID(CPComponentProperty) && IS_OBJECT_PTR_VALID(InPropertyWidget))
	{
		if (InPropertyWidget == CPComponentProperty->GetChildAt(0))
		{
			return;
		}
		CPComponentProperty->ClearChildren();
		CPComponentProperty->AddChild(InPropertyWidget);
		PropertySlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InPropertyWidget);
		SetSlotToSuitContent(PropertySlot,false);
		ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
			, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
			, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
	}
}

//void UMultiComponentLayoutWidget::AddSingleComponentPropertyWidget(UUserWidget * InPropertyWidget)
//{
//	if (BorSingleCompProperty && IS_OBJECT_PTR_VALID(InPropertyWidget))
//	{
//		BorSingleCompProperty->ClearChildren();
//		BorSingleCompProperty->AddChild(InPropertyWidget);
//	}
//}
//
//void UMultiComponentLayoutWidget::ClearSingleComponentPropertyWidget()
//{
//	if (BorSingleCompProperty && IS_OBJECT_PTR_VALID(BorSingleCompProperty->GetChildAt(0)))
//	{
//		BorSingleCompProperty->GetChildAt(0)->SetVisibility(ESlateVisibility::Collapsed);
//	}
//}

void UMultiComponentLayoutWidget::SetDetailPanelIsShow(bool IsShow)
{
	if (CPDetail)
	{
		CPDetail->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
		, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
		, IsShow ? (IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector) : FVector2D::ZeroVector);
}

UMultiComponentLayoutWidget * UMultiComponentLayoutWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiComponentLayoutWidget>(UMultiComponentLayoutWidget::MultiCompLayoutPath);
}

void UMultiComponentLayoutWidget::InitResizeWidget()
{
	LeftDragWidget = UDragBorderWidget::Create();
	LeftDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnLeftResizeHandler")));
	LeftDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZLeftResize))
	{
		SZLeftResize->AddChild(LeftDragWidget);
	}

	RightDragWidget = UDragBorderWidget::Create();
	RightDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnRightResizeHandler")));
	RightDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZRightResize))
	{
		SZRightResize->AddChild(RightDragWidget);
	}
}

void UMultiComponentLayoutWidget::OnLeftResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("multi component tree resize"));
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsLeftResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UMultiComponentLayoutWidget::OnRightResizeHandler(bool IsDrag, const FPointerEvent & MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("multi component property resize"));
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsRightResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UMultiComponentLayoutWidget::OnClickedCamera()
{
	PictureTypeDelegate.ExecuteIfBound(-1);
	OpenCameraWidget(true);
}

void UMultiComponentLayoutWidget::OpenCameraWidget(bool IsOpen)
{
	if (IsOpen&&CameraWidget)
	{
		SBCamera->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBTop->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBLeft->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SBRight->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
	else
	{
		SBCamera->SetVisibility(ESlateVisibility::Collapsed);
		SBTop->SetVisibility(ESlateVisibility::Collapsed);
		SBLeft->SetVisibility(ESlateVisibility::Collapsed);
		SBRight->SetVisibility(ESlateVisibility::Collapsed);

	}
}

void UMultiComponentLayoutWidget::TakePicture(const int32& Type)
{
	switch (Type)
	{
	case 0:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 1:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 2:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 3:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 4:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 5:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 6:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 7:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 8:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 9:
		PictureTypeDelegate.ExecuteIfBound(Type);
		break;
	case 10:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	case 11:
		PictureTypeDelegate.ExecuteIfBound(Type);
		OpenCameraWidget(false);
		break;
	default:
		break;
	}
}

FReply UMultiComponentLayoutWidget::NativeOnMouseButtonUp(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("mouse up on multi component ui"));
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		LeftMouseUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}

	return FReply::Unhandled();
}

FReply UMultiComponentLayoutWidget::NativeOnMouseMove(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (IsLeftMouseDown)
	{
		FVector2D MoveVector = UUIFunctionLibrary::GetMouseMoveVector(InMouseEvent, LastMousePosition);
		UE_LOG(LogTemp, Log, TEXT("multi component Move vector : %s, last pos : %s"), *MoveVector.ToString(), *LastMousePosition.ToString());
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		int ResolutionX = 0;
		if (GWorld && GWorld->GetGameViewport() && GWorld->GetGameViewport()->Viewport)
		{
			ResolutionX = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
		}
		if (IsLeftResize)
		{	
			float NewSizeX = 0.0f;
			if (CPDetail->GetVisibility() == ESlateVisibility::Visible)
			{
				NewSizeX = UKismetMathLibrary::FMin(IS_OBJECT_PTR_VALID(PropertySlot) ? (ResolutionX - PropertySlot->GetSize().X - 10.0f) : (ResolutionX - 10.0f), UKismetMathLibrary::FMax(MinLeftSize, LastMousePosition.X));
			}
			else
			{
				NewSizeX = UKismetMathLibrary::FMin(ResolutionX - 10.0f, UKismetMathLibrary::FMax(MinLeftSize, LastMousePosition.X));
			}
			UE_LOG(LogTemp, Log, TEXT("ResolutionX : %d,Left : %f, Mouse : %f, new right x size : %f"), ResolutionX, FolderTreeSlot->GetSize().X, LastMousePosition.X, NewSizeX);
			FolderTreeSlot->SetSize(FVector2D(NewSizeX, FolderTreeSlot->GetSize().Y));
			ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
				, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
				, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
		else if (IsRightResize)
		{
			float NewSizeX = UKismetMathLibrary::FMax(MinRightSize, ResolutionX - UKismetMathLibrary::FMax(FolderTreeSlot->GetSize().X + 10.0f, LastMousePosition.X));
			UE_LOG(LogTemp, Log, TEXT("ResolutionX : %d,Left : %f, Mouse : %f, new right x size : %f"), ResolutionX, PropertySlot->GetSize().X, LastMousePosition.X, NewSizeX);
			PropertySlot->SetSize(FVector2D(NewSizeX, PropertySlot->GetSize().Y));
			ResizeSceneViewport(IS_OBJECT_PTR_VALID(ToolBarSlot) ? ToolBarSlot->GetSize() : FVector2D::ZeroVector
				, IS_OBJECT_PTR_VALID(FolderTreeSlot) ? FolderTreeSlot->GetSize() : FVector2D::ZeroVector
				, IS_OBJECT_PTR_VALID(PropertySlot) ? PropertySlot->GetSize() : FVector2D::ZeroVector);
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
	}

	return FReply::Unhandled();
}

void UMultiComponentLayoutWidget::SetCameraBtnState(const int32& Type, bool IsEnable)
{
	if (CameraWidget)
	{
		CameraWidget->SetBtnState(Type, IsEnable);
	}
}

void UMultiComponentLayoutWidget::SetCameraImage(const FString & ImagePath, bool IsShow)
{
	if (CameraWidget)
	{
		CameraWidget->SetImage(ImagePath);
		CameraWidget->ShowImage(IsShow);
	}
}

void UMultiComponentLayoutWidget::OnClickedReset()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->ResetCameraTransform();
	}
}

void UMultiComponentLayoutWidget::OnClickedFront()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::EFrontView);
	}
}

void UMultiComponentLayoutWidget::OnClickedSide()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::ERightView);
	}
}

void UMultiComponentLayoutWidget::OnClickedDown()
{
	ACatalogPlayerController* PC = ACatalogPlayerController::Get();
	if (PC)
	{
		ACatalogPawn* CurrentPawn = Cast<ACatalogPawn>(PC->GetPawn());
		CurrentPawn->TakePictureType(ECameraType::ETopView);
	}
}
