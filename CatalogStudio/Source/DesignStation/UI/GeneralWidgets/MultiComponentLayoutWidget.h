// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
//#include "LayoutWidgetInterface.h"
#include "LayoutBaseWidget.h"
#include "DesignStation/UI/Camera/CameraWidget.h"
#include "MultiComponentLayoutWidget.generated.h"

/**
*
*/

class UCanvasPanel;
class UBorder;
class UCanvasPanelSlot;
class UScaleBox;
class UDragBorderWidget;
class USizeBox;
class UCameraWidget;

UCLASS()
class DESIGNSTATION_API UMultiComponentLayoutWidget : public ULayoutBaseWidget/*UUserWidget, public ILayoutWidgetInterface*/
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	virtual void AddToolBar(UUserWidget* InToolBar) override;

	virtual void AddFolderWidget(UUserWidget* InFolderWidget) override;

	virtual void AddFileListWidget(UUserWidget* InFileListWidget) override;

	virtual void AddPropertyWidget(UUserWidget* InPropertyWidget) override;

	//void AddSingleComponentPropertyWidget(UUserWidget* InPropertyWidget);
	//void ClearSingleComponentPropertyWidget();

	virtual UUserWidget* GetThis() override { return this; }

	virtual void SetDetailPanelIsShow(bool IsShow) override;

	static UMultiComponentLayoutWidget* Create();

private:
	void InitResizeWidget();
	UFUNCTION()
		void OnLeftResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnRightResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnClickedCamera();
	UFUNCTION()
		void OpenCameraWidget(bool IsOpen);
	UFUNCTION()
		void TakePicture(const int32& Type);

	UFUNCTION()
		void OnClickedReset();
	UFUNCTION()
		void OnClickedFront();
	UFUNCTION()
		void OnClickedSide();
	UFUNCTION()
		void OnClickedDown();
private:
	UPROPERTY()
		UDragBorderWidget* LeftDragWidget;
	UPROPERTY()
		UDragBorderWidget* RightDragWidget;

	static FString MultiCompLayoutPath;

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	/*UPROPERTY()
		UCanvasPanel* CPToolBar;*/
	UPROPERTY()
		UBorder* BorToolBar;

	/*UPROPERTY()
		UCanvasPanel* CPFolder;
	UPROPERTY()
		UBorder* BorFolder;*/
	UPROPERTY()
		UCanvasPanel* CPComponentTree;

	UPROPERTY()
		UCanvasPanel* CPDetail;
	UPROPERTY()
		UCanvasPanel* CPComponentProperty;
	UPROPERTY()
		USizeBox*	SBCamera;
	UPROPERTY()
		UButton*	BtnCamera;
	UPROPERTY()
		USizeBox*	SBTop;
	UPROPERTY()
		USizeBox*	SBLeft;
	UPROPERTY()
		USizeBox*	SBRight;

	UPROPERTY()
		UButton* BtnReset;
	UPROPERTY()
		UButton* BtnFront;
	UPROPERTY()
		UButton* BtnSide;
	UPROPERTY()
		UButton* BtnDown;
	/*UPROPERTY()
		UCanvasPanel* CPDetail;
	UPROPERTY()
		UBorder* BorSingleCompList;
	UPROPERTY()
		UBorder* BorSingleCompProperty;*/

	UCameraWidget*	CameraWidget;
public:
	void SetCameraBtnState(const int32& Type,bool IsEnable);
	void SetCameraImage(const FString & ImagePath, bool IsShow);
public:
	FCameraBtnTypeDelegate	PictureTypeDelegate;
};

