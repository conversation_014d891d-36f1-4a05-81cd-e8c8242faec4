// Fill out your copyright notice in the Description page of Project Settings.

#include "StyleLayoutWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/MenuAnchor.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/Engine/Classes/Kismet/KismetMathLibrary.h"
#include "DragBorderWidget.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/MergeProcessWidget.h"
#include "Kismet/KismetSystemLibrary.h"


FString UStyleLayoutWidget::StyleLayoutBp = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/StyleLayoutUI.StyleLayoutUI_C'");

bool UStyleLayoutWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}


	return true;
}

void UStyleLayoutWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, UStyleLayoutWidget::OnClickedBtnClose);
	BIND_PARAM_CPP_TO_UMG(CPStyle, CP_Style);
	BIND_PARAM_CPP_TO_UMG(SZLeftResize, SZ_LeftResize);
	BIND_PARAM_CPP_TO_UMG(BorOption, Bor_Option);
	BIND_PARAM_CPP_TO_UMG(SZRightResize, SZ_RightResize);
	BIND_PARAM_CPP_TO_UMG(CPValue, CP_Value);
	BIND_PARAM_CPP_TO_UMG(MAMenu, MA_Menu);
	BIND_PARAM_CPP_TO_UMG(CPMenu, CP_Menu);
	//BIND_PARAM_CPP_TO_UMG(SBMenu, SB_Menu);
	BIND_PARAM_CPP_TO_UMG(SBImage, SB_Image);
	BIND_PARAM_CPP_TO_UMG(BtnUpload, Btn_Upload);
	BIND_PARAM_CPP_TO_UMG(BtnDownload, Btn_Download);
	BIND_WIDGET_FUNCTION(BtnUpload, OnClicked, UStyleLayoutWidget::OnClickedBtnUpload);
	BIND_WIDGET_FUNCTION(BtnDownload, OnClicked, UStyleLayoutWidget::OnClickedBtnDownload);


	BIND_SLATE_WIDGET_FUNCTION(MAMenu, OnGetUserMenuContentEvent, FName(TEXT("CreateRightMenu")));

	LastMousePosition = FVector2D::ZeroVector;
	IsLeftMouseDown = false;
	IsLeftResize = false;
	IsRightResize = false;
	RightMenuWidget = nullptr;
	//ValueParams.Empty();
	InitResizeWidget();
	//InitSubWidget();

	//MAMenu->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

	if (!ValueMenuWidget)
	{
		ValueMenuWidget = UValueMenuWidget::Create();
		ValueMenuWidget->ValueMenuDelegate.BindUFunction(this, FName(TEXT("GetMenuOption")));
	}

	if (!IS_OBJECT_PTR_VALID(SeeImageWidget))
	{
		SeeImageWidget = USeeImageWidget::Create();
		SBImage->AddChild(SeeImageWidget);
		SeeImageWidget->SetVisibility(ESlateVisibility::Collapsed);
	}

#ifdef USE_REF_LOCAL_FILE

	BindDelegate();

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UStyleLayoutWidget::OnDownloadFileResponseHandler);
		CatalogPC->UploadFileResponseDelegate.AddUniqueDynamic(this, &UStyleLayoutWidget::OnUploadFileResponseHandler);
	}
#endif
}

void UStyleLayoutWidget::AddFolderWidget(UUserWidget* InStyleWidget)
{
	if (IS_OBJECT_PTR_VALID(InStyleWidget) && IS_OBJECT_PTR_VALID(CPStyle))
	{
		if (CPStyle->GetChildrenCount() > 0)
		{
			return;
		}
		CPStyle->AddChild(InStyleWidget);
		FolderTreeSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InStyleWidget);
		SetCPSlotSuitContent(FolderTreeSlot);
	}
}

void UStyleLayoutWidget::AddFileListWidget(UUserWidget* InOptionWidget)
{
	if (IS_OBJECT_PTR_VALID(BorOption) && IS_OBJECT_PTR_VALID(InOptionWidget))
	{
		BorOption->ClearChildren();
		BorOption->AddChild(InOptionWidget);
	}
}

void UStyleLayoutWidget::AddPropertyWidget(UUserWidget* InValueWidget)
{
	if (IS_OBJECT_PTR_VALID(CPValue) && IS_OBJECT_PTR_VALID(InValueWidget))
	{
		if (CPValue->GetChildrenCount() > 0)
		{
			return;
		}
		CPValue->AddChild(InValueWidget);
		PropertySlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(InValueWidget);
		SetCPSlotSuitContent(PropertySlot, false);
	}
}

void UStyleLayoutWidget::ResetStyleSelectState()
{
	RightMenuCopySelection.SetInValid();
	RightMenuWidget = nullptr;
	if (IS_OBJECT_PTR_VALID(SubStyleWidget))
	{
		SubStyleWidget->ResetSelectState();
	}
	if (IS_OBJECT_PTR_VALID(SubOptionWidget))
	{
		SubOptionWidget->ResetSelectState();
	}
	if (IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget->ResetSelectState();
		SubValueWidget->ClearContent();
	}
}

UStyleLayoutWidget* UStyleLayoutWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UStyleLayoutWidget>(UStyleLayoutWidget::StyleLayoutBp);
}

void UStyleLayoutWidget::InitSubWidget()
{
#ifdef USE_REF_LOCAL_FILE

	INIT_STYLE_REF_DATA();
	
#endif

	UpdateSubStyleWidget();
	UpdateSubOptionWidget();
	UpdateSubValueWidget();
}

void UStyleLayoutWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->ReleaseStyleParamsResponseDelegate.AddUniqueDynamic(this, &UStyleLayoutWidget::OnReleaseResponseHandler);

	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UStyleLayoutWidget::OnDownloadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UStyleLayoutWidget::OnUploadFileResponseHandler);

}

void UStyleLayoutWidget::OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg)
{
	if (UUID.Equals(ReleaseUUID))
	{
		if (bSuccess)
		{
			ReleaseUUID = EMPTY_NET_MARK;
			UI_POP_WINDOW_ERROR_ST(TEXT("release style success"));
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), Msg);
			//UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UStyleLayoutWidget::UpdateSubStyleWidget()
{
	if (!IS_OBJECT_PTR_VALID(SubStyleWidget))
	{
		SubStyleWidget = UStyleWidget::Create();
		AddFolderWidget(SubStyleWidget);
	}
	if (IS_OBJECT_PTR_VALID(SubStyleWidget))
	{
		/*TArray<FDecorateStyle> DecorateStyles;
		if (UDecorateStyleOperatorLibrary::SelectAllDecorateStyle(DecorateStyles))
		{
			SubStyleWidget->UpdateContent(DecorateStyles);
		}*/
		SubStyleWidget->SetSelectStyleID(TEXT("-1"));
		SubStyleWidget->GetContent();
		SubStyleWidget->ResetOperateState();
		SubStyleWidget->StyleItemSelectDelegate.BindUFunction(this, FName(TEXT("OnStyleItemSelectHandler")));
		SubStyleWidget->StyleItemDeleteDelegate.BindUFunction(this, FName(TEXT("OnStyleItemDeleteHandler")));
		SubStyleWidget->SetVisibility(ESlateVisibility::Visible);
		SubStyleWidget->StyleImageDelegate.BindUFunction(this, FName(TEXT("ShowStyleImage")));
		SubStyleWidget->ResetScroll();
	}
}

void UStyleLayoutWidget::UpdateSubOptionWidget()
{
	if (!IS_OBJECT_PTR_VALID(SubOptionWidget))
	{
		SubOptionWidget = UOptionWidget::Create();
		AddFileListWidget(SubOptionWidget);
	}
	if (IS_OBJECT_PTR_VALID(SubOptionWidget))
	{
		/*TArray<FDecorateContent> DecorateOptions;
		if (UDecorateContentOperatorLibrary::SelectAllOption(DecorateOptions))
		{
			SubOptionWidget->UpdateContent(DecorateOptions);
		}*/
		SubOptionWidget->GetOptionsContent();
		SubOptionWidget->ResetSelectState();
		SubOptionWidget->OptionSelectDelegate.BindUFunction(this, FName(TEXT("OnOptionItemSelectHandler")));
		SubOptionWidget->OptionDelDelegate.BindUFunction(this, FName(TEXT("OnOptionItemDeleteHandler")));
		SubOptionWidget->SetVisibility(ESlateVisibility::Visible);
		SubOptionWidget->ResetScroll();
	}
}

void UStyleLayoutWidget::UpdateSubValueWidget()
{
	if (!IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget = UValueWidget::Create();
		AddPropertyWidget(SubValueWidget);
	}
	if (IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget->UpdateValueContent(TEXT("-1"), TEXT("-1"));
		SubValueWidget->ClearParamContent();
		SubValueWidget->ResetOperateState();
		SubValueWidget->SetVisibility(ESlateVisibility::Visible);
		SubValueWidget->ResetScroll();
	}
}

void UStyleLayoutWidget::SetCPSlotSuitContent(UCanvasPanelSlot* InSlot, bool IsStyleWidget)
{
	if (IS_OBJECT_PTR_VALID(InSlot))
	{
		InSlot->SetAlignment(FVector2D::ZeroVector);
		if (IsStyleWidget)
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(600.0f, 0.0f));
		}
		else
		{
			InSlot->SetAnchors(FAnchors(0.0f, 0.0f, 0.0f, 1.0f));
			InSlot->SetOffsets(FMargin());
			InSlot->SetSize(FVector2D(600.0f, 0.0f));
		}
	}
}

void UStyleLayoutWidget::InitResizeWidget()
{
	LeftDragWidget = UDragBorderWidget::Create();
	LeftDragWidget->SetBorderBrushColor(FLinearColor(0.806952f, 0.806952f, 0.806952f, 1.0f));
	LeftDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnLeftResizeHandler")));
	LeftDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZLeftResize))
	{
		SZLeftResize->AddChild(LeftDragWidget);
	}

	RightDragWidget = UDragBorderWidget::Create();
	RightDragWidget->SetBorderBrushColor(FLinearColor(0.806952f, 0.806952f, 0.806952f, 1.0f));
	RightDragWidget->DragBorderDelegate.BindUFunction(this, FName(TEXT("OnRightResizeHandler")));
	RightDragWidget->SetVisibility(ESlateVisibility::Visible);
	if (IS_OBJECT_PTR_VALID(SZRightResize))
	{
		SZRightResize->AddChild(RightDragWidget);
	}
}

void UStyleLayoutWidget::OnLeftResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsLeftResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UStyleLayoutWidget::OnRightResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsDrag)
		{
			IsLeftMouseDown = true;
			IsRightResize = true;
			UpdateLastMousePos(MouseEvent);
		}
		else
		{
			LeftMouseUpToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UStyleLayoutWidget::UpdateStyleValueContent(const FString& StyleID, const FString& OptionID)
{
	if (IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget->UpdateValueContent(StyleID, OptionID);
		SubValueWidget->StyleValueDelegate.BindUFunction(this, FName(TEXT("ShowRightMenu")));
	}
}

void UStyleLayoutWidget::OnStyleItemSelectHandler(const FString& StyleID)
{
	if (IS_OBJECT_PTR_VALID(SubOptionWidget))
	{
		SubOptionWidget->SetBtnAddEnable(true);
		UpdateStyleValueContent(StyleID, SubOptionWidget->GetSelectOptionID());
	}
}

void UStyleLayoutWidget::OnStyleItemDeleteHandler()
{
	if (IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget->ClearParamContent();
		SubValueWidget->UpdateValueContent(TEXT("-1"), SubOptionWidget->GetSelectOptionID());
	}
}

void UStyleLayoutWidget::OnOptionItemSelectHandler(const FString& StyleID)
{
	if (IS_OBJECT_PTR_VALID(SubStyleWidget) && IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		UpdateStyleValueContent(SubStyleWidget->GetSelectStyleID(), StyleID);
		SubValueWidget->SetBtnAddEnable(true);
	}
}

void UStyleLayoutWidget::OnOptionItemDeleteHandler()
{
	if (IS_OBJECT_PTR_VALID(SubValueWidget))
	{
		SubValueWidget->ResetOperateState();
		SubValueWidget->ClearContent();
	}
}

FReply UStyleLayoutWidget::NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		LeftMouseUpToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}
	return FReply::Unhandled();
}

FReply UStyleLayoutWidget::NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (IsLeftMouseDown)
	{
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		int ResolutionX = GWorld->GetGameViewport()->Viewport->GetSizeXY().X;
		float NewX;
		if (IsLeftResize)
		{
			NewX = UKismetMathLibrary::FMax(400.0f, UKismetMathLibrary::FMin(LastMousePosition.X, ResolutionX - PropertySlot->GetSize().X - 400.0f));
			UE_LOG(LogTemp, Log, TEXT("left resize new x : %f; last mouse position : %f"), NewX, LastMousePosition.X);
			FolderTreeSlot->SetSize(FVector2D(NewX, FolderTreeSlot->GetSize().Y));
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
		else if (IsRightResize)
		{
			NewX = UKismetMathLibrary::FMax(400.0f, ResolutionX - UKismetMathLibrary::FMax(FolderTreeSlot->GetSize().X + 400.0f, LastMousePosition.X));
			UE_LOG(LogTemp, Log, TEXT("right resize new x : %f; last mouse position : %f"), NewX, LastMousePosition.X);
			PropertySlot->SetSize(FVector2D(NewX, PropertySlot->GetSize().Y));
			return UpdateLastMousePos(InMouseEvent).NativeReply;
		}
	}

	return FReply::Unhandled();
}

void UStyleLayoutWidget::RemoveProcess()
{
	if (MergeProcessWidget)
	{
		MergeProcessWidget->RemoveFromParent();
	}
}

void UStyleLayoutWidget::UploadSuccess(bool IsTrue)
{
	if (IsTrue)
	{
		MergeProcessWidget->SetPercent(1.f);
	}
	else
	{
		MergeProcessWidget->SetSuccess(false);
	}
	FLatentActionInfo LatentActionInfo;
	LatentActionInfo.CallbackTarget = this;
	LatentActionInfo.ExecutionFunction = TEXT("RemoveProcess");
	LatentActionInfo.Linkage = 1;
	LatentActionInfo.UUID = 100;
	UKismetSystemLibrary::Delay(this, 0.5f, LatentActionInfo);
}

void UStyleLayoutWidget::OnStyleRelease()
{
	if(bool ConfirmRes = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(
		FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(),
		FText::FromStringTable(FName("PosSt"), TEXT("confirm to release style data?")).ToString()))
	{
		//save and release
		UFolderWidget::Get()->SaveStyle();
		FString StyleRelativePath = URefToStyleDataLibrary::GetStyleRelativeAddress();
		SaveToRelease = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(StyleRelativePath);
		
		//ReleaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendReleaseStyleOrParameterRequest(1);
	}
}

void UStyleLayoutWidget::OnClickedBtnClose()
{

	GET_STYLE_REF_FILE_DATA();
	//EXIT_EDIT_STYLE_TO_SYNC_LOCAL_NETWORK(StyleRefData);
	bool SaveRes = URefRelationFunction::SaveStyleRefRelationToFile(StyleRefData);
	UFolderWidget::Get()->UploadStyle();

	this->SetVisibility(ESlateVisibility::Collapsed);
}

void UStyleLayoutWidget::OnClickedBtnUpload()
{
#ifdef USE_REF_LOCAL_FILE
#else
	TArray<FString> Path;
	Path.Add(FString("Cache/server_cache.db"));
	DownloadUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(Path);
	UE_LOG(LogTemp, Error, TEXT("Upload Start, Downloading Database"));
	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(true);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.1f);
	}
#endif
}

void UStyleLayoutWidget::OnClickedBtnDownload()
{
#ifdef USE_REF_LOCAL_FILE
	//此方法现用于强制更新风格数据
	FRefToStyleFile& CurStyleInfo = UFolderWidget::Get()->GetStyleRefDataRef();
	for (auto& CD : CurStyleInfo.content_datas)
	{
		for (auto& OD : CD.option_datas)
		{
			for (auto& OP : OD.option_params)
			{
				for (auto& NP : OP.EnumData)
				{
					if (NP.expression.IsEmpty() && !NP.value.IsEmpty())
					{
						NP.expression = NP.value;
					}
				}
			}
		}
	}

	UFolderWidget::Get()->SaveStyle();
	UFolderWidget::Get()->UploadStyle();


#else
	DownloadOverUUID = ACatalogPlayerController::Get()->DownloadFileRequest(TEXT("Cache/server_cache.db"));
	UE_LOG(LogTemp, Error, TEXT("Download Database Start"));

	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(false);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.2f);
	}
#endif
}

void UStyleLayoutWidget::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	if (DownloadOverUUID.Equals(UUID))
	{
#ifdef USE_REF_LOCAL_FILE
#else
		DownloadOverUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		UE_LOG(LogTemp, Error, TEXT("Download Finished, Begin Over Write Database"));
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(FPaths::Combine(FPaths::ProjectContentDir(), TEXT("Cache/server_cache.db")));
		TArray<FString> FilesToDownload;
		{//风格
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_style"));
			TArray<FDecorateStyle> ServerStyle = TArray<FDecorateStyle>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from decorate_style ORDER BY SORT_ORDER ASC"), ServerStyle);
			if (ServerStyle.Num() > 0)
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_style(ID , CHECKED, CODE, DESCRIPTION, THUMBNAIL_PATH, SORT_ORDER) values "));
				for (auto& OutStyleiter : ServerStyle)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%d','%s','%s','%s','%d'),"), *OutStyleiter.id, OutStyleiter.checked, *OutStyleiter.code, *OutStyleiter.description, *OutStyleiter.thumbnail_path, OutStyleiter.sort_order);

					if (!OutStyleiter.thumbnail_path.IsEmpty())
					{
						FDownloadFileData ThumbnailInfo;
						FDownloadFileDataLibrary::RetriveServerFileMD5(TEXT("style_image"), OutStyleiter.thumbnail_path, ThumbnailInfo);
						FDownloadFileDataLibrary::UpdateFileMD5(TEXT("style_image"), ThumbnailInfo);

						const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + OutStyleiter.thumbnail_path);
						FString LocalMD5 = TEXT("");
						int64 FileSize = 0;
						ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

						if (LocalMD5.IsEmpty() || (false == ThumbnailInfo.md5.Equals(LocalMD5, ESearchCase::IgnoreCase)))
							FilesToDownload.Add(OutStyleiter.thumbnail_path);
					}
				}
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		UE_LOG(LogTemp, Error, TEXT("40%"));
		MergeProcessWidget->SetPercent(0.4f);
		{//风格内容
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_content"));
			TArray<FDecorateContent> OutContent = TArray<FDecorateContent>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from decorate_content ORDER BY SORT_ORDER ASC"), OutContent);
			if (OutContent.Num())
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_content(ID, NAME ,SORT_ORDER) values "));
				for (auto& OutContentiter : OutContent)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%d'),"), *OutContentiter.id, *OutContentiter.name, OutContentiter.sort_order);
				}
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		UE_LOG(LogTemp, Error, TEXT("50%"));
		MergeProcessWidget->SetPercent(0.5f);
		{//风格值
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_sel"));
			TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel ORDER BY SORT_ORDER ASC")), OutSelection);
			if (OutSelection.Num() > 0)
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_sel(ID ,CODE, DESCRIPTION, SORT_ORDER, VISIBILITY, VISIBILITY_EXP, PARENT_ID) values "));
				for (auto& OutSelectioniter : OutSelection)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s'),"), *OutSelectioniter.id, *OutSelectioniter.code, *OutSelectioniter.description, OutSelectioniter.sort_order, *OutSelectioniter.visibility, *OutSelectioniter.visibility_exp, *OutSelectioniter.parent_id);
				}
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		UE_LOG(LogTemp, Error, TEXT("60%"));
		MergeProcessWidget->SetPercent(0.6f);
		{//风格选中信息
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_selch"));
			TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from decorate_selch"), OutDecoSelectionChecks);
			if (OutDecoSelectionChecks.Num() > 0)
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_selch(ID ,SELECTION_ID, CONTENT_ID ,STYLE_ID, IS_PRIME) values "));
				for (auto& OutDecoSelectionChecksiter : OutDecoSelectionChecks)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%d','%s','%s','%s','%d'),"), OutDecoSelectionChecksiter.id, *OutDecoSelectionChecksiter.selection_id, *OutDecoSelectionChecksiter.content_id, *OutDecoSelectionChecksiter.style_id, OutDecoSelectionChecksiter.is_prime);
				}
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		UE_LOG(LogTemp, Error, TEXT("70%"));
		MergeProcessWidget->SetPercent(0.7f);
		{//风格变量
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_param"));
			TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from decorate_param"), OutParamsTableData);
			if (OutParamsTableData.Num() > 0)
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param values "));
				for (auto& OutParamsTableDataiter : OutParamsTableData)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),"), *OutParamsTableDataiter.id, *OutParamsTableDataiter.name, *OutParamsTableDataiter.description, OutParamsTableDataiter.classific_id, *OutParamsTableDataiter.value, *OutParamsTableDataiter.expression, *OutParamsTableDataiter.max_value, *OutParamsTableDataiter.max_expression, *OutParamsTableDataiter.min_value, *OutParamsTableDataiter.min_expression, *OutParamsTableDataiter.visibility, *OutParamsTableDataiter.visibility_exp, *OutParamsTableDataiter.editable, *OutParamsTableDataiter.editable_exp, OutParamsTableDataiter.is_enum, *OutParamsTableDataiter.param_id, *OutParamsTableDataiter.main_id);
				}
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		UE_LOG(LogTemp, Error, TEXT("80%"));
		MergeProcessWidget->SetPercent(0.8f);
		{//风格变量枚举
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("DELETE FROM decorate_param_enum"));
			TArray<FEnumParameterTableData> OutDecoParamsEnum = TArray<FEnumParameterTableData>();
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from decorate_param_enum"), OutDecoParamsEnum);
			if (OutDecoParamsEnum.Num() > 0)
			{
				FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param_enum values "));
				for (auto& OutDecoParamsEnumiter : OutDecoParamsEnum)
				{
					InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *OutDecoParamsEnumiter.id, *OutDecoParamsEnumiter.value, *OutDecoParamsEnumiter.expression, *OutDecoParamsEnumiter.name_for_display, *OutDecoParamsEnumiter.image_for_display, *OutDecoParamsEnumiter.visibility, *OutDecoParamsEnumiter.visibility_exp, *OutDecoParamsEnumiter.priority, *OutDecoParamsEnumiter.main_id);
					if (false == OutDecoParamsEnumiter.image_for_display.IsEmpty())
					{
						FDownloadFileData ThumbnailInfo;
						FDownloadFileDataLibrary::RetriveServerFileMD5(TEXT("param_image"), OutDecoParamsEnumiter.image_for_display, ThumbnailInfo);
						FDownloadFileDataLibrary::UpdateFileMD5(TEXT("param_image"), ThumbnailInfo);

						const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + OutDecoParamsEnumiter.image_for_display);
						FString LocalMD5 = TEXT("");
						int64 FileSize = 0;
						ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

						if (LocalMD5.IsEmpty() || (false == ThumbnailInfo.md5.Equals(LocalMD5, ESearchCase::IgnoreCase)))
							FilesToDownload.Add(OutDecoParamsEnumiter.image_for_display);
					}
				}
				if (InsertEnumSQL.Len() > 50) FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
			}
		}
		LocalDBSubsystem->CloseServerDatabase();
		/*MergeProcessWidget->SetPercent(0.5f);
		UE_LOG(LogTemp, Error, TEXT("Download Finished, Begin Over Write Database"));
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->CloseDatabase();
		LocalDBSubsystem->CloseServerDatabase();
		MergeProcessWidget->SetPercent(0.8f);
		FString LocalAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir() + TEXT("Cache/local_cache.dat")));
		FString ServerAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir() + TEXT("Cache/server_cache.db")));
		FCatalogFunctionLibrary::CopyFileTo(ServerAbsPath, LocalAbsPath);
		LocalDBSubsystem->OpenDatabase(LocalAbsPath);*/
		UE_LOG(LogTemp, Error, TEXT("Style Download And Replace Succuessful"));
		UE_LOG(LogTemp, Error, TEXT("Download Over Write Done, OK"));
		UE_LOG(LogTemp, Error, TEXT("Download Local File"));
		if (FilesToDownload.Num() > 0)
		{
			DownloadFileUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(FilesToDownload);
		}
		else
		{
			UploadSuccess(true);
			InitSubWidget();
			FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Style")).ToString();
			FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();
			MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
		}
#endif
	}
	else if (DownloadUUID.Equals(UUID))
	{
#ifdef USE_REF_LOCAL_FILE
#else
		DownloadUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		MergeProcessWidget->SetPercent(0.3f);
		UE_LOG(LogTemp, Error, TEXT("Upload, Download Finished Begin Over Write Database"));
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(FPaths::Combine(FPaths::ProjectContentDir(), TEXT("Cache/server_cache.db")));
		FilesToUpload.Empty();
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_style"));
		TArray<FDecorateStyle> OutStyle = TArray<FDecorateStyle>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_style ORDER BY SORT_ORDER ASC"), OutStyle))
		{
			FString InsertStyleSQL = FString::Printf(TEXT("insert into decorate_style(ID, CHECKED, CODE, DESCRIPTION, THUMBNAIL_PATH, SORT_ORDER) values "));
			for (auto& OutStyleiter : OutStyle)
			{
				InsertStyleSQL = InsertStyleSQL + FString::Printf(TEXT("('%s','%d','%s','%s','%s','%d'),"), *OutStyleiter.id, OutStyleiter.checked, *OutStyleiter.code, *OutStyleiter.description, *OutStyleiter.thumbnail_path, OutStyleiter.sort_order);
				if (!OutStyleiter.thumbnail_path.IsEmpty())
				{
					FDownloadFileData ThumbnailInfo;
					FDownloadFileDataLibrary::RetriveFileMD5(TEXT("style_image"), OutStyleiter.thumbnail_path, ThumbnailInfo);
					FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("style_image"), ThumbnailInfo);
					if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + OutStyleiter.thumbnail_path)))
						FilesToUpload.Add(OutStyleiter.thumbnail_path);
				}
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertStyleSQL.Mid(0, InsertStyleSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.4f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_content"));
		TArray<FDecorateContent> OutContent = TArray<FDecorateContent>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_content ORDER BY SORT_ORDER ASC"), OutContent))
		{
			FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_content(ID, NAME ,SORT_ORDER) values "));
			for (auto& OutContentiter : OutContent)
			{
				InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%d'),"), *OutContentiter.id, *OutContentiter.name, OutContentiter.sort_order);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.5f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_sel"));
		TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel ORDER BY SORT_ORDER ASC")), OutSelection))
		{
			FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_sel(ID ,CODE, DESCRIPTION, SORT_ORDER, VISIBILITY, VISIBILITY_EXP, PARENT_ID) values "));
			for (auto& OutSelectioniter : OutSelection)
			{
				InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s'),"), *OutSelectioniter.id, *OutSelectioniter.code, *OutSelectioniter.description, OutSelectioniter.sort_order, *OutSelectioniter.visibility, *OutSelectioniter.visibility_exp, *OutSelectioniter.parent_id);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.6f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_selch"));
		TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_selch"), OutDecoSelectionChecks))
		{
			FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_selch(ID ,SELECTION_ID, CONTENT_ID ,STYLE_ID, IS_PRIME) values "));
			for (auto& OutDecoSelectionChecksiter : OutDecoSelectionChecks)
			{
				InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%d','%s','%s','%s','%d'),"), OutDecoSelectionChecksiter.id, *OutDecoSelectionChecksiter.selection_id, *OutDecoSelectionChecksiter.content_id, *OutDecoSelectionChecksiter.style_id, OutDecoSelectionChecksiter.is_prime);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.7f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_param"));
		TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_param"), OutParamsTableData))
		{
			FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param values "));
			for (auto& OutParamsTableDataiter : OutParamsTableData)
			{
				InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s'),"), *OutParamsTableDataiter.id, *OutParamsTableDataiter.name, *OutParamsTableDataiter.description, OutParamsTableDataiter.classific_id, *OutParamsTableDataiter.value, *OutParamsTableDataiter.expression, *OutParamsTableDataiter.max_value, *OutParamsTableDataiter.max_expression, *OutParamsTableDataiter.min_value, *OutParamsTableDataiter.min_expression, *OutParamsTableDataiter.visibility, *OutParamsTableDataiter.visibility_exp, *OutParamsTableDataiter.editable, *OutParamsTableDataiter.editable_exp, OutParamsTableDataiter.is_enum, *OutParamsTableDataiter.param_id, *OutParamsTableDataiter.main_id);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.8f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("DELETE FROM decorate_param_enum"));
		TArray<FEnumParameterTableData> OutDecoParamsEnum = TArray<FEnumParameterTableData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_param_enum"), OutDecoParamsEnum))
		{
			FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param_enum values "));
			for (auto& OutDecoParamsEnumiter : OutDecoParamsEnum)
			{
				InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *OutDecoParamsEnumiter.id, *OutDecoParamsEnumiter.value, *OutDecoParamsEnumiter.expression, *OutDecoParamsEnumiter.name_for_display, *OutDecoParamsEnumiter.image_for_display, *OutDecoParamsEnumiter.visibility, *OutDecoParamsEnumiter.visibility_exp, *OutDecoParamsEnumiter.priority, *OutDecoParamsEnumiter.main_id);
				if (false == OutDecoParamsEnumiter.image_for_display.IsEmpty())
				{
					FDownloadFileData ThumbnailInfo;
					FDownloadFileDataLibrary::RetriveFileMD5(TEXT("param_image"), OutDecoParamsEnumiter.image_for_display, ThumbnailInfo);
					FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("param_image"), ThumbnailInfo);
					if (FPaths::FileExists(FPaths::ProjectContentDir() + OutDecoParamsEnumiter.image_for_display))
						FilesToUpload.Add(OutDecoParamsEnumiter.image_for_display);
				}
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
		}
		LocalDBSubsystem->CloseServerDatabase();
		UE_LOG(LogTemp, Error, TEXT("Upload, Over Write Database Finished, Uploading"));
		MergeProcessWidget->SetPercent(0.9f);
		UploadUUID = ACatalogPlayerController::Get()->UploadFileRequest(TEXT("Cache/server_cache.db"));
#endif
	}
	else if (DownloadFileUUID.Equals(UUID))
	{
		UploadSuccess(true);
		InitSubWidget();

#ifdef USE_REF_LOCAL_FILE
#else
		FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Style")).ToString();
		FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
		FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();
		MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
#endif
	}
}

void UStyleLayoutWidget::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (OutFilePath.Equals(SaveToRelease, ESearchCase::IgnoreCase))
	{
		SaveToRelease = TEXT("NoWay");
		if (OutRes)
		{
			ReleaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendReleaseStyleOrParameterRequest(1);
		}
	}

//	if (UploadUUID.Equals(OutFilePath))
//	{//no use
//		UploadUUID.Empty();
//		if (false == OutRes)
//		{
//			UploadSuccess(true);
//			return;
//		}
//		if (FilesToUpload.Num() > 0)
//		{
//#ifdef USE_REF_LOCAL_FILE
//
//			UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FilesToUpload.Top());
//
//#else
//			UploadUUID = ACatalogPlayerController::Get()->UploadFileRequest(FilesToUpload.Top());
//#endif
//			FilesToUpload.Pop();
//		}
//		else
//		{
//			FString msg = OutRes ? TEXT("Upload, upload success") : TEXT("Upload, style upload fail");
//			UE_LOG(LogTemp, Error, TEXT("%s"), *msg);
//
//#ifdef USE_REF_LOCAL_FILE
//
//#else
//			FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Style")).ToString();
//			FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
//			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Upload")).ToString();
//			MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
//#endif
//			UploadSuccess(true);
//		}
//	}
}

void UStyleLayoutWidget::ShowRightMenu(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos)
{
	//MAMenu->SetContent(ValueMenuWidget);
	RightMenuWidget = ValueItem;
	MAMenu->Close();
	UCanvasPanelSlot* CPSMenu = UWidgetLayoutLibrary::SlotAsCanvasSlot(MAMenu);
	CPSMenu->SetPosition(MousePos);
	ValueMenuWidget->SetMenuState((ValueItem->GetIsCheck() && ValueItem->GetIsVisiable()), RightMenuCopySelection.IsValid());
	MAMenu->Open(true);
}

UValueMenuWidget* UStyleLayoutWidget::CreateRightMenu()
{
	return ValueMenuWidget;
	//return nullptr;
}
void UStyleLayoutWidget::GetMenuOption(const int32& option)
{
	if (option == static_cast<int32>(EValueMenuType::EDisplay))
	{
		SubValueWidget->SetSubItemPrime(RightMenuWidget->GetData().id, true);
	}
	else if (option == static_cast<int32>(EValueMenuType::ECopy))
	{
		if (IS_OBJECT_PTR_VALID(RightMenuWidget))
		{
			RightMenuCopySelection = RightMenuWidget->GetData();
		}
	}
	else if (option == static_cast<int32>(EValueMenuType::EPaste))
	{
		if (IS_OBJECT_PTR_VALID(RightMenuWidget) && IS_OBJECT_PTR_VALID(SubValueWidget))
		{
			SubValueWidget->InsertValue(RightMenuWidget->GetData().id, RightMenuCopySelection);
		}
		RightMenuCopySelection.SetInValid();
	}
	//RightMenuOptionDelegate.ExecuteIfBound(option);
	MAMenu->Close();
}
void UStyleLayoutWidget::ShowStyleImage(const FString& Path)
{
	if (IS_OBJECT_PTR_VALID(SeeImageWidget))
	{
		SeeImageWidget->SetImage(Path);
		SeeImageWidget->SetVisibility(ESlateVisibility::Visible);
	}
}
FReply UStyleLayoutWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	//MAMenu->Open(MAMenu->IsOpen());
	return FReply::Handled();
}

