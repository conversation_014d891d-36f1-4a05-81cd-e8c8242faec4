// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LayoutBaseWidget.h"
#include "DesignStation/UI/Style/OptionWidget.h"
#include "DesignStation/UI/Style/SeeImageWidget.h"
#include "DesignStation/UI/Style/ValueMenuWidget.h"
#include "DesignStation/UI/Style/ValueWidget.h"
#include "StyleLayoutWidget.generated.h"

/**
 *
 */

class UButton;
class UCanvasPanel;
class UBorder;
class UDragBorderWidget;
class UMenuAnchor;
class USizeBox;
class UOverlay;
class UMergeProcessWidget;
UCLASS()
class DESIGNSTATION_API UStyleLayoutWidget : public ULayoutBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	virtual void NativeOnInitialized() override;

	virtual void AddFolderWidget(UUserWidget* InStyleWidget) override;
	virtual void AddFileListWidget(UUserWidget* InOptionWidget) override;
	virtual void AddPropertyWidget(UUserWidget* InValueWidget) override;

	void ResetStyleSelectState();

	static UStyleLayoutWidget* Create();

	UFUNCTION()
		void RemoveProcess();
	UFUNCTION()
		void UploadSuccess(bool IsTrue);

	UFUNCTION(BlueprintCallable, Category = "StyleLayout")
		void OnStyleRelease();

public:
	void UpdateSubStyleWidget();
	void UpdateSubOptionWidget();
	void UpdateSubValueWidget();

	void InitSubWidget();
private:

	void BindDelegate();
	UFUNCTION()
	void OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg);
	/*UFUNCTION()
	void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);*/

	void SetCPSlotSuitContent(UCanvasPanelSlot* InSlot, bool IsStyleWidget = true);
	void InitResizeWidget();
	UFUNCTION()
		void OnLeftResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnRightResizeHandler(bool IsDrag, const FPointerEvent& MouseEvent);

	void UpdateStyleValueContent(const FString& StyleID, const FString& OptionID);
	UFUNCTION()
		void OnStyleItemSelectHandler(const FString& StyleID);
	UFUNCTION()
		void OnStyleItemDeleteHandler();
	UFUNCTION()
		void OnOptionItemSelectHandler(const FString& StyleID);
	UFUNCTION()
		void OnOptionItemDeleteHandler();

private:
	UPROPERTY()
		UStyleWidget* SubStyleWidget;
	UPROPERTY()
		UOptionWidget* SubOptionWidget;
	UPROPERTY()
		UValueWidget* SubValueWidget;

	UPROPERTY()
		FDecorateSelection RightMenuCopySelection;
	//UPROPERTY()
	//	TArray<FDecorateParam> ValueParams;
	UPROPERTY()
		UValueWidgetChild* RightMenuWidget;

private:
	UPROPERTY()
		UDragBorderWidget* LeftDragWidget;
	UPROPERTY()
		UDragBorderWidget* RightDragWidget;

	static FString StyleLayoutBp;

	FString DownloadUUID;
	FString DownloadOverUUID;
	FString UploadUUID;
	FString MergeLogUUID;
	FString DownloadFileUUID;

	UPROPERTY()
	FString ReleaseUUID = TEXT("NoWay");
	UPROPERTY()
	FString SaveToRelease = TEXT("NoWay");

	UPROPERTY()
		TArray<FString> FilesToUpload;
protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)override;

protected:
	UFUNCTION()
		void OnClickedBtnClose();
	UFUNCTION()
		void OnClickedBtnUpload();
	UFUNCTION()
		void OnClickedBtnDownload();
	UFUNCTION()
		void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
		void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);
	UFUNCTION()
		void ShowRightMenu(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos);
	UFUNCTION()
		UValueMenuWidget* CreateRightMenu();
	UFUNCTION()
		void GetMenuOption(const int32& option);
	UFUNCTION()
		void ShowStyleImage(const FString& Path);
private:
	UPROPERTY()
		UButton* BtnClose;
	UPROPERTY()
		UCanvasPanel* CPStyle;
	UPROPERTY()
		UBorder* BorOption;
	UPROPERTY()
		UCanvasPanel* CPValue;
	UPROPERTY()
		UMenuAnchor* MAMenu;
	UPROPERTY()
		UCanvasPanel* CPMenu;
	UPROPERTY()
		USizeBox* SBMenu;
	UPROPERTY()
		USizeBox* SBImage;
	UPROPERTY()
		UButton* BtnUpload;
	UPROPERTY()
		UButton* BtnDownload;

	UPROPERTY()
		UValueMenuWidget* ValueMenuWidget;
	UPROPERTY()
		USeeImageWidget* SeeImageWidget;
	UPROPERTY()
		UMergeProcessWidget* MergeProcessWidget;

public:
	FStyleOpTypeDelegate RightMenuOptionDelegate;
};
