// Fill out your copyright notice in the Description page of Project Settings.

#include "LoadPageWidget.h"

#include "Components/Border.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"

FString ULoadPageWidget::LoadPageWidgetPath = TEXT("WidgetBlueprint'/Game/UI/LoadUI/LoadPageWidgetUI.LoadPageWidgetUI_C'");
ULoadPageWidget* ULoadPageWidget::SingleInstance = NULL;

bool ULoadPageWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(Sb<PERSON><PERSON><PERSON>, Sb_<PERSON>);
	BIND_PARAM_CPP_TO_UMG(SbSuccess, Sb_Success);
	BIND_PARAM_CPP_TO_UMG(SbFailure, Sb_Failure);
	BIND_PARAM_CPP_TO_UMG(TxtTitle, Txt_Title);
	BIND_PARAM_CPP_TO_UMG(PbLoading, Pb_Loading);
	BIND_PARAM_CPP_TO_UMG(PbSuccess, Pb_Success);
	BIND_PARAM_CPP_TO_UMG(PbFailure, Pb_Failure);
	BIND_PARAM_CPP_TO_UMG(BorBack, Bor_Back);

	return true;
}

ULoadPageWidget* ULoadPageWidget::GetInstance()
{
	if (!IS_OBJECT_PTR_VALID(SingleInstance))
	{
		ULoadPageWidget::SingleInstance = ULoadPageWidget::Create();
		ULoadPageWidget::SingleInstance->AddToViewport(10.0f);
	}
	return ULoadPageWidget::SingleInstance;
}

ULoadPageWidget* ULoadPageWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<ULoadPageWidget>(ULoadPageWidget::LoadPageWidgetPath);
}

void ULoadPageWidget::InitLoadPage()
{
	SbNormal->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	SbSuccess->SetVisibility(ESlateVisibility::Collapsed);
	SbFailure->SetVisibility(ESlateVisibility::Collapsed);
	PbLoading->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	PbSuccess->SetVisibility(ESlateVisibility::Collapsed);
	PbFailure->SetVisibility(ESlateVisibility::Collapsed);
	TxtTitle->SetText(FText::FromString(TEXT("Loading")));
}

void ULoadPageWidget::SetLoadingPercent(const float& perc)
{
	PbLoading->SetPercent(perc);
}

float ULoadPageWidget::GetCurrentPercent()
{
	return PbLoading->GetPercent();
}

void ULoadPageWidget::LoadDone(bool bSuccess)
{
	if (bSuccess)
	{
		PbLoading->SetPercent(1.0f);
		PbSuccess->SetPercent(1.0f);
		TxtTitle->SetText(FText::FromString(TEXT("Load Success")));
		SbNormal->SetVisibility(ESlateVisibility::Collapsed);
		SbSuccess->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		SbFailure->SetVisibility(ESlateVisibility::Collapsed);
		PbLoading->SetVisibility(ESlateVisibility::Collapsed);
		PbSuccess->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		PbFailure->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		PbFailure->SetPercent(SingleInstance->PbLoading->GetPercent());
		TxtTitle->SetText(FText::FromString(TEXT("Load Failure")));
		SbNormal->SetVisibility(ESlateVisibility::Collapsed);
		SbSuccess->SetVisibility(ESlateVisibility::Collapsed);
		SbFailure->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
		PbLoading->SetVisibility(ESlateVisibility::Collapsed);
		PbSuccess->SetVisibility(ESlateVisibility::Collapsed);
		PbFailure->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	}
}

void ULoadPageWidget::SetBackgroundColor(bool IsLightColor)
{
	IsLightColor ? BorBack->SetBrushColor(FLinearColor(1.0f,1.0f,1.0f,1.0f)) : BorBack->SetBrushColor(FLinearColor(0.072272f, 0.072272f, 0.072272f,0.68f));
}
