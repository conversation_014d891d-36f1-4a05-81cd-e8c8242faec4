// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "LoadPageWidget.generated.h"

/**
 * 
 */

class USizeBox;
class UProgressBar;
class UTextBlock;
class UBorder;
UCLASS()
class DESIGNSTATION_API ULoadPageWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	static ULoadPageWidget* GetInstance();

private:
	virtual bool Initialize() override;
	static ULoadPageWidget* Create();

private:
	static FString LoadPageWidgetPath;
	static ULoadPageWidget* SingleInstance;

public:
	void InitLoadPage();
	void SetLoadingPercent(const float& perc);
	float GetCurrentPercent();
	void LoadDone(bool bSuccess);
	void SetBackgroundColor(bool IsLightColor = true);
private:
	UPROPERTY()
		USizeBox* SbNormal;
	UPROPERTY()
		USizeBox* SbSuccess;
	UPROPERTY()
		USizeBox* SbFailure;
	UPROPERTY()
		UTextBlock* TxtTitle;
	UPROPERTY()
		UProgressBar* PbLoading;
	UPROPERTY()
		UProgressBar* PbSuccess;
	UPROPERTY()
		UProgressBar* PbFailure;
	UPROPERTY()
		UBorder*	BorBack;
};