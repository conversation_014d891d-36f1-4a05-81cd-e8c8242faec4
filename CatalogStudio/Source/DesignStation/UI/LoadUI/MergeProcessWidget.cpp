// Fill out your copyright notice in the Description page of Project Settings.

#include "MergeProcessWidget.h"

#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"

FString UMergeProcessWidget::BpPath = TEXT("WidgetBlueprint'/Game/UI/LoadUI/MergeProcessWidgetUI.MergeProcessWidgetUI_C'");

bool UMergeProcessWidget::Initialize()
{
    if (!Super::Initialize())
    {
        return false;
    }
    BIND_PARAM_CPP_TO_UMG(PgPec, pg_pec);
    BIND_PARAM_CPP_TO_UMG(TxtPec, txt_pec);
    BIND_PARAM_CPP_TO_UMG(ButtonClose, But<PERSON>_Close);
    ButtonClose->SetVisibility(ESlateVisibility::Collapsed);

    return true;
}

UMergeProcessWidget* UMergeProcessWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMergeProcessWidget>(UMergeProcessWidget::BpPath);
}

void UMergeProcessWidget::SetPercent(const float& InPec)
{
    PgPec->SetPercent(InPec);
    TxtPec->SetText(FText::FromString(FString::FromInt(InPec * 100.f)));
}

