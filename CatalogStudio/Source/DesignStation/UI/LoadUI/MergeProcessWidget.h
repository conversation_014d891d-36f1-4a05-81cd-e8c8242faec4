// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MergeProcessWidget.generated.h"

/**
 * 
 */

class UProgressBar;
class UTextBlock;
class UButton;
UCLASS()
class DESIGNSTATION_API UMergeProcessWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	bool Initialize() override;

	static UMergeProcessWidget* Create();
private:
	static FString BpPath;
public:
	void SetPercent(const float& InPec);

	UFUNCTION(BlueprintImplementableEvent)
		void UploadOrDownload(bool bUpload);

	UFUNCTION(BlueprintImplementableEvent)
		void SetSuccess(bool bTure);
public:
	UPROPERTY()
		UProgressBar* PgPec;
	UPROPERTY()
		UTextBlock* TxtPec;
	UPROPERTY()
		UButton* ButtonClose;
};
