// Fill out your copyright notice in the Description page of Project Settings.

#include "OperationOnHoldWidget.h"

#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"

FString UOperationOnHoldWidget::OperationOnHoldWidgetPath = TEXT("WidgetBlueprint'/Game/UI/LoadUI/OperationOnHoldWidgetUI.OperationOnHoldWidgetUI_C'");
UOperationOnHoldWidget* UOperationOnHoldWidget::SingleInstance = NULL;

bool UOperationOnHoldWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(TBTitle, TB_Title);
	BIND_PARAM_CPP_TO_UMG(TBOperation, TB_Operation);
	BIND_PARAM_CPP_TO_UMG(PbPaste, Pb_Cut);
	BIND_PARAM_CPP_TO_UMG(PbSuccess, Pb_Success);
	BIND_PARAM_CPP_TO_UMG(PbDeleting, Pb_Deleting);

	return true;
}

UOperationOnHoldWidget* UOperationOnHoldWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UOperationOnHoldWidget>(UOperationOnHoldWidget::OperationOnHoldWidgetPath);
}

UOperationOnHoldWidget* UOperationOnHoldWidget::GetInstance()
{
	if (!IS_OBJECT_PTR_VALID(SingleInstance))
	{
		UOperationOnHoldWidget::SingleInstance = UOperationOnHoldWidget::Create();
		UOperationOnHoldWidget::SingleInstance->AddToViewport(400.0f);
	}
	return UOperationOnHoldWidget::SingleInstance;
}

/*0 for success 1 for paste 2 for delete*/
void UOperationOnHoldWidget::SwitchState(int32 InState)
{
	switch (InState)
	{
	case 0:
		TBTitle->SetText(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")));
		TBOperation->SetText(FText::FromStringTable(FName("PosSt"), TEXT("Done")));
		PbSuccess->SetPercent(1.0f);
		PbSuccess->SetVisibility(ESlateVisibility::Visible);
		PbPaste->SetVisibility(ESlateVisibility::Collapsed);
		PbDeleting->SetVisibility(ESlateVisibility::Collapsed);
		break;
	case 1:
		PbSuccess->SetVisibility(ESlateVisibility::Collapsed);
		SetOperationText(TEXT("Paste"));
		PbPaste->SetPercent(0.75f);
		PbPaste->SetVisibility(ESlateVisibility::Visible);
		PbDeleting->SetVisibility(ESlateVisibility::Collapsed);
		break;
	case 2:
		PbSuccess->SetVisibility(ESlateVisibility::Collapsed);
		PbPaste->SetVisibility(ESlateVisibility::Collapsed);
		SetOperationText(TEXT("Delete"));
		PbDeleting->SetPercent(0.25f);
		PbDeleting->SetVisibility(ESlateVisibility::Visible);
		break;
	default:
		PbSuccess->SetPercent(0.5f);
		PbSuccess->SetVisibility(ESlateVisibility::Visible);
		PbPaste->SetVisibility(ESlateVisibility::Collapsed);
		PbDeleting->SetVisibility(ESlateVisibility::Collapsed);
		break;
	}
}

void UOperationOnHoldWidget::SetOperationText(const FString& InString)
{
	TBTitle->SetText(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")));
	FString Action = FText::FromStringTable(FName("PosSt"), TEXT("Working on")).ToString() + FText::FromStringTable(FName("PosSt"), InString).ToString();
	TBOperation->SetText(FText::FromString(Action));
}

void UOperationOnHoldWidget::SetOperator(int32 InState /*= 0*/, const FString& InString /*= TEXT("")*/)
{
	this->SwitchState(InState);
	this->SetOperationText(InString);
	this->SetVisibility(InState == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
}
