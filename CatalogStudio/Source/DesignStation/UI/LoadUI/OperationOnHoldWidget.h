// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "OperationOnHoldWidget.generated.h"

class UProgressBar;
class UTextBlock;

UCLASS()
class DESIGNSTATION_API UOperationOnHoldWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UOperationOnHoldWidget* Create();
	static UOperationOnHoldWidget* GetInstance();

	/*0 for success 1 for paste 2 for delete*/
	void SwitchState(int32 InState);

	void SetOperationText(const FString& InString);

	void SetOperator(int32 InState = 0, const FString& InString = TEXT(""));

private:
	static FString OperationOnHoldWidgetPath;
	static UOperationOnHoldWidget* SingleInstance;

private:
	UPROPERTY()
		UTextBlock* TBTitle;
	UPROPERTY()
		UTextBlock* TBOperation;
	UPROPERTY()
		UProgressBar* PbPaste;
	UPROPERTY()
		UProgressBar* PbSuccess;
	UPROPERTY()
		UProgressBar* PbDeleting;
};
