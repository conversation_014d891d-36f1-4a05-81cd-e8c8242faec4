// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

/*
 * @@ use for multi thread
 */
#define CALL_THREAD_INNER(EventRef, InTaskDelegate, OtherTask, CallThreadName) \
EventRef = FSimpleDelegateGraphTask::CreateAndDispatchWhenReady(InTaskDelegate, TStatId(), OtherTask, CallThreadName)

#define CALL_THREAD_ARRAY_INNER(EventRef, InTaskDelegate, OtherTaskArray, CallThreadName) \
EventRef = FSimpleDelegateGraphTask::CreateAndDispatchWhenReady(InTaskDelegate, TStatId(), &OtherTaskArray, CallThreadName)

#define CALL_THREAD_LAMBDA_OUT(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateL<PERSON>bda(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_STATIC_OUT(EventRef, OtherTask, CallThreadName, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateStatic(Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UFUNCTION_OUT(EventRef, OtherTask, CallThreadName, Object, Method, ...) \
CALL_THREAD_INNER(EventRef, FSimpleDelegate::CreateUFunction(Object, Method,##__VA_ARGS__), OtherTask, CallThreadName)

#define CALL_THREAD_UFUNCTION_ARRAY_OUT(EventRef, OtherTaskArray, CallThreadName, Object, Method, ...) \
CALL_THREAD_ARRAY_INNER(EventRef, FSimpleDelegate::CreateUFunction(Object, Method,##__VA_ARGS__), OtherTaskArray, CallThreadName)