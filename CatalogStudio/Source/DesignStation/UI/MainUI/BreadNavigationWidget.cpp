// Fill out your copyright notice in the Description page of Project Settings.

#include "BreadNavigationWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_FolderItem.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"

FString UBreadNavigationWidget::BreadNavigationPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/BreadNavigationUI.BreadNavigationUI_C'");

bool UBreadNavigationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BtnOne, Btn_One);
	BIND_WIDGET_FUNCTION(BtnOne, OnClicked, UBreadNavigationWidget::OnClickedBtnOne);
	BIND_PARAM_CPP_TO_UMG(TxtOne, Txt_One);
	BIND_PARAM_CPP_TO_UMG(ImgOne, Img_One);

	BIND_PARAM_CPP_TO_UMG(BtnTwo, Btn_Two);
	BIND_WIDGET_FUNCTION(BtnTwo, OnClicked, UBreadNavigationWidget::OnClickedBtnTwo);
	BIND_PARAM_CPP_TO_UMG(TxtTwo, Txt_Two);
	BIND_PARAM_CPP_TO_UMG(ImgTwo, Img_Two);

	BIND_PARAM_CPP_TO_UMG(BtnThree, Btn_Three);
	BIND_WIDGET_FUNCTION(BtnThree, OnClicked, UBreadNavigationWidget::OnClickedBtnThree);
	BIND_PARAM_CPP_TO_UMG(TxtThree, Txt_Three);
	BIND_PARAM_CPP_TO_UMG(ImgThree, Img_Three);

	BIND_PARAM_CPP_TO_UMG(BtnFour, Btn_Four);
	BIND_WIDGET_FUNCTION(BtnFour, OnClicked, UBreadNavigationWidget::OnClickedBtnFour);
	BIND_PARAM_CPP_TO_UMG(TxtFour, Txt_Four);
	BIND_PARAM_CPP_TO_UMG(ImgFour, Img_Four);

	BIND_PARAM_CPP_TO_UMG(BtnFive, Btn_Five);
	BIND_WIDGET_FUNCTION(BtnFive, OnClicked, UBreadNavigationWidget::OnClickedBtnFive);
	BIND_PARAM_CPP_TO_UMG(TxtFive, Txt_Five);
	BIND_PARAM_CPP_TO_UMG(ImgFive, Img_Five);

	BIND_PARAM_CPP_TO_UMG(BtnSix, Btn_Six);
	BIND_WIDGET_FUNCTION(BtnSix, OnClicked, UBreadNavigationWidget::OnClickedBtnSix);
	BIND_PARAM_CPP_TO_UMG(TxtSix, Txt_Six);

	return true;
}

void UBreadNavigationWidget::UpdateContent(UWidget_FolderItem * SelectFolder, UWidget_FileItem* SelectFile)
{
	if (!IS_OBJECT_PTR_VALID(SelectFolder))
	{
		UpdateNavigationShow(0);
		return;
	}

	NavigationWidgets.Empty();
	if (IS_OBJECT_PTR_VALID(SelectFolder))
	{
		NavigationWidgets.EmplaceAt(0, SelectFolder);
		auto ParentFolderWidget = SelectFolder->ParentFolderWidget.Get();
		while (IS_OBJECT_PTR_VALID(ParentFolderWidget))
		{
			NavigationWidgets.EmplaceAt(0, ParentFolderWidget);
			ParentFolderWidget = ParentFolderWidget->ParentFolderWidget.Get();
		}
	}
	UpdateNavigationShow(NavigationWidgets.Num());
	UpdateNavigationTxtShow();
	LastNavigationItem = SelectFile;
	if (IS_OBJECT_PTR_VALID(LastNavigationItem))
	{
		UpdateNavigationShowByFile(NavigationWidgets.Num(), LastNavigationItem->GetItemData().folder_name);
	}
}

UBreadNavigationWidget * UBreadNavigationWidget::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UBreadNavigationWidget::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UBreadNavigationWidget>(UBreadNavigationWidget::BreadNavigationPath);
}

void UBreadNavigationWidget::UpdateNavigationShow(const int32 & Num)
{
	if (IS_OBJECT_PTR_VALID(BtnOne) && IS_OBJECT_PTR_VALID(ImgOne))
	{
		BtnOne->SetVisibility((Num >= 1) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		ImgOne->SetVisibility((Num >= 1) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (IS_OBJECT_PTR_VALID(BtnTwo) && IS_OBJECT_PTR_VALID(ImgTwo))
	{
		BtnTwo->SetVisibility((Num >= 2) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		ImgTwo->SetVisibility((Num >= 2) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (IS_OBJECT_PTR_VALID(BtnThree) && IS_OBJECT_PTR_VALID(ImgThree))
	{
		BtnThree->SetVisibility((Num >= 3) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		ImgThree->SetVisibility((Num >= 3) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (IS_OBJECT_PTR_VALID(BtnFour) && IS_OBJECT_PTR_VALID(ImgFour))
	{
		BtnFour->SetVisibility((Num >= 4) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		ImgFour->SetVisibility((Num >= 4) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (IS_OBJECT_PTR_VALID(BtnFive) && IS_OBJECT_PTR_VALID(ImgFive))
	{
		BtnFive->SetVisibility((Num >= 5) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		ImgFive->SetVisibility((Num >= 5) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
	if (IS_OBJECT_PTR_VALID(BtnSix))
	{
		BtnSix->SetVisibility((Num >= 6) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

void UBreadNavigationWidget::UpdateNavigationShowByFile(const int32 & FolderNum, const FString & FileName)
{
	if (FolderNum == 1)
	{
		ImgOne->SetVisibility(ESlateVisibility::Visible);
		BtnTwo->SetVisibility(ESlateVisibility::Visible);
		TxtTwo->SetText(FText::FromString(FileName));
	}
	else if(FolderNum == 2)
	{
		ImgTwo->SetVisibility(ESlateVisibility::Visible);
		BtnThree->SetVisibility(ESlateVisibility::Visible);
		TxtThree->SetText(FText::FromString(FileName));
	}
	else if (FolderNum == 3)
	{
		ImgThree->SetVisibility(ESlateVisibility::Visible);
		BtnFour->SetVisibility(ESlateVisibility::Visible);
		TxtFour->SetText(FText::FromString(FileName));
	}
	else if (FolderNum == 4)
	{
		ImgFour->SetVisibility(ESlateVisibility::Visible);
		BtnFive->SetVisibility(ESlateVisibility::Visible);
		TxtFive->SetText(FText::FromString(FileName));
	}
	else if (FolderNum == 5)
	{
		ImgFive->SetVisibility(ESlateVisibility::Visible);
		BtnSix->SetVisibility(ESlateVisibility::Visible);
		TxtSix->SetText(FText::FromString(FileName));
	}
}

void UBreadNavigationWidget::UpdateNavigationTxtShow()
{
	if (NavigationWidgets.IsValidIndex(0) && IS_OBJECT_PTR_VALID(TxtOne))
	{
		TxtOne->SetText(FText::FromString(NavigationWidgets[0]->GetItemData().folder_name));
	}
	if (NavigationWidgets.IsValidIndex(1) && IS_OBJECT_PTR_VALID(TxtTwo))
	{
		TxtTwo->SetText(FText::FromString(NavigationWidgets[1]->GetItemData().folder_name));
	}
	if (NavigationWidgets.IsValidIndex(2) && IS_OBJECT_PTR_VALID(TxtThree))
	{
		TxtThree->SetText(FText::FromString(NavigationWidgets[2]->GetItemData().folder_name));
	}
	if (NavigationWidgets.IsValidIndex(3) && IS_OBJECT_PTR_VALID(TxtFour))
	{
		TxtFour->SetText(FText::FromString(NavigationWidgets[3]->GetItemData().folder_name));
	}
	if (NavigationWidgets.IsValidIndex(4) && IS_OBJECT_PTR_VALID(TxtFive))
	{
		TxtFive->SetText(FText::FromString(NavigationWidgets[4]->GetItemData().folder_name));
	}
	if (NavigationWidgets.IsValidIndex(5) && IS_OBJECT_PTR_VALID(TxtSix))
	{
		TxtSix->SetText(FText::FromString(NavigationWidgets[4]->GetItemData().folder_name));
	}
}

void UBreadNavigationWidget::OnClickedBtnOne()
{
	if (NavigationWidgets.IsValidIndex(0))
	{
		BreadClickDelegate.ExecuteIfBound(NavigationWidgets[0]);
	}
}

void UBreadNavigationWidget::OnClickedBtnTwo()
{
	if (NavigationWidgets.IsValidIndex(1))
	{
		BreadClickDelegate.ExecuteIfBound(NavigationWidgets[1]);
	}
}

void UBreadNavigationWidget::OnClickedBtnThree()
{
	if (NavigationWidgets.IsValidIndex(2))
	{
		BreadClickDelegate.ExecuteIfBound(NavigationWidgets[2]);
	}
}

void UBreadNavigationWidget::OnClickedBtnFour()
{
	if (NavigationWidgets.IsValidIndex(3))
	{
		BreadClickDelegate.ExecuteIfBound(NavigationWidgets[3]);
	}
}

void UBreadNavigationWidget::OnClickedBtnFive()
{
	if (NavigationWidgets.IsValidIndex(4))
	{
		BreadClickDelegate.ExecuteIfBound(NavigationWidgets[4]);
	}
}

void UBreadNavigationWidget::OnClickedBtnSix()
{
}
