// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "BreadNavigationWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;
class UTextBlock;
class UWidget_FolderItem;
class UWidget_FileItem;

DECLARE_DYNAMIC_DELEGATE_OneParam(FBreadClickDelegate, UWidget_FolderItem*, SelectFolder);

UCLASS()
class DESIGNSTATION_API UBreadNavigationWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(UWidget_FolderItem* SelectFolder, UWidget_FileItem* SelectFile = nullptr);
	
	static UBreadNavigationWidget* Create();

private:
	void UpdateNavigationShow(const int32& Num);
	void UpdateNavigationShowByFile(const int32& FolderNum, const FString& FileName);
	void UpdateNavigationTxtShow();

public:
	FBreadClickDelegate BreadClickDelegate;

private:
	UPROPERTY()
		TArray<UWidget_FolderItem*> NavigationWidgets;
	UPROPERTY()
		UWidget_FileItem* LastNavigationItem;

	static FString BreadNavigationPath;

protected:
	UFUNCTION()
		void OnClickedBtnOne();
	UFUNCTION()
		void OnClickedBtnTwo();
	UFUNCTION()
		void OnClickedBtnThree();
	UFUNCTION()
		void OnClickedBtnFour();
	UFUNCTION()
		void OnClickedBtnFive();
	UFUNCTION()
		void OnClickedBtnSix();

private:
	UPROPERTY()
		UButton* BtnOne;
	UPROPERTY()
		UTextBlock* TxtOne;
	UPROPERTY()
		UImage* ImgOne;

	UPROPERTY()
		UButton* BtnTwo;
	UPROPERTY()
		UTextBlock* TxtTwo;
	UPROPERTY()
		UImage* ImgTwo;

	UPROPERTY()
		UButton* BtnThree;
	UPROPERTY()
		UTextBlock* TxtThree;
	UPROPERTY()
		UImage* ImgThree;

	UPROPERTY()
		UButton* BtnFour;
	UPROPERTY()
		UTextBlock* TxtFour;
	UPROPERTY()
		UImage* ImgFour;

	UPROPERTY()
		UButton* BtnFive;
	UPROPERTY()
		UTextBlock* TxtFive;
	UPROPERTY()
		UImage* ImgFive;

	UPROPERTY()
		UButton* BtnSix;
	UPROPERTY()
		UTextBlock* TxtSix;
};
