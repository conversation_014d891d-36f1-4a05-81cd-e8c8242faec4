// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderAndFileListWidget.h"

#include "FolderWidget.h"
#include "Components/Border.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_ContentFileItem.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_ContentFolderItem.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_FolderItem.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "Runtime/UMG/Public/Components/WrapBox.h"
#include <Operators/ProtobufOperatorFunctionLibrary.h>

extern const FString RootParentID;

bool UFolderAndFileListWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}
	if (UWrapBox* WP_Content = Cast<UWrapBox>(GetWidgetFromName(TEXT("WP_Content"))))
	{
		WPContent = WP_Content;
	}
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_SLATE_WIDGET_FUNCTION(BorBackground, OnMouseButtonDownEvent, FName(TEXT("OnBorBackgroundClicked")));

	ParentFolder = nullptr;

	BindDelegate();

	return true;
}

void UFolderAndFileListWidget::UpdateContent(const FString& FolderId, const FString& SyncID)
{
	UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---update view content, folder id : %s, sync id : %s"), *FolderId, *SyncID);
	ContentFolders.Empty();
	ContentFiles.Empty();
	if (WPContent)
	{
		WPContent->ClearChildren();
	}
	if (FolderId.Equals(RootParentID))
	{
		return;
	}
	SyncItemID = SyncID;

	TArray<FFolderTableData> ContentDatas;
#ifdef USE_REF_LOCAL_FILE
	/*const FRefToLocalFileData CurRefData = UFolderWidget::Get()->GetSelectLocalRefData();
	FString BackendFolderDirectory = URefRelationFunction::GetFolderDirectory(CurRefData.FolderDBData.backend_directory, CurRefData.FolderDBData.is_folder);
	if(!UFolderWidget::Get()->GetCacheDataForDirectory(BackendFolderDirectory, ContentDatas))
	{
		
	}*/

	FRefDirectoryData FolderData;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(FolderId, FolderData))
	{
		TArray<FRefDirectoryData> SubDatas;
		UFolderWidget::Get()->GetCacheDataForDirectory(FolderData.backendFolderPath, SubDatas);
		URefRelationFunction::ConvertDirctoryDataToDBData(SubDatas, ContentDatas);
	}

#else
	bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(FolderId, EFolderFileSearchType::EFolderAndFile, ContentDatas);
#endif

	GenerateContentViews(ContentDatas);

}

void UFolderAndFileListWidget::UpdateContent(UWidget_FolderItem* SelectFolder, const FString& SyncID)
{
	UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---update view content with folder"));
	ContentFolders.Empty();
	ContentFiles.Empty();
	if (WPContent)
	{
		WPContent->ClearChildren();
	}
	if (!IS_OBJECT_PTR_VALID(SelectFolder))
	{
		return;
	}
	SyncItemID = SyncID;

	TArray<FFolderTableData> ContentDatas = SelectFolder->GetSubDatas();

	GenerateContentViews(ContentDatas);
}

void UFolderAndFileListWidget::UpdateParentFolder(UWidget_FolderItem * InParentFolder)
{
	ParentFolder = InParentFolder;
}

void UFolderAndFileListWidget::SyncSelectFile(const FString & InFileId)
{
	for (auto& FileItem : ContentFiles)
	{
		if (FileItem->GetFolderItemId().Equals(InFileId))
		{
			FileItem->SetIsSelected(true);
			CurrentViewItem = FileItem;
			CurrentViewType = static_cast<int32>(ELayoutItemType::ViewFileItem);
			UpdateSelectFile(FileItem);
			break;
		}
	}
}

void UFolderAndFileListWidget::SyncSelectItem()
{
	/*FFolderTableData FolderData;
	if (UFolderTableOperatorLibrary::SelectFolderDataByID(CurrentViewItem->GetFolderItemId(), FolderData))
	{
		if (Cast<UWidget_ContentFileItem>(CurrentViewItem))
		{
			Cast<UWidget_ContentFileItem>(CurrentViewItem)->UpdateFileName(FolderData.folder_name, FolderData.thumbnail_path);
			SelectFileDelegate.ExecuteIfBound(CurrentViewItem->GetFolderItemId());
		}
	}*/
}

void UFolderAndFileListWidget::SyncSelectViewItemName(const FString& FolderId, const FString & NewName, bool IsFolder, bool IsVisibility)
{
	if (IS_OBJECT_PTR_VALID(CurrentViewItem))
	{
		if (!(CurrentViewItem->GetFolderItemId().Equals(FolderId)))
		{
			UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---folder not equal, select id : %s // Property panel id : %s"), *CurrentViewItem->GetFolderItemId(), *FolderId);
			return;
		}
	}
	if (!ParentFolder.IsValid())
		return;

	if (ParentFolder.Get()->GetItemData().id.Equals(FolderId))
	{
		for (int32 i = 0; i < ContentFolders.Num(); ++i)
		{
			ContentFolders[i]->SetSuperiorVisible(ParentFolder.Get()->GetCurrentVisibleState());
			ContentFolders[i]->RefreshVisibleState();
			//ContentFolders[i]->UpdateFolderState(IsVisibility && ParentFolder.Get()->GetSuperiorVisible());
		}
		for (int32 i = 0; i < ContentFiles.Num(); ++i)
		{
			ContentFiles[i]->SetSuperiorVisible(ParentFolder.Get()->GetCurrentVisibleState());
			ContentFiles[i]->RefreshVisibleState();
			//ContentFiles[i]->UpdateFolderState(IsVisibility && ParentFolder.Get()->GetSuperiorVisible());
		}
		return;
	}
	if (IsFolder)
	{
		for (auto& FolderItem : ContentFolders)
		{
			if (FolderItem->GetFolderItemId().Equals(FolderId))
			{
				FolderItem->UpdateFolderName(NewName);
				FolderItem->UpdateFolderState(IsVisibility);
				break;
			}
		}
	}
	else
	{
		for (auto& FileItem : ContentFiles)
		{
			if (FileItem->GetFolderItemId().Equals(FolderId))
			{
				FileItem->UpdateFileName(NewName);
				FileItem->UpdateFolderState(IsVisibility);
				break;
			}
		}
	}
}

void UFolderAndFileListWidget::ResetCurrentWidgetState()
{
	if (CurrentViewItem)
	{
		if (CurrentViewType == static_cast<int32>(ELayoutItemType::ViewFolderItem))
		{
			Cast<UWidget_ContentFolderItem>(CurrentViewItem)->SetIsSelected(false);
		}
		else if(CurrentViewType == static_cast<int32>(ELayoutItemType::ViewFileItem))
		{
			Cast<UWidget_ContentFileItem>(CurrentViewItem)->SetIsSelected(false);
		}
	}
}

void UFolderAndFileListWidget::UpdateCurrentViewWidget(const FString & InName, bool IsFolder)
{
	if (CurrentViewItem)
	{
		if (IsFolder)
		{
			Cast<UWidget_ContentFolderItem>(CurrentViewItem)->UpdateFolderName(InName);
		}
		else
		{
			Cast<UWidget_ContentFileItem>(CurrentViewItem)->UpdateFileName(InName, TEXT(""));
		}
	}
}

void UFolderAndFileListWidget::DeleteViewItem(const FString& ItemId, bool IsFolder, bool IsSelected)
{
	if (ItemId.Equals(RootParentID))
	{
		return;
	}
	UE_LOG(LogTemp, Log, TEXT("list action id : %s, is folder : %d, is selected : %d"), *ItemId, IsFolder, IsSelected);
	if (IsFolder)
	{
		for (auto& ViewItem : ContentFolders)
		{
			if (ItemId.Equals(ViewItem->GetViewItemData().id))
			{
				ViewItem->SetVisibility(ESlateVisibility::Collapsed);
				ViewItem->RemoveFromParent();
				break;
			}
		}
	}
	else
	{
		for (auto& ViewItem : ContentFiles)
		{
			if (ItemId.Equals(ViewItem->GetViewItemData().id))
			{
				ViewItem->SetVisibility(ESlateVisibility::Collapsed);
				ViewItem->RemoveFromParent();
				break;
			}
		}
	}

	if (IsSelected)
	{
		ClearListContent();
		CurrentViewItem = nullptr;
		CurrentViewType = static_cast<int>(ELayoutItemType::None);
	}
}

void UFolderAndFileListWidget::ClearListContent()
{
	ContentFolders.Empty();
	ContentFiles.Empty();
	if (WPContent)
	{
		WPContent->ClearChildren();
	}
	CurrentViewItem = nullptr;
	CurrentViewType = -1;
}

void UFolderAndFileListWidget::FolderFileCopyFinishAction(const FString& ActionReturnID)
{
	if (IsInGameThread())
	{
		CopyDirectActionByGameThread(ActionReturnID);
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread,
			[this]()->void 
		{
			this->ViewListRightActionDelegate.ExecuteIfBound(FFolderTableData(), static_cast<int32>(ERightMenuType::Paste));
		}
		);
	}
}

void UFolderAndFileListWidget::CopyDirectActionByGameThread(const FString& ActionID)
{
	ViewListRightActionDelegate.ExecuteIfBound(FFolderTableData(), static_cast<int32>(ERightMenuType::Paste));
}

void UFolderAndFileListWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryCopyResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileListWidget::OnCopyActionResponseHandle);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryCutResponseDelegate.AddUniqueDynamic(this, &UFolderAndFileListWidget::OnCutActionResponseHandle);
}

void UFolderAndFileListWidget::OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if(NetUUID.CopyUUID.Equals(UUID))
	{
		NetUUID.ResetCopyAction();
		if(bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewListRightActionDelegate.ExecuteIfBound(FFolderTableData(), static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UFolderAndFileListWidget::OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (NetUUID.CutUUID.Equals(UUID))
	{
		NetUUID.ResetCutAction();
		if (bSuccess /*&& DirData.IsValidIndex(0)*/)
		{
			OPERATOR_SUCCESS();

			//�޸��ļ������·��
			FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(DirData[0]);
			FRefToLocalFileData CurFileData;
			if (UFolderWidget::Get()->GetCacheDataForFile(MarkID, CurFileData))
			{
				CurFileData.FolderDBData.backend_directory = DirData[0].backendFolderPath;

				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(MarkID);
				const FString RefFilePath = FPaths::ConvertRelativePathToFull(
					FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath)
				);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, CurFileData);
				UFolderWidget::Get()->UploadFileRequest(FileRelativePath);

			}

			ViewListRightActionDelegate.ExecuteIfBound(FFolderTableData(), static_cast<int32>(ERightMenuType::Paste));
		}
		else
		{
			OPERATOR_FAILED_WITH_POP_MSG(Msg);
		}
	}
}

void UFolderAndFileListWidget::SelectItemEdit(UUserWidget* InWidget, const int32& ItemType)
{
	UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---select item edit"));
	FFolderTableData SelectFolderData;
	if (ItemType == static_cast<int32>(ELayoutItemType::ViewFolderItem))
	{
		if (CurrentViewItem == Cast<UWidget_ContentFolderItem>(InWidget))
		{
			return;
		}
		ResetCurrentWidgetState();
		CurrentViewItem = Cast<UWidget_ContentFolderItem>(InWidget);
		Cast<UWidget_ContentFolderItem>(InWidget)->SetIsSelected(true);
		CurrentViewType = ItemType;
		SelectFolderData = Cast<UWidget_ContentFolderItem>(InWidget)->GetViewItemData();
	}
	else if (ItemType == static_cast<int32>(ELayoutItemType::ViewFileItem))
	{
		if (CurrentViewItem == Cast<UWidget_ContentFileItem>(InWidget))
		{
			return;
		}
		ResetCurrentWidgetState();
		CurrentViewItem = Cast<UWidget_ContentFileItem>(InWidget);
		UpdateSelectFile(CurrentViewItem);
		Cast<UWidget_ContentFileItem>(InWidget)->SetIsSelected(true);
		CurrentViewType = ItemType;
		SelectFolderData = Cast<UWidget_ContentFileItem>(InWidget)->GetViewItemData();
	}
	SelectFileDelegate.ExecuteIfBound(SelectFolderData);
}

void UFolderAndFileListWidget::OpenItemEdit(const FFolderTableData& ViewItemData)
{
	UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---open name : %s"), *ViewItemData.folder_name);
	//ResetCurrentWidgetState();
	OpenFileDelegate.ExecuteIfBound(ViewItemData);
}

void UFolderAndFileListWidget::ViewItemRightActionHandler(const FFolderTableData & ViewItemData, const int32 & ActionType)
{
	UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---right action name : %s, id : %s"), *ViewItemData.folder_name, *ViewItemData.id);
	ViewListRightActionDelegate.ExecuteIfBound(ViewItemData, ActionType);
}

void UFolderAndFileListWidget::GenerateContentViews(const TArray<FFolderTableData>& OutParams)
{
	for (int i = 0; i < OutParams.Num(); ++i)
	{
		if (OutParams[i].deletable)
		{
			UE_LOG(LogTemp, Log, TEXT("FolderAndFileListWidget---sub folder id/name : %s/%s is delete"), *OutParams[i].id, *OutParams[i].folder_name);
			continue;
		}
		if (OutParams[i].can_add_subfolder)
		{
			if (UWidget_ContentFolderItem* ContentFolderItem = Cast<UWidget_ContentFolderItem>(UFolderFunctionLibrary::CreateFolderLayoutItem((int)ELayoutItemType::ViewFolderItem)))
			{
				ContentFolderItem->FolderSelectDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
				ContentFolderItem->FolderOpenDelegate.BindUFunction(this, FName(TEXT("OpenItemEdit")));
				ContentFolderItem->ViewItemRightActionDelegate.BindUFunction(this, FName(TEXT("ViewItemRightActionHandler")));
				ContentFolderItem->UpdateContent(OutParams[i]);
				ContentFolderItem->SetSuperiorVisible(ParentFolder.Get()->GetCurrentVisibleState());
				ContentFolderItem->UpdateFolderState(!FMath::IsNearlyZero(OutParams[i].visibility));
				ContentFolderItem->SetFolderItemId(OutParams[i].id);
				ContentFolders.Add(ContentFolderItem);
				WPContent->AddChild(ContentFolderItem);
			}
		}
		else
		{
			if (UWidget_ContentFileItem* ContentFileItem = Cast<UWidget_ContentFileItem>(UFolderFunctionLibrary::CreateFolderLayoutItem((int)ELayoutItemType::ViewFileItem)))
			{
				ContentFileItem->FileSelectDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
				ContentFileItem->FileOpenDelegate.BindUFunction(this, FName(TEXT("OpenItemEdit")));
				ContentFileItem->ViewItemRightActionDelegate.BindUFunction(this, FName(TEXT("ViewItemRightActionHandler")));
				ContentFileItem->UpdateContent(OutParams[i]);
				ContentFileItem->SetSuperiorVisible(ParentFolder.Get()->GetCurrentVisibleState());
				ContentFileItem->UpdateFolderState(!FMath::IsNearlyZero(OutParams[i].visibility));
				ContentFileItem->SetFolderItemId(OutParams[i].id);
				//ContentFileItem->SetIsSelected(SyncItemID == -1 ? false : (SyncItemID == OutParams[i].id ? true : false));
				//ContentFileItem->SetFolderType((int32)SubFolderData[i].folder_type);
				ContentFiles.Add(ContentFileItem);
				WPContent->AddChild(ContentFileItem);
			}
		}
	}

	if (!SyncItemID.Equals(RootParentID))
	{
		bool IsSync = false;
		for (auto& ViewItem : ContentFiles)
		{
			if (SyncItemID.Equals(ViewItem->GetViewItemData().id))
			{
				IsSync = true;
				ViewItem->SetIsSelected(true);
				CurrentViewItem = ViewItem;
				UpdateSelectFile(ViewItem);//CATALOG-1351
				CurrentViewType = static_cast<int32>(ELayoutItemType::ViewFileItem);
				break;
			}
		}
		if (!IsSync)
		{
			for (auto& ViewItem : ContentFolders)
			{
				if (SyncItemID.Equals(ViewItem->GetViewItemData().id))
				{
					ViewItem->SetIsSelected(true);
					CurrentViewItem = ViewItem;
					CurrentViewType = static_cast<int32>(ELayoutItemType::ViewFolderItem);
					break;
				}

			}
		}
	}
	else
	{
		CurrentViewItem = nullptr;
		CurrentViewType = static_cast<int32>(ELayoutItemType::None);
	}
}

void UFolderAndFileListWidget::UpdateViewContent()
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderAndFileListWidget::UpdateViewContent()"));
	if (WPContent)
	{
		WPContent->ClearChildren();
		for (int32 i = 0; i < ContentFolders.Num(); ++i)
		{
			WPContent->AddChild(ContentFolders[i]);
		}
		for (int32 i = 0; i < ContentFiles.Num(); ++i)
		{
			WPContent->AddChild(ContentFiles[i]);
		}
	}
}

FEventReply UFolderAndFileListWidget::OnBorBackgroundClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	bool bIsRightMouseButton = EKeys::RightMouseButton == MouseEvent.GetEffectingButton();
	if (bIsRightMouseButton)
	{
		URightMouseMenuWidget* RightMenuWidget = URightMouseMenuWidget::Create();
		RightMenuWidget->AddToViewport(10);
		RightMenuWidget->UpdateContent(MouseEvent.GetScreenSpacePosition());
		RightMenuWidget->RightMenuDelegate.BindUFunction(this, FName(TEXT("OnRightMenuClickHandler")));
		RightMenuWidget->SetVisibility(ESlateVisibility::Visible);
	}
	return FEventReply(bIsRightMouseButton);
}

void UFolderAndFileListWidget::OnRightMenuClickHandler(const ERightMenuType & ActionType)
{
	if (ActionType == ERightMenuType::Copy)
	{
	}
	else if (ActionType == ERightMenuType::Shear)
	{
	}
	else if (ActionType == ERightMenuType::Paste)
	{
		if (UFolderWidget::Get()->GetActionID().Equals(RootParentID))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Warning"), FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Shear First!!")).ToString());
			return;
		}
		if (!URefRelationFunction::IsSameRootDirectory(UFolderWidget::Get()->GetActionPath(), ParentFolder.Get()->GetItemData().backend_folder_path))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Error"), FText::FromStringTable(FName("PosSt"), TEXT("Can not paste to different type folder!!")).ToString());
			return;
		}

		UE_LOG(LogTemp, Log, TEXT("Creating On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(1);
		OnHold->SetOperationText(TEXT("Paste"));
		OnHold->SetVisibility(ESlateVisibility::Visible);

		bool IsSuccess = true;
		FFolderTableData NewActionData;
		if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Copy))
		{
#ifdef USE_REF_LOCAL_FILE

			FString OriginID = UFolderWidget::Get()->GetActionID();
			FString TargetID = ParentFolder.Get()->GetItemData().id;
			if (UFolderWidget::Get()->CanExecuteCopyOperator(OriginID, TargetID))
			{
				NetUUID.CopyUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryCopyRequest(
					/*UFolderWidget::Get()->GetActionID()*/OriginID,
					/*ParentFolder.Get()->GetItemData().id*/TargetID
				);
			}
			else
			{
				//no need copy action, reset state
				UI_POP_WINDOW_ERROR_ST(TEXT("can not copy upper folder to child"));
				OPERATOR_SUCCESS();
			}

#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCopyLogic(this, UFolderWidget::Get()->GetActionID(), ParentFolder.Get()->GetItemData(), NewActionData);
#endif
		}
		else if (UFolderWidget::Get()->GetActionType() == static_cast<int32>(ERightMenuType::Shear))
		{
#ifdef USE_REF_LOCAL_FILE
			FRefDirectoryData CutModifyData;
			bool Res = UFolderWidget::Get()->ConstructCutActionData(UFolderWidget::Get()->GetActionID(), ParentFolder.Get()->GetItemData().id, CutModifyData);
			if (Res)
			{
				NetUUID.CutUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryCutRequest(CutModifyData);
			}
			else
			{
				UI_POP_WINDOW_ERROR(TEXT("UFolderAndFileListWidget::OnRightMenuClickHandler --- cut error"));
			}
#else
			IsSuccess = UFolderTableOperatorLibrary::FolderFileCut(UFolderWidget::Get()->GetActionID(), ParentFolder.Get()->GetItemData(), NewActionData);
#endif
		}

#ifdef USE_REF_LOCAL_FILE
#else
		if (IsSuccess)
		{
			ViewListRightActionDelegate.ExecuteIfBound(FFolderTableData(), static_cast<int32>(ERightMenuType::Paste));
		}
#endif
	}
	else if (ActionType == ERightMenuType::Delete)
	{
	}
}
