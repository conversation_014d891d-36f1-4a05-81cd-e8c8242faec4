// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DesignStation/UI/FolderLayoutUI/FolderAndFileBaseWidget.h"
#include "FolderAndFileListWidget.generated.h"

/**
 * 
 */

class UWrapBox;
class UBorder;
class UWidget_ContentFolderItem;
class UWidget_ContentFileItem;
class UWidget_FolderItem;

//DECLARE_DYNAMIC_DELEGATE_OneParam(FOpenFileDelegate, const FFolderTableData&, FolderID);
DECLARE_DYNAMIC_DELEGATE_OneParam(FSelectFileDelegate, const FFolderTableData&, FolderID);

UCLASS()
class DESIGNSTATION_API UFolderAndFileListWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void UpdateContent(const FString& FolderId, const FString& SyncID = TEXT("-1"));
	void UpdateContent(UWidget_FolderItem* SelectFolder, const FString& SyncID = TEXT("-1"));
	void UpdateParentFolder(UWidget_FolderItem* InParentFolder);
	void SyncSelectFile(const FString& InFileId);
	void SyncSelectItem();
	void SyncSelectViewItemName(const FString& FolderId, const FString& NewName, bool IsFolder, bool IsVisibility);
	void ResetCurrentWidgetState();
	void UpdateCurrentViewWidget(const FString& InName, bool IsFolder);
	void DeleteViewItem(const FString& ItemId, bool IsFolder, bool IsSelected);
	void ClearListContent();

	UFUNCTION(BlueprintImplementableEvent, Category = "FolderAndFileList")
		void UpdateSelectFile(UFolderAndFileBaseWidget* CurrentViewItem);

	UFUNCTION()
		void FolderFileCopyFinishAction(const FString& ActionReturnID);
	void CopyDirectActionByGameThread(const FString& ActionID);

protected:
	void BindDelegate();

	UFUNCTION()
	void OnCopyActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData);

	UFUNCTION()
	void OnCutActionResponseHandle(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData);

protected:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

private:
	FString ViewSearchUUID;
	FString CutOrCopyUUID;
	FString SyncItemID;

protected:
	UFUNCTION()
		void SelectItemEdit(UUserWidget* InWidget, const int32& ItemType);
	UFUNCTION()
		void OpenItemEdit(const FFolderTableData& ViewItemData);
	UFUNCTION()
		void ViewItemRightActionHandler(const FFolderTableData& ViewItemData, const int32& ActionType);

	void GenerateContentViews(const TArray<FFolderTableData>& OutParams);

private:
	void UpdateViewContent();

private:
	UPROPERTY()
		TArray<UWidget_ContentFolderItem*> ContentFolders;
	UPROPERTY()
		TArray<UWidget_ContentFileItem*> ContentFiles;

	UPROPERTY()
		UFolderAndFileBaseWidget* CurrentViewItem;
	int32 CurrentViewType;

	TWeakObjectPtr<UWidget_FolderItem> ParentFolder;

protected:
	UFUNCTION()
		FEventReply OnBorBackgroundClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent);
	UFUNCTION()
		void OnRightMenuClickHandler(const ERightMenuType& ActionType);

private:
	UPROPERTY()
		UBorder* BorBackground;
	UPROPERTY()
		UWrapBox* WPContent;

public:
	FViewFolderOrFileOpenedDelegate OpenFileDelegate;
	FSelectFileDelegate SelectFileDelegate;
	FViewItemRightActionDelegate ViewListRightActionDelegate;
};
