// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderAndFilePropertyWidget.h"

#include "FolderWidget.h"
#include "Components/CanvasPanel.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/FrontDirectory/FrontDirectoryWidget.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/UI/Obsurce/ObsurceLayoutWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "DesignStation/UI/PlaceRule/PlaceRuleCustomWidget.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/EditableTextBox.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/Core/Public/Internationalization/Regex.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"

#define LOCTEXT_NAMESPACE "FolderAndFileProperty"

extern const FString ParamShowFlag;
extern const FString ParamHiddenFlag;
extern const FString ParamEditableFlag = TEXT("1");
extern const FString ParamUnEditableFlag = TEXT("0");

extern const FString RootParentID;

#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

bool UFolderAndFilePropertyWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	

	return true;
}

void UFolderAndFilePropertyWidget::NativeOnInitialized()
{
	BIND_PARAM_CPP_TO_UMG(SZID, SZ_ID);
	BIND_PARAM_CPP_TO_UMG(EdtId, Edt_Id);
	//BIND_WIDGET_FUNCTION(EdtId, OnTextChanged, UFolderAndFilePropertyWidget::OnTextChangedEdtId);
	BIND_WIDGET_FUNCTION(EdtId, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtId);
	BIND_PARAM_CPP_TO_UMG(SZCoding, SZ_Coding);
	BIND_PARAM_CPP_TO_UMG(EdtCoding, Edt_Coding);
	BIND_WIDGET_FUNCTION(EdtCoding, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtCoding);
	BIND_PARAM_CPP_TO_UMG(EdtCodeExp, Edt_CodeExp);
	BIND_WIDGET_FUNCTION(EdtCodeExp, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtCodeExp);

	BIND_PARAM_CPP_TO_UMG(BtnCodingExp, Btn_CodingExp);
	BIND_WIDGET_FUNCTION(BtnCodingExp, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnCodingExp);

	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtName);
	BIND_PARAM_CPP_TO_UMG(EdtNameExpress, Edt_NameExpress);
	BIND_WIDGET_FUNCTION(EdtNameExpress, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtNameExpress);
	BIND_PARAM_CPP_TO_UMG(BtnNameExpress, Btn_NameExpress);
	BIND_WIDGET_FUNCTION(BtnNameExpress, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnNameExpress);

	BIND_PARAM_CPP_TO_UMG(BtnDelParam, Btn_DelParam);
	BIND_WIDGET_FUNCTION(BtnDelParam, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnDelParam);
	BIND_PARAM_CPP_TO_UMG(BtnAddParam, Btn_AddParam);
	BIND_WIDGET_FUNCTION(BtnAddParam, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnAddParam);

	BIND_PARAM_CPP_TO_UMG(SBParams, SB_Params);

	BIND_PARAM_CPP_TO_UMG(BtnVisiExpress, Btn_VisiExpress);
	BIND_PARAM_CPP_TO_UMG(EdtVisiExpress, Edt_VisiExpress);
	BIND_PARAM_CPP_TO_UMG(EdtVisiValue, Edt_VisiValue);
	BIND_WIDGET_FUNCTION(EdtVisiExpress, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtVisiExpress);
	BIND_WIDGET_FUNCTION(EdtVisiValue, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtVisiValue);
	BIND_WIDGET_FUNCTION(BtnVisiExpress, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnVisible);
	BIND_WIDGET_FUNCTION(CB_IsNew, OnCheckStateChanged, UFolderAndFilePropertyWidget::OnIsNewCheckStateChanged);

	BIND_WIDGET_FUNCTION(Btn_Property, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnProperty);
	BIND_WIDGET_FUNCTION(Btn_Associate, OnClicked, UFolderAndFilePropertyWidget::OnClickedBtnAssociate);

	BIND_WIDGET_FUNCTION(Edt_Description, OnTextCommitted, UFolderAndFilePropertyWidget::OnTextCommittedEdtDescription);

	BIND_WIDGET_FUNCTION(Btn_Up, OnClicked, UFolderAndFilePropertyWidget::OnClickBtnUP);
	BIND_WIDGET_FUNCTION(Btn_Down, OnClicked, UFolderAndFilePropertyWidget::OnClickBtnDown);

	ParentParameters = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
	FolderParamsMap = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
	FolderParams = TArray<UFolderParamWidget*>();
	InsertEnumParams = TArray<FEnumParameterTableData>();
	DeleteEnumParams = TArray<FEnumParameterTableData>();
	InsertParams = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
	DeleteParams = TArray<FParameterData>();
	OldVisibleType = true;
	SelectParam = nullptr;
	IsNewBorderShow = false;

	BindDelegate();
}

void UFolderAndFilePropertyWidget::UpdateContent(const FString& InFolderId)
{
	TArray<FFolderTableData> FolderDatas;
	FString ErrorMessage;
	/*if (UFolderTableOperatorLibrary::SelectFolderData(InFolderId, FolderDatas, ErrorMessage))
	{
		if (FolderDatas.Num() > 0)
		{
			UpdateContent(FolderDatas[0]);
		}
	}*/
}

void UFolderAndFilePropertyWidget::UpdateContent(const FFolderTableData& InData)
{
	if (InData.id.IsEmpty()) return;

	bool IsSameData = FolderData.id.Equals(InData.id, ESearchCase::IgnoreCase);
	FolderData = InData;

	ResetShowProperty();

#ifdef USE_REF_LOCAL_FILE

	FRefToLocalFileData RefData = UFolderWidget::Get()->GetSelectLocalRefData();
	if (!RefData.FolderDBData.id.Equals(InData.id, ESearchCase::IgnoreCase))
	{//select not equal, need read file to get data
		FString SearchID = (InData.folder_id.IsEmpty() || InData.can_add_subfolder) ? InData.id : InData.folder_id;
		UFolderWidget::Get()->GetCacheDataForFile(SearchID, RefData);
	}
	GenerateFolderFileParameters(RefData.ParamDatas);
	UpdateMappingShow(RefData.FolderDBData.front_directory);
	UpdateSelectPlaceRule(FolderData.placeRule);
	Edt_Description->SetText(FText::FromString(InData.description));

#else

	FolderData.CopyData(InData);



	if (IS_OBJECT_PTR_VALID(SZID) && IS_OBJECT_PTR_VALID(SZCoding))
	{
		SZID->SetVisibility(FolderData.can_add_subfolder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		SZCoding->SetVisibility(FolderData.can_add_subfolder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
	UpdatePropertyWidgetState(FolderData.parent_id.Equals(RootParentID));
	if (IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtId) && IS_OBJECT_PTR_VALID(EdtCoding) && IS_OBJECT_PTR_VALID(EdtCodeExp))
	{
		EdtName->SetText(FText::FromString(FolderData.folder_name));
		EdtId->SetText(FText::FromString(FolderData.folder_id));
		EdtCoding->SetText(FText::FromString(FolderData.folder_code));
		EdtCodeExp->SetText(FText::FromString(FolderData.folder_code_exp));
	}
	if (IS_OBJECT_PTR_VALID(CB_IsNew))
	{
		IsNewBorderShow = !FolderData.can_add_subfolder;
		//CB_IsNew->SetVisibility(FolderData.can_add_subfolder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		//if (!FolderData.can_add_subfolder)
		CB_IsNew->SetCheckedState(FolderData.is_new ? ECheckBoxState::Checked : ECheckBoxState::Unchecked);
	}
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisiValue))
	{
		EdtVisiExpress->SetText(FText::FromString(FolderData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(FolderData.visibility)));
	}

	//SwitchVisibleType(InData.visibility ? EFolderOrFileVisibleType::Show : EFolderOrFileVisibleType::Hidden);
	//SwitchVisibleWidgetEnable(!(InData.folder_type == EFolderType::EMaterial)); Fix bug 解决材质文件夹无法修改可见性的逻辑，为硬装做准备

	UpdateFolderParamContent(InData.id, InData.can_add_subfolder);

#endif

	//UpdatePraentParameters(InData.parent_id);
	DeleteParams.Empty();
	InsertParams.Empty();
	DeleteEnumParams.Empty();
	InsertEnumParams.Empty();
	if (IS_OBJECT_PTR_VALID(SelectParam) && !IsSameData)
	{
		SelectParam->SetIsSelect(false);
		SelectParam = nullptr;
	}
}

void UFolderAndFilePropertyWidget::RefreshProperty(const FFolderTableData& InData)
{
	FolderData = InData;
	if (IS_OBJECT_PTR_VALID(SZID) && IS_OBJECT_PTR_VALID(SZCoding))
	{
		SZID->SetVisibility(InData.can_add_subfolder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		SZCoding->SetVisibility(InData.can_add_subfolder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
	UpdatePropertyWidgetState(InData.parent_id.Equals(RootParentID));
	if (IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtNameExpress) && IS_OBJECT_PTR_VALID(EdtId) && IS_OBJECT_PTR_VALID(EdtCoding) && IS_OBJECT_PTR_VALID(EdtCodeExp))
	{
		EdtName->SetText(FText::FromString(InData.folder_name));
		EdtNameExpress->SetText(FText::FromString(InData.folder_name_exp));
		EdtId->SetText(FText::FromString(InData.folder_id));
		EdtCoding->SetText(FText::FromString(InData.folder_code));
		EdtCodeExp->SetText(FText::FromString(InData.folder_code_exp));
	}
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisiValue))
	{
		EdtVisiExpress->SetText(FText::FromString(InData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(InData.visibility)));
	}

	Edt_Description->SetText(FText::FromString(InData.description));
}

bool UFolderAndFilePropertyWidget::RefreshFolderProperty()
{
	/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, PeerParameters);
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;

	FString OutValue;
	FString OutExpression;
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, PeerParameters, FolderData.visibility_exp, OutValue, OutExpression);
	bool VisiChange = false;
	if (Res)
	{
		VisiChange = !FMath::IsNearlyEqual(FolderData.visibility, FCString::Atof(*OutValue), 0.01f);
		FolderData.visibility_exp = OutExpression;
		FolderData.visibility = FMath::IsNearlyZero(FCString::Atof(*OutValue), 0.01f) ? 0.0f : FCString::Atof(*OutValue);
		EdtVisiExpress->SetText(FText::FromString(FolderData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(FolderData.visibility)));
	}
	FString OutCodeValue;
	FString OutCodeExpression;
	bool CodingRes = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, PeerParameters, FolderData.folder_code_exp, OutCodeValue, OutCodeExpression);
	bool CodeChange = false;
	auto NewData = FolderData;
	if (CodingRes)
	{
		CodeChange = !FolderData.folder_code.Equals(OutCodeValue);

		NewData.folder_code = OutCodeValue;
		NewData.folder_code_exp = OutCodeExpression;
		EdtCoding->SetText(FText::FromString(NewData.folder_code));
		EdtCodeExp->SetText(FText::FromString(NewData.folder_code_exp));
	}*/

	auto NewData = FolderData;
	TArray<FParameterData> TempParams;
	FolderParamsMap.GenerateValueArray(TempParams);
	bool IsPropertyChange = IsPropertyChangeWhenParamsEdit(TempParams, NewData);
	bool IsSizeChange = IsSizeChangeWhenParamsEdit(TempParams, NewData);
	if (IsPropertyChange || IsSizeChange)
	{
		SendUpdateDataRequest(NewData);
	}
	else
	{
		RefreshProperty(FolderData);
	}
	return IsPropertyChange || IsSizeChange;


//	if (VisiChange || CodeChange)
//	{
//#ifdef USE_REF_LOCAL_FILE
//		SendUpdateDataRequest(NewData);
//#else
//		UpdateFolderData(FolderData);
//#endif
//	}
//	else
//	{
//		RefreshProperty(FolderData);
//	}
//
//	return VisiChange || CodeChange;
}

void UFolderAndFilePropertyWidget::SyncDataInfoWhenParamsEdit()
{
	auto NewData = FolderData;
	if (IsPropertyChangeWhenParamsEdit(ParemtersToSave, NewData)
		|| IsSizeChangeWhenParamsEdit(ParemtersToSave, NewData))
	{
		FRefDirectoryData Directory;
		if (UFolderWidget::Get()->GetCacheDataForRefDirectory(NewData.id, Directory))
		{
			URefRelationFunction::ConvertDBDataToDirctoryData(NewData, Directory);
			UFolderWidget::Get()->AddCacheDataForRefDirectory(Directory);
		}
	}
}

bool UFolderAndFilePropertyWidget::IsPropertyChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FFolderTableData& NewData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();;

	auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(NewParams);

	FString OutValue;
	FString OutExpression;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, CombineParams, NewData.visibility_exp, OutValue, OutExpression);
	bool VisiChange = false;
	if (Res)
	{
		VisiChange = !FMath::IsNearlyEqual(FolderData.visibility, FCString::Atof(*OutValue), 0.01f);
		NewData.visibility_exp = OutExpression;
		NewData.visibility = FMath::IsNearlyZero(FCString::Atof(*OutValue), 0.01f) ? 0.0f : FCString::Atof(*OutValue);

	}
	FString OutCodeValue;
	FString OutCodeExpression;
	bool CodingRes = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, CombineParams, NewData.folder_code_exp, OutCodeValue, OutCodeExpression);
	bool CodeChange = false;

	if (CodingRes)
	{
		CodeChange = !FolderData.folder_code.Equals(OutCodeValue, ESearchCase::CaseSensitive);

		NewData.folder_code = OutCodeValue;
		NewData.folder_code_exp = OutCodeExpression;
	}

	bool NameChange = false;
	if (NewData.folder_name_exp.IsEmpty() && !NewData.folder_name.IsEmpty())
	{
		NewData.folder_name_exp = TEXT("\"") + NewData.folder_name + TEXT("\"");
		NameChange = true;
	}
	else
	{
		FString OutNameValue;
		FString OutNameExpression;
		bool NameRes = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, CombineParams, NewData.folder_name_exp, OutNameValue, OutNameExpression);

		if (NameRes)
		{
			NameChange = !FolderData.folder_name.Equals(OutNameValue, ESearchCase::CaseSensitive);

			NewData.folder_name = OutNameValue;
			NewData.folder_name_exp = OutNameExpression;
		}
	}

	return (VisiChange || CodeChange || NameChange);

}

bool UFolderAndFilePropertyWidget::IsSizeChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FRefDirectoryData& RefData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, NewParams, PeerParameters);
	FString NewWidth, NewHeight, NewDepth;
	URefRelationFunction::GetSizeInfo(ACatalogPlayerController::Get()->GetGlobalParameterMap(), PeerParameters, NewWidth, NewHeight, NewDepth);
	bool WidthChange = !FMath::IsNearlyEqual(FCString::Atod(*NewWidth), FCString::Atod(*RefData.width), 0.01);
	bool HeightChange = !FMath::IsNearlyEqual(FCString::Atod(*NewHeight), FCString::Atod(*RefData.height), 0.01);
	bool DepthChange = !FMath::IsNearlyEqual(FCString::Atod(*NewDepth), FCString::Atod(*RefData.depth), 0.01);
	bool Change = WidthChange || HeightChange || DepthChange;
	if (Change)
	{
		RefData.width = NewWidth;
		RefData.height = NewHeight;
		RefData.depth = NewDepth;
	}
	return Change;
}

bool UFolderAndFilePropertyWidget::IsSizeChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FFolderTableData& NewData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, NewParams, PeerParameters);
	FString NewWidth, NewHeight, NewDepth;
	URefRelationFunction::GetSizeInfo(ACatalogPlayerController::Get()->GetGlobalParameterMap(), PeerParameters, NewWidth, NewHeight, NewDepth);
	bool WidthChange = !FMath::IsNearlyEqual(FCString::Atod(*NewWidth), FCString::Atod(*NewData.width), 0.01);
	bool HeightChange = !FMath::IsNearlyEqual(FCString::Atod(*NewHeight), FCString::Atod(*NewData.height), 0.01);
	bool DepthChange = !FMath::IsNearlyEqual(FCString::Atod(*NewDepth), FCString::Atod(*NewData.depth), 0.01);
	bool Change = WidthChange || HeightChange || DepthChange;
	if (Change)
	{
		NewData.width = NewWidth;
		NewData.height = NewHeight;
		NewData.depth = NewDepth;
	}
	return Change;
}

//void UFolderAndFilePropertyWidget::UpdateContent(const int32& InFolderId, const int32& InParentFolderId, const FString & FileName, const FString & ThumbnailPath, bool IsVisible)
//{
//	FolderId = InFolderId;
//	if (EdtName)
//	{
//		EdtName->SetText(FText::FromString(FileName));
//	}
//	if(CBSVisible)
//	{
//		OldVisibleType = IsVisible;
//		CBSVisible->SetSelectedOption(IsVisible ? EVisibleType[0] : EVisibleType[1]);
//	}
//	UpdateFolderParamContent(InFolderId);
//	UpdatePraentParameters(InParentFolderId);
//	DeleteParams.Empty();
//	InsertParams.Empty();
//	DeleteEnumParams.Empty();
//	InsertEnumParams.Empty();
//}

void UFolderAndFilePropertyWidget::UpdateFolderData(const FFolderTableData& InData)
{
	if (UFolderTableOperatorLibrary::UpdateFolderFile(InData))
	{
		if (FolderData.id != InData.id)
		{
			return;
		}
		RefreshProperty(InData);
		if (UFolderWidget::Get())
		{
			UFolderWidget::Get()->UpdateSelectFolderData(InData, InData.can_add_subfolder);
		}
		FolderPropertyChangeDelegate.ExecuteIfBound(InData.id, InData.folder_name, InData.can_add_subfolder, !FMath::IsNearlyZero(InData.visibility));
	}
}

void UFolderAndFilePropertyWidget::UpdateParamData(const TArray<FParameterData>& InParam)
{
	if (InParam.Num() <= 0) return;

	if (UFolderTableOperatorLibrary::UpdateFolderFileParameter(InParam, FolderData.can_add_subfolder))
	{
		for (auto& ParamIter : InParam)
		{
			const int32 Index = FolderParams.IndexOfByPredicate([ParamIter](UFolderParamWidget* InOther) { return InOther->GetParamData().Data.name.Equals(ParamIter.Data.name, ESearchCase::CaseSensitive); });
			if (INDEX_NONE != Index)
			{
				FolderParams[Index]->UpdateContent(ParamIter);
				if (FolderParamsMap.Contains(ParamIter.Data.name))
					FolderParamsMap[ParamIter.Data.name] = ParamIter;
				else
					FolderParamsMap.Add(ParamIter.Data.name, ParamIter);
			}
		}
		RefreshFolderProperty();
		return;
	}
}

void UFolderAndFilePropertyWidget::OnIsNewCheckStateChanged(bool IsChecked)
{
	auto NewData = FolderData;
	NewData.is_new = IsChecked;
#ifdef USE_REF_LOCAL_FILE
	SendUpdateDataRequest(NewData);
#else
	UpdateFolderData(FolderData);
#endif
}

void UFolderAndFilePropertyWidget::UpdatePropertyWidgetState(bool IsRootFolder)
{
	if (EdtId && EdtName && CkbShow && CkbHidden && EdtCoding && EdtCodeExp)
	{
		EdtId->SetIsEnabled(!IsRootFolder);
		EdtCoding->SetIsEnabled(!IsRootFolder);
		EdtCodeExp->SetIsEnabled(!IsRootFolder);
		EdtName->SetIsEnabled(!IsRootFolder);
		CkbShow->SetIsEnabled(!IsRootFolder);
		CkbHidden->SetIsEnabled(!IsRootFolder);
	}
}

void UFolderAndFilePropertyWidget::SwitchVisibleType(const EFolderOrFileVisibleType& InType)
{
	if (IS_OBJECT_PTR_VALID(CkbShow) && IS_OBJECT_PTR_VALID(CkbHidden))
	{
		CkbShow->SetIsChecked(InType == EFolderOrFileVisibleType::Show ? true : false);
		CkbHidden->SetIsChecked(InType == EFolderOrFileVisibleType::Hidden ? true : false);
	}
}

void UFolderAndFilePropertyWidget::SwitchVisibleWidgetEnable(bool IsEnable)
{
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisiValue))
	{
		EdtVisiExpress->SetIsEnabled(IsEnable);
		EdtVisiValue->SetIsEnabled(IsEnable);
	}
}

void UFolderAndFilePropertyWidget::FormatParamExpressionChange(FParameterData& ParamData)
{
	FString ErrorMessage;
	FString OutValue;
	FString OutFormatExpression;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

	bool Result = CalculateExpressionValue({}, CombineParams, ParamData.Data.expression, OutValue, OutFormatExpression);
	if (!Result)
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Calculate Expression Wrong!")).ToString());
		return;
	}
	UUIFunctionLibrary::FormatInputValue(OutValue);
	ParamData.Data.value = OutValue;
	ParamData.Data.expression = OutFormatExpression;
}

void UFolderAndFilePropertyWidget::FormatParamValueChange(FParameterData& ParamData)
{
	UUIFunctionLibrary::FormatInputValue(ParamData.Data.value);
	ParamData.Data.expression = ParamData.Data.value;
}

bool UFolderAndFilePropertyWidget::CalculateExpressionValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, const FString& InExpression, FString& OutValue, FString& OutFormatExpress)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	bool CaculateValueResult = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, InParentParameters, InLocalParameters,InExpression, OutValue, OutFormatExpress);
	if (!CaculateValueResult)
	{
		/*SOneButtonWidget::PopupModalWindow(NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorKey", "ERROR"),
			NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorContentKey", "Caculate value wrong!"), NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorButtonKey", "OK"));*/
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		return false;
	}
	return true;
}

void UFolderAndFilePropertyWidget::CreateFolderParamWidget(const FParameterData& InData)
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderAndFilePropertyWidget::CreateFolderParamWidget()"));
	//UFolderParamWidget* FolderParamItem = UUIFunctionLibrary::CreateUIWidget<UFolderParamWidget>();
	UFolderParamWidget* FolderParamItem = UFolderParamWidget::Create();
	checkf(FolderParamItem, TEXT("create folder param item error!"));
	auto GetOverrideParameters = [&](const FParameterData& InParameter)->TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> 
		{
			auto PeerParameters = FolderParamsMap;
			PeerParameters.Remove(InParameter.Data.name);
			return PeerParameters;
		};
	FolderParamItem->UpdateParentsParameters(GetOverrideParameters);
	FolderParamItem->SetFolderOrFileParentParams(ParentParameters);
	FolderParamItem->UpdateContent(InData);
	FolderParamItem->FolderParamDelegate.BindUFunction(this, FName(TEXT("FolderParamsEdit")));
	FolderParamItem->ParamSelectDelegate.BindUFunction(this, FName(TEXT("ParamSelectEdit")));
	//FolderParamItem->RequestParentParamDelegate.BindUFunction(this, FName(TEXT("OnFolderParentParamHandler")));
	FolderParamItem->BtnUpDownClickDelegate.BindUObject(this, &UFolderAndFilePropertyWidget::OnClickBtnUPDown);
	FolderParamItem->SetIsFolderParamNew(false);
	FolderParamItem->SetVisibility(ESlateVisibility::Visible);
	FolderParams.Add(FolderParamItem);
	FolderParamsMap.Add(InData.Data.name, InData);
	if (SBParams)
	{
		SBParams->AddChild(FolderParamItem);
	}

	if (IS_OBJECT_PTR_VALID(SelectParam))
	{
		if (SelectParam->GetParamData().Data.id.Equals(InData.Data.id, ESearchCase::CaseSensitive))
		{
			SelectParam = FolderParamItem;
			SelectParam->SetIsSelect(true);
		}
	}
}

void UFolderAndFilePropertyWidget::UpdateFolderParamContent()
{
	const TArray<FParameterData> FolderFileParams = UFolderWidget::Get()->GetSelectLocalRefData().ParamDatas;
	GenerateFolderFileParameters(FolderFileParams);
}

void UFolderAndFilePropertyWidget::UpdateFolderParamContent(const FString& FolderId, bool IsFolder)
{
	TArray<FParameterData> FolderFileParams;
	bool Res = UFolderTableOperatorLibrary::GetFolderFileParameters(FolderId, IsFolder, FolderFileParams);
	GenerateFolderFileParameters(FolderFileParams);

}


void UFolderAndFilePropertyWidget::UpdateParentsParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters)
{
	ParentParameters = InParentParameters;
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(
		ACatalogPlayerController::Get()->GetGlobalParameterMap(), 
		{}, 
		ParentParameters
	);

}

FRefToLocalFileData UFolderAndFilePropertyWidget::GetPropertyShowLocalFileData()
{
	FRefToLocalFileData RefData = UFolderWidget::Get()->GetSelectLocalRefData();
	if (!RefData.FolderDBData.id.Equals(FolderData.id, ESearchCase::IgnoreCase))
	{//select not equal, need read file to get data
		FString SearchID = URefRelationFunction::GetMarkToBackendDirectory(FolderData);
		UFolderWidget::Get()->GetCacheDataForFile(SearchID, RefData);
	}

	return RefData;
}

UFrontDirectoryWidget* UFolderAndFilePropertyWidget::GetFrontDirectoryWidget()
{
	UFrontDirectoryWidget::GetInstance()->FrontMappingDelegate.BindUFunction(this, FName(TEXT("OnMappingEditHander")));
	//UFrontDirectoryWidget::GetInstance()->SwitchShowType(EFrontDirectoryDataType::E_Mapping);
	return UFrontDirectoryWidget::GetInstance();
}

void UFolderAndFilePropertyWidget::OnMappingEditHander(const FString& NewMapping)
{
	if (!NewMapping.IsEmpty())
	{
		SendUpdateDataRequest(FolderData, NewMapping);
	}

	MappingUIShow(false);
	UFrontDirectoryWidget::GetInstance()->FrontMappingDelegate.Unbind();
}

bool UFolderAndFilePropertyWidget::CanMapping()
{
	return ACatalogPlayerController::Get()->IsAdminLogin() && !FolderData.can_add_subfolder;
}

void UFolderAndFilePropertyWidget::InitPlaceRuleComboBox(const TArray<FString>& PlaceRuleStrArr)
{
	if(CBS_PlaceRule)
	{
		CBS_PlaceRule->ClearOptions();
		for(const auto& PRSA : PlaceRuleStrArr)
		{
			CBS_PlaceRule->AddOption(PRSA);
		}
	}
}

void UFolderAndFilePropertyWidget::UpdateSelectPlaceRule(const int32& InSelectID)
{
	if (CBS_PlaceRule)
	{
		TArray<FRefPlaceRuleData> AllSettingRule = UFolderWidget::Get()->GetPlaceRuleDatas();
		/*TArray<FRefPlaceRuleData> AllSettingRule = UFolderWidget::Get()->GetPlaceRuleDatasByCombineStr(
			{ TEXT("0/left左边right右/0"), TEXT("1/left左边/0"), TEXT("2/right右边12/0"), TEXT("3/upper上边/0"), TEXT("4/down下边/0") }
		);*/
		const int32 Index = AllSettingRule.IndexOfByPredicate(
			[InSelectID](const FRefPlaceRuleData& InOther) 
			{ 
				return InOther.id == InSelectID;
			}
		);
		if (Index != INDEX_NONE)
		{
			CBS_PlaceRule->SetSelectedIndex(Index);
			UpdateUserCanConfig(AllSettingRule[Index]);
		}
		else
		{
			CBS_PlaceRule->ClearSelection();
			bCanModifyPlaceRule = false;
		}


	}
}

void UFolderAndFilePropertyWidget::UpdateUserCanConfig(const FRefPlaceRuleData& InDefaultPlaceRule)
{
	FRefToLocalFileData ThisLocalFile = GetPropertyShowLocalFileData();
	if (ThisLocalFile.IsValid())
	{
		if (ThisLocalFile.HasPlaceRuleUserCustom())
		{
			bCanModifyPlaceRule = ThisLocalFile.UserCustomPlaceRuleConfigFalg() == 0;
		}
		else
		{
			bCanModifyPlaceRule = InDefaultPlaceRule.isConfig == 0;
		}
	}
}

void UFolderAndFilePropertyWidget::UpdateCustomPlaceRuleWidget(const FRefPlaceRuleCustomData& InCustomData)
{
	if(PlaceRuleCustomWidget == nullptr)
	{//init
		PlaceRuleCustomWidget = UPlaceRuleCustomWidget::Create();
		PlaceRuleCustomWidget->ModifyRefPlaceRuleCustomDelegate.BindUFunction(this, FName(TEXT("OnCustomPlaceRuleModifyHandler")));
		PlaceRuleCustomWidget->AddToViewport(10);
	}
	if(PlaceRuleCustomWidget != nullptr)
	{//update
		PlaceRuleCustomWidget->UpdateParams(ParentParameters, FolderParamsMap);
		PlaceRuleCustomWidget->UpdateRuleData(InCustomData);
		PlaceRuleCustomWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UFolderAndFilePropertyWidget::OnCustomPlaceRuleModifyHandler(const FRefPlaceRuleCustomData& ModifyData)
{
	if(ModifyData.IsCustomByUser())
	{//place rule been custom by user
		FRefToLocalFileData ThisLocalFile = GetPropertyShowLocalFileData();
		ThisLocalFile.PlaceRuleCustomData = ModifyData;

		const FString RefFilePathIdentify = URefRelationFunction::GetRefFileRelativePath(ThisLocalFile);
		const FString RefFilePath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathIdentify)
		);
		UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, ThisLocalFile);

		FRefDirectoryData CurRefData;
		if (UFolderWidget::Get()->GetCacheDataForRefDirectory(ThisLocalFile.FolderDBData.id, CurRefData))
		{
			int64 FileSize = 0;
			ACatalogPlayerController::GetFileMD5AndSize(RefFilePath, CurRefData.md5, FileSize);

			/*
			*  @@ update ref file update user
			*/
			CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

			UFolderWidget::Get()->UpdateDataRequest(CurRefData);
			UFolderWidget::Get()->UploadFileRequest(RefFilePathIdentify);

		}

		UFolderWidget::Get()->SyncSelect(ThisLocalFile);
	}
}

void UFolderAndFilePropertyWidget::AfterModifyCustomPlaceRule(const FRefToLocalFileData& FileData)
{
	const FString RefFilePathIdentify = URefRelationFunction::GetRefFileRelativePath(FileData);
	const FString RefFilePath = FPaths::ConvertRelativePathToFull(
		FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathIdentify)
	);
	UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, FileData);

	FRefDirectoryData CurRefData;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(FileData.FolderDBData.id, CurRefData))
	{
		int64 FileSize = 0;
		ACatalogPlayerController::GetFileMD5AndSize(RefFilePath, CurRefData.md5, FileSize);

		/*
		*  @@ update ref file update user
		*/
		CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

		UFolderWidget::Get()->UpdateDataRequest(CurRefData);
		UFolderWidget::Get()->UploadFileRequest(RefFilePathIdentify);

	}
}

void UFolderAndFilePropertyWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &UFolderAndFilePropertyWidget::OnBackendDirectoryUpdateHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataArrForParseResponseDelegate.AddUniqueDynamic(this, &UFolderAndFilePropertyWidget::OnGetFurnitureOrMatDataArrForParseResponseHandler);
}

void UFolderAndFilePropertyWidget::SendUpdateDataRequest(const FFolderTableData& UpdateFolderData, const FString& FrontPath /*= TEXT("")*/)
{
	FRefDirectoryData Data;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(UpdateFolderData.id, Data))
	{
		Data.folderCode = UpdateFolderData.folder_code;
		Data.folderCodeExp = UpdateFolderData.folder_code_exp;
		Data.folderId = UpdateFolderData.folder_id;
		Data.folderName = UpdateFolderData.folder_name;
		Data.folderNameExp = UpdateFolderData.folder_name_exp;

		Data.isNew = UpdateFolderData.is_new;
		Data.visibility = UpdateFolderData.visibility;
		Data.visibilityExp = UpdateFolderData.visibility_exp;
		Data.backendFolderPath = UpdateFolderData.backend_folder_path;

		Data.width = UpdateFolderData.width;
		Data.height = UpdateFolderData.height;
		Data.depth = UpdateFolderData.depth;
		Data.boxOffset = UpdateFolderData.boxOffset;

		if (!FrontPath.IsEmpty())
		{
			Data.fontFolderPath = FrontPath;
		}

		Data.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();
		Data.description = UpdateFolderData.description;

		NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(Data);
	}

}

void UFolderAndFilePropertyWidget::OnBackendDirectoryUpdateHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.UpdateUUID.Equals(UUID))
	{
		NetUUID.ResetUpdateAction();
		if (bSuccess && Datas.IsValidIndex(0))
		{
			if (FolderData.id != Datas[0].id)
			{
				RefreshProperty(FolderData);
				return;
			}

			FFolderTableData InData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], InData);

			URefRelationFunction::RenameLocalFile(
				URefRelationFunction::GetRefFileRelativePath(FolderData),
				URefRelationFunction::GetRefFileRelativePath(InData)
			);

			FolderData = InData;
			RefreshProperty(FolderData);
			if (UFolderWidget::Get())
			{
				UFolderWidget::Get()->SyncSelectData(Datas[0]);
				UFolderWidget::Get()->SyncRefLocalData(Datas[0]);
				UFolderWidget::Get()->UpdateSelectFolderData(InData, InData.can_add_subfolder);
			}

			FolderPropertyChangeDelegate.ExecuteIfBound(InData.id, InData.folder_name, InData.can_add_subfolder, !FMath::IsNearlyZero(InData.visibility));

		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
			RefreshProperty(FolderData);
		}
	}
}

void UFolderAndFilePropertyWidget::SetQueryMatInfo(const FString& MatFolderID)
{
	MatInfo.FolderID = MatFolderID;
	FrontNetUUID.MatDetailToParseAdditionUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetFurnitureOrMaterialDataForParseRequest(
		{ MatFolderID }, 1
	);
}

void UFolderAndFilePropertyWidget::OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData)
{
	if (UUID.Equals(FrontNetUUID.MatDetailToParseAdditionUUID))
	{
		if (!bSuccess)
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
		
		if (NetData.IsValidIndex(0) && NetData[0].folderId.Equals(MatInfo.FolderID, ESearchCase::CaseSensitive))
		{
			MatInfo.SetInfo(NetData[0].name, NetData[0].folderId, NetData[0].folderCode, NetData[0].structData);
		}

		UpdateContent(FolderData);
	}
}

void UFolderAndFilePropertyWidget::FolderParamsEdit(const int32& EditType, UFolderParamWidget* ParamItem)
{
	checkf(ParamItem, TEXT("folder param widget ptr is null"));
	if (false == FolderParamsMap.Contains(ParamItem->GetFolderParamName())) return;
	switch (static_cast<EFolderParamType>(EditType))
	{
	case EFolderParamType::NameChange:
	{
		//FolderParamsMap.Find(ParamItem)->Data.name = ParamItem->GetFolderParamName();
		break;
	}
	case EFolderParamType::ValueChange:
	{
		FParameterData TempParam(ParamItem->GetParamData());

		ParseAffectedParameters(ParemtersToSave, TempParam, false);
		int32 ParmeterIndex = ParemtersToSave.IndexOfByPredicate(
			[TempParam](const FParameterData& p)->bool { return p.Data.name.Equals(TempParam.Data.name, ESearchCase::CaseSensitive); }
		);
		if (ParmeterIndex != INDEX_NONE)
		{
			FText FormatValue = UParameterPropertyData::FormatParameterValue(ParemtersToSave[ParmeterIndex].Data.value);
			/*FolderParamsMap[ParamItem->GetFolderParamName()].Data.value = FormatValue.ToString();
			FolderParamsMap[ParamItem->GetFolderParamName()].Data.expression = FormatValue.ToString();*/
			ParamItem->SetFolderParamValue(FormatValue.ToString());
			ParamItem->SetFolderParamExpression(ParemtersToSave[ParmeterIndex].Data.expression);
		}

		UE_LOG(LogTemp, Error, TEXT("FolderParamsEdit ValueChange"));
#ifdef USE_REF_LOCAL_FILE
		SyncDataInfoWhenParamsEdit();
		UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
		UpdateParamData(ParemtersToSave);
#endif
		ParemtersToSave.Empty();
		break;
	}
	case EFolderParamType::ExpressionChange:
	{
		FParameterData TempParam(ParamItem->GetParamData());
		bool Res = ParseAffectedParameters(ParemtersToSave, TempParam, false);
		if (false == Res)
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
			TempParam = FolderParamsMap[ParamItem->GetFolderParamName()];
			ParamItem->UpdateContent(TempParam);
			return;
		}
		UE_LOG(LogTemp, Error, TEXT("FolderParamsEdit ExpressionChange"));
		int32 ParmeterIndex = ParemtersToSave.IndexOfByPredicate(
			[TempParam](const FParameterData& p)->bool { return p.Data.name.Equals(TempParam.Data.name, ESearchCase::CaseSensitive); }
		);
		if (ParmeterIndex != INDEX_NONE)
		{
			FText FormatValue = UParameterPropertyData::FormatParameterValue(ParemtersToSave[ParmeterIndex].Data.value);

			ParamItem->SetFolderParamValue(FormatValue.ToString());
			ParamItem->SetFolderParamExpression(ParemtersToSave[ParmeterIndex].Data.expression);
		}
#ifdef USE_REF_LOCAL_FILE
		SyncDataInfoWhenParamsEdit();
		UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
		UpdateParamData(ParemtersToSave);
#endif
		ParemtersToSave.Empty();
		break;
	}
	case EFolderParamType::ParamUpdate:
	{
		FParameterData TempParam(ParamItem->GetParamData());
		//auto GlobalParameters = GetWorld()->GetGameInstance<UCatalogStudioGameInstance>()->GetGlobalParameter();
		//bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, TempParam);
		//if (false == Res)
		//{
		//	UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
		//		FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		//	TempParam = FolderParamsMap[ParamItem->GetFolderParamName()];
		//	ParamItem->UpdateContent(TempParam);
		//	return;
		//}
		//{//解析是否存在引用环
		//	TArray<FString> AffectedParameterNames;
		//	FParameterEffectionParser Parser;
		//	auto NewFolderParamsMap = FolderParamsMap;
		//	NewFolderParamsMap[TempParam.Data.name] = TempParam;
		//	bool Res = Parser.FindParametersAffectBySpecificParameter(NewFolderParamsMap, TempParam.Data.name, AffectedParameterNames, true);
		//	if (false == Res)
		//	{//同级存在循环引用
		//		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
		//			FText::FromStringTable(FName("PosSt"), TEXT("Parameter has circle refrence!")).ToString());
		//		TempParam = FolderParamsMap[ParamItem->GetFolderParamName()];
		//		ParamItem->UpdateContent(TempParam);
		//		return;
		//	}
		//}
		//ParamItem->UpdateContent(TempParam);
		//FolderParamsMap[ParamItem->GetFolderParamName()].CopyData(TempParam);
		bool Res = ParseAffectedParameters(ParemtersToSave, TempParam, false);
		if (false == Res)
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
			TempParam = FolderParamsMap[ParamItem->GetFolderParamName()];
			ParamItem->UpdateContent(TempParam);
			return;
		}
		//ParemtersToSave.Add(TempParam);
#ifdef USE_REF_LOCAL_FILE
		SyncDataInfoWhenParamsEdit();
		UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
		UpdateParamData(ParemtersToSave);
#endif
		ParemtersToSave.Empty();
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void UFolderAndFilePropertyWidget::FormatParamsEdit(const int32& EditType, const FString& InData)
{
	switch ((EFormatEditType)EditType)
	{
	case EFormatEditType::Name:
	{
		break;
	}
	case EFormatEditType::Value:
	{
		FString ValueTemp = InData;
		UUIFunctionLibrary::FormatInputValue(ValueTemp);
		UParameterDetailWidget::Get()->FormatValue(ValueTemp);
		break;
	}
	case EFormatEditType::Expression:
	{
		FString ErrorMessage;
		FString OutValue;
		FString OutFormatExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		bool Result = CalculateExpressionValue({}, CombineParams, InData, OutValue, OutFormatExpression);
		if (!Result)
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Calculate Expression Wrong!")).ToString());
			return;
		}
		UUIFunctionLibrary::FormatInputValue(OutValue);
		UParameterDetailWidget::Get()->FormatExpression(OutFormatExpression, OutValue);
		break;
	}
	case EFormatEditType::Visibility:
	{
		FString ErrorMessage;
		FString OutValue;
		FString OutVisibilityExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		bool Result = CalculateExpressionValue({}, CombineParams, InData, OutValue, OutVisibilityExpression);
		UParameterDetailWidget::Get()->SetIsVisibilityValid(Result, OutVisibilityExpression);
		break;
	}
	case EFormatEditType::Editable:
	{
		FString ErrorMessage;
		FString OutValue;
		FString OutEditableExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		bool Result = CalculateExpressionValue({}, CombineParams, InData, OutValue, OutEditableExpression);
		UParameterDetailWidget::Get()->SetIsEditableValid(Result, OutEditableExpression);
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void UFolderAndFilePropertyWidget::ParamSelectEdit(UFolderParamWidget* InParam)
{
	if (IS_OBJECT_PTR_VALID(SelectParam))
	{
		SelectParam->SetIsSelect(false);
	}
	if (IS_OBJECT_PTR_VALID(InParam))
	{
		InParam->SetIsSelect(true);
		SelectParam = InParam;
	}
}

void UFolderAndFilePropertyWidget::OnParamUpdateEditHandler(const FParameterData& ParamData)
{//在文件或文件夹上添加新变量的逻辑
	FParameterData NewParamData;
	NewParamData.CopyData(ParamData);
	NewParamData.Data.main_id = FolderData.id;
	NewParamData.Data.FormatDefaultExpress();
	if (FolderParamsMap.Contains(NewParamData.Data.name))
	{//变量已经被添加过，无法再次添加
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("This Param already Exist, can not add again")).ToString());
		return;
	}
	ParseAffectedParameters(ParemtersToSave, NewParamData, true);
	{//开始保存
#ifdef USE_REF_LOCAL_FILE

#else
		if (UFolderTableOperatorLibrary::CreateFolderFileParameter(NewParamData, FolderData.can_add_subfolder))
#endif
		{
			//因插入时会赋予新UUID，导致后续参数更新失效，在此加入UUID更新
			int32 NewParamDataIndex = ParemtersToSave.IndexOfByPredicate([&](const FParameterData& InOther)->bool { return InOther.Data.name.Equals(NewParamData.Data.name, ESearchCase::CaseSensitive); });
			if (NewParamDataIndex != INDEX_NONE)
			{
				ParemtersToSave[NewParamDataIndex] = NewParamData;
			}
			CreateFolderParamWidget(NewParamData);
			if (ParemtersToSave.Num() > 0)
			{//添加新创建变量成功后需要更新受影响的变量

#ifdef USE_REF_LOCAL_FILE
				SyncDataInfoWhenParamsEdit();
				UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
				UpdateParamData(ParemtersToSave);
#endif

				ParemtersToSave.Empty();
			}
		}
	}
}

void UFolderAndFilePropertyWidget::OnParamUpdateEditHandler02(const TArray<FParameterData>& ParamDatas)
{
	//for (auto Ite : ParamDatas)
	//{
	//	if (FolderParamsMap.Contains(Ite.Data.name))
	//	{
	//		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("This Param already Exist, can not add again")).ToString());
	//		break;
	//	}
	//}

	TArray<FParameterData> TempParamDatas = ParamDatas;
	TempParamDatas.RemoveAll([&](const FParameterData& InData) ->bool {
		return FolderParamsMap.Contains(InData.Data.name);
		});

	for (auto Ite : TempParamDatas)
	{
		OnParamUpdateEditHandler(Ite);
	}
}

bool UFolderAndFilePropertyWidget::ParseAffectedParameters(TArray<FParameterData>& AffectedParameters, FParameterData& ModifyParameter, bool DeleteModifyParameter)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	AffectedParameters.Empty();
	//需要解析同级中受影响的变量
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters = FolderParamsMap;

	if (DeleteModifyParameter)
	{
		if (PeerParameters.Contains(ModifyParameter.Data.name))
			PeerParameters.Remove(ModifyParameter.Data.name);
		else
			PeerParameters.Add(ModifyParameter.Data.name, ModifyParameter);
	}
	else
	{
		if (PeerParameters.Contains(ModifyParameter.Data.name))
			PeerParameters[ModifyParameter.Data.name] = ModifyParameter;
		else
			PeerParameters.Add(ModifyParameter.Data.name, ModifyParameter);
	}
	//测试位置
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, PeerParameters, CombineParams);
	bool Res = UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, {}, CombineParams);

	for (auto& PP : PeerParameters)
	{
		if (CombineParams.Contains(PP.Key))
		{
			PP.Value = CombineParams[PP.Key];
		}
	}

	if (PeerParameters.Contains(ModifyParameter.Data.name))
	{
		ModifyParameter = PeerParameters[ModifyParameter.Data.name];
	}

	for (const auto& iter : PeerParameters)
	{
		AffectedParameters.AddUnique(iter.Value);
	}

	return Res;
}

bool UFolderAndFilePropertyWidget::GenerateFolderFileParameters(TArray<FParameterData> OutParams)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CalculateParentParams = ParentParameters;

	//是否有材质信息
	const int32 MarkIndex = OutParams.IndexOfByPredicate(
		[](const FParameterData& Param)->bool 
		{
			return Param.Data.name.Equals(TEXT("DZCZ"), ESearchCase::CaseSensitive);
		}
	);
	if (MarkIndex != INDEX_NONE)
	{
		if (!OutParams[MarkIndex].Data.value.Equals(MatInfo.FolderID, ESearchCase::CaseSensitive))
		{
			UE_LOG(LogTemp, Log, TEXT("UFolderAndFilePropertyWidget::GenerateFolderFileParameters --- Params Has DZCZ [%s], Need Consider Mat Info"), *OutParams[MarkIndex].Data.value);
			SetQueryMatInfo(OutParams[MarkIndex].Data.value);
			return false;
		}
		else
		{//
			TArray<FString> AdditionMatStr = MatInfo.GetAdditionStrArr();
			for (const auto& AMS : AdditionMatStr)
			{
				FString Exp, Value;
				AMS.Split(TEXT("="), &Exp, &Value);
				if (!Exp.IsEmpty() && !Value.IsEmpty())
				{
					if (CalculateParentParams.Contains(Exp))
					{
						CalculateParentParams[Exp].Data.expression = Value;
						CalculateParentParams[Exp].Data.value = Value;
					}
					else if (GlobalParameters.Contains(Exp))
					{//no param, new param to add
						FParameterData ToAddParam = GlobalParameters[Exp];
						ToAddParam.Data.expression = Value;
						ToAddParam.Data.value = Value;
						CalculateParentParams.Add(Exp, ToAddParam);
					}
				}
			}
		}
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(PeerParameters, OutParams);

	//
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParameters;
	UParameterRelativeLibrary::CombineParameters(CalculateParentParams, PeerParameters, OverrideParameters);

	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, {}, OverrideParameters);

	ParemtersToSave.Empty();
	for (auto& PP : PeerParameters)
	{
		if (OverrideParameters.Contains(PP.Key))
		{
			ParemtersToSave.Add(OverrideParameters[PP.Key]);
		}
	}

	//TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParameters = PeerParameters;
	//PeerParameters.GenerateValueArray(ParemtersToSave);

	if (SBParams)
	{
		SBParams->ClearChildren();
	}
	FolderParams.Empty();
	FolderParamsMap.Empty();
	TArray<FParameterData> NeedSave;
	for (auto& ParamData : OutParams)
	{
		if (OverrideParameters.Contains(ParamData.Data.name))
		{
			CreateFolderParamWidget(OverrideParameters[ParamData.Data.name]);
			if (!OverrideParameters[ParamData.Data.name].Equal_Precise(ParamData))
			{
				NeedSave.Add(OverrideParameters[ParamData.Data.name]);
			}
		}
		else
		{
			CreateFolderParamWidget(ParamData);
		}
	}
	if (NeedSave.Num() > 0/* && ParemtersToSave.Num() > 0*/)
	{
#ifdef USE_REF_LOCAL_FILE
		SyncDataInfoWhenParamsEdit();
		UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
		UpdateParamData(ParemtersToSave);
#endif
		UE_LOG(LogTemp, Log, TEXT("UFolderAndFilePropertyWidget::OnUESearchParametersResponseHandler update parameters %d"), NeedSave.Num());
		ParemtersToSave.Empty();
		return true;
	}
	else
	{//Fix bug CATALOG-1324
		RefreshFolderProperty();
	}

	return false;
}

void UFolderAndFilePropertyWidget::OnFolderParentParamHandler()
{
	/*if (!FolderParentParamMark.IsGet)
	{
		FolderParentParemDelegate.ExecuteIfBound(FolderData.parent_id);
	}*/
}

void UFolderAndFilePropertyWidget::FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression)
{
	if (EditType == (int32)EExpressionFolderType::VisibilityExpression)
	{
		FString OutValue;
		FString OutExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		if (UUIFunctionLibrary::CalculateExpressionValue({}, CombineParams, Expression, OutValue, OutExpression))
		{
			EdtVisiExpress->SetText(FText::FromString(OutExpression));
			EdtVisiValue->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.visibility_exp = OutExpression;
			NewData.visibility = FCString::Atof(*OutValue);
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(NewData);
#endif
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("visibility expression calculate func error"));
			BIND_EXPRESSION_WIDGET(EditType, Expression, FName(TEXT("FolderOrFileExpressionEdit")));
		}
	}
	else if (EditType == (int32)EExpressionFolderType::CodingExpression)
	{
		FString OutValue;
		FString OutExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		if (UUIFunctionLibrary::CalculateExpressionValue({}, CombineParams, Expression, OutValue, OutExpression))
		{
			EdtCodeExp->SetText(FText::FromString(OutExpression));
			EdtCoding->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.folder_code_exp = OutExpression;
			NewData.folder_code = OutValue;
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(FolderData);
#endif
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Code expression calculate func error"));
			BIND_EXPRESSION_WIDGET(EditType, Expression, FName(TEXT("FolderOrFileExpressionEdit")));
		}
	}
	else if (EditType == (int32)EExpressionFolderType::NameExpression)
	{
		FString OutValue;
		FString OutExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		if (UUIFunctionLibrary::CalculateExpressionValue({}, CombineParams, Expression, OutValue, OutExpression))
		{
			EdtNameExpress->SetText(FText::FromString(OutExpression));
			EdtName->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.folder_name_exp = OutExpression;
			NewData.folder_name = OutValue;
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(FolderData);
#endif
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Code expression calculate func error"));
			BIND_EXPRESSION_WIDGET(EditType, Expression, FName(TEXT("FolderOrFileExpressionEdit")));
		}
	}
}

void UFolderAndFilePropertyWidget::RemoveFrontZeroChar(FString& EditString)
{
#define ZERO_CHAR '0'
	if (EditString.Len() > 0)
	{
		while (EditString[0] == ZERO_CHAR)
		{
			EditString.RemoveAt(0);
			if (EditString.IsEmpty())
				return;
		}
	}
#undef ZERO_CHAR
}

void UFolderAndFilePropertyWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	//TODO get current selected folder parent parameters
}

void UFolderAndFilePropertyWidget::OnTextChangedEdtId(const FText& Text)
{
#ifdef  USE_REF_LOCAL_FILE

	IDUnique = true;

#else

	FString IdStr = Text.ToString();
	RemoveFrontZeroChar(IdStr);
	if (IdStr.IsEmpty() || IdStr.Equals(FolderData.folder_id, ESearchCase::CaseSensitive))
	{
		IDUnique = true;
		return;
	}

	FString	CleanData;
	int32 ToInt = FCString::Atoi(*Text.ToString());
	CleanData = FString::FromInt(ToInt);
	IDUnique = UFolderTableOperatorLibrary::IsFolderIdUnique(CleanData);

#endif //  USE_REF_LOCAL_FILE
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtId(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	//UE_LOG(LogTemp, Log, TEXT("id : %s, is unique : %d"), *Text.ToString(), IDUnique);
	FString IdStr = Text.ToString();
	RemoveFrontZeroChar(IdStr);
	if (IdStr.Equals(FolderData.folder_id, ESearchCase::CaseSensitive)
		&& CommitMethod != ETextCommit::Type::OnCleared && CommitMethod != ETextCommit::Type::Default)
	{
		EdtId->SetText(FText::FromString(FolderData.folder_id));
		return;
	}
	bool IsNumber = !IdStr.IsEmpty() && IdStr.IsNumeric();
	bool IsEmpty = IdStr.IsEmpty();
	//if ((!IDUnique || !IsNumber) && !IsEmpty)
	if (!IsNumber && !IsEmpty)
	{
		FString TableKey = !IsNumber ? FString(TEXT("Id not number, please change")) : FString(TEXT("Id not unique, please change"));
		EdtId->SetText(FText::FromString(FolderData.folder_id));
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
			FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TableKey/*TEXT("Id not unique, please change")*/).ToString());
		return;
	}
	FRegexPattern Pattern(TEXT("[.]"));
	FRegexMatcher RegMatcher(Pattern, IdStr);
	RegMatcher.SetLimits(0, IdStr.Len());

	if (CommitMethod != ETextCommit::Type::OnCleared && !RegMatcher.FindNext() && IdStr.Len() <= 8 && (IdStr.IsNumeric() || IdStr.IsEmpty())
		&& CommitMethod != ETextCommit::Type::Default)
	{
		auto NewData = FolderData;
		NewData.folder_id = IdStr;
		NewData.backend_folder_path = FPaths::Combine(URefRelationFunction::GetFolderDirectory(NewData.backend_folder_path, false), NewData.folder_id.IsEmpty() ? NewData.id : NewData.folder_id);
		UE_LOG(LogTemp, Log, TEXT("FolderAndFilePropertyWidget---update folder id db data"));

#ifdef USE_REF_LOCAL_FILE
		SendUpdateDataRequest(NewData);
#else
		UpdateFolderData(FolderData);
#endif

	}
	else if (CommitMethod != ETextCommit::Type::OnCleared && CommitMethod != ETextCommit::Type::Default)
	{
		EdtId->SetText(FText::FromString(FolderData.folder_id));
	}
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	if (Text.ToString().Equals(FolderData.folder_code, ESearchCase::CaseSensitive))
	{
		return;
	}
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
	if (CleanExp.IsEmpty())
	{
		EdtCoding->SetText(FText::FromString(FolderData.folder_code));
		EdtCodeExp->SetText(FText::FromString(FolderData.folder_code_exp));
		return;
	}

	FString FinalStr = TEXT("\"") + Text.ToString() + TEXT("\"");
	OnTextCommittedEdtCodeExp(FText::FromString(FinalStr), CommitMethod);
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtCodeExp(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	const FString CleanExp = Text.ToString();

	if (!CleanExp.IsEmpty())
	{
		FString OutValue;
		FString OutExpression;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		bool bRes = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, CombineParams, CleanExp, OutValue, OutExpression);
		if (bRes)
		{
			EdtCodeExp->SetText(FText::FromString(OutExpression));
			EdtCoding->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.folder_code_exp = OutExpression;
			NewData.folder_code = OutValue;
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(FolderData);
#endif
		}
		else
		{
			EdtCoding->SetText(FText::FromString(FolderData.folder_code));
			EdtCodeExp->SetText(FText::FromString(FolderData.folder_code_exp));
		}
	}
	else
	{
		auto NewData = FolderData;
		NewData.folder_code_exp = CleanExp;
		NewData.folder_code = CleanExp;
#ifdef USE_REF_LOCAL_FILE
		SendUpdateDataRequest(NewData);
#else
		UpdateFolderData(FolderData);

		EdtCoding->SetText(FText::FromString(FolderData.folder_code));
		EdtCodeExp->SetText(FText::FromString(FolderData.folder_code_exp));
#endif
	}
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	if (Text.ToString().Equals(FolderData.folder_name, ESearchCase::CaseSensitive))
	{
		return;
	}
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
	if (CleanExp.IsEmpty())
	{
		EdtName->SetText(FText::FromString(FolderData.folder_name));
		EdtNameExpress->SetText(FText::FromString(FolderData.folder_name_exp));
		return;
	}
	FString FinalStr = TEXT("\"") + Text.ToString() + TEXT("\"");
	OnTextCommittedEdtNameExpress(FText::FromString(FinalStr), CommitMethod);
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtNameExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	const FString CleanExp = Text.ToString();

	if (!CleanExp.IsEmpty())
	{
		FString OutValue;
		FString OutExpression;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		if (UUIFunctionLibrary::CalculateExpressionValue({}, CombineParams, CleanExp, OutValue, OutExpression))
		{
			EdtNameExpress->SetText(FText::FromString(OutExpression));
			EdtName->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.folder_name_exp = OutExpression;
			NewData.folder_name = OutValue;
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(FolderData);
#endif
		}
		else
		{
			EdtName->SetText(FText::FromString(FolderData.folder_code));
			EdtNameExpress->SetText(FText::FromString(FolderData.folder_code_exp));
		}
	}
	else
	{
		auto NewData = FolderData;
		NewData.folder_name_exp = CleanExp;
		NewData.folder_name = CleanExp;
#ifdef USE_REF_LOCAL_FILE
		SendUpdateDataRequest(NewData);
#else
		UpdateFolderData(FolderData);

		EdtName->SetText(FText::FromString(FolderData.folder_name));
		EdtNameExp->SetText(FText::FromString(FolderData.folder_name_exp));
#endif
	}
}

void UFolderAndFilePropertyWidget::OnClickedBtnNameExpress()
{
	if (IS_OBJECT_PTR_VALID(EdtNameExpress))
	{
		BIND_EXPRESSION_WIDGET(static_cast<int32>(EExpressionFolderType::NameExpression), EdtNameExpress->GetText().ToString(), FName(TEXT("FolderOrFileExpressionEdit")));
	}
}

void UFolderAndFilePropertyWidget::OnCheckStateChangedCkbShow(bool IsChecked)
{
	SwitchVisibleType(EFolderOrFileVisibleType::Show);
	UE_LOG(LogTemp, Log, TEXT("FolderAndFilePropertyWidget---update folder visible show db data"));
	auto NewData = FolderData;
	NewData.visibility = true;
#ifdef USE_REF_LOCAL_FILE
	SendUpdateDataRequest(NewData);
#else
	UpdateFolderData(FolderData);
#endif
}

void UFolderAndFilePropertyWidget::OnCheckStateChangedCkbHidden(bool IsChecked)
{
	SwitchVisibleType(EFolderOrFileVisibleType::Hidden);
	UE_LOG(LogTemp, Log, TEXT("FolderAndFilePropertyWidget---update folder visible hidden db data"));
	auto NewData = FolderData;
	NewData.visibility = false;
#ifdef USE_REF_LOCAL_FILE
	SendUpdateDataRequest(NewData);
#else
	UpdateFolderData(FolderData);
#endif
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	if (Text.IsEmpty())
	{
		EdtVisiExpress->SetText(FText::FromString(FolderData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(FolderData.visibility)));
		return;
	}
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString OutValue;
		FString OutExpression;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> CombineParams;
		UParameterRelativeLibrary::CombineParameters(ParentParameters, FolderParamsMap, CombineParams);

		bool bRes = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, CombineParams, CleanExp, OutValue, OutExpression);
		if (bRes)
		{
			EdtVisiExpress->SetText(FText::FromString(OutExpression));
			EdtVisiValue->SetText(FText::FromString(OutValue));
			auto NewData = FolderData;
			NewData.visibility_exp = OutExpression;
			NewData.visibility = FCString::Atof(*OutValue);
#ifdef USE_REF_LOCAL_FILE
			SendUpdateDataRequest(NewData);
#else
			UpdateFolderData(FolderData);
#endif
			return;
		}
		EdtVisiExpress->SetText(FText::FromString(FolderData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(FolderData.visibility)));
	}
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtVisiValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString OutValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(OutValue);
		EdtVisiExpress->SetText(FText::FromString(OutValue));
		EdtVisiValue->SetText(FText::FromString(OutValue));
		auto NewData = FolderData;
		NewData.visibility_exp = OutValue;
		NewData.visibility = FCString::Atof(*OutValue);
#ifdef USE_REF_LOCAL_FILE
		SendUpdateDataRequest(NewData);
#else
		UpdateFolderData(FolderData);
#endif
	}
	else
	{
		EdtVisiExpress->SetText(FText::FromString(FolderData.visibility_exp));
		EdtVisiValue->SetText(FText::FromString(FString::SanitizeFloat(FolderData.visibility)));
	}
}

void UFolderAndFilePropertyWidget::OnClickedBtnVisible()
{
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress))
	{
		BIND_EXPRESSION_WIDGET(static_cast<int32>(EExpressionFolderType::VisibilityExpression), EdtVisiExpress->GetText().ToString(), FName(TEXT("FolderOrFileExpressionEdit")));
	}
}

void UFolderAndFilePropertyWidget::OnClickedBtnCodingExp()
{
	if (IS_OBJECT_PTR_VALID(EdtCodeExp))
	{
		BIND_EXPRESSION_WIDGET(static_cast<int32>(EExpressionFolderType::CodingExpression), EdtCodeExp->GetText().ToString(), FName(TEXT("FolderOrFileExpressionEdit")));
	}
}

void UFolderAndFilePropertyWidget::OnClickBtnPlaceRule()
{
	FRefPlaceRuleCustomData Data;
	FRefToLocalFileData ThisLocalFile = GetPropertyShowLocalFileData();
	if (ThisLocalFile.PlaceRuleCustomData.IsCustomByUser())
	{//custom
		Data = ThisLocalFile.PlaceRuleCustomData;
	}
	else
	{//default by setting
		FRefPlaceRuleData SettingRule = UFolderWidget::Get()->GetPlaceRuleByID(FolderData.placeRule);
		Data.ConstructFromPlaceRuleData(SettingRule);
	}
	UpdateCustomPlaceRuleWidget(Data);
}

void UFolderAndFilePropertyWidget::OnPlaceRuleSelectionChange(const FString& InSelectionItem)
{
	TArray<FString> StrArr;
	InSelectionItem.ParseIntoArray(StrArr, TEXT("/"));
	int32 SelectionID = StrArr.IsValidIndex(0) ? FCString::Atoi(*StrArr[0]) : INDEX_NONE;

	if (SelectionID != INDEX_NONE)
	{
		FRefToLocalFileData ThisLocalFile = GetPropertyShowLocalFileData();
		ThisLocalFile.PlaceRuleCustomData = FRefPlaceRuleCustomData();
		ThisLocalFile.PlaceRuleCustomData.id = SelectionID;

		const FString RefFilePathIdentify = URefRelationFunction::GetRefFileRelativePath(ThisLocalFile);
		const FString RefFilePath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathIdentify)
		);
		UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, ThisLocalFile);

		FRefDirectoryData CurRefData;
		if (UFolderWidget::Get()->GetCacheDataForRefDirectory(ThisLocalFile.FolderDBData.id, CurRefData))
		{
			CurRefData.placeRule = SelectionID;

			int64 FileSize = 0;
			ACatalogPlayerController::GetFileMD5AndSize(RefFilePath, CurRefData.md5, FileSize);

			/*
			*  @@ update ref file update user
			*/
			CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

			UFolderWidget::Get()->UpdateDataRequest(CurRefData);
			UFolderWidget::Get()->UploadFileRequest(RefFilePathIdentify);
			UFolderWidget::Get()->SyncSelectWidget(CurRefData);
		}

		UFolderWidget::Get()->SyncSelect(ThisLocalFile);

	}

}

void UFolderAndFilePropertyWidget::OnClickedBtnDelParam()
{
	if (!IS_OBJECT_PTR_VALID(SelectParam))
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Please select a parameter first!")).ToString());
		return;
	}
	bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Make sure to delete selected parameter?")).ToString());
	if (!Res) return;
	ParseAffectedParameters(ParemtersToSave, SelectParam->GetParamData(), true);
	{
		const FString DeleteParameterName = SelectParam->GetFolderParamName();
		const int32 Index = ParemtersToSave.IndexOfByPredicate([DeleteParameterName](const FParameterData& InOther) { return InOther.Data.name.Equals(DeleteParameterName, ESearchCase::CaseSensitive); });
		if (INDEX_NONE != Index) ParemtersToSave.RemoveAt(Index);

		//if (UFolderTableOperatorLibrary::DeleteFolderFileParameter(SelectParam->GetParamData().Data.id, FolderData.can_add_subfolder))
		{
			SelectParam->SetVisibility(ESlateVisibility::Collapsed);
			FolderParams.Remove(SelectParam);
			if (SBParams)
			{
				SBParams->RemoveChild(SelectParam);
			}
			FolderParamsMap.Remove(SelectParam->GetFolderParamName());
			SelectParam = nullptr;

#ifdef USE_REF_LOCAL_FILE
			SyncDataInfoWhenParamsEdit();
			UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, true);
#else
			UpdateParamData(ParemtersToSave);
#endif
			ParemtersToSave.Empty();
			RefreshFolderProperty();
		}
		/*else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Delete folder parameter error")).ToString());
		}*/
	}
}

void UFolderAndFilePropertyWidget::OnClickedBtnAddParam()
{
	FParameterData NewData;
	UParameterDetailWidget::Get()->SetFolderOrFileParentParams(ParentParameters);
	UParameterDetailWidget::Get()->SetFolderOrFileLocalParams(FolderParamsMap);
	UParameterDetailWidget::Get()->UpdateContent(NewData, 2);
	//UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamUpdateEditHandler")));
	UParameterDetailWidget::Get()->SelectedParamUpdateDatasDelegate.BindUFunction(this, FName(TEXT("OnParamUpdateEditHandler02")));
	UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
	UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
}

void UFolderAndFilePropertyWidget::OnTextCommittedEdtDescription(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	const FString CleanExp = Text.ToString();

	auto NewData = FolderData;
	NewData.description = CleanExp;
#ifdef USE_REF_LOCAL_FILE
	SendUpdateDataRequest(NewData);
#else
	UpdateFolderData(NewData);
#endif
}

void UFolderAndFilePropertyWidget::OnClickedBtnProperty()
{
	IsShowProperty = true;
	SwitchShowPropertyOrAssociate(true);
}

void UFolderAndFilePropertyWidget::OnClickedBtnAssociate()
{
	IsShowProperty = false;
	SwitchShowPropertyOrAssociate(false);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParameters;
	TArray<FString> UpperDirectoryID = URefRelationFunction::GetUpperFolderDirectory(FolderData.backend_folder_path, true);
	TArray<FRefToLocalFileData> UpperRefDatas;
	UFolderWidget::Get()->GetCacheDataForFile(UpperDirectoryID, UpperRefDatas);
	URefRelationFunction::GetTopLevelFolderParameterData(UpperRefDatas, OverrideParameters);

	InitAssociateShow(FolderData.id, OverrideParameters);
}

void UFolderAndFilePropertyWidget::ResetShowProperty()
{
	IsShowProperty = true;
	IsFile = !FolderData.can_add_subfolder;
	SwitchShowPropertyOrAssociate(true);
}

void UFolderAndFilePropertyWidget::SwitchShowPropertyOrAssociate(bool IsProperty)
{
	if (IsProperty)
	{
		Btn_NoPass_Property->SetVisibility(ESlateVisibility::Visible);
		CP_Associate->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		Btn_NoPass_Property->SetVisibility(ESlateVisibility::Collapsed);
		CP_Associate->SetVisibility(ESlateVisibility::Visible);
	}

}

void UFolderAndFilePropertyWidget::InitAssociateShow(const FString& AssociateID, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParams)
{
	if (IS_OBJECT_PTR_VALID(AssociateLayoutBP))
	{
		AssociateLayoutBP->Init(AssociateID, InParams);
	}
}

void UFolderAndFilePropertyWidget::OnClickBtnUPDown(const FParameterData& InData, bool bUp)
{
	if (FolderParamsMap.Contains(InData.Data.name))
	{
		FolderParamsMap.GenerateValueArray(ParemtersToSave);
		int32 ParamIndex = ParemtersToSave.IndexOfByPredicate([&](const FParameterData& InOther)->bool { return InOther.Data.name.Equals(InData.Data.name, ESearchCase::CaseSensitive); });
		if (ParamIndex != INDEX_NONE)
		{
			if (bUp && ParamIndex == 0)
				return;

			if (!bUp && ParamIndex == ParemtersToSave.Num() - 1)
				return;

			ParemtersToSave.Swap(bUp ? ParamIndex - 1 : ParamIndex + 1, ParamIndex);
			FolderParamsMap.Empty();
			SBParams->ClearChildren();
			FolderParams.Empty();

			for (auto &ParamIte : ParemtersToSave)
			{
				CreateFolderParamWidget(ParamIte);
			}
		}

#ifdef USE_REF_LOCAL_FILE
		//SyncDataInfoWhenParamsEdit();
		UFolderWidget::Get()->UpdateTopLevelParameters(ParemtersToSave, false);
#else
		UpdateParamData(ParemtersToSave);
#endif

		ParemtersToSave.Empty();

		SelectParam = nullptr;

		UFolderParamWidget** FindWidget = FolderParams.FindByPredicate([&](UFolderParamWidget* InWidget) { return InWidget->GetParamData().Data.name.Equals(InData.Data.name); });
		if (FindWidget != nullptr)
		{
			ParamSelectEdit(*FindWidget);
		}

	}
}

void UFolderAndFilePropertyWidget::OnClickBtnUP()
{
	if (IS_OBJECT_PTR_VALID(SelectParam))
	{
		OnClickBtnUPDown(SelectParam->GetParamData(), true);
	}
}

void UFolderAndFilePropertyWidget::OnClickBtnDown()
{
	if (IS_OBJECT_PTR_VALID(SelectParam))
	{
		OnClickBtnUPDown(SelectParam->GetParamData(), false);
	}
}


#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif

#undef LOCTEXT_NAMESPACE
