// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "FolderParamWidget.h"
#include "Components/CanvasPanel.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToPlaceDataLibrary.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "DesignStation/UI/Obsurce/ObsurceLayoutWidget.h"
#include "DesignStation/UI/PlaceRule/PlaceRuleCustomWidget.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "FolderAndFilePropertyWidget.generated.h"

/**
 *
 */

struct FRefToLocalFileData;
class UFrontDirectoryWidget;
class UEditableText;
class UComboBoxString;
class UButton;
class UImage;
class UScrollBox;
class UCheckBox;
class USizeBox;
class UParameterDetailWidget;
class UEditableTextBox;

UENUM(BlueprintType)
enum class EFolderOrFileVisibleType : uint8
{
	Show = 0,
	Hidden
};

UENUM(BlueprintType)
enum class EExpressionFolderType : uint8
{
	VisibilityExpression = 0,
	CodingExpression,
	NameExpression
};

USTRUCT(BlueprintType)
struct FParentParameterMark
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
		int32 FolderID;
	UPROPERTY()
		bool  IsGet;

public:
	FParentParameterMark()
		: FolderID(-1), IsGet(false)
	{}
};

DECLARE_DYNAMIC_DELEGATE_FourParams(FViewPropertyNameChangedDelegate, const FString&, FolderId, const FString&, NewName, bool, IsFolder, bool, IsVisible);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFolderParentParamDelegate, const FString&, FolderID, bool, IsFolder);

UCLASS()
class DESIGNSTATION_API UFolderAndFilePropertyWidget : public UUserWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void UpdateContent(const FString& InFolderId);
	void UpdateContent(const FFolderTableData& InData);
	void RefreshProperty(const FFolderTableData& InData);
	bool RefreshFolderProperty();
	void SyncDataInfoWhenParamsEdit();
	bool IsPropertyChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FFolderTableData& NewData);
	bool IsSizeChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FRefDirectoryData& RefData);
	bool IsSizeChangeWhenParamsEdit(const TArray<FParameterData>& NewParams, FFolderTableData& NewData);
	void UpdateParentsParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters);

	FORCEINLINE FString GetCurrentFolderId() const { return FolderData.id; }

	FRefToLocalFileData GetPropertyShowLocalFileData();

	//映射逻辑
	UFUNCTION(BlueprintImplementableEvent, Category = "FolderFileProperty")
	void UpdateMappingShow(const FString& MappingPath);

	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	UFrontDirectoryWidget* GetFrontDirectoryWidget();

	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	void OnMappingEditHander(const FString& NewMapping);

	UFUNCTION(BlueprintImplementableEvent, Category = "FolderFileProperty")
	void MappingUIShow(bool IsShow);

	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	bool CanMapping();

	
	//place rule custom
	void InitPlaceRuleComboBox(const TArray<FString>& PlaceRuleStrArr);

	void UpdateSelectPlaceRule(const int32& InSelectID);
	void UpdateUserCanConfig(const FRefPlaceRuleData& InDefaultPlaceRule);
	
	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	void OnClickBtnPlaceRule();

	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	void OnPlaceRuleSelectionChange(const FString& InSelectionItem);

	void UpdateCustomPlaceRuleWidget(const FRefPlaceRuleCustomData& InCustomData);
	UFUNCTION()
		void OnCustomPlaceRuleModifyHandler(const FRefPlaceRuleCustomData& ModifyData);

	void AfterModifyCustomPlaceRule(const FRefToLocalFileData& FileData);

public:
	void BindDelegate();
	void SendUpdateDataRequest(const FFolderTableData& UpdateFolderData, const FString& FrontPath = TEXT(""));
	UFUNCTION()
	void OnBackendDirectoryUpdateHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	void SetQueryMatInfo(const FString& MatFolderID);
	UFUNCTION()
	void OnGetFurnitureOrMatDataArrForParseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FCSModelMatData>& NetData);


private:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

	UPROPERTY()
	FFrontDirectoryNetUUID FrontNetUUID;

public:
	FViewPropertyNameChangedDelegate FolderPropertyChangeDelegate;
	FFolderParentParamDelegate FolderParentParemDelegate;

private:
	void UpdateFolderData(const FFolderTableData& InData);
	void UpdateParamData(const TArray<FParameterData>& InParam);

private:
	bool IDUnique;

private:
	void UpdatePropertyWidgetState(bool IsRootFolder);
	void SwitchVisibleType(const EFolderOrFileVisibleType& InType);
	void SwitchVisibleWidgetEnable(bool IsEnable);
	void FormatParamExpressionChange(FParameterData& ParamData);
	void FormatParamValueChange(FParameterData& ParamData);
	bool CalculateExpressionValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters,const FString& InExpression, FString& OutValue, FString& OutFormatExpress);
	void CreateFolderParamWidget(const FParameterData& InData);

	//数据存在FolderWidget中
	void UpdateFolderParamContent();
	void UpdateFolderParamContent(const FString& FolderId, bool IsFolder);

	bool ParseAffectedParameters(TArray<FParameterData>& AffectedParameters, FParameterData& ModifyParameter, bool DeleteModifyParameter);

	bool GenerateFolderFileParameters(TArray<FParameterData> OutParams);

	UFUNCTION()
		void FolderParamsEdit(const int32& EditType, UFolderParamWidget* ParamItem);
	UFUNCTION()
		void FormatParamsEdit(const int32& EditType, const FString& InData);
	UFUNCTION()
		void ParamSelectEdit(UFolderParamWidget* InParam);
	UFUNCTION()
		void OnParamUpdateEditHandler(const FParameterData& ParamData);
		UFUNCTION()
		void OnParamUpdateEditHandler02(const TArray<FParameterData>& ParamDatas);
	UFUNCTION()
		void OnFolderParentParamHandler();

	UFUNCTION()
		void FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression);

	void RemoveFrontZeroChar(FString& EditString);


	UFUNCTION()
	void OnClickBtnUPDown(const FParameterData& InData, bool bUp);

private:
	UPROPERTY()
		TArray<FParameterData> DeleteParams;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  InsertParams;
	UPROPERTY()
		TArray<FEnumParameterTableData> DeleteEnumParams;
	UPROPERTY()
		TArray<FEnumParameterTableData> InsertEnumParams;

	UPROPERTY()
		TArray<UFolderParamWidget*> FolderParams;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  FolderParamsMap;

		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;

	UPROPERTY()
		UFolderParamWidget* SelectParam;

	bool OldVisibleType;
	//int32 FolderId;
	FFolderTableData FolderData;

	UPROPERTY()
		FMatAdditionInfo MatInfo;

	/*UPROPERTY()
		FParentParameterMark FolderParentParamMark;*/
	UPROPERTY()
		TArray<FParameterData> ParemtersToSave;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnTextChangedEdtId(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtId(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCodeExp(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtNameExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnNameExpress();
	UFUNCTION()
		void OnCheckStateChangedCkbShow(bool IsChecked);
	UFUNCTION()
		void OnCheckStateChangedCkbHidden(bool IsChecked);

	UFUNCTION()
		void OnIsNewCheckStateChanged(bool IsChecked);

	UFUNCTION()
		void OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtVisiValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisible();
	UFUNCTION()
		void OnClickedBtnCodingExp();
	
	UFUNCTION()
		void OnClickedBtnDelParam();
	UFUNCTION()
		void OnClickedBtnAddParam();
		UFUNCTION()
		void OnTextCommittedEdtDescription(const FText& Text, ETextCommit::Type CommitMethod);

		UFUNCTION()
		void OnClickBtnUP();

		UFUNCTION()
		void OnClickBtnDown();
public:
	UPROPERTY(BlueprintReadOnly, Category = "Is_New Check Border Show Or Not")
		bool IsNewBorderShow; //用于New标签的显示

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Associate")
		bool IsShowProperty;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Associate")
		bool IsFile;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Place Rule")
		bool bCanModifyPlaceRule = true;

private:
	UFUNCTION()
	void OnClickedBtnProperty();

	UFUNCTION()
	void OnClickedBtnAssociate();

	void ResetShowProperty();
	void SwitchShowPropertyOrAssociate(bool IsProperty);

	void InitAssociateShow(const FString& AssociateID, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParams);

private:

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Property;
	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Associate;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_NoPass_Property;
	UPROPERTY(meta=(BindWidget))
	UCanvasPanel* CP_Associate;
	UPROPERTY(meta=(BindWidget))
	UObsurceLayoutWidget* AssociateLayoutBP;


	//place rule and custom
	UPROPERTY(meta=(BindWidget))
	UComboBoxString* CBS_PlaceRule;

	UPROPERTY()
	UPlaceRuleCustomWidget* PlaceRuleCustomWidget;


	UPROPERTY()
		USizeBox* SZID;
	UPROPERTY()
		UEditableText* EdtId;
	UPROPERTY()
		USizeBox* SZCoding;
	UPROPERTY()
		UEditableText* EdtCoding;
	UPROPERTY()
		UEditableText* EdtCodeExp;
	UPROPERTY()
		UEditableText* EdtName;
	UPROPERTY()
		UEditableText* EdtNameExpress;
	UPROPERTY()
		UButton* BtnNameExpress;
	UPROPERTY()
		UCheckBox* CkbShow;
	UPROPERTY()
		UCheckBox* CkbHidden;
	UPROPERTY()
		UButton* BtnDelParam;
	UPROPERTY()
		UButton* BtnAddParam;
	UPROPERTY()
		UScrollBox* SBParams;
	UPROPERTY()
		UEditableTextBox* EdtVisiExpress;
	UPROPERTY()
		UEditableText* EdtVisiValue;
	UPROPERTY()
		UButton* BtnVisiExpress;
	UPROPERTY()
		UButton* BtnCodingExp;

	UPROPERTY(BlueprintReadWrite, Category = "FolderAndFilePropertyWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UCheckBox* CB_IsNew;

		UPROPERTY(meta = (BindWidget))
		UEditableText* Edt_Description;

		UPROPERTY(meta = (BindWidget))
		UButton* Btn_Up;

		UPROPERTY(meta = (BindWidget))
		UButton* Btn_Down;
};
