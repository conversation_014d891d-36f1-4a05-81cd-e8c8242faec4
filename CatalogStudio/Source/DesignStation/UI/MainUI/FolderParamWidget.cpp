// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderParamWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

FString UFolderParamWidget::FolderParamPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/FolderParamUI.FolderParamUI_C'");

const FLinearColor ParamBorColorNormal = FLinearColor::White;
const FLinearColor ParamBorColorHover = FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.3f);
const FLinearColor ParamBorColorSelect = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);

const FLinearColor ParamTextNormal = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.000000f);
const FLinearColor ParamTextSelect = FLinearColor::White;

bool UFolderParamWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseButtonDownEvent, FName(TEXT("OnMouseClickedToSelectParam")));
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseDoubleClickEvent, FName(TEXT("OnMouseDoubleClickToEditParam")));

	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(TxtNameW, Txt_NameW);


	BIND_PARAM_CPP_TO_UMG(BorExpress, Bor_Express);
	BIND_SLATE_WIDGET_FUNCTION(BorExpress, OnMouseDoubleClickEvent, FName(TEXT("OnMouseDoubleClickToEditExpress")));
	BIND_PARAM_CPP_TO_UMG(EdtExpress, Edt_Express);
	BIND_WIDGET_FUNCTION(EdtExpress, OnTextCommitted, UFolderParamWidget::OnTextCommittedEdtExpression);
	BIND_PARAM_CPP_TO_UMG(BtnExpress, Btn_Express);
	BIND_WIDGET_FUNCTION(BtnExpress, OnClicked, UFolderParamWidget::OnClickedBtnExpress);
	BIND_PARAM_CPP_TO_UMG(TxtExpress, Txt_EXpress);

	BIND_PARAM_CPP_TO_UMG(BorValue, Bor_Value);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UFolderParamWidget::OnTextCommittedEdtValue);

	BIND_PARAM_CPP_TO_UMG(BorEnumValue, Bor_EnumValue);
	BIND_PARAM_CPP_TO_UMG(CBSValues, CBS_Values);
	BIND_WIDGET_FUNCTION(CBSValues, OnSelectionChanged, UFolderParamWidget::OnSelectionChangedCBSValues);

	BIND_PARAM_CPP_TO_UMG(ImgNormal, Img_Normal);
	BIND_PARAM_CPP_TO_UMG(ImgHover, Img_Hover);
	BIND_PARAM_CPP_TO_UMG(ImgBan, Img_Ban);
	//BIND_WIDGET_FUNCTION(Btn_Up, OnClicked, UFolderParamWidget::OnClickBtnUP);
	//BIND_WIDGET_FUNCTION(Btn_Down, OnClicked, UFolderParamWidget::OnClickBtnDown);

	IsSelected = false;
	bEditing = false;

	return true;
}

void UFolderParamWidget::UpdateContent(const FParameterData& InData)
{
	ParamData.CopyData(InData);
	OldExpression = ParamData.Data.expression;
	SwitchValueTypeShow(InData.Data.is_enum ? true : false);

	ParameterDes = FText::FromString(FParameterTableData::GetCleanDataWithoutAdditionMsg(InData.Data.description));
	TB_ParameterDes->SetText(ParameterDes);
	TB_ParameterDes->SetToolTipText(ParameterDes);

	if (TxtName && EdtExpress)
	{
		TxtName->SetText(FText::FromString(InData.Data.name));
		TxtNameW->SetText(FText::FromString(InData.Data.name));

		EdtExpress->SetText(FText::FromString(InData.Data.expression));
		TxtExpress->SetText(FText::FromString(InData.Data.expression));
	}
	if (InData.Data.is_enum && (InData.EnumData.Num() > 0))
	{
		FString SelectOption;
		CBSValues->ClearOptions();
		for (auto& Data : InData.EnumData)
		{
			if (!Data.IsVisiable())
			{
				continue;
			}
			const FString EnumDisplayName = Data.name_for_display.IsEmpty() ? Data.value : Data.name_for_display;
			CBSValues->AddOption(EnumDisplayName);
			if (SelectOption.IsEmpty() && UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(InData.Data.value)
				&& UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(Data.value) && FCString::Atof(*InData.Data.value) == FCString::Atof(*Data.value))
			{
				SelectOption = EnumDisplayName;
			}
			else if(SelectOption.IsEmpty() && Data.value.Equals(InData.Data.value))
			{
				SelectOption = EnumDisplayName;
			}
		}
		CBSValues->RefreshOptions();
		if (!SelectOption.IsEmpty())
		{
			CBSValues->SetSelectedOption(SelectOption);
		}
		else
		{
			CBSValues->SetSelectedOption(TEXT(""));
		}
	}
	else
	{
		EdtValue->SetText(FText::FromString(InData.Data.value));
	}
	/*Name = InData.Data.name;
	Value = InData.Data.value;
	Expression = InData.Data.expression;*/
	IsNew = false;
}

void UFolderParamWidget::SetIsFolderParamNew(bool _IsNew)
{
	IsNew = _IsNew;
	/*if (EdtName)
	{
		EdtName->SetIsEnabled(_IsNew ? true : false);
	}*/
}

void UFolderParamWidget::UpdateParentsParameters(FGetOverrideParameters InGetOverrideParameterFuc)
{
	GetOverrideParametersFuc = InGetOverrideParameterFuc;
}

void UFolderParamWidget::SetFolderParamValue(const FString& InValue)
{
	if (ParamData.Data.is_enum)
	{
		bool EnumExist = false;
		int32 EnumIndex = 0;

		for (auto& iter : ParamData.EnumData)
		{
			if (iter.value == InValue)
			{
				EnumExist = true;
				break;
			}
			++EnumIndex;
		}
		if (EnumExist)
		{
			CBSValues->SetSelectedOption(CBSValues->GetOptionAtIndex(EnumIndex));
			ParamData.Data.value = InValue;
		}
		else
		{
			CBSValues->SetSelectedOption(CBSValues->GetOptionAtIndex(0));
			ParamData.Data.value = ParamData.EnumData[0].value;
		}
	}

	if (EdtValue)
	{
		EdtValue->SetText(FText::FromString(InValue));
	}
}

void UFolderParamWidget::SetFolderParamExpression(const FString& InExpression)
{

	ParamData.Data.expression = InExpression;
	OldExpression = ParamData.Data.expression;
	if (EdtExpress)
	{
		EdtExpress->SetText(FText::FromString(InExpression));
		TxtExpress->SetText(FText::FromString(InExpression));
	}
}

void UFolderParamWidget::SetIsSelect(bool _IsSelect)
{
	IsSelected = _IsSelect;
	UpdateParamBorColor(IsSelected ? EParamBorderType::Select : EParamBorderType::Normal);
}

UFolderParamWidget* UFolderParamWidget::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderParamWidget::Create()"));
	UClass* FolderParamBp = LoadClass<UUserWidget>(NULL, *FolderParamPath);
	checkf(FolderParamBp, TEXT("load folder param bp error!"));
	UFolderParamWidget* FolderParamItem = CreateWidget<UFolderParamWidget>(GWorld.GetReference(), FolderParamBp);
	checkf(FolderParamItem, TEXT("create folder param item error!"));
	return FolderParamItem;
}

void UFolderParamWidget::SwitchValueTypeShow(bool IsEnum)
{
	if (BorValue && BorEnumValue && EdtExpress && BtnExpress)
	{
		BorValue->SetVisibility(IsEnum ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		BorEnumValue->SetVisibility(IsEnum ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		//EdtExpress->SetIsEnabled(!IsEnum);
		//EdtExpress->SetText(FText::FromString(TEXT("")));
		//BtnExpress->SetIsEnabled(!IsEnum);
	}
}

void UFolderParamWidget::UpdateParamBorColor(const EParamBorderType& InType)
{
	if (BorName && BorExpress && BorValue && BorEnumValue && TxtName && EdtExpress && EdtValue)
	{
		if (InType == EParamBorderType::Normal)
		{
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorNormal, Bor_ParameterDes);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorNormal, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorNormal, BorExpress);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorNormal, BorValue->GetVisibility() == ESlateVisibility::Visible ? BorValue : BorEnumValue);
			//TxtName->SetColorAndOpacity(FSlateColor(ParamTextNormal));
			TxtName->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			TxtNameW->SetVisibility(ESlateVisibility::Collapsed);
			EdtExpress->SetVisibility(ESlateVisibility::Visible);
			TxtExpress->SetVisibility(ESlateVisibility::Collapsed);
			if (BtnExpress->GetIsEnabled())
			{
				ImgNormal->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				ImgHover->SetVisibility(ESlateVisibility::Collapsed);
				ImgBan->SetVisibility(ESlateVisibility::Collapsed);

			}
			else
			{
				ImgNormal->SetVisibility(ESlateVisibility::Collapsed);
				ImgHover->SetVisibility(ESlateVisibility::Collapsed);
				ImgBan->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			}


		}
		else if (InType == EParamBorderType::Hover)
		{
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorHover, Bor_ParameterDes);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorHover, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorHover, BorExpress);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorHover, BorValue->GetVisibility() == ESlateVisibility::Visible ? BorValue : BorEnumValue);
			//TxtName->SetColorAndOpacity(FSlateColor(ParamTextSelect));
			TxtName->SetVisibility(ESlateVisibility::Collapsed);
			TxtNameW->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			if (bEditing)
			{
				EdtExpress->SetVisibility(ESlateVisibility::Visible);
				TxtExpress->SetVisibility(ESlateVisibility::Collapsed);
			}
			else
			{
				EdtExpress->SetVisibility(ESlateVisibility::Collapsed);
				TxtExpress->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			}

			if (BtnExpress->GetIsEnabled())
			{
				ImgNormal->SetVisibility(ESlateVisibility::Collapsed);
				ImgHover->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				ImgBan->SetVisibility(ESlateVisibility::Collapsed);
			}
			else
			{
				ImgNormal->SetVisibility(ESlateVisibility::Collapsed);
				ImgHover->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				ImgBan->SetVisibility(ESlateVisibility::Collapsed);
			}
			//CBSValues->ForegroundColor = FSlateColor(ParamTextSelect);
		}
		else if (InType == EParamBorderType::Select)
		{
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorSelect, Bor_ParameterDes);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorSelect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorSelect, BorExpress);
			UUIFunctionLibrary::SetBorderBrushColor(ParamBorColorSelect, BorValue->GetVisibility() == ESlateVisibility::Visible ? BorValue : BorEnumValue);
			//TxtName->SetColorAndOpacity(FSlateColor(ParamTextSelect));
			TxtName->SetVisibility(ESlateVisibility::Collapsed);
			TxtNameW->SetVisibility(ESlateVisibility::SelfHitTestInvisible);

			EdtExpress->SetVisibility(ESlateVisibility::Collapsed);
			TxtExpress->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
			if (BtnExpress->GetIsEnabled())
			{
				ImgNormal->SetVisibility(ESlateVisibility::Collapsed);
				ImgHover->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				ImgBan->SetVisibility(ESlateVisibility::Collapsed);
			}
			else
			{
				ImgNormal->SetVisibility(ESlateVisibility::Collapsed);
				ImgHover->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				ImgBan->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}
}

void UFolderParamWidget::OnExpressionEditHandler(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int)EParamExpressType::Express)
	{
		OnTextCommittedEdtExpression(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

void UFolderParamWidget::OnParamDetailUpdateHandler(const FParameterData& InParamData)
{
	ParamData.CopyData(InParamData);
	FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ParamUpdate, this);
	/*UpdateContent(ParamData);
	FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ParamUpdate, this);*/
}

void UFolderParamWidget::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		FText FormatValue = UParameterPropertyData::FormatParameterValue(Text.ToString());
		FString ValueStr = FormatValue.ToString();
		if (ValueStr.IsEmpty())
		{
			GetWorld()->GetTimerManager().SetTimerForNextTick([&]() { if (EdtValue != nullptr) EdtValue->SetText(FText::FromString(ParamData.Data.value)); });
			return;
		}
		if (ParamData.Data.value == FormatValue.ToString())
		{
			return;
		}
		ParamData.Data.value = FormatValue.ToString();
		ParamData.Data.expression = FormatValue.ToString();
		EdtValue->SetText(FormatValue);
		TxtExpress->SetText(FormatValue);
		FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ValueChange, this);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared && EdtValue)
	{
		EdtValue->SetText(FText::FromString(ParamData.Data.value));
		TxtExpress->SetText(FText::FromString(ParamData.Data.value));
	}
}

void UFolderParamWidget::OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::OnMouseClick)
	{
		SelectedItem = ParamData.EnumData[CBSValues->GetSelectedIndex()].value;//枚举选项的值

		TArray<TPair<FString, FString>> EnumArray = TArray<TPair<FString, FString>>();
		for (auto& En : ParamData.EnumData)
		{
			EnumArray.Add({ En.expression, En.value });
		}

		int32 EnumIndex = CBSValues->GetSelectedIndex();

		if (EnumIndex >= 0)
		{
			ParamData.Data.value = EnumArray[EnumIndex].Value;
			ParamData.Data.expression = EnumArray[EnumIndex].Key;
		}
		else
		{
			ParamData.Data.value = EnumArray[0].Value;
			ParamData.Data.expression = EnumArray[0].Key;
		}
		FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ValueChange, this);
	}
}

//void UFolderParamWidget::OnTextCommittedEdtExpression(const FText & Text, ETextCommit::Type CommitMethod)
//{
//	if (CommitMethod != ETextCommit::Type::OnCleared)
//	{
//		Expression = Text.ToString();
//		FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ExpressionChange, this);
//	}
//}

FEventReply UFolderParamWidget::OnMouseClickedToSelectParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		UE_LOG(LogTemp, Log, TEXT("click to select param"));
		ParamSelectDelegate.ExecuteIfBound(this);
		return FEventReply(true);
	}
	return FEventReply();
}

FEventReply UFolderParamWidget::OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		UParameterDetailWidget::Get()->SetFolderOrFileLocalParams(GetOverrideParametersFuc(ParamData));
		UParameterDetailWidget::Get()->SetFolderOrFileParentParams(ParentParameters);
		UParameterDetailWidget::Get()->UpdateContent(ParamData, 1);
		UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamDetailUpdateHandler")));
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
		UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		return FEventReply(true);
	}
	return FEventReply();
}

FEventReply UFolderParamWidget::OnMouseDoubleClickToEditExpress(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		bEditing = true;
		EdtExpress->SetVisibility(ESlateVisibility::Visible);
		TxtExpress->SetVisibility(ESlateVisibility::Collapsed);
		auto PC = GWorld->GetFirstPlayerController();
		EdtExpress->SetUserFocus(PC);
		RequestParentParamDelegate.ExecuteIfBound();
		return FEventReply(true);
	}
	return FEventReply();
}


void UFolderParamWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelected)
	{
		UpdateParamBorColor(EParamBorderType::Hover);
	}
}

void UFolderParamWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelected)
	{
		UpdateParamBorColor(EParamBorderType::Normal);
	}
}

void UFolderParamWidget::OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		OldExpression = ParamData.Data.expression;
		FString ExpStr = Text.ToString();
		if (ExpStr.IsEmpty())
		{
			GetWorld()->GetTimerManager().SetTimerForNextTick([&]() { if (EdtExpress != nullptr) EdtExpress->SetText(FText::FromString(OldExpression)); });
			return;
		}

		ParamData.Data.expression = ExpStr;
		FolderParamDelegate.ExecuteIfBound((int32)EFolderParamType::ExpressionChange, this);
		bEditing = false;
	}
}

void UFolderParamWidget::OnClickedBtnExpress()
{
	if (EdtExpress)
	{
		BIND_EXPRESSION_WIDGET((int)EParamExpressType::Express, EdtExpress->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void UFolderParamWidget::SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData)
{
	ParentParameters = InParamData;
}


void UFolderParamWidget::OnClickBtnUP()
{
	BtnUpDownClickDelegate.ExecuteIfBound(GetParamData(), true);
}

void UFolderParamWidget::OnClickBtnDown()
{
	BtnUpDownClickDelegate.ExecuteIfBound(GetParamData(), false);
}