// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "FolderParamWidget.generated.h"

/**
 *
 */

UENUM(BlueprintType)
enum class EFolderParamType : uint8
{
	NameChange = 0,
	ValueChange,
	ExpressionChange,
	ParamUpdate
};

UENUM(BlueprintType)
enum class EParamBorderType : uint8
{
	Normal = 0,
	Hover,
	Select
};

UENUM(BlueprintType)
enum class EParamExpressType : uint8
{
	Express = 0
};

class UEditableText;
class UButton;
class UComboBoxString;
class UBorder;
class UTextBlock;
class UImage;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FFolderParamEditDelegate, const int32&, EditType, UFolderParamWidget*, ParamItem);
DECLARE_DYNAMIC_DELEGATE_OneParam(FParamSelectDelegate, UFolderParamWidget*, ParamItem);
DECLARE_DYNAMIC_DELEGATE(FRequestParentParamDelegate);
DECLARE_DELEGATE_TwoParams(FFolderParamsUpDownDelegate, const FParameterData&, bool);

using FGetOverrideParameters = TFunction<TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> (const FParameterData&)>;

UCLASS()
class DESIGNSTATION_API UFolderParamWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	void UpdateContent(const FParameterData& InData);
	void SetIsFolderParamNew(bool _IsNew);
	void UpdateParamData();
	void UpdateParentsParameters(FGetOverrideParameters GetOverrideParameterFuc);

	FORCEINLINE bool GetIsFolderParamNew() const { return IsNew; }
	FORCEINLINE FString GetFolderParamName() const { return ParamData.Data.name; }
	FORCEINLINE FString GetFolderParamValue() const { return ParamData.Data.value; }
	void SetFolderParamValue(const FString& InValue);
	FORCEINLINE FString GetFolderParamExpression() const { return ParamData.Data.expression; }
	void SetFolderParamExpression(const FString& InExpression);

	FORCEINLINE bool GetIsSelected() const { return IsSelected; }
	FORCEINLINE FParameterData& GetParamData() { return ParamData; }

	void SetIsSelect(bool _IsSelect);

	static UFolderParamWidget* Create();

	FString GetOldExpression() { return OldExpression; }

	void SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData);

private:

	TArray<FString> EnumValue;

private:
	void SwitchValueTypeShow(bool IsEnum);
	void UpdateParamBorColor(const EParamBorderType& InType);
	UFUNCTION()
		void OnExpressionEditHandler(const int32& EditType, const FString& OutExpression);
	UFUNCTION()
		void OnParamDetailUpdateHandler(const FParameterData& InParamData);

public:
	FFolderParamEditDelegate FolderParamDelegate;
	FParamSelectDelegate ParamSelectDelegate;
	FRequestParentParamDelegate RequestParentParamDelegate;
	FFolderParamsUpDownDelegate BtnUpDownClickDelegate;
private:
	FParameterData ParamData;
	FString OldExpression;
	/*FString Name;
	FString Value;
	FString Expression;*/
	bool IsNew;
	bool IsSelected;

	static FString FolderParamPath;

	FGetOverrideParameters GetOverrideParametersFuc;

protected:
	UFUNCTION()
		FEventReply OnMouseClickedToSelectParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnMouseDoubleClickToEditExpress(FGeometry MyGeometry, const FPointerEvent& MouseEvent);


protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExpress();

	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType);

		UFUNCTION()
		void OnClickBtnUP();

		UFUNCTION()
		void OnClickBtnDown();
private:
	UPROPERTY()
		UBorder* BorName;
	UPROPERTY()
		UEditableText* TxtName;
	UPROPERTY()
		UEditableText* TxtNameW;
	UPROPERTY()
		UBorder* BorExpress;
	UPROPERTY()
		UEditableText* EdtExpress;
	UPROPERTY()
		UButton* BtnExpress;

	UPROPERTY()
		UBorder* BorValue;

	UPROPERTY(BlueprintReadWrite, Category = "FolderParamWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UBorder* Bor_ParameterDes;

	UPROPERTY(BlueprintReadWrite, Category = "FolderParamWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
	UTextBlock* TB_ParameterDes;

	UPROPERTY(BlueprintReadOnly, Category = "FolderParamWidget", meta = (AllowPrivateAccess = true))
		FText ParameterDes;

	UPROPERTY()
		UEditableText* EdtValue;
	UPROPERTY()
		UBorder* BorEnumValue;
	UPROPERTY()
		UComboBoxString* CBSValues;
	UPROPERTY()
		UEditableText* TxtExpress;
	UPROPERTY()
		UImage* ImgNormal;
	UPROPERTY()
		UImage* ImgHover;
	UPROPERTY()
		UImage* ImgBan;

	//UPROPERTY(meta = (BindWidget))
	//UButton* Btn_Up;

	//UPROPERTY(meta = (BindWidget))
	//UButton* Btn_Down;

	bool bEditing;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;
};
