// Fill out your copyright notice in the Description page of Project Settings.

#include "FolderWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SQLite/LocalDatabaseParameterLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/SQLite/MultiComponentRelated/MultiComTableOperatorLibrary.h"
#include "DesignStation/SQLite/SingleComponentRelated/SingleComponentTableData.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/FolderLayoutUI/Widget_FolderItem.h"
#include "DesignStation/UI/FrontDirectory/FrontDirectoryWidget.h"
#include "DesignStation/UI/LoadUI/MergeProcessWidget.h"
#include "DesignStation/UI/LoadUI/OperationOnHoldWidget.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/FrontDirectory/FrontDirectoryWidget.h"
#include "Runtime/Slate/Public/Framework/Application/SlateApplication.h"

#define LOCTEXT_NAMESPACE "FolderWidget"

#define ServerDatabasePath FString("Cache/server_cache.db")
extern const int PopUIZOrder;
extern const FString RootParentID;

const FString NewCreateFolderName = TEXT("New Folder");
const FString NewCreateFileName = TEXT("New File");

UFolderWidget* UFolderWidget::FolderWidgetInstance = nullptr;
FString UFolderWidget::FolderWidgetPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/FolderWidgetUI.FolderWidgetUI_C'");
const FString DefaultMaterialPath = TEXT("Material'/Game/Materials/BaseMaterials/Null.Null'");

//门的类型参数名
extern const FString DoorRefTypeCode = TEXT("MBLX");

UFolderWidget* UFolderWidget::Get()
{
	if (!IS_OBJECT_PTR_VALID(UFolderWidget::FolderWidgetInstance))
	{
		UFolderWidget::FolderWidgetInstance = UUIFunctionLibrary::UIWidgetCreate<UFolderWidget>(UFolderWidget::FolderWidgetPath);
		UFolderWidget::FolderWidgetInstance->GenerateFolderLayout();
	}
	return UFolderWidget::FolderWidgetInstance;
}

bool UFolderWidget::IsSelectOnNewFolderTree(TMap<FString, FString>& InMap, UWidget_FolderItem* InSelectFolder)
{
	FString InID = TEXT("");
	FString InName = FString();
	if (!IS_OBJECT_PTR_VALID(InSelectFolder)) return false;//Not a new tree
	InID = InSelectFolder->GetItemData().id;
	InName = InSelectFolder->GetFolderName();

	if (InMap.Num() <= 0)//Map/Tree is Empty
	{
		InMap.Add(InID, InName);
		return true;
	}
	else//Map/tree exist
	{
		TArray<FString> WaitForDelete = TArray<FString>();
		bool RemoveSwitch = false;
		for (auto iter : InMap)//find current item inside tree
		{
			if (iter.Key.Equals(InID) && iter.Value.Equals(InName) && !RemoveSwitch)
			{
				RemoveSwitch = true;//exist
				continue;
			}
			if (RemoveSwitch) WaitForDelete.Add(iter.Key); // remove everything afterwards
		}
		for (auto ID : WaitForDelete)
		{
			InMap.Remove(ID);
		}
		if (RemoveSwitch)//select in the same tree
		{
			return false;
		}
		else//new tree need to build
		{
			InMap.Empty();
			InMap.Add(InID, InName);
			auto ParentFolderWidget = InSelectFolder->ParentFolderWidget.Get();
			while (IS_OBJECT_PTR_VALID(ParentFolderWidget))
			{
				InMap.Add(ParentFolderWidget->GetItemData().id, ParentFolderWidget->GetFolderName());
				ParentFolderWidget = ParentFolderWidget->ParentFolderWidget.Get();
			}
			return true;
		}
	}
	return false;
}

bool UFolderWidget::IsSelectOnNewFolderTree(TMap<FString, FString>& InMap, UWidget_FileItem* InSelectFolder)
{
	FString InID = TEXT("");
	FString InName = FString();
	if (!IS_OBJECT_PTR_VALID(InSelectFolder)) return false;//Not a new tree
	InID = InSelectFolder->GetItemData().id;
	InName = InSelectFolder->GetFileName();

	if (InMap.Num() <= 0)//Map/Tree is Empty
	{
		InMap.Add(InID, InName);
		return true;
	}
	else//Map/tree exist
	{
		TArray<FString> DeleteID;
		bool RemoveSwitch = false;
		for (auto iter : InMap)//find current item inside tree
		{
			if (iter.Key.Equals(InID) && iter.Value.Equals(InName))
			{
				RemoveSwitch = true;//exist
				continue;
			}
			if (RemoveSwitch) DeleteID.Add(iter.Key); // remove everything afterwards
		}
		for (auto del : DeleteID)
		{
			InMap.Remove(del);
		}
		if (RemoveSwitch)//select in the same tree
		{
			return false;
		}
		else//new tree need to build
		{
			InMap.Empty();
			InMap.Add(InID, InName);
			auto ParentFolderWidget = InSelectFolder->ParentFolderWidget.Get();
			while (IS_OBJECT_PTR_VALID(ParentFolderWidget))
			{
				InMap.Add(ParentFolderWidget->GetItemData().id, ParentFolderWidget->GetFolderName());
				ParentFolderWidget = ParentFolderWidget->ParentFolderWidget.Get();
			}
			return true;
		}
	}
	return false;
}

FReply UFolderWidget::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	if (InKeyEvent.GetKey() == EKeys::LeftShift
		|| InKeyEvent.GetKey() == EKeys::RightShift)
	{
		bShiftDown = true;
		return FReply::Handled();
	}

	return FReply::Unhandled();
}

FReply UFolderWidget::NativeOnKeyUp(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	if (InKeyEvent.GetKey() == EKeys::LeftShift
		|| InKeyEvent.GetKey() == EKeys::RightShift)
	{
		bShiftDown = false;
		return FReply::Handled();
	}

	return FReply::Unhandled();
}

void UFolderWidget::GenerateRootFolder(const TArray<FFolderTableData>& OutParams)
{
	for (int32 i = 0; i < OutParams.Num(); ++i)
	{
		GenerateRootFolderInner(OutParams[i]);

		/*UWidget_FolderItem* RootFolderTemp = UWidget_FolderItem::Create();
		RootFolderTemp->FolderSelectDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
		RootFolderTemp->FolderItemUnFoldDelegate.BindUFunction(this, FName(TEXT("UnFoldItem")));
		RootFolderTemp->FolderRightMenuActionDelegate.BindUFunction(this, FName(TEXT("FolderRightMenuActionEdit")));
		RootFolderTemp->SetItemData(OutParams[i]);
		RootFolderTemp->SetIndent(0);
		RootFolderTemp->UpdateFolderName(OutParams[i].folder_name);
		RootFolderTemp->UpdateFolderState(!FMath::IsNearlyZero(OutParams[i].visibility));
		RootFolderTemp->IsSuperiorVisible = !FMath::IsNearlyZero(OutParams[i].visibility);
		if (ScbFolderLayout)
		{
			ScbFolderLayout->AddChild(RootFolderTemp);
		}
		RootFolderWidgets.Add(RootFolderTemp);*/
		SetUploadeEnable(CurrentSelectItem || CurrentSelectFile);
	}
}

void UFolderWidget::GenerateRootFolderInner(const FFolderTableData& Data)
{
	UWidget_FolderItem* RootFolderTemp = UWidget_FolderItem::Create();
	RootFolderTemp->FolderSelectDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
	RootFolderTemp->FolderItemUnFoldDelegate.BindUFunction(this, FName(TEXT("UnFoldItem")));
	RootFolderTemp->FolderRightMenuActionDelegate.BindUFunction(this, FName(TEXT("FolderRightMenuActionEdit")));
	RootFolderTemp->SetItemData(Data);
	RootFolderTemp->SetIndent(0);
	RootFolderTemp->UpdateFolderName(Data.folder_name);
	RootFolderTemp->UpdateFolderState(!FMath::IsNearlyZero(Data.visibility));
	RootFolderTemp->IsSuperiorVisible = !FMath::IsNearlyZero(Data.visibility);
	if (ScbFolderLayout)
	{
		ScbFolderLayout->AddChild(RootFolderTemp);
	}
	RootFolderWidgets.Add(RootFolderTemp);
}

void UFolderWidget::SearchFileResult(const TArray<FFolderTableData>& OutParams)
{
	for (auto& Data : OutParams)
	{
		UWidget_FileItem* FileItem = UWidget_FileItem::Create();
		FileItem->UpdateFileName(Data.folder_name);
		FileItem->SetItemData(Data);
		FileItem->FileSelectDelegate.BindUFunction(this, FName(TEXT("SearchFileSelected")));
		FileItem->SetVisibility(ESlateVisibility::Visible);
		FileItem->UpdateFolderState(!FMath::IsNearlyZero(Data.visibility));
		SearchFiles.Add(FileItem);
		ScbSearch->AddChild(FileItem);
	}
}

void UFolderWidget::AddNewFolderFile(const FFolderTableData& OutParam)
{
	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{
		if (CurrentSelectItem->IsCkbExpandCheck())
		{
			if (OutParam.can_add_subfolder)
			{
				UWidget_FolderItem* NewFolder = UWidget_FolderItem::Create();
				NewFolder->FolderSelectDelegate.BindUFunction(this, FName(TEXT("SelectItemEdit")));
				NewFolder->FolderItemUnFoldDelegate.BindUFunction(this, FName(TEXT("UnFoldItem")));
				NewFolder->FolderRightMenuActionDelegate.BindUFunction(this, FName(TEXT("FolderRightMenuActionEdit")));
				NewFolder->UpdateFolderName(OutParam.folder_name);
				NewFolder->IsSuperiorVisible = CurrentSelectItem->GetCurrentVisibleState();
				NewFolder->UpdateFolderState(!FMath::IsNearlyZero(OutParam.visibility));
				NewFolder->SetVisibility(ESlateVisibility::Visible);
				NewFolder->ParentFolderWidget = CurrentSelectItem;
				NewFolder->SetIndent(CurrentSelectItem->GetCurrentFolderLevel() + 1);
				NewFolder->SetItemData(OutParam);
				CurrentSelectItem->AddFileToFolder(NewFolder);
				CurrentSelectItem->GetChildFolders().Add(OutParam.id, NewFolder);
			}
			else
			{
				//AddNewFileToDB(OutParam.id, OutParam.folder_type);
				UWidget_FileItem* NewFile = UWidget_FileItem::Create();
				NewFile->FileSelectDelegate.BindUFunction(this, FName(TEXT("SelectFileItemEdit")));
				NewFile->FileRightMenuActionDelegate.BindUFunction(this, FName(TEXT("FileRightMenuActionEdit")));
				NewFile->UpdateFileName(OutParam.folder_name);
				NewFile->IsSuperiorVisible = CurrentSelectItem->GetCurrentVisibleState();
				NewFile->UpdateFolderState(!FMath::IsNearlyZero(OutParam.visibility));
				NewFile->SetVisibility(ESlateVisibility::Visible);
				NewFile->ParentFolderWidget = CurrentSelectItem;
				NewFile->SetItemData(OutParam);
				NewFile->SetIndent(CurrentSelectItem->GetCurrentFolderLevel() + 1);
				CurrentSelectItem->AddFileToFolder(NewFile);
				CurrentSelectItem->GetChildFilesMap().Add(OutParam.id, NewFile);
			}
		}
		else
		{
			CurrentSelectItem->SetChildFolderExpand(true);
		}

		UE_LOG(LogTemp, Log, TEXT("FolderWidget---refresh view list"));
		CurrentSelectItem->RefreshContentWidget();
		SelectFolderDelegate.ExecuteIfBound(CurrentSelectItem, true);
	}
	else
	{//add root
		if (OutParam.can_add_subfolder)
		{
			GenerateRootFolderInner(OutParam);
		}
	}
}

void UFolderWidget::SwapTwoItemsAction()
{
	if (!IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		CurrentSelectItem->ParentFolderWidget.Get()->ReSelectAfterSwap(SwapDatas);
	}
	else
	{
		CurrentSelectFile->ParentFolderWidget.Get()->ReSelectAfterSwap(SwapDatas);
	}
	SwapDatas.Empty();
}

void UFolderWidget::SearchFolderPath(const TArray<FString>& OutParams)
{
	if (OutParams.Num() < 2)
	{
		UE_LOG(LogTemp, Log, TEXT("FolderWidget---Search folder path error"));
	}
	else
	{
		for (auto RootFolder : RootFolderWidgets)
		{
			if (RootFolder->GetItemData().id.Equals(OutParams[0]))
			{
				TArray<FString> SubFolderPath;
				for (int32 i = 1; i < OutParams.Num(); ++i)
				{
					SubFolderPath.Add(OutParams[i]);
				}
				RootFolder->SetSearchFolderExpand(SubFolderPath);
				break;
			}
		}
	}
}

void UFolderWidget::OpenFolderPathRecurse(const TArray<FString>& OutParams)
{
	UnGrayWidget();
	for (auto RootFolder : RootFolderWidgets)
	{
		if (RootFolder->GetItemData().id.Equals(OutParams[0]))
		{
			TArray<FString> SubFolderPath;
			for (int32 i = 1; i < OutParams.Num(); ++i)
			{
				SubFolderPath.Add(OutParams[i]);
			}
			RootFolder->OpenFolderExpand(SubFolderPath);
			break;
		}
	}
}

void UFolderWidget::GrayFolderPathRecurse(TArray<FString> GrayPath, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair)
{ //IsCheck : 当展开时，表示是否是中间Folder 
	if (GrayPath.IsEmpty())
		return;

	for (auto RootFolder : RootFolderWidgets)
	{
		if (RootFolder->GetItemData().id.Equals(GrayPath[0]))
		{
			if (GrayPath.Num() > 1)
			{
				GrayPath.RemoveAt(0);
				if (GrayPath.Num() >= 1)
					RootFolder->GrayFolderByAlreadyExpand(GrayPath, IsCheck, SelectIDFolderPair);
				else
					RootFolder->GrayFolderInnerAction(IsCheck, SelectIDFolderPair);
			}
			else
			{
				RootFolder->SetFolderToGrey(true);
				GrayWidget = RootFolder;
				//RootFolder->GrayFolderInnerAction(IsCheck, SelectIDFolderPair);
			}
			break;
		}
	}
}

void UFolderWidget::UnGrayWidget()
{
	if (IS_OBJECT_PTR_VALID(GrayWidget))
	{
		GrayWidget->SetFolderToGrey(false);
		GrayWidget = nullptr;
	}
}

void UFolderWidget::SetGrayWidget(UWidget_FolderItem* InWidget)
{
	UnGrayWidget();
	GrayWidget = InWidget;
}

bool UFolderWidget::GetChildFolderAndFile(const FString& InId, bool IsFolder, TArray<FFolderTableData>& OutDatas)
{
	FString LocalSQL = TEXT("");
	TArray<FFolderTableData> Datas;

	if (IsFolder)
	{
		TArray<FFolderTableData> SelfDatas;
		UFolderTableOperatorLibrary::FolderFileSearchByID(InId, EFolderFileSearchType::EFolderOnly, SelfDatas);
		if (SelfDatas.Num() > 0)
			OutDatas.Append(SelfDatas);
		//LocalSQL = FString::Printf(TEXT("select * from folder where id = '%s'"), *InId);
		//bool bSelected = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(LocalSQL, SelfDatas);
		//if (bSelected)
		//	OutDatas.Append(SelfDatas);
		//FString ChildASQL = FString::Printf(TEXT("select * from folder where parent_id = '%s'"), *InId);
		//TArray<FFolderTableData> ChildDatasA;
		//bool bSelectedA = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(ChildASQL, ChildDatasA);
		//if (bSelectedA)
		//	Datas.Append(ChildDatasA);
		//
		//FString ChildBSQL = FString::Printf(TEXT("select * from file where parent_id = '%s'"), *InId);
		//TArray<FFolderTableData> ChildDatasB;
		//bool bSelectedB = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(ChildBSQL, ChildDatasB);
		//if (bSelectedB)
		//	Datas.Append(ChildDatasB);
		TArray<FFolderTableData> ChildDatas;
		UFolderTableOperatorLibrary::FolderFileSearchByParentID(InId, EFolderFileSearchType::EFolderAndFile, ChildDatas);
		for (auto& iter : ChildDatas)
		{
			GetChildFolderAndFile(iter.id, iter.can_add_subfolder, OutDatas);
		}
	}
	else
	{
		TArray<FFolderTableData> SelfDatas;
		UFolderTableOperatorLibrary::FolderFileSearchByID(InId, EFolderFileSearchType::EFileOnly, SelfDatas);
		if (SelfDatas.Num() > 0)
			OutDatas.Append(SelfDatas);
		//LocalSQL = FString::Printf(TEXT("select * from file where id = '%s'"), *InId);
		//TArray<FFolderTableData> SelfDatas;
		//bool bSelected = FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(LocalSQL, SelfDatas);
		//if (bSelected)
		//	OutDatas.Append(SelfDatas);
	}
	return true;
}

bool UFolderWidget::InsertDataToServerDatabase(TArray<FFolderTableData>& OutDatas)
{
#ifdef USE_REF_LOCAL_FILE

#else
	if (MergeProcessWidget)
	{
		MergeProcessWidget->SetPercent(0.5f);
	}

	if (OutDatas.Num() > 0 && !OutDatas[0].parent_id.Equals(TEXT("-1")))
	{
		TArray<FFolderDataDB> Parents;
		FString SelectSql = FString::Printf(TEXT("select * from folder where id = '%s'"), *OutDatas[0].parent_id);
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFolderDataDB>(SelectSql, Parents);
		if (Parents.Num() <= 0)
		{
			UE_LOG(LogTemp, Log, TEXT("UFolderWidget::InsertDataToServerDatabase has no parent folder %s"), *OutDatas[0].id);
			return false;
		}
	}

	ACatalogPlayerController* CatalogPC = ACatalogPlayerController::Get();
	FilesToUpload.Empty();
	for (auto& iter : OutDatas)
	{
		if (iter.can_add_subfolder)
		{
			if (iter.parent_id != TEXT("- 1"))
			{
				FString SelectMate = FString::Printf(TEXT("select * from folder where parent_id = '%s' "), *iter.parent_id);
				TArray<FFolderDataDB> FolderMates;
				FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFolderDataDB>(SelectMate, FolderMates);
				bool bExistInServer = false;
				bool bHasOrder = false;
				int32 MaxOrder = iter.folder_order;
				for (auto& mateIter : FolderMates)
				{
					if (mateIter.id == iter.id)
					{
						bExistInServer = true;
						continue;
					}
					TArray<FFolderDataDB> Self;
					FString SelectSelf = FString::Printf(TEXT("select * from folder where id = '%s' "), *mateIter.id);
					FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderDataDB>(SelectSelf, Self);
					if (Self.Num() > 0)
					{
						if (Self[0].folder_order == iter.folder_order)
							bHasOrder = true;
						FString UpdateServerSelf = FString::Printf(TEXT("update  folder  set folder_order = %d where id = '%s'"), Self[0].folder_order, *mateIter.id);
						FLocalDatabaseOperatorLibrary::UpdateDataFromServerDataBaseBySQL(UpdateServerSelf);
						if (Self[0].folder_order > MaxOrder)
							MaxOrder = Self[0].folder_order;
					}
				}
				if (!bExistInServer && bHasOrder && FolderMates.Num() > 0)
					iter.folder_order = MaxOrder + 1;
			}

			/*	FString DeleteSql = FString::Printf(TEXT("delete from folder where id = '%s' or parent_id = '%s'"), *iter.id, *iter.id);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteSql);
				FString DeleteFileSql = FString::Printf(TEXT("delete from file where parent_id = '%s'"), *iter.id);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteFileSql);*/
			DeleteFolder(iter.id, true);


			FString InsertSql = FString::Printf(TEXT("insert into folder values('%s','%s',%d,'%s','%s',%f,%d)")
				, *iter.id, *iter.folder_name, (int32)iter.folder_type, *iter.parent_id, *iter.visibility_exp, iter.visibility, iter.folder_order);
			if (FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertSql))
			{
				FString  ParamSql = FString::Printf(TEXT("select * from folder_param where main_id = '%s'"), *iter.id);
				TArray<FParameterTableData> ParamDatas;
				if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(ParamSql, ParamDatas))
				{
					for (auto& iter : ParamDatas)
					{

						FString  ParamDeleteSql = FString::Printf(TEXT("delete from folder_param where id = '%s'"), *iter.id);
						FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(ParamDeleteSql);

						FString  InsertParamSql = FString::Printf(TEXT("insert into folder_param values('%s','%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s','%s')")
							, *iter.id, *iter.name, *iter.description, iter.classific_id, *iter.value, *iter.expression, *iter.max_value, *iter.max_expression
							, *iter.min_value, *iter.min_expression, *iter.visibility, *iter.visibility_exp, *iter.editable, *iter.editable_exp, iter.is_enum, *iter.param_id, *iter.main_id);
						FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParamSql);
						if (iter.is_enum != 0)
						{
							FString  EnumSql = FString::Printf(TEXT("select * from folder_param_enum where main_id = '%s'"), *iter.id);
							TArray<FEnumParameterTableData>EnumDatas;
							if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(EnumSql, EnumDatas))
							{
								for (auto& enumIter : EnumDatas)
								{
									FString  EnumDeleteSql = FString::Printf(TEXT("delete from folder_param_enum where id = '%s'"), *iter.id);
									FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(EnumDeleteSql);

									FString  InsertParamEnumSql = FString::Printf(TEXT("insert into folder_param_enum values('%s','%s','%s','%s','%s','%s','%s','%s','%s')")
										, *enumIter.id, *enumIter.value, *enumIter.expression, *enumIter.name_for_display, *enumIter.image_for_display, *enumIter.visibility, *enumIter.visibility_exp, *enumIter.priority, *enumIter.main_id);
									FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParamEnumSql);

									FString DeleteImageSql = FString::Printf(TEXT("delete from param_image where path = '%s'"), *enumIter.image_for_display);
									FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteImageSql);
									if (!enumIter.image_for_display.IsEmpty())
									{
										FDownloadFileData ThumbnailInfo;
										FDownloadFileDataLibrary::RetriveFileMD5(TEXT("param_image"), enumIter.image_for_display, ThumbnailInfo);
										FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("param_image"), ThumbnailInfo);
										if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + enumIter.image_for_display)))
											FilesToUpload.AddUnique(enumIter.image_for_display);
									}
								}
							}
						}
					}
				}
			}
		}
		else
		{

			FString SelectMate = FString::Printf(TEXT("select * from file where parent_id = '%s' "), *iter.parent_id);
			TArray<FFileDBData> FileMates;
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFileDBData>(SelectMate, FileMates);
			bool bExistInServer = false;
			bool bHasOrder = false;
			int32 MaxOrder = iter.folder_order;
			for (auto& mateIter : FileMates)
			{
				if (mateIter.id == iter.id)
				{
					bExistInServer = true;
					continue;
				}

				TArray<FFileDBData> Self;
				FString SelectSelf = FString::Printf(TEXT("select * from file where id = '%s' "), *mateIter.id);
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(SelectSelf, Self);
				if (Self.Num() > 0)
				{
					if (Self[0].folder_order == iter.folder_order)
						bHasOrder = true;
					FString UpdateServerSelf = FString::Printf(TEXT("update  file  set folder_order = %d where id = '%s'"), Self[0].folder_order, *mateIter.id);
					FLocalDatabaseOperatorLibrary::UpdateDataFromServerDataBaseBySQL(UpdateServerSelf);
					if (Self[0].folder_order > MaxOrder)
						MaxOrder = Self[0].folder_order;
				}
			}
			if (!bExistInServer && bHasOrder && FileMates.Num() > 0)
				iter.folder_order = MaxOrder + 1;

			FString DeleteSql = FString::Printf(TEXT("delete from file where id = '%s'"), *iter.id);
			FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteSql);
			FString DeleteFileParamSql = FString::Printf(TEXT("delete from file_param where main_id = '%s'"), *iter.id);
			FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteFileParamSql);

			FString SelectFile = FString::Printf(TEXT("select * from file"));
			TArray<FFileDBData> ServerDatas;
			FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFileDBData>(SelectFile, ServerDatas);
			for (auto& serIter : ServerDatas)
			{
				if (iter.folder_id.IsEmpty())
					continue;
				if (!serIter.id.Equals(iter.id) && serIter.folder_id.Equals(iter.folder_id))
				{
					UE_LOG(LogTemp, Log, TEXT("UFolderWidget::InsertDataToServerDatabase invalid folder_id %s"), *serIter.folder_id);
					return false;
				}
			}

			FString Sql = FString::Printf(TEXT("insert into file values('%s','%s','%s','%s','%s',%d,'%s','%s',%f,%d,%d,'%s')")
				, *iter.id, *iter.folder_id, *iter.folder_name, *iter.folder_code_exp, *iter.folder_code, iter.folder_type, *iter.thumbnail_path, *iter.visibility_exp, iter.visibility, iter.is_new, iter.folder_order, *iter.parent_id);
			UploadImageUUID = CatalogPC->UploadFileRequest(iter.thumbnail_path);
			if (FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(Sql))
			{

				FString DeleteImageSql = FString::Printf(TEXT("delete from file_image where path = '%s'"), *iter.thumbnail_path);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteImageSql);

				if (!iter.thumbnail_path.IsEmpty())
				{
					FDownloadFileData ThumbnailInfo;
					FDownloadFileDataLibrary::RetriveFileMD5(TEXT("file_image"), iter.thumbnail_path, ThumbnailInfo);
					FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("file_image"), ThumbnailInfo);
					if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.thumbnail_path)))
						FilesToUpload.AddUnique(iter.thumbnail_path);
				}
				FString  ParamSql = FString::Printf(TEXT("select * from file_param where main_id = '%s'"), *iter.id);
				TArray<FParameterTableData> ParamDatas;
				if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(ParamSql, ParamDatas))
				{
					for (auto& iter : ParamDatas)
					{
						FString  ParamDeleteSql = FString::Printf(TEXT("delete from file_param where id = '%s'"), *iter.id);
						FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(ParamDeleteSql);

						FString  EnumDeleteSql = FString::Printf(TEXT("delete from file_param_enum where main_id = '%s'"), *iter.id);
						FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(EnumDeleteSql);

						FString  InsertParamSql = FString::Printf(TEXT("insert into file_param values('%s','%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s','%s')")
							, *iter.id, *iter.name, *iter.description, iter.classific_id, *iter.value, *iter.expression, *iter.max_value, *iter.max_expression
							, *iter.min_value, *iter.min_expression, *iter.visibility, *iter.visibility_exp, *iter.editable, *iter.editable_exp, iter.is_enum, *iter.param_id, *iter.main_id);
						FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParamSql);
						if (iter.is_enum != 0)
						{
							FString  EnumSql = FString::Printf(TEXT("select * from file_param_enum where main_id = '%s'"), *iter.id);
							TArray<FEnumParameterTableData>EnumDatas;
							if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(EnumSql, EnumDatas))
							{
								for (auto& enumIter : EnumDatas)
								{
									FString  InsertParamEnumSql = FString::Printf(TEXT("insert into file_param_enum values('%s','%s','%s','%s','%s','%s','%s','%s','%s')")
										, *enumIter.id, *enumIter.value, *enumIter.expression, *enumIter.name_for_display, *enumIter.image_for_display, *enumIter.visibility, *enumIter.visibility_exp, *enumIter.priority, *enumIter.main_id);
									FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParamEnumSql);

									FString DeleteImageSql = FString::Printf(TEXT("delete from param_image where path = '%s'"), *enumIter.image_for_display);
									FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteImageSql);

									if (!enumIter.image_for_display.IsEmpty())
									{
										FDownloadFileData ThumbnailInfo;
										FDownloadFileDataLibrary::RetriveFileMD5(TEXT("param_image"), enumIter.image_for_display, ThumbnailInfo);
										FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("param_image"), ThumbnailInfo);
										if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + enumIter.image_for_display)))
											FilesToUpload.AddUnique(enumIter.image_for_display);

									}
								}
							}
						}
					}
				}
			}
			if (iter.folder_type == EFolderType::ESingleComponent)
			{
				FString DeleteSingle = FString::Printf(TEXT("delete from single_comp where folder_id = '%s'"), *iter.id);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteSingle);
				FString SelectSingleSql = FString::Printf(TEXT("select * from single_comp where folder_id = '%s'"), *iter.id);
				TArray<FSingleComponentTableData> SingleDatas;
				if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FSingleComponentTableData>(SelectSingleSql, SingleDatas))
				{
					for (auto& singleIter : SingleDatas)
					{
						FString InsertSingleSql = FString::Printf(TEXT("insert into single_comp values(%d,'%s','%s','%s')")
							, singleIter.id, *singleIter.data_path, *singleIter.depend_files, *singleIter.folder_id);
						FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertSingleSql);

						if (!singleIter.data_path.IsEmpty())
						{
							FDownloadFileData ThumbnailInfo;
							FDownloadFileDataLibrary::RetriveFileMD5(TEXT("other_file"), singleIter.data_path, ThumbnailInfo);
							FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("other_file"), ThumbnailInfo);
							if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + singleIter.data_path)))
								FilesToUpload.AddUnique(singleIter.data_path);
						}

						TArray<FString> Paths;
						singleIter.FilePathsAndMD5Pairs(Paths);
						for (auto& depIter : Paths)
						{
							FDownloadFileData ThumbnailInfo;
							FDownloadFileDataLibrary::RetriveFileMD5(TEXT("other_file"), depIter, ThumbnailInfo);
							FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("other_file"), ThumbnailInfo);
							if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + depIter)))
								FilesToUpload.AddUnique(depIter);
						}
					}
				}
			}
			else if (iter.folder_type == EFolderType::EMultiComponents)
			{
				FString DeleteMulti = FString::Printf(TEXT("delete from multi_comp where folder_id = '%s'"), *iter.id);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteMulti);


				FString SelectSql = FString::Printf(TEXT("select * from multi_comp where folder_id = '%s'"), *iter.id);
				TArray<FMultiComponentDataDB> Datas;
				FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FMultiComponentDataDB>(SelectSql, Datas);
				for (auto& multiIter : Datas)
				{
					{//插入多部件引用信息
						FString InsertSql = FString::Printf(TEXT("insert into multi_comp values ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s')")
							, *multiIter.id, *multiIter.visibility_exp, *multiIter.visibility_value, *multiIter.component_id_exp, *multiIter.component_id_value, *multiIter.description
							, *multiIter.location_x_exp, *multiIter.location_x_value, *multiIter.location_y_exp, *multiIter.location_y_value, *multiIter.location_z_exp, *multiIter.location_z_value
							, *multiIter.rotation_roll_exp, *multiIter.rotation_roll_value, *multiIter.rotation_pitch_exp, *multiIter.rotation_pitch_value, *multiIter.rotation_yaw_exp, *multiIter.rotation_yaw_value
							, *multiIter.scale_x_exp, *multiIter.scale_x_value, *multiIter.scale_y_exp, *multiIter.scale_y_value, *multiIter.scale_z_exp, *multiIter.scale_z_value, multiIter.component_order, *multiIter.folder_id);
						FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertSql);
					}
					FString DeleteMultiParam = FString::Printf(TEXT("delete from multi_param where main_id = '%s'"), *multiIter.id);
					FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteMultiParam);

					FString SelectMultiParam = FString::Printf(TEXT("select * from multi_param where main_id = '%s'"), *multiIter.id);
					TArray<FParameterTableData> MultiParams;
					if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FParameterTableData>(SelectMultiParam, MultiParams))
					{
						for (auto& multiParamIter : MultiParams)
						{
							FString  EnumDeleteSql = FString::Printf(TEXT("delete from multi_param_enum where main_id = '%s'"), *multiParamIter.id);
							FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(EnumDeleteSql);

							FString InsertSql = FString::Printf(TEXT("insert into multi_param values('%s','%s','%s',%d,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d,'%s','%s')")
								, *multiParamIter.id, *multiParamIter.name, *multiParamIter.description, multiParamIter.classific_id, *multiParamIter.value, *multiParamIter.expression, *multiParamIter.max_value, *multiParamIter.max_expression
								, *multiParamIter.min_value, *multiParamIter.min_expression, *multiParamIter.visibility, *multiParamIter.visibility_exp, *multiParamIter.editable, *multiParamIter.editable_exp, multiParamIter.is_enum, *multiParamIter.param_id, *multiParamIter.main_id);
							FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertSql);
							if (multiParamIter.is_enum != 0)
							{
								FString  EnumSql = FString::Printf(TEXT("select * from multi_param_enum where main_id = '%s'"), *multiParamIter.id);
								TArray<FEnumParameterTableData>EnumDatas;
								if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(EnumSql, EnumDatas))
								{
									for (auto& enumIter : EnumDatas)
									{
										FString  InsertParamEnumSql = FString::Printf(TEXT("insert into multi_param_enum values('%s','%s','%s','%s','%s','%s','%s','%s','%s')")
											, *enumIter.id, *enumIter.value, *enumIter.expression, *enumIter.name_for_display, *enumIter.image_for_display, *enumIter.visibility, *enumIter.visibility_exp, *enumIter.priority, *enumIter.main_id);
										FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParamEnumSql);

										FString DeleteImageSql = FString::Printf(TEXT("delete from param_image where path = '%s'"), *enumIter.image_for_display);
										FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteImageSql);

										if (!enumIter.image_for_display.IsEmpty())
										{
											FDownloadFileData ThumbnailInfo;
											FDownloadFileDataLibrary::RetriveFileMD5(TEXT("other_file"), enumIter.image_for_display, ThumbnailInfo);
											FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("other_file"), ThumbnailInfo);
											if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + enumIter.image_for_display)))
												FilesToUpload.AddUnique(enumIter.image_for_display);
										}
									}
								}
							}
						}
					}
				}
			}
			else if (iter.folder_type == EFolderType::EMaterial)
			{
				FString DeleteMat = FString::Printf(TEXT("delete from material where folder_id = '%s'"), *iter.id);
				FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteMat);
				FString SelectMatSql = FString::Printf(TEXT("select * from material where folder_id = '%s'"), *iter.id);
				TArray<FCustomMaterialTableData> MatDatas;
				if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FCustomMaterialTableData>(SelectMatSql, MatDatas))
				{
					for (auto& matIter : MatDatas)
					{
						UE_LOG(LogTemp, Warning, TEXT("mat ref path : %s"), *matIter.ref_path);
						const FString InsertMatSql = FString::Printf(TEXT("insert into material (REF_PATH,IMPORT_PATH,FOLDER_ID) values(\"%s\",'%s','%s')"), *matIter.ref_path, *matIter.import_path, *matIter.folder_id);
						bool InsertMatRes = FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertMatSql);
						UE_LOG(LogTemp, Warning, TEXT("insert mat ref info res [%d] "), InsertMatRes);

						if (!matIter.import_path.IsEmpty())
						{
							FDownloadFileData ThumbnailInfo;
							FDownloadFileDataLibrary::RetriveFileMD5(TEXT("other_file"), matIter.import_path, ThumbnailInfo);
							FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("other_file"), ThumbnailInfo);
							if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + matIter.import_path)))
								FilesToUpload.AddUnique(matIter.import_path);
						}
					}
				}
			}
		}
	}

	ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
	LocalDBSubsystem->CloseServerDatabase();
#endif
	return true;
}

void UFolderWidget::CompareFileData()
{
	TArray<FFolderTableData> ServerDatas;
	FString  ServerSql = FString::Printf(TEXT("select * from file"));
	FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFolderTableData>(ServerSql, ServerDatas);
	TArray<FString> DownloadPaths;
	for (auto& serverIter : ServerDatas)
	{
		FString  LocDataSql = FString::Printf(TEXT("select * from file where id = '%s'"), *serverIter.id);
		TArray<FFolderTableData> LocDatas;
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFolderTableData>(LocDataSql, LocDatas))
		{
			const FString ServerMD5 = FDownloadFileDataLibrary::RetriveServerFileMD5(TEXT("file_image"), serverIter.thumbnail_path);
			const FString LocalMD5 = FDownloadFileDataLibrary::RetriveServerFileMD5(TEXT("file_image"), LocDatas[0].thumbnail_path);
			if (!ServerMD5.Equals(LocalMD5, ESearchCase::IgnoreCase))
			{
				DownloadPaths.Add(serverIter.thumbnail_path);
			}
		}
		if (serverIter.folder_type == EFolderType::ESingleComponent)
		{
			FString  SingleDataSql = FString::Printf(TEXT("select * from file where folder_id = '%s'"), *serverIter.id);
			TArray<FSingleComponentTableData> ServerSingleDatas;
			TArray<FSingleComponentTableData> LocSingleDatas;

			bool FindSer = FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FSingleComponentTableData>(SingleDataSql, ServerSingleDatas);
			bool FindLoc = FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FSingleComponentTableData>(SingleDataSql, LocSingleDatas);
			if (FindSer && FindLoc /*&& (ServerSingleDatas[0].file_md5 != LocSingleDatas[0].file_md5)*/)
			{
				//UploadPaths.Add(LocSingleDatas[0].data_path);
			}
		}
	}
	//if (UploadPaths.Num() <= 0)
	//	MergeData();
	//else
	//{
	//	for (auto& iter : UploadPaths)
	//		UploadUUIDs .Add(ACatalogPlayerController::Get()->UploadFileRequest(iter));
	//	
	//}
		//DownloadFileUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(UploadPaths);
}

void UFolderWidget::MergeData()
{
#ifdef USE_REF_LOCAL_FILE

#else
	UWidget_FileItem* FileItem = GetCurrentSelectFile();
	UWidget_FolderItem* FolderItem = GetCurrentSelectItem();
	FString Id = TEXT("");
	int32 SelectType = -1;
	if (FileItem)
	{
		Id = FileItem->GetItemData().id;
		SelectType = 0;
	}
	else if (FolderItem)
	{
		Id = FolderItem->GetItemData().id;
		SelectType = 1;
	}
	if (Id.IsEmpty())
		return;
	if (MergeProcessWidget)
	{

		MergeProcessWidget->SetPercent(0.4f);

		TArray<FFolderTableData> Datas;
		GetChildFolderAndFile(Id, SelectType == 1, Datas);
		if (!InsertDataToServerDatabase(Datas))
		{
			UploadSuccess(false);
			return;
		}
		ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
		UploadDatabaseUUID = CatalogPC->UploadFileRequest(ServerDatabasePath);
	}
#endif
}

void UFolderWidget::DeleteFolder(const FString& InId, bool IsFolder)
{
	if (IsFolder)
	{
		UFolderTableOperatorLibrary::DeleteServerFolderFile(InId, true);
		FString DeleteFolderParamSql = FString::Printf(TEXT("delete from folder_param where main_id = '%s'"), *InId);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteFolderParamSql);
		TArray<FFolderDataDB> CFolder;
		FString SelectFolder = FString::Printf(TEXT("select * from folder where parent_id = '%s' "), *InId);
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFolderDataDB>(SelectFolder, CFolder);
		for (auto& iter : CFolder)
		{
			DeleteFolder(iter.id, true);
		}
		TArray<FFileDBData> CFile;
		FString SelectFile = FString::Printf(TEXT("select * from file where parent_id = '%s' "), *InId);
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FFileDBData>(SelectFile, CFile);
		for (auto& iter : CFile)
		{
			DeleteFolder(iter.id, false);
		}
	}
	else
	{
		UFolderTableOperatorLibrary::DeleteServerFolderFile(InId, false);
		FString DeleteFolderParamSql = FString::Printf(TEXT("delete from file_param where main_id = '%s'"), *InId);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(DeleteFolderParamSql);
	}
}

void UFolderWidget::SetUploadeEnable(bool IsTrue)
{
	BtnRelease->SetIsEnabled(IsTrue);
}

void UFolderWidget::UploadSuccess(bool IsTrue)
{
	if (IsTrue)
	{
		MergeProcessWidget->SetPercent(1.f);
	}
	else
	{
		MergeProcessWidget->SetSuccess(false);
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->CloseServerDatabase();
	}
	if (IsTrue)
	{
		FLatentActionInfo LatentActionInfo;
		LatentActionInfo.CallbackTarget = this;
		LatentActionInfo.ExecutionFunction = TEXT("RemoveProcess");
		LatentActionInfo.Linkage = 1;
		LatentActionInfo.UUID = 100;
		UKismetSystemLibrary::Delay(this, 3.f, LatentActionInfo);
	}
}

void UFolderWidget::ClearUnVaildMultiSelect(UWidget_FolderItem* InFolder)
{
	auto& Files = InFolder->GetChildFiles();
	auto& Folders = InFolder->GetChildFolders();

	//for(auto)
}

bool UFolderWidget::IsInMultiSelect(UWidget_FolderItem* InFolder)
{
	if (!FSlateApplication::Get().GetModifierKeys().IsShiftDown())
		return false;

	if (SelectedItems.IsEmpty())
		return false;

	for (auto Ite : SelectedItems)
	{
		if (Ite != nullptr && Ite->IsA<UWidget_FolderItem>())
		{
			if (Cast<UWidget_FolderItem>(Ite)->GetItemData().id.Equals(InFolder->GetItemData().id))
				return true;
		}
	}

	return false;
}

bool UFolderWidget::IsInMultiSelect(UWidget_FileItem* InFolder)
{
	if (!FSlateApplication::Get().GetModifierKeys().IsShiftDown())
		return false;

	if (SelectedItems.IsEmpty())
		return false;

	for (auto Ite : SelectedItems)
	{
		if (Ite != nullptr && Ite->IsA<UWidget_FileItem>())
		{
			if (Cast<UWidget_FileItem>(Ite)->GetItemData().id.Equals(InFolder->GetItemData().id))
				return true;
		}
	}

	return false;
}

void UFolderWidget::UpdateMultiSelectItem(UWidget_FolderItem* InItem)
{
	int32 FindWidgetIndex = SelectedItems.IndexOfByPredicate([InItem](UFolderAndFileBaseWidget* InWidget) {
		if (InWidget != nullptr && InWidget->IsA<UWidget_FolderItem>())
		{
			if (Cast<UWidget_FolderItem>(InWidget)->GetItemData().id.Equals(InItem->GetItemData().id))
				return true;
		}

		return false;
		});

	if (FindWidgetIndex != INDEX_NONE)
	{
		SelectedItems.RemoveAt(FindWidgetIndex);
		SelectedItems.Insert(InItem, FindWidgetIndex);
	}
}

void UFolderWidget::UpdateMultiSelectItem(UWidget_FileItem* InItem)
{
	int32 FindWidgetIndex = SelectedItems.IndexOfByPredicate([InItem](UFolderAndFileBaseWidget* InWidget) {
			if (InWidget != nullptr && InWidget->IsA<UWidget_FileItem>())
			{
				if (Cast<UWidget_FileItem>(InWidget)->GetItemData().id.Equals(InItem->GetItemData().id))
					return true;
			}
			return false;
		});

	if (FindWidgetIndex != INDEX_NONE)
	{
		SelectedItems.RemoveAt(FindWidgetIndex);
		SelectedItems.Add(InItem);
	}
}

void UFolderWidget::CreateNewCustomMatFile(const FCustomMaterialTableData& MatData)
{
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		TArray<FCustomMatParameterTableData> EmptyCustomMatParam;
	}
}

void UFolderWidget::SyncSelectLocalRefData(const FFolderTableData& Data)
{
	FRefDirectoryData DirData;
	URefRelationFunction::ConvertDBDataToDirctoryData(Data, DirData);
	const FString RefNameID = URefRelationFunction::GetMarkToBackendDirectory(DirData);

	if(URefRelationFunction::NeedDownloadFile(DirData))
	{
		/*
		*  @@ need download file 
		*  @@ if download file success , open file and sync data
		*  @@ if download file failed , execute empty data
		*  @@ execute select UI widget
		*/


		const FString FileRelPath = URefRelationFunction::GetRefFileRelativePath(DirData);

		TArray<FString> NeedDownload;
		NeedDownload.Add(FileRelPath);
		if (!DirData.thumbnailPath.IsEmpty())
		{
			NeedDownload.Add(DirData.thumbnailPath);
		}
		//NetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(FileRelPath);
		NetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(NeedDownload);

	}
	else
	{
		/*
		*  @@ no need download file
		*  @@ execute select UI widget
		*/
		//SyncRefLocalData(RefNameID);
		SyncRefLocalData_Inner(Data);
		SelfSelectWidgetAfterDownload(RefNameID);
	}
}

void UFolderWidget::SelfSelectWidgetAfterDownload(const FString& IDOrFolderID)
{
	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{
		const FString FolderID = CurrentSelectItem->GetItemData().id;
		if (FolderID.Equals(IDOrFolderID))
		{
			SelectFolderDelegate.ExecuteIfBound(CurrentSelectItem, true);
		}
		else if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
		{
			SelectFileDelegate.ExecuteIfBound(CurrentSelectFile);
		}

		//Temp logic
		ForceSyncParamEnumData();
	}
}

void UFolderWidget::UpdateTopLevelParameters(const TArray<FParameterData>& InParams, bool Delete)
{
	if(SelectRefData.IsValid())
	{
		bool NeedUpdate = false;
		if (Delete)
		{
			NeedUpdate = true;
			//SelectRefData.ParamDatas = InParams;
			TArray<int32> RemoveIndex;
			for(int32 i = SelectRefData.ParamDatas.Num() - 1; i >= 0 ; --i)
			{
				const int32 ParamIndex = InParams.IndexOfByPredicate(
					[SelectRefData = SelectRefData.ParamDatas[i]](const FParameterData& ArrData)->bool
					{
						return ArrData.Data.id.Equals(SelectRefData.Data.id, ESearchCase::CaseSensitive);
					}
				);
				if (ParamIndex == INDEX_NONE)
				{
					RemoveIndex.Add(i);
				}
			}
			for (int32 i = 0; i <= RemoveIndex.Num() - 1; ++i)
			{
				SelectRefData.ParamDatas.RemoveAt(RemoveIndex[i]);
			}
		}
		else
		{
			if (InParams.Num() != SelectRefData.ParamDatas.Num())
			{
				NeedUpdate = true;
			}
			for(int32 PIndex = 0; PIndex < InParams.Num(); ++PIndex)
			{
				auto& Iter = InParams[PIndex];

				const int32 ParamIndex = SelectRefData.ParamDatas.IndexOfByPredicate(
					[Iter](const FParameterData& ArrData)->bool
					{
						return Iter.Data.id.Equals(ArrData.Data.id, ESearchCase::CaseSensitive);
					}
				);
				if (ParamIndex == INDEX_NONE)
				{//new
					SelectRefData.ParamDatas.Insert(Iter, PIndex);
					//SelectRefData.ParamDatas.Add(Iter);
					NeedUpdate = true;
				}
				else
				{
					if (!SelectRefData.ParamDatas[ParamIndex].Equal_Precise(Iter))
					{
						SelectRefData.ParamDatas[ParamIndex] = Iter;
						NeedUpdate = true;
					}

					if (PIndex != ParamIndex)
					{
						SelectRefData.ParamDatas.Swap(PIndex, ParamIndex);
						NeedUpdate = true;
					}
				}
			}

		}

		//confirm params
		TArray<FParameterData> CleanParams = URefRelationFunction::FormatCleanParams(SelectRefData.ParamDatas);
		if (CleanParams.Num() != SelectRefData.ParamDatas.Num())
		{
			NeedUpdate = true;
			SelectRefData.ParamDatas = CleanParams;
		}

		if (NeedUpdate)
		{
			FString MarkID = SelectRefData.FolderDBData.folder_id.IsEmpty() ?
				SelectRefData.FolderDBData.id : (SelectRefData.FolderDBData.is_folder ? SelectRefData.FolderDBData.id : SelectRefData.FolderDBData.folder_id);

			FString FormatID = URefRelationFunction::FormatFolderID(MarkID);

			AddCacheDataForFile(FormatID, SelectRefData);

			RefreshLocalRefFile();

			FRefDirectoryData CurRefData;
			if (GetCacheDataForRefDirectory(SelectRefData.FolderDBData.id, CurRefData))
			{
				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FormatID);

				int64 FileSize = 0;
				const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
					FPaths::ProjectContentDir(),
					FileRelativePath
				));
				ACatalogPlayerController::GetFileMD5AndSize(AbsPath, CurRefData.md5, FileSize);

				/*
				*  @@ update ref file update user
				*/
				CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

				//sync ref type code / value
				SyncRefType(SelectRefData.ParamDatas, CurRefData.RefTypeCode, CurRefData.RefType);

				UpdateDataRequest(CurRefData);
				UploadFileRequest(FileRelativePath);

				ReSelectCurrentItem();
				
			}
		}
		
	}
}

void UFolderWidget::ForceSyncParamEnumData()
{
	if (SelectRefData.IsValid())
	{
		bool NeedUpdate = false;
		for (auto& Iter : SelectRefData.ParamDatas)
		{
			for (auto& ED : Iter.EnumData)
			{
				if (ED.expression.IsEmpty() && !ED.value.IsEmpty())
				{
					NeedUpdate = true;
					ED.expression = ED.value;
				}
			}
		}

		if (NeedUpdate)
		{
			FString MarkID = SelectRefData.FolderDBData.folder_id.IsEmpty() ?
				SelectRefData.FolderDBData.id : (SelectRefData.FolderDBData.is_folder ? SelectRefData.FolderDBData.id : SelectRefData.FolderDBData.folder_id);

			FString FormatID = URefRelationFunction::FormatFolderID(MarkID);

			AddCacheDataForFile(FormatID, SelectRefData);

			RefreshLocalRefFile();

			FRefDirectoryData CurRefData;
			if (GetCacheDataForRefDirectory(SelectRefData.FolderDBData.id, CurRefData))
			{
				FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FormatID);

				int64 FileSize = 0;
				const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
					FPaths::ProjectContentDir(),
					FileRelativePath
				));
				ACatalogPlayerController::GetFileMD5AndSize(AbsPath, CurRefData.md5, FileSize);

				/*
				*  @@ update ref file update user
				*/
				CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

				//sync ref type code / value
				SyncRefType(SelectRefData.ParamDatas, CurRefData.RefTypeCode, CurRefData.RefType);

				UpdateDataRequest(CurRefData);
				UploadFileRequest(FileRelativePath);

				ReSelectCurrentItem();

			}
		}

	}
}

//void UFolderWidget::UpdateFilePropertyData(const FFolderTableData& InProperty)
//{
//	if(SelectRefData.IsValid())
//	{
//		SelectRefData.FolderDBData.UpdateProperty(
//			InProperty.folder_id, InProperty.folder_name, InProperty.folder_name_exp,
//			InProperty.folder_code, InProperty.folder_code_exp,
//			InProperty.visibility_exp, InProperty.visibility, InProperty.is_new, TEXT(""), InProperty.backend_folder_path
//		);
//		RefreshLocalRefFile();
//	}
//	FRefDirectoryData Data;
//	if (GetCacheDataForRefDirectory(InProperty.id, Data))
//	{
//		Data.folderCode = InProperty.folder_code;
//		Data.folderCodeExp = InProperty.folder_code_exp;
//		Data.folderId = InProperty.folder_id;
//		Data.folderName = InProperty.folder_name;
//
//		UpdateDataRequest(Data);
//	}
//}

void UFolderWidget::AddCacheDataForFile(const FString& InID, const FRefToLocalFileData& InData)
{
	if(!InID.IsEmpty())
	{
		if (CacheRefData.Contains(InID))
		{
			CacheRefData[InID] = InData;
		}
		else
		{
			CacheRefData.Add(InID, InData);
		}
		
	}
}

void UFolderWidget::AddCacheDataForFile(const FString& InID, const TArray<FRefToLocalFileData>& InData)
{

}

bool UFolderWidget::GetCacheDataForFile(const FString& InID, FRefToLocalFileData& OutData)
{
	/*if(CacheRefData.Contains(InID))
	{
		OutData = CacheRefData[InID];
	}
	else*/
	{
		bool Success = URefRelationFunction::GetCurrentRefRelationFromFile(InID, OutData);
		if (Success)
		{
			CacheRefData.Add(InID, OutData);
		}
		else
		{
			return false;
		}
	}
	return true;
}

void UFolderWidget::GetCacheDataForFile(const TArray<FString>& InID, TArray<FRefToLocalFileData>& OutData)
{
	for(const auto Iter : InID)
	{
		FRefToLocalFileData Temp;
		GetCacheDataForFile(Iter, Temp);
		OutData.Add(Temp);
	}
}

void UFolderWidget::UpdateCacheDataForFile(const FRefToLocalFileData& InData)
{
	const FString AffectID = (InData.FolderDBData.folder_id.IsEmpty() || InData.FolderDBData.is_folder) ? InData.FolderDBData.id : InData.FolderDBData.folder_id;
	if(CacheRefData.Contains(AffectID))
	{
		CacheRefData[AffectID] = InData;
	}
	else
	{
		CacheRefData.Add(AffectID, InData);
	}
}

void UFolderWidget::AddCacheDataForRefDirectory(const TArray<FRefDirectoryData>& InData)
{
	for(const auto& Iter : InData)
	{
		AddCacheDataForRefDirectory(Iter);
	}
}

void UFolderWidget::AddCacheDataForRefDirectory(const FRefDirectoryData& InData)
{
	if(CacheRefDirData.Contains(InData.id))
	{
		CacheRefDirData[InData.id] = InData;
	}
	else
	{
		CacheRefDirData.Add(InData.id, InData);
	}
}

bool UFolderWidget::GetCacheDataForRefDirectory(const FString& InID, FRefDirectoryData& OutData)
{
	if(CacheRefDirData.Contains(InID))
	{
		OutData = CacheRefDirData[InID];
		return true;
	}
	return false;
}

void UFolderWidget::AddCacheDataForDirectory(const FString& InDirectory, const FRefDirectoryDataArr& InData)
{
	if (!InDirectory.IsEmpty())
	{
		if (CacheSubRefData.Contains(InDirectory))
		{
			CacheSubRefData[InDirectory] = InData;
		}
		else
		{
			CacheSubRefData.Add(InDirectory, InData);
		}
		
	}
}

void UFolderWidget::AddCacheDataForDirectory(const FString& InDirectory, const TArray<FRefDirectoryData>& InData)
{
	FRefDirectoryDataArr Arr(InData);
	AddCacheDataForDirectory(InDirectory, Arr);
}

void UFolderWidget::AddCacheDataForDirectory_Single(const FString& InDirectory, const FRefDirectoryData& InData)
{
	if (!InDirectory.IsEmpty())
	{
		if (CacheSubRefData.Contains(InDirectory))
		{
			CacheSubRefData[InDirectory].DirectoryArr.Add(InData);
		}
	}
}

bool UFolderWidget::GetCacheDataForDirectory(const FString& InDirectory, FRefDirectoryDataArr& OutData)
{
	if (CacheSubRefData.Contains(InDirectory))
	{
		OutData = CacheSubRefData[InDirectory];
		return true;
	}
	return false;
}

bool UFolderWidget::GetCacheDataForDirectory(const FString& InDirectory, TArray<FRefDirectoryData>& OutData)
{
	FRefDirectoryDataArr Arr;
	bool Res = GetCacheDataForDirectory(InDirectory, Arr);
	if(Res)
	{
		OutData = Arr.DirectoryArr;
	}
	return Res;
}

bool UFolderWidget::GetCacheDataForDirectory(const FString& InDirectory, TArray<FFolderTableData>& OutData)
{
	TArray<FRefDirectoryData> DirectoryData;
	bool Res = GetCacheDataForDirectory(InDirectory, DirectoryData);
	if(Res)
	{
		URefRelationFunction::ConvertDirctoryDataToDBData(DirectoryData, OutData);
	}
	return Res;
}

bool UFolderWidget::RemoveCacheData(const FString& InDirectory)
{
	if (CacheSubRefData.Contains(InDirectory))
	{
		CacheSubRefData[InDirectory].DirectoryArr.Empty();
		return true;
	}
	return false;
}

void UFolderWidget::SyncSelect(const FRefToLocalFileData& InData)
{
	if(SelectRefData.FolderDBData.id.Equals(InData.FolderDBData.id))
		SelectRefData = InData;
}

void UFolderWidget::SyncSelectWidget(const FRefDirectoryData& InData)
{
	if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		if (CurrentSelectFile->GetItemData().id.Equals(InData.id))
		{
			CurrentSelectFile->SyncItemData(InData);
			ReSelectCurrentItem();
		}
	}
}

void UFolderWidget::SyncAddData(const FString& ParentDirectoryPath, const FRefDirectoryData& InData)
{
	AddCacheDataForRefDirectory(InData);
	if (InData.backendFolderPath.Equals(ParentDirectoryPath))
		return;
	if(CacheSubRefData.Contains(ParentDirectoryPath))
	{
		//CacheSubRefData[ParentDirectoryPath].DirectoryArr.Add(InData);
		auto& Arr = CacheSubRefData[ParentDirectoryPath].DirectoryArr;
		const int32 Index = Arr.IndexOfByPredicate([InData](const FRefDirectoryData& Data)->bool { return Data.id.Equals(InData.id); });
		if (Index == INDEX_NONE)
		{
			Arr.Add(InData);
		}
		else
		{
			Arr[Index] = InData;
		}
	}
	else
	{
		FRefDirectoryDataArr Arr;
		Arr.DirectoryArr.Add(InData);
		CacheSubRefData.Add(ParentDirectoryPath, Arr);
	}
}

void UFolderWidget::SyncSelectData(const FRefDirectoryData& InData)
{
	const FString ParentPath = URefRelationFunction::GetFolderDirectory(InData.backendFolderPath, InData.isFolder);
	SyncAddData(ParentPath, InData);
}

bool UFolderWidget::SyncRefLocalData(const FRefDirectoryData& InData)
{
	auto FileData = SelectRefData;
	if (!InData.id.Equals(SelectRefData.FolderDBData.id))
	{
		GetCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(InData), FileData);
	}
	if (FileData.IsValid())
	{
		FString FullPath = UFrontDirectoryWidget::GetInstance()->GetMappingFullDirectoryPath(FCString::Atoi64(*InData.fontFolderPath));
		if (FullPath.IsEmpty())
		{
			FullPath = FileData.FolderDBData.front_directory;
		}
		FileData.FolderDBData.UpdateProperty(
			InData.folderId,
			InData.folderName,
			InData.folderNameExp,
			InData.folderCode,
			InData.folderCodeExp,
			InData.visibilityExp,
			InData.visibility,
			InData.isNew,
			FullPath,
			InData.backendFolderPath,
			InData.description
		);
		const FString MarkID = URefRelationFunction::GetMarkToBackendDirectory(InData);
		if (CacheRefData.Contains(MarkID))
		{
			CacheRefData[MarkID] = FileData;
		}
		else
		{
			CacheRefData.Add(MarkID, FileData);
		}

		const FString RelativePath = URefRelationFunction::GetRefFileRelativePath(InData);
		if (URefRelationFunction::SaveFile(RelativePath, FileData))
		{
			UploadFileRequest(RelativePath);
			FString LocalMd5;
			int64 FileSize = 0;
			ACatalogPlayerController::Get()->GetFileMD5AndSize(
				FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelativePath))
				, LocalMd5
				, FileSize
			);
			if (!LocalMd5.Equals(InData.md5))
			{
				FRefDirectoryData NewData = InData;
				NewData.md5 = LocalMd5;
				UpdateDataRequest(NewData);
			}
		}
	}
	if (FileData.FolderDBData.id.Equals(SelectRefData.FolderDBData.id))
	{
		SelectRefData = FileData;
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		if (CurrentSelectFile->GetItemData().id.Equals(InData.id))
		{
			CurrentSelectFile->SyncItemData(InData);
			ReSelectCurrentItem();
		}
	}

	return true;
}

void UFolderWidget::SyncSortData(const FString& InDirectory, const TArray<FFolderTableData>& InData)
{
	if (CacheSubRefData.Contains(InDirectory))
	{
		auto& Arr = CacheSubRefData[InDirectory].DirectoryArr;
		for (const auto Iter : InData)
		{
			const int32 Index = Arr.IndexOfByPredicate([Iter](const FRefDirectoryData& ArrData)->bool { return ArrData.id.Equals(Iter.id); });
			if (Index != INDEX_NONE)
			{
				Arr[Index].dirOrder = Iter.folder_order;
			}
		}
		CacheSubRefData[InDirectory].Sort();
	}
}

bool UFolderWidget::SyncRefLocalData(const FString& NameID)
{
	/*if (CacheRefData.Contains(NameID))
	{
		SelectRefData = CacheRefData[NameID];
	}
	else*/
	{
		const bool Success = URefRelationFunction::GetCurrentRefRelationFromFile(NameID, SelectRefData);
		if (!Success)
		{
			UE_LOG(LogTemp, Warning, TEXT("load ref file [%s] error"), *NameID);
			SelectRefData.Clear();
		}
		else
		{
			/*
			*  @@ if this data is copy, so need Rewrite file data
			*/
			FFolderTableData SelectData = CurrentSelectItem->GetItemData();
			if (CurrentSelectFile)
			{
				SelectData = CurrentSelectFile->GetItemData();
			}
			const FString IDOfCurRefData = URefRelationFunction::GetMarkToBackendDirectory(SelectData);
			if (IDOfCurRefData.Equals(NameID, ESearchCase::IgnoreCase) && !SelectData.id.Equals(SelectRefData.FolderDBData.id, ESearchCase::IgnoreCase))
			{
				SelectRefData.FolderDBData.CopyData(SelectData.id, SelectData.folder_id, SelectData.folder_name, SelectData.folder_name_exp, SelectData.folder_code, SelectData.folder_code_exp, SelectRefData.FolderDBData.folder_type,
					SelectData.thumbnail_path, SelectData.parent_id, SelectData.visibility_exp, SelectData.visibility, SelectData.folder_order, SelectData.is_new, SelectData.can_add_subfolder,
					SelectData.backend_folder_path, SelectRefData.FolderDBData.front_directory);
				const FString RelativePath = URefRelationFunction::GetRefFileRelativePath(SelectData);
				URefRelationFunction::SaveFile(RelativePath, SelectRefData);
				UploadFileRequest(RelativePath);
			}
			CacheRefData.Add(NameID, SelectRefData);
		}
		//checkf(Success, TEXT("load ref file[%s] data error!!"), *InFolderId);			
	}

	return false;
}

void UFolderWidget::SyncRefType(const TArray<FParameterData>& FileParams, FString& RefTypeCode, FString& RefType)
{
	//考虑门类型 （ DoorRefTypeCode = TEXT("MBLX") ）
	const int32 DoorRefTypeIndex = FileParams.IndexOfByPredicate(
		[](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(DoorRefTypeCode, ESearchCase::CaseSensitive);
		}
	);
	if (DoorRefTypeIndex != INDEX_NONE)
	{
		RefTypeCode = DoorRefTypeCode;
        RefType = FileParams[DoorRefTypeIndex].Data.value;
	}
	else
	{
		RefTypeCode = TEXT("");
        RefType = TEXT("");
	}

}

bool UFolderWidget::SyncRefLocalData_Inner(const FFolderTableData& Data)
{
	const FString NameID = URefRelationFunction::GetMarkToBackendDirectory(Data);
	const bool Success = URefRelationFunction::GetCurrentRefRelationFromFile(NameID, SelectRefData);
	if (!Success)
	{
		UE_LOG(LogTemp, Warning, TEXT("load ref file [%s] error"), *NameID);
		SelectRefData.Clear();
	}
	else
	{
		/*
		*  @@ if this data is copy, so need Rewrite file data 
		*/
		if (!Data.id.Equals(SelectRefData.FolderDBData.id, ESearchCase::IgnoreCase))
		{
			SelectRefData.FolderDBData.CopyData(Data.id, Data.folder_id, Data.folder_name, Data.folder_name_exp,Data.folder_code, Data.folder_code_exp, SelectRefData.FolderDBData.folder_type,
				Data.thumbnail_path, Data.parent_id, Data.visibility_exp, Data.visibility, Data.folder_order, Data.is_new, Data.can_add_subfolder,
				Data.backend_folder_path, SelectRefData.FolderDBData.front_directory);
			const FString RelativePath = URefRelationFunction::GetRefFileRelativePath(Data);
			URefRelationFunction::SaveFile(RelativePath, SelectRefData);
			UploadFileRequest(RelativePath);
		}

		CacheRefData.Add(NameID, SelectRefData);
	}

	return Success;
}

bool UFolderWidget::CanExecuteCopyOperator(const FString& OriginID, const FString& TargetID)
{
	FRefDirectoryData OriginData;
	bool OriginRes = GetCacheDataForRefDirectory(OriginID, OriginData);
	FRefDirectoryData TargetData;
	bool TargetRes = GetCacheDataForRefDirectory(TargetID, TargetData);

	if (OriginRes && TargetRes)
	{
		if (TargetData.backendFolderPath.Contains(OriginData.backendFolderPath))
			return false;
	}
	return true;
}

bool UFolderWidget::ConstructCutActionData(const FString& OriginID, const FString& TargetID, FRefDirectoryData& OutData)
{
	bool OriginRes = GetCacheDataForRefDirectory(OriginID, OutData);
	//UFolderWidget::Get()->AddCacheDataForDirectory()
	FRefDirectoryData TargetDirectoryData;
	bool TargetRes = GetCacheDataForRefDirectory(TargetID, TargetDirectoryData);

	//remove old sub cache
	UFolderWidget::Get()->RemoveCacheData(OutData.backendFolderPath);

	if (OriginRes && TargetRes)
	{
		//upper folder cut
		FString UpperFolderStr;
		if (OutData.backendFolderPath.Contains(TargetDirectoryData.backendFolderPath))
		{
			UpperFolderStr = TargetDirectoryData.backendFolderPath;
			OutData.backendFolderPath = FPaths::Combine(
				TargetDirectoryData.backendFolderPath,
				URefRelationFunction::GetMarkToBackendDirectory(OutData)
			);
		}
		else
		{
			UpperFolderStr = URefRelationFunction::GetFolderDirectory(TargetDirectoryData.backendFolderPath, TargetDirectoryData.isFolder);
			OutData.backendFolderPath = FPaths::Combine(
				UpperFolderStr, //URefRelationFunction::GetFolderDirectory(TargetDirectoryData.backendFolderPath, TargetDirectoryData.isFolder),
				URefRelationFunction::GetMarkToBackendDirectory(OutData)
			);
		}
		

		if (CacheSubRefData.Contains(UpperFolderStr))
		{
			auto SubArr = CacheSubRefData[UpperFolderStr].DirectoryArr;
			if (SubArr.IsValidIndex(0))
			{
				OutData.dirOrder = SubArr[0].dirOrder;
				for (int32 i = 1; i < SubArr.Num(); ++i)
				{
					if (SubArr[i].dirOrder >= OutData.dirOrder)
					{
						OutData.dirOrder = SubArr[i].dirOrder;
					}
				}
				OutData.dirOrder++;
			}
			else
			{
				OutData.dirOrder = 0;
			}
		}
	}
	
	return OriginRes && TargetRes;
}

void UFolderWidget::SyncLocalDBFile()
{
	TArray<FFolderTableData> DBFolders;
	bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(RootParentID, EFolderFileSearchType::EFolderOnly, DBFolders);

	if (DBFolders.Num() > 0)
	{
		for (const auto DF : DBFolders)
		{
			InsertDataToServerDatabase(DF, TEXT(""));
		}
	}
}

void UFolderWidget::InsertDataToServerDatabase(const FFolderTableData& FolderData, FString UpperDirectory)
{
	UE_LOG(LogTemp, Warning, TEXT("InsertDataToServerDatabase --- id [%s], folderId [%s], folderName [%s], folderType [%d] "), 
		*FolderData.id, *FolderData.folder_id, *FolderData.folder_name, static_cast<int32>(FolderData.folder_type));
	if (FolderData.IsValid())
	{
		TArray<FParameterData> LevelParams;
		bool bSuccess = FLocalDatabaseParameterLibrary::GetCurrentLevelParameters(FolderData.id, FolderData.can_add_subfolder, LevelParams);
		//if(bSuccess)
		{
			//表信息
			FRefDirectoryData DirData;
			URefRelationFunction::ConvertDBDataToDirctoryData(FolderData, DirData);
			const FString RefID = URefRelationFunction::GetMarkToBackendDirectory(DirData);
			if (UpperDirectory.IsEmpty())
			{
				UpperDirectory = RefID;
			}
			else
			{
				UpperDirectory = FPaths::Combine(UpperDirectory, RefID);
			}
			DirData.backendFolderPath = UpperDirectory;

			//文件
			FRefToLocalFileData DBRefData;
			FCatalogFolderDataDB CatalogDBFolder(
				FolderData.id, FolderData.folder_id, FolderData.folder_name,
				FolderData.folder_code, FolderData.folder_code_exp,
				static_cast<int32>(FolderData.folder_type), FolderData.thumbnail_path, FolderData.parent_id,
				FolderData.visibility_exp, FolderData.visibility, FolderData.folder_order, FolderData.is_new,
				FolderData.can_add_subfolder, DirData.backendFolderPath, DirData.fontFolderPath
			);

			{
				//文件内部数据填充
				if (FolderData.folder_type == EFolderType::EMultiComponents)
				{
					TArray<FMultiComponentDataItem> MultiComponentsData;
					FMultiComTableOperatorLibrary::RetriveFileMultiComponents(FolderData.id, MultiComponentsData);
					if (MultiComponentsData.Num() > 0)
					{
						TArray<FRefToFileComponentData> RefComponents = URefToFileData::ConvertToRefComponentsData(MultiComponentsData);
						DBRefData.Init(RefComponents);
					}
				}
				else if (FolderData.folder_type == EFolderType::ESingleComponent)
				{
					FComponentFileData FileData;
					FSingleComponentTableData SingleComponent;
					FSingleComponentOperatorLibrary::RetriveSingleComponentByFileID(FolderData.id, SingleComponent);
					FileData.file_data_path = SingleComponent.data_path;
					FileData.depend_files = SingleComponent.depend_files;

					//获取内部单部件结构
					if (!SingleComponent.data_path.IsEmpty())
					{
						FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SingleComponent.data_path);
						FSingleComponentProperty SingleComponentProperty;
						if (FPaths::FileExists(AbsPath) && !UProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(AbsPath, SingleComponentProperty))
							UE_LOG(LogTemp, Error, TEXT("Load Single Component File Error"));

						if (SingleComponentProperty.IsValid())
						{
							FileData.Init(SingleComponentProperty);
						}
						DBRefData.Init(FileData);
					}
				}

			}
			DBRefData.Init(CatalogDBFolder, LevelParams);

			if (DBRefData.IsValid())
			{
				//生成文件
				FString RefFilePathIdentify = URefToFileData::GetFileAddress(RefID);
				UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, DBRefData);

				//更新数据库数据
				FString FileMD5 = TEXT("");
				int64 FileSize = 0;
				bool bValid = ACatalogPlayerController::GetFileMD5AndSize(RefFilePathIdentify, FileMD5, FileSize);
				DirData.md5 = FileMD5;
			}

			DirData.createdBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();
			DirData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

			//SyncOldDataRequest(DirData);
			UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(DirData));
		}

		//child 
		TArray<FFolderTableData> ChildDatas;
		bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(FolderData.id, EFolderFileSearchType::EFolderAndFile, ChildDatas);
		for (const auto CD : ChildDatas)
		{
			InsertDataToServerDatabase(CD, UpperDirectory);
		}
	}

}

void UFolderWidget::SyncAssociateDataToFile(const TArray<FAssociateListData>& InAssociateData)
{
	if (SelectRefData.IsValid())
	{
		SelectRefData.AssociateListData = InAssociateData;
		
		RefreshRefFileAllThing();
	}
}

void UFolderWidget::RefreshRefFileAllThing()
{
	FString MarkID = SelectRefData.FolderDBData.folder_id.IsEmpty() ?
		SelectRefData.FolderDBData.id : (SelectRefData.FolderDBData.is_folder ? SelectRefData.FolderDBData.id : SelectRefData.FolderDBData.folder_id);

	FString FormatID = URefRelationFunction::FormatFolderID(MarkID);

	AddCacheDataForFile(FormatID, SelectRefData);

	RefreshLocalRefFile();

	FRefDirectoryData CurRefData;
	if (GetCacheDataForRefDirectory(SelectRefData.FolderDBData.id, CurRefData))
	{
		FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FormatID);

		int64 FileSize = 0;
		const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
			FPaths::ProjectContentDir(),
			FileRelativePath
		));
		ACatalogPlayerController::GetFileMD5AndSize(AbsPath, CurRefData.md5, FileSize);

		/*
		*  @@ update ref file update user
		*/
		CurRefData.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();

		//sync ref type code / value
		SyncRefType(SelectRefData.ParamDatas, CurRefData.RefTypeCode, CurRefData.RefType);

		UpdateDataRequest(CurRefData);
		UploadFileRequest(FileRelativePath);

	}
}

void UFolderWidget::RefreshLocalRefFile()
{
	if (SelectRefData.IsValid())
	{
		const FString RefFilePathIdentify = URefRelationFunction::GetRefFileRelativePath(SelectRefData);
		const FString RefFilePath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), RefFilePathIdentify)
		);
		UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePath, SelectRefData);
	}
}

void UFolderWidget::InitStyleRefData()
{
	bool Res = URefRelationFunction::GetStyleRefRelationFromFile(StyleRefFileData);
}

void UFolderWidget::FormatStyleRefData()
{
	for (auto& SCD : StyleRefFileData.content_datas)
	{
		SCD.ClearContentOption();
	}
	URefRelationFunction::SaveStyleRefRelationToFile(StyleRefFileData);
	UploadStyle();
}

void UFolderWidget::DownloadStyle()
{
	//DownloadFileRequest(URefToStyleDataLibrary::GetStyleRelativeAddress());
	NetUUID.DownloadStyleUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(
		URefToStyleDataLibrary::GetStyleRelativeAddress()
	);
}

void UFolderWidget::UploadStyle()
{
	const FString StyleAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), URefToStyleDataLibrary::GetStyleRelativeAddress()));
	if(FPaths::FileExists(StyleAbsPath))
	{
		UploadFileRequest(URefToStyleDataLibrary::GetStyleRelativeAddress());
	}
}

void UFolderWidget::SaveStyle()
{
	URefRelationFunction::SaveStyleRefRelationToFile(StyleRefFileData);
}

void UFolderWidget::BindDelegates()
{
#ifdef USE_REF_LOCAL_FILE

	/*UCatalogNetworkSubsystem::GetInstance()->ReleaseInsertDelegate.AddUniqueDynamic(this, &UFolderWidget::OnReleaseGetResponseHandler);
	
	UCatalogNetworkSubsystem::GetInstance()->MergeInsertLogResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnMergeInsertLogResponseHandler);*/

	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnDownloadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnUploadFileResponseHandler);

	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryAddResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnAddNewResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnRootDirectoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectoryUpdateResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnUpdateResponseHandler);

	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySortResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnSortResponseHandler);

	UCatalogNetworkSubsystem::GetInstance()->BackendDirectoryObscureSearchResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnObsurceSearchResponseHandler);
	
	UCatalogNetworkSubsystem::GetInstance()->RefPlaceRuleResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnSearchPlaceRuleResponseHandler);

#else

	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->ReleaseInsertDelegate.AddUniqueDynamic(this, &UFolderWidget::OnReleaseGetResponseHandler);
		//CatalogPC->ReleaseAllDelegate.AddUniqueDynamic(this, &UFolderWidget::OnReleaseGetResponseHandler);
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnDownloadFileResponseHandler);
		CatalogPC->UploadFileResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnUploadFileResponseHandler);
		CatalogPC->MergeInsertLogResponseDelegate.AddUniqueDynamic(this, &UFolderWidget::OnMergeInsertLogResponseHandler);
	}

#endif
}

void UFolderWidget::OnReleaseGetResponseHandler(const FString& UUID)
{
	if (UUID.Equals(ReleaseAllUUID))
	{
		UE_LOG(LogTemp, Log, TEXT("RELEASE SUCCESS"));
	}
}

void UFolderWidget::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
#ifdef USE_REF_LOCAL_FILE

	if (NetUUID.DownloadFile.Equals(UUID))
	{
		NetUUID.ResetDownloadFileAction();
		if (OutRes && OutFilePath.IsValidIndex(0))
		{
			const FString NameID = FPaths::GetBaseFilename(OutFilePath[0]);
			SyncRefLocalData(NameID);
			SelfSelectWidgetAfterDownload(NameID);
		}
		else
		{
			UI_POP_WINDOW_ERROR(TEXT("Download File Error"));
		}


	}
	else if (UUID.Equals(NetUUID.DownloadStyleUUID))
	{
		NetUUID.ResetDownloadStyleAction();
		if (OutRes)
		{
			URefRelationFunction::GetStyleRefRelationFromFile(StyleRefFileData);
			UE_LOG(LogTemp, Log, TEXT("Download Style Success"));
		}
		else
		{
			//UI_POP_WINDOW_ERROR(TEXT("Download Style File Error"));
			UE_LOG(LogTemp, Log, TEXT("Download Style File Error"));
		}
	
	}

#else


	if (DownloadDatabaseUUID.Equals(UUID))
	{
		DownloadDatabaseUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		if (MergeProcessWidget)
		{
			MergeProcessWidget->SetPercent(0.3f);
		}
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(FPaths::Combine(FPaths::ProjectContentDir(), ServerDatabasePath));
		//CompareFileData();
		//clear null picture
		/*const FString SelectFile = TEXT("select * from file_image");
		const FString SelectParam = TEXT("select * from param_image");
		const FString SelectOther = TEXT("select * from other_file");
		const FString SelectStyle = TEXT("select * from style_image");

		TArray<FDownloadFileData> CDatas;
		TArray<FDownloadFileData> CTemp;
		
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectFile, CDatas);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectParam, CTemp);

		CDatas.Append(CTemp);
		CTemp.Empty();

		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectStyle, CTemp);
		CDatas.Append(CTemp);

		for (auto& iter : CDatas)
		{

		}*/

		MergeData();
	}
	//else if (DownloadFileUUID.Equals(UUID))
	//{
	//	MergeData();
	//}

#endif
}
void UFolderWidget::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
#ifdef USE_REF_LOCAL_FILE

	if (NetUUID.UploadFile.Equals(OutFilePath))
	{
		NetUUID.ResetUploadFileAction();
		if (OutRes)
		{
			UE_LOG(LogTemp, Log, TEXT("Upload [%s] SUCCESS"), *OutFilePath);
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Upload [%s] FAILED"), *OutFilePath);
		}
	}

#else
	if (false == OutFilePath.Equals(UploadDatabaseUUID)) return;
	UploadDatabaseUUID.Empty();
	if (false == OutRes)
	{
		UploadSuccess(false);
		return;
	}
	if (FilesToUpload.Num() > 0)
	{


		UploadDatabaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FilesToUpload.Top());


		UploadDatabaseUUID = ACatalogPlayerController::Get()->UploadFileRequest(FilesToUpload.Top());

		FilesToUpload.Pop();
		return;
	}

	if (MergeProcessWidget)
	{
		MergeProcessWidget->SetPercent(0.7f);
	}

	UWidget_FileItem* FileItem = GetCurrentSelectFile();
	UWidget_FolderItem* FolderItem = GetCurrentSelectItem();
	FString Id = TEXT("");
	bool IsFolder = true;
	if (FileItem)
	{
		Id = FileItem->GetItemData().id;
		IsFolder = false;
	}
	else if (FolderItem)
	{
		Id = FolderItem->GetItemData().id;
	}
	FString Path = TEXT("unknow");
	TArray<FString> Paths;
	UFolderTableOperatorLibrary::SelectFolderFilePath(Id, IsFolder, Paths);
	if (Paths.Num() > 0)
	{
		Path.Empty();
		for (int32 i = 0; i < Paths.Num(); ++i)
		{
			TArray<FFolderTableData> SelfDatas;
			UFolderTableOperatorLibrary::FolderFileSearchByID(Paths[i], EFolderFileSearchType::EFolderAndFile, SelfDatas);
			if (SelfDatas.Num() > 0)
			{
				if (i == 0)
					Path.Append(SelfDatas[0].folder_name);
				else
				{
					Path.Append("/");
					Path.Append(SelfDatas[0].folder_name);
				}
			}
		}
	}
	FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
	FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Upload")).ToString();
	MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Path, TEXT(""), TEXT(""), UserName, Operation);
#endif
}

void UFolderWidget::OnMergeInsertLogResponseHandler(const FString& UUID, const int32& InsertId)
{
#ifdef USE_REF_LOCAL_FILE

#else
	if (UUID.Equals(MergeLogUUID))
	{
		UE_LOG(LogTemp, Log, TEXT("MergeInsert SUCCESS"));
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->CloseDatabase();

		//FCatalogFunctionLibrary::DeleteFile(FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));
		//FCatalogFunctionLibrary::CopyFileTo(FPaths::Combine(FPaths::ProjectContentDir(), ServerDatabasePath), FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));
		if (MergeProcessWidget)
		{
			MergeProcessWidget->SetPercent(1.f);
			FLatentActionInfo LatentActionInfo;
			LatentActionInfo.CallbackTarget = this;
			LatentActionInfo.ExecutionFunction = TEXT("RemoveProcess");
			LatentActionInfo.Linkage = 1;
			LatentActionInfo.UUID = 100;
			ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
			LocalDBSubsystem->OpenDatabase(FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));
			UKismetSystemLibrary::Delay(this, 3.f, LatentActionInfo);
		}

	}
#endif
}

void UFolderWidget::UploadFileRequest(const FString& FileRelativePath)
{
	NetUUID.UploadFile = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void UFolderWidget::UploadFileRequest_NoHandler(const FString& FileRelativePath)
{
	UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void UFolderWidget::DownloadFileRequest(const FString& FileRelativePath)
{
	NetUUID.DownloadFile = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(FileRelativePath);
}

void UFolderWidget::AddDataRequest(const FRefDirectoryData& InData)
{
	NetUUID.AddUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryAddRequest(InData);
}

void UFolderWidget::SyncOldDataRequest(const FRefDirectoryData& InData)
{
	const FString SyncUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryAddRequest(InData);
	NetUUID.SyncUUIDs.Add(SyncUUID);
	SyncDatas.Add(SyncUUID, InData);
}

void UFolderWidget::InitPlaceRuleData()
{
	SearchPlaceRuleRequest();
}

FRefPlaceRuleData UFolderWidget::GetPlaceRuleByID(const int32& InRuleID)
{
	FRefPlaceRuleData Res;
	const int32 Index = PlaceRuleDatas.IndexOfByPredicate(
		[&InRuleID](const FRefPlaceRuleData& ArrData)->bool
		{
			return ArrData.id == InRuleID;
		}
	);
	if (Index != INDEX_NONE)
	{
		Res = PlaceRuleDatas[Index];
	}
	return Res;
}

TArray<FString> UFolderWidget::GetPlaceRuleCombineStr()
{
	//test
	//return { TEXT("0/left左边right右/0"), TEXT("1/left左边/0"), TEXT("2/right右边12/0"), TEXT("3/upper上边/0"), TEXT("4/down下边/0") };
	
	TArray<FString> Res;
	for(const auto& PRD : PlaceRuleDatas)
	{
		if(!PRD.IsEnableForUser()) continue;

		Res.Add(PRD.CombineStrForShow());
	}
	return Res;
}

TArray<FRefPlaceRuleData> UFolderWidget::GetPlaceRuleDatasByCombineStr(const TArray<FString>& InCombineStr)
{
	TArray<FRefPlaceRuleData> Res;
	for (auto& ICS : InCombineStr)
	{
		TArray<FString> SplitArr;
		ICS.ParseIntoArray(SplitArr, TEXT("/"), true);
		if (SplitArr.Num() >= 3)
		{
			FRefPlaceRuleData& Temp = Res.AddDefaulted_GetRef();
			Temp.id = FCString::Atoi(*SplitArr[0]);
			Temp.name = SplitArr[1];
		}
	}
	return Res;
}

void UFolderWidget::UpdateDataRequest(const FRefDirectoryData& InData)
{
	NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(InData);
}

void UFolderWidget::SendSwapDataOrderRequest(const FString& InID1, const FString& InID2)
{
	NetUUID.SwapUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySortRequest(InID1, InID2);
}

void UFolderWidget::OnAddNewResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.AddUUID.Equals(UUID))
	{
		NetUUID.ResetAddAction();
		UE_LOG(LogTemp, Log, TEXT("AddNew response %d"), bSuccess);
		if(bSuccess && Datas.IsValidIndex(0))
		{
			const FString ParentPath = URefRelationFunction::GetFolderDirectory(Datas[0].backendFolderPath, false);
			SyncAddData(ParentPath, Datas[0]);
			FFolderTableData FolderTableData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], FolderTableData);
			AddNewFolderFile(FolderTableData);

			/*
			*  @@ upload default file
			*/
			UploadFileRequest(URefRelationFunction::GetRefFileRelativePath(Datas[0]));
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}

	if (NetUUID.SyncUUIDs.Contains(UUID))
	{
		UE_LOG(LogTemp, Log, TEXT("Sync response %d, Msg [%s]"), bSuccess, *Msg);
		NetUUID.SyncUUIDs.Remove(UUID);
		SyncDatas.Remove(UUID);
	}
}

void UFolderWidget::OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.UpdateUUID.Equals(UUID))
	{
		if (bSuccess && Datas.IsValidIndex(0))
		{
			const FString ParentPath = URefRelationFunction::GetFolderDirectory(Datas[0].backendFolderPath, false);
			SyncAddData(ParentPath, Datas[0]);
		}
	}
	else if (NetUUIDs.Contains(UUID))
	{
		if (bSuccess && Datas.IsValidIndex(0))
		{
			FFolderTableData InData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], InData);

			FRefToLocalFileData FileData;
			GetCacheDataForFile(URefRelationFunction::GetMarkToBackendDirectory(InData), FileData);

			FFolderTableData* FindData = CacheTableDatas.FindByPredicate([&InData](const FFolderTableData& InCurrentData) {
				return InData.id.Equals(InCurrentData.id);
				});

			if (FindData != nullptr)
			{
				URefRelationFunction::RenameLocalFile(
					URefRelationFunction::GetRefFileRelativePath(*FindData),
					URefRelationFunction::GetRefFileRelativePath(InData));
			}

			SyncSelectData(Datas[0]);
		    SyncRefLocalData(Datas[0]);
			//UFolderWidget::Get()->UpdateSelectFolderData(InData, InData.can_add_subfolder);

			CacheTableDatas.RemoveAll([&InData](const FFolderTableData& InCurrentData) {
				return InData.id.Equals(InCurrentData.id); });

		}
		NetUUIDs.Remove(UUID);
	}
}

void UFolderWidget::OnSortResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.SwapUUID.Equals(UUID))
	{
		NetUUID.ResetSwapAction();
		if (bSuccess)
		{
			SwapTwoItemsAction();
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFolderWidget::OnRootDirectoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if (NetUUID.SearchUUID.Equals(UUID))
	{
		NetUUID.ResetSearchAction();
		if (bSuccess)
		{
			if (Datas.Num() > 0)
			{
				const TArray<FRefDirectoryData> Root = URefRelationFunction::FliteRootDirectory(Datas);
				AddCacheDataForRefDirectory(Root);

				TArray<FFolderTableData> DBFolders;
				URefRelationFunction::ConvertDirctoryDataToDBData(Root, DBFolders);
				GenerateRootFolder(DBFolders);
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
	else if(NetUUID.ObsurceSearchExpandUUID.Equals(UUID, ESearchCase::IgnoreCase))
	{
		NetUUID.ResetObsurceSearchExpandAction();
		if(bSuccess)
		{
			if(Datas.Num() > 0)
			{
				TArray<FRefDirectoryDataContent> ContentArr = URefRelationFunction::GetDirectoryContentData(Datas, ObsurceSearchPaths);
				if(ContentArr.IsValidIndex(0))
				{
					FRefDirectoryDataContent RootContent = ContentArr[0];
					const int32 Index = RootFolderWidgets.IndexOfByPredicate(
						[&RootContent](UWidget_FolderItem* Widget)->bool
						{
							if(IS_OBJECT_PTR_VALID(Widget))
							{
								return Widget->GetItemData().id.Equals(RootContent.PID, ESearchCase::IgnoreCase);
							}
							return false;
						}
					);
					if(Index != INDEX_NONE)
					{
						ContentArr.RemoveAt(0);
						ObsurceSearchPaths.RemoveAt(0);
						RootFolderWidgets[Index]->SetObscureSearchExpand(RootContent.CDataArr, ContentArr, ObsurceSearchPaths);
					}
				}
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFolderWidget::ObsurceSearchRequest(const FString& InStr, const FString& InID)
{
	NetUUID.ObsurceSearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendObscureSearchRequest(InStr, InID);
}

void UFolderWidget::OnObsurceSearchResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	if(UUID.Equals(NetUUID.ObsurceSearchUUID, ESearchCase::IgnoreCase))
	{
		NetUUID.ResetObsurceSearchAction();
		if (bSuccess)
		{
			if (Datas.Num() > 0)
			{
				TArray<FFolderTableData> DBFolders;
				URefRelationFunction::ConvertDirctoryDataToDBData(Datas, DBFolders);

				if (DBFolders.Num() > 0)
				{
					SearchFileResult(DBFolders);
				}
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFolderWidget::ObscureSearchExpandRequest(const FString& InStrPath)
{
	NetUUID.ObsurceSearchExpandUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest(InStrPath);
}

void UFolderWidget::SearchPlaceRuleRequest()
{
	NetUUID.PlaceRuleSearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendQueryRefPlaceRuleRequest();
}

void UFolderWidget::OnSearchPlaceRuleResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefPlaceRuleData>& Datas)
{
	if(UUID.Equals(NetUUID.PlaceRuleSearchUUID, ESearchCase::IgnoreCase))
	{
		NetUUID.ResetPlaceRuleSearchAction();
		if(bSuccess)
		{
			PlaceRuleDatas = Datas;
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UFolderWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BorFolderTree, Bor_FolderTree);
	BIND_PARAM_CPP_TO_UMG(ScbFolderLayout, Scb_FolderLayout);
	BIND_PARAM_CPP_TO_UMG(BtnCreateFolder, Btn_CreateFolder);
	BIND_WIDGET_FUNCTION(BtnCreateFolder, OnClicked, UFolderWidget::OnClickedBtnCreateFolder);
	BIND_PARAM_CPP_TO_UMG(BtnCreateFile, Btn_CreateFile);
	BIND_WIDGET_FUNCTION(BtnCreateFile, OnClicked, UFolderWidget::OnClickedBtnCreateFile);

	BIND_PARAM_CPP_TO_UMG(BorSearch, Bor_Search);
	BIND_PARAM_CPP_TO_UMG(ScbSearch, Scb_Search);
	BIND_PARAM_CPP_TO_UMG(BtnRelease, Btn_Release);
	BIND_WIDGET_FUNCTION(BtnRelease, OnClicked, UFolderWidget::OnClickedBtnRelease);

	BIND_WIDGET_FUNCTION(Btn_FolderMaping, OnClicked, UFolderWidget::OnClickedBtnFolderMaping);

	LastActionID = RootParentID;
	LastActionType = static_cast<int32>(ERightMenuType::None);
	CutFolderItem = nullptr;
	CutFileItem = nullptr;
	SelectedMap.Empty();

	IsViewSelect = false;

	BindDelegates();
}

void UFolderWidget::GenerateFolderLayout()
{
	UE_LOG(LogTemp, Warning, TEXT("UFolderWidget::GenerateFolderLayout()"));
	RootFolderWidgets.Empty();
	IsEnableAddFolderOrFile(false, false);
	ScbFolderLayout->ClearChildren();
	TArray<FFolderTableData> DBFolders;

#ifdef USE_REF_LOCAL_FILE

	//URefRelationFunction::GetRootDirectory(DBFolders);
	/*TArray<FRefDirectoryData> DirectoryDatas;
	URefRelationFunction::GetRootDirectory(DirectoryDatas);
	AddCacheDataForRefDirectory(DirectoryDatas);
	URefRelationFunction::ConvertDirctoryDataToDBData(DirectoryDatas, DBFolders);*/

	NetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest(TEXT(""));

#else

	bool Res = UFolderTableOperatorLibrary::FolderFileSearchByParentID(RootParentID, EFolderFileSearchType::EFolderOnly, DBFolders);

	if (DBFolders.Num() > 0)
	{
		GenerateRootFolder(DBFolders);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT(" Folder Widget --- get root folder error "));
	}
#endif
}

bool UFolderWidget::IsOnlyFileMultiSelected()
{
	if (SelectedItems.IsEmpty())
		return false;

	bool bOnlyFile = true;
	for (auto ItemIte : SelectedItems)
	{
		if (ItemIte->IsA<UWidget_FileItem>())
		{
			UWidget_FileItem* FileItem = Cast<UWidget_FileItem>(ItemIte);
			bOnlyFile &= !FileItem->GetItemData().can_add_subfolder;
		}
		else
		{
			bOnlyFile &= false;
		}
	}

	return bOnlyFile;
}

void UFolderWidget::UpdateSelectFolderData(const FFolderTableData& InFolderData, bool IsFolder)
{
	if (IsFolder)
	{
		if (CurrentSelectItem /*&& IS_OBJECT_PTR_VALID(CurrentSelectItem->GetChildFolders()[InFolderData.id])*/)
		{
			if (CurrentSelectItem->GetItemData().id.Equals(InFolderData.id, ESearchCase::IgnoreCase))
			{
				CurrentSelectItem->SetItemData(InFolderData);
				CurrentSelectItem->SyncFolderState(!FMath::IsNearlyZero(InFolderData.visibility));
			}
			else if (CurrentSelectItem->GetChildFolders().Contains(InFolderData.id))
			{
				CurrentSelectItem->GetChildFolders()[InFolderData.id]->SetItemData(InFolderData);
				CurrentSelectItem->GetChildFolders()[InFolderData.id]->SyncFolderState(!FMath::IsNearlyZero(InFolderData.visibility));
			}
			//CurrentSelectItem->SetItemData(InFolderData);
			CurrentSelectItem->RefreshContentWidget();
		}
	}
	else
	{
		if (CurrentSelectFile)
		{
			UE_LOG(LogTemp, Warning, TEXT("sync tree file data by property change[current select id : %s, change id : %s]")
				, *(CurrentSelectFile->GetItemData().id), *InFolderData.id);
			if (CurrentSelectFile->GetItemData().id.Equals(InFolderData.id, ESearchCase::IgnoreCase))
			{
				CurrentSelectFile->SetItemData(InFolderData);
			}
			else
			{
				if (CurrentSelectFile->ParentFolderWidget.Get()
					&& CurrentSelectFile->ParentFolderWidget.Get()->GetChildFilesMap().Contains(InFolderData.id))
				{
					UE_LOG(LogTemp, Warning, TEXT("sync tree file data by property change[current select no equal]"));
					CurrentSelectFile->ParentFolderWidget.Get()->GetChildFilesMap()[InFolderData.id]->SetItemData(InFolderData);
				}
			}
			CurrentSelectFile->ParentFolderWidget.Get()->SyncSubItemData(InFolderData);
			if (IS_OBJECT_PTR_VALID(CurrentSelectFile->ParentFolderWidget.Get()))
			{
				CurrentSelectFile->ParentFolderWidget.Get()->RefreshContentWidget();
			}
			SelectFileDelegate.ExecuteIfBound(CurrentSelectFile);
		}
	}
}

void UFolderWidget::AddNewFileToDB(const FString& FolderId, const EFolderType& FileType)
{
	switch (FileType)
	{
	case EFolderType::ESingleComponent:
	{
		break;
	}
	case EFolderType::EMultiComponents:
	{
		break;
	}
	case EFolderType::ESingleMesh:
	{
		break;
	}
	case EFolderType::EMultiMeshs:
	{
		break;
	}
	case EFolderType::EMaterial:
	{
		FCustomMaterialTableData MaterialData;
		//MaterialData.folder_id = FolderId;
		MaterialData.ref_path = DefaultMaterialPath;
		CreateNewCustomMatFile(MaterialData);
		break;
	}
	default:
		break;
	}
}

void UFolderWidget::OnClickedBtnCreateFolder()
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget---create folder"));
	AddNewFolderToDB(NewCreateFolderName, static_cast<int32>(ELayoutItemType::FolderItem));
}

void UFolderWidget::OnClickedBtnCreateFile()
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget---create file"));
	AddNewFolderToDB(NewCreateFileName, static_cast<int32>(ELayoutItemType::FileItem));
}

bool UFolderWidget::GetSelectedFolder(FFolderTableData& OutFolderData)
{
	if (CurrentSelectItem)
	{
		OutFolderData = CurrentSelectItem->GetItemData();
		return true;
	}
	return false;
}

void UFolderWidget::IsEnableAddFolderOrFile(bool IsEnable1, bool IsEnable2)
{
	WS_BtnList->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	WS_BtnList->SetActiveWidgetIndex(0);
	if (BtnCreateFolder && BtnCreateFile)
	{
		BtnCreateFolder->SetIsEnabled(IsEnable1);
		BtnCreateFile->SetIsEnabled(IsEnable2);
	}
}

void UFolderWidget::EnableMutilBtnList()
{
	WS_BtnList->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	WS_BtnList->SetActiveWidgetIndex(1);
}

void UFolderWidget::ReSelectCurrentItem()
{
	if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		SelectFileDelegate.ExecuteIfBound(CurrentSelectFile);
		return;
	}

	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{

		SelectFolderDelegate.ExecuteIfBound(CurrentSelectItem, true);
	}
	
}

void UFolderWidget::FolderOrFileUpDown(const EFolderOrFileActionType& ActionType)
{
	FFolderTableData CurrentFolderData;
	UWidget_FolderItem* CurrentParentWidget = nullptr;
	SwapDatas.Empty();
	if (!IS_OBJECT_PTR_VALID(CurrentSelectFile))  //fodler selected
	{
		CurrentFolderData = CurrentSelectItem->GetItemData();
		CurrentParentWidget = CurrentSelectItem->ParentFolderWidget.Get();
	}
	else
	{
		CurrentFolderData = CurrentSelectFile->GetItemData();
		CurrentParentWidget = CurrentSelectFile->ParentFolderWidget.Get();
	}

	/*CurrentSelectItem = nullptr;
	CurrentSelectFile = nullptr;*/
	if (!CurrentParentWidget)
		return;
	TArray<FFolderTableData> OutFolders = CurrentParentWidget->GetSubFolderDatas();
	int32 CurrentFolderOrder = CurrentFolderData.folder_order;
	int32 CurrentIndex;
	FFolderTableData ExchangeFolderData;
	if (OutFolders.Find(CurrentFolderData, CurrentIndex))
	{
		if (ActionType == EFolderOrFileActionType::Down)
		{
			ExchangeFolderData = OutFolders[CurrentIndex + 1];
		}
		else if (ActionType == EFolderOrFileActionType::Up)
		{
			ExchangeFolderData = OutFolders[CurrentIndex - 1];
		}
		CurrentFolderData.folder_order = ExchangeFolderData.folder_order;
		ExchangeFolderData.folder_order = CurrentFolderOrder;
		SwapDatas.Add(CurrentFolderData);
		SwapDatas.Add(ExchangeFolderData);
	}

	/*
	*  @@ send swap data order request
	* 	
	*/
	SendSwapDataOrderRequest(CurrentFolderData.id, ExchangeFolderData.id);

	//if (UFolderTableOperatorLibrary::SwapFolderFileOrder(CurrentFolderData.id, CurrentFolderData.folder_order, ExchangeFolderData.id, ExchangeFolderData.folder_order, CurrentFolderData.can_add_subfolder))
	//{
		//SwapTwoItemsAction();
	//}
	//else
	//{
	//	UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
	//		TEXT("Error"), FText::FromStringTable(FName("PosSt"), TEXT("Up Or Down Action Net Error")).ToString());
	//}
}

void UFolderWidget::RefreshFolderTreeLayout()
{
	GenerateFolderLayout();
}

void UFolderWidget::ClearSelectWidget()
{
	CurrentSelectItem = nullptr;
	CurrentSelectFile = nullptr;
}

TArray<FFolderTableData>& UFolderWidget::GetSelectParentSubFolders(bool IsFolder)
{
	if (IsFolder)
	{
		return CurrentSelectItem->ParentFolderWidget.Get()->GetSubFolderDatas();
	}
	else
	{
		return CurrentSelectFile->ParentFolderWidget.Get()->GetSubFolderDatas();
	}
}

void UFolderWidget::SyncViewSelectFolderOrFile(const FString& InId, bool IsFolder)
{
	IsViewSelect = true;
	if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		CurrentSelectFile->SetIsSelected(false);
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{
		//CurrentSelectItem->SetIsSelected(false);
		
		if (IsFolder)
		{
			auto ChildFolderMap = CurrentSelectItem->GetChildFolders();
			if (ChildFolderMap.Contains(InId))
			{
				ChildFolderMap[InId]->SetIsSelected(true);
				SelectItemEdit(ChildFolderMap[InId], false);
				//ChildFolderMap[InId]->SelectSelf();
			}
			else
			{
				CurrentSelectItem->ClearAllSelectState();
			}
		}
		else
		{
			auto ChildFileMap = CurrentSelectItem->GetChildFilesMap();
			if (ChildFileMap.Contains(InId))
			{
				/*ChildFileMap[InId]->SetIsSelected(true);
				CurrentSelectFile = ChildFileMap[InId];*/
				ChildFileMap[InId]->SelectSelf();
			}
			else
			{
				CurrentSelectItem->ClearAllSelectState();
			}
		}
	}
	IsViewSelect = false;
}

void UFolderWidget::SyncBreadNavigation(UWidget_FolderItem* InWidget)
{
	if (IS_OBJECT_PTR_VALID(InWidget))
	{
		UWidget_FolderItem* BreadWidget = nullptr;
		if (CurrentSelectItem == InWidget)
		{
			BreadWidget = InWidget;
		}
		else
		{
			CurrentSelectItem->SetIsSelected(false);
			CurrentSelectItem->SetChildFolderExpand(false);
			BreadWidget = CurrentSelectItem->ParentFolderWidget.Get();
			while (IS_OBJECT_PTR_VALID(BreadWidget) && BreadWidget != InWidget)
			{
				BreadWidget->SetIsSelected(false);
				BreadWidget->SetChildFolderExpand(false);
				BreadWidget = BreadWidget->ParentFolderWidget.Get();
			}
		}
		SelectItemEdit(BreadWidget, true);
	}
}

void UFolderWidget::SyncViewListRightAction(const FFolderTableData& ViewItemData, const int32& ActionType)
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget---view list right action name : %s, type : %d"), *ViewItemData.folder_name, ActionType);
	if (ActionType == static_cast<int32>(ERightMenuType::Delete))
	{
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFolderItem = nullptr;
		CutFileItem = nullptr;
		FolderRightClickActionDelegate.ExecuteIfBound(ViewItemData.id, ViewItemData.can_add_subfolder, true, ActionType);
		if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
		{
			CurrentSelectItem->RemoveSubFolder(ViewItemData);
			CurrentSelectItem->SelectSelf();
		}

		UE_LOG(LogTemp, Log, TEXT("Removing On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Copy))
	{
		LastActionID = ViewItemData.id;
		LastActionFolderType = static_cast<int32>(ViewItemData.folder_type);
		LastActionType = ActionType;
		LastActionPath = ViewItemData.backend_folder_path;

		CutFolderItem = nullptr;
		CutFileItem = nullptr;
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Shear))
	{
		LastActionID = ViewItemData.id;
		LastActionFolderType = static_cast<int32>(ViewItemData.folder_type);
		LastActionType = ActionType;
		LastActionPath = ViewItemData.backend_folder_path;

		if (ViewItemData.can_add_subfolder)
		{
			if (CurrentSelectItem && CurrentSelectItem->GetChildFolders().Contains(ViewItemData.id))
			{
				CutFolderItem = CurrentSelectItem->GetChildFolders()[ViewItemData.id];
			}
			else
			{
				CutFolderItem = nullptr;
			}
			CutFileItem = nullptr;
		}
		else
		{
			CutFolderItem = nullptr;
			if (CurrentSelectItem && CurrentSelectItem->GetChildFilesMap().Contains(ViewItemData.id))
			{
				CutFileItem = CurrentSelectItem->GetChildFilesMap()[ViewItemData.id];
			}
			else
			{
				CutFileItem = nullptr;
			}
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Paste))
	{
		if (IS_OBJECT_PTR_VALID(CutFolderItem))
		{
			CutFolderItem->RemoveFromParentFolder();
		}
		if (IS_OBJECT_PTR_VALID(CutFileItem))
		{
			CutFileItem->RemoveFromParentFolder();
		}
		if (ViewItemData.id.Equals(RootParentID) || ViewItemData.id.IsEmpty())
		{
			if(CurrentSelectItem)
				CurrentSelectItem->SelectSelf();
		}
		else
		{
			if (ViewItemData.can_add_subfolder)
			{
				if (CurrentSelectItem && CurrentSelectItem->GetChildFolders().Contains(ViewItemData.id))
				{
					CurrentSelectItem->GetChildFolders()[ViewItemData.id]->SelectSelf();
				}
				else
				{
					if(CurrentSelectItem)
						CurrentSelectItem->SelectSelf();
				}
			}
			else
			{
				if(CurrentSelectItem)
					CurrentSelectItem->SelectSelf();
			}
		}
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFolderItem = nullptr;
		CutFileItem = nullptr;
		UE_LOG(LogTemp, Log, TEXT("Removing On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
	}
	SetUploadeEnable(CurrentSelectFile || CurrentSelectItem);
}

void UFolderWidget::ToMainRefreshSelectFile(const FFolderTableData& InDataItemData)
{
	if (IS_OBJECT_PTR_VALID(CurrentSelectFile) && InDataItemData.IsValid())
	{
		CurrentSelectFile->SetItemData(InDataItemData);
		CurrentSelectFile->SelectSelf();
	}
}

void UFolderWidget::SearchFile(const FString& InString)
{
	if (InString.IsEmpty())
	{
		BorSearch->SetVisibility(ESlateVisibility::Collapsed);
		BorFolderTree->SetVisibility(ESlateVisibility::Visible);
		return;
	}
	if (IS_OBJECT_PTR_VALID(BorSearch) && BorSearch->GetVisibility() == ESlateVisibility::Collapsed)
	{
		BorSearch->SetVisibility(ESlateVisibility::Visible);
		BorFolderTree->SetVisibility(ESlateVisibility::Collapsed);
	}
	SearchFiles.Empty();
	if (IS_OBJECT_PTR_VALID(ScbSearch))
	{
		ScbSearch->ClearChildren();
	}

	FString SearchFolderID = TEXT("");
	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{
		SearchFolderID = CurrentSelectItem->GetItemData().id;
	}
	else if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
	{
		SearchFolderID = CurrentSelectFile->GetItemData().parent_id;
	}

#ifdef USE_REF_LOCAL_FILE

	ObsurceSearchRequest(InString, SearchFolderID);


#else

	TArray<FFolderTableData> OutFolderData;
	bool Res = UFolderTableOperatorLibrary::SelectFilsByIDOrNameOrCode(InString, SearchFolderID, OutFolderData);
	if (OutFolderData.Num() > 0)
	{
		SearchFileResult(OutFolderData);
	}

#endif
}

void UFolderWidget::SelectItemEdit(UWidget_FolderItem* InWidget, bool IsFolderExpand)
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget---select folder id : '%s', Expand : %d"), *InWidget->GetItemData().id, IsFolderExpand);

	//bShiftDown = ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::LeftShift) || ACatalogPlayerController::Get()->IsInputKeyDown(EKeys::RightShift);
	bShiftDown = FSlateApplication::Get().GetModifierKeys().IsShiftDown();
	bool bMultiSelected = false;
	if (bShiftDown)
	{
		if (SelectedItems.Contains(InWidget))
		{
			if (SelectedItems.Num() > 1)
			{
				InWidget->SetIsSelected(false);
				SelectedItems.Remove(InWidget);

				if (IsMultiSelect() && IsOnlyFileMultiSelected())
				{
					EnableMutilBtnList();
				}
				else
				{
					WS_BtnList->SetVisibility(ESlateVisibility::Collapsed);
				}
			}

			return;
		}

		if (SelectedItems.Num() >= 1)
		{
			bMultiSelected = true;
		}

		SelectedItems.AddUnique(InWidget);
	}
	else
	{
		for (auto ItemIte : SelectedItems)
		{
			if (ItemIte != nullptr && ItemIte != InWidget)
			{
				ItemIte->SetIsSelected(false);
			}
		}

		SelectedItems.Empty();
		SelectedItems.Add(InWidget);

		bMultiSelected = false;
	}

	if (bMultiSelected)  //多选 
	{
		//SelectedMap.Empty();
		CurrentSelectFile = nullptr;
		//CurrentSelectItem = nullptr;
		if (InWidget)
		{
			if (IsFolderExpand)
			{
				InWidget->SetChildFolderExpand(true);
				CurrentSelectItem = InWidget;
			}
			InWidget->SetIsSelected(true);

#ifdef USE_REF_LOCAL_FILE
			SyncSelectLocalRefData(InWidget->GetItemData());
#else
			SelectFolderDelegate.ExecuteIfBound(InWidget, IsFolderExpand);
#endif
		}

		if (IsOnlyFileMultiSelected())
		{
			EnableMutilBtnList();
		}
		else
		{
			WS_BtnList->SetVisibility(ESlateVisibility::Collapsed);
		}

		//ClearSelectDelegate.ExecuteIfBound();
	} 
	else //单选
	{
		IsEnableAddFolderOrFile(true, true);
		IsSelectOnNewFolderTree(SelectedMap, InWidget);

		if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
		{
			CurrentSelectFile->SetIsSelected(false);
			CurrentSelectFile = nullptr;
		}

		if (CurrentSelectItem)
		{
			CurrentSelectItem->SetIsSelected(false);
			if (IsFolderExpand)
			{
				CurrentSelectItem->ClearAllSelectState();
			}
			//CurrentSelectItem = nullptr;
		}

		if (InWidget)
		{
			if (IsFolderExpand)
			{
				InWidget->SetChildFolderExpand(true);
				if (InWidget->ParentFolderWidget.Get())
				{
					InWidget->ParentFolderWidget.Get()->ClearAllSelectState();
				}
			}
			else
			{
				if (InWidget->ParentFolderWidget.Get())
				{
					InWidget->ParentFolderWidget.Get()->ClearAllSelectState();
				}
			}
			InWidget->SetIsSelected(true);
		}

		if (IsFolderExpand)
		{
			CurrentSelectItem = InWidget;
		}

		SetUploadeEnable(CurrentSelectItem || CurrentSelectFile);
		if (!IsViewSelect)
			UnGrayWidget();

#ifdef USE_REF_LOCAL_FILE
		SyncSelectLocalRefData(InWidget->GetItemData());
#else
		SelectFolderDelegate.ExecuteIfBound(InWidget, IsFolderExpand);
#endif
	}
}

void UFolderWidget::UnFoldItem(UWidget_FolderItem* InWidget, bool IsSubOpen)
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget --- check expand : %d, widget name : %s"), static_cast<int32>(IsSubOpen), *InWidget->GetItemData().folder_name);
	if (!IS_OBJECT_PTR_VALID(GrayWidget) && IsSubOpen)
	{
		UE_LOG(LogTemp, Log, TEXT(" No Gray Widget, No Need To ReSelect"));
		return;
	}
	if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
	{
#ifdef USE_REF_LOCAL_FILE

		FString SelectFolderID = CurrentSelectItem->GetItemData().id;
		bool IsSelectFolder = !IS_OBJECT_PTR_VALID(CurrentSelectFile);
		const FString SelectBackendDir = CurrentSelectItem->GetItemData().backend_folder_path;
		TArray<FString> PathArr = URefRelationFunction::GetUpperFolderDirectory(SelectBackendDir, true);
		if (PathArr.Num() > 0)
		{
			bool ActionLastFolder = PathArr.Last().Equals(InWidget->GetItemData().id, ESearchCase::IgnoreCase);
			if (ActionLastFolder && !IsSubOpen && IsSelectFolder)
			{
				UE_LOG(LogTemp, Warning, TEXT("FolderWidget--- unckeck last item--- check expand : %d, widget name : %s")
					, static_cast<int32>(IsSubOpen), *InWidget->GetItemData().folder_name);
				return;
			}

			TArray<FString> GrayPath = PathArr;
			FString SelectID = IS_OBJECT_PTR_VALID(CurrentSelectFile) ? CurrentSelectFile->GetItemData().id : SelectFolderID;
			int32 ActionIndex = UFolderTableOperatorLibrary::GetArrayIndex<FString>(PathArr, InWidget->GetItemData().id);
			if (IsSelectFolder || !ActionLastFolder)
			{
				if (ActionIndex != INDEX_NONE)
				{
					int32 InnerActionIndex = ActionIndex;
					if (!ActionLastFolder)
						InnerActionIndex++;
					GrayPath.Empty();
					for (int32 i = 0; i < InnerActionIndex; i++)
					{
						if(PathArr.IsValidIndex(i))
							GrayPath.Add(PathArr[i]);
					}
				}
				else
					return;
			}

			if (IsSubOpen)
			{
				if (!ActionLastFolder && ActionIndex != INDEX_NONE)
				{
					if(PathArr.IsValidIndex(ActionIndex + 1))
						GrayPath.Add(PathArr[ActionIndex + 1]);
				}
			}
			if (GrayPath.Num() > 0)
				GrayFolderPathRecurse(GrayPath, TPair<bool, bool>(IsSubOpen, !ActionLastFolder), TPair<FString, bool>(SelectID, IsSelectFolder));

		}

#else
		FString SelectFolderID = CurrentSelectItem->GetItemData().id;
		bool IsSelectFolder = !IS_OBJECT_PTR_VALID(CurrentSelectFile);
		int32 CurIndex = UFolderTableOperatorLibrary::GetArrayIndex<FString>(SelectFolderPath, SelectFolderID);
		if (CurIndex == INDEX_NONE)
		{//update path
			UFolderTableOperatorLibrary::SelectFolderFilePath(SelectFolderID, true, SelectFolderPath);
		}
		bool ActionLastFolder = SelectFolderPath.Last().Equals(InWidget->GetItemData().id, ESearchCase::IgnoreCase);
		if (ActionLastFolder && !IsSubOpen && IsSelectFolder)
		{
			UE_LOG(LogTemp, Warning, TEXT("FolderWidget--- unckeck last item--- check expand : %d, widget name : %s")
				, static_cast<int32>(IsSubOpen), *InWidget->GetItemData().folder_name);
			return;
		}

		TArray<FString> GrayPath = SelectFolderPath;
		FString SelectID = IS_OBJECT_PTR_VALID(CurrentSelectFile) ? CurrentSelectFile->GetItemData().id : SelectFolderID;
		int32 ActionIndex = UFolderTableOperatorLibrary::GetArrayIndex<FString>(SelectFolderPath, InWidget->GetItemData().id);
		if (IsSelectFolder || !ActionLastFolder)
		{
			if (ActionIndex != INDEX_NONE)
			{
				int32 InnerActionIndex = ActionIndex;
				if (!ActionLastFolder)
					InnerActionIndex++;
				GrayPath.Empty();
				for (int32 i = 0; i < InnerActionIndex; i++)
				{
					GrayPath.Add(SelectFolderPath[i]);
				}
			}
			else
				return;
		}

		if (IsSubOpen)
		{
			if (!ActionLastFolder && ActionIndex != INDEX_NONE)
			{
				GrayPath.Add(SelectFolderPath[ActionIndex + 1]);
			}
		}
		if (GrayPath.Num() > 0)
			GrayFolderPathRecurse(GrayPath, TPair<bool, bool>(IsSubOpen, !ActionLastFolder), TPair<FString, bool>(SelectID, IsSelectFolder));

#endif
	}
	/*if (SelectedMap.Contains(InWidget->GetItemData().id)
		&& SelectedMap[InWidget->GetItemData().id] == InWidget->GetFolderName())
	{
		InWidget->SetFolderToGrey(true);
	}
	else
	{
		InWidget->SetFolderToGrey(false);
	}*/
}

void UFolderWidget::SelectFileItemEdit(UWidget_FileItem* InWidget)
{
	UE_LOG(LogTemp, Log, TEXT("FolderWidget---select file id : '%s'"), *InWidget->GetItemData().id);
	bShiftDown = FSlateApplication::Get().GetModifierKeys().IsShiftDown();
	bool bMultiSelected = false;
	if (bShiftDown)
	{
		if (SelectedItems.Contains(InWidget))
		{
			if (SelectedItems.Num() > 1)
			{
				InWidget->SetIsSelected(false);
				SelectedItems.Remove(InWidget);

				if (IsMultiSelect() && IsOnlyFileMultiSelected())
				{
					EnableMutilBtnList();
				}
				else
				{
					WS_BtnList->SetVisibility(ESlateVisibility::Collapsed);
				}
			}

			return;
		}

		if (SelectedItems.Num() >= 1)
		{
			bMultiSelected = true;
		}

		SelectedItems.AddUnique(InWidget);
	}
	else
	{
		for (auto Ite : SelectedItems)
		{
			if (Ite != InWidget)
			{
				Ite->SetIsSelected(false);
			}
		}

		SelectedItems.Empty();
		SelectedItems.Add(InWidget);
		bMultiSelected = false;
	}

	if (bMultiSelected)
	{
		if (IsOnlyFileMultiSelected())
		{
			EnableMutilBtnList();
		}
		else
		{
			WS_BtnList->SetVisibility(ESlateVisibility::Collapsed);
		}

		if (IS_OBJECT_PTR_VALID(InWidget))
		{
			InWidget->SetIsSelected(true);
			CurrentSelectFile = InWidget;
			CurrentSelectItem = CurrentSelectFile->ParentFolderWidget.Get();

#ifdef USE_REF_LOCAL_FILE
			SyncSelectLocalRefData(InWidget->GetItemData());
#else
			SelectFileDelegate.ExecuteIfBound(InWidget);
#endif
		}

		//ClearSelectDelegate.ExecuteIfBound();
	}
	else
	{
		IsEnableAddFolderOrFile(false, false);
		bool bIsTree = IsSelectOnNewFolderTree(SelectedMap, InWidget);
		if (IS_OBJECT_PTR_VALID(CurrentSelectItem))
		{
			if (!CurrentSelectItem->GetChildFilesMap().Contains(InWidget->GetItemData().id)
				|| !CurrentSelectItem->IsGrey)
				CurrentSelectItem->ClearAllSelectState();
		}
		/*if (CurrentSelectFile == InWidget)
		{
			return;
		}*/

		if (IS_OBJECT_PTR_VALID(CurrentSelectFile))
		{
			CurrentSelectFile->SetIsSelected(false);
		}
		if (IS_OBJECT_PTR_VALID(InWidget))
		{
			InWidget->SetIsSelected(true);
			CurrentSelectFile = InWidget;
			CurrentSelectItem = CurrentSelectFile->ParentFolderWidget.Get();

#ifdef USE_REF_LOCAL_FILE

			SyncSelectLocalRefData(InWidget->GetItemData());

#else

			SelectFileDelegate.ExecuteIfBound(InWidget);

#endif


			/*if (InWidget->ParentFolderWidget.IsValid())
			{
				SelectFolderDelegate.ExecuteIfBound(InWidget->ParentFolderWidget.Get());
			}*/
		}
		SetUploadeEnable(CurrentSelectItem || CurrentSelectFile);
		if (!IsViewSelect)
			UnGrayWidget();
	}
	
}

void UFolderWidget::SearchFileSelected(UWidget_FileItem* InWidget)
{
	if (IS_OBJECT_PTR_VALID(BorFolderTree) && IS_OBJECT_PTR_VALID(BorSearch))
	{
		BorFolderTree->SetVisibility(ESlateVisibility::Visible);
		BorSearch->SetVisibility(ESlateVisibility::Collapsed);
	}

#ifdef USE_REF_LOCAL_FILE

	if(IS_OBJECT_PTR_VALID(InWidget))
	{
		FString BackendPath = InWidget->GetItemData().backend_folder_path;
		BackendPath.ParseIntoArray(ObsurceSearchPaths, TEXT("/"));
		//get all directory info
		if(ObsurceSearchPaths.IsValidIndex(0))
		{
			ObscureSearchExpandRequest(ObsurceSearchPaths[0]);
		}

		//download directory tree .dat file
		TArray<FString> DownloadPaths;
		for (auto& OSP : ObsurceSearchPaths)
		{
			FString RelPath = URefToFileData::GetFileRelativeAddress(OSP);
			DownloadPaths.AddUnique(RelPath);
		}
		if (DownloadPaths.Num() > 0)
		{
			UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(DownloadPaths);
		}
	}
	ToolBarResetDelegate.ExecuteIfBound();

#else

	if (IS_OBJECT_PTR_VALID(InWidget))
	{
		TArray<FString> OutPaths;
		bool Res = UFolderTableOperatorLibrary::SelectFolderFilePath(InWidget->GetItemData().id, InWidget->GetItemData().can_add_subfolder, OutPaths);
		if (OutPaths.Num() > 0)
		{
			SearchFolderPath(OutPaths);
		}

		/*TArray<int32> TreeID;
		TreeID.Add(InWidget->GetItemData().id);
		int32 CurrentParentID = InWidget->GetItemData().parent_id;
		while (CurrentParentID != -1)
		{
			FFolderTableData CurrentData;
			if (UFolderTableOperatorLibrary::SelectFolderDataByID(CurrentParentID, CurrentData))
			{
				TreeID.EmplaceAt(0, CurrentData.id);
				CurrentParentID = CurrentData.parent_id;
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("FolderWidget---select folder data error"));
				return;
			}
		}

		UWidget_FolderItem* ExpandTreeFolder = nullptr;
		for (auto& RootFolderItem : RootFolderWidgets)
		{
			if (RootFolderItem->GetItemData().id == TreeID[0])
			{
				ExpandTreeFolder = RootFolderItem;
				break;
			}
		}
		for (int32 i = 1; i < TreeID.Num(); ++i)
		{
			if (IS_OBJECT_PTR_VALID(ExpandTreeFolder))
			{
				ExpandTreeFolder->SetChildFolderExpand(true);
				if (i != TreeID.Num() - 1)
				{
					ExpandTreeFolder = ExpandTreeFolder->GetChildFolders()[TreeID[i]];
				}
			}
		}
		if (IS_OBJECT_PTR_VALID(ExpandTreeFolder))
		{
			SelectFileItemEdit(ExpandTreeFolder->GetChildFilesMap()[TreeID.Last()]);
		}*/
	}
	ToolBarResetDelegate.ExecuteIfBound();

#endif
}

void UFolderWidget::AddNewFolderToDB(const FString& InName, const int32& FolderType)
{
#define GENERATE_REF_FILE_PARSE_MD5() \
int32 CurFolderType = SelectRefData.IsValid() ? SelectRefData.FolderDBData.folder_type : static_cast<int32>(EFolderType::EMultiComponents); \
const FString FileRelPath = URefRelationFunction::SaveNewLocalFile(Data, CurFolderType); \
const FString FileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelPath)); \
FString FileMD5 = TEXT(""); \
int64 FileSize = 0; \
if (ACatalogPlayerController::GetFileMD5AndSize(FileAbsPath, FileMD5, FileSize)) \
{ \
	Data.md5 = FileMD5; \
}

#ifdef USE_REF_LOCAL_FILE

	FRefDirectoryData Data;
	/*const auto UserInfo = UCatalogNetworkSubsystem::GetInstance()->GetGlobalDataConstRef();
	Data.createdBy = UserInfo.GetUserID() + TEXT("/") + UserInfo.GetUserName();
	Data.updatedBy = UserInfo.GetUserID() + TEXT("/") + UserInfo.GetUserName();*/
	Data.createdBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();
	Data.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();
	Data.folderName = InName;
	/*Data.folderId = TEXT("");
	Data.folderCode = TEXT("");
	Data.folderCodeExp = TEXT("");
	Data.thumbnailPath = TEXT("");
	Data.visibility = false;*/
	Data.isFolder = FolderType == 0;

#else

	FFolderTableData FolderTableData;
	FolderTableData.folder_name = InName;
	FolderTableData.folder_id = TEXT("");
	FolderTableData.folder_code = TEXT("");
	FolderTableData.thumbnail_path = TEXT("");
	FolderTableData.visibility = false;
	FolderTableData.deletable = false;
	FolderTableData.can_add_subfolder = FolderType ? false : true;

#endif
	
	if (CurrentSelectItem)
	{
#ifdef USE_REF_LOCAL_FILE

		//Data.id = FGuid::NewGuid().ToString();
		const auto ParentData = CurrentSelectItem->GetItemData();
		Data.dirOrder = CurrentSelectItem->GetSubLastOrder() + 1;
		Data.backendFolderPath = FPaths::Combine(ParentData.backend_folder_path, Data.id);

		/*const FString FileRelPath = URefRelationFunction::SaveNewLocalFile(Data, static_cast<int32>(EFolderType::ESingleComponent));
		const FString FileAbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelPath));
		FString FileMD5 = TEXT("");
		int64 FileSize = 0;
		if (ACatalogPlayerController::GetFileMD5AndSize(FileAbsPath, FileMD5, FileSize))
		{
			Data.md5 = FileMD5;
		}*/

		GENERATE_REF_FILE_PARSE_MD5();

		URefRelationFunction::GetSizeInfo(ACatalogPlayerController::Get()->GetGlobalParameterMap(),
			Data.width, Data.height, Data.depth);

		AddDataRequest(Data);

		/*FRefDirectoryData ParentData;
		if(UFolderWidget::Get()->GetCacheDataForRefDirectory(ParentID, ParentData))
		{
			Data.backend_folder_path = ParentData.backend_folder_path + "/" + Data.id;
			NetUUID.AddUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryAddRequest(Data);

		}
		else
		{
			checkf(false, TEXT("get ref directory data error!"));
		}*/


#else
		FolderTableData.folder_type = CurrentSelectItem->GetItemData().folder_type;
		FolderTableData.parent_id = CurrentSelectItem->GetItemData().id;

		if (UFolderTableOperatorLibrary::CreateFolderFileData(FolderTableData))
		{
			AddNewFolderFile(FolderTableData);
		}
#endif
	}
	else
	{//root
#ifdef USE_REF_LOCAL_FILE

		//Data.id = FGuid::NewGuid().ToString();
		Data.backendFolderPath = Data.id;
		Data.dirOrder = 0;
		
		GENERATE_REF_FILE_PARSE_MD5();

		AddDataRequest(Data);

#endif
	}

#undef GENERATE_REF_FILE_PARSE_MD5
}

void UFolderWidget::FolderRightMenuActionEdit(UWidget_FolderItem* ActionFolder, const int32& ActionType)
{
	if (ActionType == static_cast<int32>(ERightMenuType::Delete))
	{
		if (!URefRelationFunction::DeleteLocalFile(ActionFolder->GetItemData()))
		{
			UE_LOG(LogTemp, Error, TEXT("delete local file[uuid:%s][folderID:%s] failed!"), *ActionFolder->GetItemData().id, *ActionFolder->GetItemData().folder_id);
		}

		FolderRightClickActionDelegate.ExecuteIfBound(ActionFolder->GetItemData().id, true, (ActionFolder == CurrentSelectItem), ActionType);
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFolderItem = nullptr;
		CutFileItem = nullptr;
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Copy))
	{
		if (ActionFolder)
		{
			LastActionID = ActionFolder->GetItemData().id;
			LastActionFolderType = static_cast<int32>(ActionFolder->GetItemData().folder_type);
			LastActionType = ActionType;
			LastActionPath = ActionFolder->GetItemData().backend_folder_path;

			CutFolderItem = nullptr;
			CutFileItem = nullptr;
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Shear))
	{
		if (ActionFolder)
		{
			LastActionID = ActionFolder->GetItemData().id;
			LastActionFolderType = static_cast<int32>(ActionFolder->GetItemData().folder_type);
			LastActionType = ActionType;
			LastActionPath = ActionFolder->GetItemData().backend_folder_path;

			CutFolderItem = ActionFolder;
			CutFileItem = nullptr;
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Paste))
	{
		FolderRightClickActionDelegate.ExecuteIfBound(RootParentID, true, false, ActionType);
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFolderItem = nullptr;
		CutFileItem = nullptr;
		UE_LOG(LogTemp, Log, TEXT("Removing On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
	}
	SetUploadeEnable(CurrentSelectFile || CurrentSelectItem);
}

void UFolderWidget::FileRightMenuActionEdit(UWidget_FileItem* ActionFile, const int32& ActionType)
{
	UE_LOG(LogTemp, Log, TEXT("file right action id : '%s', action type : %d"), *ActionFile->GetItemData().id, ActionType);
	if (ActionType == static_cast<int32>(ERightMenuType::Delete))
	{
		if (!URefRelationFunction::DeleteLocalFile(ActionFile->GetItemData()))
		{
			UE_LOG(LogTemp, Error, TEXT("delete local file[uuid:%s][folderID:%s] failed!"), *ActionFile->GetItemData().id, *ActionFile->GetItemData().folder_id);
		}

		FolderRightClickActionDelegate.ExecuteIfBound(ActionFile->GetItemData().id, false, (ActionFile == CurrentSelectFile), ActionType);
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFileItem = nullptr;
		CutFolderItem = nullptr;
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Copy))
	{
		if (ActionFile)
		{
			LastActionID = ActionFile->GetItemData().id;
			LastActionFolderType = static_cast<int32>(ActionFile->GetItemData().folder_type);
			LastActionType = ActionType;
			LastActionPath = ActionFile->GetItemData().backend_folder_path;

			CutFileItem = nullptr;
			CutFolderItem = nullptr;
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Shear))
	{
		if (ActionFile)
		{
			LastActionID = ActionFile->GetItemData().id;
			LastActionFolderType = static_cast<int32>(ActionFile->GetItemData().folder_type);
			LastActionType = ActionType;
			LastActionPath = ActionFile->GetItemData().backend_folder_path;

			CutFileItem = ActionFile;
			CutFolderItem = nullptr;
		}
	}
	else if (ActionType == static_cast<int32>(ERightMenuType::Paste))
	{
		FolderRightClickActionDelegate.ExecuteIfBound(RootParentID, false, false, ActionType);
		LastActionID = RootParentID;
		LastActionFolderType = -1;
		LastActionType = static_cast<int32>(ERightMenuType::None);
		LastActionPath = TEXT("");

		CutFileItem = nullptr;
		CutFolderItem = nullptr;
		UE_LOG(LogTemp, Log, TEXT("Removing On hold Page"));
		UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance();
		OnHold->SwitchState(0);
		OnHold->SetVisibility(ESlateVisibility::Collapsed);
	}
	SetUploadeEnable(CurrentSelectFile || CurrentSelectItem);
}

void UFolderWidget::OnClickedBtnRelease()
{
	if (ACatalogPlayerController::Get()->GetReleaseLock())
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Releasing")).ToString());
		return;
	}

	//ACatalogPlayerController::Get()->UploadFileRequest(ServerDatabasePath);
	UWidget_FileItem* FileItem = GetCurrentSelectFile();
	UWidget_FolderItem* FolderItem = GetCurrentSelectItem();
	FString Id = TEXT("");
	int32 SelectType = -1;
	if (FileItem)
	{
		Id = FileItem->GetItemData().id;
		SelectType = 0;
	}
	else if (FolderItem)
	{
		Id = FolderItem->GetItemData().id;
		SelectType = 1;
	}
	if (Id.IsEmpty())
		return;

	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(true);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.1f);
	}

#ifdef USE_REF_LOCAL_FILE

	DownloadDatabaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(ServerDatabasePath);

#else
	DownloadDatabaseUUID = ACatalogPlayerController::Get()->DownloadFileRequest(ServerDatabasePath);

#endif
	// 
	//{
	//	FString  De = FString::Printf(TEXT("delete from file_image"));
	//	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(De);

	//	TArray<FFileDBData> Datas;
	//	FString  Sql = FString::Printf(TEXT("select * from file"));
	//	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FFileDBData>(Sql, Datas);

	//	for (auto& iter : Datas)
	//	{
	//		if (iter.thumbnail_path.IsEmpty())
	//			continue;
	//		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.thumbnail_path);

	//		FString LocalMD5 = TEXT("");
	//		int64 FileSize = 0;
	//		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//		FString InsertSql = FString::Printf(TEXT("insert into file_image values('%s','%s',%d)")
	//			, *iter.thumbnail_path, *LocalMD5, FileSize);
	//		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//	}
	//}

	//{
	//	FString  De = FString::Printf(TEXT("delete from param_image"));
	//	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(De);

	//	{
	//		TArray<FEnumParameterTableData> Datas;
	//		FString  Sql = FString::Printf(TEXT("select * from global_param_enum"));
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(Sql, Datas);

	//		for (auto& iter : Datas)
	//		{
	//			if (iter.image_for_display.IsEmpty())
	//				continue;
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.image_for_display);

	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
	//				, *iter.image_for_display, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//	}
	//	{
	//		TArray<FEnumParameterTableData> Datas;
	//		FString  Sql = FString::Printf(TEXT("select * from folder_param_enum"));
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(Sql, Datas);

	//		for (auto& iter : Datas)
	//		{
	//			if (iter.image_for_display.IsEmpty())
	//				continue;
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.image_for_display);
	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
	//				, *iter.image_for_display, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//	}
	//	{


	//		TArray<FEnumParameterTableData> Datas;
	//		FString  Sql = FString::Printf(TEXT("select * from file_param_enum"));
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(Sql, Datas);

	//		for (auto& iter : Datas)
	//		{
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.image_for_display);
	//			if (FilePath.IsEmpty())
	//				continue;
	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
	//				, *iter.image_for_display, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//	}
	//	{
	//		TArray<FEnumParameterTableData> Datas;
	//		FString  Sql = FString::Printf(TEXT("select * from multi_param_enum"));
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(Sql, Datas);

	//		for (auto& iter : Datas)
	//		{
	//			if (iter.image_for_display.IsEmpty())
	//				continue;
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.image_for_display);

	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
	//				, *iter.image_for_display, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//	}
	//	{
	//		TArray<FEnumParameterTableData> Datas;
	//		FString  Sql = FString::Printf(TEXT("select * from decorate_param_enum"));
	//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FEnumParameterTableData>(Sql, Datas);

	//		for (auto& iter : Datas)
	//		{
	//			if (iter.image_for_display.IsEmpty())
	//				continue;
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.image_for_display);
	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
	//				, *iter.image_for_display, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//	}
	//}

	//{
	//	FString  De = FString::Printf(TEXT("delete from other_file"));
	//	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(De);
	//
	//	TArray<FSingleComponentTableData> Datas;
	//	FString  Sql = FString::Printf(TEXT("select * from single_comp"));
	//	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FSingleComponentTableData>(Sql, Datas);

	//	for (auto& iter : Datas)
	//	{
	//		{
	//			if (iter.data_path.IsEmpty())
	//				continue;
	//			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.data_path);

	//			FString LocalMD5 = TEXT("");
	//			int64 FileSize = 0;
	//			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//			FString InsertSql = FString::Printf(TEXT("insert into other_file values('%s','%s',%d)")
	//				, *iter.data_path, *LocalMD5, FileSize);
	//			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//		}
	//		{
	//			TArray<TPair<FString, FString>> PathAndMd5;
	//			iter.FilePathsAndMD5Pairs(PathAndMd5);
	//			for (auto& depIter : PathAndMd5)
	//			{
	//				if (depIter.Key.IsEmpty())
	//					continue;
	//				const FString DepPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + depIter.Key);

	//				FString DepMD5 = TEXT("");
	//				int64 DepSize = 0;
	//				ACatalogPlayerController::GetFileMD5AndSize(DepPath, DepMD5, DepSize);
	//				FString InsertSql = FString::Printf(TEXT("insert into other_file values('%s','%s',%d)")
	//					, *depIter.Key, *DepMD5, DepSize);
	//				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//			}
	//		}
	//	}
	//}
	//{


	//	TArray <FCustomMaterialTableData> Datas;
	//	FString  Sql = FString::Printf(TEXT("select * from material"));
	//	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FCustomMaterialTableData>(Sql, Datas);

	//	for (auto& iter : Datas)
	//	{
	//		if (iter.import_path.IsEmpty())
	//			continue;
	//		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.import_path);

	//		FString LocalMD5 = TEXT("");
	//		int64 FileSize = 0;
	//		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//		FString InsertSql = FString::Printf(TEXT("insert into other_file values('%s','%s',%d)")
	//			, *iter.import_path, *LocalMD5, FileSize);
	//		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//	}
	//}

	//{
	//	FString  De = FString::Printf(TEXT("delete from style_image"));
	//	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(De);

	//	TArray <FDecorateStyle> Datas;
	//	FString  Sql = FString::Printf(TEXT("select * from decorate_style"));
	//	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDecorateStyle>(Sql, Datas);

	//	for (auto& iter : Datas)
	//	{
	//		if (iter.thumbnail_path.IsEmpty())
	//			continue;
	//		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.thumbnail_path);

	//		FString LocalMD5 = TEXT("");
	//		int64 FileSize = 0;
	//		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

	//		FString InsertSql = FString::Printf(TEXT("insert into style_image values('%s','%s',%d)")
	//			, *iter.thumbnail_path, *LocalMD5, FileSize);
	//		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
	//	}
	//}

}

void UFolderWidget::RemoveProcess()
{
	if (MergeProcessWidget)
	{
		MergeProcessWidget->RemoveFromParent();
	}
}

void UFolderWidget::OnClickedBtnFolderMaping()
{

}

UFrontDirectoryWidget* UFolderWidget::GetFrontDirectoryWidget()
{
	UFrontDirectoryWidget::GetInstance()->FrontMappingDelegate.BindUFunction(this, FName(TEXT("OnMappingEditHander")));
	//UFrontDirectoryWidget::GetInstance()->SwitchShowType(EFrontDirectoryDataType::E_Mapping);
	return UFrontDirectoryWidget::GetInstance();
}

void UFolderWidget::OnMappingEditHander(const FString& NewMapping)
{
	if (!NewMapping.IsEmpty())
	{
		//SendUpdateDataRequest(FolderData, NewMapping);

		CacheTableDatas.Empty();

		for (auto ItemIte : SelectedItems)
		{
			if (ItemIte != nullptr && ItemIte->IsA<UWidget_FileItem>())
			{
				UWidget_FileItem* FileItem = Cast<UWidget_FileItem>(ItemIte);
				SendUpdateDataRequest(FileItem->GetItemData(), NewMapping);
			}
		}
	}

	MappingUIShow(false);
	UFrontDirectoryWidget::GetInstance()->FrontMappingDelegate.Unbind();
}


void UFolderWidget::SendUpdateDataRequest(const FFolderTableData& UpdateFolderData, const FString& FrontPath /*= TEXT("")*/)
{
	FRefDirectoryData Data;
	if (UFolderWidget::Get()->GetCacheDataForRefDirectory(UpdateFolderData.id, Data))
	{
		Data.folderCode = UpdateFolderData.folder_code;
		Data.folderCodeExp = UpdateFolderData.folder_code_exp;
		Data.folderId = UpdateFolderData.folder_id;
		Data.folderName = UpdateFolderData.folder_name;
		Data.folderNameExp = UpdateFolderData.folder_name_exp;

		Data.isNew = UpdateFolderData.is_new;
		Data.visibility = UpdateFolderData.visibility;
		Data.visibilityExp = UpdateFolderData.visibility_exp;
		Data.backendFolderPath = UpdateFolderData.backend_folder_path;

		Data.width = UpdateFolderData.width;
		Data.height = UpdateFolderData.height;
		Data.depth = UpdateFolderData.depth;
		Data.boxOffset = UpdateFolderData.boxOffset;

		if (!FrontPath.IsEmpty())
		{
			Data.fontFolderPath = FrontPath;
		}

		Data.updatedBy = UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo();
		Data.description = UpdateFolderData.description;

		CacheTableDatas.Add(UpdateFolderData);

		NetUUIDs.Add( UCatalogNetworkSubsystem::GetInstance()->SendBackDirectoryUpdateRequest(Data));
	}

}

void UFolderWidget::OnBackendDirectoryUpdateHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas)
{
	/*
	if (NetUUID.UpdateUUID.Equals(UUID))
	{
		NetUUID.ResetUpdateAction();
		if (bSuccess && Datas.IsValidIndex(0))
		{
			if (FolderData.id != Datas[0].id)
			{
				RefreshProperty(FolderData);
				return;
			}

			FFolderTableData InData;
			URefRelationFunction::ConvertDirctoryDataToDBData(Datas[0], InData);

			URefRelationFunction::RenameLocalFile(
				URefRelationFunction::GetRefFileRelativePath(FolderData),
				URefRelationFunction::GetRefFileRelativePath(InData)
			);

			FolderData = InData;
			RefreshProperty(FolderData);
			if (UFolderWidget::Get())
			{
				UFolderWidget::Get()->SyncSelectData(Datas[0]);
				UFolderWidget::Get()->SyncRefLocalData(Datas[0]);
				UFolderWidget::Get()->UpdateSelectFolderData(InData, InData.can_add_subfolder);
			}

			FolderPropertyChangeDelegate.ExecuteIfBound(InData.id, InData.folder_name, InData.can_add_subfolder, !FMath::IsNearlyZero(InData.visibility));

		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
			RefreshProperty(FolderData);
		}
	}
	*/
}


#undef LOCTEXT_NAMESPACE
#undef ServerDatabasePath