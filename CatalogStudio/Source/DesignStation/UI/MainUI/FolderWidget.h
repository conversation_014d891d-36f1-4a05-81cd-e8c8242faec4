// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "CustomMaterialEdit/DataDefine/MaterialParameterBasicData.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"
#include "Components/WidgetSwitcher.h"
#include "FolderWidget.generated.h"

/**
 *
 */

struct FRefDirectoryData;
struct FRefDirectoryDataArr;

UENUM(BlueprintType)
enum class EFolderOrFileActionType : uint8
{
	Up = 0,
	Down
};

class UEditableText;
class UButton;
class UScrollBox;
class UBorder;
class UWidget_PopAddFolder;
class UMaterialPopSelectWidget;
class UMaterialPopAddWidget;
class UMergeProcessWidget;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FSelectFolderDelegate, UWidget_FolderItem*, SelectFolder, bool, IsFolderExpand);
DECLARE_DYNAMIC_DELEGATE_OneParam(FFolderTreeSelectFileDelegate, UWidget_FileItem*, SelectFile);
DECLARE_DYNAMIC_DELEGATE_FourParams(FFolderActionDelegate, const FString&, FolderId, bool, IsFolder, bool, IsSelected, const int32&, ActionType);
DECLARE_DYNAMIC_DELEGATE(FToolBarResetDelegate);

UCLASS()
class DESIGNSTATION_API UFolderWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	//virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;
	void UpdateSelectFolderData(const FFolderTableData& InFolderData, bool IsFolder = true);
	bool GetSelectedFolder(FFolderTableData& OutFolderData);
	void IsEnableAddFolderOrFile(bool IsEnable1, bool IsEnable2);
	void EnableMutilBtnList();
	void ReSelectCurrentItem();
	void FolderOrFileUpDown(const EFolderOrFileActionType& ActionType);

	void RefreshFolderTreeLayout();
	void ClearSelectWidget();
	TArray<FFolderTableData>& GetSelectParentSubFolders(bool IsFolder);
	void SyncViewSelectFolderOrFile(const FString& InId, bool IsFolder);
	void SyncBreadNavigation(UWidget_FolderItem* InWidget);
	void SyncViewListRightAction(const FFolderTableData& ViewItemData, const int32& ActionType);
	void ToMainRefreshSelectFile(const FFolderTableData& InDataItemData);

	void SearchFile(const FString& InString);

	FORCEINLINE UWidget_FolderItem* GetCurrentSelectItem() const { return CurrentSelectItem; }
	FORCEINLINE void SetCurrentSelectItem(UWidget_FolderItem* InItem) { CurrentSelectItem = InItem; }
	FORCEINLINE UWidget_FileItem* GetCurrentSelectFile() const { return CurrentSelectFile; }
	FORCEINLINE UWidget_FolderItem* GetCutFolderItem() { return CutFolderItem; };
	FORCEINLINE UWidget_FileItem* GetCutFileItem() { return CutFileItem; }
	FORCEINLINE FString GetActionID() const { return LastActionID; }
	FORCEINLINE int32 GetActionType() const { return LastActionType; }
	FORCEINLINE int32 GetActionFolderType() const { return LastActionFolderType; }
	FORCEINLINE FString GetActionPath() const { return LastActionPath; }

	static UFolderWidget* Get();
	//if selected item inside present tree return false and delete anything afterwards, otherwise empty the map, add new item and its all parent to map and return ture
	bool IsSelectOnNewFolderTree(TMap<FString, FString>& InMap, UWidget_FolderItem* InSelectFolder);
	bool IsSelectOnNewFolderTree(TMap<FString, FString>& InMap, UWidget_FileItem* InSelectFolder);

	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)override ;
	virtual FReply NativeOnKeyUp(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)override;
public: // local database logic
	void GenerateRootFolder(const TArray<FFolderTableData>& OutParams);
	void GenerateRootFolderInner(const FFolderTableData& Data);
	void SearchFileResult(const TArray<FFolderTableData>& OutParams);
	void AddNewFolderFile(const FFolderTableData& OutParam);
	void SwapTwoItemsAction();

	void SearchFolderPath(const TArray<FString>& OutParams);
	void OpenFolderPathRecurse(const TArray<FString>& OutParams);
	void GrayFolderPathRecurse(TArray<FString> GrayPath, const TPair<bool, bool>& IsCheck, const TPair<FString, bool>& SelectIDFolderPair);
	void UnGrayWidget();
	void SetGrayWidget(UWidget_FolderItem* InWidget);

	bool GetChildFolderAndFile(const FString& InId, bool IsFolder, TArray<FFolderTableData>& OutDatas);
	bool InsertDataToServerDatabase(TArray<FFolderTableData>& OutDatas);
	void CompareFileData();
	void MergeData();
	void DeleteFolder(const FString& InId, bool IsFolder);
	void SetUploadeEnable(bool IsTrue);
	void UploadSuccess(bool IsTrue);

	bool IsMultiSelect() const { return SelectedItems.Num() > 1; }

	void ClearUnVaildMultiSelect(UWidget_FolderItem* InFolder);

	bool IsInMultiSelect(UWidget_FolderItem* InFolder);
	bool IsInMultiSelect(UWidget_FileItem* InFolder);
	void UpdateMultiSelectItem(UWidget_FolderItem* InItem);
	void UpdateMultiSelectItem(UWidget_FileItem* InItem);
public:
	void CreateNewCustomMatFile(const FCustomMaterialTableData& MatData);


#pragma region RefDataOperator

public:
	FRefToLocalFileData GetSelectLocalRefData() const { return SelectRefData; }
	FRefToLocalFileData& GetSelectLocalRefDataRef() { return SelectRefData; }

	void SetSelectLocalRefData(const FRefToLocalFileData& InData) { SelectRefData = InData; }

	/* 
	 *  @@ 用folder_id\id加载本地文件数据
	 *	@@ 加入文件比对下载逻辑
	 */
	void SyncSelectLocalRefData(const FFolderTableData& Data);

	/*
	*  @@ self select
	*/
	void SelfSelectWidgetAfterDownload(const FString& IDOrFolderID);

	/*
	 *  @@ 更新顶层（ 文件或文件夹上挂载的 ）参数
	 */
	void UpdateTopLevelParameters(const TArray<FParameterData>& InParams, bool Delete);

	/**
	 *  @@ 强制刷新文件中枚举信息 -- 数据同步，不用于外部
	 */
	void ForceSyncParamEnumData();


	//void UpdateFilePropertyData(const FFolderTableData& InProperty);

	void AddCacheDataForFile(const FString& InID, const FRefToLocalFileData& InData);
	void AddCacheDataForFile(const FString& InID, const TArray<FRefToLocalFileData>& InData);
	bool GetCacheDataForFile(const FString& InID, FRefToLocalFileData& OutData);
	void GetCacheDataForFile(const TArray<FString>& InID, TArray<FRefToLocalFileData>& OutData);
	void UpdateCacheDataForFile(const FRefToLocalFileData& InData);

	void AddCacheDataForRefDirectory(const TArray<FRefDirectoryData>& InData);
	void AddCacheDataForRefDirectory(const FRefDirectoryData& InData);
	bool GetCacheDataForRefDirectory(const FString& InID, FRefDirectoryData& OutData);

	void AddCacheDataForDirectory(const FString& InDirectory, const FRefDirectoryDataArr& InData);
	void AddCacheDataForDirectory(const FString& InDirectory, const TArray<FRefDirectoryData>& InData);
	void AddCacheDataForDirectory_Single(const FString& InDirectory, const FRefDirectoryData& InData);
	bool GetCacheDataForDirectory(const FString& InDirectory, FRefDirectoryDataArr& OutData);
	bool GetCacheDataForDirectory(const FString& InDirectory, TArray<FRefDirectoryData>& OutData);
	bool GetCacheDataForDirectory(const FString& InDirectory, TArray<FFolderTableData>& OutData);
	bool RemoveCacheData(const FString& InDirectory);

	void SyncSelect(const FRefToLocalFileData& InData);
	void SyncSelectWidget(const FRefDirectoryData& InData);
	void SyncAddData(const FString& ParentDirectoryPath, const FRefDirectoryData& InData);
	void SyncSelectData(const FRefDirectoryData& InData);
	bool SyncRefLocalData(const FRefDirectoryData& InData);

	//sync sort 
	void SyncSortData(const FString& InDirectory, const TArray<FFolderTableData>& InData);

	bool SyncRefLocalData(const FString& NameID);

	void SyncRefType(const TArray<FParameterData>& FileParams, FString& RefTypeCode, FString& RefType);

	/*
	*  @@ sync select data
	*/
	bool SyncRefLocalData_Inner(const FFolderTableData& Data);

	bool CanExecuteCopyOperator(const FString& OriginID, const FString& TargetID);
	bool ConstructCutActionData(const FString& OriginID, const FString& TargetID, FRefDirectoryData& OutData);

	void SyncLocalDBFile();
	void InsertDataToServerDatabase(const FFolderTableData& FolderData, FString UpperDirectory);

	//sync associate info to file
	void SyncAssociateDataToFile(const TArray<FAssociateListData>& InAssociateData);

private:
	/**
	 *  @@ 更新当前选中文件所有数据
	 */
	void RefreshRefFileAllThing();

	/*
	 *  @@ 刷新文件
	 */
	void RefreshLocalRefFile();

private:
	UPROPERTY()
	FRefToLocalFileData SelectRefData;

	/*
	 *  @@ 缓存的文件数据，防止频繁读取文件
	 *	@@ pair : folder_id(id) --- file data 
	 */
	UPROPERTY()
	TMap<FString, FRefToLocalFileData> CacheRefData;

	/*
	 *  @@ 缓存的文件数据，防止频繁查询接口
	 *	@@ pair : id --- ref data
	 */
	UPROPERTY()
	TMap<FString, FRefDirectoryData> CacheRefDirData;

	/*
	 *  @@ 缓存的子目录数据
	 *	@@ pair : directory --- subdirectories arr
	 */
	UPROPERTY()
	TMap<FString, FRefDirectoryDataArr> CacheSubRefData;

#pragma endregion

	/*
	 *  @@ 将风格数据放入单例下，便于使用
	 */
#pragma region StyleRefDataOperator

public:
	FRefToStyleFile GetStyleRefData() { return StyleRefFileData; }
	FRefToStyleFile& GetStyleRefDataRef() { return StyleRefFileData; }
	void SetStyleRefData(const FRefToStyleFile& InData) { StyleRefFileData = InData; }

	void InitStyleRefData();

	/*
	*  @@ temp logic to format style info
	*  @@ no use for other logic
	*/
	void FormatStyleRefData();

	void DownloadStyle();
	void UploadStyle();
	void SaveStyle();

	/*
	*  @@ sync old local database
	*/
	void SyncOldDataRequest(const FRefDirectoryData& InData);
	TMap<FString, FRefDirectoryData> GetSyncDatas() { return SyncDatas; }

private:
	UPROPERTY()
	FRefToStyleFile StyleRefFileData;

#pragma endregion

	/*
	 *  @@ place ruler data
	 */
#pragma region PlaceRule

public:
	void InitPlaceRuleData();
	TArray<FRefPlaceRuleData> GetPlaceRuleDatas() const { return PlaceRuleDatas; }
	FRefPlaceRuleData GetPlaceRuleByID(const int32& InRuleID);
	TArray<FString> GetPlaceRuleCombineStr();
	//test
	TArray<FRefPlaceRuleData> GetPlaceRuleDatasByCombineStr(const TArray<FString>& InCombineStr);
	
private:
	UPROPERTY()
	TArray<FRefPlaceRuleData> PlaceRuleDatas;
	
#pragma endregion

public:
	void BindDelegates();
	UFUNCTION()
		void OnReleaseGetResponseHandler(const FString& UUID);
	UFUNCTION()
		void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
		void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);
	UFUNCTION()
		void OnMergeInsertLogResponseHandler(const FString& UUID, const int32& InsertId);


	void UploadFileRequest(const FString& FileRelativePath);
	void UploadFileRequest_NoHandler(const FString& FileRelativePath);
	void DownloadFileRequest(const FString& FileRelativePath);
	void AddDataRequest(const FRefDirectoryData& InData);
	void UpdateDataRequest(const FRefDirectoryData& InData);
	void SendSwapDataOrderRequest(const FString& InID1, const FString& InID2);
	UFUNCTION()
		void OnAddNewResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);
	UFUNCTION()
		void OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	UFUNCTION()
		void OnSortResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	UFUNCTION()
	void OnRootDirectoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	void ObsurceSearchRequest(const FString& InStr, const FString& InID);
	UFUNCTION()
	void OnObsurceSearchResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	void ObscureSearchExpandRequest(const FString& InStrPath);

	void SearchPlaceRuleRequest();
	UFUNCTION()
		void OnSearchPlaceRuleResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefPlaceRuleData>& Datas);

private:
	FString ReleaseMultiUUID;
	FString ReleaseParamUUID;
	FString ReleaseInsertUUID;
	FString ReleaseCopyDirUUID;
	FString ReleaseAllUUID;

	FString UploadImageUUID;
	FString UploadSingleUUID;
	FString UploadPakUUID;

	FString UploadDatabaseUUID = TEXT("");

	FString MergeLogUUID;

	FString DownloadFileUUID;

	FString UploadMatUUID;

	/*
	 *  @@ all net uuid
	 */
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

	UPROPERTY()
	TMap<FString, FRefDirectoryData> SyncDatas;

	UPROPERTY()
		TArray<FFolderTableData> SwapDatas;

	UPROPERTY()
		TArray<FString> ObsurceSearchPaths;

private:
	void AddNewFileToDB(const FString& FolderId, const EFolderType& FileType);
	void GenerateFolderLayout();
	bool IsOnlyFileMultiSelected();
protected:
	/*UFUNCTION()
		void OnTextCommittedEdtSearch(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnSearch();*/
	UFUNCTION()
		void OnClickedBtnCreateFolder();
	UFUNCTION()
		void OnClickedBtnCreateFile();

	UFUNCTION()
		void SelectItemEdit(UWidget_FolderItem* InWidget, bool IsFolderExpand);

	UFUNCTION()
		void UnFoldItem(UWidget_FolderItem* InWidget, bool IsSubOpen);
	UFUNCTION()
		void SelectFileItemEdit(UWidget_FileItem* InWidget);

	UFUNCTION()
		void SearchFileSelected(UWidget_FileItem* InWidget);

	UFUNCTION()
		void AddNewFolderToDB(const FString& InName, const int32& FolderType);
	UFUNCTION()
		void FolderRightMenuActionEdit(UWidget_FolderItem* ActionFolder, const int32& ActionType);
	UFUNCTION()
		void FileRightMenuActionEdit(UWidget_FileItem* ActionFolder, const int32& ActionType);
	UFUNCTION()
		void OnClickedBtnRelease();
	UFUNCTION()
		void RemoveProcess();
		UFUNCTION()
		void OnClickedBtnFolderMaping();

protected:
	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	class UFrontDirectoryWidget* GetFrontDirectoryWidget();

	UFUNCTION(BlueprintCallable, Category = "FolderFileProperty")
	void OnMappingEditHander(const FString& NewMapping);

	UFUNCTION(BlueprintImplementableEvent, Category = "FolderFileProperty")
	void MappingUIShow(bool IsShow);

	void SendUpdateDataRequest(const FFolderTableData& UpdateFolderData, const FString& FrontPath = TEXT(""));
	UFUNCTION()
	void OnBackendDirectoryUpdateHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& Datas);

	UPROPERTY()
	TArray<FString> NetUUIDs;

	UPROPERTY()
	TArray<FFolderTableData> CacheTableDatas;

private:
	UPROPERTY()
		TArray<UWidget_FolderItem*> RootFolderWidgets;
	UPROPERTY()
		UWidget_FolderItem* CurrentSelectItem;
	UPROPERTY()
		UWidget_FileItem* CurrentSelectFile;

	UPROPERTY()
		TArray<FString> SelectFolderPath;
	UPROPERTY()
		UWidget_FolderItem* GrayWidget;
	bool IsViewSelect; //用于中间选中时对置灰的处理

	UPROPERTY()
		TArray<FString> FilesToUpload;

	//search file
	UPROPERTY()
		TArray<UWidget_FileItem*> SearchFiles;

	//right action
	UPROPERTY()
		FString LastActionID;
	UPROPERTY()
		int32 LastActionFolderType;
	UPROPERTY()
		int32 LastActionType;
	UPROPERTY()
		FString LastActionPath;


	UPROPERTY()
	UWidget_FolderItem*    CutFolderItem;
	UPROPERTY()
	UWidget_FileItem*      CutFileItem;
	UPROPERTY()
	TMap<FString, FString> SelectedMap;
	UPROPERTY()
	TArray<UFolderAndFileBaseWidget*> SelectedItems;
private:
	UPROPERTY()
		UBorder* BorFolderTree;
	UPROPERTY()
		UScrollBox* ScbFolderLayout;
	UPROPERTY()
		UButton* BtnCreateFolder;
	UPROPERTY()
		UButton* BtnCreateFile;

	UPROPERTY()
		UBorder* BorSearch;
	UPROPERTY()
		UScrollBox* ScbSearch;

	UPROPERTY()
		UButton* BtnRelease; //文件树下面的上传按钮

	static UFolderWidget* FolderWidgetInstance;
	static FString FolderWidgetPath;

	UPROPERTY()
		UMergeProcessWidget* MergeProcessWidget;

	UPROPERTY(meta = (BindWidget))
	UWidgetSwitcher *WS_BtnList;

	UPROPERTY(meta = (BindWidget))
	UButton *Btn_FolderMaping;

	FString DownloadDatabaseUUID;
	bool bShiftDown = false;
public:
	FSelectFolderDelegate SelectFolderDelegate;
	FFolderTreeSelectFileDelegate SelectFileDelegate;
	FFolderActionDelegate FolderRightClickActionDelegate;
	FToolBarResetDelegate ToolBarResetDelegate;
	FSimpleDelegate ClearSelectDelegate;
};
