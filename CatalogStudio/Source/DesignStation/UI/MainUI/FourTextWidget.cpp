// Fill out your copyright notice in the Description page of Project Settings.

#include "FourTextWidget.h"

#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
FString UFourTextWidget::BpPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/FourTextUI.FourTextUI_C'");

bool UFourTextWidget::Initialize()
{
    if (!Super::Initialize())
    {
        return false;
    }
    BIND_PARAM_CPP_TO_UMG(TxtReleaser, Txt_Releaser);
    BIND_PARAM_CPP_TO_UMG(TxtDate, Txt_Date);
    BIND_PARAM_CPP_TO_UMG(TxtOperation, Txt_Operation);
    BIND_PARAM_CPP_TO_UMG(TxtState, Txt_State);

    return true;
}

UFourTextWidget* UFourTextWidget::Create()
{
    return UUIFunctionLibrary::UIWidgetCreate<UFourTextWidget>(UFourTextWidget::BpPath);
}

void UFourTextWidget::UpdateLogText(const FString& Releaser, const FString& Date, const FString& Operation, const FString& State)
{
    TxtReleaser->SetText(FText::FromString(Releaser));
    TxtDate->SetText(FText::FromString(Date));
    TxtOperation->SetText(FText::FromString(Operation));
    TxtState->SetText(FText::FromString(State));
}

void UFourTextWidget::UpdateLogText(const FMergeLogData& MergeLog)
{
    TxtReleaser->SetText(FText::FromString(MergeLog.userName));
    TxtDate->SetText(FText::FromString(MergeLog.updateTime));
    TxtOperation->SetText(FText::FromString(MergeLog.operation));
    TxtState->SetText(FText::FromString(MergeLog.remark));
}
