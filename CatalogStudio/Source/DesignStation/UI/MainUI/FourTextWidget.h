// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Release/ReleaseLogData.h"
#include "FourTextWidget.generated.h"

/**
 * 
 */

class UTextBlock;


USTRUCT()
struct FMergeLog
{
	GENERATED_USTRUCT_BODY()

public:
	FString StrReleaser;
	FString StrDate;
	FString StrOperation;
	FString StrRemark;

public:
	FMergeLog() :StrReleaser(TEXT("")), StrDate(TEXT("")), StrOperation(TEXT("")), StrRemark(TEXT("")) {}

};


UCLASS()
class DESIGNSTATION_API UFourTextWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	bool Initialize() override;

	static UFourTextWidget* Create();

public:
	void UpdateLogText(const FString& Releaser,const FString& Date,const FString& Operation,const FString& State);
	void UpdateLogText(const FMergeLogData& MergeLog);

private:
	static FString BpPath;

	int32 Id;
public:
	UPROPERTY()
		UTextBlock* TxtReleaser;
	UPROPERTY()
		UTextBlock* TxtDate;
	UPROPERTY()
		UTextBlock* TxtOperation;
	UPROPERTY()
		UTextBlock* TxtState;
};
