// Fill out your copyright notice in the Description page of Project Settings.

#include "MainToolBarWidget.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/MenuAnchor.h"
#include "ReleaseLogWidget.h"
#include "Components/Image.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "DataCenter/MultiThread/MultiThreadFunctionLibrary.h"
#include "DesignStation/SQLite/ReleaseLocalDatabaseLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/FrontDirectory/FrontShowWidget.h"
#include "DesignStation/UI/LoadUI/MergeProcessWidget.h"
#include "DesignStation/UI/Release/ReleaseWidget.h"

#define ServerDatabasePath FString("Cache/server_cache.db")
#define LocalDataPath FString("Cache/local_data.db")
#define EmptyImage FString("6023aca2c84dee97eeac3a98c0499b81")
FString UMainToolBarWidget::MainToolBarWidgetPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/MainToolBarUI.MainToolBarUI_C'");

const FLinearColor MainToolBarHover = FLinearColor(1.0f, 1.0f, 1.0f, 0.2f);
const FLinearColor MainToolBarUnHover = FLinearColor(0.033105f, 0.033105f, 0.033105f, 1.0f);

bool UMainToolBarWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(EdtSearch, Edt_SearchName);
	BIND_WIDGET_FUNCTION(EdtSearch, OnTextChanged, UMainToolBarWidget::OnTextChangedEdtSearch);
	BIND_WIDGET_FUNCTION(EdtSearch, OnTextCommitted, UMainToolBarWidget::OnTextCommittedEdtSearch);
	BIND_PARAM_CPP_TO_UMG(BtnSearch, Btn_Search);
	BIND_WIDGET_FUNCTION(BtnSearch, OnClicked, UMainToolBarWidget::OnClickedBtnSearch);
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UMainToolBarWidget::OnClickedBtnUp);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UMainToolBarWidget::OnClickedBtnDown);

	BIND_PARAM_CPP_TO_UMG(BorGlobalParam, Bor_GlobalParam);
	BIND_PARAM_CPP_TO_UMG(BtnGlobalParams, Btn_GlobalParam);
	BIND_WIDGET_FUNCTION(BtnGlobalParams, OnClicked, UMainToolBarWidget::OnClickedBtnGlobalParams);
	BIND_WIDGET_FUNCTION(BtnGlobalParams, OnHovered, UMainToolBarWidget::OnHoveredBtnGlobalParams);
	BIND_WIDGET_FUNCTION(BtnGlobalParams, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnGlobalParams);

	BIND_PARAM_CPP_TO_UMG(BorStyle, Bor_Style);
	BIND_PARAM_CPP_TO_UMG(BtnStyle, Btn_Style);
	BIND_WIDGET_FUNCTION(BtnStyle, OnClicked, UMainToolBarWidget::OnClickedBtnStyle);
	BIND_WIDGET_FUNCTION(BtnStyle, OnHovered, UMainToolBarWidget::OnHoveredBtnStyle);
	BIND_WIDGET_FUNCTION(BtnStyle, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnStyle);

	BIND_PARAM_CPP_TO_UMG(BorSet, Bor_Set);
	BIND_PARAM_CPP_TO_UMG(BtnSet, Btn_Set);
	BIND_WIDGET_FUNCTION(BtnSet, OnClicked, UMainToolBarWidget::OnClickedBtnSet);
	BIND_WIDGET_FUNCTION(BtnSet, OnHovered, UMainToolBarWidget::OnHoveredBtnSet);
	BIND_WIDGET_FUNCTION(BtnSet, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnSet);

	BIND_PARAM_CPP_TO_UMG(BtnDirectory, Btn_Directory);
	BIND_WIDGET_FUNCTION(BtnDirectory, OnClicked, UMainToolBarWidget::OnClickedBtnDirectory);

	BIND_PARAM_CPP_TO_UMG(ImgUser, Img_User);
	BIND_PARAM_CPP_TO_UMG(TxtUserName, Txt_UserName);
	BIND_PARAM_CPP_TO_UMG(BtnUserSetting, Btn_UserSetting);
	BIND_WIDGET_FUNCTION(BtnUserSetting, OnClicked, UMainToolBarWidget::OnClickedBtnSetting);

	BIND_PARAM_CPP_TO_UMG(BorExit, Bor_Exit);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, UMainToolBarWidget::OnClickedBtnExit);
	BIND_WIDGET_FUNCTION(BtnExit, OnHovered, UMainToolBarWidget::OnHoveredBtnExit);
	BIND_WIDGET_FUNCTION(BtnExit, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnExit);

	BIND_PARAM_CPP_TO_UMG(BtnReleaseLog, Btn_ReleaseLog);
	BIND_WIDGET_FUNCTION(BtnReleaseLog, OnHovered, UMainToolBarWidget::OnHoveredBtnReleaseLog);
	BIND_WIDGET_FUNCTION(BtnReleaseLog, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnReleaseLog);

	BIND_PARAM_CPP_TO_UMG(MaReleaseLog, Ma_ReleaseLog);
	MaReleaseLog->MenuClass = UReleaseLogWidget::StaticClass();
	MaReleaseLog->OnGetUserMenuContentEvent.BindUFunction(this, FName(TEXT("GetLogMAContent")));

	BIND_PARAM_CPP_TO_UMG(BtnMergeLog, Btn_MergeLog);
	BIND_WIDGET_FUNCTION(BtnMergeLog, OnHovered, UMainToolBarWidget::OnHoveredBtnMergeLog);
	BIND_WIDGET_FUNCTION(BtnMergeLog, OnUnhovered, UMainToolBarWidget::OnUnHoveredBtnMergeLog);

	BIND_PARAM_CPP_TO_UMG(MaMergeLog, Ma_MergeLog);
	MaMergeLog->MenuClass = UReleaseLogWidget::StaticClass();
	MaMergeLog->OnGetUserMenuContentEvent.BindUFunction(this, FName(TEXT("GetMergeLogMAContent")));

	BIND_PARAM_CPP_TO_UMG(BtnRelease, Btn_Release);
	BIND_PARAM_CPP_TO_UMG(BtnDownload, Btn_Download);
	BIND_WIDGET_FUNCTION(BtnRelease, OnClicked, UMainToolBarWidget::OnClickedBtnRelease);
	BIND_WIDGET_FUNCTION(BtnDownload, OnClicked, UMainToolBarWidget::OnClickedBtnDownload);

	UpdateToolBarState(static_cast<int32>(EMainToolBarState::NoneEnable));
	MergeLogOrProcess(true);

	BindDelegate();

	return true;
}

void UMainToolBarWidget::UpdateToolBarInfo(const FUserInfoTableData& UserInfoData)
{
	if (IS_OBJECT_PTR_VALID(TxtUserName))
	{
		TxtUserName->SetText(FText::FromString(UserInfoData.name));
	}
}

void UMainToolBarWidget::UpdateToolBarState(const int32& ActionType)
{
	if (IS_OBJECT_PTR_VALID(BtnUp) && IS_OBJECT_PTR_VALID(BtnDown))
	{
		if (ActionType == static_cast<int32>(EMainToolBarState::UpEnable))
		{
			BtnUp->SetIsEnabled(true);
			BtnDown->SetIsEnabled(false);
		}
		else if (ActionType == static_cast<int32>(EMainToolBarState::DownEnable))
		{
			BtnUp->SetIsEnabled(false);
			BtnDown->SetIsEnabled(true);
		}
		else if (ActionType == static_cast<int32>(EMainToolBarState::AllEnable))
		{
			BtnUp->SetIsEnabled(true);
			BtnDown->SetIsEnabled(true);
		}
		else if (ActionType == static_cast<int32>(EMainToolBarState::NoneEnable))
		{
			BtnUp->SetIsEnabled(false);
			BtnDown->SetIsEnabled(false);
		}
	}
}

void UMainToolBarWidget::ResetSearchWidget()
{
	if (IS_OBJECT_PTR_VALID(EdtSearch))
	{
		EdtSearch->SetText(FText::FromString(TEXT("")));
	}
}

UMainToolBarWidget* UMainToolBarWidget::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UMainToolBarWidget::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UMainToolBarWidget>(UMainToolBarWidget::MainToolBarWidgetPath);
}

void UMainToolBarWidget::BindDelegate()
{ 
#ifdef USE_REF_LOCAL_FILE

	//UCatalogNetworkSubsystem::GetInstance()->ReleaseAllDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnReleaseFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnDownloadFileResponseHandler);
	//UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnUploadFileResponseHandler);
	//UCatalogNetworkSubsystem::GetInstance()->ReleaseInfosResponseDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnReleaseInfosResponseHandler);

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->ReleaseAllDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnReleaseFileResponseHandler);
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnDownloadFileResponseHandler);
		CatalogPC->UploadFileResponseDelegate.AddUniqueDynamic(this, &UMainToolBarWidget::OnUploadFileResponseHandler);
	}
#endif
}

void UMainToolBarWidget::OnReleaseInfosResponseHandler(const FString& UUID, bool bSuccess, const TArray<FRefDirectoryData>& Data)
{
	if (UUID.Equals(ReleaseUUID))
	{
		ReleaseUUID.Reset();
		if (bSuccess)
		{
			UReleaseWidget::GetInstance()->Init(Data);
			UReleaseWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
		}
	}
}

void UMainToolBarWidget::UpdateBorderColor(const FLinearColor& InColor, UBorder* InBorder)
{
	if (IS_OBJECT_PTR_VALID(InBorder))
	{
		InBorder->SetBrushColor(InColor);
	}
}

void UMainToolBarWidget::DownloadServerDatabase()
{
	TArray<FString> Paths;
	Paths.Add(ServerDatabasePath);
#ifdef USE_REF_LOCAL_FILE

	ReleaseDownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(Paths);

#else
	ReleaseDownloadUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(Paths);
#endif
}

void UMainToolBarWidget::ConvertDatabase()
{
	FReleaseLocalDatabaseLibrary Release;
	const FString SourcePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("Cache/server_cache.db"));
	const FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + TEXT("Cache/local_data.db"));
	Release.ReleaseLocalDatabase(SourcePath, TargetPath);
}

void UMainToolBarWidget::UploadDatabase()
{
#ifdef USE_REF_LOCAL_FILE

#else
	ReleaseUploadUUID = ACatalogPlayerController::Get()->UploadFileRequest(LocalDataPath);
#endif
}

void UMainToolBarWidget::ReleaseLog()
{
	UE_LOG(LogTemp, Warning, TEXT("ReleaseLog"));
}

void UMainToolBarWidget::ReleaseThread()
{
	SetReleasePg(0.5, 0);
	FGraphEventRef ConvertDatabaseEvent;
	UMultiThreadFunctionLibrary::CallThreadBindUFunction(ConvertDatabaseEvent, NULL, ENamedThreads::AnyThread, this, FName(TEXT("ConvertDatabase")));
	FGraphEventRef UploadDatabaseEvent;
	UMultiThreadFunctionLibrary::CallThreadBindUFunction(UploadDatabaseEvent, ConvertDatabaseEvent, ENamedThreads::AnyThread, this, FName(TEXT("UploadDatabase")));
}

UWidget* UMainToolBarWidget::GetLogMAContent()
{
	if (!IS_OBJECT_PTR_VALID(ReleaseLogWidget))
	{
		ReleaseLogWidget = UReleaseLogWidget::Create();
	}
	return ReleaseLogWidget;
}

UWidget* UMainToolBarWidget::GetMergeLogMAContent()
{
	if (!IS_OBJECT_PTR_VALID(MergeLogWidget))
	{
		MergeLogWidget = UReleaseLogWidget::Create();
	}
	return MergeLogWidget;
}

void UMainToolBarWidget::OnTextChangedEdtSearch(const FText& Text)
{
	/*if (Text.IsEmpty())
	{
		return;
	}*/
	if (IS_OBJECT_PTR_VALID(EdtSearch))
	{
		MainSearchDelegate.ExecuteIfBound(EdtSearch->GetText().ToString());
	}
}

void UMainToolBarWidget::OnTextCommittedEdtSearch(const FText& Text, ETextCommit::Type CommitMethod)
{
	/*if (IS_OBJECT_PTR_VALID(EdtSearch))
	{
		MainSearchDelegate.ExecuteIfBound(EdtSearch->GetText().ToString());
	}*/
}

void UMainToolBarWidget::OnClickedBtnSearch()
{
	if (IS_OBJECT_PTR_VALID(EdtSearch))
	{
		MainSearchDelegate.ExecuteIfBound(EdtSearch->GetText().ToString());
	}
}

void UMainToolBarWidget::OnClickedBtnUp()
{
	MainToolBarEditDelegate.ExecuteIfBound((int32)EMainToolBarType::Up);
}

void UMainToolBarWidget::OnClickedBtnDown()
{
	MainToolBarEditDelegate.ExecuteIfBound((int32)EMainToolBarType::Down);
}

void UMainToolBarWidget::OnClickedBtnGlobalParams()
{
	UE_LOG(LogTemp, Log, TEXT("MainToolBarWidget---click global params btn"));
	MainToolBarEditDelegate.ExecuteIfBound((int32)EMainToolBarType::GlobalParams);
}

void UMainToolBarWidget::OnHoveredBtnGlobalParams()
{
	UpdateBorderColor(MainToolBarHover, BorGlobalParam);
}

void UMainToolBarWidget::OnUnHoveredBtnGlobalParams()
{
	UpdateBorderColor(MainToolBarUnHover, BorGlobalParam);
}

void UMainToolBarWidget::OnClickedBtnSetting()
{
	UE_LOG(LogTemp, Log, TEXT("MainToolBarWidget---click user setting btn"));
	MainToolBarEditDelegate.ExecuteIfBound((int32)EMainToolBarType::UserSetting);
}

void UMainToolBarWidget::OnClickedBtnExit()
{
	MainToolBarEditDelegate.ExecuteIfBound((int32)EMainToolBarType::Exit);
}

void UMainToolBarWidget::OnHoveredBtnExit()
{
	UpdateBorderColor(MainToolBarHover, BorExit);
}

void UMainToolBarWidget::OnUnHoveredBtnExit()
{
	UpdateBorderColor(MainToolBarUnHover, BorExit);
}

void UMainToolBarWidget::OnHoveredBtnReleaseLog()
{
	MaReleaseLog->Open(true);

	//test
	ReleaseLogWidget->InitLogList();
}

void UMainToolBarWidget::OnUnHoveredBtnReleaseLog()
{
	MaReleaseLog->Close();
}

void UMainToolBarWidget::OnHoveredBtnMergeLog()
{
	MaMergeLog->Open(true);
	MergeLogWidget->InitMergeLogList();
}

void UMainToolBarWidget::OnUnHoveredBtnMergeLog()
{
	MaMergeLog->Close();
}

void UMainToolBarWidget::OnClickedBtnRelease()
{
#ifdef USE_REF_LOCAL_FILE

	UReleaseWidget::GetInstance()->UpdateContent();
	UReleaseWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	//ReleaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendUserReleaseInfosRequest(0, 10000);

#else

	if (ACatalogPlayerController::Get()->GetReleaseLock())
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Releasing")).ToString());
		return;
	}
	else
	{
		if (!UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Release Confirm")).ToString()))
		{
			return;
		}
	}
	ACatalogPlayerController::Get()->SetReleaseLock(true);
	MergeLogOrProcess(false);
	SetReleasePg(0.1, 0);
	DownloadServerDatabase();
#endif
}

void UMainToolBarWidget::OnClickedBtnDownload()
{
	if (ACatalogPlayerController::Get()->GetReleaseLock())
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Releasing")).ToString());
		return;
	}
	TArray<FString> Paths;
	Paths.Add(ServerDatabasePath);
	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(false);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.1f);
	}
#ifdef USE_REF_LOCAL_FILE

	DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(Paths);

#else
	DownloadUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(Paths);
#endif
}

void UMainToolBarWidget::OnReleaseGetResponseHandler(const FString& InUUID)
{
	if (ReleaseLogUUID.Equals(InUUID))
	{
		SetReleasePg(1.f, 1);
	}
}

void UMainToolBarWidget::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	const FString ProjectDir = FPaths::ProjectContentDir();
	const FString AbsoluteLocalPath = FPaths::ConvertRelativePathToFull(ProjectDir, FLocalDatabaseOperatorLibrary::GetLocalDataBasePath());
	const FString AbsoluteServerPath = FPaths::ConvertRelativePathToFull(ProjectDir, ServerDatabasePath);
	if (DownloadUUID.Equals(UUID))
	{
		DownloadUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		TArray<FString> DownloadPaths;
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(AbsoluteServerPath);
		const FString SelectFile = TEXT("select * from file_image");
		const FString SelectParam = TEXT("select * from param_image");
		const FString SelectOther = TEXT("select * from other_file");
		const FString SelectStyle = TEXT("select * from style_image");

		TArray<FDownloadFileData> ServerDatas;
		TArray<FDownloadFileData> CDatas;

		TArray<FDownloadFileData> STemp;
		TArray<FDownloadFileData> CTemp;

		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SelectFile, ServerDatas);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectFile, CDatas);

		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SelectParam, STemp);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectParam, CTemp);
		ServerDatas.Append(STemp);
		CDatas.Append(CTemp);

		STemp.Empty();
		CTemp.Empty();

		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SelectOther, STemp);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectOther, CTemp);
		ServerDatas.Append(STemp);
		CDatas.Append(CTemp);

		STemp.Empty();
		CTemp.Empty();

		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FDownloadFileData>(SelectStyle, STemp);
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL<FDownloadFileData>(SelectStyle, CTemp);
		ServerDatas.Append(STemp);
		CDatas.Append(CTemp);

		for (auto& siter : ServerDatas)
		{
			if (!CDatas.Contains(siter))
			{
				if (siter.md5.Equals(EmptyImage))
				{
					continue;
				}
				DownloadPaths.AddUnique(siter.path);
				continue;
			}

			int32 Index = CDatas.IndexOfByPredicate(
				[siter](const FDownloadFileData& CData)->bool { return siter.path == CData.path; }
			);
			if (Index != INDEX_NONE)
			{
				if (!siter.md5.IsEmpty())
				{
					bool NeedDownload = true;
					if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(ProjectDir, siter.path)))
					{
						if (siter.md5.Equals(CDatas[Index].md5, ESearchCase::Type::IgnoreCase))
						{
							NeedDownload = false;
						}
					}
					else
					{
						if (siter.md5.Equals(EmptyImage))
						{
							NeedDownload = false;
						}
					}
					if (NeedDownload)
						DownloadPaths.AddUnique(siter.path);
				}
				continue;
			}
			else
			{

			}
		}

		//下载丢失头图
		TArray<FEnumParameterTableData> AllEnumDatas;
		TArray<FEnumParameterTableData> TempEnumDatas;



		FString  SelectDecorateEnmu = FString::Printf(TEXT("select * from decorate_param_enum"));
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FEnumParameterTableData>(SelectDecorateEnmu, TempEnumDatas);
		AllEnumDatas.Append(TempEnumDatas);
		TempEnumDatas.Empty();

		FString  SelectFileEnmu = FString::Printf(TEXT("select * from file_param_enum"));
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FEnumParameterTableData>(SelectFileEnmu, TempEnumDatas);
		AllEnumDatas.Append(TempEnumDatas);
		TempEnumDatas.Empty();

		FString  SelectFolderEnmu = FString::Printf(TEXT("select * from folder_param_enum"));
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FEnumParameterTableData>(SelectFolderEnmu, TempEnumDatas);
		AllEnumDatas.Append(TempEnumDatas);
		TempEnumDatas.Empty();

		FString  SelectGlobalEnmu = FString::Printf(TEXT("select * from global_param_enum"));
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FEnumParameterTableData>(SelectGlobalEnmu, TempEnumDatas);
		AllEnumDatas.Append(TempEnumDatas);
		TempEnumDatas.Empty();

		FString  SelectMultiEnmu = FString::Printf(TEXT("select * from multi_param_enum"));
		FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL<FEnumParameterTableData>(SelectMultiEnmu, TempEnumDatas);
		AllEnumDatas.Append(TempEnumDatas);
		TempEnumDatas.Empty();

		for (auto& EnumIter : AllEnumDatas)
		{
			if (!EnumIter.image_for_display.IsEmpty() && !FPaths::FileExists(FPaths::ConvertRelativePathToFull(ProjectDir, EnumIter.image_for_display)))
			{
				DownloadPaths.AddUnique(EnumIter.image_for_display);
			}
		}


		if (DownloadPaths.Num() > 0)
		{
			DownloadFileUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(DownloadPaths);
		}
		else
		{
			MergeProcessWidget->SetPercent(0.2f);
			ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
			LocalDBSubsystem->CloseDatabase();
			LocalDBSubsystem->CloseServerDatabase();
			if (FPaths::FileExists(AbsoluteServerPath))
			{
				FCatalogFunctionLibrary::DeleteFile(AbsoluteLocalPath);
				FCatalogFunctionLibrary::CopyFileTo(AbsoluteServerPath, AbsoluteLocalPath);
			}
			LocalDBSubsystem->OpenDatabase(AbsoluteLocalPath);
			//FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString UserName = UCatalogNetworkSubsystem::GetInstance()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString Remark = FText::FromStringTable(FName("PosSt"), TEXT("download file")).ToString();
			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();

			//MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Remark, TEXT(""), TEXT(""), UserName, Operation);
			UploadSuccess(true);
		}
	}
	else if (DownloadFileUUID.Equals(UUID))
	{
		DownloadFileUUID.Empty();
		if (false == OutRes)
		{
			UE_LOG(LogTemp, Error, TEXT("UMainToolBarWidget::OnDownloadFileResponseHandler Failed Download"));
			//UploadSuccess(false);
			//return;
		}
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->CloseDatabase();
		LocalDBSubsystem->CloseServerDatabase();
		if (FPaths::FileExists(AbsoluteServerPath))
		{
			FCatalogFunctionLibrary::DeleteFile(AbsoluteLocalPath);
			FCatalogFunctionLibrary::CopyFileTo(AbsoluteServerPath, AbsoluteLocalPath);
		}
		//FCatalogFunctionLibrary::DeleteFile(FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));
		//FCatalogFunctionLibrary::CopyFileTo(FPaths::Combine(FPaths::ProjectContentDir(), ServerDatabasePath), FPaths::Combine(FPaths::ProjectContentDir(), FLocalDatabaseOperatorLibrary::GetLocalDataBasePath()));

		LocalDBSubsystem->OpenDatabase(AbsoluteLocalPath);
		//FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
		FString UserName = UCatalogNetworkSubsystem::GetInstance()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
		FString Remark = FText::FromStringTable(FName("PosSt"), TEXT("download file")).ToString();
		FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();
		//MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Remark, TEXT(""), TEXT(""), UserName, Operation);
		UploadSuccess(true);

	}
	else if (ReleaseDownloadUUID.Equals(UUID))
	{
		if (OutRes)
		{
			SetReleasePg(0.3, 0);
			ReleaseThread();
		}
		else if (!OutRes)
		{
			SetReleasePg(0.3, 2);
			PreReleaseOver();
		}
	}
}

void UMainToolBarWidget::UploadSuccess(bool IsTrue)
{
	if (IsTrue)
	{
		MergeProcessWidget->SetPercent(1.f);
	}
	else
	{
		MergeProcessWidget->SetSuccess(false);
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->CloseServerDatabase();
	}
	FLatentActionInfo LatentActionInfo;
	LatentActionInfo.CallbackTarget = this;
	LatentActionInfo.ExecutionFunction = TEXT("RemoveProcess");
	LatentActionInfo.Linkage = 1;
	LatentActionInfo.UUID = 100;
	UKismetSystemLibrary::Delay(this, 3.f, LatentActionInfo);
}

void UMainToolBarWidget::RemoveProcess()
{
	if (MergeProcessWidget)
	{
		MergeProcessWidget->RemoveFromParent();
	}
}

void UMainToolBarWidget::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (OutFilePath.Equals(ReleaseUploadUUID) && OutRes)
	{
		SetReleasePg(0.9, 0);
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + LocalDataPath);
		FString LocalMD5 = TEXT("");
		int64 FileSize = 0;
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
		UE_LOG(LogTemp, Warning, TEXT("LocalMD5 %s"), *LocalMD5);
#ifdef USE_REF_LOCAL_FILE
		ReleaseUUID = UCatalogNetworkSubsystem::GetInstance()->ReleaseRequest(LocalMD5);
#else
		ReleaseUUID = ACatalogPlayerController::Get()->ReleaseRequest(LocalMD5);
#endif
	}
	else if (!OutRes)
	{
		SetReleasePg(0.9, 2);
		PreReleaseOver();
	}
}

void UMainToolBarWidget::OnReleaseFileResponseHandler(const FString& UUID, bool bSuccess)
{
	if (UUID.Equals(ReleaseUUID) && bSuccess)
	{
		SetReleasePg(1.f, 1);
		PreReleaseOver();
	}
	else if (UUID.Equals(ReleaseUUID) && !bSuccess)
	{
		SetReleasePg(1.f, 2);
		PreReleaseOver();
	}
}

void UMainToolBarWidget::PreReleaseOver()
{
	FLatentActionInfo LatentActionInfo;
	LatentActionInfo.CallbackTarget = this;
	LatentActionInfo.ExecutionFunction = TEXT("ReleaseOver");
	LatentActionInfo.Linkage = 1;
	LatentActionInfo.UUID = 100;
	UKismetSystemLibrary::Delay(this, 3.f, LatentActionInfo);
}

void UMainToolBarWidget::ReleaseOver()
{
	ACatalogPlayerController::Get()->SetReleaseLock(false);
	MergeLogOrProcess(true);
}

void UMainToolBarWidget::OnClickedBtnStyle()
{
	MainToolBarEditDelegate.ExecuteIfBound(static_cast<int32>(EMainToolBarType::Style));
}

void UMainToolBarWidget::OnHoveredBtnStyle()
{
	UpdateBorderColor(MainToolBarHover, BorStyle);
}

void UMainToolBarWidget::OnUnHoveredBtnStyle()
{
	UpdateBorderColor(MainToolBarUnHover, BorStyle);
}

void UMainToolBarWidget::OnClickedBtnSet()
{
	MainToolBarEditDelegate.ExecuteIfBound(static_cast<int32>(EMainToolBarType::Set));
}

void UMainToolBarWidget::OnHoveredBtnSet()
{
	UpdateBorderColor(MainToolBarHover, BorSet);
}

void UMainToolBarWidget::OnUnHoveredBtnSet()
{
	UpdateBorderColor(MainToolBarUnHover, BorSet);
}

void UMainToolBarWidget::OnClickedBtnDirectory()
{
	UFrontShowWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
	UFrontShowWidget::GetInstance()->SwitchShowType(EFrontDirectoryDataType::E_Catalog);
	UFrontShowWidget::GetInstance()->BindInnerDelegate();
}

#undef ServerDatabasePath
#undef LocalDataPath
#undef EmptyImage