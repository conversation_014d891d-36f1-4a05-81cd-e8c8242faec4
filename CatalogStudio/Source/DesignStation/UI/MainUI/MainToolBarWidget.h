// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ReleaseLogWidget.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DesignStation/SQLite/UserRelated/UserInfoTableOperatorLibrary.h"
#include "MainToolBarWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMainToolBarType : uint8
{
	GlobalParams = 0,
	Style,
	Set,
	Up,
	Down,
	UserSetting,
	Exit
};

UENUM(BlueprintType)
enum class EMainToolBarState : uint8
{
	UpEnable = 0,
	DownEnable,
	AllEnable,
	NoneEnable
};

class UTextBlock;
class UButton;
class UEditableText;
class UImage;
class UBorder;
class UMenuAnchor;
class UMergeProcessWidget;
class UProgressBar;

DECLARE_DYNAMIC_DELEGATE_OneParam(FMainToolBarDelegate, const int32&, EditType);
DECLARE_DYNAMIC_DELEGATE_OneParam(FMainSearchDelegate, const FString&, InString);

UCLASS()
class DESIGNSTATION_API UMainToolBarWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateToolBarInfo(const FUserInfoTableData& UserInfoData);
	void UpdateToolBarState(const int32& ActionType);
	void ResetSearchWidget();

	static UMainToolBarWidget* Create();


private:
	void BindDelegate();

	UFUNCTION()
	void OnReleaseInfosResponseHandler(const FString& UUID, bool bSuccess, const TArray<FRefDirectoryData>& Data);

	void UpdateBorderColor(const FLinearColor& InColor, UBorder* InBorder);

	UFUNCTION()
	void DownloadServerDatabase();
	UFUNCTION()
	void ConvertDatabase();
	UFUNCTION()
	void UploadDatabase();
	UFUNCTION()
	void ReleaseLog ();
	UFUNCTION()
	void ReleaseThread();
public:
	FMainToolBarDelegate MainToolBarEditDelegate;
	FMainSearchDelegate MainSearchDelegate;

private:
	static FString MainToolBarWidgetPath;

	FString ReleaseSearchLogUUID;

	FString ReleaseUUID;

	FString DownloadUUID;
	FString DownloadFileUUID;
	FString MergeLogUUID;

	FString ReleaseDownloadUUID;
	FString ReleaseUploadUUID;

	FString ReleaseLogUUID;

	bool bReleaseLock;
public:
	UFUNCTION()
		UWidget * GetLogMAContent();
	UFUNCTION()
		UWidget* GetMergeLogMAContent();
protected:
	UFUNCTION()
		void OnTextChangedEdtSearch(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtSearch(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnSearch();

	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

	UFUNCTION()
		void OnClickedBtnGlobalParams();
	UFUNCTION()
		void OnHoveredBtnGlobalParams();
	UFUNCTION()
		void OnUnHoveredBtnGlobalParams();

	UFUNCTION()
		void OnClickedBtnStyle();
	UFUNCTION()
		void OnHoveredBtnStyle();
	UFUNCTION()
		void OnUnHoveredBtnStyle();

	UFUNCTION()
		void OnClickedBtnSet();
	UFUNCTION()
		void OnHoveredBtnSet();
	UFUNCTION()
		void OnUnHoveredBtnSet();

	UFUNCTION()
		void OnClickedBtnDirectory();

	UFUNCTION()
		void OnClickedBtnSetting();

	UFUNCTION()
		void OnClickedBtnExit();
	UFUNCTION()
		void OnHoveredBtnExit();
	UFUNCTION()
		void OnUnHoveredBtnExit();

	UFUNCTION()
		void OnHoveredBtnReleaseLog();

	UFUNCTION()
		void OnUnHoveredBtnReleaseLog();

	UFUNCTION()
		void OnHoveredBtnMergeLog();

	UFUNCTION()
		void OnUnHoveredBtnMergeLog();

	UFUNCTION()
		void OnClickedBtnRelease();

	UFUNCTION()
		void OnClickedBtnDownload();
	UFUNCTION()
		void OnReleaseGetResponseHandler(const FString& InUUID);

	UFUNCTION(BlueprintImplementableEvent)
		void MergeLogOrProcess(bool bMergLog);
	UFUNCTION(BlueprintImplementableEvent)
		void SetReleasePg(const float& InPec, int32 InState);

	UFUNCTION()
		void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	void UploadSuccess(bool IsTrue);
	UFUNCTION()
		void RemoveProcess();
	UFUNCTION()
		void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);
	UFUNCTION()
		void OnReleaseFileResponseHandler(const FString& UUID,bool bSuccess);
	UFUNCTION()
		void	PreReleaseOver();
	UFUNCTION()
		void ReleaseOver();
private:
	UPROPERTY()
		UEditableText* EdtSearch;
	UPROPERTY()
		UButton* BtnSearch;

	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;

	UPROPERTY()
		UBorder* BorGlobalParam;
	UPROPERTY()
		UButton* BtnGlobalParams;
	UPROPERTY()
		UBorder* BorStyle;
	UPROPERTY()
		UButton* BtnStyle;
	UPROPERTY()
		UBorder* BorSet;
	UPROPERTY()
		UButton* BtnSet;

	UPROPERTY()
		UButton* BtnDirectory;

	UPROPERTY()
		UImage* ImgUser;
	UPROPERTY()
		UTextBlock* TxtUserName;
	UPROPERTY()
		UButton* BtnUserSetting;

	UPROPERTY()
		UBorder* BorExit;
	UPROPERTY()
		UButton* BtnExit;

	UPROPERTY()
		UButton* BtnRelease;
	UPROPERTY()
		UButton* BtnDownload;

	UPROPERTY()
		UButton* BtnReleaseLog;
	UPROPERTY()
		UMenuAnchor*	MaReleaseLog;	
	UPROPERTY()
		UReleaseLogWidget*	ReleaseLogWidget;

	UPROPERTY()
		UButton* BtnMergeLog;
	UPROPERTY()
		UMenuAnchor* MaMergeLog;
	UPROPERTY()
		UReleaseLogWidget* MergeLogWidget;
	UPROPERTY()
		UMergeProcessWidget* MergeProcessWidget;
};
