// Fill out your copyright notice in the Description page of Project Settings.

#include "ReleaseLogWidget.h"

#include "Components/Button.h"
#include "Components/EditableTextBox.h"
#include "Components/ScrollBox.h"
#include "Components/SizeBox.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UReleaseLogWidget::BpPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/ReleaseLogUI.ReleaseLogUI_C'");

bool UReleaseLogWidget::Initialize()
{
	if (!Super::Initialize())
	{

		return false;
	}
	
	BIND_PARAM_CPP_TO_UMG(SbTitle, Sb_Title);
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON>, Scb_Log);
	BIND_PARAM_CPP_TO_UMG(BtnLeft, Btn_Left);
	BIND_PARAM_CPP_TO_UMG(BtnRight, Btn_Right);
	BIND_PARAM_CPP_TO_UMG(EdtbPage, Edtb_Page);
	BIND_PARAM_CPP_TO_UMG(TxtPageNum, Txt_PageNum);
	BIND_PARAM_CPP_TO_UMG(VbLog, Vb_Log);

	BIND_WIDGET_FUNCTION(BtnLeft, OnClicked, UReleaseLogWidget::OnClickedBtnLeft);
	BIND_WIDGET_FUNCTION(BtnRight, OnClicked, UReleaseLogWidget::OnClickedBtnRight);
	BIND_WIDGET_FUNCTION(EdtbPage, OnTextCommitted, UReleaseLogWidget::OnCommittedEdtbPage);

	BindDelegate();

	return true;
}

UReleaseLogWidget * UReleaseLogWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UReleaseLogWidget>(UReleaseLogWidget::BpPath);
}

void UReleaseLogWidget::InitLogList()
{
	bReleaseOrMerge = true;
	ThreeOrThree(bReleaseOrMerge);
	SbTitle->ClearChildren();
	//ScbLog->ClearChildren();
	VbLog->ClearChildren();
	FReleaseLog Title;
	Title.StrReleaser = FText::FromStringTable(FName("PosSt"), TEXT("releaser")).ToString();
	Title.StrDate = FText::FromStringTable(FName("PosSt"), TEXT("date")).ToString();
	Title.StrState = FText::FromStringTable(FName("PosSt"), TEXT("release content")).ToString();
	UThreeTextWidget* TitleWidget = UThreeTextWidget::Create();
	TitleWidget->UpdateLogText(Title);
	SbTitle->AddChild(TitleWidget);

	RefreshCurrentPage(1);
}

void UReleaseLogWidget::InitMergeLogList()
{
	bReleaseOrMerge = false;
	ThreeOrThree(bReleaseOrMerge);
	SbTitle->ClearChildren();
	//ScbLog->ClearChildren();
	VbLog->ClearChildren();
	FString Name = FText::FromStringTable(FName("PosSt"), TEXT("account")).ToString();
	FString Date = FText::FromStringTable(FName("PosSt"), TEXT("date")).ToString();
	FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("operation")).ToString();
	FString Remark = FText::FromStringTable(FName("PosSt"), TEXT("merge content")).ToString();
	UFourTextWidget* TitleWidget = UFourTextWidget::Create();
	TitleWidget->UpdateLogText(Name, Date, Operation, Remark);
	SbTitle->AddChild(TitleWidget);

	RefreshMergeCurrentPage(1);
}

void UReleaseLogWidget::BindDelegate()
{
#ifdef USE_REF_LOCAL_FILE

	UCatalogNetworkSubsystem::GetInstance()->ReleaseResponseDelegate.AddUniqueDynamic(this, &UReleaseLogWidget::OnSearchReleaseLogComplete);
	UCatalogNetworkSubsystem::GetInstance()->SearchMergeLogResponseDelegate.AddUniqueDynamic(this, &UReleaseLogWidget::OnSearchMergeLogComplete);

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->ReleaseResponseDelegate.AddUniqueDynamic(this, &UReleaseLogWidget::OnSearchReleaseLogComplete);
		CatalogPC->SearchMergeLogResponseDelegate.AddUniqueDynamic(this, &UReleaseLogWidget::OnSearchMergeLogComplete);

	}
#endif
}

void UReleaseLogWidget::AddReleaseLogToList(const FReleaseLog & InReleaseLog)
{
}

void UReleaseLogWidget::OnClickedBtnLeft()
{
	if (CurrentPage > 1)
	{
		if (bReleaseOrMerge)
			RefreshCurrentPage(--CurrentPage);
		else
			RefreshMergeCurrentPage(--CurrentPage);
	}

}

void UReleaseLogWidget::OnClickedBtnRight()
{
	if (CurrentPage < PageNum)
	{
		if (bReleaseOrMerge)
			RefreshCurrentPage(++CurrentPage);
		else
			RefreshMergeCurrentPage(++CurrentPage);
	}
}

void UReleaseLogWidget::OnCommittedEdtbPage(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.IsNumeric() && FCString::Atoi(*(Text.ToString()))<=PageNum &&FCString::Atoi(*(Text.ToString()))>=1)
	{
		if (bReleaseOrMerge)
			RefreshCurrentPage(FCString::Atoi(*(Text.ToString())));
		else
			RefreshMergeCurrentPage(FCString::Atoi(*(Text.ToString())));
	}
	else
	{
		EdtbPage->SetText(FText::FromString(FString::FromInt(CurrentPage)));
	}
}

void UReleaseLogWidget::RefreshCurrentPage(const int32 & InCurrentPage)
{
#ifdef USE_REF_LOCAL_FILE
	ReleaseSearchLogUUID = UCatalogNetworkSubsystem::GetInstance()->SendQueryReleaseLogRequest(InCurrentPage, 15);
#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	ReleaseSearchLogUUID = CatalogPC->SendQueryReleaseLogRequest(InCurrentPage, 15);
#endif
}

void UReleaseLogWidget::RefreshMergeCurrentPage(const int32& InCurrentPage)
{
	
#ifdef USE_REF_LOCAL_FILE

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	ReleaseSearchMergeLogUUID = CatalogPC->SearchMergeLogRequset(InCurrentPage, 15);
#endif
}

void UReleaseLogWidget::OnSearchReleaseLogComplete(const FString & InUUID, const FReleaseLogDataPage& InData)
{
	if (InUUID.Equals(ReleaseSearchLogUUID))
	{
		//ScbLog->ClearChildren();
		VbLog->ClearChildren();
		for (auto& iter : InData.list)
		{
			UThreeTextWidget* TextWidget = UThreeTextWidget::Create();
			TextWidget->UpdateLogData(iter);
			//ScbLog->AddChild(TextWidget);
			VbLog->AddChild(TextWidget);
		}
		CurrentPage = InData.page;
		PageNum = InData.pageTotal;
		if (CurrentPage <= 0)
			CurrentPage = 1;
		if (PageNum <= 0)
			PageNum = 1;
		EdtbPage->SetText(FText::FromString(FString::FromInt(CurrentPage)));
		TxtPageNum->SetText(FText::FromString(FString::FromInt(PageNum)));
	}
}

void UReleaseLogWidget::OnSearchMergeLogComplete(const FString& InUUID, const FMergePageData& InData)
{
	if (InUUID.Equals(ReleaseSearchMergeLogUUID))
	{
		//ScbLog->ClearChildren();
		VbLog->ClearChildren();
		for (auto& iter : InData.list)
		{
			UFourTextWidget* TextWidget = UFourTextWidget::Create();
			TextWidget->UpdateLogText(iter);
			//ScbLog->AddChild(TextWidget);
			VbLog->AddChild(TextWidget);
		}
		
		CurrentPage = InData.page;
		PageNum = InData.pageTotal;
		if (CurrentPage <= 0)
			CurrentPage = 1;
		if (PageNum <= 0)
			PageNum = 1;
		EdtbPage->SetText(FText::FromString(FString::FromInt(CurrentPage)));
		TxtPageNum->SetText(FText::FromString(FString::FromInt(PageNum)));
	}
}


