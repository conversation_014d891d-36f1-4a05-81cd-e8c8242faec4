// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ThreeTextWidget.h"
#include "FourTextWidget.h"
#include "ReleaseLogWidget.generated.h"

/**
 * 
 */

class USizeBox;
class UScrollBox;
class UButton;
class UEditableTextBox;
class UTextBlock;
class UVerticalBox;
UCLASS()
class DESIGNSTATION_API UReleaseLogWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	bool Initialize() override;

	static UReleaseLogWidget*	Create();

private:
	static FString BpPath;

public:

	void InitLogList();
	void InitMergeLogList();

private:
	TArray<UReleaseLogWidget*> LogList;

	FString ReleaseSearchLogUUID;
	FString ReleaseSearchMergeLogUUID;

	int32 CurrentPage;

	int32 PageNum;

private:

	void BindDelegate();

	void AddReleaseLogToList(const FReleaseLog & InReleaseLog);
private:
	UFUNCTION()
		void OnClickedBtnLeft();
	UFUNCTION()
		void OnClickedBtnRight();
	UFUNCTION()
		void OnCommittedEdtbPage(const FText& Text, ETextCommit::Type CommitMethod);

	void RefreshCurrentPage(const int32& InCurrentPage);
	void RefreshMergeCurrentPage(const int32& InCurrentPage);

	UFUNCTION()
		void OnSearchReleaseLogComplete(const FString& InUUID,const FReleaseLogDataPage& InData);
	UFUNCTION()
		void OnSearchMergeLogComplete(const FString& InUUID, const FMergePageData& InData);

private:
	UPROPERTY()
		USizeBox* SbTitle;
	UPROPERTY()
		UScrollBox*	ScbLog;
	UPROPERTY()
		UButton*	BtnLeft;
	UPROPERTY()
		UButton*	BtnRight;
	UPROPERTY()
		UEditableTextBox*	EdtbPage;
	UPROPERTY()
		UTextBlock*	TxtPageNum;
	UPROPERTY()
		UVerticalBox* VbLog;
	bool bReleaseOrMerge;

public:
	UFUNCTION(BlueprintImplementableEvent )
		void ThreeOrThree(bool IsTrue);
};
