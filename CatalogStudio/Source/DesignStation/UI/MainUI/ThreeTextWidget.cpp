// Fill out your copyright notice in the Description page of Project Settings.

#include "ThreeTextWidget.h"

#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Release/ReleaseDetailWidget.h"

FString UThreeTextWidget::BpPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/ThreeTextUI.ThreeTextUI_C'");

bool UThreeTextWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(TxtReleaser, Txt_Releaser);
	BIND_PARAM_CPP_TO_UMG(TxtDate, Txt_Date);
	BIND_PARAM_CPP_TO_UMG(TxtState, Txt_State);

	return true;
}

UThreeTextWidget * UThreeTextWidget::Create()
{
	UE_LOG(LogTemp, Warning, TEXT("UThreeTextWidget::Create()"));
	return UUIFunctionLibrary::UIWidgetCreate<UThreeTextWidget>(UThreeTextWidget::BpPath);
}

void UThreeTextWidget::UpdateLogText(const FReleaseLog & InReleaseLog)
{
	TxtReleaser->SetText(FText::FromString(InReleaseLog.StrReleaser));
	TxtDate->SetText(FText::FromString(InReleaseLog.StrDate));
	TxtState->SetText(FText::FromString(InReleaseLog.StrState));
}

void UThreeTextWidget::UpdateLogData(const FReleaseLogData& InReleaseLog)
{
	Id = InReleaseLog.id;
	FReleaseLog ReleaseLog;
	ReleaseLog.StrReleaser = InReleaseLog.releaseName;
	//TArray<int32> OutDate;
	//UJsonUtilitiesLibrary::ConvertStringTimeToTimeArray(InReleaseLog.releaseTime, OutDate);
	//FString StrDate;
	//if (OutDate.Num() >= 5)
	//{
	//	StrDate = FString::FromInt(OutDate[0])
	//		+ "/" + FString::FromInt(OutDate[1])
	//		+ "/" + FString::FromInt(OutDate[2])
	//		+ " " + FString::FromInt(OutDate[3])
	//		+ "/" + FString::FromInt(OutDate[4]);
	//}

	ReleaseLog.StrDate = InReleaseLog.releaseTime;
	FString A = FString("-");
	FString B = FString("T");
	FString C = FString("/");

	//ReleaseLog.StrDate = ReleaseLog.StrDate.Replace(*A, *C);
	ReleaseLog.StrDate = ReleaseLog.StrDate.Replace(*B, *C);
	ReleaseLog.StrDate = ReleaseLog.StrDate.Left(16);
	if (InReleaseLog.releaseType == 0)
	{
		ReleaseLog.StrState = InReleaseLog.releaseResult == 0 ? 
			FText::FromStringTable(FName("PosSt"), TEXT("complete release")).ToString() : 
			FText::FromStringTable(FName("PosSt"), TEXT("incomplete release")).ToString();
	}
	else if(InReleaseLog.releaseType == 1)
	{
			ReleaseLog.StrState = InReleaseLog.releaseResult == 0 ? 
			FText::FromStringTable(FName("PosSt"), TEXT("style release ok")).ToString() : 
			FText::FromStringTable(FName("PosSt"), TEXT("style release error")).ToString();
	}
	else if(InReleaseLog.releaseType == 2)
	{
		ReleaseLog.StrState = InReleaseLog.releaseResult == 0 ? 
			FText::FromStringTable(FName("PosSt"), TEXT("GlobalParam release ok")).ToString() : 
			FText::FromStringTable(FName("PosSt"), TEXT("GlobalParam release error")).ToString();
	}
	//ReleaseLog.StrState = FText::FromStringTable(FName("PosSt"), TEXT("complete release")).ToString();
	UpdateLogText(ReleaseLog);
}

void UThreeTextWidget::OnShowDetail()
{
	UReleaseDetailWidget::GetInstance()->InitDetail(Id);
}
