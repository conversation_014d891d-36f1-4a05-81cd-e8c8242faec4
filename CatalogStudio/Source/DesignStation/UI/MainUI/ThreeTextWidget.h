// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Release/ReleaseLogData.h"
#include "ThreeTextWidget.generated.h"

/**
 * 
 */

class UTextBlock;

USTRUCT()
struct FReleaseLog
{
	GENERATED_USTRUCT_BODY()

public:
	FString StrReleaser;
	FString StrDate;
	FString StrState;

public:
	FReleaseLog() :StrReleaser(TEXT("")), StrDate(TEXT("")), StrState(TEXT("")) {}

};


UCLASS()
class DESIGNSTATION_API UThreeTextWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	bool Initialize() override;

	static UThreeTextWidget* Create();

public:
	void UpdateLogText(const FReleaseLog & InReleaseLog);

	void UpdateLogData(const FReleaseLogData& InReleaseLog);

	UFUNCTION(BlueprintCallable, Category = "ThreeText")
	void OnShowDetail();

private:
	static FString BpPath;

	UPROPERTY()
	int32 Id;
public:
	UPROPERTY()
		UTextBlock* TxtReleaser;
	UPROPERTY()
		UTextBlock* TxtDate;
	UPROPERTY()
		UTextBlock* TxtState;
};
