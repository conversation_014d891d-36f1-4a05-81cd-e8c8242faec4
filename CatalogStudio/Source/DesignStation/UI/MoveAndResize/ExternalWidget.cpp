// Fill out your copyright notice in the Description page of Project Settings.

#include "ExternalWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"

FString UExternalWidget::ExternalWidgetPath = TEXT("WidgetBlueprint'/Game/UI/MoveAndResize/ExternalUI.ExternalUI_C'");
UExternalWidget* UExternalWidget::ExternalWidgetInstance = nullptr;

extern const int PopUIZOrder;
const float SaftyBoundarySize = 3.0f;

bool UExternalWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(CPExternal, CP_Content);

	InitMoveAndResizeWidget();
	IsMouseButtonDown = false;
	IsDrag = false;
	IsResize = false;
	LastMousePosition = FVector2D::ZeroVector;

	return true;
}

void UExternalWidget::AddContentWidget(UUserWidget * InWidget)
{
	if (MoveAndResizeWidget)
	{
		MoveAndResizeWidget->AddContentWidget(InWidget);
	}
}

void UExternalWidget::AddContentWidget(UParameterDetailWidget * InWidget)
{
	if (MoveAndResizeWidget)
	{
		MoveAndResizeWidget->AddContentWidget(InWidget);
		InWidget->ParamDetailActionDelegate.BindUFunction(this, FName(TEXT("OnParamDetailActionHandler")));
	}
}

UExternalWidget * UExternalWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UExternalWidget>(UExternalWidget::ExternalWidgetPath);
}

UExternalWidget * UExternalWidget::Get()
{
	if (!IS_OBJECT_PTR_VALID(UExternalWidget::ExternalWidgetInstance))
	{
		UExternalWidget::ExternalWidgetInstance = UExternalWidget::Create();
		UExternalWidget::ExternalWidgetInstance->AddToViewport(PopUIZOrder);
	}
	return UExternalWidget::ExternalWidgetInstance;
}

FEventReply UExternalWidget::UpdateLastMousePosition(const FPointerEvent & MouseEvent)
{
	FVector2D PixelPosTemp; // no use
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), PixelPosTemp, LastMousePosition);
	UE_LOG(LogTemp, Log, TEXT("left mouse to resize , last pos : %s"), *LastMousePosition.ToString());
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
	return UWidgetBlueprintLibrary::CaptureMouse(DetectReply, nullptr);
}

FVector2D UExternalWidget::GetMouseMoveVector(const FPointerEvent & MouseEvent)
{
	FVector2D LocalPixelPosition;// no use
	FVector2D LocalViewportPosition;
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	return LocalViewportPosition - LastMousePosition;
}

void UExternalWidget::MouseButtonReleaseToDrop()
{
	IsMouseButtonDown = false;
	IsDrag = false;
	IsResize = false;
	if (IS_OBJECT_PTR_VALID(MoveAndResizeWidget))
	{
		MoveAndResizeWidget->SetContentWidgetShow(true);
	}
}

bool UExternalWidget::CheckMouseIsOutScreen(const FPointerEvent & MouseEvent)
{
	FVector2D LocalPixelPosition;
	FVector2D LocalViewportPosition;// no use
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	FVector2D ViewportSize = UWidgetLayoutLibrary::GetViewportSize(GWorld);
	return (LocalPixelPosition.X < SaftyBoundarySize) || (LocalPixelPosition.Y < SaftyBoundarySize)
		|| (LocalPixelPosition.X >(ViewportSize.X - SaftyBoundarySize)) || (LocalPixelPosition.Y >(ViewportSize.Y - SaftyBoundarySize));
}

void UExternalWidget::InitMoveAndResizeWidget()
{
	if (!MoveAndResizeWidget)
	{
		MoveAndResizeWidget = UMoveAndResizeWidget::Create();
	}
	if (MoveAndResizeWidget && CPExternal)
	{
		MoveAndResizeWidget->bIsFocusable = true;
		MoveAndResizeWidget->MoveAndResizeDelegate.BindUFunction(this, FName(TEXT("OnMoveAndResizeActionHandler")));
		MoveAndResizeWidget->MoveWidgetDelegate.BindUFunction(this, FName(TEXT("OnMoveActionHandler")));
		MoveAndResizeWidget->ResizeWidgetDelegate.BindUFunction(this, FName(TEXT("OnResizeActionHandler")));
		CPExternal->AddChild(MoveAndResizeWidget);
		MoveAndResizeSlot = UWidgetLayoutLibrary::SlotAsCanvasSlot(MoveAndResizeWidget);
		if (IS_OBJECT_PTR_VALID(MoveAndResizeSlot))
		{
			FVector2D ViewportSize = UWidgetLayoutLibrary::GetViewportSize(GWorld);
			MoveAndResizeSlot->SetSize(FVector2D((ViewportSize.Y - 100) * 1.4, (ViewportSize.Y - 100)));
			//MoveAndResizeSlot->SetPosition(FVector2D(600.0f, 200.0f));
			MoveAndResizeSlot->SetAnchors(0.5);
			MoveAndResizeSlot->SetAlignment(FVector2D(0.5,0.5));
		}
	}
}

void UExternalWidget::OnMoveAndResizeActionHandler(const EMoveAndResizeType & ActionType)
{
	if (ActionType == EMoveAndResizeType::Close)
	{
		this->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UExternalWidget::OnMoveActionHandler(bool IsAction, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsAction)
		{
			IsMouseButtonDown = true;
			IsDrag = true;
			UpdateLastMousePosition(MouseEvent);
		}
		else
		{
			MouseButtonReleaseToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UExternalWidget::OnResizeActionHandler(bool IsAction, const FPointerEvent & MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (IsAction)
		{
			IsMouseButtonDown = true;
			IsResize = true;
			UpdateLastMousePosition(MouseEvent);
		}
		else
		{
			MouseButtonReleaseToDrop();
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(MouseEvent, nullptr, EKeys::LeftMouseButton);
			UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
		}
	}
}

void UExternalWidget::OnParamDetailActionHandler(const int32 & ActionType)
{
	if (UParameterDetailWidget::Get() == 0)
	{
		return;
	}
	this->SetVisibility(ESlateVisibility::Collapsed);
}

FReply UExternalWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (UParameterDetailWidget::Get()->ParamRightMouseAction())
		{
			//UParameterDetailWidget::Get()->ClearSelectState();
			return FReply::Handled();
		}
		return FReply::Unhandled();
	}
	return FReply::Unhandled();
}

FReply UExternalWidget::NativeOnMouseButtonUp(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		MouseButtonReleaseToDrop();
		FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
		return UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply).NativeReply;
	}
	return FReply::Unhandled();
}

FReply UExternalWidget::NativeOnMouseMove(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (IsMouseButtonDown)
	{
		if (CheckMouseIsOutScreen(InMouseEvent))
		{
			MouseButtonReleaseToDrop();
			return FReply::Handled();
		}
		else
		{
			FVector2D MouseMove = GetMouseMoveVector(InMouseEvent);
			FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
			if (IsDrag)
			{
				if (MoveAndResizeSlot)
				{
					MoveAndResizeSlot->SetPosition(MoveAndResizeSlot->GetPosition() + MouseMove);
				}
			}
			if (IsResize)
			{
				if (MoveAndResizeSlot)
				{
					FVector2D BoundarySize;
					BoundarySize.X = ((MoveAndResizeSlot->GetSize() + MouseMove).X > 640.0f) ? (MoveAndResizeSlot->GetSize() + MouseMove).X : 640.0f;
					BoundarySize.Y = ((MoveAndResizeSlot->GetSize() + MouseMove).Y > 640.0f) ? (MoveAndResizeSlot->GetSize() + MouseMove).Y : 640.0f;
					MoveAndResizeSlot->SetSize(BoundarySize);
				}
			}
			if (IS_OBJECT_PTR_VALID(MoveAndResizeWidget))
			{
				MoveAndResizeWidget->SetContentWidgetShow(true);
			}
			return UpdateLastMousePosition(InMouseEvent).NativeReply;
		}
	}
	return FReply::Unhandled();
}

void UExternalWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	MouseButtonReleaseToDrop();
	FEventReply DetectReply = UWidgetBlueprintLibrary::DetectDragIfPressed(InMouseEvent, nullptr, EKeys::LeftMouseButton);
	UWidgetBlueprintLibrary::ReleaseMouseCapture(DetectReply);
}
