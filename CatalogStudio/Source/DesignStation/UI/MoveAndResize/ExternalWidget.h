// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MoveAndResizeWidget.h"
#include "ExternalWidget.generated.h"

/**
 * 
 */

class UCanvasPanel;
class UParameterDetailWidget;

UCLASS()
class DESIGNSTATION_API UExternalWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void AddContentWidget(UUserWidget* InWidget);
	void AddContentWidget(UParameterDetailWidget* InWidget);

	static UExternalWidget* Create();
	static UExternalWidget* Get();

private:
	FEventReply UpdateLastMousePosition(const FPointerEvent & MouseEvent);
	FVector2D GetMouseMoveVector(const FPointerEvent& MouseEvent);
	void MouseButtonReleaseToDrop();
	bool CheckMouseIsOutScreen(const FPointerEvent& MouseEvent);

	void InitMoveAndResizeWidget();
	UFUNCTION()
		void OnMoveAndResizeActionHandler(const EMoveAndResizeType& ActionType);
	UFUNCTION()
		void OnMoveActionHandler(bool IsAction, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnResizeActionHandler(bool IsAction, const FPointerEvent& MouseEvent);

	UFUNCTION()
		void OnParamDetailActionHandler(const int32& ActionType);

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;

private:
	bool IsMouseButtonDown;
	bool IsDrag;
	bool IsResize;
	FVector2D LastMousePosition;

	UPROPERTY()
		UMoveAndResizeWidget* MoveAndResizeWidget;

	static FString ExternalWidgetPath;
	static UExternalWidget* ExternalWidgetInstance;
	
private:
	UPROPERTY()
		UCanvasPanel* CPExternal;
	UPROPERTY()
		UCanvasPanelSlot* MoveAndResizeSlot;
};
