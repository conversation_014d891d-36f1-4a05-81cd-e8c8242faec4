// Fill out your copyright notice in the Description page of Project Settings.

#include "MoveAndResizeWidget.h"

#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/VerticalBox.h"

#include "Runtime/UMG/Public/Components/NamedSlot.h"


FString UMoveAndResizeWidget::MoveAndResizeWidgetPath = TEXT("WidgetBlueprint'/Game/UI/MoveAndResize/MoveAndResizeUI.MoveAndResizeUI_C'");

bool UMoveAndResizeWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>);
	BIND_SLATE_WIDGET_FUNCTION(BorHead, OnMouseButtonDownEvent, FName(TEXT("LeftMouseButtonDownToDrag")));
	BIND_SLATE_WIDGET_FUNCTION(BorHead, OnMouseButtonUpEvent, FName(TEXT("LeftMouseButtonUpToStopDrag")));
	//BIND_SLATE_WIDGET_FUNCTION(BorHead, OnMouseButtonUpEvent, FName(TEXT("NativeOnMouseButtonUp")));
	BIND_PARAM_CPP_TO_UMG(TxtTitle, Txt_Title);
	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, UMoveAndResizeWidget::OnClickedBtnClose);

	BIND_PARAM_CPP_TO_UMG(BorContent, Bor_Content);
	BIND_PARAM_CPP_TO_UMG(NSContent, NS_Content);

	BIND_PARAM_CPP_TO_UMG(BorResize, Bor_Resize);
	BIND_SLATE_WIDGET_FUNCTION(BorResize, OnMouseButtonDownEvent, FName(TEXT("LeftMouseButtonDownToResize")));
	BIND_SLATE_WIDGET_FUNCTION(BorResize, OnMouseButtonUpEvent, FName(TEXT("LeftMouseButtonUpToStopResize")));

	/*IsMouseButtonDown = false;
	LastMousePosition = FVector2D::ZeroVector;*/

	return true;
}

void UMoveAndResizeWidget::UpdateTitle(const FString& InTitle)
{
	if (TxtTitle)
	{
		TxtTitle->SetText(FText::FromString(InTitle));
	}
}

void UMoveAndResizeWidget::UpdateTitleBrushColor(const FLinearColor& InColor)
{
	if (IS_OBJECT_PTR_VALID(BorHead))
	{
		BorHead->SetBrushColor(InColor);
	}
}

void UMoveAndResizeWidget::AddContentWidget(UUserWidget* InWidget)
{
	if (NSContent)
	{
		NSContent->ClearChildren();
		NSContent->AddChild(InWidget);
	}
}

UMoveAndResizeWidget* UMoveAndResizeWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMoveAndResizeWidget>(UMoveAndResizeWidget::MoveAndResizeWidgetPath);
}

void UMoveAndResizeWidget::SetContentWidgetShow(bool IsShow)
{
	if (VBContent)
	{
		VBContent->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
	if (BorResize)
	{
		BorResize->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	}
}

FEventReply UMoveAndResizeWidget::LeftMouseButtonDownToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		MoveWidgetDelegate.ExecuteIfBound(true, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

FEventReply UMoveAndResizeWidget::LeftMouseButtonUpToStopDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		MoveWidgetDelegate.ExecuteIfBound(false, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

FEventReply UMoveAndResizeWidget::LeftMouseButtonDownToResize(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ResizeWidgetDelegate.ExecuteIfBound(true, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

FEventReply UMoveAndResizeWidget::LeftMouseButtonUpToStopResize(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ResizeWidgetDelegate.ExecuteIfBound(false, MouseEvent);
		return FEventReply(true);
	}
	return FEventReply();
}

FReply UMoveAndResizeWidget::NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	return FReply::Unhandled();
}

FReply UMoveAndResizeWidget::NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	return FReply::Unhandled();
}

void UMoveAndResizeWidget::OnClickedBtnClose()
{
	if (UParameterDetailWidget::Get()->CheckSelectWidgetValid())
	{
		MoveAndResizeDelegate.ExecuteIfBound(EMoveAndResizeType::Close);
	}
}
