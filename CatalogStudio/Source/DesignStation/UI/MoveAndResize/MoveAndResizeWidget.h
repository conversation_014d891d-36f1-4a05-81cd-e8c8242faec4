// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetBlueprintLibrary.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "MoveAndResizeWidget.generated.h"

/**
 * 
 */

class UBorder;
class UTextBlock;
class UVerticalBox;
class UNamedSlot;
class UButton;

UENUM(BlueprintType)
enum class EMoveAndResizeType : uint8
{
	Close = 0
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FMoveAndResizeDelegate, const EMoveAndResizeType&, ActionType);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FMoveWidgetDelegate, bool, <PERSON>Move, const FPointerEvent&, MouseEvent);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FResizeWidgetDelegate, bool, IsResize, const FPointerEvent&, MouseEvent);

UCLASS()
class DESIGNSTATION_API UMoveAndResizeWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateTitle(const FString& InTitle);
	void UpdateTitleBrushColor(const FLinearColor& InColor);
	void AddContentWidget(UUserWidget* InWidget);
	void SetContentWidgetShow(bool IsShow);

	static UMoveAndResizeWidget* Create();

public:
	FMoveAndResizeDelegate MoveAndResizeDelegate;
	FMoveWidgetDelegate MoveWidgetDelegate;
	FResizeWidgetDelegate ResizeWidgetDelegate;

private:
	static FString MoveAndResizeWidgetPath;
	
protected:
	UFUNCTION()
		FEventReply LeftMouseButtonDownToDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseButtonUpToStopDrag(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseButtonDownToResize(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseButtonUpToStopResize(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	virtual FReply NativeOnMouseButtonUp(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseMove(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnClickedBtnClose();

private:
	UPROPERTY()
		UVerticalBox* VBContent;

	UPROPERTY()
		UBorder* BorHead;
	UPROPERTY()
		UTextBlock* TxtTitle;
	UPROPERTY()
		UButton* BtnClose;

	UPROPERTY()
		UBorder* BorContent;
	UPROPERTY()
		UNamedSlot* NSContent;

	UPROPERTY()
		UBorder* BorResize;

	UPROPERTY()
		UCanvasPanelSlot* ParentSlot;
};
