// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "MultiCompItemBaseWidget.generated.h"

/**
 * 
 */



UCLASS()
class DESIGNSTATION_API UMultiCompItemBaseWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetIsSelect(bool IsSelect);
	
public:
	UPROPERTY()
		UButton* BtnSelect;
	UPROPERTY()
		UTextBlock* TxtItemName;
	UPROPERTY()
		UBorder* BorBackground;
};
