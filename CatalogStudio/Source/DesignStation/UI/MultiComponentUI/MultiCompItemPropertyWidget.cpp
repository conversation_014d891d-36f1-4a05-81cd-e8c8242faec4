// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompItemPropertyWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"
#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

#include "Decimal.h"
#include "DecimalMath.h"

FString UMultiCompItemPropertyWidget::MultiCompItemPropertyPath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiCompItemPropertyUI.MultiCompItemPropertyUI_C'");

bool UMultiCompItemPropertyWidget::Initialize()
{
	if (!UMultiCompItemBaseWidget::Initialize())
	{
		return false;
	}
	

	return true;
}

void UMultiCompItemPropertyWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_MultiParam);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UMultiCompItemPropertyWidget::OnClickedBtnSelect);
	BIND_PARAM_CPP_TO_UMG(TxtItemName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(EdtExpression, Edt_Express);
	BIND_WIDGET_FUNCTION(EdtExpression, OnTextCommitted, UMultiCompItemPropertyWidget::OnTextCommittedEdtExpression);
	BIND_PARAM_CPP_TO_UMG(BtnExpression, Btn_Express);
	BIND_WIDGET_FUNCTION(BtnExpression, OnClicked, UMultiCompItemPropertyWidget::OnClickedBtnExpression);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UMultiCompItemPropertyWidget::OnTextCommittedEdtValue);
	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseDoubleClickEvent, FName(TEXT("OnMouseDoubleClickToEditParam")));
	BIND_PARAM_CPP_TO_UMG(BorExpression, Bor_Express);
	BIND_PARAM_CPP_TO_UMG(BorValue, Bor_Value);
	BIND_PARAM_CPP_TO_UMG(BorEnumValue, Bor_EnumValue);
	BIND_PARAM_CPP_TO_UMG(CBSValues, CBS_Values);
	BIND_WIDGET_FUNCTION(CBSValues, OnSelectionChanged, UMultiCompItemPropertyWidget::OnSelectionChangedCBSValues);

	BIND_WIDGET_FUNCTION(BtnAddToLocal, OnClicked, UMultiCompItemPropertyWidget::OnClickBtnAddToLocal);
	//BIND_WIDGET_FUNCTION(Btn_Up, OnClicked, UMultiCompItemPropertyWidget::OnClickBtnUP);
	//BIND_WIDGET_FUNCTION(Btn_Down, OnClicked, UMultiCompItemPropertyWidget::OnClickBtnDown);

	CanEdit = true;
}

void UMultiCompItemPropertyWidget::SetIsSelect(bool IsSelect)
{
	if (BorName && BorExpression && BorValue)
	{
		BorName->SetBrushColor(IsSelect ? FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f) : FLinearColor::White);
		Bor_ParameterDes->SetBrushColor(IsSelect ? FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f) : FLinearColor::White);
		BorExpression->SetBrushColor(IsSelect ? FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f) : FLinearColor::White);
		BorValue->SetBrushColor(IsSelect ? FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f) : FLinearColor::White);
		BorEnumValue->SetBrushColor(IsSelect ? FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f) : FLinearColor::White);
	}
}

void UMultiCompItemPropertyWidget::SetIsFocusable(bool IsFocusable)
{
	if (EdtExpression && EdtValue && BtnExpression)
	{
		EdtExpression->SetVisibility(IsFocusable ? ESlateVisibility::Visible : ESlateVisibility::SelfHitTestInvisible);
		EdtValue->SetVisibility(IsFocusable ? ESlateVisibility::Visible : ESlateVisibility::SelfHitTestInvisible);
		BtnExpression->SetVisibility(IsFocusable ? ESlateVisibility::Visible : ESlateVisibility::SelfHitTestInvisible);
	}
}

void UMultiCompItemPropertyWidget::UpdateContent(const FParameterData& InData)
{
	ParamData.CopyData(InData);
	//if (InData.Data.value.IsNumeric())
	//{
	//	ParamData.Data.value = FString::Printf(TEXT("%.1f"), FCString::Atof(*InData.Data.value));
	//}
	SwitchShowByValueType(InData.Data.is_enum != 0);

	ParameterDes = FText::FromString(FParameterTableData::GetCleanDataWithoutAdditionMsg(InData.Data.description));
	Txt_ParameterDes->SetText(ParameterDes);
	Txt_ParameterDes->SetToolTipText(ParameterDes);

	if (IS_OBJECT_PTR_VALID(TxtItemName) && IS_OBJECT_PTR_VALID(EdtExpression))
	{
		FString temp = /*InData.Data.name.Len() > 6 ? InData.Data.name.Left(6) : */InData.Data.name;
		TxtItemName->SetText(FText::FromString(temp));
		TxtItemName->SetToolTipText(FText::FromString(temp));

		EdtExpression->SetText(FText::FromString(InData.Data.expression));
		EdtExpression->SetToolTipText(FText::FromString(InData.Data.expression));
	}
	CBSValues->ClearOptions();
	if (InData.Data.is_enum && IS_OBJECT_PTR_VALID(CBSValues) && (InData.EnumData.Num() > 0))
	{
		FString SelectOption;
		for (auto& Data : InData.EnumData)
		{
			if (!Data.IsVisiable())
			{
				continue;
			}

			const FString EnumDisplayName = Data.name_for_display.IsEmpty() ? Data.value : Data.name_for_display;
			CBSValues->AddOption(EnumDisplayName);
			if (SelectOption.IsEmpty())
			{
				if (UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(Data.value) && UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(InData.Data.value))
				{
					if (FDecimalMath::IsNearlyEqual(FDecimal(Data.value), FDecimal(InData.Data.value)))
					{
						SelectOption = EnumDisplayName;
					}
				}
				else if (Data.value.Equals(InData.Data.value))
				{
					SelectOption = EnumDisplayName;
				}
			}
		}
		CBSValues->RefreshOptions();
		if (!SelectOption.IsEmpty())
		{
			CBSValues->SetSelectedOption(SelectOption);
		}
		else
		{
			CBSValues->SetSelectedOption(TEXT(""));
		}
	}
	else if (IS_OBJECT_PTR_VALID(EdtValue))
	{
		EdtValue->SetText(FText::FromString(ParamData.Data.value));
		EdtValue->SetToolTipText(FText::FromString(ParamData.Data.value));
	}
	/*if (EdtExpression && EdtValue && TxtItemName)
	{
		TxtItemName->SetText(FText::FromString(InData.Data.name));
		EdtExpression->SetText(FText::FromString(InData.Data.expression));
		EdtValue->SetText(FText::FromString(InData.Data.value));
	}*/
}

void UMultiCompItemPropertyWidget::UnBindDetailDelegate()
{
	if (IS_OBJECT_PTR_VALID(BorName))
	{
		BorName->OnMouseDoubleClickEvent.Unbind();
	}
}

UMultiCompItemPropertyWidget* UMultiCompItemPropertyWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiCompItemPropertyWidget>(UMultiCompItemPropertyWidget::MultiCompItemPropertyPath);
}

void UMultiCompItemPropertyWidget::SwitchShowByValueType(bool IsEnum)
{
	if (IS_OBJECT_PTR_VALID(BorValue) && IS_OBJECT_PTR_VALID(BorEnumValue) && IS_OBJECT_PTR_VALID(EdtExpression) && IS_OBJECT_PTR_VALID(BtnExpression))
	{
		BorValue->SetVisibility(IsEnum ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		BorEnumValue->SetVisibility(IsEnum ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		//EdtExpression->SetIsEnabled(!IsEnum);
		//BtnExpression->SetIsEnabled(!IsEnum);
	}
}

void UMultiCompItemPropertyWidget::PropertyExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int)EItemPropertyExpressionType::PropertyExpression)
	{
		OnTextCommittedEdtExpression(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

FEventReply UMultiCompItemPropertyWidget::OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		FMultiParamsEditDelegate.ExecuteIfBound(this, EMultiParamType::Edit, ParamData);
		return FEventReply(true);
	}

	return FEventReply();
}

void UMultiCompItemPropertyWidget::OnClickedBtnSelect()
{
	MultiParamsSelectDelegate.ExecuteIfBound(this);
}

void UMultiCompItemPropertyWidget::OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && !Text.IsEmpty())
	{
		ParamData.Data.expression = Text.ToString();
		FMultiParamsEditDelegate.ExecuteIfBound(this, EMultiParamType::Expression, ParamData);
	}
	else if (IS_OBJECT_PTR_VALID(EdtExpression) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtExpression->SetText(FText::FromString(ParamData.Data.expression));
	}
}

void UMultiCompItemPropertyWidget::OnClickedBtnExpression()
{
	if (EdtExpression)
	{
		BIND_EXPRESSION_WIDGET((int)EItemPropertyExpressionType::PropertyExpression, EdtExpression->GetText().ToString(), FName(TEXT("PropertyExpressionEdit")));
	}
}

void UMultiCompItemPropertyWidget::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ParamData.Data.value = Text.ToString();
		FMultiParamsEditDelegate.ExecuteIfBound(this, EMultiParamType::Value, ParamData);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared && IS_OBJECT_PTR_VALID(EdtValue))
	{
		EdtValue->SetText(FText::FromString(ParamData.Data.value));
	}
}

void UMultiCompItemPropertyWidget::OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		SelectedItem = ParamData.EnumData[CBSValues->GetSelectedIndex()].value;
		TArray<TPair<FString, FString>> EnumArray = TArray<TPair<FString, FString>>();
		for (auto& En : ParamData.EnumData)
		{
			EnumArray.Add({ En.expression, En.value });
		}

		int32 EnumIndex = CBSValues->GetSelectedIndex();

		if (EnumIndex >= 0)
		{
			ParamData.Data.value = EnumArray[EnumIndex].Value;
			ParamData.Data.expression = EnumArray[EnumIndex].Key;
		}
		else
		{
			ParamData.Data.value = EnumArray[0].Value;
			ParamData.Data.expression = EnumArray[0].Key;
		}

		FMultiParamsEditDelegate.ExecuteIfBound(this, EMultiParamType::Value, ParamData);
	}
}

void UMultiCompItemPropertyWidget::OnClickBtnAddToLocal()
{
	FMultiParamsEditDelegate.ExecuteIfBound(this, EMultiParamType::AddToLocal, ParamData);
}

void UMultiCompItemPropertyWidget::OnClickBtnUP()
{
	BtnUpDownClickDelegate.ExecuteIfBound(GetParamData(), true);
}

void UMultiCompItemPropertyWidget::OnClickBtnDown()
{
	BtnUpDownClickDelegate.ExecuteIfBound(GetParamData(), false);
}
