// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MultiCompItemBaseWidget.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "MultiCompItemPropertyWidget.generated.h"

/**
 * 
 */

class UEditableText;
class UBorder;
class UComboBoxString;

UENUM(BlueprintType)
enum class EMultiParamType : uint8
{
	Expression = 0,
	Value,
	Edit,
	AddToLocal
};

UENUM(BlueprintType)
enum class EItemPropertyExpressionType : uint8
{
	PropertyExpression = 0
};

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMultiParamsEditDelegate, UMultiCompItemPropertyWidget*, ParamItem, const EMultiParamType&, EditType, const FParameterData&, ParamData);
DECLARE_DYNAMIC_DELEGATE_OneParam(FMultiParamsSelectDelegate, UMultiCompItemPropertyWidget*, ParamItem);
DECLARE_DELEGATE_TwoParams(FMultiParamsUpDownDelegate, const FParameterData&, bool);

UCLASS()
class DESIGNSTATION_API UMultiCompItemPropertyWidget : public UMultiCompItemBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	virtual void SetIsSelect(bool IsSelect) override;
	void SetIsFocusable(bool IsFocusable = true);
	FParameterData& GetParamData() { return ParamData; }
	void UpdateContent(const FParameterData& InData);
	void UnBindDetailDelegate();

	void SetCanEdit(bool bCanEdit) { CanEdit = bCanEdit; }
	void SetToolTip(const FString& InTip) { ShowToolTip = InTip; }

	static UMultiCompItemPropertyWidget* Create();

public:
	UPROPERTY(BlueprintReadWrite, Category = "State")
	bool CanEdit;

	UPROPERTY(BlueprintReadWrite, Category = "State")
	FString ShowToolTip;

private:
	void SwitchShowByValueType(bool IsEnum);
	UFUNCTION()
		void PropertyExpressionEdit(const int32& EditType, const FString& OutExpression);

public:
	FMultiParamsEditDelegate FMultiParamsEditDelegate;
	FMultiParamsSelectDelegate MultiParamsSelectDelegate;
	FMultiParamsUpDownDelegate BtnUpDownClickDelegate;
private:
	FParameterData ParamData;

	static FString MultiCompItemPropertyPath;

protected:
	UFUNCTION()
		FEventReply OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	UFUNCTION()
		void OnClickedBtnSelect();

	UFUNCTION()
		void OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExpression();
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType);


	UFUNCTION()
		void OnClickBtnAddToLocal();

		UFUNCTION()
		void OnClickBtnUP();

		UFUNCTION()
		void OnClickBtnDown();

private:
	UPROPERTY()
		UEditableText* EdtExpression;
	UPROPERTY()
		UButton* BtnExpression;
	UPROPERTY()
		UEditableText* EdtValue;
	UPROPERTY()
		UBorder* BorName;
	UPROPERTY(BlueprintReadWrite, Category = "MultiCompItemPropertyWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UBorder* Bor_ParameterDes;
	UPROPERTY(BlueprintReadWrite, Category = "MultiCompItemPropertyWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* Txt_ParameterDes;
	UPROPERTY(BlueprintReadOnly, Category = "MultiCompItemPropertyWidget", meta = (AllowPrivateAccess = true))
	FText	ParameterDes;
	UPROPERTY()
	UBorder* BorExpression;
	UPROPERTY()
	UBorder* BorValue;
	UPROPERTY()
	UBorder* BorEnumValue;
	UPROPERTY()
	UComboBoxString* CBSValues;

	UPROPERTY(meta=(BindWidget=true))
	UButton* BtnAddToLocal;

	//UPROPERTY(meta = (BindWidget))
	//UButton* Btn_Up;

	//UPROPERTY(meta = (BindWidget))
	//UButton* Btn_Down;
};
