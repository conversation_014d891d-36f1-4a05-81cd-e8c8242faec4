// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DesignStation/Geometry/DataDefines/MultiComponentDataObject.h"
#include "MultiCompPosWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMultiCompPosType : uint8
{
	PosXExpression = 0,
	PosXValue,
	PosYExpression,
	PosYValue,
	PosZExpression,
	PosZValue
};

UENUM(BlueprintType)
enum class EPosExpressionType : uint8
{
	PosXExpression = 0,
	PosYExpression,
	PosZExpression
};

UENUM(BlueprintType)
enum class EMultiPosType : uint8
{
	Location = 0,
	Rotation,
	Scale,
	None
};

class UTextBlock;
class UEditableText;
class UButton;

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMultiCompLocationEditDelegate, const EMultiCompPosType&, EditType, const FLocationProperty&, LocationData, const int32&, EditIndex);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMultiCompRotationEditDelegate, const EMultiCompPosType&, EditType, const FRotationProperty&, RotationData, const int32&, EditIndex);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FMultiCompScaleEditDelegate, const EMultiCompPosType&, EditType, const FScaleProperty&, ScaleData, const int32&, EditIndex);

UCLASS()
class DESIGNSTATION_API UMultiCompPosWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void UpdateContent(const FLocationProperty& InLocationData);
	void UpdateContent(const FRotationProperty& InRotationData);
	void UpdateContent(const FScaleProperty& InScaleData);

	static UMultiCompPosWidget* Create();

private:
	UFUNCTION()
		void MultiPosExpressionEdit(const int32& EditType, const FString& OutExpression);

public:
	FMultiCompLocationEditDelegate MultiCompLocationEditDelegate;
	FMultiCompRotationEditDelegate MultiCompRotationEditDelegate;
	FMultiCompScaleEditDelegate MultiCompScaleEditDelegate;

private:
	EMultiPosType CurrentType;
	FLocationProperty LocationData;
	FRotationProperty RotationData;
	FScaleProperty ScaleData;

	static FString MultiCompPosPath;

protected:
	UFUNCTION()
		void OnTextCommittedEdtPosXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnPosXExpression();
	UFUNCTION()
		void OnTextCommittedEdtPosXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtPosYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnPosYExpression();
	UFUNCTION()
		void OnTextCommittedEdtPosYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtPosZExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnPosZExpression();
	UFUNCTION()
		void OnTextCommittedEdtPosZValue(const FText& Text, ETextCommit::Type CommitMethod);
	
private:
	UPROPERTY()
		UTextBlock* TxtPosName;
	UPROPERTY()
		UTextBlock* TxtPosX;
	UPROPERTY()
		UTextBlock* TxtPosY;
	UPROPERTY()
		UTextBlock* TxtPosZ;

	UPROPERTY()                                    
		UEditableText* EdtPosXExpression;
	UPROPERTY()
		UButton* BtnPosXExpression;
	UPROPERTY()
		UEditableText* EdtPosXValue;

	UPROPERTY()
		UEditableText* EdtPosYExpression;
	UPROPERTY()
		UButton* BtnPosYExpression;
	UPROPERTY()
		UEditableText* EdtPosYValue;

	UPROPERTY()
		UEditableText* EdtPosZExpression;
	UPROPERTY()
		UButton* BtnPosZExpression;
	UPROPERTY()
		UEditableText* EdtPosZValue;
};
