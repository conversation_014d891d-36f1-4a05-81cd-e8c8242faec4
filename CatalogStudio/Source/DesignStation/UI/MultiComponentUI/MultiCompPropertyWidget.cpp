// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompPropertyWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

#define LOCTEXT_NAMESPACE "MultiComponentProperty"

FString UMultiCompPropertyWidget::MultiCompPropertyPath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiCompPropertyUI.MultiCompPropertyUI_C'");

bool UMultiCompPropertyWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	//file property
	BIND_PARAM_CPP_TO_UMG(EdtPartIdExpression, Edt_PartIDExpress);
	BIND_WIDGET_FUNCTION(EdtPartIdExpression, OnTextCommitted, UMultiCompPropertyWidget::OnTextCommittedEdtIdExpression);

	BIND_PARAM_CPP_TO_UMG(BtnPartIdExpression, Btn_PartIDExpress);
	BIND_WIDGET_FUNCTION(BtnPartIdExpression, OnClicked, UMultiCompPropertyWidget::OnClickedBtnIdExpression);

	BIND_PARAM_CPP_TO_UMG(EdtPartIdVale, Edt_PartIDValue);
	BIND_WIDGET_FUNCTION(EdtPartIdVale, OnTextCommitted, UMultiCompPropertyWidget::OnTextCommittedEdtIdValue);

	BIND_PARAM_CPP_TO_UMG(EdtExplain, Edt_Explain);
	//BIND_WIDGET_FUNCTION(EdtExplain, OnTextCommitted, UMultiCompPropertyWidget::OnTextCommittedEdtExpalin);

	BIND_PARAM_CPP_TO_UMG(EdtVisiExpression, Edt_VisiExpress);
	BIND_WIDGET_FUNCTION(EdtVisiExpression, OnTextCommitted, UMultiCompPropertyWidget::OnTextCommittedEdtVisibleExpression);
	BIND_PARAM_CPP_TO_UMG(BtnVisiExpression, Btn_VisiExpress);
	BIND_WIDGET_FUNCTION(BtnVisiExpression, OnClicked, UMultiCompPropertyWidget::OnClickedBtnVisibleExpression);
	BIND_PARAM_CPP_TO_UMG(EdtVisiValue, Edt_VisiValue);
	BIND_WIDGET_FUNCTION(EdtVisiValue, OnTextCommitted, UMultiCompPropertyWidget::OnTextCommittedEdtVisibleValue);

	//transform
	BIND_PARAM_CPP_TO_UMG(SCBTransform, SB_Transform);

	//parameter
	BIND_PARAM_CPP_TO_UMG(BtnAddParam, Btn_AddParam);
	BIND_WIDGET_FUNCTION(BtnAddParam, OnClicked, UMultiCompPropertyWidget::OnClickedBtnAddParam);
	BIND_PARAM_CPP_TO_UMG(BtnDelParam, Btn_DelParam);
	BIND_WIDGET_FUNCTION(BtnDelParam, OnClicked, UMultiCompPropertyWidget::OnClickedBtnDelParam);
	BIND_PARAM_CPP_TO_UMG(SCBParams, SCB_Params);

	BIND_WIDGET_FUNCTION(Btn_Up, OnClicked, UMultiCompPropertyWidget::OnClickBtnUP);
	BIND_WIDGET_FUNCTION(Btn_Down, OnClicked, UMultiCompPropertyWidget::OnClickBtnDown);

	return true;
}

void UMultiCompPropertyWidget::UpdateContent(const FMultiComponentDataItem& ItemData)
{
	PropertyData = ItemData;
	if (IS_OBJECT_PTR_VALID(EdtPartIdExpression) && IS_OBJECT_PTR_VALID(EdtPartIdVale))
	{
		EdtPartIdExpression->SetText(FText::FromString(ItemData.ComponentID.Expression));
		FString temp = ItemData.ComponentID.Value.Len() > 8 ? ItemData.ComponentID.Value.Left(8) : ItemData.ComponentID.Value;
		EdtPartIdVale->SetText(FText::FromString(temp));
	}
	TxtComponentName->SetText(FText::FromString(ItemData.ComponentName));
	EdtExplain->SetText(FText::FromString(ItemData.Description));
	EdtCoding->SetText(FText::FromString(ItemData.Code));

	FString CodeExp = ItemData.CodeExp;
	if (CodeExp.Len() > 25)
	{
		CodeExp = CodeExp.Left(25);
		EdtCodingExp->SetText(FText::FromString(CodeExp));
		//UE_LOG(LogTemp, Error, TEXT("EdtCodingExp %s"), *(EdtCodingExp->GetText().ToString()));
	}
	else
	{
		EdtCodingExp->SetText(FText::FromString(CodeExp));
		//UE_LOG(LogTemp, Error, TEXT("EdtCodingExp %s"), *(EdtCodingExp->GetText().ToString()));
	}

	if (IS_OBJECT_PTR_VALID(EdtVisiExpression) && IS_OBJECT_PTR_VALID(EdtVisiValue))
	{
		EdtVisiExpression->SetText(FText::FromString(ItemData.ComponentVisibility.Expression));
		EdtVisiValue->SetText(FText::FromString(ItemData.ComponentVisibility.Value));
	}
	if (IS_OBJECT_PTR_VALID(SCBTransform))
	{
		//SCBTransform->ClearChildren();
		UpdateLocationPropertyWidget(ItemData.ComponentLocation);
		UpdateRotationPropertyWidget(ItemData.ComponentRotation);
		UpdateScalePropertyWidget(ItemData.ComponentScale);
	}
	UpdateComponentParameterWidget(ItemData.ComponentParameters);
	UpdateInheritParameterWidget(ItemData.InheritParams, ItemData.ComponentParameters);

	SelectItemPropertyWidget = nullptr;
	UpdateParamEditButtonState(false);
}

void UMultiCompPropertyWidget::SelectParameterUI(const FParameterData& InParamData)
{
	SelectItemPropertyWidget = nullptr;
	UMultiCompItemPropertyWidget** FindWidget = ParamItemWidget.FindByPredicate([&](UMultiCompItemPropertyWidget* InWidget) {
		if (InWidget != nullptr)
			return InParamData.Data.name.Equals(InWidget->GetParamData().Data.name);
		else
			return false;
		});

	if (FindWidget != nullptr)
	{
		ParamSelectEdit(*FindWidget);
	}

}

void UMultiCompPropertyWidget::SwapParameterUI(const FParameterData& InParamA, const FParameterData& InParamB)
{
	int32 FindWidgetA = ParamItemWidget.IndexOfByPredicate([&](UMultiCompItemPropertyWidget* InWidget) {
		if (InWidget != nullptr)
			return InParamA.Data.name.Equals(InWidget->GetParamData().Data.name);
		else
			return false;
		});

	int32 FindWidgetB = ParamItemWidget.IndexOfByPredicate([&](UMultiCompItemPropertyWidget* InWidget) {
		if (InWidget != nullptr)
			return InParamB.Data.name.Equals(InWidget->GetParamData().Data.name);
		else
			return false;
		});

	if (FindWidgetA != INDEX_NONE && FindWidgetB != INDEX_NONE)
	{
		//int32 MinIndex = FMath::Min(FindWidgetA, FindWidgetB);
		//int32 MaxIndex = FMath::Max(FindWidgetA, FindWidgetB);

		//SCBParams->RemoveChild(ParamItemWidget[MinIndex]);
		//SCBParams->RemoveChild(ParamItemWidget[MaxIndex]);
		//SCBParams->InsertChildAt(MinIndex, ParamItemWidget[MaxIndex]);
		//SCBParams->InsertChildAt(MaxIndex, ParamItemWidget[MinIndex]);
		////SCBParams->InvalidateLayoutAndVolatility();
		ParamItemWidget.Swap(FindWidgetA, FindWidgetB);
		SCBParams->ClearChildren();
		for (auto Ite : ParamItemWidget)
		{
			SCBParams->AddChild(Ite);
		}
	}

}

void UMultiCompPropertyWidget::SetParentParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameter)
{
	MultiCompParentParameters = InParentParameter;
}

UMultiCompPropertyWidget* UMultiCompPropertyWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiCompPropertyWidget>(UMultiCompPropertyWidget::MultiCompPropertyPath);
}

void UMultiCompPropertyWidget::UpdateLocationPropertyWidget(const FLocationProperty& InLocationData)
{
	if (!IS_OBJECT_PTR_VALID(LocationWidget))
	{
		LocationWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPosWidget>();
		SCBTransform->AddChild(LocationWidget);
	}
	if (IS_OBJECT_PTR_VALID(LocationWidget))
	{
		LocationWidget->UpdateContent(InLocationData);
		LocationWidget->MultiCompLocationEditDelegate.BindUFunction(this, FName(TEXT("MultiItemLocationEdit")));
		LocationWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMultiCompPropertyWidget::UpdateRotationPropertyWidget(const FRotationProperty& InRotationData)
{
	if (!IS_OBJECT_PTR_VALID(RotationWidget))
	{
		RotationWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPosWidget>();
		SCBTransform->AddChild(RotationWidget);
	}
	if (IS_OBJECT_PTR_VALID(RotationWidget))
	{
		RotationWidget->UpdateContent(InRotationData);
		RotationWidget->MultiCompRotationEditDelegate.BindUFunction(this, FName(TEXT("MultiItemRotationEdit")));
		RotationWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMultiCompPropertyWidget::UpdateScalePropertyWidget(const FScaleProperty& InScaleData)
{
	if (!IS_OBJECT_PTR_VALID(ScaleWidget))
	{
		ScaleWidget = UUIFunctionLibrary::CreateUIWidget<UMultiCompPosWidget>();
		SCBTransform->AddChild(ScaleWidget);
	}
	if (IS_OBJECT_PTR_VALID(ScaleWidget))
	{
		ScaleWidget->UpdateContent(InScaleData);
		ScaleWidget->MultiCompScaleEditDelegate.BindUFunction(this, FName(TEXT("MultiItemScaleEdit")));
		ScaleWidget->SetVisibility(ESlateVisibility::Visible);
	}
}

void UMultiCompPropertyWidget::UpdateComponentParameterWidget(const TArray<FParameterData>& InParamDatas)
{
	if (!IS_OBJECT_PTR_VALID(SCBParams))
	{
		return;
	}
	SCBParams->ClearChildren();
	ParamItemWidget.Empty();
	for (auto& Data : InParamDatas)
	{
		UMultiCompItemPropertyWidget* PropertyItem = UUIFunctionLibrary::CreateUIWidget<UMultiCompItemPropertyWidget>();
		PropertyItem->UpdateContent(Data);
		PropertyItem->SetCanEdit(true);
		PropertyItem->FMultiParamsEditDelegate.BindUFunction(this, FName(TEXT("MultiParamEdit")));
		PropertyItem->MultiParamsSelectDelegate.BindUFunction(this, FName(TEXT("ParamSelectEdit")));
		PropertyItem->BtnUpDownClickDelegate.BindUObject(this, &UMultiCompPropertyWidget::OnClickBtnUPDown);
		PropertyItem->SetVisibility(ESlateVisibility::Visible);
		ParamItemWidget.Add(PropertyItem);
		SCBParams->AddChild(PropertyItem);
	}
}

void UMultiCompPropertyWidget::UpdateInheritParameterWidget(const TArray<FParameterData>& InParamDatas, const TArray<FParameterData>& InComponentParams)
{
	if (!IS_OBJECT_PTR_VALID(SCBParams))
	{
		return;
	}

	for (auto& IPD : InParamDatas)
	{
		//筛除本层已经有的参数
		const int32 ParamExist = InComponentParams.IndexOfByPredicate(
			[&IPD](const FParameterData& Param)->bool
			{
				return IPD.Data.name.Equals(Param.Data.name, ESearchCase::Type::CaseSensitive);
			}
		);
		if (ParamExist != INDEX_NONE)
		{
			continue;
		}

		UMultiCompItemPropertyWidget* PropertyItem = UUIFunctionLibrary::CreateUIWidget<UMultiCompItemPropertyWidget>();
		PropertyItem->UpdateContent(IPD);
		PropertyItem->SetCanEdit(false);
		PropertyItem->FMultiParamsEditDelegate.BindUFunction(this, FName(TEXT("MultiParamEdit")));
		PropertyItem->MultiParamsSelectDelegate.BindUFunction(this, FName(TEXT("ParamSelectEdit")));
		PropertyItem->BtnUpDownClickDelegate.BindUObject(this, &UMultiCompPropertyWidget::OnClickBtnUPDown);
		PropertyItem->SetVisibility(ESlateVisibility::Visible);
		ParamItemWidget.Add(PropertyItem);
		SCBParams->AddChild(PropertyItem);
	}
}

//void UMultiCompPropertyWidget::UpdateParamPropertyWidget(bool IsNew, const FParameterData& InData)
//{
//	UParameterDetailWidget::Get()->UpdateContent(InData);
//	UParameterDetailWidget::Get()->SetIsNewParam(IsNew);
//	UParameterDetailWidget::Get()->SwitchWidgetBindFunc(ESaveType::MultiComponent);
//	UParameterDetailWidget::Get()->ParamForMultiComponentDelegate.BindUFunction(this, FName(TEXT("ParamDetailEdit")));
//	UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
//}

void UMultiCompPropertyWidget::UpdateParamEditButtonState(bool IsSelectItem)
{
	if (IS_OBJECT_PTR_VALID(BtnDelParam))
	{
		BtnDelParam->SetIsEnabled(IsSelectItem);
	}
}

void UMultiCompPropertyWidget::ParamSelectEdit(UMultiCompItemPropertyWidget* ParamItem)
{
	if (SelectItemPropertyWidget)
	{
		SelectItemPropertyWidget->SetIsSelect(false);
	}
	SelectItemPropertyWidget = ParamItem;
	ParamItem->SetIsSelect(true);
	UpdateParamEditButtonState(true);
}

void UMultiCompPropertyWidget::OnParamAddHandler(const FParameterData& ParamData)
{
	MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Add, ParamData);
}

void UMultiCompPropertyWidget::OnParamAddHandler02(const TArray<FParameterData>& ParamDatas)
{
	MultiComponentParamAddDelegate.ExecuteIfBound(EParameterType::Add, ParamDatas);
}

void UMultiCompPropertyWidget::OnParamUpdateHandler(const FParameterData& ParamData)
{
	MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Modify, ParamData);
}

//void UMultiCompPropertyWidget::ParamDetailEdit(const EMultiComponentParamType& EditType, const FParameterData& ParamData)
//{
//	MultiComponentItemParamEditDelegate.ExecuteIfBound(EditType, ParamData);
//}

void UMultiCompPropertyWidget::MultiPropertyExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EPropertyExpressionEditType::IdExpression)
	{
		OnTextCommittedEdtIdExpression(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
	else if (EditType == (int32)EPropertyExpressionEditType::VisibleExpression)
	{
		OnTextCommittedEdtVisibleExpression(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UMultiCompPropertyWidget::GetOverrideParameters(const FString& InParameterName) const
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ExcludeSelf;
	{
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
		UParameterRelativeLibrary::CombineParameters(PeerParameters, PropertyData.ComponentParameters);
		PeerParameters.Remove(InParameterName);
		UParameterRelativeLibrary::CombineParameters(MultiCompParentParameters, PeerParameters, ExcludeSelf);
	}
	return ExcludeSelf;
}

TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  UMultiCompPropertyWidget::GetOverridelocalParameters(const FString& InParameterName) const
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(PeerParameters, PropertyData.ComponentParameters);
	PeerParameters.Remove(InParameterName);
	return PeerParameters;
}

void UMultiCompPropertyWidget::MultiParamEdit(UMultiCompItemPropertyWidget* ParamItem, const EMultiParamType& EditType, const FParameterData& ParamData)
{
	if (EditType == EMultiParamType::Expression)
	{
		MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Expression, ParamData);
	}
	else if (EditType == EMultiParamType::Value)
	{
		MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Value, ParamData);
	}
	else if (EditType == EMultiParamType::Edit)
	{
		//UpdateParamPropertyWidget(false, ParamData);
		if (MultiCompParentParameters.Num() == 0)
		{
			MultiCompParentParametersDelegate.ExecuteIfBound();
		}
		if (IS_OBJECT_PTR_VALID(SelectItemPropertyWidget))
		{
			UParameterDetailWidget::Get()->SetFolderOrFileParentParams(MultiCompParentParameters);
			UParameterDetailWidget::Get()->SetFolderOrFileLocalParams(GetOverridelocalParameters(SelectItemPropertyWidget->GetParamData().Data.name));
			UParameterDetailWidget::Get()->UpdateContent(SelectItemPropertyWidget->GetParamData(), (int32)EParamDetailType::MultiComponentParam);
			UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamUpdateHandler")));
			UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
			UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
			UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		}
	}
	else if (EditType == EMultiParamType::AddToLocal)
	{
		MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Add, ParamData);
	}
}

void UMultiCompPropertyWidget::MultiItemLocationEdit(const EMultiCompPosType& EditType, const FLocationProperty& LocationData, const int32& InEditIndex)
{
	MultiCompLocationDelegate.ExecuteIfBound(EditType, LocationData, PropertyData.ID);
}

void UMultiCompPropertyWidget::MultiItemRotationEdit(const EMultiCompPosType& EditType, const FRotationProperty& RotationData, const int32& InEditIndex)
{
	MultiCompRotationDelegate.ExecuteIfBound(EditType, RotationData, PropertyData.ID);
}

void UMultiCompPropertyWidget::MultiItemScaleEdit(const EMultiCompPosType& EditType, const FScaleProperty& ScaleData, const int32& InEditIndex)
{
	MultiCompScaleDelegate.ExecuteIfBound(EditType, ScaleData, PropertyData.ID);
}

void UMultiCompPropertyWidget::AddParamToLocal(const FParameterData& ParamData)
{
	UMultiCompItemPropertyWidget* PropertyItem = UUIFunctionLibrary::CreateUIWidget<UMultiCompItemPropertyWidget>();
	PropertyItem->UpdateContent(ParamData);
	PropertyItem->SetCanEdit(true);
	PropertyItem->FMultiParamsEditDelegate.BindUFunction(this, FName(TEXT("MultiParamEdit")));
	PropertyItem->MultiParamsSelectDelegate.BindUFunction(this, FName(TEXT("ParamSelectEdit")));
	PropertyItem->BtnUpDownClickDelegate.BindUObject(this, &UMultiCompPropertyWidget::OnClickBtnUPDown);
	PropertyItem->SetVisibility(ESlateVisibility::Visible);
	ParamItemWidget.Add(PropertyItem);

	SCBParams->InsertChildAt(PropertyData.ComponentParameters.Num(), PropertyItem);
}

void UMultiCompPropertyWidget::OnTextCommittedEdtIdExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (Text.ToString().Equals(PropertyData.ComponentID.Expression))
		{
			return;
		}
		TArray<TPair<int32, FString>> Comments;
		const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
		if (true/*!CleanExp.IsEmpty()*/)
		{
            PropertyData.ComponentID.Expression = CleanExp;
			MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Expression, Text.ToString());
		}
		else
		{
			EdtPartIdExpression->SetText(FText::FromString(PropertyData.ComponentID.Expression));
		}
	}
}

void UMultiCompPropertyWidget::OnClickedBtnIdExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtPartIdExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EPropertyExpressionEditType::IdExpression, EdtPartIdExpression->GetText().ToString(), FName(TEXT("MultiPropertyExpressionEdit")));
	}
}

void UMultiCompPropertyWidget::OnTextCommittedEdtIdValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (Text.ToString().Equals(PropertyData.ComponentID.Value))
		{
			return;
		}

		TArray<TPair<int32, FString>> Comments;
		const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
		if (Text.IsNumeric())
		{
			const double NewID = FCString::Atod(*Text.ToString());
			//MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Value, FString::Printf(TEXT("%.1d"), NewID));
			MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Value, FString::SanitizeFloat(NewID));
		}
		else if (CleanExp.IsEmpty())
		{
			MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Expression, Text.ToString());
		}
		else
		{
			EdtPartIdVale->SetText(FText::FromString(PropertyData.ComponentID.Value));
		}
	}
}

void UMultiCompPropertyWidget::OnTextCommittedEdtExpalin(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len() <= 200)
	{
		MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Explain, Text.ToString());
	}
	else if (IS_OBJECT_PTR_VALID(EdtExplain) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtExplain->SetText(FText::FromString(PropertyData.Description));
	}
}

void UMultiCompPropertyWidget::OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared /*&& !Text.IsEmpty()*/)
	{
		MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::Coding, Text.ToString());
	}
	else if (IS_OBJECT_PTR_VALID(EdtCoding) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtCoding->SetText(FText::FromString(PropertyData.Code));
	}
}

void UMultiCompPropertyWidget::OnTextCommittedEdtVisibleExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TArray<TPair<int32, FString>> Comments;
		const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
		if (!CleanExp.IsEmpty())
		{
			MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::VisibleExpression, Text.ToString());
		}
		else
		{
			EdtVisiExpression->SetText(FText::FromString(PropertyData.ComponentVisibility.Expression));
		}
	}
}

void UMultiCompPropertyWidget::OnClickedBtnVisibleExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtVisiExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EPropertyExpressionEditType::VisibleExpression, EdtVisiExpression->GetText().ToString(), FName(TEXT("MultiPropertyExpressionEdit")));
	}
}

void UMultiCompPropertyWidget::OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (Text.IsNumeric() && !Text.IsEmpty())
		{
			MultiCompPropertyEditDelegate.ExecuteIfBound(EComponentIDType::VisibleValue, Text.ToString());
		}
		else
		{
			EdtVisiValue->SetText(FText::FromString(PropertyData.ComponentVisibility.Value));
		}
	}
}

void UMultiCompPropertyWidget::OnClickedBtnAddParam()
{
	FParameterData NewData;
	UParameterDetailWidget* DetailWidget = UParameterDetailWidget::Get();
	UE_LOG(LogTemp, Log, TEXT("UMultiCompPropertyWidget::OnClickedBtnAddParam %d"), IS_OBJECT_PTR_VALID(DetailWidget));
	DetailWidget->SetFolderOrFileParentParams(GetOverrideParameters(TEXT("")));
	DetailWidget->UpdateContent(NewData, 2);
	//DetailWidget->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamAddHandler")));
	DetailWidget->SelectedParamUpdateDatasDelegate.BindUFunction(this, FName(TEXT("OnParamAddHandler02")));
	DetailWidget->SetVisibility(ESlateVisibility::Visible);
	UExternalWidget::Get()->AddContentWidget(DetailWidget);
	UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
}

void UMultiCompPropertyWidget::OnClickedBtnDelParam()
{
	if (!SelectItemPropertyWidget)
	{
		return;
	}
	bool Result = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
		, FText::FromStringTable(FName("PosSt"), TEXT("Make sure to delete this parameter ?")).ToString());
	if (Result)
	{
		MultiComponentParamEditDelegate.ExecuteIfBound(EParameterType::Del, SelectItemPropertyWidget->GetParamData());
	}
}
void UMultiCompPropertyWidget::OnClickBtnUPDown(const FParameterData& InData, bool bUp)
{
	BtnUpDownClickDelegate.ExecuteIfBound(InData, bUp);
}

void UMultiCompPropertyWidget::OnClickBtnUP()
{
	if (SelectItemPropertyWidget != nullptr)
	{
		OnClickBtnUPDown(SelectItemPropertyWidget->GetParamData(), true);
	}
}

void UMultiCompPropertyWidget::OnClickBtnDown()
{
	if (SelectItemPropertyWidget != nullptr)
	{
		OnClickBtnUPDown(SelectItemPropertyWidget->GetParamData(), false);
	}
}

#undef LOCTEXT_NAMESPACE
