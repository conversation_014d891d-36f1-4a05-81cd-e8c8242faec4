// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DesignStation/Geometry/DataDefines/MultiComponentDataObject.h"
#include "MultiCompItemPropertyWidget.h"
#include "MultiCompPosWidget.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "MultiCompPropertyWidget.generated.h"

/**
 *
 */

UENUM(BlueprintType)
enum class EComponentIDType : uint8
{
	Expression = 0,
	Value,
	Name,
	VisibleExpression,
	VisibleValue,
	Explain,
	Coding
};

UENUM(BlueprintType)
enum class EParameterType : uint8
{
	Add = 0,
	Del,
	Expression,
	Value,
	Modify
};

UENUM(BlueprintType)
enum class EPropertyExpressionEditType : uint8
{
	IdExpression = 0,
	VisibleExpression
};

class UTextBlock;
class UEditableText;
class UButton;
class UBorder;
class UScrollBox;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FMultiCompPropertyEditDelegate, const EComponentIDType&, EditType, const FString&, InString);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FMultiComponentParamDelegate, const EParameterType&, EditType, const FParameterData&, ParamData);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FMultiComponentParamAddDelegate, const EParameterType&, EditType, const TArray<FParameterData>&, ParamData);
DECLARE_DYNAMIC_DELEGATE(FGetParentParametersDelegate);

UCLASS()
class DESIGNSTATION_API UMultiCompPropertyWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	void UpdateContent(const FMultiComponentDataItem& ItemData);
	void SetParentParameters(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameter);
	void SelectParameterUI(const FParameterData& InParamData);
	void SwapParameterUI(const FParameterData& InParamA, const FParameterData& InParamB);
	static UMultiCompPropertyWidget* Create();

private:
	void UpdateLocationPropertyWidget(const FLocationProperty& InLocationData);
	void UpdateRotationPropertyWidget(const FRotationProperty& InRotationData);
	void UpdateScalePropertyWidget(const FScaleProperty& InScaleData);
	void UpdateComponentParameterWidget(const TArray<FParameterData>& InParamDatas);
	void UpdateInheritParameterWidget(const TArray<FParameterData>& InParamDatas, const TArray<FParameterData>& InComponentParams);
	//void UpdateParamPropertyWidget(bool IsNew, const FParameterData& InData);
	void UpdateParamEditButtonState(bool IsSelectItem);
	UFUNCTION()
		void ParamSelectEdit(UMultiCompItemPropertyWidget* ParamItem);
	UFUNCTION()
		void OnParamAddHandler(const FParameterData& ParamData);
		UFUNCTION()
		void OnParamAddHandler02(const TArray<FParameterData>& ParamDatas);
	UFUNCTION()
		void OnParamUpdateHandler(const FParameterData& ParamData);
	//UFUNCTION()
	//	void ParamDetailEdit(const EMultiComponentParamType& EditType, const FParameterData& ParamData);
	UFUNCTION()
		void MultiPropertyExpressionEdit(const int32& EditType, const FString& OutExpression);
	UFUNCTION()
		void MultiParamEdit(UMultiCompItemPropertyWidget* ParamItem, const EMultiParamType& EditType, const FParameterData& ParamData);
	UFUNCTION()
		void MultiItemLocationEdit(const EMultiCompPosType& EditType, const FLocationProperty& LocationData, const int32& InEditIndex);
	UFUNCTION()
		void MultiItemRotationEdit(const EMultiCompPosType& EditType, const FRotationProperty& RotationData, const int32& InEditIndex);
	UFUNCTION()
		void MultiItemScaleEdit(const EMultiCompPosType& EditType, const FScaleProperty& ScaleData, const int32& InEditIndex);


	//文件夹参数添加到节点参数中
	void AddParamToLocal(const FParameterData& ParamData);

public:
	FMultiCompPropertyEditDelegate MultiCompPropertyEditDelegate;
	FMultiComponentParamDelegate MultiComponentParamEditDelegate;
	FMultiComponentParamAddDelegate MultiComponentParamAddDelegate;
	FMultiCompLocationEditDelegate MultiCompLocationDelegate;
	FMultiCompRotationEditDelegate MultiCompRotationDelegate;
	FMultiCompScaleEditDelegate MultiCompScaleDelegate;

	FGetParentParametersDelegate MultiCompParentParametersDelegate;
	FMultiParamsUpDownDelegate BtnUpDownClickDelegate;
	//FParamForMultiComponentDelegate MultiComponentItemParamEditDelegate;

private:
	UPROPERTY()
		UMultiCompPosWidget* LocationWidget;
	UPROPERTY()
		UMultiCompPosWidget* RotationWidget;
	UPROPERTY()
		UMultiCompPosWidget* ScaleWidget;
	UPROPERTY()
		TArray<UMultiCompItemPropertyWidget*> ParamItemWidget;

	UPROPERTY()
		UMultiCompItemPropertyWidget* SelectItemPropertyWidget;

private:
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  MultiCompParentParameters;

	FMultiComponentDataItem PropertyData;

	static FString MultiCompPropertyPath;

protected:
	UFUNCTION()
		void OnTextCommittedEdtIdExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnIdExpression();
	UFUNCTION()
		void OnTextCommittedEdtIdValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtExpalin(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtVisibleExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisibleExpression();
	UFUNCTION()
		void OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnClickedBtnAddParam();
	UFUNCTION()
		void OnClickedBtnDelParam();

		UFUNCTION()
		void OnClickBtnUPDown(const FParameterData& InData,bool bUp);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetOverrideParameters(const FString& InParameterName) const;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetOverridelocalParameters(const FString& InParameterName) const;

	UFUNCTION()
	void OnClickBtnUP();

	UFUNCTION()
	void OnClickBtnDown();

private:
	UPROPERTY()
		UEditableText* EdtPartIdExpression;
	UPROPERTY()
		UButton* BtnPartIdExpression;
	UPROPERTY()
		UEditableText* EdtPartIdVale;

	UPROPERTY(BlueprintReadWrite, Category = "MultiCompProperty", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* TxtComponentName;

	UPROPERTY()
	UTextBlock* EdtExplain;

	UPROPERTY(BlueprintReadWrite, Category = "MultiCompProperty", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* EdtCoding;
	UPROPERTY(BlueprintReadWrite, Category = "MultiCompProperty", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* EdtCodingExp;

	UPROPERTY()
		UEditableText* EdtVisiExpression;
	UPROPERTY()
		UButton* BtnVisiExpression;
	UPROPERTY()
		UEditableText* EdtVisiValue;

	UPROPERTY()
		UScrollBox* SCBTransform;

	UPROPERTY()
		UButton* BtnAddParam;
	UPROPERTY()
		UButton* BtnDelParam;
	UPROPERTY()
		UScrollBox* SCBParams;
		UPROPERTY(meta = (BindWidget))
		UButton* Btn_Up;

		UPROPERTY(meta = (BindWidget))
		UButton* Btn_Down;
};
