// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompToolBarWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"

FString UMultiCompToolBarWidget::MultiCompToolBarPath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiCompToolBarUI.MultiCompToolBarUI_C'");

bool UMultiCompToolBarWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	
	return true;
}

void UMultiCompToolBarWidget::NativeOnInitialized()
{
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UMultiCompToolBarWidget::OnClickedBtnUp);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UMultiCompToolBarWidget::OnClickedBtnDown);
	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, UMultiCompToolBarWidget::OnClickedBtnSave);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, UMultiCompToolBarWidget::OnClickedBtnExit);

	BIND_WIDGET_FUNCTION(BtnRecover, OnClicked, UMultiCompToolBarWidget::OnClickedBtnRecover);


	BtnUp->SetIsEnabled(false);
	BtnDown->SetIsEnabled(false);
}

UMultiCompToolBarWidget * UMultiCompToolBarWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiCompToolBarWidget>(UMultiCompToolBarWidget::MultiCompToolBarPath);
}

void UMultiCompToolBarWidget::SetUnSelectButton(const int32 & state)
{
	switch (state)
	{
	case -1:
		BtnUp->SetIsEnabled(false);
		BtnDown->SetIsEnabled(false);
		break;
	case 0:
		BtnUp->SetIsEnabled(false);
		BtnDown->SetIsEnabled(true);
		break;
	case 1:
		BtnUp->SetIsEnabled(true);
		BtnDown->SetIsEnabled(false);
		break;
	default:
		BtnUp->SetIsEnabled(true);
		BtnDown->SetIsEnabled(true);
		break;
	}
}

void UMultiCompToolBarWidget::OnClickedBtnUp()
{
	MultiCompToolBarEditDelegate.ExecuteIfBound(EMultiCompToolBarType::Up);
}

void UMultiCompToolBarWidget::OnClickedBtnDown()
{
	MultiCompToolBarEditDelegate.ExecuteIfBound(EMultiCompToolBarType::Down);
}

void UMultiCompToolBarWidget::OnClickedBtnSave()
{
	MultiCompToolBarEditDelegate.ExecuteIfBound(EMultiCompToolBarType::Save);
}

void UMultiCompToolBarWidget::OnClickedBtnExit()
{
	MultiCompToolBarEditDelegate.ExecuteIfBound(EMultiCompToolBarType::Exit);
}

void UMultiCompToolBarWidget::OnClickedBtnRecover()
{
	MultiCompToolBarEditDelegate.ExecuteIfBound(EMultiCompToolBarType::Recover);
}
