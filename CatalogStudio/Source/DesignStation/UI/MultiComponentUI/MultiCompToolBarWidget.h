// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "MultiCompToolBarWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMultiCompToolBarType : uint8
{
	Save = 0,
	Exit,
	Up,
	Down,
	Recover
};

class UButton;

DECLARE_DYNAMIC_DELEGATE_OneParam(FMultiCompToolBarEditDelegate, const EMultiCompToolBarType&, EditType);

UCLASS()
class DESIGNSTATION_API UMultiCompToolBarWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	static UMultiCompToolBarWidget* Create();

public:
	FMultiCompToolBarEditDelegate MultiCompToolBarEditDelegate;

private:
	static FString MultiCompToolBarPath;

public:
	void SetUnSelectButton(const int32& state);

protected:
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();
	UFUNCTION()
		void OnClickedBtnSave();
	UFUNCTION()
		void OnClickedBtnExit();

	UFUNCTION()
		void OnClickedBtnRecover();
	
private:
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;
	UPROPERTY()
		UButton* BtnSave;
	UPROPERTY()
		UButton* BtnExit;

	UPROPERTY(meta=(BindWidget=true))
		UButton* BtnRecover;
};
