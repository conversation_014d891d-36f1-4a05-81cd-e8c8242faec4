// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompTreeItemBaseWidget.h"

#include "Components/HorizontalBoxSlot.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "HAL/PlatformApplicationMisc.h"

bool UMultiCompTreeItemBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void UMultiCompTreeItemBaseWidget::SetIsSelect(bool _IsSelect)
{
	IsSelected = _IsSelect;
	if (BorBackground)
	{
		UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? TreeItemSelect : TreeItemNormal, BorBackground);
		UUIFunctionLibrary::SetTextColor(_IsSelect ? TreeTextSelect : TreeTextNormal, TxtName);
	}
}

void UMultiCompTreeItemBaseWidget::UpdateName(const FString& InName)
{
	if (IS_OBJECT_PTR_VALID(TxtName))
	{
		TxtName->SetText(FText::FromString(InName));
	}
}

void UMultiCompTreeItemBaseWidget::UpdateContent(const int32 & InItemIndex, const FString & InID, const FString & InName)
{
	ItemIndex = InItemIndex;
	ItemID = InID;
	ItemName = InName;
	int32 DotIndex = INDEX_NONE;
	InID.FindChar('.', DotIndex);
	if (IS_OBJECT_PTR_VALID(TxtName))
	{
		if (DotIndex == INDEX_NONE)
		{
			TxtName->SetText(FText::FromString(InID + TEXT("    ") + InName));
		}
		else
		{
			TxtName->SetText(FText::FromString(InID.Left(DotIndex) + TEXT("    ") + InName));
		}
	}
}

void UMultiCompTreeItemBaseWidget::SetIndent()
{
	if (IS_OBJECT_PTR_VALID(SZIndent))
	{
		UHorizontalBoxSlot* IndentSlot = UWidgetLayoutLibrary::SlotAsHorizontalBoxSlot(SZIndent);
		IndentSlot->SetPadding(FMargin(19.0f, 0.0f, 0.0f, 0.0f));
	}
}

void UMultiCompTreeItemBaseWidget::SetIndent(const int32 & InLevel)
{
	if (IS_OBJECT_PTR_VALID(SZIndent))
	{
		UHorizontalBoxSlot* IndentSlot = UWidgetLayoutLibrary::SlotAsHorizontalBoxSlot(SZIndent);
		IndentSlot->SetPadding(FMargin(19.0 + 10.0 * InLevel, 0.0f, 0.0f, 0.0f));
	}
}

FReply UMultiCompTreeItemBaseWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}

void UMultiCompTreeItemBaseWidget::OnClicekdBtnCopyID()
{
	FPlatformApplicationMisc::ClipboardCopy(*ItemID);
}
