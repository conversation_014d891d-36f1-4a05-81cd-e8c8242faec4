// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "MultiCompTreeItemBaseWidget.generated.h"

/**
 * 
 */

const FLinearColor TreeItemNormal = FLinearColor(0.913099f, 0.913099f, 0.913099f, 1.0f);;
const FLinearColor TreeItemSelect = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);;
const FLinearColor TreeTextNormal = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);;
const FLinearColor TreeTextSelect = FLinearColor::White;;

class UMultiCompTreeMultiItemWidget;

DECLARE_DYNAMIC_DELEGATE_OneParam(FTreeItemSelectDelegate, const int32&, ItemID);

UCLASS()
class DESIGNSTATION_API UMultiCompTreeItemBaseWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetIsSelect(bool _IsSelect);
	virtual void UpdateName(const FString& InName);
	virtual void UpdateContent(const int32& InItemIndex, const FString& InID, const FString& InName);
	FORCEINLINE int32 GetIndex() { return ItemIndex; }
	void SetIndent();
	void SetIndent(const int32& InLevel);

public:
	FTreeItemSelectDelegate TreeSingleItemSelectDelegate;

public:
	int32 ItemIndex;
	FString ItemID;
	FString ItemName;

	TWeakObjectPtr<UMultiCompTreeMultiItemWidget> ParentWidget;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

	UFUNCTION()
	void OnClicekdBtnCopyID();
public:
	bool IsSelected;
	
public:
	UPROPERTY()
		USizeBox* SZIndent;
	UPROPERTY()
		UButton* BtnSelect;
	UPROPERTY()
		UBorder* BorBackground;
	UPROPERTY()
		UImage* ImgLevel;
	UPROPERTY()
		UTextBlock* TxtName;

		UPROPERTY(meta = (BindWidget))
		UButton* Btn_CopyID;
};
