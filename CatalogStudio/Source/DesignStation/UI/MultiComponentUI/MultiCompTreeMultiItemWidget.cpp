// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompTreeMultiItemWidget.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "MultiComponentTreeWidget.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UMultiCompTreeMultiItemWidget::MultiCompTreeMultiItemPath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiCompTreeMultiItemUI.MultiCompTreeMultiItemUI_C'");


void UMultiCompTreeMultiItemWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(SZIndent, SZ_Indent);
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UMultiCompTreeMultiItemWidget::OnClicekdBtnSelect);
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(ImgLevel, Img_Level);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_ComponentName);
	BIND_PARAM_CPP_TO_UMG(CkbExpand, Ckb_Expand);
	BIND_WIDGET_FUNCTION(CkbExpand, OnCheckStateChanged, UMultiCompTreeMultiItemWidget::OnStateChangedCkbExpand);
	BIND_PARAM_CPP_TO_UMG(ImgExpand, Img_Expand);
	BIND_PARAM_CPP_TO_UMG(ScbChild, Scb_Child);
	BIND_WIDGET_FUNCTION(Btn_CopyID, OnClicked, UMultiCompTreeMultiItemWidget::OnClicekdBtnCopyID);
}

void UMultiCompTreeMultiItemWidget::SetIsSelect(bool _IsSelect)
{
	Super::SetIsSelect(_IsSelect);
	SetExpandImageBrush(_IsSelect ? (CkbExpand->IsChecked() ? EMultiItemStateType::CheckSelect : EMultiItemStateType::UnCheckSelect)
		: (CkbExpand->IsChecked() ? EMultiItemStateType::CheckUnSelect : EMultiItemStateType::UnCheckUnSelect));
}

void UMultiCompTreeMultiItemWidget::MultiItemExpandChild(bool IsExpand)
{
	if (IS_OBJECT_PTR_VALID(CkbExpand))
	{
		CkbExpand->SetIsChecked(IsExpand);
	}
	SetIsSelect(IsExpand);
	OnStateChangedCkbExpand(IsExpand);
}

void UMultiCompTreeMultiItemWidget::SetChildExpand(bool IsExpand)
{
	if (IS_OBJECT_PTR_VALID(CkbExpand))
	{
		CkbExpand->SetIsChecked(IsExpand);
	}
	OnStateChangedCkbExpand(IsExpand);
}

void UMultiCompTreeMultiItemWidget::UpdateName(const FString& InName)
{
	Super::UpdateName(InName);
}

void UMultiCompTreeMultiItemWidget::GenerateSubChild(const FMultiComponentDataItem& InData, UMultiCompTreeMultiItemWidget* ParentWidget)
{
	if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		ScbChild->ClearChildren();
	}

	if (InData.ComponentType == ECompType::ImportModel)
	{
		UMultiCompTreeSingleItemWidget* ChildItem = UMultiCompTreeSingleItemWidget::Create();
		ChildItem->UpdateContent(InData.ID, InData.ComponentID.Value, InData.ComponentName);
		ChildItem->ParentWidget = ParentWidget;
		ChildItem->TreeSingleItemSelectDelegate.BindUFunction(UMultiComponentTreeWidget::Get(), FName(TEXT("OnTreeItemSelectHandler")));
		ChildItem->SetIndent(ParentWidget->GetIndentLevel() + 1);
		ChildItem->SetVisibility(ESlateVisibility::Visible);
		TreeItemsMap.Add(InData.ID, ChildItem);
		ScbChild->AddChild(ChildItem);
		return;
	}

	for (auto& ItemData : InData.ChildComponent)
	{
		//UE_LOG(LogTemp, Log, TEXT("multiwidget---component id : %s"), *ItemData.ComponentID.Value);
		if (ItemData.ComponentType == ECompType::None || ItemData.ComponentType == ECompType::SingleCom)
		{
			UMultiCompTreeSingleItemWidget* ChildItem = UMultiCompTreeSingleItemWidget::Create();
			ChildItem->UpdateContent(ItemData.ID, ItemData.ComponentID.Value, ItemData.ComponentName);
			ChildItem->ParentWidget = ParentWidget;
			ChildItem->TreeSingleItemSelectDelegate.BindUFunction(UMultiComponentTreeWidget::Get(), FName(TEXT("OnTreeItemSelectHandler")));
			ChildItem->SetIndent(ParentWidget->GetIndentLevel() + 1);
			ChildItem->SetVisibility(ESlateVisibility::Visible);
			TreeItemsMap.Add(ItemData.ID, ChildItem);
			ScbChild->AddChild(ChildItem);
		}
		else if (ItemData.ComponentType == ECompType::MultiCom || ItemData.ComponentType == ECompType::ImportModel)
		{
			UMultiCompTreeMultiItemWidget* ChildItem = UMultiCompTreeMultiItemWidget::Create();
			ChildItem->UpdateContent(ItemData.ID, ItemData.ComponentID.Value, ItemData.ComponentName);
			ChildItem->SetKeyID(ItemData.KeyID);
			ChildItem->ParentWidget = ParentWidget;
			ChildItem->SetChildExpand(UMultiComponentTreeWidget::Get()->GetMultiExpandMap().FindRef(ItemData.KeyID));
			ChildItem->TreeSingleItemSelectDelegate.BindUFunction(UMultiComponentTreeWidget::Get(), FName(TEXT("OnTreeItemSelectHandler")));
			ChildItem->TreeMultiItemExpandChangeDelegate.BindUFunction(UMultiComponentTreeWidget::Get(), FName(TEXT("OnMultiItemExpandStateChangeHandler")));
			ChildItem->SetIndent(ParentWidget->GetIndentLevel() + 1);
			ChildItem->SetIndentLevel(ParentWidget->GetIndentLevel() + 1);
			ChildItem->SetVisibility(ESlateVisibility::Visible);
			TreeItemsMap.Add(ItemData.ID, ChildItem);
			ScbChild->AddChild(ChildItem);
			GenerateSubChildJudge(ItemData, ChildItem);
		}
	}
}

UMultiCompTreeMultiItemWidget* UMultiCompTreeMultiItemWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiCompTreeMultiItemWidget>(UMultiCompTreeMultiItemWidget::MultiCompTreeMultiItemPath);
}

void UMultiCompTreeMultiItemWidget::SetExpandImageBrush(const EMultiItemStateType& InType)
{
	if (!IS_OBJECT_PTR_VALID(ImgCheckSelect) && !IS_OBJECT_PTR_VALID(ImgCheckUnSelect)
		&& !IS_OBJECT_PTR_VALID(ImgUnCheckSelect) && !IS_OBJECT_PTR_VALID(ImgUnCheckUnSelect))
	{
		ImgCheckSelect = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/MultiComponent/open_chose.open_chose'")));
		ImgCheckUnSelect = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/MultiComponent/open.open'")));
		ImgUnCheckSelect = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/MultiComponent/close_chose.close_chose'")));
		ImgUnCheckUnSelect = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/MultiComponent/close.close'")));
	}
	if (InType == EMultiItemStateType::CheckSelect)
	{
		ImgExpand->SetBrushFromTexture(ImgCheckSelect);
	}
	else if (InType == EMultiItemStateType::CheckUnSelect)
	{
		ImgExpand->SetBrushFromTexture(ImgCheckUnSelect);
	}
	else if (InType == EMultiItemStateType::UnCheckSelect)
	{
		ImgExpand->SetBrushFromTexture(ImgUnCheckSelect);
	}
	else if (InType == EMultiItemStateType::UnCheckUnSelect)
	{
		ImgExpand->SetBrushFromTexture(ImgUnCheckUnSelect);
	}
}

void UMultiCompTreeMultiItemWidget::GenerateSubChildJudge(const FMultiComponentDataItem& InData, UMultiCompTreeMultiItemWidget* ParentWidget)
{
	if (InData.ChildComponent.Num() == 0)
	{
		return;
	}
	else
	{
		ParentWidget->GenerateSubChild(InData, ParentWidget);
	}
}

FReply UMultiCompTreeMultiItemWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	return FReply::Unhandled();
}

void UMultiCompTreeMultiItemWidget::OnClicekdBtnSelect()
{
	if (IS_OBJECT_PTR_VALID(ParentWidget.Get()))
		return;
	MultiItemExpandChild(true);
	TreeSingleItemSelectDelegate.ExecuteIfBound(ItemIndex);
}

void UMultiCompTreeMultiItemWidget::OnStateChangedCkbExpand(bool IsChecked)
{
	TreeMultiItemExpandChangeDelegate.ExecuteIfBound(KeyId, IsChecked);
	SetExpandImageBrush(IsSelected ? (IsChecked ? EMultiItemStateType::CheckSelect : EMultiItemStateType::UnCheckSelect)
		: (IsChecked ? EMultiItemStateType::CheckUnSelect : EMultiItemStateType::UnCheckUnSelect));
	if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		ScbChild->SetVisibility(IsChecked ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}
