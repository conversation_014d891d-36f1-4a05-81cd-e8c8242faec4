// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MultiCompTreeSingleItemWidget.h"
#include "DataCenter/ComponentData/MultiComponentData.h"
#include "MultiCompTreeMultiItemWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EMultiItemStateType : uint8
{
	CheckSelect = 0,
	CheckUnSelect,
	UnCheckSelect,
	UnCheckUnSelect
};

class UCheckBox;
class UScrollBox;

DECLARE_DYNAMIC_DELEGATE_OneParam(FTreeMultiItemSelectDelegate, const int32&, ItemID);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FTreeMultiItemExpandChangeDelegate, const FString&, KeyID, bool, IsExpand);

UCLASS()
class DESIGNSTATION_API UMultiCompTreeMultiItemWidget : public UMultiCompTreeItemBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual void NativeOnInitialized() override;
	virtual void SetIsSelect(bool _IsSelect) override;
	void MultiItemExpandChild(bool IsExpand);
	void SetChildExpand(bool IsExpand);
	virtual void UpdateName(const FString& InName) override;

	FORCEINLINE void SetKeyID(const FString& InKeyID) { KeyId = InKeyID; }
	FORCEINLINE void SetIndentLevel(const int32& InLevel) { IndentLevel = InLevel; }
	FORCEINLINE int32 GetIndentLevel() const { return IndentLevel; }

	void GenerateSubChild(const FMultiComponentDataItem& InData, UMultiCompTreeMultiItemWidget* ParentWidget);

	static UMultiCompTreeMultiItemWidget* Create();

private:
	void SetExpandImageBrush(const EMultiItemStateType& InType);
	void GenerateSubChildJudge(const FMultiComponentDataItem& InData, UMultiCompTreeMultiItemWidget* ParentWidget);

public:
	FTreeMultiItemSelectDelegate TreeMultiItemSelectDelegate;
	FTreeMultiItemExpandChangeDelegate TreeMultiItemExpandChangeDelegate;

private:
	static FString MultiCompTreeMultiItemPath;

	UPROPERTY()
		TMap<int32, UMultiCompTreeItemBaseWidget*> TreeItemsMap;

	UPROPERTY()
		FString KeyId;
	UPROPERTY()
		int32 IndentLevel;

	UPROPERTY()
		UTexture2D* ImgCheckSelect;
	UPROPERTY()
		UTexture2D* ImgCheckUnSelect;
	UPROPERTY()
		UTexture2D* ImgUnCheckSelect;
	UPROPERTY()
		UTexture2D* ImgUnCheckUnSelect;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnClicekdBtnSelect();
	UFUNCTION()
		void OnStateChangedCkbExpand(bool IsChecked);
	
private:
	UPROPERTY()
		UCheckBox* CkbExpand;
	UPROPERTY()
		UImage* ImgExpand;
	UPROPERTY()
		UScrollBox* ScbChild;
};
