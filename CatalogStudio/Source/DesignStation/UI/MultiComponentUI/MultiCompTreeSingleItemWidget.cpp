// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiCompTreeSingleItemWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"

FString UMultiCompTreeSingleItemWidget::MultiCompTreeSingleItemPath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiCompTreeSingleItemUI.MultiCompTreeSingleItemUI_C'");

void UMultiCompTreeSingleItemWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(SZIndent, SZ_Indent);
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UMultiCompTreeSingleItemWidget::OnClicekdBtnSelect);
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(ImgLevel, Img_Level);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_ComponentName);
	BIND_WIDGET_FUNCTION(Btn_CopyID, OnClicked, UMultiCompTreeSingleItemWidget::OnClicekdBtnCopyID);
}

void UMultiCompTreeSingleItemWidget::SetIsSelect(bool _IsSelect)
{
	Super::SetIsSelect(_IsSelect);
}

//void UMultiCompTreeSingleItemWidget::UpdateContent(const int32& InItemIndex, const FString& InID, const FString& Description)
//{
//	ItemIndex = InItemIndex;
//	ItemID = InID;
//	ItemDescription = Description;
//	int32 DotIndex = INDEX_NONE;
//	InID.FindChar('.', DotIndex);
//	if (IS_OBJECT_PTR_VALID(TxtName))
//	{
//		if (DotIndex == INDEX_NONE)
//		{
//			TxtName->SetText(FText::FromString(InID + TEXT("    ") + Description));
//		}
//		else
//		{
//			TxtName->SetText(FText::FromString(InID.Left(DotIndex) + TEXT("    ") + Description));
//		}
//	}
//}

void UMultiCompTreeSingleItemWidget::UpdateName(const FString & InName)
{
	Super::UpdateName(InName);
}

UMultiCompTreeSingleItemWidget * UMultiCompTreeSingleItemWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiCompTreeSingleItemWidget>(UMultiCompTreeSingleItemWidget::MultiCompTreeSingleItemPath);
}

FReply UMultiCompTreeSingleItemWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}

void UMultiCompTreeSingleItemWidget::OnClicekdBtnSelect()
{
	TreeSingleItemSelectDelegate.ExecuteIfBound(ItemIndex);
}
