// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MultiCompTreeItemBaseWidget.h"
#include "MultiCompTreeSingleItemWidget.generated.h"

/**
 * 
 */

UCLASS()
class DESIGNSTATION_API UMultiCompTreeSingleItemWidget : public UMultiCompTreeItemBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual void NativeOnInitialized() override;
	virtual void SetIsSelect(bool _IsSelect) override;
	virtual void UpdateName(const FString& InName) override;

	//void UpdateContent(const int32& InItemIndex, const FString& InID, const FString& Description);
	
	static UMultiCompTreeSingleItemWidget* Create();

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnClicekdBtnSelect();

private:
	static FString MultiCompTreeSingleItemPath;
};
