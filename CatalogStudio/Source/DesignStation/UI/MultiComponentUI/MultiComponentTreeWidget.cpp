// Fill out your copyright notice in the Description page of Project Settings.

#include "MultiComponentTreeWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/Button.h"

#define LOCTEXT_NAMESPACE "MultiComponentTree"

FString UMultiComponentTreeWidget::MultiComponentTreePath = TEXT("WidgetBlueprint'/Game/UI/MultiComponentUI/MultiComponentTreeUI.MultiComponentTreeUI_C'");
UMultiComponentTreeWidget* UMultiComponentTreeWidget::MultiCompTreeInstance = nullptr;

bool UMultiComponentTreeWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(SCBComponent, SCB_Component);
	BIND_PARAM_CPP_TO_UMG(BtnAdd, Btn_Add);
	BIND_WIDGET_FUNCTION(BtnAdd, OnClicked, UMultiComponentTreeWidget::OnClickedBtnAdd);
	BIND_PARAM_CPP_TO_UMG(BtnCopy, Btn_Copy);
	BIND_WIDGET_FUNCTION(BtnCopy, OnClicked, UMultiComponentTreeWidget::OnClickedBtnCopy);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UMultiComponentTreeWidget::OnClickedBtnDelete);

	SelectID = -1;
	return true;
}

//void UMultiComponentTreeWidget::UpdateContent(const TArray<FMultiComponentPropertyItem>& InTreeItems, const int32& SelectTreeItemID)
//{
//	if (IS_OBJECT_PTR_VALID(SCBComponent))
//	{
//		SCBComponent->ClearChildren();
//		TreeItemsMap.Empty();
//
//		for (auto& ItemData : InTreeItems)
//		{
//			UMultiCompTreeSingleItemWidget* TreeNode = UMultiCompTreeSingleItemWidget::Create();
//			TreeNode->UpdateContent(ItemData.ID, ItemData.SingleComponentID.Value,ItemData.Description);
//			TreeNode->TreeSingleItemSelectDelegate.BindUFunction(this, FName(TEXT("OnTreeItemSelectHandler")));
//			TreeNode->SetVisibility(ESlateVisibility::Visible);
//			SCBComponent->AddChild(TreeNode);
//			TreeItemsMap.Add(ItemData.ID, TreeNode);
//		}
//	}
//	if (SelectTreeItemID != -1)
//	{
//		SelectID = SelectTreeItemID;
//		TreeItemsMap[SelectTreeItemID]->SetIsSelect(true);
//	}
//	UpdateTreeActionButtonState(SelectTreeItemID == -1 ? false : true);
//	SetItemState();
//}

void UMultiComponentTreeWidget::UpdateContent(const TArray<FMultiComponentDataItem>& InTreeItems, const int32 & SelectTreeItemID)
{
	if (IS_OBJECT_PTR_VALID(SCBComponent))
	{
		SCBComponent->ClearChildren();
		TreeItemsMap.Empty();

		for (int32 i = 0; i < InTreeItems.Num(); ++i)
		{
			const auto ItemData = InTreeItems[i];
			UE_LOG(LogTemp, Log, TEXT("tree widget----component id : %s"), *ItemData.ComponentID.Value);
			if (ItemData.ComponentType == ECompType::SingleCom /*|| ItemData.ComponentType == ECompType::ImportModel*/)
			{
				UMultiCompTreeSingleItemWidget* TreeNode = UMultiCompTreeSingleItemWidget::Create();
				TreeNode->UpdateContent(ItemData.ID, ItemData.ComponentID.Value, ItemData.ComponentName);
				TreeNode->TreeSingleItemSelectDelegate.BindUFunction(this, FName(TEXT("OnTreeItemSelectHandler")));
				TreeNode->SetVisibility(ESlateVisibility::Visible);
				SCBComponent->AddChild(TreeNode);
				TreeItemsMap.Add(ItemData.ID, TreeNode);
			}
			else if(ItemData.ComponentType == ECompType::None 
				|| ItemData.ComponentType == ECompType::MultiCom 
				|| ItemData.ComponentType == ECompType::ImportModel
				)
			{
				UMultiCompTreeMultiItemWidget* TreeNode = UMultiCompTreeMultiItemWidget::Create();
				TreeNode->UpdateContent(ItemData.ID, ItemData.ComponentID.Value, ItemData.ComponentName);
				TreeNode->SetKeyID(ItemData.KeyID);
				TreeNode->GenerateSubChild(ItemData, TreeNode);
				TreeNode->SetChildExpand(i == SelectTreeItemID);
				//TreeNode->SetChildExpand(MultiExpandFlag.FindRef(ItemData.KeyID));
				TreeNode->TreeSingleItemSelectDelegate.BindUFunction(this, FName(TEXT("OnTreeItemSelectHandler")));
				TreeNode->TreeMultiItemExpandChangeDelegate.BindUFunction(this, FName(TEXT("OnMultiItemExpandStateChangeHandler")));
				TreeNode->SetVisibility(ESlateVisibility::Visible);
				SCBComponent->AddChild(TreeNode);
				TreeItemsMap.Add(ItemData.ID, TreeNode);
				//TreeNode->GenerateSubChild(ItemData, TreeNode);
			}
		}
	}
	if (SelectTreeItemID != -1)
	{
		SelectID = SelectTreeItemID;
		TreeItemsMap[SelectTreeItemID]->SetIsSelect(true);
	}
	UpdateTreeActionButtonState(SelectTreeItemID != -1);
	SetItemState();
}

void UMultiComponentTreeWidget::SyncSelectItem(const FString& InNameString)
{
	if (TreeItemsMap.Find(SelectID))
	{
		TreeItemsMap[SelectID]->UpdateName(InNameString);
	}
}

void UMultiComponentTreeWidget::SyncWidgetForMultiComponent(const FMultiComponentDataItem & InData)
{
	TArray<UMultiCompTreeItemBaseWidget*> TreeItemArray;
	TreeItemsMap.GenerateValueArray(TreeItemArray);
	int32 CurrentIndex = -1;
	//TreeItemArray.Find(TreeItemsMap[SelectID], CurrentIndex);

	for (int32 i = 0; i < TreeItemArray.Num(); ++i)
	{
		if (TreeItemArray[i]->GetIndex() == InData.ID)
		{
			CurrentIndex = i;
			break;
		}
	}

	if (CurrentIndex < 0)
	{
		return;
	}

	UMultiCompTreeItemBaseWidget* TreeNode;
	if (ECompType::MultiCom != InData.ComponentType && ECompType::ImportModel != InData.ComponentType)
	{
		TreeNode = UMultiCompTreeSingleItemWidget::Create();
	}
	else
	{
		TreeNode = UMultiCompTreeMultiItemWidget::Create();
		Cast<UMultiCompTreeMultiItemWidget>(TreeNode)->GenerateSubChild(InData, Cast<UMultiCompTreeMultiItemWidget>(TreeNode));
	}
	TreeNode->UpdateContent(InData.ID, InData.ComponentID.Value, InData.ComponentName);
	TreeNode->TreeSingleItemSelectDelegate.BindUFunction(this, FName(TEXT("OnTreeItemSelectHandler")));
	TreeNode->SetVisibility(ESlateVisibility::Visible);

	TreeItemArray.Remove(TreeItemsMap[CurrentIndex]);
	TreeItemArray.Insert(TreeNode, CurrentIndex);
	TreeItemsMap.Empty();
	SCBComponent->ClearChildren();
	for (auto& DataItem : TreeItemArray)
	{
		TreeItemsMap.Add(DataItem->GetIndex(), DataItem);
		SCBComponent->AddChild(DataItem);
	}
	OnTreeItemSelectHandler(TreeNode->GetIndex());
}

void UMultiComponentTreeWidget::SetItemSelectedByIndex(const int32& InIndex)
{
	OnTreeItemSelectHandler(InIndex);
}

void UMultiComponentTreeWidget::ClearData()
{
	SelectID = -1;
	MultiExpandFlag.Empty();
}

UMultiComponentTreeWidget * UMultiComponentTreeWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UMultiComponentTreeWidget>(UMultiComponentTreeWidget::MultiComponentTreePath);
}

UMultiComponentTreeWidget * UMultiComponentTreeWidget::Get()
{
	if (!IS_OBJECT_PTR_VALID(UMultiComponentTreeWidget::MultiCompTreeInstance))
	{
		UMultiComponentTreeWidget::MultiCompTreeInstance = UMultiComponentTreeWidget::Create();
		UMultiComponentTreeWidget::MultiCompTreeInstance->SetVisibility(ESlateVisibility::Visible);
		UMultiComponentTreeWidget::MultiCompTreeInstance->AddToRoot();
	}
	return UMultiComponentTreeWidget::MultiCompTreeInstance;
}

void UMultiComponentTreeWidget::UpdateTreeActionButtonState(bool IsItemSelect)
{
	if (IS_OBJECT_PTR_VALID(BtnCopy) && IS_OBJECT_PTR_VALID(BtnDelete))
	{
		BtnCopy->SetIsEnabled(IsItemSelect);
		BtnDelete->SetIsEnabled(IsItemSelect);
	}
}

void UMultiComponentTreeWidget::OnTreeItemSelectHandler(const int32 & ItemID)
{
	if (ItemID == -1)
	{
		return;
	}
	if (SelectID != -1)
	{
		if (TreeItemsMap.Find(SelectID))
		{
			TreeItemsMap[SelectID]->SetIsSelect(false);
		}
	}

	if (TreeItemsMap.Find(ItemID))
	{
		UpdateTreeActionButtonState(true);
		TreeItemsMap[ItemID]->SetIsSelect(true);
		SelectID = ItemID;
		TreeActionDelegate.ExecuteIfBound((int32)EComponentTreeEditType::Select, ItemID);
	}
	SetItemState();

}

void UMultiComponentTreeWidget::OnMultiItemExpandStateChangeHandler(const FString & InKeyId, bool IsExpand)
{
	MultiExpandFlag.FindOrAdd(InKeyId) = IsExpand;
}

void UMultiComponentTreeWidget::OnClickedBtnAdd()
{
	TreeActionDelegate.ExecuteIfBound((int32)EComponentTreeEditType::Add, -1);
}

void UMultiComponentTreeWidget::OnClickedBtnCopy()
{
	TreeActionDelegate.ExecuteIfBound((int32)EComponentTreeEditType::Copy, SelectID);
}

void UMultiComponentTreeWidget::OnClickedBtnDelete()
{
	bool Result = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
		, FText::FromStringTable(FName("PosSt"), TEXT("Make sure to delete component ?")).ToString());
	if (Result)
	{
		TreeActionDelegate.ExecuteIfBound((int32)EComponentTreeEditType::Delete, SelectID);
	}
}

void UMultiComponentTreeWidget::SetItemState()
{
	if (SelectID == -1 || TreeItemsMap.Num() == 1)
	{
		TreeSelectDelegate.ExecuteIfBound(-1);
	}
	else if (SelectID == 0)
	{
		TreeSelectDelegate.ExecuteIfBound(0);
	}
	else if (TreeItemsMap.Num() == SelectID + 1)
	{
		TreeSelectDelegate.ExecuteIfBound(1);
	}
	else
	{
		TreeSelectDelegate.ExecuteIfBound(3);
	}

}

#undef LOCTEXT_NAMESPACE
