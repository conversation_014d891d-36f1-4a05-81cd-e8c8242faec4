// Fill out your copyright notice in the Description page of Project Settings.

#include "AssociateDirInnerWidget.h"

#include "AssociateDirectoryWidget.h"
#include "Components/Button.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"


FString UAssociateDirInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Obsurce/BP_AssociateDirInnerWidget.BP_AssociateDirInnerWidget_C'");

bool UAssociateDirInnerWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Btn_Select, OnClicked, UAssociateDirInnerWidget::OnClickBtnSelect);

	return true;
}

void UAssociateDirInnerWidget::UpdateContent(const FAssociateCategoryListData& InData)
{
	CurData = InData;

	if(Txt_FolderName)
	{
		Txt_FolderName->SetText(FText::FromString(InData.name));
		Txt_FolderName->SetToolTipText(FText::FromString(InData.name));
	}
}

float UAssociateDirInnerWidget::GetExtent()
{
	if(SZ_Indent)
	{
		return SZ_Indent->GetWidthOverride();
	}

	return 0.0f;
}

void UAssociateDirInnerWidget::SetExtent(float NewExtent)
{
	if(SZ_Indent)
	{
		SZ_Indent->SetWidthOverride(NewExtent);
	}
}

void UAssociateDirInnerWidget::SetSelect(bool IsSelect)
{
	bSelect = IsSelect;
	SetSelectBP(IsSelect);
}

void UAssociateDirInnerWidget::GenerateChildWidget(const TArray<FAssociateCategoryListData>& InData, float LeftExtend)
{
	ChildWidget.Empty();
	Scb_Child->ClearChildren();

	for(auto& ACLD : InData)
	{
		UAssociateDirInnerWidget* Temp = UAssociateDirInnerWidget::Create();
		Temp->UpdateContent(ACLD);
		Temp->SetExtent(GetExtent() + 10.0f);
		Temp->OnDirectorySelectDelegate.BindUFunction(UAssociateDirectoryWidget::GetInstance(), FName(TEXT("OnDirectorySelectHandler")));
		Temp->SetVisibility(ESlateVisibility::Visible);

		ChildWidget.Add(Temp);
		Scb_Child->AddChild(Temp);
	}
	Scb_Child->SetVisibility(InData.Num() > 0 ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
}

bool UAssociateDirInnerWidget::ChildHasSelect()
{
	return false;
}

UAssociateDirInnerWidget* UAssociateDirInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UAssociateDirInnerWidget>(UAssociateDirInnerWidget::FilePath);
}

void UAssociateDirInnerWidget::OnClickBtnSelect()
{
	OnDirectorySelectDelegate.ExecuteIfBound(this);
}
