// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/ScrollBox.h"
#include "Components/SizeBox.h"
#include "Components/TextBlock.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "AssociateDirInnerWidget.generated.h"

class UButton;
class UAssociateDirectoryWidget;
class UAssociateDirInnerWidget;

DECLARE_DELEGATE_OneParam(FDirectorySelectDelegate, UAssociateDirInnerWidget*);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UAssociateDirInnerWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void UpdateContent(const FAssociateCategoryListData& InData);

	FAssociateCategoryListData GetCurData() { return CurData; }

	float GetExtent();
	void SetExtent(float NewExtent);

	void SetSelect(bool IsSelect);
	UFUNCTION(BlueprintImplementableEvent, Category = "Associate")
	void SetSelectBP(bool IsSelect);

	void GenerateChildWidget(const TArray<FAssociateCategoryListData>& InData, float LeftExtend);

	bool ChildHasSelect();

	static UAssociateDirInnerWidget* Create();

private:
	UFUNCTION()
	void OnClickBtnSelect();

public:
	FDirectorySelectDelegate OnDirectorySelectDelegate;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Associate")
	bool bSelect = false;


private:
	UPROPERTY()
	TArray<UAssociateDirInnerWidget*> ChildWidget;

	UPROPERTY()
	FAssociateCategoryListData CurData;

private:
	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Select;

	UPROPERTY(meta=(BindWidget))
	USizeBox* SZ_Indent;

	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_FolderName;

	UPROPERTY(meta=(BindWidget))
	UScrollBox* Scb_Child;

private:
	static FString FilePath;
};
