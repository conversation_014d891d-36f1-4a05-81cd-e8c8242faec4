// Fill out your copyright notice in the Description page of Project Settings.

#include "AssociateDirectoryWidget.h"

#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"


FString UAssociateDirectoryWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Obsurce/BP_AssociateDirWidget.BP_AssociateDirWidget_C'");
UAssociateDirectoryWidget* UAssociateDirectoryWidget::Instance = nullptr;

bool UAssociateDirectoryWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Btn_Cancel, OnClicked, UAssociateDirectoryWidget::OnClickBtnCancel);
	BIND_WIDGET_FUNCTION(Btn_Select, OnClicked, UAssociateDirectoryWidget::OnClickBtnSelect);

	BindDelegate();

	return true;
}

void UAssociateDirectoryWidget::Init(
	const FString& InAssociateType, 
	const FAssociateCategoryData& InCategoryData, 
	const FString& RefToID,
	const TArray<FRefAssociateData>& CurEditData
)
{
	AssociateType = InAssociateType;
	CategoryData = InCategoryData;
	RefCustomID = RefToID;

	//old associate data
	EditAssociateData = CurEditData;

	if(Txt_AssociateName)
	{
		Txt_AssociateName->SetText(FText::FromString(CategoryData.correlationTypeName));
	}

	SendQueryCategoryRequest(InAssociateType, RefCustomID);
}

void UAssociateDirectoryWidget::Clear()
{
	SelectDirectoryWidget = nullptr;

	AllCategoryListData.Empty();

	AssociateTreeWidget.Empty();

	AssociateShowWidget.Empty();

	EditAssociateData.Empty();

	RefCustomID.Empty();

	if(SCB_Tree)
	{
		SCB_Tree->ClearChildren();
	}
	if(WB_ShowContainer)
	{
		WB_ShowContainer->ClearChildren();
	}
}

UAssociateDirectoryWidget* UAssociateDirectoryWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UAssociateDirectoryWidget>(UAssociateDirectoryWidget::FilePath);
}

UAssociateDirectoryWidget* UAssociateDirectoryWidget::GetInstance()
{
	if (UAssociateDirectoryWidget::Instance == nullptr)
	{
		UAssociateDirectoryWidget::Instance = UAssociateDirectoryWidget::Create();
		UAssociateDirectoryWidget::Instance->AddToViewport(100);
		UAssociateDirectoryWidget::Instance->SetVisibility(ESlateVisibility::Collapsed);
	}
	return UAssociateDirectoryWidget::Instance;
}

void UAssociateDirectoryWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->AssociateCreateResponseDelegate.AddUniqueDynamic(this, &UAssociateDirectoryWidget::OnCreateAssociateResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->AssociateCategoryResponseDelegate.AddUniqueDynamic(this, &UAssociateDirectoryWidget::OnQueryCategoryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->AssociateCategoryShowDataResponseDelegate.AddUniqueDynamic(this, &UAssociateDirectoryWidget::OnQueryCategoryShowResponseHandler);
}

void UAssociateDirectoryWidget::SendCreateAssociateRequest(const TArray<FRefAssociateData>& Datas)
{
	NetUUID.CreateUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateCreateRequest(Datas);
}

void UAssociateDirectoryWidget::OnCreateAssociateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg,
	const TArray<FRefAssociateData>& Datas)
{
	if(UUID.Equals(NetUUID.CreateUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			AssociateDirectoryEditDelegate.ExecuteIfBound(Datas, AssociateType);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UAssociateDirectoryWidget::SendQueryCategoryRequest(const FString& InStr, const FString& InAssociateBkID)
{
	NetUUID.QueryCateListUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateCategoryRequest(InStr, InAssociateBkID);
}

void UAssociateDirectoryWidget::OnQueryCategoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg,
	const TArray<FAssociateCategoryListData>& Datas)
{
	if(UUID.Equals(NetUUID.QueryCateListUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			AllCategoryListData = Datas;
			TArray<FAssociateCategoryListData> RootDir = GetRootDirectory(Datas);
			GenerateRootWidget(RootDir);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UAssociateDirectoryWidget::SendQueryCategoryShowRequest(const int64& InCategoryID, const int32& InType, const FString& InAssociateBKID,
	const int32& InPageNumber, const int32& InPageSize)
{
	NetUUID.QueryShowUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateCategoryShowDataRequest(InCategoryID, InType, InPageNumber, InPageSize, InAssociateBKID);
}

void UAssociateDirectoryWidget::OnQueryCategoryShowResponseHandler(const FString& UUID, bool bSuccess,
	const FString& Msg, const TArray<FAssociateShowData>& Datas)
{
	if (UUID.Equals(NetUUID.QueryShowUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			GenerateShowWidget(Datas);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UAssociateDirectoryWidget::OnClickBtnCancel()
{
	Clear();
	SetVisibility(ESlateVisibility::Collapsed);
}

void UAssociateDirectoryWidget::OnClickBtnSelect()
{
	AnalysisSelectDirectoryShowHasSelect();
	SendCreateAssociateRequest(EditAssociateData);

	Clear();
	SetVisibility(ESlateVisibility::Collapsed);

}

void UAssociateDirectoryWidget::OnDirectorySelectHandler(UAssociateDirInnerWidget* InWidget)
{
	if(SelectDirectoryWidget != InWidget)
	{
		if(IS_OBJECT_PTR_VALID(SelectDirectoryWidget))
		{
			SelectDirectoryWidget->SetSelect(false);
		}

		SelectDirectoryWidget = InWidget;
		if(IS_OBJECT_PTR_VALID(InWidget))
		{
			InWidget->SetSelect(true);

			auto Data = InWidget->GetCurData();
			TArray<FAssociateCategoryListData> ChildData = GetChildDirectory(Data.id);
			InWidget->GenerateChildWidget(ChildData, InWidget->GetExtent());

			SendQueryCategoryShowRequest(Data.id, CategoryData.isMate, RefCustomID);
			//SendQueryCategoryShowRequest(Data.id, GetShowType());
		}
	}
}

void UAssociateDirectoryWidget::OnDirectoryShowEditHandler(UAssociateShowWidget* EditWidget, bool IsCheck)
{
	if (EditWidget)
	{
		if (IsCheck)
		{
			//remove folder
			FAssociateCategoryListData SelectCategoryListData = SelectDirectoryWidget->GetCurData();
			FString FolderBelongID = FString::FromInt(SelectCategoryListData.id);
			RemoveAssociate(FolderBelongID);
			RemoveUpperLevelCategoryAssociate(SelectCategoryListData);

			//add new
			FAssociateShowData Data = EditWidget->GetData();
			if (IsAlreadyAssociate(Data.id) == INDEX_NONE)
			{
				FRefAssociateData NewAssociateData;
				NewAssociateData.name = Data.name;
				NewAssociateData.meetCondition = TEXT("");
				NewAssociateData.belongId = Data.id;
				NewAssociateData.associationId = Data.associationId;
				NewAssociateData.isMate = CategoryData.isMate;
				NewAssociateData.type = 1;
				NewAssociateData.bkId = RefCustomID;
				NewAssociateData.dictValue = AssociateType;

				EditAssociateData.Add(NewAssociateData);
			}
		}
		else
		{
			FAssociateShowData Data = EditWidget->GetData();
			int32 Index = EditAssociateData.IndexOfByPredicate(
				[Data](const FRefAssociateData& Iter)->bool
				{
					return Iter.belongId.Equals(Data.id, ESearchCase::IgnoreCase);
				}
			);
			if(Index != INDEX_NONE)
			{
				EditAssociateData.RemoveAt(Index);
			}

		}
	}
}

TArray<FAssociateCategoryListData> UAssociateDirectoryWidget::GetRootDirectory(const TArray<FAssociateCategoryListData>& Datas)
{
	TArray<FAssociateCategoryListData> Res;

	for(const auto& ACLD : Datas)
	{
		bool ArrayHasPID = false;
		for(const auto& IACLD : Datas)
		{
			if(ACLD.pid == IACLD.id)
			{
				ArrayHasPID = true;
				break;
			}
		}
		if(!ArrayHasPID)
		{
			Res.Add(ACLD);
		}
	}

	return Res;
}

void UAssociateDirectoryWidget::GenerateRootWidget(const TArray<FAssociateCategoryListData>& Datas)
{
	AssociateTreeWidget.Empty();
	SCB_Tree->ClearChildren();
	for(auto& ACLD : Datas)
	{
		UAssociateDirInnerWidget* Temp = UAssociateDirInnerWidget::Create();
		Temp->UpdateContent(ACLD);
		Temp->OnDirectorySelectDelegate.BindUFunction(this, FName(TEXT("OnDirectorySelectHandler")));
		Temp->SetVisibility(ESlateVisibility::Visible);

		AssociateTreeWidget.Add(Temp);
		SCB_Tree->AddChild(Temp);
	}
}

TArray<FAssociateCategoryListData> UAssociateDirectoryWidget::GetChildDirectory(const int64& InID)
{
	TArray<FAssociateCategoryListData> Res;

	for(auto& AD : AllCategoryListData)
	{
		if(AD.pid == InID)
		{
			Res.Add(AD);
		}
	}

	return Res;
}

TArray<FAssociateCategoryListData> UAssociateDirectoryWidget::GetUpperDirectory(const int64& InPID)
{
	TArray<FAssociateCategoryListData> Res;

	for (auto& AD : AllCategoryListData)
	{
		if (AD.id == InPID)
		{
			Res.Add(AD);
		}
	}

	return Res;
}

void UAssociateDirectoryWidget::GenerateShowWidget(const TArray<FAssociateShowData>& InData)
{
	AssociateShowWidget.Empty();
	WB_ShowContainer->ClearChildren();

	for(auto& ASD : InData)
	{
		UAssociateShowWidget* Temp = UAssociateShowWidget::Create();
		Temp->UpdateContent(ASD);
		Temp->AssociateShowEditDelegate.BindUFunction(this, FName(TEXT("OnDirectoryShowEditHandler")));
		Temp->SetVisibility(ESlateVisibility::Visible);

		AssociateShowWidget.Add(Temp);
		WB_ShowContainer->AddChild(Temp);
	}
}

void UAssociateDirectoryWidget::AnalysisSelectDirectoryShowHasSelect()
{
	if (SelectDirectoryWidget)
	{
		bool HasSelect = false;
		for(auto& ASW : AssociateShowWidget)
		{
			if(ASW && ASW->IsSelect())
			{
				HasSelect = true;
				break;
			}
		}

		if(!HasSelect)
		{
			FAssociateCategoryListData SelectCategoryListData = SelectDirectoryWidget->GetCurData();
			FString NewBelongID = FString::FromInt(SelectCategoryListData.id);
			if (IsAlreadyAssociate(NewBelongID) == INDEX_NONE)
			{
				RemoveSubCategoryAssociate(SelectCategoryListData);
				RemoveUpperLevelCategoryAssociate(SelectCategoryListData);

				FRefAssociateData NewAssociateData;
				NewAssociateData.name = SelectCategoryListData.name;
				NewAssociateData.meetCondition = TEXT("");
				NewAssociateData.belongId = NewBelongID;
				NewAssociateData.associationId = SelectCategoryListData.associationId;
				NewAssociateData.isMate = CategoryData.isMate;
				NewAssociateData.type = 0;
				NewAssociateData.bkId = RefCustomID;
				NewAssociateData.dictValue = AssociateType;

				EditAssociateData.Add(NewAssociateData);
			}
			else
			{
				UI_POP_WINDOW_ERROR_ST(TEXT("this folder already associate!"));
			}
		}
	}
}

void UAssociateDirectoryWidget::RemoveSubCategoryAssociate(const FAssociateCategoryListData& CurCategoryData)
{
	TArray<FString> SubCategory;
	TArray<FAssociateCategoryListData> ChildData = GetChildDirectory(CurCategoryData.id);
	RescureSubCategory(ChildData, SubCategory);

	for (auto& SC : SubCategory)
	{
		RemoveAssociate(SC);
	}
}

void UAssociateDirectoryWidget::RescureSubCategory(const TArray<FAssociateCategoryListData>& CategoryDatas, TArray<FString>& SubCategoryIDs)
{
	for (auto& CD : CategoryDatas)
	{
		SubCategoryIDs.AddUnique(FString::FromInt(CD.id));

		TArray<FAssociateCategoryListData> ChildData = GetChildDirectory(CD.id);
		RescureSubCategory(ChildData, SubCategoryIDs);
	}
}

void UAssociateDirectoryWidget::RemoveUpperLevelCategoryAssociate(const FAssociateCategoryListData& CurCategoryData)
{
	TArray<FString> CategoryIDStr;
	TArray<FAssociateCategoryListData> CategoryData = GetUpperDirectory(CurCategoryData.pid);
	RescureUpperLevelCategory(CategoryData, CategoryIDStr);

	for (auto& SC : CategoryIDStr)
	{
		RemoveAssociate(SC);
	}
}

void UAssociateDirectoryWidget::RescureUpperLevelCategory(const TArray<FAssociateCategoryListData>& CategoryDatas, TArray<FString>& UpperCategoryIDs)
{
	for (auto& CD : CategoryDatas)
	{
		UpperCategoryIDs.AddUnique(FString::FromInt(CD.id));

		TArray<FAssociateCategoryListData> TempCategoryData = GetUpperDirectory(CD.pid);
		RescureUpperLevelCategory(TempCategoryData, UpperCategoryIDs);
	}
}

int32 UAssociateDirectoryWidget::IsAlreadyAssociate(const FString& InID)
{
	const int32 Index = EditAssociateData.IndexOfByPredicate(
		[InID](const FRefAssociateData& Iter)->bool
		{
			return Iter.belongId.Equals(InID, ESearchCase::IgnoreCase);
		}
	);
	return Index;
}

void UAssociateDirectoryWidget::RemoveAssociate(const FString& InID)
{
	const int32 Index = IsAlreadyAssociate(InID);
	if(Index != INDEX_NONE)
	{
		EditAssociateData.RemoveAt(Index);
	}
}

int32 UAssociateDirectoryWidget::GetShowType()
{
	/*
	*  @@ CategoryData.isMate --> 0 : custom; 1 : furniture; 2 : material
	*  @@ for show data type --> 0 : furniture / material; 1 : custom
	*  @@ need convert
	*/
	if (CategoryData.isMate == 0)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}
