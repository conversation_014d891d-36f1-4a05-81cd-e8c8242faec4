// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AssociateDirInnerWidget.h"
#include "AssociateShowWidget.h"
#include "Components/WrapBox.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "AssociateDirectoryWidget.generated.h"

DECLARE_DELEGATE_TwoParams(FAssociateDirectoryEditDelegate, const TArray<FRefAssociateData>&, FString)

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UAssociateDirectoryWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void Init(
		const FString& InAssociateType, 
		const FAssociateCategoryData& InCategoryData, 
		const FString& RefToID,
		const TArray<FRefAssociateData>& CurEditData
	);

	void Clear();

	static UAssociateDirectoryWidget* Create();
	static UAssociateDirectoryWidget* GetInstance();

public:
	FAssociateDirectoryEditDelegate AssociateDirectoryEditDelegate;

#pragma region NET

private:
	void BindDelegate();

	void SendCreateAssociateRequest(const TArray<FRefAssociateData>& Datas);
	UFUNCTION()
	void OnCreateAssociateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefAssociateData>& Datas);

	void SendQueryCategoryRequest(const FString& InStr, const FString& InAssociateBkID);
	UFUNCTION()
	void OnQueryCategoryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FAssociateCategoryListData>& Datas);

	void SendQueryCategoryShowRequest(const int64& InCategoryID, const int32& InType, const FString& InAssociateBKID, const int32& InPageNumber = 0, const int32& InPageSize = 200);
	UFUNCTION()
	void OnQueryCategoryShowResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FAssociateShowData>& Datas);

private:
	UPROPERTY()
	FAssociateNetUUID NetUUID;

#pragma	endregion

	UFUNCTION()
	void OnClickBtnCancel();

	UFUNCTION()
	void OnClickBtnSelect();

	UFUNCTION()
	void OnDirectorySelectHandler(UAssociateDirInnerWidget* InWidget);

	UFUNCTION()
	void OnDirectoryShowEditHandler(UAssociateShowWidget* EditWidget, bool IsCheck);

	TArray<FAssociateCategoryListData> GetRootDirectory(const TArray<FAssociateCategoryListData>& Datas);
	void GenerateRootWidget(const TArray<FAssociateCategoryListData>& Datas);
	TArray<FAssociateCategoryListData> GetChildDirectory(const int64& InID);
	TArray<FAssociateCategoryListData> GetUpperDirectory(const int64& InPID);

	void GenerateShowWidget(const TArray<FAssociateShowData>& InData);

	void AnalysisSelectDirectoryShowHasSelect();

	void RemoveSubCategoryAssociate(const FAssociateCategoryListData& CurCategoryData);
	void RescureSubCategory(const TArray<FAssociateCategoryListData>& CategoryDatas, TArray<FString>& SubCategoryIDs);

	void RemoveUpperLevelCategoryAssociate(const FAssociateCategoryListData& CurCategoryData);
	void RescureUpperLevelCategory(const TArray<FAssociateCategoryListData>& CategoryDatas, TArray<FString>& UpperCategoryIDs);

	/*
	*  @@ RetValue : index in array
	*/
	int32 IsAlreadyAssociate(const FString& InID);

	void RemoveAssociate(const FString& InID);


	//NO USE
	int32 GetShowType();

private:
	UPROPERTY()
	UAssociateDirInnerWidget* SelectDirectoryWidget;

	UPROPERTY()
	TArray<FAssociateCategoryListData> AllCategoryListData;

	UPROPERTY()
	TArray<UAssociateDirInnerWidget*> AssociateTreeWidget;

	UPROPERTY()
	TArray<UAssociateShowWidget*> AssociateShowWidget;


	UPROPERTY()
	FString AssociateType;

	UPROPERTY()
	FString RefCustomID;

	UPROPERTY()
	FAssociateCategoryData CategoryData;

	UPROPERTY()
	TArray<FRefAssociateData> EditAssociateData;

	UPROPERTY()
	TArray<int64> CheckIDs;

private:
	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_AssociateName;

	UPROPERTY(meta=(BindWidget))
	UScrollBox* SCB_Tree;

	UPROPERTY(meta=(BindWidget))
	UWrapBox* WB_ShowContainer;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Cancel;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Select;

private:
	static FString FilePath;
	static UAssociateDirectoryWidget* Instance;
};
