// Fill out your copyright notice in the Description page of Project Settings.

#include "AssociateShowWidget.h"

#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UAssociateShowWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Obsurce/BP_AssociateShowWidget.BP_AssociateShowWidget_C'");

bool UAssociateShowWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Ckb_Asso, OnCheckStateChanged, UAssociateShowWidget::OnCheckChanged);

	BindDelegate();

	return true;
}

void UAssociateShowWidget::UpdateContent(const FAssociateShowData& InData)
{
	CurData = InData;

	if(Txt_Show)
	{
		Txt_Show->SetText(FText::FromString(InData.name));
		Txt_Show->SetToolTipText(FText::FromString(InData.name));
	}

	if(!CurData.productImg.IsEmpty() || !CurData.mapsImg.IsEmpty())
	{
		FString RealPath = !CurData.productImg.IsEmpty() ? CurData.productImg : CurData.mapsImg;
		LoadThumbnail(RealPath);
	}

	if(Ckb_Asso)
	{
		Ckb_Asso->SetIsChecked(InData.associationId != INDEX_NONE);
	}
}

bool UAssociateShowWidget::IsSelect()
{
	if(Ckb_Asso)
	{
		return Ckb_Asso->GetCheckedState() == ECheckBoxState::Checked;
	}
	return false;
}

UAssociateShowWidget* UAssociateShowWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UAssociateShowWidget>(UAssociateShowWidget::FilePath);
}

void UAssociateShowWidget::OnCheckChanged(bool bIsChecked)
{
	AssociateShowEditDelegate.ExecuteIfBound(this, bIsChecked);
}

void UAssociateShowWidget::LoadThumbnail(const FString& Path)
{
#define CATALOG_NET_URL_FLAG TEXT("http")
	if(Path.Contains(CATALOG_NET_URL_FLAG))
	{
		LoadImage_BP(Path);
	}
	else
	{
		if (Path.IsEmpty())
			return;

		const FString AbsImagePath = FPaths::ConvertRelativePathToFull(
			FPaths::Combine(FPaths::ProjectContentDir(), Path)
		);
		if (FPaths::FileExists(AbsImagePath))
		{
			LoadThumbnail_Inner(Path);
		}
		else
		{
			SendDowmloadImageRequest(Path);
		}
	}
#undef CATALOG_NET_URL_FLAG
}

void UAssociateShowWidget::LoadThumbnail_Inner(const FString& Path)
{
	if (Path.IsEmpty())
		return;

	const FString AbsImagePath = FPaths::ConvertRelativePathToFull(
		FPaths::Combine(FPaths::ProjectContentDir(), Path)
	);
	UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsImagePath);
	LoadImageByTexture_BP(FileImage);
}

void UAssociateShowWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UAssociateShowWidget::OnDownloadImageResponseHandler);

}

void UAssociateShowWidget::SendDowmloadImageRequest(const FString& RelPath)
{
	DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(RelPath);
}

void UAssociateShowWidget::OnDownloadImageResponseHandler(const FString& UUID, bool bSuccess,
	const TArray<FString>& FilePath)
{
	if(DownloadUUID.Equals(UUID, ESearchCase::IgnoreCase))
	{
		DownloadUUID = TEXT("NoWay");
		if (bSuccess && FilePath.IsValidIndex(0))
		{
			LoadThumbnail_Inner(FilePath[0]);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file image error [%s]"), *CurData.productImg);
		}
	}
}
