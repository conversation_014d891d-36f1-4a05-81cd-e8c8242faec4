// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CheckBox.h"
#include "Components/TextBlock.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "AssociateShowWidget.generated.h"

class UAssociateShowWidget;

DECLARE_DELEGATE_TwoParams(FAssociateShowEditDelegate, UAssociateShowWidget*, bool);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UAssociateShowWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void UpdateContent(const FAssociateShowData& InData);

	FString GetDataID() { return CurData.id; }
	FAssociateShowData GetData() { return CurData; }

	bool IsSelect();

	UFUNCTION(BlueprintImplementableEvent, Category = "AssociateShow")
	void LoadImage_BP(const FString& URL);
	UFUNCTION(BlueprintImplementableEvent, Category = "AssociateShow")
	void LoadImageByTexture_BP(UTexture2D* InTexture);

	static UAssociateShowWidget* Create();

private:
	UFUNCTION()
	void OnCheckChanged(bool bIsChecked);

	void LoadThumbnail(const FString& Path);
	void LoadThumbnail_Inner(const FString& Path);

	void BindDelegate();
	void SendDowmloadImageRequest(const FString& RelPath);
	UFUNCTION()
	void OnDownloadImageResponseHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath);

public:
	FAssociateShowEditDelegate AssociateShowEditDelegate;

private:
	UPROPERTY()
	FAssociateShowData CurData;

	UPROPERTY()
	FString DownloadUUID;

private:
	/*UPROPERTY(BlueprintReadWrite, VisibleAnywhere, meta=(BindWidget, AllowPrivateAccess=true))
	UImage* Img_Show;*/

	UPROPERTY(meta=(BindWidget))
	UCheckBox* Ckb_Asso;

	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_Show;

private:
	static FString FilePath;
};
