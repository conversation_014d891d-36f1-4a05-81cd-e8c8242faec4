// Fill out your copyright notice in the Description page of Project Settings.

#include "ObsurceLayoutWidget.h"

#include "AssociateDirectoryWidget.h"
#include "Components/Button.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UObsurceLayoutWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Obsurce/BP_ObsuuceLayoutWidget.BP_ObsuuceLayoutWidget_C'");

#define ASSOCIATE_TYPE_DOOR				TEXT("GLMB")
//#define ASSOCIATE_TYPE_DOOR				TEXT("TEST-QW-MX") //test
#define ASSOCIATE_TYPE_COMPONENT		TEXT("GLGNJ")
#define ASSOCIATE_TYPE_MATERIAL			TEXT("GLCZ")
#define ASSOCIATE_TYPE_HANDLER			TEXT("GLLS")
#define ASSOCIATE_TYPE_REF_COMP			TEXT("GLFJPJ")

bool UObsurceLayoutWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void UObsurceLayoutWidget::NativeOnInitialized()
{
	BIND_WIDGET_FUNCTION(Btn_Add_door, OnClicked, UObsurceLayoutWidget::OnClickBtnAddDoor);
	BIND_WIDGET_FUNCTION(Btn_Add_Comp, OnClicked, UObsurceLayoutWidget::OnClickBtnAddComp);
	BIND_WIDGET_FUNCTION(Btn_Add_Material, OnClicked, UObsurceLayoutWidget::OnClickBtnAddMaterial);
	BIND_WIDGET_FUNCTION(Btn_Add_Handler, OnClicked, UObsurceLayoutWidget::OnClickBtnAddHandler);
	BIND_WIDGET_FUNCTION(Btn_Add_RefComp, OnClicked, UObsurceLayoutWidget::OnClickBtnAddRefComp);

	BindDelegate();
}

void UObsurceLayoutWidget::Init(const FString& InAssociate, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InUpperLevelParams)
{
	OverrideParams = InUpperLevelParams;
	CurAssociateID = InAssociate;
	SendQueryRequest(CurAssociateID);
}

void UObsurceLayoutWidget::SyncFile()
{
	UFolderWidget::Get()->SyncAssociateDataToFile(CollectAllAssociateData());
}

TArray<FAssociateListData> UObsurceLayoutWidget::CollectAllAssociateData()
{
	TArray<FAssociateListData> Res;
	for (const auto& Iter : AssociateCategoryData)
	{
		FAssociateListData& Temp = Res.AddDefaulted_GetRef();
		Temp.correlationType = Iter.Value.correlationType;
		Temp.correlationTypeName = Iter.Value.correlationTypeName;
		Temp.isMate = Iter.Value.isMate;
	}

	for (const auto& Iter : AssociateListShowWidget)
	{
		FAssociateListData* ListData = Res.FindByPredicate(
			[&Iter](const FAssociateListData& Data)->bool
			{
				return Iter.Key.Equals(Data.correlationType);
			}
		);
		if (ListData != nullptr)
		{
			for (const auto& VIter : Iter.Value.ShowWidgetArr)
			{
				if (VIter != nullptr)
				{
					ListData->bkAssociationDetailList.Add(VIter->GetCurData());
				}
			}
		}
	}

	return Res;
}

void UObsurceLayoutWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->AssociateQueryResponseDelegate.AddUniqueDynamic(this, &UObsurceLayoutWidget::OnQueryResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->AssociateUpdateResponseDelegate.AddUniqueDynamic(this, &UObsurceLayoutWidget::OnUpdateResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->AssociateDelResponseDelegate.AddUniqueDynamic(this, &UObsurceLayoutWidget::OnDeleteResponseHandler);
}

void UObsurceLayoutWidget::SendCreateAssociate(const FString& InAssociateType)
{

}

void UObsurceLayoutWidget::SendQueryRequest(const FString& InAssociateID)
{
	NetUUID.QueryUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateQueryRequest(InAssociateID);
}

void UObsurceLayoutWidget::OnQueryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg,
	const TArray<FAssociateListData>& Datas)
{
	if(UUID.Equals(NetUUID.QueryUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			UpdateAssociateListShow(Datas);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UObsurceLayoutWidget::SendUpdateRequest(const FRefAssociateData& InData)
{
	NetUUID.UpdateUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateUpdateRequest(InData);
}

void UObsurceLayoutWidget::OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg,
	const TArray<FRefAssociateData>& Datas)
{
	if(UUID.Equals(NetUUID.UpdateUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			if(IS_OBJECT_PTR_VALID(EditWidget) && Datas.IsValidIndex(0))
			{
				EditWidget->UpdateContent(Datas[0]);
			}

			SyncFile();
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);

			if (IS_OBJECT_PTR_VALID(EditWidget))
			{
				EditWidget->ResetOldContent();
			}
		}
	}
}

void UObsurceLayoutWidget::SendDeleteRequest(const int64& DelID)
{
	NetUUID.DeleteUUID = UCatalogNetworkSubsystem::GetInstance()->SendAssociateDeleteRequest({DelID});
}

void UObsurceLayoutWidget::OnDeleteResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg)
{
	if(UUID.Equals(NetUUID.DeleteUUID, ESearchCase::IgnoreCase))
	{
		if(bSuccess)
		{
			if(IS_OBJECT_PTR_VALID(EditWidget))
			{
				FString DelAssociateType = EditWidget->GetCurData().dictValue;
				if (AssociateListShowWidget.Contains(DelAssociateType))
				{
					AssociateListShowWidget[DelAssociateType].RemoveWidget(EditWidget);
				}

				EditWidget->SetVisibility(ESlateVisibility::Collapsed);
				EditWidget->RemoveFromParent();
			}

			SyncFile();
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UObsurceLayoutWidget::UpdateAssociateListShow(const TArray<FAssociateListData>& Datas)
{
	EmptyAssociateWidget();
	EmptyWidgetContainer();
	for(auto& ALD : Datas)
	{
		UpdateAssociateListShow_Inner(
			ALD.correlationType,
			ALD,
			ALD.correlationType.Equals(ASSOCIATE_TYPE_DOOR, ESearchCase::IgnoreCase) ? SB_Detail_Door :
			ALD.correlationType.Equals(ASSOCIATE_TYPE_COMPONENT, ESearchCase::IgnoreCase) ? SB_Detail_Comp :
			ALD.correlationType.Equals(ASSOCIATE_TYPE_MATERIAL, ESearchCase::IgnoreCase) ? SB_Detail_Material :
			ALD.correlationType.Equals(ASSOCIATE_TYPE_HANDLER, ESearchCase::IgnoreCase) ? SB_Detail_Handler :
			ALD.correlationType.Equals(ASSOCIATE_TYPE_REF_COMP, ESearchCase::IgnoreCase) ? SB_Detail_RefComp : nullptr
		);
	}
}

void UObsurceLayoutWidget::UpdateAssociateListShow_Inner(const FString& AssociateType, const FAssociateListData& Datas, UScrollBox* Container)
{
	if (!Container)
		return;

	if (!AssociateCategoryData.Contains(Datas.correlationType))
	{
		AssociateCategoryData.Add(
			Datas.correlationType, 
			FAssociateCategoryData(Datas.correlationType, Datas.correlationTypeName, Datas.isMate)
		);
	}

	for(auto& ADL : Datas.bkAssociationDetailList)
	{
		UObsurceShowItemWidget* Temp = UObsurceShowItemWidget::Create();
		//complete data
		/*auto FullData = ADL;
		FullData.dictValue = AssociateType;
		FullData.bkId = CurAssociateID;*/
		Temp->UpdateContent(ADL);
		Temp->AssociateEditDelegate.BindUFunction(this, FName(TEXT("OnObsurceItemEditHandler")));
		Temp->SetVisibility(ESlateVisibility::Visible);

		Container->AddChild(Temp);
		AddAssociateWidget(AssociateType, Temp);
	}
}

void UObsurceLayoutWidget::UpdateAssociateListShow_Arr(const FString& AssociateType, const TArray<FRefAssociateData>& Datas)
{
	UScrollBox* Container = AssociateType.Equals(ASSOCIATE_TYPE_DOOR, ESearchCase::IgnoreCase) ? SB_Detail_Door :
		AssociateType.Equals(ASSOCIATE_TYPE_COMPONENT, ESearchCase::IgnoreCase) ? SB_Detail_Comp :
		AssociateType.Equals(ASSOCIATE_TYPE_MATERIAL, ESearchCase::IgnoreCase) ? SB_Detail_Material :
		AssociateType.Equals(ASSOCIATE_TYPE_HANDLER, ESearchCase::IgnoreCase) ? SB_Detail_Handler :
		AssociateType.Equals(ASSOCIATE_TYPE_REF_COMP, ESearchCase::IgnoreCase) ? SB_Detail_RefComp : nullptr;
	if(Container)
	{
		PrepareAssociateWidgetMap(AssociateType);
		Container->ClearChildren();
		for(auto& ALD : Datas)
		{
			UpdateAssociateListShow_Inner(AssociateType, ALD, Container);
		}
	}
}

void UObsurceLayoutWidget::UpdateAssociateListShow_Inner(const FString& AssociateType, const FRefAssociateData& Datas,
	UScrollBox* Container)
{
	if (!Container)
		return;

	UObsurceShowItemWidget* Temp = UObsurceShowItemWidget::Create();
	Temp->UpdateContent(Datas);
	Temp->AssociateEditDelegate.BindUFunction(this, FName(TEXT("OnObsurceItemEditHandler")));
	Temp->SetVisibility(ESlateVisibility::Visible);

	Container->AddChild(Temp);
	AddAssociateWidget(AssociateType, Temp);
}

void UObsurceLayoutWidget::EmptyWidgetContainer()
{
	if(SB_Detail_Door)
	{
		SB_Detail_Door->ClearChildren();
	}
	if(SB_Detail_Comp)
	{
		SB_Detail_Comp->ClearChildren();
	}
	if(SB_Detail_Material)
	{
		SB_Detail_Material->ClearChildren();
	}
	if(SB_Detail_Handler)
	{
		SB_Detail_Handler->ClearChildren();
	}
	if(SB_Detail_RefComp)
	{
		SB_Detail_RefComp->ClearChildren();
	}
}

void UObsurceLayoutWidget::EmptyAssociateWidget()
{
	AssociateCategoryData.Empty();
	AssociateListShowWidget.Empty();
}

void UObsurceLayoutWidget::PrepareAssociateWidgetMap(const FString& AssociateType)
{
	if(!AssociateListShowWidget.Contains(AssociateType))
	{
		AssociateListShowWidget.Add(AssociateType, FShowWidgetArr());
	}
	else
	{
		AssociateListShowWidget[AssociateType].Empty();
	}
}

void UObsurceLayoutWidget::AddAssociateWidget(const FString& AssociateType, UObsurceShowItemWidget* InWidget)
{
	if(AssociateListShowWidget.Contains(AssociateType))
	{
		AssociateListShowWidget[AssociateType].AddWidget(InWidget);
	}
	else
	{
		FShowWidgetArr Arr;
		Arr.AddWidget(InWidget);
		AssociateListShowWidget.Add(AssociateType, Arr);
	}
}

void UObsurceLayoutWidget::RemoveAssociateWidget(const FString& AssociateType, UObsurceShowItemWidget* InWidget)
{
	if (AssociateListShowWidget.Contains(AssociateType))
	{
		AssociateListShowWidget[AssociateType].RemoveWidget(InWidget);
	}
}

TArray<FRefAssociateData> UObsurceLayoutWidget::GetCurAssociateData(const FString& InAssociateType)
{
	TArray<FRefAssociateData> Res;
	if(AssociateListShowWidget.Contains(InAssociateType))
	{
		for(auto& ALSW : AssociateListShowWidget[InAssociateType].ShowWidgetArr)
		{
			if(ALSW)
			{
				Res.Add(ALSW->GetCurData());
			}
		}
	}
	return Res;
}

void UObsurceLayoutWidget::OnObsurceItemEditHandler(UObsurceShowItemWidget* InWidget, const int32& EditType)
{
	EditWidget = InWidget;
	if(IS_OBJECT_PTR_VALID(InWidget))
	{
		if(EditType == 0) //edit
		{
			FRefAssociateData EditData = InWidget->GetEditData();
			FString Value, Expression;
			bool Res = UParameterRelativeLibrary::CalculateParameterExpression(
				ACatalogPlayerController::Get()->GetGlobalParameterMap(),
				OverrideParams, 
				{},
				EditData.meetCondition,
				Value,
				Expression
			);
			if (Res)
			{
				EditData.meetCondition = Expression;
				SendUpdateRequest(EditData);
			}
			else
			{
				UI_POP_WINDOW_ERROR_ST(TEXT("express format error!"));
				InWidget->UpdateContent(InWidget->GetCurData());
			}
		}
		else if(EditType == 1) //delete
		{ 
			SendDeleteRequest(InWidget->GetCurID());
		}
	}
	
}

void UObsurceLayoutWidget::OnAssociateDataEditHandler(const TArray<FRefAssociateData>& EditDatas,
	const FString& AssociateType)
{
	//UpdateAssociateListShow_Arr(AssociateType, EditDatas);

	Init(CurAssociateID, OverrideParams);
}

void UObsurceLayoutWidget::OnClickBtnAddDoor()
{
	FAssociateCategoryData Data = AssociateCategoryData.Contains(ASSOCIATE_TYPE_DOOR) ?
		AssociateCategoryData[ASSOCIATE_TYPE_DOOR] : FAssociateCategoryData();
	TArray<FRefAssociateData> CurAssociateData = GetCurAssociateData(ASSOCIATE_TYPE_DOOR);
	UAssociateDirectoryWidget::GetInstance()->Init(ASSOCIATE_TYPE_DOOR, Data, CurAssociateID, CurAssociateData);
	UAssociateDirectoryWidget::GetInstance()->AssociateDirectoryEditDelegate.BindUFunction(this, FName(TEXT("OnAssociateDataEditHandler")));
	UAssociateDirectoryWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
}

void UObsurceLayoutWidget::OnClickBtnAddComp()
{
	FAssociateCategoryData Data = AssociateCategoryData.Contains(ASSOCIATE_TYPE_COMPONENT) ?
		AssociateCategoryData[ASSOCIATE_TYPE_COMPONENT] : FAssociateCategoryData();
	TArray<FRefAssociateData> CurAssociateData = GetCurAssociateData(ASSOCIATE_TYPE_COMPONENT);
	UAssociateDirectoryWidget::GetInstance()->Init(ASSOCIATE_TYPE_COMPONENT, Data, CurAssociateID, CurAssociateData);
	UAssociateDirectoryWidget::GetInstance()->AssociateDirectoryEditDelegate.BindUFunction(this, FName(TEXT("OnAssociateDataEditHandler")));
	UAssociateDirectoryWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
}

void UObsurceLayoutWidget::OnClickBtnAddMaterial()
{
	FAssociateCategoryData Data = AssociateCategoryData.Contains(ASSOCIATE_TYPE_MATERIAL) ?
		AssociateCategoryData[ASSOCIATE_TYPE_MATERIAL] : FAssociateCategoryData();
	TArray<FRefAssociateData> CurAssociateData = GetCurAssociateData(ASSOCIATE_TYPE_MATERIAL);
	UAssociateDirectoryWidget::GetInstance()->Init(ASSOCIATE_TYPE_MATERIAL, Data, CurAssociateID, CurAssociateData);
	UAssociateDirectoryWidget::GetInstance()->AssociateDirectoryEditDelegate.BindUFunction(this, FName(TEXT("OnAssociateDataEditHandler")));
	UAssociateDirectoryWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
}

void UObsurceLayoutWidget::OnClickBtnAddHandler()
{
	FAssociateCategoryData Data = AssociateCategoryData.Contains(ASSOCIATE_TYPE_HANDLER) ?
		AssociateCategoryData[ASSOCIATE_TYPE_HANDLER] : FAssociateCategoryData();
	TArray<FRefAssociateData> CurAssociateData = GetCurAssociateData(ASSOCIATE_TYPE_HANDLER);
	UAssociateDirectoryWidget::GetInstance()->Init(ASSOCIATE_TYPE_HANDLER, Data, CurAssociateID, CurAssociateData);
	UAssociateDirectoryWidget::GetInstance()->AssociateDirectoryEditDelegate.BindUFunction(this, FName(TEXT("OnAssociateDataEditHandler")));
	UAssociateDirectoryWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
}

void UObsurceLayoutWidget::OnClickBtnAddRefComp()
{
	FAssociateCategoryData Data = AssociateCategoryData.Contains(ASSOCIATE_TYPE_REF_COMP) ?
		AssociateCategoryData[ASSOCIATE_TYPE_REF_COMP] : FAssociateCategoryData();
	TArray<FRefAssociateData> CurAssociateData = GetCurAssociateData(ASSOCIATE_TYPE_REF_COMP);
	UAssociateDirectoryWidget::GetInstance()->Init(ASSOCIATE_TYPE_REF_COMP, Data, CurAssociateID, CurAssociateData);
	UAssociateDirectoryWidget::GetInstance()->AssociateDirectoryEditDelegate.BindUFunction(this, FName(TEXT("OnAssociateDataEditHandler")));
	UAssociateDirectoryWidget::GetInstance()->SetVisibility(ESlateVisibility::Visible);
}
