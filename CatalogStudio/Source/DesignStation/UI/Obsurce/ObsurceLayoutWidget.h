// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ObsurceShowItemWidget.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "ObsurceLayoutWidget.generated.h"

/**
 * 
 */

class UScrollBox;
class UButton;

USTRUCT()
struct DESIGNSTATION_API FShowWidgetArr
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	TArray<UObsurceShowItemWidget*> ShowWidgetArr;

public:
	void AddWidget(UObsurceShowItemWidget* InWidget)
	{
		ShowWidgetArr.AddUnique(InWidget);
	}

	void RemoveWidget(UObsurceShowItemWidget* InWidget)
	{
		ShowWidgetArr.Remove(InWidget);
	}

	void Empty()
	{
		ShowWidgetArr.Empty();
	}
};

UCLASS()
class DESIGNSTATION_API UObsurceLayoutWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void Init(const FString& InAssociate, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InUpperLevelParams);

	void SyncFile();
	TArray<FAssociateListData> CollectAllAssociateData();

private:
#pragma region NET

	void BindDelegate();

	void SendCreateAssociate(const FString& InAssociateType);

	void SendQueryRequest(const FString& InAssociateID);
	UFUNCTION()
	void OnQueryResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FAssociateListData>& Datas);

	void SendUpdateRequest(const FRefAssociateData& InData);
	UFUNCTION()
	void OnUpdateResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefAssociateData>& Datas);

	void SendDeleteRequest(const int64& DelID);
	UFUNCTION()
	void OnDeleteResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg);

#pragma endregion

	void UpdateAssociateListShow(const TArray<FAssociateListData>& Datas);
	void UpdateAssociateListShow_Inner(const FString& AssociateType, const FAssociateListData& Datas, UScrollBox* Container);
	void UpdateAssociateListShow_Arr(const FString& AssociateType, const TArray<FRefAssociateData> & Datas);
	void UpdateAssociateListShow_Inner(const FString& AssociateType, const FRefAssociateData& Datas, UScrollBox* Container);

	void EmptyWidgetContainer();
	void EmptyAssociateWidget();
	void PrepareAssociateWidgetMap(const FString& AssociateType);
	void AddAssociateWidget(const FString& AssociateType, UObsurceShowItemWidget* InWidget);
	void RemoveAssociateWidget(const FString& AssociateType, UObsurceShowItemWidget* InWidget);

	TArray<FRefAssociateData> GetCurAssociateData(const FString& InAssociateType);

	UFUNCTION()
	void OnObsurceItemEditHandler(UObsurceShowItemWidget* InWidget, const int32& EditType);

	UFUNCTION()
		void OnAssociateDataEditHandler(const TArray<FRefAssociateData>& EditDatas, const FString& AssociateType);

private:
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  OverrideParams;

	UPROPERTY()
	FAssociateNetUUID NetUUID;

	UPROPERTY()
	UObsurceShowItemWidget* EditWidget;

	UPROPERTY()
	FString CurAssociateID;

	UPROPERTY()
	TMap<FString, FAssociateCategoryData> AssociateCategoryData;

	UPROPERTY()
	TMap<FString, FShowWidgetArr> AssociateListShowWidget;

private:
	UFUNCTION()
	void OnClickBtnAddDoor();

	UFUNCTION()
	void OnClickBtnAddComp();

	UFUNCTION()
	void OnClickBtnAddMaterial();

	UFUNCTION()
	void OnClickBtnAddHandler();

	UFUNCTION()
	void OnClickBtnAddRefComp();

private:
	//door obscure
	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Add_door;
	UPROPERTY(meta = (BindWidget))
	UScrollBox* SB_Detail_Door;

	//component obscure
	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Add_Comp;
	UPROPERTY(meta = (BindWidget))
	UScrollBox* SB_Detail_Comp;

	//material obscure
	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Add_Material;
	UPROPERTY(meta = (BindWidget))
	UScrollBox* SB_Detail_Material;

	//handler obscure
	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Add_Handler;
	UPROPERTY(meta = (BindWidget))
	UScrollBox* SB_Detail_Handler;

	//ref component obscure
	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Add_RefComp;
	UPROPERTY(meta = (BindWidget))
	UScrollBox* SB_Detail_RefComp;

private:
	static FString FilePath;

};
