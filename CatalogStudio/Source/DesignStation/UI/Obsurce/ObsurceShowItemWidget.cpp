// Fill out your copyright notice in the Description page of Project Settings.

#include "ObsurceShowItemWidget.h"

#include "Components/Button.h"
#include "Components/MultiLineEditableTextBox.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"

FString UObsurceShowItemWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Obsurce/BP_ObsurceShowItemWidget.BP_ObsurceShowItemWidget_C'");

bool UObsurceShowItemWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Edt_Condition, OnTextCommitted, UObsurceShowItemWidget::OnCommittedMlEtbCondition);
	BIND_WIDGET_FUNCTION(Btn_Expression, OnClicked, UObsurceShowItemWidget::OnClickBtnExpression);
	BIND_WIDGET_FUNCTION(Btn_Delete, OnClicked, UObsurceShowItemWidget::OnClickBtnDelete);

	return true;
}

void UObsurceShowItemWidget::UpdateContent(const FRefAssociateData& InData)
{
	CurData = InData;
	EditData = InData;

	if (Txt_ID)
	{
		FText ShowText = FText::FromString(FString::FromInt(InData.id));
		Txt_ID->SetText(ShowText);
		Txt_ID->SetToolTipText(ShowText);
	}

	if (MlEtb_Name)
	{
		FText ShowText = FText::FromString(InData.name);
		MlEtb_Name->SetText(ShowText);
		MlEtb_Name->SetToolTipText(ShowText);
	}

	if (Edt_Condition)
	{
		FText ShowText = FText::FromString(InData.meetCondition);
		Edt_Condition->SetText(ShowText);
		Edt_Condition->SetToolTipText(ShowText);
	}
}

void UObsurceShowItemWidget::ResetOldContent()
{
	UpdateContent(CurData);
}

UObsurceShowItemWidget* UObsurceShowItemWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UObsurceShowItemWidget>(UObsurceShowItemWidget::FilePath);
}

void UObsurceShowItemWidget::OnCommittedMlEtbCondition(const FText& Text, ETextCommit::Type CommitMethod)
{
	if(CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (!EditData.meetCondition.Equals(Text.ToString(), ESearchCase::CaseSensitive))
		{
			EditData.meetCondition = Text.ToString();

			AssociateEditDelegate.ExecuteIfBound(this, 0);
		}
	}
}

void UObsurceShowItemWidget::OnClickBtnExpression()
{
	if (IS_OBJECT_PTR_VALID(Edt_Condition))
	{
		BIND_EXPRESSION_WIDGET(1//static_cast<int32>(EExpressionFolderType::CodingExpression)
			, Edt_Condition->GetText().ToString()
			, FName(TEXT("FolderOrFileExpressionEdit"))
		);
	}
}

void UObsurceShowItemWidget::OnClickBtnDelete()
{
	UI_POP_WINDOW_TIP_CONFIRM(TEXT("comfirm to delete associate"));
	if (ConfirmRes)
	{
		AssociateEditDelegate.ExecuteIfBound(this, 1);
	}
}

void UObsurceShowItemWidget::FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression)
{
	//static_cast<int32>(EExpressionFolderType::CodingExpression)
	if(EditType == 1)
	{
		if (!Expression.Equals(EditData.meetCondition, ESearchCase::CaseSensitive))
		{
			EditData.meetCondition = Expression;

			AssociateEditDelegate.ExecuteIfBound(this, 0);
		}
	}
}
