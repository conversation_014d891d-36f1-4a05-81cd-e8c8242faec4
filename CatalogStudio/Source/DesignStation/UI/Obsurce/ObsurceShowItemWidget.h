// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/RefFile/Data/RefToAssociateLibrary.h"
#include "ObsurceShowItemWidget.generated.h"

/**
 * 
 */

class UButton;
class UMultiLineEditableTextBox;
class UTextBlock;
class UObsurceShowItemWidget;

DECLARE_DELEGATE_TwoParams(FAssociateEditDelegate, UObsurceShowItemWidget*, int32)

UCLASS()
class DESIGNSTATION_API UObsurceShowItemWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void UpdateContent(const FRefAssociateData& InData);

	void ResetOldContent();

	FRefAssociateData GetCurData() { return CurData; }
	FRefAssociateData GetEditData() { return EditData; }

	int64 GetCurID() { return CurData.id; }

	static UObsurceShowItemWidget* Create();

private:
	UFUNCTION()
	void OnCommittedMlEtbCondition(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
	void OnClickBtnExpression();

	UFUNCTION()
	void OnClickBtnDelete();

	UFUNCTION()
	void FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression);

public:
	//0 -- edit; 1 -- delete
	FAssociateEditDelegate AssociateEditDelegate;

private:
	UPROPERTY()
	FRefAssociateData CurData;

	UPROPERTY()
	FRefAssociateData EditData;

private:
	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_ID;

	UPROPERTY(meta=(BindWidget))
	UMultiLineEditableTextBox* MlEtb_Name;

	UPROPERTY(meta=(BindWidget))
	UMultiLineEditableTextBox* Edt_Condition;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Expression;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Delete;

private:
	static FString FilePath;

};
