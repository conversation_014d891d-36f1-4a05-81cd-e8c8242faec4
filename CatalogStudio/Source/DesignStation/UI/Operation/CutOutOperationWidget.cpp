// Fill out your copyright notice in the Description page of Project Settings.

#include "CutOutOperationWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "OperatorWidgetConfig.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString UCutOutOperationWidget::CutOutOperationWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Operation/CutoutOperatorUI.CutoutOperatorUI_C'");

bool UCutOutOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(EdtExpression, Edt_Expression);
	BIND_PARAM_CPP_TO_UMG(BtnExpression, Btn_Expression);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_PARAM_CPP_TO_UMG(BtnEdit, Btn_Edit);
	BIND_PARAM_CPP_TO_UMG(BorCutoutHead, Bor_CutoutHead);
	BIND_PARAM_CPP_TO_UMG(BorCutoutExpression, Bor_CutoutExpression);
	BIND_PARAM_CPP_TO_UMG(BorCutoutValue, Bor_CutoutValue);
	BIND_PARAM_CPP_TO_UMG(BorEdit, Bor_Edit);
	BIND_PARAM_CPP_TO_UMG(TxtCutoutHead, Txt_CutoutHead);
	BIND_PARAM_CPP_TO_UMG(TxtEdit, Txt_Edit);


	BIND_WIDGET_FUNCTION(EdtExpression, OnTextCommitted, UCutOutOperationWidget::OnTextCommittedEdtExpression);
	BIND_WIDGET_FUNCTION(BtnExpression, OnClicked, UCutOutOperationWidget::OnClickedBtnExpression);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UCutOutOperationWidget::OnTextCommittedEdtValue);
	BIND_WIDGET_FUNCTION(BtnEdit, OnClicked, UCutOutOperationWidget::OnClickedBtnEdit);

	//BIND_WIDGET_FUNCTION(BtnEdit, OnClicked, UCutOutOperationWidget::OnClickedBtnEdit);
	//BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	//BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UCutOutOperationWidget::OnClickedBtnDelete);
	//BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	//BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UCutOutOperationWidget::OnClickedBtnUp);
	//BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	//BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UCutOutOperationWidget::OnClickedBtnDown);

	return true;
}

void UCutOutOperationWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetCutOutWidgetState(IsSelect, (int32)EColorType::Select);
}

void UCutOutOperationWidget::UpdateContent(const FSectionCutOutOperation& InData)
{
	CutOutOperationData.CopyData(InData);
	IsSelect = false;
	if (EdtExpression && EdtValue)
	{
		EdtExpression->SetText(FText::FromString(InData.CutOutValue.Expression));
		EdtValue->SetText(FText::FromString(InData.CutOutValue.Value));
	}
}

UCutOutOperationWidget* UCutOutOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UCutOutOperationWidget>(UCutOutOperationWidget::CutOutOperationWidgetPath);
}

void UCutOutOperationWidget::CutOutOperationExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)ECutOutExpressionType::CutOutExpression)
	{
		CutOutOperationData.CutOutValue.Expression = OutExpression;
		CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::CutOutExpression, CutOutOperationData);
	}
	else
	{
		checkNoEntry();
	}
}

void UCutOutOperationWidget::SetCutOutWidgetState(bool IsSelect, const int32& Type)
{
	if (BorBackground)
	{
		//UUIFunctionLibrary::SetBorderBrush(IsSelect, BorBackground);
		SetBorderColor(IsSelect, Type);
		SetTextColor(IsSelect);
	}
}

void UCutOutOperationWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetCutOutWidgetState(true, (int32)EColorType::Hover);
	}
}

void UCutOutOperationWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetCutOutWidgetState(false, (int32)EColorType::Hover);
	}
}

FReply UCutOutOperationWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		SetSelectState(true);
		OperationIdDelegate.ExecuteIfBound(IsSelect, CutOutOperationData.ID);
		FSectionOperationOrder CutOutSelectOrder(ESectionOperationType::ECutoutSection, CutOutOperationData.ID);
		OperationWidgetSelectDelegate.ExecuteIfBound(CutOutSelectOrder);
		return FReply::Handled();
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		SetSelectState(false);
		OperationIdDelegate.ExecuteIfBound(IsSelect, -1);
		FSectionOperationOrder CutOutUnSelectOrder(ESectionOperationType::ECutoutSection, -1);
		OperationWidgetSelectDelegate.ExecuteIfBound(CutOutUnSelectOrder);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UCutOutOperationWidget::SetBorderColor(bool IsSelect, const int32& Type)
{

	if (IS_OBJECT_PTR_VALID(BorCutoutHead) && IS_OBJECT_PTR_VALID(BorCutoutExpression) && IS_OBJECT_PTR_VALID(BorCutoutValue) && IS_OBJECT_PTR_VALID(BorEdit))
	{

		switch (Type)
		{
		case (int32)EColorType::Select:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorCutoutHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorCutoutExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorCutoutValue);
			break;
		case (int32)EColorType::Hover:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorCutoutHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorCutoutExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorCutoutValue);
			break;
		default:
			break;
		}
		//UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderUnselect : OperatorColor::BorderHoverOrSelect, BorEdit);
	}
}

void UCutOutOperationWidget::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtCutoutHead) && IS_OBJECT_PTR_VALID(TxtEdit))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtCutoutHead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextUnselect : OperatorColor::TextHoverOrSelect, TxtEdit);

	}
}

void UCutOutOperationWidget::OnClickedBtnDelete()
{
	CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::Delete, CutOutOperationData);
}

void UCutOutOperationWidget::OnClickedBtnUp()
{
	CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::Up, CutOutOperationData);
}

void UCutOutOperationWidget::OnClickedBtnDown()
{
	CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::Down, CutOutOperationData);
}

void UCutOutOperationWidget::OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared && Text.ToString() != CutOutOperationData.CutOutValue.Expression)
	{
		CutOutOperationData.CutOutValue.Expression = Text.ToString();
		CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::CutOutExpression, CutOutOperationData);
	}
	else
	{
		EdtExpression->SetText(FText::FromString(CutOutOperationData.CutOutValue.Expression));
	}
}

void UCutOutOperationWidget::OnClickedBtnExpression()
{
	if (EdtExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ECutOutExpressionType::CutOutExpression, EdtExpression->GetText().ToString(), FName(TEXT("CutOutOperationExpressionEdit")));
	}
}

void UCutOutOperationWidget::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CutOutOperationData.CutOutValue.Value = Text.ToString();
		CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::CutOutValue, CutOutOperationData);
	}
	else
	{
		EdtValue->SetText(FText::FromString(CutOutOperationData.CutOutValue.Value));
	}
}

void UCutOutOperationWidget::OnClickedBtnEdit()
{
	CutOutOperatorDataChangeDelegate.ExecuteIfBound((int32)ECutOutOperatorType::CutOutSection, CutOutOperationData);
}
