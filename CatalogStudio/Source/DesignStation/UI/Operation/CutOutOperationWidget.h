// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OperationBaseWidget.h"
#include "CutOutOperationWidget.generated.h"

/**
 * 
 */

class UBorder;
class UEditableText;
class UButton;
class UTextBlock;

UENUM(BlueprintType)
enum class ECutOutOperatorType : uint8
{
	CutOutExpression = 0,
	CutOutValue,
	CutOutSection,
	Delete,
	Up,
	Down
};

UENUM(BlueprintType)
enum class ECutOutExpressionType : uint8
{
	CutOutExpression = 0
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FCutOutOperatorEditDelegate, const int32&, EditType, const FSectionCutOutOperation&, CutOutData);

UCLASS()
class DESIGNSTATION_API UCutOutOperationWidget : public UOperationBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect) override;
	void UpdateContent(const FSectionCutOutOperation& InData);

	static UCutOutOperationWidget* Create();
	
private:
	UFUNCTION()
		void CutOutOperationExpressionEdit(const int32& EditType, const FString& OutExpression);
	void SetCutOutWidgetState(bool IsSelect, const int32& Type);

public:
	FCutOutOperatorEditDelegate CutOutOperatorDataChangeDelegate;

private:
	FSectionCutOutOperation CutOutOperationData;

	static FString CutOutOperationWidgetPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);
private:
	UFUNCTION() 
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();


	UFUNCTION()
		void OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExpression();
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnEdit();

private:
	UPROPERTY()
		UBorder*		BorBackground;
	UPROPERTY()
		UButton*		BtnDelete;
	UPROPERTY()
		UButton*		BtnUp;
	UPROPERTY()
		UButton*		BtnDown;
	UPROPERTY()
		UEditableText*	EdtExpression;
	UPROPERTY()
		UButton*		BtnExpression;
	UPROPERTY()
		UEditableText*	EdtValue;
	UPROPERTY()
		UButton*		BtnEdit;
	UPROPERTY()
		UTextBlock*		TxtCutoutHead;
	UPROPERTY()
		UTextBlock*		TxtEdit;
	UPROPERTY()
		UBorder*		BorCutoutHead;
	UPROPERTY()
		UBorder*		BorCutoutExpression;
	UPROPERTY()
		UBorder*		BorCutoutValue;
	UPROPERTY()
		UBorder*		BorEdit;

};
