// Fill out your copyright notice in the Description page of Project Settings.

#include "LoftingOperationWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "OperatorWidgetConfig.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"


FString ULoftingOperationWidget::LoftingWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Operation/LoftingOperatorUI.LoftingOperatorUI_C'");

bool ULoftingOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(BtnEdit, Btn_Edit);
	BIND_PARAM_CPP_TO_UMG(BorL<PERSON>ingHead,Bor_LoftingHead);
	BIND_PARAM_CPP_TO_UMG(BorEdit, Bor_Edit);
	BIND_PARAM_CPP_TO_UMG(TxtLoftingHead, Txt_LoftingHead);
	BIND_PARAM_CPP_TO_UMG(TxtEdit, Txt_Edit);
	
	BIND_WIDGET_FUNCTION(BtnEdit, OnClicked, ULoftingOperationWidget::OnClickedBtnEdit);
	//BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	//BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, ULoftingOperationWidget::OnClickedBtnDelete);
	//BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	//BIND_WIDGET_FUNCTION(BtnUp, OnClicked, ULoftingOperationWidget::OnClickedBtnUp);
	//BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	//BIND_WIDGET_FUNCTION(BtnDown, OnClicked, ULoftingOperationWidget::OnClickedBtnDown);

	return true;
}

void ULoftingOperationWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetLoftingWidgetState(IsSelect,(int32)EColorType::Select);
}

void ULoftingOperationWidget::UpdateContent(const FSectionOperationOrder & InLoftingOrder)
{
	SetSelectState(false);
	LoftingOrderData = InLoftingOrder;
}

ULoftingOperationWidget * ULoftingOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<ULoftingOperationWidget>(ULoftingOperationWidget::LoftingWidgetPath);
}

void ULoftingOperationWidget::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (!IsSelect)
	{
		SetLoftingWidgetState(true,(int32)EColorType::Hover);
	}
}

void ULoftingOperationWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	if (!IsSelect)
	{
		SetLoftingWidgetState(false,(int32)EColorType::Hover);
	}
}

FReply ULoftingOperationWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		SetSelectState(true);
		OperationIdDelegate.ExecuteIfBound(IsSelect, LoftingOrderData.Index);
		OperationWidgetSelectDelegate.ExecuteIfBound(LoftingOrderData);
		return FReply::Handled();
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		SetSelectState(false);
		OperationIdDelegate.ExecuteIfBound(IsSelect, -1);
		FSectionOperationOrder UnSelectOrder(ESectionOperationType::ELoftingSection, -1);
		OperationWidgetSelectDelegate.ExecuteIfBound(UnSelectOrder);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void ULoftingOperationWidget::SetLoftingWidgetState(bool _IsSelect, const int32& Type)
{
	if (BorBackground)
	{
		//UUIFunctionLibrary::SetBorderBrush(IsSelect, BorBackground);
		SetBorderColor(_IsSelect, Type);
		SetTextColor(_IsSelect);
	}
}

void ULoftingOperationWidget::SetBorderColor(bool IsSelect,const int32&Type)
{
	if (IS_OBJECT_PTR_VALID(BorLoftingHead)&&IS_OBJECT_PTR_VALID(BorEdit))
	{
		switch (Type)
		{
		case (int32)EColorType::Select:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorLoftingHead);
			break;
		case (int32)EColorType::Hover:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorLoftingHead);
			break;
		default:
			break;
		}
		//UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderUnselect : OperatorColor::BorderHoverOrSelect, BorEdit);
	}
}

void ULoftingOperationWidget::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtLoftingHead)&&IS_OBJECT_PTR_VALID(TxtEdit))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtLoftingHead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextUnselect : OperatorColor::TextHoverOrSelect, TxtEdit);

	}
}

void ULoftingOperationWidget::OnClickedBtnDelete()
{
	LoftingOperatorDelegate.ExecuteIfBound((int32)ELoftingOperatorType::Delete);
}

void ULoftingOperationWidget::OnClickedBtnUp()
{
	LoftingOperatorDelegate.ExecuteIfBound((int32)ELoftingOperatorType::Up);
}

void ULoftingOperationWidget::OnClickedBtnDown()
{
	LoftingOperatorDelegate.ExecuteIfBound((int32)ELoftingOperatorType::Down);
}

void ULoftingOperationWidget::OnClickedBtnEdit()
{
	LoftingOperatorDelegate.ExecuteIfBound((int32)ELoftingOperatorType::SectionEdit);
}
