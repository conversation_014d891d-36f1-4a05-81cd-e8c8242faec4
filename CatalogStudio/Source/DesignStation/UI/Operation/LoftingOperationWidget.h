// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OperationBaseWidget.h"
#include "LoftingOperationWidget.generated.h"

/**
 * 
 */

class UButton;
class UTextBlock;
class UBorder;

UENUM(BlueprintType)
enum class ELoftingOperatorType : uint8
{
	SectionEdit = 0,
	Delete,
	Up,
	Down
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FLoftingOperatorEditDelegate, const int32&, EditType);

UCLASS()
class DESIGNSTATION_API ULoftingOperationWidget : public UOperationBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect);
	void UpdateContent(const FSectionOperationOrder& InLoftingOrder);

	static ULoftingOperationWidget* Create();

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	void SetLoftingWidgetState(bool IsSelect,const int32& Type);

public:
	FLoftingOperatorEditDelegate LoftingOperatorDelegate;

private:
	FSectionOperationOrder LoftingOrderData;
	static FString LoftingWidgetPath;

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);

private:
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

	UFUNCTION()
		void OnClickedBtnEdit();

private:
	UPROPERTY()
		UBorder*	BorBackground;

	UPROPERTY()
		UButton*	BtnDelete;
	UPROPERTY()
		UButton*	BtnUp;
	UPROPERTY()
		UButton*	BtnDown;
	UPROPERTY()
		UButton*	BtnEdit;
	UPROPERTY()
		UBorder*	BorLoftingHead;
	UPROPERTY()
		UBorder*	BorEdit;
	UPROPERTY()
		UTextBlock*	TxtLoftingHead;
	UPROPERTY()
		UTextBlock*	TxtEdit;
};
