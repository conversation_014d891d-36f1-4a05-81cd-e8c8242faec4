// Fill out your copyright notice in the Description page of Project Settings.

#include "OperationBaseWidget.h"

bool UOperationBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	return true;
}

void UOperationBaseWidget::SetSelectState(bool _IsSelect)
{
}

void UOperationBaseWidget::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
}

void UOperationBaseWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
}

FReply UOperationBaseWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}
