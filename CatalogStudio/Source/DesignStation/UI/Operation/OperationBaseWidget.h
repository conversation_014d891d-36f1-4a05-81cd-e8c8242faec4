// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "OperationBaseWidget.generated.h"

/**
 * 
 */

DECLARE_DYNAMIC_DELEGATE_OneParam(FOperationSelectDelegate, const FSectionOperationOrder&, OrderData);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FOperationIdDelegate, bool,_IsSelect,const int32&, OperationId);

UCLASS()
class DESIGNSTATION_API UOperationBaseWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect);
protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

public:
	FOperationSelectDelegate	    OperationWidgetSelectDelegate;
	FOperationIdDelegate			OperationIdDelegate;
	bool IsSelect;

};

