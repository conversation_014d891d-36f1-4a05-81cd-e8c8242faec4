// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

UENUM(BlueprintType)
enum class EColorType : uint8
{
	Hover = 0,
	Select
};

namespace OperatorColor
{
	const FLinearColor BorderHeadUnselect	= FLinearColor(0.806952f, 0.806952f, 0.806952f, 1.0f);
	const FLinearColor BorderUnselect		= FLinearColor::White;
	const FLinearColor TextUnselect			= FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);
	const FLinearColor BorderSelect			= FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
	const FLinearColor BorderHover			= FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.3f);
	const FLinearColor TextHoverOrSelect	= FLinearColor::White;
}
