// Fill out your copyright notice in the Description page of Project Settings.

#include "RotationOperationWidget.h"

#include "Components/Button.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString URotationOperationWidget::RotationOperatorPath = TEXT("WidgetBlueprint'/Game/UI/Operation/RotationOperatorUI.RotationOperatorUI_C'");

bool URotationOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, URotationOperationWidget::OnClickedBtnSelect);

	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, URotationOperationWidget::OnClickedBtnDelete);
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, URotationOperationWidget::OnClickedBtnUp);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, URotationOperationWidget::OnClickedBtnDown);

	BIND_PARAM_CPP_TO_UMG(EdtXExpression, Edt_XExpression);
	BIND_WIDGET_FUNCTION(EdtXExpression, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtXExpression);
	BIND_PARAM_CPP_TO_UMG(BtnXExpression, Btn_XExpression);
	BIND_WIDGET_FUNCTION(BtnXExpression, OnClicked, URotationOperationWidget::OnClickedBtnXExpression);
	BIND_PARAM_CPP_TO_UMG(EdtXValue, Edt_XValue);
	BIND_WIDGET_FUNCTION(EdtXValue, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtXValue);

	BIND_PARAM_CPP_TO_UMG(EdtYExpression, Edt_YExpression);
	BIND_WIDGET_FUNCTION(EdtYExpression, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtYExpression);
	BIND_PARAM_CPP_TO_UMG(BtnYExpression, Btn_YExpression);
	BIND_WIDGET_FUNCTION(BtnYExpression, OnClicked, URotationOperationWidget::OnClickedBtnYExpression);
	BIND_PARAM_CPP_TO_UMG(EdtYValue, Edt_YValue);
	BIND_WIDGET_FUNCTION(EdtYValue, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtYValue);

	BIND_PARAM_CPP_TO_UMG(EdtZExpression, Edt_ZExpression);
	BIND_WIDGET_FUNCTION(EdtZExpression, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtZExpression);
	BIND_PARAM_CPP_TO_UMG(BtnZExpression, Btn_ZExpression);
	BIND_WIDGET_FUNCTION(BtnZExpression, OnClicked, URotationOperationWidget::OnClickedBtnZExpression);
	BIND_PARAM_CPP_TO_UMG(EdtZValue, Edt_ZValue);
	BIND_WIDGET_FUNCTION(EdtZValue, OnTextCommitted, URotationOperationWidget::OnTextCommittedEdtZValue);

	BIND_PARAM_CPP_TO_UMG(EdtStartAngle, Edt_StartAngle);
	BIND_WIDGET_FUNCTION(EdtStartAngle, OnTextCommitted, URotationOperationWidget::OnTextCommittedStartAngle);
	BIND_PARAM_CPP_TO_UMG(EdtEndAngle, Edt_EndAngle);
	BIND_WIDGET_FUNCTION(EdtEndAngle, OnTextCommitted, URotationOperationWidget::OnTextCommittedEndAngle);

	return true;
}

void URotationOperationWidget::UpdateContent(const FSectionRotatorOperation& InRotationData)
{
	RotationData = InRotationData;
	if (EdtXExpression && EdtXValue && EdtYExpression && EdtYValue
		&& EdtZExpression && EdtZValue && EdtStartAngle && EdtEndAngle)
	{
		EdtXExpression->SetText(FText::FromString(InRotationData.RotationX.Expression));
		EdtXValue->SetText(FText::FromString(InRotationData.RotationX.Value));
		EdtYExpression->SetText(FText::FromString(InRotationData.RotationY.Expression));
		EdtYValue->SetText(FText::FromString(InRotationData.RotationY.Value));
		EdtZExpression->SetText(FText::FromString(InRotationData.RotationZ.Expression));
		EdtZValue->SetText(FText::FromString(InRotationData.RotationZ.Value));
		EdtStartAngle->SetText(FText::FromString(FString::SanitizeFloat(InRotationData.StartAngle)));
		EdtEndAngle->SetText(FText::FromString(FString::SanitizeFloat(InRotationData.EndAngle)));
	}
}

URotationOperationWidget* URotationOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<URotationOperationWidget>(URotationOperationWidget::RotationOperatorPath);
}

void URotationOperationWidget::RotationExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	switch ((ERotationExpressionType)EditType)
	{
	case ERotationExpressionType::XExpression:
	{
		RotationData.RotationX.Expression = OutExpression;
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::XExpression, RotationData);
	}
	case ERotationExpressionType::YExpression:
	{
		RotationData.RotationY.Expression = OutExpression;
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::YExpression, RotationData);
	}
	case ERotationExpressionType::ZExpression:
	{
		RotationData.RotationZ.Expression = OutExpression;
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::ZExpression, RotationData);
	}
	default:
		break;
	}
}

void URotationOperationWidget::OnClickedBtnSelect()
{
}

void URotationOperationWidget::OnClickedBtnDelete()
{
	RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::Delete, RotationData);
}

void URotationOperationWidget::OnClickedBtnUp()
{
	RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::Up, RotationData);
}

void URotationOperationWidget::OnClickedBtnDown()
{
	RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::Down, RotationData);
}

void URotationOperationWidget::OnTextCommittedEdtXExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationX.Expression = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::XExpression, RotationData);
	}
}

void URotationOperationWidget::OnClickedBtnXExpression()
{
	if (EdtXExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ERotationExpressionType::XExpression, EdtXExpression->GetText().ToString(), FName(TEXT("RotationExpressionEdit")));
	}
}

void URotationOperationWidget::OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationX.Value = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::XValue, RotationData);
	}
}

void URotationOperationWidget::OnTextCommittedEdtYExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationY.Expression = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::YExpression, RotationData);
	}
}

void URotationOperationWidget::OnClickedBtnYExpression()
{
	if (EdtYExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ERotationExpressionType::YExpression, EdtYExpression->GetText().ToString(), FName(TEXT("RotationExpressionEdit")));
	}
}

void URotationOperationWidget::OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationY.Value = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::YValue, RotationData);
	}
}

void URotationOperationWidget::OnTextCommittedEdtZExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationZ.Expression = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::ZExpression, RotationData);
	}
}

void URotationOperationWidget::OnClickedBtnZExpression()
{
	if (EdtZExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ERotationExpressionType::ZExpression, EdtZExpression->GetText().ToString(), FName(TEXT("RotationExpressionEdit")));
	}
}

void URotationOperationWidget::OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.RotationZ.Value = Text.ToString();
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::ZValue, RotationData);
	}
}

void URotationOperationWidget::OnTextCommittedStartAngle(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.StartAngle = FCString::Atof(*Text.ToString());
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::StartAngle, RotationData);
	}
}

void URotationOperationWidget::OnTextCommittedEndAngle(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod == ETextCommit::Type::OnEnter)
	{
		RotationData.EndAngle = FCString::Atof(*Text.ToString());
		RotationOperatorDataChangeDelegate.ExecuteIfBound((int32)ERotationOperatorType::EndAngle, RotationData);
	}
}
