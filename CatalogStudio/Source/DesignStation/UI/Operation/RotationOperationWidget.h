// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OperationBaseWidget.h"
#include "DesignStation/Geometry/DataDefines/SectionRotatorOperation.h"
#include "RotationOperationWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class ERotationOperatorType : uint8
{
	Delete = 0,
	Up,
	Down,
	XExpression,
	XValue,
	YExpression,
	YValue,
	ZExpression,
	ZValue,
	StartAngle,
	EndAngle
};

UENUM(BlueprintType)
enum class ERotationExpressionType : uint8
{
	XExpression = 0,
	YExpression,
	ZExpression
};

class UEditableText;
class UButton;
class UBorder;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FRotationOperatorEditDelegate, const int32&, EditType, const FSectionRotatorOperation&, RotationData);

UCLASS()
class DESIGNSTATION_API URotationOperationWidget : public UOperationBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FSectionRotatorOperation& InRotationData);

	static URotationOperationWidget* Create();

private:
	UFUNCTION()
		void RotationExpressionEdit(const int32& EditType, const FString& OutExpression);

public:
	FRotationOperatorEditDelegate RotationOperatorDataChangeDelegate;

private:
	FSectionRotatorOperation RotationData;

	static FString RotationOperatorPath;

protected:
	UFUNCTION()
		void OnClickedBtnSelect();

	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

	UFUNCTION()
		void OnTextCommittedEdtXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnXExpression();
	UFUNCTION()
		void OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnYExpression();
	UFUNCTION()
		void OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtZExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnZExpression();
	UFUNCTION()
		void OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedStartAngle(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEndAngle(const FText& Text, ETextCommit::Type CommitMethod);
	
private:
	UPROPERTY()
		UButton* BtnSelect;
	UPROPERTY()
		UBorder* BorBackground;

	UPROPERTY()
		UButton* BtnDelete;
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;

	UPROPERTY()
		UEditableText* EdtXExpression;
	UPROPERTY()
		UButton* BtnXExpression;
	UPROPERTY()
		UEditableText* EdtXValue;

	UPROPERTY()
		UEditableText* EdtYExpression;
	UPROPERTY()
		UButton* BtnYExpression;
	UPROPERTY()
		UEditableText* EdtYValue;

	UPROPERTY()
		UEditableText* EdtZExpression;
	UPROPERTY()
		UButton* BtnZExpression;
	UPROPERTY()
		UEditableText* EdtZValue;

	UPROPERTY()
		UEditableText* EdtStartAngle;
	UPROPERTY()
		UEditableText* EdtEndAngle;
};
