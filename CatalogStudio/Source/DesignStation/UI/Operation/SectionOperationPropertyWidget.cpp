// Fill out your copyright notice in the Description page of Project Settings.

#include "SectionOperationPropertyWidget.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "SectionOperatorToolBarWidget.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#define LOCTEXT_NAMESPACE "Section operation"

FString USectionOperationPropertyWidget::SectionOperatorPropertyPath = TEXT("WidgetBlueprint'/Game/UI/Operation/SectionOperatorPropertyUI.SectionOperatorPropertyUI_C'");

bool USectionOperationPropertyWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	/*BIND_PARAM_CPP_TO_UMG(BorSectionOperator, Bor_SectionOperator);
	if (UEditableText* Edt_VisibilityExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_VisibilityExpression"))))
	{
		EdtVisibleExpression = Edt_VisibilityExpression;
		EdtVisibleExpression->OnTextCommitted.AddUniqueDynamic(this, &USectionOperationPropertyWidget::OnTextCommittedEdtVisibleExpression);
	}
	if (UButton* Btn_VisibilityExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_VisibilityExpression"))))
	{
		BtnVisibleExpression = Btn_VisibilityExpression;
		BtnVisibleExpression->OnClicked.AddUniqueDynamic(this, &USectionOperationPropertyWidget::OnClickedBtnVisibleExpression);
	}
	if (UEditableText* Edt_VisibilityValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_VisibilityValue"))))
	{
		EdtVisibleValue = Edt_VisibilityValue;
		EdtVisibleValue->OnTextCommitted.AddUniqueDynamic(this, &USectionOperationPropertyWidget::OnTextCommittedEdtVisibleValue);
	}
	if (UScrollBox* Scb_Content = Cast<UScrollBox>(GetWidgetFromName(TEXT("Scb_Content"))))
	{
		ScbContent = Scb_Content;
	}
	BIND_PARAM_CPP_TO_UMG(BorLoftingAndCutOutData, Bor_LoftingData);
	BIND_PARAM_CPP_TO_UMG(ScbLoftingAndCutOutData, Scb_LoftingData);*/
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, USectionOperationPropertyWidget::OnClickedBtnUp);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, USectionOperationPropertyWidget::OnClickedBtnDown);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, USectionOperationPropertyWidget::OnClickedBtnDelete);

	BIND_PARAM_CPP_TO_UMG(BorSectionOperator, Bor_SectionOperator);
	BIND_PARAM_CPP_TO_UMG(ScbContent, Scb_Content);
	BIND_PARAM_CPP_TO_UMG(BorLoftingAndCutOutData, Bor_LoftingData);
	BIND_PARAM_CPP_TO_UMG(TxtDataTitle, Txt_DataTitle);
	BIND_PARAM_CPP_TO_UMG(ScbLoftingAndCutOutData, Scb_LoftingData);

	OldEditType = -1;
	OldWidgetId = -1;

	return true;
}
void USectionOperationPropertyWidget::UpdateContent(const FSectionOperation & InSectionOperation, const FSectionOperationOrder& _SelectOrder)
{
	checkf(ScbContent, TEXT("operator content is null"));
	ScbContent->ClearChildren();
	TensileOperatorMaps.Empty();
	ShiftingOperatorMaps.Empty();
	ZoomOperatorMaps.Empty();
	CutOutOperatorMaps.Empty();
	OperationData.CopyData(InSectionOperation);

	SelectOrder = _SelectOrder;
	for (auto& OrderData : InSectionOperation.OperatorOrder)
	{
		switch (OrderData.OperatorType)
		{
		case ESectionOperationType::EDrawSection :
		{
			FSectionDrawOperation TansileOperatorTemp = InSectionOperation.DrawOperations[OrderData.Index];
			UTensileOperationWidget* TansileOperatorItem = UUIFunctionLibrary::CreatePropertyView<UTensileOperationWidget, FSectionDrawOperation>(TansileOperatorTemp);
			TansileOperatorItem->TensileOperatorDataChangeDelegate.BindUFunction(this, FName(TEXT("TensileOperatorEdit")));
			TansileOperatorItem->OperationWidgetSelectDelegate.BindUFunction(this, FName(TEXT("OperationSelectEdit")));
			TansileOperatorItem->OperationIdDelegate.BindUFunction(this, FName(TEXT("SetSelectId")));
			ScbContent->AddChild(TansileOperatorItem);
			TensileOperatorMaps.Add(TansileOperatorTemp.ID, TansileOperatorItem);
			TansileOperatorItem->SetVisibility(ESlateVisibility::Visible);

			break;
		}
		case ESectionOperationType::EShiftSection :
		{
			FSectionShiftingOperation ShiftingOperatorTemp = InSectionOperation.ShiftOperations[OrderData.Index];
			UShiftingOperationWidget* ShiftingOperatorItem = UUIFunctionLibrary::CreatePropertyView<UShiftingOperationWidget, FSectionShiftingOperation>(ShiftingOperatorTemp);
			ShiftingOperatorItem->ShiftingOperationDelegate.BindUFunction(this, FName(TEXT("ShiftingOperatorEdit")));
			ShiftingOperatorItem->OperationWidgetSelectDelegate.BindUFunction(this, FName(TEXT("OperationSelectEdit")));
			ShiftingOperatorItem->OperationIdDelegate.BindUFunction(this, FName(TEXT("SetSelectId")));
			ScbContent->AddChild(ShiftingOperatorItem);
			ShiftingOperatorMaps.Add(ShiftingOperatorTemp.ID, ShiftingOperatorItem);
			ShiftingOperatorItem->SetVisibility(ESlateVisibility::Visible);
			break;
		}
		case ESectionOperationType::EZoomSection :
		{
			FSectionZoomOperation ZoomOperationTemp = InSectionOperation.ZoomOperations[OrderData.Index];
			UZoomOperationWidget* ZoomOperationItem = UUIFunctionLibrary::CreatePropertyView<UZoomOperationWidget, FSectionZoomOperation>(ZoomOperationTemp);
			ZoomOperationItem->ZoomOperationDelegate.BindUFunction(this, FName(TEXT("ZoomOperatorEdit")));
			ZoomOperationItem->OperationWidgetSelectDelegate.BindUFunction(this, FName(TEXT("OperationSelectEdit")));
			ZoomOperationItem->OperationIdDelegate.BindUFunction(this, FName(TEXT("SetSelectId")));
			ScbContent->AddChild(ZoomOperationItem);
			ZoomOperatorMaps.Add(ZoomOperationTemp.ID, ZoomOperationItem);
			ZoomOperationItem->SetVisibility(ESlateVisibility::Visible);
			break;
		}
		case ESectionOperationType::ECutoutSection :
		{
			FSectionCutOutOperation CutOutOperationTemp = InSectionOperation.CutoutOperations[OrderData.Index];
			UCutOutOperationWidget* CutOutOperationItem = UUIFunctionLibrary::CreatePropertyView<UCutOutOperationWidget, FSectionCutOutOperation>(CutOutOperationTemp);
			CutOutOperationItem->CutOutOperatorDataChangeDelegate.BindUFunction(this, FName("CutOutOperatorEdit"));
			CutOutOperationItem->OperationWidgetSelectDelegate.BindUFunction(this, FName("OperationSelectEdit"));
			CutOutOperationItem->OperationIdDelegate.BindUFunction(this, FName(TEXT("SetSelectId")));
			ScbContent->AddChild(CutOutOperationItem);
			CutOutOperatorMaps.Add(CutOutOperationTemp.ID, CutOutOperationItem);
			CutOutOperationItem->SetVisibility(ESlateVisibility::Visible);
			break;
		}
		case ESectionOperationType::ELoftingSection : 
		{
			if (!LoftingOperatorWidget)
			{
				LoftingOperatorWidget = UUIFunctionLibrary::CreateUIWidget<ULoftingOperationWidget>();
			}
			if (LoftingOperatorWidget)
			{
				LoftingOperatorWidget->UpdateContent(OrderData);
				LoftingOperatorWidget->LoftingOperatorDelegate.BindUFunction(this, FName("LoftingOperatorEdit"));
				LoftingOperatorWidget->OperationWidgetSelectDelegate.BindUFunction(this, FName("OperationSelectEdit"));
				LoftingOperatorWidget->OperationIdDelegate.BindUFunction(this, FName("SetSelectId"));
				ScbContent->AddChild(LoftingOperatorWidget);
				LoftingOperatorWidget->SetVisibility(ESlateVisibility::Visible);
			}
			break;
		}
		case ESectionOperationType::ERotatorSection :
		{
			if (!RotationOperatorWidget)
			{
				RotationOperatorWidget = UUIFunctionLibrary::CreateUIWidget<URotationOperationWidget>();
			}
			if (RotationOperatorWidget)
			{
				//RotationOperatorWidget->UpdateContent(InSectionOperation.RotatorOperation);
				RotationOperatorWidget->RotationOperatorDataChangeDelegate.BindUFunction(this, FName(TEXT("RotationOperatorEdit")));
				RotationOperatorWidget->OperationWidgetSelectDelegate.BindUFunction(this, FName("OperationSelectEdit"));
				ScbContent->AddChild(RotationOperatorWidget);
				RotationOperatorWidget->SetVisibility(ESlateVisibility::Visible);
			}
			break;
		}
		default:
		{
			checkNoEntry();
			break; 
		}
		}
	}

	if (InSectionOperation.OperatorOrder.IsValidIndex(_SelectOrder.Index))
	{
		switch (SelectOrder.OperatorType)
		{
		case ESectionOperationType::EDrawSection:
			TensileOperatorMaps[SelectOrder.Index]->SetSelectState(true); break;
		case ESectionOperationType::EZoomSection:
			ZoomOperatorMaps[SelectOrder.Index]->SetSelectState(true); break;
		case ESectionOperationType::EShiftSection:
			ShiftingOperatorMaps[SelectOrder.Index]->SetSelectState(true); break;
		case ESectionOperationType::ECutoutSection:
			CutOutOperatorMaps[SelectOrder.Index]->SetSelectState(true); break;
		case ESectionOperationType::ELoftingSection:
			LoftingOperatorWidget->SetSelectState(true); break;
		default:
			break;
		}
	}
	FlipShowOperatorProperty(true);
	
}

void USectionOperationPropertyWidget::UpdateContent(const FSectionLoftOperation & InLoftingData)
{
	checkf(ScbLoftingAndCutOutData, TEXT("lofting operator Scb_Content is null!"));
	ScbLoftingAndCutOutData->ClearChildren();
	ClearOperatorData();
	SetTxtDataTitle(FText::FromStringTable(FName("PosSt"), TEXT("Lofting Data")).ToString());
	if (!IS_OBJECT_PTR_VALID(LineTitleWidget))
	{
		LineTitleWidget = USingleComponentTitleWidget::Create();
		LineTitleWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Line")));
		LineTitleWidget->SetVisibility(ESlateVisibility::Visible);

	}
	if (!IS_OBJECT_PTR_VALID(PointTitleWidget))
	{
		PointTitleWidget = USingleComponentTitleWidget::Create();
		PointTitleWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Point")));
		PointTitleWidget->SetVisibility(ESlateVisibility::Visible);
	}
	ScbLoftingAndCutOutData->AddChild(PointTitleWidget);
	for (int32 i = 0; i < InLoftingData.Points.Num(); ++i)
	{
		auto pProperty = InLoftingData.Points[i];
		pProperty.ID = OperatorPoints.Num();
		USingleComponentPoint* LoftingPointTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentPoint, FGeomtryPointProperty>(pProperty);
		LoftingPointTemp->UpdatePointIndex(i + 1);
		LoftingPointTemp->PointDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorPointEdit")));
		LoftingPointTemp->PointWidgetSelectDelegate.BindUFunction(this, FName(TEXT("LoftingDataWidgetSelectEdit")));
		LoftingPointTemp->SetVisibility(ESlateVisibility::Visible);
		ScbLoftingAndCutOutData->AddChild(LoftingPointTemp);
		OperatorPoints.Add(pProperty.ID, LoftingPointTemp);
	}
	ScbLoftingAndCutOutData->AddChild(LineTitleWidget);
	for (int32 i = 0; i < InLoftingData.Lines.Num(); ++i)
	{
		auto lProperty = InLoftingData.Lines[i];
		lProperty.ID = OperatorLines.Num();
		USingleComponentLine* LoftingLineTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentLine, FGeomtryLineProperty>(lProperty);
		LoftingLineTemp->UpdateLineIndex(i + 1);
		LoftingLineTemp->LinePropertyChangeDelegate.BindUFunction(this, FName(TEXT("OperatorLineEdit")));
		LoftingLineTemp->LineWidgetSelectDelegate.BindUFunction(this, FName(TEXT("LoftingDataWidgetSelectEdit")));
		LoftingLineTemp->SetVisibility(ESlateVisibility::Visible);
		ScbLoftingAndCutOutData->AddChild(LoftingLineTemp);
		OperatorLines.Add(lProperty.ID, LoftingLineTemp);
	}
	FlipShowOperatorProperty(false);
}

void USectionOperationPropertyWidget::UpdateContent(const FSectionCutOutOperation & InCutOutData)
{
	checkf(ScbLoftingAndCutOutData, TEXT("lofting operator Scb_Content is null!"));
	ScbLoftingAndCutOutData->ClearChildren();
	ClearOperatorData();
	SetTxtDataTitle(FText::FromStringTable(FName("PosSt"), TEXT("Cutout Data")).ToString());

	if (InCutOutData.SectionType == ESectionType::ECustomPlan)
	{
		if (!IS_OBJECT_PTR_VALID(LineTitleWidget))
		{
			LineTitleWidget = USingleComponentTitleWidget::Create();
		}
		if(IS_OBJECT_PTR_VALID(LineTitleWidget))
		{
			LineTitleWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Line")));
			LineTitleWidget->SetVisibility(ESlateVisibility::Visible);
		}
		if (!IS_OBJECT_PTR_VALID(PointTitleWidget))
		{
			PointTitleWidget = USingleComponentTitleWidget::Create();
		}
		if(IS_OBJECT_PTR_VALID(PointTitleWidget))
		{		
			PointTitleWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Point")));
			PointTitleWidget->SetVisibility(ESlateVisibility::Visible);
		}
		ScbLoftingAndCutOutData->AddChild(PointTitleWidget);
		for (int32 i = 0; i < InCutOutData.Points.Num(); ++i)
		{
			auto pProperty = InCutOutData.Points[i];
			pProperty.ID = OperatorPoints.Num();
			USingleComponentPoint* CutOutPointTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentPoint, FGeomtryPointProperty>(pProperty);
			CutOutPointTemp->UpdatePointIndex(i + 1);
			CutOutPointTemp->PointDataChangeDelegate.BindUFunction(this, FName(TEXT("OperatorPointEdit")));
			CutOutPointTemp->PointWidgetSelectDelegate.BindUFunction(this, FName(TEXT("LoftingDataWidgetSelectEdit")));
			CutOutPointTemp->SetVisibility(ESlateVisibility::Visible);
			ScbLoftingAndCutOutData->AddChild(CutOutPointTemp);
			OperatorPoints.Add(pProperty.ID, CutOutPointTemp);
		}
		ScbLoftingAndCutOutData->AddChild(LineTitleWidget);
		for (int32 i = 0; i < InCutOutData.Lines.Num(); ++i)
		{
			auto lProperty = InCutOutData.Lines[i];
			lProperty.ID = OperatorLines.Num();
			USingleComponentLine* CutOutLineTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentLine, FGeomtryLineProperty>(lProperty);
			CutOutLineTemp->UpdateLineIndex(i + 1);
			CutOutLineTemp->LinePropertyChangeDelegate.BindUFunction(this, FName(TEXT("OperatorLineEdit")));
			CutOutLineTemp->LineWidgetSelectDelegate.BindUFunction(this, FName(TEXT("LoftingDataWidgetSelectEdit")));
			CutOutLineTemp->SetVisibility(ESlateVisibility::Visible);
			ScbLoftingAndCutOutData->AddChild(CutOutLineTemp);
			OperatorLines.Add(lProperty.ID, CutOutLineTemp);
		}
	}
	else if (InCutOutData.SectionType == ESectionType::ERectangle)
	{
		OperatorRectangle = UUIFunctionLibrary::CreatePropertyView<USingleComponentRectangle, FGeomtryRectanglePlanProperty>(InCutOutData.Rectangle);
		OperatorRectangle->RectangleChangeDelegate.BindUFunction(this, FName(TEXT("OperatorRectangleEdit")));
		OperatorRectangle->SetVisibility(ESlateVisibility::Visible);
		ScbLoftingAndCutOutData->AddChild(OperatorRectangle);
	}
	else if (InCutOutData.SectionType == ESectionType::EEllipse)
	{
		OperatorEllipse = UUIFunctionLibrary::CreatePropertyView<USingleComponentEllipse, FGeomtryEllipsePlanProperty>(InCutOutData.Ellipse);
		OperatorEllipse->EllipsePropertyChangeDelegate.BindUFunction(this, FName(TEXT("OperatorEllipseEdit")));
		OperatorEllipse->SetVisibility(ESlateVisibility::Visible);
		ScbLoftingAndCutOutData->AddChild(OperatorEllipse);
	}
	FlipShowOperatorProperty(false);
}

void USectionOperationPropertyWidget::UpdateSelectSectionWidgetStyle(const int32 & EditType, const int32 & WidgetId)
{
	UpdateOldSelectWidget(EditType, WidgetId);
	if (WidgetId == -1)
	{
		return;
	}
	UpdateViewSelectWidgetState(EditType, WidgetId, true);
	if (EditType == (int32)ESectionSelectType::Point)
	{
		if (ScbLoftingAndCutOutData)
		{
			ScbLoftingAndCutOutData->ScrollWidgetIntoView(OperatorPoints[WidgetId], true, EDescendantScrollDestination::Center);
		}
	}
	else if (EditType == (int32)ESectionSelectType::Line)
	{
		if (ScbLoftingAndCutOutData)
		{
			ScbLoftingAndCutOutData->ScrollWidgetIntoView(OperatorLines[WidgetId], true, EDescendantScrollDestination::Center);
		}
	}
}

void USectionOperationPropertyWidget::UpdateHoverSectionWidgetStyle(const int32 & EditType, const int32 & WidgetId, bool IsHovered)
{
	if (WidgetId == -1)
	{
		return;
	}
	switch ((ESectionSelectType)EditType)
	{
	case ESectionSelectType::Point:
	{
		OperatorPoints[WidgetId]->UpdateSelectState(IsHovered);
		if (ScbLoftingAndCutOutData && IsHovered)
		{
			ScbLoftingAndCutOutData->ScrollWidgetIntoView(OperatorPoints[WidgetId], true, EDescendantScrollDestination::Center);
		}
		break;
	}
	case ESectionSelectType::Line:
	{
		OperatorLines[WidgetId]->UpdateSelectState(IsHovered);
		if (ScbLoftingAndCutOutData && IsHovered)
		{
			ScbLoftingAndCutOutData->ScrollWidgetIntoView(OperatorLines[WidgetId], true, EDescendantScrollDestination::Center);
		}
		break;
	}
	case ESectionSelectType::Rectangle:break;
	case ESectionSelectType::Ellipse:break;
	case ESectionSelectType::Cuboid:break;
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USectionOperationPropertyWidget::FlipShowOperatorProperty(bool IsSectionOperation)
{
	if (BorSectionOperator && BorLoftingAndCutOutData)
	{
		BorSectionOperator->SetVisibility(IsSectionOperation ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BorLoftingAndCutOutData->SetVisibility(IsSectionOperation ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void USectionOperationPropertyWidget::ChangeOperateButtonState(bool IsOperateSelect)
{
	if (IS_OBJECT_PTR_VALID(BtnUp) && IS_OBJECT_PTR_VALID(BtnDown) && IS_OBJECT_PTR_VALID(BtnDelete))
	{
		BtnUp->SetIsEnabled(IsOperateSelect);
		BtnDown->SetIsEnabled(IsOperateSelect);
		BtnDelete->SetIsEnabled(IsOperateSelect);
	}
}

void USectionOperationPropertyWidget::ClearOperatorData()
{
	OperatorPoints.Empty();
	OperatorLines.Empty();
	OperatorRectangle = nullptr;
	OperatorEllipse = nullptr;
}

void USectionOperationPropertyWidget::UpdateOldSelectWidget(const int32 & EditType, const int32 & WidgetId)
{
	if (OldWidgetId != -1)
	{
		UpdateViewSelectWidgetState(OldEditType, OldWidgetId, false);
		OldEditType = -1;
		OldWidgetId = -1;
	}
	if (WidgetId != -1)
	{
		OldEditType = EditType;
		OldWidgetId = WidgetId;
	}
}

void USectionOperationPropertyWidget::UpdateViewSelectWidgetState(const int32 & EditType, const int32 & WidgetId, bool IsSelect)
{
	switch ((ESectionSelectType)EditType)
	{
	case ESectionSelectType::Point:
	{
		if (OperatorPoints.Find(WidgetId))
		{
			OperatorPoints[WidgetId]->UpdateSelectState(IsSelect);
			OperatorPoints[WidgetId]->SetViewPartSelect(IsSelect);
		}	
		break;
	}
	case ESectionSelectType::Line:
	{
		if (OperatorLines.Find(WidgetId))
		{
			OperatorLines[WidgetId]->UpdateSelectState(IsSelect);
			OperatorLines[WidgetId]->SetViewPartSelect(IsSelect);
		}	
		break;
	}
	case ESectionSelectType::Rectangle:break;
	case ESectionSelectType::Ellipse:break;
	case ESectionSelectType::Cuboid:break;
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

int32 USectionOperationPropertyWidget::JudgeOperationInsert()
{
	if (SelectOrder.Index == -1)
	{
		return -1;
	}
	return OperationData.OperatorOrder.Find(SelectOrder);
}

USectionOperationPropertyWidget * USectionOperationPropertyWidget::Create()
{
	UClass* OperatorPropertyBp = LoadClass<UUserWidget>(NULL, *SectionOperatorPropertyPath);
	checkf(OperatorPropertyBp, TEXT("load operator proeprty bp error!"));
	USectionOperationPropertyWidget* OperatorPropertyItem = CreateWidget<USectionOperationPropertyWidget>(GWorld.GetReference(), OperatorPropertyBp);
	checkf(OperatorPropertyItem, TEXT("create operator property item error!"));
	return OperatorPropertyItem;
}

int32 USectionOperationPropertyWidget::GetSelectId()
{
	return SelectId;
}

void USectionOperationPropertyWidget::OperationSelectEdit(const FSectionOperationOrder & OrderData)
{
	if (SelectOrder.Index != -1)
	{
		switch (SelectOrder.OperatorType)
		{
		case ESectionOperationType::EDrawSection:
			if (SelectOrder.Index != SelectId)
			{
				TensileOperatorMaps[SelectOrder.Index]->SetSelectState(false);
			}
			break;
		case ESectionOperationType::EShiftSection:
			if (SelectOrder.Index != SelectId)
			{
				ShiftingOperatorMaps[SelectOrder.Index]->SetSelectState(false); 
			}
			break;
		case ESectionOperationType::EZoomSection:
			if (SelectOrder.Index != SelectId)
			{
			ZoomOperatorMaps[SelectOrder.Index]->SetSelectState(false); 
			}
			break;
		case ESectionOperationType::ECutoutSection:
			if (SelectOrder.Index != SelectId)
			{
				CutOutOperatorMaps[SelectOrder.Index]->SetSelectState(false); 
			}
			break;
		case ESectionOperationType::ELoftingSection:
			if (SelectOrder.Index != SelectId)
			{ 
				LoftingOperatorWidget->SetSelectState(false); 
			}
			break;
		default:checkNoEntry();break;
		}
	}
	SelectOrder = OrderData;
	OperationSelectDelegate.ExecuteIfBound(SelectOrder);
}

void USectionOperationPropertyWidget::SetSelectId(bool _IsSelect,const int32& id)
{
	if (_IsSelect)
	{
		SelectId = id;
		return;
	}
	SelectId = -1;
}

void USectionOperationPropertyWidget::TensileOperatorEdit(const int32& EditType, const FSectionDrawOperation& TensileData)
{
	TensileOperatorEditDelegate.ExecuteIfBound(EditType, TensileData);
}

void USectionOperationPropertyWidget::ShiftingOperatorEdit(const int32& EditType, const FSectionShiftingOperation & ShiftingData)
{
	ShiftingOperationEditDelegate.ExecuteIfBound(EditType, ShiftingData);
}

void USectionOperationPropertyWidget::ZoomOperatorEdit(const int32& EditType, const FSectionZoomOperation & ZoomData)
{
	ZoomOperationEditDelegate.ExecuteIfBound(EditType, ZoomData);
}

void USectionOperationPropertyWidget::CutOutOperatorEdit(const int32 & EditType, const FSectionCutOutOperation & CutOutData)
{
	CutOutOperatorEditDelegate.ExecuteIfBound(EditType, CutOutData);
}

void USectionOperationPropertyWidget::LoftingOperatorEdit(const int32& EditType)
{
	LoftingOperatorEditDelegate.ExecuteIfBound(EditType);
}

void USectionOperationPropertyWidget::RotationOperatorEdit(const int32 & EditType, const FSectionRotatorOperation & RotationData)
{
	RotationOperatorEditDelegate.ExecuteIfBound(EditType, RotationData);
}

void USectionOperationPropertyWidget::VisibleEdit(const int32 & EditType, const FString & InExpression)
{
	if (EditType == (int32)EOperatorExpressionType::VisibleExpression)
	{
		
	}
	else
	{
		checkNoEntry();
	}
}

void USectionOperationPropertyWidget::OperatorPointEdit(const int32 & EditType, const FGeomtryPointProperty & PointData)
{
	PointDataChangeDelegate.ExecuteIfBound(EditType, PointData);
}

void USectionOperationPropertyWidget::OperatorLineEdit(const int32 & EditType, const FGeomtryLineProperty & LineProperty)
{
	LineDataChangeDelegate.ExecuteIfBound(EditType, LineProperty);
}

void USectionOperationPropertyWidget::OperatorRectangleEdit(const int32 & EditType, const FGeomtryRectanglePlanProperty & RectangleProperty)
{
	RectangleDataChangeDelegate.ExecuteIfBound(EditType, RectangleProperty);
}

void USectionOperationPropertyWidget::OperatorEllipseEdit(const int32 & EditType, const FGeomtryEllipsePlanProperty & EllipseProperty)
{
	EllipseDataChangeDelegate.ExecuteIfBound(EditType, EllipseProperty);
}

void USectionOperationPropertyWidget::LoftingDataWidgetSelectEdit(const int32 & EditType, const int32 & WidgetId, bool IsOver, bool OnProperty)
{
	DataWidgetSelectDelegate.ExecuteIfBound(EditType, WidgetId, IsOver, OnProperty);
}

void USectionOperationPropertyWidget::OnClickedBtnUp()
{
	WidgetOperationDelegate.ExecuteIfBound((int32)EWidgetOperationType::OperationUp, SelectOrder);
}

void USectionOperationPropertyWidget::OnClickedBtnDown()
{
	WidgetOperationDelegate.ExecuteIfBound((int32)EWidgetOperationType::OperationDown, SelectOrder);
	
}

void USectionOperationPropertyWidget::OnClickedBtnDelete()
{
	if (SelectOrder.Index != -1)
	{
		bool Result = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(
			FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
			, FText::FromStringTable(FName("PosSt"), TEXT("Make Sure To delete this operation?")).ToString());
		if (Result)
		{
			WidgetOperationDelegate.ExecuteIfBound((int32)EWidgetOperationType::OperationDelete, SelectOrder);
			SelectOrder.Index = -1;
		}
	}
}

void USectionOperationPropertyWidget::SetTxtDataTitle(const FString & Text)
{
	TxtDataTitle->SetText(FText::FromString(Text));
}

//void USectionOperationPropertyWidget::OnTextCommittedEdtVisibleExpression(const FText & Text, ETextCommit::Type CommitMethod)
//{
//	
//}
//
//void USectionOperationPropertyWidget::OnClickedBtnVisibleExpression()
//{
//
//}
//
//void USectionOperationPropertyWidget::OnTextCommittedEdtVisibleValue(const FText & Text, ETextCommit::Type CommitMethod)
//{
//
//}
#undef LOCTEXT_NAMESPACE 
