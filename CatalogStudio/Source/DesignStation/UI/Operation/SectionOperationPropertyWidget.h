// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DesignStation/Geometry/DataDefines/SectionRotatorOperation.h"

#include "TensileOperationWidget.h"
#include "ShiftingOperationWidget.h"
#include "ZoomOperationWidget.h"
#include "CutOutOperationWidget.h"
#include "LoftingOperationWidget.h"
#include "RotationOperationWidget.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentEllipse.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentLine.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentPoint.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentRectangle.h"
#include "DesignStation/UI/SingleComponentUI/SingleComponentTitleWidget.h"
#include "SectionOperationPropertyWidget.generated.h"

/**
 * 
 */

class UScrollBox;
class UEditableText;
class UButton;
class UBorder;
class UTextBlock;

UENUM(BlueprintType)
enum class EVisibleEditType : uint8
{
	ValueChange = 0,
	ExpressionChange
};

UENUM(BlueprintType)
enum class EOperatorExpressionType : uint8
{
	VisibleExpression = 0
};
UENUM(BlueprintType)
enum class EWidgetOperationType : uint8
{
	OperationUp = 0,
	OperationDown,
	OperationDelete
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FVisibleChangeDelegate, const int32&, EditType, const FExpressionValuePair&, VisibleParam);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FWidgetOperationDelegate, const int32&, WidgetOperationType, const FSectionOperationOrder&, _SelectOrder);
UCLASS()
class DESIGNSTATION_API USectionOperationPropertyWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FSectionOperation& InSectionOperation, const FSectionOperationOrder& _SelectOrder );
	void UpdateContent(const FSectionLoftOperation& InLoftingData);
	void UpdateContent(const FSectionCutOutOperation& InCutOutData);

	void UpdateSelectSectionWidgetStyle(const int32& EditType, const int32& WidgetId);
	void UpdateHoverSectionWidgetStyle(const int32 & EditType, const int32 & WidgetId, bool IsHovered);

	int32 JudgeOperationInsert();

	static USectionOperationPropertyWidget* Create();

	int32 GetSelectId();
public:
	FTensileOperatorEditDelegate TensileOperatorEditDelegate;
	FShiftingOperationDelegate ShiftingOperationEditDelegate;
	FZoomOperationDelegate ZoomOperationEditDelegate;
	FCutOutOperatorEditDelegate CutOutOperatorEditDelegate;
	FLoftingOperatorEditDelegate LoftingOperatorEditDelegate;
	FRotationOperatorEditDelegate RotationOperatorEditDelegate;
	FVisibleChangeDelegate VisibleChangeDelegate;

	//lofting and cutout data
	FPointDataChangedDelegate PointDataChangeDelegate;
	FLinePropertyChangeDelegate LineDataChangeDelegate;
	FRectanglePropertyChangeDelegate RectangleDataChangeDelegate;
	FEllipsePropertyChangeDelegate EllipseDataChangeDelegate;
	FWidgetSelectDelegate DataWidgetSelectDelegate;

	FWidgetOperationDelegate WidgetOperationDelegate;
	FOperationSelectDelegate OperationSelectDelegate;

private:

	void FlipShowOperatorProperty(bool IsSectionOperation);
	void ChangeOperateButtonState(bool IsOperateSelect);
	void ClearOperatorData();
	void UpdateOldSelectWidget(const int32& EditType, const int32& WidgetId);
	void UpdateViewSelectWidgetState(const int32& EditType, const int32& WidgetId, bool IsSelect);

	template<class T>
	T FindOperationData(const int32& OperationID, const TArray<T>& Operations)
	{
		for (auto& OperationData : Operations)
		{
			if (OperationID == OperationData.ID)
			{
				return OperationData;
			}
		}
		return T();
	}

	UFUNCTION()
		void OperationSelectEdit(const FSectionOperationOrder& OrderData);
	UFUNCTION()
		void SetSelectId(bool _IsSelect, const int32& id);
	UFUNCTION()
		void TensileOperatorEdit(const int32& EditType, const FSectionDrawOperation& TensileData);
	UFUNCTION()
		void ShiftingOperatorEdit(const int32& EditType, const FSectionShiftingOperation& ShiftingData);
	UFUNCTION()
		void ZoomOperatorEdit(const int32& EditType, const FSectionZoomOperation& ZoomData);
	UFUNCTION()
		void CutOutOperatorEdit(const int32& EditType, const FSectionCutOutOperation& CutOutData);
	UFUNCTION()
		void LoftingOperatorEdit(const int32& EditType);
	UFUNCTION()
		void RotationOperatorEdit(const int32& EditType, const FSectionRotatorOperation& RotationData);
	UFUNCTION()
		void VisibleEdit(const int32& EditType, const FString & InExpression);

	UFUNCTION()
		void OperatorPointEdit(const int32& EditType, const FGeomtryPointProperty& PointData);
	UFUNCTION()
		void OperatorLineEdit(const int32& EditType, const FGeomtryLineProperty& LineProperty);
	UFUNCTION()
		void OperatorRectangleEdit(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleProperty);
	UFUNCTION()
		void OperatorEllipseEdit(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseProperty);

	UFUNCTION()
		void LoftingDataWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver,bool OnProperty);

private:
	FSectionOperation OperationData;
	FSectionOperationOrder SelectOrder;

	int32 OldEditType;
	int32 OldWidgetId;

	int32 SelectId;

	UPROPERTY()
		TMap<int32, UTensileOperationWidget*> TensileOperatorMaps;
	UPROPERTY()
		TMap<int32, UShiftingOperationWidget*> ShiftingOperatorMaps;
	UPROPERTY()
		TMap<int32, UZoomOperationWidget*> ZoomOperatorMaps;
	UPROPERTY()
		TMap<int32, UCutOutOperationWidget*> CutOutOperatorMaps;
	UPROPERTY()
		ULoftingOperationWidget* LoftingOperatorWidget;
	UPROPERTY()
		URotationOperationWidget* RotationOperatorWidget;

	UPROPERTY()
		TMap<int32, USingleComponentPoint*> OperatorPoints;
	UPROPERTY()
		TMap<int32, USingleComponentLine*> OperatorLines;
	UPROPERTY()
		USingleComponentRectangle* OperatorRectangle;
	UPROPERTY()
		USingleComponentEllipse* OperatorEllipse;


	static FString SectionOperatorPropertyPath;

protected:
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void SetTxtDataTitle(const FString& Text);
//	UFUNCTION()
//		void OnTextCommittedEdtVisibleExpression(const FText& Text, ETextCommit::Type CommitMethod);
//	UFUNCTION()
//		void OnClickedBtnVisibleExpression();
//	UFUNCTION()
//		void OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod);

private:
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;
	UPROPERTY()
		UButton* BtnDelete;

	UPROPERTY()
		UBorder* BorSectionOperator;
	/*UPROPERTY()
		UEditableText* EdtVisibleExpression;
	UPROPERTY()
		UButton* BtnVisibleExpression;
	UPROPERTY()
		UEditableText* EdtVisibleValue;*/
	UPROPERTY()
		UScrollBox* ScbContent;

	UPROPERTY()
		UBorder* BorLoftingAndCutOutData;
	UPROPERTY()
		UTextBlock* TxtDataTitle;
	UPROPERTY()
		UScrollBox* ScbLoftingAndCutOutData;
	UPROPERTY()
		USingleComponentTitleWidget* LineTitleWidget;
	UPROPERTY()
		USingleComponentTitleWidget* PointTitleWidget;
};
