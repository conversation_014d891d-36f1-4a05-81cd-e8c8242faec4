// Fill out your copyright notice in the Description page of Project Settings.

#include "SectionOperatorToolBarWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/CircularThrobber.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString USectionOperatorToolBarWidget::SectionOperatorToolBarPath = TEXT("WidgetBlueprint'/Game/UI/Operation/SectionOperatorToolBarUI.SectionOperatorToolBarUI_C'");

bool USectionOperatorToolBarWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	
	BIND_PARAM_CPP_TO_UMG(BtnTensile, Btn_Tensile);
	BIND_PARAM_CPP_TO_UMG(BtnLofting, Btn_Lofting);
	BIND_PARAM_CPP_TO_UMG(BtnShifting, Btn_Shifting);
	BIND_PARAM_CPP_TO_UMG(BtnZoom, Btn_Zoom);
	BIND_PARAM_CPP_TO_UMG(BtnCutOut, Btn_CutOut);
	BIND_PARAM_CPP_TO_UMG(BtnClear, Btn_Clear);
	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);

	BIND_WIDGET_FUNCTION(BtnTensile, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnTensile);
	BIND_WIDGET_FUNCTION(BtnLofting, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnLofting);
	BIND_WIDGET_FUNCTION(BtnShifting, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnShifting);
	BIND_WIDGET_FUNCTION(BtnZoom, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnZoom);
	BIND_WIDGET_FUNCTION(BtnCutOut, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnCutOut);


	BIND_WIDGET_FUNCTION(BtnClear, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnClear);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnSave);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, USectionOperatorToolBarWidget::OnClickedBtnExit);

	return true;
}

USectionOperatorToolBarWidget * USectionOperatorToolBarWidget::Create()
{
	//UClass* OperatorToolBarBp = LoadClass<UUserWidget>(NULL, *SectionOperatorToolBarPath);
	//checkf(OperatorToolBarBp, TEXT("load operator tool bar bp error!!"));
	//USectionOperatorToolBarWidget* OperatorToolBarItem = CreateWidget<USectionOperatorToolBarWidget>(GWorld, OperatorToolBarBp);
	//checkf(OperatorToolBarItem, TEXT("create operator tool bar error!!"));
	//return OperatorToolBarItem;

	return  UUIFunctionLibrary::UIWidgetCreate<USectionOperatorToolBarWidget>(USectionOperatorToolBarWidget::SectionOperatorToolBarPath);
}

void USectionOperatorToolBarWidget::OnClickedBtnTensile()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Tensile);
}

void USectionOperatorToolBarWidget::OnClickedBtnLofting()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Lofting);
}

void USectionOperatorToolBarWidget::OnClickedBtnZoom()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Zoom);
}

void USectionOperatorToolBarWidget::OnClickedBtnShifting()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Shifting);
}

void USectionOperatorToolBarWidget::OnClickedBtnCutOut()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::CutOut);
}

void USectionOperatorToolBarWidget::OnClickedBtnRotation()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Rotation);
}

void USectionOperatorToolBarWidget::OnClickedBtnClear()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Clear);
}

void USectionOperatorToolBarWidget::OnClickedBtnSave()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Save);
}

void USectionOperatorToolBarWidget::OnClickedBtnExit()
{
	OperatorToolBarEditDelegate.ExecuteIfBound((int32)EOperatorToolBarType::Exit);
}

void USectionOperatorToolBarWidget::ChangeSaveState(bool bLoading)
{
	if (bLoading)
	{
		ImgSave->SetVisibility(ESlateVisibility::Collapsed);
		CtSave->SetVisibility(ESlateVisibility::Visible);
		BtnSave->SetIsEnabled(false);
	}
	else
	{
		ImgSave->SetVisibility(ESlateVisibility::Visible);
		CtSave->SetVisibility(ESlateVisibility::Collapsed);
		BtnSave->SetIsEnabled(true);
	}
}