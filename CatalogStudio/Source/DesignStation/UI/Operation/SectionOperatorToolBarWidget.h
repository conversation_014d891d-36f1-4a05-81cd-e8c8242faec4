// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SectionOperatorToolBarWidget.generated.h"

/**
 * 
 */

class UButton;
class UCircularThrobber;
class UImage;
UENUM(BlueprintType)
enum class EOperatorToolBarType : uint8
{
	Tensile = 0,
	Lofting,
	Zoom,
	Shifting,
	CutOut,
	Rotation,
	Clear,
	Save,
	Exit
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FOperatorToolBarEditDelegate, const int32&, EditType);

UCLASS()
class DESIGNSTATION_API USectionOperatorToolBarWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	static USectionOperatorToolBarWidget* Create();

public:
	FOperatorToolBarEditDelegate OperatorToolBarEditDelegate;

private:
	static FString SectionOperatorToolBarPath;

protected:
	UFUNCTION()
		void OnClickedBtnTensile();
	UFUNCTION()
		void OnClickedBtnLofting();
	UFUNCTION()
		void OnClickedBtnZoom();
	UFUNCTION()
		void OnClickedBtnShifting();
	UFUNCTION()
		void OnClickedBtnCutOut();
	UFUNCTION()
		void OnClickedBtnRotation();
	UFUNCTION()
		void OnClickedBtnClear();
	UFUNCTION()
		void OnClickedBtnSave();
	UFUNCTION()
		void OnClickedBtnExit();

private:
	UPROPERTY()
		UButton* BtnTensile;
	UPROPERTY()
		UButton* BtnLofting;
	UPROPERTY()
		UButton* BtnZoom;
	UPROPERTY()
		UButton* BtnShifting;
	UPROPERTY()
		UButton* BtnCutOut;
	UPROPERTY()
		UButton* BtnRotation;
	UPROPERTY()
		UButton* BtnClear;	
	UPROPERTY()
		UButton* BtnSave;
	UPROPERTY()
		UButton* BtnExit;
	UPROPERTY()
		UCircularThrobber* CtSave;
	UPROPERTY()
		UImage* ImgSave;
public:
	void ChangeSaveState(bool bLoading);
};
