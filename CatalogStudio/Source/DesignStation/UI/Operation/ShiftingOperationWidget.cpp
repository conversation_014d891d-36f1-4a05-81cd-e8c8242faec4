// Fill out your copyright notice in the Description page of Project Settings.

#include "ShiftingOperationWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "OperatorWidgetConfig.h"
#include "Components/Border.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString UShiftingOperationWidget::ShiftingOperationWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Operation/ShiftingOperationUI.ShiftingOperationUI_C'");

const FString SplitFlag = TEXT(",");

bool UShiftingOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(EdtExpression, Edt_Expression);
	BIND_PARAM_CPP_TO_UMG(BtnExpression, Btn_Expression);
	BIND_PARAM_CPP_TO_UMG(BorShiftingHead, Bor_ShiftingHead);
	BIND_PARAM_CPP_TO_UMG(BorShiftingExperssion, Bor_ShiftingExperssion);
	BIND_PARAM_CPP_TO_UMG(TxtShiftingHead, Txt_ShiftingHead);

	BIND_WIDGET_FUNCTION(EdtExpression, OnTextCommitted, UShiftingOperationWidget::OnTextCommittedEdtExpression);
	BIND_WIDGET_FUNCTION(BtnExpression, OnClicked, UShiftingOperationWidget::OnClickedBtnExpression);

	return true;
}

void UShiftingOperationWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetWidgetStat(IsSelect, (int32)EColorType::Select);
}

void UShiftingOperationWidget::UpdateContent(const FSectionShiftingOperation& InData)
{
	ShiftingOperationData.CopyData(InData);

	IsSelect = false;
	if (EdtExpression)
	{
		EdtExpression->SetText(FText::FromString(CombineExpression(InData.ShiftValue)));
	}
}

UShiftingOperationWidget* UShiftingOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UShiftingOperationWidget>(UShiftingOperationWidget::ShiftingOperationWidgetPath);
}

FString UShiftingOperationWidget::CombineExpression(const TArray<FExpressionValuePair>& InExpressions)
{
	FString ShiftingExpression = TEXT("");
	if (InExpressions.Num() > 0)
	{
		ShiftingExpression.Append(InExpressions[0].Expression);
	}
	for (int i = 1; i < InExpressions.Num(); ++i)
	{
		ShiftingExpression = ShiftingExpression + SplitFlag + InExpressions[i].Expression;
	}
	return ShiftingExpression;
}

TArray<FExpressionValuePair> UShiftingOperationWidget::SplitExpression(const FString& InExpression)
{
	TArray<FString> ShiftingExpressions;
	TArray<FExpressionValuePair> ExpressionValuePairs;
	ShiftingExpressions.Empty();
	ExpressionValuePairs.Empty();
	FString FormatExpression = InExpression;
	FormatExpression = FormatExpression.ConvertTabsToSpaces(1);
	FormatExpression.RemoveSpacesInline();
	FormatExpression.ParseIntoArray(ShiftingExpressions, *SplitFlag);
	for (auto& ExpressionTemp : ShiftingExpressions)
	{
		FExpressionValuePair PairTemp;
		PairTemp.Expression = ExpressionTemp;
		ExpressionValuePairs.Add(PairTemp);
	}
	return ExpressionValuePairs;
}

void UShiftingOperationWidget::UpdateOperationData(const FString& InExpression)
{
	ShiftingOperationData.ShiftValue = SplitExpression(InExpression);
}

void UShiftingOperationWidget::ShiftingOperationExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EShiftingExpressionType::ShiftingExpression)
	{
		if (EdtExpression)
		{
			EdtExpression->SetText(FText::FromString(OutExpression));
		}
		UpdateOperationData(OutExpression);
		ShiftingOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::DataChange, ShiftingOperationData);
	}
	else
	{
		checkNoEntry();
	}
}

void UShiftingOperationWidget::SetWidgetStat(bool IsSelect, const int32& Type)
{
	if (BorBackground)
	{
		//UUIFunctionLibrary::SetBorderBrush(IsSelect, BorBackground);
		SetBorderColor(IsSelect, Type);
		SetTextColor(IsSelect);
	}
}

void UShiftingOperationWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetWidgetStat(true, (int32)EColorType::Hover);
	}
}

void UShiftingOperationWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetWidgetStat(false, (int32)EColorType::Hover);
	}
}

FReply UShiftingOperationWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{

		SetSelectState(true);
		OperationIdDelegate.ExecuteIfBound(IsSelect, ShiftingOperationData.ID);
		FSectionOperationOrder SelectOrder(ESectionOperationType::EShiftSection, ShiftingOperationData.ID);
		OperationWidgetSelectDelegate.ExecuteIfBound(SelectOrder);
		return FReply::Handled();
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{

		SetSelectState(false);
		OperationIdDelegate.ExecuteIfBound(IsSelect, -1);
		FSectionOperationOrder UnSelectOrder(ESectionOperationType::EShiftSection, -1);
		OperationWidgetSelectDelegate.ExecuteIfBound(UnSelectOrder);
		return FReply::Handled();
	}

	return FReply::Unhandled();
}

void UShiftingOperationWidget::SetBorderColor(bool IsSelect, const int32& Type)
{
	if (IS_OBJECT_PTR_VALID(BorShiftingHead) && IS_OBJECT_PTR_VALID(BorShiftingExperssion))
	{
		switch (Type)
		{
		case (int32)EColorType::Hover:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorShiftingHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorShiftingExperssion);
			break;
		case (int32)EColorType::Select:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorShiftingHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorShiftingExperssion);
			break;
		default:
			break;
		}
	}
}

void UShiftingOperationWidget::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtShiftingHead))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtShiftingHead);
	}
}

void UShiftingOperationWidget::OnClickedBtnDelete()
{
	ShiftingOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Delete, ShiftingOperationData);
}

void UShiftingOperationWidget::OnClickedBtnUp()
{
	ShiftingOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Up, ShiftingOperationData);
}

void UShiftingOperationWidget::OnClickedBtnDown()
{
	ShiftingOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Down, ShiftingOperationData);
}

void UShiftingOperationWidget::OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		UpdateOperationData(Text.ToString());
		ShiftingOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::DataChange, ShiftingOperationData);
	}
	else
	{
		EdtExpression->SetText(FText::FromString(CombineExpression(ShiftingOperationData.ShiftValue)));
	}
}

void UShiftingOperationWidget::OnClickedBtnExpression()
{
	if (EdtExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)EShiftingExpressionType::ShiftingExpression, EdtExpression->GetText().ToString(), FName(TEXT("ShiftingOperationExpressionEdit")));
	}
}
