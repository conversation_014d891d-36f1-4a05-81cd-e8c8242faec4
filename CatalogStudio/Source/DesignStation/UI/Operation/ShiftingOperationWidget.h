// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OperationBaseWidget.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "ShiftingOperationWidget.generated.h"

/**
 * 
 */

class UEditableText;
class UButton;
class UBorder;
class UTextBlock;

UENUM(BlueprintType)
enum class EShiftingOrZoomType : uint8
{
	DataChange = 0,
	Delete,
	Up,
	Down
};

UENUM(BlueprintType)
enum class EShiftingExpressionType : uint8
{
	ShiftingExpression = 0
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FShiftingOperationDelegate, const int32&, EditType, const FSectionShiftingOperation&, ShiftingData);

UCLASS()
class DESIGNSTATION_API UShiftingOperationWidget : public UOperationBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect) override;
	virtual void UpdateOperationData(const FString& InExpression);
	void UpdateContent(const FSectionShiftingOperation& InData);

	static UShiftingOperationWidget* Create();

protected:
	FString CombineExpression(const TArray<FExpressionValuePair>& InExpressions);
	TArray<FExpressionValuePair> SplitExpression(const FString& InExpression);

private:
	UFUNCTION()
		void ShiftingOperationExpressionEdit(const int32& EditType, const FString& OutExpression);
	void SetWidgetStat(bool IsSelect,const int32& Type);

public:
	FShiftingOperationDelegate ShiftingOperationDelegate;

private:
	FSectionShiftingOperation ShiftingOperationData;

	static FString ShiftingOperationWidgetPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);

protected:
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

	UFUNCTION()
		void OnTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExpression();

private:
	UPROPERTY()
		UBorder*		BorBackground;

	UPROPERTY()
		UButton*		BtnDelete;
	UPROPERTY()
		UButton*		BtnUp;
	UPROPERTY()
		UButton*		BtnDown;
	UPROPERTY()
		UEditableText*	EdtExpression;
	UPROPERTY()
		UButton*		BtnExpression;	
	UPROPERTY()
		UBorder*		BorShiftingHead;
	UPROPERTY()
		UBorder*		BorShiftingExperssion;
	UPROPERTY()
		UTextBlock*		TxtShiftingHead;
};
