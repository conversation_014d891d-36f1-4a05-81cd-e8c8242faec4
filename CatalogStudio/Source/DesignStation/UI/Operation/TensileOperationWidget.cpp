// Fill out your copyright notice in the Description page of Project Settings.

#include "TensileOperationWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "OperatorWidgetConfig.h"
#include "Components/Border.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString UTensileOperationWidget::TensileOperatorPath = TEXT("WidgetBlueprint'/Game/UI/Operation/TensileOperationUI.TensileOperationUI_C'");

bool UTensileOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_Background);
	BIND_PARAM_CPP_TO_UMG(EdtXExpression, Edt_XExpression);
	BIND_PARAM_CPP_TO_UMG(EdtYExpression, Edt_YExpression);
	BIND_PARAM_CPP_TO_UMG(EdtZExpression, Edt_ZExpression);
	BIND_PARAM_CPP_TO_UMG(EdtXValue, Edt_XValue);
	BIND_PARAM_CPP_TO_UMG(EdtYValue, Edt_YValue);
	BIND_PARAM_CPP_TO_UMG(EdtZValue, Edt_ZValue);
	BIND_PARAM_CPP_TO_UMG(BtnXExpression, Btn_XExpression);
	BIND_PARAM_CPP_TO_UMG(BtnYExpression, Btn_YExpression);
	BIND_PARAM_CPP_TO_UMG(BtnZExpression, Btn_ZExpression);
	BIND_PARAM_CPP_TO_UMG(BorTensileHead, Bor_TensileHead);

	BIND_PARAM_CPP_TO_UMG(BorTensileHead, Bor_TensileHead);
	BIND_PARAM_CPP_TO_UMG(BorXHead, Bor_XHead);
	BIND_PARAM_CPP_TO_UMG(BorYHead, Bor_YHead);
	BIND_PARAM_CPP_TO_UMG(BorZHead, Bor_ZHead);
	BIND_PARAM_CPP_TO_UMG(BorXExpression, Bor_XExpression);
	BIND_PARAM_CPP_TO_UMG(BorYExpression, Bor_YExpression);
	BIND_PARAM_CPP_TO_UMG(BorZExpression, Bor_ZExpression);
	BIND_PARAM_CPP_TO_UMG(BorXValue, Bor_XValue);
	BIND_PARAM_CPP_TO_UMG(BorYValue, Bor_YValue);
	BIND_PARAM_CPP_TO_UMG(BorZValue, Bor_ZValue);

	BIND_PARAM_CPP_TO_UMG(TxtTensileHead, Txt_TensileHead);
	BIND_PARAM_CPP_TO_UMG(TxtXHead, Txt_XHead);
	BIND_PARAM_CPP_TO_UMG(TxtYHead, Txt_YHead);
	BIND_PARAM_CPP_TO_UMG(TxtZHead, Txt_ZHead);


	BIND_WIDGET_FUNCTION(EdtXExpression, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtXExpression);
	BIND_WIDGET_FUNCTION(EdtYExpression, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtYExpression);
	BIND_WIDGET_FUNCTION(EdtZExpression, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtZExpression);

	BIND_WIDGET_FUNCTION(EdtXValue, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtXValue);
	BIND_WIDGET_FUNCTION(EdtYValue, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtYValue);
	BIND_WIDGET_FUNCTION(EdtZValue, OnTextCommitted, UTensileOperationWidget::OnTextCommittedEdtZValue);

	BIND_WIDGET_FUNCTION(BtnXExpression, OnClicked, UTensileOperationWidget::OnClickedBtnXExpression);
	BIND_WIDGET_FUNCTION(BtnYExpression, OnClicked, UTensileOperationWidget::OnClickedBtnYExpression);
	BIND_WIDGET_FUNCTION(BtnZExpression, OnClicked, UTensileOperationWidget::OnClickedBtnZExpression);

	//if (UEditableText* Edt_XExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_XExpression"))))
	//{
	//	EdtXExpression = Edt_XExpression;
	//	EdtXExpression->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtXExpression);
	//}
	//if (UButton* Btn_XExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_XExpression"))))
	//{
	//	BtnXExpression = Btn_XExpression;
	//	BtnXExpression->OnClicked.AddUniqueDynamic(this, &UTensileOperationWidget::OnClickedBtnXExpression);
	//}
	//if (UEditableText* Edt_XValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_XValue"))))
	//{
	//	EdtXValue = Edt_XValue;
	//	EdtXValue->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtXValue);
	//}

	//if (UEditableText* Edt_YExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_YExpression"))))
	//{
	//	EdtYExpression = Edt_YExpression;
	//	EdtYExpression->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtYExpression);
	//}
	//if (UButton* Btn_YExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_YExpression"))))
	//{
	//	BtnYExpression = Btn_YExpression;
	//	BtnYExpression->OnClicked.AddUniqueDynamic(this, &UTensileOperationWidget::OnClickedBtnYExpression);
	//}
	//if (UEditableText* Edt_YValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_YValue"))))
	//{
	//	EdtYValue = Edt_YValue;
	//	EdtYValue->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtYValue);
	//}

	//if (UEditableText* Edt_ZExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_ZExpression"))))
	//{
	//	EdtZExpression = Edt_ZExpression;
	//	EdtZExpression->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtZExpression);
	//}
	//if (UButton* Btn_ZExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_ZExpression"))))
	//{
	//	BtnZExpression = Btn_ZExpression;
	//	BtnZExpression->OnClicked.AddUniqueDynamic(this, &UTensileOperationWidget::OnClickedBtnZExpression);
	//}
	//if (UEditableText* Edt_ZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_ZValue"))))
	//{
	//	EdtZValue = Edt_ZValue;
	//	EdtZValue->OnTextCommitted.AddUniqueDynamic(this, &UTensileOperationWidget::OnTextCommittedEdtZValue);
	//}

	//BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	//BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UTensileOperationWidget::OnClickedBtnDelete);
	//BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	//BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UTensileOperationWidget::OnClickedBtnUp);
	//BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	//BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UTensileOperationWidget::OnClickedBtnDown);

	return true;
}

void UTensileOperationWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	WidgetSelectState(IsSelect, (int32)EColorType::Select);
}

void UTensileOperationWidget::UpdateContent(const FSectionDrawOperation& InData)
{
	TensileData.CopyData(InData);
	IsSelect = false;
	if (EdtXValue && EdtYValue && EdtZValue && EdtXExpression && EdtYExpression && EdtZExpression)
	{
		EdtXValue->SetText(FText::FromString(InData.DrawOffsetX.Value));
		EdtXExpression->SetText(FText::FromString(InData.DrawOffsetX.Expression));
		EdtYValue->SetText(FText::FromString(InData.DrawOffsetY.Value));
		EdtYExpression->SetText(FText::FromString(InData.DrawOffsetY.Expression));
		EdtZValue->SetText(FText::FromString(InData.DrawOffsetZ.Value));
		EdtZExpression->SetText(FText::FromString(InData.DrawOffsetZ.Expression));
	}
}

UTensileOperationWidget* UTensileOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UTensileOperationWidget>(UTensileOperationWidget::TensileOperatorPath);
}

void UTensileOperationWidget::SetBorderBrush(bool IsSelect, const int32& Type)
{
	if (IS_OBJECT_PTR_VALID(BorTensileHead) && IS_OBJECT_PTR_VALID(BorXHead) && IS_OBJECT_PTR_VALID(BorXExpression) && IS_OBJECT_PTR_VALID(BorXValue)
		&& IS_OBJECT_PTR_VALID(BorYHead) && IS_OBJECT_PTR_VALID(BorYExpression) && IS_OBJECT_PTR_VALID(BorYValue)
		&& IS_OBJECT_PTR_VALID(BorZHead) && IS_OBJECT_PTR_VALID(BorZExpression) && IS_OBJECT_PTR_VALID(BorZValue))
	{

		switch (Type)
		{
		case (int32)EColorType::Select:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorTensileHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorXHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorYHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorZHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorXExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorYExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorZExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorXValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorYValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorZValue);
			break;
		case (int32)EColorType::Hover:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorTensileHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorXHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorYHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorZHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorXExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorYExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorZExpression);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorXValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorYValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorZValue);
			break;
		default:
			break;
		}


	}
}

void UTensileOperationWidget::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtTensileHead) && IS_OBJECT_PTR_VALID(TxtXHead) && IS_OBJECT_PTR_VALID(TxtYHead) && IS_OBJECT_PTR_VALID(TxtZHead))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtTensileHead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtXHead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtYHead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtZHead);
	}
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtXExpression);
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtYExpression);
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtZExpression);
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtXValue);
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtYValue);
	//UUIFunctionLibrary::SetEditableTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, EdtZValue);
}

void UTensileOperationWidget::WidgetSelectState(bool _IsSelect, const int32& Type)
{
	SetBorderBrush(_IsSelect, Type);
	SetTextColor(_IsSelect);
}


void UTensileOperationWidget::TensileExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	switch ((ETensileExpressionType)EditType)
	{
	case ETensileExpressionType::TensileXExpression:
	{
		TensileData.DrawOffsetX.Expression = OutExpression;
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileXExpression, TensileData);
		break;
	}
	case ETensileExpressionType::TensileYExpression:
	{
		TensileData.DrawOffsetY.Expression = OutExpression;
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileYExpression, TensileData);
		break;
	}
	case ETensileExpressionType::TensileZExpression:
	{
		TensileData.DrawOffsetZ.Expression = OutExpression;
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileZExpression, TensileData);
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void UTensileOperationWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		WidgetSelectState(true, (int32)EColorType::Hover);
	}
}

void UTensileOperationWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		WidgetSelectState(false, (int32)EColorType::Hover);
	}
}

FReply UTensileOperationWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{

		SetSelectState(true);
		OperationIdDelegate.ExecuteIfBound(IsSelect, TensileData.ID);
		FSectionOperationOrder SelectOrder(ESectionOperationType::EDrawSection, TensileData.ID);
		OperationWidgetSelectDelegate.ExecuteIfBound(SelectOrder);
		return FReply::Handled();
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{

		SetSelectState(false);
		OperationIdDelegate.ExecuteIfBound(IsSelect, -1);
		FSectionOperationOrder UnSelectOrder(ESectionOperationType::EDrawSection, -1);
		OperationWidgetSelectDelegate.ExecuteIfBound(UnSelectOrder);
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UTensileOperationWidget::OnClickedBtnDelete()
{
	TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::Delete, TensileData);
}

void UTensileOperationWidget::OnClickedBtnUp()
{
	TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::Up, TensileData);
}

void UTensileOperationWidget::OnClickedBtnDown()
{
	TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::Down, TensileData);
}

void UTensileOperationWidget::OnTextCommittedEdtXExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		TensileData.DrawOffsetX.Expression = Text.ToString();
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileXExpression, TensileData);
	}
	else
	{
		EdtXExpression->SetText(FText::FromString(TensileData.DrawOffsetX.Expression));
	}
}

void UTensileOperationWidget::OnClickedBtnXExpression()
{
	if (EdtXExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ETensileExpressionType::TensileXExpression, EdtXExpression->GetText().ToString(), FName(TEXT("TensileExpressionEdit")));
	}
}

void UTensileOperationWidget::OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		TensileData.DrawOffsetX.Value = Text.ToString();
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileXValue, TensileData);
	}
	else
	{
		EdtXValue->SetText(FText::FromString(TensileData.DrawOffsetX.Value));
	}
}

void UTensileOperationWidget::OnTextCommittedEdtYExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		TensileData.DrawOffsetY.Expression = Text.ToString();
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileYExpression, TensileData);
	}
	else
	{
		EdtYExpression->SetText(FText::FromString(TensileData.DrawOffsetY.Expression));
	}
}

void UTensileOperationWidget::OnClickedBtnYExpression()
{
	if (EdtYExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ETensileExpressionType::TensileYExpression, EdtYExpression->GetText().ToString(), FName(TEXT("TensileExpressionEdit")));
	}
}

void UTensileOperationWidget::OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (!Text.IsEmpty() && Text.IsNumeric())
		{
			TensileData.DrawOffsetY.Value = Text.ToString();
			TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileYValue, TensileData);
		}
		else
		{
			EdtYValue->SetText(FText::FromString(TensileData.DrawOffsetY.Value));
		}
	}
}

void UTensileOperationWidget::OnTextCommittedEdtZExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		TensileData.DrawOffsetZ.Expression = Text.ToString();
		TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileZExpression, TensileData);
	}
	else
	{
		EdtZExpression->SetText(FText::FromString(TensileData.DrawOffsetZ.Expression));
	}
}

void UTensileOperationWidget::OnClickedBtnZExpression()
{
	if (EdtZExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)ETensileExpressionType::TensileZExpression, EdtZExpression->GetText().ToString(), FName(TEXT("TensileExpressionEdit")));
	}
}

void UTensileOperationWidget::OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (!Text.IsEmpty() && Text.IsNumeric())
		{
			TensileData.DrawOffsetZ.Value = Text.ToString();
			TensileOperatorDataChangeDelegate.ExecuteIfBound((int32)ETensileOperatorType::TensileZValue, TensileData);
		}
		else
		{
			EdtZValue->SetText(FText::FromString(TensileData.DrawOffsetZ.Value));
		}
	}
}
