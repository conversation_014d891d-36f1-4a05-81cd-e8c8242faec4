// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OperationBaseWidget.h"
#include "TensileOperationWidget.generated.h"

/**
 * 
 */

class UEditableText;
class UButton;
class UBorder;
class UTextBlock;

UENUM(BlueprintType)
enum class ETensileOperatorType : uint8
{
	TensileXExpression = 0,
	TensileYExpression,
	TensileZExpression,
	TensileXValue,
	TensileYValue,
	TensileZValue,
	Delete,
	Up,
	Down
};

UENUM(BlueprintType)
enum class ETensileExpressionType : uint8
{
	TensileXExpression = 0,
	TensileYExpression,
	TensileZExpression
};


DECLARE_DYNAMIC_DELEGATE_TwoParams(FTensileOperatorEditDelegate, const int32&, EditType, const FSectionDrawOperation&, TensileData);

UCLASS()
class DESIGNSTATION_API UTensileOperationWidget : public UOperationBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect) override;
	void UpdateContent(const FSectionDrawOperation& InData);

	static UTensileOperationWidget* Create();

	void SetBorderBrush(bool _IsSelect,const int32& Type);
	void SetTextColor(bool _IsSelect);
	void WidgetSelectState(bool _IsSelect, const int32& Type);
private:
	UFUNCTION()
		void TensileExpressionEdit(const int32& EditType, const FString& OutExpression);

public:
	FTensileOperatorEditDelegate TensileOperatorDataChangeDelegate;

private:
	FSectionDrawOperation TensileData;

	static FString TensileOperatorPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent);

protected:
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

	UFUNCTION()
		void OnTextCommittedEdtXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnXExpression();
	UFUNCTION()
		void OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnYExpression();
	UFUNCTION()
		void OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtZExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnZExpression();
	UFUNCTION()
		void OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod);
	
private:
	UPROPERTY()
		UBorder* BorBackground;

	UPROPERTY()
		UButton* BtnDelete;
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;

	UPROPERTY()
		UEditableText* EdtXExpression;
	UPROPERTY()
		UButton* BtnXExpression;
	UPROPERTY()
		UEditableText* EdtXValue;

	UPROPERTY()
		UEditableText* EdtYExpression;
	UPROPERTY()
		UButton* BtnYExpression;
	UPROPERTY()
		UEditableText* EdtYValue;

	UPROPERTY()
		UEditableText* EdtZExpression;
	UPROPERTY()
		UButton* BtnZExpression;
	UPROPERTY()
		UEditableText* EdtZValue;
	UPROPERTY()
		UBorder* BorTensileHead;
	UPROPERTY()
		UBorder* BorXHead;
	UPROPERTY()
		UBorder* BorYHead;
	UPROPERTY()
		UBorder* BorZHead;
	UPROPERTY()
		UBorder* BorXExpression;
	UPROPERTY()
		UBorder* BorYExpression;
	UPROPERTY()
		UBorder* BorZExpression;
	UPROPERTY()
		UBorder* BorXValue;
	UPROPERTY()
		UBorder* BorYValue;
	UPROPERTY()
		UBorder* BorZValue;
	UPROPERTY()
		UTextBlock* TxtTensileHead;
	UPROPERTY()
		UTextBlock* TxtXHead;
	UPROPERTY()
		UTextBlock* TxtYHead;
	UPROPERTY()
		UTextBlock* TxtZHead;

};
