// Fill out your copyright notice in the Description page of Project Settings.

#include "ZoomOperationWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "OperatorWidgetConfig.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString UZoomOperationWidget::ZoomOperationWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Operation/ZoomOperatorUI.ZoomOperatorUI_C'");

bool UZoomOperationWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BorZoomBackground, Bor_Background);
	BIND_PARAM_CPP_TO_UMG(EdtZoomExpression, Edt_Expression);
	BIND_PARAM_CPP_TO_UMG(BtnZoomExpression, Btn_Expression);
	BIND_PARAM_CPP_TO_UMG(BorZoomHead, Bor_ZoomHead);
	BIND_PARAM_CPP_TO_UMG(BorZoomExperssion, Bor_ZoomExperssion);
	BIND_PARAM_CPP_TO_UMG(TxtZoomHead, Txt_ZoomHead);
	BIND_WIDGET_FUNCTION(EdtZoomExpression, OnTextCommitted, UZoomOperationWidget::OnZoomTextCommittedEdtExpression);
	BIND_WIDGET_FUNCTION(BtnZoomExpression, OnClicked, UZoomOperationWidget::OnZoomClickedBtnExpression);

	//BIND_PARAM_CPP_TO_UMG(BtnZoomDelete, Btn_Delete);
	//BIND_WIDGET_FUNCTION(BtnZoomDelete, OnClicked, UZoomOperationWidget::OnClickedBtnZoomDelete);
	//BIND_PARAM_CPP_TO_UMG(BtnZoomUp, Btn_Up);
	//BIND_WIDGET_FUNCTION(BtnZoomUp, OnClicked, UZoomOperationWidget::OnClickedBtnZoomUp);
	//BIND_PARAM_CPP_TO_UMG(BtnZoomDown, Btn_Down);
	//BIND_WIDGET_FUNCTION(BtnZoomDown, OnClicked, UZoomOperationWidget::OnClickedBtnZoomDown);

	return true;
}

void UZoomOperationWidget::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetZoomWidgetStat(IsSelect, (int32)EColorType::Select);
}

void UZoomOperationWidget::UpdateOperationData(const FString& InExpression)
{
	ZoomOperatorData.ZoomValue = SplitExpression(InExpression);
}

void UZoomOperationWidget::UpdateContent(const FSectionZoomOperation& InData)
{
	ZoomOperatorData.CopyData(InData);
	IsSelect = false;
	if (EdtZoomExpression)
	{
		EdtZoomExpression->SetText(FText::FromString(CombineExpression(InData.ZoomValue)));
	}
}

UZoomOperationWidget* UZoomOperationWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UZoomOperationWidget>(UZoomOperationWidget::ZoomOperationWidgetPath);
}

void UZoomOperationWidget::ZoomOperationExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EZoomExpressionType::ZoomExpression)
	{
		UpdateOperationData(OutExpression);
		ZoomOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::DataChange, ZoomOperatorData);
	}
	else
	{
		checkNoEntry();
	}
}

void UZoomOperationWidget::SetZoomWidgetStat(bool IsSelect, const int32& Type)
{
	if (BorZoomBackground)
	{
		SetBorderColor(IsSelect, Type);
		SetTextColor(IsSelect);
	}
}

void UZoomOperationWidget::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetZoomWidgetStat(true, (int32)EColorType::Hover);
	}
}

void UZoomOperationWidget::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetZoomWidgetStat(false, (int32)EColorType::Hover);
	}
}

FReply UZoomOperationWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		SetSelectState(true);
		OperationIdDelegate.ExecuteIfBound(IsSelect, ZoomOperatorData.ID);
		FSectionOperationOrder ZoomSelectOrder(ESectionOperationType::EZoomSection, ZoomOperatorData.ID);
		OperationWidgetSelectDelegate.ExecuteIfBound(ZoomSelectOrder);
		return FReply::Handled();
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		SetSelectState(false);
		OperationIdDelegate.ExecuteIfBound(IsSelect, -1);
		FSectionOperationOrder ZoomUnSelectOrder(ESectionOperationType::EZoomSection, -1);
		OperationWidgetSelectDelegate.ExecuteIfBound(ZoomUnSelectOrder);
		return FReply::Handled();
	}

	return FReply::Unhandled();
}

void UZoomOperationWidget::SetBorderColor(bool IsSelect, const int32& Type)
{
	if (IS_OBJECT_PTR_VALID(BorZoomHead) && IS_OBJECT_PTR_VALID(BorZoomExperssion))
	{
		switch (Type)
		{
		case (int32)EColorType::Select:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderHeadUnselect, BorZoomHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : OperatorColor::BorderUnselect, BorZoomExperssion);
			break;
		case (int32)EColorType::Hover:
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderHeadUnselect, BorZoomHead);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : OperatorColor::BorderUnselect, BorZoomExperssion);
			break;
		default:
			break;
		}

	}
}

void UZoomOperationWidget::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtZoomHead))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtZoomHead);
	}

}

void UZoomOperationWidget::OnClickedBtnZoomDelete()
{
	ZoomOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Delete, ZoomOperatorData);
}

void UZoomOperationWidget::OnClickedBtnZoomUp()
{
	ZoomOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Up, ZoomOperatorData);
}

void UZoomOperationWidget::OnClickedBtnZoomDown()
{
	ZoomOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::Down, ZoomOperatorData);
}

void UZoomOperationWidget::OnZoomTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		UpdateOperationData(Text.ToString());
		ZoomOperationDelegate.ExecuteIfBound((int32)EShiftingOrZoomType::DataChange, ZoomOperatorData);
	}
	else
	{
		EdtZoomExpression->SetText(FText::FromString(CombineExpression(ZoomOperatorData.ZoomValue)));
	}
}

void UZoomOperationWidget::OnZoomClickedBtnExpression()
{
	if (EdtZoomExpression)
	{
		BIND_EXPRESSION_WIDGET((int32)EZoomExpressionType::ZoomExpression, EdtZoomExpression->GetText().ToString(), FName(TEXT("ZoomOperationExpressionEdit")));
	}
}
