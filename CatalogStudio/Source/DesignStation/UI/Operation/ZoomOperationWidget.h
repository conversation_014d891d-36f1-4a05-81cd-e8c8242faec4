// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ShiftingOperationWidget.h"
#include "ZoomOperationWidget.generated.h"

/**
 * 
 */

class UBorder;
class UEditableText;
class UButton;
class UTextBlock;

UENUM(BlueprintType)
enum class EZoomExpressionType : uint8
{
	ZoomExpression = 0
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FZoomOperationDelegate, const int32&, EditType, const FSectionZoomOperation&, ZoomData);

UCLASS()
class DESIGNSTATION_API UZoomOperationWidget : public UShiftingOperationWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetSelectState(bool _IsSelect) override;
	virtual void UpdateOperationData(const FString& InExpression) override;
	void UpdateContent(const FSectionZoomOperation& InData);

	static UZoomOperationWidget* Create();

public:
	FZoomOperationDelegate ZoomOperationDelegate;

private:
	UFUNCTION()
		void ZoomOperationExpressionEdit(const int32& EditType, const FString& OutExpression);
	void SetZoomWidgetStat(bool IsSelect,const int32& Type);

private:
	FSectionZoomOperation ZoomOperatorData;

	static FString ZoomOperationWidgetPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);
protected:
	UFUNCTION()
		void OnClickedBtnZoomDelete();
	UFUNCTION()
		void OnClickedBtnZoomUp();
	UFUNCTION()
		void OnClickedBtnZoomDown();
	UFUNCTION()
		void OnZoomTextCommittedEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnZoomClickedBtnExpression();

private:
	UPROPERTY()
		UBorder*		BorZoomBackground;
	UPROPERTY()
		UButton*		BtnZoomDelete;
	UPROPERTY()
		UButton*		BtnZoomUp;
	UPROPERTY()
		UButton*		BtnZoomDown;
	UPROPERTY()
		UEditableText*	EdtZoomExpression;
	UPROPERTY()
		UButton*		BtnZoomExpression;
	UPROPERTY()
		UBorder*		BorZoomHead;
	UPROPERTY()
		UBorder*		BorZoomExperssion;
	UPROPERTY()
		UTextBlock*		TxtZoomHead;
	
};
