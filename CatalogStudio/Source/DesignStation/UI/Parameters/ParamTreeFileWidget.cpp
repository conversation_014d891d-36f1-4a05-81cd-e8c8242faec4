// Fill out your copyright notice in the Description page of Project Settings.

#include "ParamTreeFileWidget.h"

#include "ParameterDetailWidget.h"
#include "Components/Border.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UParamTreeFileWidget::ParamTreeFilePath = TEXT("WidgetBlueprint'/Game/UI/Parameters/ParamTreeFileUI.ParamTreeFileUI_C'");

//bool UParamTreeFileWidget::Initialize()
//{
//	if (!UFolderAndFileBaseWidget::Initialize())
//	{
//		return false;
//	}
//
//	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
//	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UParamTreeFileWidget::OnClickedBtnSelect);
//	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UParamTreeFileWidget::OnHoveredBtnSelect);
//	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UParamTreeFileWidget::OnUnHoverBtnSelect);
//	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_ParamName);
//	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_ParamFile);
//
//	IsVisible = true;
//	IsSelected = false;
//	IsNewParam = false;
//	return true;
//}

void UParamTreeFileWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UParamTreeFileWidget::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UParamTreeFileWidget::OnHoveredBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UParamTreeFileWidget::OnUnHoverBtnSelect);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_ParamName);
	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_ParamFile);

	IsVisible = true;
	IsSelected = false;
	IsNewParam = false;
}

void UParamTreeFileWidget::UpdateContent(const FParameterData& InData)
{
	ParamData.CopyData(InData);
	if (IS_OBJECT_PTR_VALID(TxtName))
	{
		TxtName->SetText(FText::FromString(InData.Data.name + TEXT("-") + InData.Data.description));
	}
}

void UParamTreeFileWidget::RemoveFromParentParamFolder()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
	ParamFileRemoveDelegate.ExecuteIfBound(this);
}

void UParamTreeFileWidget::SelectSelf()
{
	IsSelected = true;
	ParamFileSelectDelegate.ExecuteIfBound(this);
}

void UParamTreeFileWidget::SetIsSelected(bool _IsSelected)
{
	Super::SetIsSelected(_IsSelected);
}

bool UParamTreeFileWidget::IsParamAType()
{
	return false;
}

UParamTreeFileWidget* UParamTreeFileWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UParamTreeFileWidget>(UParamTreeFileWidget::ParamTreeFilePath);
}

void UParamTreeFileWidget::BindDelegates()
{
}


FReply UParamTreeFileWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (IsSelected)
		{
			ParamFileOpenContextMenuDelegate.ExecuteIfBound(this, InMouseEvent);
		}
		else
		{
			ParamItemRightClickDelegate.ExecuteIfBound();
		}
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void UParamTreeFileWidget::OnClickedBtnSelect()
{
	if (UParameterDetailWidget::Get()->CheckSelectWidgetValid())
	{
		UE_LOG(LogTemp, Log, TEXT("select file"));
		IsSelected = true;
		ParamFileSelectDelegate.ExecuteIfBound(this);
	}
}

void UParamTreeFileWidget::OnHoveredBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Hover);
	}
}

void UParamTreeFileWidget::OnUnHoverBtnSelect()
{
	if (!IsSelected && IS_OBJECT_PTR_VALID(BorFolderOrFile))
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Normal);
	}
}
