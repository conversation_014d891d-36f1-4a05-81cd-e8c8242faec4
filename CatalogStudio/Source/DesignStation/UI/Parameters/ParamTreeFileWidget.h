// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/UI/FolderLayoutUI/FolderAndFileBaseWidget.h"
#include "ParamTreeFileWidget.generated.h"

/**
 *
 */

DECLARE_DYNAMIC_DELEGATE_OneParam(FParamFileSelectDelegate, UParamTreeFileWidget*, SelectedParam);
DECLARE_DYNAMIC_DELEGATE_OneParam(FParamFileRemoveDelegate, UParamTreeFileWidget*, SelectedParam);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FParamFileOpenContextMenuDelegate, UParamTreeFileWidget*, SelectedParam, const FPointerEvent&, InMouseEvent);

UCLASS()
class DESIGNSTATION_API UParamTreeFileWidget : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()

public:
	//virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void UpdateContent(const FParameterData& InData);
	void RemoveFromParentParamFolder();
	void SelectSelf();

	FORCEINLINE FParameterData& GetParamData() { return ParamData; }
	FORCEINLINE void SetIsNew(bool _IsNew) { IsNewParam = _IsNew; }
	FORCEINLINE bool GetIsNew() const { return IsNewParam; }
	FORCEINLINE FString GetId() const { return ParamData.Data.id; }

	virtual void SetIsSelected(bool _IsSelected) override;
	virtual bool IsParamAType() override;
	virtual FString GetParamId() override { return ParamData.Data.id; }
	virtual int32 GetParamClassificID() override { return ParamData.Data.classific_id; }

	static UParamTreeFileWidget* Create();

private:
	void BindDelegates();
public:
	FParamFileSelectDelegate ParamFileSelectDelegate;
	FParamFileRemoveDelegate ParamFileRemoveDelegate;
	FParamFileOpenContextMenuDelegate ParamFileOpenContextMenuDelegate;

private:
	UPROPERTY()
		FParameterData ParamData;
	bool IsNewParam;

	static FString ParamTreeFilePath;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	UFUNCTION()
		void OnClickedBtnSelect();
	UFUNCTION()
		void OnHoveredBtnSelect();
	UFUNCTION()
		void OnUnHoverBtnSelect();

};
