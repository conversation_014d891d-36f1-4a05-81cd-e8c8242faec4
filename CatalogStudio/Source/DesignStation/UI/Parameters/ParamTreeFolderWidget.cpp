// Fill out your copyright notice in the Description page of Project Settings.

#include "ParamTreeFolderWidget.h"

#include "ParameterDetailWidget.h"
#include "Components/Border.h"
#include "Components/TextBlock.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/EditableText.h"

#define LOCTEXT_NAMESPACE "ParamTreeFolder"

FString UParamTreeFolderWidget::ParamTreeFolderPath = TEXT("WidgetBlueprint'/Game/UI/Parameters/ParamTreeFolderUI.ParamTreeFolderUI_C'");

/*
bool UParamTreeFolderWidget::Initialize()
{
	if (!UFolderAndFileBaseWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UParamTreeFolderWidget::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UParamTreeFolderWidget::OnHoveredBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UParamTreeFolderWidget::OnUnHoveredBtnSelect);
	BIND_PARAM_CPP_TO_UMG(CkbExpandChild, Ckb_ExpandChild);
	BIND_WIDGET_FUNCTION(CkbExpandChild, OnCheckStateChanged, UParamTreeFolderWidget::OnStateChangedCkbExpand);
	BIND_PARAM_CPP_TO_UMG(ScbChild, Scb_Child);
	if (IS_OBJECT_PTR_VALID(ScbChild)) ScbChild->SetVisibility(ESlateVisibility::Collapsed);

	BIND_PARAM_CPP_TO_UMG(BorChangeName, Bor_ChangeName);
	BIND_SLATE_WIDGET_FUNCTION(BorChangeName, OnMouseDoubleClickEvent, FName(TEXT("OnBorChangeNameDoubleClicked")));
	BIND_PARAM_CPP_TO_UMG(EdtFolderName, Edt_FolderName);
	BIND_WIDGET_FUNCTION(EdtFolderName, OnTextCommitted, UParamTreeFolderWidget::OnTextCommittedEdtFolderName);
	//BIND_WIDGET_FUNCTION(EdtFolderName, OnTextChanged, UParamTreeFolderWidget::OnTextChangedEdtFolderName);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FolderName);

	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_Folder);
	BIND_PARAM_CPP_TO_UMG(ImgFolder, Img_Folder);

	IsVisible = true;
	IsSelected = false;
	NoEditable = false;
	SyncID = TEXT("-1");
	SwitchNameEditableState(false);
	BindDelegates();

	return true;
}
*/

void UParamTreeFolderWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UParamTreeFolderWidget::OnClickedBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnHovered, UParamTreeFolderWidget::OnHoveredBtnSelect);
	BIND_WIDGET_FUNCTION(BtnSelect, OnUnhovered, UParamTreeFolderWidget::OnUnHoveredBtnSelect);
	BIND_PARAM_CPP_TO_UMG(CkbExpandChild, Ckb_ExpandChild);
	BIND_WIDGET_FUNCTION(CkbExpandChild, OnCheckStateChanged, UParamTreeFolderWidget::OnStateChangedCkbExpand);
	BIND_PARAM_CPP_TO_UMG(ScbChild, Scb_Child);
	if (IS_OBJECT_PTR_VALID(ScbChild)) ScbChild->SetVisibility(ESlateVisibility::Collapsed);

	BIND_PARAM_CPP_TO_UMG(BorChangeName, Bor_ChangeName);
	BIND_SLATE_WIDGET_FUNCTION(BorChangeName, OnMouseDoubleClickEvent, FName(TEXT("OnBorChangeNameDoubleClicked")));
	BIND_PARAM_CPP_TO_UMG(EdtFolderName, Edt_FolderName);
	BIND_WIDGET_FUNCTION(EdtFolderName, OnTextCommitted, UParamTreeFolderWidget::OnTextCommittedEdtFolderName);
	//BIND_WIDGET_FUNCTION(EdtFolderName, OnTextChanged, UParamTreeFolderWidget::OnTextChangedEdtFolderName);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_FolderName);

	BIND_PARAM_CPP_TO_UMG(BorFolderOrFile, Bor_Folder);
	BIND_PARAM_CPP_TO_UMG(ImgFolder, Img_Folder);

	IsVisible = true;
	IsSelected = false;
	NoEditable = false;
	SyncID = TEXT("-1");
	SwitchNameEditableState(false);
	BindDelegates();
}

void UParamTreeFolderWidget::UpdateContent(const FParameterTableData& InData)
{
	ParamData = InData;
	if (IS_OBJECT_PTR_VALID(EdtFolderName) && IS_OBJECT_PTR_VALID(TxtName))
	{
		if (InData.description.IsEmpty())
		{
			EdtFolderName->SetText(FText::FromString(InData.name));
			TxtName->SetText(FText::FromString(InData.name));
		}
		else
		{
			EdtFolderName->SetText(FText::FromString(InData.description + TEXT("-") + InData.name));
			TxtName->SetText(FText::FromString(InData.description + TEXT("-") + InData.name));
		}
	}
}

void UParamTreeFolderWidget::UpdateContent(const FParameterGroupTableData& InData)
{
	ParamGroupData = InData;
	if (IS_OBJECT_PTR_VALID(EdtFolderName) && IS_OBJECT_PTR_VALID(TxtName))
	{
		if (InData.description.IsEmpty())
		{
			EdtFolderName->SetText(FText::FromString(InData.group_name));
			TxtName->SetText(FText::FromString(InData.group_name));
		}
		else
		{
			EdtFolderName->SetText(FText::FromString(InData.description + TEXT("-") + InData.group_name));
			TxtName->SetText(FText::FromString(InData.description + TEXT("-") + InData.group_name));
		}
	}
}

void UParamTreeFolderWidget::AddSubParamFile(UParamTreeFileWidget* SelectedParam)
{
}

void UParamTreeFolderWidget::AddSubParamFile(const FParameterTableData& SubParamData)
{
	FParameterData ParamData;
	ParamData.Data = SubParamData;
	UParamTreeFileWidget* ParamWidget = UParamTreeFileWidget::Create();
	ParamWidget->UpdateContent(ParamData);
	ParamWidget->SetIsNew(true);
	ParamWidget->ParamFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsFileSelectHandler")));
	ParamWidget->ParamFileRemoveDelegate.BindUFunction(this, FName(TEXT("OnParamsFileDeleteHandler")));
	ParamWidget->ParamItemRightClickDelegate.BindUFunction(UParameterDetailWidget::Get(), FName(TEXT("OnParamTreeItemRightClickHandler")));
	ParamWidget->ParamFileOpenContextMenuDelegate.BindUFunction(UParameterDetailWidget::Get(), TEXT("HandleParamTreeFileOpenContextMenu"));
	ParamWidget->SetVisibility(ESlateVisibility::Visible);
	ScbChild->AddChild(ParamWidget);
	ScbChild->SetVisibility(ESlateVisibility::Visible);
	ChildParams.Add(ParamWidget);
	OnParamsFileSelectHandler(ParamWidget);
}

void UParamTreeFolderWidget::AddSubParamFile(const FParameterData& SubParamData, bool IsNew)
{
	if (!CkbExpandChild->IsChecked())
	{
		SyncID = SubParamData.Data.id;
		SetExpandChild(true);
	}
	UParamTreeFileWidget* ParamWidget = UParamTreeFileWidget::Create();
	ParamWidget->UpdateContent(SubParamData);
	ParamWidget->SetIsNew(IsNew);
	ParamWidget->ParamFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsFileSelectHandler")));
	ParamWidget->ParamFileRemoveDelegate.BindUFunction(this, FName(TEXT("OnParamsFileDeleteHandler")));
	ParamWidget->ParamItemRightClickDelegate.BindUFunction(UParameterDetailWidget::Get(), FName(TEXT("OnParamTreeItemRightClickHandler")));
	ParamWidget->ParamFileOpenContextMenuDelegate.BindUFunction(UParameterDetailWidget::Get(), TEXT("HandleParamTreeFileOpenContextMenu"));
	ParamWidget->SetVisibility(ESlateVisibility::Visible);
	ScbChild->AddChild(ParamWidget);
	ScbChild->SetVisibility(ESlateVisibility::Visible);
	ChildParams.Add(ParamWidget);
	OnParamsFileSelectHandler(ParamWidget);

}

void UParamTreeFolderWidget::TryUpdateSubParamFiles(const TArray<FParameterData>& InData)
{
	auto Children = ScbChild->GetAllChildren();
	for (auto Child : Children)
	{
		UParamTreeFileWidget* ParamWidget = Cast<UParamTreeFileWidget>(Child);
		const int32 Index = InData.IndexOfByPredicate([ParamWidget](const FParameterData& InOther) { return (InOther.Data.id == ParamWidget->GetParamData().Data.id) || (InOther.Data.name.Equals(ParamWidget->GetParamData().Data.name, ESearchCase::CaseSensitive)); });
		if (INDEX_NONE == Index) continue;
		ParamWidget->UpdateContent(InData[Index]);
	}
}

bool UParamTreeFolderWidget::GetIsChecked()
{
	return CkbExpandChild->IsChecked();
}

void UParamTreeFolderWidget::SetIsSelected(bool _IsSelected)
{
	Super::SetIsSelected(_IsSelected);
	SetFolderStateBrush(_IsSelected ? (CkbExpandChild->IsChecked() ? EParamFolderState::CheckSelected : EParamFolderState::UnCheckSelected) : (CkbExpandChild->IsChecked() ? EParamFolderState::CheckUnSelected : EParamFolderState::UnCheckUnSelected));
}

bool UParamTreeFolderWidget::IsParamAType()
{
	return true;
}

void UParamTreeFolderWidget::SetExpandChild(bool IsExpand)
{
	if (IS_OBJECT_PTR_VALID(CkbExpandChild))
	{
		CkbExpandChild->SetIsChecked(IsExpand);
	}
	SetFolderStateBrush(IsSelected ? (IsExpand ? EParamFolderState::CheckSelected : EParamFolderState::UnCheckSelected) : (IsExpand ? EParamFolderState::CheckUnSelected : EParamFolderState::UnCheckUnSelected));
	OnStateChangedCkbExpand(IsExpand);
}

void UParamTreeFolderWidget::ConstructSubParams()
{
	if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		ScbChild->ClearChildren();
		ChildParams.Empty();

#ifdef  USE_REF_LOCAL_FILE
		TArray<FParameterData> ParametersGroup = ACatalogPlayerController::Get()->ConstructGroupParam(GetParamGroupId());
#else
		TArray<FParameterData> ParametersGroup;
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GetParamGroupId(), ParametersGroup);
#endif
		for (auto ParametersGroupIter : ParametersGroup)
		{
			UParamTreeFileWidget* ParamWidget = UParamTreeFileWidget::Create();
			ParamWidget->UpdateContent(ParametersGroupIter);
			ParamWidget->SetIsNew(false);
			ParamWidget->ParamFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsFileSelectHandler")));
			ParamWidget->ParamFileRemoveDelegate.BindUFunction(this, FName(TEXT("OnParamsFileDeleteHandler")));
			ParamWidget->ParamItemRightClickDelegate.BindUFunction(UParameterDetailWidget::Get(), FName(TEXT("OnParamTreeItemRightClickHandler")));
			ParamWidget->ParamFileOpenContextMenuDelegate.BindUFunction(UParameterDetailWidget::Get(), TEXT("HandleParamTreeFileOpenContextMenu"));
			ParamWidget->SetVisibility(ESlateVisibility::Visible);
			ScbChild->AddChild(ParamWidget);
			ScbChild->SetVisibility(ESlateVisibility::Visible);
			ChildParams.Add(ParamWidget);
		}
	}
}

void UParamTreeFolderWidget::ConstructSubParams(FString InId)
{
	if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		ScbChild->ClearChildren();
		ChildParams.Empty();

#ifdef  USE_REF_LOCAL_FILE
		TArray<FParameterData> ParametersGroup = ACatalogPlayerController::Get()->ConstructGroupParam(GetParamGroupId());
#else
		TArray<FParameterData> ParametersGroup;
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GetParamGroupId(), ParametersGroup);
#endif
		//TArray<FParameterData> ParametersGroup;
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GetParamGroupId(), ParametersGroup);

		for (auto ParametersGroupIter : ParametersGroup)
		{
			UParamTreeFileWidget* ParamWidget = UParamTreeFileWidget::Create();
			ParamWidget->UpdateContent(ParametersGroupIter);
			ParamWidget->SetIsNew(false);
			ParamWidget->ParamFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsFileSelectHandler")));
			ParamWidget->ParamFileRemoveDelegate.BindUFunction(this, FName(TEXT("OnParamsFileDeleteHandler")));
			ParamWidget->ParamItemRightClickDelegate.BindUFunction(UParameterDetailWidget::Get(), FName(TEXT("OnParamTreeItemRightClickHandler")));
			ParamWidget->ParamFileOpenContextMenuDelegate.BindUFunction(UParameterDetailWidget::Get(), TEXT("HandleParamTreeFileOpenContextMenu"));
			ParamWidget->SetVisibility(ESlateVisibility::Visible);
			ScbChild->AddChild(ParamWidget);
			ScbChild->SetVisibility(ESlateVisibility::Visible);
			ChildParams.Add(ParamWidget);
			if (InId == ParametersGroupIter.Data.id)
				OnParamsFileSelectHandler(ParamWidget);
		}
	}
}

void UParamTreeFolderWidget::SyncSearchSelectParams(const FString& InParamName)
{
	const int32 SelectedParamIndex = ChildParams.IndexOfByPredicate([InParamName](UParamTreeFileWidget* InOther) { return InOther->GetParamData().Data.name.Equals(InParamName, ESearchCase::CaseSensitive); });
	UE_LOG(LogTemp, Log, TEXT("UParamTreeFolderWidget::SyncSearchSelectParams SelectedParamIndex=%d"), SelectedParamIndex);
	if (INDEX_NONE != SelectedParamIndex)
	{
		OnParamsFileSelectHandler(ChildParams[SelectedParamIndex]);
	}
}

void UParamTreeFolderWidget::OnParamsFileSelectHandler(UParamTreeFileWidget* SelectedParam)
{
	ParamFolderFileSelectDelegate.ExecuteIfBound(SelectedParam);
}

void UParamTreeFolderWidget::OnParamsFileDeleteHandler(UParamTreeFileWidget* SelectedParam)
{
	if (IS_OBJECT_PTR_VALID(SelectedParam))
	{
		SelectedParam->SetVisibility(ESlateVisibility::Collapsed);
		ChildParams.Remove(SelectedParam);
		if (IS_OBJECT_PTR_VALID(ScbChild))
		{
			ScbChild->RemoveChild(SelectedParam);
			if (ScbChild->GetChildrenCount() <= 0)
			{
				ScbChild->SetVisibility(ESlateVisibility::Collapsed);
			}
		}
	}
}

UParamTreeFolderWidget* UParamTreeFolderWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UParamTreeFolderWidget>(UParamTreeFolderWidget::ParamTreeFolderPath);
}

void UParamTreeFolderWidget::BindDelegates()
{
}

//void UParamTreeFolderWidget::GetFodlerImageBrush()
//{
//	ImageOne = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/jt_mr.jt_mr'")));
//	ImageThree = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/zk3.zk3'")));
//	ImageTwo = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/jt_mr2.jt_mr2'")));
//	ImageFour = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/Main/close_fnelei.close_fnelei'")));
//}

void UParamTreeFolderWidget::FolderClickAction()
{
	if (UParameterDetailWidget::Get()->CheckSelectWidgetValid())
	{
		IsSelected = true;
		ParamFolderSelectDelegate.ExecuteIfBound(this);
	}
}

void UParamTreeFolderWidget::SwitchNameEditableState(bool IsCanEdit)
{
	if (IS_OBJECT_PTR_VALID(EdtFolderName) && IS_OBJECT_PTR_VALID(TxtName))
	{
		EdtFolderName->SetIsReadOnly(!IsCanEdit);
		EdtFolderName->SetIsEnabled(IsCanEdit);
		EdtFolderName->SetVisibility(IsCanEdit ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		TxtName->SetVisibility(IsCanEdit ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible);
		auto PC = GWorld->GetFirstPlayerController();
		if (IsCanEdit)
		{
			EdtFolderName->SetUserFocus(PC);
		}
		else
		{
			TxtName->SetUserFocus(PC);
		}
	}
}

void UParamTreeFolderWidget::SetFolderStateBrush(const EParamFolderState& InType)
{
	if (!IS_OBJECT_PTR_VALID(ImgFolder))
	{
		return;
	}
	if (!IS_OBJECT_PTR_VALID(ImageOne) || !IS_OBJECT_PTR_VALID(ImageTwo))
	{
		GetFodlerImageBrush();
	}
	if (InType == EParamFolderState::UnCheckUnSelected || InType == EParamFolderState::UnCheckSelected)
	{
		ImgFolder->SetBrushFromTexture(ImageOne);
	}
	else if (InType == EParamFolderState::CheckSelected || InType == EParamFolderState::CheckUnSelected)
	{
		ImgFolder->SetBrushFromTexture(ImageTwo);
	}
}

void UParamTreeFolderWidget::OnStateChangedCkbExpand(bool IsCheck)
{
	if (IsCheck)
	{
		SetFolderStateBrush(IsSelected ? EParamFolderState::CheckSelected : EParamFolderState::CheckUnSelected);
		ConstructSubParams();
	}
	else
	{
		SetFolderStateBrush(IsSelected ? EParamFolderState::UnCheckSelected : EParamFolderState::UnCheckUnSelected);
		ScbChild->ClearChildren();
		ChildParams.Empty();
		ScbChild->SetVisibility(ESlateVisibility::Collapsed);
	}

	/*if (IS_OBJECT_PTR_VALID(ScbChild))
	{
		ScbChild->SetVisibility(IsCheck ? (ScbChild->GetChildrenCount() == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::Visible) : ESlateVisibility::Collapsed);
	}*/
}

void UParamTreeFolderWidget::OnClickedBtnSelect()
{
	if (IS_OBJECT_PTR_VALID(UParameterDetailWidget::Get()->GetCurrentSelectWidget()))
	{
		if (!UParameterDetailWidget::Get()->GetCurrentSelectWidget()->IsParamAType())
		{
			if (Cast<UParamTreeFileWidget>(UParameterDetailWidget::Get()->GetCurrentSelectWidget())->GetIsNew()
				&& Cast<UParamTreeFileWidget>(UParameterDetailWidget::Get()->GetCurrentSelectWidget())->GetParamClassificID() == FCString::Atoi(*ParamData.id))
			{
				bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
					, FText::FromStringTable(FName("PosSt"), TEXT("This action will delete the newly param, are you sure?")).ToString());
				if (Res)
				{
					//UParameterDetailWidget::Get()->SetIsAddParDele(true);
					ParamFolderSelectDelegate.ExecuteIfBound(this);
				}
			}
			else
			{
				FolderClickAction();
			}
		}
		else
		{
			FolderClickAction();
		}
	}
	else
	{
		FolderClickAction();
	}
	//if (UParameterDetailWidget::Get()->CheckSelectWidgetValid())
	//{
	//	IsSelected = true;
	//	ParamFolderSelectDelegate.ExecuteIfBound(this);
	//}
}

void UParamTreeFolderWidget::OnHoveredBtnSelect()
{
	if (!IsSelected && BorFolderOrFile && IsVisible)
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Hover);
	}
}

void UParamTreeFolderWidget::OnUnHoveredBtnSelect()
{
	if (!IsSelected && BorFolderOrFile && IsVisible)
	{
		Super::SetFolderOrFileBackBorderColor(EBackBorderState::Normal);
	}
}

void UParamTreeFolderWidget::OnTextCommittedEdtFolderName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared) return;
	FString ParamTypeName = Text.ToString();
	ParamTypeName.TrimStartAndEndInline();
	if (ParamTypeName.Len() > 20)
	{
		EdtFolderName->SetText(FText::FromString(ParamGroupData.group_name));
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("The Group Key Is Too Long, Please Change!")).ToString());
	}
	else if (ParamTypeName.IsEmpty())
	{
		EdtFolderName->SetText(FText::FromString(ParamGroupData.group_name));
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("The Group Key Is Empty, Please Change!")).ToString());
	}
	else if (ParamTypeName.Equals(ParamGroupData.group_name,ESearchCase::CaseSensitive))
	{
		SwitchNameEditableState(false);
		return;
	}
	else
	{
		//if (FLocalDatabaseParameterLibrary::SearchNameParameterGroup(ParamTypeName))
		if(ACatalogPlayerController::Get()->HasGroupName(ParamTypeName))
		{
			EdtFolderName->SetText(FText::FromString(ParamGroupData.group_name));
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("The Group Key Is Exist, Please Change!")).ToString());
		}
		else
		{
			ParamGroupData.group_name = ParamTypeName;
			ParamFolderNameChangeDelegate.ExecuteIfBound(ParamGroupData.id, ParamGroupData.group_name);
			//FLocalDatabaseParameterLibrary::UpdateParameterGroup(ParamGroupData);
			ACatalogPlayerController::Get()->UpdateGroupData(ParamGroupData);
			EdtFolderName->SetText(FText::FromString(ParamTypeName));
			UParameterDetailWidget::Get()->UpdateParameterGroupResponse(ParamGroupData);
		}
	}
	SwitchNameEditableState(false);
}

FEventReply UParamTreeFolderWidget::OnBorChangeNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		if (NoEditable)
		{
			return FEventReply();
		}
		SwitchNameEditableState(true);
		return FEventReply(true);
	}
	return FEventReply();
}

FReply UParamTreeFolderWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		if (IsSelected)
		{
			ParamFolderOpenContextMenuDelegate.ExecuteIfBound(this, InMouseEvent);
		}
		else
		{
			ParamItemRightClickDelegate.ExecuteIfBound();
		}
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

#undef LOCTEXT_NAMESPACE
