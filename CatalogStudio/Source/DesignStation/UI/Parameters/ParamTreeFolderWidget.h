// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "ParamTreeFileWidget.h"

#include "ParamTreeFolderWidget.generated.h"

/**
 *
 */

class UCheckBox;
class UImage;
class UScrollBox;
class UEditableText;

UENUM(BlueprintType)
enum class EParamFolderState : uint8
{
	UnCheckUnSelected = 0,
	UnCheckSelected,
	CheckSelected,
	CheckUnSelected
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FParamFolderSelectDelegate, UParamTreeFolderWidget*, FolderItem);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FParamFolderNameChangeDelegate, const int32&, id, const FString&, NewName);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FParamFolderOpenContextMenuDelegate, UParamTreeFolderWidget*, FolderItem, const FPointerEvent&, InMouseEvent);

UCLASS()
class DESIGNSTATION_API UParamTreeFolderWidget : public UFolderAndFileBaseWidget
{
	GENERATED_BODY()

public:
	/*virtual bool Initialize() override;*/

	virtual void NativeOnInitialized() override;

	void UpdateContent(const FParameterTableData& InData);
	void UpdateContent(const FParameterGroupTableData& InData);
	void AddSubParamFile(UParamTreeFileWidget* SelectedParam);
	void AddSubParamFile(const FParameterTableData& SubParamData);
	void AddSubParamFile(const FParameterData& SubParamData, bool IsNew = true);

	void TryUpdateSubParamFiles(const TArray<FParameterData>& InData);

	FORCEINLINE FString GetId() const { return ParamData.id; }
	FORCEINLINE void SetSyncID(const FString& InID) { SyncID = InID; }
	FORCEINLINE void SetCanNotEditable(bool InEditable) { NoEditable = InEditable; }
	bool GetIsChecked();

	virtual void SetIsSelected(bool _IsSelected) override;
	virtual bool IsParamAType() override;
	virtual FString GetParamId() override { return ParamData.id; }
	int32 GetParamGroupId() { return ParamGroupData.id; }
	FParameterGroupTableData GetParamGroup() { return ParamGroupData; }
	void SetExpandChild(bool IsExpand);

	void ConstructSubParams();
	void ConstructSubParams(FString InId);

	void SyncSearchSelectParams(const FString& InParamName);
	UFUNCTION()
		void OnParamsFileSelectHandler(UParamTreeFileWidget* SelectedParam);
	UFUNCTION()
		void OnParamsFileDeleteHandler(UParamTreeFileWidget* SelectedParam);

	static UParamTreeFolderWidget* Create();

private:
	void BindDelegates();

	FString SyncID;

private:
	//void GetFodlerImageBrush();
	void FolderClickAction();
	void SwitchNameEditableState(bool IsCanEdit);

public:
	FParamFolderSelectDelegate ParamFolderSelectDelegate;
	FParamFileSelectDelegate ParamFolderFileSelectDelegate;
	FParamFolderNameChangeDelegate ParamFolderNameChangeDelegate;
	FParamFileRemoveDelegate ParamFileRemoveDelegate;
	FParamFolderOpenContextMenuDelegate ParamFolderOpenContextMenuDelegate;

	UPROPERTY()
		FParameterTableData ParamData;
	UPROPERTY()
		FParameterGroupTableData ParamGroupData;
	UPROPERTY()
		TArray<UParamTreeFileWidget*> ChildParams;

	void SetFolderStateBrush(const EParamFolderState& InType);

	bool IsNew;
	bool NoEditable;

	static FString ParamTreeFolderPath;

protected:
	UFUNCTION()
		void OnStateChangedCkbExpand(bool IsCheck);
	UFUNCTION()
		void OnClickedBtnSelect();
	UFUNCTION()
		void OnHoveredBtnSelect();
	UFUNCTION()
		void OnUnHoveredBtnSelect();
	UFUNCTION()
		void OnTextCommittedEdtFolderName(const FText& Text, ETextCommit::Type CommitMethod);

protected:
	UFUNCTION()
		FEventReply OnBorChangeNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	UPROPERTY()
		UCheckBox* CkbExpandChild;
	UPROPERTY()
		UImage* ImgFolder;
	UPROPERTY()
		UBorder* BorChangeName;
	UPROPERTY()
		UEditableText* EdtFolderName;
	UPROPERTY()
		UScrollBox* ScbChild;
};
