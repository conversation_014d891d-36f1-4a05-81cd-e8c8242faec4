// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Parameters/ParameterAddItemWidget.h"
#include "UI/UIMacroDef.h"

void UParameterAddItemWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if (BtnDelete != nullptr)
	{
		BIND_WIDGET_FUNCTION(BtnDelete, OnClicked,UParameterAddItemWidget::OnDeleteClick);
	}
}

void UParameterAddItemWidget::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	if (ListItemObject != nullptr)
	{
		UParameterAddItemData* Data = Cast<UParameterAddItemData>(ListItemObject);
		CodeText->SetText(Data->Code);
		NameText->SetText(Data->Name);
	}
}

void UParameterAddItemWidget::OnDeleteClick()
{
	//ParameterAddItemDelete_Delegate.ExecuteIfBound(GetListItem<UParameterAddItemData>());
	UParameterAddItemData* IteData = GetListItem<UParameterAddItemData>();
	if (IteData != nullptr && IteData->ParameterDetail != nullptr)
	{
		IteData->ParameterDetail->RemoveSelectedParameter(IteData);
	}
}
