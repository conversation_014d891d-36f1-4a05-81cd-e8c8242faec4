// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/IUserObjectListEntry.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "ParameterDetailWidget.h"
#include "ParameterAddItemWidget.generated.h"


UCLASS()
class UParameterAddItemData : public UObject
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite)
	FText Code;

	UPROPERTY(BlueprintReadWrite)
	FText Name;

	UPROPERTY()
	UParameterDetailWidget* ParameterDetail = nullptr;
};

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UParameterAddItemWidget : public UUserWidget, public IUserObjectListEntry
{
	GENERATED_BODY()
	
public:

	DECLARE_DELEGATE_OneParam(FParameterAddItemDelete_Delegate, UParameterAddItemData*)

	virtual void NativeOnInitialized() override;
	
public:
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* CodeText;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* NameText;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UButton* BtnDelete;

	FParameterAddItemDelete_Delegate ParameterAddItemDelete_Delegate;

protected:
	virtual void NativeOnListItemObjectSet(UObject* ListItemObject) override;
	UFUNCTION()
	void OnDeleteClick();
};
