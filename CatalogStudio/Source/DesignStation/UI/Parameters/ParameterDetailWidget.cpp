// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterDetailWidget.h"

#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "DataCenter/Parameter/ParameterEffectionParser.h"
#include "DataCenter/RefFile/Data/RefToParamDataLibrary.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/LoadUI/MergeProcessWidget.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"
#include "ProtobufOperator/Operators/ProtobufOperatorFunctionLibrary.h"
#include "Runtime/UMG/Public/Components/ListView.h"
#include "ParameterAddItemWidget.h"
#include "Runtime/UMG/Public/Components/WidgetSwitcher.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"

#define LOCTEXT_NAMESPACE "ParameterVisibility"

#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

extern const int PopUIZOrder;
const FString DefaultTypeName = TEXT("NewType");
const FString DefaultParamName = TEXT("NewParam");

const FLinearColor EnableColor = FLinearColor::White;
const FLinearColor UnEnableColor = FLinearColor(0.056128f, 0.155926f, 0.854993f, 1.0f);

const FString GlobalParamTitle = TEXT("The global variable");
const FString FolderOrFileParamTitle = TEXT("The param detail");

const TArray<FString> EParamVisibility =
{
	NSLOCTEXT("ParameterVisibility", "ParamShow", "Show").ToString(),
	NSLOCTEXT("ParameterVisibility", "ParamHidden", "Hidden").ToString()
};

extern const FString ParamShowFlag = TEXT("1");
extern const FString ParamHiddenFlag = TEXT("0");

bool UParameterDetailWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	return true;
}

void UParameterDetailWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(BorGlobalTree, Bor_GlobalTree);
	BIND_PARAM_CPP_TO_UMG(EdtParamSearch, Edt_ParamSearch);
	BIND_WIDGET_FUNCTION(EdtParamSearch, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtParamSearch);
	BIND_WIDGET_FUNCTION(EdtParamSearch, OnTextChanged, UParameterDetailWidget::OnTextChangedEdtParamSearch);
	BIND_PARAM_CPP_TO_UMG(BtnParamSearch, Btn_ParamSearch);
	BIND_WIDGET_FUNCTION(BtnParamSearch, OnClicked, UParameterDetailWidget::OnClickedBtnParamSearch);

	BIND_PARAM_CPP_TO_UMG(BorGlobalParams, Bor_GlobalParams);
	BIND_PARAM_CPP_TO_UMG(BorSearchParams, Bor_SearchParam);
	BIND_PARAM_CPP_TO_UMG(ScbSearchParams, Scb_SearchParams);

	BIND_PARAM_CPP_TO_UMG(BorParamDetail, Bor_ParamDetail);
	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_WIDGET_FUNCTION(EdtName, OnTextChanged, UParameterDetailWidget::OnTextChangedEdtName);
	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtName);
	BIND_PARAM_CPP_TO_UMG(EdtDM, Edt_DM);
	BIND_WIDGET_FUNCTION(EdtDM, OnTextChanged, UParameterDetailWidget::OnTextChangedEdtDM);
	BIND_WIDGET_FUNCTION(EdtDM, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtDM);

	BIND_PARAM_CPP_TO_UMG(CBSClassific, CBS_Classific);
	BIND_WIDGET_FUNCTION(CBSClassific, OnSelectionChanged, UParameterDetailWidget::OnSelectionChangedCBSClassific);
	BIND_PARAM_CPP_TO_UMG(EdtID, Edt_ID);
	BIND_WIDGET_FUNCTION(EdtID, OnTextChanged, UParameterDetailWidget::OnTextChangedEdtID);
	BIND_WIDGET_FUNCTION(EdtID, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtID);

	BIND_PARAM_CPP_TO_UMG(EdtValueExpress, Edt_ValueExpress);
	BIND_WIDGET_FUNCTION(EdtValueExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtValueExpress);
	BIND_PARAM_CPP_TO_UMG(BtnValueExpress, Btn_ValueExpress);
	BIND_WIDGET_FUNCTION(BtnValueExpress, OnClicked, UParameterDetailWidget::OnClickedBtnValueExpress);
	BIND_PARAM_CPP_TO_UMG(BorNoEnum, Bor_NoEnum);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtValue);
	BIND_PARAM_CPP_TO_UMG(BorEnum, Bor_Enum);
	BIND_PARAM_CPP_TO_UMG(CBSEnumValues, CBS_EnumValues);
	BIND_WIDGET_FUNCTION(CBSEnumValues, OnSelectionChanged, UParameterDetailWidget::OnSelectionChangedCBSValue);

	BIND_PARAM_CPP_TO_UMG(EdtMaxExpress, Edt_MaxExpress);
	BIND_WIDGET_FUNCTION(EdtMaxExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMaxExpress);
	BIND_PARAM_CPP_TO_UMG(BtnMaxExpress, Btn_MaxExpress);
	BIND_WIDGET_FUNCTION(BtnMaxExpress, OnClicked, UParameterDetailWidget::OnClickedBtnValueMaxExpress);
	BIND_PARAM_CPP_TO_UMG(EdtMax, Edt_Max);
	BIND_WIDGET_FUNCTION(EdtMax, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMaxValue);

	BIND_PARAM_CPP_TO_UMG(EdtMinExpress, Edt_MinExpress);
	BIND_WIDGET_FUNCTION(EdtMinExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMinExpress);
	BIND_PARAM_CPP_TO_UMG(BtnMinExpress, Btn_MinExpress);
	BIND_WIDGET_FUNCTION(BtnMinExpress, OnClicked, UParameterDetailWidget::OnClickedBtnValueMinExpress);
	BIND_PARAM_CPP_TO_UMG(EdtMin, Edt_Min);
	BIND_WIDGET_FUNCTION(EdtMin, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMinValue);

	BIND_PARAM_CPP_TO_UMG(EdtVisiExpress, Edt_VisiExpress);
	BIND_WIDGET_FUNCTION(EdtVisiExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtVisiExpress);
	BIND_PARAM_CPP_TO_UMG(BtnVisiExpress, Btn_VisiExpress);
	BIND_WIDGET_FUNCTION(BtnVisiExpress, OnClicked, UParameterDetailWidget::OnClickedBtnVisiExpress);
	BIND_PARAM_CPP_TO_UMG(EdtVisibility, Edt_Visibility);
	BIND_WIDGET_FUNCTION(EdtVisibility, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtVisibility);

	BIND_PARAM_CPP_TO_UMG(EdtEditExpress, Edt_EditExpress);
	BIND_WIDGET_FUNCTION(EdtEditExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtEditExpress);
	BIND_PARAM_CPP_TO_UMG(BtnEditExpress, Btn_EditExpress);
	BIND_WIDGET_FUNCTION(BtnEditExpress, OnClicked, UParameterDetailWidget::OnClickedBtnEditExpress);
	BIND_PARAM_CPP_TO_UMG(EdtEditable, Edt_Editable);
	BIND_WIDGET_FUNCTION(EdtEditable, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtEditable);

	BIND_PARAM_CPP_TO_UMG(EdtSpecialExpress, Edt_SpecialExpress);
	BIND_WIDGET_FUNCTION(EdtSpecialExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtSpecialExpress);
	BIND_PARAM_CPP_TO_UMG(BtnSpecialExpress, Btn_SpecialExpress);
	BIND_WIDGET_FUNCTION(BtnSpecialExpress, OnClicked, UParameterDetailWidget::OnClickedBtnSpecialExpress);
	BIND_PARAM_CPP_TO_UMG(EdtSpecialable, Edt_Specialable);
	BIND_WIDGET_FUNCTION(EdtSpecialable, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtSpecialable);

	BIND_PARAM_CPP_TO_UMG(EdtMustExpress, Edt_MustExpress);
	BIND_WIDGET_FUNCTION(EdtMustExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMustExpress);
	BIND_PARAM_CPP_TO_UMG(BtnMustExpress, Btn_MustExpress);
	BIND_WIDGET_FUNCTION(BtnMustExpress, OnClicked, UParameterDetailWidget::OnClickedBtnMustExpress);
	BIND_PARAM_CPP_TO_UMG(EdtMustable, Edt_Mustable);
	BIND_WIDGET_FUNCTION(EdtMustable, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtMustable);

	BIND_PARAM_CPP_TO_UMG(BtnAdd, Btn_AddEnum);
	BIND_WIDGET_FUNCTION(BtnAdd, OnClicked, UParameterDetailWidget::OnClickedBtnAdd);
	BIND_PARAM_CPP_TO_UMG(ScbEnumList, Scb_EnumList);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UParameterDetailWidget::OnClickedBtnCancel);
	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, UParameterDetailWidget::OnClickedBtnSave);

	BIND_PARAM_CPP_TO_UMG(BorParamAddBack, Bor_ParamAddBack);
	BIND_PARAM_CPP_TO_UMG(BorParamAdd, Bor_ParamAdd);
	BIND_PARAM_CPP_TO_UMG(BtnParamAdd, Btn_ParamAdd);
	BIND_WIDGET_FUNCTION(BtnParamAdd, OnClicked, UParameterDetailWidget::OnClickedBtnParamAdd);

	BIND_PARAM_CPP_TO_UMG(BorParamUpLoadBack, Bor_ParamUpLoadBack);
	BIND_PARAM_CPP_TO_UMG(BorParamUpLoad, Bor_ParamUpLoad);
	BIND_PARAM_CPP_TO_UMG(BtnParamUpLoad, Btn_ParamUpLoad);
	BIND_WIDGET_FUNCTION(BtnParamUpLoad, OnClicked, UParameterDetailWidget::OnClickedBtnParamUpLoad);

	BIND_PARAM_CPP_TO_UMG(BorParamDownLoadBack, Bor_ParamDownLoadBack);
	BIND_PARAM_CPP_TO_UMG(BorParamDownLoad, Bor_ParamDownLoad);
	BIND_PARAM_CPP_TO_UMG(BtnParamDownLoad, Btn_ParamDownLoad);
	BIND_WIDGET_FUNCTION(BtnParamDownLoad, OnClicked, UParameterDetailWidget::OnClickedBtnParamDownLoad);

	BIND_PARAM_CPP_TO_UMG(CPTips, CP_Tips);
	BIND_PARAM_CPP_TO_UMG(BorTipBackground, Bor_TipBackground);
	BIND_PARAM_CPP_TO_UMG(ImgResult, Img_Result);
	BIND_PARAM_CPP_TO_UMG(TxtResult, Txt_Result);
	/*BIND_PARAM_CPP_TO_UMG(BtnTipClose, Btn_TipClose);
	BIND_WIDGET_FUNCTION(BtnTipClose, OnClicked, UParameterDetailWidget::OnClickedBtnTipClose);*/

	BIND_PARAM_CPP_TO_UMG(BtnSelect, Btn_Select);
	BIND_WIDGET_FUNCTION(BtnSelect, OnClicked, UParameterDetailWidget::OnClickedBtnSelect);

	BIND_PARAM_CPP_TO_UMG(ListViewSelect, ListView_Select);
	BIND_PARAM_CPP_TO_UMG(BoxSelectList, BS_BoxSelectList);
	BIND_PARAM_CPP_TO_UMG(SelectSwitcher, SS_SelectSwitcher);

	BIND_PARAM_CPP_TO_UMG(BtnSaveAll, Btn_Save_All);
	BIND_WIDGET_FUNCTION(BtnSaveAll, OnClicked, UParameterDetailWidget::OnClickedBtnAllSave);
	BIND_PARAM_CPP_TO_UMG(BtnCancelAll, Btn_Cancel_All);
	BIND_WIDGET_FUNCTION(BtnCancelAll, OnClicked, UParameterDetailWidget::OnClickedBtnAllCancel);

	BIND_WIDGET_FUNCTION(ChkNoMatchEnum, OnCheckStateChanged, UParameterDetailWidget::OnStateChangedChkNoMatchEnumData);

	BIND_WIDGET_FUNCTION(Edt_GridExpress, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtGridExpression);
	BIND_WIDGET_FUNCTION(Btn_GridExpress, OnClicked, UParameterDetailWidget::OnClickedBtnGridExpression);
	BIND_WIDGET_FUNCTION(Edt_GridValue, OnTextCommitted, UParameterDetailWidget::OnTextCommittedEdtGridValue);

	IsNewParameter = false;
	IsDMUnique = true;
	IsNameUnique = true;
	IsIdUnique = true;
	CurrentEditType = 0;
	SelectWidget = nullptr;
	ToDelFileWidget = nullptr;
	SwitchParamsShow();
	BindDelegate();
	PreSelectFolderID = TEXT("-1");

}

void UParameterDetailWidget::UpdateContent(const FParameterData& InData, const int32& ParamType)
{
	UE_LOG(LogTemp, Log, TEXT("ParameterDetailWidget---update content"));
	ParamDetailData.CopyData(InData);
	OriginalData.CopyData(ParamDetailData);
	CurrentEditType = ParamType;
	SwitchWidgetLayout(ParamType);
	SwitchDetailWidgetState(ParamType);
	SwitchValueTypeShow(InData.Data.is_enum ? true : false);
	SwitchParamsShow();
	SelectWidget = nullptr;
	ClearSearchText();

	if (CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{
		SelectedParamDetailDatas.Empty();
		ListViewSelect->ClearListItems();
	}

	UpdateContent(InData);
}

void UParameterDetailWidget::UpdateContent(const FParameterData& InData)
{
	UE_LOG(LogTemp, Log, TEXT("ParameterDetailWidget---update content"));
	ParamDetailData.CopyData(InData);
	OriginalData.CopyData(ParamDetailData);
	SwitchDetailWidgetState(CurrentEditType);
	SwitchValueTypeShow(InData.Data.is_enum ? true : false);

	if (EdtName)
	{
		EdtName->SetText(FText::FromString(InData.Data.description));
	}
	if (IS_OBJECT_PTR_VALID(EdtDM))
	{
		EdtDM->SetText(FText::FromString(InData.Data.name));
	}
	if (IS_OBJECT_PTR_VALID(CBSClassific))
	{
		UpdateCBSClassific(InData.Data.classific_id);
	}
	if (IS_OBJECT_PTR_VALID(EdtID))
	{
		EdtID->SetText(FText::FromString(InData.Data.param_id));
	}
	if (IS_OBJECT_PTR_VALID(EdtMaxExpress) && IS_OBJECT_PTR_VALID(EdtMax))
	{
		const FString FormatEdtVisibility = !ParamDetailData.Data.max_value.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.max_value)) : ParamDetailData.Data.max_value;
		EdtMax->SetText(FText::FromString(FormatEdtVisibility));
		EdtMaxExpress->SetText(FText::FromString(ParamDetailData.Data.max_expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtMinExpress) && IS_OBJECT_PTR_VALID(EdtMin))
	{
		const FString FormatEdtVisibility = !ParamDetailData.Data.min_value.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.min_value)) : ParamDetailData.Data.min_value;
		EdtMin->SetText(FText::FromString(FormatEdtVisibility));
		EdtMinExpress->SetText(FText::FromString(ParamDetailData.Data.min_expression));
	}
	if (InData.Data.is_enum && IS_OBJECT_PTR_VALID(CBSEnumValues) && IS_OBJECT_PTR_VALID(EdtValueExpress))
	{
		CBSEnumValues->RefreshOptions();
		//RefreshCBSValueEnum();

		//FormatValue(InData.Data.value);
		OldValue = FString(TEXT(""));
		ParamDetailData.Data.expression = InData.Data.expression;
		//EdtValueExpress->SetText(FText::FromString(InData.Data.expression));

	}
	else
	{
		if (IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(EdtValue))
		{
			EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
			//ParamDetailData.Data.value = FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.value));
			EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));

			OldValue = ParamDetailData.Data.value;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisibility))
	{
		const FString FormatEdtVisibility = !ParamDetailData.Data.visibility.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.visibility)) : ParamDetailData.Data.visibility;
		EdtVisibility->SetText(FText::FromString(FormatEdtVisibility));
		EdtVisiExpress->SetText(FText::FromString(InData.Data.visibility_exp));
	}
	if (IS_OBJECT_PTR_VALID(EdtEditExpress) && IS_OBJECT_PTR_VALID(EdtEditable))
	{
		const FString FormatEditable = !ParamDetailData.Data.editable.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.editable)) : ParamDetailData.Data.editable;
		EdtEditable->SetText(FText::FromString(FormatEditable));
		EdtEditExpress->SetText(FText::FromString(InData.Data.editable_exp));
	}

	if (IS_OBJECT_PTR_VALID(EdtSpecialExpress) && IS_OBJECT_PTR_VALID(EdtSpecialable))
	{
		const FString FormatSpecialable = !ParamDetailData.Data.Special.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.Special)) : ParamDetailData.Data.Special;
		EdtSpecialable->SetText(FText::FromString(FormatSpecialable));
		EdtSpecialExpress->SetText(FText::FromString(InData.Data.Special_exp));
	}

	if (IS_OBJECT_PTR_VALID(EdtMustExpress) && IS_OBJECT_PTR_VALID(EdtMustable))
	{
		const FString FormatMustable = !ParamDetailData.Data.Must.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.Must)) : ParamDetailData.Data.Must;
		EdtMustable->SetText(FText::FromString(FormatMustable));
		EdtMustExpress->SetText(FText::FromString(InData.Data.Must_exp));
	}

	//grid mark
	UpdateParamGridMark(ParamDetailData.Data.grid_value_mark);
	if (Edt_GridExpress && Edt_GridValue)
	{
		Edt_GridExpress->SetText(FText::FromString(ParamDetailData.Data.grid_expression));
		Edt_GridValue->SetText(FText::FromString(ParamDetailData.Data.grid_value));
	}

	//enum
	ChkNoMatchEnum->SetIsChecked(ParamDetailData.Data.no_match_enum_data);

	ParamEnumItems.Empty();
	if (ParamDetailData.Data.is_enum)
	{
		UpdateParamEnumContent(ParamDetailData.EnumData);
		RefreshCBSValueEnum();
		EdtValueExpress->SetText(FText::FromString(InData.Data.expression));
	}
	else
	{
		ScbEnumList->ClearChildren();
	}
	SyncValueState();
}

void UParameterDetailWidget::SetIsNewParam(bool _IsNew)
{

}

void UParameterDetailWidget::SetIsVisibilityValid(bool _IsValid, const FString& VisibilityExpression)
{

}

void UParameterDetailWidget::SetIsEditableValid(bool _IsValid, const FString& EditableExpression)
{

}

void UParameterDetailWidget::SwitchWidgetBindFunc(const ESaveType& InType)
{

}

void UParameterDetailWidget::SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData)
{
	ParentParameters = InParamData;
}

void UParameterDetailWidget::SetFolderOrFileLocalParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData)
{
	LocalParameters = InParamData;
}

void UParameterDetailWidget::ClearSelectState()
{
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		SelectWidget->SetIsSelected(false);
		SelectWidget = nullptr;
		SwitchParamBorderShow(false);
	}
}

void UParameterDetailWidget::ShowSaveTip(bool IsSucceed)
{
	UpdateSaveTipStyle(IsSucceed);
	if (IS_OBJECT_PTR_VALID(CPTips))
	{
		CPTips->SetVisibility(ESlateVisibility::Visible);
		CPTips->SetRenderOpacity(1.0f);
	}
	UpdateSaveTipTimeActor();
}

void UParameterDetailWidget::UpdateSaveTipTimeActor()
{
	if (!IS_OBJECT_PTR_VALID(SaveTipTimeActor))
	{
		SaveTipTimeActor = NewObject<ATimeActor>();
	}
	if (IS_OBJECT_PTR_VALID(SaveTipTimeActor))
	{
		SaveTipTimeActor->ResetTipShowTime();
		SaveTipTimeActor->BindTimeHandler();
		SaveTipTimeActor->TimeActorDelegate.BindUFunction(this, FName(TEXT("OnTimeActorHandler")));
	}
}

bool UParameterDetailWidget::ParamRightMouseAction()
{
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		if (Cast<UParamTreeFileWidget>(SelectWidget))
		{
			if (Cast<UParamTreeFileWidget>(SelectWidget)->GetIsNew())
			{
				bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
					, FText::FromStringTable(FName("PosSt"), TEXT("This action will delete the newly param, are you sure?")).ToString());
				if (Res)
				{
					ToDelFileWidget = Cast<UParamTreeFileWidget>(SelectWidget);
					ClearSelectState();
				}
				return Res;
			}
			else
			{
				if (!CheckParamDataChange())
				{
					bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
						FText::FromStringTable(FName("PosSt"), TEXT("This action will discard the change of param, are you sure?")).ToString());
					if (Res)
					{
						ClearSelectState();
					}
					return Res;
				}
				else
				{
					ClearSelectState();
					return true;
				}
			}
		}
		else if (Cast<UParamTreeFolderWidget>(SelectWidget))
		{
			ClearSelectState();
		}
	}
	return true;
}

bool UParameterDetailWidget::CheckSelectWidgetValid()
{
	if (Cast<UParamTreeFileWidget>(SelectWidget))
	{
		if (Cast<UParamTreeFileWidget>(SelectWidget)->GetIsNew())
		{
			bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
				, FText::FromStringTable(FName("PosSt"), TEXT("This action will delete the newly param, are you sure?")).ToString());
			if (Res)
			{
				ToDelFileWidget = Cast<UParamTreeFileWidget>(SelectWidget);
				IsAddParDele = true;
			}
			return Res;
		}
		else
		{
			if (!CheckParamDataChange())
			{
				return UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
					, FText::FromStringTable(FName("PosSt"), TEXT("This action will discard the change of param, are you sure?")).ToString());
			}
			return true;
		}
	}
	return true;
}

bool UParameterDetailWidget::CheckParamDataValid(FString& ErrorMessage)
{
	if (IS_OBJECT_PTR_VALID(EdtName) && !ParamDetailData.Data.description.Equals(OriginalData.Data.description)) // name -- description
	{
		//IsNameUnique = FLocalDatabaseParameterLibrary::SearchNameGlobalParameter(EdtName->GetText().ToString());
		IsNameUnique = ACatalogPlayerController::Get()->IsDescriptionUnique(EdtName->GetText().ToString());
		if (!IsNameUnique)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtName"));
			ErrorMessage = TEXT("Name Must Be Unique");
			return false;
		}
		if (EdtName->GetText().ToString().Len() > 20)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtName"));
			ErrorMessage = TEXT("Name must be not empty and unique and length less 20");
			return false;
		}
	}
	else if (EdtName->GetText().IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("EdtName"));
		ErrorMessage = TEXT("Name must be not empty and unique and length less 20");
		return false;
	}
	if (IS_OBJECT_PTR_VALID(EdtDM) && !ParamDetailData.Data.name.Equals(OriginalData.Data.name,ESearchCase::CaseSensitive))
	{
		FString CleanData = EdtDM->GetText().ToString();
		FRegexPattern Pattern(TEXT("^[a-zA-Z]+[a-zA-Z0-9]*+$"));
		FRegexMatcher RegMatcher(Pattern, CleanData);
		RegMatcher.SetLimits(0, CleanData.Len());
		bool Res = RegMatcher.FindNext();
		//IsDMUnique = FLocalDatabaseParameterLibrary::SearchDMGlobalParameter(EdtDM->GetText().ToString());
		IsDMUnique = ACatalogPlayerController::Get()->IsNameUnique(EdtDM->GetText().ToString());
		if (!EdtDM->GetText().IsEmpty() && !IsDMUnique)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtDM_1"));
			ErrorMessage = TEXT("DM Must Be Unique");
			return false;
		}
		if (EdtDM->GetText().IsEmpty() || EdtDM->GetText().ToString().Len() > 20 || !Res)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtDM_2"));
			ErrorMessage = TEXT("DM must be not empty and unique and length less 20,\ncombine by number and letter,\nthe first char can't be num");
			return false;
		}
	}
	else if (EdtDM->GetText().IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("EdtDM_2"));
		ErrorMessage = TEXT("DM must be not empty and unique and length less 20,\ncombine by number and letter,\nthe first char can't be num");
		return false;
	}
	if (IS_OBJECT_PTR_VALID(EdtID) && !ParamDetailData.Data.param_id.Equals(OriginalData.Data.param_id))
	{
		FString CleanData = EdtID->GetText().ToString();
		FRegexPattern Pattern(TEXT("[.]"));
		FRegexMatcher RegMatcher(Pattern, CleanData);
		RegMatcher.SetLimits(0, CleanData.Len());
		bool Res = RegMatcher.FindNext();
		//IsIdUnique = FLocalDatabaseParameterLibrary::SearchIDGlobalParameter(EdtID->GetText().ToString());
		IsIdUnique = ACatalogPlayerController::Get()->IsParamIDUnique(EdtID->GetText().ToString());
		if (!EdtID->GetText().IsEmpty() && !IsIdUnique)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtID"));
			ErrorMessage = TEXT("ID Must Be Unique");
			return false;
		}
		if (EdtID->GetText().IsEmpty() || !EdtID->GetText().IsNumeric() || Res || EdtID->GetText().ToString().Len() > 8)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtID"));
			ErrorMessage = TEXT("ID must be not empty and unique and integer and length less 8");
			return false;
		}
	}
	else if (EdtID->GetText().IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("EdtID"));
		ErrorMessage = TEXT("ID must be not empty and unique and integer and length less 8");
		return false;
	}
	if (IS_OBJECT_PTR_VALID(EdtValueExpress) && !ParamDetailData.Data.value.Equals(OriginalData.Data.value))
	{
		if (EdtValueExpress->GetText().IsEmpty()/* || !EdtValueExpress->GetText().IsNumeric()*/)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtValueExpress"));
			ErrorMessage = TEXT("ValueExpress must be not empty");
			return false;
		}
	}
	else if (EdtValueExpress->GetText().IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("EdtID"));
		ErrorMessage = TEXT("ValueExpress must be not empty");
		return false;
	}
	if (IS_OBJECT_PTR_VALID(BorEnum) && BorEnum->GetVisibility() != ESlateVisibility::Visible)
	{
		if (IS_OBJECT_PTR_VALID(EdtValue) && EdtValue->GetText().IsEmpty() /*|| !EdtValue->GetText().IsNumeric()*/)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtValue"));
			ErrorMessage = TEXT("Value must be not empty and numeric");
			return false;
		}
	}
	else
	{
		if (IS_OBJECT_PTR_VALID(CBSEnumValues) && CBSEnumValues->GetSelectedOption().IsEmpty())
		{
			UE_LOG(LogTemp, Error, TEXT("CBSEnumValues"));
			ErrorMessage = TEXT("EnumValue must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && !ParamDetailData.Data.visibility_exp.Equals(OriginalData.Data.visibility_exp))
	{
		if (EdtVisiExpress->GetText().IsEmpty())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtVisiExpress"));
			ErrorMessage = TEXT("Visible Expression must be not empty");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtVisibility) && !ParamDetailData.Data.visibility.Equals(OriginalData.Data.visibility))
	{
		if (EdtVisibility->GetText().IsEmpty() || !EdtVisibility->GetText().IsNumeric()/* || (EdtVisibility->GetText().ToString() != ParamShowFlag && EdtVisibility->GetText().ToString() != ParamHiddenFlag)*/)
		{
			UE_LOG(LogTemp, Error, TEXT("EdtVisibility"));
			ErrorMessage = TEXT("Visibility must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtEditExpress) && !ParamDetailData.Data.editable_exp.Equals(OriginalData.Data.editable_exp))
	{
		if (EdtEditExpress->GetText().IsEmpty())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtEditExpress"));
			ErrorMessage = TEXT("Editable Expression must be not empty");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtEditable) && !ParamDetailData.Data.editable.Equals(OriginalData.Data.editable))
	{
		if (EdtEditable->GetText().IsEmpty() || !EdtEditable->GetText().IsNumeric())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtEditable"));
			ErrorMessage = TEXT("Editable must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtSpecialable) && !ParamDetailData.Data.Special.Equals(OriginalData.Data.Special))
	{
		if (EdtSpecialable->GetText().IsEmpty() || !EdtSpecialable->GetText().IsNumeric())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtSpecialable"));
			ErrorMessage = TEXT("Special Expression must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtSpecialExpress) && !ParamDetailData.Data.Special_exp.Equals(OriginalData.Data.Special_exp))
	{
		if (EdtSpecialExpress->GetText().IsEmpty() || !EdtSpecialExpress->GetText().IsNumeric())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtSpecialExpress"));
			ErrorMessage = TEXT("Special Expression must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtMustable) && !ParamDetailData.Data.Must.Equals(OriginalData.Data.Must))
	{
		if (EdtMustable->GetText().IsEmpty() || !EdtMustable->GetText().IsNumeric())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtMustable"));
			ErrorMessage = TEXT("EdtMustable Expression must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtMustExpress) && !ParamDetailData.Data.Must_exp.Equals(OriginalData.Data.Must_exp))
	{
		if (EdtMustExpress->GetText().IsEmpty() || !EdtMustExpress->GetText().IsNumeric())
		{
			UE_LOG(LogTemp, Error, TEXT("EdtMustExpress"));
			ErrorMessage = TEXT("EdtMustExpress Expression must be not empty and numeric");
			return false;
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtMax) && !ParamDetailData.Data.max_value.Equals(OriginalData.Data.max_value))
	{
		if (!EdtMax->GetText().IsEmpty())
		{
			if (!EdtMax->GetText().IsNumeric())
			{
				UE_LOG(LogTemp, Error, TEXT("EdtMax"));
				ErrorMessage = TEXT("MaxValue must be numeric");
				return false;
			}
		}
	}
	if (IS_OBJECT_PTR_VALID(EdtMin) && !ParamDetailData.Data.min_value.Equals(OriginalData.Data.min_value))
	{
		if (!EdtMin->GetText().IsEmpty())
		{
			if (!EdtMin->GetText().IsNumeric())
			{
				UE_LOG(LogTemp, Error, TEXT("EdtMin"));
				ErrorMessage = TEXT("MinValue must be numeric");
				return false;
			}
		}
	}
	if (ParamDetailData.EnumData.Num() > 0)
	{
		for (auto EnumDataiter : ParamDetailData.EnumData)
		{
			if (EnumDataiter.value.IsEmpty())
			{
				UE_LOG(LogTemp, Error, TEXT("EnumValue"));
				ErrorMessage = TEXT("EnumValue must be not empty and numeric");
				return false;
			}
		}
	}
	return true;
}

bool UParameterDetailWidget::CheckParamDataChange()
{
	if (ParamDetailData.EnumData.Num() != OriginalData.EnumData.Num())
	{
		return false;
	}

#define	PARAMETER_NEARLY_EQUAL(Name) ((ParamDetailData.Data.Name.IsNumeric() && OriginalData.Data.Name.IsNumeric()) ? FMath::IsNearlyEqual(FCString::Atof(*ParamDetailData.Data.Name), FCString::Atof(*OriginalData.Data.Name), 0.01f) : ParamDetailData.Data.Name.Equals(OriginalData.Data.Name,ESearchCase::CaseSensitive))
	
	bool Res1 = PARAMETER_NEARLY_EQUAL(name);//ParamDetailData.Data.name.Equals(OriginalData.Data.name);
	bool Res2 = PARAMETER_NEARLY_EQUAL(description);//ParamDetailData.Data.description.Equals(OriginalData.Data.description);
	bool Res3 = PARAMETER_NEARLY_EQUAL(value);
	bool Res4 = PARAMETER_NEARLY_EQUAL(max_value);
	bool Res5 = PARAMETER_NEARLY_EQUAL(min_value);
	bool Res6 = PARAMETER_NEARLY_EQUAL(expression);
	bool Res7 = PARAMETER_NEARLY_EQUAL(visibility);
	bool Res8 = PARAMETER_NEARLY_EQUAL(editable);
	bool Res9 = ParamDetailData.Data.is_enum == OriginalData.Data.is_enum;
	bool Res10 = ParamDetailData.Data.classific_id == OriginalData.Data.classific_id;
	bool Res11 = ParamDetailData.Data.param_id == OriginalData.Data.param_id;//PARAMETER_NEARLY_EQUAL(param_id);
	bool Res12 = PARAMETER_NEARLY_EQUAL(visibility_exp);
	bool Res13 = PARAMETER_NEARLY_EQUAL(editable_exp);
	bool Res14 = PARAMETER_NEARLY_EQUAL(max_expression);
	bool Res15 = PARAMETER_NEARLY_EQUAL(min_expression);
	bool Res16 = PARAMETER_NEARLY_EQUAL(Special);
	bool Res17 = PARAMETER_NEARLY_EQUAL(Special_exp);
	bool Res18 = PARAMETER_NEARLY_EQUAL(Must);
	bool Res19 = PARAMETER_NEARLY_EQUAL(Must_exp);
	

#undef PARAMETER_NEARLY_EQUA

	if (ParamDetailData.EnumData.Num() == 0)
	{
		return Res1 && Res2 && Res3 && Res4 && Res5 && Res6 && Res7 && Res8 && Res9 && Res10 && Res11 && Res12 && Res13 && Res14 && Res15 && Res16 && Res17 && Res18 && Res19;
	}
	else
	{
		bool ResEnum = true;
		for (int32 i = 0; i < ParamDetailData.EnumData.Num(); ++i)
		{
			ResEnum = ResEnum && (ParamDetailData.EnumData[i].CompareData(OriginalData.EnumData[i]));
		}
		return Res1 && Res2 && Res3 && Res4 && Res5 && Res6 && Res7 && Res8 && Res9 && Res10 && Res11 && Res12 && Res13 && Res14 && Res15 && Res16 && Res17 && Res18 && Res19 && ResEnum;
	}
}

void UParameterDetailWidget::FormatValue(const FString& InValue)
{
	if (ParamDetailData.Data.is_enum == 0)
	{
		ParamDetailData.Data.value = GetCorrectParamValue(InValue, ParamDetailData.Data.max_value, ParamDetailData.Data.min_value);
		ParamDetailData.Data.expression = GetCorrectParamValue(InValue, ParamDetailData.Data.max_value, ParamDetailData.Data.min_value);
		ParamDetailData.Data.DefaultExpress = ParamDetailData.Data.expression;
		if (IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(EdtValue))
		{
			ParamDetailData.Data.value = FString::Printf(TEXT("%.1f"), FCString::Atof(*ParamDetailData.Data.value));
			EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));
			EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
		}
	}
	else
	{
		FormatExpression(InValue, InValue);
	}
}

void UParameterDetailWidget::FormatMaxValue(const FString& InMaxValue)
{
	ParamDetailData.Data.max_value = InMaxValue;
	ParamDetailData.Data.max_expression = ParamDetailData.Data.max_value;
	if (IS_OBJECT_PTR_VALID(EdtMaxExpress) && IS_OBJECT_PTR_VALID(EdtMax))
	{
		//UParameterRelativeLibrary::CheckParameterValue(ParamDetailData);
		EdtMax->SetText(FText::FromString(ParamDetailData.Data.max_value));
		EdtMaxExpress->SetText(FText::FromString(ParamDetailData.Data.max_value));
	}
}

void UParameterDetailWidget::FormatMinValue(const FString& InMinValue)
{
	ParamDetailData.Data.min_value = InMinValue;
	ParamDetailData.Data.min_expression = ParamDetailData.Data.min_value;
	if (IS_OBJECT_PTR_VALID(EdtMinExpress) && IS_OBJECT_PTR_VALID(EdtMin))
	{
		EdtMin->SetText(FText::FromString(ParamDetailData.Data.min_value));
		EdtMinExpress->SetText(FText::FromString(ParamDetailData.Data.min_value));
	}
}

void UParameterDetailWidget::FormatVisibilityValue(const FString& InVisiValue)
{
	ParamDetailData.Data.visibility = InVisiValue;
	ParamDetailData.Data.visibility_exp = InVisiValue;
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisibility))
	{
		EdtVisibility->SetText(FText::FromString(InVisiValue));
		EdtVisiExpress->SetText(FText::FromString(InVisiValue));
	}
}

void UParameterDetailWidget::FormatSpecialValue(const FString& InVisiValue)
{
	ParamDetailData.Data.Special = InVisiValue;
	ParamDetailData.Data.Special_exp = InVisiValue;
	if (IS_OBJECT_PTR_VALID(EdtSpecialExpress) && IS_OBJECT_PTR_VALID(EdtSpecialable))
	{
		EdtSpecialable->SetText(FText::FromString(InVisiValue));
		EdtSpecialExpress->SetText(FText::FromString(InVisiValue));
	}
}

void UParameterDetailWidget::FormatMustValue(const FString& InVisiValue)
{
	ParamDetailData.Data.Must = InVisiValue;
	ParamDetailData.Data.Must_exp = InVisiValue;
	if (IS_OBJECT_PTR_VALID(EdtMustExpress) && IS_OBJECT_PTR_VALID(EdtMustable))
	{
		EdtMustable->SetText(FText::FromString(InVisiValue));
		EdtMustExpress->SetText(FText::FromString(InVisiValue));
	}
}

void UParameterDetailWidget::FormatEditableValue(const FString& InEditValue)
{
	ParamDetailData.Data.editable = InEditValue;
	ParamDetailData.Data.editable_exp = InEditValue;
	if (IS_OBJECT_PTR_VALID(EdtEditable) && IS_OBJECT_PTR_VALID(EdtEditExpress))
	{
		EdtEditable->SetText(FText::FromString(InEditValue));
		EdtEditExpress->SetText(FText::FromString(InEditValue));
	}
}

void UParameterDetailWidget::FormatExpression(const FString& InExpression, const FString& InValue)
{
	if (ParamDetailData.Data.is_enum <= 0)
	{
		ParamDetailData.Data.value = GetCorrectParamValue(InValue, ParamDetailData.Data.max_value, ParamDetailData.Data.min_value);
		ParamDetailData.Data.expression = InExpression;
		ParamDetailData.Data.DefaultExpress = InExpression;
		EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));
		EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
		return;
	}
	else
	{
		ParamDetailData.Data.value = InValue;
		ParamDetailData.Data.expression = InExpression;
        ParamDetailData.Data.DefaultExpress = InExpression;
	}

	if (IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(EdtValue))
	{
		if (ParamDetailData.Data.is_enum && ParamDetailData.EnumData.Num() > 0)
		{
			TArray<TPair<FString, FString>> EnumArray = TArray<TPair<FString, FString>>();
			for (auto& En : ParamDetailData.EnumData)
			{
				EnumArray.Add({ En.expression, En.value });
			}

			int32 EnumIndex = CBSEnumValues->GetSelectedIndex();

			if (EnumIndex >= 0)
			{
				ParamDetailData.Data.value = EnumArray[EnumIndex].Value;
				ParamDetailData.Data.expression = EnumArray[EnumIndex].Key;
				if (CBSEnumValues->GetOptionCount() > 0)
				{
					CBSEnumValues->SetSelectedIndex(EnumIndex);
				}
			}
			else
			{
				ParamDetailData.Data.value = EnumArray[0].Value;
				ParamDetailData.Data.expression = EnumArray[0].Key;
				if (CBSEnumValues->GetOptionCount() > 0)
				{
					CBSEnumValues->SetSelectedIndex(0);
				}
			}
		}
		EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));
		EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
	}
}

void UParameterDetailWidget::FormatExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(EdtValue))
	{
		//ParamDetailData.Data.value = FString::Printf(TEXT("%.1f"), FCString::Atof(*InValue));
		if (ParamDetailData.Data.is_enum && ParamDetailData.EnumData.Num() > 0)
		{
			TArray<TPair<FString,FString>> EnumArray = TArray<TPair<FString, FString>>();

			for (auto& En : ParamDetailData.EnumData)
			{
				EnumArray.Add({ En.expression, En.value });
			}

			bool IsEnumValueExist = false;
			int32 EnumIndex = -1;
			if (EnumArray.Num() > 0)
			{
				if (ParamDetailData.Data.value.IsNumeric())
				{
					for (int32 i = 0; i < EnumArray.Num(); i++)
					{
						if (EnumArray[i].Value.IsNumeric() &&  FMath::IsNearlyEqual(FCString::Atof(*EnumArray[i].Value), FCString::Atof(*ParamDetailData.Data.value)))
						{
							IsEnumValueExist = true;
							EnumIndex = i;
							break;
						}
					}
				}
				else
				{
					for (int32 i = 0; i < EnumArray.Num(); i++)
					{
						if (EnumArray[i].Value.Equals(ParamDetailData.Data.value))
						{
							IsEnumValueExist = true;
							EnumIndex = i;
							break;
						}
					}
				}

				if (CBSEnumValues->GetOptionCount() > 0)
				{
					if (IsEnumValueExist)
					{
						CBSEnumValues->SetSelectedIndex(EnumIndex);
					}
					else
					{
						CBSEnumValues->SetSelectedIndex(0);
					}
				}
			}
		}

		EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));
		EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
	}
}

void UParameterDetailWidget::FormatMaxExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.max_value = InValue;
	ParamDetailData.Data.max_expression = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtMaxExpress) && IS_OBJECT_PTR_VALID(EdtMax))
	{
		EdtMax->SetText(FText::FromString(ParamDetailData.Data.max_value));
		EdtMaxExpress->SetText(FText::FromString(ParamDetailData.Data.max_expression));
	}
}

void UParameterDetailWidget::FormatMinExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.min_value = InValue;
	ParamDetailData.Data.min_expression = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtMinExpress) && IS_OBJECT_PTR_VALID(EdtMin))
	{
		EdtMin->SetText(FText::FromString(ParamDetailData.Data.min_value));
		EdtMinExpress->SetText(FText::FromString(ParamDetailData.Data.min_expression));
	}
}

void UParameterDetailWidget::FormatVisibilityExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.visibility = InValue;
	ParamDetailData.Data.visibility_exp = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisibility))
	{
		EdtVisibility->SetText(FText::FromString(ParamDetailData.Data.visibility));
		EdtVisiExpress->SetText(FText::FromString(ParamDetailData.Data.visibility_exp));
	}
}

void UParameterDetailWidget::FormatSpecialExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.Special = InValue;
	ParamDetailData.Data.Special_exp = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtSpecialExpress) && IS_OBJECT_PTR_VALID(EdtSpecialable))
	{
		EdtSpecialable->SetText(FText::FromString(ParamDetailData.Data.Special));
		EdtSpecialExpress->SetText(FText::FromString(ParamDetailData.Data.Special_exp));
	}
}

void UParameterDetailWidget::FormatMustExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.Must = InValue;
	ParamDetailData.Data.Must_exp = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtMustExpress) && IS_OBJECT_PTR_VALID(EdtMustable))
	{
		EdtMustable->SetText(FText::FromString(ParamDetailData.Data.Must));
		EdtMustExpress->SetText(FText::FromString(ParamDetailData.Data.Must_exp));
	}
}

void UParameterDetailWidget::FormatEditableExpression(const FString& InExpression, const FString& InValue)
{
	ParamDetailData.Data.editable = InValue;
	ParamDetailData.Data.editable_exp = InExpression;
	if (IS_OBJECT_PTR_VALID(EdtEditExpress) && IS_OBJECT_PTR_VALID(EdtEditable))
	{
		EdtEditable->SetText(FText::FromString(ParamDetailData.Data.editable));
		EdtEditExpress->SetText(FText::FromString(ParamDetailData.Data.editable_exp));
	}
}

UParameterDetailWidget* UParameterDetailWidget::Create()
{
	UClass* ParamDetailBp = LoadClass<UUserWidget>(NULL, TEXT("WidgetBlueprint'/Game/UI/Parameters/ParameterDetailUI.ParameterDetailUI_C'"));
	checkf(ParamDetailBp, TEXT("load param detail bp error!"));
	UParameterDetailWidget* ParamDetailItem = CreateWidget<UParameterDetailWidget>(GWorld.GetReference(), ParamDetailBp);
	checkf(ParamDetailItem, TEXT("create param detail item error!"));
	return ParamDetailItem;
}

UParameterDetailWidget* UParameterDetailWidget::Get()
{
	static UParameterDetailWidget* DetailWidgetInstance = nullptr;
	if (!IS_OBJECT_PTR_VALID(DetailWidgetInstance))
	{
		DetailWidgetInstance = UParameterDetailWidget::Create();
		DetailWidgetInstance->AddToRoot();
	}
	return DetailWidgetInstance;
}

void UParameterDetailWidget::PreReadyParamType()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
	this->GenerateParamTree();
}

void UParameterDetailWidget::PreReadyParamTypeData()
{
#ifdef USE_REF_LOCAL_FILE
	DownloadFile(URefToParamDataLibrary::GetRelativePath());
#else
	this->GenerateParamTree();
#endif
}

void UParameterDetailWidget::OnParameterRelease()
{
	if (bool ConfirmRes = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(
		FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(),
		FText::FromStringTable(FName("PosSt"), TEXT("confirm to release param data?")).ToString()))
	{
		NetUUID.ReleaseUUID = UCatalogNetworkSubsystem::GetInstance()->SendReleaseStyleOrParameterRequest(2);
	}
}

void UParameterDetailWidget::RemoveSelectedParameter(UParameterAddItemData* InItemData)
{
	ListViewSelect->RemoveItem(InItemData);

	SelectedParamDetailDatas.RemoveAll([InItemData](const FParameterData& InData) {
		return InItemData->Code.ToString().Equals(InData.Data.name,ESearchCase::CaseSensitive);
		});
}

void UParameterDetailWidget::GridValueMarkChanged(const EParamGridMarkType& InMark)
{
	if (ParamDetailData.Data.grid_value_mark != InMark)
	{
		ParamDetailData.Data.grid_value_mark = InMark;

		auto OrgParam = ParamDetailData;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
		if (Res)
		{
			ParamDetailData = OrgParam;
			UpdateContent(ParamDetailData);
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());

			UpdateParamGridMark(ParamDetailData.Data.grid_value_mark);
		}
	}
}

void UParameterDetailWidget::BindDelegate()
{
#ifdef USE_REF_LOCAL_FILE

	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UParameterDetailWidget::OnDownloadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UParameterDetailWidget::OnUploadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->ReleaseStyleParamsResponseDelegate.AddUniqueDynamic(this, &UParameterDetailWidget::OnReleaseResponseHandler);


#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UParameterDetailWidget::OnDownloadFileResponseHandler);
		CatalogPC->UploadFileResponseDelegate.AddUniqueDynamic(this, &UParameterDetailWidget::OnUploadFileResponseHandler);
	}
#endif
}

void UParameterDetailWidget::UpdateParamData(const FParameterData& UpdateData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParamDataMap = ACatalogPlayerController::Get()->GetGlobalParameterMapRef();
	if (ParamDataMap.Contains(UpdateData.Data.name))
	{
		ParamDataMap[UpdateData.Data.name] = UpdateData;
	}
	else
	{//code [name] change
		FString OldKey = TEXT("");
		for (auto& Iter : ParamDataMap)
		{
			if (Iter.Value.Data.id.Equals(UpdateData.Data.id, ESearchCase::CaseSensitive))
			{
				OldKey = Iter.Key;
				break;
			}
		}
		if (!OldKey.IsEmpty())
		{
			ParamDataMap.Remove(OldKey);
			ParamDataMap.Add(UpdateData.Data.name, UpdateData);
		}
	}
	ShowSaveTip(true);
	UpdateParameterResponse(UpdateData);
	SaveParamDataFileAndUpload();

	/*UpdateParameterResponse(UpdateData);
	FLocalDatabaseParameterLibrary::UpdateGlobalParameters(UpdateData);
	ShowSaveTip(true);*/
}

void UParameterDetailWidget::UpdateParamData(const TArray<FParameterData>& UpdateData)
{
#ifdef USE_REF_LOCAL_FILE

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & ParamDataMap = ACatalogPlayerController::Get()->GetGlobalParameterMapRef();
	for (auto& ParamIter : UpdateData)
	{
		if (ParamDataMap.Contains(ParamIter.Data.name))
		{
			ParamDataMap[ParamIter.Data.name] = ParamIter;
		}
	}
	//SaveParamDataFileAndUpload();
	ShowSaveTip(true);
	UpdateBatchParameterResponse(UpdateData);

#else
	if (UpdateData.Num() <= 0) return;
	for (auto& ParamIter : UpdateData)
	{
		UpdateParameterResponse(ParamIter);
		FLocalDatabaseParameterLibrary::UpdateGlobalParameters(ParamIter);
	}
	ShowSaveTip(true);
	UpdateBatchParameterResponse(UpdateData);
#endif
}

void UParameterDetailWidget::SearchParametersResponse()
{
#ifdef USE_REF_LOCAL_FILE
	TArray<FParameterGroupTableData> ParametersGroup = ACatalogPlayerController::Get()->GetParamGroup();
#else
	TArray<FParameterGroupTableData> ParametersGroup;
	FLocalDatabaseParameterLibrary::RetriveParameterGroups(ParametersGroup);
#endif
	checkf(Scb_GlobalParams, TEXT("global params content is null"));
	Scb_GlobalParams->ClearChildren();
	//SelectParamWidget = nullptr;
	SelectWidget = nullptr;
	ParamTreeFolders.Empty();
	if (CurrentEditType == (int32)EParamDetailType::FolderOrFileParam)
	{
		return;
	}
	NumOfParamType = ParametersGroup.Num();
	ParamClassificMap.Empty();
	for (auto& ParamData : ParametersGroup)
	{
		UParamTreeFolderWidget* ParamFolderWidget = UParamTreeFolderWidget::Create();
		ParamFolderWidget->UpdateContent(ParamData);
		ParamFolderWidget->SetCanNotEditable(CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam || CurrentEditType == (int32)EParamDetailType::StyleParam);
		ParamFolderWidget->ParamFolderFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsSelectHandler")));
		ParamFolderWidget->ParamFolderSelectDelegate.BindUFunction(this, FName(TEXT("OnParamFolderSelectHandler")));
		ParamFolderWidget->ParamFolderNameChangeDelegate.BindUFunction(this, FName(TEXT("OnParamFolderNameChangeHandler")));
		ParamFolderWidget->ParamItemRightClickDelegate.BindUFunction(this, FName(TEXT("OnParamTreeItemRightClickHandler")));
		ParamFolderWidget->ParamFolderOpenContextMenuDelegate.BindUFunction(this, TEXT("HandleParamTreeFolderOpenContextMenu"));
		ParamFolderWidget->SetVisibility(ESlateVisibility::Visible);
		Scb_GlobalParams->AddChild(ParamFolderWidget);
		ParamTreeFolders.Add(ParamFolderWidget);
		ParamClassificMap.Add(ParamData.id, ParamData.group_name);
	}
}

void UParameterDetailWidget::CreateParameterResponse(const FParameterData& OutParam)
{
	Cast<UParamTreeFolderWidget>(SelectWidget)->AddSubParamFile(OutParam);
}

void UParameterDetailWidget::CreateParameterGroupTableResponse(const FParameterGroupTableData& OutParam)
{
	UParamTreeFolderWidget* ParamFolderWidget = UParamTreeFolderWidget::Create();
	ParamFolderWidget->UpdateContent(OutParam);
	ParamFolderWidget->ParamFolderFileSelectDelegate.BindUFunction(this, FName(TEXT("OnParamsSelectHandler")));
	ParamFolderWidget->ParamFolderSelectDelegate.BindUFunction(this, FName(TEXT("OnParamFolderSelectHandler")));
	ParamFolderWidget->ParamFolderNameChangeDelegate.BindUFunction(this, FName(TEXT("OnParamFolderNameChangeHandler")));
	ParamFolderWidget->ParamItemRightClickDelegate.BindUFunction(this, FName(TEXT("OnParamTreeItemRightClickHandler")));
	ParamFolderWidget->ParamFolderOpenContextMenuDelegate.BindUFunction(this, TEXT("HandleParamTreeFolderOpenContextMenu"));
	ParamFolderWidget->SetVisibility(ESlateVisibility::Visible);
	Scb_GlobalParams->AddChild(ParamFolderWidget);
	ParamTreeFolders.Add(ParamFolderWidget);
	ParamClassificMap.Add(OutParam.id, OutParam.group_name);

#ifdef USE_REF_LOCAL_FILE
	TArray<FParameterGroupTableData>& GroupRef = ACatalogPlayerController::Get()->GetParamGroupRef();
	int32 NewID = GroupRef.Num() > 0 ? GroupRef.Last().id + 1 : 1;
	FParameterGroupTableData NewGroup;
	NewGroup.id = NewID;
	NewGroup.group_name = OutParam.group_name;
	NewGroup.description = OutParam.description;
	GroupRef.Add(NewGroup);
	SaveParamDataFileAndUpload();
#else
	FLocalDatabaseParameterLibrary::InsertParameterGroup(OutParam.group_name, OutParam.description);
#endif
}

void UParameterDetailWidget::UpdateParameterResponse(const FParameterData& OutParam)
{
	ParemtersToSave.Empty();
	if (OriginalData.Data.classific_id == OutParam.Data.classific_id)
	{
		OriginalData = OutParam;
		Cast<UParamTreeFileWidget>(SelectWidget)->UpdateContent(OutParam);
	}
	else
	{
		Cast<UParamTreeFileWidget>(SelectWidget)->RemoveFromParentParamFolder();
		for (auto& FolderItem : ParamTreeFolders)
		{
			if (FolderItem->GetParamGroupId() == OutParam.Data.classific_id)
			{
				FolderItem->AddSubParamFile(OutParam, false);
				break;
			}
		}
	}
}

void UParameterDetailWidget::UpdateParameterGroupResponse(const FParameterGroupTableData& OutParam)
{
	for (auto& FolderItem : ParamTreeFolders)
	{
		if (FolderItem->GetParamGroupId() == OutParam.id)
		{
			FolderItem->UpdateContent(OutParam);
			break;
		}
	}
}

void UParameterDetailWidget::UpdateBatchParameterResponse(const TArray<FParameterData>& OutParam)
{
	if (EParamDetailType::AddGlobalParam == static_cast<EParamDetailType>(CurrentEditType))
	{
		for (auto FolderWidget : ParamTreeFolders) FolderWidget->TryUpdateSubParamFiles(OutParam);
	}
}

void UParameterDetailWidget::ForceSelectFolder(UParamTreeFolderWidget* SelectParamFolder)
{
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		SelectWidget->SetIsSelected(false);
	}
	SelectParamFolder->SetIsSelected(true);
	SelectParamFolder->SetExpandChild(true);
	SwitchParamBorderShow(false);
	SelectWidget = SelectParamFolder;
	//Scb_GlobalParams->SetScrollWhenFocusChanges(EScrollWhenFocusChanges::AnimatedScroll);
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC) SelectWidget->SetUserFocus(CatalogPC);
	Scb_GlobalParams->ScrollWidgetIntoView(SelectWidget, false, EDescendantScrollDestination::Center);
	PreSelectFolderID = FString::FromInt(SelectParamFolder->GetParamGroupId());
}

void UParameterDetailWidget::ClearSearchText()
{
	if (IS_OBJECT_PTR_VALID(EdtParamSearch))
	{
		EdtParamSearch->SetText(FText::FromString(TEXT("")));
	}
}

bool UParameterDetailWidget::CheckParamValueIsChange(const FString& OriginExp, const FString& OriginValue, FString& OutExp, FString& OutValue)
{
	if (UUIFunctionLibrary::CalculateExpressionValue(ParentParameters,LocalParameters, OriginExp, OutValue, OutExp))
	{
		if (OriginValue != OutValue)
		{
			return true;
		}
	}
	return false;
}

void UParameterDetailWidget::BindExpressionDelegate()
{
	if (UExpressionPopWidget::Get()->ExpressionDelegate.IsBound())
	{
		return;
	}
	UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("DetailParamExpressionEdit")));
}

void UParameterDetailWidget::SwitchParamsShow(bool IsSearch)
{
	if (IS_OBJECT_PTR_VALID(BorGlobalParams) && IS_OBJECT_PTR_VALID(BorSearchParams))
	{
		BorGlobalParams->SetVisibility(IsSearch ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		BorSearchParams->SetVisibility(IsSearch ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

void UParameterDetailWidget::OnSearchParamFileSelectHandler(UParamTreeFileWidget* ParamFile)
{
	SwitchParamsShow();
	const int32 ParamTypeID = ParamFile->GetParamData().Data.classific_id;
	const int32 FolderIndex = ParamTreeFolders.IndexOfByPredicate([ParamTypeID](UParamTreeFolderWidget* InOther) { return InOther->GetParamGroupId() == ParamTypeID; });
	UE_LOG(LogTemp, Log, TEXT("UParameterDetailWidget::OnSearchParamFileSelectHandler ParamTypeID=%d"), ParamTypeID);
	UE_LOG(LogTemp, Log, TEXT("UParameterDetailWidget::OnSearchParamFileSelectHandler FolderIndex=%d"), FolderIndex);
	if (INDEX_NONE == FolderIndex) return;
	if (!ParamTreeFolders[FolderIndex]->GetIsChecked())
	{
		ParamTreeFolders[FolderIndex]->SetSyncID(ParamFile->GetParamData().Data.id);
		ParamTreeFolders[FolderIndex]->SetExpandChild(true);
	}
	ParamTreeFolders[FolderIndex]->SyncSearchSelectParams(ParamFile->GetParamData().Data.name);
	if (IS_OBJECT_PTR_VALID(EdtParamSearch))
	{
		EdtParamSearch->SetText(FText::FromString(TEXT("")));
	}
}

void UParameterDetailWidget::UpdateParamEnumContent(const TArray<FEnumParameterTableData>& EnumParams)
{
	checkf(ScbEnumList, TEXT("ScrollBox enum list is null"));
	ScbEnumList->ClearChildren();
	ParamEnumItems.Empty();
	ScbEnumList->ScrollToStart();
	for (int32 i = 0; i < EnumParams.Num(); ++i)
	{
		UParameterEnumItemWidget* EnumItemWidget = UParameterEnumItemWidget::Create();
		EnumItemWidget->SetGlobalWidget(this);
		EnumItemWidget->UpdateContent(EnumParams[i], i + 1);
		EnumItemWidget->SetFolderOrFileParentParams(ParentParameters);
		EnumItemWidget->SetFolderOrFileLocalParams(LocalParameters);
		EnumItemWidget->ParamEnumEditDelegate.BindUFunction(this, FName(TEXT("EnumParamItemEdit")));
		EnumItemWidget->SetVisibility(ESlateVisibility::Visible);
		checkf(EnumItemWidget, TEXT("create enum param item error!"));
		ParamEnumItems.Add(EnumItemWidget);
		ParamEnumItems[i]->EnumSort = i;
		ScbEnumList->AddChild(EnumItemWidget);
	}
	if (ParamEnumItems.Num() > 0)
	{
		for (auto& iter : ParamEnumItems)
		{
			iter->SwitchState(BtnAdd->GetIsEnabled());
		}
	}
	//OnTextCommittedEdtValueExpress(FText::FromString(ParamDetailData.Data.expression), ETextCommit::Type::OnEnter);
}

void UParameterDetailWidget::EnumParamItemEdit(const EParamEnumItemType& EditType, UParameterEnumItemWidget* EnumItem)
{
	if (IS_OBJECT_PTR_VALID(EnumItem))
	{
		FString OldExpress = ParamDetailData.Data.expression;
		FString OldValue = ParamDetailData.Data.value;
		if (EditType == EParamEnumItemType::EnumValue 
			|| EditType == EParamEnumItemType::EnumExpress
			|| EditType == EParamEnumItemType::VisiExpress 
			|| EditType == EParamEnumItemType::VisiValue
			)
		{
			ParamDetailData.EnumData[EnumItem->EnumSort].CopyData(EnumItem->GetEnumItemData());
			RefreshCBSValueEnum(OldExpress, OldValue);
		}
		else if (EditType == EParamEnumItemType::Name)
		{
			bool NameExist = false;
			for (auto& iter : ParamDetailData.EnumData)
			{
				if (iter.name_for_display == EnumItem->GetEnumItemData().name_for_display && !iter.name_for_display.IsEmpty())
				{
					NameExist = true;
					break;
				}
			}
			if (!NameExist)
			{
				ParamDetailData.EnumData[EnumItem->EnumSort].CopyData(EnumItem->GetEnumItemData());
			}
			else
			{
				EnumItem->SetDisplayName(ParamDetailData.EnumData[EnumItem->EnumSort].name_for_display);
			}
			RefreshCBSValueEnum();
		}
		else if (EditType == EParamEnumItemType::Delete)
		{
			if (EnumItem->EnumSort < ParamDetailData.EnumData.Num())
			{
				ParamDetailData.EnumData.RemoveAt(EnumItem->EnumSort);
			}
			ParamDetailData.Data.is_enum = (ParamDetailData.EnumData.Num() == 0 ? 0 : 1);
			if (ParamDetailData.Data.is_enum > 0)
			{
				for (int32 i = 0; i < ParamDetailData.EnumData.Num(); ++i)
				{
					ParamDetailData.EnumData[i].priority = FString::FromInt(i);
				}
				UpdateParamEnumContent(ParamDetailData.EnumData);
				RefreshCBSValueEnum(OldExpress, OldValue);
			}
			else
			{
				UpdateParamEnumContent(ParamDetailData.EnumData);
				RefreshCBSValueEnum(OldExpress, OldValue);
				SwitchValueTypeShow(false);
				FormatExpression(ParamDetailData.Data.expression, ParamDetailData.Data.value);
			}
		}
		else if (EditType == EParamEnumItemType::EEnumImg)
		{
			if (EnumItem->GetEnumIndex() < ParamDetailData.EnumData.Num())
			{
				ParamDetailData.EnumData[EnumItem->GetEnumIndex()].image_for_display = EnumItem->GetEnumItemData().image_for_display;
			}
		}
		else if (EditType == EParamEnumItemType::EUp)
		{
			int32 Num = ParamDetailData.EnumData.Num();
			for (int32 i = 0; i < Num; i++)
			{
				if (i > 0 && ParamDetailData.EnumData[i].id == EnumItem->GetEnumItemData().id)
				{
					int32 ObjIndex = i - 1;
					FEnumParameterTableData TempData = ParamDetailData.EnumData[ObjIndex];
					ParamDetailData.EnumData[ObjIndex] = ParamDetailData.EnumData[i];
					ParamDetailData.EnumData[ObjIndex].priority = FString::FromInt(FCString::Atoi(*ParamDetailData.EnumData[ObjIndex].priority) - 1);
					ParamDetailData.EnumData[i] = TempData;
					ParamDetailData.EnumData[i].priority = FString::FromInt(FCString::Atoi(*ParamDetailData.EnumData[i].priority) + 1);
					break;
				}
			}
			UpdateParamEnumContent(ParamDetailData.EnumData);
			RefreshCBSValueEnum(OldExpress, OldValue);

		}
		else if (EditType == EParamEnumItemType::EDown)
		{
			int32 Num = ParamDetailData.EnumData.Num();
			for (int32 i = 0; i < Num; i++)
			{
				if (i < Num - 1 && ParamDetailData.EnumData[i].id == EnumItem->GetEnumItemData().id)
				{
					int32 ObjIndex = i + 1;
					FEnumParameterTableData TempData = ParamDetailData.EnumData[ObjIndex];
					ParamDetailData.EnumData[ObjIndex] = ParamDetailData.EnumData[i];
					ParamDetailData.EnumData[ObjIndex].priority = FString::FromInt(FCString::Atoi(*ParamDetailData.EnumData[ObjIndex].priority) + 1);
					ParamDetailData.EnumData[i] = TempData;
					ParamDetailData.EnumData[i].priority = FString::FromInt(FCString::Atoi(*ParamDetailData.EnumData[i].priority) - 1);
					break;
				}
			}
			UpdateParamEnumContent(ParamDetailData.EnumData);
			RefreshCBSValueEnum(OldExpress, OldValue);
		}
		else if (EditType == EParamEnumItemType::EForceSelect)
		{
			if (ParamDetailData.EnumData.IsValidIndex(EnumItem->EnumSort))
			{
				ParamDetailData.EnumData[EnumItem->EnumSort].force_select_condition = EnumItem->GetEnumItemData().force_select_condition;
			}
			UpdateParamEnumContent(ParamDetailData.EnumData);
			RefreshCBSValueEnum(OldExpress, OldValue);
			return;
		}


		//ParamDetailData.Data.expression = OldValue;
		EdtValueExpress->SetText(FText::FromString(OldExpress));
		//UpdateParamData(ParamDetailData);
		FString OutValue;
		FString OutExpression;
		//FString Expression = EdtValueExpress->GetText().ToString().IsEmpty() ? EdtValue->GetText().ToString() : EdtValueExpress->GetText().ToString();
		if (UUIFunctionLibrary::CalculateExpressionValue(ParentParameters,LocalParameters, EdtValueExpress->GetText().ToString(), OutValue, OutExpression))
		{
			FormatExpression(OutExpression, OutValue);
		}
	}
}

void UParameterDetailWidget::DetailParamExpressionEdit(const int32& EditType, const FString& Expression)
{
	UE_LOG(LogTemp, Log, TEXT("Detail Expression Edit---edit type : %d, Expression : %s"), EditType, *Expression);
	FString OutValue;
	FString OutExpress;
	bool Res = false;
	switch ((EExpressionEditType)EditType)
	{
	case EExpressionEditType::VisibilityExpression:
	{
		OnTextCommittedEdtVisiExpress(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	case EExpressionEditType::EditableExpression:
	{
		OnTextCommittedEdtEditExpress(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	case EExpressionEditType::ValueExpression:
	{
		OnTextCommittedEdtValueExpress(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	case EExpressionEditType::MaxExpression:
	{
		OnTextCommittedEdtMaxExpress(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	case EExpressionEditType::MinExpression:
	{
		OnTextCommittedEdtMinExpress(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	case EExpressionEditType::GridExpression:
	{
		OnTextCommittedEdtGridExpression(FText::FromString(Expression), ETextCommit::OnEnter);
		break;
	}
	default:
		break;
	}
}

void UParameterDetailWidget::UpdateCBSClassific(const int32& InClassific_id)
{
	if (!IS_OBJECT_PTR_VALID(CBSClassific)) return;
	CBSClassific->ClearOptions();
	for (TMap<int32, FString>::TConstIterator Iter = ParamClassificMap.CreateConstIterator(); Iter; ++Iter)
	{
		CBSClassific->AddOption(Iter->Value);
	}
	CBSClassific->RefreshOptions();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	const int32 ClassificId = (InClassific_id <= 0 && GlobalParameters.Contains(ParamDetailData.Data.name)) ? GlobalParameters[ParamDetailData.Data.name].Data.classific_id : InClassific_id;
	if (ParamClassificMap.Contains(ClassificId)) CBSClassific->SetSelectedOption(ParamClassificMap[ClassificId]);
}

void UParameterDetailWidget::GenerateParamTree()
{
	SearchParametersResponse();
}

void UParameterDetailWidget::RefreshEnumContent()
{
	ScbEnumList->ClearChildren();
	ParamEnumItems.Empty();
	TArray<FEnumParameterTableData> TempEnums = ParamDetailData.EnumData;
	for (int32 i = 0; i < TempEnums.Num(); ++i)
	{
		UParameterEnumItemWidget* EnumItemWidget = UParameterEnumItemWidget::Create();
		EnumItemWidget->UpdateContent(TempEnums[i], i + 1);
		EnumItemWidget->ParamEnumEditDelegate.BindUFunction(this, FName(TEXT("EnumParamItemEdit")));
		EnumItemWidget->SetVisibility(ESlateVisibility::Visible);
		checkf(EnumItemWidget, TEXT("create enum param item error!"));
		ParamEnumItems.Add(EnumItemWidget);
		ParamEnumItems[i]->EnumSort = i;
		ScbEnumList->AddChild(EnumItemWidget);
	}
	/*for (int32 i = 0; i < NewEnums.Num(); ++i)
	{
		UParameterEnumItemWidget* NewEnumItemWidget = UParameterEnumItemWidget::Create();
		NewEnumItemWidget->UpdateContent(NewEnums[i], TempEnums.Num() + i + 1);
		NewEnumItemWidget->ParamEnumEditDelegate.BindUFunction(this, FName(TEXT("EnumParamItemEdit")));
		NewEnumItemWidget->SetVisibility(ESlateVisibility::Visible);
		checkf(NewEnumItemWidget, TEXT("create enum param item error!"));
		ParamEnumItems.Add(NewEnumItemWidget);
		ScbEnumList->AddChild(NewEnumItemWidget);
	}*/
	if (ParamEnumItems.Num() > 0)
	{
		for (auto& iter : ParamEnumItems)
		{
			iter->SwitchState(BtnAdd->GetIsEnabled());
		}
	}
}

FString UParameterDetailWidget::GetCorrectParamValue(const FString& ParamValue, const FString& MaxValue, const FString& MinValue)
{
	FString OutValue = ParamValue;
	if (!MaxValue.IsEmpty() && !MinValue.IsEmpty())
	{
		if (FCString::Atof(*MaxValue) < FCString::Atof(*MinValue))
		{
			return ParamValue;
		}
	}
	if (!MaxValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) < FCString::Atof(*MaxValue))
			? ParamValue : MaxValue;
	}
	if (!MinValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) > FCString::Atof(*MinValue))
			? OutValue : MinValue;
	}
	return OutValue;
}

bool UParameterDetailWidget::IsMaxNMinValueVaild(const TArray<FEnumParameterTableData>& EnumValue, const FString& MaxValue, const FString& MinValue, TArray<float>& EnumArray)
{
	EnumArray.Empty();
	if (!MaxValue.IsEmpty() && !MinValue.IsEmpty())//两极值同时存在
	{
		float fMaxValue = FCString::Atof(*MaxValue);
		float fMinValue = FCString::Atof(*MinValue);
		if (FCString::Atof(*MaxValue) < FCString::Atof(*MinValue))
		{
			//for (int32 i = 0; i < EnumValue.Num(); i++)//无效时所有值可选
			//{
			//	EnumArray.Add(FCString::Atof(*EnumValue[i].value));
			//}
			return false;
		}
		else
		{
			for (int32 i = 0; i < EnumValue.Num(); i++)
			{
				float CurValue = FCString::Atof(*EnumValue[i].value);
				if (CurValue >= fMinValue
					&& CurValue <= fMaxValue)
				{
					EnumArray.Add(CurValue);
				}
			}
			return true;
			//if (EnumArray.Num() <= 0) return false;
		}
	}
	else if (!MaxValue.IsEmpty() && MinValue.IsEmpty())//仅最大值存在
	{
		float fMaxValue = FCString::Atof(*MaxValue);
		for (int32 i = 0; i < EnumValue.Num(); i++)
		{
			float CurValue = FCString::Atof(*EnumValue[i].value);
			if (CurValue <= fMaxValue)
			{
				EnumArray.Add(CurValue);
			}
		}
		return true;
		//if (EnumArray.Num() <= 0) return false;
	}
	else if (!MinValue.IsEmpty() && MaxValue.IsEmpty())//仅最小值存在
	{
		float fMinValue = FCString::Atof(*MinValue);
		for (int32 i = 0; i < EnumValue.Num(); i++)
		{
			float CurValue = FCString::Atof(*EnumValue[i].value);
			if (CurValue >= fMinValue)
			{
				EnumArray.Add(CurValue);
			}
		}
		return true;
		//if (EnumArray.Num() <= 0) return false;
	}
	else//无极值
	{
		for (int32 i = 0; i < EnumValue.Num(); i++)
		{
			EnumArray.Add(FCString::Atof(*EnumValue[i].value));
		}
		return false;
	}
}

void UParameterDetailWidget::OnParamsSelectHandler(UParamTreeFileWidget* SelectedParam)
{
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		SelectWidget->SetIsSelected(false);
	}
	SelectWidget = SelectedParam;
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		SelectWidget->SetIsSelected(true);
		SwitchParamBorderShow(true);
		auto SelectedParameterData = SelectedParam->GetParamData();
		if (ParentParameters.Contains(SelectedParameterData.Data.name))
			SelectedParameterData = ParentParameters[SelectedParameterData.Data.name];
		UpdateContent(SelectedParameterData);
		Scb_GlobalParams->ScrollWidgetIntoView(SelectedParam, false, EDescendantScrollDestination::Center);
		if (IsAddParDele)
		{
			IsAddParDele = false;
			for (auto iter : ParamTreeFolders)
			{
				if (iter->GetParamGroupId() == SelectedParam->GetParamClassificID())
				{
					iter->ConstructSubParams(SelectedParam->GetParamId());
					break;
				}
			}
		}
	}
}

void UParameterDetailWidget::OnParamFolderSelectHandler(UParamTreeFolderWidget* SelectParamFolder)
{
	if (IS_OBJECT_PTR_VALID(SelectWidget))
	{
		SelectWidget->SetIsSelected(false);
		for (auto iter : ParamTreeFolders)
		{
			if (iter->GetParamGroupId() == FCString::Atoi(*PreSelectFolderID))
			{
				iter->SetExpandChild(false);
				break;
			}
		}
	}
	if (IS_OBJECT_PTR_VALID(SelectParamFolder))
	{
		SelectParamFolder->SetIsSelected(true);
		SelectParamFolder->SetExpandChild(true);
		SwitchParamBorderShow(false);
		SelectWidget = SelectParamFolder;
		ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
		if (CatalogPC) SelectWidget->SetUserFocus(CatalogPC);
		Scb_GlobalParams->ScrollWidgetIntoView(SelectWidget, false, EDescendantScrollDestination::TopOrLeft);
		PreSelectFolderID = FString::FromInt(SelectParamFolder->GetParamGroupId());
		if (IsAddParDele)
			IsAddParDele = false;
	}
}

void UParameterDetailWidget::OnParamFolderNameChangeHandler(const int32& id, const FString& NewName)
{
	ParamClassificMap[id] = NewName;
	UpdateCBSClassific(ParamDetailData.Data.classific_id);
}

void UParameterDetailWidget::OnParamTreeItemRightClickHandler()
{
	for (auto iter : ParamTreeFolders)
	{
		if (iter->GetParamGroupId() == FCString::Atoi(*PreSelectFolderID))
		{
			iter->SetExpandChild(false);
			break;
		}
	}
	ClearSelectState();
}

void UParameterDetailWidget::UpdateSaveTipStyle(bool IsSucceed)
{
}

void UParameterDetailWidget::OnTimeActorHandler(float ShowRate)
{
	UE_LOG(LogTemp, Log, TEXT("save tip show rate : %f"), ShowRate);
	if (IS_OBJECT_PTR_VALID(CPTips))
	{
		CPTips->SetRenderOpacity(ShowRate);
		if (ShowRate <= 0.0f)
		{
			CPTips->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
}

void UParameterDetailWidget::HandleParamTreeFileOpenContextMenu(UParamTreeFileWidget* SelectedParam, const FPointerEvent& InMouseEvent)
{
	if (SelectedParam == nullptr || CurrentEditType != static_cast<int32>(EParamDetailType::AddGlobalParam))
	{
		return;
	}

	FUIAction DeleteAction = FUIAction(FExecuteAction::CreateLambda([=, this]()
	{
		FSlateApplication::Get().DismissAllMenus();

		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(NSLOCTEXT("ParameterDetailWidget", "ParameterDetailWidgetConfirmDeleteParamTitle", "Confirm Delete Parameter").ToString(), 
															NSLOCTEXT("ParameterDetailWidget", "ParameterDetailWidgetConfirmDeleteParamContent", "Do you want to delete this parameter?").ToString()))
		{
			if (Cast<UParamTreeFileWidget>(SelectWidget))
			{
				if (Cast<UParamTreeFileWidget>(SelectWidget)->GetIsNew())
				{
					OnClickedBtnCancel();
				}
				else
				{
					TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& GlobalParamsRef = ACatalogPlayerController::Get()->GetGlobalParameterMapRef();
					GlobalParamsRef.Remove(SelectedParam->GetParamData().Data.name);

					SaveParamDataFileAndUpload();

					ClearSelectState();

					ToDelFileWidget = Cast<UParamTreeFileWidget>(SelectWidget);

					for (auto iter : ParamTreeFolders)
					{
						if (iter->GetParamGroupId() == FCString::Atoi(*PreSelectFolderID))
						{
							OnParamFolderSelectHandler(iter);
							break;
						}
					}
				}
			}
		}
	}));

	FMenuBuilder MenuBuilder(true, nullptr, nullptr, false, &FCoreStyle::Get(), false, NAME_None, false);
	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("ParameterDetailWidget", "FileContextMenuParameterDelete", "Delete Parameter"),
		NSLOCTEXT("ParameterDetailWidget", "FileContextMenuParameterDeleteToolTip", "Delete this parameter."),
		FSlateIcon(),
		DeleteAction
	);
	
	TSharedRef<SWidget> MenuWidget = MenuBuilder.MakeWidget();
	
	const FVector2D& MouseScreenPos = InMouseEvent.GetScreenSpacePosition();

	FSlateApplication::Get().PushMenu(GetCachedWidget().ToSharedRef(), FWidgetPath(), MenuWidget, MouseScreenPos, FPopupTransitionEffect::ContextMenu);
}

void UParameterDetailWidget::HandleParamTreeFolderOpenContextMenu(UParamTreeFolderWidget* FolderItem, const FPointerEvent& InMouseEvent)
{
	if (FolderItem == nullptr || CurrentEditType != static_cast<int32>(EParamDetailType::AddGlobalParam))
	{
		return;
	}

	FUIAction DeleteAction = FUIAction(FExecuteAction::CreateLambda([=, this]()
	{
		FSlateApplication::Get().DismissAllMenus();

		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(NSLOCTEXT("ParameterDetailWidget", "ParameterDetailWidgetConfirmDeleteGroupTitle", "Confirm Delete Parameter Group").ToString(),
															NSLOCTEXT("ParameterDetailWidget", "ParameterDetailWidgetConfirmDeleteGroupContent", "Do you want to delete this parameter group?").ToString()))
		{
			TArray<FParameterGroupTableData>& GlobalGroup = ACatalogPlayerController::Get()->GetParamGroupRef();
			while (GlobalGroup.IsValidIndex(0))
			{
				int32 GroupToDelete = GlobalGroup.IndexOfByPredicate([&](const FParameterGroupTableData& InTableData) { return InTableData.id == FolderItem->GetParamGroupId(); });
				if (GroupToDelete == INDEX_NONE)
				{
					break;
				}
				else
				{
					GlobalGroup.RemoveAt(GroupToDelete);
				}
			}

			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& GlobalParamsRef = ACatalogPlayerController::Get()->GetGlobalParameterMapRef();
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ParamsToDelete = GlobalParamsRef.FilterByPredicate([&](const TPair<FString, FParameterData>& InPair) { return InPair.Value.Data.classific_id == FolderItem->GetParamGroupId(); });
			for (const TPair<FString, FParameterData>& Pair : ParamsToDelete)
			{
				GlobalParamsRef.Remove(Pair.Key);
			}

			SaveParamDataFileAndUpload();

			ClearSelectState();

			ParamTreeFolders.Remove(FolderItem);
			FolderItem->RemoveFromParent();
		}
	}));

	FMenuBuilder MenuBuilder(true, nullptr, nullptr, false, &FCoreStyle::Get(), false, NAME_None, false);
	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("ParameterDetailWidget", "FileContextMenuDeleteGroup", "Delete Group"),
		NSLOCTEXT("ParameterDetailWidget", "FileContextMenuDeleteGroupToolTip", "Delete this parameter group."),
		FSlateIcon(),
		DeleteAction
	);

	TSharedRef<SWidget> MenuWidget = MenuBuilder.MakeWidget();

	const FVector2D& MouseScreenPos = InMouseEvent.GetScreenSpacePosition();

	FSlateApplication::Get().PushMenu(GetCachedWidget().ToSharedRef(), FWidgetPath(), MenuWidget, MouseScreenPos, FPopupTransitionEffect::ContextMenu);
}

void UParameterDetailWidget::DownloadFile(const FString& RelativePath)
{
	NetUUID.DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(RelativePath);
}

void UParameterDetailWidget::SaveParamDataFileAndUpload()
{
	FRefParamData RefParamData;
	RefParamData.Init(ACatalogPlayerController::Get()->GetGlobalParameterMap());
	RefParamData.Init(ACatalogPlayerController::Get()->GetParamGroup());

	const FString RefFilePathIdentify = URefToParamDataLibrary::GetRefParamsAddress();
	UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, RefParamData);
	UploadFile(URefToParamDataLibrary::GetRelativePath());
}

void UParameterDetailWidget::UploadFile(const FString& RelativePath)
{
	NetUUID.UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(RelativePath);
}

void UParameterDetailWidget::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
#ifdef USE_REF_LOCAL_FILE

	if (NetUUID.DownloadUUID.Equals(UUID))
	{
		if (OutRes)
		{
			GenerateParamTree(); 
		}
		else
		{
			//UI_POP_WINDOW_ERROR(TEXT("download params error"));
			UE_LOG(LogTemp, Warning, TEXT("download params error"));
		}
	}

#else

	if (DownloadOverUUID.Equals(UUID))
	{
		DownloadOverUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(FPaths::Combine(FPaths::ProjectContentDir(), TEXT("Cache/server_cache.db")));

		FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("delete from parameter_group"));
		TArray<FParameterGroupTableData> ParameterGroup = TArray<FParameterGroupTableData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from parameter_group order by id ASC"), ParameterGroup))
		{
			FString InsertParameterGroupSQL = FString::Printf(TEXT("insert into parameter_group(id, group_name, description) values "));
			for (auto ParameterGroupiter : ParameterGroup)
			{
				InsertParameterGroupSQL = InsertParameterGroupSQL + FString::Printf(TEXT("(%d,'%s','%s'),"), ParameterGroupiter.id, *ParameterGroupiter.group_name, *ParameterGroupiter.description);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertParameterGroupSQL.Mid(0, InsertParameterGroupSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.3f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("delete from global_param"));
		TArray<FParameterTableData> ParameterTable = TArray<FParameterTableData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(TEXT("select * from global_param"), ParameterTable))
		{
			FString InsertParameterSQL = FString::Printf(TEXT("insert into global_param(id, name ,description,classific_id,value,expression,max_value,max_expression,min_value,min_expression,visibility,visibility_exp,editable,editable_exp,is_enum,param_id) values "));
			for (auto ParameterTableiter : ParameterTable)
			{
				InsertParameterSQL = InsertParameterSQL + FString::Printf(TEXT("('%s','%s','%s',%d ,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d ,'%s'),"), *ParameterTableiter.id, *ParameterTableiter.name, *ParameterTableiter.description, ParameterTableiter.classific_id, *ParameterTableiter.value, *ParameterTableiter.expression, *ParameterTableiter.max_value, *ParameterTableiter.max_expression, *ParameterTableiter.min_value, *ParameterTableiter.min_expression, *ParameterTableiter.visibility, *ParameterTableiter.visibility_exp, *ParameterTableiter.editable, *ParameterTableiter.editable_exp, ParameterTableiter.is_enum, *ParameterTableiter.param_id);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertParameterSQL.Mid(0, InsertParameterSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.6f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(TEXT("delete from global_param_enum"));
		TArray<FEnumParameterTableData> EnumParameterTable = TArray<FEnumParameterTableData>();
		TArray<FString> FilesToDownload;
		if (FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(FString::Printf(TEXT("select * from global_param_enum")), EnumParameterTable))
		{
			FString InsertEnumParameterSQL = FString::Printf(TEXT("insert into global_param_enum(id, value, expression, name_for_display, image_for_display, visibility, visibility_exp, priority, main_id) values "));
			for (auto EnumParameterTableiter : EnumParameterTable)
			{
				InsertEnumParameterSQL = InsertEnumParameterSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumParameterTableiter.id, *EnumParameterTableiter.value, *EnumParameterTableiter.expression, *EnumParameterTableiter.name_for_display, *EnumParameterTableiter.image_for_display, *EnumParameterTableiter.visibility, *EnumParameterTableiter.visibility_exp, *EnumParameterTableiter.priority, *EnumParameterTableiter.main_id);
				if (!EnumParameterTableiter.image_for_display.IsEmpty())
				{
					FDownloadFileData ThumbnailInfo;
					FDownloadFileDataLibrary::RetriveServerFileMD5(TEXT("param_image"), EnumParameterTableiter.image_for_display, ThumbnailInfo);
					FDownloadFileDataLibrary::UpdateFileMD5(TEXT("param_image"), ThumbnailInfo);

					FilesToDownload.AddUnique(EnumParameterTableiter.image_for_display);
				}
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumParameterSQL.Mid(0, InsertEnumParameterSQL.Len() - 1));
		}
		LocalDBSubsystem->CloseServerDatabase();
		MergeProcessWidget->SetPercent(0.8f);
		UE_LOG(LogTemp, Error, TEXT("Parameter Download And Replace Succuessful"));
		if (FilesToDownload.Num() > 0)
		{
#ifdef USE_REF_LOCAL_FILE
			DownloadFileUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadMultiFilesRequest(FilesToDownload);
#else
			DownloadFileUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(FilesToDownload);
#endif
		}
		else
		{
#ifdef USE_REF_LOCAL_FILE
#else
			MergeProcessWidget->SetPercent(0.9f);
			FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Global Parameter")).ToString();
			FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();
			MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
			UploadSuccess(true);
#endif
			SearchParametersResponse();
		}
	}
	else if (DownloadUUID.Equals(UUID))
	{
		DownloadUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		ULocalDatabaseSubsystem* LocalDBSubsystem = ACatalogPlayerController::Get()->GetGameInstance()->GetSubsystem<ULocalDatabaseSubsystem>();
		LocalDBSubsystem->OpenServerDatabase(FPaths::Combine(FPaths::ProjectContentDir(), TEXT("Cache/server_cache.db")));

		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("delete from parameter_group"));
		TArray<FParameterGroupTableData> ParameterGroup = TArray<FParameterGroupTableData>();
		if (FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from parameter_group order by id ASC"), ParameterGroup))
		{
			FString InsertParameterGroupSQL = FString::Printf(TEXT("insert into parameter_group(id, group_name, description) values "));
			for (auto ParameterGroupiter : ParameterGroup)
			{
				InsertParameterGroupSQL = InsertParameterGroupSQL + FString::Printf(TEXT("(%d,'%s','%s'),"), ParameterGroupiter.id, *ParameterGroupiter.group_name, *ParameterGroupiter.description);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParameterGroupSQL.Mid(0, InsertParameterGroupSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.3f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("delete from global_param"));
		TArray<FParameterTableData> ParameterTable = TArray<FParameterTableData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from global_param"), ParameterTable);
		if (ParameterTable.Num() > 0)
		{
			FString InsertParameterSQL = FString::Printf(TEXT("insert into global_param(id, name ,description,classific_id,value,expression,max_value,max_expression,min_value,min_expression,visibility,visibility_exp,editable,editable_exp,is_enum,param_id) values "));
			for (auto ParameterTableiter : ParameterTable)
			{
				InsertParameterSQL = InsertParameterSQL + FString::Printf(TEXT("('%s','%s','%s',%d ,'%s','%s','%s','%s','%s','%s','%s','%s','%s','%s',%d ,'%s'),"), *ParameterTableiter.id, *ParameterTableiter.name, *ParameterTableiter.description, ParameterTableiter.classific_id, *ParameterTableiter.value, *ParameterTableiter.expression, *ParameterTableiter.max_value, *ParameterTableiter.max_expression, *ParameterTableiter.min_value, *ParameterTableiter.min_expression, *ParameterTableiter.visibility, *ParameterTableiter.visibility_exp, *ParameterTableiter.editable, *ParameterTableiter.editable_exp, ParameterTableiter.is_enum, *ParameterTableiter.param_id);
			}
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertParameterSQL.Mid(0, InsertParameterSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.6f);
		FLocalDatabaseOperatorLibrary::DeleteDataFromServerDataBaseBySQL(TEXT("delete from global_param_enum"));
		TArray<FEnumParameterTableData> EnumParameterTable = TArray<FEnumParameterTableData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from global_param_enum")), EnumParameterTable);
		FilesToUpload.Empty();
		if (EnumParameterTable.Num() > 0)
		{
			FString InsertEnumParameterSQL = FString::Printf(TEXT("insert into global_param_enum(id, value, expression, name_for_display, image_for_display, visibility, visibility_exp, priority, main_id) values "));
			for (auto EnumParameterTableiter : EnumParameterTable)
			{
				InsertEnumParameterSQL = InsertEnumParameterSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *EnumParameterTableiter.id, *EnumParameterTableiter.value, *EnumParameterTableiter.expression, *EnumParameterTableiter.name_for_display, *EnumParameterTableiter.image_for_display, *EnumParameterTableiter.visibility, *EnumParameterTableiter.visibility_exp, *EnumParameterTableiter.priority, *EnumParameterTableiter.main_id);
				if (!EnumParameterTableiter.image_for_display.IsEmpty())
				{
					FDownloadFileData ThumbnailInfo;
					FDownloadFileDataLibrary::RetriveFileMD5(TEXT("param_image"), EnumParameterTableiter.image_for_display, ThumbnailInfo);
					FDownloadFileDataLibrary::UpdateServerFileMD5(TEXT("param_image"), ThumbnailInfo);
					if (FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumParameterTableiter.image_for_display)))
					{
						FilesToUpload.AddUnique(EnumParameterTableiter.image_for_display);
					}
				}
			}
			const int32 num = FilesToUpload.Num();

			//UE_LOG(LogTemp, Error, TEXT("FilesToUpload   %d"), num);
			FLocalDatabaseOperatorLibrary::InsertDataFromServerDataBaseBySQL(InsertEnumParameterSQL.Left(InsertEnumParameterSQL.Len() - 1));
		}
		MergeProcessWidget->SetPercent(0.8f);
		LocalDBSubsystem->CloseServerDatabase();
#ifdef USE_REF_LOCAL_FILE
		UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FString("Cache/server_cache.db"));
#else
		UploadUUID = ACatalogPlayerController::Get()->UploadFileRequest(FString("Cache/server_cache.db"));
#endif
	}
	else if (DownloadFileUUID.Equals(UUID))
	{
		MergeProcessWidget->SetPercent(0.9f);
		UploadSuccess(OutRes);
		SearchParametersResponse();
		if (OutRes)
		{
#ifdef USE_REF_LOCAL_FILE
#else
			FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Global Parameter")).ToString();
			FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Download")).ToString();
			MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
#endif
		}
	}

#endif
}

void UParameterDetailWidget::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (UploadUUID.Equals(OutFilePath))
	{
		UploadUUID.Empty();
		if (false == OutRes)
		{
			UploadSuccess(false);
			return;
		}
		const int32 num = FilesToUpload.Num();

		//UE_LOG(LogTemp, Error, TEXT("upload   %d"), num);

		if (FilesToUpload.Num() > 0)
		{
#ifdef USE_REF_LOCAL_FILE
			UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FilesToUpload.Top());
#else
			UploadUUID = ACatalogPlayerController::Get()->UploadFileRequest(FilesToUpload.Top());
#endif
			//UE_LOG(LogTemp, Error, TEXT("FilesToUpload   %s"), *FilesToUpload.Top());
			FilesToUpload.Pop();
		}
		else
		{
#ifdef USE_REF_LOCAL_FILE
#else
			FString msg = OutRes ? TEXT("Parameter Upload Success") : TEXT("Parameter Upload Fail");
			UE_LOG(LogTemp, Error, TEXT("%s"), *msg);
			FString Msg = FText::FromStringTable(FName("PosSt"), TEXT("Global Parameter")).ToString();
			FString UserName = ACatalogPlayerController::Get()->GetGlobalDataRef().GetLoginUserInfoConstRef().UserName;
			FString Operation = FText::FromStringTable(FName("PosSt"), TEXT("Upload")).ToString();
			MergeLogUUID = ACatalogPlayerController::Get()->MergeInsertRequest(TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), TEXT(""), Msg, TEXT(""), TEXT(""), UserName, Operation);
			UploadSuccess(true);
#endif
		}
	}
	else if (NetUUID.UploadUUID.Equals(OutFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("Upload Parameter file [%d]"), OutRes);
		NetUUID.ResetUploadAction();
		if (OutRes)
		{
			//UE_LOG(LogTemp, Error, TEXT("Parameter Save Success"));
		}
	}
}

void UParameterDetailWidget::OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg)
{
	if (UUID.Equals(NetUUID.ReleaseUUID))
	{
		NetUUID.ResetReleaseAction();
		if (bSuccess)
		{
			UI_POP_WINDOW_TIP(TEXT("发布成功！"));
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UParameterDetailWidget::SwitchParamBorderShow(bool IsDetailBorder)
{
	if (IS_OBJECT_PTR_VALID(BorParamAdd) && IS_OBJECT_PTR_VALID(BorParamDetail))
	{
		BorParamAdd->SetVisibility(IsDetailBorder ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		BorParamDetail->SetVisibility(IsDetailBorder ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

void UParameterDetailWidget::SwitchWidgetLayout(const int32& ParamType)
{
	if (IS_OBJECT_PTR_VALID(BorGlobalTree) /*&& IS_OBJECT_PTR_VALID(TxtTitle)*/)
	{
		BorGlobalTree->SetVisibility((ParamType == (int32)EParamDetailType::FolderOrFileParam || ParamType == (int32)EParamDetailType::MultiComponentParam) ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		//TxtTitle->SetText(FText::FromString(ParamType == (int32)EParamDetailType::AddGlobalParam ? GlobalParamTitle : FolderOrFileParamTitle));
	}
	if (IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtID))
	{
		EdtName->SetIsEnabled(true);
		EdtID->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
	}
	if (IS_OBJECT_PTR_VALID(BtnParamAdd))
	{
		BtnParamAdd->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
	}
	if (IS_OBJECT_PTR_VALID(BtnParamUpLoad))
	{
		BtnParamUpLoad->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
	}
	if (IS_OBJECT_PTR_VALID(BtnParamDownLoad))
	{
		BtnParamDownLoad->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
	}

	SwitchParamBorderShow(ParamType == (int32)EParamDetailType::FolderOrFileParam || ParamType == (int32)EParamDetailType::MultiComponentParam);
	UpdateReleaseBtnShow(
		ParamType == static_cast<int32>(EParamDetailType::AddGlobalParam) &&
		ACatalogPlayerController::Get()->IsAdminLogin()
	);
	if (BorGlobalTree->GetVisibility() == ESlateVisibility::Visible)
	{
		GenerateParamTree();
	}
}

void UParameterDetailWidget::SwitchValueTypeShow(bool IsEnum)
{
	if (IS_OBJECT_PTR_VALID(BorNoEnum) && IS_OBJECT_PTR_VALID(BorEnum)/* && IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(BtnValueExpress)*/)
	{
		BorEnum->SetVisibility(IsEnum ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BorNoEnum->SetVisibility(IsEnum ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
		//BtnEditExpress->SetIsEnable(!IsEnum);
		//EdtValueExpress->SetIsReadOnly(IsEnum);
	}
}

void UParameterDetailWidget::SwitchDetailWidgetState(const int32& ParamType)
{
	if (IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtID) && IS_OBJECT_PTR_VALID(CBSClassific) && IS_OBJECT_PTR_VALID(EdtDM))
	{
		EdtName->SetIsEnabled(true);
		EdtID->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
		CBSClassific->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
		EdtDM->SetIsEnabled(ParamType == (int32)EParamDetailType::AddGlobalParam);
	}
	if (IS_OBJECT_PTR_VALID(EdtValueExpress) && IS_OBJECT_PTR_VALID(BtnValueExpress)
		&& IS_OBJECT_PTR_VALID(EdtValue) && IS_OBJECT_PTR_VALID(CBSEnumValues))
	{
		EdtValueExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnValueExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtValue->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		CBSEnumValues->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtMaxExpress) && IS_OBJECT_PTR_VALID(BtnMaxExpress) && IS_OBJECT_PTR_VALID(EdtMax))
	{
		EdtMaxExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnMaxExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtMax->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtMinExpress) && IS_OBJECT_PTR_VALID(BtnMinExpress) && IS_OBJECT_PTR_VALID(EdtMin))
	{
		EdtMinExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnMinExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtMin->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(BtnVisiExpress) && IS_OBJECT_PTR_VALID(EdtVisibility))
	{
		EdtVisiExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnVisiExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtVisibility->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtEditExpress) && IS_OBJECT_PTR_VALID(BtnEditExpress) && IS_OBJECT_PTR_VALID(EdtEditable))
	{
		EdtEditExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnEditExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtEditable->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtSpecialExpress) && IS_OBJECT_PTR_VALID(BtnSpecialExpress) && IS_OBJECT_PTR_VALID(EdtSpecialable))
	{
		EdtSpecialExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnSpecialExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtSpecialable->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(EdtMustExpress) && IS_OBJECT_PTR_VALID(BtnMustExpress) && IS_OBJECT_PTR_VALID(EdtMustable))
	{
		EdtMustExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		BtnMustExpress->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
		EdtMustable->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(BtnAdd) && IS_OBJECT_PTR_VALID(BorParamAddBack))
	{
		BorParamAddBack->SetVisibility((ParamType == (int32)EParamDetailType::AddGlobalParam) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnAdd->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(BtnParamUpLoad) && IS_OBJECT_PTR_VALID(BorParamUpLoadBack))
	{
		BorParamUpLoadBack->SetVisibility((ParamType == (int32)EParamDetailType::AddGlobalParam) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnParamUpLoad->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(BtnParamDownLoad) && IS_OBJECT_PTR_VALID(BorParamDownLoadBack))
	{
		BorParamDownLoadBack->SetVisibility((ParamType == (int32)EParamDetailType::AddGlobalParam) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnParamDownLoad->SetIsEnabled(!(ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam));
	}
	if (IS_OBJECT_PTR_VALID(BoxSelectList) && IS_OBJECT_PTR_VALID(SelectSwitcher))
	{
		BoxSelectList->SetVisibility((ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		SelectSwitcher->SetActiveWidgetIndex( (ParamType == (int32)EParamDetailType::AddFolderOrFileParam || ParamType == (int32)EParamDetailType::StyleParam) ? 1 : 0);
	}
}

void UParameterDetailWidget::SyncValueState()
{
	if (ParamDetailData.EnumData.Num() > 0 /*|| NewEnums.Num() > 0*/)
	{
		SwitchValueTypeShow(true);
		RefreshCBSValueEnum();
	}
	else
	{
		SwitchValueTypeShow(false);
	}
}

void UParameterDetailWidget::RefreshCBSValueEnum(FString& NewExpress, FString& NewValue)
{
	CBSEnumValues->OnSelectionChanged.Clear();
	auto Selection = CBSEnumValues->GetSelectedOption();
	CBSEnumValues->ClearOptions();
	NewExpress.Empty();
	NewValue.Empty();
	if (ParamDetailData.Data.is_enum && ParamDetailData.EnumData.Num() > 0)
	{
		int32 Index = 0;

		for (auto& iter : ParamDetailData.EnumData)
		{
			const FString EnumDisplayName = iter.name_for_display.IsEmpty() ? iter.value : iter.name_for_display;
			CBSEnumValues->AddOption(EnumDisplayName);
			if (NewValue.IsNumeric() && ParamDetailData.Data.value.IsNumeric() && FMath::IsNearlyEqual(FCString::Atof(*ParamDetailData.Data.value), FCString::Atof(*iter.value), 0.001f)
				|| ParamDetailData.Data.value.Equals(iter.value))
			{
				NewValue = iter.value;
				NewExpress = iter.expression;
			}
		}
		if (NewValue.IsEmpty())
		{
			NewValue = CBSEnumValues->GetOptionAtIndex(0);
			NewExpress = ParamDetailData.EnumData[0].expression;
		}
		CBSEnumValues->SetSelectedOption(NewValue);
	}
	else
	{
		NewExpress = TEXT("0");
		NewValue = TEXT("0");
	}
	CBSEnumValues->OnSelectionChanged.AddUniqueDynamic(this, &UParameterDetailWidget::OnSelectionChangedCBSValue);
}

void UParameterDetailWidget::RefreshCBSValueEnum()
{
	CBSEnumValues->OnSelectionChanged.Clear();
	CBSEnumValues->ClearOptions();

	if (ParamDetailData.Data.is_enum && ParamDetailData.EnumData.Num() > 0)
	{
		int32 Index = 0;
		FString SelectedEumItem;
		FString SelectExpress;
		for (auto& iter : ParamDetailData.EnumData)
		{
			const FString EnumDisplayName = iter.name_for_display.IsEmpty() ? iter.value : iter.name_for_display;
			CBSEnumValues->AddOption(EnumDisplayName);
			if (SelectedEumItem.IsEmpty() && ParamDetailData.Data.value.IsNumeric() && iter.value.IsNumeric() && FMath::IsNearlyEqual(FCString::Atof(*ParamDetailData.Data.value), FCString::Atof(*iter.value), 0.001f))
			{
				SelectedEumItem = EnumDisplayName;
				//SelectExpress = iter.expression;
			}
			else if (SelectedEumItem.IsEmpty() && ParamDetailData.Data.value.Equals(iter.value))
			{
				SelectedEumItem = EnumDisplayName;
			}
		}
		if (!SelectedEumItem.IsEmpty())
		{
			CBSEnumValues->SetSelectedOption(SelectedEumItem);
			//EdtValueExpress->SetText(FText::FromString(SelectExpress));
		}
		else
		{
			CBSEnumValues->SetSelectedOption(TEXT(""));
			//EdtValueExpress->SetText(FText::FromString(ParamDetailData.EnumData[0].expression));
		}
	}
	CBSEnumValues->OnSelectionChanged.AddUniqueDynamic(this, &UParameterDetailWidget::OnSelectionChangedCBSValue);
}

bool UParameterDetailWidget::IsOptionExist(const FString& InSelection, const TArray<FEnumParameterTableData>& EnumData, const TArray<FEnumParameterTableData>& NewEnumData)
{
	for (auto& Data : EnumData)
	{

		if (InSelection == Data.name_for_display || InSelection == Data.value)
		{
			return true;
		}
	}
	/*for (auto& Data : NewEnumData)
	{
		if (InSelection == Data.value)
		{
			return true;
		}
		continue;
	}*/
	return false;
}

FReply UParameterDetailWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (/*InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton || */InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		/*if (IS_OBJECT_PTR_VALID(SelectParamWidget))
		{
			SelectParamWidget->SetIsSelected(false);
			SelectParamWidget = nullptr;
			SwitchParamBorderShow(false);
			return FReply::Handled();
		}*/
	}
	return FReply::Unhandled();
}

//void UParameterDetailWidget::OnClickedBtnClose()
//{
//	this->SetVisibility(ESlateVisibility::Collapsed);
//}

void UParameterDetailWidget::OnTextChangedEdtParamSearch(const FText& Text)
{
	if (Text.IsEmpty())
	{
		SwitchParamsShow(false);
		return;
	}
	TArray<FParameterData> Parameters = ACatalogPlayerController::Get()->SearchNameToParam(Text.ToString());
	//FLocalDatabaseParameterLibrary::SearchNameGlobalParameters(*Text.ToString(), Parameters);

	SwitchParamsShow(true);
	checkf(ScbSearchParams, TEXT("search content widget is null"));
	ScbSearchParams->ClearChildren();
	SearchParams.Empty();
	for (auto& ParamData : Parameters)
	{
		UParamTreeFileWidget* ParamWidget = UParamTreeFileWidget::Create();
		ParamWidget->UpdateContent(ParamData);
		ParamWidget->ParamFileSelectDelegate.BindUFunction(this, FName(TEXT("OnSearchParamFileSelectHandler")));
		ParamWidget->SetVisibility(ESlateVisibility::Visible);
		ScbSearchParams->AddChild(ParamWidget);
		SearchParams.Add(ParamWidget);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtParamSearch(const FText& Text, ETextCommit::Type CommitMethod)
{
}

void UParameterDetailWidget::OnClickedBtnParamSearch()
{
	if (IS_OBJECT_PTR_VALID(EdtParamSearch))
	{
		OnTextChangedEdtParamSearch(EdtParamSearch->GetText());
	}
}

void UParameterDetailWidget::OnTextChangedEdtName(const FText& Text)
{
	//if (Text.ToString() == ParamDetailData.Data.description)
	//{
	//	IsNameUnique = true;
	//	return;
	//}
	bool ValidDescription = FParameterTableData::IsValidDescription(Text.ToString());
	if (!Text.IsEmpty() && ValidDescription)
	{
		ParamDetailData.Data.description = Text.ToString();
	}
}

void UParameterDetailWidget::OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (/*!Text.IsEmpty() && */CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString temp = Text.ToString().TrimStartAndEnd();
		bool ValidDescription = FParameterTableData::IsValidDescription(temp);
		if (ValidDescription)
		{
			ParamDetailData.Data.description = temp;
			EdtName->SetText(FText::FromString(ParamDetailData.Data.description));
		}
		else
		{
			EdtName->SetText(FText::FromString(OriginalData.Data.description));
		}

		//UpdateParamDBFile(ParamDetailData.Data);
	}
	/*else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		if (EdtName)
		{
			EdtName->SetText(FText::FromString(ParamDetailData.Data.description));
		}
	}*/
}

void UParameterDetailWidget::OnTextChangedEdtDM(const FText& Text)
{
	/*if (Text.ToString().Equals(ParamDetailData.Data.name))
	{
		IsDMUnique = true;
		return;
	}
	if (!Text.IsEmpty())
	{
	}*/
}

void UParameterDetailWidget::OnTextCommittedEdtDM(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (/*!Text.IsEmpty() && */CommitMethod != ETextCommit::Type::OnCleared /*&& Text.ToString().Len()<=20*/)
	{
		/*if (Text.ToString().Equals(ParamDetailData.Data.name))
		{
			IsDMUnique = true;
			return;
		}
		else if (!Text.IsEmpty())
		{*/
		ParamDetailData.Data.name = Text.ToString();
		//}
	}
	/*else if (CommitMethod != ETextCommit::Type::OnCleared && IS_OBJECT_PTR_VALID(EdtDM))
	{
		EdtDM->SetText(FText::FromString(ParamDetailData.Data.name));
	}*/
}

void UParameterDetailWidget::OnSelectionChangedCBSClassific(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		ParamDetailData.Data.classific_id = *ParamClassificMap.FindKey(SelectedItem);
	}

}

void UParameterDetailWidget::OnTextChangedEdtID(const FText& Text)
{
	if (Text.ToString() == ParamDetailData.Data.param_id)
	{
		IsIdUnique = true;
		return;
	}
	if (!Text.IsEmpty())
	{
		ParamDetailData.Data.param_id = Text.ToString();
	}
}

void UParameterDetailWidget::OnTextCommittedEdtID(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (/*!Text.IsEmpty() && */CommitMethod != ETextCommit::Type::OnCleared /*&& Text.IsNumeric()*/)
	{
		ParamDetailData.Data.param_id = Text.ToString();
		/*FString CleanData  = Text.ToString();
		FRegexPattern Pattern(TEXT("[.]"));
		FRegexMatcher RegMatcher(Pattern, CleanData);
		RegMatcher.SetLimits(0, CleanData.Len());
		if (!RegMatcher.FindNext())
		{
			ParamDetailData.Data.param_id = Text.ToString();
		}*/
	}
	/*else if (CommitMethod != ETextCommit::Type::OnCleared && IS_OBJECT_PTR_VALID(EdtID))
	{
		EdtID->SetText(FText::FromString(ParamDetailData.Data.param_id));
	}*/
}

void UParameterDetailWidget::OnTextCommittedEdtValueExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	FString InExpression = Text.ToString();

	if (InExpression.IsEmpty())
	{
		GetWorld()->GetTimerManager().SetTimerForNextTick([&]() { if (EdtValueExpress != nullptr) EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression)); });
		return;
	}

	FString OutValue;
	FString OutExpression;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.expression = InExpression;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		FormatExpression();

		if (ParamDetailData.Data.no_match_enum_data)
		{
			UpdateContent(ParamDetailData);
		}
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());

		if (IS_OBJECT_PTR_VALID(EdtValueExpress))
		{
			EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
		}
	}
}

void UParameterDetailWidget::OnClickedBtnValueExpress()
{
	if (EdtValueExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::ValueExpression, EdtValueExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamValue = Text.ToString();
		if (ParamValue.IsEmpty())
		{
			GetWorld()->GetTimerManager().SetTimerForNextTick([&]() { if (EdtValue != nullptr) EdtValue->SetText(FText::FromString(ParamDetailData.Data.value)); });
			return;
		}


		UUIFunctionLibrary::FormatInputValue(ParamValue);
		FormatValue(ParamValue);
	}
}

void UParameterDetailWidget::OnSelectionChangedCBSValue(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		if (ParamDetailData.Data.is_enum && ParamDetailData.EnumData.Num() > 0)
		{
			TArray<TPair<FString, FString>> EnumArray = TArray<TPair<FString, FString>>();
			for (auto& En : ParamDetailData.EnumData)
			{
				EnumArray.Add({ En.expression, En.value });
			}

			int32 EnumIndex = CBSEnumValues->GetSelectedIndex();

			if (EnumIndex >= 0)
			{
				ParamDetailData.Data.value = EnumArray[EnumIndex].Value;
				ParamDetailData.Data.expression = EnumArray[EnumIndex].Key;
			}
			else
			{
				ParamDetailData.Data.value = EnumArray[0].Value;
				ParamDetailData.Data.expression = EnumArray[0].Key;
			}
		}
		EdtValue->SetText(FText::FromString(ParamDetailData.Data.value));
		EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMaxExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.max_expression = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtMaxExpress->SetText(FText::FromString(ParamDetailData.Data.max_expression));
		EdtMax->SetText(FText::FromString(ParamDetailData.Data.max_value));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtMaxExpress->SetText(FText::FromString(ParamDetailData.Data.max_expression));
	}

}

void UParameterDetailWidget::OnClickedBtnValueMaxExpress()
{
	if (EdtMaxExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::MaxExpression, EdtMaxExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMaxValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamMaxValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamMaxValue);
		FormatMaxValue(ParamMaxValue);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMinExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;
	TArray<TPair<int32, FString>> Comments;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.min_expression = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtMinExpress->SetText(FText::FromString(ParamDetailData.Data.min_expression));
		EdtMin->SetText(FText::FromString(ParamDetailData.Data.min_value));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtMinExpress->SetText(FText::FromString(ParamDetailData.Data.min_expression));
	}
}

void UParameterDetailWidget::OnClickedBtnValueMinExpress()
{
	if (EdtMinExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::MinExpression, EdtMinExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMinValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamMinValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamMinValue);
		FormatMinValue(ParamMinValue);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.visibility_exp = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtVisiExpress->SetText(FText::FromString(ParamDetailData.Data.visibility_exp));
		EdtVisibility->SetText(FText::FromString(ParamDetailData.Data.visibility));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtVisiExpress->SetText(FText::FromString(ParamDetailData.Data.visibility_exp));
	}
}

void UParameterDetailWidget::OnClickedBtnVisiExpress()
{
	if (EdtVisiExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::VisibilityExpression, EdtVisiExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtVisibility(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamVisiValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamVisiValue);
		FormatVisibilityValue(ParamVisiValue);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtEditExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.editable_exp = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtEditExpress->SetText(FText::FromString(ParamDetailData.Data.editable_exp));
		EdtEditable->SetText(FText::FromString(ParamDetailData.Data.editable));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtEditExpress->SetText(FText::FromString(ParamDetailData.Data.editable_exp));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtSpecialExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.Special_exp = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();

	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtSpecialExpress->SetText(FText::FromString(ParamDetailData.Data.Special_exp));
		EdtSpecialable->SetText(FText::FromString(ParamDetailData.Data.Special));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtSpecialExpress->SetText(FText::FromString(ParamDetailData.Data.Special_exp));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMustExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	auto OrgParam = ParamDetailData;
	OrgParam.Data.Must_exp = Text.ToString();
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		EdtMustExpress->SetText(FText::FromString(ParamDetailData.Data.Must_exp));
		EdtMustable->SetText(FText::FromString(ParamDetailData.Data.Must));
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtMustExpress->SetText(FText::FromString(ParamDetailData.Data.Must_exp));
	}
}

void UParameterDetailWidget::OnClickedBtnEditExpress()
{
	if (EdtEditExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::EditableExpression, EdtEditExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnClickedBtnSpecialExpress()
{
	if (EdtSpecialExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::SpecialExpression, EdtSpecialExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnClickedBtnMustExpress()
{
	if (EdtMustExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::MustExpression, EdtMustExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtEditable(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamEditValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamEditValue);
		FormatEditableValue(ParamEditValue);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtSpecialable(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamSpecialValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamSpecialValue);
		FormatSpecialValue(ParamSpecialValue);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtMustable(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString ParamMustValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(ParamMustValue);
		FormatMustValue(ParamMustValue);
	}
}

void UParameterDetailWidget::OnClickedBtnAdd()
{
	FEnumParameterTableData EnumParamData;
	EnumParamData.id = FGuid::NewGuid().ToString().ToLower();;
	EnumParamData.priority = FString::FromInt(ParamDetailData.FindMaxID() + 1);
	EnumParamData.main_id = ParamDetailData.Data.id;

	OldValue = ParamDetailData.Data.expression;
	ParamDetailData.Data.is_enum = 1;
	ParamDetailData.EnumData.Add(EnumParamData);

	UParameterEnumItemWidget* EnumParamItem = UParameterEnumItemWidget::Create();
	EnumParamItem->UpdateContent(EnumParamData, ParamEnumItems.Num() + 1);
	EnumParamItem->ParamEnumEditDelegate.BindUFunction(this, FName(TEXT("EnumParamItemEdit")));
	EnumParamItem->SetVisibility(ESlateVisibility::Visible);
	EnumParamItem->SetFolderOrFileLocalParams(LocalParameters);
	EnumParamItem->SetFolderOrFileParentParams(ParentParameters);
	ParamEnumItems.Add(EnumParamItem);
	int32 i = 0;
	for (auto& iter : ParamEnumItems)
	{
		iter->EnumSort = i++;
	}
	if (ScbEnumList)
	{
		ScbEnumList->AddChild(EnumParamItem);
	}

	if (OldValue.IsEmpty())
		ParamDetailData.Data.expression = ParamDetailData.Data.value = ParamDetailData.EnumData[0].value;
	SyncValueState();
	ParamDetailData.Data.expression = OldValue;
	EdtValueExpress->SetText(FText::FromString(ParamDetailData.Data.expression));
}

void UParameterDetailWidget::OnClickedBtnSave()
{
	bool IsNull = false;
	if (CurrentEditType == (int32)EParamDetailType::AddGlobalParam)
	{//添加或修改全局变量
		if (IS_OBJECT_PTR_VALID(SelectWidget))
		{
			if (Cast<UParamTreeFileWidget>(SelectWidget)->GetIsNew())
			{//添加全局变量，新添加的变量不会被其他变量引用，因此可以直接添加
				FString ErrorMessage;
				if (CheckParamDataValid(ErrorMessage))
				{
					Cast<UParamTreeFileWidget>(SelectWidget)->SetIsNew(false);
					//FLocalDatabaseParameterLibrary::InsertGlobalParameters(ParamDetailData);
					ACatalogPlayerController::Get()->InsertParam(ParamDetailData);
					SaveParamDataFileAndUpload();
					if (ParamDetailData.Data.classific_id != GetPreSelectFolderID())
					{
						for (auto iter : ParamTreeFolders)
						{
							if (iter->GetParamGroupId() == GetPreSelectFolderID())
							{
								iter->SetExpandChild(false);
								break;
							}
						}
						for (auto iter : ParamTreeFolders)
						{
							if (iter->GetParamGroupId() == ParamDetailData.Data.classific_id)
							{
								iter->ConstructSubParams(ParamDetailData.Data.id);
								break;
							}
						}
					}
					Cast<UParamTreeFileWidget>(SelectWidget)->UpdateContent(ParamDetailData);
					OriginalData.CopyData(ParamDetailData);
					ShowSaveTip(true);
				}
				else
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
						FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), 
						FText::FromStringTable(FName("PosSt"), ErrorMessage).ToString()
					);
					//UpdateContent(ParamDetailData);
					return;
				}
			}
			else
			{//修改全局变量
				//UE_LOG(LogTemp, Error, TEXT("修改全局变量 Step 1 CheckParamDataChange() %d"), CheckParamDataChange());
				if (CheckParamDataChange()) return;
				FString ErrorMessage;
				if (CheckParamDataValid(ErrorMessage))
				{//需要检查全局变量中直接或间接引用到此变量的变量，同时修改那些变量的值。
					TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
					//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
					if (GlobalParameters.Contains(ParamDetailData.Data.name))
						GlobalParameters[ParamDetailData.Data.name] = ParamDetailData;
					else
						GlobalParameters.Add(ParamDetailData.Data.name, ParamDetailData);
					ParemtersToSave.Empty();
					TArray<FString> AffectedParameters;
					FParameterEffectionParser Parser;
					bool bIsValid = Parser.FindParametersAffectBySpecificParameterWithAllEXP(GlobalParameters, ParamDetailData.Data.name, AffectedParameters, false);
					if (!bIsValid)
					{
						UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
							FText::FromStringTable(FName("PosSt"), TEXT("Parameter has circle refrence!")).ToString());
					}
					if (AffectedParameters.Num() > 0)
					{//存在受此变量影响的变量直接更新变量
						while (AffectedParameters.Num() > 0)
						{
							const FString ParameterName = AffectedParameters.Top();
							AffectedParameters.Pop();
							if (false == GlobalParameters.Contains(ParameterName)) continue;
							bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, {}, {}, GlobalParameters[ParameterName]);
							if (!Res)
							{
								UE_LOG(LogTemp, Error, TEXT("UParameterDetailWidget::OnClickedBtnSave parameter[%s] affect [%s] calculate failed"), *ParamDetailData.Data.name, *ParameterName);
								continue;
							}
							//GlobalParameters[ParameterName].Data.value = GetCorrectParamValue(GlobalParameters[ParameterName].Data.value, GlobalParameters[ParameterName].Data.max_value, GlobalParameters[ParameterName].Data.min_value);
							ParemtersToSave.Add(GlobalParameters[ParameterName]);
							//FLocalDatabaseParameterLibrary::UpdateGlobalParameters(GlobalParameters[ParameterName]);
							ACatalogPlayerController::Get()->UpdateParamData(GlobalParameters[ParameterName]);
						}
					}
					UpdateParamData(ParamDetailData);
					if (ParamDetailData.Data.classific_id != GetPreSelectFolderID())
					{
						for (auto iter : ParamTreeFolders)
						{
							if (iter->GetParamGroupId() == GetPreSelectFolderID())
							{
								iter->SetExpandChild(false);
								break;
							}
						}
						for (auto iter : ParamTreeFolders)
						{
							if (iter->GetParamGroupId() == ParamDetailData.Data.classific_id)
							{
								iter->SetExpandChild(true);
								iter->ConstructSubParams(ParamDetailData.Data.id);
								break;
							}
						}
						SetPreSelectFolderID(FString::FromInt(OriginalData.Data.classific_id));
					}
					else
					{
						for (auto iter : ParamTreeFolders)
						{
							if (iter->GetParamGroupId() == ParamDetailData.Data.classific_id)
							{
								iter->SetExpandChild(true);
								iter->ConstructSubParams(ParamDetailData.Data.id);
								break;
							}
						}
					}
					OriginalData.CopyData(ParamDetailData);
				}
				else
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), ErrorMessage).ToString());
					//UpdateContent(ParamDetailData);
					return;
				}
			}
			return;
		}
	}
	else
	{
		if (ParamDetailData.EnumData.Num() > 0)
		{
			for (auto EnumDataiter : ParamDetailData.EnumData)
			{
				if (EnumDataiter.value.IsEmpty())
				{
					IsNull = true;
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), FTextKey("EnumValue must be not empty and numeric")).ToString());
					break;
				}
			}
		}
		if (!IsNull)
		{
			ParamDetailActionDelegate.ExecuteIfBound((int32)EParamDetailActionType::Save);
			UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
		}
	}

	if (/*CurrentEditType == (int32)EParamDetailType::AddGlobalParam
		|| */CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{
		if (!IsNull)
			ParamUpdateDataDelegate.ExecuteIfBound(ParamDetailData);
			
	}
	else if (CurrentEditType == (int32)EParamDetailType::FolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::MultiComponentParam)
	{
		//UpdateParamDBFile(ParamDetailData.Data);
		if (!IsNull)
			ParamUpdateDataDelegate.ExecuteIfBound(ParamDetailData);	
	}
	/*else if (CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam)
	{
		ParamUpdateDataDelegate.ExecuteIfBound(ParamDetailData);
	}*/
}

void UParameterDetailWidget::OnClickedBtnSelect()
{
	if (/*CurrentEditType == (int32)EParamDetailType::FolderOrFileParam*/
		 CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
	/*	|| CurrentEditType == (int32)EParamDetailType::MultiComponentParam*/
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{

		if (ParamDetailData.EnumData.Num() > 0)
		{
			for (auto EnumDataiter : ParamDetailData.EnumData)
			{
				if (EnumDataiter.value.IsEmpty())
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), FTextKey("EnumValue must be not empty and numeric")).ToString());
					return;
				}
			}
		}

		bool bSelected = false;
		for (auto& ParamIte : SelectedParamDetailDatas)
		{
			if (ParamDetailData.Data.name.Equals(ParamIte.Data.name,ESearchCase::CaseSensitive))
			{
				bSelected = true;
				break;
			}
		}

		if (!bSelected)
		{
			FParameterData NewParam;
			NewParam.CopyData(ParamDetailData);
			SelectedParamDetailDatas.Add(NewParam);

			UParameterAddItemData* NewItem = NewObject<UParameterAddItemData>();
			NewItem->Code = FText::FromString(NewParam.Data.name); 
			NewItem->Name = FText::FromString(NewParam.Data.description);
			NewItem->ParameterDetail = this;
			ListViewSelect->AddItem(NewItem);
		}
	}
}

void UParameterDetailWidget::OnClickedBtnAllCancel()
{
	if (CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{
		UpdateContent(OriginalData);
		ParamDetailActionDelegate.ExecuteIfBound((int32)EParamDetailActionType::Cancel);
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UParameterDetailWidget::OnClickedBtnAllSave()
{
	if (CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{
		ParamDetailActionDelegate.ExecuteIfBound((int32)EParamDetailActionType::Save);
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);

		if (SelectedParamDetailDatas.Num() == 0)
		{
			SelectedParamDetailDatas.Add(ParamDetailData);
		}

		SelectedParamUpdateDatasDelegate.ExecuteIfBound(SelectedParamDetailDatas);
	}
}

void UParameterDetailWidget::OnClickedBtnParamAdd()
{
	if (!IS_OBJECT_PTR_VALID(SelectWidget))
	{
		FParameterGroupTableData NewParam;
		if (ParamTreeFolders.IsEmpty())
		{
			NewParam.id = 1;
		}
		else
		{
			NewParam.id = ParamTreeFolders.Last()->GetParamGroupId() + 1;
		}
		NewParam.group_name = DefaultTypeName + FString::FromInt(NumOfParamType + 1);
		++NumOfParamType;
		NewParam.description = TEXT("");
		CreateParameterGroupTableResponse(NewParam);
	}
	else
	{
		FParameterData NewParam;
		NewParam.Data.id = FGuid::NewGuid().ToString().ToLower();
		NewParam.Data.value = TEXT("");
		NewParam.Data.visibility = TEXT("0");
		NewParam.Data.editable = TEXT("1");
		NewParam.Data.visibility_exp = TEXT("0");
		NewParam.Data.editable_exp = TEXT("1");
		NewParam.Data.Special = TEXT("1");
		NewParam.Data.Special_exp = TEXT("1");
		NewParam.Data.Must = TEXT("1");
		NewParam.Data.Must_exp = TEXT("1");
		NewParam.Data.description = TEXT("");
		NewParam.Data.classific_id = GetPreSelectFolderID();
		CreateParameterResponse(NewParam);
	}
}

void UParameterDetailWidget::OnClickedBtnParamUpLoad()
{
#ifdef USE_REF_LOCAL_FILE
#else
	TArray<FString> Path;
	Path.Add(FString("Cache/server_cache.db"));
	DownloadUUID = ACatalogPlayerController::Get()->DownloadMultiFilesRequest(Path);
	UE_LOG(LogTemp, Error, TEXT("Upload Start, Downloading Database"));
	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(true);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.1f);
	}
#endif
}

void UParameterDetailWidget::OnClickedBtnParamDownLoad()
{
#ifdef USE_REF_LOCAL_FILE
	//此方法暂时为强制刷新参数功能使用

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParams = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	for (auto& GP : GlobalParams)
	{
		for (auto& GPN : GP.Value.EnumData)
		{
			if (GPN.expression.IsEmpty() && !GPN.value.IsEmpty())
			{
				GPN.expression = GPN.value;
			}
		}
	}

	FRefParamData RefParamData;
	RefParamData.Init(GlobalParams);
	RefParamData.Init(ACatalogPlayerController::Get()->GetParamGroup());

	const FString RefFilePathIdentify = URefToParamDataLibrary::GetRefParamsAddress();
	UProtobufOperatorFunctionLibrary::SaveRelationToFile(RefFilePathIdentify, RefParamData);
	UploadFile(URefToParamDataLibrary::GetRelativePath());

	ACatalogPlayerController::Get()->ClearGlobalParamData();

#else
	DownloadOverUUID = ACatalogPlayerController::Get()->DownloadFileRequest(TEXT("Cache/server_cache.db"));
	UE_LOG(LogTemp, Error, TEXT("Download Database Start"));

	if (!MergeProcessWidget)
	{
		MergeProcessWidget = UMergeProcessWidget::Create();
	}
	if (MergeProcessWidget)
	{
		MergeProcessWidget->UploadOrDownload(false);
		MergeProcessWidget->SetSuccess(true);
		MergeProcessWidget->AddToViewport(10);
		MergeProcessWidget->SetPercent(0.1f);
	}
#endif
}

void UParameterDetailWidget::OnClickedBtnTipClose()
{
}

void UParameterDetailWidget::OnStateChangedChkNoMatchEnumData(bool bIsChecked)
{
	auto OrgParam = ParamDetailData;
	OrgParam.Data.no_match_enum_data = bIsChecked;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
	if (Res)
	{
		ParamDetailData = OrgParam;
		UpdateContent(ParamDetailData);
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
														FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		
		ChkNoMatchEnum->SetIsChecked(ParamDetailData.Data.no_match_enum_data);
	}
}

void UParameterDetailWidget::OnTextCommittedEdtGridExpression(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		auto OrgParam = ParamDetailData;
		OrgParam.Data.grid_expression = Text.ToString();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
		if (Res)
		{
			ParamDetailData = OrgParam;
			UpdateContent(ParamDetailData);
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());

			Edt_GridExpress->SetText(FText::FromString(ParamDetailData.Data.grid_expression));
		}
	}
}

void UParameterDetailWidget::OnClickedBtnGridExpression()
{
	if (Edt_GridExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EExpressionEditType::GridExpression, Edt_GridExpress->GetText().ToString(), FName(TEXT("DetailParamExpressionEdit")));
	}
}

void UParameterDetailWidget::OnTextCommittedEdtGridValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		auto OrgParam = ParamDetailData;
		OrgParam.Data.grid_value = Text.ToString();
		OrgParam.Data.grid_expression = Text.ToString();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, OrgParam);
		if (Res)
		{
			ParamDetailData = OrgParam;
			UpdateContent(ParamDetailData);
		}
		else
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
				FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());

			Edt_GridValue->SetText(FText::FromString(ParamDetailData.Data.grid_value));
		}
	}
}

void UParameterDetailWidget::RemoveProcess()
{
	if (MergeProcessWidget)
	{
		MergeProcessWidget->RemoveFromParent();
	}
}

void UParameterDetailWidget::UploadSuccess(bool IsTrue)
{
	if (IsTrue)
	{
		MergeProcessWidget->SetPercent(1.f);
	}
	else
	{
		MergeProcessWidget->SetSuccess(false);
	}
	FLatentActionInfo LatentActionInfo;
	LatentActionInfo.CallbackTarget = this;
	LatentActionInfo.ExecutionFunction = TEXT("RemoveProcess");
	LatentActionInfo.Linkage = 1;
	LatentActionInfo.UUID = 100;
	UKismetSystemLibrary::Delay(this, 3.f, LatentActionInfo);
}

void UParameterDetailWidget::OnClickedBtnCancel()
{
	FParameterData ParamData;
	if (CurrentEditType == (int32)EParamDetailType::AddGlobalParam)
	{
		if (Cast<UParamTreeFileWidget>(SelectWidget))
		{
			if (Cast<UParamTreeFileWidget>(SelectWidget)->GetIsNew())
			{
				ToDelFileWidget = Cast<UParamTreeFileWidget>(SelectWidget);

				for (auto iter : ParamTreeFolders)
				{
					if (iter->GetParamGroupId() == FCString::Atoi(*PreSelectFolderID))
					{
						OnParamFolderSelectHandler(iter);
						break;
					}
				}
			}
			else
			{
				UpdateContent(OriginalData);
			}
		}
	}
	else if (CurrentEditType == (int32)EParamDetailType::AddFolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::FolderOrFileParam
		|| CurrentEditType == (int32)EParamDetailType::MultiComponentParam
		|| CurrentEditType == (int32)EParamDetailType::StyleParam)
	{
		UpdateContent(OriginalData);
		ParamDetailActionDelegate.ExecuteIfBound((int32)EParamDetailActionType::Cancel);
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("UParameterDetailWidget::OnClickedBtnCancel() NOT Handled"));
	}
	//else
	//{
	//SwitchParamBorderShow(true);
	//ParamDetailActionDelegate.ExecuteIfBound((int32)EParamDetailActionType::Cancel);
	//}
	//UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif

#undef LOCTEXT_NAMESPACE
