// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ParameterEnumItemWidget.h"
#include "ParamTreeFolderWidget.h"
#include "TimeActor.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "ParameterDetailWidget.generated.h"

/**
 *
 */

class UEditableText;
class UButton;
class UScrollBox;
class UBorder;
class UCheckBox;
class UComboBoxString;
class UTextBlock;
class UCanvasPanel;
class UImage;
class UMergeProcessWidget;
class UListView;

UENUM(BlueprintType)
enum class EParamDetailType : uint8
{
	AddGlobalParam = 0,
	FolderOrFileParam,
	AddFolderOrFileParam,
	MultiComponentParam,
	StyleParam
};

UENUM(BlueprintType)
enum class EFormatEditType : uint8
{
	Name = 0,
	Value,
	Expression,
	Visibility,
	Editable
};

UENUM(BlueprintType)
enum class EExpressionEditType : uint8
{
	VisibilityExpression = 0,
	EditableExpression,
	ValueExpression,
	MaxExpression,
	MinExpression,
	SpecialExpression,
	MustExpression,
	GridExpression
};

UENUM(BlueprintType)
enum class ESaveType : uint8
{
	Normal = 0,
	MultiComponent
};

UENUM(BlueprintType)
enum class EMultiComponentParamType : uint8
{
	New = 0,
	Value,
	MaxValue,
	MinValue
};

UENUM(BlueprintType)
enum class EParamDetailActionType : uint8
{
	Save = 0,
	Cancel
};


DECLARE_DYNAMIC_DELEGATE_FourParams(FParamDetailDelegate, bool, IsNew, FParameterData&, DetailData, const TArray<FEnumParameterTableData>&, DeleteEnums, TArray<FEnumParameterTableData>&, InsertEnums);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FFormatParamEditDelegate, const int32&, EditType, const FString&, InData);
DECLARE_DYNAMIC_DELEGATE_OneParam(FParamUpdateDataDelegate, const FParameterData&, ParamData);
DECLARE_DYNAMIC_DELEGATE_OneParam(FParamDetailActionDelegate, const int32&, ActionType);
DECLARE_DYNAMIC_DELEGATE_OneParam(FParamSelectedUpdateDataDelegate, const TArray<FParameterData>&, ParamDatas);

UCLASS()
class DESIGNSTATION_API UParameterDetailWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	virtual void NativeOnInitialized() override;

	void UpdateContent(const FParameterData& InData, const int32& ParamType);
	void UpdateContent(const FParameterData& InData);
	void SetIsNewParam(bool _IsNew);
	void SetIsVisibilityValid(bool _IsValid, const FString& VisibilityExpression);
	void SetIsEditableValid(bool _IsValid, const FString& EditableExpression);
	void SwitchWidgetBindFunc(const ESaveType& InType);

	int32 GetParamDetailShowType() const { return CurrentEditType; }

	void SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData);
	void SetFolderOrFileLocalParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetFolderOrFileParentParams() { return ParentParameters; }
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetFolderOrFileLocalParams() { return LocalParameters; }


	void ClearSelectState();

	void ShowSaveTip(bool IsSucceed);
	void UpdateSaveTipTimeActor();

	bool ParamRightMouseAction();
	bool CheckSelectWidgetValid();
	bool CheckParamDataValid(FString& ErrorMessage);
	bool CheckParamDataChange();

	void FormatValue(const FString& InValue);
	void FormatMaxValue(const FString& InMaxValue);
	void FormatMinValue(const FString& InMinValue);
	void FormatVisibilityValue(const FString& InVisiValue);
	void FormatSpecialValue(const FString& InVisiValue);
	void FormatMustValue(const FString& InVisiValue);
	void FormatEditableValue(const FString& InEditValue);

	void FormatExpression(const FString& InExpression, const FString& InValue);
	void FormatExpression();

	void FormatMaxExpression(const FString& InExpression, const FString& InValue);
	void FormatMinExpression(const FString& InExpression, const FString& InValue);
	void FormatVisibilityExpression(const FString& InExpression, const FString& InValue);
	void FormatSpecialExpression(const FString& InExpression, const FString& InValue);
	void FormatMustExpression(const FString& InExpression, const FString& InValue);
	void FormatEditableExpression(const FString& InExpression, const FString& InValue);


	FORCEINLINE FParameterData& GetParamDetailData() { return ParamDetailData; }
	FORCEINLINE int32 GetCurrentEditType() const { return CurrentEditType; }
	FORCEINLINE void SetIsParamNew(bool IsNew) { IsNewParameter = IsNew; }
	FORCEINLINE bool GetIsParamNew() { return IsNewParameter; }
	FORCEINLINE UFolderAndFileBaseWidget* GetCurrentSelectWidget() { return SelectWidget; }
	FORCEINLINE TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetParentParameters() { return ParentParameters; }

	static UParameterDetailWidget* Create();
	static UParameterDetailWidget* Get();
	void PreReadyParamType();
	void PreReadyParamTypeData();

	UFUNCTION(BlueprintCallable, Category = "Global Parameter")
	void OnParameterRelease();

	UFUNCTION(BlueprintImplementableEvent, Category = "Global Parameter")
	void UpdateReleaseBtnShow(bool bShow);

	void RemoveSelectedParameter(class UParameterAddItemData* InData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Parameter | Enum")
	void UpdataChkNoMatchEnumDataState(bool bIsCheck);


	UFUNCTION(BlueprintImplementableEvent, Category = "Parameter | Grid")
	void UpdateParamGridMark(const EParamGridMarkType& InMark);

	UFUNCTION(BlueprintCallable, Category = "Parameter | Grid")
	void GridValueMarkChanged(const EParamGridMarkType& InMark);

public:
	void BindDelegate();

	void UpdateParamData(const FParameterData& UpdateData);
	void UpdateParamData(const TArray<FParameterData>& UpdateData);
	void UpdateBatchParameterResponse(const TArray<FParameterData>& OutParam);
	void UpdateParameterResponse(const FParameterData& OutParam);
	void UpdateParameterGroupResponse(const FParameterGroupTableData& OutParam);
	int32 GetPreSelectFolderID() { return FCString::Atoi(*PreSelectFolderID); }
	void SetPreSelectFolderID(FString InPreSelectFolderID) { PreSelectFolderID = InPreSelectFolderID; }
	void SwitchParamBorderShow(bool IsDetailBorder);
	bool IsAddParDele = false;

private:
	int32  NumOfParamType;
	bool IsDMUnique; // Dm -- name
	bool IsNameUnique; // name -- description
	bool IsIdUnique;  // id -- paramid

public:
	FParamDetailDelegate ParamDetailEditDelegate;
	FFormatParamEditDelegate FormatParamEditDelegate;
	FParamUpdateDataDelegate ParamUpdateDataDelegate;
	FParamDetailActionDelegate ParamDetailActionDelegate;
	FParamSelectedUpdateDataDelegate SelectedParamUpdateDatasDelegate;
private:

	UFUNCTION()
		void OnSearchParamFileSelectHandler(UParamTreeFileWidget* ParamFile);
	UFUNCTION()
		void EnumParamItemEdit(const EParamEnumItemType& EditType, UParameterEnumItemWidget* EnumItem);
	UFUNCTION()
		void DetailParamExpressionEdit(const int32& EditType, const FString& Expression);
	UFUNCTION()
		void OnParamsSelectHandler(UParamTreeFileWidget* SelectedParam);
	UFUNCTION()
		void OnParamFolderSelectHandler(UParamTreeFolderWidget* SelectParamFolder);
	UFUNCTION()
		void OnParamFolderNameChangeHandler(const int32& id, const FString& NewName);
	UFUNCTION()
		void OnParamTreeItemRightClickHandler();
	UFUNCTION()
		void OnTimeActorHandler(float ShowRate);

	UFUNCTION()
	void HandleParamTreeFileOpenContextMenu(UParamTreeFileWidget* SelectedParam, const FPointerEvent& InMouseEvent);

	UFUNCTION()
	void HandleParamTreeFolderOpenContextMenu(UParamTreeFolderWidget* FolderItem, const FPointerEvent& InMouseEvent);

	void DownloadFile(const FString& RelativePath);
	void SaveParamDataFileAndUpload();
	void UploadFile(const FString& RelativePath);
	
	UFUNCTION()
		void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
		void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);

	UFUNCTION()
		void OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg);

	UFUNCTION()
		void RemoveProcess();
	UFUNCTION()
		void UploadSuccess(bool IsTrue);

	void ForceSelectFolder(UParamTreeFolderWidget* SelectParamFolder);
	void ClearSearchText();
	bool CheckParamValueIsChange(const FString& OriginExp, const FString& OriginValue, FString& OutExp, FString& OutValue);
	void BindExpressionDelegate();
	void SwitchParamsShow(bool IsSearch = false);
	void UpdateParamEnumContent(const TArray<FEnumParameterTableData>& EnumParams);
	void UpdateSaveTipStyle(bool IsSucceed);
	void UpdateCBSClassific(const int32& InClassific_id = 0);
	void GenerateParamTree();
	void RefreshEnumContent();
	void SwitchWidgetLayout(const int32& ParamType);
	void SwitchValueTypeShow(bool IsEnum);
	void SwitchDetailWidgetState(const int32& ParamType);
	void SyncValueState();
	void RefreshCBSValueEnum();
	void RefreshCBSValueEnum(FString& NewExpress,FString& NewValue);
	bool IsOptionExist(const FString& InSelection, const TArray<FEnumParameterTableData>& EnumData, const TArray<FEnumParameterTableData>& NewEnumData);
	void SearchParametersResponse();
	void CreateParameterResponse(const FParameterData& OutParam);
	void CreateParameterGroupTableResponse(const FParameterGroupTableData& OutParam);

	FString GetCorrectParamValue(const FString& ParamValue, const FString& MaxValue, const FString& MinValue);
	bool IsMaxNMinValueVaild(const TArray<FEnumParameterTableData>& EnumValue, const FString& MaxValue, const FString& MinValue, TArray<float>& EnumArray);

public:
	UPROPERTY(BlueprintReadOnly, Category = "Parameter | Data")
		FParameterData ParamDetailData;

private:
	UPROPERTY()
		FParameterGroupTableData ParamGroupDetailData;
	UPROPERTY()
		FParameterData OriginalData;
	UPROPERTY()
		TArray<FEnumParameterTableData> NewEnums;
	UPROPERTY()
		TArray<FEnumParameterTableData> DeleteEnums;
	UPROPERTY()
		TArray<UParamTreeFolderWidget*> ParamTreeFolders;
	UPROPERTY()
		TMap<int32, FString> ParamClassificMap;
	UPROPERTY()
		UFolderAndFileBaseWidget* SelectWidget;
	UPROPERTY()
		UParamTreeFileWidget* ToDelFileWidget; // use in mark new file widget when selected changed
	UPROPERTY()
		TArray<UParamTreeFileWidget*> SearchParams;
	UPROPERTY()
		TArray<UParameterEnumItemWidget*> ParamEnumItems;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  LocalParameters;
	UPROPERTY()
		ATimeActor* SaveTipTimeActor;
	UPROPERTY()
		TArray<FParameterData> ParemtersToSave;
	UPROPERTY()
		FString PreSelectFolderID;
	UPROPERTY()
		TArray<FString> FilesToUpload;

	bool IsNewParameter;
	int32 CurrentEditType;
	FString OldValue;

	FString DownloadUUID;
	FString DownloadOverUUID;
	FString UploadUUID;
	FString MergeLogUUID;
	FString DownloadFileUUID;

	UPROPERTY()
	FCatalogParamNetUUID NetUUID;


	UPROPERTY()
	TArray<FParameterData> SelectedParamDetailDatas;

protected:
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

protected:
	/*UFUNCTION()
		void OnClickedBtnClose();*/
	UFUNCTION()
		void OnTextChangedEdtParamSearch(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtParamSearch(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnParamSearch();
	UFUNCTION()
		void OnTextChangedEdtName(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextChangedEdtDM(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtDM(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnSelectionChangedCBSClassific(FString SelectedItem, ESelectInfo::Type SelectionType);
	UFUNCTION()
		void OnTextChangedEdtID(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtID(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtValueExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnValueExpress();
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnSelectionChangedCBSValue(FString SelectedItem, ESelectInfo::Type SelectionType);
	UFUNCTION()
		void OnTextCommittedEdtMaxExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnValueMaxExpress();
	UFUNCTION()
		void OnTextCommittedEdtMaxValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtMinExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnValueMinExpress();
	UFUNCTION()
		void OnTextCommittedEdtMinValue(const FText& Text, ETextCommit::Type CommitMethod);
	//visibility
	UFUNCTION()
		void OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisiExpress();
	UFUNCTION()
		void OnTextCommittedEdtVisibility(const FText& Text, ETextCommit::Type CommitMethod);
	//editable
	UFUNCTION()
		void OnTextCommittedEdtEditExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtSpecialExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtMustExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnEditExpress();
	UFUNCTION()
		void OnClickedBtnSpecialExpress();
	UFUNCTION()
		void OnClickedBtnMustExpress();
	UFUNCTION()
		void OnTextCommittedEdtEditable(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtSpecialable(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtMustable(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnAdd(); //enum add
	UFUNCTION()
		void OnClickedBtnCancel();
	UFUNCTION()
		void OnClickedBtnSave();
		UFUNCTION()
		void OnClickedBtnSelect();
		UFUNCTION()
		void OnClickedBtnAllCancel();
		UFUNCTION()
		void OnClickedBtnAllSave();
	//param add
	UFUNCTION()
		void OnClickedBtnParamAdd();
	UFUNCTION()
		void OnClickedBtnParamUpLoad();

	//逻辑现为刷所有参数数据
	UFUNCTION()
		void OnClickedBtnParamDownLoad();
	UFUNCTION()
		void OnClickedBtnTipClose();

	UFUNCTION()
		void OnStateChangedChkNoMatchEnumData(bool bIsChecked);


	UFUNCTION()
	void OnTextCommittedEdtGridExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
	void OnClickedBtnGridExpression();
	UFUNCTION()
	void OnTextCommittedEdtGridValue(const FText& Text, ETextCommit::Type CommitMethod);

private:
	/*UPROPERTY()
		UTextBlock* TxtTitle;
	UPROPERTY()
		UButton* BtnClose;*/
	UPROPERTY()
		UBorder* BorGlobalTree;
	UPROPERTY()
		UEditableText* EdtParamSearch;
	UPROPERTY()
		UButton* BtnParamSearch;
	UPROPERTY()
		UBorder* BorGlobalParams;
	UPROPERTY(BlueprintReadWrite, Category = "DetailWidget", meta = (AllowPrivateAccess = true, BindWidget = true))
		UScrollBox* Scb_GlobalParams;
	UPROPERTY()
		UBorder* BorSearchParams;
	UPROPERTY()
		UScrollBox* ScbSearchParams;
	UPROPERTY()
		UBorder* BorParamDetail;
	UPROPERTY()
		UEditableText* EdtName;
	UPROPERTY()
		UEditableText* EdtDM;
	UPROPERTY()
		UComboBoxString* CBSClassific;
	UPROPERTY()
		UEditableText* EdtID;
	UPROPERTY()
		UEditableText* EdtValueExpress;
	UPROPERTY()
		UButton* BtnValueExpress;
	UPROPERTY()
		UBorder* BorNoEnum;
	UPROPERTY()
		UEditableText* EdtValue;
	UPROPERTY()
		UBorder* BorEnum;
	UPROPERTY()
		UComboBoxString* CBSEnumValues;
	UPROPERTY()
		UEditableText* EdtMaxExpress;
	UPROPERTY()
		UButton* BtnMaxExpress;
	UPROPERTY()
		UEditableText* EdtMax;
	UPROPERTY()
		UEditableText* EdtMinExpress;
	UPROPERTY()
		UButton* BtnMinExpress;
	UPROPERTY()
		UEditableText* EdtMin;
	UPROPERTY()
		UEditableText* EdtVisiExpress;
	UPROPERTY()
		UButton* BtnVisiExpress;
	UPROPERTY()
		UEditableText* EdtVisibility;
	UPROPERTY()
		UEditableText* EdtEditExpress;
	UPROPERTY()
		UEditableText* EdtSpecialExpress;
	UPROPERTY()
		UEditableText* EdtMustExpress;
	UPROPERTY()
		UButton* BtnEditExpress;
	UPROPERTY()
		UButton* BtnSpecialExpress;
	UPROPERTY()
		UButton* BtnMustExpress;
	UPROPERTY()
		UEditableText* EdtEditable;
	UPROPERTY()
		UEditableText* EdtSpecialable;
	UPROPERTY()
		UEditableText* EdtMustable;
	UPROPERTY()
		UButton* BtnAdd;
	UPROPERTY()
		UScrollBox* ScbEnumList;
	UPROPERTY()
		UButton* BtnSave;
	UPROPERTY()
		UButton* BtnCancel;
	UPROPERTY()
		UBorder* BorParamAddBack;
	UPROPERTY()
		UBorder* BorParamAdd;
	UPROPERTY()
		UButton* BtnParamAdd;
	UPROPERTY()
		UBorder* BorParamUpLoadBack;
	UPROPERTY()
		UBorder* BorParamUpLoad;
	UPROPERTY()
		UButton* BtnParamUpLoad;
	UPROPERTY()
		UBorder* BorParamDownLoadBack;
	UPROPERTY()
		UBorder* BorParamDownLoad;
	UPROPERTY()
		UButton* BtnParamDownLoad;
	UPROPERTY()
		UCanvasPanel* CPTips;
	UPROPERTY()
		UBorder* BorTipBackground;
	UPROPERTY()
		UImage* ImgResult;
	UPROPERTY()
		UTextBlock* TxtResult;
	UPROPERTY()
		UButton* BtnTipClose;
	UPROPERTY()
		UMergeProcessWidget* MergeProcessWidget;

		UPROPERTY()
		UButton* BtnSelect;

		UPROPERTY()
		UListView* ListViewSelect;

		UPROPERTY()
		USizeBox* BoxSelectList;

		UPROPERTY()
		class UWidgetSwitcher* SelectSwitcher;

		UPROPERTY()
		UButton* BtnSaveAll;

		UPROPERTY()
		UButton* BtnCancelAll;

	UPROPERTY(meta=(BindWidget=true))
		UCheckBox* ChkNoMatchEnum;

	//grid
	UPROPERTY(meta = (BindWidget=true))
		UEditableText* Edt_GridExpress;
	UPROPERTY(meta = (BindWidget = true))
		UButton* Btn_GridExpress;
	UPROPERTY(meta = (BindWidget = true))
		UEditableText* Edt_GridValue;

};
