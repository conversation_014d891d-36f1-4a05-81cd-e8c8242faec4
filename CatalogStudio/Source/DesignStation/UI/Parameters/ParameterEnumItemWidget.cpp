// Fill out your copyright notice in the Description page of Project Settings.

#include "ParameterEnumItemWidget.h"
#include "ParameterDetailWidget.h"
#include "Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DataCenter/Parameter/ParameterEffectionParser.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

#define LOCTEXT_NAMESPACE "param enum"

FString UParameterEnumItemWidget::EnumParamPath = TEXT("WidgetBlueprint'/Game/UI/Parameters/ParameterEnumItemUI.ParameterEnumItemUI_C'");

bool UParameterEnumItemWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	
	return true;
}

void UParameterEnumItemWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(TxtIndex, Txt_Index);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtValue);

	BIND_PARAM_CPP_TO_UMG(EdtExpress, Edt_Express);
	BIND_WIDGET_FUNCTION(EdtExpress, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtExpress);
	BIND_PARAM_CPP_TO_UMG(BtnExpress, Btn_Express);
	BIND_WIDGET_FUNCTION(BtnExpress, OnClicked, UParameterEnumItemWidget::OnClickedBtnExpress);

	BIND_PARAM_CPP_TO_UMG(EdtVisiExpress, Edt_VisiExpress);
	BIND_WIDGET_FUNCTION(EdtVisiExpress, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtVisiExpress);
	BIND_PARAM_CPP_TO_UMG(BtnVisiExpress, Btn_VisiExpress);
	BIND_WIDGET_FUNCTION(BtnVisiExpress, OnClicked, UParameterEnumItemWidget::OnClickedBtnVisiExpress);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UParameterEnumItemWidget::OnClickedBtnDelete);
	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtName);
	BIND_PARAM_CPP_TO_UMG(EdtVisiValue, Edt_VisiValue);
	BIND_WIDGET_FUNCTION(EdtVisiValue, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtVisiValue);

	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UParameterEnumItemWidget::OnClickedBtnUp);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UParameterEnumItemWidget::OnClickedBtnDown);


	BIND_PARAM_CPP_TO_UMG(BtnUpdateImg, Btn_UpdateImg);
	BIND_PARAM_CPP_TO_UMG(BtnDeleteImage, Btn_DeleteImage);
	BIND_PARAM_CPP_TO_UMG(ImgEnum, Img_Enum);
	BIND_WIDGET_FUNCTION(BtnDeleteImage, OnClicked, UParameterEnumItemWidget::OnClickedBtnDeleteImage);
	BIND_WIDGET_FUNCTION(BtnUpdateImg, OnClicked, UParameterEnumItemWidget::OnClickedBtnUpdateImg);

	BIND_WIDGET_FUNCTION(EdtFSC, OnTextCommitted, UParameterEnumItemWidget::OnTextCommittedEdtFSC);
	BIND_WIDGET_FUNCTION(BtnFSC, OnClicked, UParameterEnumItemWidget::OnClickBtnFSC);

	BtnDeleteImage->SetVisibility(ESlateVisibility::Collapsed);

	BindDelegate();

	//IsDetailInfoShow = false;
	//IsNewEnumParam = false;
}

void UParameterEnumItemWidget::SetGlobalWidget(UParameterDetailWidget* InWidget)
{
	GlobalWidget = TWeakObjectPtr<UParameterDetailWidget>(InWidget);
}

void UParameterEnumItemWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UParameterEnumItemWidget::OnDownloadFileResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->UploadFileResponseDelegate.AddUniqueDynamic(this, &UParameterEnumItemWidget::OnUploadFileResponseHandler);
}

void UParameterEnumItemWidget::UploadFileRequest(const FString& FileRelativePath)
{
	UploadUUID = UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(FileRelativePath);
}

void UParameterEnumItemWidget::OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath)
{
	if (UploadUUID.Equals(OutFilePath, ESearchCase::IgnoreCase))
	{
		UploadUUID = TEXT("NoWay");
		if (OutRes)
		{
			UE_LOG(LogTemp, Log, TEXT("Upload [%s] SUCCESS"), *OutFilePath);
			EnumParamData.image_for_display = OutFilePath;
			ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EEnumImg, this);
			BtnDeleteImage->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Upload [%s] FAILED"), *OutFilePath);
		}
	}
}

void UParameterEnumItemWidget::DownloadFileRequest(const FString& FileRelativePath)
{
	DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(FileRelativePath);
}

void UParameterEnumItemWidget::OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	if (DownloadUUID.Equals(UUID))
	{
		DownloadUUID = TEXT("NoWay");
		if (OutRes && OutFilePath.IsValidIndex(0))
		{
			const FString AbsPath = FPaths::ConvertRelativePathToFull(
				FPaths::Combine(FPaths::ProjectContentDir(), EnumParamData.image_for_display)
			);
			if (FPaths::FileExists(AbsPath))
			{
				UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);
				ImgEnum->SetBrushFromTexture(ThumbnailTex);
				BtnDeleteImage->SetVisibility(ESlateVisibility::Visible);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Download [%s] FAILED"), *DownloadUUID);
		}
	}
}

void UParameterEnumItemWidget::ClearExpAndValue()
{
	if (IS_OBJECT_PTR_VALID(EdtValue) && IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(TxtIndex))
	{
		EdtExpress->SetText(FText::FromString(TEXT("0.0")));
		EdtValue->SetText(FText::FromString(TEXT("0.0")));
		EnumParamData.expression = TEXT("0.0");
		EnumParamData.value = TEXT("0.0");
	}
}

void UParameterEnumItemWidget::UpdateContent(const FEnumParameterTableData& InData, const int32& Index)
{
	UE_LOG(LogTemp, Log, TEXT("ParamEnumItem---update data"));
	EnumParamData.CopyData(InData);
	if (IS_OBJECT_PTR_VALID(EdtValue) && IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(TxtIndex))
	{
		TxtIndex->SetText(FText::FromString(FString::FromInt(Index)));
		EdtExpress->SetText(FText::FromString(InData.expression));
		EdtValue->SetText(FText::FromString(InData.value));
		EdtName->SetText(FText::FromString(InData.name_for_display));
		const FString FormatEdtVisiValue = !InData.visibility.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*InData.visibility)) : InData.visibility;
		EdtVisiValue->SetText(FText::FromString(FormatEdtVisiValue));
		EdtVisiExpress->SetText(FText::FromString(InData.visibility_exp));

		EdtFSC->SetText(FText::FromString(InData.force_select_condition));

		if (!InData.image_for_display.IsEmpty())
		{
			DownloadFileRequest(InData.image_for_display);
		}
		else
		{
			UTexture2D* DefaultImg = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/GlobalParameter/DefaultEnum.DefaultEnum'")));
			ImgEnum->SetBrushFromTexture(DefaultImg);
		}
	}
}

void UParameterEnumItemWidget::FormatEnumVisibility(const FString& InVisibility)
{
	EnumParamData.visibility = InVisibility;
}

bool UParameterEnumItemWidget::CheckEnumValid(FString& ErrorMessage)
{
	if (IS_OBJECT_PTR_VALID(EdtValue))
	{
		if (EdtValue->GetText().IsEmpty() || !EdtValue->GetText().IsNumeric())
		{
			ErrorMessage = NSLOCTEXT(LOCTEXT_NAMESPACE, "ValidKey", "EnumValue must be not empty and numeric").ToString();
			return false;
		}
	}
	return true;
}

int32 UParameterEnumItemWidget::GetEnumIndex()
{
	if (IS_OBJECT_PTR_VALID(TxtIndex))
	{
		return FCString::Atoi(*TxtIndex->GetText().ToString()) - 1;
	}
	return -1;
}

UParameterEnumItemWidget* UParameterEnumItemWidget::Create()
{
	UClass* EnumParamItemBp = LoadClass<UUserWidget>(NULL, *EnumParamPath);
	checkf(EnumParamItemBp, TEXT("load enum param item bp error!"));
	UParameterEnumItemWidget* EnumParamItem = CreateWidget<UParameterEnumItemWidget>(GWorld.GetReference(), EnumParamItemBp);
	checkf(EnumParamItem, TEXT("create enum param item error!"));
	return EnumParamItem;
}

FEnumParameterTableData UParameterEnumItemWidget::GetData()
{
	return EnumParamData;
}

void UParameterEnumItemWidget::OnVisiExpressHandler(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EParamEnumExpressType::VisiExpress)
	{
		OnTextCommittedEdtVisiExpress(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

void UParameterEnumItemWidget::OnExpressHandler(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EParamEnumExpressType::Express)
	{
		OnTextCommittedEdtExpress(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString EnumValue = Text.ToString();
		bool bIsNumericValue = UCatalogExpressionFunctionLibrary::CheckStringIsNumeric(EnumValue);

		for (auto EnumDataiter : UParameterDetailWidget::Get()->GetParamDetailData().EnumData)
		{
			if (EnumParamData.id != EnumDataiter.id && !EnumDataiter.value.IsEmpty())
			{
				bool bIsSameNumber = bIsNumericValue && FMath::IsNearlyEqual(FCString::Atof(*EnumDataiter.value), FCString::Atof(*EnumValue), 0.001f);
				bool bIsSameString = !bIsNumericValue && EnumDataiter.value.Equals(EnumValue, ESearchCase::CaseSensitive);

				if (bIsSameNumber || bIsSameString)
				{
					UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Error"), FText::FromStringTable(FName("PosSt"), TEXT("EnumValue Is Exist, Please Change!")).ToString());
					EdtValue->SetText(FText::FromString(EnumParamData.value));
					return;
				}
			}
		}

		if (bIsNumericValue)
		{
			UUIFunctionLibrary::FormatInputValue(EnumValue);
		}

		EnumParamData.expression = EnumValue;
		EnumParamData.value = EnumValue;
		EdtValue->SetText(FText::FromString(EnumValue));
		EdtExpress->SetText(FText::FromString(EnumParamData.expression));
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EnumValue, this);
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	bool bHasSameExpression = UParameterDetailWidget::Get()->GetParamDetailData().EnumData.ContainsByPredicate([&](const FEnumParameterTableData& InEnumData) 
	{
		return EnumParamData.id != InEnumData.id && !InEnumData.value.IsEmpty() && InEnumData.expression.Equals(Text.ToString(), ESearchCase::CaseSensitive);
	});

	if (bHasSameExpression)
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(TEXT("Error"), FText::FromStringTable(FName("PosSt"), TEXT("EnumExpression Is Exist, Please Change!")).ToString());
		EdtValue->SetText(FText::FromString(EnumParamData.value));
		EdtExpress->SetText(FText::FromString(EnumParamData.expression));
		return;
	}

	FString OutValue;
	FString OutExperssion;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, Text.ToString(), OutValue, OutExperssion);
	if (Res)
	{
		EnumParamData.value = OutValue;
		EnumParamData.expression = OutExperssion;
		EdtValue->SetText(FText::FromString(EnumParamData.value));
		EdtExpress->SetText(FText::FromString(EnumParamData.expression));
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EnumExpress, this);
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtValue->SetText(FText::FromString(EnumParamData.value));
		EdtExpress->SetText(FText::FromString(EnumParamData.expression));
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::Type::OnCleared || CommitMethod == ETextCommit::Type::Default) return;

	FString OutValue;
	FString OutExperssion;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParentParameters, LocalParameters, Text.ToString(), OutValue, OutExperssion);
	if (Res)
	{
		EnumParamData.visibility = OutValue;
		EnumParamData.visibility_exp = OutExperssion;
		EdtVisiValue->SetText(FText::FromString(EnumParamData.visibility));
		EdtVisiExpress->SetText(FText::FromString(EnumParamData.visibility_exp));
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EnumExpress, this);
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		EdtVisiValue->SetText(FText::FromString(EnumParamData.visibility));
		EdtVisiExpress->SetText(FText::FromString(EnumParamData.visibility_exp));
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtVisiValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.IsNumeric())
	{
		FString OutValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(OutValue);
		EdtVisiExpress->SetText(FText::FromString(OutValue));
		EnumParamData.visibility = OutValue;
		EnumParamData.visibility_exp = OutValue;
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::VisiValue, this);
	}
	else if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		const FString FormatEdtVisiValue = !EnumParamData.visibility.IsEmpty() ? FString::Printf(TEXT("%.1f"), FCString::Atof(*EnumParamData.visibility)) : EnumParamData.visibility;
		EdtVisiValue->SetText(FText::FromString(FormatEdtVisiValue));
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len() <= 20)
	{
		EnumParamData.name_for_display = Text.ToString();
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::Name, this);
	}
	else if (IS_OBJECT_PTR_VALID(EdtVisiExpress) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtName->SetText(FText::FromString(EnumParamData.name_for_display));
	}
}
void UParameterEnumItemWidget::SetDisplayName(const FString& Name)
{
	EnumParamData.name_for_display = Name;
	EdtName->SetText(FText::FromString(Name));

}
void UParameterEnumItemWidget::SetImagePath(const FString& InPath)
{
	EnumParamData.image_for_display = InPath;
}

void UParameterEnumItemWidget::OnClickedBtnExpress()
{
	if (EdtExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EParamEnumExpressType::Express, EdtExpress->GetText().ToString(), FName(TEXT("OnExpressHandler")));
	}
}

void UParameterEnumItemWidget::OnClickedBtnVisiExpress()
{
	if (EdtVisiExpress)
	{
		BIND_EXPRESSION_WIDGET((int32)EParamEnumExpressType::VisiExpress, EdtVisiExpress->GetText().ToString(), FName(TEXT("OnVisiExpressHandler")));
	}
}

void UParameterEnumItemWidget::OnClickedBtnDelete()
{
	bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString()
		, FText::FromStringTable(FName("PosSt"), (TEXT("This action will delete the param, are you sure?"))).ToString());
	if (Res)
	{
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::Delete, this);
	}
}

void UParameterEnumItemWidget::OnClickedBtnUp()
{
	ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EUp, this);
}

void UParameterEnumItemWidget::OnClickedBtnDown()
{
	ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EDown, this);
}

void UParameterEnumItemWidget::OnClickedBtnDeleteImage()
{
	EnumParamData.image_for_display.Empty();
	UTexture2D* DefaultImg = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/GlobalParameter/DefaultEnum.DefaultEnum'")));
	ImgEnum->SetBrushFromTexture(DefaultImg);
	ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EEnumImg, this);
}

void UParameterEnumItemWidget::OnClickedBtnUpdateImg()
{
	if (bImageSelecting) return;
	bImageSelecting = true;
	FString EnumImgPath;
	FCatalogFunctionLibrary::OpenFileDialogForImage(EnumImgPath);
	bImageSelecting = false;
	if (!EnumImgPath.IsEmpty())
	{
		UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(EnumImgPath);
		ImgEnum->SetBrushFromTexture(ThumbnailTex);

		FString uuid = FGuid::NewGuid().ToString().ToLower();

		FString SavePath = FString::Printf(TEXT("EnumImage/%s.%s"), *uuid, *FPaths::GetExtension(EnumImgPath));
		if (FCatalogFunctionLibrary::CopyFileTo(EnumImgPath, FPaths::ProjectContentDir() + SavePath) == ECopyFileErrorCode::ESuccess)
		{
			FDownloadFileData FileInfo(SavePath);
			FString TargetPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);
			ACatalogPlayerController::Get()->GetFileMD5AndSize(TargetPath, FileInfo.md5, FileInfo.size);

			UploadFileRequest(FileInfo.path);
		}
		//FDownloadFileDataLibrary::UpdateFileMD5(TEXT("param_image"), FileInfo);
		//EnumParamData.image_for_display = SavePath;
		//ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EEnumImg, this);
		//BtnDeleteImage->SetVisibility(ESlateVisibility::Visible);
	}
}

void UParameterEnumItemWidget::OnTextCommittedEdtFSC(const FText& Text, ETextCommit::Type CommiteMethod)
{
	if (CommiteMethod != ETextCommit::Type::OnCleared)
	{
		EnumParamData.force_select_condition = Text.ToString();
		ParamEnumEditDelegate.ExecuteIfBound(EParamEnumItemType::EForceSelect, this);
	}
}

void UParameterEnumItemWidget::OnClickBtnFSC()
{
	if (EdtFSC)
	{
		BIND_EXPRESSION_WIDGET((int32)EParamEnumExpressType::Condition, EdtFSC->GetText().ToString(), FName(TEXT("OnFSCExpressHandler")));
	}
}

void UParameterEnumItemWidget::OnFSCExpressHandler(const int32& EditType, const FString& OutExpression)
{
	if (EditType == static_cast<int32>(EParamEnumExpressType::Condition))
	{
		OnTextCommittedEdtFSC(FText::FromString(OutExpression), ETextCommit::OnEnter);
	}
}

void UParameterEnumItemWidget::SwitchState(bool bEnable)
{
	if (IS_OBJECT_PTR_VALID(BtnVisiExpress) && IS_OBJECT_PTR_VALID(BtnDelete)
		&& IS_OBJECT_PTR_VALID(EdtVisiExpress) && IS_OBJECT_PTR_VALID(EdtValue)
		&& IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtVisiValue) && IS_OBJECT_PTR_VALID(EdtExpress))
	{
		BtnVisiExpress->SetIsEnabled(bEnable);
		BtnDelete->SetIsEnabled(bEnable);
		EdtVisiExpress->SetIsEnabled(bEnable);
		EdtValue->SetIsEnabled(bEnable);
		EdtExpress->SetIsEnabled(bEnable);
		EdtName->SetIsEnabled(bEnable);
		EdtVisiValue->SetIsEnabled(bEnable);
	}
}

void UParameterEnumItemWidget::SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData)
{
	ParentParameters = InParamData;
}

void UParameterEnumItemWidget::SetFolderOrFileLocalParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData)
{
	LocalParameters = InParamData;
}

#undef LOCTEXT_NAMESPACE