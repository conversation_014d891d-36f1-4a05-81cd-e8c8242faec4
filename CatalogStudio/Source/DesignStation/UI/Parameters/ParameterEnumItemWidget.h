// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "ParameterEnumItemWidget.generated.h"

/**
 *
 */
extern class UParameterDetailWidget;

UENUM(BlueprintType)
enum class EParamEnumItemType : uint8
{
	EnumValue = 0,
	EnumExpress,
	Name,
	VisiExpress,
	VisiValue,
	Delete,
	EEnumImg,
	EUp,
	EDown,
	EForceSelect
};

UENUM(BlueprintType)
enum class EParamEnumExpressType : uint8
{
	VisiExpress = 0,
	Express,
	Condition
};

class UTextBlock;
class UEditableText;
class UButton;
class UBorder;
class UImage;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FParamEnumEditDelegate, const EParamEnumItemType&, EditType, UParameterEnumItemWidget*, EnumItem);

UCLASS()
class DESIGNSTATION_API UParameterEnumItemWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void UpdateContent(const FEnumParameterTableData& InData, const int32& Index);
	void FormatEnumVisibility(const FString& InVisibility);
	bool CheckEnumValid(FString& ErrorMessage);
	int32 GetEnumIndex();
	FORCEINLINE FEnumParameterTableData& GetEnumItemData() { return EnumParamData; }

	static UParameterEnumItemWidget* Create();
	FEnumParameterTableData GetData();

	int32 EnumSort;

	void SwitchState(bool bEnable);

	void SetDisplayName(const FString& Name);

	void SetImagePath(const FString& InPath);

	void SetGlobalWidget(UParameterDetailWidget* InWidget);

	UFUNCTION(BlueprintImplementableEvent, Category = "EnumWidget")
	void SetDisplayImage(UTexture2D* InTexture);


#pragma region NET

public:
	void BindDelegate();

	void UploadFileRequest(const FString& FileRelativePath);
	UFUNCTION()
	void OnUploadFileResponseHandler(bool OutRes, const FString& OutFilePath);

	void DownloadFileRequest(const FString& FileRelativePath);
	UFUNCTION()
	void OnDownloadFileResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);

	void ClearExpAndValue();

	void SetFolderOrFileParentParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData);
	void SetFolderOrFileLocalParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParamData);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetFolderOrFileParentParams() { return ParentParameters; }
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GetFolderOrFileLocalParams() { return LocalParameters; }
private:
	UPROPERTY()
	FString UploadUUID = TEXT("NoWay");

	UPROPERTY()
	FString DownloadUUID = TEXT("NoWay");

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  LocalParameters;

#pragma endregion

private:
	UFUNCTION()
		void OnVisiExpressHandler(const int32& EditType, const FString& OutExpression);
	UFUNCTION()
		void OnExpressHandler(const int32& EditType, const FString& OutExpression);

public:
	FParamEnumEditDelegate ParamEnumEditDelegate;

private:
	UPROPERTY()
		FEnumParameterTableData EnumParamData;

	//UPROPERTY()
	TWeakObjectPtr<UParameterDetailWidget> GlobalWidget;

	static FString EnumParamPath;

	FString ImagePath;

	//Fix bug CATALOG-1536
	bool bImageSelecting = false;
protected:
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExpress();
	UFUNCTION()
		void OnTextCommittedEdtVisiExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtVisiValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisiExpress();
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();
	UFUNCTION()
		void OnClickedBtnDeleteImage();
	UFUNCTION()
		void OnClickedBtnUpdateImg();

	UFUNCTION()
		void OnTextCommittedEdtFSC(const FText& Text, ETextCommit::Type CommiteMethod);
	UFUNCTION()
		void OnClickBtnFSC();

	UFUNCTION()
		void OnFSCExpressHandler(const int32& EditType, const FString& OutExpression);

private:
	UPROPERTY()
		UTextBlock* TxtIndex;
	UPROPERTY()
		UEditableText* EdtValue;
	UPROPERTY()
		UEditableText* EdtExpress;
	UPROPERTY()
		UButton* BtnExpress;
	UPROPERTY()
		UEditableText* EdtVisiExpress;
	UPROPERTY()
		UButton* BtnVisiExpress;
	UPROPERTY()
		UButton* BtnDelete;
	UPROPERTY()
		UEditableText* EdtName;
	UPROPERTY()
		UEditableText* EdtVisiValue;
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;
	UPROPERTY()
		UButton* BtnDeleteImage;
	UPROPERTY()
		UImage* ImgEnum;
	UPROPERTY()
		UButton* BtnUpdateImg;

	UPROPERTY(meta = (BindWidget = true))
		UEditableText* EdtFSC;
	UPROPERTY(meta=(BindWidget=true))
		UButton* BtnFSC;
};
