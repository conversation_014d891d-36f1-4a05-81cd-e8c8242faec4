// Fill out your copyright notice in the Description page of Project Settings.

#include "TimeActor.h"
#include "Runtime/Engine/Public/TimerManager.h"

// Sets default values
ATimeActor::ATimeActor()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

}

void ATimeActor::ResetTipShowTime()
{
	ShowTime = 3.0f;
}

void ATimeActor::BindTimeHandler()
{
	GWorld->GetTimerManager().SetTimer(ParameterTimeHandler, this, &ATimeActor::TimeHandlerFunc, 0.1f, true);
}

void ATimeActor::TimeHandlerFunc()
{
	UE_LOG(LogTemp, Log, TEXT("param tip time handler"));
	ShowTime -= 0.1f;
	if (ShowTime <= 1.0f)
	{
		TimeActorDelegate.ExecuteIfBound(ShowTime);
	}
	if (ShowTime <= 0.0f)
	{
		GWorld->GetTimerManager().ClearTimer(ParameterTimeHandler);
	}
}

// Called when the game starts or when spawned
void ATimeActor::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ATimeActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

