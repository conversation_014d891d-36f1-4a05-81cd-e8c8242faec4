// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "TimeActor.generated.h"

DECLARE_DYNAMIC_DELEGATE_OneParam(FTimeActorDelegate, float, TimeShow);

UCLASS()
class DESIGNSTATION_API ATimeActor : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ATimeActor();

	void ResetTipShowTime();
	void BindTimeHandler();
	UFUNCTION()
		void TimeHandlerFunc();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

public:
	FTimeActorDelegate TimeActorDelegate;

private:
	float ShowTime;
	FTimerHandle ParameterTimeHandler;
};
