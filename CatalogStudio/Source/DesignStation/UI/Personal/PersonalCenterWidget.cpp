// Fill out your copyright notice in the Description page of Project Settings.

#include "PersonalCenterWidget.h"

#include "Components/Button.h"
#include "Components/EditableText.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/Border.h"

FString UPersonalCenterWidget::PersonalCenterPath = TEXT("WidgetBlueprint'/Game/UI/Personal/PersonalCenterUI.PersonalCenterUI_C'");

bool UPersonalCenterWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnPhoto, Btn_Photo);
	BIND_WIDGET_FUNCTION(BtnPhoto, OnClicked, UPersonalCenterWidget::OnClickedBtnPhoto);
	BIND_PARAM_CPP_TO_UMG(ImgPhoto, Img_Photo);
	BIND_PARAM_CPP_TO_UMG(TxtUserName, Txt_UserName);
	BIND_PARAM_CPP_TO_UMG(TxtAccount, Txt_Account);

	BIND_PARAM_CPP_TO_UMG(BorNormal, Bor_Normal);
	BIND_PARAM_CPP_TO_UMG(TxtRealName, Txt_RealName);
	BIND_PARAM_CPP_TO_UMG(BtnAlter, Btn_Alter);
	BIND_WIDGET_FUNCTION(BtnAlter, OnClicked, UPersonalCenterWidget::OnClickedBtnAlter);

	BIND_PARAM_CPP_TO_UMG(BorChangeName, Bor_ChangeName);
	BIND_PARAM_CPP_TO_UMG(EdtRealName, Edt_RealName);
	BIND_WIDGET_FUNCTION(EdtRealName, OnTextCommitted, UPersonalCenterWidget::OnTextCommittedEdtRealName);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UPersonalCenterWidget::OnClickedBtnCancel);
	BIND_PARAM_CPP_TO_UMG(BtnComfirm, Btn_Comfirm);
	BIND_WIDGET_FUNCTION(BtnComfirm, OnClicked, UPersonalCenterWidget::OnClickedBtnComfirm);

	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, UPersonalCenterWidget::OnClickedBtnClose);
	

	BIND_PARAM_CPP_TO_UMG(TxtPhoneNumber, Txt_PhoneNumber);
	BIND_PARAM_CPP_TO_UMG(BtnChangePN, Btn_ChangePN);
	BIND_WIDGET_FUNCTION(BtnChangePN, OnClicked, UPersonalCenterWidget::OnClickedBtnChangePN);

	BIND_PARAM_CPP_TO_UMG(TxtPassWord, Txt_PassWord);
	BIND_PARAM_CPP_TO_UMG(BtnChangePW, Btn_ChangePW);
	BIND_WIDGET_FUNCTION(BtnChangePW, OnClicked, UPersonalCenterWidget::OnClickedBtnChangePW);

	SwitchRealNameShow(true);
	return true;
}

UPersonalCenterWidget * UPersonalCenterWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UPersonalCenterWidget>(UPersonalCenterWidget::PersonalCenterPath);
}

void UPersonalCenterWidget::SwitchRealNameShow(bool IsNormal)
{
	UE_LOG(LogTemp, Log, TEXT("switeh real name border show"));
	if (IS_OBJECT_PTR_VALID(BorNormal) && IS_OBJECT_PTR_VALID(BorChangeName))
	{
		UE_LOG(LogTemp, Log, TEXT("switeh real name border show is real execute"));
		BorNormal->SetVisibility(IsNormal ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BorChangeName->SetVisibility(IsNormal ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UPersonalCenterWidget::OnClickedBtnPhoto()
{
}

void UPersonalCenterWidget::OnClickedBtnAlter()
{
	this->SwitchRealNameShow(false);
}

void UPersonalCenterWidget::OnTextCommittedEdtRealName(const FText & Text, ETextCommit::Type CommitMethod)
{
}

void UPersonalCenterWidget::OnClickedBtnCancel()
{
}

void UPersonalCenterWidget::OnClickedBtnComfirm()
{
	SwitchRealNameShow(true);
}

void UPersonalCenterWidget::OnClickedBtnChangePN()
{
}

void UPersonalCenterWidget::OnClickedBtnChangePW()
{
}

void UPersonalCenterWidget::OnClickedBtnClose()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
}
