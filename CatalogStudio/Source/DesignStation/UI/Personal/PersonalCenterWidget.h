// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PersonalCenterWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;
class UTextBlock;
class UEditableText;
class UBorder;

UCLASS()
class DESIGNSTATION_API UPersonalCenterWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	static UPersonalCenterWidget* Create();

private:
	void SwitchRealNameShow(bool IsNormal);

private:
	static FString PersonalCenterPath;

protected:
	UFUNCTION()
		void OnClickedBtnPhoto();
	UFUNCTION()
		void OnClickedBtnAlter();
	UFUNCTION()
		void OnTextCommittedEdtRealName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnCancel();
	UFUNCTION()
		void OnClickedBtnComfirm();
	UFUNCTION()
		void OnClickedBtnChangePN();
	UFUNCTION()
		void OnClickedBtnChangePW();
	UFUNCTION()
		void OnClickedBtnClose();
	
private:
	UPROPERTY()                 //username
		UButton* BtnPhoto;
	UPROPERTY()
		UImage* ImgPhoto;
	UPROPERTY()
		UTextBlock* TxtUserName;
	UPROPERTY()
		UTextBlock* TxtAccount;
	 
	UPROPERTY()                //realname            
		UBorder* BorNormal;
	UPROPERTY()
		UTextBlock* TxtRealName;
	UPROPERTY()
		UButton* BtnAlter;

	UPROPERTY()
		UBorder* BorChangeName;
	UPROPERTY()
		UEditableText* EdtRealName;
	UPROPERTY()
		UButton* BtnCancel;
	UPROPERTY()
		UButton* BtnComfirm;

	UPROPERTY()
		UButton* BtnClose;

	UPROPERTY()                //phonenumber
		UTextBlock* TxtPhoneNumber;
	UPROPERTY()
		UButton* BtnChangePN;

	UPROPERTY()                //password
		UTextBlock* TxtPassWord;
	UPROPERTY()
		UButton* BtnChangePW;
};
