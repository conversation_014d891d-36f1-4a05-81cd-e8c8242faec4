// Fill out your copyright notice in the Description page of Project Settings.


#include "PlaceRuleCustomWidget.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"

DEFINE_LOG_CATEGORY(PlaceRuleCustomLog);

FString UPlaceRuleCustomWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/MainUI/BP_CustomPlaceRule.BP_CustomPlaceRule_C'");

bool UPlaceRuleCustomWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}
	
	HasUserEdit = false;

	return true;
}

UPlaceRuleCustomWidget* UPlaceRuleCustomWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UPlaceRuleCustomWidget>(UPlaceRuleCustomWidget::FilePath);
}

void UPlaceRuleCustomWidget::UpdateParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParams, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InFileParams)
{
	ParentParameters = InParentParams;
	FileParameters = InFileParams;
}

void UPlaceRuleCustomWidget::UpdateRuleData(const FRefPlaceRuleCustomData& InPlaceRuleCustomData)
{
	PlaceRuleCustomData = InPlaceRuleCustomData;

	UpdateWidgetCustomPlaceRuleShow(PlaceRuleCustomData);
}

void UPlaceRuleCustomWidget::UpdateRuleData(const FRefPlaceRuleData& InPlaceRuleData)
{
	PlaceRuleCustomData.ConstructFromPlaceRuleData(InPlaceRuleData);

	UpdateWidgetCustomPlaceRuleShow(PlaceRuleCustomData);
}

void UPlaceRuleCustomWidget::ResetEditFlag()
{
	HasUserEdit = false;
}

FRefPlaceRuleCustomData UPlaceRuleCustomWidget::GetPlaceRuleCustomData() const
{
	return PlaceRuleCustomData;
}


void UPlaceRuleCustomWidget::StringDataChange(const FString& InStr, EPlaceRuleEditStrType InEditType)
{
	bool EditChange = false;
	if(InEditType == EPlaceRuleEditStrType::E_L_Express
		|| InEditType == EPlaceRuleEditStrType::E_R_Express
		|| InEditType == EPlaceRuleEditStrType::E_U_Express
		|| InEditType == EPlaceRuleEditStrType::E_D_Express
		|| InEditType == EPlaceRuleEditStrType::E_F_Express
		|| InEditType == EPlaceRuleEditStrType::E_B_Express)
	{
		EditChange = ExpressionEditExecute(InStr, InEditType);
	}
	else if(InEditType == EPlaceRuleEditStrType::E_L_Value
		|| InEditType == EPlaceRuleEditStrType::E_R_Value
		|| InEditType == EPlaceRuleEditStrType::E_U_Value
		|| InEditType == EPlaceRuleEditStrType::E_D_Value
		|| InEditType == EPlaceRuleEditStrType::E_F_Value
		|| InEditType == EPlaceRuleEditStrType::E_B_Value)
	{
		EditChange = ValueEditExecute(InStr, InEditType);
	}

	if(EditChange)
	{
		UpdateWidgetCustomPlaceRuleShow(PlaceRuleCustomData);
	}
}

void UPlaceRuleCustomWidget::CheckDataChange(const bool& InCheck, EPlaceRuleEditCheckType InCheckType)
{
	CheckEditExecute(InCheck, InCheckType);

	UpdateWidgetCustomPlaceRuleShow(PlaceRuleCustomData);
}

void UPlaceRuleCustomWidget::OnExpressionClick(const FString& InEditStr, EPlaceRuleEditStrType InEditType)
{
	int32 EditType = static_cast<int32>(InEditType);
	BIND_EXPRESSION_WIDGET(EditType, InEditStr, FName(TEXT("FolderOrFileExpressionEdit")));
}

void UPlaceRuleCustomWidget::OnCloseClickExecute()
{
	if (HasUserEdit)
	{
		ModifyRefPlaceRuleCustomDelegate.ExecuteIfBound(PlaceRuleCustomData);
	}
	
	ResetEditFlag();
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void UPlaceRuleCustomWidget::FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression)
{
	bool Res = ExpressionEditExecute(Expression, static_cast<EPlaceRuleEditStrType>(EditType));
	if(Res)
	{
		UpdateWidgetCustomPlaceRuleShow(PlaceRuleCustomData);
	}
}

FString UPlaceRuleCustomWidget::GetCurrentDataString(const EPlaceRuleEditStrType& InEditType)
{
	FString Res = TEXT("");
	if(InEditType == EPlaceRuleEditStrType::E_L_Express)
	{
		Res = PlaceRuleCustomData.leftConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_L_Value)
	{
		Res = PlaceRuleCustomData.leftConfig;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_R_Express)
	{
		Res = PlaceRuleCustomData.rightConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_R_Value)
	{
		Res = PlaceRuleCustomData.rightConfig;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_U_Express)
	{
		Res = PlaceRuleCustomData.upperConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_U_Value)
	{
		Res = PlaceRuleCustomData.upperConfig;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_D_Express)
	{
		Res = PlaceRuleCustomData.downConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_D_Value)
	{
		Res = PlaceRuleCustomData.downConfig;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_F_Express)
	{
		Res = PlaceRuleCustomData.frontConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_F_Value)
	{
		Res = PlaceRuleCustomData.frontConfig;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_B_Express)
	{
		Res = PlaceRuleCustomData.afterConfigExpression;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_B_Value)
	{
		Res = PlaceRuleCustomData.afterConfig;
	}

	return Res;
}

bool UPlaceRuleCustomWidget::ValueEditExecute(const FString& InStr, const EPlaceRuleEditStrType& InEditType)
{
	if(InStr.IsEmpty() || !InStr.IsNumeric())
	{
		return false;
	}

	FString CurStr = GetCurrentDataString(InEditType);
	if(InStr.Equals(CurStr))
	{
		UE_LOG(PlaceRuleCustomLog, Log, TEXT("Type[%d] Value[%s] No Change To Edit"), static_cast<int32>(InEditType), *InStr);
		return false;
	}
	
	if(InEditType == EPlaceRuleEditStrType::E_L_Value)
	{
		PlaceRuleCustomData.leftConfigExpression = InStr;
		PlaceRuleCustomData.leftConfig = InStr;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_R_Value)
	{
		PlaceRuleCustomData.rightConfigExpression = InStr;
		PlaceRuleCustomData.rightConfig = InStr;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_U_Value)
	{
		PlaceRuleCustomData.upperConfigExpression = InStr;
		PlaceRuleCustomData.upperConfig = InStr;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_D_Value)
	{
		PlaceRuleCustomData.downConfigExpression = InStr;
		PlaceRuleCustomData.downConfig = InStr;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_F_Value)
	{
		PlaceRuleCustomData.frontConfigExpression = InStr;
		PlaceRuleCustomData.frontConfig = InStr;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_B_Value)
	{
		PlaceRuleCustomData.afterConfigExpression = InStr;
		PlaceRuleCustomData.afterConfig = InStr;
	}
	
	HasUserEdit = true;
	PlaceRuleCustomData.SetUserToCustom();

	return true;
}

bool UPlaceRuleCustomWidget::ExpressionEditExecute(const FString& InStr, const EPlaceRuleEditStrType& InEditType)
{
	if(InStr.IsEmpty())
	{
		return false;
	}

	FString CurStr = GetCurrentDataString(InEditType);
	if(InStr.Equals(CurStr))
	{
		UE_LOG(PlaceRuleCustomLog, Log, TEXT("Type[%d] Expression[%s] No Change To Calculate Edit"), static_cast<int32>(InEditType), *InStr);
		return false;
	}

	FString OutExpression;
	FString OutValue;
	bool CalRes = UParameterRelativeLibrary::CalculateParameterExpression(
		ACatalogPlayerController::Get()->GetGlobalParameterMap(),
		ParentParameters,
		FileParameters,
		InStr,
		OutValue,
		OutExpression);
	if(!CalRes)
	{
		UE_LOG(PlaceRuleCustomLog, Error, TEXT("Calculate Type[%d] Expression[%s] Error!"), static_cast<int32>(InEditType), *InStr);
		return false;
	}
	
	if(InEditType == EPlaceRuleEditStrType::E_L_Express)
	{
		PlaceRuleCustomData.leftConfigExpression = OutExpression;
		PlaceRuleCustomData.leftConfig = OutValue;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_R_Express)
	{
		PlaceRuleCustomData.rightConfigExpression = OutExpression;
		PlaceRuleCustomData.rightConfig = OutValue;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_U_Express)
	{
		PlaceRuleCustomData.upperConfigExpression = OutExpression;
		PlaceRuleCustomData.upperConfig = OutValue;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_D_Express)
	{
		PlaceRuleCustomData.downConfigExpression = OutExpression;
		PlaceRuleCustomData.downConfig = OutValue;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_F_Express)
	{
		PlaceRuleCustomData.frontConfigExpression = OutExpression;
		PlaceRuleCustomData.frontConfig = OutValue;
	}
	else if(InEditType == EPlaceRuleEditStrType::E_B_Express)
	{
		PlaceRuleCustomData.afterConfigExpression = OutExpression;
		PlaceRuleCustomData.afterConfig = OutValue;
	}

	HasUserEdit = true;
	PlaceRuleCustomData.SetUserToCustom();

	return true;
}

void UPlaceRuleCustomWidget::CheckEditExecute(bool IsEditCheck, const EPlaceRuleEditCheckType& InCheckType)
{
	int32 CheckFlag = IsEditCheck ? 0 : 1;
	if(InCheckType == EPlaceRuleEditCheckType::E_L)
	{
		PlaceRuleCustomData.isLeft = CheckFlag;
	}
	else if(InCheckType == EPlaceRuleEditCheckType::E_R)
	{
		PlaceRuleCustomData.isRight = CheckFlag;
	}
	else if(InCheckType == EPlaceRuleEditCheckType::E_U)
	{
		PlaceRuleCustomData.isUpper = CheckFlag;
	}
	else if(InCheckType == EPlaceRuleEditCheckType::E_D)
	{
		PlaceRuleCustomData.isDown = CheckFlag;
	}
	else if(InCheckType == EPlaceRuleEditCheckType::E_F)
	{
		PlaceRuleCustomData.isFront = CheckFlag;
	}
	else if(InCheckType == EPlaceRuleEditCheckType::E_B)
	{
		PlaceRuleCustomData.isAfter = CheckFlag;
	}

	HasUserEdit = true;
	PlaceRuleCustomData.SetUserToCustom();
}
