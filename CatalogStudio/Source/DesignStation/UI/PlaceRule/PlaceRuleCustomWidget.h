// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DataCenter/RefFile/Data/RefToPlaceDataLibrary.h"
#include "CatalogExpression/Public/CaseSensitiveKeyFuncs.h"
#include "PlaceRuleCustomWidget.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(PlaceRuleCustomLog, Log, All);

UENUM(BlueprintType, Blueprintable)
enum class EPlaceRuleEditStrType : uint8
{
	E_L_Express = 0						UMETA(DisplayName = "左边表达式"),
	E_L_Value							UMETA(DisplayName = "左边值"),
	E_R_Express							UMETA(DisplayName = "右边表达式"),
	E_R_Value							UMETA(DisplayName = "右边值"),
	E_U_Express							UMETA(DisplayName = "上边表达式"),
	E_U_Value							UMETA(DisplayName = "上边值"),
	E_D_Express							UMETA(DisplayName = "下边表达式"),
	E_D_Value							UMETA(DisplayName = "下边值"),
	E_F_Express							UMETA(DisplayName = "前边表达式"),
	E_F_Value							UMETA(DisplayName = "前边值"),
	E_B_Express							UMETA(DisplayName = "后边表达式"),
	E_B_Value							UMETA(DisplayName = "后边值")
};

UENUM(BlueprintType, Blueprintable)
enum class EPlaceRuleEditCheckType : uint8
{
	E_L = 0								UMETA(DisplayName = "左边选择"),
	E_R									UMETA(DisplayName = "右边选择"),
	E_U									UMETA(DisplayName = "上边选择"),
	E_D									UMETA(DisplayName = "下边选择"),
	E_F									UMETA(DisplayName = "前边选择"),
	E_B									UMETA(DisplayName = "后边选择")
};

DECLARE_DELEGATE_OneParam(FModifyRefPlaceRuleCustomDelegate, FRefPlaceRuleCustomData);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UPlaceRuleCustomWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	static UPlaceRuleCustomWidget* Create();

	void UpdateParams(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParams, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InFileParams);
	void UpdateRuleData(const FRefPlaceRuleCustomData& InPlaceRuleCustomData);
	void UpdateRuleData(const FRefPlaceRuleData& InPlaceRuleData);

	void ResetEditFlag();

	UFUNCTION(BlueprintCallable, Category = "PlaceRuleCustom")
	FRefPlaceRuleCustomData GetPlaceRuleCustomData() const;

	UFUNCTION(BlueprintImplementableEvent, Category = "PlaceRuleCustom")
		void UpdateWidgetCustomPlaceRuleShow(const FRefPlaceRuleCustomData& InPlaceRuleCustomData);

	UFUNCTION(BlueprintCallable, Category = "PlaceRuleCustom")
		void StringDataChange(const FString& InStr, EPlaceRuleEditStrType InEditType);

	UFUNCTION(BlueprintCallable, Category = "PlaceRuleCustom")
		void CheckDataChange(const bool& InCheck, EPlaceRuleEditCheckType InCheckType);

	UFUNCTION(BlueprintCallable, Category = "PlaceRuleCustom")
		void OnExpressionClick(const FString& InEditStr, EPlaceRuleEditStrType InEditType);

	UFUNCTION(BlueprintCallable, Category = "PlaceRuleCustom")
		void OnCloseClickExecute();

	//bind to pop expression edit widget, handler
	UFUNCTION()
	void FolderOrFileExpressionEdit(const int32& EditType, const FString& Expression);

private:
	FString GetCurrentDataString(const EPlaceRuleEditStrType& InEditType);
	bool ValueEditExecute(const FString& InStr, const EPlaceRuleEditStrType& InEditType);
	bool ExpressionEditExecute(const FString& InStr, const EPlaceRuleEditStrType& InEditType);
	void CheckEditExecute(bool IsEditCheck, const EPlaceRuleEditCheckType& InCheckType);

public:
	FModifyRefPlaceRuleCustomDelegate ModifyRefPlaceRuleCustomDelegate;
	
private:
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  FileParameters;
	
	UPROPERTY()
	FRefPlaceRuleCustomData PlaceRuleCustomData;

	UPROPERTY()
	bool HasUserEdit;
	
	static FString FilePath;
};
