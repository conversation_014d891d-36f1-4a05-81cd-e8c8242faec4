// Fill out your copyright notice in the Description page of Project Settings.


#include "PlaceRuleSelectWidget.h"

FString UPlaceRuleSelectWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/MainUI/BP_PlaceRuleWidget.BP_PlaceRuleWidget_C'");

bool UPlaceRuleSelectWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	return true;
}

void UPlaceRuleSelectWidget::UpdatePlaceRuleShow(const int32& InID, const FString& InName, bool InCanConfig)
{
	RuleID = InID;
	ShowName = InName;
	bCanConfig = InCanConfig;
}

void UPlaceRuleSelectWidget::UpdatePlaceRuleShowByCombine(const FString& InCombineData)
{
	TArray<FString> StrArr;
	InCombineData.ParseIntoArray(StrArr, TEXT("/"));
	int32 ID = StrArr.IsValidIndex(0) ? FCString::Atoi(*StrArr[0]) : INDEX_NONE;
	FString Show = StrArr.IsValidIndex(1) ? StrArr[1] : TEXT("");
	bool CanConfig = StrArr.IsValidIndex(2) ? FCString::Atoi(*StrArr[0]) == 0 : true;
	UpdatePlaceRuleShow(ID, Show, CanConfig);
}
