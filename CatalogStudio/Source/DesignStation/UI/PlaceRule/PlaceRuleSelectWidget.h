// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PlaceRuleSelectWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UPlaceRuleSelectWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	//CombineStr : ID/Name/canConfig
	UFUNCTION(BlueprintCallable, Category = "PlaceRuleSelect")
		void UpdatePlaceRuleShowByCombine(const FString& InCombineData);

private:
	void UpdatePlaceRuleShow(const int32& InID, const FString& InName, bool InCanConfig);
	
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "PlaceRuleSelect")
	int32 RuleID;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "PlaceRuleSelect")
	FString ShowName;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "PlaceRuleSelect")
	bool bCanConfig;
	
	static FString FilePath;
};
