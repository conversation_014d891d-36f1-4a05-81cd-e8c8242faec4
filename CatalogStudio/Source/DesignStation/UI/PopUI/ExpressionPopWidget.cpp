// Fill out your copyright notice in the Description page of Project Settings.

#include "ExpressionPopWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "DesignStation/UI/Expression/ExpressionMultiLineText.h"
#include "DesignStation/UI/Expression/ExpressionQuickInputWidget.h"

FString UExpressionPopWidget::ExpressionPopWidgetPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/ExpressionPopUI.ExpressionPopUI_C'");
UExpressionPopWidget* UExpressionPopWidget::ExpressionPopInstance = nullptr;

extern const int PopUIZOrder;
extern const int ExpressionZOrder = 15;

bool UExpressionPopWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	
	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, UExpressionPopWidget::OnClickedBtnClose);
	BIND_PARAM_CPP_TO_UMG(MultiEdtExpression, MultiEdt_Expression);
	BIND_WIDGET_FUNCTION(MultiEdtExpression, OnTextChanged, UExpressionPopWidget::OnTextChangedMultiEdtExpression);
	BIND_WIDGET_FUNCTION(MultiEdtExpression, OnTextCommitted, UExpressionPopWidget::OnTextCommittedMultiEdtExpression);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, UExpressionPopWidget::OnClickedBtnCancel);
	BIND_PARAM_CPP_TO_UMG(BtnSure, Btn_Sure);
	BIND_WIDGET_FUNCTION(BtnSure, OnClicked, UExpressionPopWidget::OnClickedBtnSure);

	ExpressionQuickInputWidget->GetExpressionQuickInputStr.BindDynamic(this, &ThisClass::OnGetExpressionQuickInputStr);
	return true;
}

void UExpressionPopWidget::UpdateContent(const int32& InEditType, const FString& InExpression)
{
	EditType = InEditType;
	if (MultiEdtExpression)
	{
		MultiEdtExpression->SetText(FText::FromString(InExpression));
	}
}

UExpressionPopWidget * UExpressionPopWidget::Create()
{
	UClass* ExpressionPopBp = LoadClass<UUserWidget>(NULL, *ExpressionPopWidgetPath);
	checkf(ExpressionPopBp, TEXT("load expression pop bp error!"));
	UExpressionPopWidget* ExpressionPopWidget = CreateWidget<UExpressionPopWidget>(GWorld.GetReference(), ExpressionPopBp);
	checkf(ExpressionPopWidget, TEXT("create expression widget error!"));
	return ExpressionPopWidget;
}

UExpressionPopWidget * UExpressionPopWidget::Get()
{
	if (!IS_OBJECT_PTR_VALID(UExpressionPopWidget::ExpressionPopInstance))
	{
		UExpressionPopWidget::ExpressionPopInstance = UUIFunctionLibrary::CreateUIWidget<UExpressionPopWidget>();
		UExpressionPopWidget::ExpressionPopInstance->AddToViewport(ExpressionZOrder);
	}
	return UExpressionPopWidget::ExpressionPopInstance;
}

void UExpressionPopWidget::NativeOnInitialized()
{
}

void UExpressionPopWidget::UnbindDelegates()
{
	if (ExpressionDelegate.IsBound())
	{
		ExpressionDelegate.Unbind();
	}
}

void UExpressionPopWidget::OnClickedBtnClose()
{
	UnbindDelegates();
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
}

void UExpressionPopWidget::OnTextChangedMultiEdtExpression(const FText & Text)
{
}

void UExpressionPopWidget::OnTextCommittedMultiEdtExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::OnCleared && MultiEdtExpression)
	{
		MultiEdtExpression->SetText(Text);
	}
}

void UExpressionPopWidget::OnClickedBtnSure()
{
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
	if (MultiEdtExpression)
	{
		ExpressionDelegate.ExecuteIfBound(EditType, MultiEdtExpression->GetText().ToString());
		MultiEdtExpression->SetText(FText::GetEmpty());
	}
	//OnClickedBtnClose();
	/*UnbindDelegates();
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);*/
}

void UExpressionPopWidget::OnGetExpressionQuickInputStr(const FText& InQuickInputText)
{
	MultiEdtExpression->SetFocus();
	FTextLocation TextLocation = MultiEdtExpression->GetCursorLocation();
	MultiEdtExpression->InsertByCursorLocation(InQuickInputText, TextLocation);

}

void UExpressionPopWidget::OnClickedBtnCancel()
{
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);
	if (MultiEdtExpression)
	{
		MultiEdtExpression->SetText(FText::GetEmpty());
	}
	//OnClickedBtnClose();
	/*UnbindDelegates();
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Collapsed);*/
}
