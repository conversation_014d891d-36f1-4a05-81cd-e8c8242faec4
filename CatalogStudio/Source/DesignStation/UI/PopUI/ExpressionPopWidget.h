// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ExpressionPopWidget.generated.h"

/**
 * 
 */

class UExpressionMultiLineText;
class UExpressionQuickInputWidget;
class UButton;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FExpressionDelegate, const int32&, EditType, const FString&, OutExpression);

UCLASS()
class DESIGNSTATION_API UExpressionPopWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const int32& InEditType, const FString& InExpression);

	static UExpressionPopWidget* Create();
	static UExpressionPopWidget* Get();

protected:
	virtual void NativeOnInitialized() override;

public:
	FExpressionDelegate ExpressionDelegate;

private:
	void UnbindDelegates();

private:
	int32 EditType;

	static FString ExpressionPopWidgetPath;
	static UExpressionPopWidget* ExpressionPopInstance;

protected:
	UFUNCTION()
		void OnClickedBtnClose();
	UFUNCTION()
		void OnTextChangedMultiEdtExpression(const FText& Text);
	UFUNCTION()
		void OnTextCommittedMultiEdtExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnCancel();
	UFUNCTION()
		void OnClickedBtnSure();

	UFUNCTION()
	void OnGetExpressionQuickInputStr(const FText& InQuickInputText);
protected:
	UPROPERTY(meta = (BindWidget))
	UExpressionQuickInputWidget* ExpressionQuickInputWidget;

private:
	UPROPERTY()
		UButton* BtnClose;
	UPROPERTY()
		UExpressionMultiLineText* MultiEdtExpression;
	UPROPERTY()
		UButton* BtnCancel;
	UPROPERTY()
		UButton* BtnSure;
};
