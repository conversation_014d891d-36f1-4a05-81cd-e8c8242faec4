// Fill out your copyright notice in the Description page of Project Settings.

#include "SOneButtonWidget.h"
#include "SlateOptMacros.h"
#include "Runtime/Slate/Public/Widgets/Input/SButton.h"
#include "Runtime/Slate/Public/Widgets/Text/STextBlock.h"
#include "Runtime/SlateCore/Public/Widgets/SBoxPanel.h"
#include "DesignStation/UI/Styles/GeneralStyle.h"
#include "DesignStation/UI/Styles/GeneralThemeWidgetStyle.h"
#include "Runtime/SlateCore/Public/Widgets/SWindow.h"
#include "Runtime/Slate/Public/Framework/Application/SlateApplication.h"
#include "Runtime/Slate/Public/Widgets/Layout/SWindowTitleBarArea.h"

BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION
void SOneButtonWidget::Construct(const FArguments& InArgs)
{
	WidgetWindow = InArgs._WidgetWindow;
	IsButtonClicked = false;

	const struct FGeneralThemeStyle* MainPageStyle = &FGeneralStyle::Get().GetWidgetStyle<FGeneralThemeStyle>("GeneralThemeStyle");

	ChildSlot
		[
			SNew(SVerticalBox)
			+ SVerticalBox::Slot()
		.AutoHeight()
		[
			SNew(SBox)
			.HeightOverride(30)
		[

			SNew(SBorder)
			.BorderImage(&MainPageStyle->TitleImage)
		.Content()
		[
			SNew(SWindowTitleBarArea)
			.Content()
		[

			SNew(SOverlay)
			+ SOverlay::Slot()
		.HAlign(EHorizontalAlignment::HAlign_Right)
		[
			SNew(SHorizontalBox)
			/*+ SHorizontalBox::Slot()
		.AutoWidth()
		.HAlign(EHorizontalAlignment::HAlign_Right)
		.Padding(FMargin(0.0, 0.0, 0.0, 0))
		[
			SNew(SButton)
			.ButtonStyle(&MainPageStyle->MinButtonStyle)
		.OnClicked_Lambda([this] {return this->OnClickTitleButton(ETitleButtonTypeOne::Minimum); })
		]*/
	+ SHorizontalBox::Slot()
		.AutoWidth()
		.HAlign(EHorizontalAlignment::HAlign_Right)
		.Padding(FMargin(0.0, 0.0, 0.0, 0))
		[
			SNew(SButton)
			.ButtonStyle(&MainPageStyle->CloseButtonStyle)
		.OnClicked_Lambda([this] {return this->OnClickTitleButton(ETitleButtonTypeOne::Close); })
		]

		]
	+ SOverlay::Slot()
		.Padding(FMargin(0.0, 5, 0.0, 0.0))
		[

			SAssignNew(ContentTextblock, STextBlock)
			.TextStyle(&MainPageStyle->TitleTextStyle)
		.Text(InArgs._TitleText)
		.Justification(ETextJustify::Center)
		.Visibility(EVisibility::HitTestInvisible)
		]
		]

		]
		]
		]
	+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(FMargin(10, 42, 10, 0.0))
		[
			SAssignNew(ContentTextblock, STextBlock)
			.TextStyle(&MainPageStyle->GeneralContentTextStyle)
		.Text(InArgs._ContentText)
		.Justification(ETextJustify::Center)
		]
	+ SVerticalBox::Slot()
		.AutoHeight()
		.HAlign(EHorizontalAlignment::HAlign_Center)
		.Padding(FMargin(0.0, 35, 0.0, 17))
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
		.HAlign(EHorizontalAlignment::HAlign_Center)
		.Padding(FMargin(0.0, 0.0, 0.0, 0))
		[
			SNew(SOverlay)
			+ SOverlay::Slot()
		[
			SNew(SButton)
			.ButtonStyle(&MainPageStyle->SureButtonStyle)
		.OnClicked_Lambda([this] {return this->OnClickTitleButton(ETitleButtonTypeOne::Sure); })
		]
	+ SOverlay::Slot()
		[
			SNew(STextBlock)
			.TextStyle(&MainPageStyle->ButtonlTextStyle)
		.Text(InArgs._Button1Text)
		.Justification(ETextJustify::Center)
		.Margin(FMargin(0, 4, 0, 4))
		.Visibility(EVisibility::HitTestInvisible)
		]
		]
		]
		];

}
END_SLATE_FUNCTION_BUILD_OPTIMIZATION

bool SOneButtonWidget::PopupModalWindow(const FText& InTitle, const FText& InContent, const FText& InButtton1Text)
{
	const struct FGeneralThemeStyle* MainPageStyle = &FGeneralStyle::Get().GetWidgetStyle<FGeneralThemeStyle>("GeneralThemeStyle");

	TSharedPtr<SWindow> PopWindow = SNew(SWindow)
		.Style(&MainPageStyle->GeneralWindowStyle)
		.Type(EWindowType::GameWindow)
		.SizingRule(ESizingRule::Autosized)
		.SupportsMinimize(false)
		.SupportsMaximize(false)
		.CreateTitleBar(false)
		.LayoutBorder(FMargin(0, 0, 0, 0));
	TSharedPtr<SOneButtonWidget> SelectWidget = SNew(SOneButtonWidget)
		.WidgetWindow(PopWindow)
		.TitleText(InTitle)
		.ContentText(InContent)
		.Button1Text(InButtton1Text);
	PopWindow->SetContent(SelectWidget.ToSharedRef());
	TSharedPtr<SWindow> RootWindow = FSlateApplication::Get().GetActiveTopLevelWindow();
	if (RootWindow.IsValid())
	{
		FSlateApplication::Get().AddModalWindow(PopWindow.ToSharedRef(), RootWindow.ToSharedRef());
		return SelectWidget->GetIsButtonClicked();
	}
	return false;
}

FReply SOneButtonWidget::OnClickTitleButton(const ETitleButtonTypeOne& BtnType)
{
	if (WidgetWindow.IsValid())
	{
		switch (BtnType)
		{
		//case ETitleButtonTypeOne::Minimum:
		//	WidgetWindow.Pin()->Minimize();
		//	break;
		case ETitleButtonTypeOne::Close:
			WidgetWindow.Pin()->RequestDestroyWindow();
			break;
		case ETitleButtonTypeOne::Sure:
			WidgetWindow.Pin()->RequestDestroyWindow();
			break;
		default:
			break;
		}
	}
	return FReply::Handled();
}