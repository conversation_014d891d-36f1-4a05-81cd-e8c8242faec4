// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Runtime/Slate/Public/Widgets/Text/STextBlock.h"

UENUM(BlueprintType)
enum class ETitleButtonTypeOne :uint8
{
	None,
	Minimum,
	Close,
	Sure
};


/**
 * 
 */
class DESIGNSTATION_API SOneButtonWidget : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SOneButtonWidget)
		:_WidgetWindow()
		, _TitleText()
		, _ContentText()
		, _Button1Text()
	{}
	SLATE_ARGUMENT(TSharedPtr<SWindow>, WidgetWindow)
	SLATE_ARGUMENT(FText, TitleText)
	SLATE_ARGUMENT(FText, ContentText)
	SLATE_ARGUMENT(FText, Button1Text)
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);

	bool GetIsButtonClicked() const { return IsButtonClicked; }

	static bool PopupModalWindow(const FText& InTitle, const FText& InContent, const FText& InButtton1Text);

private:
	TWeakPtr< SWindow > WidgetWindow;
	TSharedPtr<STextBlock> ContentTextblock;
	bool IsButtonClicked;
	FReply OnClickTitleButton(const ETitleButtonTypeOne& BtnType);
};
