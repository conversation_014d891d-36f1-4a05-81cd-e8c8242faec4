// Fill out your copyright notice in the Description page of Project Settings.

#include "SPlanTypeSelectWidget.h"
#include "SlateOptMacros.h"
#include "Runtime/SlateCore/Public/Widgets/SBoxPanel.h"
#include "Runtime/Slate/Public/Widgets/Input/SCheckBox.h"
#include "Runtime/Slate/Public/Widgets/Input/SButton.h"
#include "Runtime/Slate/Public/Widgets/Text/STextBlock.h"
#include "DesignStation/UI/Styles/GeneralStyle.h"
#include "DesignStation/UI/Styles/GeneralThemeWidgetStyle.h"
#include "Runtime/SlateCore/Public/Widgets/SWindow.h"
#include "Runtime/Slate/Public/Framework/Application/SlateApplication.h"


BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION
void SPlanTypeSelectWidget::Construct(const FArguments& InArgs)
{
	SlectedPlan = ESelectPlanType::Type::XY;
	WidgetWindow = InArgs._WidgetWindow;
	IsConfirm = false;
	ESelectPlanType::Type FreezePlanType = static_cast<ESelectPlanType::Type>(InArgs._FreezePlanType);

	const struct FGeneralThemeStyle* MainPageStyle = &FGeneralStyle::Get().GetWidgetStyle<FGeneralThemeStyle>("GeneralThemeStyle");

	ChildSlot
	[
		SNew(SVerticalBox)

		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(FMargin(0.0, 30.0, 0.0, 0.0))
		.HAlign(EHorizontalAlignment::HAlign_Center)
		.VAlign(EVerticalAlignment::VAlign_Fill)
		[
			SNew(SHorizontalBox)

			+ SHorizontalBox::Slot()
			.AutoWidth()
			.HAlign(EHorizontalAlignment::HAlign_Left)
			.VAlign(EVerticalAlignment::VAlign_Center)
			[
				SNew(SVerticalBox)

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(SButton)
					.IsEnabled(ESelectPlanType::Type::XY != FreezePlanType)
					.ButtonStyle(&MainPageStyle->XYPlanButtonStyle)
					.OnClicked_Lambda([&] {return this->HandleRadioButtonClicked(ESelectPlanType::Type::XY); })
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(STextBlock)
					.TextStyle(&MainPageStyle->GeneralButtonTextStyle)
					.Text(FText::FromString(TEXT("XY")))
					.Justification(ETextJustify::Center)
				]
			]

			+ SHorizontalBox::Slot()
			.AutoWidth()
			.HAlign(EHorizontalAlignment::HAlign_Left)
			.VAlign(EVerticalAlignment::VAlign_Center)
			.Padding(FMargin(15.0,0.0,15.0,0.0))
			[
				SNew(SVerticalBox)

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(SButton)
					.IsEnabled(ESelectPlanType::Type::YZ != FreezePlanType)
					.ButtonStyle(&MainPageStyle->YZPlanButtonStyle)
					.OnClicked_Lambda([&] {return this->HandleRadioButtonClicked(ESelectPlanType::Type::YZ); })
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(STextBlock)
					.TextStyle(&MainPageStyle->GeneralButtonTextStyle)
					.Text(FText::FromString(TEXT("YZ")))
					.Justification(ETextJustify::Center)
				]
			]

			+ SHorizontalBox::Slot()
			.AutoWidth()
			.HAlign(EHorizontalAlignment::HAlign_Left)
			.VAlign(EVerticalAlignment::VAlign_Center)
			[
				SNew(SVerticalBox)

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(SButton)
					.IsEnabled(ESelectPlanType::Type::XZ != FreezePlanType)
					.ButtonStyle(&MainPageStyle->XZPlanButtonStyle)
					.OnClicked_Lambda([&] {return this->HandleRadioButtonClicked(ESelectPlanType::Type::XZ); })
				]

				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(STextBlock)
					.TextStyle(&MainPageStyle->GeneralButtonTextStyle)
					.Text(FText::FromString(TEXT("XZ")))
					.Justification(ETextJustify::Center)
				]
			]
		]
	];
	
}
END_SLATE_FUNCTION_BUILD_OPTIMIZATION

FReply SPlanTypeSelectWidget::HandleRadioButtonClicked(ESelectPlanType::Type Choice)
{
	IsConfirm = true;
	SlectedPlan = Choice;
	if (WidgetWindow.IsValid())
	{
		WidgetWindow.Pin()->RequestDestroyWindow();
	}
	return FReply::Handled();
}

ESelectPlanType::Type SPlanTypeSelectWidget::PopupModalWindow(const FText& InTitle, ESelectPlanType::Type FreezePlanType)
{
	const struct FGeneralThemeStyle* MainPageStyle = &FGeneralStyle::Get().GetWidgetStyle<FGeneralThemeStyle>("GeneralThemeStyle");
	TSharedPtr<SWindow> PopWindow = SNew(SWindow)
		.Style(&MainPageStyle->GeneralWindowStyle)
		.SizingRule(ESizingRule::Autosized)
		.SupportsMinimize(false)
		.SupportsMaximize(false)
		.Title(InTitle)
		.CreateTitleBar(true)
		.HasCloseButton(true);
	TSharedPtr<SPlanTypeSelectWidget> SelectWidget = SNew(SPlanTypeSelectWidget).WidgetWindow(PopWindow).FreezePlanType(static_cast<int32>(FreezePlanType));
	PopWindow->SetContent(SelectWidget.ToSharedRef());
	TSharedPtr<SWindow> RootWindow = FSlateApplication::Get().GetActiveTopLevelWindow();
	if (RootWindow.IsValid())
	{
		FSlateApplication::Get().AddModalWindow(PopWindow.ToSharedRef(), RootWindow.ToSharedRef());
		if (SelectWidget->GetIsConfirm())
		{
			return SelectWidget->GetSelectedPlan();
		}
	}
	return ESelectPlanType::Type::None;
}