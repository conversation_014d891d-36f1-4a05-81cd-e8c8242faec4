// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Runtime/SlateCore/Public/Styling/SlateTypes.h"

namespace ESelectPlanType
{
	enum Type
	{
		None,
		XY,
		YZ,
		XZ
	};
}

/**
 * 
 */
class DESIGNSTATION_API SPlanTypeSelectWidget : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SPlanTypeSelectWidget)
		:_WidgetWindow()
		,_FreezePlanType(0)
	{}
	SLATE_ARGUMENT(TSharedPtr<SWindow>, WidgetWindow)
	SLATE_ARGUMENT(int32, FreezePlanType)
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);

	ESelectPlanType::Type GetSelectedPlan() const { return SlectedPlan; }
	bool GetIsConfirm() const { return IsConfirm; }

	static ESelectPlanType::Type PopupModalWindow(const FText& InTitle, ESelectPlanType::Type FreezePlanType = ESelectPlanType::Type::None);

private:

	ESelectPlanType::Type SlectedPlan;
	TWeakPtr< SWindow > WidgetWindow;
	bool IsConfirm;

private:

	FReply HandleRadioButtonClicked(ESelectPlanType::Type Choice);
};
