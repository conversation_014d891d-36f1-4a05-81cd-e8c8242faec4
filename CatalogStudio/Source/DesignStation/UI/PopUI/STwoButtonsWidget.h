// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Runtime/Slate/Public/Widgets/Input/SButton.h"
#include "Runtime/Slate/Public/Widgets/Text/STextBlock.h"



//namespace EButtonIndex
//{
//	enum Type
//	{
//		None,
//		Button1,
//		Button2
//	};
//}

UENUM(BlueprintType)
enum class EPopButtonType :uint8
{
	None,
	Confirm,
	Cancel,
	Minimum,
	Close
};


/**
 * 
 */
class DESIGNSTATION_API STwoButtonsWidget : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(STwoButtonsWidget)
		:_WidgetWindow()
		,_TitleText()
		,_ContentText()
		,_Button1Text()
		,_Button2Text()
	{}
	SLATE_ARGUMENT(TSharedPtr<SWindow>, WidgetWindow)
	SLATE_ARGUMENT(FText, TitleText)
	SLATE_ARGUMENT(FText, ContentText)
	SLATE_ARGUMENT(FText, Button1Text)
	SLATE_ARGUMENT(FText, Button2Text)
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);

	void SetContentText(const FText& InText);

	EPopButtonType GetClickedButton() const { return ClickedButton; }

	static EPopButtonType PopupModalWindow(const FText& InTitle, const FText& InContent, const FText& InButtton1Text, const FText& InButton2Text);

private:

	EPopButtonType ClickedButton;

	TSharedPtr<STextBlock> ContentTextblock;
	TWeakPtr< SWindow > WidgetWindow;

private:

	//FReply OnClickButtonHandler(EButtonIndex::Type InButton);
	FReply OnClickedButtonHandler(const EPopButtonType& BtnType);
};
