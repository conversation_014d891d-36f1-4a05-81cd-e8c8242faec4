// Fill out your copyright notice in the Description page of Project Settings.

#include "ToolTipWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"

FString UToolTipWidget::ToolTipPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/ToolTipUI.ToolTipUI_C'");

bool UToolTipWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	/*BIND_PARAM_CPP_TO_UMG(ImgBackground, Img_Background);*/
	BIND_PARAM_CPP_TO_UMG(TxtToolTip, Txt_ToolTip);

	return true;
}

void UToolTipWidget::UpdateContent(const FText& InToolTip)
{
	if (IS_OBJECT_PTR_VALID(TxtToolTip))
	{
		TxtToolTip->SetText(InToolTip);
	}
}

UToolTipWidget * UToolTipWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UToolTipWidget>(UToolTipWidget::ToolTipPath);
}
