// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ToolTipWidget.generated.h"

/**
 * 
 */

class UImage;
class UTextBlock;

UCLASS()
class DESIGNSTATION_API UToolTipWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FText& InToolTip);

	static UToolTipWidget* Create();

private:
	static FString ToolTipPath;
	
private:
	UPROPERTY()
		UImage* ImgBackground;
	UPROPERTY()
		UTextBlock* TxtToolTip;
};
