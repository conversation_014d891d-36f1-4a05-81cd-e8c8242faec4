// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_SelectSection.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Border.h"


FString UWidget_SelectSection::WidgetSelectSectionPath = TEXT("WidgetBlueprint'/Game/UI/PopUI/Widget_SelectSection.Widget_SelectSection_C'");

bool UWidget_SelectSection::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnSelectSure, Btn_SelectSure);
	BIND_PARAM_CPP_TO_UMG(BtnSelectClose, Btn_SelectClose);
	BIND_PARAM_CPP_TO_UMG(BtnSideview,Btn_Sideview);
	BIND_PARAM_CPP_TO_UMG(BtnVerticalview, Btn_Verticalview);
	BIND_PARAM_CPP_TO_UMG(BtnFrontview, Btn_Frontview);
	BIND_PARAM_CPP_TO_UMG(BorSideview, Bor_Sideview);
	BIND_PARAM_CPP_TO_UMG(BorVerticalview, Bor_Verticalview);
	BIND_PARAM_CPP_TO_UMG(BorFrontview, Bor_Frontview);
	
	BIND_WIDGET_FUNCTION(BtnSelectSure, OnClicked, UWidget_SelectSection::OnClickedBtnSelectSure);
	BIND_WIDGET_FUNCTION(BtnSelectClose, OnClicked, UWidget_SelectSection::OnClickedBtnSelectClose);
	BIND_WIDGET_FUNCTION(BtnSideview, OnClicked, UWidget_SelectSection::OnClickedBtnSideview);
	BIND_WIDGET_FUNCTION(BtnVerticalview, OnClicked, UWidget_SelectSection::OnClickedBtnVerticalview);
	BIND_WIDGET_FUNCTION(BtnFrontview, OnClicked, UWidget_SelectSection::OnClickedBtnFrontview);

	ResetState();

	return true;
}

void UWidget_SelectSection::ResetState()
{
	if (SelectView != ESelectView::NoSelect)
	{
		UUIFunctionLibrary::SetBorderBrushColor(ViewSelectDefaultColor, GetBorder(SelectView));
		SelectView = ESelectView::NoSelect;
	}
	BtnSelectSure->SetIsEnabled(false);
	
}

UWidget_SelectSection * UWidget_SelectSection::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UWidget_SelectSection>(UWidget_SelectSection::WidgetSelectSectionPath);
}

void	UWidget_SelectSection::OnSelectView(const ESelectView& Select)
{
	UE_LOG(LogTemp, Log, TEXT("SELECT"));
	BtnSelectSure->SetIsEnabled(true);
	if (SelectView == Select)
		return;
	UUIFunctionLibrary::SetBorderBrushColor(ViewSelectDefaultColor,GetBorder(SelectView));
	SelectView = Select;
	UUIFunctionLibrary::SetBorderBrushColor(ViewSelectColor,GetBorder(SelectView));

}

UBorder*	UWidget_SelectSection::GetBorder(const ESelectView& BorView)
{
	return (BorView == ESelectView::Sideview) ? (BorSideview) : ((BorView == ESelectView::Verticalview) ? (BorVerticalview) : (BorFrontview));
}

void	UWidget_SelectSection::OnClickedBtnSideview()
{
	UE_LOG(LogTemp, Log, TEXT("SELECT1"));
	OnSelectView(ESelectView::Sideview);
}

void	UWidget_SelectSection::OnClickedBtnVerticalview()
{
	UE_LOG(LogTemp, Log, TEXT("SELECT2"));
	OnSelectView(ESelectView::Verticalview);
}

void	UWidget_SelectSection::OnClickedBtnFrontview()
{
	UE_LOG(LogTemp, Log, TEXT("SELECT3"));
	OnSelectView(ESelectView::Frontview);
}

void	UWidget_SelectSection::OnClickedBtnSelectSure()
{
	SelectSureDelegate.ExecuteIfBound(static_cast<int32>(SelectView));
	this->SetVisibility(ESlateVisibility::Collapsed);
	ResetState();
}

void	UWidget_SelectSection::OnClickedBtnSelectClose()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
	ResetState();
	//SelectCloseDelegate.ExecuteIfBound(true);
}


