// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Widget_SelectSection.generated.h"

/**
 * 
 */

class	UButton;
class	UBorder;


UENUM(BlueprintType)
enum class ESelectView:uint8
{
	NoSelect = 0,
	Sideview,
	Verticalview,
	Frontview
};

const FLinearColor ViewSelectColor = FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.2f);
const FLinearColor ViewSelectDefaultColor = FLinearColor(1.0f, 1.0f, 1.0f, 0.2f);


DECLARE_DYNAMIC_DELEGATE_OneParam(FSelectSureDelegate, const int32&, ViewSelect);
//DECLARE_DYNAMIC_DELEGATE_OneParam(FSelectCloseDelegate, bool, IsSure);



UCLASS()
class DESIGNSTATION_API UWidget_SelectSection : public UUserWidget
{
	GENERATED_BODY()
	
public:
	
	virtual bool Initialize() override;
	void ResetState();

	static UWidget_SelectSection * Create();

protected:

	void	OnSelectView(const ESelectView& Select);

	UBorder*	GetBorder(const ESelectView& BorView);

	UFUNCTION()
		void	OnClickedBtnSelectSure();
	UFUNCTION()
		void	OnClickedBtnSelectClose();
	UFUNCTION()
		void	OnClickedBtnSideview();
	UFUNCTION()
		void	OnClickedBtnVerticalview();
	UFUNCTION()
		void	OnClickedBtnFrontview();


private:
	UPROPERTY()
		UButton*	BtnSelectSure;
	UPROPERTY()
		UButton*	BtnSelectClose;
	UPROPERTY()
		UButton*	BtnSideview;
	UPROPERTY()
		UButton*	BtnVerticalview;
	UPROPERTY()
		UButton*	BtnFrontview;
	UPROPERTY()
		UBorder*	BorSideview;
	UPROPERTY()
		UBorder*	BorVerticalview;
	UPROPERTY()
		UBorder*	BorFrontview;

private:
	static FString WidgetSelectSectionPath;
public:
	FSelectSureDelegate		SelectSureDelegate;
	//FSelectCloseDelegate	SelectCloseDelegate;
	
	ESelectView	SelectView;
};
