// Fill out your copyright notice in the Description page of Project Settings.


#include "ReleaseDetailInnerWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"

FString UReleaseDetailInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Release/BP_ReleaseDetailInnerWidget.BP_ReleaseDetailInnerWidget_C'");

bool UReleaseDetailInnerWidget::Initialize()
{
	return Super::Initialize();
}

void UReleaseDetailInnerWidget::Init(const FReleaseDetail& InInfo)
{
	DetailInfo = InInfo;

	UpdateUIShow(DetailInfo);
}

UReleaseDetailInnerWidget* UReleaseDetailInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UReleaseDetailInnerWidget>(UReleaseDetailInnerWidget::FilePath);
}
