// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/Release/ReleaseLogData.h"
#include "ReleaseDetailInnerWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UReleaseDetailInnerWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void Init(const FReleaseDetail& InInfo);

	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseDetailInner")
	void UpdateUIShow(const FReleaseDetail& InInfo);


public:
	static UReleaseDetailInnerWidget* Create();

private:
	UPROPERTY()
	FReleaseDetail DetailInfo;

	static FString FilePath;
};

