// Fill out your copyright notice in the Description page of Project Settings.


#include "ReleaseDetailWidget.h"
#include "ReleaseDetailInnerWidget.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UReleaseDetailWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Release/BP_ReleaseDetailWidget.BP_ReleaseDetailWidget_C'");
UReleaseDetailWidget* UReleaseDetailWidget::Instance = nullptr;

bool UReleaseDetailWidget::Initialize()
{
	BindDelegate();

	return Super::Initialize();
}

void UReleaseDetailWidget::InitDetail(const int32& InMainID)
{
	DetailUUID = UCatalogNetworkSubsystem::GetInstance()->SendGetReleaseDetailInfoRequest(InMainID);

	SetVisibility(ESlateVisibility::Visible);
}

void UReleaseDetailWidget::OnClose()
{
	this->SetVisibility(ESlateVisibility::Collapsed);

	ClearContent();
}

void UReleaseDetailWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->ReleaseDetailResponseDelegate.AddUniqueDynamic(this, &UReleaseDetailWidget::OnGetReleaseDetailResponseHandler);
}

void UReleaseDetailWidget::OnGetReleaseDetailResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FReleaseDetail>& Data)
{
	if (UUID.Equals(DetailUUID))
	{
		if (bSuccess)
		{
			ClearContent();

			for (const auto DetaiData : Data)
			{
				UReleaseDetailInnerWidget* InnerWidget = UReleaseDetailInnerWidget::Create();
				InnerWidget->Init(DetaiData);
				AddContentItem(InnerWidget);
			}
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

UReleaseDetailWidget* UReleaseDetailWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UReleaseDetailWidget>(UReleaseDetailWidget::FilePath);
}

UReleaseDetailWidget* UReleaseDetailWidget::GetInstance()
{
	if (UReleaseDetailWidget::Instance == nullptr)
	{
		UReleaseDetailWidget::Instance = UReleaseDetailWidget::Create();
		UReleaseDetailWidget::Instance->AddToViewport(100);
		UReleaseDetailWidget::Instance->AddToRoot();
		UReleaseDetailWidget::Instance->SetVisibility(ESlateVisibility::Collapsed);
	}
	return UReleaseDetailWidget::Instance;
}
