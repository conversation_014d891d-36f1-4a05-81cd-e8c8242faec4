// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ReleaseDetailWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UReleaseDetailWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void InitDetail(const int32& InMainID);

	//content item 
	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseDetail")
	void ClearContent();
	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseDetail")
	void AddContentItem(UUserWidget* InWidget);

	UFUNCTION(BlueprintCallable, Category = "ReleaseDetail")
	void OnClose();

private: 
	//network
	void BindDelegate();

	UFUNCTION()
	void OnGetReleaseDetailResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FReleaseDetail>& Data);

private:
	UPROPERTY()
	FString DetailUUID = TEXT("NoWay");

public:

	static UReleaseDetailWidget* Create();
	static UReleaseDetailWidget* GetInstance();

private:
	static FString FilePath;
	
	static UReleaseDetailWidget* Instance;
};
