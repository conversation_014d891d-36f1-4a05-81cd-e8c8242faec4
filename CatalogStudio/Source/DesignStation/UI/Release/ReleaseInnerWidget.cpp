// Fill out your copyright notice in the Description page of Project Settings.

#include "ReleaseInnerWidget.h"

#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"

FString UReleaseInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Release/BP_ReleaseInnerWidget.BP_ReleaseInnerWidget_C'");

bool UReleaseInnerWidget::Initialize()
{
	return Super::Initialize();
}

void UReleaseInnerWidget::LoadImage(const FString& RelativePath)
{
	const FString& AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), RelativePath));
	if (FPaths::FileExists(AbsPath))
	{
		UTexture2D* Img = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);
		if (Img)
		{
			ShowImage(Img);
		}
	}
}

void UReleaseInnerWidget::OnSelectOrUnSelect(FString ItemID, bool bSelect)
{
	SelectOrUnSelectDelegate.ExecuteIfBound(ItemID, bSelect);
}

UReleaseInnerWidget* UReleaseInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UReleaseInnerWidget>(UReleaseInnerWidget::FilePath);

}

void UReleaseInnerWidget::BindDelegate()
{
}

void UReleaseInnerWidget::OnDownloadImageResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& FilePath)
{
}
