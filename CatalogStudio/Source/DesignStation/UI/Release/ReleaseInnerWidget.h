// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "ReleaseInnerWidget.generated.h"

/*
*  @@ Delegate to select or unselect the item
*  @@ int64 : the item id
*  @@ bool : true to select, false to unselect
*/
DECLARE_DELEGATE_TwoParams(FSelectOrUnSelectDelegate, FString, bool)

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UReleaseInnerWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseInner")
	void InitUI(const FRefDirectoryData& InData);
	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseInner")
	void ShowImage(UTexture2D* Img);

	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseInner")
	void SetSelect(bool bSelect);

	UFUNCTION(BlueprintCallable, Category = "ReleaseInner")
	void LoadImage(const FString& RelativePath);

	UFUNCTION(BlueprintCallable, Category = "ReleaseInner")
	void OnSelectOrUnSelect(FString ItemID, bool bSelect);

	static UReleaseInnerWidget* Create();

public:
	UPROPERTY(BlueprintReadWrite, Category = "ReleaseInnerWidget")
	FRefDirectoryData Data;

private:
	void BindDelegate();

	UFUNCTION()
	void OnDownloadImageResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& FilePath);

public:
	FSelectOrUnSelectDelegate SelectOrUnSelectDelegate;

private:
	static FString FilePath;
	
};
