// Fill out your copyright notice in the Description page of Project Settings.

#include "ReleaseWidget.h"

#include "ReleaseInnerWidget.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"


FString UReleaseWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/Release/BP_ReleaseWidget.BP_ReleaseWidget_C'");
UReleaseWidget* UReleaseWidget::Instance = nullptr;

bool UReleaseWidget::Initialize()
{
	BindDelegate();

	return Super::Initialize();
}

void UReleaseWidget::Init(const TArray<FRefDirectoryData>& InDatas)
{
	Clear();

	for (const auto Data : InDatas)
	{
		UReleaseInnerWidget* InnerWidget = UReleaseInnerWidget::Create();
		InnerWidget->InitUI(Data);
		InnerWidget->SelectOrUnSelectDelegate.BindUFunction(this, FName("OnSelectOrUnSelect"));
		InnerWidget->SetVisibility(ESlateVisibility::Visible);
		AddWidget(InnerWidget);
		DirectoryDatas.Add(Data.id, Data);
		InnerWidgets.Add(InnerWidget);
	}
}

void UReleaseWidget::UpdateContent()
{
	SendGetReleaseDataRequest();
}

void UReleaseWidget::Clear()
{
	ClearWidgets();

	DirectoryDatas.Empty();

	InnerWidgets.Empty();

	SelectedItemIDs.Empty();
}

void UReleaseWidget::BindDelegate()
{
	UCatalogNetworkSubsystem::GetInstance()->ReleaseInfosResponseDelegate.AddUniqueDynamic(this, &UReleaseWidget::OnGetReleaseDataResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->ReleaseSelectResponseDelegate.AddUniqueDynamic(this, &UReleaseWidget::OnReleaseResponseHandler);
}

void UReleaseWidget::SendGetReleaseDataRequest()
{
	NetUUID.ReleaseInfoUUID = UCatalogNetworkSubsystem::GetInstance()->SendQueryReleaseListRequest(
		UCatalogNetworkSubsystem::GetInstance()->GetUpdataUserInfo(), 0, 1000
	);
}

void UReleaseWidget::SendReleaseRequest()
{
	TArray<FString> FinalReleaseIDS;
	for (const auto SII : SelectedItemIDs)
	{
		if (DirectoryDatas.Contains(SII))
		{
			TArray<FString> CurPaths = URefRelationFunction::GetUpperFolderDirectory(DirectoryDatas[SII].backendFolderPath, false);
			FinalReleaseIDS.AddUnique(SII);
			for (const auto CP : CurPaths)
			{
				if (!CP.IsEmpty())
				{
					FinalReleaseIDS.AddUnique(CP);
				}
			}
		}
	}
	if (FinalReleaseIDS.Num() > 0)
	{
		NetUUID.ReleaseSelectUUID = UCatalogNetworkSubsystem::GetInstance()->SendReleaseRequest(
			FinalReleaseIDS
		);
		this->SetVisibility(ESlateVisibility::Collapsed);
	}
}

void UReleaseWidget::OnGetReleaseDataResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const int32& Total, const TArray<FRefDirectoryData>& Data)
{
	if (NetUUID.ReleaseInfoUUID.Equals(UUID))
	{
		if (bSuccess)
		{
			Init(Data);
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UReleaseWidget::OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const int32& Total, const TArray<FRefDirectoryData>& Data)
{
	if (NetUUID.ReleaseSelectUUID.Equals(UUID))
	{
		if (bSuccess)
		{
			Clear();
		}
		else
		{
			UI_POP_WINDOW_ERROR(Msg);
		}
	}
}

void UReleaseWidget::SelectAll(bool bSelect)
{
	for (auto& Iter : InnerWidgets)
	{
		if (Iter)
		{
			Iter->SetSelect(bSelect);
		}
	}
}

void UReleaseWidget::OnSelectOrUnSelect(const FString& ItemID, bool bSelect)
{
	if (bSelect)
	{
		SelectedItemIDs.AddUnique(ItemID);
	}
	else
	{
		SelectedItemIDs.Remove(ItemID);
	}
}

void UReleaseWidget::OnReleaseAction()
{
	SendReleaseRequest();
}

UReleaseWidget* UReleaseWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UReleaseWidget>(UReleaseWidget::FilePath);
}

UReleaseWidget* UReleaseWidget::GetInstance()
{
	if (UReleaseWidget::Instance == nullptr)
	{
		UReleaseWidget::Instance = UReleaseWidget::Create();
		UReleaseWidget::Instance->AddToViewport(100);
		UReleaseWidget::Instance->AddToRoot();
		UReleaseWidget::Instance->SetVisibility(ESlateVisibility::Collapsed);
	}
	return UReleaseWidget::Instance;
}
