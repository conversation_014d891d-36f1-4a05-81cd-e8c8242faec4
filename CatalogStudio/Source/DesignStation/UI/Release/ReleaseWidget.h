// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "ReleaseInnerWidget.h"
#include "ReleaseWidget.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UReleaseWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void Init(const TArray<FRefDirectoryData>& InDatas);
	void UpdateContent();
	
	void Clear();

private: //network
	void BindDelegate();

	void SendGetReleaseDataRequest();
	void SendReleaseRequest();

	UFUNCTION()
	void OnGetReleaseDataResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const int32& Total, const TArray<FRefDirectoryData>& Data);

	UFUNCTION()
	void OnReleaseResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const int32& Total, const TArray<FRefDirectoryData>& Data);

private:
	UPROPERTY()
	FBackendDirectoryNetUUID NetUUID;

public:
	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseInner")
	void AddWidget(UUserWidget* InWidget);
	UFUNCTION(BlueprintImplementableEvent, Category = "ReleaseInner")
	void ClearWidgets();

	UFUNCTION(BlueprintCallable, Category = "ReleaseInner")
	void SelectAll(bool bSelect);

	UFUNCTION()
	void OnSelectOrUnSelect(const FString& ItemID, bool bSelect);

	UFUNCTION(BlueprintCallable, Category = "ReleaseInner")
	void OnReleaseAction();

	static UReleaseWidget* Create();
	static UReleaseWidget* GetInstance();

private:
	/*
	*  @@ UI widget show data
	*/
	UPROPERTY()
	TMap<FString, FRefDirectoryData> DirectoryDatas;

	UPROPERTY()
	TArray<UReleaseInnerWidget*> InnerWidgets;

	UPROPERTY()
	TArray<FString> SelectedItemIDs;

	static FString FilePath;
	static UReleaseWidget* Instance;
};
