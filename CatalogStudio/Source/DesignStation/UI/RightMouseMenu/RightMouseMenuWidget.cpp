// Fill out your copyright notice in the Description page of Project Settings.

#include "RightMouseMenuWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/CanvasPanelSlot.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"

FString URightMouseMenuWidget::RightMouseMenuPath = TEXT("WidgetBlueprint'/Game/UI/RightMouseMenu/RightMouseMenuUI.RightMouseMenuUI_C'");

const FLinearColor RightMenuNormalColor = FLinearColor::White;
const FLinearColor RightMenuHoverColor = FLinearColor(0.83077f, 0.83077f, 0.83077f, 1.0f);

bool URightMouseMenuWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnBackground, Btn_Background);
	BIND_WIDGET_FUNCTION(BtnBackground, OnClicked, URightMouseMenuWidget::OnClickedBtnBackground);
	BIND_PARAM_CPP_TO_UMG(BorRightMenu, Bor_RightMenu);

	BIND_PARAM_CPP_TO_UMG(BtnCopy, Btn_Copy);
	BIND_WIDGET_FUNCTION(BtnCopy, OnClicked, URightMouseMenuWidget::OnClickedBtnCopy);
	BIND_WIDGET_FUNCTION(BtnCopy, OnHovered, URightMouseMenuWidget::OnHoveredBtnCopy);
	BIND_WIDGET_FUNCTION(BtnCopy, OnUnhovered, URightMouseMenuWidget::OnUnHoveredBtnCopy);
	BIND_PARAM_CPP_TO_UMG(BorCopy, Bor_Copy);

	BIND_PARAM_CPP_TO_UMG(BtnShear, Btn_Shear);
	BIND_WIDGET_FUNCTION(BtnShear, OnClicked, URightMouseMenuWidget::OnClickedBtnShear);
	BIND_WIDGET_FUNCTION(BtnShear, OnHovered, URightMouseMenuWidget::OnHoveredBtnShear);
	BIND_WIDGET_FUNCTION(BtnShear, OnUnhovered, URightMouseMenuWidget::OnUnHoveredBtnShear);
	BIND_PARAM_CPP_TO_UMG(BorShear, Bor_Shear);

	BIND_PARAM_CPP_TO_UMG(BtnPaste, Btn_Paste);
	BIND_WIDGET_FUNCTION(BtnPaste, OnClicked, URightMouseMenuWidget::OnClickedBtnPaste);
	BIND_WIDGET_FUNCTION(BtnPaste, OnHovered, URightMouseMenuWidget::OnHoveredBtnPaste);
	BIND_WIDGET_FUNCTION(BtnPaste, OnUnhovered, URightMouseMenuWidget::OnUnHoveredBtnPaste);
	BIND_PARAM_CPP_TO_UMG(BorPaste, Bor_Paste);

	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, URightMouseMenuWidget::OnClickedBtnDelete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnHovered, URightMouseMenuWidget::OnHoveredBtnDelete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnUnhovered, URightMouseMenuWidget::OnUnHoveredBtnDelete);
	BIND_PARAM_CPP_TO_UMG(BorDelete, Bor_Delete);

	return true;
}

void URightMouseMenuWidget::UpdateContent(const FVector2D & Pos)
{
	if (!IS_OBJECT_PTR_VALID(CPSRightMenu))
	{
		GetRightMenuSlot();
	}
	//FVector2D NewPos;
	//if (GEngine && GEngine->GameViewport && GEngine->GameViewport->Viewport)
	//{
	//	FVector2D CurrentRes = FVector2D(GEngine->GameViewport->Viewport->GetSizeXY());
	//	UE_LOG(LogTemp, Log, TEXT("folder right menu----current res : %s"), *CurrentRes.ToString());
	//	NewPos = FVector2D(Pos.X * CurrentRes.X / 1920.0f, Pos.Y * CurrentRes.Y / 1080.0f);
	//}
	//CPSRightMenu->SetPosition(NewPos);

	APlayerController* PC = GWorld->GetFirstPlayerController();
	ULocalPlayer* LocalPlayer = PC->GetLocalPlayer();
	FVector2D Position;
	if (LocalPlayer->ViewportClient->GetMousePosition(Position))
	{
		CPSRightMenu->SetPosition(Position);
	}
}

URightMouseMenuWidget * URightMouseMenuWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<URightMouseMenuWidget>(URightMouseMenuWidget::RightMouseMenuPath);
}

void URightMouseMenuWidget::GetRightMenuSlot()
{
	CPSRightMenu = UWidgetLayoutLibrary::SlotAsCanvasSlot(BorRightMenu);
}

void URightMouseMenuWidget::OnClickedBtnBackground()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URightMouseMenuWidget::OnClickedBtnCopy()
{
	RightMenuDelegate.ExecuteIfBound(ERightMenuType::Copy);
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URightMouseMenuWidget::OnHoveredBtnCopy()
{
	if (IS_OBJECT_PTR_VALID(BorCopy))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuHoverColor, BorCopy);
	}
}

void URightMouseMenuWidget::OnUnHoveredBtnCopy()
{
	if (IS_OBJECT_PTR_VALID(BorCopy))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuNormalColor, BorCopy);
	}
}

void URightMouseMenuWidget::OnClickedBtnShear()
{
	RightMenuDelegate.ExecuteIfBound(ERightMenuType::Shear);
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URightMouseMenuWidget::OnHoveredBtnShear()
{
	if (IS_OBJECT_PTR_VALID(BorShear))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuHoverColor, BorShear);
	}
}

void URightMouseMenuWidget::OnUnHoveredBtnShear()
{
	if (IS_OBJECT_PTR_VALID(BorShear))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuNormalColor, BorShear);
	}
}

void URightMouseMenuWidget::OnClickedBtnPaste()
{
	RightMenuDelegate.ExecuteIfBound(ERightMenuType::Paste);
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URightMouseMenuWidget::OnHoveredBtnPaste()
{
	if (IS_OBJECT_PTR_VALID(BorPaste))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuHoverColor, BorPaste);
	}
}

void URightMouseMenuWidget::OnUnHoveredBtnPaste()
{
	if (IS_OBJECT_PTR_VALID(BorPaste))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuNormalColor, BorPaste);
	}
}

void URightMouseMenuWidget::OnClickedBtnDelete()
{
	RightMenuDelegate.ExecuteIfBound(ERightMenuType::Delete);
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URightMouseMenuWidget::OnHoveredBtnDelete()
{
	if (IS_OBJECT_PTR_VALID(BorDelete))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuHoverColor, BorDelete);
	}
}

void URightMouseMenuWidget::OnUnHoveredBtnDelete()
{
	if (IS_OBJECT_PTR_VALID(BorDelete))
	{
		UUIFunctionLibrary::SetBorderBrushColor(RightMenuNormalColor, BorDelete);
	}
}
