// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "RightMouseMenuWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class ERightMenuType : uint8
{
	Copy = 0,
	Shear,
	Paste,
	Delete,
	None
};

class UBorder;
class UButton;
class UCanvasPanelSlot;

DECLARE_DYNAMIC_DELEGATE_OneParam(FRightMenuDelegate, const ERightMenuType&, EditType);

UCLASS()
class DESIGNSTATION_API URightMouseMenuWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	void UpdateContent(const FVector2D& Pos);

	static URightMouseMenuWidget* Create();

private:
	void GetRightMenuSlot();

public:
	FRightMenuDelegate RightMenuDelegate;

private:
	static FString RightMouseMenuPath;

protected:
	UFUNCTION()
		void OnClickedBtnBackground();

	UFUNCTION()
		void OnClickedBtnCopy();
	UFUNCTION()
		void OnHoveredBtnCopy();
	UFUNCTION()
		void OnUnHoveredBtnCopy();

	UFUNCTION()
		void OnClickedBtnShear();
	UFUNCTION()
		void OnHoveredBtnShear();
	UFUNCTION()
		void OnUnHoveredBtnShear();

	UFUNCTION()
		void OnClickedBtnPaste();
	UFUNCTION()
		void OnHoveredBtnPaste();
	UFUNCTION()
		void OnUnHoveredBtnPaste();

	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnHoveredBtnDelete();
	UFUNCTION()
		void OnUnHoveredBtnDelete();

private:
	UPROPERTY()
		UButton* BtnBackground;
	UPROPERTY()
		UBorder* BorRightMenu;
	UPROPERTY()
		UCanvasPanelSlot* CPSRightMenu;

	UPROPERTY()
		UBorder* BorCopy;
	UPROPERTY()
		UButton* BtnCopy;

	UPROPERTY()
		UBorder* BorShear;
	UPROPERTY()
		UButton* BtnShear;

	UPROPERTY()
		UBorder* BorPaste;
	UPROPERTY()
		UButton* BtnPaste;

	UPROPERTY()
		UBorder* BorDelete;
	UPROPERTY()
		UButton* BtnDelete;
};
