// Fill out your copyright notice in the Description page of Project Settings.

#include "SRightMouseMenuWidget.h"
#include "Runtime/Slate/Public/Framework/MultiBox/MultiBoxBuilder.h"
#include "Runtime/SlateCore/Public/Textures/SlateIcon.h"
#include "Runtime/Slate/Public/Framework/Commands/UIAction.h"
#include "SlateOptMacros.h"
#include "DesignStation/UI/FolderLayoutUI/FolderAndFileBaseWidget.h"
#define LOCTEXT_NAMESPACE "FolderAndFileRightMenu"

BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION
void SRightMouseMenuWidget::Construct(const FArguments& InArgs)
{
	OwnerWidget = InArgs._OwnerWidget;
	/*
	ChildSlot
	[
		// Populate the widget
	];
	*/
}

void SRightMouseMenuWidget::ConstructMenuContent()
{
	FMenuBuilder MenuBuilder(true, NULL);

	MenuBuilder.BeginSection("Right Menu Hook", FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "DisplayTitle", "Folder Or File Action").ToString()));
	MenuBuilder.AddMenuEntry(
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "DisplayDel", "Delete").ToString()), 
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "ToolTipDel", "Folder / File Delete").ToString()),
		FSlateIcon(), 
		FUIAction(FExecuteAction::CreateLambda([this]() {this->MenuContentButtonClick(EFolderAndFileRightMenu::Type::Delete); })));
	MenuBuilder.AddMenuEntry(
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "DisplayCopy", "Copy").ToString()),
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "ToolTipCopy", "Folder / File Copy").ToString()),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateLambda([this]() {this->MenuContentButtonClick(EFolderAndFileRightMenu::Type::Copy); })));
	MenuBuilder.AddMenuEntry(
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "DisplayCut", "Cut").ToString()),
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "ToolTipCut", "Folder / File Cut").ToString()),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateLambda([this]() {this->MenuContentButtonClick(EFolderAndFileRightMenu::Type::Cut); })));
	MenuBuilder.AddMenuEntry(
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "DisplayPaste", "Paste").ToString()),
		FText::FromString(NSLOCTEXT(LOCTEXT_NAMESPACE, "ToolTipPaste", "Folder / File Paste").ToString()),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateLambda([this]() {this->MenuContentButtonClick(EFolderAndFileRightMenu::Type::Paste); })));
	MenuBuilder.EndSection();

	TSharedPtr<SWindow> ParentWindow = FSlateApplication::Get().GetActiveTopLevelWindow();
	FSlateApplication::Get().PushMenu(ParentWindow.ToSharedRef(), FWidgetPath(), MenuBuilder.MakeWidget(), FSlateApplication::Get().GetCursorPos(), FPopupTransitionEffect(FPopupTransitionEffect::ContextMenu));
}


FReply SRightMouseMenuWidget::MenuContentButtonClick(const EFolderAndFileRightMenu::Type & ActionType)
{
	/*if (OwnerWidget.IsValid())
	{
		OwnerWidget.Get()->RightClickMenuEdit(ActionType);
		return FReply::Handled();
	}*/
	return FReply::Unhandled();
	
}
END_SLATE_FUNCTION_BUILD_OPTIMIZATION

#undef LOCTEXT_NAMESPACE
