// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"

/**
 * 
 */

namespace EFolderAndFileRightMenu
{
	enum Type
	{
		Delete,
		Copy,
		Cut,
		Paste,
		None
	};
}

class UFolderAndFileBaseWidget;

class DESIGNSTATION_API SRightMouseMenuWidget : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SRightMouseMenuWidget)
	{}
	SLATE_ARGUMENT(TWeakObjectPtr<UFolderAndFileBaseWidget>, OwnerWidget)
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);
	void ConstructMenuContent();

	FReply MenuContentButtonClick(const EFolderAndFileRightMenu::Type& ActionType);

private:
	TWeakObjectPtr<UFolderAndFileBaseWidget> OwnerWidget;
};
