// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentCuboid.h"

#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString USingleComponentCuboid::SingleComponentCuboidPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentCuboid.SingleComponentCuboid_C'");

bool USingleComponentCuboid::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	if (UEditableText* Txt_BeginXExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginXExpression"))))
	{
		TxtBeginXExpress = Txt_BeginXExpression;
		TxtBeginXExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginXExpress);
	}
	if (UButton* Btn_BeginXExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginXExpression"))))
	{
		BtnBeginXExpress = Btn_BeginXExpression;
		BtnBeginXExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnBeginXExpress);
	}
	if (UEditableText* Edt_BeginXValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginXValue"))))
	{
		EdtBeginXValue = Edt_BeginXValue;
		EdtBeginXValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginXValue);
	}
	if (UEditableText* Txt_BeginYExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginYExpression"))))
	{
		TxtBeginYExpress = Txt_BeginYExpression;
		TxtBeginYExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginYExpress);
	}
	if (UButton* Btn_BeginYExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginYExpression"))))
	{
		BtnBeginYExpress = Btn_BeginYExpression;
		BtnBeginYExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnBeginYExpress);
	}
	if (UEditableText* Edt_BeginYValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginYValue"))))
	{
		EdtBeginYValue = Edt_BeginYValue;
		EdtBeginYValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginYValue);
	}
	if (UEditableText* Txt_BeginZExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginZExpression"))))
	{
		TxtBeginZExpress = Txt_BeginZExpression;
		TxtBeginZExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginZExpress);
	}
	if (UButton* Btn_BeginZExPression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginZExPression"))))
	{
		BtnBeginZExpress = Btn_BeginZExPression;
		BtnBeginZExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnBeginZExpress);
	}
	if (UEditableText* Edt_BeginZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginZValue"))))
	{
		EdtBeginZValue = Edt_BeginZValue;
		EdtBeginZValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtBeginZValue);
	}
	//end point
	if (UEditableText* Txt_EndXExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndXExpress"))))
	{
		TxtEndXExpress = Txt_EndXExpress;
		TxtEndXExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndXExpress);
	}
	if (UButton* Btn_EndXExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndXExpress"))))
	{
		BtnEndXExpress = Btn_EndXExpress;
		BtnEndXExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnEndXExpress);
	}
	if (UEditableText* Edt_EndXValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndXValue"))))
	{
		EdtEndXValue = Edt_EndXValue;
		EdtEndXValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndXValue);
	}
	if (UEditableText* Txt_EndYExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndYExpress"))))
	{
		TxtEndYExpress = Txt_EndYExpress;
		TxtEndYExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndYExpress);
	}
	if (UButton* Btn_EndYExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndYExpress"))))
	{
		BtnEndYExpress = Btn_EndYExpress;
		BtnEndYExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnEndYExpress);
	}
	if (UEditableText* Edt_EndYValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndYValue"))))
	{
		EdtEndYValue = Edt_EndYValue;
		EdtEndYValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndYValue);
	}
	if (UEditableText* Txt_EndZExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndZExpress"))))
	{
		TxtEndZExpress = Txt_EndZExpress;
		TxtEndZExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndZExpress);
	}
	if (UButton* Btn_EndZExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndZExpress"))))
	{
		BtnEndZExpress = Btn_EndZExpress;
		BtnEndZExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentCuboid::OnClickedBtnEndZExpress);
	}
	if (UEditableText* Edt_EndZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndZValue"))))
	{
		EdtEndZValue = Edt_EndZValue;
		EdtEndZValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentCuboid::OnTextCommittedEdtEndZValue);
	}

	return true;
}

void USingleComponentCuboid::UpdateContent(const FGeomtryCubeProperty& InData)
{
	CuboidProperty = InData;
	if (EdtBeginXValue && EdtBeginYValue && EdtBeginZValue)
	{
		FText Text = UParameterPropertyData::FormatParameterValue(InData.StartLocationX.Value);
		EdtBeginXValue->SetText(Text);
		if (InData.StartLocationX.Expression.IsNumeric())
		{
			TxtBeginXExpress->SetText(Text);
		}
		else
		{
			TxtBeginXExpress->SetText(FText::FromString(InData.StartLocationX.Expression));
		}
		Text = UParameterPropertyData::FormatParameterValue(InData.StartLocationY.Value);
		EdtBeginYValue->SetText(Text);
		if (InData.StartLocationY.Expression.IsNumeric())
		{
			TxtBeginYExpress->SetText(EdtBeginYValue->GetText());
		}
		else
		{
			TxtBeginYExpress->SetText(FText::FromString(InData.StartLocationY.Expression));
		}
		Text = UParameterPropertyData::FormatParameterValue(InData.StartLocationZ.Value);
		EdtBeginZValue->SetText(Text);
		if (InData.StartLocationZ.Expression.IsNumeric())
		{
			TxtBeginZExpress->SetText(EdtBeginZValue->GetText());
		}
		else
		{
			TxtBeginZExpress->SetText(FText::FromString(InData.StartLocationZ.Expression));
		}
	}
	if (EdtEndXValue && EdtEndYValue && EdtEndZValue)
	{
		FText Text = UParameterPropertyData::FormatParameterValue(InData.EndLocationX.Value);
		EdtEndXValue->SetText(Text);
		if (InData.EndLocationX.Expression.IsNumeric())
		{
			TxtEndXExpress->SetText(EdtEndXValue->GetText());
		}
		else
		{
			TxtEndXExpress->SetText(FText::FromString(InData.EndLocationX.Expression));
		}
		Text = UParameterPropertyData::FormatParameterValue(InData.EndLocationY.Value);
		EdtEndYValue->SetText(Text);
		if (InData.EndLocationY.Expression.IsNumeric())
		{
			TxtEndYExpress->SetText(EdtEndYValue->GetText());
		}
		else
		{
			TxtEndYExpress->SetText(FText::FromString(InData.EndLocationY.Expression));
		}
		Text = UParameterPropertyData::FormatParameterValue(InData.EndLocationZ.Value);
		EdtEndZValue->SetText(Text);
		if (InData.EndLocationZ.Expression.IsNumeric())
		{
			TxtEndZExpress->SetText(EdtEndZValue->GetText());
		}
		else
		{
			TxtEndZExpress->SetText(FText::FromString(InData.EndLocationZ.Expression));
		}
	}
}

void USingleComponentCuboid::BindCuboidExpressionEdit(const int32& InEditType, const FString& InExpression)
{
	if (UExpressionPopWidget::Get())
	{
		UExpressionPopWidget::Get()->UpdateContent(InEditType, InExpression);
		UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("CuboidExpressionEdit")));
		UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
}

USingleComponentCuboid* USingleComponentCuboid::Create()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentPoint---create cuboid view"));
	UClass* CuboidBp = LoadClass<UUserWidget>(NULL, *SingleComponentCuboidPath);
	checkf(CuboidBp, TEXT("load cuboid bp error!"));
	USingleComponentCuboid* CuboidItem = CreateWidget<USingleComponentCuboid>(GWorld.GetReference(), CuboidBp);
	checkf(CuboidItem, TEXT("create cuboid item error!"));
	return CuboidItem;
}

void USingleComponentCuboid::CuboidExpressionEdit(const int32& EditType, const FString& InExpression)
{
	switch ((ECuboidExpressionType)EditType)
	{
	case ECuboidExpressionType::BeginXExpression:
	{
		if (TxtBeginXExpress)
		{
			TxtBeginXExpress->SetText(FText::FromString(InExpression));
			CuboidProperty.StartLocationX.Expression = InExpression;
			CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationXExpress, CuboidProperty);
		}
		break;
	}
	case ECuboidExpressionType::BeginYExpression:
	{
		CuboidProperty.StartLocationY.Expression = InExpression;
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationYExpress, CuboidProperty);
		break;
	}
	case ECuboidExpressionType::BeginZExpression:
	{
		CuboidProperty.StartLocationZ.Expression = InExpression;
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationZExpress, CuboidProperty);
		break;
	}
	case ECuboidExpressionType::EndXExpression:
	{
		CuboidProperty.EndLocationX.Expression = InExpression;
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationXExpress, CuboidProperty);
		break;
	}
	case ECuboidExpressionType::EndYExpression:
	{
		CuboidProperty.EndLocationY.Expression = InExpression;
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationYExpress, CuboidProperty);
		break;
	}
	case ECuboidExpressionType::EndZExpression:
	{
		CuboidProperty.EndLocationZ.Expression = InExpression;
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationZExpress, CuboidProperty);
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USingleComponentCuboid::OnClickedBtnBeginXExpress()
{
	if (TxtBeginXExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::BeginXExpression, TxtBeginXExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.StartLocationX.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationXExpress, CuboidProperty);
	}
	else
	{
		TxtBeginXExpress->SetText(FText::FromString(CuboidProperty.StartLocationX.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtBeginXValue)
	//{
	//	OriginValue = EdtBeginXValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.StartLocationX = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationX, CuboidProperty);
	}
	else
	{
		EdtBeginXValue->SetText(FText::FromString(CuboidProperty.StartLocationX.Value));
	}
}

void USingleComponentCuboid::OnClickedBtnBeginYExpress()
{
	if (TxtBeginYExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::BeginYExpression, TxtBeginYExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);
	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.StartLocationY.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationYExpress, CuboidProperty);
	}
	else
	{
		TxtBeginYExpress->SetText(FText::FromString(CuboidProperty.StartLocationY.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtBeginYValue)
	//{
	//	OriginValue = EdtBeginYValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.StartLocationY = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationY, CuboidProperty);
	}
	else
	{
		EdtBeginYValue->SetText(FText::FromString(CuboidProperty.StartLocationY.Value));
	}
}

void USingleComponentCuboid::OnClickedBtnBeginZExpress()
{
	if (TxtBeginZExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::BeginZExpression, TxtBeginZExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.StartLocationZ.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationZExpress, CuboidProperty);
	}
	else
	{
		TxtBeginZExpress->SetText(FText::FromString(CuboidProperty.StartLocationZ.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtBeginZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtBeginZValue)
	//{
	//	OriginValue = EdtBeginZValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.StartLocationZ = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::StartLocationZ, CuboidProperty);
	}
	else
	{
		EdtBeginZValue->SetText(FText::FromString(CuboidProperty.StartLocationZ.Value));
	}
}

void USingleComponentCuboid::OnClickedBtnEndXExpress()
{
	if (TxtEndXExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::EndXExpression, TxtEndXExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.EndLocationX.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationXExpress, CuboidProperty);
	}
	else
	{
		TxtEndXExpress->SetText(FText::FromString(CuboidProperty.EndLocationX.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtEndXValue)
	//{
	//	OriginValue = EdtEndXValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.EndLocationX = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationX, CuboidProperty);
	}
	else
	{
		EdtEndXValue->SetText(FText::FromString(CuboidProperty.EndLocationX.Value));
	}
}

void USingleComponentCuboid::OnClickedBtnEndYExpress()
{
	if (TxtEndYExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::EndYExpression, TxtEndYExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.EndLocationY.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationYExpress, CuboidProperty);
	}
	else
	{
		TxtEndYExpress->SetText(FText::FromString(CuboidProperty.EndLocationY.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtEndYValue)
	//{
	//	OriginValue = EdtEndYValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.EndLocationY = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationY, CuboidProperty);
	}
	else
	{
		EdtEndYValue->SetText(FText::FromString(CuboidProperty.EndLocationY.Value));
	}
}

void USingleComponentCuboid::OnClickedBtnEndZExpress()
{
	if (TxtEndZExpress)
	{
		BindCuboidExpressionEdit((int32)ECuboidExpressionType::EndZExpression, TxtEndZExpress->GetText().ToString());
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		CuboidProperty.EndLocationZ.Expression = Text.ToString();
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationZExpress, CuboidProperty);
	}
	else
	{
		TxtEndZExpress->SetText(FText::FromString(CuboidProperty.EndLocationZ.Expression));
	}
}

void USingleComponentCuboid::OnTextCommittedEdtEndZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//FString OriginValue;
	//if (EdtEndZValue)
	//{
	//	OriginValue = EdtEndZValue->GetText().ToString();
	//}
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		CuboidProperty.EndLocationZ = FExpressionValuePair(FString::SanitizeFloat(Value));
		CuboidChangeDelegate.ExecuteIfBound((int32)ECuboidChangeType::EndLocationZ, CuboidProperty);
	}
	else
	{
		EdtEndZValue->SetText(FText::FromString(CuboidProperty.EndLocationZ.Value));
	}
}




