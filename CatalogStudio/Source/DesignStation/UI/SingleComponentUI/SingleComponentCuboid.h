// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSectionUnitWidget.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "SingleComponentCuboid.generated.h"

/**
 * 
 */

class UTextBlock;
class UButton;
class UEditableText;

UENUM(BlueprintType)
enum class ECuboidChangeType : uint8
{
	StartLocationX = 0,
	StartLocationY,
	StartLocationZ,
	EndLocationX,
	EndLocationY,
	EndLocationZ,
	StartLocationXExpress,
	StartLocationYExpress,
	StartLocationZExpress,
	EndLocationXExpress,
	EndLocationYExpress,
	EndLocationZExpress
};

UENUM(BlueprintType)
enum class ECuboidExpressionType : uint8
{
	BeginXExpression = 0,
	BeginYExpression,
	BeginZExpression,
	EndXExpression,
	EndYExpression,
	EndZExpression
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FCuboidPropertyChangeDelegate, const int32&, EditType, const FGeomtryCubeProperty&, CuboidProperty);

UCLASS()
class DESIGNSTATION_API USingleComponentCuboid : public USingleComponentSectionUnitWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FGeomtryCubeProperty& InData);
	void BindCuboidExpressionEdit(const int32& InEditType, const FString& InExpression);

	static USingleComponentCuboid* Create();
	
private:
	UFUNCTION()
		void CuboidExpressionEdit(const int32& EditType, const FString& InExpression);

private:
	FGeomtryCubeProperty CuboidProperty;

	static FString SingleComponentCuboidPath;

public:
	FCuboidPropertyChangeDelegate CuboidChangeDelegate;

protected:
	UFUNCTION() //begin point
		void OnClickedBtnBeginXExpress();
	UFUNCTION()
		void OnTextCommittedEdtBeginXExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtBeginXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnBeginYExpress();
	UFUNCTION()
		void OnTextCommittedEdtBeginYExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtBeginYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnBeginZExpress();
	UFUNCTION()
		void OnTextCommittedEdtBeginZExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtBeginZValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION() //end point
		void OnClickedBtnEndXExpress();
	UFUNCTION()
		void OnTextCommittedEdtEndXExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtEndXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnEndYExpress();
	UFUNCTION()
		void OnTextCommittedEdtEndYExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtEndYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnEndZExpress();
	UFUNCTION()
		void OnTextCommittedEdtEndZExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtEndZValue(const FText& Text, ETextCommit::Type CommitMethod);

private:
	UPROPERTY()          //begin point
		UEditableText* TxtBeginXExpress;
	UPROPERTY()
		UButton* BtnBeginXExpress;
	UPROPERTY()
		UEditableText* EdtBeginXValue;
	UPROPERTY()
		UEditableText* TxtBeginYExpress;
	UPROPERTY()
		UButton* BtnBeginYExpress;
	UPROPERTY()
		UEditableText* EdtBeginYValue;
	UPROPERTY()
		UEditableText* TxtBeginZExpress;
	UPROPERTY()
		UButton* BtnBeginZExpress;
	UPROPERTY()
		UEditableText* EdtBeginZValue;

	UPROPERTY()          //end point
		UEditableText* TxtEndXExpress;
	UPROPERTY()
		UButton* BtnEndXExpress;
	UPROPERTY()
		UEditableText* EdtEndXValue;
	UPROPERTY()
		UEditableText* TxtEndYExpress;
	UPROPERTY()
		UButton* BtnEndYExpress;
	UPROPERTY()
		UEditableText* EdtEndYValue;
	UPROPERTY()
		UEditableText* TxtEndZExpress;
	UPROPERTY()
		UButton* BtnEndZExpress;
	UPROPERTY()
		UEditableText* EdtEndZValue;
};
