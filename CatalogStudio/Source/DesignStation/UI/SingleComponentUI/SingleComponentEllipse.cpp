// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentEllipse.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString USingleComponentEllipse::EllipseViewPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentEllipse.SingleComponentEllipse_C'");

bool USingleComponentEllipse::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	if (UEditableText* Txt_XExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_XExpression"))))
	{
		TxtXExpress = Txt_XExpression;
		TxtXExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtXExpress);
	}
	if (UButton* Btn_XExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_XExpression"))))
	{
		BtnXExpress = Btn_XExpression;
		BtnXExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentEllipse::OnClickedBtnXExpress);
	}
	if (UEditableText* Edt_XValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_XValue"))))
	{
		EdtXValue = Edt_XValue;
		EdtXValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtXValue);
	}
	if (UEditableText* Txt_YExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_YExpression"))))
	{
		TxtYExpress = Txt_YExpression;
		TxtYExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtYExpress);
	}
	if (UButton* Btn_YExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_YExpression"))))
	{
		BtnYExpress = Btn_YExpression;
		BtnYExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentEllipse::OnClickedBtnYExpress);
	}
	if (UEditableText* Edt_YValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_YValue"))))
	{
		EdtYValue = Edt_YValue;
		EdtYValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtYValue);
	}
	if (UEditableText* Txt_ZExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_ZExpression"))))
	{
		TxtZExpress = Txt_ZExpression;
		TxtZExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtZExpress);
	}
	if (UButton* Btn_ZExPression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_ZExPression"))))
	{
		BtnZExpress = Btn_ZExPression;
		BtnZExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentEllipse::OnClickedBtnZExpress);
	}
	if (UEditableText* Edt_ZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_ZValue"))))
	{
		EdtZValue = Edt_ZValue;
		EdtZValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtZValue);
	}
	//radius
	if (UEditableText* Txt_LongRadiusExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_LongRadiusExpress"))))
	{
		TxtLongRadiusExpress = Txt_LongRadiusExpress;
		TxtLongRadiusExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtLongRadiusExpress);
	}
	if (UButton* Btn_LongRadiusButton = Cast<UButton>(GetWidgetFromName(TEXT("Btn_LongRadiusButton"))))
	{
		BtnLongRadiusExpress = Btn_LongRadiusButton;
		BtnLongRadiusExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentEllipse::OnClickedBtnLongRadiusExpress);
	}
	if (UEditableText* Edt_LongRadiusValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_LongRadiusValue"))))
	{
		EdtLongRadiusValue = Edt_LongRadiusValue;
		EdtLongRadiusValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtLongRadiusValue);
	}
	if (UEditableText* Txt_ShortRadiusExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_ShortRadiusExpress"))))
	{
		TxtShortRadiusExpress = Txt_ShortRadiusExpress;
		TxtShortRadiusExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtShortRadiusExpress);
	}
	if (UButton* Btn_ShortRadiusButton = Cast<UButton>(GetWidgetFromName(TEXT("Btn_ShortRadiusButton"))))
	{
		Btn_ShortRadiusButton = Btn_ShortRadiusButton;
		Btn_ShortRadiusButton->OnClicked.AddUniqueDynamic(this, &USingleComponentEllipse::OnClickedBtnShortRadiusExpress);
	}
	if (UEditableText* Edt_ShortRadiusValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_ShortRadiusValue"))))
	{
		EdtShortRadiusValue = Edt_ShortRadiusValue;
		EdtShortRadiusValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtShortRadiusValue);
	}
	if (UEditableText* Edt_PointNum = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_PointNum"))))
	{
		EdtPointNum = Edt_PointNum;
		EdtPointNum->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentEllipse::OnTextCommittedEdtPointNum);
	}

	BIND_PARAM_CPP_TO_UMG(TxtPointNumExpress, Txt_PointNumExpress);
	BIND_PARAM_CPP_TO_UMG(BtnPointNum, Btn_PointNum);
	BIND_WIDGET_FUNCTION(BtnPointNum, OnClicked, USingleComponentEllipse::OnClickedBtnPointNum);
	BIND_WIDGET_FUNCTION(TxtPointNumExpress, OnTextCommitted, USingleComponentEllipse::OnTextCommittedPointNumExpress);
	return true;
}

void USingleComponentEllipse::UpdateContent(const FGeomtryEllipsePlanProperty& InData)
{
	EllipseProperty.CopyData(InData);
	if (IS_OBJECT_PTR_VALID(EdtXValue) && IS_OBJECT_PTR_VALID(TxtXExpress))
	{
		EdtXValue->SetText(UParameterPropertyData::FormatParameterValue(InData.CenterLocationX.Value));
		TxtXExpress->SetText(FText::FromString(InData.CenterLocationX.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtYValue) && IS_OBJECT_PTR_VALID(TxtYExpress))
	{
		EdtYValue->SetText(UParameterPropertyData::FormatParameterValue(InData.CenterLocationY.Value));
		TxtYExpress->SetText(FText::FromString(InData.CenterLocationY.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtZValue) && IS_OBJECT_PTR_VALID(TxtZExpress))
	{
		EdtZValue->SetText(UParameterPropertyData::FormatParameterValue(InData.CenterLocationZ.Value));
		TxtZExpress->SetText(FText::FromString(InData.CenterLocationZ.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtLongRadiusValue) && IS_OBJECT_PTR_VALID(TxtLongRadiusExpress))
	{
		EdtLongRadiusValue->SetText(UParameterPropertyData::FormatParameterValue(InData.LongRadiusData.Value));
		TxtLongRadiusExpress->SetText(FText::FromString(InData.LongRadiusData.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtShortRadiusValue) && IS_OBJECT_PTR_VALID(TxtShortRadiusExpress))
	{
		EdtShortRadiusValue->SetText(UParameterPropertyData::FormatParameterValue(InData.ShortRadiusData.Value));
		TxtShortRadiusExpress->SetText(FText::FromString(InData.ShortRadiusData.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtPointNum) && IS_OBJECT_PTR_VALID(TxtPointNumExpress))
	{
		EdtPointNum->SetText(FText::FromString(InData.InterpPointCountData.Value));
		TxtPointNumExpress->SetText(FText::FromString(InData.InterpPointCountData.Expression));
	}
}

void USingleComponentEllipse::BindEllipseExpressionEdit(const int32& InEditType, const FString& InExpression)
{
	if (UExpressionPopWidget::Get())
	{
		UExpressionPopWidget::Get()->UpdateContent(InEditType, InExpression);
		UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("EllipseExpressionEdit")));
		UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
}

USingleComponentEllipse* USingleComponentEllipse::Create()
{
	UClass* EllipseItemBp = LoadClass<UUserWidget>(NULL, *EllipseViewPath);
	checkf(EllipseItemBp, TEXT("load ellipse item bp error!"));
	USingleComponentEllipse* EllipseItem = CreateWidget<USingleComponentEllipse>(GWorld.GetReference(), EllipseItemBp);
	checkf(EllipseItem, TEXT("create ellipse item error!"));
	return EllipseItem;
}

FString USingleComponentEllipse::GetEdtOriginValue(UEditableText* EdtWidget)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---get editabletext origin value"));
	if (EdtWidget)
	{
		return EdtWidget->GetText().ToString();
	}
	return FString();
}

void USingleComponentEllipse::EllipseExpressionEdit(const int32& EditType, const FString& InExpression)
{
	switch ((EEllipseExpressionType)EditType)
	{
	case EEllipseExpressionType::CLXExpression:
	{
		if (TxtXExpress)
		{
			TxtXExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.CenterLocationX.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationXExpress, EllipseProperty);
		}
		break;
	}
	case EEllipseExpressionType::CLYExpression:
	{
		if (TxtYExpress)
		{
			TxtYExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.CenterLocationY.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationYExpress, EllipseProperty);
		}
		break;
	}
	case EEllipseExpressionType::CLZExpression:
	{
		if (TxtZExpress)
		{
			TxtZExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.CenterLocationZ.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationZExpress, EllipseProperty);
		}
		break;
	}
	case EEllipseExpressionType::RadiusOne:
	{
		if (TxtLongRadiusExpress)
		{
			TxtLongRadiusExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.LongRadiusData.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::LongRadiusExpress, EllipseProperty);
		}
		break;
	}
	case EEllipseExpressionType::RadiusTwo:
	{
		if (TxtShortRadiusExpress)
		{
			TxtShortRadiusExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.ShortRadiusData.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::ShortRadiusExpress, EllipseProperty);
		}
		break;
	}
	case EEllipseExpressionType::PointNum:
	{
		if (TxtPointNumExpress)
		{
			TxtPointNumExpress->SetText(FText::FromString(InExpression));
			EllipseProperty.InterpPointCountData.Expression = InExpression;
			EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::PointNumExpress, EllipseProperty);
		}
		break;
	}

	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USingleComponentEllipse::OnClickedBtnXExpress()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---clicked x express"));
	if (TxtXExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::CLXExpression, TxtXExpress->GetText().ToString());
	}
}

void USingleComponentEllipse::OnTextCommittedEdtXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.CenterLocationX.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationXExpress, EllipseProperty);
	}
	else
	{
		TxtXExpress->SetText(FText::FromString(EllipseProperty.CenterLocationX.Expression));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---x value change"));
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		EllipseProperty.CenterLocationX.Value = FString::SanitizeFloat(Value);
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationX, EllipseProperty);
	}
	else
	{
		EdtXValue->SetText(UParameterPropertyData::FormatParameterValue(EllipseProperty.CenterLocationX.Value));
	}
}

void USingleComponentEllipse::OnClickedBtnYExpress()
{
	if (TxtYExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::CLYExpression, TxtYExpress->GetText().ToString());
	}
}

void USingleComponentEllipse::OnTextCommittedEdtYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.CenterLocationY.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationYExpress, EllipseProperty);
	}
	else
	{
		TxtYExpress->SetText(FText::FromString(EllipseProperty.CenterLocationY.Expression));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---y value change"));
	//FString OriginValue = GetEdtOriginValue(EdtYValue);
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		EllipseProperty.CenterLocationY.Value = FString::SanitizeFloat(Value);
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationY, EllipseProperty);
	}
	else
	{
		EdtYValue->SetText(UParameterPropertyData::FormatParameterValue(EllipseProperty.CenterLocationY.Value));
	}
}

void USingleComponentEllipse::OnClickedBtnZExpress()
{
	if (TxtZExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::CLZExpression, TxtZExpress->GetText().ToString());
	}
}

void USingleComponentEllipse::OnTextCommittedEdtZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.CenterLocationZ.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationZExpress, EllipseProperty);
	}
	else
	{
		TxtZExpress->SetText(FText::FromString(EllipseProperty.CenterLocationZ.Expression));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---z value change"));
	//FString OriginValue = GetEdtOriginValue(EdtZValue);
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		EllipseProperty.CenterLocationZ.Value = FString::SanitizeFloat(Value);
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::CenterLocationZ, EllipseProperty);
	}
	else
	{
		EdtZValue->SetText(UParameterPropertyData::FormatParameterValue(EllipseProperty.CenterLocationZ.Value));
	}
}

void USingleComponentEllipse::OnClickedBtnLongRadiusExpress()
{
	if (TxtLongRadiusExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::RadiusOne, TxtLongRadiusExpress->GetText().ToString());
	}
}

void USingleComponentEllipse::OnTextCommittedEdtLongRadiusExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.LongRadiusData.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::LongRadiusExpress, EllipseProperty);
	}
	else
	{
		TxtLongRadiusExpress->SetText(FText::FromString(EllipseProperty.LongRadiusData.Expression));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtLongRadiusValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---long radius value change"));
	//FString OriginValue = GetEdtOriginValue(EdtLongRadiusValue);
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared && FCString::Atof(*Text.ToString()) > 0)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		EllipseProperty.LongRadiusData.Value = FString::SanitizeFloat(Value);
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::LongRadius, EllipseProperty);
	}
	else
	{
		EdtLongRadiusValue->SetText(UParameterPropertyData::FormatParameterValue(EllipseProperty.LongRadiusData.Value));
	}
}

void USingleComponentEllipse::OnClickedBtnShortRadiusExpress()
{
	if (TxtShortRadiusExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::RadiusTwo, TxtShortRadiusExpress->GetText().ToString());
	}
}

void USingleComponentEllipse::OnTextCommittedEdtShortRadiusExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.ShortRadiusData.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::ShortRadiusExpress, EllipseProperty);
	}
	else
	{
		TxtShortRadiusExpress->SetText(FText::FromString(EllipseProperty.ShortRadiusData.Expression));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtShortRadiusValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---short radius value change"));
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared && FCString::Atof(*Text.ToString()) > 0)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		EllipseProperty.ShortRadiusData.Value = FString::SanitizeFloat(Value);
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::ShortRadius, EllipseProperty);
	}
	else
	{
		EdtShortRadiusValue->SetText(UParameterPropertyData::FormatParameterValue(EllipseProperty.ShortRadiusData.Value));
	}
}

void USingleComponentEllipse::OnTextCommittedEdtPointNum(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentEllipse---point num value change"));
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared && FCString::Atoi(*Text.ToString()) > 0)
	{
		EllipseProperty.InterpPointCountData.Value = UKismetStringLibrary::Conv_IntToString(FCString::Atoi(*Text.ToString()));
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::PointNum, EllipseProperty);
	}
	else
	{
		EdtPointNum->SetText(FText::FromString(EllipseProperty.InterpPointCountData.Value));
	}
}

void USingleComponentEllipse::OnTextCommittedPointNumExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EllipseProperty.InterpPointCountData.Expression = Text.ToString();
		EllipsePropertyChangeDelegate.ExecuteIfBound((int32)EEllipseChangeType::PointNumExpress, EllipseProperty);
	}
	else
	{
		TxtPointNumExpress->SetText(FText::FromString(EllipseProperty.InterpPointCountData.Expression));
	}
}


void USingleComponentEllipse::OnClickedBtnPointNum()
{
	if (TxtPointNumExpress)
	{
		BindEllipseExpressionEdit((int32)EEllipseExpressionType::PointNum, TxtPointNumExpress->GetText().ToString());
	}
}