// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSectionUnitWidget.h"
#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "SingleComponentEllipse.generated.h"

/**
 * 
 */

class UTextBlock;
class UButton;
class UEditableText;

UENUM(BlueprintType)
enum class EEllipseChangeType : uint8
{
	CenterLocationX = 0,
	CenterLocationY,
	CenterLocationZ,
	ShortRadius,
	LongRadius,
	PointNum,
	CenterLocationXExpress,
	CenterLocationYExpress,
	CenterLocationZExpress,
	ShortRadiusExpress,
	LongRadiusExpress,
	PointNumExpress
};

UENUM(blueprintType)
enum class EEllipseExpressionType : uint8
{
	CLXExpression = 0,
	CLYExpression,
	CLZExpression,
	RadiusOne,
	RadiusTwo,
	PointNum
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FEllipsePropertyChangeDelegate, const int32&, EditType, const FGeomtryEllipsePlanProperty&, EllipseProperty);

UCLASS()
class DESIGNSTATION_API USingleComponentEllipse : public USingleComponentSectionUnitWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FGeomtryEllipsePlanProperty& InData);
	void BindEllipseExpressionEdit(const int32& InEditType, const FString& InExpression);

	static USingleComponentEllipse* Create();

public:
	FEllipsePropertyChangeDelegate EllipsePropertyChangeDelegate;

private:
	FString GetEdtOriginValue(UEditableText* EdtWidget);
	UFUNCTION()
		void EllipseExpressionEdit(const int32& EditType, const FString& InExpression);

private:
	FGeomtryEllipsePlanProperty EllipseProperty;

	static FString EllipseViewPath;

protected:
	UFUNCTION()
		void OnClickedBtnXExpress();
	UFUNCTION()
		void OnTextCommittedEdtXExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnYExpress();
	UFUNCTION()
		void OnTextCommittedEdtYExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnZExpress();
	UFUNCTION()
		void OnTextCommittedEdtZExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnLongRadiusExpress();
	UFUNCTION()
		void OnTextCommittedEdtLongRadiusExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtLongRadiusValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnShortRadiusExpress();
	UFUNCTION()
		void OnTextCommittedEdtShortRadiusExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtShortRadiusValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnTextCommittedEdtPointNum(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedPointNumExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnPointNum();

private:
	UPROPERTY()
		UEditableText* TxtXExpress;
	UPROPERTY()
		UButton* BtnXExpress;
	UPROPERTY()
		UEditableText* EdtXValue;
	UPROPERTY()
		UEditableText* TxtYExpress;
	UPROPERTY()
		UButton* BtnYExpress;
	UPROPERTY()
		UEditableText* EdtYValue;
	UPROPERTY()
		UEditableText* TxtZExpress;
	UPROPERTY()
		UButton* BtnZExpress;
	UPROPERTY()
		UEditableText* EdtZValue;

	UPROPERTY()
		UEditableText* TxtLongRadiusExpress;
	UPROPERTY()
		UButton* BtnLongRadiusExpress;
	UPROPERTY()
		UEditableText* EdtLongRadiusValue;
	UPROPERTY()
		UEditableText* TxtShortRadiusExpress;
	UPROPERTY()
		UButton* BtnShortRadiusExpress;
	UPROPERTY()
		UEditableText* EdtShortRadiusValue;

	UPROPERTY()
		UEditableText* EdtPointNum;
	UPROPERTY()
		UEditableText* TxtPointNumExpress;
	UPROPERTY()
		UButton*	BtnPointNum;
};
