// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentImportSection.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"

FString USingleComponentImportSection::SingleComponentImportSectionPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentImportSection.SingleComponentImportSection_C'");

bool USingleComponentImportSection::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	//bind expression text
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateXExpression, Edt_CoordinateXExpression);
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateYExpression, Edt_CoordinateYExpression);
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateZExpression, Edt_CoordinateZExpression);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingXExpression, Edt_RotatingXExpression);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingYExpression, Edt_RotatingYExpression);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingZExpression, Edt_RotatingZExpression);
	BIND_PARAM_CPP_TO_UMG(EdtZoomXExpression, Edt_ZoomXExpression);
	BIND_PARAM_CPP_TO_UMG(EdtZoomYExpression, Edt_ZoomYExpression);
	BIND_PARAM_CPP_TO_UMG(EdtZoomZExpression, Edt_ZoomZExpression);

	//bind expression button
	BIND_PARAM_CPP_TO_UMG(BtnCoordinateXExpression, Btn_CoordinateXExpression);
	BIND_PARAM_CPP_TO_UMG(BtnCoordinateYExpression, Btn_CoordinateYExpression);
	BIND_PARAM_CPP_TO_UMG(BtnCoordinateZExpression, Btn_CoordinateZExpression);
	BIND_PARAM_CPP_TO_UMG(BtnRotatingXExpression, Btn_RotatingXExpression);
	BIND_PARAM_CPP_TO_UMG(BtnRotatingYExpression, Btn_RotatingYExpression);
	BIND_PARAM_CPP_TO_UMG(BtnRotatingZExpression, Btn_RotatingZExpression);
	BIND_PARAM_CPP_TO_UMG(BtnZoomXExpression, Btn_ZoomXExpression);
	BIND_PARAM_CPP_TO_UMG(BtnZoomYExpression, Btn_ZoomYExpression);
	BIND_PARAM_CPP_TO_UMG(BtnZoomZExpression, Btn_ZoomZExpression);

	//bind value text
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateXValue, Edt_CoordinateXValue);
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateYValue, Edt_CoordinateYValue);
	BIND_PARAM_CPP_TO_UMG(EdtCoordinateZValue, Edt_CoordinateZValue);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingXValue, Edt_RotatingXValue);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingYValue, Edt_RotatingYValue);
	BIND_PARAM_CPP_TO_UMG(EdtRotatingZValue, Edt_RotatingZValue);
	BIND_PARAM_CPP_TO_UMG(EdtZoomXValue, Edt_ZoomXValue);
	BIND_PARAM_CPP_TO_UMG(EdtZoomYValue, Edt_ZoomYValue);
	BIND_PARAM_CPP_TO_UMG(EdtZoomZValue, Edt_ZoomZValue);

	//bind function to expression text
	//coordinate
	BIND_WIDGET_FUNCTION(EdtCoordinateXExpression,OnTextCommitted, 
		USingleComponentImportSection::OnTextCommittedCoordinateXExpression);
	BIND_WIDGET_FUNCTION(EdtCoordinateYExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedCoordinateYExpression);
	BIND_WIDGET_FUNCTION(EdtCoordinateZExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedCoordinateZExpression);

	//rotating
	BIND_WIDGET_FUNCTION(EdtRotatingXExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingXExpression);
	BIND_WIDGET_FUNCTION(EdtRotatingYExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingYExpression);
	BIND_WIDGET_FUNCTION(EdtRotatingZExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingZExpression);

	//zoom
	BIND_WIDGET_FUNCTION(EdtZoomXExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomXExpression);
	BIND_WIDGET_FUNCTION(EdtZoomYExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomYExpression);
	BIND_WIDGET_FUNCTION(EdtZoomZExpression, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomZExpression);

	//bind function to expression button
	//coordiante
	BIND_WIDGET_FUNCTION(BtnCoordinateXExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnCoordinateXExpression);
	BIND_WIDGET_FUNCTION(BtnCoordinateYExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnCoordinateYExpression);
	BIND_WIDGET_FUNCTION(BtnCoordinateZExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnCoordinateZExpression);

	//rotating
	BIND_WIDGET_FUNCTION(BtnRotatingXExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnRotatingXExpression);
	BIND_WIDGET_FUNCTION(BtnRotatingYExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnRotatingYExpression);
	BIND_WIDGET_FUNCTION(BtnRotatingZExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnRotatingZExpression);

	//zoom
	BIND_WIDGET_FUNCTION(BtnZoomXExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnZoomXExpression);
	BIND_WIDGET_FUNCTION(BtnZoomYExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnZoomYExpression);
	BIND_WIDGET_FUNCTION(BtnZoomZExpression, OnClicked,
		USingleComponentImportSection::OnClickedBtnZoomZExpression);

	//bind function to value text
	//coordinate
	BIND_WIDGET_FUNCTION(EdtCoordinateXValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedCoordinateXValue);
	BIND_WIDGET_FUNCTION(EdtCoordinateYValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedCoordinateYValue);
	BIND_WIDGET_FUNCTION(EdtCoordinateZValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedCoordinateZValue);
	
	//rotating
	BIND_WIDGET_FUNCTION(EdtRotatingXValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingXValue);
	BIND_WIDGET_FUNCTION(EdtRotatingYValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingYValue);
	BIND_WIDGET_FUNCTION(EdtRotatingZValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedRotatingZValue);
	
	//zoom
	BIND_WIDGET_FUNCTION(EdtZoomXValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomXValue);
	BIND_WIDGET_FUNCTION(EdtZoomYValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomYValue);
	BIND_WIDGET_FUNCTION(EdtZoomZValue, OnTextCommitted,
		USingleComponentImportSection::OnTextCommittedZoomZValue);


	return true;
}

void USingleComponentImportSection::UpdateContent(const FLocationProperty & InLocation, const FRotationProperty & InRotation, const FScaleProperty & InScale)
{
	if (IS_OBJECT_PTR_VALID(EdtCoordinateXExpression) && IS_OBJECT_PTR_VALID(EdtCoordinateXValue) && IS_OBJECT_PTR_VALID(EdtCoordinateYExpression)
		&& IS_OBJECT_PTR_VALID(EdtCoordinateYValue) && IS_OBJECT_PTR_VALID(EdtCoordinateZExpression) && IS_OBJECT_PTR_VALID(EdtCoordinateZValue))
	{
		EdtCoordinateXExpression->SetText(FText::FromString(InLocation.LocationX.Expression));
		EdtCoordinateXValue->SetText(FText::FromString(InLocation.LocationX.Value));
		EdtCoordinateYExpression->SetText(FText::FromString(InLocation.LocationY.Expression));
		EdtCoordinateYValue->SetText(FText::FromString(InLocation.LocationY.Value));
		EdtCoordinateZExpression->SetText(FText::FromString(InLocation.LocationZ.Expression));
		EdtCoordinateZValue->SetText(FText::FromString(InLocation.LocationZ.Value));
		this->InLocation = InLocation;
	}
	if (IS_OBJECT_PTR_VALID(EdtRotatingXExpression) && IS_OBJECT_PTR_VALID(EdtRotatingXValue) && IS_OBJECT_PTR_VALID(EdtRotatingYExpression)
		&& IS_OBJECT_PTR_VALID(EdtRotatingYValue) && IS_OBJECT_PTR_VALID(EdtRotatingZExpression) && IS_OBJECT_PTR_VALID(EdtRotatingZValue))
	{
		EdtRotatingXExpression->SetText(FText::FromString(InRotation.Roll.Expression));
		EdtRotatingXValue->SetText(FText::FromString(InRotation.Roll.Value));
		EdtRotatingYExpression->SetText(FText::FromString(InRotation.Pitch.Expression));
		EdtRotatingYValue->SetText(FText::FromString(InRotation.Pitch.Value));
		EdtRotatingZExpression->SetText(FText::FromString(InRotation.Yaw.Expression));
		EdtRotatingZValue->SetText(FText::FromString(InRotation.Yaw.Value));
		this->InRotation = InRotation;
	}
	if (IS_OBJECT_PTR_VALID(EdtZoomXExpression) && IS_OBJECT_PTR_VALID(EdtZoomXValue) && IS_OBJECT_PTR_VALID(EdtZoomYExpression)
		&& IS_OBJECT_PTR_VALID(EdtZoomYValue) && IS_OBJECT_PTR_VALID(EdtZoomZExpression) && IS_OBJECT_PTR_VALID(EdtZoomZValue))
	{
		EdtZoomXExpression->SetText(FText::FromString(InScale.X.Expression));
		EdtZoomXValue->SetText(FText::FromString(InScale.X.Value));
		EdtZoomYExpression->SetText(FText::FromString(InScale.Y.Expression));
		EdtZoomYValue->SetText(FText::FromString(InScale.Y.Value));
		EdtZoomZExpression->SetText(FText::FromString(InScale.Z.Expression));
		EdtZoomZValue->SetText(FText::FromString(InScale.Z.Value));
		this->InScale = InScale;
	}
}

USingleComponentImportSection * USingleComponentImportSection::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USingleComponentImportSection>(USingleComponentImportSection::SingleComponentImportSectionPath);
}

void USingleComponentImportSection::OnExpressionEditHandler(const int32 & EditType, const FString & InExpression)
{
	switch ((EImportExpressionType)EditType)
	{
	case EImportExpressionType::LocXExpress :
	{
		OnTextCommittedCoordinateXExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::LocYExpress:
	{
		OnTextCommittedCoordinateYExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::LocZExpress:
	{
		OnTextCommittedCoordinateZExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::RotXExpress:
	{
		OnTextCommittedRotatingXExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::RotYExpress:
	{
		OnTextCommittedRotatingYExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::RotZExpress:
	{
		OnTextCommittedRotatingZExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::ScaXExpress:
	{
		OnTextCommittedZoomXExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::ScaYExpress:
	{
		OnTextCommittedZoomYExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	case EImportExpressionType::ScaZExpress:
	{
		OnTextCommittedZoomZExpression(FText::FromString(InExpression), ETextCommit::Type::OnEnter);
		break;
	}
	default:
		break;
	}
}

void USingleComponentImportSection::OnClickedBtnCoordinateXExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtCoordinateXExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::LocXExpress, EdtCoordinateXExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnCoordinateYExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtCoordinateYExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::LocYExpress, EdtCoordinateYExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnCoordinateZExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtCoordinateZExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::LocZExpress, EdtCoordinateZExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnRotatingXExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtRotatingXExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::RotXExpress, EdtRotatingXExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnRotatingYExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtRotatingYExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::RotYExpress, EdtRotatingYExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnRotatingZExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtRotatingZExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::RotZExpress, EdtRotatingZExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnZoomXExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtZoomXExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::ScaXExpress, EdtZoomXExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnZoomYExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtZoomYExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::ScaYExpress, EdtZoomYExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnClickedBtnZoomZExpression()
{
	if (IS_OBJECT_PTR_VALID(EdtZoomZExpression))
	{
		BIND_EXPRESSION_WIDGET((int32)EImportExpressionType::ScaZExpress, EdtZoomZExpression->GetText().ToString(), FName(TEXT("OnExpressionEditHandler")));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateXExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateXExpress, InputText);
	}
	else
	{
		EdtCoordinateXExpression->SetText(FText::FromString(InLocation.LocationX.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateYExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateYExpress, InputText);
	}
	else
	{
		EdtCoordinateYExpression->SetText(FText::FromString(InLocation.LocationY.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateZExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateZExpress, InputText);
	}
	else
	{
		EdtCoordinateZExpression->SetText(FText::FromString(InLocation.LocationZ.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingXExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingXExpress, InputText);
	}
	else
	{
		EdtRotatingXExpression->SetText(FText::FromString(InRotation.Roll.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingYExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingYExpress, InputText);
	}
	else
	{
		EdtRotatingYExpression->SetText(FText::FromString(InRotation.Pitch.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingZExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingZExpress, InputText);
	}
	else
	{
		EdtRotatingZExpression->SetText(FText::FromString(InRotation.Yaw.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomXExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomXExpress, InputText);
	}
	else
	{
		EdtZoomXExpression->SetText(FText::FromString(InScale.X.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomYExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomYExpress, InputText);
	}
	else
	{
		EdtZoomYExpression->SetText(FText::FromString(InScale.Y.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomZExpression(const FText & Text, ETextCommit::Type CommitMethod)
{
	FString InputText = Text.ToString();
	InputText = InputText.TrimStart();
	InputText = InputText.TrimEnd();
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomZExpress, InputText);
	}
	else
	{
		EdtZoomZExpression->SetText(FText::FromString(InScale.Z.Expression));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateXValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateXValue, Text.ToString());
	}
	else
	{
		EdtCoordinateXValue->SetText(FText::FromString(InLocation.LocationX.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateYValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateYValue, Text.ToString());
	}
	else
	{
		EdtCoordinateYValue->SetText(FText::FromString(InLocation.LocationY.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedCoordinateZValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionLocationDelegate.ExecuteIfBound((int32)EImportLocationType::CoordinateZValue, Text.ToString());
	}
	else
	{
		EdtCoordinateZValue->SetText(FText::FromString(InLocation.LocationZ.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingXValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingXValue, Text.ToString());
	}
	else
	{
		EdtRotatingXValue->SetText(FText::FromString(InRotation.Roll.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingYValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingYValue, Text.ToString());
	}
	else
	{
		EdtRotatingYValue->SetText(FText::FromString(InRotation.Pitch.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedRotatingZValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionRotationDelegate.ExecuteIfBound((int32)EImportRotationType::RotatingZValue, Text.ToString());
	}
	else
	{
		EdtRotatingZValue->SetText(FText::FromString(InRotation.Yaw.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomXValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomXValue, Text.ToString());
	}
	else
	{
		EdtZoomXValue->SetText(FText::FromString(InScale.X.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomYValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomYValue, Text.ToString());
	}
	else
	{
		EdtZoomYValue->SetText(FText::FromString(InScale.Y.Value));
	}
}

void USingleComponentImportSection::OnTextCommittedZoomZValue(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared&&Text.IsNumeric())
	{
		ImportSectionScaleDelegate.ExecuteIfBound((int32)EImportScaleType::ZoomZValue, Text.ToString());
	}
	else
	{
		EdtZoomZValue->SetText(FText::FromString(InScale.Z.Value));
	}
}
