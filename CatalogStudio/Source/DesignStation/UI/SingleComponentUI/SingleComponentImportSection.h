// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "SingleComponentImportSection.generated.h"

UENUM(BlueprintType)
enum class EImportLocationType : uint8
{
	CoordinateXExpress = 0,
	CoordinateXValue,
	CoordinateYExpress,
	CoordinateYValue,
	CoordinateZExpress,
	CoordinateZValue
};

UENUM(BlueprintType)
enum class EImportRotationType : uint8
{
	RotatingXExpress = 0,
	RotatingXValue,
	RotatingYExpress,
	RotatingYValue,
	RotatingZExpress,
	RotatingZValue
};

UENUM(BlueprintType)
enum class EImportScaleType : uint8
{
	ZoomXExpress = 0,
	ZoomXValue,
	ZoomYExpress,
	ZoomYValue,
	ZoomZExpress,
	ZoomZValue
};

UENUM(BlueprintType)
enum class EImportExpressionType : uint8
{
	LocXExpress = 0,
	LocYExpress,
	LocZExpress,
	RotXExpress,
	RotYExpress,
	RotZExpress,
	ScaXExpress,
	ScaYExpress,
	ScaZExpress
};

class UButton;
class UEditableText;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FImportSectionLocationDelegate, const int32&, EditType, const FString&, OutString);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FImportSectionRotationDelegate, const int32&, EditType, const FString&, OutString);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FImportSectionScaleDelegate, const int32&, EditType, const FString&, OutString);

UCLASS()
class DESIGNSTATION_API USingleComponentImportSection : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;	
	void UpdateContent(const FLocationProperty& InLocation, const FRotationProperty& InRotation, const FScaleProperty& InScale);
	static USingleComponentImportSection * Create();

private:
	UFUNCTION()
		void OnExpressionEditHandler(const int32& EditType, const FString& InExpression);

public:
	FImportSectionLocationDelegate ImportSectionLocationDelegate;
	FImportSectionRotationDelegate ImportSectionRotationDelegate;
	FImportSectionScaleDelegate ImportSectionScaleDelegate;

private:
	static FString SingleComponentImportSectionPath;

	FLocationProperty	InLocation;
	FRotationProperty	InRotation;
	FScaleProperty		InScale;
protected:
	
	//Expression button
	UFUNCTION()
		void OnClickedBtnCoordinateXExpression();
	UFUNCTION()
		void OnClickedBtnCoordinateYExpression();
	UFUNCTION()
		void OnClickedBtnCoordinateZExpression();
	UFUNCTION()
		void OnClickedBtnRotatingXExpression();
	UFUNCTION()
		void OnClickedBtnRotatingYExpression();
	UFUNCTION()
		void OnClickedBtnRotatingZExpression();
	UFUNCTION()
		void OnClickedBtnZoomXExpression();
	UFUNCTION()
		void OnClickedBtnZoomYExpression();
	UFUNCTION()
		void OnClickedBtnZoomZExpression();

	//Expresstion text
	UFUNCTION()
		void OnTextCommittedCoordinateXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedCoordinateYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedCoordinateZExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingZExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomXExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomYExpression(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomZExpression(const FText& Text, ETextCommit::Type CommitMethod);

	//Value text
	UFUNCTION()
		void OnTextCommittedCoordinateXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedCoordinateYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedCoordinateZValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedRotatingZValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedZoomZValue(const FText& Text, ETextCommit::Type CommitMethod);

private:
	UPROPERTY()
		UEditableText*	EdtCoordinateXExpression;
	UPROPERTY()
		UEditableText*	EdtCoordinateYExpression;
	UPROPERTY()
		UEditableText*	EdtCoordinateZExpression;
	UPROPERTY()
		UEditableText*	EdtRotatingXExpression;
	UPROPERTY()
		UEditableText*	EdtRotatingYExpression;
	UPROPERTY()
		UEditableText*	EdtRotatingZExpression;
	UPROPERTY()
		UEditableText*	EdtZoomXExpression;
	UPROPERTY()
		UEditableText*	EdtZoomYExpression;
	UPROPERTY()
		UEditableText*	EdtZoomZExpression;
	UPROPERTY()
		UButton*		BtnCoordinateXExpression;
	UPROPERTY()
		UButton*		BtnCoordinateYExpression;
	UPROPERTY()
		UButton*		BtnCoordinateZExpression;
	UPROPERTY()
		UButton*		BtnRotatingXExpression;
	UPROPERTY()
		UButton*		BtnRotatingYExpression;
	UPROPERTY()
		UButton*		BtnRotatingZExpression;
	UPROPERTY()
		UButton*		BtnZoomXExpression;
	UPROPERTY()
		UButton*		BtnZoomYExpression;
	UPROPERTY()
		UButton*		BtnZoomZExpression;
	UPROPERTY()
		UEditableText*	EdtCoordinateXValue;
	UPROPERTY()
		UEditableText*	EdtCoordinateYValue;
	UPROPERTY()
		UEditableText*	EdtCoordinateZValue;
	UPROPERTY()
		UEditableText*	EdtRotatingXValue;
	UPROPERTY()
		UEditableText*	EdtRotatingYValue;
	UPROPERTY()
		UEditableText*	EdtRotatingZValue;
	UPROPERTY()
		UEditableText*	EdtZoomXValue;
	UPROPERTY()
		UEditableText*	EdtZoomYValue;
	UPROPERTY()
		UEditableText*	EdtZoomZValue;
};
