// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentImportWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/ProgressBar.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/EditableTextBox.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString USingleComponentImportWidget::SingleComponentImportPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentImportUI.SingleComponentImportUI_C'");

const FLinearColor NormalOrImportingColor = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
const FLinearColor SucceedColor = FLinearColor(0.093059f, 0.679543f, 0.14996f, 1.0f);
const FLinearColor FailedColor = FLinearColor(0.651406f, 0.03434f, 0.009134f, 1.0f);

bool USingleComponentImportWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, USingleComponentImportWidget::OnClickedBtnClose);
	BIND_PARAM_CPP_TO_UMG(BtnChangeImage, Btn_ChangeImage);
	BIND_WIDGET_FUNCTION(BtnChangeImage, OnClicked, USingleComponentImportWidget::OnClickedBtnChangeImage);
	BIND_PARAM_CPP_TO_UMG(ImgSection, Img_Section);
	BIND_PARAM_CPP_TO_UMG(TxtFileName, Txt_FileName);
	BIND_PARAM_CPP_TO_UMG(PBImport, PB_Import);
	BIND_PARAM_CPP_TO_UMG(ImgImportResult, Img_ImportResult);
	BIND_PARAM_CPP_TO_UMG(BtnImportFile, Btn_ImportFile);
	BIND_WIDGET_FUNCTION(BtnImportFile, OnClicked, USingleComponentImportWidget::OnClickedBtnImportFile);
	BIND_PARAM_CPP_TO_UMG(BtnCancel, Btn_Cancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnClicked, USingleComponentImportWidget::OnClickedBtnCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnHovered, USingleComponentImportWidget::OnHoveredBtnCancel);
	BIND_WIDGET_FUNCTION(BtnCancel, OnUnhovered, USingleComponentImportWidget::OnUnHoveredBtnCancel);
	BIND_PARAM_CPP_TO_UMG(TxtCancel, Txt_Cancel);
	BIND_PARAM_CPP_TO_UMG(BtnSure, Btn_Sure);
	BIND_WIDGET_FUNCTION(BtnSure, OnClicked, USingleComponentImportWidget::OnClickedBtnSure);

	BIND_PARAM_CPP_TO_UMG(SbPath, Sb_Path);

	BIND_PARAM_CPP_TO_UMG(EdtSave, Edt_Save);
	BIND_PARAM_CPP_TO_UMG(EdtRef, Edt_Ref);

	BIND_WIDGET_FUNCTION(EdtSave, OnTextChanged, USingleComponentImportWidget::OnTextChangedEdtSave);
	BIND_WIDGET_FUNCTION(EdtRef, OnTextChanged, USingleComponentImportWidget::OnTextChangedEdtRef);
	BIND_WIDGET_FUNCTION(EdtSave, OnTextCommitted, USingleComponentImportWidget::OnTextCommittedEdtSave);
	BIND_WIDGET_FUNCTION(EdtRef, OnTextCommitted, USingleComponentImportWidget::OnTextCommittedEdtRef);

	CurrentType = EImportStateType::None;
	ShowEdtPath(false);

	return true;
}

void USingleComponentImportWidget::UpdateContent(float InProgress)
{
	if (IS_OBJECT_PTR_VALID(PBImport))
	{
		PBImport->SetPercent(InProgress);
	}
}

void USingleComponentImportWidget::UpdateState(const EImportStateType& InState)
{
	if (InState == CurrentType)
	{
		return;
	}
	UpdateImportWidgetState(InState);
	CurrentType = InState;
}

void USingleComponentImportWidget::UpdateImage(const FString& ThumbnailPath)
{
	UTexture2D* ImportImage = FCatalogFunctionLibrary::LoadTextureFromJPG(ThumbnailPath);
	if (IS_OBJECT_PTR_VALID(ImportImage) && IS_OBJECT_PTR_VALID(ImgSection))
	{
		ImgSection->SetBrushFromTexture(ImportImage);
	}
}

void USingleComponentImportWidget::UpdateImportName(const FString& InName)
{
	if (IS_OBJECT_PTR_VALID(TxtFileName))
	{
		TxtFileName->SetText(FText::FromString(InName));
	}
}

USingleComponentImportWidget* USingleComponentImportWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USingleComponentImportWidget>(USingleComponentImportWidget::SingleComponentImportPath);
}

void USingleComponentImportWidget::ShowEdtPath(bool IsShow)
{
	BtnSure->SetIsEnabled(!IsShow);
	SbPath->SetVisibility(IsShow ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	SavePath = TEXT("");
	RefPath = TEXT("");
	EdtSave->SetText(FText::FromString(SavePath));
	EdtRef->SetText(FText::FromString(RefPath));
}

void USingleComponentImportWidget::UpdateImportWidgetState(const EImportStateType& InType)
{
	if (IS_OBJECT_PTR_VALID(PBImport) && IS_OBJECT_PTR_VALID(ImgImportResult) && IS_OBJECT_PTR_VALID(BtnSure))
	{
		if (!IS_OBJECT_PTR_VALID(NormalOrImportingTexture) || !IS_OBJECT_PTR_VALID(SucceedTexture) || !IS_OBJECT_PTR_VALID(FailedTexture))
		{
			GetTexture();
		}
		if (InType == EImportStateType::Normal)
		{
			PBImport->SetFillColorAndOpacity(NormalOrImportingColor);
			ImgImportResult->SetBrushFromTexture(NormalOrImportingTexture);
			BtnImportFile->SetIsEnabled(true);
			BtnSure->SetIsEnabled(false);
		}
		else if (InType == EImportStateType::Importing)
		{
			PBImport->SetFillColorAndOpacity(NormalOrImportingColor);
			ImgImportResult->SetBrushFromTexture(NormalOrImportingTexture);
			BtnImportFile->SetIsEnabled(false);
			BtnSure->SetIsEnabled(false);
		}
		else if (InType == EImportStateType::Succeed)
		{
			PBImport->SetFillColorAndOpacity(SucceedColor);
			ImgImportResult->SetBrushFromTexture(SucceedTexture);
			BtnImportFile->SetIsEnabled(true);
			BtnSure->SetIsEnabled(true);
		}
		else if (InType == EImportStateType::Failed)
		{
			PBImport->SetFillColorAndOpacity(FailedColor);
			ImgImportResult->SetBrushFromTexture(FailedTexture);
			BtnImportFile->SetIsEnabled(true);
			BtnSure->SetIsEnabled(false);
		}
	}
}

void USingleComponentImportWidget::GetTexture()
{
	NormalOrImportingTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/midd.midd'")));
	SucceedTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/complete.complete'")));
	FailedTexture = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/mistake.mistake'")));
}

void USingleComponentImportWidget::ResetImportImage()
{
	if (!IS_OBJECT_PTR_VALID(DefaultImportImage))
	{
		DefaultImportImage = Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, TEXT("Texture2D'/Game/UI/UIIcon/SingleComponent/Import/IMG.IMG'")));
	}
	if (IS_OBJECT_PTR_VALID(ImgSection))
	{
		ImgSection->SetBrushFromTexture(DefaultImportImage);
	}
}

void USingleComponentImportWidget::OnClickedBtnClose()
{
	UpdateState(EImportStateType::Normal);
	ResetImportImage();
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void USingleComponentImportWidget::OnClickedBtnChangeImage()
{
	ImportActionDelegate.ExecuteIfBound(EImportActionType::ChangeImage, TEXT(""), TEXT(""));
}

void USingleComponentImportWidget::OnClickedBtnImportFile()
{
	ResetImportImage();
	ImportActionDelegate.ExecuteIfBound(EImportActionType::ImportFile, TEXT(""), TEXT(""));
}

void USingleComponentImportWidget::OnClickedBtnCancel()
{
	UpdateState(EImportStateType::Normal);
	ResetImportImage();
	this->SetVisibility(ESlateVisibility::Collapsed);
	ImportActionDelegate.ExecuteIfBound(EImportActionType::Cancel, TEXT(""), TEXT(""));
}

void USingleComponentImportWidget::OnHoveredBtnCancel()
{
	if (IS_OBJECT_PTR_VALID(TxtCancel))
	{
		TxtCancel->SetColorAndOpacity(FSlateColor(FLinearColor::White));
	}
}

void USingleComponentImportWidget::OnUnHoveredBtnCancel()
{
	if (IS_OBJECT_PTR_VALID(TxtCancel))
	{
		TxtCancel->SetColorAndOpacity(FSlateColor(FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f)));
	}
}

void USingleComponentImportWidget::OnClickedBtnSure()
{
	UpdateState(EImportStateType::Normal);
	ResetImportImage();
	this->SetVisibility(ESlateVisibility::Collapsed);
	ImportActionDelegate.ExecuteIfBound(EImportActionType::Sure, SavePath, RefPath);
}

void USingleComponentImportWidget::OnTextChangedEdtSave(const FText& Text)
{
	BtnSure->SetIsEnabled(!EdtRef->GetText().IsEmpty() && !Text.IsEmpty());
}

void USingleComponentImportWidget::OnTextCommittedEdtSave(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SavePath = Text.ToString();
	}
}

void USingleComponentImportWidget::OnTextChangedEdtRef(const FText& Text)
{
	BtnSure->SetIsEnabled(!EdtSave->GetText().IsEmpty() && !Text.IsEmpty());
}

void USingleComponentImportWidget::OnTextCommittedEdtRef(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		RefPath = Text.ToString();
		//加入依据引用路径自动计算PAK文件保存路径的功能
		//引用路径形如Blueprint'/Game/SoftLoading/HomeDecoration/Ornament/26/BP_1.BP_1'
		//从Game后现第一个/处开始截取(不包含/)，至最后一个/结束(不包含/)
		FString Left(TEXT(""));
		FString Right(TEXT(""));
		bool bValidPath = RefPath.Split(TEXT("/Game/"), &Left, &Right, ESearchCase::CaseSensitive);
		if (!bValidPath) return;
		int32 PathEndPos = INDEX_NONE;
		bValidPath = Right.FindLastChar('/', PathEndPos);
		if (!bValidPath) return;
		SavePath = FString::Printf(TEXT("%s/1.PAK"), *Right.Left(PathEndPos));
		EdtSave->SetText(FText::FromString(SavePath));
	}
}
