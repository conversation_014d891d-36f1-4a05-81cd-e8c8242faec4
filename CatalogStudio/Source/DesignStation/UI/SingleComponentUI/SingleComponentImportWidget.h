// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentImportWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;
class UTextBlock;
class UProgressBar;
class UEditableText;
class USizeBox;
class UEditableTextBox;

UENUM(BlueprintType)
enum class EImportStateType : uint8
{
	Normal = 0,
	Importing,
	Succeed,
	Failed,
	None
};

UENUM(BlueprintType)
enum class EImportActionType : uint8
{
	ChangeImage = 0,
	ImportFile,
	Cancel,
	Sure,
	ImportCADFile
};

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FImportActionDelegate, const EImportActionType&, ActionType,const FString&,InSavePath,const FString&,InRefPath);

UCLASS()
class DESIGNSTATION_API USingleComponentImportWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(float InProgress);
	void UpdateState(const EImportStateType& InState);
	void UpdateImage(const FString& ThumbnailPath);
	void UpdateImportName(const FString& InName);

	static USingleComponentImportWidget* Create();

	void ShowEdtPath(bool IsShow);

private:
	void UpdateImportWidgetState(const EImportStateType& InType);
	void GetTexture();
	void ResetImportImage();

public:
	FImportActionDelegate ImportActionDelegate;

private:
	EImportStateType CurrentType;

	UPROPERTY()
		UTexture2D* DefaultImportImage;
	UPROPERTY()
		UTexture2D* NormalOrImportingTexture;
	UPROPERTY()
		UTexture2D* SucceedTexture;
	UPROPERTY()
		UTexture2D* FailedTexture;
	
	static FString SingleComponentImportPath;

	FString SavePath;
	FString	RefPath;

protected:
	UFUNCTION()
		void OnClickedBtnClose();
	UFUNCTION()
		void OnClickedBtnChangeImage();
	UFUNCTION()
		void OnClickedBtnImportFile();
	UFUNCTION()
		void OnClickedBtnCancel();
	UFUNCTION()
		void OnHoveredBtnCancel();
	UFUNCTION()
		void OnUnHoveredBtnCancel();
	UFUNCTION()
		void OnClickedBtnSure();
	UFUNCTION()
		void OnTextChangedEdtSave(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtSave(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextChangedEdtRef(const FText& Text);
	UFUNCTION()
		void OnTextCommittedEdtRef(const FText& Text, ETextCommit::Type CommitMethod);
private:
	UPROPERTY()
		UButton* BtnClose;

	UPROPERTY()
		UButton* BtnChangeImage;
	UPROPERTY()
		UImage* ImgSection;

	UPROPERTY()
		UTextBlock* TxtFileName;
	UPROPERTY()
		UProgressBar* PBImport;

	UPROPERTY()
		UImage* ImgImportResult;
	UPROPERTY()
		UButton* BtnImportFile;

	UPROPERTY()
		UButton* BtnCancel;
	UPROPERTY()
		UTextBlock* TxtCancel;
	UPROPERTY()
		UButton* BtnSure;
	UPROPERTY()
		UEditableTextBox*	EdtSave;
	UPROPERTY()
		UEditableTextBox*	EdtRef;
	UPROPERTY()
		USizeBox*	SbPath;
};
