// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentLine.h"

#include "Components/Border.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"


#define LOCTEXT_NAMESPACE "SingleComponenetLineType"

const TArray<FString> SingleComponentLineType =
{
	NSLOCTEXT(LOCTEXT_NAMESPACE, "SingleLineSegment", "LineSegment").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "SingleHighArc", "HighArc").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "SingleRadiusArc", "RadiusArc").ToString()
};

const TArray<FString> UnitName =
{
	NSLOCTEXT(LOCTEXT_NAMESPACE, "LineUnit", "None").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "HeightArcUnit", "Height").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "RadiusArcUnit", "Radius").ToString()
};

const TArray<FString> RadianType =
{
	NSLOCTEXT(LOCTEXT_NAMESPACE, "BigArcUnit", "BigArc").ToString(),
	NSLOCTEXT(LOCTEXT_NAMESPACE, "SmallArcUnit", "SmallArc").ToString()
};

FString USingleComponentLine::LineBpPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponenetLine.SingleComponenetLine_C'");

bool USingleComponentLine::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorLineIndex, Bor_LineIndex);
	BIND_PARAM_CPP_TO_UMG(TxtLineIndex, Txt_LineIndex);

	BIND_PARAM_CPP_TO_UMG(BorType, Bor_Type);
	BIND_PARAM_CPP_TO_UMG(TxtType, Txt_Type);
	BIND_PARAM_CPP_TO_UMG(BorLineType, Bor_LineType);
	BIND_PARAM_CPP_TO_UMG(CBSLineType, CBS_LineType);
	BIND_WIDGET_FUNCTION(CBSLineType, OnOpening, USingleComponentLine::OnOpeningCBSType);
	BIND_WIDGET_FUNCTION(CBSLineType, OnSelectionChanged, USingleComponentLine::OnSelectChangedCBSType);

	BIND_PARAM_CPP_TO_UMG(BorRadian, Bor_Radian);
	BIND_PARAM_CPP_TO_UMG(TxtRadian, Txt_Radian);
	BIND_PARAM_CPP_TO_UMG(BorRadianType, Bor_RadianType);
	BIND_PARAM_CPP_TO_UMG(CBSRadianType, CBS_RadianType);
	BIND_WIDGET_FUNCTION(CBSRadianType, OnOpening, USingleComponentLine::OnOpeningCBSRadian);
	BIND_WIDGET_FUNCTION(CBSRadianType, OnSelectionChanged, USingleComponentLine::OnSelectChangedCBSRadianType);

	BIND_PARAM_CPP_TO_UMG(BorUnitName, Bor_UnitName);
	BIND_PARAM_CPP_TO_UMG(TxtUnitName, Txt_UnitName);
	BIND_PARAM_CPP_TO_UMG(BorRadiusExpress, Bor_RadiusExpress);
	BIND_PARAM_CPP_TO_UMG(TxtRadiusExpression, Txt_RadiusExpression);
	BIND_WIDGET_FUNCTION(TxtRadiusExpression, OnTextCommitted, USingleComponentLine::OnTextCommittedEdtRadiusExpress);
	BIND_PARAM_CPP_TO_UMG(BtnRadiusExpression, Btn_RadiusExpression);
	BIND_WIDGET_FUNCTION(BtnRadiusExpression, OnClicked, USingleComponentLine::OnClickedBtnRadiusExpression);
	BIND_PARAM_CPP_TO_UMG(BorRadiusValue, Bor_RadiusValue);
	BIND_PARAM_CPP_TO_UMG(EdtRadiusValue, Edt_RadiusValue);
	BIND_WIDGET_FUNCTION(EdtRadiusValue, OnTextCommitted, USingleComponentLine::OnTextCommittedEdtRadiusValue);

	BIND_PARAM_CPP_TO_UMG(BorPointNum, Bor_PointNum);
	BIND_PARAM_CPP_TO_UMG(TxtPointNum, Txt_PointNum);
	BIND_PARAM_CPP_TO_UMG(BorPointNumExpress, Bor_PointNumExpress);
	BIND_PARAM_CPP_TO_UMG(EdtPointNumExpress, Edt_PointNumExpress);
	BIND_WIDGET_FUNCTION(EdtPointNumExpress, OnTextCommitted, USingleComponentLine::OnTextCommittedEdtPointNumExpress);
	BIND_PARAM_CPP_TO_UMG(BtnPointNumExpress, Btn_PointNUmExpress);
	BIND_WIDGET_FUNCTION(BtnPointNumExpress, OnClicked, USingleComponentLine::OnClickedBtnPointNum);
	BIND_PARAM_CPP_TO_UMG(BorPointNumValue, Bor_PointNumValue);
	BIND_PARAM_CPP_TO_UMG(EdtPointNumValue, Edt_PointNumValue);
	BIND_WIDGET_FUNCTION(EdtPointNumValue, OnTextCommitted, USingleComponentLine::OnTextCommittedEdtPointNumValue);

	InitCBSLineType();
	InitCBSRadianType();
	return true;
}

void USingleComponentLine::UpdateContent(const FGeomtryLineProperty& InData)
{
	LineProperty.CopyData(InData);
	IsViewSelect = false;
	if (IS_OBJECT_PTR_VALID(CBSLineType))
	{
		CBSLineType->SetSelectedOption(SingleComponentLineType[(int)InData.LineType]);
	}
	if (IS_OBJECT_PTR_VALID(CBSRadianType))
	{
		CBSRadianType->SetSelectedOption(InData.BigArc ? RadianType[0] : RadianType[1]);
	}
	if (IS_OBJECT_PTR_VALID(TxtUnitName))
	{
		//TxtUnitName->SetText(FText::FromString(UnitName[(int)InData.LineType]));
		TxtUnitName->SetText(FText::FromStringTable(FName("PosSt"), UnitName[(int)InData.LineType]));
	}
	if (IS_OBJECT_PTR_VALID(TxtRadiusExpression) && IS_OBJECT_PTR_VALID(EdtRadiusValue))
	{
		TxtRadiusExpression->SetText(FText::FromString(InData.RadiusOrHeightData.Expression));
		EdtRadiusValue->SetText(FText::FromString(InData.RadiusOrHeightData.Value));
	}
	if (IS_OBJECT_PTR_VALID(EdtPointNumExpress) && IS_OBJECT_PTR_VALID(EdtPointNumValue))
	{
		EdtPointNumExpress->SetText(FText::FromString(InData.InterpPointCountData.Expression));
		EdtPointNumValue->SetText(FText::FromString(InData.InterpPointCountData.Value));
	}
	/*EdtRadiusValue->SetText(UParameterPropertyData::FormatParameterValue(InData.RadiusOrHeightData.Value));
	if (InData.RadiusOrHeightData.Expression.IsNumeric())
	{
		TxtRadiusExpression->SetText(EdtRadiusValue->GetText());
	}
	else
	{
		TxtRadiusExpression->SetText(FText::FromString(InData.RadiusOrHeightData.Expression));
	}*/
	if (InData.LineType == ELineType::ELineSegment)
	{
		SetLineSegmentWidgetState();
	}
	else if (InData.LineType == ELineType::EHeightArc)
	{
		SetHighArcWidgetState();
	}
	else if (InData.LineType == ELineType::ERadiusArc)
	{
		SetRadiusArcWidgetState();
	}
}

void USingleComponentLine::UpdateLineIndex(int Index)
{
	if (TxtLineIndex)
	{
		TxtLineIndex->SetText(FText::FromString(UKismetStringLibrary::Conv_IntToString(Index)));
	}
}

void USingleComponentLine::UpdateSelectState(bool IsSelect)
{
	/*if (BorBackground)
	{
		UUIFunctionLibrary::SetBorderBrush(IsSelect, BorBackground);
	}*/
	if (IS_OBJECT_PTR_VALID(BorLineIndex) && IS_OBJECT_PTR_VALID(BorType) && IS_OBJECT_PTR_VALID(BorUnitName) && IS_OBJECT_PTR_VALID(BorPointNum)
		&& IS_OBJECT_PTR_VALID(BorRadiusExpress) && IS_OBJECT_PTR_VALID(BorRadiusValue) && IS_OBJECT_PTR_VALID(BorPointNumExpress) && IS_OBJECT_PTR_VALID(BorPointNumValue)
		&& IS_OBJECT_PTR_VALID(BorLineType) && IS_OBJECT_PTR_VALID(BorRadian) && IS_OBJECT_PTR_VALID(BorRadianType))
	{
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorLineIndex);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorType);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorUnitName);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorPointNum);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorRadiusExpress);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorRadiusValue);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorPointNumExpress);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorPointNumValue);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorLineType);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorRadian);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorRadianType);
	}
	if (IS_OBJECT_PTR_VALID(TxtLineIndex) && IS_OBJECT_PTR_VALID(TxtType) && IS_OBJECT_PTR_VALID(TxtRadian)
		&& IS_OBJECT_PTR_VALID(TxtUnitName) && IS_OBJECT_PTR_VALID(TxtPointNum))
	{
		TxtLineIndex->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtType->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtRadian->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtUnitName->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtPointNum->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
	}
}

USingleComponentLine* USingleComponentLine::Create()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponenetLine---create section line"));
	UClass* LineViewBp = LoadClass<UUserWidget>(NULL, *LineBpPath);
	checkf(LineViewBp, TEXT("load line view bp error!"));
	USingleComponentLine* LineViewTemp = CreateWidget<USingleComponentLine>(GWorld.GetReference(), LineViewBp);
	checkf(LineViewTemp, TEXT("create line view error!"));
	return LineViewTemp;
}

void USingleComponentLine::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsViewSelect)
	{
		UpdateSelectState(true);
		LineWidgetSelectDelegate.ExecuteIfBound((int32)ESectionSelectType::Line, LineProperty.ID, true, true);
	}
}

void USingleComponentLine::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsViewSelect)
	{
		UpdateSelectState(false);
		LineWidgetSelectDelegate.ExecuteIfBound((int32)ESectionSelectType::Line, LineProperty.ID, false, false);
	}
}

FReply USingleComponentLine::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		UpdateSelectState(false);
		IsViewSelect = false;
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void USingleComponentLine::LineExpressionEdit(const int32& EditType, const FString& InExpression)
{

	if (EditType == (int32)ELineExpressionType::RadiusOrHeight)
	{
		if (TxtRadiusExpression)
		{
			TxtRadiusExpression->SetText(FText::FromString(InExpression));
			LineProperty.RadiusOrHeightData.Expression = InExpression;
			LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::RadiusOrHighExpress, LineProperty);
		}
	}
	else if (EditType == (int32)ELineExpressionType::PointNum)
	{
		if (EdtPointNumExpress)
		{
			EdtPointNumExpress->SetText(FText::FromString(InExpression));
			LineProperty.InterpPointCountData.Expression = InExpression;
			LinePropertyChangeDelegate.ExecuteIfBound(4, LineProperty);
		}
	}

}

void USingleComponentLine::OnOpeningCBSType()
{
}

void USingleComponentLine::OnSelectChangedCBSType(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponenetLine---CBS select change"));
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		if (SelectedItem == SingleComponentLineType[0])
		{
			LineProperty.LineType = ELineType::ELineSegment;
			SetLineSegmentWidgetState();
			UpdateUnitName(UnitName[0]);
		}
		else if (SelectedItem == SingleComponentLineType[1])
		{
			LineProperty.LineType = ELineType::EHeightArc;
			SetHighArcWidgetState();
			UpdateUnitName(UnitName[1]);
		}
		else if (SelectedItem == SingleComponentLineType[2])
		{
			LineProperty.LineType = ELineType::ERadiusArc;
			SetRadiusArcWidgetState();
			UpdateUnitName(UnitName[2]);
		}
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::LineType, LineProperty);
	}
}

void USingleComponentLine::OnOpeningCBSRadian()
{
}

void USingleComponentLine::OnSelectChangedCBSRadianType(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		LineProperty.BigArc = (SelectedItem == RadianType[0]);
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::IsBigArc, LineProperty);
	}
}

void USingleComponentLine::OnClickedBtnRadiusExpression()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponenetLine---edit radius expression"));
	if (UExpressionPopWidget::Get() && TxtRadiusExpression)
	{
		UExpressionPopWidget::Get()->UpdateContent((int32)ELineExpressionType::RadiusOrHeight, TxtRadiusExpression->GetText().ToString());
		UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("LineExpressionEdit")));
		UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
}

void USingleComponentLine::OnTextCommittedEdtRadiusExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		LineProperty.RadiusOrHeightData.Expression = Text.ToString();
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::RadiusOrHighExpress, LineProperty);
	}
	else
	{
		TxtRadiusExpression->SetText(FText::FromString(LineProperty.RadiusOrHeightData.Expression));
	}
}

void USingleComponentLine::OnTextCommittedEdtRadiusValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		LineProperty.RadiusOrHeightData.Value = FString::SanitizeFloat(Value);
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::RadiusOrHigh, LineProperty);
	}
	else if (IS_OBJECT_PTR_VALID(EdtRadiusValue) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtRadiusValue->SetText(FText::FromString(LineProperty.RadiusOrHeightData.Value));
	}
}

void USingleComponentLine::OnClickedBtnPointNum()
{
	if (IS_OBJECT_PTR_VALID(EdtPointNumExpress))
	{
		BIND_EXPRESSION_WIDGET((int32)ELineExpressionType::PointNum, EdtPointNumExpress->GetText().ToString(), FName(TEXT("LineExpressionEdit")));
	}
}

void USingleComponentLine::OnTextCommittedEdtPointNumExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		LineProperty.InterpPointCountData.Expression = Text.ToString();
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::PointNUmExpress, LineProperty);
	}
	else
	{
		EdtPointNumExpress->SetText(FText::FromString(LineProperty.InterpPointCountData.Expression));
	}
}

void USingleComponentLine::OnTextCommittedEdtPointNumValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponenetLine---edit point num"));
	if (!Text.IsEmpty() && Text.IsNumeric() && FCString::Atoi(*Text.ToString()) > 0 && CommitMethod != ETextCommit::Type::OnCleared)
	{
		LineProperty.InterpPointCountData.Value = Text.ToString();
		LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::PointNum, LineProperty);
	}
	else if (IS_OBJECT_PTR_VALID(EdtPointNumValue) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtPointNumValue->SetText(FText::FromString(LineProperty.InterpPointCountData.Value));
	}
}

//void USingleComponentLine::OnStateChangedCkbBigArc(bool IsChecked)
//{
//	UE_LOG(LogTemp, Log, TEXT("SingleComponenetLine---edit line is big arc or not"));
//	LineProperty.BigArc = IsChecked;
//	LinePropertyChangeDelegate.ExecuteIfBound((int32)ELineChangeType::IsBigArc, LineProperty);
//}

void USingleComponentLine::UpdateUnitName(const FString& InName)
{
	if (TxtUnitName)
	{
		TxtUnitName->SetText(FText::FromString(InName));
	}
}

void USingleComponentLine::ResetWidgetState()
{
	if (BtnRadiusExpression && EdtRadiusValue && EdtPointNumValue && CBSRadianType && EdtPointNumExpress)
	{
		BtnRadiusExpression->SetIsEnabled(true);
		EdtRadiusValue->SetIsEnabled(true);
		TxtRadiusExpression->SetIsEnabled(true);
		EdtPointNumExpress->SetIsEnabled(true);
		BtnPointNumExpress->SetIsEnabled(true);
		EdtPointNumValue->SetIsEnabled(true);
		CBSRadianType->SetIsEnabled(true);
		//CkbBigArc->SetIsEnabled(true);
	}
}

void USingleComponentLine::SetLineSegmentWidgetState()
{
	if (BtnRadiusExpression && EdtRadiusValue && EdtPointNumValue && CBSRadianType && EdtPointNumExpress)
	{
		BtnRadiusExpression->SetIsEnabled(false);
		EdtRadiusValue->SetIsEnabled(false);
		TxtRadiusExpression->SetIsEnabled(false);
		EdtPointNumExpress->SetIsEnabled(false);
		BtnPointNumExpress->SetIsEnabled(false);
		EdtPointNumValue->SetIsEnabled(false);
		CBSRadianType->SetIsEnabled(false);
		//CkbBigArc->SetIsEnabled(false);
	}
}

void USingleComponentLine::SetHighArcWidgetState()
{
	if (BtnRadiusExpression && EdtRadiusValue && EdtPointNumValue && CBSRadianType && EdtPointNumExpress)
	{
		BtnRadiusExpression->SetIsEnabled(true);
		EdtRadiusValue->SetIsEnabled(true);
		TxtRadiusExpression->SetIsEnabled(true);
		EdtPointNumExpress->SetIsEnabled(true);
		BtnPointNumExpress->SetIsEnabled(true);
		EdtPointNumValue->SetIsEnabled(true);
		CBSRadianType->SetIsEnabled(false);
		//CkbBigArc->SetIsEnabled(false);
	}
}

void USingleComponentLine::SetRadiusArcWidgetState()
{
	ResetWidgetState();
}

void USingleComponentLine::InitCBSLineType()
{
	if (CBSLineType)
	{
		for (const FString& Type : SingleComponentLineType)
		{
			CBSLineType->AddOption(Type);
		}
		CBSLineType->RefreshOptions();
	}
}

void USingleComponentLine::InitCBSRadianType()
{
	if (IS_OBJECT_PTR_VALID(CBSRadianType))
	{
		for (const FString& Type : RadianType)
		{
			CBSRadianType->AddOption(Type);
		}
		CBSRadianType->RefreshOptions();
	}
}

#undef LOCTEXT_NAMESPACE