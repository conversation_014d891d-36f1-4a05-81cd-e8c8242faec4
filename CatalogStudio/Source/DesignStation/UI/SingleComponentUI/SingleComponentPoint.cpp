// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentPoint.h"

#include "Components/Border.h"
#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/Engine/Classes/Kismet/KismetStringLibrary.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString USingleComponentPoint::PointBpPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentPoint.SingleComponentPoint_C'");

bool USingleComponentPoint::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BorPointIndex, Bor_PointIndex);
	BIND_PARAM_CPP_TO_UMG(TxtPointIndex, Txt_PointIndex);
	BIND_PARAM_CPP_TO_UMG(BorX, Bor_X);
	BIND_PARAM_CPP_TO_UMG(TxtX, Txt_X);
	BIND_PARAM_CPP_TO_UMG(BorXExpress, Bor_XExpress);
	BIND_PARAM_CPP_TO_UMG(TxtXExpression, Txt_XExpression);
	BIND_WIDGET_FUNCTION(TxtXExpression, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtXExpress);
	BIND_PARAM_CPP_TO_UMG(BtnXExpression, Btn_XExpression);
	BIND_WIDGET_FUNCTION(BtnXExpression, OnClicked, USingleComponentPoint::OnClickedBtnXExpression);
	BIND_PARAM_CPP_TO_UMG(BorXValue, Bor_XValue);
	BIND_PARAM_CPP_TO_UMG(EdtXValue, Edt_XValue);
	BIND_WIDGET_FUNCTION(EdtXValue, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtXValue);

	BIND_PARAM_CPP_TO_UMG(BorY, Bor_Y);
	BIND_PARAM_CPP_TO_UMG(TxtY, Txt_Y);
	BIND_PARAM_CPP_TO_UMG(BorYExpress, Bor_YExpress);
	BIND_PARAM_CPP_TO_UMG(TxtYExpression, Txt_YExpression);
	BIND_WIDGET_FUNCTION(TxtYExpression, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtYExpress);
	BIND_PARAM_CPP_TO_UMG(BtnYExpression, Btn_YExpression);
	BIND_WIDGET_FUNCTION(BtnYExpression, OnClicked, USingleComponentPoint::OnClickedBtnYExpression);
	BIND_PARAM_CPP_TO_UMG(BorYValue, Bor_YValue);
	BIND_PARAM_CPP_TO_UMG(EdtYValue, Edt_YValue);
	BIND_WIDGET_FUNCTION(EdtYValue, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtYValue);

	BIND_PARAM_CPP_TO_UMG(BorZ, Bor_Z);
	BIND_PARAM_CPP_TO_UMG(TxtZ, Txt_Z);
	BIND_PARAM_CPP_TO_UMG(BorZExpress, Bor_ZExpress);
	BIND_PARAM_CPP_TO_UMG(TxtZExpression, Txt_ZExpression);
	BIND_WIDGET_FUNCTION(TxtZExpression, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtZExpress);
	BIND_PARAM_CPP_TO_UMG(BtnZExpression, Btn_ZExPression);
	BIND_WIDGET_FUNCTION(BtnZExpression, OnClicked, USingleComponentPoint::OnClickedBtnZExpression);
	BIND_PARAM_CPP_TO_UMG(BorZValue, Bor_ZValue);
	BIND_PARAM_CPP_TO_UMG(EdtZValue, Edt_ZValue);
	BIND_WIDGET_FUNCTION(EdtZValue, OnTextCommitted, USingleComponentPoint::OnTextCommittedEdtZValue);

	return true;
}

void USingleComponentPoint::UpdateContent(const FGeomtryPointProperty& InData)
{
	PointProperty.CopyData(InData);
	IsViewSelect = false;
	EdtXValue->SetText(UParameterPropertyData::FormatParameterValue(InData.LocationX.Value));
	if (InData.LocationX.Expression.IsNumeric())
	{
		TxtXExpression->SetText(EdtXValue->GetText());
	}
	else
	{
		TxtXExpression->SetText(FText::FromString(InData.LocationX.Expression));
	}
	EdtYValue->SetText(UParameterPropertyData::FormatParameterValue(InData.LocationY.Value));
	if (InData.LocationY.Expression.IsNumeric())
	{
		TxtYExpression->SetText(EdtYValue->GetText());
	}
	else
	{
		TxtYExpression->SetText(FText::FromString(InData.LocationY.Expression));
	}
	EdtZValue->SetText(UParameterPropertyData::FormatParameterValue(InData.LocationZ.Value));
	if (InData.LocationZ.Expression.IsNumeric())
	{
		TxtZExpression->SetText(EdtZValue->GetText());
	}
	else
	{
		TxtZExpression->SetText(FText::FromString(InData.LocationZ.Expression));
	}
	//CkbAbsolute->SetIsChecked(InData.PositionType == EPositionType::EAbsolute);
}

void USingleComponentPoint::UpdatePointIndex(int Index)
{
	if (TxtPointIndex)
	{
		TxtPointIndex->SetText(FText::FromString(UKismetStringLibrary::Conv_IntToString(Index)));
		
	}
}

void USingleComponentPoint::UpdateSelectState(bool IsSelect)
{
	/*if (BorBackground)
	{
		UUIFunctionLibrary::SetBorderBrush(IsSelect, BorBackground);
	}*/
	if (IS_OBJECT_PTR_VALID(BorPointIndex) && IS_OBJECT_PTR_VALID(BorX) && IS_OBJECT_PTR_VALID(BorY) && IS_OBJECT_PTR_VALID(BorZ)
		&& IS_OBJECT_PTR_VALID(BorXExpress) && IS_OBJECT_PTR_VALID(BorYExpress) && IS_OBJECT_PTR_VALID(BorZExpress)
		&& IS_OBJECT_PTR_VALID(BorXValue) && IS_OBJECT_PTR_VALID(BorYValue) && IS_OBJECT_PTR_VALID(BorZValue))
	{
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorPointIndex);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorX);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorY);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionNormal, BorZ);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorXExpress);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorYExpress);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorZExpress);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorXValue);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorYValue);
		UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? SectionHoverOrSelect : SectionValueNormal, BorZValue);
	}
	if (IS_OBJECT_PTR_VALID(TxtPointIndex) && IS_OBJECT_PTR_VALID(TxtX) && IS_OBJECT_PTR_VALID(TxtY) && IS_OBJECT_PTR_VALID(TxtZ))
	{
		TxtPointIndex->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtX->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtY->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtZ->SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
	}
	/*if (IS_OBJECT_PTR_VALID(TxtXExpression) && IS_OBJECT_PTR_VALID(TxtYExpression) && IS_OBJECT_PTR_VALID(TxtZExpression)
		&& IS_OBJECT_PTR_VALID(EdtXValue) && IS_OBJECT_PTR_VALID(EdtYValue) && IS_OBJECT_PTR_VALID(EdtZValue))
	{
		TxtXExpression->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtYExpression->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		TxtZExpression->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		EdtXValue->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		EdtYValue->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
		EdtZValue->WidgetStyle = TxtXExpression->WidgetStyle.SetColorAndOpacity(IsSelect ? FSlateColor(FontHoverOrSelect) : FSlateColor(FontNormal));
	}*/
}

void USingleComponentPoint::BindPointExpressionEdit(const int32& InEditType, const FString& InExpression)
{
	if (UExpressionPopWidget::Get())
	{
		UExpressionPopWidget::Get()->UpdateContent(InEditType, InExpression);
		UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("PointExpressionEdit")));
		UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
}

USingleComponentPoint* USingleComponentPoint::Create()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentPoint---create point view"));
	UClass* PointViewBp = LoadClass<UUserWidget>(NULL, *PointBpPath);
	checkf(PointViewBp, TEXT("load point view bp error!"));
	USingleComponentPoint* PointView = CreateWidget<USingleComponentPoint>(GWorld.GetReference(), PointViewBp);
	checkf(PointView, TEXT("create point view error!"));
	return PointView;
}

void USingleComponentPoint::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("mouse enter this point %d widget"), PointProperty.ID);
	if (!IsViewSelect)
	{
		UpdateSelectState(true);
		PointWidgetSelectDelegate.ExecuteIfBound((int32)ESectionSelectType::Point, PointProperty.ID, true, true);
	}
}

void USingleComponentPoint::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("mouse leave this point %d widget"), PointProperty.ID);
	if (!IsViewSelect)
	{
		UpdateSelectState(false);
		PointWidgetSelectDelegate.ExecuteIfBound((int32)ESectionSelectType::Point, PointProperty.ID, false, false);
	}
}

FReply USingleComponentPoint::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		UpdateSelectState(false);
		IsViewSelect = false;
		return FReply::Handled();
	}
	return FReply::Unhandled();
}

void USingleComponentPoint::PointExpressionEdit(const int32& EditType, const FString& InExpression)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(InExpression, Comments);
	if (CleanExp.IsEmpty()) return;
	
	switch ((EPointExpressionType)EditType)
	{
		case EPointExpressionType::XExpression:
		{
			if (TxtXExpression)
			{
				TxtXExpression->SetText(FText::FromString(InExpression));
				PointProperty.LocationX.Expression = InExpression;
				PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionXExpress, PointProperty);
			}
			break;
		}
		case EPointExpressionType::YExpression:
		{
			if (TxtYExpression)
			{
				TxtYExpression->SetText(FText::FromString(InExpression));
				PointProperty.LocationY.Expression = InExpression;
				PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionYExpress, PointProperty);
			}
			break;
		}
		case EPointExpressionType::ZExpression:
		{
			if (TxtZExpression)
			{
				TxtZExpression->SetText(FText::FromString(InExpression));
				PointProperty.LocationZ.Expression = InExpression;
				PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionZExpress, PointProperty);
			}
			break;
		}
		default:
		{
			checkNoEntry();
			break;
		}
	}
}

void USingleComponentPoint::OnClickedBtnXExpression()
{
	if (TxtXExpression)
	{
		BindPointExpressionEdit((int32)EPointExpressionType::XExpression, TxtXExpression->GetText().ToString());
	}
}

void USingleComponentPoint::OnClickedBtnYExpression()
{
	if (TxtYExpression)
	{
		BindPointExpressionEdit((int32)EPointExpressionType::YExpression, TxtYExpression->GetText().ToString());
	}
}

void USingleComponentPoint::OnClickedBtnZExpression()
{
	if (TxtZExpression)
	{
		BindPointExpressionEdit((int32)EPointExpressionType::ZExpression, TxtZExpression->GetText().ToString());
	}
}

void USingleComponentPoint::OnTextCommittedEdtXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		PointProperty.LocationX.Expression = Text.ToString();
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionXExpress, PointProperty);
	}
	else
	{
		FString Temp = PointProperty.LocationX.Expression;
		if (Temp.IsNumeric())
		{
			int32 P = Temp.Find(TEXT("."));
			if (P > 0)
			{
				Temp = Temp.Left(P + 2);
			}
		}
		TxtXExpression->SetText(FText::FromString(Temp));
	}
}

void USingleComponentPoint::OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		PointProperty.LocationX.Value = FString::SanitizeFloat(Value);
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionX, PointProperty);
	}
	else if (IS_OBJECT_PTR_VALID(EdtXValue) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtXValue->SetText(UParameterPropertyData::FormatParameterValue(PointProperty.LocationX.Value));
	}
}

void USingleComponentPoint::OnTextCommittedEdtYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		PointProperty.LocationY.Expression = Text.ToString();
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionYExpress, PointProperty);
	}
	else
	{
		FString Temp = PointProperty.LocationY.Expression;
		if (Temp.IsNumeric())
		{
			int32 P = Temp.Find(TEXT("."));
			if (P > 0)
			{
				Temp = Temp.Left(P + 2);
			}
		}
		TxtYExpression->SetText(FText::FromString(Temp));
	}
}

void USingleComponentPoint::OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		PointProperty.LocationY.Value = FString::SanitizeFloat(Value);
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionY, PointProperty);
	}
	else if (IS_OBJECT_PTR_VALID(EdtYValue) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtYValue->SetText(UParameterPropertyData::FormatParameterValue(PointProperty.LocationY.Value));
	}
}

void USingleComponentPoint::OnTextCommittedEdtZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		PointProperty.LocationZ.Expression = Text.ToString();
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionZExpress, PointProperty);
	}
	else
	{
		FString Temp = PointProperty.LocationZ.Expression;
		if (Temp.IsNumeric())
		{
			int32 P = Temp.Find(TEXT("."));
			if (P > 0)
			{
				Temp = Temp.Left(P + 2);
			}
		}
		TxtZExpression->SetText(FText::FromString(Temp));
	}
}

void USingleComponentPoint::OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		PointProperty.LocationZ.Value = FString::SanitizeFloat(Value);
		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::PositionZ, PointProperty);
	}
	else if (IS_OBJECT_PTR_VALID(EdtZValue) && CommitMethod != ETextCommit::Type::OnCleared)
	{
		EdtZValue->SetText(UParameterPropertyData::FormatParameterValue(PointProperty.LocationZ.Value));
	}
}

//void USingleComponentPoint::OnStateChangedCkbAbsolute(bool IsChecked)
//{
//	if (CkbAbsolute)
//	{
//		PointProperty.PositionType = IsChecked ? EPositionType::EAbsolute : EPositionType::ERelative;
//		PointDataChangeDelegate.ExecuteIfBound((int32)EPointChangeType::Relative, PointProperty);
//	}
//}
