// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSectionUnitWidget.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "SingleComponentPoint.generated.h"

/**
 * 
 */

class UTextBlock;
class UEditableText;
class UButton;
class UCheckBox;
class UBorder;

UENUM(BlueprintType)
enum class EPointChangeType : uint8
{
	PositionX = 0,
	PositionY,
	PositionZ,
	Relative,
	PositionXExpress,
	PositionYExpress,
	PositionZExpress,
};

UENUM(BlueprintType)
enum class EPointExpressionType : uint8
{
	XExpression = 0,
	YExpression,
	ZExpression
};

DECLARE_DYNAMIC_DELEGATE_TwoParams(FPointDataChangedDelegate, const int32&, EditType, const FGeomtryPointProperty&, PointData);

UCLASS()
class DESIGNSTATION_API USingleComponentPoint : public USingleComponentSectionUnitWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void UpdateContent(const FGeomtryPointProperty& InData);
	void UpdatePointIndex(int Index);
	void UpdateSelectState(bool IsSelect);
	void BindPointExpressionEdit(const int32& InEditType, const FString& InExpression);

	static USingleComponentPoint* Create();

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

private:
	UFUNCTION()
		void PointExpressionEdit(const int32& EditType, const FString& InExpression);

public:
	FPointDataChangedDelegate	PointDataChangeDelegate;
	FWidgetSelectDelegate		PointWidgetSelectDelegate;


private:
	FGeomtryPointProperty PointProperty;

	static FString PointBpPath;

protected:
	UFUNCTION()
		void OnClickedBtnXExpression();
	UFUNCTION() 
		void OnClickedBtnYExpression();
	UFUNCTION()
		void OnClickedBtnZExpression();
	UFUNCTION()
		void OnTextCommittedEdtXExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtYExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtZExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtXValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtYValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtZValue(const FText& Text, ETextCommit::Type CommitMethod);
	/*UFUNCTION()
		void OnStateChangedCkbAbsolute(bool IsChecked);*/
	
private:
	UPROPERTY()
		UBorder* BorPointIndex;
	UPROPERTY()
		UTextBlock* TxtPointIndex;
	UPROPERTY()
		UBorder* BorBackground;

	//x
	UPROPERTY()
		UBorder* BorX;
	UPROPERTY()
		UTextBlock* TxtX;
	UPROPERTY()
		UBorder* BorXExpress;
	UPROPERTY()
		UEditableText* TxtXExpression;
	UPROPERTY()
		UButton* BtnXExpression;
	UPROPERTY()
		UBorder* BorXValue;
	UPROPERTY()
		UEditableText* EdtXValue;

	//y
	UPROPERTY()
		UBorder* BorY;
	UPROPERTY()
		UTextBlock* TxtY;
	UPROPERTY()
		UBorder* BorYExpress;
	UPROPERTY()
		UEditableText* TxtYExpression;
	UPROPERTY()
		UButton* BtnYExpression;
	UPROPERTY()
		UBorder* BorYValue;
	UPROPERTY()
		UEditableText* EdtYValue;

	//z
	UPROPERTY()
		UBorder* BorZ;
	UPROPERTY()
		UTextBlock* TxtZ;
	UPROPERTY()
		UBorder* BorZExpress;
	UPROPERTY()
		UEditableText* TxtZExpression;
	UPROPERTY()
		UButton* BtnZExpression;
	UPROPERTY()
		UBorder* BorZValue;
	UPROPERTY()
		UEditableText* EdtZValue;

	/*UPROPERTY()
		UCheckBox* CkbAbsolute;*/
};
