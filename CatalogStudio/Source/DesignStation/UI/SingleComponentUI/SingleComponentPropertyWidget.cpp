// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentPropertyWidget.h"

#include "Components/Border.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "DesignStation/UI/PopUI/ToolTipWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/EditableText.h"

#define LOCTEXT_NAMESPACE "SingleComponentPropertyWidget"

extern const int PopUIZOrder;

bool USingleComponentPropertyWidget::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(EdtId, Edt_Id);
	BIND_WIDGET_FUNCTION(EdtId, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtId);
	BIND_PARAM_CPP_TO_UMG(EdtCoding, Edt_Coding);
	BIND_WIDGET_FUNCTION(EdtCoding, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtCoding);
	BIND_PARAM_CPP_TO_UMG(EdtCodingExp, Edt_CodingExp);
	BIND_WIDGET_FUNCTION(EdtCodingExp, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtCodingExp);
	BIND_PARAM_CPP_TO_UMG(BtnCodingExp, Btn_CodingExp);
	BIND_WIDGET_FUNCTION(BtnCodingExp, OnClicked, USingleComponentPropertyWidget::OnClickedBtnCodingExp);
	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_PARAM_CPP_TO_UMG(EdtNameExpress, Edt_NameExpress);
	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtName);
	BIND_WIDGET_FUNCTION(EdtNameExpress, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtNameExpress);
	BIND_PARAM_CPP_TO_UMG(EdtVisibleExpress, Edt_Express);
	BIND_WIDGET_FUNCTION(EdtVisibleExpress, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtVisibleExpress);
	BIND_PARAM_CPP_TO_UMG(BtnVisibleExpress, Btn_Express);
	BIND_WIDGET_FUNCTION(BtnVisibleExpress, OnClicked, USingleComponentPropertyWidget::OnClickedBtnVisibleExpress);
	BIND_PARAM_CPP_TO_UMG(EdtVisibleValue, Edt_Value);
	BIND_WIDGET_FUNCTION(EdtVisibleValue, OnTextCommitted, USingleComponentPropertyWidget::OnTextCommittedEdtVisibleValue);
	BIND_PARAM_CPP_TO_UMG(BtnNameExpress, Btn_NameExpress);
	BIND_WIDGET_FUNCTION(BtnNameExpress, OnClicked, USingleComponentPropertyWidget::OnClickedBtnEdtNameExpress);

	BIND_PARAM_CPP_TO_UMG(BtnNew, Btn_New);
	BIND_WIDGET_FUNCTION(BtnNew, OnClicked, USingleComponentPropertyWidget::OnClickedBtnNew);
	BIND_SLATE_WIDGET_FUNCTION(BtnNew, ToolTipWidgetDelegate, FName(TEXT("OnCreateBtnNewToolTip")));
	BIND_PARAM_CPP_TO_UMG(BorNew, Bor_New);

	BIND_PARAM_CPP_TO_UMG(BtnOperator, Btn_Operation);
	BIND_WIDGET_FUNCTION(BtnOperator, OnClicked, USingleComponentPropertyWidget::OnClickedBtnOperator);
	BIND_SLATE_WIDGET_FUNCTION(BtnOperator, ToolTipWidgetDelegate, FName(TEXT("OnCreateBtnOperatorToolTip")));
	BIND_PARAM_CPP_TO_UMG(BorOperator, Bor_Operator);

	BIND_PARAM_CPP_TO_UMG(BtnImport, Btn_Import);
	BIND_WIDGET_FUNCTION(BtnImport, OnClicked, USingleComponentPropertyWidget::OnClickedBtnImport);
	BIND_SLATE_WIDGET_FUNCTION(BtnImport, ToolTipWidgetDelegate, FName(TEXT("OnCreateBtnImportToolTip")));
	BIND_PARAM_CPP_TO_UMG(BorImport, Bor_Import);

	BIND_PARAM_CPP_TO_UMG(BtnCopy, Btn_Copy);
	BIND_WIDGET_FUNCTION(BtnCopy, OnClicked, USingleComponentPropertyWidget::OnClickedBtnCopy);
	BIND_SLATE_WIDGET_FUNCTION(BtnCopy, ToolTipWidgetDelegate, FName(TEXT("OnCreateBtnCopyToolTip")));
	BIND_PARAM_CPP_TO_UMG(BorCopy, Bor_Copy);

	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, USingleComponentPropertyWidget::OnClickedBtnDelete);
	BIND_SLATE_WIDGET_FUNCTION(BtnDelete, ToolTipWidgetDelegate, FName(TEXT("OnCreateBtnDeleteToolTip")));
	BIND_PARAM_CPP_TO_UMG(BorDelete, Bor_Delete);

	BIND_PARAM_CPP_TO_UMG(ScbParams, SB_Params);

	SingleComponentSections.Empty();
	SelectedSection = nullptr;
	return true;
}

void USingleComponentPropertyWidget::UpdateContent(const TArray<FSectionData>& SectionDatas)
{
	ScbParams->ClearChildren();
	SingleComponentSections.Empty();
	UE_LOG(LogTemp, Log, TEXT("SingleComponentProperty---update property content"));
	for (auto& Data : SectionDatas)
	{
		USingleComponentSection* SectionItem = USingleComponentSection::Create();
		SectionItem->UpdateContent(Data);
		SectionItem->SectionDataChangeDelegate.BindUFunction(this, FName(TEXT("SectionEdit")));
		SectionItem->SectionSelectDelegate.BindUFunction(this, FName(TEXT("OnSectionItemSelectHandler")));
		SectionItem->SetVisibility(ESlateVisibility::Visible);
		ScbParams->AddChild(SectionItem);
		SingleComponentSections.Add(Data.Id, SectionItem);
	}
	bool IsCustom = false;
	if (IS_OBJECT_PTR_VALID(SelectedSection))
	{
		SingleComponentSections[SelectedSection->GetSectionData().Id]->SelectedColor();
		IsCustom = ESingleComponentSource::ECustom == SelectedSection->GetSectionData().ComponentSource;
	}
	UpdateOperatorWidgetState(IS_OBJECT_PTR_VALID(SelectedSection), IsCustom);
}

void USingleComponentPropertyWidget::UpdateContent_New(const TArray<FSectionData>& SectionDatas)
{
	ScbParams->ClearChildren();
	SingleComponentSections.Empty();
	UE_LOG(LogTemp, Log, TEXT("SingleComponentProperty---update property content with depend file"));
	for (auto& Data : SectionDatas)
	{
		USingleComponentSection* SectionItem = USingleComponentSection::Create();
		SectionItem->UpdateContent_New(Data);
		SectionItem->SectionDataChangeDelegate.BindUFunction(this, FName(TEXT("SectionEdit")));
		SectionItem->SectionSelectDelegate.BindUFunction(this, FName(TEXT("OnSectionItemSelectHandler")));
		SectionItem->SetVisibility(ESlateVisibility::Visible);
		ScbParams->AddChild(SectionItem);
		SingleComponentSections.Add(Data.Id, SectionItem);
	}
	bool IsCustom = false;
	if (IS_OBJECT_PTR_VALID(SelectedSection))
	{
		SingleComponentSections[SelectedSection->GetSectionData().Id]->SelectedColor();
		IsCustom = ESingleComponentSource::ECustom == SelectedSection->GetSectionData().ComponentSource;
	}
	UpdateOperatorWidgetState(IS_OBJECT_PTR_VALID(SelectedSection), IsCustom);
}

void USingleComponentPropertyWidget::SetSectionImage(const FString& ThumbnailPath)
{
	SelectedSection->GetSectionData().ThumbnailPath = ThumbnailPath;
}

void USingleComponentPropertyWidget::SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name)
{
	SelectedSection->SetMatImageAndName(MatIndex, ThumbnailPath, Name);
}

void USingleComponentPropertyWidget::UpdateFileProperty(const FFolderTableData& InFileData)
{
	//UFolderWidget::Get()->GetCacheDataForRefDirectory();

	PreFileData = InFileData;
	if (IS_OBJECT_PTR_VALID(EdtId) && IS_OBJECT_PTR_VALID(EdtCoding) && IS_OBJECT_PTR_VALID(EdtCodingExp)
		&& IS_OBJECT_PTR_VALID(EdtName) && IS_OBJECT_PTR_VALID(EdtVisibleExpress) && IS_OBJECT_PTR_VALID(EdtVisibleValue))
	{
		EdtId->SetText(FText::FromString(InFileData.folder_id));
		EdtCoding->SetText(FText::FromString(InFileData.folder_code));
		EdtCodingExp->SetText(FText::FromString(InFileData.folder_code_exp));
		EdtName->SetText(FText::FromString(InFileData.folder_name));
		EdtNameExpress->SetText(FText::FromString(InFileData.folder_name_exp));
		EdtVisibleExpress->SetText(FText::FromString(InFileData.visibility_exp));
		EdtVisibleValue->SetText(FText::FromString(FString::SanitizeFloat(InFileData.visibility)));
	}
}

USingleComponentPropertyWidget* USingleComponentPropertyWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USingleComponentPropertyWidget>(TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentProperty.SingleComponentProperty_C'"));
}

void USingleComponentPropertyWidget::OnDeleteConfirmHandler()
{
	int32 DeleteSectionId = SelectedSection->GetSectionData().Id;
	SelectedSection = nullptr;
	SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::Delete, DeleteSectionId);
}

void USingleComponentPropertyWidget::OnExpressionHandler(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)EPropertyExpressType::Visible)
	{
		OnTextCommittedEdtVisibleExpress(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
	else if (EditType == (int32)EPropertyExpressType::CodingExp)
	{
		OnTextCommittedEdtCodingExp(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
	else if (EditType == (int32)EPropertyExpressType::Name)
	{
		OnTextCommittedEdtNameExpress(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
	}
}

UToolTipWidget* USingleComponentPropertyWidget::CreateToolTip(UButton* InBtn)
{
	if (!InBtn)
	{
		return nullptr;
	}
	CustomToolTipWidget = UToolTipWidget::Create();
	CustomToolTipWidget->UpdateContent(InBtn->GetToolTipText());
	CustomToolTipWidget->SetVisibility(ESlateVisibility::Visible);
	return CustomToolTipWidget;
}

void USingleComponentPropertyWidget::UpdateOperatorWidgetState(bool HasSelectItem, bool IsCustom)
{
	if (IS_OBJECT_PTR_VALID(BtnOperator))
	{
		BtnOperator->SetIsEnabled(HasSelectItem && IsCustom);
	}
	if (IS_OBJECT_PTR_VALID(BtnCopy) && IS_OBJECT_PTR_VALID(BtnDelete))
	{
		BtnCopy->SetIsEnabled(HasSelectItem);
		BtnDelete->SetIsEnabled(HasSelectItem);
	}
}

void USingleComponentPropertyWidget::SectionEdit(const int32& EditType, FSectionData& SectionData)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentProperty---content section data change"));
	SectionDataChangeDelegate.ExecuteIfBound(EditType, SectionData);
}

void USingleComponentPropertyWidget::OnSectionItemSelectHandler(USingleComponentSection* SelectSection)
{
	for (auto& iter : SingleComponentSections)
	{
		if ((*iter.Value).GetSelectState())
		{
			(*iter.Value).SelectedColor();
			(*iter.Value).SetSelectState(false);
		}
		else
		{
			(*iter.Value).UnSelectedColor();
		}
	}
	SelectedSection = SelectSection;
	UpdateOperatorWidgetState(true, SelectSection->GetSectionData().ComponentSource == ESingleComponentSource::ECustom);
}

void USingleComponentPropertyWidget::OnTextCommittedEdtId(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::Id, Text.ToString());
	}
}

void USingleComponentPropertyWidget::OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::Coding, Text.ToString());
	}
	/*if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString StrCoding(Text.ToString());
		while (StrCoding.operator[](0)== ' ')
		{
			StrCoding.RemoveAt(0, 1, 1);
		}
		if (StrCoding.Len() > 20)
		{
			StrCoding.RemoveAt(20, StrCoding.Len()-20, 1);
		}
		EdtCoding->SetText(FText::FromString(StrCoding));
	}*/
}


void USingleComponentPropertyWidget::OnTextCommittedEdtCodingExp(const FText& Text, ETextCommit::Type CommitMethod)
{
	const FString CleanExp = Text.ToString();
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::CodingExp, CleanExp);
	}
	else
	{
		EdtCodingExp->SetText(FText::FromString(PreFileData.folder_code_exp));
	}
}

void USingleComponentPropertyWidget::OnClickedBtnCodingExp()
{
	if (IS_OBJECT_PTR_VALID(EdtCodingExp))
	{
		BIND_EXPRESSION_WIDGET((int32)EPropertyExpressType::CodingExp, EdtCodingExp->GetText().ToString(), FName(TEXT("OnExpressionHandler")));
	}
}


void USingleComponentPropertyWidget::OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::Name, Text.ToString());
	}
	/*if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		FString StrName(Text.ToString());
		if (StrName.IsEmpty())
		{
			UE_LOG(LogTemp, Log, TEXT("name is null"));
			return;
		}

		while (StrName.operator[](0) == ' ')
		{
			StrName.RemoveAt(0, 1, 1);
		}
		if (StrName.Len() > 20)
		{
			StrName.RemoveAt(20, StrName.Len() - 20, 1);
		}
		EdtName->SetText(FText::FromString(StrName));

		UE_LOG(LogTemp, Log, TEXT("%d"),FolderId);

		FString SQL = FString::Printf(TEXT("update folders set folder_name=\"%s\" where id=\"%d\""), *StrName,FolderId);
		if (USqliteDatabaseLibrary::UpdateDataFromDataBase(SQL))
		{
		}
		else
		{

		}
	}*/

}

void USingleComponentPropertyWidget::OnTextCommittedEdtNameExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	const FString CleanExp = Text.ToString();
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::NameExpress, CleanExp);
	}
	else
	{
		EdtNameExpress->SetText(FText::FromString(PreFileData.folder_name_exp));
	}
}

void USingleComponentPropertyWidget::OnClickedBtnEdtNameExpress()
{
	if (IS_OBJECT_PTR_VALID(EdtNameExpress))
	{
		BIND_EXPRESSION_WIDGET((int32)EPropertyExpressType::Name, EdtNameExpress->GetText().ToString(), FName(TEXT("OnExpressionHandler")));
	}
}

void USingleComponentPropertyWidget::OnTextCommittedEdtVisibleExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	const FString CleanExp = Text.ToString();
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::VisiExpress, CleanExp);
	}
	else
	{
		EdtVisibleExpress->SetText(FText::FromString(PreFileData.visibility_exp));
	}
}

void USingleComponentPropertyWidget::OnClickedBtnVisibleExpress()
{
	if (IS_OBJECT_PTR_VALID(EdtVisibleExpress))
	{
		BIND_EXPRESSION_WIDGET((int32)EPropertyExpressType::Visible, EdtVisibleExpress->GetText().ToString(), FName(TEXT("OnExpressionHandler")));
	}
}

void USingleComponentPropertyWidget::OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SingleComponentFilePropertyDelegate.ExecuteIfBound((int32)EFilePropertyType::VisiValue, Text.ToString());
	}
}

void USingleComponentPropertyWidget::OnClickedBtnNew()
{
	SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::AddSection, -1);
}

void USingleComponentPropertyWidget::OnClickedBtnOperator()
{
	if (IS_OBJECT_PTR_VALID(SelectedSection))
	{
		SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::Operator, SelectedSection->GetSectionData().Id);
	}
}

void USingleComponentPropertyWidget::OnClickedBtnImport()
{
	SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::Import, -1);
}

void USingleComponentPropertyWidget::OnClickedBtnCopy()
{
	if (IS_OBJECT_PTR_VALID(SelectedSection))
	{
		SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::Copy, SelectedSection->GetSectionData().Id);
	}
}

void USingleComponentPropertyWidget::OnClickedBtnDelete()
{
	if (IS_OBJECT_PTR_VALID(SelectedSection))
	{
		if (UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Please make sure to delete select section!")).ToString()))
		{
			int32 DeleteSectionId = SelectedSection->GetSectionData().Id;
			SelectedSection = nullptr;
			SingleComponentPropertyDelegate.ExecuteIfBound((int32)EPropertyEditType::Delete, DeleteSectionId);
		}
	}
}

UToolTipWidget* USingleComponentPropertyWidget::OnCreateBtnNewToolTip()
{
	return CreateToolTip(BtnNew);
}

UToolTipWidget* USingleComponentPropertyWidget::OnCreateBtnOperatorToolTip()
{
	return CreateToolTip(BtnOperator);
}

UToolTipWidget* USingleComponentPropertyWidget::OnCreateBtnImportToolTip()
{
	return CreateToolTip(BtnImport);
}

UToolTipWidget* USingleComponentPropertyWidget::OnCreateBtnCopyToolTip()
{
	return CreateToolTip(BtnCopy);
}

UToolTipWidget* USingleComponentPropertyWidget::OnCreateBtnDeleteToolTip()
{
	return CreateToolTip(BtnDelete);
}

#undef LOCTEXT_NAMESPACE
