// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSection.h"
#include "DesignStation/SQLite/FolderRelated/FolderTableOperatorLibrary.h"


#include "SingleComponentPropertyWidget.generated.h"

/**
 *
 */

UENUM(BlueprintType)
enum class EFilePropertyType : uint8
{
	Id = 0,
	Coding,
	CodingExp,
	Name,
	NameExpress,
	VisiExpress,
	VisiValue
};

UENUM(BlueprintType)
enum class EPropertyEditType : uint8
{
	InsertOperate = 0,
	AddSection,
	Operator,
	Import,
	Copy,
	Delete
};

UENUM(BlueprintType)
enum class EPropertyExpressType : uint8
{
	Visible = 0,
	CodingExp,
	Name
};

class UEditableText;
class UButton;
class UBorder;
class UScrollBox;

DECLARE_DYNAMIC_DELEGATE_TwoParams(FSingleComponentPropertyDelegate, const int32&, EditType, const int32&, SectionId);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FSingleComponentFilePropertyDelegate, const int32&, EditType, const FString&, OutString);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FSectionDataChangeDelegate, const int32&, EditType, FSectionData&, SectionData);

UCLASS()
class DESIGNSTATION_API USingleComponentPropertyWidget : public UUserWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void UpdateContent(const TArray<FSectionData>& SectionDatas);
	void UpdateContent_New(const TArray<FSectionData>& SectionDatas);
	void UpdateFileProperty(const FFolderTableData& InFileData);
	void SetSectionImage(const FString& ThumbnailPath);
	void SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name);
	FORCEINLINE TMap<int32, USingleComponentSection*>& GetSections() { return SingleComponentSections; }

	static USingleComponentPropertyWidget* Create();

private:
	UFUNCTION()
		void OnDeleteConfirmHandler();
	UFUNCTION()
		void OnExpressionHandler(const int32& EditType, const FString& OutExpression);

	UToolTipWidget* CreateToolTip(UButton* InBtn);

	void UpdateOperatorWidgetState(bool HasSelectItem, bool IsCustom = true);

protected:
	UFUNCTION()
		void SectionEdit(const int32& EditType, FSectionData& SectionData);
	UFUNCTION()
		void OnSectionItemSelectHandler(USingleComponentSection* SelectSection);

	UFUNCTION()
		void OnTextCommittedEdtId(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCoding(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtCodingExp(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnCodingExp();
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtNameExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnEdtNameExpress();
	UFUNCTION()
		void OnTextCommittedEdtVisibleExpress(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnVisibleExpress();
	UFUNCTION()
		void OnTextCommittedEdtVisibleValue(const FText& Text, ETextCommit::Type CommitMethod);

	UFUNCTION()
		void OnClickedBtnNew();
	UFUNCTION()
		void OnClickedBtnOperator();
	UFUNCTION()
		void OnClickedBtnImport();
	UFUNCTION()
		void OnClickedBtnCopy();
	UFUNCTION()
		void OnClickedBtnDelete();

	UFUNCTION()
		UToolTipWidget* OnCreateBtnNewToolTip();
	UFUNCTION()
		UToolTipWidget* OnCreateBtnOperatorToolTip();
	UFUNCTION()
		UToolTipWidget* OnCreateBtnImportToolTip();
	UFUNCTION()
		UToolTipWidget* OnCreateBtnCopyToolTip();
	UFUNCTION()
		UToolTipWidget* OnCreateBtnDeleteToolTip();

private:
	UPROPERTY()
		TMap<int32, USingleComponentSection*> SingleComponentSections;

	UPROPERTY()
		USingleComponentSection* SelectedSection;

	UPROPERTY()
		UToolTipWidget* CustomToolTipWidget;

private:

	FFolderTableData PreFileData = FFolderTableData();

	//file property
	UPROPERTY()
		UEditableText* EdtId;
	UPROPERTY()
		UEditableText* EdtCoding;
	UPROPERTY()
		UEditableText* EdtCodingExp;
	UPROPERTY()
		UButton* BtnCodingExp;

	UPROPERTY()
		UEditableText* EdtName;
	UPROPERTY()
		UEditableText* EdtNameExpress;
	UPROPERTY()
		UEditableText* EdtVisibleExpress;
	UPROPERTY()
		UButton* BtnVisibleExpress;
	UPROPERTY()
		UEditableText* EdtVisibleValue;
	UPROPERTY()
		UButton* BtnNameExpress;
	//cross section
	UPROPERTY()
		UButton* BtnNew;
	UPROPERTY()
		UBorder* BorNew;

	UPROPERTY()
		UButton* BtnOperator;
	UPROPERTY()
		UBorder* BorOperator;

	UPROPERTY()
		UButton* BtnImport;
	UPROPERTY()
		UBorder* BorImport;

	UPROPERTY()
		UButton* BtnCopy;
	UPROPERTY()
		UBorder* BorCopy;

	UPROPERTY()
		UButton* BtnDelete;
	UPROPERTY()
		UBorder* BorDelete;

	UPROPERTY()
		UScrollBox* ScbParams;

public:
	FSingleComponentPropertyDelegate SingleComponentPropertyDelegate;
	FSingleComponentFilePropertyDelegate SingleComponentFilePropertyDelegate;
	FSectionDataChangeDelegate SectionDataChangeDelegate;
};
