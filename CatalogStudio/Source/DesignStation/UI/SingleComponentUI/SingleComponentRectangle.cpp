// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentRectangle.h"

#include "DataCenter/ComponentData/GeomtryItemData.h"
#include "DataCenter/ComponentData/ParameterPropertyData.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "GrammerAnalysis/Public/GrammerAnalysisSubsystem.h"

FString USingleComponentRectangle::SingleComponentRectanglePath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentRectangle.SingleComponentRectangle_C'");

bool USingleComponentRectangle::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	if (UEditableText* Txt_BeginXExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginXExpression"))))
	{
		TxtBeginXExpress = Txt_BeginXExpression;
		TxtBeginXExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginXExpress);
	}
	if (UButton* Btn_BeginXExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginXExpression"))))
	{
		BtnBeginXExpress = Btn_BeginXExpression;
		BtnBeginXExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnBeginXExpress);
	}
	if (UEditableText* Edt_BeginXValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginXValue"))))
	{
		EdtBeginXValue = Edt_BeginXValue;
		EdtBeginXValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginXValue);
	}
	if (UEditableText* Txt_BeginYExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginYExpression"))))
	{
		TxtBeginYExpress = Txt_BeginYExpression;
		TxtBeginYExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginYExpress);
	}
	if (UButton* Btn_BeginYExpression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginYExpression"))))
	{
		BtnBeginYExpress = Btn_BeginYExpression;
		BtnBeginYExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnBeginYExpress);
	}
	if (UEditableText* Edt_BeginYValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginYValue"))))
	{
		EdtBeginYValue = Edt_BeginYValue;
		EdtBeginYValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginYValue);
	}
	if (UEditableText* Txt_BeginZExpression = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_BeginZExpression"))))
	{
		TxtBeginZExpress = Txt_BeginZExpression;
		TxtBeginZExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginZExpress);
	}
	if (UButton* Btn_BeginZExPression = Cast<UButton>(GetWidgetFromName(TEXT("Btn_BeginZExPression"))))
	{
		BtnBeginZExpress = Btn_BeginZExPression;
		BtnBeginZExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnBeginZExpress);
	}
	if (UEditableText* Edt_BeginZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_BeginZValue"))))
	{
		EdtBeginZValue = Edt_BeginZValue;
		EdtBeginZValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtBeginZValue);
	}
	//end point
	if (UEditableText* Txt_EndXExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndXExpress"))))
	{
		TxtEndXExpress = Txt_EndXExpress;
		TxtEndXExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndXExpress);
	}
	if (UButton* Btn_EndXExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndXExpress"))))
	{
		BtnEndXExpress = Btn_EndXExpress;
		BtnEndXExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnEndXExpress);
	}
	if (UEditableText* Edt_EndXValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndXValue"))))
	{
		EdtEndXValue = Edt_EndXValue;
		EdtEndXValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndXValue);
	}
	if (UEditableText* Txt_EndYExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndYExpress"))))
	{
		TxtEndYExpress = Txt_EndYExpress;
		TxtEndYExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndYExpress);
	}
	if (UButton* Btn_EndYExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndYExpress"))))
	{
		BtnEndYExpress = Btn_EndYExpress;
		BtnEndYExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnEndYExpress);
	}
	if (UEditableText* Edt_EndYValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndYValue"))))
	{
		EdtEndYValue = Edt_EndYValue;
		EdtEndYValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndYValue);
	}
	if (UEditableText* Txt_EndZExpress = Cast<UEditableText>(GetWidgetFromName(TEXT("Txt_EndZExpress"))))
	{
		TxtEndZExpress = Txt_EndZExpress;
		TxtEndZExpress->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndZExpress);
	}
	if (UButton* Btn_EndZExpress = Cast<UButton>(GetWidgetFromName(TEXT("Btn_EndZExpress"))))
	{
		BtnEndZExpress = Btn_EndZExpress;
		BtnEndZExpress->OnClicked.AddUniqueDynamic(this, &USingleComponentRectangle::OnClickedBtnEndZExpress);
	}
	if (UEditableText* Edt_EndZValue = Cast<UEditableText>(GetWidgetFromName(TEXT("Edt_EndZValue"))))
	{
		EdtEndZValue = Edt_EndZValue;
		EdtEndZValue->OnTextCommitted.AddUniqueDynamic(this, &USingleComponentRectangle::OnTextCommittedEdtEndZValue);
	}

	return true;
}

void USingleComponentRectangle::UpdateContent(const FGeomtryRectanglePlanProperty& InData)
{
	RectangleProperty.CopyData(InData);

	if (IS_OBJECT_PTR_VALID(EdtBeginXValue) && IS_OBJECT_PTR_VALID(TxtBeginXExpress))
	{
		EdtBeginXValue->SetText(UParameterPropertyData::FormatParameterValue(InData.StartLocationX.Value));
		TxtBeginXExpress->SetText(FText::FromString(InData.StartLocationX.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtBeginYValue) && IS_OBJECT_PTR_VALID(TxtBeginYExpress))
	{
		EdtBeginYValue->SetText(UParameterPropertyData::FormatParameterValue(InData.StartLocationY.Value));
		TxtBeginYExpress->SetText(FText::FromString(InData.StartLocationY.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtBeginZValue) && IS_OBJECT_PTR_VALID(TxtBeginZExpress))
	{
		EdtBeginZValue->SetText(UParameterPropertyData::FormatParameterValue(InData.StartLocationZ.Value));
		TxtBeginZExpress->SetText(FText::FromString(InData.StartLocationZ.Expression));
	}

	if (IS_OBJECT_PTR_VALID(EdtEndXValue) && IS_OBJECT_PTR_VALID(TxtEndXExpress))
	{
		EdtEndXValue->SetText(UParameterPropertyData::FormatParameterValue(InData.EndLocationX.Value));
		TxtEndXExpress->SetText(FText::FromString(InData.EndLocationX.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtEndYValue) && IS_OBJECT_PTR_VALID(TxtEndYExpress))
	{
		EdtEndYValue->SetText(UParameterPropertyData::FormatParameterValue(InData.EndLocationY.Value));
		TxtEndYExpress->SetText(FText::FromString(InData.EndLocationY.Expression));
	}
	if (IS_OBJECT_PTR_VALID(EdtEndZValue) && IS_OBJECT_PTR_VALID(TxtEndZExpress))
	{
		EdtEndZValue->SetText(UParameterPropertyData::FormatParameterValue(InData.EndLocationZ.Value));
		TxtEndZExpress->SetText(FText::FromString(InData.EndLocationZ.Expression));
	}
}

void USingleComponentRectangle::BindRectangleExpressionEdit(const int32& InEditType, const FString& InExpression)
{
	if (UExpressionPopWidget::Get())
	{
		UExpressionPopWidget::Get()->UpdateContent(InEditType, InExpression);
		UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, FName(TEXT("RectangleExpressionEdit")));
		UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
}

USingleComponentRectangle* USingleComponentRectangle::Create()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentPoint---create rectangle view"));
	UClass* RectangleBp = LoadClass<UUserWidget>(NULL, *SingleComponentRectanglePath);
	checkf(RectangleBp, TEXT("load rectangle bp error!"));
	USingleComponentRectangle* RectangleItem = CreateWidget<USingleComponentRectangle>(GWorld.GetReference(), RectangleBp);
	checkf(RectangleItem, TEXT("create rectangle item error!"));
	return RectangleItem;
}

void USingleComponentRectangle::RectangleExpressionEdit(const int32& EditType, const FString& InExpression)
{
	switch ((ERectangleExpressionType)EditType)
	{
	case ERectangleExpressionType::BeginXExpression:
	{
		if (TxtBeginXExpress)
		{
			TxtBeginXExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.StartLocationX.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationXExpress, RectangleProperty);
		}
		break;
	}
	case ERectangleExpressionType::BeginYExpression:
	{
		if (TxtBeginYExpress)
		{
			TxtBeginYExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.StartLocationY.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationYExpress, RectangleProperty);
		}
		break;
	}
	case ERectangleExpressionType::BeginZExpression:
	{
		if (TxtBeginZExpress)
		{
			TxtBeginZExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.StartLocationZ.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationZExpress, RectangleProperty);
		}
		break;
	}
	case ERectangleExpressionType::EndXExpression:
	{
		if (TxtEndXExpress)
		{
			TxtEndXExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.EndLocationX.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationXExpress, RectangleProperty);
		}
		break;
	}
	case ERectangleExpressionType::EndYExpression:
	{
		if (TxtEndYExpress)
		{
			TxtEndYExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.EndLocationY.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationYExpress, RectangleProperty);
		}
		break;
	}
	case ERectangleExpressionType::EndZExpression:
	{
		if (TxtEndZExpress)
		{
			TxtEndZExpress->SetText(FText::FromString(InExpression));
			RectangleProperty.EndLocationZ.Expression = InExpression;
			RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationZExpress, RectangleProperty);
		}
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USingleComponentRectangle::OnClickedBtnBeginXExpress()
{
	if (TxtBeginXExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::BeginXExpression, TxtBeginXExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.StartLocationX.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationXExpress, RectangleProperty);
	}
	else
	{
		TxtBeginXExpress->SetText(FText::FromString(RectangleProperty.StartLocationX.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.StartLocationX.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationX, RectangleProperty);
	}
	else
	{
		EdtBeginXValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.StartLocationX.Value));
	}
}

void USingleComponentRectangle::OnClickedBtnBeginYExpress()
{
	if (TxtBeginYExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::BeginYExpression, TxtBeginYExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.StartLocationY.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationYExpress, RectangleProperty);
	}
	else
	{
		TxtBeginYExpress->SetText(FText::FromString(RectangleProperty.StartLocationY.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.StartLocationY.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationY, RectangleProperty);
	}
	else
	{
		EdtBeginYValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.StartLocationY.Value));
	}
}

void USingleComponentRectangle::OnClickedBtnBeginZExpress()
{
	if (TxtBeginZExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::BeginZExpression, TxtBeginZExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.StartLocationZ.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationZExpress, RectangleProperty);
	}
	else
	{
		TxtBeginZExpress->SetText(FText::FromString(RectangleProperty.StartLocationZ.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtBeginZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.StartLocationZ.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::StartLocationZ, RectangleProperty);
	}
	else
	{
		EdtBeginZValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.StartLocationZ.Value));
	}
}

void USingleComponentRectangle::OnClickedBtnEndXExpress()
{
	if (TxtEndXExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::EndXExpression, TxtEndXExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndXExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.EndLocationX.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationXExpress, RectangleProperty);
	}
	else
	{
		TxtEndXExpress->SetText(FText::FromString(RectangleProperty.EndLocationX.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndXValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.EndLocationX.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationX, RectangleProperty);
	}
	else
	{
		EdtEndXValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.EndLocationX.Value));
	}
}

void USingleComponentRectangle::OnClickedBtnEndYExpress()
{
	if (TxtEndYExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::EndYExpression, TxtEndYExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndYExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.EndLocationY.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationYExpress, RectangleProperty);
	}
	else
	{
		TxtEndYExpress->SetText(FText::FromString(RectangleProperty.EndLocationY.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndYValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.EndLocationY.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationY, RectangleProperty);
	}
	else
	{
		EdtEndYValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.EndLocationY.Value));
	}
}

void USingleComponentRectangle::OnClickedBtnEndZExpress()
{
	if (TxtEndZExpress)
	{
		BindRectangleExpressionEdit((int32)ERectangleExpressionType::EndZExpression, TxtEndZExpress->GetText().ToString());
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndZExpress(const FText& Text, ETextCommit::Type CommitMethod)
{
	TArray<TPair<int32, FString>> Comments;
	const FString CleanExp = GetGameInstance()->GetSubsystem<UGrammerAnalysisSubsystem>()->RemoveComment(Text.ToString(), Comments);

	if (!CleanExp.IsEmpty() && ETextCommit::Type::OnCleared != CommitMethod)
	{
		RectangleProperty.EndLocationZ.Expression = Text.ToString();
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationZExpress, RectangleProperty);
	}
	else
	{
		TxtEndZExpress->SetText(FText::FromString(RectangleProperty.EndLocationZ.Expression));
	}
}

void USingleComponentRectangle::OnTextCommittedEdtEndZValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		float Value = UParameterPropertyData::FormatParameterValue(Text);
		RectangleProperty.EndLocationZ.Value = FString::SanitizeFloat(Value);
		RectangleChangeDelegate.ExecuteIfBound((int32)ERectangleChangeType::EndLocationZ, RectangleProperty);
	}
	else
	{
		EdtEndZValue->SetText(UParameterPropertyData::FormatParameterValue(RectangleProperty.EndLocationZ.Value));
	}
}
