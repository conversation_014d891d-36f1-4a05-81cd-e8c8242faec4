// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentSection.h"

#include "Components/Border.h"
#include "Components/Button.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/Engine/Classes/Kismet/GameplayStatics.h"
#include "Runtime/Engine/Classes/Engine/PostProcessVolume.h"
#include "Runtime/Engine/Classes/Engine/StaticMeshActor.h"

FString USingleComponentSection::SingleComponentSectionPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSection.SingleComponentSection_C'");

void FSectionMaterialData::CopyData(const FSectionMaterialData& InData)
{
	this->EditMaterialId = InData.EditMaterialId;
	this->ComponentMaterial = InData.ComponentMaterial;
}


bool USingleComponentSection::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(BorBackground, Bor_Background);
	BIND_SLATE_WIDGET_FUNCTION(BorBackground, OnMouseButtonDownEvent, FName(TEXT("LeftMouseButtonDownToSelect")));
	BIND_SLATE_WIDGET_FUNCTION(BorBackground, OnMouseDoubleClickEvent, FName(TEXT("LeftMouseButtonDoubleDownToOpen")));

	////BIND_SLATE_WIDGET_FUNCTION(BorMaterial, OnMouseButtonDownEvent, FName(TEXT("LeftMouseButtonDownToSelect")));
	////BIND_SLATE_WIDGET_FUNCTION(BorMaterial, OnMouseDoubleClickEvent, FName(TEXT("LeftMouseButtonDoubleDownToOpen")));

	BIND_PARAM_CPP_TO_UMG(BorSection, Bor_Section);
	BIND_PARAM_CPP_TO_UMG(BorSectionTitle, Bor_SectionTitle);
	BIND_PARAM_CPP_TO_UMG(BorVisibleTitle, Bor_VisibleTitle);
	BIND_PARAM_CPP_TO_UMG(ImgSectionImage, Img_SectionImage);
	BIND_PARAM_CPP_TO_UMG(EdtSectionName, Edt_SectionName);
	BIND_PARAM_CPP_TO_UMG(EdtVisible, Edt_Visible);
	BIND_PARAM_CPP_TO_UMG(EdtSectionValue, Edt_SectionValue);
	BIND_PARAM_CPP_TO_UMG(BtnSectionEdit, Btn_SectionEdit);
	BIND_PARAM_CPP_TO_UMG(BorSection, Bor_Section);
	BIND_PARAM_CPP_TO_UMG(BorSectionName, Bor_SectionName);
	BIND_PARAM_CPP_TO_UMG(BorVisible, Bor_Visible);
	BIND_PARAM_CPP_TO_UMG(BorSectionValue, Bor_SectionValue);
	BIND_PARAM_CPP_TO_UMG(SBMaterial, SB_Material);

	BIND_WIDGET_FUNCTION(BtnSectionEdit, OnClicked, USingleComponentSection::OnClickedBtnSectionEdit);
	BIND_WIDGET_FUNCTION(EdtSectionName, OnTextCommitted, USingleComponentSection::OnTextCommittedEdtSectionName);
	BIND_WIDGET_FUNCTION(EdtVisible, OnTextCommitted, USingleComponentSection::OnTextCommittedEdtVisible);
	BIND_WIDGET_FUNCTION(EdtSectionValue, OnTextCommitted, USingleComponentSection::OnTextCommittedEdtSectionValue);

	return true;
}

USingleComponentSection* USingleComponentSection::Create()
{
	return  UUIFunctionLibrary::UIWidgetCreate<USingleComponentSection>(USingleComponentSection::SingleComponentSectionPath);
}

void USingleComponentSection::UpdateContent(const FSectionData& InSectionData)
{
	SectionData.CopyData(InSectionData);
	SBMaterial->ClearChildren();
	SectionMaterials.Empty();
	//section data

	if (!InSectionData.ThumbnailPath.IsEmpty())
	{
		const FString ThumbnailPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + InSectionData.ThumbnailPath);
		UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(ThumbnailPath);
		if (nullptr != ThumbnailTex)
		{
			ImgSectionImage->SetBrushFromTexture(ThumbnailTex);
		}
	}

	if (IS_OBJECT_PTR_VALID(EdtSectionName))
	{
		EdtSectionName->SetText(FText::FromString(InSectionData.SectionName));
	}
	if (IS_OBJECT_PTR_VALID(EdtVisible) && IS_OBJECT_PTR_VALID(EdtSectionValue))
	{
		EdtVisible->SetText(FText::FromString(InSectionData.VisibleParam.Expression));
		EdtSectionValue->SetText(FText::FromString(InSectionData.VisibleParam.Value));
	}
	//material data
	for (int32 i = 0; i < InSectionData.MaterialInfo.ComponentMaterial.Num(); ++i)
	{
		USingleComponentMaterial* MaterialItem = USingleComponentMaterial::Create();
		MaterialItem->UpdateContent(InSectionData.MaterialInfo.ComponentMaterial[i].Id, InSectionData.MaterialInfo.ComponentMaterial[i].MaterialID);
		MaterialItem->SectionMaterialDelegate.BindUFunction(this, FName(TEXT("OnSectionMaterialHandler")));
		MaterialItem->SetVisibility(ESlateVisibility::Visible);
		SectionMaterials.Add(MaterialItem);
		SBMaterial->AddChild(MaterialItem);
	}
}

void USingleComponentSection::UpdateContent_New(const FSectionData& InSectionData)
{
	SectionData.CopyData(InSectionData);
	SBMaterial->ClearChildren();
	SectionMaterials.Empty();

	if (!InSectionData.ThumbnailPath.IsEmpty())
	{
		const FString ThumbnailPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + InSectionData.ThumbnailPath);
		UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(ThumbnailPath);
		if (nullptr != ThumbnailTex)
		{
			ImgSectionImage->SetBrushFromTexture(ThumbnailTex);
		}
	}

	if (IS_OBJECT_PTR_VALID(EdtSectionName))
	{
		EdtSectionName->SetText(FText::FromString(InSectionData.SectionName));
	}
	if (IS_OBJECT_PTR_VALID(EdtVisible) && IS_OBJECT_PTR_VALID(EdtSectionValue))
	{
		EdtVisible->SetText(FText::FromString(InSectionData.VisibleParam.Expression));
		EdtSectionValue->SetText(FText::FromString(InSectionData.VisibleParam.Value));
	}
	//material data
	for (int32 i = 0; i < InSectionData.MaterialInfo.ComponentMaterial.Num(); ++i)
	{
		USingleComponentMaterial* MaterialItem = USingleComponentMaterial::Create();
		//MaterialItem->UpdateContent(InSectionData.MaterialInfo.ComponentMaterial[i].Id, InSectionData.MaterialInfo.ComponentMaterial[i].MaterialID);
		MaterialItem->UpdateContent(InSectionData.MaterialInfo.ComponentMaterial[i]);
		MaterialItem->SectionMaterialDelegate.BindUFunction(this, FName(TEXT("OnSectionMaterialHandler")));
		MaterialItem->SetVisibility(ESlateVisibility::Visible);
		SectionMaterials.Add(MaterialItem);
		SBMaterial->AddChild(MaterialItem);
	}
}

void USingleComponentSection::SelectOnMaterial()
{
	//USingleComponentMaterial::
}

void USingleComponentSection::SendSelectState()
{
	SectionSelectDelegate.ExecuteIfBound(this);
}

FEventReply USingleComponentSection::LeftMouseButtonDownToSelect(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSection---one click to select"));

	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		SetSelectState(true);
		SendSelectState();
		return FEventReply(true);
	}
	return FEventReply();
}



FEventReply USingleComponentSection::LeftMouseButtonDoubleDownToOpen(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSection---two click to open"));
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		TArray<AActor*> Actors;
		UGameplayStatics::GetAllActorsWithTag(GWorld, FName(TEXT("BlendSphere")), Actors);
		if (Actors.Num() > 0)
		{
			AStaticMeshActor* A = Cast<AStaticMeshActor>(Actors[0]);
			A->SetHidden(false);
		}
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::EditSection, SectionData);
	}
	return FEventReply();
}

void USingleComponentSection::OnClickedBtnSectionEdit()
{
	if (IS_OBJECT_PTR_VALID(EdtVisible))
	{
		BIND_EXPRESSION_WIDGET((int32)ESectionExpressionType::SectionVisiblity, EdtVisible->GetText().ToString(), FName(TEXT("SectionExpressionEdit")));
	}
}

//void USingleComponentSection::OnClickedBtnMatEdit()
//{
//	if (IS_OBJECT_PTR_VALID(EdtID))
//	{
//		BIND_EXPRESSION_WIDGET((int32)ESectionExpressionType::MatIdExpression, EdtID->GetText().ToString(), FName(TEXT("SectionExpressionEdit")));
//	}
//}

void USingleComponentSection::SectionExpressionEdit(const int32& EditType, const FString& OutExpression)
{
	if (EditType == (int32)ESectionExpressionType::MatIdExpression)
	{
		/*SectionData.ComponentMaterial.Expression = OutExpression;
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialExpression, SectionData);*/
	}
	else if (EditType == (int32)ESectionExpressionType::SectionVisiblity)
	{
		SectionData.VisibleParam.Expression = OutExpression;
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::VisibilityExpression, SectionData);
	}
}

void USingleComponentSection::OnSectionMaterialHandler(const int32& MatId, const EMaterialType& MatType, const FString& NewExpressOrValue)
{
	SectionData.MaterialInfo.EditMaterialId = MatId;
	if (!SectionData.MaterialInfo.ComponentMaterial.IsValidIndex(MatId))
	{
		UE_LOG(LogTemp, Log, TEXT("material id is no valid"));
		return;
	}
	if (MatType == EMaterialType::Expression)
	{
		SectionData.MaterialInfo.ComponentMaterial[MatId].MaterialID.Expression = NewExpressOrValue;
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialExpression, SectionData);
	}
	else if (MatType == EMaterialType::Value)
	{
		SectionData.MaterialInfo.ComponentMaterial[MatId].MaterialID.Value = NewExpressOrValue;
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialValue, SectionData);
	}
}

void USingleComponentSection::OnTextCommittedEdtSectionName(const FText& Text, ETextCommit::Type CommitMethod)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSection---edit name"));
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{

		{

			FRegexPattern Pattern(TEXT("[^a-zA-Z0-9\u4E00-\u9FA5]"));
			FRegexMatcher RegMatcher(Pattern, Text.ToString());
			RegMatcher.SetLimits(0, Text.ToString().Len());
			if ((Text.ToString().Len() <= 20) && (!RegMatcher.FindNext() || Text.ToString().Len() == 0))
			{
				UE_LOG(LogTemp, Log, TEXT("SECTION IS LEGAL"));
				SectionData.SectionName = Text.ToString();
				SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::ChangeName, SectionData);
			}
			else
			{
				UE_LOG(LogTemp, Log, TEXT("SECTION ISN'T LEGAL"));
				EdtSectionName->SetText(FText::FromString(SectionData.SectionName));
			}
		}
	}
}

void USingleComponentSection::OnTextCommittedEdtVisible(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SectionData.VisibleParam.Expression = Text.ToString();
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::VisibilityExpression, SectionData);
	}
	else
	{
		EdtVisible->SetText(FText::FromString(SectionData.VisibleParam.Expression));
	}
}

//void USingleComponentSection::OnTextCommittedEdtMatName(const FText & Text, ETextCommit::Type CommitMethod)
//{
//	/*if (CommitMethod != ETextCommit::Type::OnCleared)
//	{
//		SectionData.ComponentMaterial.Expression = Text.ToString();
//		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialExpression, SectionData);
//	}*/
//}

void USingleComponentSection::OnTextCommittedEdtID(const FText& Text, ETextCommit::Type CommitMethod)
{
	/*if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SectionData.ComponentMaterial.Expression = Text.ToString();
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialExpression, SectionData);
	}*/
}

void USingleComponentSection::OnTextCommittedEdtSectionValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		SectionData.VisibleParam.Value = Text.ToString();
		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::VisibilityValue, SectionData);
	}
}

//void USingleComponentSection::OnTextCommittedEdtMatValue(const FText & Text, ETextCommit::Type CommitMethod)
//{
//	/*if (Text.IsNumeric() && CommitMethod != ETextCommit::Type::OnCleared)
//	{
//		SectionData.ComponentMaterial.Value = Text.ToString();
//		SectionDataChangeDelegate.ExecuteIfBound((int32)SectionEditType::MaterialValue, SectionData);
//	}
//	else if (CommitMethod != ETextCommit::Type::OnCleared)
//	{
//		EdtMatValue->SetText(FText::FromString(SectionData.ComponentMaterial.Value));
//	}*/
//}

void USingleComponentSection::SetSelectState(bool isSelected)
{
	IsSelected = isSelected;
}

bool USingleComponentSection::GetSelectState()
{
	return IsSelected;
}

void USingleComponentSection::MaterialSelectColor(bool isSelected)
{
	if (isSelected)
	{
		for (auto& iter : SectionMaterials)
		{
			iter->SelectedColor();
		}
	}
	else
	{
		for (auto& iter : SectionMaterials)
		{
			iter->UnSelectedColor();
		}
	}
}

void USingleComponentSection::UnSelectedColor()
{
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorSection);
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorSectionName);
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorVisible);
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorSectionValue);
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorSectionTitle);
	UUIFunctionLibrary::SetBorderBrushColor(SectionColor, BorVisibleTitle);

	MaterialSelectColor(false);
}

void USingleComponentSection::SelectedColor()
{
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorSection);
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorSectionName);
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorVisible);
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorSectionValue);
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorSectionTitle);
	UUIFunctionLibrary::SetBorderBrushColor(SingleComponentSectionSelectColor, BorVisibleTitle);

	MaterialSelectColor(true);
}

void USingleComponentSection::SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name)
{

}