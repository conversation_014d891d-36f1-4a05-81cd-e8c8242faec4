// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentMaterial.h"
#include "DesignStation/Geometry/DataDefines/GeometryDatas.h"
#include "SingleComponentSection.generated.h"

/**
 *
 */

class ACatalogPlayerController;

USTRUCT(BlueprintType)
struct FSectionMaterialDataItem
{
	GENERATED_USTRUCT_BODY()
public:
	int Id;
	FExpressionValuePair	MaterialID;
	FString Name;
	FString ImgPath;
	FSectionMaterialDataItem() :Id(0), MaterialID(FExpressionValuePair()) {}
	FSectionMaterialDataItem(const int32& InId, const FExpressionValuePair& InMaterialId) :Id(InId), MaterialID(InMaterialId) {}
};

USTRUCT(BlueprintType)
struct FSectionMaterialData
{
	GENERATED_USTRUCT_BODY()
public:
	int EditMaterialId;//被修改了的材质ID放在这个位置
	TArray<FSectionMaterialDataItem> ComponentMaterial;

	FSectionMaterialData() :EditMaterialId(-1) {}

	void CopyData(const FSectionMaterialData& InData);
};

USTRUCT(BlueprintType)
struct FSectionData
{
	GENERATED_USTRUCT_BODY()
public:
	int Id;
	FString SectionName;
	FString ThumbnailPath;
	ESingleComponentSource	ComponentSource;
	FExpressionValuePair VisibleParam;
	TArray<FString> SectionOperate;
	FSectionMaterialData	MaterialInfo;
	FSectionData() :Id(-1), SectionName(TEXT("")), ThumbnailPath(TEXT("")), ComponentSource(ESingleComponentSource::ECustom) {}
	void CopyData(const FSectionData& InData)
	{
		Id = InData.Id;
		SectionName = InData.SectionName;
		ThumbnailPath = InData.ThumbnailPath;
		ComponentSource = InData.ComponentSource;
		VisibleParam = InData.VisibleParam;
		MaterialInfo.CopyData(InData.MaterialInfo);
		SectionOperate.Empty();
		for (auto& Operate : InData.SectionOperate)
		{
			SectionOperate.Add(Operate);
		}
	}
};

UENUM(BlueprintType)
enum class SectionEditType : uint8
{
	ChangeName = 0,
	ChangeThumbnail,
	EditSection,
	DeleteSection,
	EditOperation,
	CopySection,
	MaterialExpression,
	MaterialValue,
	VisibilityExpression,
	VisibilityValue
};

UENUM(BlueprintType)
enum class ESectionExpressionType : uint8
{
	MatIdExpression = 0,
	SectionVisiblity
};

class UButton;
class UBorder;
class UImage;
class UEditableText;
class UTextBlock;
class UScrollBox;

const FLinearColor SingleComponentSectionSelectColor = FLinearColor(0.022174f, 0.467784f, 0.887923f, 0.2f);
const FLinearColor SectionColor = FLinearColor(0.806952f, 0.806952f, 0.806952f, 1.0f);


DECLARE_DYNAMIC_DELEGATE_TwoParams(FSectionInfoChangeDelegate, const int32&, EditType, FSectionData&, SectionData);
DECLARE_DYNAMIC_DELEGATE_OneParam(FSectionSelectDelegate, USingleComponentSection*, SelectItem);

UCLASS()
class DESIGNSTATION_API USingleComponentSection : public UUserWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void UpdateContent(const FSectionData& InSectionData);
	void UpdateContent_New(const FSectionData& InSectionData);

	FORCEINLINE FSectionData& GetSectionData() { return SectionData; }

	static USingleComponentSection* Create();

	UFUNCTION()
		void UnSelectedColor();
	UFUNCTION()
		void SelectedColor();

	UFUNCTION(BlueprintImplementableEvent)
		void RefreshImage(const FString& ImageURL);

	void SetMatImageAndName(const int32& MatIndex, const FString& ThumbnailPath, const FString& Name);
public:
	void SelectOnMaterial();
	void SendSelectState();
	void SetSelectState(bool isSelected);
	bool GetSelectState();
	void MaterialSelectColor(bool isSelected);

private:
	UFUNCTION()
		void SectionExpressionEdit(const int32& EditType, const FString& OutExpression);

	UFUNCTION()
		void OnSectionMaterialHandler(const int32& MatId, const EMaterialType& MatType, const FString& NewExpressOrValue);

private:

	static FString SingleComponentSectionPath;
	bool IsSelected;

protected:
	UFUNCTION()
		FEventReply LeftMouseButtonDownToSelect(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply LeftMouseButtonDoubleDownToOpen(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

protected:
	UFUNCTION()
		void OnClickedBtnSectionEdit();
	//UFUNCTION()
	//	void OnClickedBtnMatEdit();

	UFUNCTION()
		void OnTextCommittedEdtSectionName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtVisible(const FText& Text, ETextCommit::Type CommitMethod);
	//UFUNCTION()
	//	void OnTextCommittedEdtMatName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtID(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtSectionValue(const FText& Text, ETextCommit::Type CommitMethod);
	//UFUNCTION()
	//	void OnTextCommittedEdtMatValue(const FText& Text, ETextCommit::Type CommitMethod);



public:
	FSectionInfoChangeDelegate SectionDataChangeDelegate;
	FSectionSelectDelegate SectionSelectDelegate;

private:
	FSectionData SectionData;

	UPROPERTY()
		TArray<USingleComponentMaterial*> SectionMaterials;

private:
	UPROPERTY()
		UBorder* BorBackground;
	UPROPERTY()
		UBorder* BorSection;
	UPROPERTY()
		UBorder* BorSectionTitle;
	UPROPERTY()
		UBorder* BorSectionName;
	UPROPERTY()
		UBorder* BorVisibleTitle;
	UPROPERTY()
		UBorder* BorVisible;
	UPROPERTY()
		UBorder* BorSectionValue;
	UPROPERTY()
		UButton* BtnSectionEdit;
	UPROPERTY()
		UButton* BtnMatEdit;
	UPROPERTY()
		UImage* ImgSectionImage;
	UPROPERTY()
		UEditableText* EdtSectionName;
	UPROPERTY()
		UEditableText* EdtVisible;
	UPROPERTY()
		UEditableText* EdtSectionValue;
	UPROPERTY()
		UScrollBox* SBMaterial;
};
