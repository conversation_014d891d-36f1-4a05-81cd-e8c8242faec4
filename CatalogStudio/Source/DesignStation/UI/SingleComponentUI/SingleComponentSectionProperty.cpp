// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentSectionProperty.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/ScrollBoxSlot.h"
#include "SingleComponentSectionToolBar.h"
#include "SingleComponentTitleWidget.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#define LOCTEXT_NAMESPACE "SingleComponentSectionProperty"

FString USingleComponentSectionProperty::SectionPropertyPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSectionProperty.SingleComponentSectionProperty_C'");

//const TArray<FString> ScbContentTitle =
//{
//	NSLOCTEXT(LOCTEXT_NAMESPACE, "PointAndLineKey", "Point").ToString(),
//	NSLOCTEXT(LOCTEXT_NAMESPACE, "RectKey", "Rect").ToString(),
//	NSLOCTEXT(LOCTEXT_NAMESPACE, "EllipseKey", "Ellipse").ToString(),
//	NSLOCTEXT(LOCTEXT_NAMESPACE, "CuboidKey", "Cuboid").ToString()
//};

bool USingleComponentSectionProperty::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	BIND_PARAM_CPP_TO_UMG(SCBContent, SCB_Content);
	/*BIND_PARAM_CPP_TO_UMG(BtnPointBackground, Btn_Pointbackground);
	BIND_PARAM_CPP_TO_UMG(TxtTitleOne, Txt_TitleOne);
	BIND_PARAM_CPP_TO_UMG(SCBOne, SCB_One);
	BIND_PARAM_CPP_TO_UMG(BtnBackground, Btn_Background);
	BIND_PARAM_CPP_TO_UMG(SCBTwo, SCB_Two);*/

	return true;
}

void USingleComponentSectionProperty::UpdateContent(const FSectionProperty& SectionProperty)
{
	SectionPoints.Empty();
	SectionLines.Empty();
	OldEditType = -1;
	OldWidgetId = -1;
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---update sectipn property content"));

	switch (SectionProperty.Type)
	{
	case ESectionType::ECustomPlan:
	{
		SCBContent->ClearChildren();
		TitleOneWidget = USingleComponentTitleWidget::Create();
		TitleOneWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Point")));
		SCBContent->AddChild(TitleOneWidget);
		{
			TitleTwoWidget = USingleComponentTitleWidget::Create();
			TitleTwoWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Line")));
			TitleTwoWidget->PaddingWidget();
		}
		for (int i = 0; i < SectionProperty.SectionPoints.Num(); ++i)
		{
			USingleComponentPoint* PointTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentPoint, FGeomtryPointProperty>(SectionProperty.SectionPoints[i]);
			PointTemp->UpdatePointIndex(i + 1);
			PointTemp->PointDataChangeDelegate.BindUFunction(this, FName(TEXT("PointPropertyEdit")));
			PointTemp->PointWidgetSelectDelegate.BindUFunction(this, FName(TEXT("SectionPropertyWidgetSelectEdit")));
			SectionPoints.Add(SectionProperty.SectionPoints[i].ID, PointTemp);
			SCBContent->AddChild(PointTemp);
		}
		TitleTwoWidget = USingleComponentTitleWidget::Create();
		TitleTwoWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Line")));
		TitleTwoWidget->PaddingWidget();
		SCBContent->AddChild(TitleTwoWidget);
		for (int i = 0; i < SectionProperty.SectionLines.Num(); ++i)
		{
			USingleComponentLine* LineTemp = UUIFunctionLibrary::CreatePropertyView<USingleComponentLine, FGeomtryLineProperty>(SectionProperty.SectionLines[i]);
			LineTemp->UpdateLineIndex(i + 1);
			LineTemp->LinePropertyChangeDelegate.BindUFunction(this, FName(TEXT("LinePropertyEdit")));
			LineTemp->LineWidgetSelectDelegate.BindUFunction(this, FName(TEXT("SectionPropertyWidgetSelectEdit")));
			SectionLines.Add(SectionProperty.SectionLines[i].ID, LineTemp);
			SCBContent->AddChild(LineTemp);
		}
		break;
	}
	case ESectionType::ERectangle:
	{
		if (false == SCBContent->HasChild(SectionRectangle))
		{
			SCBContent->ClearChildren();
			TitleOneWidget = USingleComponentTitleWidget::Create();
			TitleOneWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Rect")));
			SCBContent->AddChild(TitleOneWidget);
			SectionRectangle = UUIFunctionLibrary::CreateUIWidget<USingleComponentRectangle>();
			SCBContent->AddChild(SectionRectangle);
		}
		SectionRectangle->UpdateContent(SectionProperty.SectionRectangle);
		SectionRectangle->SetVisibility(ESlateVisibility::Visible);
		SectionRectangle->RectangleChangeDelegate.BindUFunction(this, FName(TEXT("RectanglePropertyEdit")));
		break;
	}
	case ESectionType::EEllipse:
	{
		if (false == SCBContent->HasChild(SectionEllipse))
		{
			SCBContent->ClearChildren();
			TitleOneWidget = USingleComponentTitleWidget::Create();
			TitleOneWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Ellipse")));
			SCBContent->AddChild(TitleOneWidget);
			SectionEllipse = UUIFunctionLibrary::CreateUIWidget<USingleComponentEllipse>();
			SCBContent->AddChild(SectionEllipse);
		}
		SectionEllipse->UpdateContent(SectionProperty.SectionEllipse);
		SectionEllipse->SetVisibility(ESlateVisibility::Visible);
		SectionEllipse->EllipsePropertyChangeDelegate.BindUFunction(this, FName(TEXT("EllipsePropertyEdit")));

		break;
	}
	case ESectionType::ECube:
	{
		if (false == SCBContent->HasChild(SectionCuboid))
		{
			SCBContent->ClearChildren();
			TitleOneWidget = USingleComponentTitleWidget::Create();
			TitleOneWidget->UpdateContent(FText::FromStringTable(FName("PosSt"), TEXT("Cuboid")));
			SCBContent->AddChild(TitleOneWidget);
			SectionCuboid = UUIFunctionLibrary::CreateUIWidget<USingleComponentCuboid>();
			SCBContent->AddChild(SectionCuboid);
		}
		SectionCuboid->UpdateContent(SectionProperty.SectionCuboid);
		SectionCuboid->SetVisibility(ESlateVisibility::Visible);
		SectionCuboid->CuboidChangeDelegate.BindUFunction(this, FName(TEXT("CuboidPropertyEdit")));
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}

}

void USingleComponentSectionProperty::UpdateSectionWidgetSelectStyle(const int32& EditType, const int32& WidgetId)
{
	UpdateOldSelectWidget(EditType, WidgetId);
	if (WidgetId == -1)
	{
		return;
	}
	UpdateViewSelectWidgetState(EditType, WidgetId, true);
	switch ((ESectionSelectType)EditType)
	{
	case ESectionSelectType::Point:
	{
		if (/*SCBOne && */IS_OBJECT_PTR_VALID(SCBContent))
		{
			SCBContent->ScrollWidgetIntoView(SectionPoints[WidgetId], true, EDescendantScrollDestination::IntoView);
			//SCBOne->ScrollWidgetIntoView(SectionPoints[WidgetId], true, EDescendantScrollDestination::IntoView);
		}
		break;
	}
	case ESectionSelectType::Line:
	{
		if (/*SCBTwo && */IS_OBJECT_PTR_VALID(SCBContent))
		{
			SCBContent->ScrollWidgetIntoView(SectionLines[WidgetId], true, EDescendantScrollDestination::IntoView);
			//SCBTwo->ScrollWidgetIntoView(SectionLines[WidgetId], true, EDescendantScrollDestination::IntoView);
		}
		break;
	}
	case ESectionSelectType::Rectangle:
	{
		break;
	}
	case ESectionSelectType::Ellipse:
	{
		break;
	}
	case ESectionSelectType::Cuboid:
	{
		break;
	}
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USingleComponentSectionProperty::UpdateHoverSectionWidgetStyle(const int32& EditType, const int32& WidgetId, bool IsHovered)
{
	if (WidgetId == -1)
	{
		return;
	}
	switch ((ESectionSelectType)EditType)
	{
	case ESectionSelectType::Point:
	{
		SectionPoints[WidgetId]->UpdateSelectState(IsHovered);
		if (/*SCBOne && */IsHovered && IS_OBJECT_PTR_VALID(SCBContent))
		{
			SCBContent->ScrollWidgetIntoView(SectionPoints[WidgetId], true, EDescendantScrollDestination::IntoView);
			//SCBOne->ScrollWidgetIntoView(SectionPoints[WidgetId], true, EDescendantScrollDestination::IntoView);
		}
		break;
	}
	case ESectionSelectType::Line:
	{
		SectionLines[WidgetId]->UpdateSelectState(IsHovered);
		if (/*SCBTwo && */IsHovered && IS_OBJECT_PTR_VALID(SCBContent))
		{
			SCBContent->ScrollWidgetIntoView(SectionLines[WidgetId], true, EDescendantScrollDestination::IntoView);
			//SCBTwo->ScrollWidgetIntoView(SectionLines[WidgetId], true, EDescendantScrollDestination::IntoView);
		}
		break;
	}
	case ESectionSelectType::Rectangle:break;
	case ESectionSelectType::Ellipse:break;
	case ESectionSelectType::Cuboid:break;
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

USingleComponentSectionProperty* USingleComponentSectionProperty::Create()
{
	UClass* SectionPropertyBp = LoadClass<UUserWidget>(NULL, *SectionPropertyPath);
	checkf(SectionPropertyBp, TEXT("load section property bp error!"));
	USingleComponentSectionProperty* SectionPropertyItem = CreateWidget<USingleComponentSectionProperty>(GWorld.GetReference(), SectionPropertyBp);
	checkf(SectionPropertyItem, TEXT("create section property error"));
	return SectionPropertyItem;
}

void USingleComponentSectionProperty::PointPropertyEdit(const int32& EditType, const FGeomtryPointProperty& PointProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---point data changed"));
	SectionPointChangedDelegate.ExecuteIfBound(EditType, PointProperty);
}

void USingleComponentSectionProperty::LinePropertyEdit(const int32& EditType, const FGeomtryLineProperty& LineProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---line data changed"));
	SectionLineChangedDelegate.ExecuteIfBound(EditType, LineProperty);
}

void USingleComponentSectionProperty::RectanglePropertyEdit(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---rectangle data changed"));
	SectionRectangleChangedDelegate.ExecuteIfBound(EditType, RectangleProperty);
}

void USingleComponentSectionProperty::EllipsePropertyEdit(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---ellipse data changed"));
	SectionEllipseChangedDelegate.ExecuteIfBound(EditType, EllipseProperty);
}

void USingleComponentSectionProperty::CuboidPropertyEdit(const int32& EditType, const FGeomtryCubeProperty& CuboidProperty)
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionProperty---cuboid data changed"));
	SectionCuboidChangedDelegate.ExecuteIfBound(EditType, CuboidProperty);
}

void USingleComponentSectionProperty::UpdateOldSelectWidget(const int32& EditType, const int32& WidgetId)
{
	if (OldWidgetId != -1)
	{
		UpdateViewSelectWidgetState(OldEditType, OldWidgetId, false);
		OldEditType = -1;
		OldWidgetId = -1;
	}
	if (WidgetId != -1)
	{
		OldEditType = EditType;
		OldWidgetId = WidgetId;
	}
}

void USingleComponentSectionProperty::UpdateViewSelectWidgetState(const int32& EditType, const int32& WidgetId, bool IsSelect)
{
	switch ((ESectionSelectType)EditType)
	{
	case ESectionSelectType::Point:
	{
		SectionPoints[WidgetId]->UpdateSelectState(IsSelect);
		SectionPoints[WidgetId]->SetViewPartSelect(IsSelect);
		break;
	}
	case ESectionSelectType::Line:
	{
		SectionLines[WidgetId]->UpdateSelectState(IsSelect);
		SectionLines[WidgetId]->SetViewPartSelect(IsSelect);
		break;
	}
	case ESectionSelectType::Rectangle:break;
	case ESectionSelectType::Ellipse:break;
	case ESectionSelectType::Cuboid:break;
	default:
	{
		checkNoEntry();
		break;
	}
	}
}

void USingleComponentSectionProperty::SectionPropertyWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty)
{
	WidgetSelectDelegate.ExecuteIfBound(EditType, WidgetId, IsOver, OnProperty);
}

#undef LOCTEXT_NAMESPACE