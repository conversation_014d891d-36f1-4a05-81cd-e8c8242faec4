// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentPoint.h"
#include "SingleComponentLine.h"
#include "SingleComponentRectangle.h"
#include "SingleComponentEllipse.h"
#include "SingleComponentCuboid.h"
#include "DesignStation/BasicClasses/GeneralDelegates.h"
#include "SingleComponentSectionProperty.generated.h"

/**
 *
 */

class UScrollBox;
class USizeBox;
class UBorder;
class UButton;
class UTextBlock;
class USingleComponentTitleWidget;

USTRUCT(BlueprintType)
struct FSectionProperty
{
	GENERATED_USTRUCT_BODY()
public:
	ESectionType Type;
	TArray<FGeomtryPointProperty> SectionPoints;
	TArray<FGeomtryLineProperty> SectionLines;
	FGeomtryRectanglePlanProperty SectionRectangle;
	FGeomtryEllipsePlanProperty SectionEllipse;
	FGeomtryCubeProperty SectionCuboid;
};

UCLASS()
class DESIGNSTATION_API USingleComponentSectionProperty : public UUserWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void UpdateContent(const FSectionProperty& SectionProperty);
	void UpdateSectionWidgetSelectStyle(const int32& EditType, const int32& WidgetId);
	void UpdateHoverSectionWidgetStyle(const int32& EditType, const int32& WidgetId, bool IsHovered);

	static USingleComponentSectionProperty* Create();

	FORCEINLINE FSectionProperty& GetSectionProperty() { return SectionProperty; }

public:
	FPointDataChangedDelegate SectionPointChangedDelegate;
	FLinePropertyChangeDelegate SectionLineChangedDelegate;
	FRectanglePropertyChangeDelegate SectionRectangleChangedDelegate;
	FEllipsePropertyChangeDelegate SectionEllipseChangedDelegate;
	FCuboidPropertyChangeDelegate SectionCuboidChangedDelegate;

	FWidgetSelectDelegate WidgetSelectDelegate;
private:
	void UpdateOldSelectWidget(const int32& EditType, const int32& WidgetId);
	void UpdateViewSelectWidgetState(const int32& EditType, const int32& WidgetId, bool IsSelect);
	UFUNCTION()
		void SectionPropertyWidgetSelectEdit(const int32& EditType, const int32& WidgetId, bool IsOver, bool OnProperty);

private:
	FSectionProperty SectionProperty;

	static FString SectionPropertyPath;

protected:
	UFUNCTION()
		void PointPropertyEdit(const int32& EditType, const FGeomtryPointProperty& PointProperty);
	UFUNCTION()
		void LinePropertyEdit(const int32& EditType, const FGeomtryLineProperty& LineProperty);
	UFUNCTION()
		void RectanglePropertyEdit(const int32& EditType, const FGeomtryRectanglePlanProperty& RectangleProperty);
	UFUNCTION()
		void EllipsePropertyEdit(const int32& EditType, const FGeomtryEllipsePlanProperty& EllipseProperty);
	UFUNCTION()
		void CuboidPropertyEdit(const int32& EditType, const FGeomtryCubeProperty& CuboidProperty);

private:
	UPROPERTY()
		USingleComponentTitleWidget* TitleOneWidget;
	UPROPERTY()
		USingleComponentTitleWidget* TitleTwoWidget;

	UPROPERTY()
		TMap<int32, USingleComponentPoint*> SectionPoints;
	UPROPERTY()
		TMap<int32, USingleComponentLine*> SectionLines;

	UPROPERTY()
		USingleComponentRectangle* SectionRectangle;

	UPROPERTY()
		USingleComponentEllipse* SectionEllipse;

	UPROPERTY()
		USingleComponentCuboid* SectionCuboid;

	int32 OldEditType;
	int32 OldWidgetId;

private:
	UPROPERTY()
		UScrollBox* SCBContent;
	/*UPROPERTY()
		UButton* BtnPointBackground;
	UPROPERTY()
		UTextBlock* TxtTitleOne;
	UPROPERTY()
		UScrollBox* SCBOne;
	UPROPERTY()
		UButton* BtnBackground;
	UPROPERTY()
		UScrollBox* SCBTwo;*/
};
