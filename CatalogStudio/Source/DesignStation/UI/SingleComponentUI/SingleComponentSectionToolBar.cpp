// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentSectionToolBar.h"

#include "Components/Border.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"

#define LOCTEXT_NAMESPACE "SingleComponentSectionToolBar"

FString USingleComponentSectionToolBar::SectionToolBarPath 
= TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSectionToolBar.SingleComponentSectionToolBar_C'");

bool USingleComponentSectionToolBar::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnPoint, Btn_Point);
	BIND_PARAM_CPP_TO_UMG(BtnRect, Btn_Rect);
	BIND_PARAM_CPP_TO_UMG(BtnEllipse, Btn_Ellipse);
	BIND_PARAM_CPP_TO_UMG(BtnCuboid, Btn_Cuboid);
	BIND_PARAM_CPP_TO_UMG(BtnImportCAD, Btn_ImportCAD);
	BIND_PARAM_CPP_TO_UMG(BtnClear, Btn_Clear);    
	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);
	BIND_PARAM_CPP_TO_UMG(BorCheckedPoint, Bor_CheckedPoint);

	BIND_WIDGET_FUNCTION(BtnPoint, OnClicked, USingleComponentSectionToolBar::OnClickedBtnPoint);
	BIND_WIDGET_FUNCTION(BtnRect, OnClicked, USingleComponentSectionToolBar::OnClickedBtnRect);
	BIND_WIDGET_FUNCTION(BtnEllipse, OnClicked, USingleComponentSectionToolBar::OnClickedBtnEllipse);
	BIND_WIDGET_FUNCTION(BtnCuboid, OnClicked, USingleComponentSectionToolBar::OnClickedBtnCuboid);
	BIND_WIDGET_FUNCTION(BtnImportCAD, OnClicked, USingleComponentSectionToolBar::OnClickedBtnImportCAD);
	BIND_WIDGET_FUNCTION(BtnClear, OnClicked, USingleComponentSectionToolBar::OnClickedBtnClear);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, USingleComponentSectionToolBar::OnClickedBtnSave);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, USingleComponentSectionToolBar::OnClickedBtnExit);

	ResetToolBarState();

	return true;
}

void USingleComponentSectionToolBar::UpdateDrawState(bool IsCustom)
{
	if (IS_OBJECT_PTR_VALID(BtnPoint) && IS_OBJECT_PTR_VALID(BtnRect) && IS_OBJECT_PTR_VALID(BtnEllipse) && IS_OBJECT_PTR_VALID(BtnCuboid))
	{
		BtnPoint->SetVisibility(IsCustom ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnRect->SetVisibility(IsCustom ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnEllipse->SetVisibility(IsCustom ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		BtnCuboid->SetVisibility(IsCustom ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
	}
}

USingleComponentSectionToolBar * USingleComponentSectionToolBar::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USingleComponentSectionToolBar>(USingleComponentSectionToolBar::SectionToolBarPath);
}

void USingleComponentSectionToolBar::SetSelectType(const ESectionToolBarEditType & SelectType)
{
	if ((SelectType == ESectionToolBarEditType::Clear) || (SelectType == ESectionToolBarEditType::Save))
		return;
	SectionSelectType = SelectType;
}

void USingleComponentSectionToolBar::SectionSelectState()
{
	if (SectionSelectType == ESectionToolBarEditType::Point)
	{
		BorCheckedPoint->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		BorCheckedPoint->SetVisibility(ESlateVisibility::Hidden);
	}
}

void USingleComponentSectionToolBar::OnClickedBtnPoint()
{
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Point);
}

void USingleComponentSectionToolBar::OnClickedBtnRect()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionToolBar---rectangle edit"));

	//SwitchCheckBoxsState(ESelectCheckBoxType::Rectangle);
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Rectangle);

}

void USingleComponentSectionToolBar::OnClickedBtnEllipse()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionToolBar---ellipse edit"));

	//SwitchCheckBoxsState(ESelectCheckBoxType::Ellipse);
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Ellipse);

}

void USingleComponentSectionToolBar::OnClickedBtnCuboid()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionToolBar---cuboid edit"));


	//SwitchCheckBoxsState(ESelectCheckBoxType::Cuboid);
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Cuboid);
}

void USingleComponentSectionToolBar::OnClickedBtnImportCAD()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentSectionToolBar---ImportDXF edit"));


	//SwitchCheckBoxsState(ESelectCheckBoxType::Cuboid);
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::ImportCAD);
}

void USingleComponentSectionToolBar::OnClickedBtnClear()
{

	ResetToolBarState();
	/*bool Result = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(
	NSLOCTEXT(LOCTEXT_NAMESPACE, "ClearTitleKey", "Warning").ToString()
	, NSLOCTEXT(LOCTEXT_NAMESPACE, "ClearContentKey", "Make Sure To Clear The Section Content?").ToString());
	if (Result)
	{
		
	}*/
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Clear);
}

void USingleComponentSectionToolBar::OnClickedBtnSave()
{
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Save);
}


void USingleComponentSectionToolBar::OnClickedBtnExit()
{
	ResetToolBarState();
	SectionToolBarDelegate.ExecuteIfBound((int32)ESectionToolBarEditType::Exit);
}

void USingleComponentSectionToolBar::NormalMode()
{
	BtnRect->SetVisibility(ESlateVisibility::Visible);
	BtnEllipse->SetVisibility(ESlateVisibility::Visible);
	BtnCuboid->SetVisibility(ESlateVisibility::Visible);
	BtnImportCAD->SetVisibility(ESlateVisibility::Visible);
}

void USingleComponentSectionToolBar::LoftingOperationMode()
{
	BtnRect->SetVisibility(ESlateVisibility::Collapsed);
	BtnEllipse->SetVisibility(ESlateVisibility::Collapsed);
	BtnCuboid->SetVisibility(ESlateVisibility::Collapsed);
	BtnImportCAD->SetVisibility(ESlateVisibility::Visible);
}

void USingleComponentSectionToolBar::CutoutOperationMode()
{
	BtnRect->SetVisibility(ESlateVisibility::Visible);
	BtnEllipse->SetVisibility(ESlateVisibility::Visible);
	BtnCuboid->SetVisibility(ESlateVisibility::Collapsed);
	BtnImportCAD->SetVisibility(ESlateVisibility::Visible);
}

void USingleComponentSectionToolBar::ResetToolBarState()
{
	SetSelectType(ESectionToolBarEditType::Select);
	BorCheckedPoint->SetVisibility(ESlateVisibility::Hidden);
}


#undef LOCTEXT_NAMESPACE
