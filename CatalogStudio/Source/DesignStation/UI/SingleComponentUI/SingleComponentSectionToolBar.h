// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSectionToolBar.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class ESectionToolBarEditType : uint8
{
	Select = 0,
	Point,
	Rectangle,
	Ellipse,
	Cuboid,
	Clear,
	ImportCAD,
	Save,
	Exit
};

//UENUM(BlueprintType)
//enum class ESelectCheckBoxType : uint8
//{
//	Select = 0,
//	Point,
//	Rectangle,
//	Ellipse,
//	Cuboid,
//	NONE
//};

UENUM(BlueprintType)
enum class EToolBarState : uint8
{
	DrawSection,
	DrawLofting,
	DrawCutOut
};

class UBorder;
class UCheckBox;
class UButton;
class USizeBox;

const FLinearColor CheckedColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f);

DECLARE_DYNAMIC_DELEGATE_OneParam(FSectionToolBarDelegate, const int32&, EditType);

UCLASS()
class DESIGNSTATION_API USingleComponentSectionToolBar : public UUserWidget
{
	GENERATED_BODY()
public:
	UFUNCTION()
		void SectionSelectState();
	virtual bool Initialize() override;
	/*
	void UpdateToolBarState(const int32& ToolBarState);*/
	void UpdateDrawState(bool IsCustom);
	static USingleComponentSectionToolBar * Create();
	void SetSelectType(const ESectionToolBarEditType& SelectType);
	void ResetToolBarState();

	UFUNCTION()
		void NormalMode();
	UFUNCTION()
		void LoftingOperationMode();
	UFUNCTION()
		void CutoutOperationMode();

private:
	static FString SectionToolBarPath;
//	void SwitchCheckBoxsState(const ESelectCheckBoxType& EditType);


protected:

	//UFUNCTION()
	//	void OnStateChangeCkbPoint(bool IsChecked);
	UFUNCTION()
		void OnClickedBtnPoint();
	UFUNCTION()
		void OnClickedBtnRect();
	UFUNCTION()
		void OnClickedBtnEllipse();
	UFUNCTION()
		void OnClickedBtnCuboid();
		UFUNCTION()
		void OnClickedBtnImportCAD();
	UFUNCTION()
		void OnClickedBtnClear();
	UFUNCTION()
		void OnClickedBtnSave();
	UFUNCTION()
		void OnClickedBtnExit();

private:
	
	UPROPERTY()
		UButton*	BtnPoint;
	UPROPERTY()
		UButton*	BtnRect;
	UPROPERTY()
		UButton*	BtnEllipse;
	UPROPERTY()
		UButton*	BtnCuboid;
	UPROPERTY()
		UButton*    BtnImportCAD;
	UPROPERTY()
		UButton*	BtnClear;
	UPROPERTY()
		UButton*	BtnSave;
	UPROPERTY()
		UButton*	BtnExit;
	UPROPERTY()
		UBorder*	BorCheckedPoint;
	
public:
	FSectionToolBarDelegate SectionToolBarDelegate;

private:
	ESectionToolBarEditType SectionSelectType;
};
