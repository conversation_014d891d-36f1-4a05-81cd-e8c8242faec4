// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentSectionUnitWidget.h"

bool USingleComponentSectionUnitWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void USingleComponentSectionUnitWidget::SetViewPartSelect(bool _IsSelect)
{
	IsViewSelect = _IsSelect;
}

bool USingleComponentSectionUnitWidget::GetViewPartSelect()
{
	return IsViewSelect;
}

void USingleComponentSectionUnitWidget::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
}

void USingleComponentSectionUnitWidget::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
}

FReply USingleComponentSectionUnitWidget::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	return FReply::Unhandled();
}
