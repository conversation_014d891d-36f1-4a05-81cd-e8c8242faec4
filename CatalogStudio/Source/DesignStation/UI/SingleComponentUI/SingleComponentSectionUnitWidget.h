// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentSectionUnitWidget.generated.h"

/**
 * 
 */

const FLinearColor SectionHoverOrSelect = FLinearColor(0.022174f, 0.467784f, 0.887923f, 1.0f);
const FLinearColor SectionNormal = FLinearColor(0.806952f, 0.806952f, 0.806952f, 1.0f);
const FLinearColor SectionValueNormal = FLinearColor::White;
const FLinearColor FontHoverOrSelect = FLinearColor::White;
const FLinearColor FontNormal = FLinearColor(0.132868f, 0.132868f, 0.132868f, 1.0f);

UCLASS()
class DESIGNSTATION_API USingleComponentSectionUnitWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void SetViewPartSelect(bool _IsSelect);
	virtual bool GetViewPartSelect();
	
protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	
public:
	bool IsViewSelect;

};
