// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentTitleWidget.h"

#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "Runtime/UMG/Public/Components/BorderSlot.h"
#include "Runtime/UMG/Public/Blueprint/WidgetLayoutLibrary.h"

FString USingleComponentTitleWidget::SingleComponentTitlePath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentTitleUI.SingleComponentTitleUI_C'");

bool USingleComponentTitleWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(SZTitle, SZ_Title);
	BIND_PARAM_CPP_TO_UMG(TxtTitle, Txt_Title);

	return true;
}

void USingleComponentTitleWidget::UpdateContent(const FText& InTitle)
{
	if (IS_OBJECT_PTR_VALID(TxtTitle))
	{
		TxtTitle->SetText(InTitle);
	}
}

USingleComponentTitleWidget * USingleComponentTitleWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USingleComponentTitleWidget>(USingleComponentTitleWidget::SingleComponentTitlePath);
}

void USingleComponentTitleWidget::PaddingWidget()
{
	if (IS_OBJECT_PTR_VALID(SZTitle))
	{
		UBorderSlot* TitleSlot = UWidgetLayoutLibrary::SlotAsBorderSlot(SZTitle);
		if (IS_OBJECT_PTR_VALID(TitleSlot))
		{
			TitleSlot->SetPadding(FMargin(0.0f, 11.0f, 0.0f, 0.0f));
		}
	}
}
