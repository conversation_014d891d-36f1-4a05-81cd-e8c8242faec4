// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentTitleWidget.generated.h"

/**
 * 
 */

class USizeBox;
class UTextBlock;

UCLASS()
class DESIGNSTATION_API USingleComponentTitleWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	void UpdateContent(const FText& InTitle);
	void PaddingWidget();

	static USingleComponentTitleWidget* Create();	

private:
	static FString SingleComponentTitlePath;
	
private:
	UPROPERTY()
		USizeBox* SZTitle;
	UPROPERTY()
		UTextBlock* TxtTitle;
};
