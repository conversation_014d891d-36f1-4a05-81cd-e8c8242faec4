// Fill out your copyright notice in the Description page of Project Settings.

#include "SingleComponentToolBar.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/CircularThrobber.h"
#include "Runtime/UMG/Public/Components/Image.h"

FString USingleComponentToolBar::SingleComponentToolBarPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentToolBar.SingleComponentToolBar_C'");


bool USingleComponentToolBar::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnSave, Btn_Save);
	BIND_PARAM_CPP_TO_UMG(BtnExit, Btn_Exit);
	BIND_PARAM_CPP_TO_UMG(CtSave, Ct_Save);
	BIND_PARAM_CPP_TO_UMG(ImgSave, Img_Save);
	BIND_WIDGET_FUNCTION(BtnSave, OnClicked, USingleComponentToolBar::OnClickedBtnSave);
	BIND_WIDGET_FUNCTION(BtnExit, OnClicked, USingleComponentToolBar::OnClickedBtnExit);

	BIND_WIDGET_FUNCTION(Btn_Load, OnClicked, USingleComponentToolBar::OnClickBtnLoad);

	return true;
}

USingleComponentToolBar * USingleComponentToolBar::Create()
{
	return  UUIFunctionLibrary::UIWidgetCreate<USingleComponentToolBar>(USingleComponentToolBar::SingleComponentToolBarPath);
}


void USingleComponentToolBar::OnClickedBtnSave()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentToolBar---save single component file"));
	CurrentActionType = ESingleComponentToolBarType::Save;
	SingleComponentEditDelegate.ExecuteIfBound(static_cast<int32>(ESingleComponentToolBarType::Save));
}

void USingleComponentToolBar::OnClickedBtnExit()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentToolBar---quit single component mode"));
	CurrentActionType = ESingleComponentToolBarType::Exit;
	SingleComponentEditDelegate.ExecuteIfBound(static_cast<int32>(ESingleComponentToolBarType::Exit));
}

void USingleComponentToolBar::OnClickBtnLoad()
{
	UE_LOG(LogTemp, Log, TEXT("SingleComponentToolBar---Load preview single component mode"));
	CurrentActionType = ESingleComponentToolBarType::Load;
	SingleComponentEditDelegate.ExecuteIfBound(static_cast<int32>(ESingleComponentToolBarType::Load));
}

void USingleComponentToolBar::ChangeSaveState(bool bLoading)
{
	if (bLoading)
	{
		ImgSave->SetVisibility(ESlateVisibility::Collapsed);
		CtSave->SetVisibility(ESlateVisibility::Visible);
		BtnSave->SetIsEnabled(false);
		BtnExit->SetIsEnabled(false);
	}
	else
	{
		ImgSave->SetVisibility(ESlateVisibility::Visible);
		CtSave->SetVisibility(ESlateVisibility::Collapsed);
		BtnSave->SetIsEnabled(true);
		BtnExit->SetIsEnabled(true);
	}
}
