// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SingleComponentToolBar.generated.h"


UENUM(BlueprintType)
enum class ESingleComponentToolBarType : uint8
{
	Save = 0,
	Exit,
	Load
};

class UButton;
class UCircularThrobber;
class UImage;

DECLARE_DYNAMIC_DELEGATE_OneParam(FSingleComponentToolBarDelegate, const int32&, EditType);

UCLASS()
  class DESIGNSTATION_API USingleComponentToolBar : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	ESingleComponentToolBarType GetCurrentActionType() { return CurrentActionType; }

	static USingleComponentToolBar * Create();

public:
	static FString SingleComponentToolBarPath;

protected:
	UFUNCTION()
		void OnClickedBtnSave();
	UFUNCTION()
		void OnClickedBtnExit();

	UFUNCTION()
	void OnClickBtnLoad();

private:
	ESingleComponentToolBarType CurrentActionType;

private:
	UPROPERTY()
		UButton* BtnSave;
	UPROPERTY()
		UButton* BtnExit;

	UPROPERTY(BlueprintReadWrite, meta=(AllowPrivateAccess=true, BindWidget))
		UButton* Btn_Load;

	UPROPERTY()
		UCircularThrobber* CtSave;
	UPROPERTY()
		UImage* ImgSave;
public:
	void ChangeSaveState(bool bLoading);
public:
	FSingleComponentToolBarDelegate SingleComponentEditDelegate;
};
