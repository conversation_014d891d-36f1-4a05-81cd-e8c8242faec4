// Fill out your copyright notice in the Description page of Project Settings.

#include "ImageMenuWidget.h"

#include "Components/Button.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UImageMenuWidget::ImageMenuWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/ImageMenuWidget.ImageMenuWidget_C'");

bool UImageMenuWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnImport, Btn_Import);

	BIND_WIDGET_FUNCTION(BtnImport, OnClicked, UImageMenuWidget::OnClickedBtnImport);

	return true;
}

UImageMenuWidget * UImageMenuWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UImageMenuWidget>(UImageMenuWidget::ImageMenuWidgetPath);
}

void UImageMenuWidget::OnClickedBtnImport()
{
	ImageMenuDelegate.ExecuteIfBound(1);
}

void UImageMenuWidget::SetItem(UStyleWidgetChild * item)
{
	StyleWidgetChild = item;
}

UStyleWidgetChild * UImageMenuWidget::GetItem()
{
	return StyleWidgetChild;
}
