// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleWidgetChild.h"
#include "ImageMenuWidget.generated.h"

/**
 * 
 */
class UButton;

UCLASS()
class DESIGNSTATION_API UImageMenuWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static UImageMenuWidget* Create();
	
private:
	static FString ImageMenuWidgetPath;
protected:
	UFUNCTION()
		void OnClickedBtnImport();
private:
	UPROPERTY()
		UButton* BtnImport;
	UStyleWidgetChild* StyleWidgetChild;

public:
	FStyleOpTypeDelegate ImageMenuDelegate;

	void SetItem(UStyleWidgetChild* item);
	UStyleWidgetChild* GetItem();
};
