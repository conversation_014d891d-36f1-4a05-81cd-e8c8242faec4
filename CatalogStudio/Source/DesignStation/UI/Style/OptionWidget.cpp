// Fill out your copyright notice in the Description page of Project Settings.

#include "OptionWidget.h"

#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString UOptionWidget::OptionWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/OptionWidget.OptionWidget_C'");

bool UOptionWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnAdd, Btn_Add);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_PARAM_CPP_TO_UMG(SBItem, SB_Item);

	BIND_WIDGET_FUNCTION(BtnAdd, OnClicked, UOptionWidget::OnClickedBtnAdd);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UOptionWidget::OnClickedBtnDelete);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UOptionWidget::OnClickedBtnUp);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UOptionWidget::OnClickedBtnDown);

	SelectOptionID = TEXT("-1");
	ResetOperateState();
	BindDelegates();
	return true;
}

void UOptionWidget::ResetOperateState()
{
	/*if (IS_OBJECT_PTR_VALID(BtnAdd))
	{
		BtnAdd->SetIsEnabled(false);
	}*/
	Super::ResetOperateState();
}

void UOptionWidget::ResetSelectState()
{
	ResetOperateState();
	if (!SelectOptionID.Equals(TEXT("-1")))
	{
		if (SubOptionItems.Contains(SelectOptionID) && SubOptionItems[SelectOptionID]) SubOptionItems[SelectOptionID]->SetSelectState(false);
		SelectOptionID = TEXT("-1");
	}
}

void UOptionWidget::UpdateOperateState(const ESubItemOrderType& InType)
{
	Super::UpdateOperateState(InType);
}

void UOptionWidget::StyleWidgetOperate(const EWidgetOperateType& InType)
{
	if (InType == EWidgetOperateType::Add)
	{
		FDecorateContent NewOptionData;
		ConstructNewData(NewOptionData, !SelectOptionID.Equals(TEXT("-1")));
		OptionSelectDelegate.ExecuteIfBound(NewOptionData.id);
	}
	else if (InType == EWidgetOperateType::Del)
	{
		bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete selected option?")).ToString());
		if (!Res)
		{
			return;
		}
		if (SelectOptionID.Equals(TEXT("-1")))
		{
			return;
		}
		SubOptionItems[SelectOptionID]->DeleteCurrentOption();
		SubOptionItems.Remove(SelectOptionID);
		SelectOptionID = TEXT("-1");
		UpdateOperateState(ESubItemOrderType::NoneSelect);
		OptionDelDelegate.ExecuteIfBound();
		/*if (UDecorateContentOperatorLibrary::DeleteDecorateContent(SelectOptionID))
		{
			TArray<FDecorateSelection> OutDecorateSelections;
			if (UDecorateSelectionOperatorLibrary::SelectAllDecorateSelectionByParentID(SelectOptionID, OutDecorateSelections))
			{
				for (auto& SelectionData : OutDecorateSelections)
				{
					UDecorateSelectionOperatorLibrary::DeleteDecorateSelection(SelectionData.id);
					UDecoParamFunctionLibrary::DeleteDecoParamBySelectionID(SelectionData.id);
				}
			}
			SubOptionItems[SelectOptionID]->SetVisibility(ESlateVisibility::Collapsed);
			SBItem->RemoveChild(SubOptionItems[SelectOptionID]);
			SubOptionItems.Remove(SelectOptionID);
			SelectOptionID = -1;
			UpdateOperateState(ESubItemOrderType::NoneSelect);
			OptionDelDelegate.ExecuteIfBound();
		}*/
	}
	else if (InType == EWidgetOperateType::Up)
	{
		OptionSubItemMoveAction(SelectOptionID, true);
	}
	else if (InType == EWidgetOperateType::Down)
	{
		OptionSubItemMoveAction(SelectOptionID, false);
	}
}

void UOptionWidget::GetOptionsContent()
{
	TArray<FDecorateContent> OutContent = TArray<FDecorateContent>();
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA();
	for(const auto Iter : StyleRefData.content_datas)
	{
		auto& Temp = OutContent.AddDefaulted_GetRef();
		PARSE_CONTENT_REF_DATA_TO_ACTION_DATA(Temp, Iter);
	}

#else
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_content ORDER BY SORT_ORDER ASC"), OutContent);
#endif

	if (SelectOptionID.Equals(TEXT("-1")))
	{
		UpdateContent(OutContent);
	}
	else
	{
		RefreshContent(OutContent, SelectOptionID);
	}

	//ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	//if (CatalogPC)
	//{
	//	std::shared_ptr<catalog_studio_message::SearchDecorateContentRequest> pMsg(new catalog_studio_message::SearchDecorateContentRequest);
	//	SearchUUID = CatalogPC->UEToNetRequest<catalog_studio_message::SearchDecorateContentRequest>(pMsg, TEXT("DecorateContent/retrieve"));
	//}
}

void UOptionWidget::UpdateContent(const TArray<FDecorateContent>& Items)
{
	SBItem->ClearChildren();
	SubOptionItems.Empty();
	SelectOptionID = TEXT("-1");
	for (auto& iter : Items)
	{
		UOptionWidgetChild* Child = UOptionWidgetChild::Create();
		Child->SetContent(iter);
		Child->SelectOptionDelegate.BindUFunction(this, FName(TEXT("OnOptionItemSelectHandler")));
		Child->SetVisibility(ESlateVisibility::Visible);
		SBItem->AddChild(Child);
		SubOptionItems.Add(iter.id, Child);
	}
}

void UOptionWidget::SetBtnAddEnable(bool IsEnable)
{
	if (IS_OBJECT_PTR_VALID(BtnAdd))
	{
		BtnAdd->SetIsEnabled(IsEnable);
	}
}

UOptionWidget* UOptionWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UOptionWidget>(UOptionWidget::OptionWidgetPath);
}

void UOptionWidget::InsertNewOption(const FDecorateContent& NewOption)
{
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(FString::Printf(TEXT("insert into decorate_content(ID, NAME ,SORT_ORDER) values('%s','%s','%d')"), *NewOption.id, *NewOption.name, NewOption.sort_order));
	GetOptionsContent();
	/*ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		std::shared_ptr<catalog_studio_message::CreateDecorateContentRequest> pMsg(new catalog_studio_message::CreateDecorateContentRequest);
		catalog_studio_db::DecorateContent* TempOption = pMsg->mutable_decorate_content();
		if (false == UStyleUeToProtobufLibrary::ConvertUeDataToProtobufData(NewOption, *TempOption))
		{
			UE_LOG(LogTemp, Log, TEXT("get empty data"));
			return;
		}

		if (!SelectOptionID.Equals(TEXT("-1")))
		{
			pMsg->set_create_type(catalog_studio_message::CreateDecorateContentRequest_CreateType::CreateDecorateContentRequest_CreateType_Insert);
			pMsg->set_insert_id(SelectOptionID);
		}
		else
		{
			pMsg->set_create_type(catalog_studio_message::CreateDecorateContentRequest_CreateType::CreateDecorateContentRequest_CreateType_Append);
		}
		CreateUUID = CatalogPC->UEToNetRequest<catalog_studio_message::CreateDecorateContentRequest>(pMsg, TEXT("DecorateContent/create"));
	}*/
}

void UOptionWidget::UpdateOptions(const TArray<FDecorateContent>& Options)
{
	for (auto iter : Options)
	{
		FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_content set NAME = '%s' , SORT_ORDER = '%d'  where ID = '%s'"), *iter.name, iter.sort_order, *iter.id));
	}
}

void UOptionWidget::BindDelegates()
{

}

//void UOptionWidget::OnGetOptionsHandler(const FString & UUID, const TArray<FDecorateContent>& OutDecoStyles)
//{
//	//if (UUID == SearchUUID && SelectOptionID.Equals(TEXT("-1")))
//	//{
//	//	UpdateContent(OutDecoStyles);
//	//}
//	//else
//	//{
//	//	RefreshContent(OutDecoStyles, SelectOptionID);
//	//}
//}

//void UOptionWidget::OnCreateOptionHandler(const FString & UUID, const FDecorateContent & OutDecoStyle)
//{
//
//	//OutDatas.Empty();
//	//for (TMap<int32, UOptionWidgetChild*>::TConstIterator Iter = SubOptionItems.CreateConstIterator(); Iter; ++Iter)
//	//{
//	//	OutDatas.Add(Iter->Value->GetData());
//	//}
//	//if (CreateUUID == UUID)
//	//{
//	//	UpdateOptions(OutDatas);
//	//	if (InsertIndex == -1)
//	//	{
//	//		OutDatas.Add(OutDecoStyle);
//	//	}
//	//	else
//	//	{
//	//		OutDatas.Insert(OutDecoStyle, InsertIndex);
//	//	}
//	//	RefreshContent(OutDatas, OutDecoStyle.id);
//	//}
//	/*if (CreateUUID == UUID) 
//	{
//		GetOptionsContent();
//	}*/
//}

//void UOptionWidget::OnUpdateOptionsHandler(const FString & UUID, const TArray<FDecorateContent>& OutDecoStyles)
//{
//}

//void UOptionWidget::OnSwapOptionHandler(const FString& UUID, bool bSuccess)
//{
//	if (SwapUUID == UUID)
//	{
//		GetOptionsContent();
//		//if (bSuccess)
//		//{
//		//	UpdateOptions(OutUpdateDatas);
//		//	RefreshContent(OutUpdateDatas, SelectOptionID);
//		//}
//		//else
//		//{
//		//	UpdateOptions(OutDatas);
//		//	RefreshContent(OutDatas, SelectOptionID);
//		//}
//	}
//}

void UOptionWidget::RefreshContent(const TArray<FDecorateContent>& Items, const FString& OptionId)
{
	SBItem->ClearChildren();
	SubOptionItems.Empty();
	int32 SelectOrder = -1;
	for (auto& iter : Items)
	{
		UOptionWidgetChild* Child = UOptionWidgetChild::Create();
		Child->SetContent(iter);
		Child->SelectOptionDelegate.BindUFunction(this, FName(TEXT("OnOptionItemSelectHandler")));
		Child->SetVisibility(ESlateVisibility::Visible);
		SBItem->AddChild(Child);
		SubOptionItems.Add(iter.id, Child);
		if (iter.id == OptionId)
		{
			Child->SetSelectState(true);
			SelectOrder = iter.sort_order;
			OnOptionItemSelectHandler(SubOptionItems[OptionId]);

		}
	}
	UpdateStyleOperateState<UOptionWidgetChild>(SelectOrder, SubOptionItems);
}

void UOptionWidget::ConstructNewData(FDecorateContent& NewOptionData, bool IsInsert)
{
	NewOptionData.id = FGuid::NewGuid().ToString().ToLower();

#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 SelectIndex = INDEX_NONE;
	if (IsInsert)
	{
		FString CurID = SelectOptionID;
		SelectIndex = StyleRefData.content_datas.IndexOfByPredicate(
			[CurID](const FRefToContentData& InData)
			{
				return InData.content_id.Equals(CurID);
			}
		);
	}
	auto& TempData = SelectIndex == INDEX_NONE ? StyleRefData.content_datas.AddDefaulted_GetRef()
		: StyleRefData.content_datas.InsertDefaulted_GetRef(SelectIndex + 1);
	PARSE_CONTENT_ACTION_DATA_TO_REF_DATA(NewOptionData, TempData);
	StyleRefData.ReGenerateContentOrder();
	SelectOptionID = NewOptionData.id;
	GetOptionsContent();

#else
	if (SubOptionItems.Num() == 0)
	{
		NewOptionData.sort_order = 1;
		InsertNewOption(NewOptionData);
		return;
	}

	TArray<UOptionWidgetChild*> OptionItems;
	SubOptionItems.GenerateValueArray(OptionItems);
	if (IsInsert)
	{
		int32 index = OptionItems.IndexOfByKey<UOptionWidgetChild*>(SubOptionItems[SelectOptionID]);
		if (OptionItems.Last()->GetData().id.Equals(SubOptionItems[SelectOptionID]->GetData().id))
		{
			NewOptionData.sort_order = OptionItems.Last()->GetData().sort_order + 1;
		}
		else
		{
			for (int32 i = 0, neworder = 0; i < OptionItems.Num(); i++)
			{
				if (i == index + 1)
				{
					NewOptionData.sort_order = neworder;
					neworder++;
				}
				FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_content set SORT_ORDER = '%d' where ID = '%s'")
					, neworder, *OptionItems[i]->GetData().id));
				neworder++;
			}
		}
	}
	else
	{
		if (SubOptionItems.Num() == 0)
		{
			NewOptionData.sort_order = 1;
		}
		else
		{
			NewOptionData.sort_order = OptionItems.Last()->GetData().sort_order + 1;
		}
	}
	InsertNewOption(NewOptionData);
#endif
}

void UOptionWidget::OptionSubItemMoveAction(const FString& OptionID, bool IsUp)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	SWAPE_ITEM_ACTION(StyleRefData.content_datas, FRefToContentData, content_id, OptionID, IsUp);
	StyleRefData.ReGenerateContentOrder();
	
#else
	TArray<UOptionWidgetChild*> ContentItems;
	SubOptionItems.GenerateValueArray(ContentItems);
	int32 index = ContentItems.IndexOfByKey<UOptionWidgetChild*>(SubOptionItems[OptionID]);
	int32 preIndex = (IsUp ? -1 : 1) + index;

	if (preIndex > ContentItems.Num() || preIndex < 0)
		return;
	FString CurrentID = ContentItems[index]->GetData().id;
	int32 currentOrder = ContentItems[index]->GetData().sort_order;
	FString SwapID = ContentItems[preIndex]->GetData().id;
	int32 SwapOrder = ContentItems[preIndex]->GetData().sort_order;

	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_content set SORT_ORDER= '%d' where ID = '%s'"), SwapOrder, *CurrentID));
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_content set SORT_ORDER= '%d' where ID = '%s'"), currentOrder, *SwapID));
#endif

	GetOptionsContent();
	OnOptionItemSelectHandler(SubOptionItems[OptionID]);
}

void UOptionWidget::OnOptionItemSelectHandler(UOptionWidgetChild* InOption)
{
	if (!SelectOptionID.Equals(TEXT("-1")))
	{
		SubOptionItems[SelectOptionID]->SetSelectState(false);
		SubOptionItems[SelectOptionID]->ResetNameReadOnly();
	}
	if (IS_OBJECT_PTR_VALID(InOption))
	{
		InOption->SetSelectState(true);
		UpdateStyleOperateState<UOptionWidgetChild>(InOption->GetData().sort_order, SubOptionItems);
		if (InOption->GetData().id != SelectOptionID)
		{
			OptionSelectDelegate.ExecuteIfBound(InOption->GetData().id);
			SelectOptionID = InOption->GetData().id;
		}
	}
}

void UOptionWidget::OnClickedBtnAdd()
{
	StyleWidgetOperate(EWidgetOperateType::Add);
}

void UOptionWidget::OnClickedBtnDelete()
{
	StyleWidgetOperate(EWidgetOperateType::Del);
}

void UOptionWidget::OnClickedBtnUp()
{
	StyleWidgetOperate(EWidgetOperateType::Up);
}

void UOptionWidget::OnClickedBtnDown()
{
	StyleWidgetOperate(EWidgetOperateType::Down);
}

void UOptionWidget::ResetScroll()
{
	SBItem->ScrollToStart();
}