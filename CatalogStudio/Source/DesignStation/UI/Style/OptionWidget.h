// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleWidget.h"
#include "StyleBaseWidget.h"
#include "OptionWidgetChild.h"
#include "OptionWidget.generated.h"

/**
 * 
 */

DECLARE_DYNAMIC_DELEGATE(FOptionDelDelegate);

UCLASS()
class DESIGNSTATION_API UOptionWidget : public UStyleBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void ResetOperateState() override;
	virtual void ResetSelectState() override;
	virtual void UpdateOperateState(const ESubItemOrderType& InType) override;
	virtual void StyleWidgetOperate(const EWidgetOperateType& InType) override;

	void GetOptionsContent();
	void UpdateContent(const TArray<FDecorateContent>& Items);
	void SetBtnAddEnable(bool IsEnable);
	FORCEINLINE FString GetSelectOptionID() { return SelectOptionID; }

	static UOptionWidget* Create();

public:
	void InsertNewOption(const FDecorateContent& NewOption);
	void UpdateOptions(const TArray<FDecorateContent>& Options);
	void ResetScroll();
private:
	void BindDelegates();
	//UFUNCTION()
	//	void OnGetOptionsHandler(const FString& UUID, const TArray<FDecorateContent>& OutDecoStyles);
	//UFUNCTION()
	//	void OnCreateOptionHandler(const FString& UUID, const FDecorateContent& OutDecoStyle);
	/*UFUNCTION()
		void OnUpdateOptionsHandler(const FString& UUID, const TArray<FDecorateContent>& OutDecoStyles);*/
	//UFUNCTION()
	//	void OnSwapOptionHandler(const FString& UUID, bool bSuccess);

private:
	FString CreateUUID;
	FString SearchUUID;
	FString UpdateUUID;
	FString SwapUUID;
	int32 InsertIndex;
	UPROPERTY()
		TArray<FDecorateContent> OutUpdateDatas;
	UPROPERTY()
		TArray<FDecorateContent> OutDatas;

private:
	//void UpdateStyleOperateState(const int32& CurrentOrder);
	void RefreshContent(const TArray<FDecorateContent>& Items, const FString& OptionId);
	void ConstructNewData(FDecorateContent& NewOptionData, bool IsInsert);
	void OptionSubItemMoveAction(const FString& OptionID, bool IsUp);
	UFUNCTION()
		void OnOptionItemSelectHandler(UOptionWidgetChild* InOption);

public:
	FStyleItemSelectDelegate OptionSelectDelegate;
	FOptionDelDelegate OptionDelDelegate;

private:
	UPROPERTY()
		TMap<FString, UOptionWidgetChild*> SubOptionItems;

	FString SelectOptionID;
	FString OldSelectOptionID;


	static FString OptionWidgetPath;

protected:
	UFUNCTION()
		void OnClickedBtnAdd();
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();

public:
	FStyleOpTypeDelegate OptionOpTypeDelegate;
	
	
};
