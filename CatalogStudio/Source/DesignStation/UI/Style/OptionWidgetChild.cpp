// Fill out your copyright notice in the Description page of Project Settings.

#include "OptionWidgetChild.h"

#include "Components/Border.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Operation/OperatorWidgetConfig.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"

#define LOCTEXT_NAMESPACE "Style-OptionData"

FString UOptionWidgetChild::OptionWidgetChildPath = TEXT("WidgetBlueprint'/Game/UI/Style/OptionWidgetChild.OptionWidgetChild_C'");

bool UOptionWidgetChild::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	
	return true;
}

void UOptionWidgetChild::NativeOnInitialized()
{
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(TxtNameRead, Txt_NameRead);
	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_PARAM_CPP_TO_UMG(BorBody, Bor_Body);

	BIND_SLATE_WIDGET_FUNCTION(BorBody, OnMouseButtonDownEvent, FName(TEXT("OnBorBodyClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorBody, OnMouseDoubleClickEvent, FName(TEXT("OnBorNameDoubleClicked")));

	BIND_WIDGET_FUNCTION(TxtName, OnTextCommitted, UOptionWidgetChild::OnTextCommittedName);
	BIND_WIDGET_FUNCTION(TxtName, OnTextChanged, UOptionWidgetChild::OnTextChangedName);

	BorCode->OnMouseButtonDownEvent.BindUFunction(this, FName(TEXT("OnBorBodyClicked_Code")));
	//BorCode->OnMouseDoubleClickEvent.BindUFunction(this, FName(TEXT("OnBorNameDoubleClicked_Code")));

	//TxtCode->OnTextCommitted.AddUniqueDynamic(this, &UOptionWidgetChild::OnTextCommittedCode);
	//TxtCode->OnTextChanged.AddUniqueDynamic(this, &UOptionWidgetChild::OnTextChangedCode);

	BorType->OnMouseButtonDownEvent.BindUFunction(this, FName(TEXT("OnBorBodyClicked_Type")));
	//BorType->OnMouseDoubleClickEvent.BindUFunction(this, FName(TEXT("OnBorNameDoubleClicked_Type")));

	BtnSelectCode->OnClicked.AddUniqueDynamic(this, &UOptionWidgetChild::OnClickSelectTypeHandler);

	CBSType->OnSelectionChanged.AddUniqueDynamic(this, &UOptionWidgetChild::OnSelectTypeChangedHandler);

	BindDelegates();

	InitType();
}

UOptionWidgetChild * UOptionWidgetChild::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UOptionWidgetChild>(UOptionWidgetChild::OptionWidgetChildPath);
}

void UOptionWidgetChild::SetContent(const FDecorateContent & Option)
{
	//
	TxtName->SetText(FText::FromString(Option.name));
	TxtNameRead->SetText(TxtName->GetText());
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);

	//code
    TxtCode->SetText(FText::FromString(Option.RelationCode));
	//TxtCodeRead->SetText(TxtCode->GetText());
    //TxtCode->SetVisibility(ESlateVisibility::Collapsed);
    //TxtCodeRead->SetVisibility(ESlateVisibility::Visible);

	//type
#if WITH_EDITOR
	FString CurDisplayName = UEnum::GetDisplayValueAsText(Option.BaseType).ToString();
#else 
	int32 Index = static_cast<int32>(Option.BaseType);
	FString CurDisplayName = ContentBaseTypeLocText[Index].ToString();
#endif
    CBSType->SetSelectedOption(CurDisplayName);

	DecorateOption = Option;
}

void UOptionWidgetChild::SelectSelf()
{
	SelectOptionDelegate.ExecuteIfBound(this);
}

void UOptionWidgetChild::DeleteCurrentOption()
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InContent) {return InContent.content_id.Equals(DecorateOption.id); }
	);
	if(Index != INDEX_NONE)
	{
		StyleRefData.content_datas.RemoveAt(Index);
	}

#else
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_selch WHERE CONTENT_ID = '%s'"), *DecorateOption.id));
	TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_sel WHERE PARENT_ID = '%s'"), *DecorateOption.id), OutSelection);
	for (auto& SelectionIter : OutSelection)
	{
		TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *SelectionIter.id), OutParamsTableData);
		for (auto& ParamIter : OutParamsTableData)
		{
			FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *ParamIter.id));
		}
		FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param WHERE MAIN_ID = '%s'"), *SelectionIter.id));
	}
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_sel WHERE PARENT_ID = '%s'"), *DecorateOption.id));
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_content WHERE id = '%s'"), *DecorateOption.id));
#endif
	this->SetVisibility(ESlateVisibility::Collapsed);
	this->RemoveFromParent();
	
}

void UOptionWidgetChild::UpdateCurrentOption(const FDecorateContent & NewDecoOption)
{
#ifdef USE_REF_LOCAL_FILE
	GET_STYLE_REF_FILE_DATA_REF();
	int32 EditIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[NewDecoOption](const FRefToContentData& InData)->bool { return InData.content_id.Equals(NewDecoOption.id); }
	);
	if (EditIndex != INDEX_NONE)
	{
		PARSE_CONTENT_ACTION_DATA_TO_REF_DATA(NewDecoOption, StyleRefData.content_datas[EditIndex]);
	}
#else
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_content set NAME = '%s' , SORT_ORDER= '%d'  where ID = '%s'"), *NewDecoOption.name, NewDecoOption.sort_order, *NewDecoOption.id));
#endif

	SetContent(NewDecoOption);
	
}

void UOptionWidgetChild::BindDelegates()
{
	
}

void UOptionWidgetChild::NativeOnMouseEnter(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(true, static_cast<int32>(EColorType::Hover));
		SetTextColor(true);
	}
}

void UOptionWidgetChild::NativeOnMouseLeave(const FPointerEvent & InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(false, static_cast<int32>(EColorType::Hover));
		SetTextColor(false);
	}
}


FEventReply UOptionWidgetChild::OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	SelectOptionDelegate.ExecuteIfBound(this);
	return FEventReply();
}

void UOptionWidgetChild::SetBorderColor(bool _IsSelect, const int32 & Type)
{
	if (IS_OBJECT_PTR_VALID(BorBody))
	{

		switch (Type)
		{
		case static_cast<int32>(EColorType::Select):
		{
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorBody);
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorType);
			break;
		}
		case static_cast<int32>(EColorType::Hover):
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorBody);
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(_IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorType);
			break;
		default:
			break;
		}
	}
}

void UOptionWidgetChild::SetTextColor(bool _IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtNameRead))
	{
		UUIFunctionLibrary::SetTextColor(_IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtNameRead);
		UUIFunctionLibrary::SetEditableTextColor(_IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtCode);
	}
}

void UOptionWidgetChild::ResetNameReadOnly()
{
	if (IS_OBJECT_PTR_VALID(TxtName) && IS_OBJECT_PTR_VALID(TxtNameRead))
	{
		TxtName->SetIsReadOnly(true);
		TxtName->SetVisibility(ESlateVisibility::Collapsed);
		TxtNameRead->SetVisibility(ESlateVisibility::Visible);
	}
	
}

void UOptionWidgetChild::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetTextColor(_IsSelect);
	SetBorderColor(_IsSelect, static_cast<int32>(EColorType::Select));
}

FEventReply UOptionWidgetChild::OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
{
	TxtName->SetIsReadOnly(false);
	TxtName->SetVisibility(ESlateVisibility::Visible);
	TxtNameRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtName->SetUserFocus(PC);

	return FEventReply();
}

void UOptionWidgetChild::OnTextCommittedName(const FText & Text, ETextCommit::Type CommitMethod)
{
	if (Text.ToString() == DecorateOption.name)
	{
		return;
	}

	if (!NameUnique)
	{
		TxtName->SetText(FText::FromString(DecorateOption.name));
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
			TEXT("Error"), FText::FromStringTable(FName("PosSt"), TEXT("Name should be unique, please change")).ToString());
		return;
	}

	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len()<=50)
	{
		TxtName->SetText(Text);
		DecorateOption.name = Text.ToString();
		UpdateCurrentOption(DecorateOption);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtName->SetText(FText::FromString(DecorateOption.name));
	}
	TxtName->SetIsReadOnly(true);
	TxtNameRead->SetText(TxtName->GetText());
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);
}

void UOptionWidgetChild::OnTextChangedName(const FText & Text)
{
	if (Text.ToString() == DecorateOption.name || Text.ToString().IsEmpty())
	{
		NameUnique = true;
		return;
	}

#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	NameUnique = !StyleRefData.IsContentNameExist(Text.ToString());

#else
	TArray<FDecorateContent> OutContent = TArray<FDecorateContent>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_content where NAME = '%s' "), *Text.ToString()), OutContent);
	NameUnique = OutContent.Num() <= 0;
#endif
}

FEventReply UOptionWidgetChild::OnBorBodyClicked_Code(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectOptionDelegate.ExecuteIfBound(this);
	return FEventReply(true);
}

FEventReply UOptionWidgetChild::OnBorNameDoubleClicked_Code(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	TxtCode->SetIsReadOnly(false);
	TxtCode->SetVisibility(ESlateVisibility::Visible);
	TxtCodeRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtCode->SetUserFocus(PC);

	return FEventReply(true);
}

void UOptionWidgetChild::OnTextCommittedCode(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.ToString().Equals(DecorateOption.RelationCode))
	{
		return;
	}

	if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtCode->SetText(Text);
		DecorateOption.RelationCode = Text.ToString();
		UpdateCurrentOption(DecorateOption);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtCode->SetText(FText::FromString(DecorateOption.RelationCode));
	}
	TxtCode->SetIsReadOnly(true);
	TxtCodeRead->SetText(TxtCode->GetText());
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);
	TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
}

void UOptionWidgetChild::OnTextChangedCode(const FText& Text)
{
}

void UOptionWidgetChild::OnClickSelectTypeHandler()
{
	FParameterData NewData;
	UParameterDetailWidget::Get()->SetFolderOrFileParentParams(ACatalogPlayerController::Get()->GetGlobalParameterMap());
	UParameterDetailWidget::Get()->UpdateContent(NewData, 2);
	UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnSelectParamCodeHandler")));
	UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
	UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
}

void UOptionWidgetChild::OnSelectParamCodeHandler(const FParameterData& ParamData)
{
	if (!ParamData.Data.name.Equals(DecorateOption.RelationCode,ESearchCase::CaseSensitive))
	{
        DecorateOption.RelationCode = ParamData.Data.name;
        UpdateCurrentOption(DecorateOption);
	}
}

void UOptionWidgetChild::InitType()
{
	if (CBSType)
	{
		CBSType->ClearOptions();
		for (int32 i = 0; i < StaticEnum<EDecorateContentBaseType>()->NumEnums() - 1; i++)
		{
			FString DisplayName = TEXT("");
#if WITH_EDITOR
			DisplayName = StaticEnum<EDecorateContentBaseType>()->GetDisplayNameTextByIndex(i).ToString();
#else
			if (ContentBaseTypeLocText.IsValidIndex(i))
			{
				DisplayName = ContentBaseTypeLocText[i].ToString();
			}
#endif
			CBSType->AddOption(DisplayName);
		}
	}
}

FEventReply UOptionWidgetChild::OnBorBodyClicked_Type(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectOptionDelegate.ExecuteIfBound(this);
	return FEventReply(true);
}

FEventReply UOptionWidgetChild::OnBorNameDoubleClicked_Type(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	return FEventReply(true);
}

void UOptionWidgetChild::OnSelectTypeChangedHandler(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::OnMouseClick)
	{
		for (int32 i = 0; i < StaticEnum<EDecorateContentBaseType>()->NumEnums() - 1; i++)
		{
#if WITH_EDITOR
			FString DisplayName = StaticEnum<EDecorateContentBaseType>()->GetDisplayNameTextByIndex(i).ToString();
			if (DisplayName.Equals(SelectedItem) && i != static_cast<int32>(DecorateOption.BaseType))
			{
				CBSType->SetSelectedIndex(i);
				DecorateOption.BaseType = static_cast<EDecorateContentBaseType>(i);
				UpdateCurrentOption(DecorateOption);
				return;
			}
#else
			if (ContentBaseTypeLocText.IsValidIndex(i))
			{
				if (ContentBaseTypeLocText[i].ToString().Equals(SelectedItem) && i != static_cast<int32>(DecorateOption.BaseType))
				{
					CBSType->SetSelectedIndex(i);
					DecorateOption.BaseType = static_cast<EDecorateContentBaseType>(i);
					UpdateCurrentOption(DecorateOption);
					return;
				}
			}
#endif
			
		}

		//reset
		CBSType->SetSelectedIndex(static_cast<int32>(DecorateOption.BaseType));
	}
}

#undef LOCTEXT_NAMESPACE
