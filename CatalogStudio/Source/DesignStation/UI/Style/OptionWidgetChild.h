// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleWidgetChild.h"
#include "DesignStation/SQLite/DecorateStyleRelated/DecorateContentOperatorLibrary.h"
#include "OptionWidgetChild.generated.h"

/**
 *
 */

class UTextBlock;
class UBorder;
class UEditableText;
class UComboBoxString;
class UButton;

struct FParameterData;

DECLARE_DYNAMIC_DELEGATE_OneParam(FOptionSelectDelegate, UOptionWidgetChild*, OptionItem);

UCLASS()
class DESIGNSTATION_API UOptionWidgetChild : public UUserWidget
{
	GENERATED_BODY()


public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	static UOptionWidgetChild* Create();
	void SetContent(const FDecorateContent& Selection);
	void SelectSelf();
	FDecorateContent GetData() { return DecorateOption; }

public:
	void DeleteCurrentOption();
	void UpdateCurrentOption(const FDecorateContent& NewDecoOption);

private:
	void BindDelegates();
private:
	FString UpdateUUID;
	FString DeleteUUID;
	FString UniqueUUID;
	bool NameUnique;

private:
	static FString OptionWidgetChildPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	//virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	UFUNCTION()
	FEventReply OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);


	UFUNCTION()
	FEventReply OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	void OnTextCommittedName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
	void OnTextChangedName(const FText& Text);


	//code 
	UFUNCTION()
	FEventReply OnBorBodyClicked_Code(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	FEventReply OnBorNameDoubleClicked_Code(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	void OnTextCommittedCode(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
	void OnTextChangedCode(const FText& Text);

	UFUNCTION()
	void OnClickSelectTypeHandler();

	UFUNCTION()
	void OnSelectParamCodeHandler(const FParameterData& ParamData);

	//type
	void InitType();

	UFUNCTION()
	FEventReply OnBorBodyClicked_Type(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	FEventReply OnBorNameDoubleClicked_Type(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	void OnSelectTypeChangedHandler(FString SelectedItem, ESelectInfo::Type SelectionType);

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);
	void ResetNameReadOnly();

private:

	//content
	UPROPERTY()
	UEditableText* TxtName;
	UPROPERTY()
	UBorder* BorName;
	UPROPERTY()
	UBorder* BorBody;
	UPROPERTY()
	UTextBlock* TxtNameRead;

	//code
	UPROPERTY(meta=(BindWidget))
	UBorder* BorCode;
    UPROPERTY(meta=(BindWidget))
	UEditableText* TxtCode;
    UPROPERTY(meta=(BindWidget))
    UTextBlock* TxtCodeRead;
    UPROPERTY(meta=(BindWidget))
	UButton* BtnSelectCode;

	//type
    UPROPERTY(meta=(BindWidget))
    UBorder* BorType;

    UPROPERTY(meta=(BindWidget))
    UComboBoxString* CBSType;

private:
	int32  Index;
	bool IsSelect;
	FDecorateContent DecorateOption;
public:
	FOptionSelectDelegate SelectOptionDelegate;
public:
	void SetSelectState(bool Select);

};
