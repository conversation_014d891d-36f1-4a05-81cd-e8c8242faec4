// Fill out your copyright notice in the Description page of Project Settings.

#include "ParamaterWidgetChild.h"

#include "Components/Border.h"
#include "Components/Button.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Operation/OperatorWidgetConfig.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/ComboBoxString.h"

FString UParamaterWidgetChild::ParaWidgetChildPath = TEXT("WidgetBlueprint'/Game/UI/Style/ParanaterWidgetChild.ParanaterWidgetChild_C'");

bool UParamaterWidgetChild::Initialize()
{
	if (!UUserWidget::Initialize())
	{
		return false;
	}

	
	return true;
}

void UParamaterWidgetChild::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(EdtName, Edt_Name);
	BIND_PARAM_CPP_TO_UMG(EdtExperssion, Edt_Experssion);
	BIND_PARAM_CPP_TO_UMG(EdtValue, Edt_Value);
	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_PARAM_CPP_TO_UMG(BorExperssion, Bor_Experssion);
	BIND_PARAM_CPP_TO_UMG(BorValue, Bor_Value);
	BIND_PARAM_CPP_TO_UMG(BtnExperssion, Btn_Experssion);
	BIND_PARAM_CPP_TO_UMG(BorBody, Bor_Body);
	BIND_PARAM_CPP_TO_UMG(TxtNameRead, Txt_NameRead);
	BIND_PARAM_CPP_TO_UMG(TxtExperssionRead, Txt_ExperssionRead);
	BIND_PARAM_CPP_TO_UMG(TxtValueRead, Txt_ValueRead);
	BIND_PARAM_CPP_TO_UMG(BorEnum, Bor_Enum);
	BIND_PARAM_CPP_TO_UMG(CBSValues, CBS_Values);
	BIND_PARAM_CPP_TO_UMG(BorValueSection, Bor_ValueSection);


	BIND_WIDGET_FUNCTION(EdtName, OnTextCommitted, UParamaterWidgetChild::OnTextCommittedEdtName);
	BIND_WIDGET_FUNCTION(EdtExperssion, OnTextCommitted, UParamaterWidgetChild::OnTextCommittedEdtExperssion);
	BIND_WIDGET_FUNCTION(EdtValue, OnTextCommitted, UParamaterWidgetChild::OnTextCommittedEdtValue);
	BIND_WIDGET_FUNCTION(BtnExperssion, OnClicked, UParamaterWidgetChild::OnClickedBtnExperssion);
	BIND_SLATE_WIDGET_FUNCTION(BorBody, OnMouseButtonDownEvent, FName(TEXT("OnBorBodyClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorExperssion, OnMouseDoubleClickEvent, FName(TEXT("OnBorExperssionDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorValue, OnMouseDoubleClickEvent, FName(TEXT("OnBorValueDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseDoubleClickEvent, FName(TEXT("OnBorNameDoubleClicked")));
	BIND_WIDGET_FUNCTION(CBSValues, OnSelectionChanged, UParamaterWidgetChild::OnSelectionChangedCBSValues);

}

void UParamaterWidgetChild::UpdateContent(const FParameterData& InData)
{
	ParamData = InData;

	ParameterDes = FText::FromString(FParameterTableData::GetCleanDataWithoutAdditionMsg(InData.Data.description));
	TB_ParameterDes->SetText(ParameterDes);
	TB_ParameterDes->SetToolTipText(ParameterDes);

	TxtNameRead->SetText(FText::FromString(InData.Data.name));
	TxtNameRead->SetToolTipText(FText::FromString(InData.Data.name));

	FString Express = /*InData.Data.expression.Len() > 30 ? InData.Data.expression.Left(30) : */InData.Data.expression;
	EdtExperssion->SetText(FText::FromString(InData.Data.expression));
	TxtExperssionRead->SetText(FText::FromString(Express));
	TxtExperssionRead->SetToolTipText(FText::FromString(Express));

	CBSValues->ClearOptions();
	if (ParamData.Data.is_enum && IS_OBJECT_PTR_VALID(CBSValues) && (ParamData.EnumData.Num() > 0))
	{
		BorValue->SetVisibility(ESlateVisibility::Collapsed);
		BorEnum->SetVisibility(ESlateVisibility::Visible);

		FString SelectOption;
		for (auto& Data : InData.EnumData)
		{
			if (!Data.IsVisiable())
			{
				continue;
			}

			const FString EnumDisplayName = Data.name_for_display.IsEmpty() ? Data.value : Data.name_for_display;
			CBSValues->AddOption(EnumDisplayName);
			if (SelectOption.IsEmpty() && FCString::Atof(*InData.Data.value) == FCString::Atof(*Data.value))
			{
				SelectOption = EnumDisplayName;
			}
		}
		CBSValues->RefreshOptions();
		if (!SelectOption.IsEmpty())
		{
			CBSValues->SetSelectedOption(SelectOption);
		}
		else
		{
			CBSValues->SetSelectedOption(CBSValues->GetOptionAtIndex(0));
		}
	}
	else
	{
		BorEnum->SetVisibility(ESlateVisibility::Collapsed);
		BorValue->SetVisibility(ESlateVisibility::Visible);
		if (InData.Data.value.IsNumeric())
		{
			float TempFloat = FCString::Atof(*InData.Data.value);
			FString TempValue = FString::SanitizeFloat(TempFloat);
			EdtValue->SetText(FText::FromString(TempValue));
			TxtValueRead->SetText(EdtValue->GetText());
			TxtValueRead->SetToolTipText(EdtValue->GetText());
		}
		else
		{
			EdtValue->SetText(FText::FromString(InData.Data.value));
			TxtValueRead->SetText(EdtValue->GetText());
			TxtValueRead->SetToolTipText(EdtValue->GetText());
		}
	}
}

void UParamaterWidgetChild::SelectSelf()
{
	ValueParamDelegate.ExecuteIfBound(this);
}

UParamaterWidgetChild* UParamaterWidgetChild::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UParamaterWidgetChild>(UParamaterWidgetChild::ParaWidgetChildPath);
}

void UParamaterWidgetChild::UpdateCurrentParam(const FParameterData& InData)
{
#ifdef USE_REF_LOCAL_FILE

#else
	FLocalDatabaseParameterLibrary::UpdateStyleParameter(InData);
#endif
	UpdateContent(InData);
}

void UParamaterWidgetChild::DeleteCurrentParam()
{
#ifdef USE_REF_LOCAL_FILE

#else
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *ParamData.Data.id));
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param WHERE ID = '%s'"), *ParamData.Data.id));
#endif
	this->SetVisibility(ESlateVisibility::Collapsed);
	this->RemoveFromParent();
}

void UParamaterWidgetChild::OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len() <= 50)
	{
		EdtName->SetText(Text);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		//TxtName->SetText(FText::FromString(DecorateValue.description));
	}
}

void UParamaterWidgetChild::OnTextCommittedEdtExperssion(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		FParameterData NewParameter = ParamData;
		NewParameter.Data.expression = Text.ToString();
		ParameterChanged.ExecuteIfBound(this, NewParameter);
		return;
	}
	EdtExperssion->SetVisibility(ESlateVisibility::Collapsed);
	FString Express = /*ParamData.Data.expression.Len() > 30 ? ParamData.Data.expression.Left(30) : */ParamData.Data.expression;
	TxtExperssionRead->SetText(FText::FromString(Express));
	TxtExperssionRead->SetToolTipText(FText::FromString(Express));
	TxtExperssionRead->SetVisibility(ESlateVisibility::Visible);
}

void UParamaterWidgetChild::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared && Text.IsNumeric())
	{
		FString OutValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(OutValue);
		FParameterData NewParameter = ParamData;
		NewParameter.Data.expression = NewParameter.Data.value = OutValue;
		ParameterChanged.ExecuteIfBound(this, NewParameter);
		return;
	}
	else
	{
		EdtValue->SetText(FText::FromString(ParamData.Data.value));
		EdtExperssion->SetText(FText::FromString(ParamData.Data.expression));
	}
	EdtValue->SetVisibility(ESlateVisibility::Collapsed);
	TxtValueRead->SetText(EdtValue->GetText());
	TxtValueRead->SetToolTipText(EdtValue->GetText());
	TxtValueRead->SetVisibility(ESlateVisibility::Visible);
}

void UParamaterWidgetChild::OnClickedBtnExperssion()
{
	if (IS_OBJECT_PTR_VALID(EdtExperssion))
	{
		BIND_EXPRESSION_WIDGET(1, ParamData.Data.expression, FName(TEXT("OnParamaterExpressionEditHandler")));
	}
}
void UParamaterWidgetChild::OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		SelectedItem = ParamData.EnumData[CBSValues->GetSelectedIndex()].value;//枚举选项的值
		float SelectedValue = FCString::Atof(*SelectedItem);
		float MaxValue = ParamData.Data.max_value.IsEmpty() ? 99999.0f : FCString::Atof(*ParamData.Data.max_value);
		float MinValue = ParamData.Data.min_value.IsEmpty() ? -99999.0f : FCString::Atof(*ParamData.Data.min_value);
		TArray<float> EnumArray = TArray<float>();
		bool IsMaxNMinVaild = ParamData.IsMaxNMinValueVaild(EnumArray);
		bool IsEnumValueExist = false;
		int32 EnumIndex = 0;
		if (IsMaxNMinVaild)
		{
			if (SelectedValue < MinValue || SelectedValue > MaxValue)
			{
				CBSValues->SetSelectedIndex(ParamData.EnumData.IndexOfByPredicate([&](const FEnumParameterTableData& InOther)->bool { return FMath::IsNearlyEqual(FCString::Atof(*InOther.value), FCString::Atof(*ParamData.Data.value)); }));
				return;
			}
			if (EnumArray.Num() > 0)
			{
				for (int32 i = 0; i < EnumArray.Num(); i++)
				{
					if (FMath::IsNearlyEqual(EnumArray[i], SelectedValue))
					{
						IsEnumValueExist = true;
						EnumIndex = i;
						break;
					}
				}
				if (IsEnumValueExist)
				{
					FString RightOption = FString::Printf(TEXT("%.1f"), EnumArray[EnumIndex]);
					UE_LOG(LogTemp, Log, TEXT("RightOption param is %s @ index in %d"), *RightOption, EnumIndex);
					CBSValues->SetSelectedIndex(ParamData.EnumData.IndexOfByPredicate([&](const FEnumParameterTableData& InOther)->bool { return InOther.value.Equals(RightOption); }));
					ParamData.Data.value = RightOption;
					ParamData.Data.expression = RightOption;

				}
				else
				{
					FString RightOption = FString::Printf(TEXT("%.1f"), EnumArray[0]);
					CBSValues->SetSelectedIndex(ParamData.EnumData.IndexOfByPredicate([&](const FEnumParameterTableData& InOther)->bool { return InOther.value.Equals(RightOption); }));
					ParamData.Data.value = RightOption;
					ParamData.Data.expression = RightOption;

				}
			}
			else
			{
				FString RightOption = ParamData.EnumData[0].value;
				CBSValues->SetSelectedIndex(ParamData.EnumData.IndexOfByPredicate([&](const FEnumParameterTableData& InOther)->bool { return InOther.value.Equals(RightOption); }));
				ParamData.Data.value = RightOption;
				ParamData.Data.expression = RightOption;

			}
			ParameterChanged.ExecuteIfBound(this, ParamData);
			return;
			/*for (auto iter : EnumArray)
			{
				if (iter == SelectedValue)
				{
					ParamData.Data.value = SelectedItem;
					ParamData.Data.expression = ParamData.Data.value;
					ParameterChanged.ExecuteIfBound(this, ParamData);
					return;
				}
			}
			CBSValues->SetSelectedIndex(ParamData.EnumData.IndexOfByPredicate([&](const FEnumParameterTableData& InOther)->bool { return InOther.value.Equals(ParamData.Data.value); }));
			return;*/
		}
		else
		{
			ParamData.Data.value = SelectedItem;
			ParamData.Data.expression = ParamData.Data.value;
			ParameterChanged.ExecuteIfBound(this, ParamData);
			return;
		}
	}
}
//
//FEventReply UParamaterWidgetChild::OnMouseClickedToSelectParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
//{
//	return FEventReply();
//}
//
//FEventReply UParamaterWidgetChild::OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
//{
//	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
//	{
//		UParameterDetailWidget::Get()->SetFolderOrFileParentParams(GetOverrideParametersFuc(ParamData));
//		UParameterDetailWidget::Get()->UpdateContent(ParamData, 1);
//		UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamDetailUpdateHandler")));
//		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
//		UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
//		UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
//		return FEventReply(true);
//	}
//	return FEventReply();
//}
//
//FEventReply UParamaterWidgetChild::OnMouseDoubleClickToEditExpress(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
//{
//	return FEventReply();
//}
//

void UParamaterWidgetChild::OnParamaterExpressionEditHandler(const int32& EditType, const FString& OutExpression)
{
	OnTextCommittedEdtExperssion(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
}
void UParamaterWidgetChild::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(true, static_cast<int32>(EColorType::Hover));
		SetTextColor(true);
	}
}

void UParamaterWidgetChild::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(false, static_cast<int32>(EColorType::Hover));
		SetTextColor(false);
	}
	//EdtName->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	//EdtExperssion->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
	//EdtValue->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
}

//FReply UParamaterWidgetChild::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
//{
//	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
//	{
//		SelectIndexDelegate.ExecuteIfBound(Index);
//		EdtName->SetVisibility(ESlateVisibility::Visible);
//		EdtExperssion->SetVisibility(ESlateVisibility::Visible);
//		EdtValue->SetVisibility(ESlateVisibility::Visible);
//		return FReply::Handled();
//	}
//	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
//	{
//		SelectIndexDelegate.ExecuteIfBound(-1);
//		return FReply::Handled();
//	}
//	return FReply::Unhandled();
//}

void UParamaterWidgetChild::SetBorderColor(bool IsSelect, const int32& Type)
{
	if (IS_OBJECT_PTR_VALID(BorName)
		&& IS_OBJECT_PTR_VALID(Bor_ParameterDes)
		&& IS_OBJECT_PTR_VALID(BorExperssion)
		&& IS_OBJECT_PTR_VALID(BorValue)
		&& IS_OBJECT_PTR_VALID(BorEnum))
	{

		switch (Type)
		{
		case static_cast<int32>(EColorType::Select):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, Bor_ParameterDes);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorExperssion);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorEnum);
			break;
		case static_cast<int32>(EColorType::Hover):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, Bor_ParameterDes);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorExperssion);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorValue);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorEnum);
			break;
		default:
			break;
		}
	}
}

void UParamaterWidgetChild::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtNameRead) && IS_OBJECT_PTR_VALID(TxtExperssionRead) && IS_OBJECT_PTR_VALID(TxtValueRead))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtNameRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtExperssionRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtValueRead);
	}
}


FEventReply UParamaterWidgetChild::OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	ValueParamDelegate.ExecuteIfBound(this);
	return FEventReply();
}

FEventReply UParamaterWidgetChild::OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		ParameterPage.ExecuteIfBound(this, ParamData);
	}
	return FEventReply();
}

void UParamaterWidgetChild::OnParamDetailUpdateHandler(const FParameterData& InParamData)
{
	UpdateCurrentParam(InParamData);
	ParameterChanged.ExecuteIfBound(this, InParamData);
}
FEventReply UParamaterWidgetChild::OnBorExperssionDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	EdtExperssion->SetVisibility(ESlateVisibility::Visible);
	EdtExperssion->SetText(FText::FromString(ParamData.Data.expression));
	TxtExperssionRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	EdtExperssion->SetUserFocus(PC);
	return FEventReply();
}

FEventReply UParamaterWidgetChild::OnBorValueDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	EdtValue->SetVisibility(ESlateVisibility::Visible);
	TxtValueRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	EdtValue->SetUserFocus(PC);
	return FEventReply();
}

void UParamaterWidgetChild::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetTextColor(_IsSelect);
	SetBorderColor(_IsSelect, static_cast<int32>(EColorType::Select));
}
