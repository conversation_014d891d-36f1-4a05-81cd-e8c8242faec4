// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleWidgetChild.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "ParamaterWidgetChild.generated.h"

/**
 *
 */

class UEditableText;
class UButton;
class UTextBlock;
class UComboBoxString;

DECLARE_DYNAMIC_DELEGATE_OneParam(FValueParamDelegate, UParamaterWidgetChild*, ParamItem);

DECLARE_DELEGATE_TwoParams(FParameterChangedDelegate, UParamaterWidgetChild*, const FParameterData&);

DECLARE_DELEGATE_TwoParams(FParameterPageDelegate, UParamaterWidgetChild*, const FParameterData&);

UCLASS()
class DESIGNSTATION_API UParamaterWidgetChild : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	void UpdateContent(const FParameterData& InData);
	void SelectSelf();
	void SetSelectState(bool IsSelect);
	FORCEINLINE FParameterData GetData() { return ParamData; }
	static UParamaterWidgetChild* Create();

public:
	void UpdateCurrentParam(const FParameterData& InData);
	void DeleteCurrentParam();

public:
	FValueParamDelegate ValueParamDelegate;

private:
	UPROPERTY()
		FParameterData ParamData;

	static FString ParaWidgetChildPath;

protected:
	UFUNCTION()
		void OnTextCommittedEdtName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtExperssion(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExperssion();
	UFUNCTION()
		void OnSelectionChangedCBSValues(FString SelectedItem, ESelectInfo::Type SelectionType);

	/*UFUNCTION()
		FEventReply OnMouseClickedToSelectParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnMouseDoubleClickToEditParam(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnMouseDoubleClickToEditExpress(FGeometry MyGeometry, const FPointerEvent& MouseEvent);*/

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	//virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	UFUNCTION()
		FEventReply OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorExperssionDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorValueDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnParamaterExpressionEditHandler(const int32& EditType, const FString& OutExpression);

	UFUNCTION()
		void OnParamDetailUpdateHandler(const FParameterData& InParamData);

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);
private:
	UPROPERTY()
		UEditableText* EdtName;
	UPROPERTY()
		UEditableText* EdtExperssion;
	UPROPERTY()
		UEditableText* EdtValue;
	UPROPERTY()
		UButton* BtnExperssion;
	UPROPERTY()
		UBorder* BorName;
	UPROPERTY(BlueprintReadWrite, Category = "ParamaterWidgetChild", meta = (AllowPrivateAccess = true, BindWidget = true))
		UBorder* Bor_ParameterDes;

	UPROPERTY(BlueprintReadWrite, Category = "ParamaterWidgetChild", meta = (AllowPrivateAccess = true, BindWidget = true))
		UTextBlock* TB_ParameterDes;

	UPROPERTY(BlueprintReadOnly, Category = "ParamaterWidgetChild", meta = (AllowPrivateAccess = true))
		FText	ParameterDes;
	UPROPERTY()
		UBorder* BorExperssion;
	UPROPERTY()
		UBorder* BorValue;
	UPROPERTY()
		UBorder* BorBody;
	UPROPERTY()
		UTextBlock* TxtNameRead;
	UPROPERTY()
		UTextBlock* TxtExperssionRead;
	UPROPERTY()
		UTextBlock* TxtValueRead;
	UPROPERTY()
		UBorder* BorValueSection;
	UPROPERTY()
		UBorder* BorEnum;
	UPROPERTY()
		UComboBoxString* CBSValues;
private:
	int32  Index;
	bool IsSelect;
	FParameterData GParamData;
public:
	FStyleOpTypeDelegate SelectIndexDelegate;
	FParameterChangedDelegate ParameterChanged;
	FParameterPageDelegate ParameterPage;
};
