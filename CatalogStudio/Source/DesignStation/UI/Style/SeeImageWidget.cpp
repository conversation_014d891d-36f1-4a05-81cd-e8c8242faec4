// Fill out your copyright notice in the Description page of Project Settings.

#include "SeeImageWidget.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString USeeImageWidget::SeeImageWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/SeeImageWidget.SeeImageWidget_C'");

bool USeeImageWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnClose, Btn_Close);
	BIND_PARAM_CPP_TO_UMG(ImgStyle, Img_Style);

	BIND_WIDGET_FUNCTION(BtnClose, OnClicked, USeeImageWidget::OnClickedBtnClose);

	return true;
}

USeeImageWidget* USeeImageWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<USeeImageWidget>(USeeImageWidget::SeeImageWidgetPath);
}

void USeeImageWidget::OnClickedBtnClose()
{
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void USeeImageWidget::SetImage(const FString& Path)
{

	UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ProjectContentDir() + Path);
	if (nullptr != ThumbnailTex)
	{
		ImgStyle->SetBrushFromTexture(ThumbnailTex);
	}
	else
	{
		FString DefaultPath = FString::Printf(TEXT("Styles/Config/DefaultImage/default.png"));
		ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ProjectContentDir() + DefaultPath);
		ImgStyle->SetBrushFromTexture(ThumbnailTex);
	}
}
