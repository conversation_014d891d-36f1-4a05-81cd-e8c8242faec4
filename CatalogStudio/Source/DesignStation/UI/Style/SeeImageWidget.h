// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SeeImageWidget.generated.h"

/**
 * 
 */

class UButton;
class UImage;

UCLASS()
class DESIGNSTATION_API USeeImageWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	static USeeImageWidget* Create();

private:
	static FString SeeImageWidgetPath;
	
protected:
	UFUNCTION()
		void OnClickedBtnClose();

private:
	UPROPERTY()
		UButton* BtnClose;
	UPROPERTY()
		UImage* ImgStyle;

public:
	UFUNCTION()
		void SetImage(const FString& Path);

};
