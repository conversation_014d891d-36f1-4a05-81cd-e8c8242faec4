// Fill out your copyright notice in the Description page of Project Settings.

#include "StyleBaseWidget.h"

#include "DesignStation/DesignStation.h"

bool UStyleBaseWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;	
}

void UStyleBaseWidget::ResetOperateState()
{
	if (IS_OBJECT_PTR_VALID(BtnDelete) && IS_OBJECT_PTR_VALID(BtnUp) && IS_OBJECT_PTR_VALID(BtnDown))
	{
		BtnDelete->SetIsEnabled(false);
		BtnUp->SetIsEnabled(false);
		BtnDown->SetIsEnabled(false);
	}
}

void UStyleBaseWidget::ResetSelectState()
{
}

void UStyleBaseWidget::UpdateOperateState(const ESubItemOrderType & InType)
{
	BtnDelete->SetIsEnabled(InType != ESubItemOrderType::NoneSelect);
	if (InType == ESubItemOrderType::DownEnable)
	{
		BtnUp->SetIsEnabled(false);
		BtnDown->SetIsEnabled(true);
	}
	else if (InType == ESubItemOrderType::BothEnable)
	{
		BtnUp->SetIsEnabled(true);
		BtnDown->SetIsEnabled(true);
	}
	else if (InType == ESubItemOrderType::UpEnable)
	{
		BtnUp->SetIsEnabled(true);
		BtnDown->SetIsEnabled(false);
	}
	else if (InType == ESubItemOrderType::NoneEnable || InType == ESubItemOrderType::NoneSelect)
	{
		BtnUp->SetIsEnabled(false);
		BtnDown->SetIsEnabled(false);
	}
}

void UStyleBaseWidget::StyleWidgetOperate(const EWidgetOperateType & InType)
{
}
