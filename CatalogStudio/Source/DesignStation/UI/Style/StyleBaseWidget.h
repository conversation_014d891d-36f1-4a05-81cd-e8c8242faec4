// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "StyleBaseWidget.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class ESubItemOrderType : uint8
{
	DownEnable = 0,
	BothEnable,
	UpEnable,
	NoneEnable,
	NoneSelect
};

UENUM(BlueprintType)
enum class EWidgetOperateType : uint8
{
	Add = 0,
	Del,
	Up,
	Down
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FStyleItemSelectDelegate, const FString&, StyleID);

UCLASS()
class DESIGNSTATION_API UStyleBaseWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void ResetOperateState();
	virtual void ResetSelectState();
	virtual void UpdateOperateState(const ESubItemOrderType& InType);
	virtual void StyleWidgetOperate(const EWidgetOperateType& InType);

	template<class T>
	void UpdateStyleOperateState(const int32& CurrentOrder, const TMap<FString, T*>& SubItemsMap)
	{
		if (SubItemsMap.Num() <= 1)
		{
			UpdateOperateState(ESubItemOrderType::NoneEnable);
		}
		else
		{
			TArray<int32> SubItemOrders;
			for (typename TMap<FString, T*>::TConstIterator Iter = SubItemsMap.CreateConstIterator(); Iter; ++Iter)
			{
				SubItemOrders.Add(Iter->Value->GetData().sort_order);
			}
			if (CurrentOrder == SubItemOrders[0])
			{
				UpdateOperateState(ESubItemOrderType::DownEnable);
			}
			else if (CurrentOrder == SubItemOrders.Last())
			{
				UpdateOperateState(ESubItemOrderType::UpEnable);
			}
			else
			{
				UpdateOperateState(ESubItemOrderType::BothEnable);
			}
			if (CurrentOrder == -1)
			{
				UpdateOperateState(ESubItemOrderType::NoneSelect);
			}
		}
	}

	template<class T1, class T2>
	void StyleItemMoveAction(TArray<T1>& OutDatas, TArray<T1>& OutUpdateDatas, const TMap<int32, T2*>& StyleItemsMap, const int32& ItemID, bool IsUp)
	{
		OutDatas.Empty();
		OutUpdateDatas.Empty();
		for (typename TMap<int32, T2*>::TConstIterator Iter = StyleItemsMap.CreateConstIterator(); Iter; ++Iter)
		{
			OutDatas.Add(Iter->Value->GetData());
		}

		T1 ActionStyleData = StyleItemsMap[ItemID]->GetData();
		int32 CurrentOrder = ActionStyleData.sort_order;
		int32 CurrentIndex;
		T1 ExChangeStyleData;
		if (OutDatas.Find(ActionStyleData, CurrentIndex))
		{
			int32 ExChangeIndex;
			if (IsUp)
			{
				ExChangeStyleData = OutDatas[CurrentIndex - 1];
				ExChangeIndex = CurrentIndex - 1;
			}
			else
			{
				ExChangeStyleData = OutDatas[CurrentIndex + 1];
				ExChangeIndex = CurrentIndex + 1;
			}
			UE_LOG(LogTemp, Log, TEXT("CurrentIndex : %d, ExChangeIndex : %d,ActionSort : %d, ExchangeSort : %d"), CurrentIndex, ExChangeIndex, CurrentOrder, ExChangeStyleData.sort_order);
			ActionStyleData.sort_order = ExChangeStyleData.sort_order;
			ExChangeStyleData.sort_order = CurrentOrder;
			OutUpdateDatas.Add(ActionStyleData);
			OutUpdateDatas.Add(ExChangeStyleData);
			OutDatas[CurrentIndex] = ExChangeStyleData;
			OutDatas[ExChangeIndex] = ActionStyleData;
		}
	}
	
	template<class T1, class T2>
	void InsertNewData(const TMap<int32, T1*>& StyleItemsMap, const int32& SelectID, T2& OutInsertData, TArray<T2>& OutUpdateDatas, int32& InsertIndex)
	{ 
		OutUpdateDatas.Empty();
		for (typename TMap<int32, T1*>::TConstIterator Iter = StyleItemsMap.CreateConstIterator(); Iter; ++Iter)
		{
			OutUpdateDatas.Add(Iter->Value->GetData());
		}
		T2 SelectData = StyleItemsMap[SelectID]->GetData();
		int32 CurrentIndex;
		if (OutUpdateDatas.Find(SelectData, CurrentIndex))
		{
			OutInsertData.sort_order = SelectData.sort_order + 1;
			int32 JudgeSort = OutInsertData.sort_order;
			for (int32 i = CurrentIndex + 1; i < OutUpdateDatas.Num(); ++i)
			{
				while (OutUpdateDatas[i].sort_order <= JudgeSort)
				{
					++OutUpdateDatas[i].sort_order;
				}
				JudgeSort = OutUpdateDatas[i].sort_order;
			}
			InsertIndex = ++CurrentIndex;
		}
	}

public:
	UPROPERTY()
		UButton* BtnAdd;
	UPROPERTY()
		UButton* BtnDelete;
	UPROPERTY()
		UButton* BtnUp;
	UPROPERTY()
		UButton* BtnDown;
	UPROPERTY()
		UScrollBox* SBItem;
};
