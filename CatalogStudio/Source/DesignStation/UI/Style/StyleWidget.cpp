// Fill out your copyright notice in the Description page of Project Settings.

#include "StyleWidget.h"

#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/CanvasPanel.h"
#include "Runtime/UMG/Public/Components/MenuAnchor.h"

FString UStyleWidget::StyleWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/StyleWidget.StyleWidget_C'");

bool UStyleWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnAdd, Btn_Add);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_PARAM_CPP_TO_UMG(SBItem, SB_Item);
	BIND_PARAM_CPP_TO_UMG(MAMenu, MA_Menu);
	BIND_PARAM_CPP_TO_UMG(CPMenu, CP_Menu);

	BIND_WIDGET_FUNCTION(BtnAdd, OnClicked, UStyleWidget::OnClickedBtnAdd);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UStyleWidget::OnClickedBtnDelete);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UStyleWidget::OnClickedBtnUp);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UStyleWidget::OnClickedBtnDown);
	BIND_SLATE_WIDGET_FUNCTION(MAMenu, OnGetUserMenuContentEvent, FName(TEXT("CreateRightMenu")));

	if (!ImageMenuWidget)
	{
		ImageMenuWidget = UImageMenuWidget::Create();
		ImageMenuWidget->ImageMenuDelegate.BindUFunction(this, FName(TEXT("GetMenuOption")));
	}

	SelectStyleID = TEXT("-1");

	BindDelegate();

	return true;
}


void UStyleWidget::ResetOperateState()
{
	Super::ResetOperateState();
}

void UStyleWidget::ResetSelectState()
{
	ResetOperateState();
	if (!SelectStyleID.Equals(TEXT("-1")))
	{
		if (SubItemsMap.Contains(SelectStyleID))
			SubItemsMap[SelectStyleID]->SetSelectState(false);
		SelectStyleID = TEXT("-1");
	}
}

void UStyleWidget::UpdateOperateState(const ESubItemOrderType& InType)
{
	Super::UpdateOperateState(InType);
}

void UStyleWidget::StyleWidgetOperate(const EWidgetOperateType& InType)
{
	if (InType == EWidgetOperateType::Add)
	{
		FDecorateStyle NewStyle;
		ConstructNewData(NewStyle, !SelectStyleID.Equals(TEXT("-1")));
		StyleItemSelectDelegate.ExecuteIfBound(NewStyle.id);

	}
	else if (InType == EWidgetOperateType::Del)
	{
		bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete selected style?")).ToString());
		if (!Res)
		{
			return;
		}
		if (SelectStyleID.Equals(TEXT("-1")))
		{
			return;
		}
		SubItemsMap[SelectStyleID]->DeleteCurrentStyle();
		SubItemsMap.Remove(SelectStyleID);
		SelectStyleID = TEXT("-1");
		UpdateOperateState(ESubItemOrderType::NoneSelect);
		StyleItemDeleteDelegate.Execute();
	}
	else if (InType == EWidgetOperateType::Up)
	{
		StyleSubItemMoveAction(SelectStyleID, true);
	}
	else if (InType == EWidgetOperateType::Down)
	{
		StyleSubItemMoveAction(SelectStyleID, false);
	}
}

void UStyleWidget::GetContent()
{
	TArray<FDecorateStyle> OutStyle = TArray<FDecorateStyle>();
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA();
	for(const auto Iter : StyleRefData.style_datas)
	{
		auto& Temp = OutStyle.AddDefaulted_GetRef();
		PARSE_STYLE_REF_DATA_TO_ACTION_DATA(Temp, Iter);
	}

#else
	//FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_style ORDER BY SORT_ORDER ASC"), OutStyle);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("select * from decorate_style"), OutStyle);
#endif
	OutStyle.Sort([&](const FDecorateStyle& Left, const FDecorateStyle& Right) {return Left.sort_order < Right.sort_order; });

	if (SelectStyleID.Equals(TEXT("-1")))
	{
		UpdateContent(OutStyle);
	}
	else
	{
		RefreshContent(OutStyle, SelectStyleID);
	}
}

void UStyleWidget::UpdateContent(const TArray<FDecorateStyle>& Items, FString SelectID)
{
	SelectStyleID = SelectID;
	SBItem->ClearChildren();
	SubItemsMap.Empty();
	for (auto& iter : Items)
	{
		UStyleWidgetChild* Child = UStyleWidgetChild::Create();
		Child->SetContent(iter);
		Child->SelectStyleDelegate.BindUFunction(this, FName(TEXT("OnStyleSubWidgetSelectHandler")));
		Child->StyleImageDelegate.BindUFunction(this, FName(TEXT("ImageStyleHandler")));
		Child->MenuPosDelegate.BindUFunction(this, FName(TEXT("ShowRightMenu")));
		Child->StyleCheckDelegate.BindUFunction(this, FName(TEXT("OnStyleCheckChangeHandler")));

		SBItem->AddChild(Child);
		SubItemsMap.Add(iter.id, Child);
	}
}

void UStyleWidget::ConstructNewData(FDecorateStyle& NewStyle, bool IsInsert)
{
	NewStyle.id = FGuid::NewGuid().ToString().ToLower();
	NewStyle.checked = 0;

#ifdef USE_REF_LOCAL_FILE
	FString LocalMD5 = TEXT("");
	int64 FileSize = 0;
	if (!NewStyle.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + NewStyle.thumbnail_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
	}

	GET_STYLE_REF_FILE_DATA_REF();
	int32 StyleIndex = INDEX_NONE;
	if (IsInsert)
	{
		FString CurStyleID = SelectStyleID;
		StyleIndex = StyleRefData.style_datas.IndexOfByPredicate(
			[CurStyleID](const FRefToStyleData& InData)
			{
				return InData.style_id.Equals(CurStyleID);
			}
		);
	}
	auto& TempStyleData = StyleIndex == INDEX_NONE ? StyleRefData.style_datas.AddDefaulted_GetRef()
		: StyleRefData.style_datas.InsertDefaulted_GetRef(StyleIndex + 1);
	PARSE_ACTION_DATA_TO_REF_DATA(TempStyleData, NewStyle, LocalMD5);
	StyleRefData.ReGenerateStyleOrder();
	SelectStyleID = NewStyle.id;
	GetContent();

#else
	if (SubItemsMap.Num() == 0)
	{
		NewStyle.sort_order = 1;
		InsertNewStyle(NewStyle);
		return;
	}


	TArray<UStyleWidgetChild*> StyleItems;
	SubItemsMap.GenerateValueArray(StyleItems);

	if (IsInsert)
	{

		int32 index = StyleItems.IndexOfByKey<UStyleWidgetChild*>(SubItemsMap[SelectStyleID]);
		if (StyleItems.Last()->GetData().id.Equals(SubItemsMap[SelectStyleID]->GetData().id))
		{
			NewStyle.sort_order = StyleItems.Last()->GetData().sort_order + 1;
		}
		else
		{
			for (int32 i = 0, neworder = 0; i < StyleItems.Num(); i++)
			{
				if (i == index + 1)
				{
					NewStyle.sort_order = neworder;
					neworder++;
				}
				FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set SORT_ORDER = '%d' where ID = '%s'")
					, neworder, *StyleItems[i]->GetData().id));
				neworder++;
			}
		}
	}
	else
	{
		if (SubItemsMap.Num() == 0)
		{
			NewStyle.sort_order = 1;
		}
		else
		{
			NewStyle.sort_order = StyleItems.Last()->GetData().sort_order + 1;
		}
	}
	InsertNewStyle(NewStyle);
#endif
}

void UStyleWidget::RefreshContent(const TArray<FDecorateStyle>& Items, const FString& StyleId)
{
	SBItem->ClearChildren();
	SubItemsMap.Empty();
	int32 SelectOrder = -1;
	for (auto& iter : Items)
	{
		UStyleWidgetChild* Child = UStyleWidgetChild::Create();
		Child->SetContent(iter);
		Child->SelectStyleDelegate.BindUFunction(this, FName(TEXT("OnStyleSubWidgetSelectHandler")));
		Child->StyleImageDelegate.BindUFunction(this, FName(TEXT("ImageStyleHandler")));
		Child->MenuPosDelegate.BindUFunction(this, FName(TEXT("ShowRightMenu")));
		Child->StyleCheckDelegate.BindUFunction(this, FName(TEXT("OnStyleCheckChangeHandler")));

		Child->SetVisibility(ESlateVisibility::Visible);
		SBItem->AddChild(Child);
		SubItemsMap.Add(iter.id, Child);
		if (iter.id == StyleId)
		{
			Child->SetSelectState(true);
			SelectOrder = iter.sort_order;
			OnStyleSubWidgetSelectHandler(StyleId);
		}
	}
	UpdateStyleOperateState<UStyleWidgetChild>(SelectOrder, SubItemsMap);
}

UStyleWidget* UStyleWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UStyleWidget>(UStyleWidget::StyleWidgetPath);
}

void UStyleWidget::InsertNewStyle(const FDecorateStyle& OutDecoStyle)
{
#ifdef USE_REF_LOCAL_FILE
	FString LocalMD5 = TEXT("");
	int64 FileSize = 0;
	if (!OutDecoStyle.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + OutDecoStyle.thumbnail_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
		
	}

	GET_STYLE_REF_FILE_DATA_REF();
	auto& TempStyleData = StyleRefData.style_datas.AddDefaulted_GetRef();
	PARSE_ACTION_DATA_TO_REF_DATA(TempStyleData, OutDecoStyle, LocalMD5);

#else
	FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(FString::Printf(TEXT("insert into decorate_style(ID ,CHECKED ,CODE, DESCRIPTION, THUMBNAIL_PATH, SORT_ORDER) values('%s',%d,'%s','%s','%s',%d)"), *OutDecoStyle.id, OutDecoStyle.checked, *OutDecoStyle.code, *OutDecoStyle.description, *OutDecoStyle.thumbnail_path, OutDecoStyle.sort_order));

	if (!OutDecoStyle.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + OutDecoStyle.thumbnail_path);
		FString LocalMD5 = TEXT("");
		int64 FileSize = 0;
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
		FDownloadFileData FileInfo(OutDecoStyle.thumbnail_path, LocalMD5, FileSize);
		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("style_image"), FileInfo);
	}
#endif
	GetContent();
}

void UStyleWidget::UpdateStyles(const TArray<FDecorateStyle>& OutDecoStyles)
{
	for (auto iter : OutDecoStyles)
	{
		FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set CODE = '%s', DESCRIPTION= '%s', THUMBNAIL_PATH= '%s', SORT_ORDER= '%d' where ID = '%s'"), *iter.code, *iter.description, *iter.thumbnail_path, iter.sort_order, *iter.id));
		if (!iter.thumbnail_path.IsEmpty())
		{
			const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + iter.thumbnail_path);
			FString LocalMD5 = TEXT("");
			int64 FileSize = 0;
			ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);
			FDownloadFileData FileInfo(iter.thumbnail_path, LocalMD5, FileSize);
			FDownloadFileDataLibrary::UpdateFileMD5(TEXT("style_image"), FileInfo);
		}
	}
}

void UStyleWidget::BindDelegate()
{

}

void UStyleWidget::StyleSubItemMoveAction(const FString& StyleId, bool IsUp)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	SWAPE_ITEM_ACTION(StyleRefData.style_datas, FRefToStyleData, style_id, StyleId, IsUp);
	/*int32 StyleIndex = StyleRefData.style_datas.IndexOfByPredicate(
		[StyleId](const FRefToStyleData& InData)
		{
			return InData.style_id.Equals(StyleId);
		}
	);
	if(StyleIndex != INDEX_NONE)
	{
		auto StyleData = StyleRefData.style_datas[StyleIndex];
		if(IsUp)
		{
			int32 AnotherIndex = StyleIndex - 1;
			auto AnotherStyleData = StyleRefData.style_datas[AnotherIndex];
			StyleRefData.style_datas.Insert(StyleData, AnotherIndex);
			StyleRefData.style_datas.RemoveAt(StyleIndex + 1);
		}
		else
		{
			int32 AnotherIndex = StyleIndex + 1;
			auto AnotherStyleData = StyleRefData.style_datas[AnotherIndex];
			StyleRefData.style_datas.RemoveAt(AnotherIndex);
			StyleRefData.style_datas.Insert(StyleData, StyleIndex);
		}
	}
	else
	{
		checkf(false, TEXT("no reason to active this logic"));
	}*/
	StyleRefData.ReGenerateStyleOrder();
	
#else
	TArray<UStyleWidgetChild*> StyleItems;
	SubItemsMap.GenerateValueArray(StyleItems);
	int32 index = StyleItems.IndexOfByKey<UStyleWidgetChild*>(SubItemsMap[StyleId]);
	int32 preIndex = (IsUp ? -1 : 1) + index;

	if (preIndex > StyleItems.Num() || preIndex < 0)
		return;
	FString CurrentID = StyleItems[index]->GetData().id;
	int32 currentOrder = StyleItems[index]->GetData().sort_order;
	FString SwapID = StyleItems[preIndex]->GetData().id;
	int32 SwapOrder = StyleItems[preIndex]->GetData().sort_order;

	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set SORT_ORDER= '%d' where ID = '%s'"), SwapOrder, *CurrentID));
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set SORT_ORDER= '%d' where ID = '%s'"), currentOrder, *SwapID));
#endif

	GetContent();
	OnStyleSubWidgetSelectHandler(StyleId);
}

void UStyleWidget::OnStyleSubWidgetSelectHandler(const FString& StyleID)
{
	if (!SelectStyleID.Equals(TEXT("-1")))
	{
		SubItemsMap[SelectStyleID]->SetSelectState(false);
		SubItemsMap[SelectStyleID]->ResetReadOnly();

	}
	UpdateStyleOperateState<UStyleWidgetChild>(SubItemsMap[StyleID]->GetData().sort_order, SubItemsMap);
	SubItemsMap[StyleID]->SetSelectState(true);
	if (SelectStyleID != StyleID)
	{
		StyleItemSelectDelegate.ExecuteIfBound(StyleID);
		SelectStyleID = StyleID;
	}
}

void UStyleWidget::OnClickedBtnAdd()
{
	StyleWidgetOperate(EWidgetOperateType::Add);
}

void UStyleWidget::OnClickedBtnDelete()
{
	StyleWidgetOperate(EWidgetOperateType::Del);
}

void UStyleWidget::OnClickedBtnUp()
{
	StyleWidgetOperate(EWidgetOperateType::Up);
}

void UStyleWidget::OnClickedBtnDown()
{
	StyleWidgetOperate(EWidgetOperateType::Down);
}

void UStyleWidget::OnStyleCheckChangeHandler(const FString& InStyleID, bool IsCheck)
{
#ifdef USE_REF_LOCAL_FILE
	GET_STYLE_REF_FILE_DATA_REF();
	int32 StyleIndex = StyleRefData.style_datas.IndexOfByPredicate(
		[InStyleID](const FRefToStyleData& InData)
		{
			return InData.style_id.Equals(InStyleID);
		}
	);
	if (StyleIndex != INDEX_NONE)
	{
		StyleRefData.style_datas[StyleIndex].is_checked = IsCheck;
	}

#else
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set checked = %d where id = '%s'"), IsCheck ? 1 : 0, *InStyleID));
#endif
}

void UStyleWidget::ImageStyleHandler(const FString& Path)
{
	StyleImageDelegate.ExecuteIfBound(Path);
}

void UStyleWidget::ShowRightMenu(UStyleWidgetChild* item, const FVector2D& MousePos)
{
	ImageMenuWidget->SetItem(item);
	MAMenu->Close();
	UCanvasPanelSlot* CPSMenu = UWidgetLayoutLibrary::SlotAsCanvasSlot(MAMenu);
	CPSMenu->SetPosition(FVector2D(MousePos.X, MousePos.Y - 30));
	MAMenu->Open(true);
}

UImageMenuWidget* UStyleWidget::CreateRightMenu()
{
	return ImageMenuWidget;
}

FReply UStyleWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	return FReply::Handled();
}

void UStyleWidget::GetMenuOption(const int32& option)
{
	if (option == 1)
	{
		ImageMenuWidget->GetItem()->SelectStyleImage();
	}
}

void UStyleWidget::ResetScroll()
{
	SBItem->ScrollToStart();
}