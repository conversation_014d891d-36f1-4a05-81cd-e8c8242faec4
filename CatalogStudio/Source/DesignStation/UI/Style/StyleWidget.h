// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleBaseWidget.h"
#include "StyleWidgetChild.h"
#include "ImageMenuWidget.h"
#include "StyleWidget.generated.h"

/**
 * 
 */

class UButton;
class UScrollBox;
class UMenuAnchor;
class UCanvasPanel;

UENUM(BlueprintType)
enum class EStyleOpType : uint8
{
	EAdd = 0,
	EDelete,
	EUp,
	EDown
};

DECLARE_DYNAMIC_DELEGATE(FStyleItemDeleteDelegate);

UCLASS()
class DESIGNSTATION_API UStyleWidget : public UStyleBaseWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;
	virtual void ResetOperateState() override;
	virtual void ResetSelectState() override;
	virtual void UpdateOperateState(const ESubItemOrderType& InType) override;
	virtual void StyleWidgetOperate(const EWidgetOperateType& InType) override;

	void GetContent();
	void UpdateContent(const TArray<FDecorateStyle>& Items, FString SelectID = TEXT("-1"));

	FORCEINLINE FString GetSelectStyleID() { return SelectStyleID; }
	void SetSelectStyleID(FString InSelectStyleID) { SelectStyleID = InSelectStyleID; }

	static UStyleWidget* Create();

public:
	void InsertNewStyle(const FDecorateStyle& OutDecoStyle);
	void UpdateStyles(const TArray<FDecorateStyle>& OutDecoStyles);
	void ResetScroll();
private:
	void BindDelegate();

private:
	FString CreateUUID;
	FString GetStylesUUID;
	FString UpdateUUID;
	FString SwapUUID;

	int32 InsertIndex;
	UPROPERTY()
		TArray<FDecorateStyle> OutUpdateDatas;
	UPROPERTY()
		TArray<FDecorateStyle> OutDatas;

private:

	void ConstructNewData(FDecorateStyle& NewStyle, bool IsInsert);
	void RefreshContent(const TArray<FDecorateStyle>& Items, const FString& StyleId);
	void StyleSubItemMoveAction(const FString& StyleId, bool IsUp);
	UFUNCTION()
		void OnStyleSubWidgetSelectHandler(const FString& StyleID);

public:
	FStyleItemSelectDelegate StyleItemSelectDelegate;
	FStyleItemDeleteDelegate StyleItemDeleteDelegate;
	
private:
	UPROPERTY()
		TMap<FString, UStyleWidgetChild*> SubItemsMap;

	FString SelectStyleID;
	FString OldSelectStyleID;

	static FString StyleWidgetPath;

	UPROPERTY()
		UMenuAnchor* MAMenu;
	UPROPERTY()
		UCanvasPanel* CPMenu;
	UPROPERTY()
		UImageMenuWidget* ImageMenuWidget;

protected:
	UFUNCTION()
		void OnClickedBtnAdd();
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();
	UFUNCTION()
		void OnStyleCheckChangeHandler(const FString& InStyleID, bool IsCheck);
	UFUNCTION()
		void ImageStyleHandler(const FString& Path);
	UFUNCTION()
		void ShowRightMenu(UStyleWidgetChild* item, const FVector2D & MousePos);
	UFUNCTION()
		UImageMenuWidget* CreateRightMenu();
	UFUNCTION()
		void GetMenuOption(const int32& option);

	virtual FReply NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent) override;

public:
	FStyleOpTypeDelegate StyleOpTypeDelegate;
	FStylePathDelegate StyleImageDelegate;

};
