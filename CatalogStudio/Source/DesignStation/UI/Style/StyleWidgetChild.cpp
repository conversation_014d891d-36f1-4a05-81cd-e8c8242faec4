// Fill out your copyright notice in the Description page of Project Settings.

#include "StyleWidgetChild.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/SQLite/FolderRelated/DownloadFileData.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Operation/OperatorWidgetConfig.h"


#define LOCTEXT_NAMESPACE "Style-StyleData"

FString UStyleWidgetChild::StyleWidgetChildPath = TEXT("WidgetBlueprint'/Game/UI/Style/StyleWidgetChild.StyleWidgetChild_C'");

bool UStyleWidgetChild::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	return true;
}

void UStyleWidgetChild::NativeOnInitialized()
{
	BIND_PARAM_CPP_TO_UMG(ImgStyle, Img_Style);
	BIND_PARAM_CPP_TO_UMG(TxtCode, Txt_Code);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(TxtCodeRead, Txt_CodeRead);
	BIND_PARAM_CPP_TO_UMG(TxtNameRead, Txt_NameRead);
	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_PARAM_CPP_TO_UMG(BorCode, Bor_Code);
	BIND_PARAM_CPP_TO_UMG(BorImage, Bor_Image);
	BIND_PARAM_CPP_TO_UMG(BorBody, Bor_Body);
	BIND_PARAM_CPP_TO_UMG(CBSelect, CB_Select);
	BIND_PARAM_CPP_TO_UMG(BorCraft, Bor_Craft);

	BIND_SLATE_WIDGET_FUNCTION(BorImage, OnMouseButtonDownEvent, FName(TEXT("OnBorImageClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorImage, OnMouseDoubleClickEvent, FName(TEXT("OnBorImageDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorBody, OnMouseButtonDownEvent, FName(TEXT("OnBorBodyClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseDoubleClickEvent, FName(TEXT("OnBorNameDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorCode, OnMouseDoubleClickEvent, FName(TEXT("OnBorCodeDoubleClicked")));


	BIND_WIDGET_FUNCTION(CBSelect, OnCheckStateChanged, UStyleWidgetChild::OnStateChangedCkbSelect);

	BIND_WIDGET_FUNCTION(TxtCode, OnTextCommitted, UStyleWidgetChild::OnTextCommittedCode);
	BIND_WIDGET_FUNCTION(TxtCode, OnTextChanged, UStyleWidgetChild::OnTextChangedCode);
	BIND_WIDGET_FUNCTION(TxtName, OnTextCommitted, UStyleWidgetChild::OnTextCommittedName);

	BIND_WIDGET_FUNCTION(CBS_Group, OnSelectionChanged, UStyleWidgetChild::OnGroupSelectChangeHandler);

	BindDelegates();
}

UStyleWidgetChild* UStyleWidgetChild::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UStyleWidgetChild>(UStyleWidgetChild::StyleWidgetChildPath);
}

void UStyleWidgetChild::SetContent(const FDecorateStyle& style)
{
	InitCraft(ACatalogPlayerController::Get()->GetStyleCraft());
	UpdateCurCraft(style.craft);

	CBSelect->SetIsChecked(style.checked == 1 ? true : false);
	TxtCode->SetText(FText::FromString(style.code));
	TxtCode->SetIsReadOnly(true);
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);
	FString Read = TxtCode->GetText().ToString();
	TxtCodeRead->SetText(FText::FromString(Read.Left(15)));
	TxtCodeRead->SetVisibility(ESlateVisibility::Visible);

	TxtName->SetText(FText::FromString(style.description));
	TxtName->SetIsReadOnly(true);
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	TxtNameRead->SetText(TxtName->GetText());
	TxtNameRead->SetToolTipText(TxtName->GetText());
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);

	if (!style.thumbnail_path.IsEmpty())
	{
		LoadThumbnail(style.thumbnail_path, style.thumbnail_md5);
		/*const FString ThumbnailPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + style.thumbnail_path);
		UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(ThumbnailPath);
		if (nullptr != ThumbnailTex)
		{
			ImgStyle->SetBrushFromTexture(ThumbnailTex);
		}*/
	}

	if(CBS_Group)
	{
		CBS_Group->SetSelectedIndex(style.style_group);
	}

	DecorateStyle = style;
}

void UStyleWidgetChild::SelectSelf()
{
	SelectStyleDelegate.ExecuteIfBound(DecorateStyle.id);
}

void UStyleWidgetChild::ResetReadOnly()
{
	if (IS_OBJECT_PTR_VALID(TxtCode) && IS_OBJECT_PTR_VALID(TxtCodeRead))
	{
		TxtCode->SetIsReadOnly(true);
		TxtCode->SetVisibility(ESlateVisibility::Collapsed);
		TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
	}
}

void UStyleWidgetChild::InitCraft(const TArray<FString>& CraftOptions)
{
	ClearCraftOptions();
	for (const auto CO : CraftOptions)
	{
		AddCraftOption(CO);
	}
}


void UStyleWidgetChild::OnCraftChanged(const FString& NewOption)
{
	DecorateStyle.craft = NewOption;
	UpdateCurrentStyle(DecorateStyle);
}

void UStyleWidgetChild::UpdateCurrentStyle(const FDecorateStyle& style)
{
#ifdef USE_REF_LOCAL_FILE
	GET_STYLE_REF_FILE_DATA_REF();
	FString FileMd5 = TEXT("");
	if (!style.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + style.thumbnail_path);
		FDownloadFileData FileInfo(style.thumbnail_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, FileMd5, FileInfo.size);
	}
	int32 EditIndex = StyleRefData.style_datas.IndexOfByPredicate(
		[style](const FRefToStyleData& InData)->bool { return InData.style_id.Equals(style.id); }
	);
	if(EditIndex != INDEX_NONE)
	{
		PARSE_ACTION_DATA_TO_REF_DATA(StyleRefData.style_datas[EditIndex], style, FileMd5);
	}
#else
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_style set CODE = '%s', DESCRIPTION= '%s', THUMBNAIL_PATH= '%s', SORT_ORDER= '%d' where ID = '%s'"), *style.code, *style.description, *style.thumbnail_path, style.sort_order, *style.id));

	if (!style.thumbnail_path.IsEmpty())
	{
		const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + style.thumbnail_path);
		FDownloadFileData FileInfo(style.thumbnail_path);
		ACatalogPlayerController::GetFileMD5AndSize(FilePath, FileInfo.md5, FileInfo.size);
		FDownloadFileDataLibrary::UpdateFileMD5(TEXT("style_image"), FileInfo);
	}
#endif

	InitCraft(ACatalogPlayerController::Get()->GetStyleCraft());

	SetContent(style);
}

void UStyleWidgetChild::DeleteCurrentStyle()
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	StyleRefData.DeleteStyle(DecorateStyle.id);

#else
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_selch WHERE STYLE_ID = '%s'"), *DecorateStyle.id));
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_style WHERE id = '%s'"), *DecorateStyle.id));
#endif
	this->SetVisibility(ESlateVisibility::Collapsed);
	this->RemoveFromParent();
}

void UStyleWidgetChild::LoadThumbnail(const FString& InPath, const FString& InMd5)
{
	if (!InPath.IsEmpty())
	{
		const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
			FPaths::ProjectContentDir(), InPath
		));
		if (FPaths::FileExists(AbsPath))
		{
			FString FileMd5 = TEXT("");
			int64 FileSize = 0;
			ACatalogPlayerController::GetFileMD5AndSize(AbsPath, FileMd5, FileSize);
			if (InMd5.Equals(FileMd5))
			{
				UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);
				if (nullptr != ThumbnailTex)
				{
					ImgStyle->SetBrushFromTexture(ThumbnailTex);
				}
			}
			else
			{
				DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(InPath);
			}
		}
		else
		{
			DownloadUUID = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(InPath);
		}
	}
}

void UStyleWidgetChild::BindDelegates()
{
#ifdef USE_REF_LOCAL_FILE

	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UStyleWidgetChild::OnDownloadResponseHandler);

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UStyleWidgetChild::OnDownloadResponseHandler);
	}
#endif
}

void UStyleWidgetChild::OnDownloadResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	if (DownloadUUID.Equals(UUID))
	{
		if (OutRes)
		{
			if (OutFilePath.IsValidIndex(0))
			{
				const FString AbsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(
					FPaths::ProjectContentDir(), OutFilePath[0]
				));
				if (UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath))
				{
					ImgStyle->SetBrushFromTexture(FileImage);
				}
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file thumbnail Error"));
		}
	}
}
void UStyleWidgetChild::OnUploadResponseHandler(const FString& UUID, bool OutRes, const FString& OutFilePath)
{
	if (UploadUUID.Equals(UUID))
	{
		DecorateStyle.thumbnail_path = OutFilePath;
		UpdateCurrentStyle(DecorateStyle);
	}
}
void UStyleWidgetChild::OnCodeUniqueResponseHandler(const FString& UUID, bool OutResult)
{
	/*if (UUID == UniqueUUID)
	{
		CodeUnique = OutResult;
	}*/
}

void UStyleWidgetChild::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(true, static_cast<int32>(EColorType::Hover));
		SetTextColor(true);
	}
}

void UStyleWidgetChild::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(false, static_cast<int32>(EColorType::Hover));
		SetTextColor(false);
	}
}

//FReply UStyleWidgetChild::NativeOnMouseButtonDown(const FGeometry & InGeometry, const FPointerEvent & InMouseEvent)
//{
//
//	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
//	{
//		SelectStyleDelegate.ExecuteIfBound(DecorateStyle.id);
//		return FReply::Handled();
//	}
//	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
//	{
//		//SelectStyleDelegate.ExecuteIfBound(-1);
//		return FReply::Handled();
//	}
//	return FReply::Unhandled();
//
//}

void UStyleWidgetChild::SetBorderColor(bool IsSelect, const int32& Type)
{
	if (
		IS_OBJECT_PTR_VALID(BorCode) && 
		IS_OBJECT_PTR_VALID(BorName) && 
		IS_OBJECT_PTR_VALID(BorCraft) && 
		IS_OBJECT_PTR_VALID(Bor_Group)
		)
	{

		switch (Type)
		{
		case static_cast<int32>(EColorType::Select):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorCraft);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, Bor_Group);
			break;
		case static_cast<int32>(EColorType::Hover):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorCraft);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, Bor_Group);
			break;
		default:
			break;
		}
	}
}

void UStyleWidgetChild::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtCode) && IS_OBJECT_PTR_VALID(TxtName))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtCodeRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : OperatorColor::TextUnselect, TxtNameRead);
	}
}

FEventReply UStyleWidgetChild::OnBorImageClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		StyleImageDelegate.ExecuteIfBound(DecorateStyle.thumbnail_path);
		return FEventReply(true);
	}
	if (MouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		APlayerController* PC = GWorld->GetFirstPlayerController();
		ULocalPlayer* LocalPlayer = PC->GetLocalPlayer();
		FVector2D Position;
		if (LocalPlayer->ViewportClient->GetMousePosition(Position))
		{
			MenuPosDelegate.ExecuteIfBound(this, Position);
		}
		return FEventReply(true);
	}
	return FEventReply();
}

//FEventReply UStyleWidgetChild::OnBorImageDoubleClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent)
//{
//	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
//	{
//		FString ImagePath;
//		FCatalogFunctionLibrary::OpenFileDialogForImage(ImagePath);
//		if (!ImagePath.IsEmpty())
//		{
//
//			FString TempPath = FPaths::ProjectContentDir() + FString::Printf(TEXT("Styles/%d/Thumbnails"), DecorateStyle.id);
//
//			TempPath = FPaths::ConvertRelativePathToFull(TempPath);
//
//			IPlatformFile& fileManager = FPlatformFileManager::Get().GetPlatformFile();
//			if (!fileManager.DeleteFile(*TempPath))
//			{
//				fileManager.CreateDirectoryTree(*TempPath);
//			}
//
//			FString SavePath = FString::Printf(TEXT("Styles/%d/Thumbnails/style.%s"), DecorateStyle.id, *FPaths::GetExtension(ImagePath));
//			FString DataPath = FPaths::ProjectContentDir() + SavePath;
//			DataPath = FPaths::ConvertRelativePathToFull(DataPath);
//
//			FCatalogFunctionLibrary::CopyFileTo(ImagePath, DataPath);
//
//			DecorateStyle.thumbnail_path = SavePath;
//
//			UDecorateStyleOperatorLibrary::UpdateStyle(DecorateStyle);
//			
//			UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(DataPath);
//			if (nullptr != ThumbnailTex)
//			{
//				ImgStyle->SetBrushFromTexture(ThumbnailTex);
//			}
//		}
//		return FEventReply(true);
//	}
//	return FEventReply();
//}

void UStyleWidgetChild::SelectStyleImage()
{
	if (bImageSelecting) return;
	bImageSelecting = true;
	FString ImagePath;
	FCatalogFunctionLibrary::OpenFileDialogForImage(ImagePath);
	bImageSelecting = false;
	if (ImagePath.IsEmpty()) return;

	{
		FString SavePath = FString::Printf(TEXT("Styles/%s/Thumbnails/style.%s"), *DecorateStyle.id, *FPaths::GetExtension(ImagePath));
		FString DataPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + SavePath);

		if (FCatalogFunctionLibrary::CopyFileTo(ImagePath, DataPath) == ECopyFileErrorCode::ESuccess)
		{
			DecorateStyle.thumbnail_path = SavePath;

			UCatalogNetworkSubsystem::GetInstance()->SendUploadFileRequest(SavePath);

			FString FileMd5 = TEXT("");
			int64 FileSize = 0;
			ACatalogPlayerController::GetFileMD5AndSize(DataPath, FileMd5, FileSize);
			DecorateStyle.thumbnail_md5 = FileMd5;

			UpdateCurrentStyle(DecorateStyle);
		}
	}
}

FEventReply UStyleWidgetChild::OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectStyleDelegate.ExecuteIfBound(DecorateStyle.id);
	return FEventReply();
}

FEventReply UStyleWidgetChild::OnBorCodeDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	TxtCode->SetIsReadOnly(false);
	TxtCode->SetVisibility(ESlateVisibility::Visible);
	TxtCodeRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtCode->SetUserFocus(PC);
	return FEventReply();
}

FEventReply UStyleWidgetChild::OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	TxtName->SetIsReadOnly(false);
	TxtName->SetVisibility(ESlateVisibility::Visible);
	TxtNameRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtName->SetUserFocus(PC);
	return FEventReply();
}

void UStyleWidgetChild::SetSelectState(bool _IsSelect)
{
	SetTextColor(_IsSelect);
	SetBorderColor(_IsSelect, static_cast<int32>(EColorType::Select));
	IsSelect = _IsSelect;
}

void UStyleWidgetChild::OnTextCommittedCode(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.ToString().Equals(DecorateStyle.code, ESearchCase::CaseSensitive))
	{
		return;
	}
	if (!CodeUnique)
	{
		TxtCode->SetText(FText::FromString(DecorateStyle.code));
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
			TEXT("Error"),
			FText::FromStringTable(FName("PosSt"), TEXT("Code should be unique, please change")).ToString());
		return;
	}
	FString CleanData = TxtCode->GetText().ToString();
	FRegexPattern Pattern(TEXT("^[A-Z]+[A-Z0-9]*+$"));
	FRegexMatcher RegMatcher(Pattern, CleanData);
	RegMatcher.SetLimits(0, CleanData.Len());
	bool Res = RegMatcher.FindNext();
	if (Text.IsEmpty()) Res = true;
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len() <= 20 && Res)
	{
		TxtCode->SetText(Text);
		DecorateStyle.code = Text.ToString();
		//UDecorateStyleOperatorLibrary::UpdateStyle(DecorateStyle);
		UpdateCurrentStyle(DecorateStyle);
		//TxtCode->SetIsReadOnly(true);
		FString Read = Text.ToString();
		TxtCodeRead->SetText(FText::FromString(Read.Left(15)));
		/*TxtCode->SetVisibility(ESlateVisibility::Collapsed);
		TxtCodeRead->SetVisibility(ESlateVisibility::Visible);*/
	}
	if (!Res)
	{
		FString EditText = TxtCodeRead->GetText().ToString();
		TxtCode->SetText(FText::FromString(EditText.Left(15)));
	}
	/*else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtCode->SetText(FText::FromString(DecorateStyle.code));
	}*/
	TxtCode->SetIsReadOnly(true);
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);
	TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
}

void UStyleWidgetChild::OnTextCommittedName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared && Text.ToString().Len() <= 50)
	{
		TxtName->SetText(Text);
		DecorateStyle.description = Text.ToString();
		//UDecorateStyleOperatorLibrary::UpdateStyle(DecorateStyle);
		UpdateCurrentStyle(DecorateStyle);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtName->SetText(FText::FromString(DecorateStyle.description));
	}
	TxtName->SetIsReadOnly(true);
	TxtNameRead->SetText(TxtName->GetText());
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);

}

void UStyleWidgetChild::OnTextChangedCode(const FText& Text)
{
	if ((Text.ToString() == DecorateStyle.code) || Text.ToString().IsEmpty())
	{
		CodeUnique = true;
		return;
	}

#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	CodeUnique = !StyleRefData.IsStyleCodeExist(Text.ToString());

#else
	TArray<FDecorateStyle> OutStyle = TArray<FDecorateStyle>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_style where CODE = '%s' "), *Text.ToString()), OutStyle);
	CodeUnique = OutStyle.Num() <= 0;
#endif
}

void UStyleWidgetChild::OnStateChangedCkbSelect(bool IsChecked)
{
	DecorateStyle.checked = IsChecked ? 1 : 0;
	StyleCheckDelegate.ExecuteIfBound(DecorateStyle.id, IsChecked);//���·�����ݿ�
}

void UStyleWidgetChild::OnGroupSelectChangeHandler(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if(SelectionType == ESelectInfo::Type::OnMouseClick)
	{
		int32 Index = CBS_Group->FindOptionIndex(SelectedItem);
		if(DecorateStyle.style_group != Index)
		{
			DecorateStyle.style_group = Index;
			UpdateCurrentStyle(DecorateStyle);
		}
	}
}

#undef LOCTEXT_NAMESPACE
