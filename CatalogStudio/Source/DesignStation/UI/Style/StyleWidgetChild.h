// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/ComboBoxString.h"
#include "DesignStation/SQLite/DecorateStyleRelated/DecorateStyleOperatorLibrary.h"
#include "StyleWidgetChild.generated.h"

class UImage;
class UTextBlock;
class UBorder;
class UEditableText;
class UCheckBox;

DECLARE_DYNAMIC_DELEGATE_OneParam(FStyleOpTypeDelegate, const int32&, Type);
DECLARE_DYNAMIC_DELEGATE_OneParam(FStyleSelectDelegate, const FString&, StyleID);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FStyleCheckDelegate, const FString&, StyleID, bool, IsCheck);
DECLARE_DYNAMIC_DELEGATE_OneParam(FStylePathDelegate, const FString&, Path);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FMenuPosDelegate, UStyleWidgetChild*, item, const FVector2D&, Pos);



const FLinearColor StyleChildUnselect = FLinearColor(0.938686f, 0.947307f, 0.955974f, 1.0f);


UCLASS()
class DESIGNSTATION_API UStyleWidgetChild : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void NativeOnInitialized() override;

	static UStyleWidgetChild* Create();
	void SetContent(const FDecorateStyle& style);
	void SelectSelf();
	FDecorateStyle GetData() { return DecorateStyle; }
	void ResetReadOnly();

	void InitCraft(const TArray<FString>& CraftOptions);
	UFUNCTION(BlueprintImplementableEvent, Category = "StyleWidgetChild")
	void ClearCraftOptions();
	UFUNCTION(BlueprintImplementableEvent, Category = "StyleWidgetChild")
	void AddCraftOption(const FString& CraftOption);

	UFUNCTION(BlueprintImplementableEvent, Category = "StyleWidgetChild")
	void UpdateCurCraft(const FString& InCraft);

	UFUNCTION(BlueprintCallable, Category = "StyleWidgetChild")
	void OnCraftChanged(const FString& NewOption);

public:
	void UpdateCurrentStyle(const FDecorateStyle& style);
	void DeleteCurrentStyle();

	void LoadThumbnail(const FString& InPath, const FString& InMd5);

private:
	void BindDelegates();
	/*UFUNCTION()
		void OnUpdateStyleItemHandler(const FString& UUID, const TArray<FDecorateStyle>& OutDecoStyle);*/
		/*UFUNCTION()
			void OnDeleteStyleItemHandler(const FString& UUID, bool OutResult);*/
	UFUNCTION()
		void OnCodeUniqueResponseHandler(const FString& UUID, bool OutResult);
	UFUNCTION()
		void OnDownloadResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
		void OnUploadResponseHandler(const FString& UUID, bool OutRes, const FString& OutFilePath);
private:
	FString UpdateUUID;
	FString DeleteUUID;
	FString UniqueUUID;
	FString DownloadUUID;
	FString UploadUUID;
	bool CodeUnique;
private:
	static FString StyleWidgetChildPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	//virtual FReply NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;

public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);

protected:
	UFUNCTION()
		FEventReply OnBorImageClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	/*UFUNCTION()
		FEventReply OnBorImageDoubleClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent);*/
	UFUNCTION()
		FEventReply OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorCodeDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnTextCommittedCode(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextChangedCode(const FText& Text);
	UFUNCTION()
		void OnStateChangedCkbSelect(bool IsChecked);

	UFUNCTION()
	void OnGroupSelectChangeHandler(FString SelectedItem, ESelectInfo::Type SelectionType);

private:
	UPROPERTY()
		UImage* ImgStyle;
	UPROPERTY()
		UEditableText* TxtCode;
	UPROPERTY()
		UEditableText* TxtName;
	UPROPERTY()
		UBorder* BorCode;
	UPROPERTY()
		UBorder* BorName;
	UPROPERTY()
		UBorder* BorImage;
	UPROPERTY()
		UBorder* BorBody;
	UPROPERTY()
		UTextBlock* TxtCodeRead;
	UPROPERTY()
		UTextBlock* TxtNameRead;
	UPROPERTY()
		UCheckBox* CBSelect;
	//UPROPERTY(BlueprintReadWrite, Category = "StyleWidgetChild", meta = (AllowPrivateAccess = true, BindWidget = true))
	//	UCheckBox* CB_Select;

	UPROPERTY()
		UBorder* BorCraft;

	UPROPERTY(meta=(BindWidget))
	UBorder* Bor_Group;

	UPROPERTY(meta=(BindWidget))
	UComboBoxString* CBS_Group;

	UPROPERTY()
		FDecorateStyle DecorateStyle;

private:
	int32  Index;
	bool IsSelect;
	bool IsBoxChecked;
	//Fix bug CATALOG-1536
	bool bImageSelecting = false;

public:
	FStyleSelectDelegate SelectStyleDelegate;
	FStyleCheckDelegate StyleCheckDelegate;
	FStylePathDelegate StyleImageDelegate;
	FMenuPosDelegate MenuPosDelegate;


public:
	void SetSelectState(bool Select);
	void SelectStyleImage();

};
