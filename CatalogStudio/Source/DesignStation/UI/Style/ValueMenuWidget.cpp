// Fill out your copyright notice in the Description page of Project Settings.

#include "ValueMenuWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Button.h"

FString UValueMenuWidget::ValueMenuWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/ValueMenuWidget.ValueMenuWidget_C'");

bool UValueMenuWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnDisplay, Btn_Display);
	BIND_PARAM_CPP_TO_UMG(BtnCopy, Btn_Copy);
	BIND_PARAM_CPP_TO_UMG(BtnPaste, Btn_Paste);

	BIND_WIDGET_FUNCTION(BtnDisplay, OnClicked, UValueMenuWidget::OnClickedBtnDisplay);
	BIND_WIDGET_FUNCTION(BtnCopy, OnClicked, UValueMenuWidget::OnClickedBtnCopy);
	BIND_WIDGET_FUNCTION(BtnPaste, OnClicked, UValueMenuWidget::OnClickedBtnPaste);

	return true;
}

void UValueMenuWidget::SetBtnDisplayState(bool IsChecked)
{
	if (BtnDisplay)
	{
		BtnDisplay->SetIsEnabled(IsChecked);
	}
}

void UValueMenuWidget::SetBtnPasteState(bool IsCopy)
{
	if (BtnPaste)
	{
		BtnPaste->SetIsEnabled(IsCopy);
	}
}

void UValueMenuWidget::SetMenuState(bool IsCheck, bool IsCanCopy)
{
	SetBtnDisplayState(IsCheck);
	SetBtnPasteState(IsCanCopy);
}

UValueMenuWidget * UValueMenuWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UValueMenuWidget>(UValueMenuWidget::ValueMenuWidgetPath);
}

void UValueMenuWidget::OnClickedBtnDisplay()
{
	ValueMenuDelegate.ExecuteIfBound((int32)EValueMenuType::EDisplay);
}

void UValueMenuWidget::OnClickedBtnCopy()
{
	ValueMenuDelegate.ExecuteIfBound((int32)EValueMenuType::ECopy);
}

void UValueMenuWidget::OnClickedBtnPaste()
{
	ValueMenuDelegate.ExecuteIfBound((int32)EValueMenuType::EPaste);
}
