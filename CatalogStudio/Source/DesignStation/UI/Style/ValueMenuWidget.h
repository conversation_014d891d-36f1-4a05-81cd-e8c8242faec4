// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "StyleWidgetChild.h"
#include "ValueMenuWidget.generated.h"

/**
 * 
 */

class UButton;

UENUM(BlueprintType)
enum class EValueMenuType : uint8
{
	EDisplay = 0,
	ECopy,
	EPaste
};

UCLASS()
class DESIGNSTATION_API UValueMenuWidget : public UUserWidget
{
	GENERATED_BODY()
public:
	virtual bool Initialize() override;
	void SetMenuState(bool IsCheck, bool IsCanCopy);

	static UValueMenuWidget* Create();

private:
	void SetBtnDisplayState(bool IsChecked);
	void SetBtnPasteState(bool IsCopy);
	
private:
	static FString ValueMenuWidgetPath;

protected:
	UFUNCTION()
		void OnClickedBtnDisplay();
	UFUNCTION()
		void OnClickedBtnCopy();
	UFUNCTION()
		void OnClickedBtnPaste();

private:
	UPROPERTY()
		UButton* BtnDisplay;
	UPROPERTY()
		UButton* BtnCopy;
	UPROPERTY()
		UButton* BtnPaste;

public:
	FStyleOpTypeDelegate ValueMenuDelegate;
};
