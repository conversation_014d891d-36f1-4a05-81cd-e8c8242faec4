// Fill out your copyright notice in the Description page of Project Settings.

#include "ValueWidget.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Components/ScrollBox.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "StyleWidget.h"
#include "DataCenter/Parameter/ParameterEffectionParser.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "DesignStation/SQLite/LocalDatabaseOperatorLibrary.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/MoveAndResize/ExternalWidget.h"
#include "DesignStation/UI/Parameters/ParameterDetailWidget.h"
#include <RefRelation/RefRelationFunction.h>

FString UValueWidget::ValueWidgetPath = TEXT("WidgetBlueprint'/Game/UI/Style/ValueWidget.ValueWidget_C'");


bool UValueWidget::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(BtnAdd, Btn_Add);
	BIND_PARAM_CPP_TO_UMG(BtnDelete, Btn_Delete);
	BIND_PARAM_CPP_TO_UMG(BtnUp, Btn_Up);
	BIND_PARAM_CPP_TO_UMG(BtnDown, Btn_Down);
	BIND_PARAM_CPP_TO_UMG(SBItem, SB_Item);

	BIND_PARAM_CPP_TO_UMG(BtnParaAdd, Btn_ParaAdd);
	BIND_PARAM_CPP_TO_UMG(BtnParaDelete, Btn_ParaDelete);
	BIND_PARAM_CPP_TO_UMG(SBPara, SB_Para);


	BIND_WIDGET_FUNCTION(BtnAdd, OnClicked, UValueWidget::OnClickedBtnAdd);
	BIND_WIDGET_FUNCTION(BtnDelete, OnClicked, UValueWidget::OnClickedBtnDelete);
	BIND_WIDGET_FUNCTION(BtnUp, OnClicked, UValueWidget::OnClickedBtnUp);
	BIND_WIDGET_FUNCTION(BtnDown, OnClicked, UValueWidget::OnClickedBtnDown);
	BIND_WIDGET_FUNCTION(BtnParaAdd, OnClicked, UValueWidget::OnClickedBtnParaAdd);
	BIND_WIDGET_FUNCTION(BtnParaDelete, OnClicked, UValueWidget::OnClickedBtnParaDelete);

	BIND_WIDGET_FUNCTION(CB_All, OnCheckStateChanged, UValueWidget::OnStateChangedCkbSelect);

	BIND_WIDGET_FUNCTION(Btn_Para_Up, OnClicked, UValueWidget::OnClickedBtnParamUp);
	BIND_WIDGET_FUNCTION(Btn_Para_Down, OnClicked, UValueWidget::OnClickedBtnParamDown);

	CurrentStyleID = TEXT("-1");
	CurrentOptionID = TEXT("-1");
	SelectValueID = TEXT("-1");
	SelectParamID = TEXT("-1");

	CopyId = TEXT("-1");

	NumOfSelectionCheck = 0;
	InsertIndex = -1;
	OutUpdateDatas.Empty();

	BindDelegates();
	return true;
}

void UValueWidget::OnStateChangedCkbSelect(bool IsChecked)
{
	for (auto Ite : SubValuesMap)
	{
		FDecoSelectionCheckData CheckValueData;
		CheckValueData.selection_id = Ite.Value->GetDataRef().id;
		CheckValueData.content_id = CurrentOptionID;
		CheckValueData.style_id = CurrentStyleID;
		CheckValueData.is_prime = Ite.Value->GetIsPrime();

#ifdef USE_REF_LOCAL_FILE

		if (IsChecked)
		{
			//DeleteDecoSelectionCheck(CheckValueData, false);
			if(!IsDecoSelectionCheck(CheckValueData))
			   CreateDecoSelectionCheck(CheckValueData,false);
		}
		else
		{
			DeleteDecoSelectionCheck(CheckValueData, false);
		}

#else


		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("SELECT * FROM decorate_selch ORDER BY ID ASC"), checks);
		if (IsChecked)
		{
			if (checks.IsEmpty())
			{
				CheckValueData.id = 1;
				CreateDecoSelectionCheck(CheckValueData, false);
			}
			else
			{
				CheckValueData.id = checks.Last().id + 1;
				CreateDecoSelectionCheck(CheckValueData, false);
			}
		}
		else
		{
			DeleteDecoSelectionCheck(CheckValueData, false);
		}

#endif
		//Ite.Value->SetCheck(IsChecked);
	}

	SearchDecoSelection(CurrentOptionID);
}

void UValueWidget::ResetOperateState()
{
	Super::ResetOperateState();
	if (IS_OBJECT_PTR_VALID(BtnAdd))
	{
		BtnAdd->SetIsEnabled(false);
	}
	if (IS_OBJECT_PTR_VALID(BtnParaAdd) && IS_OBJECT_PTR_VALID(BtnParaDelete))
	{
		BtnParaAdd->SetIsEnabled(false);
		BtnParaDelete->SetIsEnabled(false);
	}
}

void UValueWidget::ResetSelectState()
{
	CurrentStyleID = TEXT("-1");
	CurrentOptionID = TEXT("-1");
	ResetOperateState();
	if (SubValuesMap.Contains(SelectValueID))
	{
		SubValuesMap[SelectValueID]->SetSelectState(false);
	}
	if (SubParamsMap.Contains(SelectParamID))
	{
		SubParamsMap[SelectParamID]->SetSelectState(false);
	}
	CB_All->SetIsChecked(false);
}

void UValueWidget::UpdateOperateState(const ESubItemOrderType& InType)
{
	Super::UpdateOperateState(InType);
}

void UValueWidget::StyleWidgetOperate(const EWidgetOperateType& InType)
{
	if (InType == EWidgetOperateType::Add)
	{
		FDecorateSelection NewStyleValue;
		ConstructNewData(NewStyleValue, !SelectValueID.Equals(TEXT("-1")));
	}
	else if (InType == EWidgetOperateType::Del)
	{
		bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete selected value?")).ToString());
		if (!Res)
		{
			return;
		}
		if (SelectValueID.Equals(TEXT("-1")))
		{
			return;
		}
		SubValuesMap[SelectValueID]->DeleteCurrentSelection();
		UpdateOperateState(ESubItemOrderType::NoneSelect);
		/*if (UDecorateSelectionOperatorLibrary::DeleteDecorateSelection(SelectValueID)
			&& UDecoParamFunctionLibrary::DeleteDecoParamBySelectionID(SelectValueID))
		{
			SubValuesMap[SelectValueID]->SetVisibility(ESlateVisibility::Collapsed);
			SBItem->RemoveChild(SubValuesMap[SelectValueID]);
			SubValuesMap.Remove(SelectValueID);
			SelectValueID = -1;
			SBPara->ClearChildren();
			UpdateOperateState(ESubItemOrderType::NoneSelect);
			UpdateValueParamOperateState(EValueParamOperateState::NoValueNoParam);
		}*/
	}
	else if (InType == EWidgetOperateType::Up)
	{
		ValueSubItemMoveAction(SelectValueID, true);
	}
	else if (InType == EWidgetOperateType::Down)
	{
		ValueSubItemMoveAction(SelectValueID, false);
	}
}

void UValueWidget::UpdateValueParamOperateState(const EValueParamOperateState& InType)
{
	if (IS_OBJECT_PTR_VALID(BtnParaAdd) && IS_OBJECT_PTR_VALID(BtnParaDelete))
	{
		BtnParaAdd->SetIsEnabled(InType != EValueParamOperateState::NoValueNoParam);
		BtnParaDelete->SetIsEnabled(InType == EValueParamOperateState::ValueParam);
	}
}

void UValueWidget::StyleValueParamOperate(const EValueParamOperateType& InType)
{
	if (InType == EValueParamOperateType::Add)
	{
		FParameterData NewData;
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;
		UParameterDetailWidget::Get()->UpdateContent(NewData, 4);
		UParameterDetailWidget::Get()->SetFolderOrFileParentParams(ParentParameters);
		//UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(this, FName(TEXT("OnParamAddHandler")));
		UParameterDetailWidget::Get()->SelectedParamUpdateDatasDelegate.BindUFunction(this, FName(TEXT("OnParamAddHandler02")));
		UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
		UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
		UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	}
	else if (InType == EValueParamOperateType::Del)
	{
		bool Res = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(), FText::FromStringTable(FName("PosSt"), TEXT("Do you want to delete selected param?")).ToString());
		if (!Res)
		{
			return;
		}
		if (SelectParamID.Equals(TEXT("-1")))
		{
			return;
		}
		const auto SelectedParameter = SubParamsMap[SelectParamID]->GetData();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
		//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
		TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
		UParameterRelativeLibrary::CombineParameters(PeerParameters, CurrentParameters);
		if (PeerParameters.Contains(SelectedParameter.Data.name))
		{
			PeerParameters.Remove(SelectedParameter.Data.name);
		}
		UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, ParentParameters, PeerParameters);
		for (const auto& iter : PeerParameters)
		{
			if (SubParamsMap.Contains(PeerParameters[iter.Value.Data.name].Data.id))
			{
				SubParamsMap[PeerParameters[iter.Value.Data.name].Data.id]->UpdateContent(PeerParameters[iter.Value.Data.name]);
				SubParamsMap[PeerParameters[iter.Value.Data.name].Data.id]->UpdateCurrentParam(PeerParameters[iter.Value.Data.name]);
#ifdef USE_REF_LOCAL_FILE
				UpdateParam(PeerParameters[iter.Value.Data.name]);
#endif
			}
		}

		OptionParamsUpdate();
		OptionVisibilityUpdate_Current();

		/*TMap<FString, FParameterData> PeerParameters;
		/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;

		UParameterRelativeLibrary::CombineParameters(PeerParameters, CurrentParameters);
		const auto SelectedParameter = SubParamsMap[SelectParamID]->GetData();
		TArray<FString> AffectedParameters;
		FParameterEffectionParser Parser;
		Res = Parser.FindParametersAffectBySpecificParameter(PeerParameters, SelectedParameter.Data.name, AffectedParameters, false);
		if (Res)
		{
			PeerParameters.Remove(SelectedParameter.Data.name);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			while (AffectedParameters.Num() > 0)
			{
				const FString ParameterName = AffectedParameters.Top();
				AffectedParameters.Pop();
				if (false == PeerParameters.Contains(ParameterName)) continue;
				auto ParameterExcludeSelf = PeerParameters;
				ParameterExcludeSelf.Remove(ParameterName);
				bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParameterExcludeSelf, PeerParameters[ParameterName]);
				if (!Res)
				{
					UE_LOG(LogTemp, Error, TEXT("UValueWidget::StyleValueParamOperate parameter[%s] affect [%s] calculate failed"), *SelectedParameter.Data.name, *ParameterName);
					continue;
				}
				if (SubParamsMap.Contains(PeerParameters[ParameterName].Data.id))
				{
					SubParamsMap[PeerParameters[ParameterName].Data.id]->UpdateContent(PeerParameters[ParameterName]);
					SubParamsMap[PeerParameters[ParameterName].Data.id]->UpdateCurrentParam(PeerParameters[ParameterName]);
				}
			}
		}*/
		SubParamsMap[SelectParamID]->DeleteCurrentParam();
#ifdef USE_REF_LOCAL_FILE
		DeleteParam(SubParamsMap[SelectParamID]->GetData());
#endif
		SubParamsMap.Remove(SelectParamID);
		SelectParamID = TEXT("-1");
		BtnParaDelete->SetIsEnabled(false);
		FDecorateSelection ChangedValue = SubValuesMap[SelectValueID]->GetData();
		//ValueVisibilityExpCalibration(ChangedValue.id, ChangedValue.visibility_exp, ChangedValue.visibility);
		SubValuesMap[SelectValueID]->UpdateCurrentSelection(ChangedValue);
	}
}

void UValueWidget::SetBtnAddEnable(bool IsEnable)
{
	if (IS_OBJECT_PTR_VALID(BtnAdd))
	{
		BtnAdd->SetIsEnabled(IsEnable);
	}
}

void UValueWidget::RenewSelection()
{
	SearchDecoSelection(CurrentOptionID);
}

void UValueWidget::AutoPrimeCheckProcess(const FString& MayPrimeID)
{
	bool PrimeExistFlag = false;
	for (auto iter : SubValuesMap)
	{
		if (!iter.Value->GetIsPrime()) continue;
		PrimeExistFlag = true;
	}

	if (PrimeExistFlag) return;
	SetSubItemPrime(MayPrimeID, true);
	UpdataSelectionCheck(MayPrimeID, CurrentOptionID, CurrentStyleID, true);
}

/*
*  @@ EditType : option[Value] prime edit type
*  @@ 0 : delete; 1 : change
*/
void UValueWidget::DefaultPrimeManager(const FString& ChangedID, const int32& EditType)
{
	FRefToStyleFile& StyleRefData = UFolderWidget::Get()->GetStyleRefDataRef();
	const int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool
		{
			return InData.content_id.Equals(CurrentOptionID, ESearchCase::IgnoreCase);
		}
	);
	if (ContentIndex != INDEX_NONE)
	{
		FRefToContentData& EditContent = StyleRefData.content_datas[ContentIndex];
		if (EditContent.style_option_checks.Contains(CurrentStyleID))
		{
			TArray<FRefToOptionCheck>& CurrentChecks = EditContent.style_option_checks[CurrentStyleID].option_checks;
			const int32 CheckIndex = CurrentChecks.IndexOfByPredicate(
				[ChangedID](const FRefToOptionCheck& InCheck)->bool
				{
					return InCheck.option_id.Equals(ChangedID, ESearchCase::IgnoreCase);
				}
			);
			if (EditType == 0)
			{
				if (CheckIndex != INDEX_NONE)
				{
					CurrentChecks.RemoveAt(CheckIndex);
				}
				/*if (CurrentChecks.Num() > 0)
				{
					CurrentChecks[0].is_prime = true;
					if (SubValuesMap.Contains(CurrentChecks[0].option_id))
					{
						SubValuesMap[CurrentChecks[0].option_id]->SetIsPrime(true);
					}
				}*/
			}
			else if (EditType == 1)
			{
				if (CheckIndex != INDEX_NONE && SubValuesMap.Contains(ChangedID))
				{
					CurrentChecks[CheckIndex].is_prime = false;
					SubValuesMap[ChangedID]->SetIsPrime(false);
				}

				/*if (CheckIndex > 0)
				{
					CurrentChecks[0].is_prime = true;
					if (SubValuesMap.Contains(CurrentChecks[0].option_id))
					{
						SubValuesMap[CurrentChecks[0].option_id]->SetIsPrime(true);
					}
				}
				else if (CheckIndex == 0)
				{
					if (CurrentChecks.IsValidIndex(1))
					{
						CurrentChecks[1].is_prime = true;
						if (SubValuesMap.Contains(CurrentChecks[1].option_id))
						{
							SubValuesMap[CurrentChecks[1].option_id]->SetIsPrime(true);
						}
					}
				}*/
			}

			EditContent.CheckStyleOptionState(CurrentStyleID);
			const int32 NewCheckIndex = CurrentChecks.IndexOfByPredicate(
				[](const FRefToOptionCheck& InCheck)->bool
				{
					return InCheck.is_prime;
				}
			);
			if (NewCheckIndex != INDEX_NONE && SubValuesMap.Contains(CurrentChecks[NewCheckIndex].option_id))
			{
				SubValuesMap[CurrentChecks[NewCheckIndex].option_id]->SetIsPrime(true);
			}
		}
	}

	/*bool PrimeExistFlag = false;
	for (auto iter : SubValuesMap)
	{
		if (iter.Value->GetIsPrime())
		{
			if (iter.Value->IsVaildForPrime())
			{
				PrimeExistFlag = true;
			}
			else
			{
				PrimeExistFlag = false;
				SetSubItemPrime(iter.Key, false);
				UpdataSelectionCheck(iter.Key, CurrentOptionID, CurrentStyleID, false);
			}
		}
	}
	if (PrimeExistFlag) return;
	for (auto NewIter : SubValuesMap)
	{
		if (NewIter.Value->IsVaildForPrime())
		{
			SetSubItemPrime(NewIter.Key, true);
			UpdataSelectionCheck(NewIter.Key, CurrentOptionID, CurrentStyleID, true);
			break;
		}
	}*/
}

void UValueWidget::UpdateAllOptionData(const FString& InContentID, const FString& InStyleID)
{
	if (InContentID.IsEmpty() || InStyleID.IsEmpty())
		return;

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[InContentID](const FRefToContentData& InData)->bool { return InData.content_id.Equals(InContentID); }
	);
	if (Index != INDEX_NONE)
	{
		FRefToContentData& ContentRefData = StyleRefData.content_datas[Index];
		for (int32 i = 0; i < ContentRefData.option_datas.Num(); ++i)
		{
			FRefToOptionData& OptionData = ContentRefData.option_datas[i];

			TArray<FParameterData> OtherContentPrimeParams;
			GetOtherOptionParams_Prime(InStyleID, InContentID, OtherContentPrimeParams);

			//calculate current option params
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
			UParameterRelativeLibrary::CombineParameters(ParentParameters, OtherContentPrimeParams);

			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  CurParametersMap;
			UParameterRelativeLibrary::CombineParameters(CurParametersMap, OptionData.option_params);
			UParameterRelativeLibrary::CalculateParameterValue_LevelSort(
				ACatalogPlayerController::Get()->GetGlobalParameterMap(),
				ParentParameters,
				CurParametersMap
			);

			for (auto& Param : OptionData.option_params)
			{
				if (CurParametersMap.Contains(Param.Data.name))
				{
					Param = CurParametersMap[Param.Data.name];
				}
			}

			//calculate current option visibility if is expression
			if (!OptionData.option_visibility_exp.IsNumeric())
			{
				//UParameterRelativeLibrary::CombineParameters(ParentParameters, OptionData.option_params);
				auto LocalParamMap = URefRelationFunction::ConvertParamsArrayToMap(OptionData.option_params);

				FString TempValue = FString();
				FString OutFormatExpress = FString();
				bool Res = UUIFunctionLibrary::CalculateExpressionValue(ParentParameters, LocalParamMap,OptionData.option_visibility_exp, TempValue, OutFormatExpress);
				if (Res)
				{
					OptionData.option_visibility_exp = OutFormatExpress;
					UUIFunctionLibrary::FormatInputValue(TempValue);
					OptionData.option_visibility = TempValue;

					//check state 
					const double TempValueDouble = FCString::Atod(*TempValue);
					if (FMath::IsNearlyZero(TempValueDouble, 0.01) && ContentRefData.style_option_checks.Contains(InStyleID))
					{
						TArray<FRefToOptionCheck>& OptionChecks = ContentRefData.style_option_checks[InStyleID].option_checks;
						const int32 EditOptionCheckIndex = OptionChecks.IndexOfByPredicate(
							[OptionData, InContentID, InStyleID](const FRefToOptionCheck& InCheck)->bool
							{
								return InCheck.option_id.Equals(OptionData.option_id, ESearchCase::IgnoreCase) 
									&& InCheck.content_id.Equals(InContentID, ESearchCase::IgnoreCase)
									&& InCheck.style_id.Equals(InStyleID, ESearchCase::IgnoreCase);
							}
						);
						if (EditOptionCheckIndex != INDEX_NONE && OptionChecks[EditOptionCheckIndex].is_prime)
						{
							OptionChecks[EditOptionCheckIndex].is_prime = false;
							ContentRefData.CheckStyleOptionState(InStyleID);
							/*if (EditOptionCheckIndex == 0 && OptionChecks.IsValidIndex(1))
							{
								OptionChecks[1].is_prime = true;
							}
							else
							{
								OptionChecks[0].is_prime = true;
							}*/
						}
					}
				}
			}
		}
	}
}

/*
 *  @@ 复制
 *	@@ InsertID ： 原始ID; ValueData ： 复制的数据
 */
void UValueWidget::InsertValue(const FString& InsertID, FDecorateSelection& ValueData)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		const int32 OptionIndex = StyleRefData.content_datas[Index].option_datas.IndexOfByPredicate(
			[InsertID](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(InsertID); }
		);
		auto& ContentData = StyleRefData.content_datas[Index];
		if (OptionIndex != INDEX_NONE)
		{
			auto& OptionArr = ContentData.option_datas;

			//option data
			ValueData.id = FGuid::NewGuid().ToString().ToLower();
			FRefToOptionData Temp;
			PARSE_OPTION_ACTION_DATA_TO_REF_DATA(ValueData, Temp);
			Temp.option_content_id = ContentData.content_id;
			int32 NewIndex = OptionArr.Insert(Temp, OptionIndex + 1);
			ContentData.ReSortOption();

			//params
			auto Params = OptionArr[OptionIndex].option_params;
			for (int32 i = 0; i < Params.Num(); ++i)
			{
				Params[i].Data.main_id = ValueData.id;
				Params[i].Data.id = FGuid::NewGuid().ToString().ToLower();
				OptionArr[NewIndex].option_params.Add(Params[i]);
			}
		}
	}

	SearchDecoSelection(CurrentOptionID);
	if (SubValuesMap.Contains(ValueData.id))
		SubValuesMap[ValueData.id]->SelectSelf();

#else
	//值粘贴的逻辑
	//获取当前选项所有值
	UE_LOG(LogTemp, Error, TEXT("1"));
	TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel where PARENT_ID = '%s' ORDER BY SORT_ORDER ASC"), *CurrentOptionID), OutSelection);
	//获取当前值所有参数
	UE_LOG(LogTemp, Error, TEXT("2"));
	TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
	TArray<FEnumParameterTableData> OutDecoParamsEnum = TArray<FEnumParameterTableData>();
	TArray<FParameterData> OutDecoParams = TArray<FParameterData>();
	FString sqlcommand = FString::Printf(TEXT("select * from decorate_param where MAIN_ID = '%s'"), *ValueData.id);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(sqlcommand, OutParamsTableData);
	for (auto iter : OutParamsTableData)
	{
		FParameterData Temp = FParameterData();
		Temp.Data = iter;
		if (iter.is_enum)
		{
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_param_enum where MAIN_ID = '%s'"), *iter.id), OutDecoParamsEnum);
			Temp.EnumData = OutDecoParamsEnum;
			OutDecoParamsEnum.Empty();
		}
		OutDecoParams.Add(Temp);
	}
	//整理现有的所有排序
	UE_LOG(LogTemp, Error, TEXT("3"));
	int32 NewOrder = 1;
	int32 CopyTargetIndex = 0;
	for (auto& iter : OutSelection)
	{
		iter.sort_order = NewOrder;
		if (iter.id == SelectValueID)
		{
			NewOrder++;//保留位置
			CopyTargetIndex = NewOrder;
		}
		NewOrder++;
	}
	//配置新参数的UUID
	UE_LOG(LogTemp, Error, TEXT("4"));
	FDecorateSelection NewCopySelection = FDecorateSelection();
	NewCopySelection = ValueData;
	NewCopySelection.id = FGuid::NewGuid().ToString().ToLower();
	NewCopySelection.sort_order = CopyTargetIndex;
	OutSelection.Insert(NewCopySelection, CopyTargetIndex - 1);
	//编辑参数的UUID自身与父类
	UE_LOG(LogTemp, Error, TEXT("5"));
	for (auto& Piter : OutDecoParams)
	{
		Piter.Data.id = FGuid::NewGuid().ToString().ToLower();
		Piter.Data.main_id = NewCopySelection.id;
		for (auto& Eiter : Piter.EnumData)
		{
			Eiter.id = FGuid::NewGuid().ToString().ToLower();
			Eiter.main_id = Piter.Data.id;
		}
	}
	//插入新的值,更新旧值
	UE_LOG(LogTemp, Error, TEXT("6"));
	CreateDecoSelection(NewCopySelection, TEXT("-1"));
	UpdateDecoSelections(OutSelection);
	//插入新的参数和枚举
	UE_LOG(LogTemp, Error, TEXT("7"));
	for (auto Paiter : OutDecoParams)
	{
		CreateSelectionParam(Paiter);
	}
	if (SubValuesMap.Contains(NewCopySelection.id))
		SubValuesMap[NewCopySelection.id]->SelectSelf();
#endif
}

void UValueWidget::UpdateContent(const TArray<FDecorateSelection>& Items)
{
	SBItem->ClearChildren();
	SubValuesMap.Empty();
	SelectValueID = TEXT("-1");
	SBPara->ClearChildren();
	SubParamsMap.Empty();
	SelectParamID = TEXT("-1");
	bool bCanChecked = !CurrentStyleID.Equals(TEXT("-1"));
	for (auto& iter : Items)
	{
		UValueWidgetChild* Child = UValueWidgetChild::Create();
		Child->SelectValueDelegate.BindUFunction(this, FName(TEXT("OnValueItemSelectHandler")));
		Child->ValueCheckDelegate.BindUFunction(this, FName(TEXT("OnValueCheckChangeHandler")));
		Child->DeleteValueDelegate.BindUFunction(this, FName(TEXT("OnDeleteValueHandler")));
		Child->ValueVisibilityExpEditDelegatep.BindUFunction(this, FName(TEXT("OnValueVisibilityEditHandler")));
		Child->AutoPrimeDelegate.BindUFunction(this, FName(TEXT("AutoPrimeCheckProcess")));
		Child->DefaultPrimeDelegate.BindUFunction(this, FName(TEXT("DefaultPrimeManager")));
		//Child->SetContent(iter);
		Child->SetSyncData(iter);
		Child->SetVisibility(ESlateVisibility::Visible);
		Child->SetCheckEnable(bCanChecked);
		SBItem->AddChild(Child);
		SubValuesMap.Add(iter.id, Child);
	}
	SearchDecoSelectionCheck(CurrentOptionID, CurrentStyleID);
}

void UValueWidget::UpdateValueParam(const TArray<FParameterData>& ParamItems)
{
	SBPara->ClearChildren();
	SubParamsMap.Empty();
	bool IdExist = false;
	for (auto& ParamData : ParamItems)
	{
		UParamaterWidgetChild* ParamItem = UParamaterWidgetChild::Create();
		ParamItem->UpdateContent(ParamData);
		ParamItem->ValueParamDelegate.BindUFunction(this, FName(TEXT("OnParamItemSelectHandler")));
		ParamItem->ParameterChanged.BindUFunction(this, FName(TEXT("OnParameterChangedHandler")));
		ParamItem->ParameterPage.BindUFunction(this, FName(TEXT("OnParameterPageHandler")));
		ParamItem->SetVisibility(ESlateVisibility::Visible);
		SBPara->AddChild(ParamItem);
		SubParamsMap.Add(ParamData.Data.id, ParamItem);
		if (ParamData.Data.id.Equals(SelectParamID))
		{
			IdExist = true;
		}
		//FLocalDatabaseParameterLibrary::UpdateStyleParameter(ParamData);
	}
	CurrentParameters = ParamItems;
	if (!IdExist)
	{
		SelectParamID = TEXT("-1");
	}
	else if (!SelectParamID.Equals(TEXT("-1")))
	{
		SubParamsMap[SelectParamID]->SetSelectState(true);
		BtnParaDelete->SetIsEnabled(true);
	}
	if (!CurrentStyleID.Equals(TEXT("-1")))
	{
		if (ParamItems.Num() > 0 && IdExist)
		{
			UpdateValueParamOperateState(EValueParamOperateState::ValueParam);
		}
		else if (SelectValueID.Equals(TEXT("-1")))
		{
			UpdateValueParamOperateState(EValueParamOperateState::NoValueNoParam);
		}
		else
		{
			UpdateValueParamOperateState(EValueParamOperateState::ValueNoParam);
		}

	}
	else
	{
		UpdateValueParamOperateState(EValueParamOperateState::NoValueNoParam);
	}
}

void UValueWidget::RefreshContent(const TArray<FDecorateSelection>& Items, const FString& ValueID)
{
	SBItem->ClearChildren();
	SubValuesMap.Empty();
	int32 SelectOrder = -1;
	bool bCanChecked = !CurrentStyleID.Equals(TEXT("-1"));
	for (auto& iter : Items)
	{
		UValueWidgetChild* Child = UValueWidgetChild::Create();
		Child->SelectValueDelegate.BindUFunction(this, FName(TEXT("OnValueItemSelectHandler")));
		Child->ValueCheckDelegate.BindUFunction(this, FName(TEXT("OnValueCheckChangeHandler")));
		Child->DeleteValueDelegate.BindUFunction(this, FName(TEXT("OnDeleteValueHandler")));
		Child->ValueVisibilityExpEditDelegatep.BindUFunction(this, FName(TEXT("OnValueVisibilityEditHandler")));
		Child->AutoPrimeDelegate.BindUFunction(this, FName(TEXT("AutoPrimeCheckProcess")));
		Child->DefaultPrimeDelegate.BindUFunction(this, FName(TEXT("DefaultPrimeManager")));
		Child->SetContent(iter);
		Child->SetVisibility(ESlateVisibility::Visible);
		Child->SetCheckEnable(bCanChecked);
		SBItem->AddChild(Child);
		SubValuesMap.Add(iter.id, Child);
		if (ValueID == iter.id)
		{
			Child->SetSelectState(true);
			SelectOrder = iter.sort_order;
			OnValueItemSelectHandler(SubValuesMap[ValueID], true);
		}
	}
	SearchDecoSelectionCheck(CurrentOptionID, CurrentStyleID);
	/*TArray<FDecoSelectionCheckData> OutData;
	if (UDecoSelectionCheckLibrary::GetCheckSelectionByContentAndStyleID(CurrentOptionID, CurrentStyleID, OutData))
	{
		for (auto& CheckData : OutData)
		{
			SubValuesMap[CheckData.selection_id]->SetIsCheck(true, CheckData.is_prime);
		}
	}*/
	UpdateStyleOperateState<UValueWidgetChild>(SelectOrder, SubValuesMap);
}

void UValueWidget::UpdateValueContent(const FString& StyleID, const FString& OptionID)
{
	ResetOperateState();
	CurrentStyleID = StyleID;
	if (CurrentOptionID != OptionID)
	{
		SelectValueID = TEXT("-1");
		CurrentOptionID = OptionID;
	}
	if (OptionID.Equals(TEXT("-1")))
	{
		ClearValueContent();
	}
	else
	{
		SetBtnAddEnable(true);
		SearchDecoSelection(OptionID);
	}
}

void UValueWidget::SetSubItemPrime(const FString& ItemID, bool IsPrime)
{

	GET_STYLE_REF_FILE_DATA_REF();
	const int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& ContentItem)->bool
		{
			return ContentItem.content_id.Equals(CurrentOptionID, ESearchCase::IgnoreCase);
		}
	);
	if (ContentIndex != INDEX_NONE)
	{
		TMap<FString, FOptionCheckArr>& EditSOC = StyleRefData.content_datas[ContentIndex].style_option_checks;
		if (EditSOC.Contains(CurrentStyleID))
		{
			for (auto& CheckIter : EditSOC[CurrentStyleID].option_checks)
			{
				CheckIter.is_prime = (CheckIter.option_id.Equals(ItemID, ESearchCase::IgnoreCase));
			}
		}
	}
	SearchDecoSelectionCheck(CurrentOptionID, CurrentStyleID);

	/*for (auto& iter : CheckDatas)
	{
		if (iter.selection_id.Equals(ItemID, ESearchCase::IgnoreCase))
		{
			iter.is_prime = IsPrime;
		}
		else
		{
			iter.is_prime = false;
		}
	}
	UpdateDecoSelectionCheck(CheckDatas);*/
}

void UValueWidget::ClearContent()
{
	if (IS_OBJECT_PTR_VALID(SBItem))
	{
		SBItem->ClearChildren();
	}
	if (IS_OBJECT_PTR_VALID(SBPara))
	{
		SBPara->ClearChildren();
	}
}

void UValueWidget::ClearParamContent()
{
	if (IS_OBJECT_PTR_VALID(SBPara))
	{
		SBPara->ClearChildren();
		SubParamsMap.Empty();
		SelectParamID = TEXT("-1");
		BtnParaDelete->SetIsEnabled(false);
	}
	for (TMap<FString, UValueWidgetChild*>::TConstIterator Iter = SubValuesMap.CreateConstIterator(); Iter; ++Iter)
	{
		Iter->Value->SetIsCheck(false, false);
	}
}

void UValueWidget::ClearValueContent()
{
	if (IS_OBJECT_PTR_VALID(SBItem))
	{
		SBItem->ClearChildren();
	}
	SubValuesMap.Empty();
	SelectValueID = TEXT("-1");
	SubParamsMap.Empty();
	SelectParamID = TEXT("-1");
	BtnParaDelete->SetIsEnabled(false);
}

bool UValueWidget::ValueVisibilityExpCalibration(const FString& InSelectionID, const FString& InExpression, FString& OutValue)
{
	UE_LOG(LogTemp, Error, TEXT("ValueVisibilityExpcalibration begin"));

#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	FString CurrentSelectOptionID = CurrentOptionID;
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[CurrentSelectOptionID](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentSelectOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		for (const auto Iter : StyleRefData.content_datas[Index].option_datas)
		{
			if (Iter.option_id.Equals(InSelectionID))
			{
				CurrentParameters = Iter.option_params;
				break;
			}
		}
	}

#else
	//按优先级获取参数值	
	TArray<FParameterData> CurParameters = TArray<FParameterData>();
	TArray<FParameterData> CurParams = TArray<FParameterData>();
	TArray<FParameterTableData> CurTableData = TArray<FParameterTableData>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *InSelectionID), CurTableData);
	for (auto TableDataIter : CurTableData)
	{
		FParameterData Temp = FParameterData();
		TArray<FEnumParameterTableData> OutEnumData = TArray<FEnumParameterTableData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *TableDataIter.id), OutEnumData);
		Temp.Data = TableDataIter;
		Temp.EnumData = OutEnumData;
		OutEnumData.Empty();
		CurParams.Add(Temp);
	}
	CurParameters.Append(CurParams);
	CurrentParameters = CurParameters;
#endif
	UE_LOG(LogTemp, Error, TEXT("ValueVisibilityExpcalibration CurrentValueParamter = %d"), CurrentParameters.Num());


	TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
	TArray<FDecoSelectionCheckData> VaildSelectionChecks;

#ifdef USE_REF_LOCAL_FILE

	for (const auto Iter : StyleRefData.content_datas)
	{
		if (Iter.content_id.Equals(CurrentOptionID))
		{
			for (const auto StyleOptionCheckIter : Iter.style_option_checks)
			{
				if (CurrentStyleID.Equals(TEXT("-1")) || StyleOptionCheckIter.Key.Equals(CurrentStyleID))
				{
					for (const auto CheckArrIter : StyleOptionCheckIter.Value.option_checks)
					{
						if (CheckArrIter.is_prime)
						{
							auto& Temp = VaildSelectionChecks.AddDefaulted_GetRef();
							Temp.selection_id = CheckArrIter.option_id;
							Temp.content_id = CheckArrIter.content_id;
							Temp.style_id = CheckArrIter.style_id;
							Temp.is_prime = CheckArrIter.is_prime;
							break;
						}
					}
				}
			}
			break;
		}
	}

#else
	if (CurrentStyleID.Equals(TEXT("-1")))
	{
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch")), OutDecoSelectionChecks);
	}
	else
	{
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch WHERE STYLE_ID = '%s'"), *CurrentStyleID), OutDecoSelectionChecks);
	}

	for (auto SelectIter : OutDecoSelectionChecks)
	{//排除非优先显示的值
		if (SelectIter.is_prime == 0) continue;
		VaildSelectionChecks.Add(SelectIter);
	}
#endif

	TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();

#ifdef USE_REF_LOCAL_FILE

	TArray<FParameterData> PrimeParameters = TArray<FParameterData>();
	if (Index != INDEX_NONE)
	{
		for (const auto CheckIter : VaildSelectionChecks)
		{
			int32 Index_Check = StyleRefData.content_datas[Index].option_datas.IndexOfByPredicate(
				[CheckIter](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(CheckIter.selection_id); }
			);
			if (Index_Check != INDEX_NONE)
			{
				auto& Temp = OutSelection.AddDefaulted_GetRef();
				PARSE_OPTION_REF_DATA_TO_ACTION_DATA(Temp, StyleRefData.content_datas[Index].option_datas[Index_Check]);

				PrimeParameters.Append(StyleRefData.content_datas[Index].option_datas[Index_Check].option_params);
			}
		}
	}

#else
	for (auto CheckIter : VaildSelectionChecks)
	{
		TArray<FDecorateSelection> TempSelections = TArray<FDecorateSelection>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel WHERE ID = '%s' AND PARENT_ID = '%s'"), *CheckIter.selection_id, *CheckIter.content_id), TempSelections);
		//UE_LOG(LogTemp, Error, TEXT("ValueVisibilityExpcalibration TempSelections Num = %d"), TempSelections.Num());
		OutSelection.Append(TempSelections);
	}


	TArray<FParameterData> PrimeParameters = TArray<FParameterData>();
	for (auto SelectIter : OutSelection)
	{
		TArray<FParameterData> OurParams = TArray<FParameterData>();
		TArray<FParameterTableData> OutTableData = TArray<FParameterTableData>();
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *SelectIter.id), OutTableData);
		for (auto TableDataIter : OutTableData)
		{
			FParameterData Temp = FParameterData();
			TArray<FEnumParameterTableData> OutEnumData = TArray<FEnumParameterTableData>();
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *TableDataIter.id), OutEnumData);
			Temp.Data = TableDataIter;
			Temp.EnumData = OutEnumData;
			OutEnumData.Empty();
			OurParams.Add(Temp);
		}
		PrimeParameters.Append(OurParams);
	}
#endif

	//表达式计算
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(PeerParameters, CurrentParameters);
	UParameterRelativeLibrary::CombineFirstParameters(PeerParameters, PrimeParameters);
	FString TempValue = FString();
	FString OutFormatExpress = FString();

	auto GlobalParam = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	bool Res = UUIFunctionLibrary::CalculateExpressionValue(GlobalParam,PeerParameters, InExpression, TempValue, OutFormatExpress);
	if (Res)
	{
		UUIFunctionLibrary::FormatInputValue(OutValue);
		UUIFunctionLibrary::FormatInputValue(TempValue);
		if (!TempValue.Equals(OutValue))
		{
			OutValue = TempValue;
			return true;
		}
	}
	return false;
}

bool UValueWidget::UpdataSelectionCheck(const FString& InSelectionID, const FString& InContentID, const FString& InStyleID, const bool IsPrime)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	const int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[InContentID](const FRefToContentData& InData)->bool { return InData.content_id.Equals(InContentID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		if (ContentData.style_option_checks.Contains(InStyleID))
		{
			for (auto& Iter : ContentData.style_option_checks[InStyleID].option_checks)
			{
				if (Iter.option_id.Equals(InSelectionID))
				{
					Iter.is_prime = IsPrime;
					return true;
				}
			}
		}
	}

#else
	TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
	FLocalDatabaseOperatorLibrary::SelectDataFromServerDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch WHERE SELECTION_ID = '%s' AND CONTENT_ID = '%s' AND STYLE_ID = '%s'"), *InSelectionID, *InContentID, *InStyleID), OutDecoSelectionChecks);
	if (OutDecoSelectionChecks.Num() > 0)
	{
		FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_selch set SELECTION_ID = '%s' , CONTENT_ID = '%s' ,STYLE_ID = '%s', IS_PRIME= '%d'  where ID = '%d'"), *OutDecoSelectionChecks[0].selection_id, *OutDecoSelectionChecks[0].content_id, *OutDecoSelectionChecks[0].style_id, IsPrime, OutDecoSelectionChecks[0].id));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("UpdataSelectionCheck Failed"));
	}
#endif
	return false;
}

bool UValueWidget::AllChildrenChecked()
{
	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[&](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		if (ContentData.style_option_checks.Contains(CurrentStyleID))
		{
			int32 CheckNum = 0;
			for (auto& OptionIte : ContentData.option_datas)
			{
				if (ContentData.style_option_checks[CurrentStyleID].option_checks.ContainsByPredicate([&OptionIte](const FRefToOptionCheck& InCheck) {

					return InCheck.option_id.Equals(OptionIte.option_id);

					}))
				{
					CheckNum++;
				}
			}

			return CheckNum == ContentData.option_datas.Num();
		}
	}

	return false;
}

void UValueWidget::AddParam(const FParameterData& Param)
{
	/*
	*  @@ only calculate params and visibility
	*/
	OptionParamsUpdate();
	OptionVisibilityUpdate_Current();
}

void UValueWidget::UpdateParam(const FParameterData& Param)
{
	GET_STYLE_REF_FILE_DATA_REF();
	const int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		const int32 OptionIndex = ContentData.option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
		);
		for (auto& Iter : ContentData.option_datas[OptionIndex].option_params)
		{
			if (Iter.Data.name.Equals(Param.Data.name,ESearchCase::CaseSensitive))
			{
				Iter = Param;
				break;
			}

		}
	}
}

void UValueWidget::DeleteParam(const FParameterData& Param)
{
	GET_STYLE_REF_FILE_DATA_REF();
	const int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		const int32 OptionIndex = ContentData.option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
		);
		for (auto Iter = ContentData.option_datas[OptionIndex].option_params.CreateIterator(); Iter; ++Iter)
		{
			if (Iter->Data.name.Equals(Param.Data.name, ESearchCase::CaseSensitive))
			{
				Iter.RemoveCurrent();
				break;
			}

		}
	}

	OptionParamsUpdate();
	OptionVisibilityUpdate_Current();
}

UValueWidget* UValueWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UValueWidget>(UValueWidget::ValueWidgetPath);
}

void UValueWidget::SearchDecoSelection(const FString& OptionId)
{
	TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();

#ifdef USE_REF_LOCAL_FILE

	UpdateAllOptionData(OptionId, CurrentStyleID);

	GET_STYLE_REF_FILE_DATA();
	FString CurID = OptionId.IsEmpty() ? CurrentOptionID : OptionId;
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[CurID](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurID); }
	);
	if (Index != INDEX_NONE)
	{

		for (const auto Iter : StyleRefData.content_datas[Index].option_datas)
		{
			auto& Temp = OutSelection.AddDefaulted_GetRef();
			PARSE_OPTION_REF_DATA_TO_ACTION_DATA(Temp, Iter);

		}
	}

#else
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel where PARENT_ID = '%s' ORDER BY SORT_ORDER ASC"), *OptionId), OutSelection);
#endif

	for (auto& iter : OutSelection)
	{
		if (iter.visibility_exp.IsEmpty())
		{
			iter.visibility_exp = TEXT("1");
			iter.visibility = TEXT("1");
		}
		if (iter.visibility_exp.IsNumeric())
		{
			iter.visibility = iter.visibility_exp;
		}

#ifdef  USE_REF_LOCAL_FILE

		//bool Res = ValueVisibilityExpCalibration(iter.id, iter.visibility_exp, iter.visibility);

#else
		TArray<FFolderTableData> OutParams = TArray<FFolderTableData>();
		FString SearchCode = iter.code;
		if (!SearchCode.IsEmpty())
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from file where FOLDER_CODE = '%s' "), *SearchCode), OutParams);
		if (iter.description.IsEmpty() && OutParams.Num() > 0)
		{
			iter.description = OutParams[0].folder_name;
		}
		bool res = ValueVisibilityExpCalibration(iter.id, iter.visibility_exp, iter.visibility);
		if (res) FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set CODE = '%s' , DESCRIPTION = '%s' , SORT_ORDER = '%d' , VISIBILITY = '%s', VISIBILITY_EXP = '%s', PARENT_ID = '%s' where ID = '%s'"), *iter.code, *iter.description, iter.sort_order, *iter.visibility, *iter.visibility_exp, *iter.parent_id, *iter.id));
#endif
	}

	if (SelectValueID.Equals(TEXT("-1")))
	{
		UpdateContent(OutSelection);
	}
	else
	{
		RefreshContent(OutSelection, SelectValueID);
	}
}

void UValueWidget::SearchDecoSelectionCheck(const FString& OptionId, const FString& StyleId)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA();
	int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[OptionId](const FRefToContentData& InData)->bool { return InData.content_id.Equals(OptionId); }
	);
	int32 CheckNum = 0;
	if (ContentIndex != INDEX_NONE && StyleRefData.content_datas[ContentIndex].style_option_checks.Contains(StyleId))
	{
		auto& StyleOptionCheck = StyleRefData.content_datas[ContentIndex].style_option_checks[StyleId].option_checks;
		FString ChangedID;
	
		for (auto& CheckData : StyleOptionCheck)
		{
			if (!SubValuesMap.Contains(CheckData.option_id)) continue;
			if (!SubValuesMap[CheckData.option_id]->GetIsVisiable() && CheckData.is_prime)
			{
				ChangedID = CheckData.option_id;
				CheckData.is_prime = false;
			}
			SubValuesMap[CheckData.option_id]->SetIsCheck(true, CheckData.is_prime);
			++CheckNum;
		}
		//DefaultPrimeManager(ChangedID);
	}

	if (ContentIndex != INDEX_NONE)
		CB_All->SetIsChecked(CheckNum == StyleRefData.content_datas[ContentIndex].option_datas.Num());
	else
		CB_All->SetIsChecked(false);
	

#else
	TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch where CONTENT_ID = '%s' AND STYLE_ID = '%s'"), *OptionId, *StyleId), OutDecoSelectionChecks);
	FString ChangedID;
	for (auto& CheckData : OutDecoSelectionChecks)
	{
		if (!SubValuesMap.Contains(CheckData.selection_id)) continue;
		if (!SubValuesMap[CheckData.selection_id]->GetIsVisiable() && CheckData.is_prime)
		{
			ChangedID = CheckData.selection_id;
			CheckData.is_prime = 0;
			FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_selch set SELECTION_ID = '%s' , CONTENT_ID = '%s' ,STYLE_ID = '%s', IS_PRIME= '%d'  where ID = '%d'"), *CheckData.selection_id, *CheckData.content_id, *CheckData.style_id, CheckData.is_prime, CheckData.id));
		}
		SubValuesMap[CheckData.selection_id]->SetIsCheck(true, CheckData.is_prime);
	}
	CheckDatas.Empty();
	CheckDatas = OutDecoSelectionChecks;
	DefaultPrimeManager(ChangedID);
#endif
}

void UValueWidget::CreateDecoSelection(const FDecorateSelection& NewSelection, const FString& CopyId)
{
	checkf(false, TEXT("CreateDecoSelection no use"));

	/*OutDatas.Empty();
	for (TMap<FString, UValueWidgetChild*>::TConstIterator Iter = SubValuesMap.CreateConstIterator(); Iter; ++Iter)
	{
		OutDatas.Add(Iter->Value->GetData());
	}*/

	bool res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(FString::Printf(TEXT("insert into decorate_sel(ID ,CODE, DESCRIPTION, SORT_ORDER, VISIBILITY, VISIBILITY_EXP, PARENT_ID) values('%s','%s','%s','%d','%s','%s','%s')"), *NewSelection.id, *NewSelection.code, *NewSelection.description, NewSelection.sort_order, *NewSelection.visibility, *NewSelection.visibility_exp, *NewSelection.parent_id));
	UE_LOG(LogTemp, Error, TEXT("Update : %d"), res);
	SearchDecoSelection(CurrentOptionID);
}

void UValueWidget::CreateDecoSelectionCheck(FDecoSelectionCheckData NewSelectionCheck, bool bUpdateContent)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[NewSelectionCheck](const FRefToContentData& InData)->bool { return InData.content_id.Equals(NewSelectionCheck.content_id); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		if (ContentData.style_option_checks.Contains(NewSelectionCheck.style_id))
		{
			auto& StyleOptionCheck = ContentData.style_option_checks[NewSelectionCheck.style_id].option_checks;

			//if origin check is null, so first default is prime
			/*if (StyleOptionCheck.IsEmpty())
			{
				NewSelectionCheck.is_prime = true;
			}*/

			auto& Temp = StyleOptionCheck.AddDefaulted_GetRef();
			PARSE_CHECK_ACTION_DATA_TO_REF_DATA(NewSelectionCheck, Temp);

			//CheckOptionPrime(ContentData, StyleOptionCheck);

		}
		else
		{
			ContentData.style_option_checks.Add(NewSelectionCheck.style_id, FOptionCheckArr());
			FOptionCheckArr& EditArr = ContentData.style_option_checks[NewSelectionCheck.style_id];
			//NewSelectionCheck.is_prime = true;
			auto& Temp = EditArr.option_checks.AddDefaulted_GetRef();
			PARSE_CHECK_ACTION_DATA_TO_REF_DATA(NewSelectionCheck, Temp);

			//CheckOptionPrime(ContentData, EditArr.option_checks);
		}
		ContentData.CheckStyleOptionState(NewSelectionCheck.style_id);
	}
#else

	UE_LOG(LogTemp, Error, TEXT("creating new selectioncheck id =  %d"), NewSelectionCheck.id);
	bool res = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(FString::Printf(TEXT("insert into decorate_selch values('%d','%s','%s','%s','%d')"), NewSelectionCheck.id, *NewSelectionCheck.selection_id, *NewSelectionCheck.content_id, *NewSelectionCheck.style_id, NewSelectionCheck.is_prime));
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck = %d"), res);
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck = %s"), *NewSelectionCheck.selection_id);

#endif

	if (bUpdateContent)
	{
		SearchDecoSelection(CurrentOptionID);
	}
}

void UValueWidget::DeleteDecoSelectionCheck(const FDecoSelectionCheckData& DelSelectionCheck,bool bUpdateContent)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[DelSelectionCheck](const FRefToContentData& InData)->bool { return InData.content_id.Equals(DelSelectionCheck.content_id); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		if (ContentData.style_option_checks.Contains(DelSelectionCheck.style_id))
		{
			auto& StyleOptionCheck = ContentData.style_option_checks[DelSelectionCheck.style_id].option_checks;
			for (auto Iter = StyleOptionCheck.CreateIterator(); Iter; ++Iter)
			{
				if (Iter->option_id.Equals(DelSelectionCheck.selection_id) &&
					Iter->content_id.Equals(DelSelectionCheck.content_id) &&
					Iter->style_id.Equals(DelSelectionCheck.style_id))
				{
					Iter.RemoveCurrent();
					break;
				}
			}
			/*if (StyleOptionCheck.Num() > 0 && DelSelectionCheck.is_prime)
			{
				StyleOptionCheck[0].is_prime = true;
			}*/

			//CheckOptionPrime(ContentData, StyleOptionCheck);
			ContentData.CheckStyleOptionState(DelSelectionCheck.style_id);
		}
	}

#else
	bool res = FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("delete from decorate_selch where SELECTION_ID = '%s' AND CONTENT_ID = '%s' AND STYLE_ID = '%s'"), *DelSelectionCheck.selection_id, *DelSelectionCheck.content_id, *DelSelectionCheck.style_id));
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck = %d"), res);
	//UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck id  = %d"), DelSelectionCheck.id);
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck selection_id = %s"), *DelSelectionCheck.selection_id);
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck content_id = %s"), *DelSelectionCheck.content_id);
	UE_LOG(LogTemp, Error, TEXT("CreateDecoSelectionCheck style_id = %s"), *DelSelectionCheck.style_id);
#endif

	if (bUpdateContent)
	{
		SearchDecoSelection(CurrentOptionID);
	}
}

bool UValueWidget::IsDecoSelectionCheck(const FDecoSelectionCheckData& DelSelectionCheck)
{
	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[DelSelectionCheck](const FRefToContentData& InData)->bool { return InData.content_id.Equals(DelSelectionCheck.content_id); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		if (ContentData.style_option_checks.Contains(DelSelectionCheck.style_id))
		{
			auto& StyleOptionCheck = ContentData.style_option_checks[DelSelectionCheck.style_id].option_checks;
			for (auto Iter = StyleOptionCheck.CreateIterator(); Iter; ++Iter)
			{
				if (Iter->option_id.Equals(DelSelectionCheck.selection_id) &&
					Iter->content_id.Equals(DelSelectionCheck.content_id) &&
					Iter->style_id.Equals(DelSelectionCheck.style_id))
				{
					return true;
				}
			}
		}
	}

	return false;
}

void UValueWidget::UpdateDecoSelections(const FDecorateSelection& DecoSelection)
{
	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		for (auto& Iter : ContentData.option_datas)
		{
			if (Iter.option_id.Equals(DecoSelection.id))
			{
				PARSE_OPTION_ACTION_DATA_TO_REF_DATA(DecoSelection, Iter);
				break;
			}
		}
	}

}

void UValueWidget::UpdateDecoSelections(const TArray<FDecorateSelection>& DecoSelections)
{
#ifdef USE_REF_LOCAL_FILE

	for (const auto Iter : DecoSelections)
	{
		UpdateDecoSelections(Iter);
	}

#else
	for (auto iter : DecoSelections)
	{
		bool res = FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set CODE = '%s' , DESCRIPTION = '%s' , SORT_ORDER = '%d' , VISIBILITY = '%s', VISIBILITY_EXP = '%s', PARENT_ID = '%s' where ID = '%s'"), *iter.code, *iter.description, iter.sort_order, *iter.visibility, *iter.visibility_exp, *iter.parent_id, *iter.id));
		UE_LOG(LogTemp, Error, TEXT("Update : %d"), res);
		UE_LOG(LogTemp, Error, TEXT("Update SORT_ORDER =  %d"), iter.sort_order);
	}
#endif
	SearchDecoSelection(CurrentOptionID);
	/*OutDatas.Empty();
	for (TMap<FString, UValueWidgetChild*>::TConstIterator Iter = SubValuesMap.CreateConstIterator(); Iter; ++Iter)
	{
		OutDatas.Add(Iter->Value->GetData());
	}*/
	/*RefreshContent(OutDatas, SelectValueID);*/
}

void UValueWidget::UpdateDecoSelectionCheck(const FDecoSelectionCheckData& CheckData)
{
	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE && StyleRefData.content_datas[Index].style_option_checks.Contains(CheckData.style_id))
	{
		auto& StyleOptionCheck = StyleRefData.content_datas[Index].style_option_checks[CheckData.style_id].option_checks;
		for (auto& Iter : StyleOptionCheck)
		{
			if (Iter.option_id.Equals(CheckData.selection_id) &&
				Iter.content_id.Equals(CheckData.content_id) &&
				Iter.style_id.Equals(CheckData.style_id))
			{
				PARSE_CHECK_ACTION_DATA_TO_REF_DATA(CheckData, Iter);
				break;
			}
		}
	}
}

void UValueWidget::UpdateDecoSelectionCheck(const TArray<FDecoSelectionCheckData>& CheckDatas)
{
#ifdef USE_REF_LOCAL_FILE

	for (const auto Iter : CheckDatas)
	{
		UpdateDecoSelectionCheck(Iter);
	}

#else
	for (auto iter : CheckDatas)
	{
		FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_selch set SELECTION_ID = '%s' , CONTENT_ID = '%s' ,STYLE_ID = '%s', IS_PRIME= '%d'  where ID = '%d'"), *iter.selection_id, *iter.content_id, *iter.style_id, iter.is_prime, iter.id));
	}
	/*OutDatas.Empty();
	for (TMap<FString, UValueWidgetChild*>::TConstIterator Iter = SubValuesMap.CreateConstIterator(); Iter; ++Iter)
	{
		OutDatas.Add(Iter->Value->GetData());
	}
	RefreshContent(OutDatas, SelectValueID);*/
#endif
	SearchDecoSelectionCheck(CurrentOptionID, CurrentStyleID);

}

void UValueWidget::SearchSelectionParams(const FString& ParamId, const FString& ParamName, const FString& SelectionId, const FString& StyleId)
{
#ifdef USE_REF_LOCAL_FILE

	if (SelectionId.Equals(TEXT("-1")) || StyleId.Equals(TEXT("-1")))
	{
		UpdateValueParam(TArray<FParameterData>());
	}
	else
	{
		TArray<FParameterData> DecoParams = TArray<FParameterData>();

		GET_STYLE_REF_FILE_DATA();
		int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
			[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
		);
		if (Index != INDEX_NONE)
		{
			for (const auto Iter : StyleRefData.content_datas[Index].option_datas)
			{
				if (Iter.option_id.Equals(SelectionId))
				{
					DecoParams = Iter.option_params;
					break;
				}
			}
		}
		TArray<FParameterData> TempParams = DecoParams;
		if (!ParamId.Equals(TEXT("-1")))
		{
			for (auto Iter : DecoParams)
			{
				if (Iter.Data.param_id.Equals(ParamId,ESearchCase::CaseSensitive))
				{
					TempParams.Add(Iter);
				}
			}
		}
		TArray<FParameterData> TempParams2 = TempParams;
		if (!ParamName.IsEmpty())
		{
			for (auto Iter : TempParams)
			{
				if (Iter.Data.name.Equals(ParamName, ESearchCase::CaseSensitive))
				{
					TempParams2.Add(Iter);
				}
			}
		}
		UpdateValueParam(TempParams2);
	}


#else
	TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
	TArray<FEnumParameterTableData> OutDecoParamsEnum = TArray<FEnumParameterTableData>();
	TArray<FParameterData> OutDecoParams = TArray<FParameterData>();
	FString sqlcommand = TEXT("select * from decorate_param where ");
	bool IsAnd = false;
	if (!ParamId.Equals(TEXT("-1")))
	{
		IsAnd = true;
		sqlcommand += FString::Printf(TEXT("PARAM_ID = '%s'"), *ParamId);
	}
	if (!ParamName.IsEmpty())
	{
		if (IsAnd)
		{
			sqlcommand += FString::Printf(TEXT(" and NAME = '%s'"), *ParamName);
		}
		else
		{
			IsAnd = true;
			sqlcommand += FString::Printf(TEXT("NAME = '%s'"), *ParamName);
		}
	}
	if (!SelectionId.Equals(TEXT("-1")))
	{
		if (IsAnd)
		{
			sqlcommand += FString::Printf(TEXT(" and MAIN_ID = '%s'"), *SelectionId);
		}
		else
		{
			sqlcommand += FString::Printf(TEXT("MAIN_ID = '%s'"), *SelectionId);
		}

	}
	UE_LOG(LogTemp, Error, TEXT("Search MainID %s"), *SelectionId);
	UE_LOG(LogTemp, Error, TEXT("Search sqlcommand %s"), *sqlcommand);
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(sqlcommand, OutParamsTableData);
	for (auto iter : OutParamsTableData)
	{
		FParameterData Temp = FParameterData();
		Temp.Data = iter;
		if (iter.is_enum)
		{
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_param_enum where MAIN_ID = '%s'"), *iter.id), OutDecoParamsEnum);
			Temp.EnumData = OutDecoParamsEnum;
			OutDecoParamsEnum.Empty();
		}
		OutDecoParams.Add(Temp);
	}
	UE_LOG(LogTemp, Error, TEXT("Search CurrentStyleID %s"), *CurrentStyleID);
	UE_LOG(LogTemp, Error, TEXT("Search SelectValueID %s"), *SelectValueID);
	if (CurrentStyleID.Equals(TEXT("-1")) || SelectValueID.Equals(TEXT("-1")))
	{
		TArray<FParameterData> EmptyParams = TArray<FParameterData>();
		EmptyParams.Empty();
		UpdateValueParam(EmptyParams);
	}
	else
	{
		{
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters = TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> ();
			TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
			UParameterRelativeLibrary::CombineParameters(PeerParameters, OutDecoParams);
			TArray<FParameterData> ParamsCopy = TArray<FParameterData>();
			UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, ParentParameters, PeerParameters);
			PeerParameters.GenerateValueArray(ParamsCopy);

			for (auto& iter : OutDecoParams)
			{
				int32 CurIndex = ParamsCopy.IndexOfByPredicate(
					[iter](const FParameterData& p)->bool { return p.Data.name.Equals(iter.Data.name, ESearchCase::IgnoreCase); }
				);
				if (CurIndex != INDEX_NONE)
				{
					iter = ParamsCopy[CurIndex];
				}
			}
			////解析本级变量的引用关系，依据引用关系重新计算各变量的值
			//FParameterRefrenceParser Parser;
			//bool Res = Parser.SortParameterByRefrence(OutDecoParams);
			//auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
			//TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParametersMap;
			//UParameterRelativeLibrary::CombineParameters(PeerParametersMap, OutDecoParams);

			/*TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters;
			FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);*/
			/*for (int32 i = 0; i < SortedParameters.Num(); ++i)
			{
				auto& EditParameter = SortedParameters[i];
				PeerParametersMap.Remove(EditParameter.Data.name);
				UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, PeerParametersMap, EditParameter);
				PeerParametersMap.Add(EditParameter.Data.name, EditParameter);
			}
			PeerParametersMap.GenerateValueArray(OutDecoParams);*/
		}


		UpdateValueParam(OutDecoParams);
		UE_LOG(LogTemp, Error, TEXT("Search OutDecoParams num %d"), OutDecoParams.Num());
		if (SubValuesMap.Contains(SelectValueID) && SubValuesMap[SelectValueID])
		{
			FDecorateSelection SelctedValueData = SubValuesMap[SelectValueID]->GetData();
			OnValueVisibilityEditHandler(SubValuesMap[SelectValueID], SelctedValueData.visibility_exp);
		}
	}
#endif
}

void UValueWidget::CreateSelectionParam(const FParameterData& SelectionParam)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		int32 SelectIndex = ContentData.option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
		);
		if (SelectIndex != INDEX_NONE)
		{
			ContentData.option_datas[SelectIndex].option_params.Add(SelectionParam);

		}
	}

	/*
	*  @@ calculate params and visibility
	*/
	AddParam(SelectionParam);

#else
	FString sql = FString::Printf(TEXT("insert into decorate_param values('%s','%s','%s','%d','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%d','%s','%s')")
		, *SelectionParam.Data.id, *SelectionParam.Data.name, *SelectionParam.Data.description, SelectionParam.Data.classific_id, *SelectionParam.Data.value, *SelectionParam.Data.expression, *SelectionParam.Data.max_value, *SelectionParam.Data.max_expression, *SelectionParam.Data.min_value, *SelectionParam.Data.min_expression
		, *SelectionParam.Data.visibility, *SelectionParam.Data.visibility_exp, *SelectionParam.Data.editable, *SelectionParam.Data.editable_exp, SelectionParam.Data.is_enum, *SelectionParam.Data.param_id, *SelectionParam.Data.main_id/**SelectValueID*/);
	bool res1 = FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(sql);
	UE_LOG(LogTemp, Error, TEXT("CreateSelectionParam  %d"), res1);
	if (SelectionParam.Data.is_enum)
	{
		FString InsertEnumSQL = FString::Printf(TEXT("insert into decorate_param_enum values "));
		for (auto& EnumDataiter : SelectionParam.EnumData)
		{
			InsertEnumSQL = InsertEnumSQL + FString::Printf(TEXT("('%s','%s','%s','%s','%s','%s','%s','%s','%s'),"), *FGuid::NewGuid().ToString().ToLower(), *EnumDataiter.value, *EnumDataiter.expression, *EnumDataiter.name_for_display, *EnumDataiter.image_for_display, *EnumDataiter.visibility, *EnumDataiter.visibility_exp, *EnumDataiter.priority, *SelectionParam.Data.id);
			if (!EnumDataiter.image_for_display.IsEmpty())
			{
				const FString FilePath = FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir() + EnumDataiter.image_for_display);
				FString LocalMD5 = TEXT("");
				int64 FileSize = 0;
				ACatalogPlayerController::GetFileMD5AndSize(FilePath, LocalMD5, FileSize);

				FString InsertSql = FString::Printf(TEXT("insert into param_image values('%s','%s',%d)")
					, *EnumDataiter.image_for_display, *LocalMD5, FileSize);
				FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertSql);
			}
		}
		FLocalDatabaseOperatorLibrary::InsertDataFromDataBaseBySQL(InsertEnumSQL.Mid(0, InsertEnumSQL.Len() - 1));
	}
#endif
	//UE_LOG(LogTemp, Error, TEXT("CreateSelectionParam MainID/selectionID/ValueID %s"), *SelectValueID);

	SearchSelectionParams(TEXT("-1"), TEXT(""), SelectValueID, CurrentStyleID);

}

void UValueWidget::DeleteSelectionParam(const FString& ParamId, const FString& ValueId, const FString& StyleId)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (Index != INDEX_NONE)
	{
		auto& ContentData = StyleRefData.content_datas[Index];
		int32 SelectIndex = ContentData.option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
		);
		if (SelectIndex != INDEX_NONE)
		{
			for (auto Iter = ContentData.option_datas[SelectIndex].option_params.CreateIterator(); Iter; ++Iter)
			{
				if (Iter->Data.id.Equals(ParamId))
				{
					Iter.RemoveCurrent();
					break;
				}
			}
		}
	}
#else
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *ParamId));
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param WHERE MAIN_ID = '%s'"), *ValueId));
#endif

	SearchSelectionParams(TEXT("-1"), TEXT(""), SelectValueID, CurrentStyleID);

}

void UValueWidget::BindDelegates()
{
}

void UValueWidget::OnDeleteValueHandler(bool Res)
{
	//DeleteSelectionParam(TEXT("-1"), SelectValueID, TEXT("-1"));

	//remove this value widget
	if (SubValuesMap.Contains(SelectValueID))
		SubValuesMap.Remove(SelectValueID);
	SelectValueID = TEXT("-1");

	//clear this value params
	SBPara->ClearChildren();
	SubParamsMap.Empty();

	//SearchDecoSelection(CurrentOptionID);

	//clear param operator state
	UpdateValueParamOperateState(EValueParamOperateState::NoValueNoParam);
	CB_All->SetIsChecked(AllChildrenChecked());
}

void UValueWidget::ConstructNewData(FDecorateSelection& NewStyleValue, bool IsInsert)
{
	if(CurrentOptionID.IsEmpty())
	{
		UI_POP_WINDOW_ERROR(
			FText::FromStringTable(FName("PosSt"), TEXT("Please Select Content To Add New")).ToString()
		);
		return;
	}

	NewStyleValue.parent_id = CurrentOptionID;
	NewStyleValue.id = FGuid::NewGuid().ToString().ToLower();
	NewStyleValue.visibility = FString(TEXT("1"));
	NewStyleValue.visibility_exp = FString(TEXT("1"));

#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (ContentIndex != INDEX_NONE)
	{
		int32 SelectIndex = INDEX_NONE;
		if (IsInsert)
		{
			SelectIndex = StyleRefData.content_datas[ContentIndex].option_datas.IndexOfByPredicate(
				[this](const FRefToOptionData& InData)
				{
					return InData.option_id.Equals(SelectValueID);
				}
			);
		}
		auto& TempData = SelectIndex == INDEX_NONE ? StyleRefData.content_datas[ContentIndex].option_datas.AddDefaulted_GetRef()
			: StyleRefData.content_datas[ContentIndex].option_datas.InsertDefaulted_GetRef(SelectIndex + 1);
		PARSE_OPTION_ACTION_DATA_TO_REF_DATA(NewStyleValue, TempData);
		StyleRefData.ReGenerateOptionOrder(CurrentOptionID);
	}
	SelectValueID = NewStyleValue.id;
	SearchDecoSelection(CurrentOptionID);

#else
	if (SubValuesMap.Num() == 0)
	{
		NewStyleValue.sort_order = 1;
		CreateDecoSelection(NewStyleValue);
		return;
	}
	TArray<UValueWidgetChild*> ValueItems;
	SubValuesMap.GenerateValueArray(ValueItems);
	if (IsInsert)
	{
		int32 index = ValueItems.IndexOfByKey<UValueWidgetChild*>(SubValuesMap[SelectValueID]);
		if (ValueItems.Last()->GetData().id.Equals(SubValuesMap[SelectValueID]->GetData().id))
		{
			NewStyleValue.sort_order = ValueItems.Last()->GetData().sort_order + 1;
		}
		else
		{
			for (int32 i = 0, neworder = 0; i < ValueItems.Num(); i++)
			{
				if (i == index + 1)
				{
					NewStyleValue.sort_order = neworder;
					neworder++;
				}
				FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set SORT_ORDER = '%d' where ID = '%s'")
					, neworder, *ValueItems[i]->GetData().id));
				neworder++;
			}
		}
	}
	else
	{
		if (SubValuesMap.Num() == 0)
		{
			NewStyleValue.sort_order = 1;
		}
		else
		{
			NewStyleValue.sort_order = ValueItems.Last()->GetData().sort_order + 1;
		}
	}
	CreateDecoSelection(NewStyleValue);
#endif
}

void UValueWidget::ValueSubItemMoveAction(const FString& ValueID, bool IsUp)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 OptionIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
	);
	if (OptionIndex != INDEX_NONE)
	{
		SWAPE_ITEM_ACTION(StyleRefData.content_datas[OptionIndex].option_datas, FRefToOptionData, option_id, ValueID, IsUp);
		StyleRefData.ReGenerateOptionOrder(CurrentOptionID);
	}

#else
	TArray<UValueWidgetChild*> ValueItems;
	SubValuesMap.GenerateValueArray(ValueItems);
	int32 index = ValueItems.IndexOfByKey<UValueWidgetChild*>(SubValuesMap[ValueID]);
	int32 preIndex = (IsUp ? -1 : 1) + index;

	if (preIndex > ValueItems.Num() || preIndex < 0)
		return;
	FString CurrentID = ValueItems[index]->GetData().id;
	int32 currentOrder = ValueItems[index]->GetData().sort_order;
	FString SwapID = ValueItems[preIndex]->GetData().id;
	int32 SwapOrder = ValueItems[preIndex]->GetData().sort_order;

	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set SORT_ORDER= '%d' where ID = '%s'"), SwapOrder, *CurrentID));
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set SORT_ORDER= '%d' where ID = '%s'"), currentOrder, *SwapID));
#endif

	SearchDecoSelection(SubValuesMap[ValueID]->GetData().parent_id);
	OnValueItemSelectHandler(SubValuesMap[ValueID], true);

}

void UValueWidget::OnValueItemSelectHandler(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos)
{
	//if (IsLeftMouse)
	{
		if (SubValuesMap.Contains(SelectValueID))
		{
			SubValuesMap[SelectValueID]->SetSelectState(false);
			SubValuesMap[SelectValueID]->ResetReadOnly();
		}
		if (IS_OBJECT_PTR_VALID(ValueItem))
		{
			ValueItem->SetSelectState(true);
			UpdateStyleOperateState<UValueWidgetChild>(ValueItem->GetData().sort_order, SubValuesMap);
			SelectValueID = ValueItem->GetData().id;
			SearchSelectionParams(TEXT("-1"), TEXT(""), SelectValueID, CurrentStyleID);
		}
	}
	if (!IsLeftMouse)
	{
		SetRightMenu(ValueItem, IsLeftMouse, MousePos);
	}
}

void UValueWidget::OnValueVisibilityEditHandler(UValueWidgetChild* ValueItem, const FString& Expression)
{
	if (!IS_OBJECT_PTR_VALID(ValueItem))
		return;

	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler begin"));
	FDecorateSelection EditSelectionOption = ValueItem->GetData();

	OptionVisibilityUpdate(
		CurrentStyleID,
		CurrentOptionID,
		SelectValueID,
		EditSelectionOption,
		Expression
	);
	ValueItem->UpdateCurrentSelection(EditSelectionOption);
	ValueItem->CheckPrime();

	/*FString NewExpression, NewValue;
	if (ValueVisibilityRefresh_NoPrime(Original.id, Expression, NewExpression, NewValue))
	{
		UUIFunctionLibrary::FormatInputValue(NewValue);
		Original.visibility = NewValue;
		Original.visibility_exp = NewExpression;
		ValueItem->UpdateCurrentSelection(Original);
	}
	else
	{
		ValueItem->UpdateCurrentSelection(Original);
	}*/

	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler end"));

//#ifdef USE_REF_LOCAL_FILE
//
//	GET_PARAMS_WITH_REF_FILE(CurrentOptionID, SelectValueID, CurrentParameters);
//
//#else
//	//按优先级获取参数值	
//	TArray<FParameterData> CurParameters = TArray<FParameterData>();
//	TArray<FParameterData> CurParams = TArray<FParameterData>();
//	TArray<FParameterTableData> CurTableData = TArray<FParameterTableData>();
//	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *Original.id), CurTableData);
//	for (auto TableDataIter : CurTableData)
//	{
//		FParameterData Temp = FParameterData();
//		TArray<FEnumParameterTableData> OutEnumData = TArray<FEnumParameterTableData>();
//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *TableDataIter.id), OutEnumData);
//		Temp.Data = TableDataIter;
//		Temp.EnumData = OutEnumData;
//		OutEnumData.Empty();
//		CurParams.Add(Temp);
//	}
//	CurParameters.Append(CurParams);
//	CurrentParameters = CurParameters;
//#endif
//	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler CurrentValueParamter = %d"), CurrentParameters.Num());
//	//获取所有勾选的值
//	TArray<FDecoSelectionCheckData> OutDecoSelectionChecks = TArray<FDecoSelectionCheckData>();
//	TArray<FDecoSelectionCheckData> VaildSelectionChecks;
//
//#ifdef USE_REF_LOCAL_FILE
//
//	for (const auto Iter : StyleRefData.content_datas)
//	{
//		if (Iter.content_id.Equals(CurrentOptionID))
//		{
//			for (const auto StyleOptionCheckIter : Iter.style_option_checks)
//			{
//				if (CurrentStyleID.Equals(TEXT("-1")) || StyleOptionCheckIter.Key.Equals(CurrentStyleID))
//				{
//					for (const auto CheckArrIter : StyleOptionCheckIter.Value.option_checks)
//					{
//						if (CheckArrIter.is_prime)
//						{
//							auto& Temp = VaildSelectionChecks.AddDefaulted_GetRef();
//							Temp.selection_id = CheckArrIter.option_id;
//							Temp.content_id = CheckArrIter.content_id;
//							Temp.style_id = CheckArrIter.style_id;
//							Temp.is_prime = CheckArrIter.is_prime;
//							break;
//						}
//					}
//				}
//			}
//		}
//	}
//
//
//
//#else
//	if (CurrentStyleID.Equals(TEXT("-1")))
//	{
//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch")), OutDecoSelectionChecks);
//	}
//	else
//	{
//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_selch WHERE STYLE_ID = '%s'"), *CurrentStyleID), OutDecoSelectionChecks);
//	}
//	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler SelectionChecks Num = %d"), OutDecoSelectionChecks.Num());
//	for (auto SelectIter : OutDecoSelectionChecks)
//	{//排除非优先显示的值
//		if (SelectIter.is_prime == 0) continue;
//		VaildSelectionChecks.Add(SelectIter);
//	}
//#endif
//
//	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler VaildSelectionChecks Num = %d"), VaildSelectionChecks.Num());
//	TArray<FDecorateSelection> OutSelection = TArray<FDecorateSelection>();
//
//#ifdef USE_REF_LOCAL_FILE
//
//	TArray<FParameterData> PrimeParameters = TArray<FParameterData>();
//	if (Index != INDEX_NONE)
//	{
//		for (const auto CheckIter : VaildSelectionChecks)
//		{
//			int32 Index_Check = StyleRefData.content_datas[Index].option_datas.IndexOfByPredicate(
//				[CheckIter](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(CheckIter.selection_id); }
//			);
//			if (Index_Check != INDEX_NONE)
//			{
//				auto& Temp = OutSelection.AddDefaulted_GetRef();
//				PARSE_OPTION_REF_DATA_TO_ACTION_DATA(Temp, StyleRefData.content_datas[Index].option_datas[Index_Check]);
//
//				PrimeParameters.Append(StyleRefData.content_datas[Index].option_datas[Index_Check].option_params);
//			}
//		}
//	}
//#else
//	for (auto CheckIter : VaildSelectionChecks)
//	{
//		TArray<FDecorateSelection> TempSelections = TArray<FDecorateSelection>();
//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from decorate_sel WHERE ID = '%s' AND PARENT_ID = '%s'"), *CheckIter.selection_id, *CheckIter.content_id), TempSelections);
//		UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler TempSelections Num = %d"), TempSelections.Num());
//		OutSelection.Append(TempSelections);
//	}
//	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler OutSelection Num = %d Total"), OutSelection.Num());
//	TArray<FParameterData> PrimeParameters = TArray<FParameterData>();
//	for (auto SelectIter : OutSelection)
//	{
//		TArray<FParameterData> OurParams = TArray<FParameterData>();
//		TArray<FParameterTableData> OutTableData = TArray<FParameterTableData>();
//		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *SelectIter.id), OutTableData);
//		for (auto TableDataIter : OutTableData)
//		{
//			FParameterData Temp = FParameterData();
//			TArray<FEnumParameterTableData> OutEnumData = TArray<FEnumParameterTableData>();
//			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *TableDataIter.id), OutEnumData);
//			Temp.Data = TableDataIter;
//			Temp.EnumData = OutEnumData;
//			OutEnumData.Empty();
//			OurParams.Add(Temp);
//		}
//		PrimeParameters.Append(OurParams);
//	}
//#endif
//
//	UE_LOG(LogTemp, Error, TEXT("OnValueVisibilityEditHandler PrimeParameters Num = %d"), PrimeParameters.Num());
//	//表达式计算
//	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
//	UParameterRelativeLibrary::CombineParameters(PeerParameters, CurrentParameters);
//	UParameterRelativeLibrary::CombineFirstParameters(PeerParameters, PrimeParameters);
//	FString OutValue = FString();
//	FString OutFormatExpress = FString();
//	bool Res = UUIFunctionLibrary::CalculateExpressionValue(PeerParameters, Expression, OutValue, OutFormatExpress);
//	if (false == Res)
//	{
//		ValueItem->UpdateCurrentSelection(Original);
//		return;
//	}
//	UUIFunctionLibrary::FormatInputValue(OutValue);
//	FDecorateSelection DecorateValue;
//	DecorateValue = ValueItem->GetData();
//	DecorateValue.visibility = OutValue;
//	DecorateValue.visibility_exp = OutFormatExpress;
//	ValueItem->UpdateCurrentSelection(DecorateValue);
}

bool UValueWidget::ValueVisibilityRefresh_NoPrime(
	const FString& InSelectionID, 
	const FString& InExpression, 
	FString& OutExpression, 
	FString& OutValue
)
{
	/*
	*  @@ New Logic : option --- value[selection], content --- option , style --- style
	*/

	//this value option params
	TArray<FParameterData> CurrentOptionParams;
	GET_STYLE_REF_FILE_DATA_REF();
	const int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool
		{
			return InData.content_id.Equals(CurrentOptionID);
		}
	);
	if (ContentIndex != INDEX_NONE)
	{
		for (const auto Iter : StyleRefData.content_datas[ContentIndex].option_datas)
		{
			if (Iter.option_id.Equals(InSelectionID))
			{
				CurrentOptionParams = Iter.option_params;
				break;
			}
		}
	}

	//other content params [prime]
	TArray<FParameterData> OtherContentOptionPrimeParams;
	for (const auto& ContentIter : StyleRefData.content_datas)
	{
		if (ContentIter.content_id.Equals(CurrentOptionID, ESearchCase::IgnoreCase))
			continue;

		//has current style check
		if (ContentIter.style_option_checks.Contains(CurrentStyleID))
		{
			const auto& CheckArr = ContentIter.style_option_checks[CurrentStyleID].option_checks;
			if (CheckArr.Num() > 0)
			{
				FString PrimeOptionID = TEXT("");
				for (const auto& CI : CheckArr)
				{
					if (CI.is_prime)
					{
						PrimeOptionID = CI.option_id;
						break;
					}
				}
				if (PrimeOptionID.IsEmpty())
				{
					UE_LOG(LogTemp, Error, TEXT("style[%s], content[%s] --- check no prime!"), *CurrentStyleID, *ContentIter.content_id);
					PrimeOptionID = CheckArr[0].option_id;
				}

				if (!PrimeOptionID.IsEmpty())
				{//search option --- get prime params
					for (const auto& COD : ContentIter.option_datas)
					{
						if (COD.option_id.Equals(PrimeOptionID, ESearchCase::IgnoreCase)
							|| COD.option_params.Num() > 0)
						{
							OtherContentOptionPrimeParams.Append(COD.option_params);
						}
					}
				}
			}
		}
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, OtherContentOptionPrimeParams);
	UParameterRelativeLibrary::CombineParameters(ParentParameters, CurrentOptionParams);
	//UParameterRelativeLibrary::CombineFirstParameters(ParentParameters, OtherContentOptionPrimeParams);

	bool Res = UUIFunctionLibrary::CalculateExpressionValue(ACatalogPlayerController::Get()->GetGlobalParameterMap(), ParentParameters, InExpression, OutValue, OutExpression);
	UUIFunctionLibrary::FormatInputValue(OutValue);

	return Res;
}

bool UValueWidget::ValueVisibilityRefresh_Prime(
	const FString& InSelectionID, 
	const FString& InExpression,
	FString& OutExpression, 
	FString& OutValue
)
{
	return true;
}

void UValueWidget::OptionParamsUpdate()
{
	//this value option params
	TArray<FParameterData> CurrentOptionParams;
	GET_STYLE_REF_FILE_DATA_REF();
	const int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool
		{
			return InData.content_id.Equals(CurrentOptionID);
		}
	);
	int32 OptionIndex = INDEX_NONE;
	if (ContentIndex != INDEX_NONE)
	{
		for (int32 i = 0; i < StyleRefData.content_datas[ContentIndex].option_datas.Num(); ++i)
		{

			if (StyleRefData.content_datas[ContentIndex].option_datas[i].option_id.Equals(SelectValueID))
			{
				CurrentOptionParams = StyleRefData.content_datas[ContentIndex].option_datas[i].option_params;
				OptionIndex = i;
				break;
			}
		}
	}

	//other content params [prime]
	TArray<FParameterData> OtherContentOptionPrimeParams;
	for (const auto& ContentIter : StyleRefData.content_datas)
	{
		if (ContentIter.content_id.Equals(CurrentOptionID, ESearchCase::IgnoreCase))
			continue;

		//has current style check
		if (ContentIter.style_option_checks.Contains(CurrentStyleID))
		{
			const auto& CheckArr = ContentIter.style_option_checks[CurrentStyleID].option_checks;
			if (CheckArr.Num() > 0)
			{
				FString PrimeOptionID = TEXT("");
				for (const auto& CI : CheckArr)
				{
					if (CI.is_prime)
					{
						PrimeOptionID = CI.option_id;
						break;
					}
				}
				if (PrimeOptionID.IsEmpty())
				{
					UE_LOG(LogTemp, Error, TEXT("style[%s], content[%s] --- check no prime!"), *CurrentStyleID, *ContentIter.content_id);
					PrimeOptionID = CheckArr[0].option_id;
				}

				if (!PrimeOptionID.IsEmpty())
				{//search option --- get prime params
					for (const auto& COD : ContentIter.option_datas)
					{
						if (COD.option_id.Equals(PrimeOptionID, ESearchCase::IgnoreCase)
							|| COD.option_params.Num() > 0)
						{
							OtherContentOptionPrimeParams.Append(COD.option_params);
						}
					}
				}
			}
		}
	}

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  ParentParameters;
	UParameterRelativeLibrary::CombineParameters(ParentParameters, OtherContentOptionPrimeParams);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  CurParametersMap;
	UParameterRelativeLibrary::CombineParameters(CurParametersMap, CurrentOptionParams);
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(
		ACatalogPlayerController::Get()->GetGlobalParameterMap(),
		ParentParameters,
		CurParametersMap
	);

	if (ContentIndex != INDEX_NONE && OptionIndex != INDEX_NONE)
	{
		auto& OptionRef = StyleRefData.content_datas[ContentIndex].option_datas[OptionIndex];
		for (auto& Param : OptionRef.option_params)
		{
			if (CurParametersMap.Contains(Param.Data.name))
			{
				Param = CurParametersMap[Param.Data.name];
			}
		}
	}
}

void UValueWidget::OptionVisibilityUpdate(
	const FString& CurStyleID,
	const FString& CurContentID,
	const FString& CurOptionID,
	FDecorateSelection& EditSelectionOption, 
	const FString& InExpression
)
{
	if (InExpression.IsNumeric())
	{
		FString NumericValue = InExpression;
		EditSelectionOption.visibility_exp = NumericValue;
		UUIFunctionLibrary::FormatInputValue(NumericValue);
		EditSelectionOption.visibility = NumericValue;
		return ;
	}

	TArray<FParameterData> OptionParams;
	int32 CurContentIndex = INDEX_NONE;
	int32 CurOptionIndex = INDEX_NONE;
	GetCurrentOptionParams(
		CurStyleID,
		CurContentID,
		CurOptionID,
		OptionParams,
		CurContentIndex,
		CurOptionIndex
	);

	TArray<FParameterData> OtherContentPrimeParams;
	GetOtherOptionParams_Prime(
		CurStyleID,
		CurContentID,
		OtherContentPrimeParams
	);

	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	UParameterRelativeLibrary::CombineParameters(PeerParameters, OtherContentPrimeParams);
	UParameterRelativeLibrary::CombineParameters(PeerParameters, OptionParams);

	FString TempValue = FString();
	FString OutFormatExpress = FString();
	bool Res = UUIFunctionLibrary::CalculateExpressionValue(ACatalogPlayerController::Get()->GetGlobalParameterMap(), PeerParameters, InExpression, TempValue, OutFormatExpress);
	if (Res)
	{
		EditSelectionOption.visibility_exp = OutFormatExpress;
		UUIFunctionLibrary::FormatInputValue(TempValue);
		EditSelectionOption.visibility = TempValue;
	}
}

void UValueWidget::OptionVisibilityUpdate_Current()
{
	if (SubValuesMap.Contains(SelectValueID))
	{
		FDecorateSelection CurSelectOption = SubValuesMap[SelectValueID]->GetData();
		if (!CurSelectOption.visibility_exp.IsNumeric())
		{
			OptionVisibilityUpdate(
				CurrentStyleID,
				CurrentOptionID,
				SelectValueID,
				CurSelectOption,
				CurSelectOption.visibility_exp
			);
			SubValuesMap[SelectValueID]->UpdateCurrentSelection(CurSelectOption);
			SubValuesMap[SelectValueID]->CheckPrime();
		}
	}
}

void UValueWidget::GetCurrentOptionParams(
	const FString& CurStyleID, 
	const FString& CurContentID, 
	const FString& CurOptionID, 
	TArray<FParameterData>& OptionParams, 
	int32& CurContentIndex, 
	int32& CurOptionIndex
)
{
	GET_STYLE_REF_FILE_DATA_REF();
	CurContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[CurContentID](const FRefToContentData& InData)->bool
		{
			return InData.content_id.Equals(CurContentID);
		}
	);
	if (CurContentIndex != INDEX_NONE)
	{
		for (int32 i = 0; i < StyleRefData.content_datas[CurContentIndex].option_datas.Num(); ++i)
		{

			if (StyleRefData.content_datas[CurContentIndex].option_datas[i].option_id.Equals(CurOptionID))
			{
				OptionParams = StyleRefData.content_datas[CurContentIndex].option_datas[i].option_params;
				CurOptionIndex = i;
				break;
			}
		}
	}
}

void UValueWidget::GetOtherOptionParams_Prime(
	const FString& CurStyleID, 
	const FString& CurContentID, 
	TArray<FParameterData>& OptionParams
)
{
	GET_STYLE_REF_FILE_DATA_REF();
	for (const auto& ContentIter : StyleRefData.content_datas)
	{
		if (ContentIter.content_id.Equals(CurContentID, ESearchCase::IgnoreCase))
			continue;

		//has current style check
		if (ContentIter.style_option_checks.Contains(CurStyleID))
		{
			const auto& CheckArr = ContentIter.style_option_checks[CurStyleID].option_checks;
			if (CheckArr.Num() > 0)
			{
				FString PrimeOptionID = TEXT("");
				for (const auto& CI : CheckArr)
				{
					if (CI.is_prime)
					{
						PrimeOptionID = CI.option_id;
						break;
					}
				}
				/*if (PrimeOptionID.IsEmpty())
				{
					UE_LOG(LogTemp, Error, TEXT("style[%s], content[%s] --- check no prime!"), *CurStyleID, *ContentIter.content_id);
					PrimeOptionID = CheckArr[0].option_id;
				}*/

				if (!PrimeOptionID.IsEmpty())
				{//search option --- get prime params
					for (const auto& COD : ContentIter.option_datas)
					{
						if (COD.option_id.Equals(PrimeOptionID, ESearchCase::IgnoreCase)
							&& COD.option_params.Num() > 0)
						{
							OptionParams.Append(COD.option_params);
						}
					}
				}
			}
		}
	}
}

//
void UValueWidget::CheckOptionPrime(
	const FRefToContentData& RefToContentData,
	TArray<FRefToOptionCheck>& RefToOptionChecks
)
{
	if(RefToOptionChecks.IsEmpty())
	{
		return;
	}
	const int32& PrimeIndex = RefToOptionChecks.IndexOfByPredicate(
		[](const FRefToOptionCheck& InData)->bool
		{
			return InData.is_prime;
		}
	);
	if (PrimeIndex == INDEX_NONE)
	{
		for (auto& RTOC : RefToOptionChecks)
		{
			const int32 OptionIndex = RefToContentData.option_datas.IndexOfByPredicate(
				[&RTOC](const FRefToOptionData& InData)->bool
				{
					return InData.option_id.Equals(RTOC.option_id);
				}
			);
			if (OptionIndex != INDEX_NONE)
			{
				if (RefToContentData.option_datas[OptionIndex].IsVisible())
				{
					RTOC.is_prime = true;
					return;
				}
			}
		}
	}
}

void UValueWidget::OnValueCheckChangeHandler(const FString& ValueID, bool IsCheck, bool IsPrime)
{
	FDecoSelectionCheckData CheckValueData;
	CheckValueData.selection_id = ValueID;
	CheckValueData.content_id = CurrentOptionID;
	CheckValueData.style_id = CurrentStyleID;
	CheckValueData.is_prime = IsPrime;

#ifdef USE_REF_LOCAL_FILE

	if (IsCheck)
	{
		CreateDecoSelectionCheck(CheckValueData);
	}
	else
	{
		DeleteDecoSelectionCheck(CheckValueData);
	}

#else

	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(TEXT("SELECT * FROM decorate_selch ORDER BY ID ASC"), checks);
	if (IsCheck)
	{
		if (checks.IsEmpty())
		{
			CheckValueData.id = 1;
			CreateDecoSelectionCheck(CheckValueData);
		}
		else
		{
			CheckValueData.id = checks.Last().id + 1;
			CreateDecoSelectionCheck(CheckValueData);
		}
	}
	else
	{
		DeleteDecoSelectionCheck(CheckValueData);
	}

#endif

	CB_All->SetIsChecked(AllChildrenChecked());
}

void UValueWidget::OnParamItemSelectHandler(UParamaterWidgetChild* ParamItem)
{
	if (!SelectParamID.Equals(TEXT("-1")))
	{
		for (auto iter : SubParamsMap)
		{
			iter.Value->SetSelectState(false);
		}
	}
	if (IS_OBJECT_PTR_VALID(ParamItem))
	{
		ParamItem->SetSelectState(true);
		SelectParamID = ParamItem->GetData().Data.id;
		BtnParaDelete->SetIsEnabled(true);
	}
	if (!CurrentStyleID.Equals(TEXT("-1")))
	{
		UpdateValueParamOperateState(EValueParamOperateState::ValueParam);
	}
}

void UValueWidget::OnParameterChangedHandler(UParamaterWidgetChild* InWidget, const FParameterData& ParamData)
{
	//if (SelectParamID != ParamData.Data.id) return;	
	FParameterData NewParamterData = ParamData;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	for (const auto& ParameterIter : CurrentParameters)
	{
		PeerParameters.Add(ParameterIter.Data.name, ParameterIter);
	}
	if (PeerParameters.Contains(NewParamterData.Data.name))
	{
		PeerParameters[NewParamterData.Data.name] = NewParamterData;
	}
	UParameterRelativeLibrary::CalculateParameterValue_LevelSort(GlobalParameters, TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> (), PeerParameters);
	TArray<FParameterData> ParametersToSave;
	//PeerParameters.GenerateValueArray(ParametersToSave);
	for (const auto& iter : CurrentParameters)//保持在风格中的顺序
	{
		if (PeerParameters.Contains(iter.Data.name))
		{
			ParametersToSave.Add(PeerParameters[iter.Data.name]);
		}
	}

	for (auto& Iter : ParametersToSave)
	{
		int32 Index = CurrentParameters.IndexOfByPredicate([Iter](const FParameterData& InOther) { return Iter.Data.id.Equals(InOther.Data.id,ESearchCase::CaseSensitive); });
		if (INDEX_NONE != Index) CurrentParameters[Index] = Iter;

#ifdef USE_REF_LOCAL_FILE
		UpdateParam(Iter);
#else
		FLocalDatabaseParameterLibrary::UpdateStyleParameter(Iter);
#endif
		if (SubParamsMap.Contains(Iter.Data.id))
		{
			SubParamsMap[Iter.Data.id]->UpdateContent(Iter);
			SubParamsMap[Iter.Data.id]->UpdateCurrentParam(Iter);
		}
	}

	OptionParamsUpdate();
	OptionVisibilityUpdate_Current();

	//if (SubValuesMap.Contains(SelectValueID) && SubValuesMap[SelectValueID])
	//{
	//	FDecorateSelection SelctedValueData = SubValuesMap[SelectValueID]->GetData();
	//	OnValueVisibilityEditHandler(SubValuesMap[SelectValueID], SelctedValueData.visibility_exp);
	//}

	/*bool Res = false;
	{
		for (const auto& ParameterIter : CurrentParameters)
		{
			PeerParameters.Add(ParameterIter.Data.name, ParameterIter);
		}
		if (PeerParameters.Contains(NewParamterData.Data.name)) PeerParameters.Remove(NewParamterData.Data.name);
		FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
		Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, PeerParameters, NewParamterData);
	}
	if (false == Res)
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		const auto PreParameter = InWidget->GetData();
		InWidget->UpdateContent(PreParameter);
		return;
	}
	PeerParameters.Add(NewParamterData.Data.name, NewParamterData);
	TArray<FString> AffectedParameters;
	FParameterEffectionParser Parser;
	Res = Parser.FindParametersAffectBySpecificParameter(PeerParameters, NewParamterData.Data.name, AffectedParameters);
	if (Res)
	{
		TArray<FParameterData> ParametersToSave;
		ParametersToSave.Add(NewParamterData);
		while (AffectedParameters.Num() > 0)
		{
			const FString ParameterName = AffectedParameters.Top();
			AffectedParameters.Pop();
			if (ParameterName.Equals(NewParamterData.Data.name)) continue;
			auto ParameterExcludeSelf = PeerParameters;
			ParameterExcludeSelf.Remove(ParameterName);
			bool Res = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, ParameterExcludeSelf, PeerParameters[ParameterName]);
			if (!Res)
			{
				UE_LOG(LogTemp, Error, TEXT("UParameterDetailWidget::OnClickedBtnSave parameter[%s] affect [%s] calculate failed"), *NewParamterData.Data.name, *ParameterName);
				continue;
			}
			ParametersToSave.Add(PeerParameters[ParameterName]);
		}
		for (auto& Iter : ParametersToSave)
		{
			int32 Index = CurrentParameters.IndexOfByPredicate([Iter](const FParameterData& InOther) { return Iter.Data.id == InOther.Data.id; });
			if (INDEX_NONE != Index) CurrentParameters[Index] = Iter;

			FLocalDatabaseParameterLibrary::UpdateStyleParameter(Iter);
			if (SubParamsMap.Contains(Iter.Data.id))
			{
				SubParamsMap[Iter.Data.id]->UpdateContent(Iter);
				SubParamsMap[Iter.Data.id]->UpdateCurrentParam(Iter);
			}
		}
		if (SubValuesMap.Contains(SelectValueID) && SubValuesMap[SelectValueID])
		{
			FDecorateSelection SelctedValueData = SubValuesMap[SelectValueID]->GetData();
			OnValueVisibilityEditHandler(SubValuesMap[SelectValueID], SelctedValueData.visibility_exp);
		}
	}
	else
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Parameter has circle refrence!")).ToString());
		const auto PreParameter = InWidget->GetData();
		InWidget->UpdateContent(PreParameter);
	}*/
}

void UValueWidget::OnParameterPageHandler(UParamaterWidgetChild* InWidget, const FParameterData& ParamData)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  PeerParameters;
	for (const auto& ParameterIter : CurrentParameters)
	{
		PeerParameters.Add(ParameterIter.Data.name, ParameterIter);
	}
	if (PeerParameters.Contains(ParamData.Data.name)) PeerParameters.Remove(ParamData.Data.name);
	UParameterDetailWidget::Get()->SetFolderOrFileParentParams(PeerParameters);
	UParameterDetailWidget::Get()->UpdateContent(ParamData, 1);
	UParameterDetailWidget::Get()->ParamUpdateDataDelegate.BindUFunction(InWidget, FName(TEXT("OnParamDetailUpdateHandler")));
	UParameterDetailWidget::Get()->SetVisibility(ESlateVisibility::Visible);
	UExternalWidget::Get()->AddContentWidget(UParameterDetailWidget::Get());
	UExternalWidget::Get()->SetVisibility(ESlateVisibility::Visible);
}

void UValueWidget::OnParamAddHandler(const FParameterData& ParamData)
{
	FParameterData NewParamData = ParamData;
	NewParamData.Data.id = FGuid::NewGuid().ToString().ToLower();
	NewParamData.Data.main_id = SelectValueID;
	for (auto& iter : SubParamsMap)
	{
		if (iter.Value->GetData().Data.name.Equals(NewParamData.Data.name,ESearchCase::CaseSensitive))
		{
			UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
				FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), 
				FText::FromStringTable(FName("PosSt"), TEXT("This Param is exist,can not add again")).ToString()
			);
			return;
		}
	}

	CreateSelectionParam(NewParamData);

}
void UValueWidget::OnParamAddHandler02(const TArray<FParameterData>& ParamDatas)
{
	/*
	for (auto NewIte : ParamDatas)
	{
		bool bHave = false;
		for (auto& iter : SubParamsMap)
		{
			if (iter.Value->GetData().Data.name == NewIte.Data.name)
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
					FText::FromStringTable(FName("PosSt"), TEXT("This Param is exist,can not add again")).ToString()
				);
				bHave = true;
				break;
			}
		}

		if (bHave)
			break;
	}
	*/

	TArray<FParameterData> TempParamDatas = ParamDatas;
	TempParamDatas.RemoveAll([&](const FParameterData& InData) {
		for (auto& iter : SubParamsMap)
		{
			if (iter.Value->GetData().Data.name.Equals(InData.Data.name,ESearchCase::CaseSensitive))
			{
				UUIFunctionLibrary::ComfirmByOneButtonPopWindow(
					FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
					FText::FromStringTable(FName("PosSt"), TEXT("This Param is exist,can not add again")).ToString()
				);
				return true;
			}
		}
		return false;
		});

	for (auto Ite : TempParamDatas)
	{
		OnParamAddHandler(Ite);
	}
}



void UValueWidget::OnCopySelectopnHandler(const FString& UUID, bool bSuccess)
{
	if (UUID == CopyUUID)
	{
		SearchDecoSelection(CurrentOptionID);
	}
}
void UValueWidget::OnClickedBtnAdd()
{
	bCopy = false;
	StyleWidgetOperate(EWidgetOperateType::Add);
}

void UValueWidget::OnClickedBtnDelete()
{
	StyleWidgetOperate(EWidgetOperateType::Del);
}

void UValueWidget::OnClickedBtnUp()
{
	StyleWidgetOperate(EWidgetOperateType::Up);
}

void UValueWidget::OnClickedBtnDown()
{
	StyleWidgetOperate(EWidgetOperateType::Down);
}

void UValueWidget::OnClickedBtnParaAdd()
{
	StyleValueParamOperate(EValueParamOperateType::Add);
}

void UValueWidget::OnClickedBtnParaDelete()
{
	StyleValueParamOperate(EValueParamOperateType::Del);
}

void UValueWidget::SetRightMenu(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos)
{
	StyleValueDelegate.ExecuteIfBound(ValueItem, IsLeftMouse, MousePos);
}

void UValueWidget::ResetScroll()
{
	SBItem->ScrollToStart();
}


void UValueWidget::OnClickedBtnParamUp()
{
	if (!SelectParamID.Equals(TEXT("-1")))
	{
		GET_STYLE_REF_FILE_DATA_REF();
		int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
			[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
		);
		if (Index != INDEX_NONE)
		{
			auto& ContentData = StyleRefData.content_datas[Index];
			int32 SelectIndex = ContentData.option_datas.IndexOfByPredicate(
				[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
			);
			if (SelectIndex != INDEX_NONE)
			{
				FString CurSelectID = SelectParamID;

				int32 ParamIndex = ContentData.option_datas[SelectIndex].option_params.IndexOfByPredicate([&](const FParameterData& InParam) {
					return InParam.Data.id.Equals(CurSelectID);
					});

				if (ParamIndex == 0)
					return;

				ContentData.option_datas[SelectIndex].option_params.Swap(ParamIndex - 1, ParamIndex);
				UpdateValueParam(ContentData.option_datas[SelectIndex].option_params);

				if (SubParamsMap.Contains(CurSelectID))
				{
					OnParamItemSelectHandler(SubParamsMap[CurSelectID]);
				}
			}
		}
	}
}

void UValueWidget::OnClickedBtnParamDown()
{
	if (!SelectParamID.Equals(TEXT("-1")))
	{
		GET_STYLE_REF_FILE_DATA_REF();
		int32 Index = StyleRefData.content_datas.IndexOfByPredicate(
			[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(CurrentOptionID); }
		);
		if (Index != INDEX_NONE)
		{
			auto& ContentData = StyleRefData.content_datas[Index];
			int32 SelectIndex = ContentData.option_datas.IndexOfByPredicate(
				[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(SelectValueID); }
			);
			if (SelectIndex != INDEX_NONE)
			{
				FString CurSelectID = SelectParamID;

				int32 ParamIndex = ContentData.option_datas[SelectIndex].option_params.IndexOfByPredicate([&](const FParameterData& InParam) {
					return InParam.Data.id.Equals(CurSelectID);
					});

				if (ParamIndex == ContentData.option_datas[SelectIndex].option_params.Num() - 1)
					return;

				ContentData.option_datas[SelectIndex].option_params.Swap(ParamIndex + 1, ParamIndex);
				UpdateValueParam(ContentData.option_datas[SelectIndex].option_params);

				if (SubParamsMap.Contains(CurSelectID))
				{
					OnParamItemSelectHandler(SubParamsMap[CurSelectID]);
				}
			}
		}
	}
}