#pragma once

#include "CoreMinimal.h"
#include "ValueWidgetChild.h"
#include "StyleBaseWidget.h"
#include "ParamaterWidgetChild.h"
#include "DataCenter/RefFile/Data/RefToStyleDataLibrary.h"
#include "DesignStation/SQLite/DecorateStyleRelated/DecoSelectionCheckLibrary.h"
#include "ValueWidget.generated.h"


UENUM(BlueprintType)
enum class EValueParamOperateState : uint8
{
	NoValueNoParam = 0,
	ValueNoParam,
	ValueParam
};

UENUM(BlueprintType)
enum class EValueParamOperateType : uint8
{
	Add = 0,
	Del
};

UCLASS()
class DESIGNSTATION_API UValueWidget : public UStyleBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;
	virtual void ResetOperateState() override;
	virtual void ResetSelectState() override;
	virtual void UpdateOperateState(const ESubItemOrderType& InType) override;
	virtual void StyleWidgetOperate(const EWidgetOperateType& InType) override;
	void UpdateValueParamOperateState(const EValueParamOperateState& InType);
	void StyleValueParamOperate(const EValueParamOperateType& InType);
	void SetBtnAddEnable(bool IsEnable);
	void InsertValue(const FString& InsertID, FDecorateSelection& ValueData);
	void UpdateValueContent(const FString& StyleID, const FString& OptionID);
	void SetSubItemPrime(const FString& ItemID, bool IsPrime);
	void ClearContent();
	void ClearParamContent();

	static UValueWidget* Create();

public:
	void SearchDecoSelection(const FString& OptionId);
	void SearchDecoSelectionCheck(const FString& OptionId, const FString& StyleId);
	void CreateDecoSelection(const FDecorateSelection& NewSelection, const FString& CopyId = TEXT("-1"));
	void CreateDecoSelectionCheck(FDecoSelectionCheckData NewSelectionCheck, bool bUpdateContent = true);
	void DeleteDecoSelectionCheck(const FDecoSelectionCheckData& DelSelectionCheck, bool bUpdateContent = true);
	bool IsDecoSelectionCheck(const FDecoSelectionCheckData& DelSelectionCheck);
	void UpdateDecoSelections(const FDecorateSelection& DecoSelection);
	void UpdateDecoSelections(const TArray<FDecorateSelection>& DecoSelections);
	void UpdateDecoSelectionCheck(const FDecoSelectionCheckData& CheckData);
	void UpdateDecoSelectionCheck(const TArray<FDecoSelectionCheckData>& CheckDatas);
	void SearchSelectionParams(const FString& ParamId, const FString& ParamName, const FString& SelectionId, const FString& StyleId);
	void CreateSelectionParam(const FParameterData& SelectionParam);
	void DeleteSelectionParam(const FString& ParamId, const FString& ValueId, const FString& StyleId);
	void ResetScroll();
	void RenewSelection();
	UFUNCTION()
		void AutoPrimeCheckProcess(const FString& MayPrimeID);
	UFUNCTION()
		void DefaultPrimeManager(const FString& ChangedID, const int32& EditType);

#pragma region SYNC_DATA

		void UpdateAllOptionData(const FString& InContentID, const FString& InStyleID);

		/*
		*  @@ calculate current value params
		*  @@ params add, params delete, params update
		*  @@ InStyleID : current style id; InOptionID : current option id ( option[new name] ---> value[old name] )
		*/
		void OptionParamsUpdate();

		/*
		*  @@ calculate current value visibility
		*  @@ situation 1 : params add, params delete, params update
		*  @@ situation 2 : value visibility expression edit
		*  @@ situation 3 : other prime value change( select calculate ? )
		*/
		void OptionVisibilityUpdate(
			const FString& CurStyleID,
			const FString& CurContentID,
			const FString& CurOptionID,
			FDecorateSelection& EditSelectionOption, 
			const FString& InExpression
		);
		void OptionVisibilityUpdate_Current();

		/*
		*  @@ get current value params
		*/
		void GetCurrentOptionParams(
			const FString& CurStyleID,
			const FString& CurContentID,
			const FString& CurOptionID,
			TArray<FParameterData>& OptionParams,
			int32& CurContentIndex, 
			int32& CurOptionIndex
		);
		
		/*
		*  @@ get other content prime params
		*/
		void GetOtherOptionParams_Prime(
			const FString& CurStyleID,
			const FString& CurContentID,
			TArray<FParameterData>& OptionParams
		);

		/*
		*  @@ check option prime
		*/
		void CheckOptionPrime(
			const FRefToContentData& RefToContentData,
			TArray<FRefToOptionCheck>& RefToOptionChecks
		);


#pragma endregion

private:
	void BindDelegates();

	UFUNCTION()
		void OnCopySelectopnHandler(const FString& UUID, bool bSuccess);
	UFUNCTION()
		void OnDeleteValueHandler(bool Res);
private:
	FString SearchSelectionUUID;
	FString SearchSelectionCheckUUID;
	FString CreateSelectionUUID;
	FString UpdateSelectionUUID;
	FString SearchParamsUUID;
	FString SwapUUID;
	FString CreateParamUUID;
	FString DeleteParamUUID;
	FString CopyUUID;
	FString DeleteCheckUUID;

	int32 NumOfSelectionCheck;
	int32 InsertIndex;
	UPROPERTY()
		TArray<FDecorateSelection> OutUpdateDatas;
	UPROPERTY()
		TArray<FParameterData> CopyParams;
	UPROPERTY()
		TArray<FDecorateSelection> OutDatas;
	UPROPERTY()
		TArray<FDecoSelectionCheckData> CheckDatas;
private:
	void ConstructNewData(FDecorateSelection& NewStyleValue, bool IsInsert);
	void ValueSubItemMoveAction(const FString& ValueID, bool IsUp);
	void UpdateContent(const TArray<FDecorateSelection>& Items);
	void UpdateValueParam(const TArray<FParameterData>& ParamItems);
	void RefreshContent(const TArray<FDecorateSelection>& Items, const FString& ValueID);
	void ClearValueContent();
	bool ValueVisibilityExpCalibration(const FString& InSelectionID, const FString& InExpression, FString& OutValue);	
	bool UpdataSelectionCheck(const FString& InSelectionID, const FString& InContentID, const FString& InStyleID, const bool IsPrime);
	bool AllChildrenChecked();
//��ѡ��Ĳ������������ϲ�
#pragma region VALUE_PARAMETERS

	void AddParam(const FParameterData& Param);
	void UpdateParam(const FParameterData& Param);
	void DeleteParam(const FParameterData& Param);

#pragma endregion

	UFUNCTION()
		void OnValueItemSelectHandler(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos = FVector2D(0, 0));
	
	UFUNCTION()
		void OnValueVisibilityEditHandler(UValueWidgetChild* ValueItem, const FString& Expression);

	/*
	*  @@ change value visibility expression or value
	*/
	bool ValueVisibilityRefresh_NoPrime(const FString& InSelectionID, const FString& InExpression, FString& OutExpression, FString& OutValue);
	bool ValueVisibilityRefresh_Prime(const FString& InSelectionID, const FString& InExpression, FString& OutExpression, FString& OutValue);

	


	UFUNCTION()
		void OnValueCheckChangeHandler(const FString& ValueID, bool IsCheck, bool IsPrime);

	UFUNCTION()
		void OnParamItemSelectHandler(UParamaterWidgetChild* ParamItem);

	UFUNCTION()
		void OnParameterChangedHandler(UParamaterWidgetChild* InWidget, const FParameterData& ParamData);

	UFUNCTION()
		void OnParameterPageHandler(UParamaterWidgetChild* InWidget, const FParameterData& ParamData);

	UFUNCTION()
		void OnParamAddHandler(const FParameterData& ParamData);

		UFUNCTION()
		void OnParamAddHandler02(const TArray<FParameterData>& ParamDatas);

	TArray<FParameterData> CurrentParameters;

public:
	//FStyleItemSelectDelegate StyleItemSelectDelegate;
	FStyleValueDelegate StyleValueDelegate;
private:
	UPROPERTY()
		FString CurrentStyleID;

	UPROPERTY()
		FString CurrentOptionID;

	/*
	*  @@ id --- widget pair
	*/
	UPROPERTY()
		TMap<FString, UValueWidgetChild*> SubValuesMap;


	FString SelectValueID;
	FString OldSelectValueID;

	UPROPERTY()
		TMap<FString, UParamaterWidgetChild*> SubParamsMap;
	FString SelectParamID;
	FString RightId;


	static FString ValueWidgetPath;

	FString CopyId;
	bool bCopy;

protected:
	UFUNCTION()
		void OnClickedBtnAdd();
	UFUNCTION()
		void OnClickedBtnDelete();
	UFUNCTION()
		void OnClickedBtnUp();
	UFUNCTION()
		void OnClickedBtnDown();
	UFUNCTION()
		void OnClickedBtnParaAdd();
	UFUNCTION()
		void OnClickedBtnParaDelete();
	UFUNCTION()
		void SetRightMenu(UValueWidgetChild* ValueItem, bool IsLeftMouse, const FVector2D& MousePos);

	UFUNCTION()
	void OnStateChangedCkbSelect(bool IsChecked);
	UFUNCTION()
	void OnClickedBtnParamUp();
	UFUNCTION()
	void OnClickedBtnParamDown();
private:
	UPROPERTY()
	UButton*    BtnParaAdd;
	UPROPERTY()
	UButton*    BtnParaDelete;
	UPROPERTY()
	UScrollBox* SBPara;

	UPROPERTY(meta = (BindWidget))
	UCheckBox*  CB_All;

	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Para_Up;

	UPROPERTY(meta = (BindWidget))
	UButton* Btn_Para_Down;
public:
	FStyleOpTypeDelegate ValueOpTypeDelegate;
	FStyleOpTypeDelegate ParaOpTypeDelegate;
};
