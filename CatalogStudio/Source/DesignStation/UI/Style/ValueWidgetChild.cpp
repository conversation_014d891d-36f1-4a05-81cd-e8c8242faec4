// Fill out your copyright notice in the Description page of Project Settings.

#include "ValueWidgetChild.h"

#include "Components/Border.h"
#include "Components/Button.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/TextBlock.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"
#include "DesignStation/SubSystem/CatalogNetworkSubsystem.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"
#include "DesignStation/UI/Operation/OperatorWidgetConfig.h"
#include "DesignStation/UI/PopUI/ExpressionPopWidget.h"


#define LOCTEXT_NAMESPACE "Style-ValueData"

FString UValueWidgetChild::ValueWidgetChildPath = TEXT("WidgetBlueprint'/Game/UI/Style/ValueWidgetChild.ValueWidgetChild_C'");

void UValueWidgetChild::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BIND_PARAM_CPP_TO_UMG(ImgStyle, Img_Style);
	BIND_PARAM_CPP_TO_UMG(TxtCode, Txt_Code);
	BIND_PARAM_CPP_TO_UMG(TxtName, Txt_Name);
	BIND_PARAM_CPP_TO_UMG(TxtCodeRead, Txt_CodeRead);
	BIND_PARAM_CPP_TO_UMG(TxtNameRead, Txt_NameRead);
	BIND_PARAM_CPP_TO_UMG(CBSelect, CB_Select);
	BIND_WIDGET_FUNCTION(CBSelect, OnCheckStateChanged, UValueWidgetChild::OnStateChangedCkbSelect);
	BIND_PARAM_CPP_TO_UMG(BorCode, Bor_Code);
	BIND_PARAM_CPP_TO_UMG(BorName, Bor_Name);
	BIND_PARAM_CPP_TO_UMG(BorBody, Bor_Body);
	//����ӵ�UI
	BIND_PARAM_CPP_TO_UMG(BorValue, Bor_Value);
	BIND_PARAM_CPP_TO_UMG(BorExperssion, Bor_Experssion);
	BIND_PARAM_CPP_TO_UMG(TxtVisibilityExp, Edt_Experssion);
	BIND_PARAM_CPP_TO_UMG(TxtVisibilityExpRead, Txt_ExperssionRead);
	BIND_PARAM_CPP_TO_UMG(BtnExperssion, Btn_Experssion);
	BIND_PARAM_CPP_TO_UMG(TxtVisibilityValue, Edt_Value);
	BIND_PARAM_CPP_TO_UMG(TxtVisibilityValueRead, Txt_ValueRead);


	BIND_SLATE_WIDGET_FUNCTION(BorBody, OnMouseButtonDownEvent, FName(TEXT("OnBorBodyClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorName, OnMouseDoubleClickEvent, FName(TEXT("OnBorNameDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorCode, OnMouseDoubleClickEvent, FName(TEXT("OnBorCodeDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorExperssion, OnMouseDoubleClickEvent, FName(TEXT("OnBorExperssionDoubleClicked")));
	BIND_SLATE_WIDGET_FUNCTION(BorValue, OnMouseDoubleClickEvent, FName(TEXT("OnBorValueDoubleClicked")));

	BIND_WIDGET_FUNCTION(BtnExperssion, OnClicked, UValueWidgetChild::OnClickedBtnExperssion);

	BIND_WIDGET_FUNCTION(TxtVisibilityValue, OnTextCommitted, UValueWidgetChild::OnTextCommittedEdtValue);
	BIND_WIDGET_FUNCTION(TxtVisibilityExp, OnTextCommitted, UValueWidgetChild::OnTextCommittedEdtExperssion);
	BIND_WIDGET_FUNCTION(TxtCode, OnTextCommitted, UValueWidgetChild::OnTextCommittedCode);
	BIND_WIDGET_FUNCTION(TxtName, OnTextCommitted, UValueWidgetChild::OnTextCommittedName);
	BIND_WIDGET_FUNCTION(TxtCode, OnTextChanged, UValueWidgetChild::OnTextChangedCode);


	IsPrime = false;
	IsBoxChecked = false;
	IsVisible = false;
	DefaultTextColor = OperatorColor::TextUnselect;

	BindDelegates();
}

UValueWidgetChild* UValueWidgetChild::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UValueWidgetChild>(UValueWidgetChild::ValueWidgetChildPath);
}

void UValueWidgetChild::SetContent(const FDecorateSelection& value)
{
	DecorateValue = value;

	TxtCode->SetText(FText::FromString(value.code));
	TxtCode->SetIsReadOnly(true);
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);

	FString Read = TxtCode->GetText().ToString();
	TxtCodeRead->SetText(FText::FromString(Read/*.Left(15)*/));
	TxtCodeRead->SetVisibility(ESlateVisibility::Visible);

	TxtName->SetText(FText::FromString(value.description));
	FString text = TxtName->GetText().ToString();
	TxtName->SetVisibility(ESlateVisibility::Collapsed);

	TxtNameRead->SetText(FText::FromString(text/*.Left(13)*/));
	TxtNameRead->SetToolTipText(FText::FromString(text));

	TxtNameRead->SetVisibility(ESlateVisibility::Visible);

	//TxtNameRead->SetText(TxtName->GetText());
	TxtVisibilityExp->SetText(FText::FromString(value.visibility_exp));
	TxtVisibilityExpRead->SetText(FText::FromString(value.visibility_exp.Left(18)));
	//TxtVisibilityExpRead->SetText(FText::FromString(value.visibility_exp));
	TxtVisibilityValue->SetText(FText::FromString(value.visibility));
	TxtVisibilityValueRead->SetText(FText::FromString(value.visibility));
	IsVisible = !value.visibility.Equals(TEXT("0.0"));

	if (IsVaildForPrime()) AutoPrimeDelegate.ExecuteIfBound(value.id);
	//DefaultPrimeDelegate.ExecuteIfBound(value.id);

	/*TArray<FFolderTableData> Files = TArray<FFolderTableData>();
	FString SearchCode = value.code;
	if (!SearchCode.IsEmpty())
		FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from file where FOLDER_CODE = '%s' "), *SearchCode), Files);
	const FFolderTableData& FolderRelatived = Files.Num() > 0 ? Files[0] : FFolderTableData();
	if (Files.Num() > 0)
	{
		if (!FolderRelatived.thumbnail_path.IsEmpty())
		{
			UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ProjectContentDir() + FolderRelatived.thumbnail_path);
			if (nullptr != ThumbnailTex)
			{
				ImgStyle->SetBrushFromTexture(ThumbnailTex);
			}
		}
	}*/

	/*
	*  @@ custom file and image exist
	*/
	if (value.data_source == 1 && !value.thumbnail_path.IsEmpty())
	{
		const FString AbsPath = FPaths::Combine(
			FPaths::ProjectContentDir(), value.thumbnail_path
		);
		if (FPaths::FileExists(AbsPath))
		{
			LoadImage(value.thumbnail_path);
		}
		else
		{
			SendDowmloadImageRequest(value.thumbnail_path);
		}
	}
	else
	{
		RefreshImage(value.thumbnail_path);
	}
}

void UValueWidgetChild::SetSyncData(const FDecorateSelection& InValue)
{
	DecorateValue = InValue;

	if (InValue.code.IsEmpty())
	{
		DecorateValue.code = TEXT("");
		DecorateValue.description = TEXT("");
		DecorateValue.thumbnail_path = TEXT("");

		SetContent(DecorateValue);
	}
	else
	{
		bSync = true;
		QueryFolderCode = InValue.code;
		QueryModelOrMaterialRequest(QueryFolderCode);
	}
}

void UValueWidgetChild::SelectSelf()
{
	SelectValueDelegate.ExecuteIfBound(this, true, FVector2D::ZeroVector);
}

void UValueWidgetChild::SetIsCheck(bool IsChecked, bool _IsPrime)
{
	if (CBSelect)
	{
		CBSelect->SetIsChecked(IsChecked);
	}
	IsPrime = _IsPrime;
	SetPriorityDisplay(_IsPrime);
}

void UValueWidgetChild::SetCheck(bool IsChecked)
{
	if (CBSelect)
	{
		CBSelect->SetIsChecked(IsChecked);
		IsBoxChecked = IsChecked;
		if (!IsChecked && IsPrime)
		{
			//DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id);
			SetPriorityDisplay(false);
		}
	}
}

bool UValueWidgetChild::GetIsCheck()
{
	if (CBSelect)
	{
		return CBSelect->IsChecked();
	}
	return false;
}

bool UValueWidgetChild::GetIsVisiable()
{
	//return !DecorateValue.visibility.Equals(TEXT("0.0"));
	const float VisiValue = FCString::Atof(*DecorateValue.visibility);
	return !FMath::IsNearlyZero(VisiValue, 0.01f);
}

void UValueWidgetChild::SetCheckEnable(bool bEnable)
{
	CBSelect->SetIsEnabled(bEnable);
}

void UValueWidgetChild::SetIsPrime(bool _IsPrime)
{
	IsPrime = _IsPrime;
	SetPriorityDisplay(_IsPrime);
}

void UValueWidgetChild::LoadImage(const FString& InRelPath)
{
	const FString AbsPath = FPaths::Combine(
		FPaths::ProjectContentDir(), InRelPath
	);
	UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(AbsPath);
	if (nullptr != ThumbnailTex && ImgStyle)
	{
		ImgStyle->SetBrushFromTexture(ThumbnailTex);
	}
}

void UValueWidgetChild::UpdateCurrentSelection(const FDecorateSelection& Selection)
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(DecorateValue.parent_id); }
	);
	if (ContentIndex != INDEX_NONE)
	{
		int32 OptionIndex = StyleRefData.content_datas[ContentIndex].option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(DecorateValue.id); }
		);
		if(OptionIndex != INDEX_NONE)
		{
			auto& Temp = StyleRefData.content_datas[ContentIndex].option_datas[OptionIndex];
			PARSE_OPTION_ACTION_DATA_TO_REF_DATA(Selection, Temp);
		}
	}

#else
	FLocalDatabaseOperatorLibrary::UpdateDataFromDataBaseBySQL(FString::Printf(TEXT("update decorate_sel set CODE = '%s' , DESCRIPTION = '%s' , SORT_ORDER = '%d' , VISIBILITY = '%s', VISIBILITY_EXP = '%s', PARENT_ID = '%s'where ID = '%s'")
		, *Selection.code, *Selection.description, Selection.sort_order, *Selection.visibility, *Selection.visibility_exp, *Selection.parent_id, *Selection.id));
#endif

	SetContent(Selection);

	//DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id);
}

void UValueWidgetChild::DeleteCurrentSelection()
{
#ifdef USE_REF_LOCAL_FILE

	GET_STYLE_REF_FILE_DATA_REF();
	int32 ContentIndex = StyleRefData.content_datas.IndexOfByPredicate(
		[this](const FRefToContentData& InData)->bool { return InData.content_id.Equals(DecorateValue.parent_id); }
	);
	if(ContentIndex != INDEX_NONE)
	{
		int32 OptionIndex = StyleRefData.content_datas[ContentIndex].option_datas.IndexOfByPredicate(
			[this](const FRefToOptionData& InData)->bool { return InData.option_id.Equals(DecorateValue.id); }
		);
		if(OptionIndex != INDEX_NONE)
		{
			StyleRefData.content_datas[ContentIndex].option_datas.RemoveAt(OptionIndex);
		}

		for(auto& CheckIter : StyleRefData.content_datas[ContentIndex].style_option_checks)
		{
			auto& CheckArr = CheckIter.Value.option_checks;
			TArray<int32> RemoveIndex;
			for(int32 i = CheckArr.Num() - 1; i >= 0; --i)
			{
				if(CheckArr[i].content_id.Equals(DecorateValue.parent_id) && CheckArr[i].option_id.Equals(DecorateValue.id))
				{
					RemoveIndex.AddUnique(i);
				}
			}

			for(auto RI : RemoveIndex)
			{
				CheckArr.RemoveAt(RI);
			}
		}
	}

#else
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_selch WHERE SELECTION_ID = '%s'"), *DecorateValue.id));
	TArray<FParameterTableData> OutParamsTableData = TArray<FParameterTableData>();
	FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("SELECT * FROM decorate_param WHERE MAIN_ID = '%s'"), *DecorateValue.id), OutParamsTableData);
	for (auto& ParamIter : OutParamsTableData)
	{
		FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param_enum WHERE MAIN_ID = '%s'"), *ParamIter.id));
	}
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE FROM decorate_param WHERE MAIN_ID = '%s'"), *DecorateValue.id));
	FLocalDatabaseOperatorLibrary::DeleteDataFromDataBaseBySQL(FString::Printf(TEXT("DELETE from decorate_sel WHERE id = '%s'"), *DecorateValue.id));
#endif

	this->SetVisibility(ESlateVisibility::Collapsed);
	this->RemoveFromParent();

	if (IsPrime)
	{
		DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id, 0);
	}

	DeleteValueDelegate.ExecuteIfBound(true);
	//DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id);
}

void UValueWidgetChild::CheckPrime()
{
	const double VisiValue = FCString::Atod(*DecorateValue.visibility);
	if (IsPrime && FMath::IsNearlyZero(VisiValue, 0.01))
	{
		DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id, 1);
	}
}

void UValueWidgetChild::BindDelegates()
{
#ifdef USE_REF_LOCAL_FILE

	UCatalogNetworkSubsystem::GetInstance()->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UValueWidgetChild::OnDownloadImageHandler);
	UCatalogNetworkSubsystem::GetInstance()->CategoryDataForParseResponseDelegate.AddUniqueDynamic(this, &UValueWidgetChild::OnQueryModelOrMaterialResponseHandler);
	UCatalogNetworkSubsystem::GetInstance()->BackDirectorySearchResponseDelegate.AddUniqueDynamic(this, &UValueWidgetChild::OnQueryCustomResponseHandler);

#else
	ACatalogPlayerController* CatalogPC = Cast<ACatalogPlayerController>(GWorld->GetFirstPlayerController());
	if (CatalogPC)
	{
		CatalogPC->DownloadFileResponseDelegate.AddUniqueDynamic(this, &UValueWidgetChild::OnDownloadResponseHandler);
	}
#endif
}

void UValueWidgetChild::SendDowmloadImageRequest(const FString& ImgPath)
{
	BackendNetUUID.DownloadImage = UCatalogNetworkSubsystem::GetInstance()->SendDownloadFileRequest(ImgPath);
}

void UValueWidgetChild::OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath)
{
	if (BackendNetUUID.DownloadImage.Equals(UUID, ESearchCase::IgnoreCase))
	{
		if (bSuccess && FilePath.IsValidIndex(0))
		{
			LoadImage(FilePath[0]);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file image error [%s]"), *DecorateValue.thumbnail_path);
		}
	}
}

void UValueWidgetChild::QueryModelOrMaterialRequest(const FString& InFolderCode)
{
	NetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendFurnitureOrMaterialDataForParseSearchRequest_FolderCode(
		InFolderCode
	);
}

void UValueWidgetChild::OnQueryModelOrMaterialResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& Data)
{
	if (UUID.Equals(NetUUID.SearchUUID))
	{
		NetUUID.ResetSearchAction();
		if (bSuccess)
		{
			DecorateValue.code = Data.folderCode;
			//
			DecorateValue.description = Data.name;
			//DecorateValue.description = Data.name;
			DecorateValue.thumbnail_path = Data.IsPakFile() ? Data.productImg : Data.mapsImg;

			DecorateValue.data_source = 0;

			UpdateCurrentSelection(DecorateValue);
		}
		else
		{
			QueryCustomRequset(QueryFolderCode);

			/*UI_POP_WINDOW_ERROR(
				FText::FromStringTable(FName("PosSt"), TEXT("code error, please check")).ToString()
			);

			UpdateCurrentSelection(DecorateValue);*/
		}
	}
}

void UValueWidgetChild::QueryCustomRequset(const FString& InFolderCode)
{
	BackendNetUUID.SearchUUID = UCatalogNetworkSubsystem::GetInstance()->SendBackDirectorySearchRequest_FolderCode(
		InFolderCode
	);
}

void UValueWidgetChild::OnQueryCustomResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData)
{
	if (UUID.Equals(BackendNetUUID.SearchUUID, ESearchCase::IgnoreCase))
	{
		if (bSuccess)
		{
			if(DirData.IsValidIndex(0))
			{
				DecorateValue.code = DirData[0].folderCode;
				DecorateValue.description = DirData[0].folderName;
				//DecorateValue.description = DirData[0].folderName;
				DecorateValue.thumbnail_path = DirData[0].thumbnailPath;

				DecorateValue.data_source = 1;
				DecorateValue.custom_id = DirData[0].id;
			}
			else
			{
				DecorateValue.code = QueryFolderCode;
				//DecorateValue.description = TEXT("");
				DecorateValue.thumbnail_path = TEXT("");

				DecorateValue.data_source = 2;
			}
			UpdateCurrentSelection(DecorateValue);
		}
		else
		{
			/*if (!bSync)
			{
				UI_POP_WINDOW_ERROR(
					FText::FromStringTable(FName("PosSt"), TEXT("code error, please check")).ToString()
				);
				bSync = false;
			}*/

			DecorateValue.code = QueryFolderCode;
			//DecorateValue.description = TEXT("");
			DecorateValue.thumbnail_path = TEXT("");
			DecorateValue.visibility_exp = TEXT("1");
			DecorateValue.visibility = TEXT("1");
			DecorateValue.data_source = INDEX_NONE;

			UpdateCurrentSelection(DecorateValue);
		}
	}
}

void UValueWidgetChild::OnStateChangedCkbSelect(bool IsChecked)
{
	IsBoxChecked = IsChecked;
	ValueCheckDelegate.ExecuteIfBound(DecorateValue.id, IsChecked, IsPrime);//更新SelectionCheck数据库

	/*UE_LOG(LogTemp, Error, TEXT("IsVaildForPrime() = %d "), IsVaildForPrime());
	if (IsVaildForPrime()) AutoPrimeDelegate.ExecuteIfBound(DecorateValue.id);
	UE_LOG(LogTemp, Error, TEXT("BoxCheck = %d | IsVisiAble = %d"), IsBoxChecked, IsVisible);*/

	if (!IsChecked && IsPrime)
	{
		//DefaultPrimeDelegate.ExecuteIfBound(DecorateValue.id);
		SetPriorityDisplay(false);
	}
}

void UValueWidgetChild::OnDownloadResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath)
{
	if (DownloadUUID == UUID)
	{
		if (OutRes)
		{
			UTexture2D* FileImage = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir().Append(OutFilePath[0])));
			if (FileImage)
			{
				ImgStyle->SetBrushFromTexture(FileImage);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("download file thumbnail Error"));
		}
	}
}

void UValueWidgetChild::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(true, static_cast<int32>(EColorType::Hover));
		SetTextColor(true);
	}
}

void UValueWidgetChild::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	if (!IsSelect)
	{
		SetBorderColor(false, static_cast<int32>(EColorType::Hover));
		SetTextColor(false);
	}
}


FEventReply UValueWidgetChild::OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	APlayerController* PC = GWorld->GetFirstPlayerController();
	ULocalPlayer* LocalPlayer = PC->GetLocalPlayer();
	FVector2D Position;
	if (LocalPlayer->ViewportClient->GetMousePosition(Position))
	{
		SelectValueDelegate.ExecuteIfBound(this, MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton, Position);
	}
	return FEventReply();
}

void UValueWidgetChild::SetBorderColor(bool IsSelect, const int32& Type)
{
	if (IS_OBJECT_PTR_VALID(BorCode)
		&& IS_OBJECT_PTR_VALID(BorName)
		&& IS_OBJECT_PTR_VALID(BorExperssion)
		&& IS_OBJECT_PTR_VALID(BorValue))
	{

		switch (Type)
		{
		case static_cast<int32>(EColorType::Select):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorExperssion);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderSelect : StyleChildUnselect, BorValue);
			break;
		case static_cast<int32>(EColorType::Hover):
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorCode);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorName);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorExperssion);
			UUIFunctionLibrary::SetBorderBrushColor(IsSelect ? OperatorColor::BorderHover : StyleChildUnselect, BorValue);
			break;
		default:
			break;
		}
	}
}

void UValueWidgetChild::SetTextColor(bool IsSelect)
{
	if (IS_OBJECT_PTR_VALID(TxtCode)
		&& IS_OBJECT_PTR_VALID(TxtName)
		&& IS_OBJECT_PTR_VALID(TxtVisibilityExpRead)
		&& IS_OBJECT_PTR_VALID(TxtVisibilityValueRead))
	{
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : DefaultTextColor, TxtCodeRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : DefaultTextColor, TxtNameRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : DefaultTextColor, TxtVisibilityExpRead);
		UUIFunctionLibrary::SetTextColor(IsSelect ? OperatorColor::TextHoverOrSelect : DefaultTextColor, TxtVisibilityValueRead);
	}
}

void UValueWidgetChild::SetPriorityDisplay(bool IsTrue)
{
	if (IsTrue)
	{
		DefaultTextColor = OperatorColor::BorderSelect;
	}
	else
	{
		DefaultTextColor = OperatorColor::TextUnselect;
	}
	SetTextColor(IsSelect);
}

void UValueWidgetChild::ResetReadOnly()
{
	TxtCode->SetIsReadOnly(true);
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);
	TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
	TxtName->SetIsReadOnly(true);
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);
	TxtVisibilityExp->SetIsReadOnly(true);
	TxtVisibilityExp->SetVisibility(ESlateVisibility::Collapsed);
	TxtVisibilityExpRead->SetVisibility(ESlateVisibility::Visible);
	TxtVisibilityValue->SetIsReadOnly(true);
	TxtVisibilityValue->SetVisibility(ESlateVisibility::Collapsed);
	TxtVisibilityValueRead->SetVisibility(ESlateVisibility::Visible);
}

void UValueWidgetChild::SetSelectState(bool _IsSelect)
{
	IsSelect = _IsSelect;
	SetTextColor(_IsSelect);
	SetBorderColor(_IsSelect, static_cast<int32>(EColorType::Select));
}

FEventReply UValueWidgetChild::OnBorCodeDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectSelf();
	TxtCode->SetIsReadOnly(false);
	TxtCode->SetVisibility(ESlateVisibility::Visible);
	TxtCodeRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtCode->SetUserFocus(PC);
	return FEventReply();
}

FEventReply UValueWidgetChild::OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectSelf();
	TxtName->SetIsReadOnly(false);
	TxtName->SetVisibility(ESlateVisibility::Visible);
	TxtNameRead->SetVisibility(ESlateVisibility::Collapsed);
	auto PC = GWorld->GetFirstPlayerController();
	TxtName->SetUserFocus(PC);
	return FEventReply();
}

FEventReply UValueWidgetChild::OnBorExperssionDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectSelf();
	TxtVisibilityExpRead->SetVisibility(ESlateVisibility::Collapsed);
	TxtVisibilityExp->SetIsReadOnly(false);
	TxtVisibilityExp->SetText(FText::FromString(DecorateValue.visibility_exp));
	TxtVisibilityExp->SetVisibility(ESlateVisibility::Visible);
	auto PC = GWorld->GetFirstPlayerController();
	TxtVisibilityExp->SetUserFocus(PC);
	return FEventReply();
}

FEventReply UValueWidgetChild::OnBorValueDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	SelectSelf();
	TxtVisibilityValueRead->SetVisibility(ESlateVisibility::Collapsed);
	TxtVisibilityValue->SetIsReadOnly(false);
	TxtVisibilityValue->SetText(FText::FromString(DecorateValue.visibility));
	TxtVisibilityValue->SetVisibility(ESlateVisibility::Visible);
	auto PC = GWorld->GetFirstPlayerController();
	TxtVisibilityValue->SetUserFocus(PC);
	return FEventReply();
}

void UValueWidgetChild::OnTextCommittedEdtExperssion(const FText& Text, ETextCommit::Type CommitMethod)
{
	//if (CommitMethod == ETextCommit::Type::OnUserMovedFocus) return;

	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared)
	{
		if(!DecorateValue.visibility_exp.Equals(Text.ToString()))
			ValueVisibilityExpEditDelegatep.ExecuteIfBound(this, Text.ToString());
	}
	else
	{
		TxtVisibilityValueRead->SetText(FText::FromString(DecorateValue.visibility));
		TxtVisibilityExpRead->SetText(FText::FromString(DecorateValue.visibility_exp.Left(18)));
		//TxtVisibilityExpRead->SetText(FText::FromString(DecorateValue.visibility_exp));
	}
	TxtVisibilityExp->SetIsReadOnly(true);
	TxtVisibilityExp->SetVisibility(ESlateVisibility::Collapsed);

	TxtVisibilityExpRead->SetText(FText::FromString(DecorateValue.visibility_exp.Left(18)));
	TxtVisibilityExpRead->SetVisibility(ESlateVisibility::Visible);
}

void UValueWidgetChild::OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod)
{
	//if (CommitMethod == ETextCommit::Type::OnUserMovedFocus) return;
	if (!Text.IsEmpty() && CommitMethod != ETextCommit::Type::OnCleared && Text.IsNumeric())
	{
		FString OutValue = Text.ToString();
		UUIFunctionLibrary::FormatInputValue(OutValue);

		//if (!DecorateValue.visibility.Equals(OutValue))
		ValueVisibilityExpEditDelegatep.ExecuteIfBound(this, OutValue);

		/*DecorateValue.visibility_exp = OutValue;
		DecorateValue.visibility = OutValue;
		TxtVisibilityValueRead->SetText(FText::FromString(DecorateValue.visibility));
		TxtVisibilityValue->SetText(FText::FromString(DecorateValue.visibility));
		TxtVisibilityExpRead->SetText(FText::FromString(DecorateValue.visibility_exp));
		TxtVisibilityExp->SetText(FText::FromString(DecorateValue.visibility_exp));
		UpdateCurrentSelection(DecorateValue);*/
	}
	else
	{
		TxtVisibilityValueRead->SetText(FText::FromString(DecorateValue.visibility));
		TxtVisibilityValue->SetText(FText::FromString(DecorateValue.visibility));
		TxtVisibilityExpRead->SetText(FText::FromString(DecorateValue.visibility_exp.Left(18)));
		TxtVisibilityExp->SetText(FText::FromString(DecorateValue.visibility_exp));
	}
	TxtVisibilityValue->SetIsReadOnly(true);
	TxtVisibilityValue->SetVisibility(ESlateVisibility::Collapsed);
	TxtVisibilityValueRead->SetVisibility(ESlateVisibility::Visible);
}

void UValueWidgetChild::OnVisibilityExpEditHandler(const int32& EditType, const FString& OutExpression)
{
	OnTextCommittedEdtExperssion(FText::FromString(OutExpression), ETextCommit::Type::OnEnter);
}

void UValueWidgetChild::OnClickedBtnExperssion()
{
	SelectSelf();
	BIND_EXPRESSION_WIDGET(1, DecorateValue.visibility_exp, FName(TEXT("OnVisibilityExpEditHandler")));
}



void UValueWidgetChild::OnTextCommittedCode(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (Text.ToString().Equals(DecorateValue.code, ESearchCase::CaseSensitive))
	{
		FString Read = TxtCode->GetText().ToString();
		TxtCodeRead->SetText(FText::FromString(Read/*.Left(15)*/));
		return;
	}
	FString CleanData = TxtCode->GetText().ToString();
	TxtCode->SetIsReadOnly(true);
	TxtCode->SetVisibility(ESlateVisibility::Collapsed);
	FRegexPattern Pattern(TEXT("^[A-Z]+[A-Z0-9\\-]*$"));
	FRegexMatcher RegMatcher(Pattern, CleanData);
	RegMatcher.SetLimits(0, CleanData.Len());
	bool Res = RegMatcher.FindNext();
	if (CommitMethod != ETextCommit::Type::OnCleared /*&& Text.ToString().Len() <= 20*/ && (Res || Text.IsEmpty()))
	{
		if (Text.IsEmpty())
		{
			DecorateValue.code = TEXT("");
			DecorateValue.description = TEXT("");
			DecorateValue.thumbnail_path = TEXT("");

			UpdateCurrentSelection(DecorateValue);
		}
		else
		{
			QueryFolderCode = Text.ToString();
			QueryModelOrMaterialRequest(QueryFolderCode);
		}

		/*TArray<FFolderTableData> OutParams = TArray<FFolderTableData>();
		FString SearchCode = Text.ToString();
		if (!Text.ToString().IsEmpty())
			FLocalDatabaseOperatorLibrary::SelectDataFromDataBaseBySQL(FString::Printf(TEXT("select * from file where FOLDER_CODE = '%s' "), *SearchCode), OutParams);
		DecorateValue.code = Text.ToString();
		const FFolderTableData& FolderRelatived = OutParams.Num() > 0 ? OutParams[0] : FFolderTableData();
		TxtCode->SetText(FText::FromString(DecorateValue.code));
		TxtCode->SetIsReadOnly(true);
		TxtCode->SetVisibility(ESlateVisibility::Collapsed);
		FString Read = TxtCode->GetText().ToString();
		TxtCodeRead->SetText(FText::FromString(Read.Left(15)));
		TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
		if (OutParams.Num() > 0)
		{
			DecorateValue.description = FolderRelatived.folder_name;
			if (!FolderRelatived.thumbnail_path.IsEmpty())
			{
				UTexture2D* ThumbnailTex = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ProjectContentDir() + FolderRelatived.thumbnail_path);
				if (nullptr != ThumbnailTex)
				{
					ImgStyle->SetBrushFromTexture(ThumbnailTex);
				}
			}
		}

		UpdateCurrentSelection(DecorateValue);
		SetContent(DecorateValue);*/
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		UI_POP_WINDOW_ERROR(
			FText::FromStringTable(FName("PosSt"), TEXT("code format error")).ToString()
		);

		TxtCode->SetText(FText::FromString(DecorateValue.code));
		TxtCode->SetIsReadOnly(true);
		TxtCode->SetVisibility(ESlateVisibility::Collapsed);
		FString Read = TxtCode->GetText().ToString();
		TxtCodeRead->SetText(FText::FromString(Read/*.Left(15)*/));
		TxtCodeRead->SetVisibility(ESlateVisibility::Visible);
	}
}

void UValueWidgetChild::OnTextCommittedName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod != ETextCommit::Type::OnCleared /*&& Text.ToString().Len() <= 50*/)
	{
		TxtName->SetText(Text);
		DecorateValue.description = Text.ToString();
		UpdateCurrentSelection(DecorateValue);
	}
	else if (CommitMethod != ETextCommit::Type::OnCleared)
	{
		TxtName->SetText(FText::FromString(DecorateValue.description));
	}
	TxtName->SetIsReadOnly(true);
	TxtName->SetVisibility(ESlateVisibility::Collapsed);
	if (TxtName->GetText().ToString().Len() >= 13)
	{
		FString text = TxtName->GetText().ToString();
		TxtNameRead->SetText(FText::FromString(text/*.Left(13)*/));
		TxtNameRead->SetVisibility(ESlateVisibility::Visible);
		return;
	}
	TxtNameRead->SetText(TxtName->GetText());
	TxtNameRead->SetVisibility(ESlateVisibility::Visible);
}

void UValueWidgetChild::OnTextChangedCode(const FText& Text)
{
}

#undef LOCTEXT_NAMESPACE
