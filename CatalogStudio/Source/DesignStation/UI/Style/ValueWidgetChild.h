// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"

#include "StyleWidgetChild.h"
#include "DataCenter/RefFile/Data/FrontFolderDataLibrary.h"
#include "DataCenter/RefFile/Data/RefToDirectoryDataLibrary.h"
#include "DesignStation/SQLite/DecorateStyleRelated/DecorateSelectionOperatorLibrary.h"

#include "ValueWidgetChild.generated.h"

/**
 *  
 */

struct FCSModelMatData;
class UCheckBox;
class UImage;
class UTextBlock;
class UBorder;
class UEditableText;
class UButton;

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FStyleValueDelegate, UValueWidgetChild*, ValueItem, bool, IsLeftMouse, const FVector2D&, MousePos);

/*
*  @@ check state change logic
*  @@ ValueID : style value ( option ) id
*  @@ IsCheck : check state
*  @@ IsPrime : check value is prime -- use for prime value check to uncheck
*/
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FValueCheckDelegate, const FString&, ValueID, bool, IsCheck, bool, IsPrime);

DECLARE_DYNAMIC_DELEGATE_OneParam(FDeleteValueDelegate,  bool, Res);
DECLARE_DYNAMIC_DELEGATE_OneParam(FAutoPrimeDelegate, const FString&, ValueID);
DECLARE_DYNAMIC_DELEGATE_TwoParams(FValueVisibilityExpEditDelegate, UValueWidgetChild*, ValueItem, const FString&, Expression);

/*
*  @@ prime logic 
*  @@ EditType : effect prime action
*  @@ EditType : 0 -- delete; 1 -- prime check to uncheck; 2 -- uncheck to check
*/
DECLARE_DYNAMIC_DELEGATE_TwoParams(FDefaultPrimeDelegate, const FString&, ValueID, const int32&, EditType);

UCLASS()
class DESIGNSTATION_API UValueWidgetChild : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual void NativeOnInitialized() override;
	static UValueWidgetChild* Create();
	void SetContent(const FDecorateSelection& value);
	void SetSyncData(const FDecorateSelection& InValue);
	void SelectSelf();
	void SetIsCheck(bool IsChecked, bool _IsPrime = false);
	void SetCheck(bool IsChecked);
	bool GetIsCheck();
	bool GetIsVisiable();
	void SetCheckEnable(bool bEnable);

	FDecorateSelection GetData() { return DecorateValue; }
	const FDecorateSelection& GetDataRef() const { return DecorateValue; }

	UFUNCTION(BlueprintImplementableEvent, Category = "ValueWidgetChild")
	void RefreshImage(const FString& ImageURL);
	UFUNCTION(BlueprintImplementableEvent, Category = "ValueWidgetChild")
	void ResetImage();

	void SetIsPrime(bool _IsPrime);
	bool GetIsPrime() { return IsPrime; }

	void LoadImage(const FString& InRelPath);

public:
	void UpdateCurrentSelection(const FDecorateSelection& Selection);
	void DeleteCurrentSelection();

	void CheckPrime();

private:
	void BindDelegates();

	void SendDowmloadImageRequest(const FString& ImgPath);
	UFUNCTION()
	void OnDownloadImageHandler(const FString& UUID, bool bSuccess, const TArray<FString>& FilePath);

	void QueryModelOrMaterialRequest(const FString& InFolderCode);
	UFUNCTION()
	void OnQueryModelOrMaterialResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const FCSModelMatData& Data);

	void QueryCustomRequset(const FString& InFolderCode);
	UFUNCTION()
	void OnQueryCustomResponseHandler(const FString& UUID, bool bSuccess, const FString& Msg, const TArray<FRefDirectoryData>& DirData);

private:
	FString UpdateUUID;
	FString DeleteUUID;
	FString UniqueUUID;
	FString DownloadUUID;
	FString SearchFolder;
	bool CodeUnique;
	bool IsPrime;
	bool IsBoxChecked;
	bool IsVisible;

	/*
	*  @@ mark sync data is update from save file or not
	*  @@ if true, no pop window
	*/
	UPROPERTY()
		bool bSync = false;

	UPROPERTY()
		FString QueryFolderCode;

	UPROPERTY()
		FBackendDirectoryNetUUID BackendNetUUID;

	UPROPERTY()
		FFrontDirectoryNetUUID NetUUID;

	static FString ValueWidgetChildPath;

protected:
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;
	UFUNCTION()
		void OnStateChangedCkbSelect(bool IsChecked);
	UFUNCTION()
		void OnDownloadResponseHandler(const FString& UUID, bool OutRes, const TArray<FString>& OutFilePath);
	UFUNCTION()
		FEventReply OnBorBodyClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent);
	UFUNCTION()
		FEventReply OnBorCodeDoubleClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent);
	UFUNCTION()
		FEventReply OnBorNameDoubleClicked(FGeometry MyGeometry, const FPointerEvent & MouseEvent);
	UFUNCTION()
		FEventReply OnBorExperssionDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		FEventReply OnBorValueDoubleClicked(FGeometry MyGeometry, const FPointerEvent& MouseEvent);
	UFUNCTION()
		void OnTextCommittedEdtExperssion(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedEdtValue(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedBtnExperssion();
	UFUNCTION()
		void OnVisibilityExpEditHandler(const int32& EditType, const FString& OutExpression);
	UFUNCTION()
		void OnTextCommittedCode(const FText & Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextCommittedName(const FText & Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextChangedCode(const FText & Text);
public:
	void SetBorderColor(bool IsSelect, const int32& Type);
	void SetTextColor(bool IsSelect);
	void SetPriorityDisplay(bool IsTrue);
	void ResetReadOnly();
	void SetSelectState(bool Select);
	bool IsVaildForPrime() { return  GetIsCheck() && GetIsVisiable(); }
private:
	UPROPERTY()
		UImage* ImgStyle;
	UPROPERTY()
		UEditableText* TxtCode;
	UPROPERTY()
		UEditableText* TxtName;
	UPROPERTY()
		UCheckBox* CBSelect;
	UPROPERTY()
		UBorder* BorCode;
	UPROPERTY()
		UBorder* BorName;
	UPROPERTY()
		UBorder* BorBody;
	UPROPERTY()
		UTextBlock* TxtCodeRead;
	UPROPERTY()
		UTextBlock* TxtNameRead;
	UPROPERTY()
		UBorder* BorExperssion;
	UPROPERTY()
		UBorder* BorValue;
	UPROPERTY()
		UEditableText* TxtVisibilityExp;
	UPROPERTY()
		UTextBlock* TxtVisibilityExpRead;
	UPROPERTY()
		UButton* BtnExperssion;
	UPROPERTY()
		UEditableText* TxtVisibilityValue;
	UPROPERTY()
		UTextBlock* TxtVisibilityValueRead;
	
	FLinearColor DefaultTextColor;

private:
	UPROPERTY()
		int32  Index;

	UPROPERTY()
		bool IsSelect;

	UPROPERTY()
		FDecorateSelection DecorateValue;

public:
	FStyleValueDelegate SelectValueDelegate;
	FDeleteValueDelegate DeleteValueDelegate;
	FValueVisibilityExpEditDelegate ValueVisibilityExpEditDelegatep;
	FAutoPrimeDelegate AutoPrimeDelegate;
	FValueCheckDelegate ValueCheckDelegate;
	FDefaultPrimeDelegate DefaultPrimeDelegate;

};
