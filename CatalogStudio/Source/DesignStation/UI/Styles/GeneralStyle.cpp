// Fill out your copyright notice in the Description page of Project Settings.

#include "GeneralStyle.h"

#include "Slate/SlateGameResources.h"

FGeneralStyle::FGeneralStyle()
{
}

FGeneralStyle::~FGeneralStyle()
{
}

TSharedPtr<FSlateStyleSet> FGeneralStyle::GeneralStyleInstance = NULL;

void FGeneralStyle::Initialize()
{
	if (!GeneralStyleInstance.IsValid())
	{
		GeneralStyleInstance = Create();
		FSlateStyleRegistry::RegisterSlateStyle(*GeneralStyleInstance);
	}
}

void FGeneralStyle::Shutdown()
{
	FSlateStyleRegistry::UnRegisterSlateStyle(*GeneralStyleInstance);
	ensure(GeneralStyleInstance.IsUnique());
	GeneralStyleInstance.Reset();
}

FName FGeneralStyle::GetStyleSetName()
{
	static FName StyleSetName(TEXT("GeneralStyles"));
	return StyleSetName;
}

TSharedRef<FSlateStyleSet> FGeneralStyle::Create()
{
	TSharedRef<FSlateStyleSet> StyleRef = FSlateGameResources::New(FGeneralStyle::GetStyleSetName(), "/Game/UI/SlateStyles", "/Game/UI/SlateStyles");
	return StyleRef;
}

const ISlateStyle& FGeneralStyle::Get()
{
	return *GeneralStyleInstance;
}