// Fill out your copyright notice in the Description page of Project Settings.

#include "GeneralThemeWidgetStyle.h"


FGeneralThemeStyle::FGeneralThemeStyle()
{
}

FGeneralThemeStyle::~FGeneralThemeStyle()
{
}

const FName FGeneralThemeStyle::TypeName(TEXT("FGeneralThemeStyle"));

const FGeneralThemeStyle& FGeneralThemeStyle::GetDefault()
{
	static FGeneralThemeStyle Default;
	return Default;
}

void FGeneralThemeStyle::GetResources(TArray<const FSlateBrush*>& OutBrushes) const
{
	// Add any brush resources here so that Slate can correctly atlas and reference them
	OutBrushes.Add(&BackgroundImage);
	OutBrushes.Add(&TitleImage); 
}

