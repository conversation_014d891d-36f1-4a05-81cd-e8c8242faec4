// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Styling/SlateWidgetStyle.h"
#include "Runtime/SlateCore/Public/Styling/SlateTypes.h"
#include "GeneralThemeWidgetStyle.generated.h"

/**
 * 
 */
USTRUCT()
struct DESIGNSTATION_API FGeneralThemeStyle : public FSlateWidgetStyle
{
	GENERATED_USTRUCT_BODY()

	FGeneralThemeStyle();
	virtual ~FGeneralThemeStyle();

	// FSlateWidgetStyle
	virtual void GetResources(TArray<const FSlateBrush*>& OutBrushes) const override;
	static const FName TypeName;
	virtual const FName GetTypeName() const override { return TypeName; };

	// Allows us to set default values for our various styles. 
	static const FGeneralThemeStyle& GetDefault();

	// Style that define the appearance of all menu buttons. 
	UPROPERTY(EditAnywhere, Category = Appearance)
	FWindowStyle GeneralWindowStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FButtonStyle XYPlanButtonStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FButtonStyle YZPlanButtonStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FButtonStyle XZPlanButtonStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FTextBlockStyle PlanSelectionTextStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FTextBlockStyle GeneralButtonTextStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FTextBlockStyle GeneralContentTextStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FTextBlockStyle TitleTextStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
		FTextBlockStyle ButtonlTextStyle;
	UPROPERTY(EditAnywhere, Category = Appearance)
		FTextBlockStyle Button2TextStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FButtonStyle GeneralButtonStyle;
	
	UPROPERTY(EditAnywhere, Category = Appearance)
		FButtonStyle	MinButtonStyle;
	UPROPERTY(EditAnywhere, Category = Appearance)
		FButtonStyle	CloseButtonStyle;
	
	UPROPERTY(EditAnywhere, Category = Appearance)
		FButtonStyle	SureButtonStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
		FButtonStyle	CancleButtonStyle;

	UPROPERTY(EditAnywhere, Category = Appearance)
		FSlateBrush	TitleImage;

	UPROPERTY(EditAnywhere, Category = Appearance)
	FSlateBrush	BackgroundImage;

	

};

/**
 */
UCLASS(hidecategories=Object, MinimalAPI)
class UGeneralThemeWidgetStyle : public USlateWidgetStyleContainerBase
{
	GENERATED_BODY()

public:
	/** The actual data describing the widget appearance. */
	UPROPERTY(Category=Appearance, EditAnywhere, meta=(ShowOnlyInnerProperties))
	FGeneralThemeStyle WidgetStyle;

	virtual const struct FSlateWidgetStyle* const GetStyle() const override
	{
		return static_cast< const struct FSlateWidgetStyle* >( &WidgetStyle );
	}
};
