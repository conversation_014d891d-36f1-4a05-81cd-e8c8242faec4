// Fill out your copyright notice in the Description page of Project Settings.

#include "RecoverShowInnerWidget.h"		

#include "Components/Border.h"
#include "Components/Button.h"
#include "Components/TextBlock.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"


FString URecoverShowInnerWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/TempSaveRecover/BP_RecoverShowItem.BP_RecoverShowItem_C'");

bool URecoverShowInnerWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_SLATE_WIDGET_FUNCTION(Bor_BackShow, OnMouseButtonDownEvent, FName("OnBorBackShowMouseDownHandler"));
	BIND_WIDGET_FUNCTION(Btn_Load, OnClicked, URecoverShowInnerWidget::OnClickBtnLoadHandler);

	return true;
}

void URecoverShowInnerWidget::Init(const int32& Index, const FString& ID, const FString& InStr)
{
	FileMark = InStr;

	if(Txt_Index)
	{
		Txt_Index->SetText(FText::FromString(FString::FromInt(Index)));
	}
	FString ShowStr = InStr;
	ShowStr.RemoveFromStart((ID + TEXT("-")));
	ShowStr.RemoveFromEnd(TEXT(".dat"));
	if(Txt_SaveData)
	{
		Txt_SaveData->SetText(FText::FromString(ShowStr));
	}
}

void URecoverShowInnerWidget::SetSelect(bool bSelect)
{
	if(Bor_BackShow)
	{
		Bor_BackShow->SetBrushColor(bSelect ? FLinearColor::Gray : FLinearColor::White);
	}
}

URecoverShowInnerWidget* URecoverShowInnerWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<URecoverShowInnerWidget>(URecoverShowInnerWidget::FilePath);
}

FEventReply URecoverShowInnerWidget::OnBorBackShowMouseDownHandler(FGeometry MyGeometry, const FPointerEvent& MouseEvent)
{
	if(MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		SelectDelegate.ExecuteIfBound(this);

		return FEventReply(true);
	}

	return FEventReply(false);
}

void URecoverShowInnerWidget::OnClickBtnLoadHandler()
{
	OpenDelegate.ExecuteIfBound(FileMark);
}
