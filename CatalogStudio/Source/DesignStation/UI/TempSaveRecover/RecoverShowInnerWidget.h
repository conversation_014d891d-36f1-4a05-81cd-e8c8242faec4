// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "RecoverShowInnerWidget.generated.h"

/**
 * 
 */

class UButton;
class UTextBlock;
class UBorder;
class URecoverShowInnerWidget;

DECLARE_DELEGATE_OneParam(FSelectDelegate, URecoverShowInnerWidget*);
DECLARE_DELEGATE_OneParam(FOpenDelegate, FString);

UCLASS()
class DESIGNSTATION_API URecoverShowInnerWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void Init(const int32& Inde, const FString& ID, const FString& InStr);

	void SetSelect(bool bSelect);

	FString GetFileMark() const { return FileMark; }

	static URecoverShowInnerWidget* Create();

private:
	UFUNCTION()
	FEventReply OnBorBackShowMouseDownHandler(FGeometry MyGeometry, const FPointerEvent& MouseEvent);

	UFUNCTION()
	void OnClickBtnLoadHandler();

private:
	static FString FilePath;

	UPROPERTY()
	FString FileMark;

public:
	FSelectDelegate SelectDelegate;
	FOpenDelegate OpenDelegate;
	
private:
	UPROPERTY(meta=(BindWidget))
	UBorder* Bor_BackShow;

	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_Index;

	UPROPERTY(meta=(BindWidget))
	UTextBlock* Txt_SaveData;

	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Load;
};
