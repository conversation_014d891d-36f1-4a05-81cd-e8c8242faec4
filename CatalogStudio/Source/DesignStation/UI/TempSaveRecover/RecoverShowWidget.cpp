// Fill out your copyright notice in the Description page of Project Settings.

#include "RecoverShowWidget.h"

#include "Components/Button.h"
#include "Components/ScrollBox.h"
#include "DataCenter/RefFile/Data/RefToFileData.h"
#include "DesignStation/DesignStation.h"
#include "DesignStation/RefRelation/RefRelationFunction.h"
#include "DesignStation/UI/UIFunctionLibrary.h"
#include "DesignStation/UI/UIMacroDef.h"

FString URecoverShowWidget::FilePath = TEXT("WidgetBlueprint'/Game/UI/TempSaveRecover/BP_RecoverShow.BP_RecoverShow_C'");
URecoverShowWidget* URecoverShowWidget::Instance = nullptr;

bool URecoverShowWidget::Initialize()
{
	if(!Super::Initialize())
	{
		return false;
	}

	BIND_WIDGET_FUNCTION(Btn_Close, OnClicked, URecoverShowWidget::OnClickBtnClose);
	//BIND_WIDGET_FUNCTION(Btn_Load, OnClicked, URecoverShowWidget::OnClickBtnLoad);

	CurrentSelectWidget = nullptr;

	return true;
}

void URecoverShowWidget::UpdateOldVersionInfo(const FString& InMarkID)
{
	if(InMarkID.IsEmpty())
	{
		return;
	}

	const FString TempFolder = URefToFileData::GetTempSaveFolderRelativeAddress(InMarkID);
	TArray<FString> TempSaveFiles = URefRelationFunction::GetAllFileInFolders(
		FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), TempFolder))
	);

	SB_Detail->ClearChildren();
	CurrentSelectWidget = nullptr;
	for(int32 i = 0; i < TempSaveFiles.Num(); ++i)
	{
		URecoverShowInnerWidget* InnerWidget = URecoverShowInnerWidget::Create();
		InnerWidget->Init(i, InMarkID, TempSaveFiles[i]);
		InnerWidget->SelectDelegate.BindUFunction(this, FName("OnSelectRecoverHandler"));
		InnerWidget->OpenDelegate.BindUFunction(this, FName(TEXT("OnLoadVersionHandler")));
		InnerWidget->SetVisibility(ESlateVisibility::Visible);
		SB_Detail->AddChild(InnerWidget);
		RecoverItemWidgets.Add(InnerWidget);
	}
}

URecoverShowWidget* URecoverShowWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<URecoverShowWidget>(URecoverShowWidget::FilePath);
}

URecoverShowWidget* URecoverShowWidget::GetInstance()
{
	if (URecoverShowWidget::Instance == nullptr)
	{
		URecoverShowWidget::Instance = URecoverShowWidget::Create();
		URecoverShowWidget::Instance->AddToViewport(100);
		URecoverShowWidget::Instance->SetVisibility(ESlateVisibility::Collapsed);
	}
	return URecoverShowWidget::Instance;
}

void URecoverShowWidget::OnSelectRecoverHandler(URecoverShowInnerWidget* NewSelect)
{
	if (CurrentSelectWidget != nullptr)
	{
		CurrentSelectWidget->SetSelect(false);
	}

	CurrentSelectWidget = NewSelect;
	CurrentSelectWidget->SetSelect(true);
}

void URecoverShowWidget::OnLoadVersionHandler(const FString& FileMark)
{
	RecoverVersionDelegate.ExecuteIfBound(FileMark);
	OnClickBtnClose();
}

void URecoverShowWidget::OnClickBtnClose()
{
	CurrentSelectWidget = nullptr;
	RecoverItemWidgets.Empty();
	this->SetVisibility(ESlateVisibility::Collapsed);
}

void URecoverShowWidget::OnClickBtnLoad()
{
	if(IS_OBJECT_PTR_VALID(CurrentSelectWidget))
	{
		RecoverVersionDelegate.ExecuteIfBound(CurrentSelectWidget->GetFileMark());
		OnClickBtnClose();
	}
	else
	{
		UI_POP_WINDOW_ERROR(TEXT("please select a data to load"));
	}
}
