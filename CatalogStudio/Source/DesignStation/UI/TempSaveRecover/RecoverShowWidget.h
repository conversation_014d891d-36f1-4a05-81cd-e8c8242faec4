// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RecoverShowInnerWidget.h"
#include "RecoverShowWidget.generated.h"

/**
 * 
 */

class UScrollBox;
DECLARE_DELEGATE_OneParam(FRecoverVersionDelegate, FString);

UCLASS()
class DESIGNSTATION_API URecoverShowWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void UpdateOldVersionInfo(const FString& InMarkID);

	static URecoverShowWidget* Create();
	static URecoverShowWidget* GetInstance();

public:
	FRecoverVersionDelegate RecoverVersionDelegate;

private:
	UFUNCTION()
	void OnSelectRecoverHandler(URecoverShowInnerWidget* NewSelect);
	UFUNCTION()
	void OnLoadVersionHandler(const FString& FileMark);

	UFUNCTION()
	void OnClickBtnClose();

	UFUNCTION()
	void OnClickBtnLoad();

private:
	UPROPERTY()
	TArray<URecoverShowInnerWidget*> RecoverItemWidgets;

	UPROPERTY()
	URecoverShowInnerWidget* CurrentSelectWidget;

private:
	static FString FilePath;
	static URecoverShowWidget* Instance;

private:
	UPROPERTY(meta=(BindWidget))
	UButton* Btn_Close;

	UPROPERTY(meta=(BindWidget))
	UScrollBox* SB_Detail;

	/*UPROPERTY(meta=(BindWidget))
	UButton* Btn_Load;*/
};
