// Fill out your copyright notice in the Description page of Project Settings.

#include "UIFunctionLibrary.h"

#include "Components/TextBlock.h"
#include "DesignStation/BasicClasses/CatalogPlayerController.h"
#include "Runtime/UMG/Public/Blueprint/UserWidget.h"
#include "Runtime/UMG/Public/Components/Border.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/UMG/Public/Blueprint/SlateBlueprintLibrary.h"
#include "Runtime/Engine/Classes/Engine/Engine.h"
#include "DesignStation/Parameter/ParameterRelativeLibrary.h"
#include "GeneralWidgets/MainLayoutWidget.h"
#include "MainUI/MainToolBarWidget.h"
#include "PopUI/SOneButtonWidget.h"
#include "PopUI/STwoButtonsWidget.h"
#include "SingleComponentUI/SingleComponentPropertyWidget.h"
#include "SingleComponentUI/SingleComponentSectionProperty.h"
#include "SingleComponentUI/SingleComponentSectionToolBar.h"
#include "SingleComponentUI/SingleComponentToolBar.h"
#include "DesignStation/UI/MainUI/FolderAndFileListWidget.h"
#include "GenericPlatform/GenericPlatformHttp.h"


#define LOCTEXT_NAMESPACE "UIFunction"

FString UUIFunctionLibrary::MainUILayoutPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/MainLayout.MainLayout_C'");
FString UUIFunctionLibrary::EditUILayoutPath = TEXT("WidgetBlueprint'/Game/UI/GeneralWidgets/EditLayout.EditLayout_C'");

FString UUIFunctionLibrary::MainUIToolBarPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/MainToolBarUI.MainToolBarUI_C'");
FString UUIFunctionLibrary::SingleComponentToolBarPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentToolBar.SingleComponentToolBar_C'");
FString UUIFunctionLibrary::SingleComponentSectionToolBarPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSectionToolBar.SingleComponentSectionToolBar_C'");

FString UUIFunctionLibrary::FolderAndFileListPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/FolderAndFileListUI.FolderAndFileListUI_C'");

FString UUIFunctionLibrary::FolderAndFilePropertyPath = TEXT("WidgetBlueprint'/Game/UI/MainUI/FolderAndFilePropertyUI.FolderAndFilePropertyUI_C'");
FString UUIFunctionLibrary::SingleComponentPropertyPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentProperty.SingleComponentProperty_C'");
FString UUIFunctionLibrary::SingleComponentSectionPropertyPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSectionProperty.SingleComponentSectionProperty_C'");

FString UUIFunctionLibrary::SingleComponentSectionPath = TEXT("WidgetBlueprint'/Game/UI/SingleComponentUI/SingleComponentSection.SingleComponentSection_C'");

const FString FolderNameAppend = TEXT("_Copy");

FString UUIFunctionLibrary::CatalogEncodeBase64(const FString& InString)
{

	//FString EncodeURL = FGenericPlatformHttp::UrlEncode(InString);
	//UE_LOG(LogTemp, Warning, TEXT("EncodeURL:%s"), *EncodeURL);
	//return EncodeURL;

    if (InString.IsEmpty())
	{
		return FString();
	}

	FString Path = FPaths::GetPath(InString);
	FString Name = FPaths::GetBaseFilename(InString);
	FString Extension = FPaths::GetExtension(InString, true);
	FString NameEncode = FGenericPlatformHttp::UrlEncode(Name);
	NameEncode.Append(Extension);
	FString EncodeURL = FPaths::Combine(Path, NameEncode);
	UE_LOG(LogTemp, Warning, TEXT("EncodeURL:%s"), *EncodeURL);
	return EncodeURL;
}

void UUIFunctionLibrary::ModifyViewportSize(const ECatalogModuleType& InType, bool IsPropertyShow)
{
	FVector2D ToolBarSize = FVector2D(1600.0f, 100.0f);
	FVector2D FolderAndPropertySize = FVector2D(200.0f, 800.0f);
	if (GEngine && GEngine->GameViewport && GWorld && GWorld->WorldType == EWorldType::Game)
	{
		FVector2D CurrentViewportSize;
		GEngine->GameViewport->GetViewportSize(CurrentViewportSize);
		UE_LOG(LogTemp, Log, TEXT("current viewport size:%f,%f"), CurrentViewportSize.X, CurrentViewportSize.Y);
		if (CurrentViewportSize.X > 0 && CurrentViewportSize.Y > 0)
		{
			FVector2D ToolBarRelativeToViewport = ToolBarSize / CurrentViewportSize;
			FVector2D FolderAndPropertyRelativeToViewport = FolderAndPropertySize / CurrentViewportSize;
			switch (InType)
			{
			case ECatalogModuleType::Main:
			{
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderAndPropertyRelativeToViewport.X;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolBarRelativeToViewport.Y;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = IsPropertyShow ? (1.0f - 2 * FolderAndPropertyRelativeToViewport.X) : (1.0f - FolderAndPropertyRelativeToViewport.X);
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
				break;
			}
			case ECatalogModuleType::SingleComponent:
			case ECatalogModuleType::MultiComponent:
			{
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderAndPropertyRelativeToViewport.X;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolBarRelativeToViewport.Y;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - 2 * FolderAndPropertyRelativeToViewport.X;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
				break;
			}
			case ECatalogModuleType::SingleMesh:
			case ECatalogModuleType::MultiMesh:
			{
				break;
			}
			case ECatalogModuleType::Material:
			{
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolBarRelativeToViewport.Y;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderAndPropertyRelativeToViewport.X;
				GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
				break;
			}
			default:
			{
				checkNoEntry();
				break;
			}
			}
		}
	}
}

UUserWidget* UUIFunctionLibrary::CreateLayoutUI(int LayoutType)
{
	UUserWidget* ItemWidget = nullptr;
	switch ((EUILayoutType)LayoutType)
	{
	case EUILayoutType::MainUILayout:
	{
		if (UClass* MainUILayoutBp = LoadClass<UUserWidget>(NULL, *MainUILayoutPath))
		{
			ItemWidget = CreateWidget<UMainLayoutWidget>(GWorld.GetReference(), MainUILayoutBp);
		}
		break;
	}
	case EUILayoutType::EditUILayout:
	{
		if (UClass* EditUILayoutBp = LoadClass<UUserWidget>(NULL, *EditUILayoutPath))
		{
			ItemWidget = CreateWidget<UEditorLayoutWidget>(GWorld.GetReference(), EditUILayoutBp);
		}
	}
	default:
		break;
	}

	return ItemWidget;
}

UUserWidget* UUIFunctionLibrary::CreateToolBarUI(int ToolBarType)
{
	UUserWidget* ItemWidget = nullptr;
	switch ((EToolBarType)ToolBarType)
	{
	case EToolBarType::MainUIToolBar:
	{
		if (UClass* MainUIToolBarBp = LoadClass<UUserWidget>(NULL, *MainUIToolBarPath))
		{
			ItemWidget = CreateWidget<UMainToolBarWidget>(GWorld.GetReference(), MainUIToolBarBp);
		}
		break;
	}
	case EToolBarType::SingleComponentToolBar:
	{
		if (UClass* SingleCompToolBarBp = LoadClass<UUserWidget>(NULL, *SingleComponentToolBarPath))
		{
			ItemWidget = CreateWidget<USingleComponentToolBar>(GWorld.GetReference(), SingleCompToolBarBp);
		}
		break;
	}
	case EToolBarType::SingleComponentSectionToolBar:
	{
		if (UClass* SingleComponentSectionToolBarBp = LoadClass<UUserWidget>(NULL, *SingleComponentSectionToolBarPath))
		{
			ItemWidget = CreateWidget<USingleComponentSectionToolBar>(GWorld.GetReference(), SingleComponentSectionToolBarBp);
		}
		break;
	}
	default:
		break;
	}

	return ItemWidget;
}

UFolderAndFileListWidget* UUIFunctionLibrary::CreateFolderAndFileListUI()
{
	if (UClass* FolderAndFileListBp = LoadClass<UUserWidget>(nullptr, *FolderAndFileListPath))
	{
		return CreateWidget<UFolderAndFileListWidget>(GWorld.GetReference(), FolderAndFileListBp);
	}

	return nullptr;
}

UUserWidget* UUIFunctionLibrary::CreatePropertyUI(int PropertyType)
{
	UUserWidget* ItemWidget = nullptr;
	switch ((EPropertyUIType)PropertyType)
	{
	case EPropertyUIType::FolderAndFileProperty:
	{
		if (UClass* FolderAndFilePropertyBp = LoadClass<UUserWidget>(NULL, *FolderAndFilePropertyPath))
		{
			ItemWidget = CreateWidget<UFolderAndFilePropertyWidget>(GWorld.GetReference(), FolderAndFilePropertyBp);
		}
		break;
	}
	case EPropertyUIType::SingleComponentProperty:
	{
		if (UClass* SingleComponentPropertyBp = LoadClass<UUserWidget>(NULL, *SingleComponentPropertyPath))
		{
			ItemWidget = CreateWidget<USingleComponentPropertyWidget>(GWorld.GetReference(), SingleComponentPropertyBp);
		}
		break;
	}
	case EPropertyUIType::SingleComponenetSectionProperty:
	{
		if (UClass* SingleComponenetSectionPropertyBp = LoadClass<UUserWidget>(NULL, *SingleComponentSectionPropertyPath))
		{
			ItemWidget = CreateWidget<USingleComponentSectionProperty>(GWorld.GetReference(), SingleComponenetSectionPropertyBp);
		}
		break;
	}
	default:
		break;
	}

	return ItemWidget;
}

UUserWidget* UUIFunctionLibrary::CreateSingleComponentItem(int SingleComponentType)
{
	UUserWidget* ItemWidget = nullptr;
	switch ((ESingleComponentItemType)SingleComponentType)
	{
	case ESingleComponentItemType::Section:
	{
		if (UClass* SingleComponenetSectionBp = LoadClass<UUserWidget>(NULL, *SingleComponentSectionPath))
		{
			ItemWidget = CreateWidget<USingleComponentSection>(GWorld.GetReference(), SingleComponenetSectionBp);
		}
		break;
	}
	default:
		break;
	}

	return ItemWidget;
}

FString UUIFunctionLibrary::GetCurrentValue(const FString& ParamValue, const FString& MaxValue, const FString& MinValue)
{
	FString OutValue = ParamValue;
	if (!MaxValue.IsEmpty() && !MinValue.IsEmpty())
	{
		if (FCString::Atof(*MaxValue) < FCString::Atof(*MinValue))
		{
			return ParamValue;
		}
	}
	if (!MaxValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) < FCString::Atof(*MaxValue))
			? ParamValue : MaxValue;
	}
	if (!MinValue.IsEmpty())
	{
		OutValue = (FCString::Atof(*OutValue) > FCString::Atof(*MinValue))
			? OutValue : MinValue;
	}
	return OutValue;
}



void UUIFunctionLibrary::SetBorderBrush(const FLinearColor& InColor, UBorder* InBorder)
{
}

void UUIFunctionLibrary::SetBorderBrushColor(const FLinearColor& InColor, UBorder* InBorder)
{
	if (InBorder)
	{
		InBorder->SetBrushColor(InColor);
	}
}

void UUIFunctionLibrary::SetBorderBrush(bool IsSelect, UBorder* InBorder)
{
	/* DrawAs(ESlateBrushDrawType::Image)
	   Tiling(ESlateBrushTileType::NoTile)
	   Mirroring(ESlateBrushMirrorType::NoMirror)
	   ImageType(ESlateBrushImageType::NoImage)
	   TintColor(FLinearColor::White)
	   ImageSize(SlateBrushDefs::DefaultImageSize, SlateBrushDefs::DefaultImageSize)*/
	FSlateBrush BorderBrush;
	BorderBrush.DrawAs = ESlateBrushDrawType::Image;
	BorderBrush.Tiling = ESlateBrushTileType::NoTile;
	BorderBrush.Mirroring = ESlateBrushMirrorType::NoMirror;
	BorderBrush.ImageType = ESlateBrushImageType::NoImage;
	BorderBrush.ImageSize = FVector2D(32.0f, 32.0f);
	BorderBrush.TintColor = IsSelect ? SectionSelectColor : SectionUnSelectColor;
	if (InBorder)
	{
		InBorder->SetBrush(BorderBrush);
	}
}

void UUIFunctionLibrary::SetButtonBrush(bool IsSelect, UButton* InButton)
{
	FSlateBrush ButtonBrush;
	ButtonBrush.DrawAs = ESlateBrushDrawType::Image;
	ButtonBrush.Tiling = ESlateBrushTileType::NoTile;
	ButtonBrush.Mirroring = ESlateBrushMirrorType::NoMirror;
	ButtonBrush.ImageType = ESlateBrushImageType::NoImage;
	ButtonBrush.ImageSize = FVector2D(32.0f, 32.0f);
	ButtonBrush.TintColor = IsSelect ? SectionSelectColor : SectionUnSelectColor;

	FButtonStyle ButtonStyle;
	ButtonStyle.SetNormal(ButtonBrush);
	ButtonStyle.SetHovered(ButtonBrush);
	ButtonStyle.SetPressed(ButtonBrush);
	if (InButton)
	{
		InButton->SetStyle(ButtonStyle);
	}
}

void UUIFunctionLibrary::SetTextColor(const FLinearColor& InColor, UTextBlock* InText)
{
	if (InText)
	{
		InText->SetColorAndOpacity(InColor);
	}
}

void UUIFunctionLibrary::SetEditableTextColor(const FLinearColor& InColor, UEditableText* InText)
{
	if (InText)
	{
		//InText->SetColorAndOpacity(InColor);
		//InText->ColorAndOpacity_DEPRECATED = 
	}
}

FVector2D UUIFunctionLibrary::GetMouseMoveVector(const FPointerEvent& MouseEvent, const FVector2D& LastMousePos)
{

	FVector2D LocalPixelPosition;// no use
	FVector2D LocalViewportPosition;
	USlateBlueprintLibrary::AbsoluteToViewport(GWorld, MouseEvent.GetScreenSpacePosition(), LocalPixelPosition, LocalViewportPosition);
	return LocalViewportPosition - LastMousePos;
}

void UUIFunctionLibrary::SaveUserInfo(const FUserInfoRemebered& InUserInfo)
{
	URemeberUserName* SaveLoadInstance = URemeberUserName::Get();
	SaveLoadInstance->SetRemeberData(InUserInfo);
	SaveLoadInstance->SaveRemeberDataToFile(FPaths::ConvertRelativePathToFull(FPaths::ProjectSavedDir() + TEXT("1.bin")));
}

FUserInfoRemebered UUIFunctionLibrary::GetUserInfo()
{
	URemeberUserName* SaveLoadInstance = URemeberUserName::Get();
	SaveLoadInstance->LoadRemeberDataFromFile(FPaths::ConvertRelativePathToFull(FPaths::ProjectSavedDir() + TEXT("1.bin")));
	return SaveLoadInstance->GetRemeberDataRef();
}

void UUIFunctionLibrary::FormatInputValue(FString& Value)
{
	Value = UParameterPropertyData::FormatParameterValue(Value).ToString();
}

bool UUIFunctionLibrary::CalculateExpressionValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters,const FString& InExpression, FString& OutValue, FString& OutFormatExpress)
{
	TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>  GlobalParameters = ACatalogPlayerController::Get()->GetGlobalParameterMap();
	//FLocalDatabaseParameterLibrary::RetriveGlobalParameters(GlobalParameters);
	bool CaculateValueResult = UParameterRelativeLibrary::CalculateParameterExpression(GlobalParameters, InParentParameters, InLocalParameters, InExpression, OutValue, OutFormatExpress);
	if (!CaculateValueResult)
	{
		UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(),
			FText::FromStringTable(FName("PosSt"), TEXT("Caculate value wrong!")).ToString());
		/*SOneButtonWidget::PopupModalWindow(NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorKey", "ERROR"),
			NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorContentKey", "Caculate value wrong!"), NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorButtonKey", "OK"));*/
		return false;
	}
	if (OutValue.IsNumeric())
		UUIFunctionLibrary::FormatInputValue(OutValue);
	return true;
}

bool UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(const FString& Title, const FString& ContentMessage)
{
	FText TitleMessage = FText::FromString(Title);
	FText TipMessage = FText::FromString(ContentMessage);
	EPopButtonType ButtonType = STwoButtonsWidget::PopupModalWindow(
		FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "TitltKey", "{0}"), TitleMessage)
		, FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "ContentKey", "{0}"), TipMessage)
		, FText::FromStringTable(FName("PosSt"), TEXT("Confirm"))
		, FText::FromStringTable(FName("PosSt"), TEXT("Cancel")));

	if (ButtonType == EPopButtonType::Confirm)
	{
		return true;
	}
	return false;
}

void UUIFunctionLibrary::ComfirmByOneButtonPopWindow(const FString& Title, const FString& ContentMessage)
{
	FText TitleMessage = FText::FromString(Title);
	FText TipMessage = FText::FromString(ContentMessage);
	SOneButtonWidget::PopupModalWindow(
		FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorKey", "{0}"), TitleMessage)
		, FText::Format(NSLOCTEXT(LOCTEXT_NAMESPACE, "ErrorOneContentKey", "{0}"), TipMessage)
		, FText::FromStringTable(FName("PosSt"), TEXT("OK")));
}

bool UUIFunctionLibrary::ComfirmByOneButtonPopWindow_Retry()
{
	return SOneButtonWidget::PopupModalWindow(
		FText::FromStringTable(FName("PosSt"), TEXT("Error")), 
		FText::FromStringTable(FName("PosSt"), TEXT("Download Depend File Error")), 
		FText::FromStringTable(FName("PosSt"), TEXT("Retry")));
}

bool UUIFunctionLibrary::ComfirmByTwoButtonToDeleteFolders()
{
	return UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
		FText::FromStringTable(FName("PosSt"), TEXT("Make Sure To Delete This Folder ?")).ToString());
}

bool UUIFunctionLibrary::ComfirmByTwoButtonToDeleteFiles()
{
	return UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
		FText::FromStringTable(FName("PosSt"), TEXT("Make Sure To Delete This File ?")).ToString());
}

void UUIFunctionLibrary::ComfirmByOneButtonToPaste()
{
	return UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Warning")).ToString(),
		FText::FromStringTable(FName("PosSt"), TEXT("Please Copy Or Cut File First ?")).ToString());
}


#undef LOCTEXT_NAMESPACE
