// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "GeneralWidgets/EditorLayoutWidget.h"
#include "MainUI/FolderWidget.h"
#include "MainUI/FolderAndFilePropertyWidget.h"
#include "DataCenter/Parameter/ParameterData.h"
#include "DesignStation/SaveAndLoad/RemeberUserName.h"
#include "UIFunctionLibrary.generated.h"

/**
 * 
 */

UENUM(BlueprintType)
enum class EUILayoutType : uint8
{
	MainUILayout = 0,
	EditUILayout
};

UENUM(BlueprintType)
enum class EToolBarType : uint8
{
	MainUIToolBar = 0,
	SingleComponentToolBar,
	SingleComponentSectionToolBar
};

UENUM(BlueprintType)
enum class EPropertyUIType : uint8
{
	FolderAndFileProperty = 0,
	SingleComponentProperty,
	SingleComponenetSectionProperty
};

UENUM(BlueprintType)
enum class ESingleComponentItemType : uint8
{
	Section = 0
};

UENUM(BlueprintType)
enum class ESectionPropertyItemType : uint8
{
	Point = 0,
	Line,
	Rectangle,
	Ellipse,
	Cuboid
};

UENUM(BlueprintType)
enum class ESectionSelectType : uint8
{
	Point = 0,
	Line,
	Rectangle,
	Ellipse,
	Cuboid
};

UENUM(BlueprintType)
enum class ECatalogModuleType : uint8
{
	Main = 0,
	SingleComponent,
	MultiComponent,
	SingleMesh,
	MultiMesh,
	Material
};

const FLinearColor SectionSelectColor = FLinearColor(0.022174, 0.467784, 0.887923, 1.0);
const FLinearColor SectionUnSelectColor = FLinearColor::White;
class UUserWidget;

UCLASS(Blueprintable)
class DESIGNSTATION_API UUIFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	UFUNCTION(BlueprintCallable, Category = "UIFunctionLibrary")
	static FString CatalogEncodeBase64(const FString& InString);


	static void ModifyViewportSize(const ECatalogModuleType& InType, bool IsPropertyShow);

	static UUserWidget* CreateLayoutUI(int LayoutType);

	static UUserWidget* CreateToolBarUI(int ToolBarType);

	static UFolderAndFileListWidget* CreateFolderAndFileListUI();

	static UUserWidget* CreatePropertyUI(int PropertyType);

	static UUserWidget* CreateSingleComponentItem(int SingleComponentType);

	static FString GetCurrentValue(const FString & ParamValue, const FString & MaxValue, const FString & MinValue);

	static void SetBorderBrush(const FLinearColor& InColor, UBorder* InBorder);
	static void SetBorderBrushColor(const FLinearColor& InColor, UBorder* InBorder);
	static void SetBorderBrush(bool IsSelect, UBorder* InBorder);
	static void SetButtonBrush(bool IsSelect, UButton* InButton);
	static void SetTextColor(const FLinearColor& InColor, UTextBlock* InText);
	static void SetEditableTextColor(const FLinearColor& InColor, UEditableText* InText);

	static FVector2D GetMouseMoveVector(const FPointerEvent & MouseEvent, const FVector2D& LastMousePos);

	static void SaveUserInfo(const FUserInfoRemebered& InUserInfo);
	static FUserInfoRemebered GetUserInfo();
	
	template<class T>
	static void SetWidgetBrush(bool IsSelect, T* InWidget)
	{
		FSlateBrush WidgetBrush;
		WidgetBrush.DrawAs = ESlateBrushDrawType::Image;
		WidgetBrush.Tiling = ESlateBrushTileType::NoTile;
		WidgetBrush.Mirroring = ESlateBrushMirrorType::NoMirror;
		WidgetBrush.ImageType = ESlateBrushImageType::NoImage;
		WidgetBrush.ImageSize = FVector2D(32.0f, 32.0f);
		WidgetBrush.TintColor = IsSelect ? SectionSelectColor : SectionUnSelectColor;
		if (InWidget)
		{
			InWidget->SetBrush(WidgetBrush);
		}
	}

	template<class T1, class T2>
	static T1* CreatePropertyView(const T2& InData)
	{
		T1* ResultView = nullptr;
		ResultView = T1::Create();
		ResultView->UpdateContent(InData);
		return ResultView;
	}

	template<class T>
	static T* CreateUIWidget()
	{
		return T::Create();
	}

	template<class T>
	static T* UIWidgetCreate(const FString& BPPath)
	{
		UClass* ItemBp = LoadClass<UUserWidget>(NULL, *BPPath);
		checkf(ItemBp, TEXT("load bp error, error path : %s"), *BPPath);
		T* ItemWidget = CreateWidget<T>(GWorld.GetReference(), ItemBp);
		checkf(ItemWidget, TEXT("create item widget error!"));
		return ItemWidget;
	}

	static void FormatInputValue(FString& Value);
	static bool CalculateExpressionValue(const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)> & InParentParameters, const TMap<FString, STRING_CASE_SENSITIVE_DEFINE(FParameterData)>& InLocalParameters, const FString& InExpression, FString& OutValue, FString& OutFormatExpress);
	static bool ConfirmByTwoButtonPopWindow(const FString& Title, const FString& ContentMessage);
	static void ComfirmByOneButtonPopWindow(const FString& Title, const FString& ContentMessage);

	static bool ComfirmByOneButtonPopWindow_Retry();

	static bool ComfirmByTwoButtonToDeleteFolders();
	static bool ComfirmByTwoButtonToDeleteFiles();
	static void ComfirmByOneButtonToPaste();

private:
	//layout
	static FString MainUILayoutPath;
	static FString EditUILayoutPath;
	//toolbar
	static FString MainUIToolBarPath;
	static FString SingleComponentToolBarPath;
	static FString SingleComponentSectionToolBarPath;
	//main ui view
	static FString FolderAndFileListPath;
	//detail ui
	static FString FolderAndFilePropertyPath;
	static FString SingleComponentPropertyPath;
	static FString SingleComponentSectionPropertyPath;
	//single component item
	static FString SingleComponentSectionPath;
};
