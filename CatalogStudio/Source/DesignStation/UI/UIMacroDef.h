// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#define DECL_PARAM_TYPE(X) decltype(X)

#define BIND_PARAM_CPP_TO_UMG(TCPPName, TUMGName)\
do{TCPPName = static_cast<DECL_PARAM_TYPE(TCPPName)>(this->GetWidgetFromName(#TUMGName));} while (0)

#define BIND_WIDGET_FUNCTION(TWidgetName, TWidgetAction, TWidgetInvokerFunc)\
do{TWidgetName->TWidgetAction.AddUniqueDynamic(this, &TWidgetInvokerFunc);} while(0)

#define BIND_SLATE_WIDGET_FUNCTION(TWidgetName, TWidgetAction, InvokerFuncName)\
do{TWidgetName->TWidgetAction.BindUFunction(this, InvokerFuncName);} while(0)

#define BIND_EXPRESSION_WIDGET(EditType, InExpression, InvokerFuncName)\
do{\
	UExpressionPopWidget::Get()->UpdateContent(EditType, InExpression);\
	UExpressionPopWidget::Get()->ExpressionDelegate.BindUFunction(this, InvokerFuncName);\
	UExpressionPopWidget::Get()->SetVisibility(ESlateVisibility::Visible);\
}while(0)

#define UI_POP_WINDOW_ERROR(Msg) \
UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), Msg)

#define UI_POP_WINDOW_ERROR_ST(Msg) \
UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Error")).ToString(), FText::FromStringTable(FName("PosSt"), Msg).ToString())

#define UI_POP_WINDOW_TIP(Msg) \
UUIFunctionLibrary::ComfirmByOneButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), Msg)

#define UI_POP_WINDOW_TIP_CONFIRM(Msg) \
bool ConfirmRes = UUIFunctionLibrary::ConfirmByTwoButtonPopWindow(FText::FromStringTable(FName("PosSt"), TEXT("Prompt")).ToString(), FText::FromStringTable(FName("PosSt"), Msg).ToString())

#define UI_POP_WINDOW_DOWNLOAD_ERROR_RETRY() \
bool bClickRetry = UUIFunctionLibrary::ComfirmByOneButtonPopWindow_Retry()

#define OPERATOR_SUCCESS() \
UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance(); \
OnHold->SwitchState(0); \
OnHold->SetVisibility(ESlateVisibility::Collapsed)

#define OPERATOR_FAILED_WITH_POP_MSG(Msg) \
UOperationOnHoldWidget* OnHold = UOperationOnHoldWidget::GetInstance(); \
OnHold->SetVisibility(ESlateVisibility::Collapsed); \
UI_POP_WINDOW_ERROR(Msg)

#define LOG_MSG_UPLOAD(Msg, Success) \
UE_LOG(LogTemp, Log, TEXT("Upload [%s] --- [%d]"), *##Msg, Success)
