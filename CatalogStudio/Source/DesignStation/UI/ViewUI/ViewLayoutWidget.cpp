// Fill out your copyright notice in the Description page of Project Settings.

#include "ViewLayoutWidget.h"

#include "DesignStation/UI/UIFunctionLibrary.h"


FString UViewLayoutWidget::ViewLayoutPath = TEXT("WidgetBlueprint'/Game/UI/ViewUI/BP_ViewLayoutUI.BP_ViewLayoutUI_C'");

bool UViewLayoutWidget::Initialize()
{
	return Super::Initialize();
}

UViewLayoutWidget* UViewLayoutWidget::Create()
{
	return UUIFunctionLibrary::UIWidgetCreate<UViewLayoutWidget>(UViewLayoutWidget::ViewLayoutPath);
}

void UViewLayoutWidget::ExitView()
{
	ViewLayoutExitDelegate.ExecuteIfBound(EViewLayoutActionType::E_Exit);
}

