// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DesignStation/UI/GeneralWidgets/LayoutBaseWidget.h"
#include "ViewLayoutWidget.generated.h"

/**
*
*/

UENUM(BlueprintType)
enum class EViewLayoutActionType : uint8
{
	E_Exit = 0    UMETA(DisplayName = "Exit The View Module"),
};

DECLARE_DELEGATE_OneParam(FViewLayoutActionDelegate, EViewLayoutActionType);

UCLASS(Blueprintable, BlueprintType)
class DESIGNSTATION_API UViewLayoutWidget : public ULayoutBaseWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	static UViewLayoutWidget* Create();

public:
	FViewLayoutActionDelegate ViewLayoutExitDelegate;

//BP Function
public:
	UFUNCTION(BlueprintCallable, Category = "ViewLayoutWidget")
		void ExitView();

private:
	static FString ViewLayoutPath;
};

