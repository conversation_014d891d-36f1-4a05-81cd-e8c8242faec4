// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget_Login.h"

#include "UIMacroDef.h"
#include "Runtime/UMG/Public/Components/Image.h"
#include "Runtime/UMG/Public/Components/EditableText.h"
#include "Runtime/UMG/Public/Components/Button.h"
#include "Runtime/Engine/Classes/Engine/Texture2D.h"
#include "Runtime/UMG/Public/Components/CheckBox.h"
#include "Runtime/UMG/Public/Components/SizeBox.h"
#include "CustomMaterialEdit/CatalogFunctionLibrary.h"


const FString LoginBackground = TEXT("System/Logo/1.png");
const FString LoginLogo = TEXT("System/Logo/2.png");

bool UWidget_Login::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	BIND_PARAM_CPP_TO_UMG(ImgBackground, Img_Background);
	BIND_PARAM_CPP_TO_UMG(ImgLogo, Img_LOGO);
	BIND_PARAM_CPP_TO_UMG(EdtUserID, Edt_UserName);
	BIND_WIDGET_FUNCTION(EdtUserID, OnTextChanged, UWidget_Login::OnTextChangedUserName);
	BIND_WIDGET_FUNCTION(EdtUserID, OnTextCommitted, UWidget_Login::OnTextCommittedUserName);
	BIND_PARAM_CPP_TO_UMG(EdtPassWord, Edt_PassWord);
	BIND_WIDGET_FUNCTION(EdtPassWord, OnTextChanged, UWidget_Login::OnTextChangedPassWord);
	BIND_WIDGET_FUNCTION(EdtPassWord, OnTextCommitted, UWidget_Login::OnTextCommittedPassWord);
	BIND_PARAM_CPP_TO_UMG(CkbRemeberMe, Ckb_RemeberUserName);
	BIND_WIDGET_FUNCTION(CkbRemeberMe, OnCheckStateChanged, UWidget_Login::OnStateChangedCkbRemeberUserName);
	BIND_PARAM_CPP_TO_UMG(BtnLogin, Btn_Login);
	BIND_WIDGET_FUNCTION(BtnLogin, OnClicked, UWidget_Login::OnClickedLogin);
	BIND_PARAM_CPP_TO_UMG(SZErrorMessage, SZ_ErrorMessage);
	SZErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	BIND_PARAM_CPP_TO_UMG(BtnForgotPassword, Btn_ForgotPassword);
	BIND_WIDGET_FUNCTION(BtnForgotPassword, OnClicked, UWidget_Login::OnClickedBtnForgotPassword);

	LoadBackgroundAndLogoImage();

	return true;
}

void UWidget_Login::UpdateLastestUserID(const FString& LastestUserID, const FString& LastestUserPW, bool IsChecked)
{
	if (IsChecked && EdtUserID)
	{
		UE_LOG(LogTemp, Log, TEXT("update edt user id"));
		EdtUserID->SetHintText(FText::FromString(TEXT("")));
		EdtUserID->SetText(FText::FromString(LastestUserID));
	}
	if (IsChecked && EdtPassWord)
	{
		EdtPassWord->SetHintText(FText::FromString(TEXT("")));
		EdtPassWord->SetText(FText::FromString(LastestUserPW));
	}
	if (CkbRemeberMe)
	{
		CkbRemeberMe->SetIsChecked(IsChecked);
	}
}

void UWidget_Login::UpdateLoginErrorState()
{
	if (SZErrorMessage)
	{
		SZErrorMessage->SetVisibility(ESlateVisibility::Visible);
	}
}

void UWidget_Login::ResetPasswordState()
{
	if (EdtUserID)
	{
		EdtUserID->SetHintText(FText::FromStringTable(FName("PosSt"), TEXT("username")));
		EdtUserID->SetText(FText::FromString(TEXT("")));
	}
	if (EdtPassWord)
	{
		EdtPassWord->SetHintText(FText::FromStringTable(FName("PosSt"), TEXT("password")));
		EdtPassWord->SetText(FText::FromString(TEXT("")));
	}
}

bool UWidget_Login::IsRemeberChecked()
{
	if (CkbRemeberMe)
	{
		return CkbRemeberMe->IsChecked();
	}
	return false;
}

void UWidget_Login::Clear()
{
	if (ImgBackground && EdtUserID && EdtPassWord && BtnLogin && CkbRemeberMe && SZErrorMessage && BtnForgotPassword)
	{
		ImgBackground = nullptr;
		EdtUserID = nullptr;
		EdtPassWord = nullptr;
		BtnLogin = nullptr;
		CkbRemeberMe = nullptr;
		SZErrorMessage = nullptr;
		BtnForgotPassword = nullptr;
	}
}

void UWidget_Login::LoadBackgroundAndLogoImage()
{
	if (ImgBackground && ImgLogo)
	{
		UTexture2D* BackgroundTexture = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir().Append(LoginBackground)));
		UTexture2D* LogoTexture = FCatalogFunctionLibrary::LoadTextureFromJPG(FPaths::ConvertRelativePathToFull(FPaths::ProjectContentDir().Append(LoginLogo)));
		ImgBackground->SetBrushFromTexture(BackgroundTexture);
		ImgLogo->SetBrushFromTexture(LogoTexture);
	}
}

void UWidget_Login::OnTextChangedUserName(const FText& Text)
{
	if (SZErrorMessage)
	{
		SZErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}
	if (EdtUserID)
	{
		EdtUserID->SetHintText(FText::FromString(TEXT("")));
	}
}

void UWidget_Login::OnTextCommittedUserName(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::OnEnter)
	{
		OnClickedLogin();
	}
}

void UWidget_Login::OnTextChangedPassWord(const FText& Text)
{
	if (SZErrorMessage)
	{
		SZErrorMessage->SetVisibility(ESlateVisibility::Collapsed);
	}
	if (EdtPassWord)
	{
		EdtPassWord->SetHintText(FText::FromString(TEXT("")));
	}
}

void UWidget_Login::OnTextCommittedPassWord(const FText& Text, ETextCommit::Type CommitMethod)
{
	if (CommitMethod == ETextCommit::OnEnter)
	{
		OnClickedLogin();
	}
}

void UWidget_Login::OnClickedLogin()
{
	LoginDelegate.Broadcast(EdtUserID->GetText().ToString().ToLower(), EdtPassWord->GetText().ToString().ToLower());
}

void UWidget_Login::OnStateChangedCkbRemeberUserName(bool IsRemeber)
{

}

void UWidget_Login::OnClickedBtnForgotPassword()
{

}
