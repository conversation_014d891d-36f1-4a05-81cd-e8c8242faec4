// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Widget_Login.generated.h"

class UImage;
class UEditableText;
class UButton;
class UCircularThrobber;
class UTextBlock;
class <PERSON>heckBox;
class USizeBox;

/**
 * 
 */

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FLoginDelegate, const FString&, UserID, const FString&, PassWord);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FRemeberMeDelegate, bool, IsRemeber);

UCLASS()
class DESIGNSTATION_API UWidget_Login : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual bool Initialize() override;

	void UpdateLastestUserID(const FString& LastestUserID, const FString& LastestUserPW, bool IsChecked);
	void UpdateLoginErrorState();
	void ResetPasswordState();
	bool IsRemeberChecked();
	void Clear();

private:
	void LoadBackgroundAndLogoImage();

protected:
	UFUNCTION()
		void OnTextChangedUserName(const FText& Text);
	UFUNCTION()
		void OnTextCommittedUserName(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnTextChangedPassWord(const FText& Text);
	UFUNCTION()
		void OnTextCommittedPassWord(const FText& Text, ETextCommit::Type CommitMethod);
	UFUNCTION()
		void OnClickedLogin();
	UFUNCTION()
		void OnStateChangedCkbRemeberUserName(bool IsRemeber);
	UFUNCTION()
		void OnClickedBtnForgotPassword();

private:
	UPROPERTY()
		UImage*            ImgBackground;
	UPROPERTY()
		UImage*            ImgLogo;

	UPROPERTY()
		UEditableText*     EdtUserID;
	UPROPERTY()
		UEditableText*     EdtPassWord;

	UPROPERTY()
		USizeBox*          SZErrorMessage;

	UPROPERTY()
		UButton*           BtnLogin;

	UPROPERTY()
		UCheckBox*         CkbRemeberMe;

	UPROPERTY()
		UButton*           BtnForgotPassword;


public:
	FLoginDelegate         LoginDelegate;
	FRemeberMeDelegate     RemeberMeDelegate;
};
