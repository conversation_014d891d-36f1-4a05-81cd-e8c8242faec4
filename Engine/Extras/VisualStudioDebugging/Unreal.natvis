<?xml version="1.0" encoding="utf-8"?>
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">

  <!-- Epic Games, Inc. UE Visualizers -->

  <!-- Intrinsic for locating a global debug visualizer state pointer using a unique ID. See FVisualizerDebuggingState for more documentation. -->
  <Intrinsic Name="GetDebugState" SideEffect="false" Expression="GDebuggingState->Ptrs[(strstr(GDebuggingState->GuidString, GuidString) - GDebuggingState->GuidString)/32]">
    <!-- Must be a 32 character lower-case string representing the GUID of the string entry of the form "07813a3186aa4a31a492ef012289038b". -->
    <Parameter Name="GuidString" Type="const char*"/>
  </Intrinsic>

  <!-- FString visualizer -->
  <Type Name="FString">
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax &gt; 0">Empty, Max={Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Data.ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &lt; Data.ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &gt;= Data.ArrayNum">{Data.AllocatorInstance.Data,su}</DisplayString>
    <StringView Condition="Data.ArrayMax &gt;= Data.ArrayNum">Data.AllocatorInstance.Data,su</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Data.ArrayNum &lt; 0 || Data.ArrayNum &gt; Data.ArrayMax) ? 0 : (Data.ArrayNum == 0) ? 1 : Data.ArrayNum - 1</Size>
        <!-- When the string is unallocated, point at the zero which is ArrayNum to act as a null terminator, so we can use &Arr[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Data.ArrayNum != 0) ? (TCHAR*)Data.AllocatorInstance.Data : (TCHAR*)&amp;Data.ArrayNum</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="FAnsiString">
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax &gt; 0">Empty, Max={Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Data.ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &lt; Data.ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &gt;= Data.ArrayNum">{Data.AllocatorInstance.Data,s}</DisplayString>
    <StringView Condition="Data.ArrayMax &gt;= Data.ArrayNum">Data.AllocatorInstance.Data,s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Data.ArrayNum &lt; 0 || Data.ArrayNum &gt; Data.ArrayMax) ? 0 : (Data.ArrayNum == 0) ? 1 : Data.ArrayNum - 1</Size>
        <!-- When the string is unallocated, point at the zero which is ArrayNum to act as a null terminator, so we can use &Arr[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Data.ArrayNum != 0) ? (ANSICHAR*)Data.AllocatorInstance.Data : (ANSICHAR*)&amp;Data.ArrayNum</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="FUtf8String">
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Data.ArrayNum == 0 &amp;&amp; Data.ArrayMax &gt; 0">Empty, Max={Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Data.ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &lt; Data.ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="Data.ArrayMax &gt;= Data.ArrayNum">{Data.AllocatorInstance.Data,s8}</DisplayString>
    <StringView Condition="Data.ArrayMax &gt;= Data.ArrayNum">Data.AllocatorInstance.Data,s8</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Data.ArrayNum &lt; 0 || Data.ArrayNum &gt; Data.ArrayMax) ? 0 : (Data.ArrayNum == 0) ? 1 : Data.ArrayNum - 1</Size>
        <!-- When the string is unallocated, point at the zero which is ArrayNum to act as a null terminator, so we can use &Arr[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Data.ArrayNum != 0) ? (UTF8CHAR*)Data.AllocatorInstance.Data : (UTF8CHAR*)&amp;Data.ArrayNum</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TStringView default visualizer -->
  <Type Name="TStringView&lt;*&gt;">
    <DisplayString Condition="Size == 0">Empty</DisplayString>
    <DisplayString Condition="Size &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Size &gt; 0">{DataPtr,[Size]}</DisplayString>
    <StringView Condition="Size &gt; 0">DataPtr,[Size]</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Size &lt; 0) ? 0 : (Size &gt;= 1) ? Size : 1</Size>
        <!-- When the view is empty, point at the zero which is Size to act as a null terminator, so we can use &View[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Size != 0) ? DataPtr : ($T1*)&amp;Size</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TStringView<WIDECHAR> visualizer -->
  <Type Name="TStringView&lt;WIDECHAR&gt;">
    <DisplayString Condition="Size == 0">Empty</DisplayString>
    <DisplayString Condition="Size &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Size &gt; 0">{DataPtr,[Size]su}</DisplayString>
    <StringView Condition="Size &gt; 0">DataPtr,[Size]su</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Size &lt; 0) ? 0 : (Size &gt;= 1) ? Size : 1</Size>
        <!-- When the view is empty, point at the zero which is Size to act as a null terminator, so we can use &View[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Size != 0) ? DataPtr : (WIDECHAR*)&amp;Size</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TStringView<ANSICHAR> visualizer -->
  <Type Name="TStringView&lt;ANSICHAR&gt;">
    <DisplayString Condition="Size == 0">Empty</DisplayString>
    <DisplayString Condition="Size &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Size &gt; 0">{DataPtr,[Size]s}</DisplayString>
    <StringView Condition="Size &gt; 0">DataPtr,[Size]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(Size &lt; 0) ? 0 : (Size &gt;= 1) ? Size : 1</Size>
        <!-- When the view is empty, point at the zero which is Size to act as a null terminator, so we can use &View[0] in expressions even when there's nothing to point at -->
        <ValuePointer>(Size != 0) ? DataPtr : (ANSICHAR*)&amp;Size</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <Type Name="TStringBuilderBase&lt;*&gt;">
    <DisplayString Condition="Base == CurPos">Empty</DisplayString>
    <DisplayString Condition="sizeof($T1) == 1">{Base,[CurPos-Base]s}</DisplayString>
    <DisplayString>{Base,[CurPos-Base]su}</DisplayString>
    <StringView Condition="sizeof($T1) == 1">Base,[CurPos-Base]s</StringView>
    <StringView>Base,[CurPos-Base]su</StringView>
  </Type>

  <!-- FStringFormatArg visualizer -->
  <Type Name="FStringFormatArg">
    <DisplayString Condition="Type == Int">{IntValue}</DisplayString>
    <DisplayString Condition="Type == UInt">{UIntValue}</DisplayString>
    <DisplayString Condition="Type == Double">{DoubleValue}</DisplayString>
    <DisplayString Condition="Type == String">{StringValue}</DisplayString>
    <DisplayString Condition="Type == StringLiteralANSI">{StringLiteralANSIValue}</DisplayString>
    <DisplayString Condition="Type == StringLiteralWIDE">{StringLiteralWIDEValue}</DisplayString>
    <DisplayString Condition="Type == StringLiteralUCS2">{StringLiteralUCS2Value}</DisplayString>
    <DisplayString Condition="Type == StringLiteralUTF8">{StringLiteralUTF8Value}</DisplayString>
    <Expand>
      <Item Name="Type">Type</Item>
      <Item Name="Value" Condition="Type == Int">IntValue</Item>
      <Item Name="Value" Condition="Type == UInt">UIntValue</Item>
      <Item Name="Value" Condition="Type == Double">DoubleValue</Item>
      <Item Name="Value" Condition="Type == String">StringValue</Item>
      <Item Name="Value" Condition="Type == StringLiteralANSI">StringLiteralANSIValue</Item>
      <Item Name="Value" Condition="Type == StringLiteralWIDE">StringLiteralWIDEValue</Item>
      <Item Name="Value" Condition="Type == StringLiteralUCS2">StringLiteralUCS2Value</Item>
      <Item Name="Value" Condition="Type == StringLiteralUTF8">StringLiteralUTF8Value</Item>
    </Expand>
  </Type>

  <Type Name="FGuid">
    <DisplayString>{{{A,Xb}-{(unsigned __int16)(B >> 16),Xb}-{(unsigned __int16)B,Xb}-{(unsigned __int16)(C >> 16),Xb}-{(unsigned __int16)C,Xb}{D,Xb}}}</DisplayString>
    <Expand>
      <Item Name="A">A,X</Item>
      <Item Name="B">B,X</Item>
      <Item Name="C">C,X</Item>
      <Item Name="D">D,X</Item>
    </Expand>
  </Type>

  <!-- FText visualizer -->
  <Type Name="FTextKey">
    <Intrinsic Name="GetTextKeyState" Expression="((FTextKeyState*)GDebuggingState->Ptrs[(strstr(GDebuggingState->GuidString, &quot;d31281c0182b4419814e25be4b7e7b41&quot;) - GDebuggingState->GuidString)/32])"></Intrinsic>
    <DisplayString Condition="Index == -1">Empty</DisplayString>
    <DisplayString Condition="Index != -1">{(GetTextKeyState()->KeyDataAllocations.Elements.Chunks[Index / GetTextKeyState()->KeyDataAllocations.Elements.NumElementsPerChunk].Elements[Index % GetTextKeyState()->KeyDataAllocations.Elements.NumElementsPerChunk]).Value.Str,s8}</DisplayString>
    <Expand>
      <Item Name="Data" Condition="Index != -1">(GetTextKeyState()->KeyDataAllocations.Elements.Chunks[Index / GetTextKeyState()->KeyDataAllocations.Elements.NumElementsPerChunk].Elements[Index % GetTextKeyState()->KeyDataAllocations.Elements.NumElementsPerChunk]).Value</Item>
    </Expand>
  </Type>
  <Type Name="UE::Text::Private::FRefCountedDisplayString">
    <DisplayString>{DisplayString}</DisplayString>
  </Type>
  <Type Name="UE::Text::Private::TDisplayStringPtrBase&lt;*&gt;">
    <DisplayString>{DisplayStringPtr}</DisplayString>
  </Type>
  <Type Name="FTextHistory_StringTableEntry::FStringTableReferenceData">
	<DisplayString Condition="StringTableEntry.Object == 0 || StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value == 0">&lt;MISSING STRING TABLE ENTRY&gt;</DisplayString>
	<DisplayString Condition="StringTableEntry.Object != 0 &amp;&amp; StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0 &amp;&amp; DisplayString.DisplayStringPtr.Reference">{*DisplayString.DisplayStringPtr.Reference}</DisplayString>
	<DisplayString Condition="StringTableEntry.Object != 0 &amp;&amp; StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0 &amp;&amp; !DisplayString.DisplayStringPtr.Reference">{StringTableEntry.Object->SourceString}</DisplayString>
  </Type>
  <Type Name="FTextHistory_StringTableEntry::FStringTableReferenceData">
	<DisplayString Condition="StringTableEntry.Object == 0 || StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value == 0">&lt;MISSING STRING TABLE ENTRY&gt;</DisplayString>
	<DisplayString Condition="StringTableEntry.Object != 0 &amp;&amp; StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0 &amp;&amp; DisplayString.Object">{*DisplayString.Object}</DisplayString>
	<DisplayString Condition="StringTableEntry.Object != 0 &amp;&amp; StringTableEntry.WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0 &amp;&amp; !DisplayString.Object">{StringTableEntry.Object->SourceString}</DisplayString>
  </Type>
  <Type Name="FTextHistory_StringTableEntry">
    <DisplayString>{*StringTableReferenceData.Object}</DisplayString>
  </Type>
  <Type Name="FTextHistory_Generated" Inheritable="true">
    <DisplayString>{DisplayString}</DisplayString>
  </Type>
  <Type Name="FTextHistory_Base">
    <DisplayString Condition="LocalizedString.DisplayStringPtr.Reference">{*LocalizedString.DisplayStringPtr.Reference}</DisplayString>
    <DisplayString Condition="!LocalizedString.DisplayStringPtr.Reference">{SourceString}</DisplayString>
  </Type>
  <Type Name="FTextHistory_Base">
    <DisplayString Condition="LocalizedString.Object">{*LocalizedString.Object}</DisplayString>
    <DisplayString Condition="!LocalizedString.Object">{SourceString}</DisplayString>
  </Type>
  <Type Name="FText">
    <DisplayString>{*TextData.Reference}</DisplayString>
  </Type>

  <!-- FName visualizer -->

  <Type Name="FName" Priority="MediumLow">
    <Intrinsic Name="GetNameEntry" Expression="((FNameEntry*)&amp;GNameBlocksDebug[InIndex &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (InIndex &amp; FNameDebugVisualizer::OffsetMask)])">
      <Parameter Name="InIndex" Type="uint32"/>
    </Intrinsic>
    <Intrinsic Name="IsNumberedName" Expression="((FNameEntry*)&amp;GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->Header.Len == 0" />
    <Intrinsic Name="NumberedNameId" Expression="*(FNameEntryId*)(((FNameEntry*)&amp;GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->NumberedName.Id)" />
    <DisplayString>{ComparisonIndex}</DisplayString>
    <Expand>
      <Item Name="Number" Condition="IsNumberedName()">*(uint32*)(((FNameEntry*)&amp;GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->NumberedName.Number)</Item>
      <ArrayItems>
        <Size>GetNameEntry(IsNumberedName() ? NumberedNameId().Value : ComparisonIndex.Value)->Header.Len</Size>
        <ValuePointer Condition="IsNumberedName()">((FNameEntry*)&amp;GNameBlocksDebug[NumberedNameId().Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (NumberedNameId().Value &amp; FNameDebugVisualizer::OffsetMask)])->AnsiName</ValuePointer>
        <ValuePointer Condition="!IsNumberedName()">((FNameEntry*)&amp;GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->AnsiName</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <Type Name="FName" Priority="Medium">
    <Intrinsic Name="GetNameEntry" Expression="((FNameEntry*)&amp;GNameBlocksDebug[InIndex &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (InIndex &amp; FNameDebugVisualizer::OffsetMask)])">
      <Parameter Name="InIndex" Type="uint32"/>
    </Intrinsic>
    <Intrinsic Name="IsNumberedName" Expression="((FNameEntry*)&amp;GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->Header.Len == 0" />
    <Intrinsic Name="NumberedNameId" Expression="*(FNameEntryId*)(((FNameEntry*)&amp;GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->NumberedName.Id)" />
    <DisplayString>{DisplayIndex}</DisplayString>
    <Expand>
      <Item Name="Number" Condition="IsNumberedName()">*(uint32*)(((FNameEntry*)&amp;GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->NumberedName.Number)</Item>
      <ArrayItems>
        <Size>GetNameEntry(IsNumberedName() ? NumberedNameId().Value : DisplayIndex.Value)->Header.Len</Size>
        <ValuePointer Condition="IsNumberedName()">((FNameEntry*)&amp;GNameBlocksDebug[NumberedNameId().Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (NumberedNameId().Value &amp; FNameDebugVisualizer::OffsetMask)])->AnsiName</ValuePointer>
        <ValuePointer Condition="!IsNumberedName()">((FNameEntry*)&amp;GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)])->AnsiName</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>


  <Type Name="FName" Priority="MediumHigh">
    <DisplayString Condition="this->Number">{ComparisonIndex}_{(this->Number - 1),d}</DisplayString>
    <DisplayString>{ComparisonIndex}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>((FNameEntry&amp;)GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)]).Header.Len</Size>
        <ValuePointer>((FNameEntry&amp;)GNameBlocksDebug[ComparisonIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (ComparisonIndex.Value &amp; FNameDebugVisualizer::OffsetMask)]).AnsiName</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <Type Name="FName" Priority="High">
    <DisplayString Condition="this->Number">{DisplayIndex}_{(this->Number - 1),d}</DisplayString>
    <DisplayString>{DisplayIndex}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>((FNameEntry&amp;)GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)]).Header.Len</Size>
        <ValuePointer>((FNameEntry&amp;)GNameBlocksDebug[DisplayIndex.Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (DisplayIndex.Value &amp; FNameDebugVisualizer::OffsetMask)]).AnsiName</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <Type Name="FMinimalName">
    <DisplayString Optional="true" Condition="this->Number">{Index}_{(this->Number - 1),d}</DisplayString>
    <DisplayString>{Index}</DisplayString>
  </Type>

  <Type Name="FNameEntryId">
    <DisplayString Condition="Value &amp; FNameDebugVisualizer::UnusedMask">Illegal name (block index out of range)</DisplayString>
    <DisplayString Condition="!GNameBlocksDebug[Value &gt;&gt; FNameDebugVisualizer::OffsetBits]">Illegal name (null block)</DisplayString>

    <DisplayString>{(FNameEntry&amp;)GNameBlocksDebug[Value &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (Value &amp; FNameDebugVisualizer::OffsetMask)]}</DisplayString>
  </Type>

  <Type Name="FNameEntry">
    <DisplayString Optional="true" Condition="Header.Len == 0">{*(FNameEntryId*)NumberedName.Id}_{*(uint32*)NumberedName.Number - 1,d}</DisplayString>
    <DisplayString Condition="Header.Len &gt; FNameDebugVisualizer::MaxLength">Illegal name (length > NAME_SIZE)</DisplayString>
    <DisplayString Condition="Header.bIsWide">{WideName,[Header.Len]su}</DisplayString>
    <DisplayString>{AnsiName,[Header.Len]s}</DisplayString>
  </Type>
  
  <Type Name="FDisplayNameEntryId">
    <DisplayString Optional="true">{Id}</DisplayString>
    <DisplayString Optional="true" Condition="Value &amp; FNameDebugVisualizer::UnusedMask">
      {(FNameEntry&amp;)GNameBlocksDebug[(Value &amp; ~FNameDebugVisualizer::UnusedMask) &gt;&gt; FNameDebugVisualizer::OffsetBits][FNameDebugVisualizer::EntryStride * (Value &amp; FNameDebugVisualizer::OffsetMask)]}
    </DisplayString>
    <DisplayString>{(FNameEntryId&amp;)(*this)}</DisplayString>
  </Type>
  
  <!-- FStatNameAndInfo -->
  <Type Name="FStatNameAndInfo">
    <DisplayString>{(EStatOperation::Type)((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatOperation::Shift)) &amp; EStatOperation::Mask),en} {(EStatDataType::Type)((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask),en} {NameAndInfo}</DisplayString>
    <Expand>
      <Item Name="[Name]">NameAndInfo</Item>
      <Item Name="[StatOperation]">(EStatOperation::Type)((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatOperation::Shift)) &amp; EStatOperation::Mask)</Item>
      <Item Name="[StatDataType]">(EStatDataType::Type)((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask)</Item>
      <Item Name="[IsCycle]">((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsCycle) == EStatMetaFlags::IsCycle</Item>
      <Item Name="[IsMemory]">((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsMemory) == EStatMetaFlags::IsMemory</Item>
      <Item Name="[IsPackedCCAndDuration]">((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsPackedCCAndDuration) == EStatMetaFlags::IsPackedCCAndDuration</Item>
      <Item Name="[ShouldClearEveryFrame]">((NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::ShouldClearEveryFrame) == EStatMetaFlags::ShouldClearEveryFrame</Item>
    </Expand>
  </Type>

  <!-- FStatMessage without DebugStatData-->
  <Type Name="FStatMessage">
    <DisplayString>{NameAndInfo}</DisplayString>
  </Type>

  <!-- FStatMessage with DebugStatData -->
  <Type Name="FStatMessage" Priority="High">
    <!--ST_None	-->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_None">
      {{NoneType NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_int64 && !IsPackedCCAndDuration && !IsCycle -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_int64 &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsPackedCCAndDuration) != EStatMetaFlags::IsPackedCCAndDuration &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsCycle) != EStatMetaFlags::IsCycle">
      {{Int64={DebugStatData.Cycles} NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_int64 && !IsPackedCCAndDuration && IsCycle -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_int64 &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsPackedCCAndDuration) != EStatMetaFlags::IsPackedCCAndDuration &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsCycle) == EStatMetaFlags::IsCycle">
      {{Cycles={DebugStatData.Cycles} NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_int64 && IsPackedCCAndDuration && IsCycle -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_int64 &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsPackedCCAndDuration) == EStatMetaFlags::IsPackedCCAndDuration &amp;&amp; ((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatMetaFlags::Shift)) &amp; EStatMetaFlags::IsCycle) == EStatMetaFlags::IsCycle">
      {{Count={DebugStatData.CCAndDuration[0]},Cycles={DebugStatData.CCAndDuration[1]} NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_double -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_double">
      {{Float={DebugStatData.Float} NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_FName -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_FName">
      {{Name={(FNameEntryId&amp;)DebugStatData.Cycles} NameAndInfo={NameAndInfo}}}
    </DisplayString>

    <!--ST_Ptr -->
    <DisplayString Condition="(EStatDataType::Type)((NameAndInfo.NameAndInfo.Number >> (EStatAllFields::StartShift + EStatDataType::Shift)) &amp; EStatDataType::Mask) == EStatDataType::ST_Ptr">
      {{Ptr={DebugStatData.Ptr} NameAndInfo={NameAndInfo}}}
    </DisplayString>
  </Type>

  <!-- FAllocationInfo -->
  <!--
		uint64 OldPtr;
		uint64 Ptr;
		int64 Size;
		FName EncodedCallstack;
		uint32 SequenceTag;
		EMemoryOperation Op; Alloc=1, Free=2, Realloc=3
		bool bHasBrokenCallstack;
	-->
  <Type Name="FAllocationInfo">
    <!-- Alloc -->
    <DisplayString Condition="Op == 1" >
      {{A SeqTag={SequenceTag} Ptr={Ptr} Size={Size} Callstack={EncodedCallstack} bHasBrokenCallstack={bHasBrokenCallstack}}}
    </DisplayString>

    <!-- Free -->
    <DisplayString Condition="Op == 2" >
      {{F SeqTag={SequenceTag} Ptr={Ptr} bHasBrokenCallstack={bHasBrokenCallstack}}}
    </DisplayString>

    <!-- Realloc -->
    <DisplayString Condition="Op == 3" >
      {{R SeqTag={SequenceTag} OldPtr={OldPtr} Ptr={Ptr} NewSize={Size} Callstack={EncodedCallstack} bHasBrokenCallstack={bHasBrokenCallstack}}}
    </DisplayString>
  </Type>

  <Type Name="FThreadSafeCounter">
    <DisplayString>{Counter}</DisplayString>
  </Type>

  <Type Name="FThreadSafeBool">
    <DisplayString Condition="Counter==0">False</DisplayString>
    <DisplayString Condition="Counter==1">True</DisplayString>
  </Type>

  <!-- FTimespan visualizer -->
  <Type Name="FTimespan">
    <DisplayString>Ticks = {Ticks}</DisplayString>
    <Expand>
      <Item Name="Total Milliseconds">Ticks / ETimespan::TicksPerMillisecond</Item>
      <Item Name="Total Seconds">Ticks / ETimespan::TicksPerSecond</Item>
      <Item Name="Total Minutes">Ticks / ETimespan::TicksPerMinute</Item>
      <Item Name="Total Hours">Ticks / ETimespan::TicksPerHour</Item>
      <Item Name="Total Days">Ticks / ETimespan::TicksPerDay</Item>
    </Expand>
  </Type>

  <Type Name="FAsyncPackageData">
    <DisplayString>Exports={Exports.ArrayNum}, Bundles={ExportBundleCount}, Nodes={ExportBundleNodes.ArrayNum}, ImportedPackages={ImportedAsyncPackages.ArrayNum}, ShaderMapHashes={ShaderMapHashes.ArrayNum}</DisplayString>
  </Type>

  <Type Name="FEventLoadNode2">
    <DisplayString>bIsDone={bIsDone._Storage._Value,nvo}, BarrierCount={BarrierCount}, {Spec-&gt;Func,na}</DisplayString>
  </Type>

  <Type Name="FExportObject">
    <DisplayString Condition="Object != 0">{Object-&gt;NamePrivate} ({Object-&gt;ObjectFlags,en})</DisplayString>
    <DisplayString Condition="!bFiltered &amp;&amp; !bExportLoadFailed">Null (Create)</DisplayString>
    <DisplayString Condition="bFiltered">Null (Filtered)</DisplayString>
    <DisplayString>Null (Failed)</DisplayString>
  </Type>

  <Type Name="FAsyncPackageDesc2">
    <DisplayString Condition="UPackageId.Id != PackageIdToLoad.Id">{UPackageName} ({UPackageId.Id,X}) - {PackagePathToLoad.PackageName} ({PackageIdToLoad.Id,X})</DisplayString>
    <DisplayString>{UPackageName} ({UPackageId.Id,X})</DisplayString>
  </Type>

  <Type Name="FAsyncPackage2">
    <DisplayString>{Desc}</DisplayString>
  </Type>

  <Type Name="FPackageId">
    <DisplayString Condition="Id != InvalidId">{Id,X}</DisplayString>
    <DisplayString>Null</DisplayString>
  </Type>
  
  <Type Name="FPackageIndex">
    <DisplayString Condition="Index &lt; 0">ImportIndex={-Index-1}</DisplayString>
    <DisplayString Condition="Index &gt; 0">ExportIndex={Index-1}</DisplayString>
    <DisplayString>Null</DisplayString>
  </Type>

  <Type Name="FPackageObjectIndex">
    <DisplayString Condition="TypeAndId != Invalid">{(EType)(TypeAndId >> TypeShift)} {TypeAndId &amp; IndexMask,X}</DisplayString>
    <DisplayString>Null</DisplayString>
  </Type>

  <Type Name="FLoadedPackageRef">
    <DisplayString Condition="PackageObjectIndex &gt;= 0">{GObjectArrayForDebugVisualizers->Objects[PackageObjectIndex / FChunkedFixedUObjectArray::NumElementsPerChunk][PackageObjectIndex % FChunkedFixedUObjectArray::NumElementsPerChunk].Object,na}, FullyLoaded={bAreAllPublicExportsLoaded}, RefCount={RefCount}, Exports={PublicExportMap.Count}</DisplayString>
    <DisplayString>Null, bIsMissing={bIsMissing}, bHasFailed={bHasFailed}, RefCount={RefCount}, Exports={PublicExportMap.Count}</DisplayString>
    <Expand>
      <Item Name="[Package]" Condition="PackageObjectIndex &gt;= 0">GObjectArrayForDebugVisualizers->Objects[PackageObjectIndex / FChunkedFixedUObjectArray::NumElementsPerChunk][PackageObjectIndex % FChunkedFixedUObjectArray::NumElementsPerChunk].Object</Item>
      <Item Name="[Package]" Condition="PackageObjectIndex &lt; 0">Null</Item>
      <ExpandedItem>this,!</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="FLoadedPackageRef::FPublicExportMap">
    <DisplayString Condition="Count == 0">Empty</DisplayString>
    <!--<DisplayString Condition="Count == 1">Exports={Count}, {{{SingleItemKey,x},{SingleItemValue}}}</DisplayString>
    <DisplayString>ExportCount={Count}</DisplayString>-->
    <Expand>
      <Item Name="[ExportHash]" Condition="Count == 1">SingleItemKey,x</Item>
      <Item Name="[ObjectIndex]" Condition="Count == 1">SingleItemValue</Item>
      <Item Name="[Object]" Condition="Count == 1 &amp;&amp; SingleItemValue &gt; 0">GObjectArrayForDebugVisualizers->Objects[SingleItemValue / FChunkedFixedUObjectArray::NumElementsPerChunk][SingleItemValue % FChunkedFixedUObjectArray::NumElementsPerChunk].Object</Item>
      <Item Name="[Object]" Condition="Count == 1 &amp;&amp; SingleItemValue == -1">(UObject*)nullptr</Item>
      <Synthetic Name="[ExportHashes]" Condition="Count &gt; 1">
        <DisplayString>Num={Count}</DisplayString>
        <Expand>
          <ArrayItems>
            <Size>Count</Size>
            <ValuePointer>((uint64*)Allocation),x</ValuePointer>
          </ArrayItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="[ObjectIndices]" Condition="Count &gt; 1">
        <DisplayString>Num={Count}</DisplayString>
        <Expand>
          <ArrayItems>
            <Size>Count</Size>
            <ValuePointer>((int32*)(Allocation + Count * sizeof(uint64)))</ValuePointer>
          </ArrayItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="[Objects]" Condition="Count &gt; 1">
        <DisplayString>Num={Count}</DisplayString>
        <Expand>
          <IndexListItems>
            <Size>Count</Size>
            <ValueNode>GObjectArrayForDebugVisualizers->Objects[((int32*)(Allocation + Count * sizeof(uint64)))[$i] / FChunkedFixedUObjectArray::NumElementsPerChunk][((int32*)(Allocation + Count * sizeof(uint64)))[$i] % FChunkedFixedUObjectArray::NumElementsPerChunk].Object</ValueNode>
          </IndexListItems>
        </Expand>
      </Synthetic>
      <ExpandedItem>this,!</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="FPublicExportKey">
    <Expand>
      <Item Name="PackageId">uint64(PackageIdHigh) &lt;&lt; 32 | PackageIdLow,X</Item>
      <Item Name="ExportHash">uint64(ExportHashHigh) &lt;&lt; 32 | ExportHashLow,X</Item>
    </Expand>
  </Type>

  <Type Name="UE::Math::TVector&lt;*&gt;">
    <DisplayString>{{X={X} Y={Y} Z={Z}}}</DisplayString>
    <Expand>
      <Item Name="X">X</Item>
      <Item Name="Y">Y</Item>
      <Item Name="Z">Z</Item>
      <Item Name="||V||&#xB2;">X*X + Y*Y + Z*Z</Item>
    </Expand>
  </Type>
  
  <Type Name="UE::Math::TVector4&lt;*&gt;">
    <DisplayString>{{X={X} Y={Y} Z={Z} W={W}}}</DisplayString>
    <Expand>
      <Item Name="X">X</Item>
      <Item Name="Y">Y</Item>
      <Item Name="Z">Z</Item>
      <Item Name="W">W</Item>
      <Item Name="||V||&#xB2;">X*X + Y*Y + Z*Z + W*W</Item>
    </Expand>
  </Type>

  <Type Name="UE::Math::TQuat&lt;*&gt;">
    <DisplayString>{{X={X} Y={Y} Z={Z} W={W}}}</DisplayString>
    <Expand>
      <Item Name="X">X</Item>
      <Item Name="Y">Y</Item>
      <Item Name="Z">Z</Item>
      <Item Name="W">W</Item>
      <Item Name="||Q||&#xB2;">X*X + Y*Y + Z*Z + W*W</Item>
    </Expand>
  </Type>

  <Type Name="FRotator">
    <DisplayString>{{Pitch={Pitch} Yaw={Yaw} Roll={Roll}}}</DisplayString>
    <Expand>
      <Item Name="Pitch (Y)">Pitch</Item>
      <Item Name="Yaw (Z)">Yaw</Item>
      <Item Name="Roll (X)">Roll</Item>
    </Expand>
  </Type>

  <Type Name="UE::Math::TTransform&lt;float&gt;">
    <DisplayString>{{Translation={{X={Translation.m128_f32[0]} Y={Translation.m128_f32[1]} Z={Translation.m128_f32[2]}}} Rotation={{X={Rotation.m128_f32[0]} Y={Rotation.m128_f32[1]} Z={Rotation.m128_f32[2]} W={Rotation.m128_f32[3]}}}}}</DisplayString>
    <Expand>
      <Synthetic Name="Translation">
        <DisplayString>{{X={Translation.m128_f32[0]} Y={Translation.m128_f32[1]} Z={Translation.m128_f32[2]}}}</DisplayString>
        <Expand>
          <Item Name="X">Translation.m128_f32[0]</Item>
          <Item Name="Y">Translation.m128_f32[1]</Item>
          <Item Name="Z">Translation.m128_f32[2]</Item>
          <Item Name="||V||&#xB2;">Translation.m128_f32[0]*Translation.m128_f32[0] + Translation.m128_f32[1]*Translation.m128_f32[1] + Translation.m128_f32[2]*Translation.m128_f32[2]</Item>
        </Expand>
      </Synthetic>
      <Synthetic Name="Rotation">
        <DisplayString>{{X={Rotation.m128_f32[0]} Y={Rotation.m128_f32[1]} Z={Rotation.m128_f32[2]} W={Rotation.m128_f32[3]}}}</DisplayString>
        <Expand>
          <Item Name="X">Rotation.m128_f32[0]</Item>
          <Item Name="Y">Rotation.m128_f32[1]</Item>
          <Item Name="Z">Rotation.m128_f32[2]</Item>
          <Item Name="W">Rotation.m128_f32[3]</Item>
          <Item Name="||Q||&#xB2;">Rotation.m128_f32[0]*Rotation.m128_f32[0] + Rotation.m128_f32[1]*Rotation.m128_f32[1] + Rotation.m128_f32[2]*Rotation.m128_f32[2] + Rotation.m128_f32[3]*Rotation.m128_f32[3]</Item>
        </Expand>
      </Synthetic>
      <Synthetic Name="Scale3D">
        <DisplayString>{{X={Scale3D.m128_f32[0]} Y={Scale3D.m128_f32[1]} Z={Scale3D.m128_f32[2]}}}</DisplayString>
        <Expand>
          <Item Name="X">Scale3D.m128_f32[0]</Item>
          <Item Name="Y">Scale3D.m128_f32[1]</Item>
          <Item Name="Z">Scale3D.m128_f32[2]</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <Type Name="UE::Math::TTransform&lt;double&gt;">
    <DisplayString>{{Translation={{X={Translation.XY.m128d_f64[0]} Y={Translation.XY.m128d_f64[1]} Z={Translation.ZW.m128d_f64[0]}}} Rotation={{X={Rotation.XY.m128d_f64[0]} Y={Rotation.XY.m128d_f64[1]} Z={Rotation.ZW.m128d_f64[0]} W={Rotation.ZW.m128d_f64[1]}}}}}</DisplayString>
    <Expand>
      <Synthetic Name="Translation">
        <DisplayString>{{X={Translation.XY.m128d_f64[0]} Y={Translation.XY.m128d_f64[1]} Z={Translation.ZW.m128d_f64[0]}}}</DisplayString>
        <Expand>
          <Item Name="X">Translation.XY.m128d_f64[0]</Item>
          <Item Name="Y">Translation.XY.m128d_f64[1]</Item>
          <Item Name="Z">Translation.ZW.m128d_f64[0]</Item>
          <Item Name="||V||&#xB2;">Translation.XY.m128d_f64[0]*Translation.XY.m128d_f64[0] + Translation.XY.m128d_f64[1]*Translation.XY.m128d_f64[1] + Translation.ZW.m128d_f64[0]*Translation.ZW.m128d_f64[0]</Item>
        </Expand>
      </Synthetic>
      <Synthetic Name="Rotation">
        <DisplayString>{{X={Rotation.XY.m128d_f64[0]} Y={Rotation.XY.m128d_f64[1]} Z={Rotation.ZW.m128d_f64[0]} W={Rotation.ZW.m128d_f64[1]}}}</DisplayString>
        <Expand>
          <Item Name="X">Rotation.XY.m128d_f64[0]</Item>
          <Item Name="Y">Rotation.XY.m128d_f64[1]</Item>
          <Item Name="Z">Rotation.ZW.m128d_f64[0]</Item>
          <Item Name="W">Rotation.ZW.m128d_f64[1]</Item>
          <Item Name="||Q||&#xB2;">Rotation.XY.m128d_f64[0]*Rotation.XY.m128d_f64[0] + Rotation.XY.m128d_f64[1]*Rotation.XY.m128d_f64[1] + Rotation.ZW.m128d_f64[0]*Rotation.ZW.m128d_f64[0] + Rotation.ZW.m128d_f64[1]*Rotation.ZW.m128d_f64[1]</Item>
        </Expand>
      </Synthetic>
      <Synthetic Name="Scale3D">
        <DisplayString>{{X={Scale3D.XY.m128d_f64[0]} Y={Scale3D.XY.m128d_f64[1]} Z={Scale3D.ZW.m128d_f64[0]}}}</DisplayString>
        <Expand>
          <Item Name="X">Scale3D.XY.m128d_f64[0]</Item>
          <Item Name="Y">Scale3D.XY.m128d_f64[1]</Item>
          <Item Name="Z">Scale3D.ZW.m128d_f64[0]</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <Type Name="UE::Math::TPlane&lt;*&gt;">
    <DisplayString>{{X={X} Y={Y} Z={Z} W={W}}}</DisplayString>
    <Expand>
      <Item Name="X">X</Item>
      <Item Name="Y">Y</Item>
      <Item Name="Z">Z</Item>
      <Item Name="W">W</Item>
    </Expand>
  </Type>

  <!-- TEnumAsByte visualizer -->
  <Type Name="TEnumAsByte&lt;*&gt;">
    <DisplayString>{($T1)Value}</DisplayString>
  </Type>

  <!-- UObjectBase visualizer -->
  <Type Name="UObjectBase">
    <DisplayString>(Name={NamePrivate}, InternalFlags={(EInternalObjectFlags)GObjectArrayForDebugVisualizers->Objects[InternalIndex / 65536][InternalIndex % 65536].Flags})</DisplayString>
  </Type>

  <!-- FFieldClass visualizer -->
  <Type Name="FFieldClass">
    <DisplayString>(Name={Name})</DisplayString>
  </Type>
  
  <!-- FFieldVariant visualizer -->
  <Type Name="FFieldVariant">
    <DisplayString Condition="((int)Container.Object &amp; 1)==0">{Container.Field}</DisplayString>
    <DisplayString Condition="((int)Container.Object &amp; 1)==1">{(UObject*)((uintptr_t)Container.Object &amp; 0xFFFFFFFFFFFFFFFE)}</DisplayString>
    <Expand>
      <Item Name="Field" Condition="((int)Container.Object &amp; 1)==0">Container.Field</Item>
      <Item Name="Object" Condition="((int)Container.Object &amp; 1)==1">(UObject*)((uintptr_t)Container.Object &amp; 0xFFFFFFFFFFFFFFFE)</Item>
    </Expand>    
  </Type>
  
  <!-- FField visualizer -->
  <Type Name="FField">
    <DisplayString>Name={NamePrivate}</DisplayString>
  </Type>

  <!--TFieldPath visualizer-->
  <Type Name="TFieldPath&lt;*&gt;">
    <DisplayString Condition="ResolvedField != nullptr">{ResolvedField->NamePrivate}</DisplayString>
    <DisplayString Condition="Path.ArrayNum &gt; 0">{((FName*)Path.AllocatorInstance.Data)[Path.ArrayNum - 1]}</DisplayString>
    <DisplayString>Invalid</DisplayString>
  </Type>

  <!-- UE::FPropertyTypeName visualizer -->
  <Type Name="UE::FPropertyTypeName">
    <DisplayString>{((UE::FPropertyTypeNameNode*&amp;)UE::GPropertyTypeNameTable.Blocks[Index >> UE::GPropertyTypeNameBlockBits])[Index &amp; UE::GPropertyTypeNameBlockOffsetMask]}</DisplayString>
    <Expand>
      <ExpandedItem>((UE::FPropertyTypeNameNode*&amp;)UE::GPropertyTypeNameTable.Blocks[Index >> UE::GPropertyTypeNameBlockBits])[Index &amp; UE::GPropertyTypeNameBlockOffsetMask]</ExpandedItem>
    </Expand>
  </Type>

  <!-- UE::FPropertyTypeNameBuilder visualizer -->
  <Type Name="UE::FPropertyTypeNameBuilder">
    <DisplayString Condition="Nodes.ArrayNum == 0">Empty</DisplayString>
    <DisplayString>{Nodes[0]}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Nodes.ArrayNum > 0">Nodes[0]</ExpandedItem>
    </Expand>
  </Type>

  <!-- UE::FPropertyTypeNameNode visualizer -->
  <Type Name="UE::FPropertyTypeNameNode">
    <DisplayString Condition="InnerCount == 0 &amp;&amp; Name.Number == 0">{Name,sb}</DisplayString>
    <DisplayString Condition="InnerCount == 0 &amp;&amp; Name.Number">{Name.DisplayIndex,sb}_{Name.Number-1}</DisplayString>
    <DisplayString Condition="InnerCount == 1 &amp;&amp; Name.Number == 0">{Name,sb}({this[1]})</DisplayString>
    <DisplayString Condition="InnerCount == 1 &amp;&amp; Name.Number">{Name.DisplayIndex,sb}_{Name.Number-1}({this[1]})</DisplayString>
    <DisplayString Condition="InnerCount >= 2 &amp;&amp; Name.Number == 0">{Name,sb}({this[1]},...)</DisplayString>
    <DisplayString Condition="InnerCount >= 2 &amp;&amp; Name.Number">{Name.DisplayIndex,sb}_{Name.Number-1}({this[1]},...)</DisplayString>
    <Expand HideRawView="true">
      <Item Name="Name">Name</Item>
      <CustomListItems>
        <Variable Name="Params" InitialValue="InnerCount"/>
        <Variable Name="Index" InitialValue="1"/>
        <Variable Name="Skip" InitialValue="0"/>
        <Loop Condition="Params != 0">
          <If Condition="Skip == 0">
            <Item>this[Index]</Item>
            <Exec>--Params</Exec>
          </If>
          <Else>
            <Exec>--Skip</Exec>
          </Else>
          <Exec>Skip += this[Index].InnerCount</Exec>
          <Exec>++Index</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <!-- UE::FPropertyPathName visualizer -->
  <Type Name="UE::FPropertyPathName">
    <Expand>
      <ExpandedItem>(UE::FPropertyPathName::FSegment*)Segments.AllocatorInstance.Data,[Segments.ArrayNum]</ExpandedItem>
    </Expand>
  </Type>

  <!-- UE::FPropertyPathName::FSegment visualizer -->
  <Type Name="UE::FPropertyPathName::FSegment">
    <DisplayString Condition="NameWithIndex.Number == 0 &amp;&amp; Type.Index == 0">{NameWithIndex.DisplayIndex,sb}</DisplayString>
    <DisplayString Condition="NameWithIndex.Number == 0 &amp;&amp; Type.Index">{NameWithIndex.DisplayIndex,sb} ({Type})</DisplayString>
    <DisplayString Condition="NameWithIndex.Number &amp;&amp; Type.Index == 0">{NameWithIndex.DisplayIndex,sb}[{NameWithIndex.Number-1}]</DisplayString>
    <DisplayString Condition="NameWithIndex.Number &amp;&amp; Type.Index">{NameWithIndex.DisplayIndex,sb}[{NameWithIndex.Number-1}] ({Type})</DisplayString>
    <Expand>
      <Item Name="Name">NameWithIndex.DisplayIndex,sb</Item>
      <Item Name="Type">Type</Item>
      <Item Name="Index">(int32)NameWithIndex.Number-1</Item>
    </Expand>
  </Type>

  <!-- FChunkedFixedUObjectArray visualizer -->
  <Type Name="FChunkedFixedUObjectArray">
    <DisplayString Condition="NumElements == 0">Empty</DisplayString>
    <DisplayString Condition="NumElements &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="NumElements &gt; 0">NumElements={NumElements}, NumChunks={NumChunks}, {NumElementsPerChunk}</DisplayString>

    <Expand>
      <IndexListItems>
        <Size>(NumElements &gt; 0) ? NumElements : 0</Size>
        <ValueNode>
          Objects[$i / NumElementsPerChunk][$i % NumElementsPerChunk]
        </ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TArray<*,TFixedAllocator<*> > visualizer -->
  <Type Name="TArray&lt;*,TFixedAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(TArray&lt;$T1,TFixedAllocator&lt;$T2&gt; &gt;::ElementType*)AllocatorInstance.InlineData</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray<*,TSizedInlineAllocator<*,*,*> > visualizer -->
  <Type Name="TArray&lt;*,TSizedInlineAllocator&lt;*,*,*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data == 0">(TArray&lt;$T1,TSizedInlineAllocator&lt;$T2,$T3,$T4&gt; &gt;::ElementType*)AllocatorInstance.InlineData</ValuePointer>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data != 0">(TArray&lt;$T1,TSizedInlineAllocator&lt;$T2,$T3,$T4&gt; &gt;::ElementType*)AllocatorInstance.SecondaryData.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray<*,TMemoryImageAllocator<*>> visualizer -->
  <Type Name="TArray&lt;*,TMemoryImageAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax}</DisplayString>
    <DisplayString>Ptr={AllocatorInstance.Data.UnfrozenPtr}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer Condition="(AllocatorInstance.Data.Frozen.Packed &amp; 1) != 0">(TArray&lt;$T1,TMemoryImageAllocator&lt;$T2&gt; &gt;::ElementType*)( (char*)&amp;AllocatorInstance.Data + (int64(AllocatorInstance.Data.Frozen.Packed) >> 24))</ValuePointer>
        <ValuePointer Condition="(AllocatorInstance.Data.Frozen.Packed &amp; 1) == 0">(TArray&lt;$T1,TMemoryImageAllocator&lt;$T2&gt; &gt;::ElementType*)AllocatorInstance.Data.UnfrozenPtr</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray visualizer -->
  <Type Name="TArray&lt;*,*&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(TArray&lt;$T1,$T2&gt;::ElementType*)AllocatorInstance.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray<char> visualizers -->
  <Type Name="TArray&lt;char,*&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(char*)AllocatorInstance.Data,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(char*)AllocatorInstance.Data,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(char*)AllocatorInstance.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;char,TFixedAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(char*)AllocatorInstance.InlineData,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(char*)AllocatorInstance.InlineData,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(char*)AllocatorInstance.InlineData</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;char,TSizedInlineAllocator&lt;*,*,*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">Num={ArrayNum}, Max={ArrayMax} {(char*)AllocatorInstance.InlineData,[ArrayNum]s}</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">Num={ArrayNum}, Max={ArrayMax} {(char*)AllocatorInstance.SecondaryData.Data,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">(char*)AllocatorInstance.InlineData,[ArrayNum]s</StringView>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">(char*)AllocatorInstance.SecondaryData.Data,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data == 0">(char*)AllocatorInstance.InlineData</ValuePointer>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data != 0">(char*)AllocatorInstance.SecondaryData.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray<unsigned char> visualizers -->
  <Type Name="TArray&lt;unsigned char,*&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(unsigned char*)AllocatorInstance.Data,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(unsigned char*)AllocatorInstance.Data,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(unsigned char*)AllocatorInstance.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;unsigned char,TFixedAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(unsigned char*)AllocatorInstance.InlineData,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(unsigned char*)AllocatorInstance.InlineData,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(unsigned char*)AllocatorInstance.InlineData</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;unsigned char,TSizedInlineAllocator&lt;*,*,*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">Num={ArrayNum}, Max={ArrayMax} {(unsigned char*)AllocatorInstance.InlineData,[ArrayNum]s}</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">Num={ArrayNum}, Max={ArrayMax} {(unsigned char*)AllocatorInstance.SecondaryData.Data,[ArrayNum]s}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">(unsigned char*)AllocatorInstance.InlineData,[ArrayNum]s</StringView>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">(unsigned char*)AllocatorInstance.SecondaryData.Data,[ArrayNum]s</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data == 0">(unsigned char*)AllocatorInstance.InlineData</ValuePointer>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data != 0">(unsigned char*)AllocatorInstance.SecondaryData.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArray<wchar_t> visualizers -->
  <Type Name="TArray&lt;wchar_t,*&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(wchar_t*)AllocatorInstance.Data,[ArrayNum]su}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(wchar_t*)AllocatorInstance.Data,[ArrayNum]su</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(wchar_t*)AllocatorInstance.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;wchar_t,TFixedAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum">Num={ArrayNum}, Max={ArrayMax} {(wchar_t*)AllocatorInstance.InlineData,[ArrayNum]su}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum">(wchar_t*)AllocatorInstance.InlineData,[ArrayNum]su</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer>(wchar_t*)AllocatorInstance.InlineData</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TArray&lt;wchar_t,TSizedInlineAllocator&lt;*,*,*&gt;&gt;">
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum == 0 &amp;&amp; ArrayMax &gt; 0">Empty, Max={ArrayMax}</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &lt; ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">Num={ArrayNum}, Max={ArrayMax} {(wchar_t*)AllocatorInstance.InlineData,[ArrayNum]su}</DisplayString>
    <DisplayString Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">Num={ArrayNum}, Max={ArrayMax} {(wchar_t*)AllocatorInstance.SecondaryData.Data,[ArrayNum]su}</DisplayString>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data == 0">(wchar_t*)AllocatorInstance.InlineData,[ArrayNum]su</StringView>
    <StringView Condition="ArrayMax &gt;= ArrayNum &amp;&amp; AllocatorInstance.SecondaryData.Data != 0">(wchar_t*)AllocatorInstance.SecondaryData.Data,[ArrayNum]su</StringView>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &lt;= ArrayMax) ? ArrayNum : 0</Size>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data == 0">(wchar_t*)AllocatorInstance.InlineData</ValuePointer>
        <ValuePointer Condition="AllocatorInstance.SecondaryData.Data != 0">(wchar_t*)AllocatorInstance.SecondaryData.Data</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TArrayView visualizer -->
  <Type Name="TArrayView&lt;*&gt;">
    <DisplayString Condition="ArrayNum == 0">Empty</DisplayString>
    <DisplayString Condition="ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="ArrayNum &gt; 0">Num={ArrayNum}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(ArrayNum &gt; 0) ? ArrayNum : 0</Size>
        <ValuePointer>DataPtr</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TIndirectArray visualizer -->
  <Type Name="TIndirectArray&lt;*,*&gt;">
    <DisplayString Condition="Array.ArrayNum == 0 &amp;&amp; Array.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Array.ArrayNum == 0 &amp;&amp; Array.ArrayMax &gt; 0">Empty, Max={Array.ArrayMax}</DisplayString>
    <DisplayString Condition="Array.ArrayNum &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Array.ArrayMax &lt; Array.ArrayNum">Invalid</DisplayString>
    <DisplayString Condition="Array.ArrayMax &gt;= Array.ArrayNum">Num={Array.ArrayNum}, Max={Array.ArrayMax}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(Array.ArrayNum &lt;= Array.ArrayMax) ? Array.ArrayNum : 0</Size>
        <ValueNode>*((TIndirectArray&lt;$T1,$T2&gt;::ElementType**)Array.AllocatorInstance.Data)[$i]</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TChunkedArray visualizer -->
  <Type Name="TChunkedArray&lt;*,*,*&gt;">
    <DisplayString Condition="NumElements == 0 &amp;&amp; Chunks.Array.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="NumElements == 0 &amp;&amp; Chunks.Array.ArrayMax &gt; 0">Empty, Max={Chunks.Array.ArrayMax*NumElementsPerChunk}, NumChunks={Chunks.Array.ArrayNum}, MaxChunks={Chunks.Array.ArrayMax}, ElementsPerChunk={NumElementsPerChunk}</DisplayString>
    <DisplayString Condition="NumElements &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="NumElements &gt; 0">Num={NumElements}, Max={Chunks.Array.ArrayMax*NumElementsPerChunk}, NumChunks={Chunks.Array.ArrayNum}, MaxChunks={Chunks.Array.ArrayMax}, ElementsPerChunk={NumElementsPerChunk}</DisplayString>

    <Expand>
      <IndexListItems>
        <Size>(NumElements &gt; 0) ? NumElements : 0</Size>
        <ValueNode>
          *(
          *(
          (TChunkedArray&lt;$T1,$T2,$T3&gt;::ElementType**)Chunks.Array.AllocatorInstance.Data + ($i / NumElementsPerChunk)
          ) + ($i % NumElementsPerChunk)
          )
        </ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TSparseArray visualizer -->
  <Type Name="TSparseArray&lt;*,*&gt;">
    <DisplayString Condition="(Data.ArrayNum - NumFreeIndices) &lt;= 0 &amp;&amp; Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="(Data.ArrayNum - NumFreeIndices) &lt;= 0 &amp;&amp; Data.ArrayMax &gt; 0">Empty, Max={Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Data.ArrayNum &lt;= Data.ArrayMax">Num={Data.ArrayNum - NumFreeIndices}, Max={Data.ArrayMax}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(Data.ArrayNum &gt; 0 &amp;&amp; Data.ArrayNum &lt;= Data.ArrayMax) ? Data.ArrayNum : 0</Size>
        <ValueNode Condition="(AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocationFlags.AllocatorInstance.SecondaryData.Data)[$i/32]&gt;&gt;($i%32) &amp; 1) != 0">*reinterpret_cast&lt;ElementType*&gt;(reinterpret_cast&lt;FElementOrFreeListLink*&gt;(Data.AllocatorInstance.Data) + $i)</ValueNode>
        <ValueNode Condition="(AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocationFlags.AllocatorInstance.InlineData        )[$i/32]&gt;&gt;($i%32) &amp; 1) != 0">*reinterpret_cast&lt;ElementType*&gt;(reinterpret_cast&lt;FElementOrFreeListLink*&gt;(Data.AllocatorInstance.Data) + $i)</ValueNode>
        <ValueNode Condition="(AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocationFlags.AllocatorInstance.SecondaryData.Data)[$i/32]&gt;&gt;($i%32) &amp; 1) == 0">"Invalid",sb</ValueNode>
        <ValueNode Condition="(AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocationFlags.AllocatorInstance.InlineData        )[$i/32]&gt;&gt;($i%32) &amp; 1) == 0">"Invalid",sb</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TBitArray visualizer -->
  <Type Name="TBitArray&lt;*&gt;">
    <DisplayString Condition="NumBits == 0 &amp;&amp; MaxBits == 0">Empty</DisplayString>
    <DisplayString Condition="NumBits == 0 &amp;&amp; MaxBits &gt; 0">Empty, MaxBits={MaxBits}</DisplayString>
    <DisplayString Condition="NumBits &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="NumBits &gt; 0">NumBits={NumBits}, MaxBits={MaxBits}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(NumBits &gt; 0) ? NumBits : 0</Size>
        <ValueNode Condition="(AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocatorInstance.SecondaryData.Data	)[$i/32]&gt;&gt;($i%32) &amp; 1) != 0">1</ValueNode>
        <ValueNode Condition="(AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocatorInstance.InlineData			)[$i/32]&gt;&gt;($i%32) &amp; 1) != 0">1</ValueNode>
        <ValueNode Condition="(AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocatorInstance.SecondaryData.Data	)[$i/32]&gt;&gt;($i%32) &amp; 1) == 0">0</ValueNode>
        <ValueNode Condition="(AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AllocatorInstance.InlineData			)[$i/32]&gt;&gt;($i%32) &amp; 1) == 0">0</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TStaticArray visualizer -->
  <Type Name="TStaticArray&lt;*,*,*&gt;">
    <DisplayString>Num={$T2}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>$T2</Size>
        <ValueNode>Storage.Elements[$i].Element</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TPagedArray visualizer -->
  <Type Name="TPagedArray&lt;*,*,*&gt;">
    <DisplayString Condition="Count == 0 &amp;&amp; Pages.ArrayNum == 0">Empty</DisplayString>
    <DisplayString Condition="Count == 0 &amp;&amp; Pages.ArrayNum &gt; 0">Empty, Max={Pages.ArrayNum * ($T2 / sizeof($T1))}</DisplayString>
    <DisplayString Condition="Count &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Count &gt; 0">Num={Count}, Pages={Pages.ArrayNum}, Max={Pages.ArrayNum * ($T2 / sizeof($T1))}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(Count &gt; 0) ? Count : 0</Size>
        <ValueNode>*(($T1*) ((PageType*)Pages.AllocatorInstance.Data + ($i / PageTraits::Capacity))-&gt;AllocatorInstance.Data + ($i % PageTraits::Capacity))</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TDeque visualizer -->
  <Type Name="TDeque&lt;*,*&gt;">
    <DisplayString Condition="Count == 0 &amp;&amp; Capacity == 0">Empty</DisplayString>
    <DisplayString Condition="Count == 0 &amp;&amp; Capacity &gt; 0">Empty, Max={Capacity}</DisplayString>
    <DisplayString Condition="Count &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="Capacity &lt; Count">Invalid</DisplayString>
    <DisplayString Condition="Capacity &gt;= Count">Num={Count}, Max={Capacity}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(Count &lt;= Capacity) ? Count : 0</Size>
        <ValueNode>*(($T1*)Storage.Data + (($i + Head) &lt; Capacity ? ($i + Head) : ($i + Head - Capacity)))</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <Type Name="UE::Deque::Private::TIteratorBase&lt;*,*&gt;">
    <DisplayString Condition="Data == 0">Invalid</DisplayString>
    <DisplayString Condition="Offset == Range">End</DisplayString>
    <DisplayString Condition="Offset &lt; Range">{*($T1*)(Data + Offset)}</DisplayString>
    <Expand>
      <Item Name="[ptr]">($T1*)(Data + Offset)</Item>
    </Expand>
  </Type>
    
  <!-- TRingBuffer visualizer -->
  <Type Name="TRingBuffer&lt;*,*&gt;">
    <DisplayString Condition="AfterBack == Front">Empty</DisplayString>
    <DisplayString Condition="AfterBack - Front &lt;= IndexMask+1">Num={AfterBack - Front}</DisplayString>
    <DisplayString Condition="AfterBack - Front &gt; IndexMask+1">Invalid</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(AfterBack - Front &lt;= IndexMask+1) ? AfterBack - Front : 0</Size>
        <ValueNode>AllocationData[(Front + $i) &amp; IndexMask]</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TRefCountPtr visualizer -->
  <Type Name="TRefCountPtr&lt;*&gt;">
    <SmartPointer Usage="Minimal">Reference</SmartPointer>
    <DisplayString Condition="Reference == 0">nullptr</DisplayString>
    <DisplayString Condition="Reference != 0">{Reference}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Reference">*Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- TUniquePtr visualizer -->
  <Type Name="TUniquePtr&lt;*&gt;">
    <SmartPointer Usage="Minimal">Ptr</SmartPointer>
    <DisplayString Condition="Ptr == 0">nullptr</DisplayString>
    <DisplayString Condition="Ptr != 0">Ptr={Ptr}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Ptr">*Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- TSharedPtr visualizer -->
  <Type Name="TSharedPtr&lt;*,*&gt;">
    <SmartPointer Usage="Minimal">Object</SmartPointer>
    <DisplayString Condition="Object == 0">nullptr</DisplayString>
    <DisplayString Condition="Object != 0">SharedPtr={(void*)Object} {*Object}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Object != 0">*Object</ExpandedItem>
      <Synthetic Name="[Internal]" Condition="Object != 0">
        <DisplayString>SharedCount={SharedReferenceCount.ReferenceController->SharedReferenceCount}, WeakCount={SharedReferenceCount.ReferenceController->WeakReferenceCount}, Ptr={(void*)Object}</DisplayString>
        <Expand>
          <Item Name="[SharedReferenceCount]">SharedReferenceCount.ReferenceController->SharedReferenceCount</Item>
          <Item Name="[WeakReferenceCount]">SharedReferenceCount.ReferenceController->WeakReferenceCount</Item>
          <Item Name="[Ptr]">(void*)Object</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <!-- TSharedRef visualizer -->
  <Type Name="TSharedRef&lt;*,*&gt;">
    <SmartPointer Usage="Minimal">Object</SmartPointer>
    <DisplayString Condition="Object == 0">Unset</DisplayString>
    <DisplayString Condition="Object != 0">SharedRef={(void*)Object} {*Object}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Object != 0">*Object</ExpandedItem>
      <Synthetic Name="[Internal]" Condition="Object != 0">
        <DisplayString>SharedCount={SharedReferenceCount.ReferenceController->SharedReferenceCount}, WeakCount={SharedReferenceCount.ReferenceController->WeakReferenceCount}, Ptr={(void*)Object}</DisplayString>
        <Expand>
          <Item Name="[SharedReferenceCount]">SharedReferenceCount.ReferenceController->SharedReferenceCount</Item>
          <Item Name="[WeakReferenceCount]">SharedReferenceCount.ReferenceController->WeakReferenceCount</Item>
          <Item Name="[Ptr]">(void*)Object</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <!-- TWeakPtr visualizer -->
  <Type Name="TWeakPtr&lt;*,*&gt;">
    <SmartPointer Usage="Minimal">(WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0) ? Object : 0</SmartPointer>
    <DisplayString Condition="Object == 0">nullptr</DisplayString>
    <DisplayString Condition="WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value == 0">Object has been destroyed</DisplayString>
    <DisplayString Condition="Object != 0">WeakPtr={(void*)Object} {*Object}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Object != 0 &amp;&amp; WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0">*Object</ExpandedItem>
      <Synthetic Name="[Internal]" Condition="Object != 0">
        <!-- Include the ptr if it is valid -->
        <DisplayString Condition="WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0">SharedCount={WeakReferenceCount.ReferenceController->SharedReferenceCount}, WeakCount={WeakReferenceCount.ReferenceController->WeakReferenceCount}, Ptr={(void*)Object}</DisplayString>
        <!-- Exclude the ptr if it is invalid -->
        <DisplayString>SharedCount={WeakReferenceCount.ReferenceController->SharedReferenceCount}, WeakCount={WeakReferenceCount.ReferenceController->WeakReferenceCount}</DisplayString>
        <Expand>
          <Item Name="[SharedReferenceCount]">WeakReferenceCount.ReferenceController->SharedReferenceCount</Item>
          <Item Name="[WeakReferenceCount]">WeakReferenceCount.ReferenceController->WeakReferenceCount</Item>
          <Item Name="[Ptr]" Condition="WeakReferenceCount.ReferenceController->SharedReferenceCount._Storage._Value > 0">(void*)Object</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <!-- TMemoryImagePtr visualizer -->
  <Type Name="TMemoryImagePtr&lt;*&gt;">
    <SmartPointer Usage="Minimal">UnfrozenPtr</SmartPointer>
    <DisplayString Condition="UnfrozenPtr == 0">nullptr</DisplayString>
    <DisplayString Condition="UnfrozenPtr != 0 &amp;&amp; (Packed &amp; 1) != 0">{*($T1*)((char*)this + (int64(Packed) >> 24))}</DisplayString>
    <DisplayString Condition="UnfrozenPtr != 0 &amp;&amp; (Packed &amp; 1) == 0">{*($T1*)UnfrozenPtr}</DisplayString>
    <Expand>
      <Item Condition="UnfrozenPtr != 0 &amp;&amp; (Packed &amp; 1) != 0" Name="Object">($T1*)( (char*)this + (int64(Packed) >> 24))</Item>
      <Item Condition="UnfrozenPtr != 0 &amp;&amp; (Packed &amp; 1) == 0" Name="Object">($T1*)UnfrozenPtr</Item>
    </Expand>
  </Type>

  <Type Name="FHashedNameDebugString">
    <DisplayString Condition="String.UnfrozenPtr == 0">Empty</DisplayString>
    <DisplayString Condition="(String.Frozen.Packed &amp; 1) != 0">{(char*)this + (int64(String.Frozen.Packed) >> 24)}</DisplayString>
    <DisplayString>{String.UnfrozenPtr}</DisplayString>
  </Type>

  <!-- TMapBase visualizer -->
  <Type Name="TMapBase&lt;*,*,*,*&gt;">
    <DisplayString>{Pairs}</DisplayString>
    <Expand>
      <ExpandedItem>Pairs</ExpandedItem>
    </Expand>
  </Type>

  <!-- TSet visualizer -->
  <Type Name="TSet&lt;*,*,*&gt;">
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax &gt; 0">Empty, Max={Elements.Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax">Num={Elements.Data.ArrayNum - Elements.NumFreeIndices}, Max={Elements.Data.ArrayMax}</DisplayString>
    <Expand>
      <CustomListItems>
        <Variable Name="Index" InitialValue="0" />
        <!-- Workaround: ElementsAdded and ElementsCount exist so that we could explicitly track the number of items added and break
                         the loop. It's needed because on some platforms CustomListItems does not properly handle the Size tag and we
                         ended up with unbounded iteration over the collection leading to freezes in Visual Studio.
                         Intended to be removed once the debugger incompatibility on the platform in question is resolved.
						 Other visualizers using CustomListItems have been also updated. -->
        <Variable Name="ElementsAdded" InitialValue="0" />
        <Variable Name="ElementsCount" InitialValue="(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0" />
        <Size>(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0</Size>
        <Loop Condition="ElementsAdded &lt; ElementsCount">
          <If Condition="
                 ((Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; ((reinterpret_cast&lt;uint32*&gt;(Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data)[Index/32]&gt;&gt;(Index%32)) &amp; 1) != 0)
              || ((Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; ((reinterpret_cast&lt;uint32*&gt;(Elements.AllocationFlags.AllocatorInstance.InlineData        )[Index/32]&gt;&gt;(Index%32)) &amp; 1) != 0)
          ">
            <Item>((TSetElement &lt;$T1&gt; *)Elements.Data.AllocatorInstance.Data)[Index].Value</Item>
            <Exec>++ElementsAdded</Exec>
          </If>
          <Exec>++Index</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <!-- TSet<*,*,TInlineSetAllocator<*>> visualizer -->
  <Type Name="TSet&lt;*,*,TInlineSetAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax &gt; 0">Empty, Max={Elements.Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax">Num={Elements.Data.ArrayNum - Elements.NumFreeIndices}, Max={Elements.Data.ArrayMax}</DisplayString>
    <Expand>
      <CustomListItems>
        <Variable Name="Index" InitialValue="0" />
        <Variable Name="ElementsAdded" InitialValue="0" />
        <Variable Name="ElementsCount" InitialValue="(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0" />
        <Size>(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0</Size>
        <Loop Condition="ElementsAdded &lt; ElementsCount">
          <If Condition="
                 ((Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; ((reinterpret_cast&lt;uint32*&gt;(Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data)[Index/32]&gt;&gt;(Index%32)) &amp; 1) != 0)
              || ((Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; ((reinterpret_cast&lt;uint32*&gt;(Elements.AllocationFlags.AllocatorInstance.InlineData        )[Index/32]&gt;&gt;(Index%32)) &amp; 1) != 0)
          ">
            <If Condition="Elements.Data.AllocatorInstance.SecondaryData.Data == 0">
              <Item>((TSetElement&lt;$T1&gt; *)Elements.Data.AllocatorInstance.InlineData)[Index].Value</Item>
              <Exec>++ElementsAdded</Exec>
            </If>
            <If Condition="Elements.Data.AllocatorInstance.SecondaryData.Data != 0">
              <Item>((TSetElement&lt;$T1&gt; *)Elements.Data.AllocatorInstance.SecondaryData.Data)[Index].Value</Item>
              <Exec>++ElementsAdded</Exec>
            </If>
          </If>
          <Exec>++Index</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <!-- TSet<*,*,TFixedSetAllocator<*>> visualizer -->
  <Type Name="TSet&lt;*,*,TFixedSetAllocator&lt;*&gt;&gt;">
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax == 0">Empty</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum - Elements.NumFreeIndices &lt;= 0 &amp;&amp; Elements.Data.ArrayMax &gt; 0">Empty, Max={Elements.Data.ArrayMax}</DisplayString>
    <DisplayString Condition="Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax">Num={Elements.Data.ArrayNum - Elements.NumFreeIndices}, Max={Elements.Data.ArrayMax}</DisplayString>
    <Expand>
      <CustomListItems>
        <Variable Name="Index" InitialValue="0" />
        <Variable Name="ElementsAdded" InitialValue="0" />
        <Variable Name="ElementsCount" InitialValue="(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0" />
        <Size>(Elements.Data.ArrayNum - Elements.NumFreeIndices &gt; 0 &amp;&amp; Elements.Data.ArrayNum &lt;= Elements.Data.ArrayMax) ? Elements.Data.ArrayNum - Elements.NumFreeIndices : 0</Size>
        <Loop Condition="ElementsAdded &lt; ElementsCount">
          <If Condition="((reinterpret_cast&lt;uint32*&gt;(Elements.AllocationFlags.AllocatorInstance.InlineData)[Index/32]&gt;&gt;(Index%32)) &amp; 1) != 0">
            <Item>((TSetElement&lt;$T1&gt; *)Elements.Data.AllocatorInstance.InlineData)[Index].Value</Item>
            <Exec>++ElementsAdded</Exec>
          </If>
          <Exec>++Index</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <Type Name="FObjectKey">
    <DisplayString>{*(FWeakObjectPtr*)this}</DisplayString>
  </Type>
  
  <!-- FWeakObjectPtr visualizer -->
  <Type Name="FWeakObjectPtr">
    <SmartPointer Usage="Minimal">GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object</SmartPointer>
    <DisplayString Condition="ObjectSerialNumber &lt; 1">nullptr</DisplayString>
    <DisplayString Condition="GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].SerialNumber != ObjectSerialNumber">STALE</DisplayString>
    <DisplayString>{GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object}</DisplayString>
    <Expand>
      <ExpandedItem>GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object</ExpandedItem>
    </Expand>
  </Type>

  <!-- TWeakObjectPtr<*> visualizer -->
  <Type Name="TWeakObjectPtr&lt;*&gt;">
    <SmartPointer Usage="Minimal">($T1*)GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object</SmartPointer>
    <DisplayString Condition="ObjectSerialNumber &lt; 1">nullptr</DisplayString>
    <DisplayString Condition="GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].SerialNumber != ObjectSerialNumber">STALE</DisplayString>
    <DisplayString>{($T1*)GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object}</DisplayString>
    <Expand>
      <ExpandedItem>($T1*)GObjectArrayForDebugVisualizers->Objects[ObjectIndex / 65536][ObjectIndex % 65536].Object</ExpandedItem>
    </Expand>
  </Type>

  <!-- TSoftObjectPtr<*> visualizer -->
  <Type Name="TSoftObjectPtr&lt;*&gt;">
    <DisplayString>{SoftObjectPtr}</DisplayString>
    <Expand>
      <ExpandedItem>SoftObjectPtr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FSoftObjectPtr visualizer -->
  <Type Name="FSoftObjectPtr">
    <DisplayString>{WeakPtr} ({ObjectID})</DisplayString>
    <Expand>
      <ExpandedItem>WeakPtr</ExpandedItem>
      <ExpandedItem>ObjectId</ExpandedItem>
    </Expand>
  </Type>

  <!-- FTypedElementHandle visualizer -->
  <Type Name="FTypedElementHandle">
    <DisplayString Condition="DataPtr == 0">Unset</DisplayString>
    <DisplayString Condition="DataPtr != 0">{*DataPtr}</DisplayString>
  </Type>
  
  <!-- TTypedElementOwner visualizer -->
  <Type Name="TTypedElementOwner&lt;*,*&gt;">
    <DisplayString Condition="DataPtr == 0">Unset</DisplayString>
    <DisplayString Condition="DataPtr != 0">{*DataPtr}</DisplayString>
  </Type>

  <!-- FVertexID visualizer -->
  <Type Name="FVertexID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- FVertexInstanceID visualizer -->
  <Type Name="FVertexInstanceID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- FEdgeID visualizer -->
  <Type Name="FEdgeID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- FPolygonID visualizer -->
  <Type Name="FPolygonID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- FPolygonGroupID visualizer -->
  <Type Name="FPolygonGroupID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- FTriangleID visualizer -->
  <Type Name="FTriangleID">
    <DisplayString>{IDValue}</DisplayString>
  </Type>

  <!-- TOptional visualizer -->
  <Type Name="TOptional&lt;*&gt;">
    <SmartPointer Usage="Minimal">($T1*)&amp;Value</SmartPointer>
    <!-- Until we can generically detect whether a value with an intrusive unset state is set or unset, we just always display
         the value with an "Optional:" prefix, letting the type's own visualizer control how it shows its 'unset' state. -->
    <DisplayString Condition="sizeof(*this) == sizeof($T1)">Optional: {{{*($T1*)&amp;Value}}}</DisplayString>
    <DisplayString Condition="sizeof(*this) != sizeof($T1) &amp;&amp; !*(bool*)(($T1*)&amp;Value + 1)">Unset</DisplayString>
    <DisplayString Condition="sizeof(*this) != sizeof($T1) &amp;&amp; *(bool*)(($T1*)&amp;Value + 1)">Set: {{{*($T1*)&amp;Value}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="sizeof(*this) != sizeof($T1) &amp;&amp; *(bool*)(($T1*)&amp;Value + 1)">*($T1*)&amp;Value</ExpandedItem>
      <ExpandedItem Condition="sizeof(*this) == sizeof($T1)">*($T1*)&amp;Value</ExpandedItem>
    </Expand>
  </Type>

  <!-- TUnion visualizer -->
  <Type Name="TUnion&lt;*&gt;">
    <DisplayString>TUnion{{{CurrentSubtypeIndex}}}</DisplayString>
    <Expand>
      <Item Name="[TUnion.CurrentSubtypeIndex]">CurrentSubtypeIndex</Item>
      <ExpandedItem Condition="CurrentSubtypeIndex==0">*($T1*)&amp;Values.A</ExpandedItem>
      <ExpandedItem Condition="CurrentSubtypeIndex==1">*($T2*)&amp;Values.B</ExpandedItem>
      <ExpandedItem Condition="CurrentSubtypeIndex==2">*($T3*)&amp;Values.C</ExpandedItem>
      <ExpandedItem Condition="CurrentSubtypeIndex==3">*($T4*)&amp;Values.D</ExpandedItem>
      <ExpandedItem Condition="CurrentSubtypeIndex==4">*($T5*)&amp;Values.E</ExpandedItem>
      <ExpandedItem Condition="CurrentSubtypeIndex==5">*($T6*)&amp;Values.F</ExpandedItem>
    </Expand>
  </Type>

  <!-- TInlineValue visualizer -->
  <Type Name="TInlineValue&lt;*&gt;">
    <SmartPointer Usage="Minimal">!bIsValid ? nullptr : bInline ? ($T1*)&amp;Data : *($T1**)&amp;Data</SmartPointer>
    <DisplayString Condition="!bIsValid">Unset</DisplayString>
    <DisplayString Condition="bIsValid &amp;&amp; bInline">{{{*($T1*)&amp;Data}}}</DisplayString>
    <DisplayString Condition="bIsValid &amp;&amp; !bInline">{{{**(($T1**)&amp;Data)}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="bIsValid &amp;&amp; bInline">*($T1*)&amp;Data</ExpandedItem>
      <ExpandedItem Condition="bIsValid &amp;&amp; !bInline">**(($T1**)&amp;Data)</ExpandedItem>
    </Expand>
  </Type>

  <!-- TFunction visualizer -->
  <Type Name="UE::Core::Private::Function::TDebugHelper&lt;void&gt;">
    <DisplayString>{Ptr}</DisplayString>
    <Expand>
      <Item Name="[Lambda]">Ptr</Item>
    </Expand>
  </Type>
  <Type Name="UE::Core::Private::Function::TDebugHelper&lt;*&gt;">
    <DisplayString>{*Ptr}</DisplayString>
    <Expand>
      <Item Name="[Lambda]">*Ptr</Item>
    </Expand>
  </Type>
  <Type Name="TFunctionRef&lt;*&gt;">
    <DisplayString Condition="Callable">{DebugPtrStorage}</DisplayString>
    <DisplayString Condition="!Callable">Unset</DisplayString>
    <Expand>
      <ExpandedItem Condition="Callable">DebugPtrStorage</ExpandedItem>
    </Expand>
  </Type>
  <Type Name="TFunction&lt;*&gt;">
    <AlternativeType Name="TUniqueFunction&lt;*&gt;"></AlternativeType>
    <DisplayString Condition="Callable != 0">{DebugPtrStorage}</DisplayString>
    <DisplayString Condition="Callable == 0">Unset</DisplayString>
    <Expand>
      <ExpandedItem Condition="Callable != 0">DebugPtrStorage</ExpandedItem>
    </Expand>
  </Type>

  <!-- FGameplayTagContainer visualizer -->
  <Type Name="FGameplayTagContainer">
    <DisplayString Condition="GameplayTags.ArrayNum == 0">Empty</DisplayString>
    <DisplayString Condition="GameplayTags.ArrayNum == 1">Tag={*((FName*)(GameplayTags.AllocatorInstance.Data))}</DisplayString>
    <DisplayString Condition="GameplayTags.ArrayNum > 1">Num={GameplayTags.ArrayNum}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>(GameplayTags.ArrayNum &lt;= GameplayTags.ArrayMax) ? GameplayTags.ArrayNum : 0</Size>
        <ValuePointer>((FName*)(GameplayTags.AllocatorInstance.Data))</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- FActorRepList visualizer -->
  <Type Name="FActorRepList">
    <DisplayString >({Num}/{Max} {RefCount})</DisplayString>
    <Expand>
      <Item Name="[RefCount]">RefCount</Item>
      <Item Name="[Num]">Num</Item>
      <Item Name="[Max]">Max</Item>
      <ArrayItems>
        <Size>Num</Size>
        <ValuePointer>((FActorRepListType*)(Data))</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- FBoneIndexWithOperators visualizer -->
  <Type Name="FBoneIndexWithOperators&lt;*&gt;">
    <DisplayString>{BoneIndex}</DisplayString>
  </Type>

  <!-- TTuple visualizer -->
  <Type Name="TTuple&lt;&gt;">
    <DisplayString>{{}}</DisplayString>
    <Expand>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,1&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,1&gt;*)this)->Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*&gt;">
    <DisplayString>{{{Key},{Value}}}</DisplayString>
    <Expand>
      <Item Name="[0:Key]">Key</Item>
      <Item Name="[1:Value]">Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*,*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,3&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,3&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,3&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,3&gt;*)this)->Value</Item>
      <Item Name="[1]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,3&gt;*)this)->Value</Item>
      <Item Name="[2]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,3&gt;*)this)->Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*,*,*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,4&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,4&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,4&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,4&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,4&gt;*)this)->Value</Item>
      <Item Name="[1]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,4&gt;*)this)->Value</Item>
      <Item Name="[2]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,4&gt;*)this)->Value</Item>
      <Item Name="[3]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,4&gt;*)this)->Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*,*,*,*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,5&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,5&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,5&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,5&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,5&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,5&gt;*)this)->Value</Item>
      <Item Name="[1]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,5&gt;*)this)->Value</Item>
      <Item Name="[2]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,5&gt;*)this)->Value</Item>
      <Item Name="[3]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,5&gt;*)this)->Value</Item>
      <Item Name="[4]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,5&gt;*)this)->Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*,*,*,*,*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,6&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,6&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,6&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,6&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,6&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T6,5,6&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,6&gt;*)this)->Value</Item>
      <Item Name="[1]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,6&gt;*)this)->Value</Item>
      <Item Name="[2]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,6&gt;*)this)->Value</Item>
      <Item Name="[3]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,6&gt;*)this)->Value</Item>
      <Item Name="[4]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,6&gt;*)this)->Value</Item>
      <Item Name="[5]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T6,5,6&gt;*)this)->Value</Item>
    </Expand>
  </Type>
  <Type Name="TTuple&lt;*,*,*,*,*,*,*&gt;">
    <DisplayString>{{{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T6,5,7&gt;*)this).Value},{(*(UE::Core::Private::Tuple::TTupleBaseElement&lt;$T7,6,7&gt;*)this).Value}}}</DisplayString>
    <Expand>
      <Item Name="[0]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T1,0,7&gt;*)this)->Value</Item>
      <Item Name="[1]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T2,1,7&gt;*)this)->Value</Item>
      <Item Name="[2]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T3,2,7&gt;*)this)->Value</Item>
      <Item Name="[3]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T4,3,7&gt;*)this)->Value</Item>
      <Item Name="[4]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T5,4,7&gt;*)this)->Value</Item>
      <Item Name="[5]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T6,5,7&gt;*)this)->Value</Item>
      <Item Name="[6]">((UE::Core::Private::Tuple::TTupleBaseElement&lt;$T7,6,7&gt;*)this)->Value</Item>
    </Expand>
  </Type>

  <!-- TDelegateBase visualizer -->
  <Type Name="TBaseStaticDelegateInstance&lt;*&gt;">
    <DisplayString>{StaticFuncPtr}</DisplayString>
    <Expand>
      <Item Name="[StaticFuncPtr]">StaticFuncPtr</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TBaseFunctorDelegateInstance&lt;*&gt;">
    <DisplayString>{Functor}</DisplayString>
    <Expand>
      <Item Name="[Functor]">Functor</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TBaseRawMethodDelegateInstance&lt;*,*,*&gt;">
    <DisplayString>{UserObject}</DisplayString>
    <Expand>
      <Item Name="[UserObject]">UserObject</Item>
      <Item Name="[MethodPtr]">MethodPtr</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TBaseSPMethodDelegateInstance&lt;*,*,*&gt;">
    <DisplayString>{UserObject}</DisplayString>
    <Expand>
      <Item Name="[UserObject]">UserObject</Item>
      <Item Name="[MethodPtr]">MethodPtr</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TBaseUObjectMethodDelegateInstance&lt;*,*,*&gt;">
    <DisplayString>{UserObject}</DisplayString>
    <Expand>
      <Item Name="[UserObject]">UserObject</Item>
      <Item Name="[MethodPtr]">MethodPtr</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TBaseUFunctionDelegateInstance&lt;*,*&gt;">
    <DisplayString>{UserObjectPtr}</DisplayString>
    <Expand>
      <Item Name="[UserObject]">UserObjectPtr</Item>
      <Item Name="[FunctionName]">FunctionName</Item>
      <Item Name="[Payload]">Payload</Item>
      <Item Name="[Handle]">Handle</Item>
    </Expand>
  </Type>
  <Type Name="TDelegateBase&lt;*&gt;" Priority="MediumLow">
    <DisplayString Condition="DelegateSize == 0">Unbound</DisplayString>
    <DisplayString Condition="DelegateSize &lt;= 2">{*((IDelegateInstance*)(DelegateAllocator.InlineData))}</DisplayString>
    <DisplayString Condition="DelegateSize &gt; 2">{*((IDelegateInstance*)(DelegateAllocator.SecondaryData.Data))}</DisplayString>
    <Expand>
      <ExpandedItem Condition="DelegateSize == 0">DelegateAllocator</ExpandedItem>
      <ExpandedItem Condition="DelegateSize &lt;= 2">*((IDelegateInstance*)(DelegateAllocator.InlineData))</ExpandedItem>
      <ExpandedItem Condition="DelegateSize &gt; 2">*((IDelegateInstance*)(DelegateAllocator.SecondaryData.Data))</ExpandedItem>
    </Expand>
  </Type>
  <Type Name="TDelegateBase&lt;*&gt;">
    <DisplayString Condition="DelegateSize == 0">Unbound</DisplayString>
    <DisplayString Condition="DelegateSize != 0">{*((IDelegateInstance*)(DelegateAllocator.Data))}</DisplayString>
    <Expand>
      <ExpandedItem Condition="DelegateSize == 0">DelegateAllocator</ExpandedItem>
      <ExpandedItem Condition="DelegateSize != 0">*((IDelegateInstance*)(DelegateAllocator.Data))</ExpandedItem>
    </Expand>
  </Type>
  <Type Name="TMulticastDelegate&lt;*,*&gt;" Priority="MediumLow">
    <DisplayString Condition="InvocationList.ArrayNum == 0">Empty</DisplayString>
    <DisplayString Condition="InvocationList.ArrayNum != 0">Num={InvocationList.ArrayNum}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>InvocationList.ArrayNum</Size>
        <ValuePointer Condition="InvocationList.AllocatorInstance.SecondaryData.Data == 0">(TDelegateBase&lt;$T2&gt;*)(InvocationList.AllocatorInstance.InlineData)</ValuePointer>
        <ValuePointer Condition="InvocationList.AllocatorInstance.SecondaryData.Data != 0">(TDelegateBase&lt;$T2&gt;*)(InvocationList.AllocatorInstance.SecondaryData.Data)</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="TMulticastDelegate&lt;*,*&gt;">
    <DisplayString Condition="InvocationList.ArrayNum == 0">Empty</DisplayString>
    <DisplayString Condition="InvocationList.ArrayNum != 0">Num={InvocationList.ArrayNum}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>InvocationList.ArrayNum</Size>
        <ValuePointer>(TDelegateBase&lt;$T2&gt;*)(InvocationList.AllocatorInstance.Data)</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- TAtomic visualizer -->
  <Type Name="TAtomic&lt;*&gt;">
    <DisplayString>{Element}</DisplayString>
    <Expand>
      <ExpandedItem>Element</ExpandedItem>
    </Expand>
  </Type>

  <!-- TRange visualizer -->
  <Type Name="TRange&lt;*&gt;">
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Open &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Open">[-&#8734;, +&#8734;]</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Open &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Inclusive">[-&#8734;, {UpperBound.Value}]</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Open &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Exclusive">[-&#8734;, {UpperBound.Value})</DisplayString>

    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Inclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Open">[{LowerBound.Value}, +&#8734;)</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Inclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Inclusive">[{LowerBound.Value}, {UpperBound.Value}]</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Inclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Exclusive">[{LowerBound.Value}, {UpperBound.Value})</DisplayString>

    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Exclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Open">({LowerBound.Value}, +&#8734;]</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Exclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Inclusive">({LowerBound.Value}, {UpperBound.Value}]</DisplayString>
    <DisplayString Condition="LowerBound.Type.Value == ERangeBoundTypes::Exclusive &amp;&amp; UpperBound.Type.Value == ERangeBoundTypes::Exclusive">({LowerBound.Value}, {UpperBound.Value})</DisplayString>
  </Type>

  <!-- FFrameNumber visualizer -->
  <Type Name="FFrameNumber">
    <DisplayString>{Value}</DisplayString>
  </Type>

  <!-- FFrameTime visualizer -->
  <Type Name="FFrameTime">
    <DisplayString Condition="SubFrame == 0.0">{FrameNumber}</DisplayString>
    <DisplayString Condition="SubFrame != 0.0">{(double)(FrameNumber.Value)+SubFrame}</DisplayString>
  </Type>

  <!-- FRHICommandList visualizer -->
  <Type Name="FRHICommandBase">
    <DisplayString>{{ RHI Command -> { this->__vfptr[0] } }}</DisplayString>
  </Type>

  <Type Name="FRHICommandListBase">
    <Expand>
      <LinkedListItems>
        <HeadPointer>Root</HeadPointer>
        <NextPointer>Next</NextPointer>
        <ValueNode>this</ValueNode>
      </LinkedListItems>
    </Expand>
  </Type>

  <!-- TStructOnScope visualizer -->
  <Type Name="TStructOnScope&lt;*&gt;">
    <DisplayString Condition="SampleStructMemory == 0">nullptr</DisplayString>
    <DisplayString Condition="SampleStructMemory != 0">Struct={(void*)SampleStructMemory} {*($T1*)SampleStructMemory}</DisplayString>
    <Expand>
      <ExpandedItem Condition="SampleStructMemory != 0">*($T1*)SampleStructMemory</ExpandedItem>
    </Expand>
  </Type>
	
	<!-- TVariant visualizer -->
	<Type Name="TVariant&lt;*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 3">{*($T4*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 3">*($T4*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 3">{*($T4*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 4">{*($T5*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 3">*($T4*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 4">*($T5*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*,*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 3">{*($T4*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 4">{*($T5*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 5">{*($T6*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 3">*($T4*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 4">*($T5*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 5">*($T6*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*,*,*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 3">{*($T4*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 4">{*($T5*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 5">{*($T6*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 6">{*($T7*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 3">*($T4*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 4">*($T5*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 5">*($T6*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 6">*($T7*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>
	<Type Name="TVariant&lt;*,*,*,*,*,*,*,*&gt;">
		<DisplayString Condition="TypeIndex == 0">{*($T1*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 1">{*($T2*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 2">{*($T3*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 3">{*($T4*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 4">{*($T5*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 5">{*($T6*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 6">{*($T7*)&amp;Storage}</DisplayString>
		<DisplayString Condition="TypeIndex == 7">{*($T8*)&amp;Storage}</DisplayString>
		<DisplayString>Uninitialized</DisplayString>
		<Expand>
			<Item Name="Storage" Condition="TypeIndex == 0">*($T1*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 1">*($T2*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 2">*($T3*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 3">*($T4*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 4">*($T5*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 5">*($T6*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 6">*($T7*)&amp;Storage</Item>
			<Item Name="Storage" Condition="TypeIndex == 7">*($T8*)&amp;Storage</Item>
			<Item Name="TypeIndex">TypeIndex</Item>
		</Expand>
	</Type>

  <!-- 
  *
  * Animation Visualizers 
  *
  -->

  <!-- FBoneContainer -->
  <Type Name="FBoneContainer">
    <DisplayString>{{Num={BoneIndicesArray.ArrayNum} Asset={Asset} Skeleton={AssetSkeleton}}}</DisplayString>
    <Expand>
      <Item Name="Asset">Asset</Item>
      <Item Name="AssetSkeletalMesh">AssetSkeletalMesh</Item>
      <Item Name="AssetSkeleton">AssetSkeleton</Item>
      <Item Name="RefSkeleton">RefSkeleton</Item>
      <Synthetic Name="BoneIndicesArray">
        <DisplayString Condition="BoneIndicesArray.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="BoneIndicesArray.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="BoneIndicesArray.ArrayMax &lt; BoneIndicesArray.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="BoneIndicesArray.ArrayMax &gt;= BoneIndicesArray.ArrayNum">Num={BoneIndicesArray.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
			<Variable Name="CompactPoseBoneCount" InitialValue="(BoneIndicesArray.ArrayNum &lt;= BoneIndicesArray.ArrayMax) ? BoneIndicesArray.ArrayNum : 0" />
            <Size>(BoneIndicesArray.ArrayNum &lt;= BoneIndicesArray.ArrayMax) ? BoneIndicesArray.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((unsigned short*)BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="BoneSwitchArray">
        <DisplayString Condition="BoneSwitchArray.NumBits == 0">Empty</DisplayString>
        <DisplayString Condition="BoneSwitchArray.NumBits &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="BoneSwitchArray.NumBits &gt; 0">NumBits={BoneSwitchArray.NumBits}, MaxBits={BoneSwitchArray.MaxBits}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="MeshBoneIndex" InitialValue="0"/>
            <Variable Name="MeshBoneCount" InitialValue="(BoneSwitchArray.NumBits &gt; 0) ? BoneSwitchArray.NumBits : 0" />
            <Size>(BoneSwitchArray.NumBits &gt; 0) ? BoneSwitchArray.NumBits : 0</Size>
            <Loop Condition="BoneSwitchArray.AllocatorInstance.SecondaryData.Data != 0 &amp;&amp; MeshBoneIndex &lt; MeshBoneCount">
              <Item Name="[{MeshBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[MeshBoneIndex].Name}" Condition="MeshBoneIndex &lt; RefSkeleton->FinalRefBoneInfo.ArrayNum">
                ((uint32*)BoneSwitchArray.AllocatorInstance.SecondaryData.Data)[MeshBoneIndex/32] >> (MeshBoneIndex%32) &amp; 1
              </Item>
              <Item Name="[{MeshBoneIndex}]" Condition="MeshBoneIndex &gt;= RefSkeleton->FinalRefBoneInfo.ArrayNum">
                ((uint32*)BoneSwitchArray.AllocatorInstance.SecondaryData.Data)[MeshBoneIndex/32] >> (MeshBoneIndex%32) &amp; 1
              </Item>
              <Exec>++MeshBoneIndex</Exec>
            </Loop>
            <Loop Condition="BoneSwitchArray.AllocatorInstance.SecondaryData.Data == 0 &amp;&amp; MeshBoneIndex &lt; MeshBoneCount">
              <Item Name="[{MeshBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[MeshBoneIndex].Name}" Condition="MeshBoneIndex &lt; RefSkeleton->FinalRefBoneInfo.ArrayNum">
                ((uint32*)BoneSwitchArray.AllocatorInstance.InlineData)[MeshBoneIndex/32] >> (MeshBoneIndex%32) &amp; 1
              </Item>
              <Item Name="[{MeshBoneIndex}]" Condition="MeshBoneIndex &gt;= RefSkeleton->FinalRefBoneInfo.ArrayNum">
                ((uint32*)BoneSwitchArray.AllocatorInstance.InlineData)[MeshBoneIndex/32] >> (MeshBoneIndex%32) &amp; 1
              </Item>
              <Exec>++MeshBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="SkeletonToPoseBoneIndexArray">
        <DisplayString Condition="SkeletonToPoseBoneIndexArray.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="SkeletonToPoseBoneIndexArray.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="SkeletonToPoseBoneIndexArray.ArrayMax &lt; SkeletonToPoseBoneIndexArray.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="SkeletonToPoseBoneIndexArray.ArrayMax &gt;= SkeletonToPoseBoneIndexArray.ArrayNum">Num={SkeletonToPoseBoneIndexArray.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="SkeletonBoneIndex" InitialValue="0"/>
            <Variable Name="SkeletonBoneCount" InitialValue="(SkeletonToPoseBoneIndexArray.ArrayNum &lt;= SkeletonToPoseBoneIndexArray.ArrayMax) ? SkeletonToPoseBoneIndexArray.ArrayNum : 0" />
            <Size>(SkeletonToPoseBoneIndexArray.ArrayNum &lt;= SkeletonToPoseBoneIndexArray.ArrayMax) ? SkeletonToPoseBoneIndexArray.ArrayNum : 0</Size>
            <Loop Condition="SkeletonBoneIndex &lt; SkeletonBoneCount">
              <Item Name="[{SkeletonBoneIndex}] {((FMeshBoneInfo*)((USkeleton*)GObjectArrayForDebugVisualizers->Objects[AssetSkeleton.ObjectIndex / 65536][AssetSkeleton.ObjectIndex % 65536].Object)->ReferenceSkeleton.FinalRefBoneInfo.AllocatorInstance.Data)[SkeletonBoneIndex].Name}">
                ((int*)SkeletonToPoseBoneIndexArray.AllocatorInstance.Data)[SkeletonBoneIndex]
              </Item>
              <Exec>++SkeletonBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="PoseToSkeletonBoneIndexArray">
        <DisplayString Condition="PoseToSkeletonBoneIndexArray.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="PoseToSkeletonBoneIndexArray.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="PoseToSkeletonBoneIndexArray.ArrayMax &lt; PoseToSkeletonBoneIndexArray.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="PoseToSkeletonBoneIndexArray.ArrayMax &gt;= PoseToSkeletonBoneIndexArray.ArrayNum">Num={PoseToSkeletonBoneIndexArray.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="MeshBoneIndex" InitialValue="0"/>
			<Variable Name="MeshBoneCount" InitialValue="(PoseToSkeletonBoneIndexArray.ArrayNum &lt;= PoseToSkeletonBoneIndexArray.ArrayMax) ? PoseToSkeletonBoneIndexArray.ArrayNum : 0" />
            <Size>(PoseToSkeletonBoneIndexArray.ArrayNum &lt;= PoseToSkeletonBoneIndexArray.ArrayMax) ? PoseToSkeletonBoneIndexArray.ArrayNum : 0</Size>
            <Loop Condition="MeshBoneIndex &lt; MeshBoneCount">
              <Item Name="[{MeshBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[MeshBoneIndex].Name}">
                ((int*)PoseToSkeletonBoneIndexArray.AllocatorInstance.Data)[MeshBoneIndex]
              </Item>
              <Exec>++MeshBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="CompactPoseToSkeletonIndex">
        <DisplayString Condition="CompactPoseToSkeletonIndex.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="CompactPoseToSkeletonIndex.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseToSkeletonIndex.ArrayMax &lt; CompactPoseToSkeletonIndex.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseToSkeletonIndex.ArrayMax &gt;= CompactPoseToSkeletonIndex.ArrayNum">Num={CompactPoseToSkeletonIndex.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
			<Variable Name="CompactPoseBoneCount" InitialValue="(CompactPoseToSkeletonIndex.ArrayNum &lt;= CompactPoseToSkeletonIndex.ArrayMax) ? CompactPoseParentBones.ArrayNum : 0" />
            <Size>(CompactPoseToSkeletonIndex.ArrayNum &lt;= CompactPoseToSkeletonIndex.ArrayMax) ? CompactPoseParentBones.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((int*)CompactPoseToSkeletonIndex.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="SkeletonToCompactPose">
        <DisplayString Condition="SkeletonToCompactPose.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="SkeletonToCompactPose.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="SkeletonToCompactPose.ArrayMax &lt; SkeletonToCompactPose.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="SkeletonToCompactPose.ArrayMax &gt;= SkeletonToCompactPose.ArrayNum">Num={SkeletonToCompactPose.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="SkeletonBoneIndex" InitialValue="0"/>
            <Variable Name="SkeletonBoneCount" InitialValue="(SkeletonToCompactPose.ArrayNum &lt;= SkeletonToCompactPose.ArrayMax) ? SkeletonToCompactPose.ArrayNum : 0" />
            <Size>(SkeletonToCompactPose.ArrayNum &lt;= SkeletonToCompactPose.ArrayMax) ? SkeletonToCompactPose.ArrayNum : 0</Size>
            <Loop Condition="SkeletonBoneIndex &lt; SkeletonBoneCount">
              <Item Name="[{SkeletonBoneIndex}] {((FMeshBoneInfo*)((USkeleton*)GObjectArrayForDebugVisualizers->Objects[AssetSkeleton.ObjectIndex / 65536][AssetSkeleton.ObjectIndex % 65536].Object)->ReferenceSkeleton.FinalRefBoneInfo.AllocatorInstance.Data)[SkeletonBoneIndex].Name}">
                ((int*)SkeletonToCompactPose.AllocatorInstance.Data)[SkeletonBoneIndex]
              </Item>
              <Exec>++SkeletonBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="CompactPoseParentBones">
        <DisplayString Condition="CompactPoseParentBones.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="CompactPoseParentBones.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseParentBones.ArrayMax &lt; CompactPoseParentBones.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseParentBones.ArrayMax &gt;= CompactPoseParentBones.ArrayNum">Num={CompactPoseParentBones.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
            <Variable Name="CompactPoseBoneCount" InitialValue="(CompactPoseParentBones.ArrayNum &lt;= CompactPoseParentBones.ArrayMax) ? CompactPoseParentBones.ArrayNum : 0" />
            <Size>(CompactPoseParentBones.ArrayNum &lt;= CompactPoseParentBones.ArrayMax) ? CompactPoseParentBones.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((FCompactPoseBoneIndex*)CompactPoseParentBones.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="CompactPoseRefPoseBones">
        <DisplayString Condition="CompactPoseRefPoseBones.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="CompactPoseRefPoseBones.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseRefPoseBones.ArrayMax &lt; CompactPoseRefPoseBones.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="CompactPoseRefPoseBones.ArrayMax &gt;= CompactPoseRefPoseBones.ArrayNum">Num={CompactPoseRefPoseBones.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
            <Variable Name="CompactPoseBoneCount" InitialValue="(CompactPoseRefPoseBones.ArrayNum &lt;= CompactPoseRefPoseBones.ArrayMax) ? CompactPoseRefPoseBones.ArrayNum : 0" />
            <Size>(CompactPoseRefPoseBones.ArrayNum &lt;= CompactPoseRefPoseBones.ArrayMax) ? CompactPoseRefPoseBones.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((FTransform*)CompactPoseRefPoseBones.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Item Name="VirtualBoneCompactPoseData">VirtualBoneCompactPoseData</Item>
      <Synthetic Name="UIDToArrayIndexLUT">
        <DisplayString Condition="UIDToArrayIndexLUT.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="UIDToArrayIndexLUT.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="UIDToArrayIndexLUT.ArrayMax &lt; UIDToArrayIndexLUT.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="UIDToArrayIndexLUT.ArrayMax &gt;= UIDToArrayIndexLUT.ArrayNum">Num={UIDToArrayIndexLUT.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="UID" InitialValue="0"/>
            <Variable Name="UIDCount" InitialValue="(UIDToArrayIndexLUT.ArrayNum &lt;= UIDToArrayIndexLUT.ArrayMax) ? UIDToArrayIndexLUT.ArrayNum : 0" />
            <Size>(UIDToArrayIndexLUT.ArrayNum &lt;= UIDToArrayIndexLUT.ArrayMax) ? UIDToArrayIndexLUT.ArrayNum : 0</Size>
            <Loop Condition="UID &lt; UIDCount">
              <Item Name="[{UID}] {((FName*)UIDToNameLUT.AllocatorInstance.Data)[UID]}">
                ((unsigned short*)UIDToArrayIndexLUT.AllocatorInstance.Data)[UID]
              </Item>
              <Exec>++UID</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Item Name="UIDToNameLUT">UIDToNameLUT</Item>
      <Synthetic Name="UIDToCurveTypeLUT">
        <DisplayString Condition="UIDToCurveTypeLUT.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="UIDToCurveTypeLUT.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="UIDToCurveTypeLUT.ArrayMax &lt; UIDToCurveTypeLUT.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="UIDToCurveTypeLUT.ArrayMax &gt;= UIDToCurveTypeLUT.ArrayNum">Num={UIDToCurveTypeLUT.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="UID" InitialValue="0"/>
            <Variable Name="UIDCount" InitialValue="(UIDToCurveTypeLUT.ArrayNum &lt;= UIDToCurveTypeLUT.ArrayMax) ? UIDToCurveTypeLUT.ArrayNum : 0" />
            <Size>(UIDToCurveTypeLUT.ArrayNum &lt;= UIDToCurveTypeLUT.ArrayMax) ? UIDToCurveTypeLUT.ArrayNum : 0</Size>
            <Loop Condition="UID &lt; UIDCount">
              <Item Name="[{UID}] {((FName*)UIDToNameLUT.AllocatorInstance.Data)[UID]}">
                ((FAnimCurveType*)UIDToCurveTypeLUT.AllocatorInstance.Data)[UID]
              </Item>
              <Exec>++UID</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Item Name="bDisableRetargeting">bDisableRetargeting</Item>
      <Item Name="bUseRAWData">bUseRAWData</Item>
      <Item Name="bUseSourceData">bUseSourceData</Item>
      <Item Name="RetargetSourceCachedDataLUT">RetargetSourceCachedDataLUT</Item>
    </Expand>
  </Type>

  <!-- FCompactPose -->
  <Type Name="FCompactPose">
    <DisplayString>{{Num={Bones.ArrayNum} Asset={BoneContainer->Asset} Skeleton={BoneContainer->AssetSkeleton}}}</DisplayString>
    <Expand>
      <Item Name="BoneContainer">BoneContainer</Item>
      <Synthetic Name="Bones" Condition="BoneContainer != 0">
        <DisplayString Condition="Bones.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="Bones.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="Bones.ArrayMax &lt; Bones.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="Bones.ArrayMax &gt;= Bones.ArrayNum">Num={Bones.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
            <Variable Name="CompactPoseBoneCount" InitialValue="(Bones.ArrayNum &lt;= Bones.ArrayMax) ? Bones.ArrayNum : 0" />
            <Size>(Bones.ArrayNum &lt;= Bones.ArrayMax) ? Bones.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)BoneContainer->RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)BoneContainer->BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((FTransform*)Bones.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Item Name="Bones" Condition="BoneContainer == 0">Bones</Item>
    </Expand>
  </Type>

  <!-- FCSPose<FCompactPose> -->
  <Type Name="FCSPose&lt;FCompactPose&gt;">
    <DisplayString>{{Num={Pose.Bones.ArrayNum} Asset={Pose.BoneContainer->Asset} Skeleton={Pose.BoneContainer->AssetSkeleton}}}</DisplayString>
    <Expand>
      <Item Name="Pose">Pose</Item>
      <Synthetic Name="ComponentSpaceFlags">
        <DisplayString Condition="ComponentSpaceFlags.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="ComponentSpaceFlags.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="ComponentSpaceFlags.ArrayMax &lt; ComponentSpaceFlags.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="ComponentSpaceFlags.ArrayMax &gt;= ComponentSpaceFlags.ArrayNum">Num={ComponentSpaceFlags.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
            <Variable Name="CompactPoseBoneCount" InitialValue="(ComponentSpaceFlags.ArrayNum &lt;= ComponentSpaceFlags.ArrayMax) ? ComponentSpaceFlags.ArrayNum : 0" />
            <Size>(ComponentSpaceFlags.ArrayNum &lt;= ComponentSpaceFlags.ArrayMax) ? ComponentSpaceFlags.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)Pose.BoneContainer->RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)Pose.BoneContainer->BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((unsigned char*)ComponentSpaceFlags.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Synthetic Name="BoneMask">
        <DisplayString Condition="BoneMask.ArrayNum == 0">Empty</DisplayString>
        <DisplayString Condition="BoneMask.ArrayNum &lt; 0">Invalid</DisplayString>
        <DisplayString Condition="BoneMask.ArrayMax &lt; BoneMask.ArrayNum">Invalid</DisplayString>
        <DisplayString Condition="BoneMask.ArrayMax &gt;= BoneMask.ArrayNum">Num={BoneMask.ArrayNum}</DisplayString>
        <Expand>
          <CustomListItems>
            <Variable Name="CompactPoseBoneIndex" InitialValue="0"/>
            <Variable Name="CompactPoseBoneCount" InitialValue="(BoneMask.ArrayNum &lt;= BoneMask.ArrayMax) ? BoneMask.ArrayNum : 0" />
            <Size>(BoneMask.ArrayNum &lt;= BoneMask.ArrayMax) ? BoneMask.ArrayNum : 0</Size>
            <Loop Condition="CompactPoseBoneIndex &lt; CompactPoseBoneCount">
              <Item Name="[{CompactPoseBoneIndex}] {((FMeshBoneInfo*)Pose.BoneContainer->RefSkeleton->FinalRefBoneInfo.AllocatorInstance.Data)[((unsigned short*)Pose.BoneContainer->BoneIndicesArray.AllocatorInstance.Data)[CompactPoseBoneIndex]].Name}">
                ((unsigned char*)BoneMask.AllocatorInstance.Data)[CompactPoseBoneIndex]
              </Item>
              <Exec>++CompactPoseBoneIndex</Exec>
            </Loop>
          </CustomListItems>
        </Expand>
      </Synthetic>
      <Item Name="BonesToConvert">BonesToConvert</Item>
    </Expand>
  </Type>

  <!-- FPoseContext -->
  <Type Name="FPoseContext">
    <Expand>
      <Item Name="AnimInstanceProxy">AnimInstanceProxy</Item>
      <Item Name="Pose">Pose</Item>
      <Synthetic Name="Curve" Condition="Curve.bInitialized">
        <DisplayString>{{Num={Curve.Elements.ArrayNum} bInitialized={Curve.bInitialized}}}</DisplayString>
        <Expand>
          <Synthetic Name="Elements">
            <DisplayString Condition="Curve.Elements.ArrayNum == 0">Empty</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayNum &lt; 0">Invalid</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayMax &lt; Curve.Elements.ArrayNum">Invalid</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayMax &gt;= Curve.Elements.ArrayNum">Num={Curve.Elements.ArrayNum}</DisplayString>
            <Expand>
              <CustomListItems>
                <Variable Name="UID" InitialValue="0"/>
                <Variable Name="UIDCount" InitialValue="(Curve.Elements.ArrayNum &lt;= Curve.Elements.ArrayMax) ? Curve.Elements.ArrayNum : 0" />
                <Size>(Curve.Elements.ArrayNum &lt;= Curve.Elements.ArrayMax) ? Curve.Elements.ArrayNum : 0</Size>
                <Loop Condition="UID &lt; UIDCount">
                  <Item Name="{((FName*)AnimInstanceProxy->Skeleton->AnimCurveMapping->CurveNameList.AllocatorInstance.Data)[UID]}">
                    ((FCurveElement*)Curve.Elements.AllocatorInstance.Data)[((unsigned short*)Curve.UIDToArrayIndexLUT->AllocatorInstance.Data)[UID]]
                  </Item>
                  <Exec>++UID</Exec>
                </Loop>
              </CustomListItems>
            </Expand>
          </Synthetic>
          <Synthetic Name="UIDToArrayIndexLUT" Condition="Curve.UIDToArrayIndexLUT != 0">
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayNum == 0">Empty</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayNum &lt; 0">Invalid</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayMax &lt; Curve.UIDToArrayIndexLUT->ArrayNum">Invalid</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayMax &gt;= Curve.UIDToArrayIndexLUT->ArrayNum">Num={Curve.UIDToArrayIndexLUT->ArrayNum}</DisplayString>
            <Expand>
              <CustomListItems>
                <Variable Name="UID" InitialValue="0"/>
                <Variable Name="UIDCount" InitialValue="(Curve.UIDToArrayIndexLUT->ArrayNum &lt;= Curve.UIDToArrayIndexLUT->ArrayMax) ? Curve.UIDToArrayIndexLUT->ArrayNum : 0" />
                <Size>(Curve.UIDToArrayIndexLUT->ArrayNum &lt;= Curve.UIDToArrayIndexLUT->ArrayMax) ? Curve.UIDToArrayIndexLUT->ArrayNum : 0</Size>
                <Loop Condition="UID &lt; UIDCount">
                  <Item Name="[{UID}] {((FName*)AnimInstanceProxy->Skeleton->AnimCurveMapping->CurveNameList.AllocatorInstance.Data)[UID]}">
                    ((unsigned short*)Curve.UIDToArrayIndexLUT->AllocatorInstance.Data)[UID]
                  </Item>
                  <Exec>++UID</Exec>
                </Loop>
              </CustomListItems>
            </Expand>
          </Synthetic>
          <Item Name="UIDToArrayIndexLUT" Condition="Curve.UIDToArrayIndexLUT == 0">Curve.UIDToArrayIndexLUT</Item>
          <Item Name="NumValidCurveCount">Curve.NumValidCurveCount</Item>
          <Item Name="bInitialized">Curve.bInitialized</Item>
        </Expand>
      </Synthetic>
      <Item Name="Curve" Condition="!Curve.bInitialized">Curve</Item>
    </Expand>
  </Type>

  <!-- FComponentSpacePoseContext -->
  <Type Name="FComponentSpacePoseContext">
    <Expand>
      <Item Name="AnimInstanceProxy">AnimInstanceProxy</Item>
      <Item Name="Pose">Pose</Item>
      <Synthetic Name="Curve" Condition="Curve.bInitialized">
        <DisplayString>{{Num={Curve.Elements.ArrayNum} bInitialized={Curve.bInitialized}}}</DisplayString>
        <Expand>
          <Synthetic Name="Elements">
            <DisplayString Condition="Curve.Elements.ArrayNum == 0">Empty</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayNum &lt; 0">Invalid</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayMax &lt; Curve.Elements.ArrayNum">Invalid</DisplayString>
            <DisplayString Condition="Curve.Elements.ArrayMax &gt;= Curve.Elements.ArrayNum">Num={Curve.Elements.ArrayNum}</DisplayString>
            <Expand>
              <CustomListItems>
                <Variable Name="UID" InitialValue="0"/>
                <Variable Name="UIDCount" InitialValue="(Curve.Elements.ArrayNum &lt;= Curve.Elements.ArrayMax) ? Curve.Elements.ArrayNum : 0" />
                <Size>(Curve.Elements.ArrayNum &lt;= Curve.Elements.ArrayMax) ? Curve.Elements.ArrayNum : 0</Size>
                <Loop Condition="UID &lt; UIDCount">
                  <Item Name="{((FName*)AnimInstanceProxy->Skeleton->AnimCurveMapping->CurveNameList.AllocatorInstance.Data)[UID]}">
                    ((FCurveElement*)Curve.Elements.AllocatorInstance.Data)[((unsigned short*)Curve.UIDToArrayIndexLUT->AllocatorInstance.Data)[UID]]
                  </Item>
                  <Exec>++UID</Exec>
                </Loop>
              </CustomListItems>
            </Expand>
          </Synthetic>
          <Synthetic Name="UIDToArrayIndexLUT" Condition="Curve.UIDToArrayIndexLUT != 0">
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayNum == 0">Empty</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayNum &lt; 0">Invalid</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayMax &lt; Curve.UIDToArrayIndexLUT->ArrayNum">Invalid</DisplayString>
            <DisplayString Condition="Curve.UIDToArrayIndexLUT->ArrayMax &gt;= Curve.UIDToArrayIndexLUT->ArrayNum">Num={Curve.UIDToArrayIndexLUT->ArrayNum}</DisplayString>
            <Expand>
              <CustomListItems>
                <Variable Name="UID" InitialValue="0"/>
                <Variable Name="UIDCount" InitialValue="(Curve.UIDToArrayIndexLUT->ArrayNum &lt;= Curve.UIDToArrayIndexLUT->ArrayMax) ? Curve.UIDToArrayIndexLUT->ArrayNum : 0" />
                <Size>(Curve.UIDToArrayIndexLUT->ArrayNum &lt;= Curve.UIDToArrayIndexLUT->ArrayMax) ? Curve.UIDToArrayIndexLUT->ArrayNum : 0</Size>
                <Loop Condition="UID &lt; UIDCount">
                  <Item Name="[{UID}] {((FName*)AnimInstanceProxy->Skeleton->AnimCurveMapping->CurveNameList.AllocatorInstance.Data)[UID]}">
                    ((unsigned short*)Curve.UIDToArrayIndexLUT->AllocatorInstance.Data)[UID]
                  </Item>
                  <Exec>++UID</Exec>
                </Loop>
              </CustomListItems>
            </Expand>
          </Synthetic>
          <Item Name="UIDToArrayIndexLUT" Condition="Curve.UIDToArrayIndexLUT == 0">Curve.UIDToArrayIndexLUT</Item>
          <Item Name="NumValidCurveCount">Curve.NumValidCurveCount</Item>
          <Item Name="bInitialized">Curve.bInitialized</Item>
        </Expand>
      </Synthetic>
      <Item Name="Curve" Condition="!Curve.bInitialized">Curve</Item>
    </Expand>
  </Type>

  <!-- 
  *
  * Chaos Visualizers 
  *
  -->

  <Type Name="Chaos::Private::FScratchBuffer">
    <DisplayString Condition="(BufferEnd != nullptr) &amp;&amp; (*(size_t*)BufferEnd != Chaos::Private::FScratchBuffer::SentinelValue)">[[--SENTINEL ERROR--]] Used: {BufferNext - BufferBegin} / {BufferEnd - BufferBegin} (Free: {BufferEnd - BufferNext})</DisplayString>
    <DisplayString>Used: {BufferNext - BufferBegin} / {BufferEnd - BufferBegin} (Free: {BufferEnd - BufferNext})</DisplayString>
    <Expand>
      <Item Name="BufferNext">BufferNext,8</Item>
      <Item Name="BufferBegin">BufferBegin,8</Item>
      <Item Name="BufferEnd">BufferEnd,8</Item>
      <Item Name="Sentinel" Condition="BufferEnd != nullptr">*(size_t*)BufferEnd,x</Item>
    </Expand>
  </Type>

  <!-- 2D TVector vizualizer -->
  <Type Name="Chaos::TVector&lt;int,2&gt;">
    <DisplayString>{{{X}, {Y}}}</DisplayString>
  </Type>
  <Type Name="Chaos::TVector&lt;Chaos::FReal,2&gt;">
    <DisplayString>{{{X}, {Y}}}</DisplayString>
  </Type>

  <!-- 3D TVector vizualizer -->
  <Type Name="Chaos::TVector&lt;*,3&gt;">
    <DisplayString>{{{X}, {Y}, {Z}}}</DisplayString>
  </Type>

  <!-- 3D TRotation vizualizer -->
  <Type Name="Chaos::TRotation&lt;*,3&gt;">
    <DisplayString>{{{X}, {Y}, {Z}, {W}}}</DisplayString>
  </Type>

  <!-- 3D TRigidTransform vizualizer -->
  <Type Name="Chaos::TRigidTransform&lt;*,3&gt;">
    <Expand>
      <Item Name="Translation">(Chaos::TVector&lt;$T1,3&gt;&amp;)Translation</Item>
      <Item Name="Rotation">(Chaos::TRotation&lt;$T1,3&gt;&amp;)Rotation</Item>
      <Item Name="Scale3D">(Chaos::TVector&lt;$T1,3&gt;&amp;)Scale3D</Item>
    </Expand>
  </Type>

  <Type Name="Chaos::TAABB&lt;*,*&gt;">
    <DisplayString>{MMin}, {MMax}</DisplayString>
    <Expand>
      <Item Name="MMin">MMin</Item>
      <Item Name="MMax">MMax</Item>
      <Synthetic Name="Size">
        <DisplayString>({MMax.X - MMin.X}, {MMax.Y - MMin.Y}, {MMax.Z - MMin.Z})</DisplayString>
        <Expand>
          <Item Name="X">MMax.X - MMin.X</Item>
          <Item Name="Y">MMax.Y - MMin.Y</Item>
          <Item Name="Z">MMax.Z - MMin.Z</Item>
        </Expand>
      </Synthetic>
    </Expand>
  </Type>

  <!-- 3x3 PMatrix visualizer. Just show diagonal elements in preview line. -->
  <Type Name="Chaos::PMatrix&lt;*,3,3&gt;">
    <DisplayString>{{{M[0][0]}, ..., {M[1][1]}, ..., {M[2][2]}}}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::TSimdSelector&lt;4&gt;">
    <DisplayString>{((uint32*)V)[0],x}, {((uint32*)V)[1],x}, {((uint32*)V)[2],x}, {((uint32*)V)[3],x}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::TSimdInt32&lt;*&gt;">
    <DisplayString>({V[0]}, {V[1]}, {V[2]}, {V[3]})</DisplayString>
  </Type>

  <Type Name="Chaos::Private::TSimdRealf&lt;4&gt;">
    <DisplayString>({V[0]}, {V[1]}, {V[2]}, {V[3]})</DisplayString>
  </Type>

  <Type Name="Chaos::Private::TSimdVec3f&lt;4&gt;">
    <DisplayString>X=({VX[0]}, {VX[1]}, {VX[2]}, {VX[3]}) Y=({VX[0]}, {VY[1]}, {VY[2]}, {VY[3]}) Z=({VZ[0]}, {VZ[1]}, {VZ[2]}, {VZ[3]})</DisplayString>
  </Type>

  <Type Name="FChaosUserData">
    <DisplayString>{Type,en}</DisplayString>
    <Expand>
      <Item Name="Payload" Condition="Type == 0">Payload</Item>
      <Item Name="Payload" Condition="Type == 1">(FBodyInstance*)Payload</Item>
      <Item Name="Payload" Condition="Type == 2">(UPhysicalMaterial*)Payload</Item>
      <Item Name="Payload" Condition="Type == 3">(FPhysScene_Chaos*)Payload</Item>
      <Item Name="Payload" Condition="Type == 4">(FConstraintInstance*)Payload</Item>
      <Item Name="Payload" Condition="Type == 5">(UPrimitiveComponent*)Payload</Item>
      <Item Name="Payload" Condition="Type == 6">(FKShapeElem*)Payload</Item>
      <Item Name="Payload" Condition="Type == 7">(FCustomChaosPayload*)Payload</Item>
    </Expand>
  </Type>

  <Type Name="Chaos::TGeometryParticle&lt;*,*&gt;">
    <!-- Optional because DebugName is compiled out in shipping builds -->
    <DisplayString Optional="true" Condition="((Chaos::TPBDRigidParticle&lt;$T1,$T2&gt;*)this)->MMiscData.Property.bDisabled == true">[Disabled] {Type,en} {*MNonFrequentData.Property.MDebugName} {*MNonFrequentData.Property.MGeometry.Reference}</DisplayString>
    <DisplayString Optional="true">{Type,en} {*MNonFrequentData.Property.MDebugName} {*MNonFrequentData.Property.MGeometry.Reference}</DisplayString>
    <!-- Basic info for shipping builds -->
    <DisplayString>{Type,en} {*MNonFrequentData.Property.MGeometry.Reference}</DisplayString>
    <Expand>
      <Item Name="Proxy">Proxy</Item>
      <Item Name="MXR">MXR</Item>
      <Item Name="MVelocities" Condition="Type &gt;= 1">((Chaos::TKinematicGeometryParticle&lt;$T1,$T2&gt;*)this)->MVelocities</Item>
      <Item Name="MKinematicTarget" Condition="Type &gt;= 1">((Chaos::TKinematicGeometryParticle&lt;$T1,$T2&gt;*)this)->MKinematicTarget</Item>
      <Item Name="MMassProps" Condition="Type &gt;= 2">((Chaos::TPBDRigidParticle&lt;$T1,$T2&gt;*)this)->MMassProps</Item>
      <Item Name="MDynamics" Condition="Type &gt;= 2">((Chaos::TPBDRigidParticle&lt;$T1,$T2&gt;*)this)->MDynamics</Item>
      <Item Name="MMiscData" Condition="Type &gt;= 2">((Chaos::TPBDRigidParticle&lt;$T1,$T2&gt;*)this)->MMiscData</Item>
      <Item Name="MNonFrequentData">MNonFrequentData</Item>
      <Item Name="MShapesArray">MShapesArray</Item>
      <Item Name="Type">Type</Item>
      <Item Name="MDirtyFlags">MDirtyFlags</Item>
      <!-- 
        For some reason FPhysicsUserData_Chaos and/or FPhysicsUserData are often unknown to natvis, preventing TGeometryParticle from showing in the debugger. 
        This is my compromise which always shows the void* version and sometimes the typed version if you're lucky
      -->
      <Item Name="MUserData" Optional="true">(FPhysicsUserData_Chaos*)MUserData</Item>
      <Item Name="MUserData" Optional="true">(FPhysicsUserData*)MUserData</Item>
      <Item Name="MUserData[void*]">MUserData</Item>
    </Expand>
  </Type>

  <!-- Chaos Particle Handle Vizualizer. Displays the elements from the SoA for the particle represented by the handle. -->
  <Type Name="Chaos::TGeometryParticleHandleImp&lt;*,*,*&gt;">
    <!-- non-transient handle. Shows ParticleType, ObjectsState, ID and DebugName (in debug and dev) -->
    <DisplayString Condition="$T3 == 1 &amp;&amp; Type &lt; 2">&lt;{HandleIdx}&gt; {Type,en} {*(((TRefCountPtr&lt;Chaos::FImplicitObject&gt;*)GeometryParticles->MGeometry.AllocatorInstance.Data)[ParticleIdx]).Reference}</DisplayString>
    <!-- natvis uses the first DisplayString that matches, so one is optional and validity depends on the existance of MDebugName -->
    <!-- <DisplayString Condition="$T3 == 1 &amp;&amp; Type &gt;= 2" Optional="true">{Type,en} {((enum Chaos::EObjectStateType*)PBDRigidParticles->MObjectState.AllocatorInstance.Data)[ParticleIdx],en} &lt;{HandleIdx}&gt; {*((TSharedPtr&lt;FString,1&gt;*)PBDRigidParticles->MDebugName.AllocatorInstance.Data)[ParticleIdx]}</DisplayString> -->
    <DisplayString Condition="$T3 == 1 &amp;&amp; Type &gt;= 2 &amp;&amp; ((Chaos::FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].bDisabled">[Disabled] &lt;{HandleIdx}&gt; {Type,en} {((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].ObjectState,en} {*(((TRefCountPtr&lt;Chaos::FImplicitObject&gt;*)GeometryParticles->MGeometry.AllocatorInstance.Data)[ParticleIdx]).Reference}</DisplayString>
    <DisplayString Condition="$T3 == 1 &amp;&amp; Type &gt;= 2">&lt;{HandleIdx}&gt; {Type,en} {((Chaos::FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].ObjectState,en} {*(((TRefCountPtr&lt;Chaos::FImplicitObject&gt;*)GeometryParticles->MGeometry.AllocatorInstance.Data)[ParticleIdx]).Reference}</DisplayString>
    <!-- transient handle. Shows ParticleType and ID -->
    <DisplayString Condition="$T3 == 0 &amp;&amp; Type &lt; 2">{Type,en} &lt;T{((Chaos::TGeometryParticleHandleImp&lt;$T1,$T2,1&gt;**)GeometryParticles->MGeometryParticleHandle.AllocatorInstance.Data)[ParticleIdx]->HandleIdx}&gt;</DisplayString>
    <DisplayString Condition="$T3 == 0 &amp;&amp; Type &gt;= 2">{Type,en} &lt;T{((Chaos::TGeometryParticleHandleImp&lt;$T1,$T2,1&gt;**)GeometryParticles->MGeometryParticleHandle.AllocatorInstance.Data)[ParticleIdx]->HandleIdx}&gt;</DisplayString>

    <Expand>
      <Item Condition="Type &gt;= 0" Name="DebugName" Optional="true">((TSharedPtr&lt;FString,1&gt;*)GeometryParticles->MDebugName.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="Type">Type</Item>
      <Item Condition="Type &gt;= 0" Name="ParticleIdx">ParticleIdx</Item>
      <Item Condition="Type &gt;= 0" Name="ListMask">((Chaos::EGeometryParticleListMask*)GeometryParticles->MParticleListMask.AllocatorInstance.Data)[ParticleIdx],en</Item>
      <Item Condition="Type &gt;= 0" Name="X">((Chaos::TVector&lt;$T1,$T2&gt;*)GeometryParticles->MX.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="R">((Chaos::TRotation&lt;float,$T2&gt;*)GeometryParticles->MR.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="P">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MP.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="Q">((Chaos::TRotation&lt;float,$T2&gt;*)PBDRigidParticles->MQ.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 1" Name="V">((Chaos::TVector&lt;float,$T2&gt;*)KinematicGeometryParticles->MV.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 1" Name="W">((Chaos::TVector&lt;float,$T2&gt;*)KinematicGeometryParticles->MW.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 0 &amp;&amp; $T3==1" Name="HandleIdx">HandleIdx</Item>
      <Item Condition="Type &gt;= 0" Name="UniqueIdx">((int32*)GeometryParticles->MUniqueIdx.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="ParticleID">((FParticleID*)GeometryParticles->MParticleIDs.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="SpatialIdx">((FSpatialAccelerationIdx*)GeometryParticles->MSpatialIdx.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="Geometry">((TRefCountPtr&lt;Chaos::FImplicitObject&gt;*)GeometryParticles->MGeometry.AllocatorInstance.Data)[ParticleIdx].Reference</Item>
      <Item Condition="Type &gt;= 0" Name="GeometryParticle">((Chaos::TGeometryParticle&lt;$T1,$T2&gt;**)GeometryParticles->MGeometryParticle.AllocatorInstance.Data)[ParticleIdx]</Item>
      <!-- NOTE: We could use Chaos::FShapesArray here instead of this monstrosity, but that doesn't seem to work in modules outside of Chaos (FShapesArray is defined in a using statement) -->
      <Item Condition="Type &gt;= 0" Name="ShapesArray">((TArray&lt;TUniquePtr&lt;Chaos::FPerShapeData,TDefaultDelete&lt;Chaos::FPerShapeData&gt; &gt;,TSizedInlineAllocator&lt;1,32,TSizedDefaultAllocator&lt;32&gt; &gt; &gt;*)GeometryParticles->MShapesArray.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="LocalBounds">((Chaos::TAABB&lt;double,3&gt;*)GeometryParticles->MLocalBounds.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="WorldSpaceInflatedBounds">((Chaos::TAABB&lt;$T1,$T2&gt;*)GeometryParticles->MWorldSpaceInflatedBounds.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 0" Name="GraphNode">((Chaos::Private::FPBDIslandParticle**)GeometryParticles->MGraphNode.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="ParticleConstraints">((Chaos::FConstraintHandleArray*)GeometryParticles->MParticleConstraints.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="ParticleCollisions">((Chaos::FParticleCollisions*)GeometryParticles->MParticleCollisions.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="CCDAxisThreshold">((Chaos::FVec3*)GeometryParticles->MCCDAxisThreshold.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 0" Name="SyncState">((Chaos::ESyncState*)GeometryParticles->MSyncState.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 1" Name="KinematicTarget">((Chaos::FKinematicTarget*)KinematicGeometryParticles->KinematicTargets.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 2" Name="PreV">((Chaos::TVector&lt;float,$T2&gt;*)PBDRigidParticles->MPreV.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="PreW">((Chaos::TVector&lt;float,$T2&gt;*)PBDRigidParticles->MPreW.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="VSmooth">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MVSmooth.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="WSmooth">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MWSmooth.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="Acceleration">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MAcceleration.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="AngularAcceleration">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MAngularAcceleration.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="LinearImpulseVelocity">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MLinearImpulseVelocity.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="AngularImpulseVelocity">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MAngularImpulseVelocity.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="MaxLinearSpeedSq">((Chaos::FReal*)PBDRigidParticles->MaxLinearSpeedsSq.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="MaxAngularSpeedSq">((Chaos::FReal*)PBDRigidParticles->MaxAngularSpeedsSq.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="InitialOverlapDepenetrationVelocity">((Chaos::FRealSingle*)PBDRigidParticles->MInitialOverlapDepenetrationVelocity.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="M">((Chaos::FReal*)PBDRigidParticles->MM.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="InvM">((Chaos::FReal*)PBDRigidParticles->MInvM.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="I">((Chaos::TVector&lt;float,3&gt;*)PBDRigidParticles->MI.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="InvI">((Chaos::TVector&lt;float,3&gt;*)PBDRigidParticles->MInvI.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="InvIConditioning">((Chaos::TVector&lt;float,3&gt;*)PBDRigidParticles->MInvIConditioning.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="CenterOfMass">((Chaos::TVector&lt;$T1,$T2&gt;*)PBDRigidParticles->MCenterOfMass.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="RotationOfMass">((Chaos::TRotation&lt;$T1,$T2&gt;*)PBDRigidParticles->MRotationOfMass.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="CollisionParticles">((TUniquePtr&lt;Chaos::FBVHParticles,TDefaultDelete&lt;Chaos::FBVHParticles&gt; &gt;*)PBDRigidParticles->MCollisionParticles.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="CollisionGroup">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].CollisionGroup</Item>
      <Item Condition="Type &gt;= 2" Name="CollisionConstraintFlags">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].CollisionConstraintFlags</Item>
      <Item Condition="Type &gt;= 2" Name="Disabled">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].bDisabled</Item>
      <Item Condition="Type &gt;= 2" Name="ObjectState">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].ObjectState</Item>
      <Item Condition="Type &gt;= 2" Name="PreObjectState">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].PreObjectState</Item>
      <Item Condition="Type &gt;= 2" Name="SleepType">((Chaos::ESleepType*)PBDRigidParticles->MSleepType.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="SleepCounter">((int8*)PBDRigidParticles->MSleepCounter.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="DisableCounter">((int8*)PBDRigidParticles->MDisableCounter.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="SolverBodyIndex">((int32*)PBDRigidParticles->MSolverBodyIndex.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 2" Name="ControlFlags">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].ControlFlags</Item>
      <Item Condition="Type &gt;= 2" Name="TransientFlags">((FRigidParticleCoreData*)PBDRigidParticles->CoreData.AllocatorInstance.Data)[ParticleIdx].TransientFlags</Item>

      <Item Condition="Type &gt;= 3" Name="ClusterId">((Chaos::ClusterId*)PBDRigidClusteredParticles->MClusterIds.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="ChildToParent">((Chaos::FRigidTransform3*)PBDRigidClusteredParticles->MChildToParent.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="ClusterGroupIndex">((int32*)PBDRigidClusteredParticles->MClusterGroupIndex.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="RigidClusteredFlags">((FRigidClusteredFlags*)PBDRigidClusteredParticles->MRigidClusteredFlags.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="ChildrenSpatial">((TUniquePtr&lt;Chaos::FImplicitObjectUnionClustered,TDefaultDelete&lt;Chaos::FImplicitObjectUnionClustered&gt; &gt;*)PBDRigidClusteredParticles->MChildrenSpatial.AllocatorInstance.Data)[ParticleIdx]</Item>
      <!-- Marked as optional because Error: identifier "TSet<IPhysicsProxyBase*,DefaultKeyFuncs<IPhysicsProxyBase*,0>,FDefaultSetAllocator>" is undefined -->
      <Item Condition="Type &gt;= 3" Optional="true" Name="PhysicsProxies">((TSet&lt;IPhysicsProxyBase*,DefaultKeyFuncs&lt;IPhysicsProxyBase*,0&gt;,FDefaultSetAllocator&gt;*)PBDRigidClusteredParticles->MPhysicsProxies.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 3" Name="CollisionImpulses">((Chaos::FRealSingle*)PBDRigidClusteredParticles->MCollisionImpulses.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="ExternalStrains">((Chaos::FRealSingle*)PBDRigidClusteredParticles->MExternalStrains.AllocatorInstance.Data)[ParticleIdx]</Item>
      <Item Condition="Type &gt;= 3" Name="Strain">((Chaos::FRealSingle*)PBDRigidClusteredParticles->MStrains.AllocatorInstance.Data)[ParticleIdx]</Item>
      <!-- Marked as optional because in some modules FConnectivityEdgeArray is not understood by natvis which prevents the whole struct from working -->
      <Item Condition="Type &gt;= 3" Optional="true" Name="ConnectivityEdges">((Chaos::FConnectivityEdgeArray*)PBDRigidClusteredParticles->MConnectivityEdges.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Condition="Type &gt;= 3" Name="ConvexOptimizer">((TPimplPtr&lt;Chaos::Private::FConvexOptimizer,0&gt;*)PBDRigidClusteredParticles->MConvexOptimizers.AllocatorInstance.Data)[ParticleIdx]</Item>

      <Item Name="PhysicsProxy">((IPhysicsProxyBase**)GeometryParticles->MPhysicsProxy.AllocatorInstance.Data)[ParticleIdx]</Item>
    </Expand>
  </Type>

  <!-- Chaos Generic Particle Handle Vizualizer. Displays as a particle handle. -->
  <Type Name="Chaos::FGenericParticleHandleImp">
    <DisplayString Condition="MHandle == nullptr">Invalid</DisplayString>
    <DisplayString >{*MHandle}</DisplayString>
    <Expand>
      <ExpandedItem>MHandle</ExpandedItem>
    </Expand>
  </Type>
  
  <Type Name="Chaos::FGenericParticleHandle">
    <DisplayString>{Imp}</DisplayString>
    <Expand>
      <ExpandedItem>Imp</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="Chaos::FConstGenericParticleHandle">
    <DisplayString>{Imp}</DisplayString>
    <Expand>
      <ExpandedItem>Imp</ExpandedItem>
    </Expand>
  </Type>

  <!-- Chaos ClusterId Vizualizer. -->
  <Type Name="Chaos::ClusterId">
    <Expand>
      <Item Name="Id">(Chaos::FPBDRigidParticleHandle*)Id</Item>
      <Item Name="NumChildren">NumChildren</Item>
    </Expand>
  </Type>

  <!-- Chaos Kinematic Target Vizualizer. -->
  <Type Name="Chaos::FKinematicTarget">
    <DisplayString>Mode: {Mode,en}</DisplayString>
    <Expand>
      <Item Condition="Mode &gt; 0" Name="Position">Position</Item>
      <Item Condition="Mode &gt; 0" Name="Rotation">Rotation</Item>
    </Expand>
  </Type>

  <!-- Chaos Solver Body Visualizer -->
  <Type Name="Chaos::FSolverBody">
    <DisplayString Condition="State.InvM != 0">[Dynamic]</DisplayString>
    <DisplayString Condition="State.InvM == 0">[Kinematic]</DisplayString>
    <Expand>
      <ExpandedItem>State</ExpandedItem>
    </Expand>
  </Type>

  <!-- Chaos Solver Body Visualizer -->
  <Type Name="Chaos::FConstraintSolverBody">
    <DisplayString Condition="Body == nullptr">[None]</DisplayString>
    <DisplayString Condition="Body != nullptr">{*Body}</DisplayString>
  </Type>

  <!-- Chaos Solver Visualizer -->
  <Type Name="Chaos::Private::FPBDCollisionSolver">
    <DisplayString>{State.NumManifoldPoints} {State.SolverBodies[0]} -:- {State.SolverBodies[1]}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDCollisionSolverManifoldPointsSimd&lt;*&gt;">
    <DisplayString>ConstraintIndex={SimdConstraintIndex} ManifoldPointIndex={SimdManifoldPointIndex}</DisplayString>
  </Type>


  <!-- Chaos Dense matrix visualizer. -->
  <Type Name="Chaos::TDenseMatrix&lt;*&gt;">
    <Expand>
      <ArrayItems>
        <Rank>2</Rank>
        <Size>$i == 0 ? NRows : NCols</Size>
        <ValuePointer>M</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- 
  Particles SoA vizualizer. This works by displaying the persistent handle for the particle which uses the above vizualizer.
  We are lucky we can do this - I don't think there's an easy way to vizualize SoAs in object order in natvis otherwise.
  -->
  <Type Name="Chaos::TGeometryParticlesImp&lt;*,*,*&gt;">
    <DisplayString Condition="MSize &lt;= 0">Empty</DisplayString>
    <DisplayString Condition="MSize &gt; 0">Size={MSize}</DisplayString>
    <Expand>
      <IndexListItems>
        <Size>(MSize &gt; 0) ? MSize : 0</Size>
        <ValueNode>
          ((Chaos::TGeometryParticleHandleImp&lt;$T1,$T2,1&gt;**)MGeometryParticleHandle.AllocatorInstance.Data)[$i]
        </ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- Chaos Character Ground Constraint Handle Vizualizer. -->
  <Type Name="Chaos::FCharacterGroundConstraintHandle">
    <DisplayString>Character [{*CharacterParticle} -:- {*GroundParticle}]</DisplayString>
  </Type>

  <!-- Chaos Joint Handle Vizualizer. -->
  <Type Name="Chaos::FPBDJointConstraintHandle">
    <DisplayString>Joint &lt;{ConstraintIndex}&gt; [{*(((Chaos::FParticlePair*)((FPBDJointConstraints*)ConstraintContainer)->ConstraintParticles.AllocatorInstance.Data)[ConstraintIndex]).V[0]} -:- {*(((Chaos::FParticlePair*)((FPBDJointConstraints*)ConstraintContainer)->ConstraintParticles.AllocatorInstance.Data)[ConstraintIndex]).V[1]}]</DisplayString>
    <Expand>
      <Item Name="ConstraintIndex">ConstraintIndex</Item>
      <Item Name="GraphEdge">GraphEdge</Item>
      <Item Name="ConstraintParticles">((Chaos::FParticlePair*)((FPBDJointConstraints*)ConstraintContainer)->ConstraintParticles.AllocatorInstance.Data)[ConstraintIndex]</Item>
      <Item Name="ConstraintSettings">((Chaos::FPBDJointSettings*)((FPBDJointConstraints*)ConstraintContainer)->ConstraintSettings.AllocatorInstance.Data)[ConstraintIndex]</Item>
      <Item Name="ConstraintState">((Chaos::FPBDJointState*)((FPBDJointConstraints*)ConstraintContainer)->ConstraintStates.AllocatorInstance.Data)[ConstraintIndex]</Item>
    </Expand>
  </Type>
  
  <!-- Chaos Suspension Handle Vizualizer -->
  <Type Name="Chaos::FPBDSuspensionConstraintHandle">
    <DisplayString>Suspension &lt;{ConstraintIndex}&gt; {*((Chaos::FGeometryParticleHandle**)((Chaos::FPBDSuspensionConstraints*)ConstraintContainer)->ConstrainedParticles.AllocatorInstance.Data)[ConstraintIndex]}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FCollisionParticlePairKey">
    <DisplayString>&lt;{Key.Key32s[0].Key31}&gt; -:- &lt;{Key.Key32s[1].Key31}&gt;</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FCollisionShapePairKey">
    <DisplayString>&lt;{ShapeID0}&gt; -:- &lt;{ShapeID1}&gt;</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FCollisionSortKeyNonHashed">
    <DisplayString>&lt;{ParticlePairKey.Key.Key32s[0].Key31}-{ShapePairKey.ShapeID0}&gt; -:- &lt;{ParticlePairKey.Key.Key32s[1].Key31}-{ShapePairKey.ShapeID1}&gt;</DisplayString>
  </Type>

  <!-- Chaos Collision Vizualizer. -->
  <Type Name="Chaos::FPBDCollisionConstraint">
    <DisplayString Condition="ContainerCookie.CCDConstraintIndex &gt;= 0">Swept Collision &lt;{ContainerCookie.CCDConstraintIndex}&gt; | {CollisionSortKey} [{*Particle[0]} -:- {*Particle[1]} NumManifoldPoints={ManifoldPoints.ArrayNum}]</DisplayString>
    <DisplayString Condition="ContainerCookie.ConstraintIndex &gt;= 0">Collision &lt;{ContainerCookie.ConstraintIndex}&gt; | {CollisionSortKey} [{*Particle[0]} -:- {*Particle[1]} NumManifoldPoints={ManifoldPoints.ArrayNum}]</DisplayString>
    <DisplayString>Collision &lt;Inactive&gt; | {CollisionSortKey} [{*Particle[0]} -:- {*Particle[1]}]</DisplayString>
  </Type>

  <!-- Chaos Contact Point Visualizer -->
  <Type Name="Chaos::TContactPoint&lt;*&gt;">
    <DisplayString Condition="FaceIndex &lt; 0">{ContactType,en} @ {Phi} {ShapeContactNormal}</DisplayString>
    <DisplayString Condition="FaceIndex &gt;= 0">{ContactType,en} @ {Phi} &lt;{FaceIndex}&gt; {ShapeContactNormal}</DisplayString>
  </Type>

  <Type Name="Chaos::FContactEdgeOrVertexID">
    <DisplayString>{EdgeID.VertexIDs[0]}, {EdgeID.VertexIDs[1]}</DisplayString>
  </Type>


  <Type Name="Chaos::FTriangleContactPointData">
    <DisplayString Condition="bIsEnabled == 0">Tri: {ContactTriangleIndex} Verts: {EdgeOrVertexID} NormalDot: {ContactNormalDotTriangleNormal} [DISABLED]</DisplayString>
    <DisplayString>Tri: {ContactTriangleIndex} Verts: {EdgeOrVertexID} NormalDot: {ContactNormalDotTriangleNormal}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FMeshContactGeneratorTriangle">
    <DisplayString Condition="bEnabled">&lt;{TriangleIndex}&gt; &lt;{VertexIndices[0]},{VertexIndices[1]},{VertexIndices[2]}&gt; {Normal}</DisplayString>
    <DisplayString Condition="!bEnabled">[DISABLED] &lt;{TriangleIndex}&gt; {Normal}</DisplayString>
  </Type>
  
  <Type Name="Chaos::Private::FBroadPhaseOverlap">
    <DisplayString>{*Particles[0]} -:- {*Particles[1]}</DisplayString>
  </Type>

  <!-- Chaos Null Constraint Vizualizer. -->
  <Type Name="Chaos::FPBDNullConstraintHandle">
    <DisplayString>NullConstraint [&lt;{ConstraintIndex}&gt; {(((Chaos::FPBDNullConstraint*)((FPBDNullConstraints*)ConstraintContainer)->Constraints.AllocatorInstance.Data)[ConstraintIndex])}]</DisplayString>
  </Type>
  
  <!-- Chaos Null Constraint Handle Vizualizer. -->
  <Type Name="Chaos::FPBDNullConstraint">
    <DisplayString>{*ConstrainedParticles.V[0]} -:- {*ConstrainedParticles.V[1]}; Enabled: {bEnabled}; Sleeping: {bSleeping}</DisplayString>
  </Type>

  <Type Name="Chaos::FConstraintHandleHolder">
    <DisplayString>{*Handle}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDIslandParticle">
    <DisplayString>{*Particle} NumEdges={Edges.ArrayNum}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDIslandConstraint">
    <DisplayString>{*Constraint} (Level {Level})</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDIsland">
    <DisplayString>Island&lt;{ArrayIndex}&gt; NumEdges={NumEdges} Sleeping={Flags.bIsSleeping}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDIslandConstraintGroupSolver">
    <DisplayString>NumParticles={NumParticles} NumConstraints={NumConstraints} NumIslands={Islands.ArrayNum}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDIslandGroupManager">
    <DisplayString>NumWorkerThreads={NumWorkerThreads} NumActiveGroups={NumActiveGroups}</DisplayString>
  </Type>

  <Type Name="Chaos::FPBDCollisionContainerSolver">
    <DisplayString>CollisionSolver Num={NumCollisionSolverManifoldPoints}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FPBDJointContainerSolver">
    <DisplayString Condition="NonLinearConstraintSolvers.ArrayNum != 0">NonLinearJointSolver Num={NonLinearConstraintSolvers.ArrayNum}</DisplayString>
    <DisplayString>LinearJointSolver Num={LinearConstraintSolvers.ArrayNum}</DisplayString>
  </Type>

  <Type Name="Chaos::TIndexedConstraintContainerSolver&lt;Chaos::FPBDSuspensionConstraints&gt;">
    <DisplayString>SuspensionSolver Num={ConstraintIndices.ArrayNum}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FCharacterGroundConstraintContainerSolver">
    <DisplayString>CharacterGroundSolver Num={Solvers.ArrayNum}</DisplayString>
  </Type>

  <!-- Chaos Convex StructureData Vizualizer -->
  <Type Name="Chaos::FConvexStructureData">
    <Expand>
      <Item Name="IndexType">IndexType</Item>
      <Item Condition="IndexType == 1" Name="Data">Data.DataS</Item>
      <Item Condition="IndexType == 2" Name="Data">Data.DataM</Item>
      <Item Condition="IndexType == 3" Name="Data">Data.DataL</Item>
    </Expand>
  </Type>

  <!-- Chaos Implicit Visualizer -->
  <!-- NOTE: the enum values don't resolve in modules outside of Chaos. Chaos::ImplicitObjectType is an anonymous enum in a namespace... -->
  <Type Name="Chaos::FImplicitObject">
    <!-- Clustered Union is first because it sets CollisionType to Union. For all other types we want to know the CollisionType, but here it would hide some info -->
    <DisplayString Condition="Type == 15">[Clustered Union]</DisplayString>

    <DisplayString Condition="CollisionType == 0">[Sphere]</DisplayString>
    <DisplayString Condition="CollisionType == 1">[Box]</DisplayString>
    <DisplayString Condition="CollisionType == 2">[Plane]</DisplayString>
    <DisplayString Condition="CollisionType == 3">[Capsule]</DisplayString>
    <DisplayString Condition="CollisionType == 4">[Transformed]</DisplayString>
    <DisplayString Condition="CollisionType == 5">[Union]</DisplayString>
    <DisplayString Condition="CollisionType == 6">[LevelSet]</DisplayString>
    <DisplayString Condition="CollisionType == 7">[Unknown]</DisplayString>
    <DisplayString Condition="CollisionType == 8">[Convex]</DisplayString>
    <DisplayString Condition="CollisionType == 9">[TaperedCylinder]</DisplayString>
    <DisplayString Condition="CollisionType == 10">[Cylinder]</DisplayString>
    <DisplayString Condition="CollisionType == 11">[TriangleMesh]</DisplayString>
    <DisplayString Condition="CollisionType == 12">[HeightField]</DisplayString>
    <DisplayString Condition="CollisionType == 13">[DEPRECATED SCALED]</DisplayString>
    <DisplayString Condition="CollisionType == 14">[Triangle]</DisplayString>
    <DisplayString Condition="CollisionType == 15">[Clustered Union]</DisplayString>
    <DisplayString Condition="CollisionType == (128 | 8)">[Scaled Convex]</DisplayString>
    <DisplayString Condition="CollisionType == (128 | 11)">[Scaled TriangleMesh]</DisplayString>
    <DisplayString Condition="CollisionType == (128 | 12)">[Scaled HeightField]</DisplayString>
    <DisplayString Condition="CollisionType == (64 | 8)">[Instanced Convex]</DisplayString>
    <DisplayString Condition="CollisionType == (64 | 11)">[Instanced TriangleMesh]</DisplayString>
    <DisplayString Condition="CollisionType == (64 | 12)">[Instanced HeightField]</DisplayString>
  </Type>

  <Type Name="TRefCountPtr&lt;Chaos::FImplicitObject&gt;">
    <DisplayString>{Reference}</DisplayString>
  </Type>

  <Type Name="Chaos::FTriangleMeshImplicitObject">
    <DisplayString>[TriangleMesh NumVerts={MParticles.MSize}]</DisplayString>
  </Type>

  <Type Name="Chaos::FHeightField">
    <DisplayString>[HeightField Grid={GeomData.NumCols + 1} x {GeomData.NumRows + 1}]</DisplayString>
  </Type>

  <Type Name="Chaos::FConvex">
    <DisplayString>[Convex NumPlanes={Planes.ArrayNum}]</DisplayString>
  </Type>

  <Type Name="Chaos::TImplicitObjectInstanced&lt;*&gt;">
    <DisplayString>[Instanced {*MObject.Reference}]</DisplayString>
  </Type>

  <Type Name="Chaos::TImplicitObjectScaled&lt;*,*&gt;">
    <DisplayString>[Scaled {*MObject.Reference}]</DisplayString>
  </Type>

  <Type Name="Chaos::TImplicitObjectTransformed&lt;*,*,*&gt;">
    <DisplayString>[Transformed {*(FVector*)&amp;MTransform.Translation} {*MObject.Reference}]</DisplayString>
  </Type>

  <Type Name="Chaos::FImplicitObjectUnion">
    <DisplayString Condition="BVH.Ptr != nullptr">[Union NumObjects|Leafs={MObjects.ArrayNum}|{NumLeafObjects} [BVH {*BVH.Ptr}]]</DisplayString>
    <DisplayString>[Union NumObjects|Leafs={MObjects.ArrayNum}|{NumLeafObjects}]</DisplayString>
  </Type>

  <Type Name="Chaos::FImplicitObjectUnionClustered">
    <DisplayString Condition="BVH.Ptr != nullptr">[Clustered Union NumObjects|Leafs={MObjects.ArrayNum}|{NumLeafObjects} [BVH {*BVH.Ptr}]]</DisplayString>
    <DisplayString>[Clustered Union NumObjects|Leafs={MObjects.ArrayNum}|{NumLeafObjects}]</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FImplicitBVHObject">
    <DisplayString>{Geometry} @ {X}</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FImplicitBVHNode">
    <DisplayString Condition="ChildNodeIndices[0] == -1">Num={ObjectEndIndex - ObjectBeginIndex} ({ObjectBeginIndex} - {ObjectEndIndex-1}) &lt;Leaf&gt;</DisplayString>
    <DisplayString Condition="ChildNodeIndices[0] != -1">Num={ObjectEndIndex - ObjectBeginIndex} ({ObjectBeginIndex} - {ObjectEndIndex-1}); Children=({ChildNodeIndices[0]}, {ChildNodeIndices[1]})</DisplayString>
  </Type>

  <Type Name="Chaos::Private::FImplicitBVH">
      <DisplayString>Depth={TreeDepth} NumNodes={Nodes.ArrayNum}</DisplayString>
  </Type>
  
  <!-- Chaos FPerShapeData Visualizer -->
  <Type Name="Chaos::FPerShapeData">
    <DisplayString>{Geometry.Reference}</DisplayString>
  </Type>

  <!-- Chaos TSerializablePtr Visualizer -->
  <Type Name="Chaos::TSerializablePtr&lt;*&gt;">
    <DisplayString>{Ptr}</DisplayString>
  </Type>

  <Type Name="Chaos::FParticlePairMidPhase">
    <DisplayString>NumConstraints={NumActiveConstraints} [{*Particle0} -:- {*Particle1}]</DisplayString>
  </Type>

  <Type Name="Chaos::FSingleShapePairCollisionDetector">
    <DisplayString>{*Shape0} -:- {*Shape1}</DisplayString>
  </Type>

  <Type Name="Chaos::FCollisionParticlePairKey">
    <DisplayString>&lt;{Key.Key32s[0].Key31}&gt; -:- &lt;{Key.Key32s[1].Key31}&gt; [{Key.Key64,x}]</DisplayString>
  </Type>

  <Type Name="ChaosDD::Private::FChaosDDScene">
    <DisplayString>{Name}</DisplayString>
  </Type>

  <Type Name="ChaosDD::Private::FChaosDDTimeline">
    <DisplayString>{Name}</DisplayString>
  </Type>

  <Type Name="ChaosDD::Private::FChaosDDFrame">
    <DisplayString>{Time}s [{CommandCost} / {CommandBudget}]</DisplayString>
  </Type>

  <Type Name="TManagedArray&lt;*&gt;">
    <DisplayString>{Array}</DisplayString>
    <Expand>
      <ExpandedItem>Array</ExpandedItem>
    </Expand>
  </Type>

  <!-- FIoChunkId visualizer -->
  <Type Name="FIoChunkId">
    <!-- Id is 12 bytes long and the object is considered Invalid if all values are 0 -->
    <DisplayString Condition="reinterpret_cast&lt;uint32*&gt;(Id)[0] == 0 &amp;&amp; reinterpret_cast&lt;uint32*&gt;(Id)[1] == 0 &amp;&amp; reinterpret_cast&lt;uint32*&gt;(Id)[2] == 0">Invalid</DisplayString>
    <DisplayString>{(uint8)Id[0],nvox}{(uint8)Id[1],nvoxb}{(uint8)Id[2],nvoxb}{(uint8)Id[3],nvoxb}{(uint8)Id[4],nvoxb}{(uint8)Id[5],nvoxb}{(uint8)Id[6],nvoxb}{(uint8)Id[7],nvoxb}{(uint8)Id[8],nvoxb}{(uint8)Id[9],nvoxb}{(uint8)Id[10],nvoxb}{(uint8)Id[11],nvoxb}, ChunkType={*(EIoChunkType*)&amp;Id[11],en}, ChunkId={*(uint64*)&amp;Id[0],X}, ChunkIndex={*(uint16*)&amp;Id[8]}</DisplayString>
    <Expand>
      <Item Name="ChunkId">*(uint64*)&amp;Id[0],X</Item>
      <Item Name="ChunkIndex">*(uint16*)&amp;Id[8]</Item>
      <Item Name="EIoChunkType">*(EIoChunkType*)&amp;Id[11]</Item>
    </Expand>
  </Type>

  <!-- FIoStoreTocCompressedBlockEntry visualizer -->
  <Type Name="FIoStoreTocCompressedBlockEntry">
    <Expand>
      <Item Name="Offset">((const uint64*)Data)[0] &amp; OffsetMask</Item>
      <Item Name="CompressedSize">(((const uint32*)Data)[1] &gt;&gt; SizeShift) &amp; SizeMask</Item>
      <Item Name="UncompressedSize">((const uint32*)Data)[2] &amp; SizeMask</Item>
      <Item Name="CompressionMethodIndex">((const uint32*)Data)[2] &gt;&gt; SizeBits</Item> 
    </Expand>
  </Type>

  <!-- FIoOffsetAndLength visualizer -->
  <Type Name="FIoOffsetAndLength">
    <Expand>
      <Item Name="Offset">uint64(OffsetAndLength[4]) | uint64(OffsetAndLength[3])&lt;&lt;8 | uint64(OffsetAndLength[2])&lt;&lt;16 | uint64(OffsetAndLength[1])&lt;&lt;24 | uint64(OffsetAndLength[0])&lt;&lt;32</Item>
      <Item Name="Length">uint64(OffsetAndLength[9]) | uint64(OffsetAndLength[8])&lt;&lt;8 | uint64(OffsetAndLength[7])&lt;&lt;16 | uint64(OffsetAndLength[6])&lt;&lt;24 | uint64(OffsetAndLength[5])&lt;&lt;32</Item>
    </Expand>
  </Type>

   <!-- 
  *
  * ObjectHandle/ObjectPtr Visualizers 
  *
  -->
  
  <!-- FStoredObjectPathDebug visualizer -->
  <Type Name="UE::CoreUObject::Private::FStoredObjectPathDebug">
    <DisplayString Condition="NumElements == 0">Empty</DisplayString>
    <DisplayString Condition="NumElements &lt; 0">Invalid</DisplayString>
    <DisplayString Condition="NumElements != 0">Num={NumElements}</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>NumElements</Size>
        <ValuePointer>(FMinimalName*)(NumElements &lt;= NumInlineElements ? Short : Long)</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- UE::CoreUObject::Private::FObjectPathIdDebug visualizer -->
  <Type Name="UE::CoreUObject::Private::FObjectPathIdDebug">
    <DisplayString Condition="Index == 0 &amp;&amp; Number == 0">Empty</DisplayString>
    <DisplayString Condition="((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask) == UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask)">{*(FNameEntryId*)(&amp;Index)}_{(Number &amp; ~UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask),d}</DisplayString>
    <DisplayString Condition="((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask) == UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask) &amp;&amp; GObjectArrayForDebugVisualizers->Objects[Index / 65536][Index % 65536].SerialNumber != (Number &amp; ~UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask)">STALE</DisplayString>
    <DisplayString  Condition="((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask) == UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask)">{GObjectArrayForDebugVisualizers->Objects[Index / 65536][Index % 65536].Object}</DisplayString>
    <DisplayString Condition="(Index != 0) &amp;&amp; ((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask) != UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask  &amp;&amp; (Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask) != UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask)">
      Complex Path [Num={GComplexObjectPathDebug[Index-1].NumElements}]
    </DisplayString>
    <Expand>
      <ExpandedItem Condition="(Number != 0 &amp;&amp; Index != 0) &amp;&amp; ((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask) != UE::CoreUObject::Private::FObjectPathIdDebug::SimpleNameMask)">
        GComplexObjectPathDebug[Index-1]
      </ExpandedItem>
      <ExpandedItem Condition="((Number &amp; UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask) == UE::CoreUObject::Private::FObjectPathIdDebug::WeakObjectMask)">GObjectArrayForDebugVisualizers->Objects[Index / 65536][Index % 65536].Object</ExpandedItem>
    </Expand>
  </Type>

  <!-- UE::CoreUObject::Private::FPackedObjectRef visualizer.  Expandable items made to match fields of FObjectRef in name and order. -->
  <Type Name="UE::CoreUObject::Private::FPackedObjectRef">
    <Intrinsic Name="ObjectIdShift" Expression="2"/>
    <Intrinsic Name="PackageIdShift" Expression="34"/>
    <Intrinsic Name="PackageIdMask" Expression="0x3FFFFFFF"/>
    <Expand>
      <Item Condition="EncodedRef &amp; 1" Name="PackageName">
        GObjectHandlePackageDebug[*(uint64*)this &gt;&gt; PackageIdShift() &amp; PackageIdMask()].PackageName
      </Item>
      <Item Condition="EncodedRef &amp; 1 &amp;&amp; ((uint32)EncodedRef &gt;&gt; ObjectIdShift()) != 0" Name="ObjectPath">
        ((UE::CoreUObject::Private::FObjectDescriptorDebug*)(GObjectHandlePackageDebug[*(uint64*)this &gt;&gt; PackageIdShift() &amp; PackageIdMask()].ObjectDescriptors.Data))[(*(uint32*)this &gt;&gt; 1) - 1].ObjectPath
      </Item>
      <Item Condition="(EncodedRef &amp; 1) &amp;&amp; ((uint32)EncodedRef &gt;&gt; ObjectIdShift()) == 0" Name="ClassPackageName">
        "/Script/CoreUObject"
      </Item>
      <Item Condition="(EncodedRef &amp; 1) &amp;&amp; ((uint32)EncodedRef &gt;&gt; ObjectIdShift()) == 0" Name="ClassName">
        "Package"
      </Item>
      <Item Condition="(EncodedRef &amp; 1) &amp;&amp; ((uint32)EncodedRef &gt;&gt; ObjectIdShift()) != 0" Name="ClassPackageName">
        ((UE::CoreUObject::Private::FObjectDescriptorDebug*)(GObjectHandlePackageDebug[*(uint64*)this &gt;&gt; PackageIdShift() &amp; PackageIdMask()].ObjectDescriptors.Data))[(*(uint32*)this &gt;&gt; 1) - 1].ClassDescriptor.PackageName
      </Item>
      <Item Condition="(EncodedRef &amp; 1) &amp;&amp; ((uint32)EncodedRef &gt;&gt; ObjectIdShift()) != 0" Name="ClassName">
        ((UE::CoreUObject::Private::FObjectDescriptorDebug*)(GObjectHandlePackageDebug[*(uint64*)this &gt;&gt; PackageIdShift() &amp; PackageIdMask()].ObjectDescriptors.Data))[(*(uint32*)this &gt;&gt; 1) - 1].ClassDescriptor.ClassName
      </Item>
    </Expand>
  </Type>

  <Type Name="FObjectRef">
    <DisplayString>{*reinterpret_cast&lt;UE::CoreUObject::Private::FObjectPathIdDebug*&gt;(&amp;ObjectPathId)}</DisplayString>
    <Expand>
      <Item Name="PackageName">PackageName</Item>
      <Item Name="ClassPackageName">ClassPackageName</Item>
      <Item Name="ClassName">ClassName</Item>
      <Item Name="ObjectPath">
        *reinterpret_cast&lt;UE::CoreUObject::Private::FObjectPathIdDebug*&gt;(&amp;ObjectPathId)
      </Item>
    </Expand>
  </Type>

  <!-- Tasks -->

  <!-- LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData visualizer -->
  <Type Name="LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData">
    <DisplayString>DebugName={(const TCHAR*)(DebugName)}, State={(::LowLevelTasks::ETaskState)(State)}</DisplayString>
    <Expand>
      <Item Name="DebugName">(const TCHAR*)(DebugName)</Item>
      <Item Name="Priority">(::LowLevelTasks::ETaskPriority)(Priority)</Item>
      <Item Name="Flags">(::LowLevelTasks::ETaskFlags)(Flags)</Item>
      <Item Name="State">(::LowLevelTasks::ETaskState)(State)</Item>
    </Expand>
  </Type>

  <!-- LowLevelTasks::Tasks_Impl::FTaskBase::FPackedDataAtomic visualizer -->
  <Type Name="LowLevelTasks::Tasks_Impl::FTaskBase::FPackedDataAtomic">
    <Intrinsic Optional="false" Name="value" Expression="*(::LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData*)this"/>
    <DisplayString>{value()}</DisplayString>
    <Expand>
      <Item Name="DebugName">(const TCHAR*)(((::LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData*)this)->DebugName)</Item>
      <Item Name="Priority">(::LowLevelTasks::ETaskPriority)(((::LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData*)this)->Priority)</Item>
      <Item Name="Flags">(::LowLevelTasks::ETaskFlags)(((::LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData*)this)->Flags)</Item>
      <Item Name="State">(::LowLevelTasks::ETaskState)(((::LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData*)this)->State)</Item>
    </Expand>
  </Type>
  
  <!-- LowLevelTasks::FTask visualizer -->
  <Type Name="LowLevelTasks::FTask">
    <DisplayString>{PackedData}</DisplayString>
  </Type>

  <!-- LowLevelTasks::FScheduler::FEventStack::FTopNode visualizer -->
  <Type Name="LowLevelTasks::FScheduler::FEventStack::FTopNode">
    <DisplayString>Address={(FSleepEvent*)(Address &lt;&lt; 3)}}</DisplayString>
    <Expand>
      <Item Name="Address">(FSleepEvent*)(Address &lt;&lt; 3)</Item>
      <Item Name="Revision">(int)Revision</Item>
    </Expand>
  </Type>

  <Type Name="UE::Tasks::Private::FTaskBase">
    <DisplayString Condition="Pipe == nullptr">"{(LowLevelTasks::ETaskState)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).State,en}" state, {(LowLevelTasks::ETaskPriority)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).Priority,en}Pri, "{(const TCHAR*)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).DebugName,sub}", no pipe</DisplayString>
    <DisplayString>"{(LowLevelTasks::ETaskState)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).State,en}" state, {(LowLevelTasks::ETaskPriority)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).Priority,en}Pri, "{(const TCHAR*)((LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value).DebugName,sub}", pipe "{Pipe-&gt;DebugName,sub}"</DisplayString>

    <Expand>
      <ExpandedItem>(LowLevelTasks::Tasks_Impl::FTaskBase::FPackedData&amp;)LowLevelTask.PackedData.PackedData._Storage._Value</ExpandedItem>
      <Item Name="ExtendedPriority">ExtendedPriority</Item>
      <Item Name="NumLocks">NumLocks._Storage._Value</Item>
      <Item Name="Subsequents">Subsequents</Item>
      <Item Name="Prerequisites">Prerequisites</Item>
      <Item Name="Pipe">Pipe</Item>
      <Item Name="RefCount">RefCount</Item>
    </Expand>
  </Type>

  <Type Name="UE::Tasks::Private::FTaskHandle">
    <DisplayString Condition="Pimpl.Reference != nullptr">{Pimpl.Reference}</DisplayString>
    <DisplayString>{{empty}}</DisplayString>

    <Expand>
      <ExpandedItem Condition="Pimpl.Reference != nullptr">Pimpl.Reference</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="UE::Tasks::FPipe">
    <DisplayString Condition="LastTask._Storage._Value != nullptr">Blocked by task {LastTask._Storage._Value}</DisplayString>
    <DisplayString>Free, "{DebugName,sub}"</DisplayString>
  </Type>
  
  <!--Concurrent queues -->

  <!-- TClosableMpscQueue visualizer -->
  <Type Name="TClosableMpscQueue&lt;*&gt;">
    <DisplayString Condition="Head._Storage._Value == &amp;Sentinel">{{Empty}}</DisplayString>
    <DisplayString Condition="Head._Storage._Value == nullptr">{{Closed}}</DisplayString>
    <DisplayString Condition="Sentinel.Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[1]</DisplayString>
    <DisplayString Condition="Sentinel.Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[2]</DisplayString>
    <DisplayString Condition="Sentinel.Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[3]</DisplayString>
    <DisplayString Condition="Sentinel.Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[4]</DisplayString>
    <DisplayString Condition="Sentinel.Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[5]</DisplayString>
    <DisplayString>[...]</DisplayString>

    <Expand>
      <Item Name="Sentinel">Sentinel.Value</Item>
      <LinkedListItems>
        <HeadPointer>Sentinel.Next._Storage._Value</HeadPointer>
        <NextPointer>Next._Storage._Value</NextPointer>
        <ValueNode>Value</ValueNode>
      </LinkedListItems>
    </Expand>
  </Type>

  <!-- atomic_queue::AtomicQueue visualizer -->
  <Type Name="atomic_queue::AtomicQueue&lt;*,*&gt;">
    <DisplayString Condition="head_._Storage._Value - tail_._Storage._Value &lt;= 0">{{Empty}}</DisplayString>
    <DisplayString>[{head_._Storage._Value - tail_._Storage._Value}]</DisplayString>

    <Expand>
      <Item Name="Num">head_._Storage._Value - tail_._Storage._Value</Item>
      <Item Name="Capacity">$T2</Item>
      <!--<IndexListItems>
        <Size>head_._Storage._Value - tail_._Storage._Value</Size>
        <ValueNode>elements_[(tail_._Storage._Value % $T2 + $i) % $T2]</ValueNode>
      </IndexListItems>-->
    </Expand>
  </Type>
  
  <!-- FAAArrayDeque visualizer -->
  <Type Name="FAAArrayDeque&lt;*&gt;">
    <DisplayString Condition="head._Storage._Value == tail._Storage._Value &amp;&amp; tail._Storage._Value-&gt;deqidx._Storage._Value == tail._Storage._Value-&gt;enqidx._Storage._Value">[Empty]</DisplayString>
    <DisplayString Condition="head._Storage._Value == tail._Storage._Value">[{tail._Storage._Value-&gt;enqidx._Storage._Value - tail._Storage._Value-&gt;deqidx._Storage._Value}]</DisplayString>
    <DisplayString>[&gt;={tail._Storage._Value-&gt;enqidx._Storage._Value - tail._Storage._Value-&gt;deqidx._Storage._Value}]</DisplayString>
  </Type>
  
  <!-- TSpscQueue<T> -->
  <Type Name="TSpscQueue&lt;*&gt;">
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value == nullptr">[Empty]</DisplayString>
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[1]</DisplayString>
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[2]</DisplayString>
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[3]</DisplayString>
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[4]</DisplayString>
    <DisplayString Condition="Tail._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value-&gt;Next._Storage._Value == nullptr">[5]</DisplayString>
    <DisplayString>[>5]</DisplayString>

    <Expand>
      <LinkedListItems>
        <HeadPointer>Tail._Storage._Value-&gt;Next._Storage._Value</HeadPointer>
        <NextPointer>Next._Storage._Value</NextPointer>
        <ValueNode>Value</ValueNode>
      </LinkedListItems>
    </Expand>
  </Type>

  <!--TTypeCompatibleBytes<T>-->
  <Type Name="TTypeCompatibleBytes&lt;*&gt;">
    <DisplayString>{*(ElementTypeAlias_NatVisHelper*)this}</DisplayString>
    <Expand>
      <ExpandedItem>*(ElementTypeAlias_NatVisHelper*)this</ExpandedItem>
    </Expand>
  </Type>

  <!-- FObjectHandleInternal visualizer -->
  <Type Name="UE::CoreUObject::Private::FObjectHandlePrivate">
    <DisplayString Condition="*(uintptr_t*)this == 0">nullptr</DisplayString>
    <DisplayString Condition="*(uintptr_t*)this &amp; 1">{*(UE::CoreUObject::Private::FPackedObjectRef*)this}</DisplayString>
    <DisplayString Condition="(*(uintptr_t*)this != 0) &amp;&amp; !(*(uintptr_t*)this &amp; 1)">{*(UObject**)this}</DisplayString>
    <Expand>
      <ExpandedItem Condition="*(uintptr_t*)this &amp; 1">*(UE::CoreUObject::Private::FPackedObjectRef*)this</ExpandedItem>
      <ExpandedItem Condition="!(*(uintptr_t*)this &amp; 1)">*(UObject**)this</ExpandedItem>
    </Expand>
  </Type>

  <!-- FObjectPtr visualizer -->
  <Type Name="FObjectPtr" Priority="High">
    <SmartPointer Usage="Minimal">*(UE::CoreUObject::Private::FObjectHandlePrivate**)&amp;Handle</SmartPointer>
    <DisplayString>{Handle}</DisplayString>
    <Expand>
      <ExpandedItem>Handle</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="FObjectPtr">
    <SmartPointer Usage="Minimal">((unsigned long long)Handle.PointerOrRef &amp; 1) ? (UObject*)Handle.PointerOrRef : nullptr</SmartPointer>
    <DisplayString>{Handle}</DisplayString>
    <Expand>
      <ExpandedItem>Handle</ExpandedItem>
    </Expand>
  </Type>
  
  <!-- TObjectPtr visualizer -->
  <Type Name="TObjectPtr&lt;*&gt;">
    <Intrinsic Name="IsNullObjectPtr" Expression="*(uintptr_t*)&amp;ObjectPtr.Handle == 0">
      <Parameter Name="ObjectPtr" Type="const FObjectPtr&amp;"/>
    </Intrinsic>
    <Intrinsic Name="IsUnresolvedObjectPtr" Expression="*(uintptr_t*)&amp;ObjectPtr.Handle &amp; 1">
      <Parameter Name="ObjectPtr" Type="const FObjectPtr&amp;"/>
    </Intrinsic>
    <Intrinsic Name="IsDerivedObjectType" Expression="strcmp(&quot;$T1&quot;,&quot;UObject&quot;) != 0 &amp;&amp; strcmp(&quot;$T1&quot;,&quot;UObject const&quot;) != 0"/>
    <Intrinsic Name="IsPlaceholderObjectPtr" Expression="IsDerivedObjectType() &amp;&amp; (*(uintptr_t*)&amp;ObjectPtr.Handle &amp; 3) == 3">
      <Parameter Name="ObjectPtr" Type="const FObjectPtr&amp;"/>
    </Intrinsic>
    <SmartPointer Usage="Minimal">!IsNullObjectPtr(ObjectPtr) &amp;&amp; !IsPlaceholderObjectPtr(ObjectPtr) &amp;&amp; !IsUnresolvedObjectPtr(ObjectPtr) ? *($T1**)&amp;ObjectPtr.Handle : ($T1*)0</SmartPointer>
    <DisplayString Condition="IsNullObjectPtr(ObjectPtr)">nullptr</DisplayString>
    <DisplayString Condition="IsPlaceholderObjectPtr(ObjectPtr)">nullptr (placeholder)</DisplayString>
    <DisplayString Condition="IsUnresolvedObjectPtr(ObjectPtr)">{*(UE::CoreUObject::Private::FPackedObjectRef*)&amp;ObjectPtr.Handle}</DisplayString>
    <DisplayString>{*($T1**)&amp;ObjectPtr.Handle}</DisplayString>
    <Expand>
      <ExpandedItem Condition="IsNullObjectPtr(ObjectPtr)">($T1*)0</ExpandedItem>
      <ExpandedItem Condition="!IsNullObjectPtr(ObjectPtr) &amp;&amp; IsPlaceholderObjectPtr(ObjectPtr)">($T1*)0</ExpandedItem>
      <ExpandedItem Condition="!IsNullObjectPtr(ObjectPtr) &amp;&amp; !IsPlaceholderObjectPtr(ObjectPtr) &amp;&amp; IsUnresolvedObjectPtr(ObjectPtr)">*(UE::CoreUObject::Private::FPackedObjectRef*)&amp;ObjectPtr.Handle</ExpandedItem>
      <ExpandedItem Condition="!IsNullObjectPtr(ObjectPtr) &amp;&amp; !IsPlaceholderObjectPtr(ObjectPtr) &amp;&amp; !IsUnresolvedObjectPtr(ObjectPtr)">*($T1**)&amp;ObjectPtr.Handle</ExpandedItem>
    </Expand>
  </Type>

  <!-- FMD5Hash visualizer -->
  <Type Name="FMD5Hash">
    <DisplayString Condition="bIsValid">{uint32(Bytes[0]&lt;&lt;24 | Bytes[1]&lt;&lt;16 | Bytes[2]&lt;&lt;8 | Bytes[3]),xb}{uint32(Bytes[4]&lt;&lt;24 | Bytes[5]&lt;&lt;16 | Bytes[6]&lt;&lt;8 | Bytes[7]),xb}{uint32(Bytes[8]&lt;&lt;24 | Bytes[9]&lt;&lt;16 | Bytes[10]&lt;&lt;8 | Bytes[11]),xb}{uint32(Bytes[12]&lt;&lt;24 | Bytes[13]&lt;&lt;16 | Bytes[14]&lt;&lt;8 | Bytes[15]),xb}</DisplayString>
    <DisplayString>Invalid</DisplayString>
    <Expand>
      <ExpandedItem Condition="bIsValid">Bytes</ExpandedItem>
    </Expand>
  </Type>

  <!-- FSHAHash visualizer -->
  <Type Name="FSHAHash">
    <DisplayString>{uint32(Hash[0]&lt;&lt;24 | Hash[1]&lt;&lt;16 | Hash[2]&lt;&lt;8 | Hash[3]),xb}{uint32(Hash[4]&lt;&lt;24 | Hash[5]&lt;&lt;16 | Hash[6]&lt;&lt;8 | Hash[7]),xb}{uint32(Hash[8]&lt;&lt;24 | Hash[9]&lt;&lt;16 | Hash[10]&lt;&lt;8 | Hash[11]),xb}{uint32(Hash[12]&lt;&lt;24 | Hash[13]&lt;&lt;16 | Hash[14]&lt;&lt;8 | Hash[15]),xb}{uint32(Hash[16]&lt;&lt;24 | Hash[17]&lt;&lt;16 | Hash[18]&lt;&lt;8 | Hash[19]),xb}</DisplayString>
    <Expand>
      <ExpandedItem>Hash</ExpandedItem>
    </Expand>
  </Type>

  <!-- FSharedBuffer visualizer -->
  <Type Name="FSharedBuffer">
    <DisplayString Condition="Owner.Owner == 0">Null</DisplayString>
    <DisplayString>Size={Owner.Owner->Size} Owner={Owner.Owner}</DisplayString>
    <Expand>
      <ExpandedItem>Owner.Owner</ExpandedItem>
    </Expand>
  </Type>

  <!-- FWeakSharedBuffer visualizer -->
  <Type Name="FWeakSharedBuffer">
    <DisplayString Condition="Owner.Owner == 0">Null</DisplayString>
    <DisplayString>Size={Owner.Owner->Size} Owner={Owner.Owner}</DisplayString>
    <Expand>
      <ExpandedItem>Owner.Owner</ExpandedItem>
    </Expand>
  </Type>

  <!-- FUniqueBuffer visualizer -->
  <Type Name="FUniqueBuffer">
    <DisplayString Condition="Owner.Owner == 0">Null</DisplayString>
    <DisplayString>Size={Owner.Owner->Size} Owner={Owner.Owner}</DisplayString>
    <Expand>
      <ExpandedItem>Owner.Owner</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBufferOwner visualizer -->
  <Type Name="FBufferOwner">
    <Expand>
      <Item Name="Data" Condition="this != 0">Data</Item>
      <Item Name="Size" Condition="this != 0">Size</Item>
      <Item Name="SharedRefCount" Condition="this != 0">uint32(ReferenceCountsAndFlags._Storage._Value >> 0) &amp; 0x3fffffff</Item>
      <Item Name="WeakRefCount" Condition="this != 0">uint32(ReferenceCountsAndFlags._Storage._Value >> 30) &amp; 0x3fffffff</Item>
      <Item Name="Flags" Condition="this != 0">EBufferOwnerFlags(ReferenceCountsAndFlags._Storage._Value &gt;&gt; 60)</Item>
    </Expand>
  </Type>

  <!-- FCompositeBuffer visualizer -->
  <Type Name="FCompositeBuffer">
    <DisplayString Condition="Segments.ArrayNum == 0">Null</DisplayString>
    <Expand>
      <CustomListItems>
        <Variable Name="SegmentIndex" InitialValue="0"/>
        <Variable Name="TotalSize" InitialValue="0"/>
        <Variable Name="SegmentData" InitialValue="(FSharedBuffer*)Segments.AllocatorInstance.SecondaryData.Data"/>
        <If Condition="SegmentData == nullptr">
          <Exec>SegmentData = (FSharedBuffer*)Segments.AllocatorInstance.InlineData</Exec>
        </If>
        <Loop>
          <Break Condition="SegmentIndex == Segments.ArrayNum"/>
          <Exec>TotalSize += SegmentData[SegmentIndex].Owner.Owner->Size</Exec>
          <Exec>SegmentIndex += 1</Exec>
        </Loop>
        <Item Name="Size">TotalSize</Item>
      </CustomListItems>
      <Item Name="Segments">Segments</Item>
    </Expand>
  </Type>

  <!-- FCompressedBuffer visualizer -->
  <Type Name="FCompressedBuffer">
    <DisplayString Condition="CompressedData.Segments.ArrayNum == 0">Null</DisplayString>
    <Expand>
      <CustomListItems Condition="CompressedData.Segments.ArrayNum > 0">
        <Variable Name="HeaderData" InitialValue="(const uint8*)nullptr"/>
        <Variable Name="SegmentData" InitialValue="(FSharedBuffer*)CompressedData.Segments.AllocatorInstance.SecondaryData.Data"/>
        <If Condition="SegmentData == nullptr">
          <Exec>SegmentData = (FSharedBuffer*)CompressedData.Segments.AllocatorInstance.InlineData</Exec>
        </If>
        <Exec>HeaderData = (const uint8*)SegmentData[0].Owner.Owner-&gt;Data</Exec>
        <Item Name="CompressedSize">(uint64(HeaderData[24])&lt;&lt;56) | (uint64(HeaderData[25])&lt;&lt;48) | (uint64(HeaderData[26])&lt;&lt;40) | (uint64(HeaderData[27])&lt;&lt;32) | (uint64(HeaderData[28])&lt;&lt;24) | (uint64(HeaderData[29])&lt;&lt;16) | (uint64(HeaderData[30])&lt;&lt;8) | (uint64(HeaderData[31]))</Item>
        <Item Name="RawSize">(uint64(HeaderData[16])&lt;&lt;56) | (uint64(HeaderData[17])&lt;&lt;48) | (uint64(HeaderData[18])&lt;&lt;40) | (uint64(HeaderData[19])&lt;&lt;32) | (uint64(HeaderData[20])&lt;&lt;24) | (uint64(HeaderData[21])&lt;&lt;16) | (uint64(HeaderData[22])&lt;&lt;8) | (uint64(HeaderData[23]))</Item>
        <Item Name="RawHash">*(FBlake3Hash*)(HeaderData + 32)</Item>
        <Item Name="Method">(UE::CompressedBuffer::Private::EMethod)HeaderData[8]</Item>
        <Item Name="Compressor">(ECompressedBufferCompressor)HeaderData[9]</Item>
        <Item Name="CompressionLevel">(ECompressedBufferCompressionLevel)HeaderData[10]</Item>
        <Item Name="BlockSize">uint64(1)&lt;&lt;HeaderData[11]</Item>
        <Item Name="BlockCount">(uint32(HeaderData[12])&lt;&lt;24) | (uint32(HeaderData[13])&lt;&lt;16) | (uint32(HeaderData[14])&lt;&lt;8) | (uint32(HeaderData[15]))</Item>
      </CustomListItems>
      <Item Name="CompressedData">CompressedData</Item>
    </Expand>
  </Type>

  <!-- FBlake3Hash visualizer -->
  <Type Name="FBlake3Hash">
    <DisplayString>{uint32(Hash[0]&lt;&lt;24 | Hash[1]&lt;&lt;16 | Hash[2]&lt;&lt;8 | Hash[3]),xb}{uint32(Hash[4]&lt;&lt;24 | Hash[5]&lt;&lt;16 | Hash[6]&lt;&lt;8 | Hash[7]),xb}{uint32(Hash[8]&lt;&lt;24 | Hash[9]&lt;&lt;16 | Hash[10]&lt;&lt;8 | Hash[11]),xb}{uint32(Hash[12]&lt;&lt;24 | Hash[13]&lt;&lt;16 | Hash[14]&lt;&lt;8 | Hash[15]),xb}{uint32(Hash[16]&lt;&lt;24 | Hash[17]&lt;&lt;16 | Hash[18]&lt;&lt;8 | Hash[19]),xb}{uint32(Hash[20]&lt;&lt;24 | Hash[21]&lt;&lt;16 | Hash[22]&lt;&lt;8 | Hash[23]),xb}{uint32(Hash[24]&lt;&lt;24 | Hash[25]&lt;&lt;16 | Hash[26]&lt;&lt;8 | Hash[27]),xb}{uint32(Hash[28]&lt;&lt;24 | Hash[29]&lt;&lt;16 | Hash[30]&lt;&lt;8 | Hash[31]),xb}</DisplayString>
    <Expand>
      <ExpandedItem>Hash</ExpandedItem>
    </Expand>
  </Type>

  <!-- FCbObjectId visualizer -->
  <Type Name="FCbObjectId">
    <DisplayString>{uint32(Bytes[0]&lt;&lt;24 | Bytes[1]&lt;&lt;16 | Bytes[2]&lt;&lt;8 | Bytes[3]),xb}{uint32(Bytes[4]&lt;&lt;24 | Bytes[5]&lt;&lt;16 | Bytes[6]&lt;&lt;8 | Bytes[7]),xb}{uint32(Bytes[8]&lt;&lt;24 | Bytes[9]&lt;&lt;16 | Bytes[10]&lt;&lt;8 | Bytes[11]),xb}</DisplayString>
    <Expand>
      <ExpandedItem>Bytes</ExpandedItem>
    </Expand>
  </Type>

  <!-- FIoHash visualizer -->
  <Type Name="FIoHash">
    <DisplayString>{uint32(Hash[0]&lt;&lt;24 | Hash[1]&lt;&lt;16 | Hash[2]&lt;&lt;8 | Hash[3]),xb}{uint32(Hash[4]&lt;&lt;24 | Hash[5]&lt;&lt;16 | Hash[6]&lt;&lt;8 | Hash[7]),xb}{uint32(Hash[8]&lt;&lt;24 | Hash[9]&lt;&lt;16 | Hash[10]&lt;&lt;8 | Hash[11]),xb}{uint32(Hash[12]&lt;&lt;24 | Hash[13]&lt;&lt;16 | Hash[14]&lt;&lt;8 | Hash[15]),xb}{uint32(Hash[16]&lt;&lt;24 | Hash[17]&lt;&lt;16 | Hash[18]&lt;&lt;8 | Hash[19]),xb}</DisplayString>
    <Expand>
      <ExpandedItem>Hash</ExpandedItem>
    </Expand>
  </Type>

  <!-- TSharedString visualizer -->
  <Type Name="UE::DerivedData::TSharedString&lt;*&gt;">
    <DisplayString Condition="!Chars">Empty</DisplayString>
    <DisplayString>{Chars,s8}</DisplayString>
    <StringView>Chars,s8</StringView>
    <Expand>
      <Item Name="Chars" Condition="!!Chars">Chars,[((int32*)Chars)[-1]]</Item>
      <Item Name="Length" Condition="!!Chars">((int32*)Chars)[-1]</Item>
      <Item Name="ReferenceCount" Condition="!!Chars">((int32*)Chars)[-2]</Item>
    </Expand>
  </Type>

  <!-- FWideSharedString visualizer -->
  <Type Name="UE::DerivedData::TSharedString&lt;wchar_t&gt;">
    <DisplayString Condition="!Chars">Empty</DisplayString>
    <DisplayString>{Chars,su}</DisplayString>
    <StringView>Chars,su</StringView>
    <Expand>
      <Item Name="Chars" Condition="!!Chars">Chars,[((int32*)Chars)[-1]]</Item>
      <Item Name="Length" Condition="!!Chars">((int32*)Chars)[-1]</Item>
      <Item Name="ReferenceCount" Condition="!!Chars">((int32*)Chars)[-2]</Item>
    </Expand>
  </Type>

  <!-- FRequestOwner visualizer -->
  <Type Name="UE::DerivedData::FRequestOwner">
    <DisplayString Condition="Owner.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Priority={((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->Priority} Requests={((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->Requests} BarrierCount={((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->BarrierCount} bIsCanceled={((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->bIsCanceled} bKeepAlive={((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->bKeepAlive}}}</DisplayString>
    <Expand>
      <Item Name="Priority" Condition="Owner.Ptr">((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->Priority</Item>
      <Item Name="Requests" Condition="Owner.Ptr">((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->Requests</Item>
      <Item Name="BarrierCount" Condition="Owner.Ptr">((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->BarrierCount</Item>
      <Item Name="bIsCanceled" Condition="Owner.Ptr">((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->bIsCanceled</Item>
      <Item Name="bKeepAlive" Condition="Owner.Ptr">((UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr)->bKeepAlive</Item>
      <Item Name="[Detail]" Condition="Owner.Ptr">(UE::DerivedData::Private::FRequestOwnerShared*)Owner.Ptr</Item>
    </Expand>
  </Type>

  <!-- FValueId visualizer -->
  <Type Name="UE::DerivedData::FValueId">
    <DisplayString>{uint32(Bytes[0]&lt;&lt;24 | Bytes[1]&lt;&lt;16 | Bytes[2]&lt;&lt;8 | Bytes[3]),xb}{uint32(Bytes[4]&lt;&lt;24 | Bytes[5]&lt;&lt;16 | Bytes[6]&lt;&lt;8 | Bytes[7]),xb}{uint32(Bytes[8]&lt;&lt;24 | Bytes[9]&lt;&lt;16 | Bytes[10]&lt;&lt;8 | Bytes[11]),xb}</DisplayString>
    <Expand>
      <ExpandedItem>Bytes</ExpandedItem>
    </Expand>
  </Type>

  <!-- FCacheBucket visualizer -->
  <Type Name="UE::DerivedData::FCacheBucket">
    <DisplayString>{Name,sb}</DisplayString>
    <StringView>Name,sb</StringView>
  </Type>

  <!-- FCacheKey visualizer -->
  <Type Name="UE::DerivedData::FCacheKey">
    <DisplayString>{Bucket}/{Hash}</DisplayString>
  </Type>

  <!-- FCacheRecord visualizer -->
  <Type Name="UE::DerivedData::FCacheRecord">
    <DisplayString Condition="Record.Reference == nullptr">Null</DisplayString>
    <DisplayString>{((UE::DerivedData::Private::FCacheRecordInternal*)Record.Reference)->Key}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Record.Reference">(UE::DerivedData::Private::FCacheRecordInternal*)Record.Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- FCacheRecordBuilder visualizer -->
  <Type Name="UE::DerivedData::FCacheRecordBuilder">
    <DisplayString Condition="RecordBuilder.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{((UE::DerivedData::Private::FCacheRecordBuilderInternal*)RecordBuilder.Ptr)->Key}</DisplayString>
    <Expand>
      <ExpandedItem Condition="RecordBuilder.Ptr">(UE::DerivedData::Private::FCacheRecordBuilderInternal*)RecordBuilder.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FCacheRecordPolicy visualizer -->
  <Type Name="UE::DerivedData::FCacheRecordPolicy">
    <Expand>
      <Item Name="RecordPolicy">RecordPolicy</Item>
      <Item Name="DefaultValuePolicy">DefaultValuePolicy</Item>
      <Item Name="ValuePolicies" Condition="Shared.Reference">((UE::DerivedData::Private::FCacheRecordPolicyShared*)Shared.Reference)->Values</Item>
    </Expand>
  </Type>

  <!-- FCacheRecordPolicyBuilder visualizer -->
  <Type Name="UE::DerivedData::FCacheRecordPolicyBuilder">
    <Expand>
      <Item Name="BasePolicy">BasePolicy</Item>
      <Item Name="ValuePolicies" Condition="Shared.Reference">((UE::DerivedData::Private::FCacheRecordPolicyShared*)Shared.Reference)->Values</Item>
    </Expand>
  </Type>

  <!-- FBuildAction visualizer -->
  <Type Name="UE::DerivedData::FBuildAction">
    <DisplayString Condition="Action.Reference == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildActionInternal*)Action.Reference)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildActionInternal*)Action.Reference)->Function,s8b} Key={((UE::DerivedData::Private::FBuildActionInternal*)Action.Reference)->Key.Hash}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Action.Reference">(UE::DerivedData::Private::FBuildActionInternal*)Action.Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildActionBuilder visualizer -->
  <Type Name="UE::DerivedData::FBuildActionBuilder">
    <DisplayString Condition="ActionBuilder.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildActionBuilderInternal*)ActionBuilder.Ptr)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildActionBuilderInternal*)ActionBuilder.Ptr)->Function,s8b}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="ActionBuilder.Ptr">(UE::DerivedData::Private::FBuildActionBuilderInternal*)ActionBuilder.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildDefinition visualizer -->
  <Type Name="UE::DerivedData::FBuildDefinition">
    <DisplayString Condition="Definition.Reference == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildDefinitionInternal*)Definition.Reference)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildDefinitionInternal*)Definition.Reference)->Function,s8b} Key={((UE::DerivedData::Private::FBuildDefinitionInternal*)Definition.Reference)->Key.Hash}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Definition.Reference">(UE::DerivedData::Private::FBuildDefinitionInternal*)Definition.Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildDefinitionBuilder visualizer -->
  <Type Name="UE::DerivedData::FBuildDefinitionBuilder">
    <DisplayString Condition="DefinitionBuilder.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildDefinitionBuilderInternal*)DefinitionBuilder.Ptr)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildDefinitionBuilderInternal*)DefinitionBuilder.Ptr)->Function,s8b}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="DefinitionBuilder.Ptr">(UE::DerivedData::Private::FBuildDefinitionBuilderInternal*)DefinitionBuilder.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildInputs visualizer -->
  <Type Name="UE::DerivedData::FBuildInputs">
    <DisplayString Condition="Inputs.Reference == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildInputsInternal*)Inputs.Reference)->Name,s8b}"}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Inputs.Reference">(UE::DerivedData::Private::FBuildInputsInternal*)Inputs.Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildInputsBuilder visualizer -->
  <Type Name="UE::DerivedData::FBuildInputsBuilder">
    <DisplayString Condition="InputsBuilder.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildInputsBuilderInternal*)InputsBuilder.Ptr)->Name,s8b}"}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="InputsBuilder.Ptr">(UE::DerivedData::Private::FBuildInputsBuilderInternal*)InputsBuilder.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildOutput visualizer -->
  <Type Name="UE::DerivedData::FBuildOutput">
    <DisplayString Condition="Output.Reference == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildOutputInternal*)Output.Reference)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildOutputInternal*)Output.Reference)->Function,s8b}}}</DisplayString>
    <Expand>
      <ExpandedItem>(UE::DerivedData::Private::FBuildOutputInternal*)Output.Reference</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildOutputBuilder visualizer -->
  <Type Name="UE::DerivedData::FBuildOutputBuilder">
    <DisplayString Condition="OutputBuilder.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildOutputBuilderInternal*)OutputBuilder.Ptr)->Name,s8b}" Function={((UE::DerivedData::Private::FBuildOutputBuilderInternal*)OutputBuilder.Ptr)->Function,s8b} bHasError={((UE::DerivedData::Private::FBuildOutputBuilderInternal*)OutputBuilder.Ptr)->bHasError}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="OutputBuilder.Ptr">(UE::DerivedData::Private::FBuildOutputBuilderInternal*)OutputBuilder.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- FBuildPolicy visualizer -->
  <Type Name="UE::DerivedData::FBuildPolicy">
    <Expand>
      <Item Name="CombinedPolicy">CombinedPolicy</Item>
      <Item Name="DefaultValuePolicy">DefaultValuePolicy</Item>
      <Item Name="ValuePolicies" Condition="Shared.Reference">((UE::DerivedData::Private::FBuildPolicyShared*)Shared.Reference)->Values</Item>
    </Expand>
  </Type>

  <!-- FBuildPolicyBuilder visualizer -->
  <Type Name="UE::DerivedData::FBuildPolicyBuilder">
    <Expand>
      <Item Name="BasePolicy">BasePolicy</Item>
      <Item Name="ValuePolicies" Condition="Shared.Reference">((UE::DerivedData::Private::FBuildPolicyShared*)Shared.Reference)->Values</Item>
    </Expand>
  </Type>

  <!-- FBuildSession visualizer -->
  <Type Name="UE::DerivedData::FBuildSession">
    <DisplayString Condition="Session.Ptr == nullptr">Null</DisplayString>
    <DisplayString>{{Name="{((UE::DerivedData::Private::FBuildSessionInternal*)Session.Ptr)->Name,s8b}"}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="Session.Ptr">(UE::DerivedData::Private::FBuildSessionInternal*)Session.Ptr</ExpandedItem>
    </Expand>
  </Type>

  <!-- TSlateAttribute -->
  <Type Name="SWidget::TSlateAttribute&lt;*&gt;">
    <DisplayString>{Value}</DisplayString>
  </Type>
  <Type Name="SWidget::TSlateManagedAttribute&lt;*&gt;">
    <DisplayString>{Value}</DisplayString>
  </Type>
  <Type Name="TWidgetSlotWithAttributeSupport&lt;*&gt;::TSlateSlotAttribute&lt;*&gt;">
    <DisplayString>{Value}</DisplayString>
  </Type>

  <!-- SWidget -->
  <Type Name="SWidget" Inheritable="false">
    <DisplayString>{{Type={TypeOfWidget}}}</DisplayString>
    <Expand>
      <Item Name="[Tag]" Condition="Tag.ComparisonIndex.Value != 0">Tag</Item>
      <Item Name="[Created In]">CreatedInLocation</Item>
      <Item Name="[Visibility]">VisibilityAttribute</Item>
      <Item Name="[Enabled]">EnabledStateAttribute</Item>
      <Item Name="[DesiredSize]" Condition="DesiredSize.bIsSet">DesiredSize.Value</Item>
      <Item Name="[ParentWidget]">ParentWidgetPtr</Item>
      <Item Name="[ActiveTimers]" Condition="ActiveTimers.ArrayNum>0">ActiveTimers</Item>
      <Item Name="[Attributes]" Condition="bHasRegisteredSlateAttribute">((TSharedRef&lt;FSlateAttributeMetaData,1&gt;*)MetaData.AllocatorInstance.Data)->Object->Attributes</Item>
    </Expand>
  </Type>

  <!-- FSlateAttributeMetaData -->
  <Type Name="FSlateAttributeMetaData">
    <DisplayString Condition="AffectVisibilityCounter>0">{{Num={Attributes.ArrayNum} AffectVisibility}}</DisplayString>
    <DisplayString>{{Num={Attributes.ArrayNum}}}</DisplayString>
  </Type>
  <Type Name="FSlateAttributeMetaData::FGetterItem">
    <DisplayString Condition="CachedAttributeDescriptor!=0">{{Name={CachedAttributeDescriptor->Name}}}</DisplayString>
    <DisplayString></DisplayString>
  </Type>

  <!-- TGuardedInt visualizer -->
  <Type Name="TGuardedInt&lt;*&gt;">
    <DisplayString Condition="!bIsValid">Invalid</DisplayString>
    <DisplayString Condition="bIsValid">Valid: {{{Value}}}</DisplayString>
    <Expand>
      <ExpandedItem Condition="bIsValid">Value</ExpandedItem>
    </Expand>
  </Type>

  <!-- FEditPropertyChain visualizer -->
  <Type Name="FEditPropertyChain">
    <DisplayString>{{Size = {ListSize}}}</DisplayString>
    <Expand>
      <LinkedListItems>
        <Size>ListSize</Size>
        <HeadPointer>HeadNode</HeadPointer>
        <NextPointer>NextNode</NextPointer>
        <ValueNode>Value</ValueNode>
      </LinkedListItems>
      <Item Condition="ActivePropertyNode == nullptr" Name="ActivePropertyNode">"nullptr"</Item>
      <Item Condition="ActivePropertyNode != nullptr" Name="ActivePropertyNode">ActivePropertyNode->Value</Item>
      <Item Condition="ActiveMemberPropertyNode == nullptr" Name="ActiveMemberPropertyNode">"nullptr"</Item>
      <Item Condition="ActiveMemberPropertyNode != nullptr" Name="ActiveMemberPropertyNode">ActiveMemberPropertyNode->Value</Item>
      <Item Name="AffectedInstances">AffectedInstances</Item>
    </Expand>
  </Type>

  <!-- FObjectPropertyNode visualizer -->
  <Type Name="FObjectPropertyNode">
    <DisplayString>{{Object={*BaseClass}}}</DisplayString>
  </Type>

  <!-- FStructurePropertyNode visualizer -->
  <Type Name="FStructurePropertyNode">
    <DisplayString>{{Struct={*StructData.Object->ScriptStruct}}}</DisplayString>
  </Type>

  <!-- FCategoryPropertyNode visualizer -->
  <Type Name="FCategoryPropertyNode">
    <DisplayString>{{Category={CategoryName}}}</DisplayString>
  </Type>
  
  <!-- FPropertyNode visualizer -->
  <Type Name="FPropertyNode">
    <DisplayString Condition="PropertyPath.Data.AllocatorInstance.Data !=0">{{Property={PropertyPath.Data.AllocatorInstance.Data,sub}}}</DisplayString>
    <DisplayString Condition="Property.Field.ResolvedField != 0">{{Property={Property.Field}}}</DisplayString>
  </Type>

  <!-- FPropertyHandleBase visualizer -->
  <Type Name="FPropertyHandleBase">
    <DisplayString>{*Implementation.Object->PropertyNode.Object}</DisplayString>
    <Expand>
      <ExpandedItem>*Implementation.Object</ExpandedItem>
    </Expand>
  </Type>
  
  <!-- FDetailPropertyRow visualizer -->
  <Type Name="FDetailPropertyRow">
    <DisplayString>{*PropertyNode.Object}</DisplayString>
  </Type>
  
  <!-- FDetailGroup visualizer -->
  <Type Name="FDetailGroup">
    <DisplayString>{{{GroupName}}}</DisplayString>
  </Type>
  
  <!-- FDetailWidgetRow visualizer -->
  <Type Name="FDetailWidgetRow">
    <DisplayString Condition="RowTagName.ComparisonIndex.Value == 0 &amp;&amp; RowTagName.Number == 0">{{Custom Widget}}</DisplayString>
    <DisplayString>{{{RowTagName}}}</DisplayString>
  </Type>

  <!-- FDetailCustomBuilderRow visualizer -->
  <Type Name="FDetailCustomBuilderRow">
    <DisplayString>{{{OriginalPath,sub}}}</DisplayString>
  </Type>

  <!-- FDetailItemNode visualizer -->
  <Type Name="FDetailItemNode">
    <DisplayString>{{{Customization}}}</DisplayString>
  </Type>

  <!-- FDetailCategoryImpl visualizer -->
  <Type Name="FDetailCategoryImpl">
    <DisplayString>{{Category: {CategoryName}}}</DisplayString>
  </Type>

  <!-- FDetailCustomization visualizer -->
  <Type Name="FDetailLayoutCustomization">
    <DisplayString Condition="PropertyRow.Object != 0">{{Property: {*PropertyRow.Object}}}</DisplayString>
    <DisplayString Condition="DetailGroup.Object != 0">{{Group: {*DetailGroup.Object}}}</DisplayString>
    <DisplayString Condition="WidgetDecl.Object != 0">{{Custom Widget: {*WidgetDecl.Object}}}</DisplayString>
    <DisplayString Condition="CustomBuilderRow.Object != 0">{{Custom Builder: {*CustomBuilderRow.Object}}}</DisplayString>
    <DisplayString>Invalid</DisplayString>
    <Expand>
      <ExpandedItem Condition="PropertyRow.Object != 0">*PropertyRow.Object</ExpandedItem>
      <ExpandedItem Condition="DetailGroup.Object != 0">*DetailGroup.Object</ExpandedItem>
      <ExpandedItem Condition="WidgetDecl.Object != 0">*WidgetDecl.Object</ExpandedItem>
      <ExpandedItem Condition="CustomBuilderRow.Object != 0">*CustomBuilderRow.Object</ExpandedItem>
    </Expand>
  </Type>

  <!-- TOnlineIdHandle visualizer -->
  <Type Name="UE::Online::TOnlineId&lt;*&gt;">
    <DisplayString Condition="Value == 0">Invalid</DisplayString>
    <DisplayString Condition="Value > 0">{{Type={(UE::Online::EOnlineServices)(Value >> 24)} Handle={Value &amp; 0x00FFFFFF}}}</DisplayString>
    <Expand>
      <Item Name="[Type]" Condition="Value > 0">(UE::Online::EOnlineServices)(Value >> 24)</Item>
      <Item Name="[Handle]" Condition="Value > 0">Value &amp; 0x00FFFFFF</Item>
      <Item Name="[String]" Condition="Value > 0">UE::Online::ToLogString(*this)</Item>
    </Expand>
  </Type>

  <!-- FReferenceToken visualizer -->
  <Type Name="FReferenceToken">
    <DisplayString Condition="EncodedBits == EReferenceTokenType::None">None</DisplayString>
    <DisplayString Condition="EncodedBits == EReferenceTokenType::Barrier">Barrier</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::Object">{{UObject: {*((UObjectBase*)(EncodedBits &amp; ~EncodingBits))}}}</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCObject">{{FGCObject: {*((FGCObject*)(EncodedBits &amp; ~EncodingBits))}}}</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::VerseCell">{{Verse::VCell: {*((Verse::VCell*)(EncodedBits &amp; ~EncodingBits))}}}</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCObjectInfo">{{FGCObjectInfo: {*((FGCObjectInfo*)(EncodedBits &amp; ~EncodingBits))}}}</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCVerseCellInfo">{{FGCVerseCellInfo: {*((FGCVerseCellInfo*)(EncodedBits &amp; ~EncodingBits))}}}</DisplayString>
    <DisplayString>???</DisplayString>
    <Expand>
      <ExpandedItem Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::Object">*((UObject*)(EncodedBits &amp; ~EncodingBits))</ExpandedItem>
      <ExpandedItem Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCObject">*((FGCObject*)(EncodedBits &amp; ~EncodingBits))</ExpandedItem>
      <ExpandedItem Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::VerseCell">*((Verse::VCell*)(EncodedBits &amp; ~EncodingBits))</ExpandedItem>
      <ExpandedItem Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCObjectInfo">*((FGCObjectInfo*)(EncodedBits &amp; ~EncodingBits))</ExpandedItem>
      <ExpandedItem Condition="(EncodedBits &amp; EncodingBits) == EReferenceTokenType::GCVerseCellInfo">*((FGCVerseCellInfo*)(EncodedBits &amp; ~EncodingBits))</ExpandedItem>
    </Expand>
  </Type>

  <!-- AActor visualizer -->
  <Type Name="AActor">
	<DisplayString Condition="ActorLabel.Data.ArrayNum != 0" >(Label={ActorLabel},Name={NamePrivate})</DisplayString>
    <DisplayString>(Name={NamePrivate})</DisplayString>
  </Type>

  <Type Name="UActorComponent">
    <DisplayString>(Name = {NamePrivate}, Owner = {OwnerPrivate})</DisplayString>
  </Type>

  <Type Name="FWorldPartitionActorDescView">
    <DisplayString>(Name = {ActorDesc.ActorLabel})</DisplayString>
  </Type>

  <!-- FAttributeStorage visualizer-->
  <Type Name="UE::Interchange::FAttributeStorage">
    <DisplayString Condition="AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &lt; 0 || AttributeAllocationTable.Pairs.Elements.NumFreeIndices &lt; 0 || (AttributeAllocationTable.Pairs.Elements.Data.ArrayNum - AttributeAllocationTable.Pairs.Elements.NumFreeIndices) &lt; 0 || AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &gt; AttributeAllocationTable.Pairs.Elements.Data.ArrayMax">Invalid</DisplayString>
    <DisplayString Condition="(AttributeAllocationTable.Pairs.Elements.Data.ArrayNum - AttributeAllocationTable.Pairs.Elements.NumFreeIndices) == 0">Empty</DisplayString>
    <DisplayString Condition="(AttributeAllocationTable.Pairs.Elements.Data.ArrayNum - AttributeAllocationTable.Pairs.Elements.NumFreeIndices) &gt; 0">Num={AttributeAllocationTable.Pairs.Elements.Data.ArrayNum - AttributeAllocationTable.Pairs.Elements.NumFreeIndices}</DisplayString>
    <Expand>
      <CustomListItems MaxItemsPerView="100">
        <Variable Name="ItemIndex" InitialValue="0"/>
        <Variable Name="ItemType" InitialValue="0"/>
        <Variable Name="ItemAllocationInfo" InitialValue="(const UE::Interchange::FAttributeStorage::FAttributeAllocationInfo*)nullptr"/>
        <Variable Name="ItemKey" InitialValue="(const UE::Interchange::FAttributeKey*)nullptr"/>
        <Variable Name="CanDisplayItem" InitialValue="false"/>
        <Variable Name="ValidOffset" InitialValue="false"/>
        <Variable Name="ItemSize" InitialValue="0"/>
        <!-- This is the max number of different type supported by the attribute storage, see UE::Interchange::EAttributeTypes enum -->
        <Variable Name="AttributeTypeCount" InitialValue="58"/>
        <Variable Name="ItemCount" InitialValue="(AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &gt;= 0 &amp;&amp; AttributeAllocationTable.Pairs.Elements.NumFreeIndices &gt;= 0 &amp;&amp; AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &lt;= AttributeAllocationTable.Pairs.Elements.Data.ArrayMax) ? AttributeAllocationTable.Pairs.Elements.Data.ArrayNum : 0" />
        <Size>(AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &gt;= 0 &amp;&amp; AttributeAllocationTable.Pairs.Elements.NumFreeIndices &gt;= 0 &amp;&amp; AttributeAllocationTable.Pairs.Elements.Data.ArrayNum &lt;= AttributeAllocationTable.Pairs.Elements.Data.ArrayMax) ? AttributeAllocationTable.Pairs.Elements.Data.ArrayNum : 0</Size>
        <Loop Condition="ItemIndex &lt; ItemCount">
          <If Condition="((AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data)[ItemIndex/32]&gt;&gt;(ItemIndex%32) &amp; 1) == 0) || ((AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.InlineData)[ItemIndex/32]&gt;&gt;(ItemIndex%32) &amp; 1) == 0)">
            <Item Name="[{ItemIndex}]">"Invalid (Item was removed)"</Item>
          </If>
          <If Condition="((AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data != 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data)[ItemIndex/32]&gt;&gt;(ItemIndex%32) &amp; 1) != 0) || ((AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.SecondaryData.Data == 0) &amp;&amp; (reinterpret_cast&lt;uint32*&gt;(AttributeAllocationTable.Pairs.Elements.AllocationFlags.AllocatorInstance.InlineData)[ItemIndex/32]&gt;&gt;(ItemIndex%32) &amp; 1) != 0)">
            <Exec>ItemAllocationInfo = (const FAttributeAllocationInfo*)&amp;(((TSetElement&lt;TTuple&lt;UE::Interchange::FAttributeKey,UE::Interchange::FAttributeStorage::FAttributeAllocationInfo&gt; &gt; *)AttributeAllocationTable.Pairs.Elements.Data.AllocatorInstance.Data)[ItemIndex].Value.Value)</Exec>
            <Exec>ItemKey = (UE::Interchange::FAttributeKey*)&amp;(((TSetElement&lt;TTuple&lt;UE::Interchange::FAttributeKey,UE::Interchange::FAttributeStorage::FAttributeAllocationInfo&gt; &gt; *)AttributeAllocationTable.Pairs.Elements.Data.AllocatorInstance.Data)[ItemIndex].Value.Key)</Exec>
            <Exec>CanDisplayItem = ItemKey != nullptr &amp;&amp; ItemAllocationInfo != nullptr</Exec>
            <Exec>ValidOffset = CanDisplayItem &amp;&amp; AttributeStorage.ArrayNum &gt; ItemAllocationInfo-&gt;Offset</Exec>
            <If Condition="ItemKey == nullptr">
              <Item Name="[{ItemIndex}]">"Invalid Key"</Item>
            </If>
            <If Condition="ItemKey != nullptr &amp;&amp; ItemAllocationInfo == nullptr">
              <Item Name="[{ItemIndex}] ({(*ItemKey).Key})">"Invalid Allocation Table"</Item>
            </If>
            <If Condition="CanDisplayItem &amp;&amp; !ValidOffset">
              <Item Name="[{ItemIndex}] ({(*ItemKey).Key})">"Invalid Item Offset"</Item>
            </If>
            <If Condition="CanDisplayItem &amp;&amp; ValidOffset">
              <Exec>ItemSize = ItemAllocationInfo-&gt;Size</Exec>
              <Exec>ItemType = ItemAllocationInfo-&gt;Type</Exec>
              <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition ="ItemType &lt;= 0 &amp;&amp; ItemType &gt;= AttributeTypeCount">"Invalid Item Type"</Item>
              <If Condition="ItemType &gt; 0 &amp;&amp; ItemType &lt; AttributeTypeCount">
                <!-- Use the UE::Interchange::EAttributeTypes has the condition to make the proper typecast -->
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 1">*(bool*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (TArray&lt;uint8&gt;)" Condition="ItemType == 2">
                  <!-- TArray<uint8> type, only the buffer bytes are stored in the storage it cannot be cast to TArray<uint8>. We display only the number of byte -->
                  ItemSize
                </Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (TArray64&lt;uint8&gt;)" Condition="ItemType == 3">
                  <!-- TArray64<uint8> type, only the buffer bytes are stored in the storage it cannot be cast to TArray64<uint8>. We display only the number of byte -->
                  ItemSize
                </Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 4">*(FColor*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 5">*(FDateTime*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 6">*(double*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (Enum)" Condition="ItemType == 7">*(uint8*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 8">*(float*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 9">*(FGuid*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 10">*(int8*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 11">*(int16*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 12">*(int32*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 13">*(int64*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 14">*(FIntRect*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 15">*(FLinearColor*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (FName)" Condition="ItemType == 16">
                  <!-- FName type is store has characters in the storage it cannot be cast to FName -->
                  (WIDECHAR*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset]),na
                </Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 17">*(FRandomStream*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (FString)" Condition="ItemType == 18">
                  <!-- FString type is store has characters in the storage it cannot be cast to FString -->
                  (WIDECHAR*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset]),na
                </Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 19">*(FTimespan*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 20">*(FTwoVectors*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 21">*(uint8*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 22">*(uint16*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 23">*(uint32*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 24">*(uint64*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 25">*(FVector2D*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 26">*(FIntPoint*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 27">*(FIntVector*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 28">*(FVector2DHalf*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 29">*(FFloat16*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 30">*(FOrientedBox*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 31">*(FFrameNumber*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 32">*(FFrameRate*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 33">*(FFrameTime*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key}) (FSoftObjectPath)" Condition="ItemType == 34">
                  <!-- FSoftObjectPath type is store has characters in the storage it cannot be cast to FSoftObjectPath -->
                  (WIDECHAR*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset]),na
                </Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 35">*(FMatrix44f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 36">*(FMatrix44d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 37">*(FPlane4f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 38">*(FPlane4d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 39">*(FQuat4f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 40">*(FQuat4d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 41">*(FRotator3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 42">*(FRotator3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 43">*(FTransform3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 44">*(FTransform3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 45">*(FVector3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 46">*(FVector3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 47">*(FVector2f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 48">*(FVector4f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 49">*(FVector4d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 50">*(FBox2f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 51">*(FBox2D*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 52">*(FBox3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 53">*(FBox3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 54">*(FBoxSphereBounds3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 55">*(FBoxSphereBounds3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 56">*(FSphere3f*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
                <Item Name="[{ItemIndex}] ({(*ItemKey).Key})" Condition="ItemType == 57">*(FSphere3d*)&amp;(((uint8*)AttributeStorage.AllocatorInstance.Data)[ItemAllocationInfo-&gt;Offset])</Item>
              </If>
            </If>
          </If>
          <Exec>++ItemIndex</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <Type Name="UE::BulkData::Private::FBulkMetaData">
   <Expand>
      <Item Name="Size">Data[0] | (Data[1] &lt;&lt; 8) | (Data[2] &lt;&lt; 16) | (Data[3] &lt;&lt; 24) | (Data[4] &lt;&lt; 32)</Item>
      <Item Name="Offset">Data[5] | (Data[6] &lt;&lt; 8) | (Data[7] &lt;&lt; 16) | (Data[8] &lt;&lt; 24) | (Data[9] &lt;&lt; 32)</Item>
      <Item Name="Offset" Condition="(Data[5] | (Data[6] &lt;&lt; 8) | (Data[7] &lt;&lt; 16) | (Data[8] &lt;&lt; 24) | (Data[9] &lt;&lt; 32)) &gt; 0xFFFFFFFFE">-1</Item>
      <Item Name="LockFlags">(EBulkDataLockStatus)Data[11]</Item>
      <Item Name="BulkDataFlags">*(EBulkDataFlags*)&amp;Data[12]</Item>
      <Item Name="SizeOnDisk" Condition="sizeof(UE::BulkData::Private::FBulkMetaData) == 24">*(int64*)&amp;Data[16]</Item>
    </Expand>
  </Type>
  
  <Type Name="UE::BulkData::Private::FBulkDataChunkId">
    <DisplayString>{ImplPtr}</DisplayString>
  </Type>
  
  <Type Name="UE::BulkData::Private::FBulkDataChunkId::FImpl">
    <DisplayString>{PathOrId}</DisplayString>
  </Type>

  <!-- FSdfPathImpl visualizer -->
  <Type Name="UE::FSdfPath">
    <Intrinsic Name="SdfPathImpl" Expression="(UnrealEditor-UnrealUSDWrapper.dll!UE::Internal::FSdfPathImpl*)(this->Impl.Ptr)"/>
    <DisplayString>(SdfPath={SdfPathImpl()->DebugPath})</DisplayString>
  </Type>

  <Type Name="UE::Internal::FSdfPathImpl">
    <Expand>
        <ExpandedItem>(UnrealEditor-UnrealUSDWrapper.dll!UE::Internal::FSdfPathImpl*)this,!</ExpandedItem>
    </Expand>
  </Type>

  <!-- FUsdPrimImpl visualizer -->
  <Type Name="UE::FUsdPrim">
    <Intrinsic Name="PrimImpl" Expression="(UnrealEditor-UnrealUSDWrapper.dll!UE::Internal::FUsdPrimImpl*)(this->Impl.Ptr)"/>
    <DisplayString>(PrimPath={PrimImpl()->DebugPath})</DisplayString>
  </Type>

  <Type Name="UE::Internal::FUsdPrimImpl">
    <Expand>
        <ExpandedItem>(UnrealEditor-UnrealUSDWrapper.dll!UE::Internal::FUsdPrimImpl*)this,!</ExpandedItem>
    </Expand>
  </Type>

  <Type Name="FActorPrimitiveComponentInterface">
    <DisplayString>ComponentInterface</DisplayString>
    <Expand>
      <!--Item Name ="Offset">(size_t)&amp;((UPrimitiveComponent*)0)->PrimitiveComponentInterface</Item-->
      <Item Name ="Component">(UPrimitiveComponent*)(((char*)this)-((size_t)&amp;((UPrimitiveComponent*)0)->PrimitiveComponentInterface))</Item>      
    </Expand>
  </Type>

 <Type Name="FInstanceCullingLoadBalancerBase::FPackedBatch">
    <DisplayString>F={FirstItem_NumItems >> FInstanceCullingLoadBalancerBase::NumInstancesItemBits} N={FirstItem_NumItems &amp; FInstanceCullingLoadBalancerBase::NumInstancesItemMask} </DisplayString>
    <Expand>
      <Item Name ="FirstItem">FirstItem_NumItems >> FInstanceCullingLoadBalancerBase::NumInstancesItemBits</Item>      
      <Item Name ="NumItems">FirstItem_NumItems &amp; FInstanceCullingLoadBalancerBase::NumInstancesItemMask</Item>      
    </Expand>
  </Type>

 <Type Name="FInstanceCullingLoadBalancerBase::FPackedItem">
    <DisplayString>O={InstanceDataOffset_NumInstances >> FInstanceCullingLoadBalancerBase::NumInstancesItemBits} N={InstanceDataOffset_NumInstances &amp; FInstanceCullingLoadBalancerBase::NumInstancesItemMask}
	 P={Payload_BatchPrefixOffset >> FInstanceCullingLoadBalancerBase::PrefixBits} B={Payload_BatchPrefixOffset &amp; FInstanceCullingLoadBalancerBase::PrefixBitMask}
	</DisplayString>
    <Expand>
      <Item Name ="InstanceDataOffset">InstanceDataOffset_NumInstances >> FInstanceCullingLoadBalancerBase::NumInstancesItemBits</Item>      
      <Item Name ="NumInstances">InstanceDataOffset_NumInstances &amp; FInstanceCullingLoadBalancerBase::NumInstancesItemMask</Item>      
      <Item Name ="Payload">Payload_BatchPrefixOffset >> FInstanceCullingLoadBalancerBase::PrefixBits</Item>      
      <Item Name ="BatchPrefixSum">Payload_BatchPrefixOffset &amp; FInstanceCullingLoadBalancerBase::PrefixBitMask</Item>      
    </Expand>
  </Type>


 <Type Name="FActorStaticMeshComponentInterface">
    <DisplayString>ComponentInterface</DisplayString>
    <Expand>
      <!--Item Name ="Offset">(size_t)&amp;((UStaticMeshComponent*)0)->StaticMeshComponentInterface</Item-->
      <Item Name ="Component">(UStaticMeshComponent*)(((char*)this)-((size_t)&amp;((UStaticMeshComponent*)0)->PrimitiveComponentInterface))</Item>      
    </Expand>
  </Type>

  <!-- FShaderCompilerDefinitions visualizer -->
  <Type Name="FShaderCompilerDefinitions">
    <DisplayString Condition="Pairs.ArrayMax &lt;= 0">Empty</DisplayString>
    <DisplayString Condition="ValueCount &lt;= 0">Empty, Max={Pairs.ArrayMax}</DisplayString>
    <DisplayString Condition="ValueCount &gt; 0">Num={ValueCount}, Max={Pairs.ArrayMax}</DisplayString>
    <Expand>
      <CustomListItems>
        <Variable Name="Index" InitialValue="0" />
        <Size>ValueCount</Size>
        <Loop Condition="Index &lt; ValueTypes.ArrayNum">
          <If Condition="((uint8*)ValueTypes.AllocatorInstance.Data)[Index] == 1">
            <Item Name="[{Index}]  {((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].Key}">((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].ValueInteger</Item>
          </If>
          <If Condition="((uint8*)ValueTypes.AllocatorInstance.Data)[Index] == 2">
            <Item Name="[{Index}]  {((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].Key}">((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].ValueUnsigned</Item>
          </If>
          <If Condition="((uint8*)ValueTypes.AllocatorInstance.Data)[Index] == 3">
            <Item Name="[{Index}]  {((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].Key}">((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].ValueFloat</Item>
          </If>
          <If Condition="((uint8*)ValueTypes.AllocatorInstance.Data)[Index] == 4">
            <Item Name="[{Index}]  {((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].Key}">
              ((FString*)StringValues.AllocatorInstance.Data)[((FShaderCompilerDefinitions::FPairType*)Pairs.AllocatorInstance.Data)[Index].ValueInteger]
            </Item>
          </If>
          <Exec>++Index</Exec>
        </Loop>
      </CustomListItems>
    </Expand>
  </Type>

  <!-- FInstancedStructContainer visualizer -->
  <Type Name="FInstancedStructContainer">
    <DisplayString Condition="NumItems == 0">Empty</DisplayString>
    <DisplayString Condition="NumItems &gt; 0">{{ NumItems={NumItems} }}</DisplayString>
    <Expand>
      <IndexListItems Condition="NumItems != 0">
        <Size>NumItems</Size>
        <ValueNode>(FInstancedStructContainer::FItem*)(Memory + AllocatedSize - ($i + 1) * ItemStride)</ValueNode>
      </IndexListItems>
    </Expand>
  </Type>

  <!-- TInstancedStruct visualizer -->
  <Type Name="TInstancedStruct&lt;*&gt;">
    <DisplayString Condition="InstancedStruct.ScriptStruct == nullptr">Empty</DisplayString>
    <DisplayString Condition="InstancedStruct.ScriptStruct != nullptr">{InstancedStruct.ScriptStruct->NamePrivate}</DisplayString>
    <Expand>
      <Item Name="[Type]">InstancedStruct.ScriptStruct</Item>
      <Item Name="[Value]">($T1*)InstancedStruct.StructMemory</Item>
    </Expand>
  </Type>

  <!-- Epic Games, Inc. Verse VM Visualizers -->

  <!-- VValue -->
  <Type Name="Verse::VValue">
    <DisplayString Condition="EncodedBits == UninitializedValue">Uninitialized</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; NonCellTagMask) == RootTag">Root(SplitDepth = {(EncodedBits >> 32) &amp; 0xffff})</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; NonCellTagMask) == PlaceholderTag">Placeholder</DisplayString>
    <DisplayString Condition="!(EncodedBits &amp; NonCellTagMask) &amp;&amp; EncodedBits != UninitializedValue">{Cell}</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; NumberTagMask) == Int32Tag">int32({Bits.Payload})</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; NumberTagMask) &amp;&amp; (EncodedBits &amp; NumberTagMask) != Int32Tag">double({EncodedBits - FloatOffset,X})</DisplayString>
    <DisplayString Condition="(EncodedBits &amp; NonCellTagMask) == UObjectTag">UObject (Name={((UObjectBase*)(EncodedBits &amp; ~UObjectTag))->NamePrivate})</DisplayString>
    <DisplayString>???</DisplayString>
    <Expand>
      <ExpandedItem Condition="(EncodedBits &amp; NonCellTagMask) == PlaceholderTag">(Verse::VPlaceholder*)((uintptr_t)Cell &amp; ~NonCellTagMask)</ExpandedItem>
      <ExpandedItem Condition="(EncodedBits &amp; NonCellTagMask) == UObjectTag">((UObject*)(EncodedBits &amp; ~UObjectTag))</ExpandedItem>
      <ExpandedItem Condition="!(EncodedBits &amp; NonCellTagMask) &amp;&amp; Cell != 0">Cell</ExpandedItem>
    </Expand>
  </Type>

  <!-- VPlaceholder -->
  <Type Name="Verse::VPlaceholder">
    <DisplayString>Placeholder ({Value}))</DisplayString>
  </Type>

  <!-- TWriteBarrier -->
  <Type Name="Verse::TWriteBarrier&lt;*&gt;">
    <DisplayString>{Value}</DisplayString>
    <Expand>
      <ExpandedItem>Value</ExpandedItem>
    </Expand>
  </Type>

  <!-- TGlobalHeapPtr -->
  <Type Name="Verse::TGlobalHeapPtr&lt;*&gt;">
    <DisplayString>{Impl.Data._Storage._Value->Value}</DisplayString>
    <Expand>
      <Item Name="Value">Impl.Data._Storage._Value->Value</Item>
    </Expand>
  </Type>

  <!-- TNeverDestroyed<*> -->
  <Type Name="Verse::TNeverDestroyed&lt;*&gt;">
    <DisplayString>{*($T1*)this}</DisplayString>
    <Expand>
      <ExpandedItem>*($T1*)this</ExpandedItem>
    </Expand>
  </Type>

  <!-- VNameValueMap -->
  <Type Name="Verse::VNameValueMap">
    <DisplayString>{NameAndValues.Value}</DisplayString>
    <Expand>
      <ExpandedItem>NameAndValues.Value</ExpandedItem>
    </Expand>
  </Type>

  <!-- VBuffer -->
  <Type Name="Verse::VBuffer">
    <DisplayString Condition="((Header*)Ptr)->Type == EArrayType::None">None[{((Header*)Ptr)->NumValues,d}]</DisplayString>
    <DisplayString Condition="((Header*)Ptr)->Type == EArrayType::VValue">VValue[{((Header*)Ptr)->NumValues,d}]</DisplayString>
    <DisplayString Condition="((Header*)Ptr)->Type == EArrayType::Int32">Int32[{((Header*)Ptr)->NumValues,d}]</DisplayString>
    <DisplayString Condition="((Header*)Ptr)->Type == EArrayType::Char8">Char8[{((Header*)Ptr)->NumValues,d}] {(char*)((Header*)Ptr + 1),s8}</DisplayString>
    <DisplayString Condition="((Header*)Ptr)->Type == EArrayType::Char32">Char32[{((Header*)Ptr)->NumValues,d}]</DisplayString>
    <Expand>
      <ArrayItems Condition="((Header*)Ptr)->Type != EArrayType::None">
        <Size>((Header*)Ptr)->NumValues</Size>
        <ValuePointer Condition="((Header*)Ptr)->Type == EArrayType::VValue">(VValue*)((Header*)Ptr + 1)</ValuePointer>
        <ValuePointer Condition="((Header*)Ptr)->Type == EArrayType::Int32">(int32*)((Header*)Ptr + 1)</ValuePointer>
        <ValuePointer Condition="((Header*)Ptr)->Type == EArrayType::Char8">(uint8*)((Header*)Ptr + 1)</ValuePointer>
        <ValuePointer Condition="((Header*)Ptr)->Type == EArrayType::Char32">(uint32*)((Header*)Ptr + 1)</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- VCell (keep subtypes in alphabetical order, and make sure each has a visualizer below, even if empty) -->
  <Type Name="Verse::VCell">
    <DisplayString Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VArray::StaticCppClassInfo">Cell ({*(Verse::VArray*)this})</DisplayString>
    <DisplayString>Cell ({((VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo->Name,sub})</DisplayString>
    <Expand>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VArray::StaticCppClassInfo">(Verse::VArray*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VClass::StaticCppClassInfo">(Verse::VClass*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VConstructor::StaticCppClassInfo">(Verse::VConstructor*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VFrame::StaticCppClassInfo">(Verse::VFrame*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VFunction::StaticCppClassInfo">(Verse::VFunction*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VHeapInt::StaticCppClassInfo">(Verse::VHeapInt*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VMap::StaticCppClassInfo">(Verse::VMap*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VMutableArray::StaticCppClassInfo">(Verse::VMutableArray*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VMutableMap::StaticCppClassInfo">(Verse::VMutableMap*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VNativeFunction::StaticCppClassInfo">(Verse::VNativeFunction*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VNativeRef::StaticCppClassInfo">(Verse::VNativeRef*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VOption::StaticCppClassInfo">(Verse::VOption*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VPackage::StaticCppClassInfo">(Verse::VPackage*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VProcedure::StaticCppClassInfo">(Verse::VProcedure*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VProgram::StaticCppClassInfo">(Verse::VProgram*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VRational::StaticCppClassInfo">(Verse::VRational*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VScope::StaticCppClassInfo">(Verse::VScope*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VShape::StaticCppClassInfo">(Verse::VShape*)this</ExpandedItem>
      <ExpandedItem Condition="((Verse::VEmergentType*)((char*)Verse::FHeap::EmergentTypeBase + EmergentTypeOffset * Verse::FHeap::EmergentAlignment))->CppClassInfo == &amp;Verse::VVar::StaticCppClassInfo">(Verse::VVar*)this</ExpandedItem>
    </Expand>
  </Type>

  <!-- VArrayBase -->
  <Type Name="Verse::VArrayBase">
    <DisplayString>{Buffer.Value}</DisplayString>
    <Expand>
      <ExpandedItem>Buffer.Value</ExpandedItem>
    </Expand>
  </Type>

  <!-- VClass -->
  <Type Name="Verse::VClass">
  </Type>

  <!-- VConstructor -->
  <Type Name="Verse::VConstructor">
    <DisplayString>[{NumEntries,d}]</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>NumEntries</Size>
        <ValuePointer>Entries</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- VFrame -->
  <Type Name="Verse::VFrame">
    <Expand>
      <ArrayItems>
        <Size>NumRegisters</Size>
        <ValuePointer>Registers</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- VFunction -->
  <Type Name="Verse::VFunction">
  </Type>

  <!-- VHeapInt -->
  <Type Name="Verse::VHeapInt">
    <DisplayString>[{Length,d}]</DisplayString>
    <Expand>
      <ArrayItems>
        <Size>Length</Size>
        <ValuePointer>Digits</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- VMapBase -->
  <Type Name="Verse::VMapBase">
    <Expand>
      <ExpandedItem>InternalMap</ExpandedItem>
    </Expand>
  </Type>

  <!-- VNativeFunction -->
  <Type Name="Verse::VNativeFunction">
  </Type>

  <!-- VNativeRef -->
  <Type Name="Verse::VNativeRef">
  </Type>

  <!-- VOption -->
  <Type Name="Verse::VOption">
    <DisplayString>Option ({Value}))</DisplayString>
  </Type>

  <!-- VPackage -->
  <Type Name="Verse::VPackage">
    <DisplayString>[{*PackageName.Value} - {((VBuffer::Header*)Map.NameAndValues.Value->Buffer.Value.Ptr)->NumValues / 2,d}]</DisplayString>
  </Type>

  <!-- VProcedure -->
  <Type Name="Verse::VProcedure">
    <Expand>
      <ArrayItems>
        <Size>NumConstants</Size>
        <ValuePointer>Constants</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <!-- VProgram -->
  <Type Name="Verse::VProgram">
    <DisplayString>[{((VBuffer::Header*)Map.NameAndValues.Value->Buffer.Value.Ptr)->NumValues / 2,d}]</DisplayString>
  </Type>

  <!-- VRational -->
  <Type Name="Verse::VRational">
    <DisplayString>{Numerator.Value}/{Denominator.Value}</DisplayString>
    <Expand>
      <Item Name="Numerator">Numerator.Value</Item>
      <Item Name="Denominator">Denominator.Value</Item>
    </Expand>
  </Type>

  <!-- VScope -->
  <Type Name="Verse::VScope">
  </Type>

  <!-- VShape -->
  <Type Name="Verse::VShape">
  </Type>

  <!-- VVar -->
  <Type Name="Verse::VVar">
  </Type>

</AutoVisualizer>
