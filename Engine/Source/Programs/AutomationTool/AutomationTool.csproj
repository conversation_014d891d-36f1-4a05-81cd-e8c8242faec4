<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\Shared\UnrealEngine.csproj.props" />
  
  <PropertyGroup>
    <IsWindows Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))' == 'true'">true</IsWindows> 
    <IsOSX Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::OSX)))' == 'true'">true</IsOSX> 
    <IsLinux Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Linux)))' == 'true'">true</IsLinux> 
  </PropertyGroup>
  
  <Choose>
    <When Condition="'$(IsWindows)'=='true'">
      <PropertyGroup>
        <TargetFramework>net8.0-windows10.0.19041</TargetFramework>
        <UseWindowsForms>true</UseWindowsForms>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
      </PropertyGroup>
    </Otherwise>
  </Choose>
  
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Development</Configuration>
    <OutputType>Exe</OutputType>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <Configurations>Debug;Release;Development;Analyze</Configurations>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <RootNamespace>AutomationTool</RootNamespace>
    <AssemblyName>AutomationTool</AssemblyName>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
    <NoWarn>$(NoWarn);NETSDK1206</NoWarn>
    <OutputPath>..\..\..\Binaries\DotNET\AutomationTool</OutputPath>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages> <!-- remove non english resource languages -->
    <DebugType>pdbonly</DebugType>
    <DebugType Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))' == 'true' And '$([System.Runtime.InteropServices.RuntimeInformation]::OSArchitecture)' == 'Arm64'">portable</DebugType>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks> <!-- required for FileMatcher support code in MSBuild subdir -->
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Development|AnyCPU' ">
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Analyze|AnyCPU' ">
    <RunAnalyzersDuringBuild>True</RunAnalyzersDuringBuild>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile></DocumentationFile>
  </PropertyGroup>
  
  <ItemGroup>
    <Reference Include="Ionic.Zip.Reduced">
      <HintPath>..\..\..\Binaries\DotNET\Ionic.Zip.Reduced.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="fastJSON">
      <HintPath>..\..\..\Binaries\ThirdParty\fastJSON\netstandard2.0\fastJSON.dll</HintPath>
      <Private>true</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="'$(IsWindows)'=='true'">
      <PropertyGroup>
        <!-- Reset output type back to a console application as the import of WindowsDesktop sets it to a windows executable -->
        <OutputType>Exe</OutputType>
      </PropertyGroup>
    </When>
  </Choose>

  <!-- This set of dependencies should be kept as small as possible
       This list must match the list of locations searched for changed files in BuildUAT.bat and BuildUAT.sh -->
  <ItemGroup>
    <Compile Include="..\Shared\MetaData.cs">
      <Link>Properties\MetaData.cs</Link>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <None Include="app.manifest">
      <SubType>Designer</SubType>
    </None>
    <None Include="..\..\..\Intermediate\ProjectFiles\AutomationTool.csproj.References" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.300.105" />
    <PackageReference Include="Microsoft.Build" Version="17.1.0" ExcludeAssets="runtime" PrivateAssets="all" />
    <PackageReference Include="Microsoft.Build.Locator" Version="1.4.1" PrivateAssets="all" />
    <!-- Added as a temporary workaround to running from visual studio where the system version of dotnet is run. 
         Adding a loader context resolves many of the issues, but it still fails when MSBuild tries to write the json -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>
  
  <!-- This set of dependencies should be kept as small as possible
       This list must match the list of locations searched for changed files in BuildUAT.bat and BuildUAT.sh -->
  <ItemGroup>
    <ProjectReference Include="..\Shared\EpicGames.Core\EpicGames.Core.csproj" PrivateAssets="all" />
    <ProjectReference Include="..\Shared\EpicGames.Build\EpicGames.Build.csproj" PrivateAssets="all" />
    <ProjectReference Include="..\UnrealBuildTool\UnrealBuildTool.csproj" PrivateAssets="all" />
  </ItemGroup>

  <Choose>
    <When Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))' == 'true' And '$([System.Runtime.InteropServices.RuntimeInformation]::OSArchitecture)' != 'Arm64'">
      <PropertyGroup Condition="Exists('..\..\..\Restricted\NotForLicensees\Source\Programs\UnrealBuildTool\Executors\Experimental\BoxExecutor.cs')">
        <DefineConstants>$(DefineConstants);__BOXEXECUTOR_AVAILABLE__</DefineConstants>
      </PropertyGroup>
      <PropertyGroup Condition="Exists('..\..\..\Restricted\NotForLicensees\Source\Programs\Shared\EpicGames.Box\BoxServer.cs')">
        <DefineConstants>$(DefineConstants);__BOX_AVAILABLE__</DefineConstants>
      </PropertyGroup>
    </When>
  </Choose>

  <ItemGroup>
    <None Include="..\..\..\Build\BatchFiles\BuildUAT.bat"><Link>BatchFiles\BuildUAT.bat</Link></None>
    <None Include="..\..\..\Build\BatchFiles\BuildUAT.sh"><Link>BatchFiles\BuildUAT.sh</Link></None>
    <None Include="..\..\..\Build\BatchFiles\RunUAT.bat"><Link>BatchFiles\RunUAT.bat</Link></None>
    <None Include="..\..\..\Build\BatchFiles\RunUAT.sh"><Link>BatchFiles\RunUAT.sh</Link></None>
    <None Include="..\..\..\Build\BatchFiles\RunUAT.command"><Link>BatchFiles\RunUAT.command</Link></None>
  </ItemGroup>
  
  <!-- Reference file is generated by GenerateProjectFiles scripts (calling UnrealBuildTool). It contains a list of all project files known to UBT based on how it was run. -->
  <Import Project="..\..\..\Intermediate\ProjectFiles\AutomationTool.csproj.References" Condition="Exists('..\..\..\Intermediate\ProjectFiles\AutomationTool.csproj.References') And '$(AutomationToolProjectOnly)' == ''" />
</Project>