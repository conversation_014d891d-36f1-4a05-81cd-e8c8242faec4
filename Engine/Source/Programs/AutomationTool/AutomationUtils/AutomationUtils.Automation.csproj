<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\Shared\UnrealEngine.csproj.props" />
  
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configuration Condition=" '$(Configuration)' == '' ">Development</Configuration>
    <OutputType>Library</OutputType>
    <RootNamespace>AutomationUtils</RootNamespace>
    <AssemblyName>AutomationUtils.Automation</AssemblyName>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <OutputPath>..\..\..\..\Binaries\DotNET\AutomationTool\AutomationUtils</OutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugType Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))' == 'true' And '$([System.Runtime.InteropServices.RuntimeInformation]::OSArchitecture)' == 'Arm64'">portable</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="fastJSON">
      <HintPath>..\..\..\..\Binaries\ThirdParty\fastJSON\netstandard2.0\fastJSON.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ionic.Zip.Reduced">
      <HintPath>..\..\..\..\Binaries\DotNET\Ionic.Zip.Reduced.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Development|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
  </PropertyGroup>

  <!-- The following will glob all platform extensions' AutomationUtils source
  files and include them in the build. They will also appear in VisualStudio -->
  <ItemGroup>
    <Compile Include="../../../../Platforms/*/Source/Programs/AutomationTool/AutomationUtils/**/*.cs">
      <!-- RecursiveDir metadata expands to [PlatformName]/Source/Programs/[etc]. The Replace() will
      replace everything from the first slash to the end leaving just the platform name. Should the
      Replace() fail then RecursiveDir is still an agreeable value -->
      <Link>Platform/$([System.Text.RegularExpressions.Regex]::Replace(%(Compile.RecursiveDir), [\\/].+$, ``))/%(Compile.FileName).cs</Link>
    </Compile>
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\Shared\EpicGames.Core\EpicGames.Core.csproj" PrivateAssets="All"><Private>true</Private></ProjectReference>
    <ProjectReference Include="..\..\Shared\EpicGames.Build\EpicGames.Build.csproj" PrivateAssets="All"> <Private>true</Private> </ProjectReference>
    <ProjectReference Include="..\..\Shared\EpicGames.Horde\EpicGames.Horde.csproj" PrivateAssets="All">
      <Private>true</Private>
    </ProjectReference>
    <ProjectReference Include="..\..\Shared\EpicGames.Perforce\EpicGames.Perforce.csproj" />
    <ProjectReference Include="..\..\Shared\EpicGames.ProjectStore\EpicGames.ProjectStore.csproj" PrivateAssets="All" />
    <ProjectReference Include="..\..\UnrealBuildTool\UnrealBuildTool.csproj" PrivateAssets="All"><Private>true</Private></ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Drawing.Common" Version="4.7.3" />

    <!--Required by Ionic -->
    <PackageReference Include="System.Security.Permissions" Version="4.7.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="6.0.0" />

    <PackageReference Include="Google.Apis.Drive.v3" Version="1.33.1.1225" PrivateAssets="all" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.33.1.1229" PrivateAssets="all" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>
</Project>