<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\..\Shared\UnrealEngine.csproj.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<OutputType>Library</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Development</Configurations>
    <!-- <BaseOutputPath>bin\</BaseOutputPath> -->
	<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
	<OutputPath>..\..\..\..\..\Binaries\DotNET\AutomationTool\AutomationScripts\Mutable\</OutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Gauntlet\Gauntlet.Automation.csproj" />
  </ItemGroup>

</Project>
